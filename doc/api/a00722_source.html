<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: scalar_multiplication.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">scalar_multiplication.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00722.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_scalar_multiplication is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_scalar_multiplication extension included&quot;)</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;../vec2.hpp&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;../vec3.hpp&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;../vec4.hpp&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;../mat2x2.hpp&quot;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &lt;type_traits&gt;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;{</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> Vec&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        <span class="keyword">using</span> return_type_scalar_multiplication = <span class="keyword">typename</span> std::enable_if&lt;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                !std::is_same&lt;T, float&gt;::value       <span class="comment">// T may not be a float</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                &amp;&amp; std::is_arithmetic&lt;T&gt;::value, Vec <span class="comment">// But it may be an int or double (no vec3 or mat3, ...)</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        &gt;::type;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor">#define GLM_IMPLEMENT_SCAL_MULT(Vec) \</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">        template&lt;typename T&gt; \</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">        return_type_scalar_multiplication&lt;T, Vec&gt; \</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">        operator*(T const&amp; s, Vec rh){ \</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">                return rh *= static_cast&lt;float&gt;(s); \</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">        } \</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">         \</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="preprocessor">        template&lt;typename T&gt; \</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="preprocessor">        return_type_scalar_multiplication&lt;T, Vec&gt; \</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="preprocessor">        operator*(Vec lh, T const&amp; s){ \</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">                return lh *= static_cast&lt;float&gt;(s); \</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">        } \</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">         \</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">        template&lt;typename T&gt; \</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">        return_type_scalar_multiplication&lt;T, Vec&gt; \</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">        operator/(Vec lh, T const&amp; s){ \</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">                return lh *= 1.0f / static_cast&lt;float&gt;(s); \</span></div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="preprocessor">        }</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160; </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00890.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a>)</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00890.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a>)</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00890.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a>)</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#ga8dd59e7fc6913ac5d61b86553e9148ba">mat2</a>)</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#ga493ab21243abe564b3f7d381e677d29a">mat2x3</a>)</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#ga8e879b57ddd81e5bf5a88929844e8b40">mat2x4</a>)</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#ga2c27aea32de57d58aec8e92d5d2181e2">mat3x2</a>)</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#gaefb0fc7a4960b782c18708bb6b655262">mat3</a>)</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#gaf991cad0b34f64e33af186326dbc4d66">mat3x4</a>)</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#gad941c947ad6cdd117a0e8554a4754983">mat4x2</a>)</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#gac7574544bb94777bdbd2eb224eb72fd0">mat4x3</a>)</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;GLM_IMPLEMENT_SCAL_MULT(<a class="code" href="a00892.html#ga0db98d836c5549d31cf64ecd043b7af7">mat4</a>)</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#undef GLM_IMPLEMENT_SCAL_MULT</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;} <span class="comment">// namespace glm</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00892_html_gad941c947ad6cdd117a0e8554a4754983"><div class="ttname"><a href="a00892.html#gad941c947ad6cdd117a0e8554a4754983">glm::mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, defaultp &gt; mat4x2</div><div class="ttdoc">4 columns of 2 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00185_source.html#l00015">matrix_float4x2.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_ga0db98d836c5549d31cf64ecd043b7af7"><div class="ttname"><a href="a00892.html#ga0db98d836c5549d31cf64ecd043b7af7">glm::mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, defaultp &gt; mat4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00197_source.html#l00020">matrix_float4x4.hpp:20</a></div></div>
<div class="ttc" id="aa00890_html_gabe65c061834f61b4f7cb6037b19006a4"><div class="ttname"><a href="a00890.html#gabe65c061834f61b4f7cb6037b19006a4">glm::vec2</a></div><div class="ttdeci">vec&lt; 2, float, defaultp &gt; vec2</div><div class="ttdoc">2 components vector of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00437_source.html#l00015">vector_float2.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_gaefb0fc7a4960b782c18708bb6b655262"><div class="ttname"><a href="a00892.html#gaefb0fc7a4960b782c18708bb6b655262">glm::mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, defaultp &gt; mat3</div><div class="ttdoc">3 columns of 3 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00173_source.html#l00020">matrix_float3x3.hpp:20</a></div></div>
<div class="ttc" id="aa00892_html_gac7574544bb94777bdbd2eb224eb72fd0"><div class="ttname"><a href="a00892.html#gac7574544bb94777bdbd2eb224eb72fd0">glm::mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, defaultp &gt; mat4x3</div><div class="ttdoc">4 columns of 3 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00191_source.html#l00015">matrix_float4x3.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_gaf991cad0b34f64e33af186326dbc4d66"><div class="ttname"><a href="a00892.html#gaf991cad0b34f64e33af186326dbc4d66">glm::mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, defaultp &gt; mat3x4</div><div class="ttdoc">3 columns of 4 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00179_source.html#l00015">matrix_float3x4.hpp:15</a></div></div>
<div class="ttc" id="aa00890_html_ga9c3019b13faf179e4ad3626ea66df334"><div class="ttname"><a href="a00890.html#ga9c3019b13faf179e4ad3626ea66df334">glm::vec3</a></div><div class="ttdeci">vec&lt; 3, float, defaultp &gt; vec3</div><div class="ttdoc">3 components vector of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00443_source.html#l00015">vector_float3.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_ga493ab21243abe564b3f7d381e677d29a"><div class="ttname"><a href="a00892.html#ga493ab21243abe564b3f7d381e677d29a">glm::mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, defaultp &gt; mat2x3</div><div class="ttdoc">2 columns of 3 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00015">matrix_float2x3.hpp:15</a></div></div>
<div class="ttc" id="aa00890_html_gac215a35481a6597d1bf622a382e9d6e2"><div class="ttname"><a href="a00890.html#gac215a35481a6597d1bf622a382e9d6e2">glm::vec4</a></div><div class="ttdeci">vec&lt; 4, float, defaultp &gt; vec4</div><div class="ttdoc">4 components vector of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00449_source.html#l00015">vector_float4.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_ga2c27aea32de57d58aec8e92d5d2181e2"><div class="ttname"><a href="a00892.html#ga2c27aea32de57d58aec8e92d5d2181e2">glm::mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, defaultp &gt; mat3x2</div><div class="ttdoc">3 columns of 2 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00167_source.html#l00015">matrix_float3x2.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_ga8e879b57ddd81e5bf5a88929844e8b40"><div class="ttname"><a href="a00892.html#ga8e879b57ddd81e5bf5a88929844e8b40">glm::mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, defaultp &gt; mat2x4</div><div class="ttdoc">2 columns of 4 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00015">matrix_float2x4.hpp:15</a></div></div>
<div class="ttc" id="aa00892_html_ga8dd59e7fc6913ac5d61b86553e9148ba"><div class="ttname"><a href="a00892.html#ga8dd59e7fc6913ac5d61b86553e9148ba">glm::mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, defaultp &gt; mat2</div><div class="ttdoc">2 columns of 2 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00149_source.html#l00020">matrix_float2x2.hpp:20</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
