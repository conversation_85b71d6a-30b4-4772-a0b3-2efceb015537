<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: gradient_paint.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gradient_paint.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00635.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../gtx/optimum_pow.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_gradient_paint is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_gradient_paint extension included&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="a00936.html#gaaecb1e93de4cbe0758b882812d4da294">   34</a></span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00936.html#gaaecb1e93de4cbe0758b882812d4da294">radialGradient</a>(</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; Center,</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                T <span class="keyword">const</span>&amp; Radius,</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; Focal,</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; Position);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="a00936.html#ga849241df1e55129b8ce9476200307419">   43</a></span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00936.html#ga849241df1e55129b8ce9476200307419">linearGradient</a>(</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; Point0,</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; Point1,</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; Position);</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;}<span class="comment">// namespace glm</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#include &quot;gradient_paint.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00936_html_gaaecb1e93de4cbe0758b882812d4da294"><div class="ttname"><a href="a00936.html#gaaecb1e93de4cbe0758b882812d4da294">glm::radialGradient</a></div><div class="ttdeci">GLM_FUNC_DECL T radialGradient(vec&lt; 2, T, Q &gt; const &amp;Center, T const &amp;Radius, vec&lt; 2, T, Q &gt; const &amp;Focal, vec&lt; 2, T, Q &gt; const &amp;Position)</div><div class="ttdoc">Return a color from a radial gradient.</div></div>
<div class="ttc" id="aa00936_html_ga849241df1e55129b8ce9476200307419"><div class="ttname"><a href="a00936.html#ga849241df1e55129b8ce9476200307419">glm::linearGradient</a></div><div class="ttdeci">GLM_FUNC_DECL T linearGradient(vec&lt; 2, T, Q &gt; const &amp;Point0, vec&lt; 2, T, Q &gt; const &amp;Point1, vec&lt; 2, T, Q &gt; const &amp;Position)</div><div class="ttdoc">Return a color from a linear gradient.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
