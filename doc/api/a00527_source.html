<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: glm.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">glm.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="preprocessor">#include &quot;detail/_fixes.hpp&quot;</span></div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#include &quot;detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#include &lt;climits&gt;</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="preprocessor">#include &lt;cfloat&gt;</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor">#include &lt;cassert&gt;</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor">#include &quot;fwd.hpp&quot;</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00791.html">vec2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00794.html">vec3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00797.html">vec4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00758.html">mat2x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00761.html">mat2x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00764.html">mat2x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00767.html">mat3x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00770.html">mat3x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00773.html">mat3x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00776.html">mat4x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00779.html">mat4x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00782.html">mat4x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160; </div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00788.html">trigonometric.hpp</a>&quot;</span></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00083.html">exponential.hpp</a>&quot;</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a01564.html">common.hpp</a>&quot;</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00554.html">packing.hpp</a>&quot;</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00524.html">geometric.hpp</a>&quot;</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00785.html">matrix.hpp</a>&quot;</span></div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00488.html">vector_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00542.html">integer.hpp</a>&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00758_html"><div class="ttname"><a href="a00758.html">mat2x2.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00542_html"><div class="ttname"><a href="a00542.html">integer.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00524_html"><div class="ttname"><a href="a00524.html">geometric.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00779_html"><div class="ttname"><a href="a00779.html">mat4x3.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00554_html"><div class="ttname"><a href="a00554.html">packing.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00767_html"><div class="ttname"><a href="a00767.html">mat3x2.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00083_html"><div class="ttname"><a href="a00083.html">exponential.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00488_html"><div class="ttname"><a href="a00488.html">vector_relational.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00788_html"><div class="ttname"><a href="a00788.html">trigonometric.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00776_html"><div class="ttname"><a href="a00776.html">mat4x2.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00761_html"><div class="ttname"><a href="a00761.html">mat2x3.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00797_html"><div class="ttname"><a href="a00797.html">vec4.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00782_html"><div class="ttname"><a href="a00782.html">mat4x4.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00770_html"><div class="ttname"><a href="a00770.html">mat3x3.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00764_html"><div class="ttname"><a href="a00764.html">mat2x4.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00791_html"><div class="ttname"><a href="a00791.html">vec2.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00785_html"><div class="ttname"><a href="a00785.html">matrix.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa01564_html"><div class="ttname"><a href="a01564.html">common.hpp</a></div><div class="ttdoc">GLM_GTX_common</div></div>
<div class="ttc" id="aa00773_html"><div class="ttname"><a href="a00773.html">mat3x4.hpp</a></div><div class="ttdoc">Core features</div></div>
<div class="ttc" id="aa00794_html"><div class="ttname"><a href="a00794.html">vec3.hpp</a></div><div class="ttdoc">Core features</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
