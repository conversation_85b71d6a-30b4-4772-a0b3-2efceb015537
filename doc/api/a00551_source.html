<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: noise.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_d9678a6a9012da969e0b059b39347945.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">noise.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00551.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;../detail/_noise.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../geometric.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../vector_relational.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../vec2.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;../vec3.hpp&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;../vec4.hpp&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_noise extension included&quot;)</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;{</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="a00906.html#ga1e043ce3b51510e9bc4469227cefc38a">   42</a></span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00906.html#gac270edc54c5fc52f5985a45f940bb103">perlin</a>(</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="a00906.html#gac270edc54c5fc52f5985a45f940bb103">   48</a></span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00906.html#gac270edc54c5fc52f5985a45f940bb103">perlin</a>(</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p,</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; rep);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="a00906.html#ga8122468c69015ff397349a7dcc638b27">   55</a></span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00906.html#ga8122468c69015ff397349a7dcc638b27">simplex</a>(</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160; </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#include &quot;noise.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00906_html_ga8122468c69015ff397349a7dcc638b27"><div class="ttname"><a href="a00906.html#ga8122468c69015ff397349a7dcc638b27">glm::simplex</a></div><div class="ttdeci">GLM_FUNC_DECL T simplex(vec&lt; L, T, Q &gt; const &amp;p)</div><div class="ttdoc">Simplex noise.</div></div>
<div class="ttc" id="aa00906_html_gac270edc54c5fc52f5985a45f940bb103"><div class="ttname"><a href="a00906.html#gac270edc54c5fc52f5985a45f940bb103">glm::perlin</a></div><div class="ttdeci">GLM_FUNC_DECL T perlin(vec&lt; L, T, Q &gt; const &amp;p, vec&lt; L, T, Q &gt; const &amp;rep)</div><div class="ttdoc">Periodic perlin noise.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
