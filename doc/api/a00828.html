<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_matrix_transform</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_matrix_transform<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga81696f2b8d1db02ea1aff8da8f269314"><td class="memTemplParams" colspan="2"><a id="ga81696f2b8d1db02ea1aff8da8f269314"></a>
template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga81696f2b8d1db02ea1aff8da8f269314"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#ga81696f2b8d1db02ea1aff8da8f269314">identity</a> ()</td></tr>
<tr class="memdesc:ga81696f2b8d1db02ea1aff8da8f269314"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds an identity matrix. <br /></td></tr>
<tr class="separator:ga81696f2b8d1db02ea1aff8da8f269314"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa64aa951a0e99136bba9008d2b59c78e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa64aa951a0e99136bba9008d2b59c78e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#gaa64aa951a0e99136bba9008d2b59c78e">lookAt</a> (vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</td></tr>
<tr class="memdesc:gaa64aa951a0e99136bba9008d2b59c78e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a look at view matrix based on the default handedness.  <a href="a00828.html#gaa64aa951a0e99136bba9008d2b59c78e">More...</a><br /></td></tr>
<tr class="separator:gaa64aa951a0e99136bba9008d2b59c78e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2c09e25b0a16d3a9d89cc85bbae41b0"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab2c09e25b0a16d3a9d89cc85bbae41b0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">lookAtLH</a> (vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</td></tr>
<tr class="memdesc:gab2c09e25b0a16d3a9d89cc85bbae41b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a left handed look at view matrix.  <a href="a00828.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">More...</a><br /></td></tr>
<tr class="separator:gab2c09e25b0a16d3a9d89cc85bbae41b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacfa12c8889c754846bc20c65d9b5c701"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacfa12c8889c754846bc20c65d9b5c701"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#gacfa12c8889c754846bc20c65d9b5c701">lookAtRH</a> (vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</td></tr>
<tr class="memdesc:gacfa12c8889c754846bc20c65d9b5c701"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a right handed look at view matrix.  <a href="a00828.html#gacfa12c8889c754846bc20c65d9b5c701">More...</a><br /></td></tr>
<tr class="separator:gacfa12c8889c754846bc20c65d9b5c701"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee9e865eaa9776370996da2940873fd4"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaee9e865eaa9776370996da2940873fd4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#gaee9e865eaa9776370996da2940873fd4">rotate</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T angle, vec&lt; 3, T, Q &gt; const &amp;axis)</td></tr>
<tr class="memdesc:gaee9e865eaa9776370996da2940873fd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a rotation 4 * 4 matrix created from an axis vector and an angle.  <a href="a00828.html#gaee9e865eaa9776370996da2940873fd4">More...</a><br /></td></tr>
<tr class="separator:gaee9e865eaa9776370996da2940873fd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05051adbee603fb3c5095d8cf5cc229b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga05051adbee603fb3c5095d8cf5cc229b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#ga05051adbee603fb3c5095d8cf5cc229b">scale</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga05051adbee603fb3c5095d8cf5cc229b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a scale 4 * 4 matrix created from 3 scalars.  <a href="a00828.html#ga05051adbee603fb3c5095d8cf5cc229b">More...</a><br /></td></tr>
<tr class="separator:ga05051adbee603fb3c5095d8cf5cc229b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga391e0142852ab4139dcea0d9b1bbc048"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga391e0142852ab4139dcea0d9b1bbc048"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#ga391e0142852ab4139dcea0d9b1bbc048">shear</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;p, vec&lt; 2, T, Q &gt; const &amp;l_x, vec&lt; 2, T, Q &gt; const &amp;l_y, vec&lt; 2, T, Q &gt; const &amp;l_z)</td></tr>
<tr class="memdesc:ga391e0142852ab4139dcea0d9b1bbc048"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a scale 4 * 4 matrix created from point referent 3 shearers.  <a href="a00828.html#ga391e0142852ab4139dcea0d9b1bbc048">More...</a><br /></td></tr>
<tr class="separator:ga391e0142852ab4139dcea0d9b1bbc048"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6b494bda2f47615b2fd3e70f3d2c912"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac6b494bda2f47615b2fd3e70f3d2c912"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">translate</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gac6b494bda2f47615b2fd3e70f3d2c912"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a translation 4 * 4 matrix created from a vector of 3 components.  <a href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">More...</a><br /></td></tr>
<tr class="separator:gac6b494bda2f47615b2fd3e70f3d2c912"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Defines functions that generate common transformation matrices.</p>
<p>The matrices generated by this extension use standard OpenGL fixed-function conventions. For example, the lookAt function generates a transform from world space into the specific eye space that the projective matrix functions (perspective, ortho, etc) are designed to expect. The OpenGL compatibility specifications defines the particular layout of this eye space.</p>
<p>Include &lt;<a class="el" href="a01579.html" title="GLM_EXT_matrix_transform">glm/ext/matrix_transform.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00826.html">GLM_EXT_matrix_projection</a> </dd>
<dd>
<a class="el" href="a00805.html">GLM_EXT_matrix_clip_space</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a id="gaa64aa951a0e99136bba9008d2b59c78e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa64aa951a0e99136bba9008d2b59c78e">&#9670;&nbsp;</a></span>lookAt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::lookAt </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>eye</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>center</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a look at view matrix based on the default handedness. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">eye</td><td>Position of the camera </td></tr>
    <tr><td class="paramname">center</td><td>Position where the camera is looking at </td></tr>
    <tr><td class="paramname">up</td><td>Normalized up vector, how the camera is oriented. Typically (0, 0, 1)</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- frustum(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; nearVal, T const&amp; farVal) frustum(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; nearVal, T const&amp; farVal) </dd>
<dd>
<a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluLookAt.xml">gluLookAt man page</a> </dd></dl>

</div>
</div>
<a id="gab2c09e25b0a16d3a9d89cc85bbae41b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab2c09e25b0a16d3a9d89cc85bbae41b0">&#9670;&nbsp;</a></span>lookAtLH()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::lookAtLH </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>eye</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>center</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a left handed look at view matrix. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">eye</td><td>Position of the camera </td></tr>
    <tr><td class="paramname">center</td><td>Position where the camera is looking at </td></tr>
    <tr><td class="paramname">up</td><td>Normalized up vector, how the camera is oriented. Typically (0, 0, 1)</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- frustum(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; nearVal, T const&amp; farVal) frustum(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; nearVal, T const&amp; farVal) </dd></dl>

</div>
</div>
<a id="gacfa12c8889c754846bc20c65d9b5c701"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacfa12c8889c754846bc20c65d9b5c701">&#9670;&nbsp;</a></span>lookAtRH()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::lookAtRH </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>eye</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>center</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a right handed look at view matrix. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">eye</td><td>Position of the camera </td></tr>
    <tr><td class="paramname">center</td><td>Position where the camera is looking at </td></tr>
    <tr><td class="paramname">up</td><td>Normalized up vector, how the camera is oriented. Typically (0, 0, 1)</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- frustum(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; nearVal, T const&amp; farVal) frustum(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; nearVal, T const&amp; farVal) </dd></dl>

</div>
</div>
<a id="gaee9e865eaa9776370996da2940873fd4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaee9e865eaa9776370996da2940873fd4">&#9670;&nbsp;</a></span>rotate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>axis</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a rotation 4 * 4 matrix created from an axis vector and an angle. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this rotation matrix. </td></tr>
    <tr><td class="paramname">angle</td><td>Rotation angle expressed in radians. </td></tr>
    <tr><td class="paramname">axis</td><td>Rotation axis, recommended to be normalized.</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- rotate(mat&lt;4, 4, T, Q&gt; const&amp; m, T angle, T x, T y, T z) </dd>
<dd>
- rotate(T angle, vec&lt;3, T, Q&gt; const&amp; v) </dd>
<dd>
<a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glRotate.xml">glRotate man page</a> </dd></dl>

</div>
</div>
<a id="ga05051adbee603fb3c5095d8cf5cc229b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga05051adbee603fb3c5095d8cf5cc229b">&#9670;&nbsp;</a></span>scale()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::scale </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a scale 4 * 4 matrix created from 3 scalars. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this scale matrix. </td></tr>
    <tr><td class="paramname">v</td><td>Ratio of scaling for each axis.</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- scale(mat&lt;4, 4, T, Q&gt; const&amp; m, T x, T y, T z) </dd>
<dd>
- scale(vec&lt;3, T, Q&gt; const&amp; v) </dd>
<dd>
<a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glScale.xml">glScale man page</a> </dd></dl>

</div>
</div>
<a id="ga391e0142852ab4139dcea0d9b1bbc048"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga391e0142852ab4139dcea0d9b1bbc048">&#9670;&nbsp;</a></span>shear()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;4, 4, T, Q&gt; glm::shear </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>l_x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>l_y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>l_z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a scale 4 * 4 matrix created from point referent 3 shearers. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this shear matrix. </td></tr>
    <tr><td class="paramname">p</td><td>Point of shearing as reference. </td></tr>
    <tr><td class="paramname">l_x</td><td>Ratio of matrix.x projection in YZ plane relative to the y-axis/z-axis. </td></tr>
    <tr><td class="paramname">l_y</td><td>Ratio of matrix.y projection in XZ plane relative to the x-axis/z-axis. </td></tr>
    <tr><td class="paramname">l_z</td><td>Ratio of matrix.z projection in XY plane relative to the x-axis/y-axis.</td></tr>
  </table>
  </dd>
</dl>
<p>as example: [1 , l_xy, l_xz, -(l_xy+l_xz) * p_x] [x] T [x<code>, y</code>, z<code>, w</code>] = [x<code>, y</code>, z<code>, w</code>] * [l_yx, 1 , l_yz, -(l_yx+l_yz) * p_y] [y] [l_zx, l_zy, 1 , -(l_zx+l_zy) * p_z] [z] [0 , 0 , 0 , 1 ] [w]</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point shear type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- shear(mat&lt;4, 4, T, Q&gt; const&amp; m, T x, T y, T z) </dd>
<dd>
- shear(vec&lt;3, T, Q&gt; const&amp; p) </dd>
<dd>
- shear(vec&lt;2, T, Q&gt; const&amp; l_x) </dd>
<dd>
- shear(vec&lt;2, T, Q&gt; const&amp; l_y) </dd>
<dd>
- shear(vec&lt;2, T, Q&gt; const&amp; l_z) </dd>
<dd>
no resource... </dd></dl>

</div>
</div>
<a id="gac6b494bda2f47615b2fd3e70f3d2c912"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac6b494bda2f47615b2fd3e70f3d2c912">&#9670;&nbsp;</a></span>translate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR mat&lt;4, 4, T, Q&gt; glm::translate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a translation 4 * 4 matrix created from a vector of 3 components. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this translation matrix. </td></tr>
    <tr><td class="paramname">v</td><td>Coordinates of a translation vector.</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;glm/glm.hpp&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="a01582.html">glm/gtc/matrix_transform.hpp</a>&gt;</span></div>
<div class="line">...</div>
<div class="line">glm::mat4 m = <a class="code" href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">glm::translate</a>(<a class="code" href="a00892.html#ga0db98d836c5549d31cf64ecd043b7af7">glm::mat4</a>(1.0f), <a class="code" href="a00890.html#ga9c3019b13faf179e4ad3626ea66df334">glm::vec3</a>(1.0f));</div>
<div class="line"><span class="comment">// m[0][0] == 1.0f, m[0][1] == 0.0f, m[0][2] == 0.0f, m[0][3] == 0.0f</span></div>
<div class="line"><span class="comment">// m[1][0] == 0.0f, m[1][1] == 1.0f, m[1][2] == 0.0f, m[1][3] == 0.0f</span></div>
<div class="line"><span class="comment">// m[2][0] == 0.0f, m[2][1] == 0.0f, m[2][2] == 1.0f, m[2][3] == 0.0f</span></div>
<div class="line"><span class="comment">// m[3][0] == 1.0f, m[3][1] == 1.0f, m[3][2] == 1.0f, m[3][3] == 1.0f</span></div>
</div><!-- fragment --><dl class="section see"><dt>See also</dt><dd>- translate(mat&lt;4, 4, T, Q&gt; const&amp; m, T x, T y, T z) </dd>
<dd>
- translate(vec&lt;3, T, Q&gt; const&amp; v) </dd>
<dd>
<a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glTranslate.xml">glTranslate man page</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<div class="ttc" id="aa00828_html_gac6b494bda2f47615b2fd3e70f3d2c912"><div class="ttname"><a href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">glm::translate</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR mat&lt; 4, 4, T, Q &gt; translate(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a translation 4 * 4 matrix created from a vector of 3 components.</div></div>
<div class="ttc" id="aa00892_html_ga0db98d836c5549d31cf64ecd043b7af7"><div class="ttname"><a href="a00892.html#ga0db98d836c5549d31cf64ecd043b7af7">glm::mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, defaultp &gt; mat4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00197_source.html#l00020">matrix_float4x4.hpp:20</a></div></div>
<div class="ttc" id="aa00890_html_ga9c3019b13faf179e4ad3626ea66df334"><div class="ttname"><a href="a00890.html#ga9c3019b13faf179e4ad3626ea66df334">glm::vec3</a></div><div class="ttdeci">vec&lt; 3, float, defaultp &gt; vec3</div><div class="ttdoc">3 components vector of single-precision floating-point numbers.</div><div class="ttdef"><b>Definition:</b> <a href="a00443_source.html#l00015">vector_float3.hpp:15</a></div></div>
<div class="ttc" id="aa01582_html"><div class="ttname"><a href="a01582.html">matrix_transform.hpp</a></div><div class="ttdoc">GLM_GTC_matrix_transform</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
