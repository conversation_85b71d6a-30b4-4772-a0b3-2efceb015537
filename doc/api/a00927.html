<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_GTX_easing</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_easing<div class="ingroups"><a class="el" href="a00896.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga93cddcdb6347a44d5927cc2bf2570816"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga93cddcdb6347a44d5927cc2bf2570816"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga93cddcdb6347a44d5927cc2bf2570816">backEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="separator:ga93cddcdb6347a44d5927cc2bf2570816"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga33777c9dd98f61d9472f96aafdf2bd36"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga33777c9dd98f61d9472f96aafdf2bd36"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga33777c9dd98f61d9472f96aafdf2bd36">backEaseIn</a> (genType const &amp;a, genType const &amp;o)</td></tr>
<tr class="separator:ga33777c9dd98f61d9472f96aafdf2bd36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace6d24722a2f6722b56398206eb810bb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gace6d24722a2f6722b56398206eb810bb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gace6d24722a2f6722b56398206eb810bb">backEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="separator:gace6d24722a2f6722b56398206eb810bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga68a7b760f2afdfab298d5cd6d7611fb1"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga68a7b760f2afdfab298d5cd6d7611fb1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga68a7b760f2afdfab298d5cd6d7611fb1">backEaseInOut</a> (genType const &amp;a, genType const &amp;o)</td></tr>
<tr class="separator:ga68a7b760f2afdfab298d5cd6d7611fb1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf25069fa906413c858fd46903d520b9"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gabf25069fa906413c858fd46903d520b9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gabf25069fa906413c858fd46903d520b9">backEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="separator:gabf25069fa906413c858fd46903d520b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga640c1ac6fe9d277a197da69daf60ee4f"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga640c1ac6fe9d277a197da69daf60ee4f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga640c1ac6fe9d277a197da69daf60ee4f">backEaseOut</a> (genType const &amp;a, genType const &amp;o)</td></tr>
<tr class="separator:ga640c1ac6fe9d277a197da69daf60ee4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaac30767f2e430b0c3fc859a4d59c7b5b"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaac30767f2e430b0c3fc859a4d59c7b5b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gaac30767f2e430b0c3fc859a4d59c7b5b">bounceEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="separator:gaac30767f2e430b0c3fc859a4d59c7b5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf9f38eff1e5f4c2fa5b629a25ae413e"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gadf9f38eff1e5f4c2fa5b629a25ae413e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gadf9f38eff1e5f4c2fa5b629a25ae413e">bounceEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="separator:gadf9f38eff1e5f4c2fa5b629a25ae413e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94007005ff0dcfa0749ebfa2aec540b2"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga94007005ff0dcfa0749ebfa2aec540b2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga94007005ff0dcfa0749ebfa2aec540b2">bounceEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="separator:ga94007005ff0dcfa0749ebfa2aec540b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga34508d4b204a321ec26d6086aa047997"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga34508d4b204a321ec26d6086aa047997"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga34508d4b204a321ec26d6086aa047997">circularEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga34508d4b204a321ec26d6086aa047997"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after shifted quadrant IV of unit circle.  <a href="a00927.html#ga34508d4b204a321ec26d6086aa047997">More...</a><br /></td></tr>
<tr class="separator:ga34508d4b204a321ec26d6086aa047997"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0c1027637a5b02d4bb3612aa12599d69"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga0c1027637a5b02d4bb3612aa12599d69"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga0c1027637a5b02d4bb3612aa12599d69">circularEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga0c1027637a5b02d4bb3612aa12599d69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise circular function y = (1/2)(1 - sqrt(1 - 4x^2)) ; [0, 0.5) y = (1/2)(sqrt(-(2x - 3)*(2x - 1)) + 1) ; [0.5, 1].  <a href="a00927.html#ga0c1027637a5b02d4bb3612aa12599d69">More...</a><br /></td></tr>
<tr class="separator:ga0c1027637a5b02d4bb3612aa12599d69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga26fefde9ced9b72745fe21f1a3fe8da7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga26fefde9ced9b72745fe21f1a3fe8da7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga26fefde9ced9b72745fe21f1a3fe8da7">circularEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga26fefde9ced9b72745fe21f1a3fe8da7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after shifted quadrant II of unit circle.  <a href="a00927.html#ga26fefde9ced9b72745fe21f1a3fe8da7">More...</a><br /></td></tr>
<tr class="separator:ga26fefde9ced9b72745fe21f1a3fe8da7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff52f746102b94864d105563ba8895ae"><td class="memTemplParams" colspan="2"><a id="gaff52f746102b94864d105563ba8895ae"></a>
template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaff52f746102b94864d105563ba8895ae"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gaff52f746102b94864d105563ba8895ae">cubicEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:gaff52f746102b94864d105563ba8895ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the cubic y = x^3. <br /></td></tr>
<tr class="separator:gaff52f746102b94864d105563ba8895ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga55134072b42d75452189321d4a2ad91c"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga55134072b42d75452189321d4a2ad91c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga55134072b42d75452189321d4a2ad91c">cubicEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga55134072b42d75452189321d4a2ad91c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise cubic y = (1/2)((2x)^3) ; [0, 0.5) y = (1/2)((2x-2)^3 + 2) ; [0.5, 1].  <a href="a00927.html#ga55134072b42d75452189321d4a2ad91c">More...</a><br /></td></tr>
<tr class="separator:ga55134072b42d75452189321d4a2ad91c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga40d746385d8bcc5973f5bc6a2340ca91"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga40d746385d8bcc5973f5bc6a2340ca91"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga40d746385d8bcc5973f5bc6a2340ca91">cubicEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga40d746385d8bcc5973f5bc6a2340ca91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the cubic y = (x - 1)^3 + 1.  <a href="a00927.html#ga40d746385d8bcc5973f5bc6a2340ca91">More...</a><br /></td></tr>
<tr class="separator:ga40d746385d8bcc5973f5bc6a2340ca91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga230918eccee4e113d10ec5b8cdc58695"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga230918eccee4e113d10ec5b8cdc58695"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga230918eccee4e113d10ec5b8cdc58695">elasticEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga230918eccee4e113d10ec5b8cdc58695"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the damped sine wave y = sin(13pi/2*x)*pow(2, 10 * (x - 1))  <a href="a00927.html#ga230918eccee4e113d10ec5b8cdc58695">More...</a><br /></td></tr>
<tr class="separator:ga230918eccee4e113d10ec5b8cdc58695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2db4ac8959559b11b4029e54812908d6"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga2db4ac8959559b11b4029e54812908d6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga2db4ac8959559b11b4029e54812908d6">elasticEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga2db4ac8959559b11b4029e54812908d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise exponentially-damped sine wave: y = (1/2)*sin(13pi/2*(2*x))*pow(2, 10 * ((2*x) - 1)) ; [0,0.5) y = (1/2)*(sin(-13pi/2*((2x-1)+1))*pow(2,-10(2*x-1)) + 2) ; [0.5, 1].  <a href="a00927.html#ga2db4ac8959559b11b4029e54812908d6">More...</a><br /></td></tr>
<tr class="separator:ga2db4ac8959559b11b4029e54812908d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace9c9d1bdf88bf2ab1e7cdefa54c7365"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gace9c9d1bdf88bf2ab1e7cdefa54c7365"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gace9c9d1bdf88bf2ab1e7cdefa54c7365">elasticEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:gace9c9d1bdf88bf2ab1e7cdefa54c7365"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the damped sine wave y = sin(-13pi/2*(x + 1))*pow(2, -10x) + 1.  <a href="a00927.html#gace9c9d1bdf88bf2ab1e7cdefa54c7365">More...</a><br /></td></tr>
<tr class="separator:gace9c9d1bdf88bf2ab1e7cdefa54c7365"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f24ee9219ab4c84dc8de24be84c1e3c"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga7f24ee9219ab4c84dc8de24be84c1e3c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga7f24ee9219ab4c84dc8de24be84c1e3c">exponentialEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga7f24ee9219ab4c84dc8de24be84c1e3c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the exponential function y = 2^(10(x - 1))  <a href="a00927.html#ga7f24ee9219ab4c84dc8de24be84c1e3c">More...</a><br /></td></tr>
<tr class="separator:ga7f24ee9219ab4c84dc8de24be84c1e3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga232fb6dc093c5ce94bee105ff2947501"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga232fb6dc093c5ce94bee105ff2947501"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga232fb6dc093c5ce94bee105ff2947501">exponentialEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga232fb6dc093c5ce94bee105ff2947501"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise exponential y = (1/2)2^(10(2x - 1)) ; [0,0.5) y = -(1/2)*2^(-10(2x - 1))) + 1 ; [0.5,1].  <a href="a00927.html#ga232fb6dc093c5ce94bee105ff2947501">More...</a><br /></td></tr>
<tr class="separator:ga232fb6dc093c5ce94bee105ff2947501"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga517f2bcfd15bc2c25c466ae50808efc3"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga517f2bcfd15bc2c25c466ae50808efc3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga517f2bcfd15bc2c25c466ae50808efc3">exponentialEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga517f2bcfd15bc2c25c466ae50808efc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the exponential function y = -2^(-10x) + 1.  <a href="a00927.html#ga517f2bcfd15bc2c25c466ae50808efc3">More...</a><br /></td></tr>
<tr class="separator:ga517f2bcfd15bc2c25c466ae50808efc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga290c3e47cb0a49f2e8abe90b1872b649"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga290c3e47cb0a49f2e8abe90b1872b649"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga290c3e47cb0a49f2e8abe90b1872b649">linearInterpolation</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga290c3e47cb0a49f2e8abe90b1872b649"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the line y = x.  <a href="a00927.html#ga290c3e47cb0a49f2e8abe90b1872b649">More...</a><br /></td></tr>
<tr class="separator:ga290c3e47cb0a49f2e8abe90b1872b649"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf42089d35855695132d217cd902304a0"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaf42089d35855695132d217cd902304a0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gaf42089d35855695132d217cd902304a0">quadraticEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:gaf42089d35855695132d217cd902304a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the parabola y = x^2.  <a href="a00927.html#gaf42089d35855695132d217cd902304a0">More...</a><br /></td></tr>
<tr class="separator:gaf42089d35855695132d217cd902304a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e8fc2d7945a4e63ee33b2159c14cea"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga03e8fc2d7945a4e63ee33b2159c14cea"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga03e8fc2d7945a4e63ee33b2159c14cea">quadraticEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga03e8fc2d7945a4e63ee33b2159c14cea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise quadratic y = (1/2)((2x)^2) ; [0, 0.5) y = -(1/2)((2x-1)*(2x-3) - 1) ; [0.5, 1].  <a href="a00927.html#ga03e8fc2d7945a4e63ee33b2159c14cea">More...</a><br /></td></tr>
<tr class="separator:ga03e8fc2d7945a4e63ee33b2159c14cea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga283717bc2d937547ad34ec0472234ee3"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga283717bc2d937547ad34ec0472234ee3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga283717bc2d937547ad34ec0472234ee3">quadraticEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga283717bc2d937547ad34ec0472234ee3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the parabola y = -x^2 + 2x.  <a href="a00927.html#ga283717bc2d937547ad34ec0472234ee3">More...</a><br /></td></tr>
<tr class="separator:ga283717bc2d937547ad34ec0472234ee3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga808b41f14514f47dad5dcc69eb924afd"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga808b41f14514f47dad5dcc69eb924afd"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga808b41f14514f47dad5dcc69eb924afd">quarticEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga808b41f14514f47dad5dcc69eb924afd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the quartic x^4.  <a href="a00927.html#ga808b41f14514f47dad5dcc69eb924afd">More...</a><br /></td></tr>
<tr class="separator:ga808b41f14514f47dad5dcc69eb924afd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d000f852de12b197e154f234b20c505"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga6d000f852de12b197e154f234b20c505"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga6d000f852de12b197e154f234b20c505">quarticEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga6d000f852de12b197e154f234b20c505"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise quartic y = (1/2)((2x)^4) ; [0, 0.5) y = -(1/2)((2x-2)^4 - 2) ; [0.5, 1].  <a href="a00927.html#ga6d000f852de12b197e154f234b20c505">More...</a><br /></td></tr>
<tr class="separator:ga6d000f852de12b197e154f234b20c505"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4dfb33fa7664aa888eb647999d329b98"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga4dfb33fa7664aa888eb647999d329b98"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga4dfb33fa7664aa888eb647999d329b98">quarticEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga4dfb33fa7664aa888eb647999d329b98"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the quartic y = 1 - (x - 1)^4.  <a href="a00927.html#ga4dfb33fa7664aa888eb647999d329b98">More...</a><br /></td></tr>
<tr class="separator:ga4dfb33fa7664aa888eb647999d329b98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga097579d8e087dcf48037588140a21640"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga097579d8e087dcf48037588140a21640"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga097579d8e087dcf48037588140a21640">quinticEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga097579d8e087dcf48037588140a21640"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the quintic y = x^5.  <a href="a00927.html#ga097579d8e087dcf48037588140a21640">More...</a><br /></td></tr>
<tr class="separator:ga097579d8e087dcf48037588140a21640"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a82d5c46df7e2d21cc0108eb7b83934"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga2a82d5c46df7e2d21cc0108eb7b83934"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga2a82d5c46df7e2d21cc0108eb7b83934">quinticEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga2a82d5c46df7e2d21cc0108eb7b83934"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the piecewise quintic y = (1/2)((2x)^5) ; [0, 0.5) y = (1/2)((2x-2)^5 + 2) ; [0.5, 1].  <a href="a00927.html#ga2a82d5c46df7e2d21cc0108eb7b83934">More...</a><br /></td></tr>
<tr class="separator:ga2a82d5c46df7e2d21cc0108eb7b83934"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7dbd4d5c8da3f5353121f615e7b591d7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga7dbd4d5c8da3f5353121f615e7b591d7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#ga7dbd4d5c8da3f5353121f615e7b591d7">quinticEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:ga7dbd4d5c8da3f5353121f615e7b591d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after the quintic y = (x - 1)^5 + 1.  <a href="a00927.html#ga7dbd4d5c8da3f5353121f615e7b591d7">More...</a><br /></td></tr>
<tr class="separator:ga7dbd4d5c8da3f5353121f615e7b591d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb338ac6f6b2bcafee50e3dca5201dbf"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gafb338ac6f6b2bcafee50e3dca5201dbf"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gafb338ac6f6b2bcafee50e3dca5201dbf">sineEaseIn</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:gafb338ac6f6b2bcafee50e3dca5201dbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after quarter-cycle of sine wave.  <a href="a00927.html#gafb338ac6f6b2bcafee50e3dca5201dbf">More...</a><br /></td></tr>
<tr class="separator:gafb338ac6f6b2bcafee50e3dca5201dbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa46e3d5fbf7a15caa28eff9ef192d7c7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaa46e3d5fbf7a15caa28eff9ef192d7c7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gaa46e3d5fbf7a15caa28eff9ef192d7c7">sineEaseInOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:gaa46e3d5fbf7a15caa28eff9ef192d7c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after half sine wave.  <a href="a00927.html#gaa46e3d5fbf7a15caa28eff9ef192d7c7">More...</a><br /></td></tr>
<tr class="separator:gaa46e3d5fbf7a15caa28eff9ef192d7c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3e454f883afc1606ef91363881bf5a3"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gab3e454f883afc1606ef91363881bf5a3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00927.html#gab3e454f883afc1606ef91363881bf5a3">sineEaseOut</a> (genType const &amp;a)</td></tr>
<tr class="memdesc:gab3e454f883afc1606ef91363881bf5a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modelled after quarter-cycle of sine wave (different phase)  <a href="a00927.html#gab3e454f883afc1606ef91363881bf5a3">More...</a><br /></td></tr>
<tr class="separator:gab3e454f883afc1606ef91363881bf5a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00608.html" title="GLM_GTX_easing">glm/gtx/easing.hpp</a>&gt; to use the features of this extension.</p>
<p>Easing functions for animations and transitions All functions take a parameter x in the range [0.0,1.0]</p>
<p>Based on the AHEasing project of Warren Moore (<a href="https://github.com/warrenm/AHEasing">https://github.com/warrenm/AHEasing</a>) </p>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga93cddcdb6347a44d5927cc2bf2570816"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga93cddcdb6347a44d5927cc2bf2570816">&#9670;&nbsp;</a></span>backEaseIn() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::backEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga33777c9dd98f61d9472f96aafdf2bd36"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga33777c9dd98f61d9472f96aafdf2bd36">&#9670;&nbsp;</a></span>backEaseIn() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::backEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>o</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td>parameter </td></tr>
    <tr><td class="paramname">o</td><td>Optional overshoot modifier </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gace6d24722a2f6722b56398206eb810bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gace6d24722a2f6722b56398206eb810bb">&#9670;&nbsp;</a></span>backEaseInOut() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::backEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga68a7b760f2afdfab298d5cd6d7611fb1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga68a7b760f2afdfab298d5cd6d7611fb1">&#9670;&nbsp;</a></span>backEaseInOut() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::backEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>o</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td>parameter </td></tr>
    <tr><td class="paramname">o</td><td>Optional overshoot modifier </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gabf25069fa906413c858fd46903d520b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabf25069fa906413c858fd46903d520b9">&#9670;&nbsp;</a></span>backEaseOut() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::backEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga640c1ac6fe9d277a197da69daf60ee4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga640c1ac6fe9d277a197da69daf60ee4f">&#9670;&nbsp;</a></span>backEaseOut() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::backEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>o</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td>parameter </td></tr>
    <tr><td class="paramname">o</td><td>Optional overshoot modifier </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gaac30767f2e430b0c3fc859a4d59c7b5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaac30767f2e430b0c3fc859a4d59c7b5b">&#9670;&nbsp;</a></span>bounceEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::bounceEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gadf9f38eff1e5f4c2fa5b629a25ae413e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadf9f38eff1e5f4c2fa5b629a25ae413e">&#9670;&nbsp;</a></span>bounceEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::bounceEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga94007005ff0dcfa0749ebfa2aec540b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga94007005ff0dcfa0749ebfa2aec540b2">&#9670;&nbsp;</a></span>bounceEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::bounceEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga34508d4b204a321ec26d6086aa047997"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga34508d4b204a321ec26d6086aa047997">&#9670;&nbsp;</a></span>circularEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::circularEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after shifted quadrant IV of unit circle. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga0c1027637a5b02d4bb3612aa12599d69"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0c1027637a5b02d4bb3612aa12599d69">&#9670;&nbsp;</a></span>circularEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::circularEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise circular function y = (1/2)(1 - sqrt(1 - 4x^2)) ; [0, 0.5) y = (1/2)(sqrt(-(2x - 3)*(2x - 1)) + 1) ; [0.5, 1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga26fefde9ced9b72745fe21f1a3fe8da7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga26fefde9ced9b72745fe21f1a3fe8da7">&#9670;&nbsp;</a></span>circularEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::circularEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after shifted quadrant II of unit circle. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga55134072b42d75452189321d4a2ad91c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga55134072b42d75452189321d4a2ad91c">&#9670;&nbsp;</a></span>cubicEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::cubicEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise cubic y = (1/2)((2x)^3) ; [0, 0.5) y = (1/2)((2x-2)^3 + 2) ; [0.5, 1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga40d746385d8bcc5973f5bc6a2340ca91"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga40d746385d8bcc5973f5bc6a2340ca91">&#9670;&nbsp;</a></span>cubicEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::cubicEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the cubic y = (x - 1)^3 + 1. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga230918eccee4e113d10ec5b8cdc58695"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga230918eccee4e113d10ec5b8cdc58695">&#9670;&nbsp;</a></span>elasticEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::elasticEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the damped sine wave y = sin(13pi/2*x)*pow(2, 10 * (x - 1)) </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga2db4ac8959559b11b4029e54812908d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2db4ac8959559b11b4029e54812908d6">&#9670;&nbsp;</a></span>elasticEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::elasticEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise exponentially-damped sine wave: y = (1/2)*sin(13pi/2*(2*x))*pow(2, 10 * ((2*x) - 1)) ; [0,0.5) y = (1/2)*(sin(-13pi/2*((2x-1)+1))*pow(2,-10(2*x-1)) + 2) ; [0.5, 1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gace9c9d1bdf88bf2ab1e7cdefa54c7365"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gace9c9d1bdf88bf2ab1e7cdefa54c7365">&#9670;&nbsp;</a></span>elasticEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::elasticEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the damped sine wave y = sin(-13pi/2*(x + 1))*pow(2, -10x) + 1. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga7f24ee9219ab4c84dc8de24be84c1e3c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7f24ee9219ab4c84dc8de24be84c1e3c">&#9670;&nbsp;</a></span>exponentialEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::exponentialEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the exponential function y = 2^(10(x - 1)) </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga232fb6dc093c5ce94bee105ff2947501"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga232fb6dc093c5ce94bee105ff2947501">&#9670;&nbsp;</a></span>exponentialEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::exponentialEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise exponential y = (1/2)2^(10(2x - 1)) ; [0,0.5) y = -(1/2)*2^(-10(2x - 1))) + 1 ; [0.5,1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga517f2bcfd15bc2c25c466ae50808efc3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga517f2bcfd15bc2c25c466ae50808efc3">&#9670;&nbsp;</a></span>exponentialEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::exponentialEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the exponential function y = -2^(-10x) + 1. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga290c3e47cb0a49f2e8abe90b1872b649"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga290c3e47cb0a49f2e8abe90b1872b649">&#9670;&nbsp;</a></span>linearInterpolation()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::linearInterpolation </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the line y = x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gaf42089d35855695132d217cd902304a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf42089d35855695132d217cd902304a0">&#9670;&nbsp;</a></span>quadraticEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quadraticEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the parabola y = x^2. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga03e8fc2d7945a4e63ee33b2159c14cea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga03e8fc2d7945a4e63ee33b2159c14cea">&#9670;&nbsp;</a></span>quadraticEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quadraticEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise quadratic y = (1/2)((2x)^2) ; [0, 0.5) y = -(1/2)((2x-1)*(2x-3) - 1) ; [0.5, 1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga283717bc2d937547ad34ec0472234ee3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga283717bc2d937547ad34ec0472234ee3">&#9670;&nbsp;</a></span>quadraticEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quadraticEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the parabola y = -x^2 + 2x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga808b41f14514f47dad5dcc69eb924afd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga808b41f14514f47dad5dcc69eb924afd">&#9670;&nbsp;</a></span>quarticEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quarticEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the quartic x^4. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga6d000f852de12b197e154f234b20c505"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6d000f852de12b197e154f234b20c505">&#9670;&nbsp;</a></span>quarticEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quarticEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise quartic y = (1/2)((2x)^4) ; [0, 0.5) y = -(1/2)((2x-2)^4 - 2) ; [0.5, 1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga4dfb33fa7664aa888eb647999d329b98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4dfb33fa7664aa888eb647999d329b98">&#9670;&nbsp;</a></span>quarticEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quarticEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the quartic y = 1 - (x - 1)^4. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga097579d8e087dcf48037588140a21640"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga097579d8e087dcf48037588140a21640">&#9670;&nbsp;</a></span>quinticEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quinticEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the quintic y = x^5. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga2a82d5c46df7e2d21cc0108eb7b83934"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2a82d5c46df7e2d21cc0108eb7b83934">&#9670;&nbsp;</a></span>quinticEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quinticEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the piecewise quintic y = (1/2)((2x)^5) ; [0, 0.5) y = (1/2)((2x-2)^5 + 2) ; [0.5, 1]. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="ga7dbd4d5c8da3f5353121f615e7b591d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7dbd4d5c8da3f5353121f615e7b591d7">&#9670;&nbsp;</a></span>quinticEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::quinticEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after the quintic y = (x - 1)^5 + 1. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gafb338ac6f6b2bcafee50e3dca5201dbf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafb338ac6f6b2bcafee50e3dca5201dbf">&#9670;&nbsp;</a></span>sineEaseIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::sineEaseIn </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after quarter-cycle of sine wave. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gaa46e3d5fbf7a15caa28eff9ef192d7c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa46e3d5fbf7a15caa28eff9ef192d7c7">&#9670;&nbsp;</a></span>sineEaseInOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::sineEaseInOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after half sine wave. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
<a id="gab3e454f883afc1606ef91363881bf5a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab3e454f883afc1606ef91363881bf5a3">&#9670;&nbsp;</a></span>sineEaseOut()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::sineEaseOut </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>a</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modelled after quarter-cycle of sine wave (different phase) </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00927.html">GLM_GTX_easing</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
