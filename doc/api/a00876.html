<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_vector_int3_sized</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_vector_int3_sized<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gae9c90a867a6026b1f6eab00456f3fb8b"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, <a class="el" href="a00859.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00876.html#gae9c90a867a6026b1f6eab00456f3fb8b">i16vec3</a></td></tr>
<tr class="memdesc:gae9c90a867a6026b1f6eab00456f3fb8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">16 bit signed integer vector of 3 components type.  <a href="a00876.html#gae9c90a867a6026b1f6eab00456f3fb8b">More...</a><br /></td></tr>
<tr class="separator:gae9c90a867a6026b1f6eab00456f3fb8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f526b5cccef126a2ebcf9bdd890394e"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, <a class="el" href="a00859.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00876.html#ga7f526b5cccef126a2ebcf9bdd890394e">i32vec3</a></td></tr>
<tr class="memdesc:ga7f526b5cccef126a2ebcf9bdd890394e"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit signed integer vector of 3 components type.  <a href="a00876.html#ga7f526b5cccef126a2ebcf9bdd890394e">More...</a><br /></td></tr>
<tr class="separator:ga7f526b5cccef126a2ebcf9bdd890394e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga667948cfe6fb3d6606c750729ec49f77"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, <a class="el" href="a00859.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00876.html#ga667948cfe6fb3d6606c750729ec49f77">i64vec3</a></td></tr>
<tr class="memdesc:ga667948cfe6fb3d6606c750729ec49f77"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit signed integer vector of 3 components type.  <a href="a00876.html#ga667948cfe6fb3d6606c750729ec49f77">More...</a><br /></td></tr>
<tr class="separator:ga667948cfe6fb3d6606c750729ec49f77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a08d36cf7917cd19d081a603d0eae3e"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, <a class="el" href="a00859.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00876.html#ga5a08d36cf7917cd19d081a603d0eae3e">i8vec3</a></td></tr>
<tr class="memdesc:ga5a08d36cf7917cd19d081a603d0eae3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">8 bit signed integer vector of 3 components type.  <a href="a00876.html#ga5a08d36cf7917cd19d081a603d0eae3e">More...</a><br /></td></tr>
<tr class="separator:ga5a08d36cf7917cd19d081a603d0eae3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes sized signed integer vector of 3 components type.</p>
<p>Include &lt;<a class="el" href="a00470.html" title="GLM_EXT_vector_int3_sized">glm/ext/vector_int3_sized.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html">GLM_EXT_scalar_int_sized</a> </dd>
<dd>
<a class="el" href="a00885.html">GLM_EXT_vector_uint3_sized</a> </dd></dl>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="gae9c90a867a6026b1f6eab00456f3fb8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae9c90a867a6026b1f6eab00456f3fb8b">&#9670;&nbsp;</a></span>i16vec3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, <a class="el" href="a00913.html#ga3ab5fe184343d394fb6c2723c3ee3699">i16</a>, defaultp &gt; <a class="el" href="a00876.html#gae9c90a867a6026b1f6eab00456f3fb8b">i16vec3</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>16 bit signed integer vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00876.html">GLM_EXT_vector_int3_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00470_source.html#l00036">36</a> of file <a class="el" href="a00470_source.html">vector_int3_sized.hpp</a>.</p>

</div>
</div>
<a id="ga7f526b5cccef126a2ebcf9bdd890394e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7f526b5cccef126a2ebcf9bdd890394e">&#9670;&nbsp;</a></span>i32vec3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, <a class="el" href="a00913.html#ga96faea43ac5f875d2d3ffbf8d213e3eb">i32</a>, defaultp &gt; <a class="el" href="a00876.html#ga7f526b5cccef126a2ebcf9bdd890394e">i32vec3</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>32 bit signed integer vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00876.html">GLM_EXT_vector_int3_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00470_source.html#l00041">41</a> of file <a class="el" href="a00470_source.html">vector_int3_sized.hpp</a>.</p>

</div>
</div>
<a id="ga667948cfe6fb3d6606c750729ec49f77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga667948cfe6fb3d6606c750729ec49f77">&#9670;&nbsp;</a></span>i64vec3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, <a class="el" href="a00913.html#gadb997e409103d4da18abd837e636a496">i64</a>, defaultp &gt; <a class="el" href="a00876.html#ga667948cfe6fb3d6606c750729ec49f77">i64vec3</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>64 bit signed integer vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00876.html">GLM_EXT_vector_int3_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00470_source.html#l00046">46</a> of file <a class="el" href="a00470_source.html">vector_int3_sized.hpp</a>.</p>

</div>
</div>
<a id="ga5a08d36cf7917cd19d081a603d0eae3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5a08d36cf7917cd19d081a603d0eae3e">&#9670;&nbsp;</a></span>i8vec3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, <a class="el" href="a00913.html#ga302ec977b0c0c3ea245b6c9275495355">i8</a>, defaultp &gt; <a class="el" href="a00876.html#ga5a08d36cf7917cd19d081a603d0eae3e">i8vec3</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>8 bit signed integer vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00876.html">GLM_EXT_vector_int3_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00470_source.html#l00031">31</a> of file <a class="el" href="a00470_source.html">vector_int3_sized.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
