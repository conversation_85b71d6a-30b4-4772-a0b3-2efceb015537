<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: exponential.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">exponential.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00889.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00083_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga071566cadc7505455e611f2a0353f4d4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga071566cadc7505455e611f2a0353f4d4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#ga071566cadc7505455e611f2a0353f4d4">exp</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga071566cadc7505455e611f2a0353f4d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the natural exponentiation of v, i.e., e^v.  <a href="a00804.html#ga071566cadc7505455e611f2a0353f4d4">More...</a><br /></td></tr>
<tr class="separator:ga071566cadc7505455e611f2a0353f4d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#gaff17ace6b579a03bf223ed4d1ed2cd16">exp2</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 2 raised to the v power.  <a href="a00804.html#gaff17ace6b579a03bf223ed4d1ed2cd16">More...</a><br /></td></tr>
<tr class="separator:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a">inversesqrt</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the reciprocal of the positive square root of v.  <a href="a00804.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a">More...</a><br /></td></tr>
<tr class="separator:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#ga918c9f3fd086ce20e6760c903bd30fa9">log</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the natural logarithm of v, i.e., returns the value y which satisfies the equation x = e^y.  <a href="a00804.html#ga918c9f3fd086ce20e6760c903bd30fa9">More...</a><br /></td></tr>
<tr class="separator:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga82831c7d9cca777cebedfe03a19c8d75"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga82831c7d9cca777cebedfe03a19c8d75"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#ga82831c7d9cca777cebedfe03a19c8d75">log2</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga82831c7d9cca777cebedfe03a19c8d75"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the base 2 log of x, i.e., returns the value y, which satisfies the equation x = 2 ^ y.  <a href="a00804.html#ga82831c7d9cca777cebedfe03a19c8d75">More...</a><br /></td></tr>
<tr class="separator:ga82831c7d9cca777cebedfe03a19c8d75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2254981952d4f333b900a6bf5167a6c4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2254981952d4f333b900a6bf5167a6c4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#ga2254981952d4f333b900a6bf5167a6c4">pow</a> (vec&lt; L, T, Q &gt; const &amp;base, vec&lt; L, T, Q &gt; const &amp;exponent)</td></tr>
<tr class="memdesc:ga2254981952d4f333b900a6bf5167a6c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 'base' raised to the power 'exponent'.  <a href="a00804.html#ga2254981952d4f333b900a6bf5167a6c4">More...</a><br /></td></tr>
<tr class="separator:ga2254981952d4f333b900a6bf5167a6c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00804.html#gaa83e5f1648b7ccdf33b87c07c76cb77c">sqrt</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the positive square root of v.  <a href="a00804.html#gaa83e5f1648b7ccdf33b87c07c76cb77c">More...</a><br /></td></tr>
<tr class="separator:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00889.html">Core features</a> </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

<p class="definition">Definition in file <a class="el" href="a00083_source.html">exponential.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
