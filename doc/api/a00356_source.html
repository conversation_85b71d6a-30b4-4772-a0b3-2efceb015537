<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: scalar_constants.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">scalar_constants.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00356.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_scalar_constants extension included&quot;)</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;{</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00027"></a><span class="lineno"><a class="line" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">   27</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>();</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="a00858.html#ga94bafeb2a0f23ab6450fed1f98ee4e45">   31</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00858.html#ga94bafeb2a0f23ab6450fed1f98ee4e45">pi</a>();</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="a00858.html#gae8d1938913da2e5b2e102b9076cd0389">   35</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00858.html#gae8d1938913da2e5b2e102b9076cd0389">cos_one_over_two</a>();</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;scalar_constants.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00858_html_ga2a1e57fc5592b69cfae84174cbfc9429"><div class="ttname"><a href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">glm::epsilon</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType epsilon()</div><div class="ttdoc">Return the epsilon constant for floating point types.</div></div>
<div class="ttc" id="aa00858_html_ga94bafeb2a0f23ab6450fed1f98ee4e45"><div class="ttname"><a href="a00858.html#ga94bafeb2a0f23ab6450fed1f98ee4e45">glm::pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType pi()</div><div class="ttdoc">Return the pi constant for floating point types.</div></div>
<div class="ttc" id="aa00858_html_gae8d1938913da2e5b2e102b9076cd0389"><div class="ttname"><a href="a00858.html#gae8d1938913da2e5b2e102b9076cd0389">glm::cos_one_over_two</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType cos_one_over_two()</div><div class="ttdoc">Return the value of cos(1 / 2) for floating point types.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
