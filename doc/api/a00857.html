<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_scalar_common</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_scalar_common<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga6c0cc6bd1d67ea1008d2592e998bad33"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga6c0cc6bd1d67ea1008d2592e998bad33"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga6c0cc6bd1d67ea1008d2592e998bad33">clamp</a> (genType const &amp;Texcoord)</td></tr>
<tr class="memdesc:ga6c0cc6bd1d67ea1008d2592e998bad33"><td class="mdescLeft">&#160;</td><td class="mdescRight">Simulate GL_CLAMP OpenGL wrap mode.  <a href="a00857.html#ga6c0cc6bd1d67ea1008d2592e998bad33">More...</a><br /></td></tr>
<tr class="separator:ga6c0cc6bd1d67ea1008d2592e998bad33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga1e28539d3a46965ed9ef92ec7cb3b18a">fclamp</a> (genType x, genType minVal, genType maxVal)</td></tr>
<tr class="memdesc:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x.  <a href="a00857.html#ga1e28539d3a46965ed9ef92ec7cb3b18a">More...</a><br /></td></tr>
<tr class="separator:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga36920478565cf608e93064283ce06421"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga36920478565cf608e93064283ce06421"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga36920478565cf608e93064283ce06421">fmax</a> (T a, T b)</td></tr>
<tr class="memdesc:ga36920478565cf608e93064283ce06421"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 2 inputs.  <a href="a00857.html#ga36920478565cf608e93064283ce06421">More...</a><br /></td></tr>
<tr class="separator:ga36920478565cf608e93064283ce06421"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga0007bba71ca451ac70e99d28dfbeaab9">fmax</a> (T a, T b, T C)</td></tr>
<tr class="memdesc:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 3 inputs.  <a href="a00857.html#ga0007bba71ca451ac70e99d28dfbeaab9">More...</a><br /></td></tr>
<tr class="separator:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">fmax</a> (T a, T b, T C, T D)</td></tr>
<tr class="memdesc:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 4 inputs.  <a href="a00857.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">More...</a><br /></td></tr>
<tr class="separator:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b2b438a765e2a62098c79eb212f28f0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga7b2b438a765e2a62098c79eb212f28f0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga7b2b438a765e2a62098c79eb212f28f0">fmin</a> (T a, T b)</td></tr>
<tr class="memdesc:ga7b2b438a765e2a62098c79eb212f28f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 2 inputs.  <a href="a00857.html#ga7b2b438a765e2a62098c79eb212f28f0">More...</a><br /></td></tr>
<tr class="separator:ga7b2b438a765e2a62098c79eb212f28f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga1a95fe4cf5437e8133f1093fe9726a64">fmin</a> (T a, T b, T c)</td></tr>
<tr class="memdesc:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 3 inputs.  <a href="a00857.html#ga1a95fe4cf5437e8133f1093fe9726a64">More...</a><br /></td></tr>
<tr class="separator:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">fmin</a> (T a, T b, T c, T d)</td></tr>
<tr class="memdesc:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 4 inputs.  <a href="a00857.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">More...</a><br /></td></tr>
<tr class="separator:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa45ff5782fde5182afa7ab211f9758a3"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaa45ff5782fde5182afa7ab211f9758a3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#gaa45ff5782fde5182afa7ab211f9758a3">iround</a> (genType const &amp;x)</td></tr>
<tr class="memdesc:gaa45ff5782fde5182afa7ab211f9758a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x.  <a href="a00857.html#gaa45ff5782fde5182afa7ab211f9758a3">More...</a><br /></td></tr>
<tr class="separator:gaa45ff5782fde5182afa7ab211f9758a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa20839d9ab14514f8966f69877ea0de8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa20839d9ab14514f8966f69877ea0de8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#gaa20839d9ab14514f8966f69877ea0de8">max</a> (T a, T b, T c)</td></tr>
<tr class="memdesc:gaa20839d9ab14514f8966f69877ea0de8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 3 inputs.  <a href="a00857.html#gaa20839d9ab14514f8966f69877ea0de8">More...</a><br /></td></tr>
<tr class="separator:gaa20839d9ab14514f8966f69877ea0de8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">max</a> (T a, T b, T c, T d)</td></tr>
<tr class="memdesc:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 4 inputs.  <a href="a00857.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">More...</a><br /></td></tr>
<tr class="separator:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga420b37cbd98c395b93dab0278305cd46"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga420b37cbd98c395b93dab0278305cd46"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga420b37cbd98c395b93dab0278305cd46">min</a> (T a, T b, T c)</td></tr>
<tr class="memdesc:ga420b37cbd98c395b93dab0278305cd46"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 3 inputs.  <a href="a00857.html#ga420b37cbd98c395b93dab0278305cd46">More...</a><br /></td></tr>
<tr class="separator:ga420b37cbd98c395b93dab0278305cd46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga0d24a9acb8178df77e4aff90cbb2010d">min</a> (T a, T b, T c, T d)</td></tr>
<tr class="memdesc:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 4 inputs.  <a href="a00857.html#ga0d24a9acb8178df77e4aff90cbb2010d">More...</a><br /></td></tr>
<tr class="separator:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa6856a0a048d2749252848da35e10c8b"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaa6856a0a048d2749252848da35e10c8b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#gaa6856a0a048d2749252848da35e10c8b">mirrorClamp</a> (genType const &amp;Texcoord)</td></tr>
<tr class="memdesc:gaa6856a0a048d2749252848da35e10c8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Simulate GL_MIRRORED_REPEAT OpenGL wrap mode.  <a href="a00857.html#gaa6856a0a048d2749252848da35e10c8b">More...</a><br /></td></tr>
<tr class="separator:gaa6856a0a048d2749252848da35e10c8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16a89b0661b60d5bea85137bbae74d73"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga16a89b0661b60d5bea85137bbae74d73"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga16a89b0661b60d5bea85137bbae74d73">mirrorRepeat</a> (genType const &amp;Texcoord)</td></tr>
<tr class="memdesc:ga16a89b0661b60d5bea85137bbae74d73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Simulate GL_MIRROR_REPEAT OpenGL wrap mode.  <a href="a00857.html#ga16a89b0661b60d5bea85137bbae74d73">More...</a><br /></td></tr>
<tr class="separator:ga16a89b0661b60d5bea85137bbae74d73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga809650c6310ea7c42666e918c117fb6f"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga809650c6310ea7c42666e918c117fb6f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga809650c6310ea7c42666e918c117fb6f">repeat</a> (genType const &amp;Texcoord)</td></tr>
<tr class="memdesc:ga809650c6310ea7c42666e918c117fb6f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Simulate GL_REPEAT OpenGL wrap mode.  <a href="a00857.html#ga809650c6310ea7c42666e918c117fb6f">More...</a><br /></td></tr>
<tr class="separator:ga809650c6310ea7c42666e918c117fb6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d915647ec33c4110946665f914d3abb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9d915647ec33c4110946665f914d3abb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00857.html#ga9d915647ec33c4110946665f914d3abb">uround</a> (genType const &amp;x)</td></tr>
<tr class="memdesc:ga9d915647ec33c4110946665f914d3abb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x.  <a href="a00857.html#ga9d915647ec33c4110946665f914d3abb">More...</a><br /></td></tr>
<tr class="separator:ga9d915647ec33c4110946665f914d3abb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes min and max functions for 3 to 4 scalar parameters.</p>
<p>Include &lt;<a class="el" href="a00353.html" title="GLM_EXT_scalar_common">glm/ext/scalar_common.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00803.html">Common functions</a> </dd>
<dd>
<a class="el" href="a00868.html">GLM_EXT_vector_common</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga6c0cc6bd1d67ea1008d2592e998bad33"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6c0cc6bd1d67ea1008d2592e998bad33">&#9670;&nbsp;</a></span>clamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::clamp </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>Texcoord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Simulate GL_CLAMP OpenGL wrap mode. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> extension. </dd></dl>

</div>
</div>
<a id="ga1e28539d3a46965ed9ef92ec7cb3b18a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1e28539d3a46965ed9ef92ec7cb3b18a">&#9670;&nbsp;</a></span>fclamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::fclamp </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>minVal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>maxVal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns min(max(x, minVal), maxVal) for each component in x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga36920478565cf608e93064283ce06421"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga36920478565cf608e93064283ce06421">&#9670;&nbsp;</a></span>fmax() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fmax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the maximum component-wise values of 2 inputs. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga0007bba71ca451ac70e99d28dfbeaab9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0007bba71ca451ac70e99d28dfbeaab9">&#9670;&nbsp;</a></span>fmax() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fmax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>C</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the maximum component-wise values of 3 inputs. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga27e260b1ff4d04c3ad4b864d26cbaf08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga27e260b1ff4d04c3ad4b864d26cbaf08">&#9670;&nbsp;</a></span>fmax() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fmax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>C</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>D</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the maximum component-wise values of 4 inputs. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga7b2b438a765e2a62098c79eb212f28f0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7b2b438a765e2a62098c79eb212f28f0">&#9670;&nbsp;</a></span>fmin() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fmin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the minimum component-wise values of 2 inputs. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga1a95fe4cf5437e8133f1093fe9726a64"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1a95fe4cf5437e8133f1093fe9726a64">&#9670;&nbsp;</a></span>fmin() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fmin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the minimum component-wise values of 3 inputs. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga3d6f9c6c16bfd6f38f2c4f8076e8b661"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">&#9670;&nbsp;</a></span>fmin() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fmin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the minimum component-wise values of 4 inputs. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="gaa45ff5782fde5182afa7ab211f9758a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa45ff5782fde5182afa7ab211f9758a3">&#9670;&nbsp;</a></span>iround()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::iround </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a value equal to the nearest integer to x. </p>
<p>The fraction 0.5 will round in a direction chosen by the implementation, presumably the direction that is fastest.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>The values of the argument must be greater or equal to zero. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>floating point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/round.xml">GLSL round man page</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> extension. </dd></dl>

</div>
</div>
<a id="gaa20839d9ab14514f8966f69877ea0de8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa20839d9ab14514f8966f69877ea0de8">&#9670;&nbsp;</a></span>max() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::max </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the maximum component-wise values of 3 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga2274b5e75ed84b0b1e50d8d22f1f2f67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2274b5e75ed84b0b1e50d8d22f1f2f67">&#9670;&nbsp;</a></span>max() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::max </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the maximum component-wise values of 4 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga420b37cbd98c395b93dab0278305cd46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga420b37cbd98c395b93dab0278305cd46">&#9670;&nbsp;</a></span>min() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::min </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the minimum component-wise values of 3 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="ga0d24a9acb8178df77e4aff90cbb2010d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0d24a9acb8178df77e4aff90cbb2010d">&#9670;&nbsp;</a></span>min() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::min </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the minimum component-wise values of 4 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> </dd></dl>

</div>
</div>
<a id="gaa6856a0a048d2749252848da35e10c8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa6856a0a048d2749252848da35e10c8b">&#9670;&nbsp;</a></span>mirrorClamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::mirrorClamp </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>Texcoord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Simulate GL_MIRRORED_REPEAT OpenGL wrap mode. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> extension. </dd></dl>

</div>
</div>
<a id="ga16a89b0661b60d5bea85137bbae74d73"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga16a89b0661b60d5bea85137bbae74d73">&#9670;&nbsp;</a></span>mirrorRepeat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::mirrorRepeat </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>Texcoord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Simulate GL_MIRROR_REPEAT OpenGL wrap mode. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> extension. </dd></dl>

</div>
</div>
<a id="ga809650c6310ea7c42666e918c117fb6f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga809650c6310ea7c42666e918c117fb6f">&#9670;&nbsp;</a></span>repeat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::repeat </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>Texcoord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Simulate GL_REPEAT OpenGL wrap mode. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00857.html">GLM_EXT_scalar_common</a> extension. </dd></dl>

</div>
</div>
<a id="ga9d915647ec33c4110946665f914d3abb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9d915647ec33c4110946665f914d3abb">&#9670;&nbsp;</a></span>uround()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::uround </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a value equal to the nearest integer to x. </p>
<p>The fraction 0.5 will round in a direction chosen by the implementation, presumably the direction that is fastest.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>The values of the argument must be greater or equal to zero. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>floating point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/round.xml">GLSL round man page</a> </dd>
<dd>
<a class="el" href="a00857.html">GLM_EXT_scalar_common</a> extension. </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
