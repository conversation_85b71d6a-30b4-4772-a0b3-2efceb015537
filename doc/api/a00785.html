<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">matrix.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00889.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00785_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga07f545826ec7726ca072e587246afdde"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga07f545826ec7726ca072e587246afdde"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00825.html#ga07f545826ec7726ca072e587246afdde">determinant</a> (mat&lt; C, R, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:ga07f545826ec7726ca072e587246afdde"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the determinant of a squared matrix.  <a href="a00825.html#ga07f545826ec7726ca072e587246afdde">More...</a><br /></td></tr>
<tr class="separator:ga07f545826ec7726ca072e587246afdde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed509fe8129b01e4f20a6d0de5690091"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaed509fe8129b01e4f20a6d0de5690091"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00982.html#gaed509fe8129b01e4f20a6d0de5690091">inverse</a> (mat&lt; C, R, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gaed509fe8129b01e4f20a6d0de5690091"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the inverse of a squared matrix.  <a href="a00982.html#gaed509fe8129b01e4f20a6d0de5690091">More...</a><br /></td></tr>
<tr class="separator:gaed509fe8129b01e4f20a6d0de5690091"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga514c5f14f88b22355731a992e683fc90"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga514c5f14f88b22355731a992e683fc90"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00825.html#ga514c5f14f88b22355731a992e683fc90">matrixCompMult</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga514c5f14f88b22355731a992e683fc90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiply matrix x by matrix y component-wise, i.e., result[i][j] is the scalar product of x[i][j] and y[i][j].  <a href="a00825.html#ga514c5f14f88b22355731a992e683fc90">More...</a><br /></td></tr>
<tr class="separator:ga514c5f14f88b22355731a992e683fc90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga80d31e9320fd77035336e01d0228321f"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga80d31e9320fd77035336e01d0228321f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL detail::outerProduct_trait&lt; C, R, T, Q &gt;::type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00825.html#ga80d31e9320fd77035336e01d0228321f">outerProduct</a> (vec&lt; C, T, Q &gt; const &amp;c, vec&lt; R, T, Q &gt; const &amp;r)</td></tr>
<tr class="memdesc:ga80d31e9320fd77035336e01d0228321f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Treats the first parameter c as a column vector and the second parameter r as a row vector and does a linear algebraic matrix multiply c * r.  <a href="a00825.html#ga80d31e9320fd77035336e01d0228321f">More...</a><br /></td></tr>
<tr class="separator:ga80d31e9320fd77035336e01d0228321f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4bedcb9c511484f38fd30a60c95f4679"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4bedcb9c511484f38fd30a60c95f4679"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;::transpose_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00825.html#ga4bedcb9c511484f38fd30a60c95f4679">transpose</a> (mat&lt; C, R, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga4bedcb9c511484f38fd30a60c95f4679"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the transposed matrix of x.  <a href="a00825.html#ga4bedcb9c511484f38fd30a60c95f4679">More...</a><br /></td></tr>
<tr class="separator:ga4bedcb9c511484f38fd30a60c95f4679"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00889.html">Core features</a> </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

<p class="definition">Definition in file <a class="el" href="a00785_source.html">matrix.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
