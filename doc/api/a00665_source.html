<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_major_storage.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_major_storage.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00665.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_matrix_major_storage is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_matrix_major_storage extension included&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160; </div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;{</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00033"></a><span class="lineno"><a class="line" href="a00947.html#gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8">   33</a></span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; <a class="code" href="a00947.html#gaf66c75ed69ca9e87462550708c2c6726">rowMajor2</a>(</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v1,</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="a00947.html#gaf66c75ed69ca9e87462550708c2c6726">   40</a></span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; <a class="code" href="a00947.html#gaf66c75ed69ca9e87462550708c2c6726">rowMajor2</a>(</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                mat&lt;2, 2, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00947.html#ga2ae46497493339f745754e40f438442e">   46</a></span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00947.html#gad8a3a50ab47bbe8d36cdb81d90dfcf77">rowMajor3</a>(</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1,</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2,</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v3);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="a00947.html#gad8a3a50ab47bbe8d36cdb81d90dfcf77">   54</a></span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00947.html#gad8a3a50ab47bbe8d36cdb81d90dfcf77">rowMajor3</a>(</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="a00947.html#ga9636cd6bbe2c32a8d0c03ffb8b1ef284">   60</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00947.html#gac92ad1c2acdf18d3eb7be45a32f9566b">rowMajor4</a>(</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v1,</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v2,</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v3,</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v4);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160; </div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="a00947.html#gac92ad1c2acdf18d3eb7be45a32f9566b">   69</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00947.html#gac92ad1c2acdf18d3eb7be45a32f9566b">rowMajor4</a>(</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160; </div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="a00947.html#gaaff72f11286e59a4a88ed21a347f284c">   75</a></span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; <a class="code" href="a00947.html#gafc25fd44196c92b1397b127aec1281ab">colMajor2</a>(</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v1,</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="a00947.html#gafc25fd44196c92b1397b127aec1281ab">   82</a></span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; <a class="code" href="a00947.html#gafc25fd44196c92b1397b127aec1281ab">colMajor2</a>(</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                mat&lt;2, 2, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="a00947.html#ga1e25b72b085087740c92f5c70f3b051f">   88</a></span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00947.html#ga86bd0656e787bb7f217607572590af27">colMajor3</a>(</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1,</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2,</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v3);</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="a00947.html#ga86bd0656e787bb7f217607572590af27">   96</a></span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00947.html#ga86bd0656e787bb7f217607572590af27">colMajor3</a>(</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="a00947.html#gaf4aa6c7e17bfce41a6c13bf6469fab05">  102</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00947.html#gaf3f9511c366c20ba2e4a64c9e4cec2b3">colMajor4</a>(</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v1,</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v2,</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v3,</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v4);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="a00947.html#gaf3f9511c366c20ba2e4a64c9e4cec2b3">  111</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00947.html#gaf3f9511c366c20ba2e4a64c9e4cec2b3">colMajor4</a>(</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="preprocessor">#include &quot;matrix_major_storage.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00947_html_gaf66c75ed69ca9e87462550708c2c6726"><div class="ttname"><a href="a00947.html#gaf66c75ed69ca9e87462550708c2c6726">glm::rowMajor2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt; rowMajor2(mat&lt; 2, 2, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build a row major matrix from other matrix.</div></div>
<div class="ttc" id="aa00947_html_ga86bd0656e787bb7f217607572590af27"><div class="ttname"><a href="a00947.html#ga86bd0656e787bb7f217607572590af27">glm::colMajor3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt; colMajor3(mat&lt; 3, 3, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build a column major matrix from other matrix.</div></div>
<div class="ttc" id="aa00947_html_gad8a3a50ab47bbe8d36cdb81d90dfcf77"><div class="ttname"><a href="a00947.html#gad8a3a50ab47bbe8d36cdb81d90dfcf77">glm::rowMajor3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt; rowMajor3(mat&lt; 3, 3, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build a row major matrix from other matrix.</div></div>
<div class="ttc" id="aa00947_html_gac92ad1c2acdf18d3eb7be45a32f9566b"><div class="ttname"><a href="a00947.html#gac92ad1c2acdf18d3eb7be45a32f9566b">glm::rowMajor4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; rowMajor4(mat&lt; 4, 4, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build a row major matrix from other matrix.</div></div>
<div class="ttc" id="aa00947_html_gaf3f9511c366c20ba2e4a64c9e4cec2b3"><div class="ttname"><a href="a00947.html#gaf3f9511c366c20ba2e4a64c9e4cec2b3">glm::colMajor4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; colMajor4(mat&lt; 4, 4, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build a column major matrix from other matrix.</div></div>
<div class="ttc" id="aa00947_html_gafc25fd44196c92b1397b127aec1281ab"><div class="ttname"><a href="a00947.html#gafc25fd44196c92b1397b127aec1281ab">glm::colMajor2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt; colMajor2(mat&lt; 2, 2, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build a column major matrix from other matrix.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
