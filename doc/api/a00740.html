<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: transform2.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">transform2.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00974.html">GLM_GTX_transform2</a>  
<a href="#details">More...</a></p>

<p><a href="a00740_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga5b992a0cdc8298054edb68e228f0d93e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5b992a0cdc8298054edb68e228f0d93e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#ga5b992a0cdc8298054edb68e228f0d93e">proj2D</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;normal)</td></tr>
<tr class="memdesc:ga5b992a0cdc8298054edb68e228f0d93e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build planar projection matrix along normal axis.  <a href="a00974.html#ga5b992a0cdc8298054edb68e228f0d93e">More...</a><br /></td></tr>
<tr class="separator:ga5b992a0cdc8298054edb68e228f0d93e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa2b7f4f15b98f697caede11bef50509e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa2b7f4f15b98f697caede11bef50509e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#gaa2b7f4f15b98f697caede11bef50509e">proj3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;normal)</td></tr>
<tr class="memdesc:gaa2b7f4f15b98f697caede11bef50509e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build planar projection matrix along normal axis.  <a href="a00974.html#gaa2b7f4f15b98f697caede11bef50509e">More...</a><br /></td></tr>
<tr class="separator:gaa2b7f4f15b98f697caede11bef50509e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#gae2bdd91a76759fecfbaef97e3020aa8e">scaleBias</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T scale, T bias)</td></tr>
<tr class="memdesc:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a scale bias matrix.  <a href="a00974.html#gae2bdd91a76759fecfbaef97e3020aa8e">More...</a><br /></td></tr>
<tr class="separator:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf249498b236e62c983d90d30d63c99c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabf249498b236e62c983d90d30d63c99c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#gabf249498b236e62c983d90d30d63c99c">scaleBias</a> (T scale, T bias)</td></tr>
<tr class="memdesc:gabf249498b236e62c983d90d30d63c99c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a scale bias matrix.  <a href="a00974.html#gabf249498b236e62c983d90d30d63c99c">More...</a><br /></td></tr>
<tr class="separator:gabf249498b236e62c983d90d30d63c99c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf714b8a358181572b32a45555f71948"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabf714b8a358181572b32a45555f71948"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#gabf714b8a358181572b32a45555f71948">shearX2D</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T y)</td></tr>
<tr class="memdesc:gabf714b8a358181572b32a45555f71948"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on X axis.  <a href="a00974.html#gabf714b8a358181572b32a45555f71948">More...</a><br /></td></tr>
<tr class="separator:gabf714b8a358181572b32a45555f71948"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73e867c6cd4d700fe2054437e56106c4"><td class="memTemplParams" colspan="2">
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga73e867c6cd4d700fe2054437e56106c4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#ga73e867c6cd4d700fe2054437e56106c4">shearX3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T y, T z)</td></tr>
<tr class="memdesc:ga73e867c6cd4d700fe2054437e56106c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on X axis From GLM_GTX_transform2 extension. <br /></td></tr>
<tr class="separator:ga73e867c6cd4d700fe2054437e56106c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7998d0763d9181550c77e8af09a182c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac7998d0763d9181550c77e8af09a182c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#gac7998d0763d9181550c77e8af09a182c">shearY2D</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T x)</td></tr>
<tr class="memdesc:gac7998d0763d9181550c77e8af09a182c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on Y axis.  <a href="a00974.html#gac7998d0763d9181550c77e8af09a182c">More...</a><br /></td></tr>
<tr class="separator:gac7998d0763d9181550c77e8af09a182c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade5bb65ffcb513973db1a1314fb5cfac"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gade5bb65ffcb513973db1a1314fb5cfac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#gade5bb65ffcb513973db1a1314fb5cfac">shearY3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T x, T z)</td></tr>
<tr class="memdesc:gade5bb65ffcb513973db1a1314fb5cfac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on Y axis.  <a href="a00974.html#gade5bb65ffcb513973db1a1314fb5cfac">More...</a><br /></td></tr>
<tr class="separator:gade5bb65ffcb513973db1a1314fb5cfac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00974.html#ga6591e0a3a9d2c9c0b6577bb4dace0255">shearZ3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T x, T y)</td></tr>
<tr class="memdesc:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on Z axis.  <a href="a00974.html#ga6591e0a3a9d2c9c0b6577bb4dace0255">More...</a><br /></td></tr>
<tr class="separator:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00974.html">GLM_GTX_transform2</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00889.html" title="Features that implement in C++ the GLSL specification as closely as possible.">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00973.html">GLM_GTX_transform</a> (dependence) </dd></dl>

<p class="definition">Definition in file <a class="el" href="a00740_source.html">transform2.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
