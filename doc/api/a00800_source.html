<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: man.doxy Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_b11711034def6a4ce452fe9c451dd3d0.html">doc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">man.doxy</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor"># Doxyfile 1.8.10</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160; </div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="preprocessor"># This file describes the settings to be used by the documentation system</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor"># doxygen (www.doxygen.org) for a project.</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor"># All text after a double hash (##) is considered a comment and is placed in</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor"># front of the TAG it is preceding.</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor"># All text after a single hash (#) is considered a comment and will be ignored.</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor"># The format is:</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor"># TAG = value [value, ...]</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor"># For lists, items can also be appended using:</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor"># TAG += value [value, ...]</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor"># Values that contain spaces should be placed between quotes (\&quot; \&quot;).</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor"># Project related configuration options</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor"># This tag specifies the encoding used for all characters in the config file</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor"># that follow. The default is UTF-8 which is also the encoding used for all text</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor"># before the first occurrence of this tag. Doxygen uses libiconv (or the iconv</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor"># built into libc) for the transcoding. See http://www.gnu.org/software/libiconv</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor"># for the list of possible encodings.</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor"># The default value is: UTF-8.</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;DOXYFILE_ENCODING      = UTF-8</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor"># The PROJECT_NAME tag is a single word (or a sequence of words surrounded by</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor"># double-quotes, unless you are using Doxywizard) that should identify the</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor"># project for which the documentation is generated. This name is used in the</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor"># title of most generated pages and in a few other places.</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor"># The default value is: My Project.</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;PROJECT_NAME           = <span class="stringliteral">&quot;0.9.9 API documentation&quot;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor"># The PROJECT_NUMBER tag can be used to enter a project or revision number. This</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor"># could be handy for archiving the generated documentation or if some version</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor"># control system is used.</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;PROJECT_NUMBER         = </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor"># Using the PROJECT_BRIEF tag one can provide an optional one line description</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor"># for a project that appears at the top of each page and should give viewer a</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor"># quick idea about the purpose of the project. Keep the description short.</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;PROJECT_BRIEF          = </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor"># With the PROJECT_LOGO tag one can specify a logo or an icon that is included</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor"># in the documentation. The maximum height of the logo should not exceed 55</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor"># pixels and the maximum width should not exceed 200 pixels. Doxygen will copy</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="preprocessor"># the logo to the output directory.</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;PROJECT_LOGO           = theme/logo-mini.png</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor"># The OUTPUT_DIRECTORY tag is used to specify the (relative or absolute) path</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor"># into which the generated documentation will be written. If a relative path is</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor"># entered, it will be relative to the location where doxygen was started. If</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor"># left blank the current directory will be used.</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;OUTPUT_DIRECTORY       = .</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;# If the CREATE_SUBDIRS tag is set to YES then doxygen will create 4096 sub-</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="preprocessor"># directories (in 2 levels) under the output directory of each output format and</span></div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor"># will distribute the generated files over these directories. Enabling this</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="preprocessor"># option can be useful when feeding doxygen a huge amount of source files, where</span></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor"># putting all generated files in the same directory would otherwise causes</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor"># performance problems for the file system.</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;CREATE_SUBDIRS         = NO</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="preprocessor"># If the ALLOW_UNICODE_NAMES tag is set to YES, doxygen will allow non-ASCII</span></div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="preprocessor"># characters to appear in the names of generated files. If set to NO, non-ASCII</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="preprocessor"># characters will be escaped, for example _xE3_x81_x84 will be used for Unicode</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="preprocessor"># U+3044.</span></div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;ALLOW_UNICODE_NAMES    = NO</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor"># The OUTPUT_LANGUAGE tag is used to specify the language in which all</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor"># documentation generated by doxygen is written. Doxygen will use this</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor"># information to generate all constant output in the proper language.</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="preprocessor"># Possible values are: Afrikaans, Arabic, Armenian, Brazilian, Catalan, Chinese,</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="preprocessor"># Chinese-Traditional, Croatian, Czech, Danish, Dutch, English (United States),</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="preprocessor"># Esperanto, Farsi (Persian), Finnish, French, German, Greek, Hungarian,</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor"># Indonesian, Italian, Japanese, Japanese-en (Japanese with English messages),</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor"># Korean, Korean-en (Korean with English messages), Latvian, Lithuanian,</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor"># Macedonian, Norwegian, Persian (Farsi), Polish, Portuguese, Romanian, Russian,</span></div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="preprocessor"># Serbian, Serbian-Cyrillic, Slovak, Slovene, Spanish, Swedish, Turkish,</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="preprocessor"># Ukrainian and Vietnamese.</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="preprocessor"># The default value is: English.</span></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;OUTPUT_LANGUAGE        = English</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor"># If the BRIEF_MEMBER_DESC tag is set to YES, doxygen will include brief member</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor"># descriptions after the members that are listed in the file and class</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="preprocessor"># documentation (similar to Javadoc). Set to NO to disable this.</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160; </div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;BRIEF_MEMBER_DESC      = YES</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; </div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor"># If the REPEAT_BRIEF tag is set to YES, doxygen will prepend the brief</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="preprocessor"># description of a member or function before the detailed description</span></div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor"># Note: If both HIDE_UNDOC_MEMBERS and BRIEF_MEMBER_DESC are set to NO, the</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor"># brief descriptions will be completely suppressed.</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;REPEAT_BRIEF           = YES</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="preprocessor"># This tag implements a quasi-intelligent brief description abbreviator that is</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor"># used to form the text in various listings. Each string in this list, if found</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor"># as the leading text of the brief description, will be stripped from the text</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor"># and the result, after processing the whole list, is used as the annotated</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="preprocessor"># text. Otherwise, the brief description is used as-is. If left blank, the</span></div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="preprocessor"># following values are used ($name is automatically replaced with the name of</span></div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor"># the entity):The $name class, The $name widget, The $name file, is, provides,</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="preprocessor"># specifies, contains, represents, a, an and the.</span></div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;ABBREVIATE_BRIEF       = <span class="stringliteral">&quot;The $name class       &quot;</span> \</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;                         <span class="stringliteral">&quot;The $name widget       &quot;</span> \</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                         <span class="stringliteral">&quot;The $name file       &quot;</span> \</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                         is \</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                         provides \</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                         specifies \</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                         contains \</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                         represents \</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;                         a \</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;                         an \</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                         the</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor"># If the ALWAYS_DETAILED_SEC and REPEAT_BRIEF tags are both set to YES then</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="preprocessor"># doxygen will generate a detailed section even if there is only a brief</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="preprocessor"># description.</span></div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;ALWAYS_DETAILED_SEC    = NO</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="preprocessor"># If the INLINE_INHERITED_MEMB tag is set to YES, doxygen will show all</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="preprocessor"># inherited members of a class in the documentation of that class as if those</span></div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="preprocessor"># members were ordinary class members. Constructors, destructors and assignment</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="preprocessor"># operators of the base classes will not be shown.</span></div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;INLINE_INHERITED_MEMB  = NO</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="preprocessor"># If the FULL_PATH_NAMES tag is set to YES, doxygen will prepend the full path</span></div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="preprocessor"># before files name in the file list and in the header files. If set to NO the</span></div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="preprocessor"># shortest path that makes the file name unique will be used</span></div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;FULL_PATH_NAMES        = NO</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="preprocessor"># The STRIP_FROM_PATH tag can be used to strip a user-defined part of the path.</span></div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;<span class="preprocessor"># Stripping is only done if one of the specified strings matches the left-hand</span></div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;<span class="preprocessor"># part of the path. The tag can be used to show relative paths in the file list.</span></div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<span class="preprocessor"># If left blank the directory from which doxygen is run is used as the path to</span></div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;<span class="preprocessor"># strip.</span></div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="preprocessor"># Note that you can specify absolute paths here, but also relative paths, which</span></div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="preprocessor"># will be relative from the directory where doxygen is started.</span></div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;<span class="preprocessor"># This tag requires that the tag FULL_PATH_NAMES is set to YES.</span></div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160; </div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;STRIP_FROM_PATH        = <span class="stringliteral">&quot;C:/Documents and Settings/Groove/       &quot;</span></div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160; </div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="preprocessor"># The STRIP_FROM_INC_PATH tag can be used to strip a user-defined part of the</span></div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;<span class="preprocessor"># path mentioned in the documentation of a class, which tells the reader which</span></div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;<span class="preprocessor"># header file to include in order to use a class. If left blank only the name of</span></div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;<span class="preprocessor"># the header file containing the class definition is used. Otherwise one should</span></div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;<span class="preprocessor"># specify the list of include paths that are normally passed to the compiler</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="preprocessor"># using the -I flag.</span></div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160; </div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;STRIP_FROM_INC_PATH    = </div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160; </div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="preprocessor"># If the SHORT_NAMES tag is set to YES, doxygen will generate much shorter (but</span></div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;<span class="preprocessor"># less readable) file names. This can be useful is your file systems doesn&#39;t</span></div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;<span class="preprocessor"># support long names like on DOS, Mac, or CD-ROM.</span></div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160; </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;SHORT_NAMES            = YES</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160; </div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="preprocessor"># If the JAVADOC_AUTOBRIEF tag is set to YES then doxygen will interpret the</span></div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="preprocessor"># first line (until the first dot) of a Javadoc-style comment as the brief</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="preprocessor"># description. If set to NO, the Javadoc-style will behave just like regular Qt-</span></div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="preprocessor"># style comments (thus requiring an explicit @brief command for a brief</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="preprocessor"># description.)</span></div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160; </div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;JAVADOC_AUTOBRIEF      = YES</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;<span class="preprocessor"># If the QT_AUTOBRIEF tag is set to YES then doxygen will interpret the first</span></div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;<span class="preprocessor"># line (until the first dot) of a Qt-style comment as the brief description. If</span></div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;<span class="preprocessor"># set to NO, the Qt-style will behave just like regular Qt-style comments (thus</span></div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="preprocessor"># requiring an explicit \brief command for a brief description.)</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160; </div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;QT_AUTOBRIEF           = NO</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160; </div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;<span class="preprocessor"># The MULTILINE_CPP_IS_BRIEF tag can be set to YES to make doxygen treat a</span></div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="preprocessor"># multi-line C++ special comment block (i.e. a block of </span></div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="preprocessor"># a brief description. This used to be the default behavior. The new default is</span></div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;<span class="preprocessor"># to treat a multi-line C++ comment block as a detailed description. Set this</span></div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;<span class="preprocessor"># tag to YES if you prefer the old behavior instead.</span></div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="preprocessor"># Note that setting this tag to YES also means that rational rose comments are</span></div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;<span class="preprocessor"># not recognized any more.</span></div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160; </div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;MULTILINE_CPP_IS_BRIEF = NO</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160; </div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="preprocessor"># If the INHERIT_DOCS tag is set to YES then an undocumented member inherits the</span></div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="preprocessor"># documentation from any documented member that it re-implements.</span></div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160; </div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;INHERIT_DOCS           = YES</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="preprocessor"># If the SEPARATE_MEMBER_PAGES tag is set to YES then doxygen will produce a new</span></div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;<span class="preprocessor"># page for each member. If set to NO, the documentation of a member will be part</span></div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;<span class="preprocessor"># of the file/class/namespace that contains it.</span></div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160; </div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;SEPARATE_MEMBER_PAGES  = NO</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;<span class="preprocessor"># The TAB_SIZE tag can be used to set the number of spaces in a tab. Doxygen</span></div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;<span class="preprocessor"># uses this value to replace tabs by spaces in code fragments.</span></div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;<span class="preprocessor"># Minimum value: 1, maximum value: 16, default value: 4.</span></div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160; </div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;TAB_SIZE               = 8</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;<span class="preprocessor"># This tag can be used to specify a number of aliases that act as commands in</span></div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;<span class="preprocessor"># the documentation. An alias has the form:</span></div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;<span class="preprocessor"># name=value</span></div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;<span class="preprocessor"># For example adding</span></div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;<span class="preprocessor"># &quot;sideeffect=@par Side Effects:\n&quot;</span></div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;<span class="preprocessor"># will allow you to put the command \sideeffect (or @sideeffect) in the</span></div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;<span class="preprocessor"># documentation, which will result in a user-defined paragraph with heading</span></div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;<span class="preprocessor"># &quot;Side Effects:&quot;. You can put \n&#39;s in the value part of an alias to insert</span></div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;<span class="preprocessor"># newlines.</span></div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160; </div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;ALIASES                = </div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160; </div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;<span class="preprocessor"># This tag can be used to specify a number of word-keyword mappings (TCL only).</span></div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;<span class="preprocessor"># A mapping has the form &quot;name=value&quot;. For example adding &quot;class=itcl::class&quot;</span></div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;<span class="preprocessor"># will allow you to use the command class in the itcl::class meaning.</span></div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160; </div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;TCL_SUBST              = </div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160; </div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;<span class="preprocessor"># Set the OPTIMIZE_OUTPUT_FOR_C tag to YES if your project consists of C sources</span></div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;<span class="preprocessor"># only. Doxygen will then generate output that is more tailored for C. For</span></div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="preprocessor"># instance, some of the names that are used will be different. The list of all</span></div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;<span class="preprocessor"># members will be omitted, etc.</span></div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160; </div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;OPTIMIZE_OUTPUT_FOR_C  = NO</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160; </div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;<span class="preprocessor"># Set the OPTIMIZE_OUTPUT_JAVA tag to YES if your project consists of Java or</span></div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;<span class="preprocessor"># Python sources only. Doxygen will then generate output that is more tailored</span></div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;<span class="preprocessor"># for that language. For instance, namespaces will be presented as packages,</span></div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;<span class="preprocessor"># qualified scopes will look different, etc.</span></div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160; </div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;OPTIMIZE_OUTPUT_JAVA   = NO</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160; </div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;<span class="preprocessor"># Set the OPTIMIZE_FOR_FORTRAN tag to YES if your project consists of Fortran</span></div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;<span class="preprocessor"># sources. Doxygen will then generate output that is tailored for Fortran.</span></div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160; </div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;OPTIMIZE_FOR_FORTRAN   = NO</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160; </div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;<span class="preprocessor"># Set the OPTIMIZE_OUTPUT_VHDL tag to YES if your project consists of VHDL</span></div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;<span class="preprocessor"># sources. Doxygen will then generate output that is tailored for VHDL.</span></div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160; </div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;OPTIMIZE_OUTPUT_VHDL   = NO</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160; </div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;<span class="preprocessor"># Doxygen selects the parser to use depending on the extension of the files it</span></div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;<span class="preprocessor"># parses. With this tag you can assign which parser to use for a given</span></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;<span class="preprocessor"># extension. Doxygen has a built-in mapping, but you can override or extend it</span></div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;<span class="preprocessor"># using this tag. The format is ext=language, where ext is a file extension, and</span></div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;<span class="preprocessor"># language is one of the parsers supported by doxygen: IDL, Java, Javascript,</span></div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;<span class="preprocessor"># C#, C, C++, D, PHP, Objective-C, Python, Fortran (fixed format Fortran:</span></div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;<span class="preprocessor"># FortranFixed, free formatted Fortran: FortranFree, unknown formatted Fortran:</span></div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;<span class="preprocessor"># Fortran. In the later case the parser tries to guess whether the code is fixed</span></div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;<span class="preprocessor"># or free formatted code, this is the default for Fortran type files), VHDL. For</span></div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;<span class="preprocessor"># instance to make doxygen treat .inc files as Fortran files (default is PHP),</span></div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;<span class="preprocessor"># and .f files as C (default is Fortran), use: inc=Fortran f=C.</span></div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;<span class="preprocessor"># Note: For files without extension you can use no_extension as a placeholder.</span></div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;<span class="preprocessor"># Note that for custom extensions you also need to set FILE_PATTERNS otherwise</span></div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;<span class="preprocessor"># the files are not read by doxygen.</span></div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160; </div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;EXTENSION_MAPPING      = </div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160; </div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;<span class="preprocessor"># If the MARKDOWN_SUPPORT tag is enabled then doxygen pre-processes all comments</span></div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;<span class="preprocessor"># according to the Markdown format, which allows for more readable</span></div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;<span class="preprocessor"># documentation. See http://daringfireball.net/projects/markdown/ for details.</span></div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;<span class="preprocessor"># The output of markdown processing is further processed by doxygen, so you can</span></div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;<span class="preprocessor"># mix doxygen, HTML, and XML commands with Markdown formatting. Disable only in</span></div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;<span class="preprocessor"># case of backward compatibilities issues.</span></div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160; </div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;MARKDOWN_SUPPORT       = YES</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160; </div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;<span class="preprocessor"># When enabled doxygen tries to link words that correspond to documented</span></div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;<span class="preprocessor"># classes, or namespaces to their corresponding documentation. Such a link can</span></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;<span class="preprocessor"># be prevented in individual cases by putting a % sign in front of the word or</span></div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;<span class="preprocessor"># globally by setting AUTOLINK_SUPPORT to NO.</span></div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160; </div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;AUTOLINK_SUPPORT       = YES</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160; </div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;<span class="preprocessor"># If you use STL classes (i.e. std::string, std::vector, etc.) but do not want</span></div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;<span class="preprocessor"># to include (a tag file for) the STL sources as input, then you should set this</span></div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;<span class="preprocessor"># tag to YES in order to let doxygen match functions declarations and</span></div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;<span class="preprocessor"># definitions whose arguments contain STL classes (e.g. func(std::string);</span></div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;<span class="preprocessor"># versus func(std::string) {}). This also make the inheritance and collaboration</span></div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;<span class="preprocessor"># diagrams that involve STL classes more complete and accurate.</span></div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160; </div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;BUILTIN_STL_SUPPORT    = NO</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160; </div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;<span class="preprocessor"># If you use Microsoft&#39;s C++/CLI language, you should set this option to YES to</span></div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;<span class="preprocessor"># enable parsing support.</span></div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160; </div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;CPP_CLI_SUPPORT        = NO</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160; </div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;<span class="preprocessor"># Set the SIP_SUPPORT tag to YES if your project consists of sip (see:</span></div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;<span class="preprocessor"># http://www.riverbankcomputing.co.uk/software/sip/intro) sources only. Doxygen</span></div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;<span class="preprocessor"># will parse them like normal C++ but will assume all classes use public instead</span></div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;<span class="preprocessor"># of private inheritance when no explicit protection keyword is present.</span></div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160; </div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;SIP_SUPPORT            = NO</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160; </div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;<span class="preprocessor"># For Microsoft&#39;s IDL there are propget and propput attributes to indicate</span></div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;<span class="preprocessor"># getter and setter methods for a property. Setting this option to YES will make</span></div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;<span class="preprocessor"># doxygen to replace the get and set methods by a property in the documentation.</span></div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;<span class="preprocessor"># This will only work if the methods are indeed getting or setting a simple</span></div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;<span class="preprocessor"># type. If this is not the case, or you want to show the methods anyway, you</span></div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;<span class="preprocessor"># should set this option to NO.</span></div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160; </div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;IDL_PROPERTY_SUPPORT   = YES</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160; </div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;<span class="preprocessor"># If member grouping is used in the documentation and the DISTRIBUTE_GROUP_DOC</span></div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;<span class="preprocessor"># tag is set to YES then doxygen will reuse the documentation of the first</span></div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;<span class="preprocessor"># member in the group (if any) for the other members of the group. By default</span></div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;<span class="preprocessor"># all members of a group must be documented explicitly.</span></div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160; </div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;DISTRIBUTE_GROUP_DOC   = NO</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160; </div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="preprocessor"># If one adds a struct or class to a group and this option is enabled, then also</span></div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;<span class="preprocessor"># any nested class or struct is added to the same group. By default this option</span></div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;<span class="preprocessor"># is disabled and one has to add nested compounds explicitly via \ingroup.</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160; </div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;GROUP_NESTED_COMPOUNDS = NO</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160; </div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;<span class="preprocessor"># Set the SUBGROUPING tag to YES to allow class member groups of the same type</span></div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;<span class="preprocessor"># (for instance a group of public functions) to be put as a subgroup of that</span></div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;<span class="preprocessor"># type (e.g. under the Public Functions section). Set it to NO to prevent</span></div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;<span class="preprocessor"># subgrouping. Alternatively, this can be done per class using the</span></div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;<span class="preprocessor"># \nosubgrouping command.</span></div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160; </div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;SUBGROUPING            = NO</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160; </div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;<span class="preprocessor"># When the INLINE_GROUPED_CLASSES tag is set to YES, classes, structs and unions</span></div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;<span class="preprocessor"># are shown inside the group in which they are included (e.g. using \ingroup)</span></div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="preprocessor"># instead of on a separate page (for HTML and Man pages) or section (for LaTeX</span></div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;<span class="preprocessor"># and RTF).</span></div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;<span class="preprocessor"># Note that this feature does not work in combination with</span></div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;<span class="preprocessor"># SEPARATE_MEMBER_PAGES.</span></div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160; </div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;INLINE_GROUPED_CLASSES = NO</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160; </div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;<span class="preprocessor"># When the INLINE_SIMPLE_STRUCTS tag is set to YES, structs, classes, and unions</span></div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;<span class="preprocessor"># with only public data fields or simple typedef fields will be shown inline in</span></div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;<span class="preprocessor"># the documentation of the scope in which they are defined (i.e. file,</span></div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;<span class="preprocessor"># namespace, or group documentation), provided this scope is documented. If set</span></div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;<span class="preprocessor"># to NO, structs, classes, and unions are shown on a separate page (for HTML and</span></div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;<span class="preprocessor"># Man pages) or section (for LaTeX and RTF).</span></div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160; </div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;INLINE_SIMPLE_STRUCTS  = NO</div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160; </div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;<span class="preprocessor"># When TYPEDEF_HIDES_STRUCT tag is enabled, a typedef of a struct, union, or</span></div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;<span class="preprocessor"># enum is documented as struct, union, or enum with the name of the typedef. So</span></div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;<span class="preprocessor"># typedef struct TypeS {} TypeT, will appear in the documentation as a struct</span></div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;<span class="preprocessor"># with name TypeT. When disabled the typedef will appear as a member of a file,</span></div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;<span class="preprocessor"># namespace, or class. And the struct will be named TypeS. This can typically be</span></div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;<span class="preprocessor"># useful for C code in case the coding convention dictates that all compound</span></div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;<span class="preprocessor"># types are typedef&#39;ed and only the typedef is referenced, never the tag name.</span></div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160; </div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;TYPEDEF_HIDES_STRUCT   = NO</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160; </div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;<span class="preprocessor"># The size of the symbol lookup cache can be set using LOOKUP_CACHE_SIZE. This</span></div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;<span class="preprocessor"># cache is used to resolve symbols given their name and scope. Since this can be</span></div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;<span class="preprocessor"># an expensive process and often the same symbol appears multiple times in the</span></div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;<span class="preprocessor"># code, doxygen keeps a cache of pre-resolved symbols. If the cache is too small</span></div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;<span class="preprocessor"># doxygen will become slower. If the cache is too large, memory is wasted. The</span></div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;<span class="preprocessor"># cache size is given by this formula: 2^(16+LOOKUP_CACHE_SIZE). The valid range</span></div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;<span class="preprocessor"># is 0..9, the default is 0, corresponding to a cache size of 2^16=65536</span></div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;<span class="preprocessor"># symbols. At the end of a run doxygen will report the cache usage and suggest</span></div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;<span class="preprocessor"># the optimal cache size from a speed point of view.</span></div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;<span class="preprocessor"># Minimum value: 0, maximum value: 9, default value: 0.</span></div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160; </div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;LOOKUP_CACHE_SIZE      = 0</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160; </div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;<span class="preprocessor"># Build related configuration options</span></div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160; </div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;<span class="preprocessor"># If the EXTRACT_ALL tag is set to YES, doxygen will assume all entities in</span></div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;<span class="preprocessor"># documentation are documented, even if no documentation was available. Private</span></div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;<span class="preprocessor"># class members and static file members will be hidden unless the</span></div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;<span class="preprocessor"># EXTRACT_PRIVATE respectively EXTRACT_STATIC tags are set to YES.</span></div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;<span class="preprocessor"># Note: This will also disable the warnings about undocumented members that are</span></div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;<span class="preprocessor"># normally produced when WARNINGS is set to YES.</span></div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160; </div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;EXTRACT_ALL            = NO</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160; </div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;<span class="preprocessor"># If the EXTRACT_PRIVATE tag is set to YES, all private members of a class will</span></div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;<span class="preprocessor"># be included in the documentation.</span></div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160; </div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;EXTRACT_PRIVATE        = NO</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160; </div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;<span class="preprocessor"># If the EXTRACT_PACKAGE tag is set to YES, all members with package or internal</span></div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;<span class="preprocessor"># scope will be included in the documentation.</span></div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160; </div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;EXTRACT_PACKAGE        = NO</div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160; </div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;<span class="preprocessor"># If the EXTRACT_STATIC tag is set to YES, all static members of a file will be</span></div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;<span class="preprocessor"># included in the documentation.</span></div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160; </div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;EXTRACT_STATIC         = YES</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160; </div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;<span class="preprocessor"># If the EXTRACT_LOCAL_CLASSES tag is set to YES, classes (and structs) defined</span></div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;<span class="preprocessor"># locally in source files will be included in the documentation. If set to NO,</span></div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;<span class="preprocessor"># only classes defined in header files are included. Does not have any effect</span></div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;<span class="preprocessor"># for Java sources.</span></div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160; </div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;EXTRACT_LOCAL_CLASSES  = NO</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160; </div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;<span class="preprocessor"># This flag is only useful for Objective-C code. If set to YES, local methods,</span></div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;<span class="preprocessor"># which are defined in the implementation section but not in the interface are</span></div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;<span class="preprocessor"># included in the documentation. If set to NO, only methods in the interface are</span></div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;<span class="preprocessor"># included.</span></div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160; </div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;EXTRACT_LOCAL_METHODS  = NO</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160; </div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;<span class="preprocessor"># If this flag is set to YES, the members of anonymous namespaces will be</span></div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;<span class="preprocessor"># extracted and appear in the documentation as a namespace called</span></div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;<span class="preprocessor"># &#39;anonymous_namespace{file}&#39;, where file will be replaced with the base name of</span></div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;<span class="preprocessor"># the file that contains the anonymous namespace. By default anonymous namespace</span></div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;<span class="preprocessor"># are hidden.</span></div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160; </div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;EXTRACT_ANON_NSPACES   = NO</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160; </div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;<span class="preprocessor"># If the HIDE_UNDOC_MEMBERS tag is set to YES, doxygen will hide all</span></div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;<span class="preprocessor"># undocumented members inside documented classes or files. If set to NO these</span></div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;<span class="preprocessor"># members will be included in the various overviews, but no documentation</span></div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;<span class="preprocessor"># section is generated. This option has no effect if EXTRACT_ALL is enabled.</span></div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160; </div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;HIDE_UNDOC_MEMBERS     = YES</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160; </div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;<span class="preprocessor"># If the HIDE_UNDOC_CLASSES tag is set to YES, doxygen will hide all</span></div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;<span class="preprocessor"># undocumented classes that are normally visible in the class hierarchy. If set</span></div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;<span class="preprocessor"># to NO, these classes will be included in the various overviews. This option</span></div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;<span class="preprocessor"># has no effect if EXTRACT_ALL is enabled.</span></div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160; </div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;HIDE_UNDOC_CLASSES     = YES</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160; </div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;<span class="preprocessor"># If the HIDE_FRIEND_COMPOUNDS tag is set to YES, doxygen will hide all friend</span></div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;<span class="preprocessor"># (class|struct|union) declarations. If set to NO, these declarations will be</span></div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;<span class="preprocessor"># included in the documentation.</span></div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160; </div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;HIDE_FRIEND_COMPOUNDS  = YES</div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160; </div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;<span class="preprocessor"># If the HIDE_IN_BODY_DOCS tag is set to YES, doxygen will hide any</span></div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;<span class="preprocessor"># documentation blocks found inside the body of a function. If set to NO, these</span></div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;<span class="preprocessor"># blocks will be appended to the function&#39;s detailed documentation block.</span></div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160; </div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;HIDE_IN_BODY_DOCS      = YES</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160; </div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;<span class="preprocessor"># The INTERNAL_DOCS tag determines if documentation that is typed after a</span></div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;<span class="preprocessor"># \internal command is included. If the tag is set to NO then the documentation</span></div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;<span class="preprocessor"># will be excluded. Set it to YES to include the internal documentation.</span></div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160; </div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;INTERNAL_DOCS          = NO</div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160; </div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;<span class="preprocessor"># If the CASE_SENSE_NAMES tag is set to NO then doxygen will only generate file</span></div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;<span class="preprocessor"># names in lower-case letters. If set to YES, upper-case letters are also</span></div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;<span class="preprocessor"># allowed. This is useful if you have classes or files whose names only differ</span></div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;<span class="preprocessor"># in case and if your file system supports case sensitive file names. Windows</span></div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;<span class="preprocessor"># and Mac users are advised to set this option to NO.</span></div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;<span class="preprocessor"># The default value is: system dependent.</span></div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160; </div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;CASE_SENSE_NAMES       = YES</div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160; </div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;<span class="preprocessor"># If the HIDE_SCOPE_NAMES tag is set to NO then doxygen will show members with</span></div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;<span class="preprocessor"># their full class and namespace scopes in the documentation. If set to YES, the</span></div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;<span class="preprocessor"># scope will be hidden.</span></div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160; </div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;HIDE_SCOPE_NAMES       = YES</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160; </div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;<span class="preprocessor"># If the HIDE_COMPOUND_REFERENCE tag is set to NO (default) then doxygen will</span></div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;<span class="preprocessor"># append additional text to a page&#39;s title, such as Class Reference. If set to</span></div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;<span class="preprocessor"># YES the compound reference will be hidden.</span></div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160; </div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;HIDE_COMPOUND_REFERENCE= NO</div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160; </div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;<span class="preprocessor"># If the SHOW_INCLUDE_FILES tag is set to YES then doxygen will put a list of</span></div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;<span class="preprocessor"># the files that are included by a file in the documentation of that file.</span></div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160; </div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;SHOW_INCLUDE_FILES     = NO</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160; </div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;<span class="preprocessor"># If the SHOW_GROUPED_MEMB_INC tag is set to YES then Doxygen will add for each</span></div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;<span class="preprocessor"># grouped member an include statement to the documentation, telling the reader</span></div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;<span class="preprocessor"># which file to include in order to use the member.</span></div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160; </div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;SHOW_GROUPED_MEMB_INC  = NO</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160; </div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;<span class="preprocessor"># If the FORCE_LOCAL_INCLUDES tag is set to YES then doxygen will list include</span></div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;<span class="preprocessor"># files with double quotes in the documentation rather than with sharp brackets.</span></div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160; </div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;FORCE_LOCAL_INCLUDES   = NO</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160; </div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;<span class="preprocessor"># If the INLINE_INFO tag is set to YES then a tag [inline] is inserted in the</span></div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;<span class="preprocessor"># documentation for inline members.</span></div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160; </div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;INLINE_INFO            = NO</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160; </div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;<span class="preprocessor"># If the SORT_MEMBER_DOCS tag is set to YES then doxygen will sort the</span></div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;<span class="preprocessor"># (detailed) documentation of file and class members alphabetically by member</span></div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;<span class="preprocessor"># name. If set to NO, the members will appear in declaration order.</span></div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160; </div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;SORT_MEMBER_DOCS       = YES</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160; </div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;<span class="preprocessor"># If the SORT_BRIEF_DOCS tag is set to YES then doxygen will sort the brief</span></div>
<div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;<span class="preprocessor"># descriptions of file, namespace and class members alphabetically by member</span></div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;<span class="preprocessor"># name. If set to NO, the members will appear in declaration order. Note that</span></div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;<span class="preprocessor"># this will also influence the order of the classes in the class list.</span></div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160; </div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;SORT_BRIEF_DOCS        = YES</div>
<div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160; </div>
<div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;<span class="preprocessor"># If the SORT_MEMBERS_CTORS_1ST tag is set to YES then doxygen will sort the</span></div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;<span class="preprocessor"># (brief and detailed) documentation of class members so that constructors and</span></div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;<span class="preprocessor"># destructors are listed first. If set to NO the constructors will appear in the</span></div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;<span class="preprocessor"># respective orders defined by SORT_BRIEF_DOCS and SORT_MEMBER_DOCS.</span></div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;<span class="preprocessor"># Note: If SORT_BRIEF_DOCS is set to NO this option is ignored for sorting brief</span></div>
<div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;<span class="preprocessor"># member documentation.</span></div>
<div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;<span class="preprocessor"># Note: If SORT_MEMBER_DOCS is set to NO this option is ignored for sorting</span></div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;<span class="preprocessor"># detailed member documentation.</span></div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160; </div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;SORT_MEMBERS_CTORS_1ST = NO</div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160; </div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;<span class="preprocessor"># If the SORT_GROUP_NAMES tag is set to YES then doxygen will sort the hierarchy</span></div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;<span class="preprocessor"># of group names into alphabetical order. If set to NO the group names will</span></div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;<span class="preprocessor"># appear in their defined order.</span></div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160; </div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;SORT_GROUP_NAMES       = NO</div>
<div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160; </div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;<span class="preprocessor"># If the SORT_BY_SCOPE_NAME tag is set to YES, the class list will be sorted by</span></div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;<span class="preprocessor"># fully-qualified names, including namespaces. If set to NO, the class list will</span></div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;<span class="preprocessor"># be sorted only by class name, not including the namespace part.</span></div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;<span class="preprocessor"># Note: This option is not very useful if HIDE_SCOPE_NAMES is set to YES.</span></div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;<span class="preprocessor"># Note: This option applies only to the class list, not to the alphabetical</span></div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;<span class="preprocessor"># list.</span></div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160; </div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;SORT_BY_SCOPE_NAME     = YES</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160; </div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;<span class="preprocessor"># If the STRICT_PROTO_MATCHING option is enabled and doxygen fails to do proper</span></div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;<span class="preprocessor"># type resolution of all parameters of a function it will reject a match between</span></div>
<div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;<span class="preprocessor"># the prototype and the implementation of a member function even if there is</span></div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;<span class="preprocessor"># only one candidate or it is obvious which candidate to choose by doing a</span></div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;<span class="preprocessor"># simple string match. By disabling STRICT_PROTO_MATCHING doxygen will still</span></div>
<div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;<span class="preprocessor"># accept a match between prototype and implementation in such cases.</span></div>
<div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160; </div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;STRICT_PROTO_MATCHING  = NO</div>
<div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160; </div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;<span class="preprocessor"># The GENERATE_TODOLIST tag can be used to enable (YES) or disable (NO) the todo</span></div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;<span class="preprocessor"># list. This list is created by putting \todo commands in the documentation.</span></div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160; </div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;GENERATE_TODOLIST      = YES</div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160; </div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;<span class="preprocessor"># The GENERATE_TESTLIST tag can be used to enable (YES) or disable (NO) the test</span></div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;<span class="preprocessor"># list. This list is created by putting \test commands in the documentation.</span></div>
<div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160; </div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;GENERATE_TESTLIST      = YES</div>
<div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160; </div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;<span class="preprocessor"># The GENERATE_BUGLIST tag can be used to enable (YES) or disable (NO) the bug</span></div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;<span class="preprocessor"># list. This list is created by putting \bug commands in the documentation.</span></div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160; </div>
<div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;GENERATE_BUGLIST       = YES</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160; </div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;<span class="preprocessor"># The GENERATE_DEPRECATEDLIST tag can be used to enable (YES) or disable (NO)</span></div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;<span class="preprocessor"># the deprecated list. This list is created by putting \deprecated commands in</span></div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;<span class="preprocessor"># the documentation.</span></div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160; </div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;GENERATE_DEPRECATEDLIST= YES</div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160; </div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;<span class="preprocessor"># The ENABLED_SECTIONS tag can be used to enable conditional documentation</span></div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;<span class="preprocessor"># sections, marked by \if &lt;section_label&gt; ... \endif and \cond &lt;section_label&gt;</span></div>
<div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;<span class="preprocessor"># ... \endcond blocks.</span></div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160; </div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;ENABLED_SECTIONS       = </div>
<div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160; </div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;<span class="preprocessor"># The MAX_INITIALIZER_LINES tag determines the maximum number of lines that the</span></div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;<span class="preprocessor"># initial value of a variable or macro / define can have for it to appear in the</span></div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;<span class="preprocessor"># documentation. If the initializer consists of more lines than specified here</span></div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;<span class="preprocessor"># it will be hidden. Use a value of 0 to hide initializers completely. The</span></div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;<span class="preprocessor"># appearance of the value of individual variables and macros / defines can be</span></div>
<div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;<span class="preprocessor"># controlled using \showinitializer or \hideinitializer command in the</span></div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;<span class="preprocessor"># documentation regardless of this setting.</span></div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;<span class="preprocessor"># Minimum value: 0, maximum value: 10000, default value: 30.</span></div>
<div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160; </div>
<div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;MAX_INITIALIZER_LINES  = 30</div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160; </div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;<span class="preprocessor"># Set the SHOW_USED_FILES tag to NO to disable the list of files generated at</span></div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;<span class="preprocessor"># the bottom of the documentation of classes and structs. If set to YES, the</span></div>
<div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;<span class="preprocessor"># list will mention the files that were used to generate the documentation.</span></div>
<div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160; </div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;SHOW_USED_FILES        = NO</div>
<div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160; </div>
<div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;<span class="preprocessor"># Set the SHOW_FILES tag to NO to disable the generation of the Files page. This</span></div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;<span class="preprocessor"># will remove the Files entry from the Quick Index and from the Folder Tree View</span></div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;<span class="preprocessor"># (if specified).</span></div>
<div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160; </div>
<div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;SHOW_FILES             = YES</div>
<div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160; </div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;<span class="preprocessor"># Set the SHOW_NAMESPACES tag to NO to disable the generation of the Namespaces</span></div>
<div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;<span class="preprocessor"># page. This will remove the Namespaces entry from the Quick Index and from the</span></div>
<div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;<span class="preprocessor"># Folder Tree View (if specified).</span></div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160; </div>
<div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;SHOW_NAMESPACES        = YES</div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160; </div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;<span class="preprocessor"># The FILE_VERSION_FILTER tag can be used to specify a program or script that</span></div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;<span class="preprocessor"># doxygen should invoke to get the current version for each file (typically from</span></div>
<div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;<span class="preprocessor"># the version control system). Doxygen will invoke the program by executing (via</span></div>
<div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;<span class="preprocessor"># popen()) the command command input-file, where command is the value of the</span></div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;<span class="preprocessor"># FILE_VERSION_FILTER tag, and input-file is the name of an input file provided</span></div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;<span class="preprocessor"># by doxygen. Whatever the program writes to standard output is used as the file</span></div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;<span class="preprocessor"># version. For an example see the documentation.</span></div>
<div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160; </div>
<div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;FILE_VERSION_FILTER    = </div>
<div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160; </div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;<span class="preprocessor"># The LAYOUT_FILE tag can be used to specify a layout file which will be parsed</span></div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;<span class="preprocessor"># by doxygen. The layout file controls the global structure of the generated</span></div>
<div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;<span class="preprocessor"># output files in an output format independent way. To create the layout file</span></div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;<span class="preprocessor"># that represents doxygen&#39;s defaults, run doxygen with the -l option. You can</span></div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;<span class="preprocessor"># optionally specify a file name after the option, if omitted DoxygenLayout.xml</span></div>
<div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;<span class="preprocessor"># will be used as the name of the layout file.</span></div>
<div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;<span class="preprocessor"># Note that if you run doxygen from a directory containing a file called</span></div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;<span class="preprocessor"># DoxygenLayout.xml, doxygen will parse it automatically even if the LAYOUT_FILE</span></div>
<div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;<span class="preprocessor"># tag is left empty.</span></div>
<div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160; </div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;LAYOUT_FILE            = </div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160; </div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;<span class="preprocessor"># The CITE_BIB_FILES tag can be used to specify one or more bib files containing</span></div>
<div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;<span class="preprocessor"># the reference definitions. This must be a list of .bib files. The .bib</span></div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;<span class="preprocessor"># extension is automatically appended if omitted. This requires the bibtex tool</span></div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;<span class="preprocessor"># to be installed. See also http://en.wikipedia.org/wiki/BibTeX for more info.</span></div>
<div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;<span class="preprocessor"># For LaTeX the style of the bibliography can be controlled using</span></div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;<span class="preprocessor"># LATEX_BIB_STYLE. To use this feature you need bibtex and perl available in the</span></div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;<span class="preprocessor"># search path. See also \cite for info how to create references.</span></div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160; </div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;CITE_BIB_FILES         = </div>
<div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160; </div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;<span class="preprocessor"># Configuration options related to warning and progress messages</span></div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160; </div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;<span class="preprocessor"># The QUIET tag can be used to turn on/off the messages that are generated to</span></div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;<span class="preprocessor"># standard output by doxygen. If QUIET is set to YES this implies that the</span></div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;<span class="preprocessor"># messages are off.</span></div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160; </div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;QUIET                  = NO</div>
<div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160; </div>
<div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;<span class="preprocessor"># The WARNINGS tag can be used to turn on/off the warning messages that are</span></div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;<span class="preprocessor"># generated to standard error (stderr) by doxygen. If WARNINGS is set to YES</span></div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;<span class="preprocessor"># this implies that the warnings are on.</span></div>
<div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;<span class="preprocessor"># Tip: Turn warnings on while writing the documentation.</span></div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160; </div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;WARNINGS               = YES</div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160; </div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;<span class="preprocessor"># If the WARN_IF_UNDOCUMENTED tag is set to YES then doxygen will generate</span></div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;<span class="preprocessor"># warnings for undocumented members. If EXTRACT_ALL is set to YES then this flag</span></div>
<div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;<span class="preprocessor"># will automatically be disabled.</span></div>
<div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160; </div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;WARN_IF_UNDOCUMENTED   = YES</div>
<div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160; </div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;<span class="preprocessor"># If the WARN_IF_DOC_ERROR tag is set to YES, doxygen will generate warnings for</span></div>
<div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;<span class="preprocessor"># potential errors in the documentation, such as not documenting some parameters</span></div>
<div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;<span class="preprocessor"># in a documented function, or documenting parameters that don&#39;t exist or using</span></div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;<span class="preprocessor"># markup commands wrongly.</span></div>
<div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;<span class="preprocessor"># The default value is: YES.</span></div>
<div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160; </div>
<div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;WARN_IF_DOC_ERROR      = YES</div>
<div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160; </div>
<div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;<span class="preprocessor"># This WARN_NO_PARAMDOC option can be enabled to get warnings for functions that</span></div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;<span class="preprocessor"># are documented, but have no documentation for their parameters or return</span></div>
<div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;<span class="preprocessor"># value. If set to NO, doxygen will only warn about wrong or incomplete</span></div>
<div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;<span class="preprocessor"># parameter documentation, but not about the absence of documentation.</span></div>
<div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160; </div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;WARN_NO_PARAMDOC       = NO</div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160; </div>
<div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;<span class="preprocessor"># The WARN_FORMAT tag determines the format of the warning messages that doxygen</span></div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;<span class="preprocessor"># can produce. The string should contain the $file, $line, and $text tags, which</span></div>
<div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;<span class="preprocessor"># will be replaced by the file and line number from which the warning originated</span></div>
<div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;<span class="preprocessor"># and the warning text. Optionally the format may contain $version, which will</span></div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;<span class="preprocessor"># be replaced by the version of the file (if it could be obtained via</span></div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;<span class="preprocessor"># FILE_VERSION_FILTER)</span></div>
<div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;<span class="preprocessor"># The default value is: $file:$line: $text.</span></div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160; </div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;WARN_FORMAT            = <span class="stringliteral">&quot;$file:$line: $text&quot;</span></div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160; </div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;<span class="preprocessor"># The WARN_LOGFILE tag can be used to specify a file to which warning and error</span></div>
<div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;<span class="preprocessor"># messages should be written. If left blank the output is written to standard</span></div>
<div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;<span class="preprocessor"># error (stderr).</span></div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160; </div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;WARN_LOGFILE           = </div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160; </div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;<span class="preprocessor"># Configuration options related to the input files</span></div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;<span class="preprocessor">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160; </div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;<span class="preprocessor"># The INPUT tag is used to specify the files and/or directories that contain</span></div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;<span class="preprocessor"># documented source files. You may enter file names like myfile.cpp or</span></div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;<span class="preprocessor"># directories like /usr/src/myproject. Separate the files or directories with</span></div>
<div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;<span class="preprocessor"># spaces. See also FILE_PATTERNS and EXTENSION_MAPPING</span></div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;<span class="preprocessor"># Note: If this tag is empty the current directory is searched.</span></div>
<div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160; </div>
<div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;INPUT                  = ../glm \</div>
<div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160;                         .</div>
<div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160; </div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;# This tag can be used to specify the character encoding of the source files</div>
<div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;<span class="preprocessor"># that doxygen parses. Internally doxygen uses the UTF-8 encoding. Doxygen uses</span></div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;<span class="preprocessor"># libiconv (or the iconv built into libc) for the transcoding. See the libiconv</span></div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;<span class="preprocessor"># documentation (see: http://www.gnu.org/software/libiconv) for the list of</span></div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;<span class="preprocessor"># possible encodings.</span></div>
<div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;<span class="preprocessor"># The default value is: UTF-8.</span></div>
<div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160; </div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;INPUT_ENCODING         = UTF-8</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160; </div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;<span class="preprocessor"># If the value of the INPUT tag contains directories, you can use the</span></div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;<span class="preprocessor"># FILE_PATTERNS tag to specify one or more wildcard patterns (like *.cpp and</span></div>
<div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;<span class="preprocessor"># *.h) to filter out the source-files in the directories.</span></div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;<span class="preprocessor"># Note that for custom extensions or not directly supported extensions you also</span></div>
<div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;<span class="preprocessor"># need to set EXTENSION_MAPPING for the extension otherwise the files are not</span></div>
<div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;<span class="preprocessor"># read by doxygen.</span></div>
<div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;<span class="preprocessor"># If left blank the following patterns are tested:*.c, *.cc, *.cxx, *.cpp,</span></div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;<span class="preprocessor"># *.c++, *.java, *.ii, *.ixx, *.ipp, *.i++, *.inl, *.idl, *.ddl, *.odl, *.h,</span></div>
<div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;<span class="preprocessor"># *.hh, *.hxx, *.hpp, *.h++, *.cs, *.d, *.php, *.php4, *.php5, *.phtml, *.inc,</span></div>
<div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;<span class="preprocessor"># *.m, *.markdown, *.md, *.mm, *.dox, *.py, *.f90, *.f, *.for, *.tcl, *.vhd,</span></div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;<span class="preprocessor"># *.vhdl, *.ucf, *.qsf, *.as and *.js.</span></div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160; </div>
<div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;FILE_PATTERNS          = *.hpp \</div>
<div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;                         *.doxy</div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160; </div>
<div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;<span class="preprocessor"># The RECURSIVE tag can be used to specify whether or not subdirectories should</span></div>
<div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;<span class="preprocessor"># be searched for input files as well.</span></div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160; </div>
<div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;RECURSIVE              = YES</div>
<div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160; </div>
<div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;<span class="preprocessor"># The EXCLUDE tag can be used to specify files and/or directories that should be</span></div>
<div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;<span class="preprocessor"># excluded from the INPUT source files. This way you can easily exclude a</span></div>
<div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;<span class="preprocessor"># subdirectory from a directory tree whose root is specified with the INPUT tag.</span></div>
<div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;<span class="preprocessor"># Note that relative paths are relative to the directory from which doxygen is</span></div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;<span class="preprocessor"># run.</span></div>
<div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160; </div>
<div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;EXCLUDE                = </div>
<div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160; </div>
<div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;<span class="preprocessor"># The EXCLUDE_SYMLINKS tag can be used to select whether or not files or</span></div>
<div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;<span class="preprocessor"># directories that are symbolic links (a Unix file system feature) are excluded</span></div>
<div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;<span class="preprocessor"># from the input.</span></div>
<div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;<span class="preprocessor"># The default value is: NO.</span></div>
<div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160; </div>
<div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;EXCLUDE_SYMLINKS       = NO</div>
<div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160; </div>
<div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;<span class="preprocessor"># If the value of the INPUT tag contains directories, you can use the</span></div>
<div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;<span class="preprocessor"># EXCLUDE_PATTERNS tag to specify one or more wildcard patterns to exclude</span></div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;<span class="preprocessor"># certain files from those directories.</span></div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;<span class="preprocessor">#</span></div>
<div class="line"><a name="l00833"></a><span class="lineno">  833</span>&#160;<span class="preprocessor"># Note that the wildcards are matched against the file with absolute path, so to</span></div>
<div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;<span class="preprocessor"># exclude all test directories for example use the pattern */test</span><span class="comment">/*</span></div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00836"></a><span class="lineno">  836</span>&#160;<span class="comment">EXCLUDE_PATTERNS       = </span></div>
<div class="line"><a name="l00837"></a><span class="lineno">  837</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;<span class="comment"># The EXCLUDE_SYMBOLS tag can be used to specify one or more symbol names</span></div>
<div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;<span class="comment"># (namespaces, classes, functions, etc.) that should be excluded from the</span></div>
<div class="line"><a name="l00840"></a><span class="lineno">  840</span>&#160;<span class="comment"># output. The symbol name can be a fully qualified name, a word, or if the</span></div>
<div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;<span class="comment"># wildcard * is used, a substring. Examples: ANamespace, AClass,</span></div>
<div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;<span class="comment"># AClass::ANamespace, ANamespace::*Test</span></div>
<div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;<span class="comment"># Note that the wildcards are matched against the file with absolute path, so to</span></div>
<div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;<span class="comment"># exclude all test directories use the pattern */</span><span class="preprocessor">test</span><span class="comment">/*</span></div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;<span class="comment">EXCLUDE_SYMBOLS        = </span></div>
<div class="line"><a name="l00848"></a><span class="lineno">  848</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00849"></a><span class="lineno">  849</span>&#160;<span class="comment"># The EXAMPLE_PATH tag can be used to specify one or more files or directories</span></div>
<div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;<span class="comment"># that contain example code fragments that are included (see the \include</span></div>
<div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;<span class="comment"># command).</span></div>
<div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;<span class="comment">EXAMPLE_PATH           = </span></div>
<div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;<span class="comment"># If the value of the EXAMPLE_PATH tag contains directories, you can use the</span></div>
<div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;<span class="comment"># EXAMPLE_PATTERNS tag to specify one or more wildcard pattern (like *.cpp and</span></div>
<div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;<span class="comment"># *.h) to filter out the source-files in the directories. If left blank all</span></div>
<div class="line"><a name="l00858"></a><span class="lineno">  858</span>&#160;<span class="comment"># files are included.</span></div>
<div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;<span class="comment">EXAMPLE_PATTERNS       = *</span></div>
<div class="line"><a name="l00861"></a><span class="lineno">  861</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;<span class="comment"># If the EXAMPLE_RECURSIVE tag is set to YES then subdirectories will be</span></div>
<div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;<span class="comment"># searched for input files to be used with the \include or \dontinclude commands</span></div>
<div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;<span class="comment"># irrespective of the value of the RECURSIVE tag.</span></div>
<div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;<span class="comment">EXAMPLE_RECURSIVE      = NO</span></div>
<div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;<span class="comment"># The IMAGE_PATH tag can be used to specify one or more files or directories</span></div>
<div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;<span class="comment"># that contain images that are to be included in the documentation (see the</span></div>
<div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;<span class="comment"># \image command).</span></div>
<div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;<span class="comment">IMAGE_PATH             = </span></div>
<div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;<span class="comment"># The INPUT_FILTER tag can be used to specify a program that doxygen should</span></div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;<span class="comment"># invoke to filter for each input file. Doxygen will invoke the filter program</span></div>
<div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;<span class="comment"># by executing (via popen()) the command:</span></div>
<div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;<span class="comment"># &lt;filter&gt; &lt;input-file&gt;</span></div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;<span class="comment"># where &lt;filter&gt; is the value of the INPUT_FILTER tag, and &lt;input-file&gt; is the</span></div>
<div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;<span class="comment"># name of an input file. Doxygen will then use the output that the filter</span></div>
<div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;<span class="comment"># program writes to standard output. If FILTER_PATTERNS is specified, this tag</span></div>
<div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;<span class="comment"># will be ignored.</span></div>
<div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;<span class="comment"># Note that the filter must not add or remove lines; it is applied before the</span></div>
<div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;<span class="comment"># code is scanned, but not when the output code is generated. If lines are added</span></div>
<div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;<span class="comment"># or removed, the anchors will not be placed correctly.</span></div>
<div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;<span class="comment">INPUT_FILTER           = </span></div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160;<span class="comment"># The FILTER_PATTERNS tag can be used to specify filters on a per file pattern</span></div>
<div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;<span class="comment"># basis. Doxygen will compare the file name with each pattern and apply the</span></div>
<div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;<span class="comment"># filter if there is a match. The filters are a list of the form: pattern=filter</span></div>
<div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;<span class="comment"># (like *.cpp=my_cpp_filter). See INPUT_FILTER for further information on how</span></div>
<div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;<span class="comment"># filters are used. If the FILTER_PATTERNS tag is empty or if none of the</span></div>
<div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;<span class="comment"># patterns match the file name, INPUT_FILTER is applied.</span></div>
<div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;<span class="comment">FILTER_PATTERNS        = </span></div>
<div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;<span class="comment"># If the FILTER_SOURCE_FILES tag is set to YES, the input filter (if set using</span></div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;<span class="comment"># INPUT_FILTER) will also be used to filter the input files that are used for</span></div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;<span class="comment"># producing the source files to browse (i.e. when SOURCE_BROWSER is set to YES).</span></div>
<div class="line"><a name="l00904"></a><span class="lineno">  904</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;<span class="comment">FILTER_SOURCE_FILES    = NO</span></div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;<span class="comment"># The FILTER_SOURCE_PATTERNS tag can be used to specify source filters per file</span></div>
<div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;<span class="comment"># pattern. A pattern will override the setting for FILTER_PATTERN (if any) and</span></div>
<div class="line"><a name="l00910"></a><span class="lineno">  910</span>&#160;<span class="comment"># it is also possible to disable source filtering for a specific pattern using</span></div>
<div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;<span class="comment"># *.ext= (so without naming a filter).</span></div>
<div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;<span class="comment"># This tag requires that the tag FILTER_SOURCE_FILES is set to YES.</span></div>
<div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;<span class="comment">FILTER_SOURCE_PATTERNS = </span></div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;<span class="comment"># If the USE_MDFILE_AS_MAINPAGE tag refers to the name of a markdown file that</span></div>
<div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;<span class="comment"># is part of the input, its contents will be placed on the main page</span></div>
<div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;<span class="comment"># (index.html). This can be useful if you have a project on for instance GitHub</span></div>
<div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;<span class="comment"># and want to reuse the introduction page also for the doxygen output.</span></div>
<div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;<span class="comment">USE_MDFILE_AS_MAINPAGE = </span></div>
<div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;<span class="comment"># Configuration options related to source browsing</span></div>
<div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;<span class="comment"># If the SOURCE_BROWSER tag is set to YES then a list of source files will be</span></div>
<div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;<span class="comment"># generated. Documented entities will be cross-referenced with these sources.</span></div>
<div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;<span class="comment"># Note: To get rid of all source code in the generated output, make sure that</span></div>
<div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;<span class="comment"># also VERBATIM_HEADERS is set to NO.</span></div>
<div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;<span class="comment">SOURCE_BROWSER         = YES</span></div>
<div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;<span class="comment"># Setting the INLINE_SOURCES tag to YES will include the body of functions,</span></div>
<div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;<span class="comment"># classes and enums directly into the documentation.</span></div>
<div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160;<span class="comment">INLINE_SOURCES         = NO</span></div>
<div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;<span class="comment"># Setting the STRIP_CODE_COMMENTS tag to YES will instruct doxygen to hide any</span></div>
<div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;<span class="comment"># special comment blocks from generated source code fragments. Normal C, C++ and</span></div>
<div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;<span class="comment"># Fortran comments will always remain visible.</span></div>
<div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l00946"></a><span class="lineno">  946</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;<span class="comment">STRIP_CODE_COMMENTS    = YES</span></div>
<div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;<span class="comment"># If the REFERENCED_BY_RELATION tag is set to YES then for each documented</span></div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;<span class="comment"># function all documented functions referencing it will be listed.</span></div>
<div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;<span class="comment">REFERENCED_BY_RELATION = YES</span></div>
<div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;<span class="comment"># If the REFERENCES_RELATION tag is set to YES then for each documented function</span></div>
<div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;<span class="comment"># all documented entities called/used by that function will be listed.</span></div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;<span class="comment">REFERENCES_RELATION    = YES</span></div>
<div class="line"><a name="l00960"></a><span class="lineno">  960</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;<span class="comment"># If the REFERENCES_LINK_SOURCE tag is set to YES and SOURCE_BROWSER tag is set</span></div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;<span class="comment"># to YES then the hyperlinks from functions in REFERENCES_RELATION and</span></div>
<div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;<span class="comment"># REFERENCED_BY_RELATION lists will link to the source code. Otherwise they will</span></div>
<div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;<span class="comment"># link to the documentation.</span></div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;<span class="comment">REFERENCES_LINK_SOURCE = YES</span></div>
<div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;<span class="comment"># If SOURCE_TOOLTIPS is enabled (the default) then hovering a hyperlink in the</span></div>
<div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;<span class="comment"># source code will show a tooltip with additional information such as prototype,</span></div>
<div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;<span class="comment"># brief description and links to the definition and documentation. Since this</span></div>
<div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;<span class="comment"># will make the HTML file larger and loading of large files a bit slower, you</span></div>
<div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;<span class="comment"># can opt to disable this feature.</span></div>
<div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;<span class="comment"># This tag requires that the tag SOURCE_BROWSER is set to YES.</span></div>
<div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;<span class="comment">SOURCE_TOOLTIPS        = YES</span></div>
<div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;<span class="comment"># If the USE_HTAGS tag is set to YES then the references to source code will</span></div>
<div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;<span class="comment"># point to the HTML generated by the htags(1) tool instead of doxygen built-in</span></div>
<div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;<span class="comment"># source browser. The htags tool is part of GNU&#39;s global source tagging system</span></div>
<div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;<span class="comment"># (see http://www.gnu.org/software/global/global.html). You will need version</span></div>
<div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;<span class="comment"># 4.8.6 or higher.</span></div>
<div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;<span class="comment"># To use it do the following:</span></div>
<div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;<span class="comment"># - Install the latest version of global</span></div>
<div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;<span class="comment"># - Enable SOURCE_BROWSER and USE_HTAGS in the config file</span></div>
<div class="line"><a name="l00988"></a><span class="lineno">  988</span>&#160;<span class="comment"># - Make sure the INPUT points to the root of the source tree</span></div>
<div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;<span class="comment"># - Run doxygen as normal</span></div>
<div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;<span class="comment"># Doxygen will invoke htags (and that will in turn invoke gtags), so these</span></div>
<div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;<span class="comment"># tools must be available from the command line (i.e. in the search path).</span></div>
<div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;<span class="comment"># The result: instead of the source browser generated by doxygen, the links to</span></div>
<div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;<span class="comment"># source code will now point to the output of htags.</span></div>
<div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;<span class="comment"># This tag requires that the tag SOURCE_BROWSER is set to YES.</span></div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;<span class="comment">USE_HTAGS              = NO</span></div>
<div class="line"><a name="l01000"></a><span class="lineno"> 1000</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;<span class="comment"># If the VERBATIM_HEADERS tag is set the YES then doxygen will generate a</span></div>
<div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;<span class="comment"># verbatim copy of the header file for each class for which an include is</span></div>
<div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;<span class="comment"># specified. Set to NO to disable this.</span></div>
<div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;<span class="comment"># See also: Section \class.</span></div>
<div class="line"><a name="l01005"></a><span class="lineno"> 1005</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;<span class="comment">VERBATIM_HEADERS       = YES</span></div>
<div class="line"><a name="l01008"></a><span class="lineno"> 1008</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01009"></a><span class="lineno"> 1009</span>&#160;<span class="comment"># If the CLANG_ASSISTED_PARSING tag is set to YES then doxygen will use the</span></div>
<div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;<span class="comment"># clang parser (see: http://clang.llvm.org/) for more accurate parsing at the</span></div>
<div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;<span class="comment"># cost of reduced performance. This can be particularly helpful with template</span></div>
<div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160;<span class="comment"># rich C++ code for which doxygen&#39;s built-in parser lacks the necessary type</span></div>
<div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;<span class="comment"># information.</span></div>
<div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;<span class="comment"># Note: The availability of this option depends on whether or not doxygen was</span></div>
<div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;<span class="comment"># compiled with the --with-libclang option.</span></div>
<div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01017"></a><span class="lineno"> 1017</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;<span class="comment">CLANG_ASSISTED_PARSING = NO</span></div>
<div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;<span class="comment"># If clang assisted parsing is enabled you can provide the compiler with command</span></div>
<div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;<span class="comment"># line options that you would normally use when invoking the compiler. Note that</span></div>
<div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;<span class="comment"># the include paths will already be set by doxygen for the files and directories</span></div>
<div class="line"><a name="l01023"></a><span class="lineno"> 1023</span>&#160;<span class="comment"># specified with INPUT and INCLUDE_PATH.</span></div>
<div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;<span class="comment"># This tag requires that the tag CLANG_ASSISTED_PARSING is set to YES.</span></div>
<div class="line"><a name="l01025"></a><span class="lineno"> 1025</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;<span class="comment">CLANG_OPTIONS          = </span></div>
<div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01029"></a><span class="lineno"> 1029</span>&#160;<span class="comment"># Configuration options related to the alphabetical class index</span></div>
<div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;<span class="comment"># If the ALPHABETICAL_INDEX tag is set to YES, an alphabetical index of all</span></div>
<div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;<span class="comment"># compounds will be generated. Enable this if the project contains a lot of</span></div>
<div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;<span class="comment"># classes, structs, unions or interfaces.</span></div>
<div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;<span class="comment">ALPHABETICAL_INDEX     = NO</span></div>
<div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;<span class="comment"># The COLS_IN_ALPHA_INDEX tag can be used to specify the number of columns in</span></div>
<div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;<span class="comment"># which the alphabetical index list will be split.</span></div>
<div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;<span class="comment"># Minimum value: 1, maximum value: 20, default value: 5.</span></div>
<div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;<span class="comment"># This tag requires that the tag ALPHABETICAL_INDEX is set to YES.</span></div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;<span class="comment">COLS_IN_ALPHA_INDEX    = 5</span></div>
<div class="line"><a name="l01045"></a><span class="lineno"> 1045</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160;<span class="comment"># In case all classes in a project start with a common prefix, all classes will</span></div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;<span class="comment"># be put under the same header in the alphabetical index. The IGNORE_PREFIX tag</span></div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;<span class="comment"># can be used to specify a prefix (or a list of prefixes) that should be ignored</span></div>
<div class="line"><a name="l01049"></a><span class="lineno"> 1049</span>&#160;<span class="comment"># while generating the index headers.</span></div>
<div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;<span class="comment"># This tag requires that the tag ALPHABETICAL_INDEX is set to YES.</span></div>
<div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;<span class="comment">IGNORE_PREFIX          = </span></div>
<div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;<span class="comment"># Configuration options related to the HTML output</span></div>
<div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01057"></a><span class="lineno"> 1057</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01058"></a><span class="lineno"> 1058</span>&#160;<span class="comment"># If the GENERATE_HTML tag is set to YES, doxygen will generate HTML output</span></div>
<div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01061"></a><span class="lineno"> 1061</span>&#160;<span class="comment">GENERATE_HTML          = YES</span></div>
<div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;<span class="comment"># The HTML_OUTPUT tag is used to specify where the HTML docs will be put. If a</span></div>
<div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;<span class="comment"># relative path is entered the value of OUTPUT_DIRECTORY will be put in front of</span></div>
<div class="line"><a name="l01065"></a><span class="lineno"> 1065</span>&#160;<span class="comment"># it.</span></div>
<div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;<span class="comment"># The default directory is: html.</span></div>
<div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;<span class="comment">HTML_OUTPUT            = html</span></div>
<div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;<span class="comment"># The HTML_FILE_EXTENSION tag can be used to specify the file extension for each</span></div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;<span class="comment"># generated HTML page (for example: .htm, .php, .asp).</span></div>
<div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;<span class="comment"># The default value is: .html.</span></div>
<div class="line"><a name="l01074"></a><span class="lineno"> 1074</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;<span class="comment">HTML_FILE_EXTENSION    = .html</span></div>
<div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;<span class="comment"># The HTML_HEADER tag can be used to specify a user-defined HTML header file for</span></div>
<div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;<span class="comment"># each generated HTML page. If the tag is left blank doxygen will generate a</span></div>
<div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;<span class="comment"># standard header.</span></div>
<div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;<span class="comment"># To get valid HTML the header file that includes any scripts and style sheets</span></div>
<div class="line"><a name="l01083"></a><span class="lineno"> 1083</span>&#160;<span class="comment"># that doxygen needs, which is dependent on the configuration options used (e.g.</span></div>
<div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;<span class="comment"># the setting GENERATE_TREEVIEW). It is highly recommended to start with a</span></div>
<div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160;<span class="comment"># default header using</span></div>
<div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;<span class="comment"># doxygen -w html new_header.html new_footer.html new_stylesheet.css</span></div>
<div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;<span class="comment"># YourConfigFile</span></div>
<div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;<span class="comment"># and then modify the file new_header.html. See also section &quot;Doxygen usage&quot;</span></div>
<div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;<span class="comment"># for information on how to generate the default header that doxygen normally</span></div>
<div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;<span class="comment"># uses.</span></div>
<div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160;<span class="comment"># Note: The header is subject to change so you typically have to regenerate the</span></div>
<div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;<span class="comment"># default header when upgrading to a newer version of doxygen. For a description</span></div>
<div class="line"><a name="l01093"></a><span class="lineno"> 1093</span>&#160;<span class="comment"># of the possible markers and block names see the documentation.</span></div>
<div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;<span class="comment">HTML_HEADER            = </span></div>
<div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;<span class="comment"># The HTML_FOOTER tag can be used to specify a user-defined HTML footer for each</span></div>
<div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;<span class="comment"># generated HTML page. If the tag is left blank doxygen will generate a standard</span></div>
<div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;<span class="comment"># footer. See HTML_HEADER for more information on how to generate a default</span></div>
<div class="line"><a name="l01101"></a><span class="lineno"> 1101</span>&#160;<span class="comment"># footer and what special commands can be used inside the footer. See also</span></div>
<div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;<span class="comment"># section &quot;Doxygen usage&quot; for information on how to generate the default footer</span></div>
<div class="line"><a name="l01103"></a><span class="lineno"> 1103</span>&#160;<span class="comment"># that doxygen normally uses.</span></div>
<div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01106"></a><span class="lineno"> 1106</span>&#160;<span class="comment">HTML_FOOTER            = </span></div>
<div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;<span class="comment"># The HTML_STYLESHEET tag can be used to specify a user-defined cascading style</span></div>
<div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;<span class="comment"># sheet that is used by each HTML page. It can be used to fine-tune the look of</span></div>
<div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;<span class="comment"># the HTML output. If left blank doxygen will generate a default style sheet.</span></div>
<div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;<span class="comment"># See also section &quot;Doxygen usage&quot; for information on how to generate the style</span></div>
<div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;<span class="comment"># sheet that doxygen normally uses.</span></div>
<div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;<span class="comment"># Note: It is recommended to use HTML_EXTRA_STYLESHEET instead of this tag, as</span></div>
<div class="line"><a name="l01114"></a><span class="lineno"> 1114</span>&#160;<span class="comment"># it is more robust and this tag (HTML_STYLESHEET) will in the future become</span></div>
<div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160;<span class="comment"># obsolete.</span></div>
<div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01118"></a><span class="lineno"> 1118</span>&#160;<span class="comment">HTML_STYLESHEET        = </span></div>
<div class="line"><a name="l01119"></a><span class="lineno"> 1119</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160;<span class="comment"># The HTML_EXTRA_STYLESHEET tag can be used to specify additional user-defined</span></div>
<div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;<span class="comment"># cascading style sheets that are included after the standard style sheets</span></div>
<div class="line"><a name="l01122"></a><span class="lineno"> 1122</span>&#160;<span class="comment"># created by doxygen. Using this option one can overrule certain style aspects.</span></div>
<div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160;<span class="comment"># This is preferred over using HTML_STYLESHEET since it does not replace the</span></div>
<div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160;<span class="comment"># standard style sheet and is therefore more robust against future updates.</span></div>
<div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;<span class="comment"># Doxygen will copy the style sheet files to the output directory.</span></div>
<div class="line"><a name="l01126"></a><span class="lineno"> 1126</span>&#160;<span class="comment"># Note: The order of the extra style sheet files is of importance (e.g. the last</span></div>
<div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;<span class="comment"># style sheet in the list overrules the setting of the previous ones in the</span></div>
<div class="line"><a name="l01128"></a><span class="lineno"> 1128</span>&#160;<span class="comment"># list). For an example see the documentation.</span></div>
<div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01131"></a><span class="lineno"> 1131</span>&#160;<span class="comment">HTML_EXTRA_STYLESHEET  = </span></div>
<div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;<span class="comment"># The HTML_EXTRA_FILES tag can be used to specify one or more extra images or</span></div>
<div class="line"><a name="l01134"></a><span class="lineno"> 1134</span>&#160;<span class="comment"># other source files which should be copied to the HTML output directory. Note</span></div>
<div class="line"><a name="l01135"></a><span class="lineno"> 1135</span>&#160;<span class="comment"># that these files will be copied to the base HTML output directory. Use the</span></div>
<div class="line"><a name="l01136"></a><span class="lineno"> 1136</span>&#160;<span class="comment"># $relpath^ marker in the HTML_HEADER and/or HTML_FOOTER files to load these</span></div>
<div class="line"><a name="l01137"></a><span class="lineno"> 1137</span>&#160;<span class="comment"># files. In the HTML_STYLESHEET file, use the file name only. Also note that the</span></div>
<div class="line"><a name="l01138"></a><span class="lineno"> 1138</span>&#160;<span class="comment"># files will be copied as-is; there are no commands or markers available.</span></div>
<div class="line"><a name="l01139"></a><span class="lineno"> 1139</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01140"></a><span class="lineno"> 1140</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01141"></a><span class="lineno"> 1141</span>&#160;<span class="comment">HTML_EXTRA_FILES       = </span></div>
<div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01143"></a><span class="lineno"> 1143</span>&#160;<span class="comment"># The HTML_COLORSTYLE_HUE tag controls the color of the HTML output. Doxygen</span></div>
<div class="line"><a name="l01144"></a><span class="lineno"> 1144</span>&#160;<span class="comment"># will adjust the colors in the style sheet and background images according to</span></div>
<div class="line"><a name="l01145"></a><span class="lineno"> 1145</span>&#160;<span class="comment"># this color. Hue is specified as an angle on a colorwheel, see</span></div>
<div class="line"><a name="l01146"></a><span class="lineno"> 1146</span>&#160;<span class="comment"># http://en.wikipedia.org/wiki/Hue for more information. For instance the value</span></div>
<div class="line"><a name="l01147"></a><span class="lineno"> 1147</span>&#160;<span class="comment"># 0 represents red, 60 is yellow, 120 is green, 180 is cyan, 240 is blue, 300</span></div>
<div class="line"><a name="l01148"></a><span class="lineno"> 1148</span>&#160;<span class="comment"># purple, and 360 is red again.</span></div>
<div class="line"><a name="l01149"></a><span class="lineno"> 1149</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 359, default value: 220.</span></div>
<div class="line"><a name="l01150"></a><span class="lineno"> 1150</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01151"></a><span class="lineno"> 1151</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01152"></a><span class="lineno"> 1152</span>&#160;<span class="comment">HTML_COLORSTYLE_HUE    = 220</span></div>
<div class="line"><a name="l01153"></a><span class="lineno"> 1153</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01154"></a><span class="lineno"> 1154</span>&#160;<span class="comment"># The HTML_COLORSTYLE_SAT tag controls the purity (or saturation) of the colors</span></div>
<div class="line"><a name="l01155"></a><span class="lineno"> 1155</span>&#160;<span class="comment"># in the HTML output. For a value of 0 the output will use grayscales only. A</span></div>
<div class="line"><a name="l01156"></a><span class="lineno"> 1156</span>&#160;<span class="comment"># value of 255 will produce the most vivid colors.</span></div>
<div class="line"><a name="l01157"></a><span class="lineno"> 1157</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 255, default value: 100.</span></div>
<div class="line"><a name="l01158"></a><span class="lineno"> 1158</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01159"></a><span class="lineno"> 1159</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01160"></a><span class="lineno"> 1160</span>&#160;<span class="comment">HTML_COLORSTYLE_SAT    = 100</span></div>
<div class="line"><a name="l01161"></a><span class="lineno"> 1161</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01162"></a><span class="lineno"> 1162</span>&#160;<span class="comment"># The HTML_COLORSTYLE_GAMMA tag controls the gamma correction applied to the</span></div>
<div class="line"><a name="l01163"></a><span class="lineno"> 1163</span>&#160;<span class="comment"># luminance component of the colors in the HTML output. Values below 100</span></div>
<div class="line"><a name="l01164"></a><span class="lineno"> 1164</span>&#160;<span class="comment"># gradually make the output lighter, whereas values above 100 make the output</span></div>
<div class="line"><a name="l01165"></a><span class="lineno"> 1165</span>&#160;<span class="comment"># darker. The value divided by 100 is the actual gamma applied, so 80 represents</span></div>
<div class="line"><a name="l01166"></a><span class="lineno"> 1166</span>&#160;<span class="comment"># a gamma of 0.8, The value 220 represents a gamma of 2.2, and 100 does not</span></div>
<div class="line"><a name="l01167"></a><span class="lineno"> 1167</span>&#160;<span class="comment"># change the gamma.</span></div>
<div class="line"><a name="l01168"></a><span class="lineno"> 1168</span>&#160;<span class="comment"># Minimum value: 40, maximum value: 240, default value: 80.</span></div>
<div class="line"><a name="l01169"></a><span class="lineno"> 1169</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01170"></a><span class="lineno"> 1170</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01171"></a><span class="lineno"> 1171</span>&#160;<span class="comment">HTML_COLORSTYLE_GAMMA  = 80</span></div>
<div class="line"><a name="l01172"></a><span class="lineno"> 1172</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01173"></a><span class="lineno"> 1173</span>&#160;<span class="comment"># If the HTML_TIMESTAMP tag is set to YES then the footer of each generated HTML</span></div>
<div class="line"><a name="l01174"></a><span class="lineno"> 1174</span>&#160;<span class="comment"># page will contain the date and time when the page was generated. Setting this</span></div>
<div class="line"><a name="l01175"></a><span class="lineno"> 1175</span>&#160;<span class="comment"># to YES can help to show when doxygen was last run and thus if the</span></div>
<div class="line"><a name="l01176"></a><span class="lineno"> 1176</span>&#160;<span class="comment"># documentation is up to date.</span></div>
<div class="line"><a name="l01177"></a><span class="lineno"> 1177</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01179"></a><span class="lineno"> 1179</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01180"></a><span class="lineno"> 1180</span>&#160;<span class="comment">HTML_TIMESTAMP         = NO</span></div>
<div class="line"><a name="l01181"></a><span class="lineno"> 1181</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01182"></a><span class="lineno"> 1182</span>&#160;<span class="comment"># If the HTML_DYNAMIC_SECTIONS tag is set to YES then the generated HTML</span></div>
<div class="line"><a name="l01183"></a><span class="lineno"> 1183</span>&#160;<span class="comment"># documentation will contain sections that can be hidden and shown after the</span></div>
<div class="line"><a name="l01184"></a><span class="lineno"> 1184</span>&#160;<span class="comment"># page has loaded.</span></div>
<div class="line"><a name="l01185"></a><span class="lineno"> 1185</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01186"></a><span class="lineno"> 1186</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01187"></a><span class="lineno"> 1187</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01188"></a><span class="lineno"> 1188</span>&#160;<span class="comment">HTML_DYNAMIC_SECTIONS  = NO</span></div>
<div class="line"><a name="l01189"></a><span class="lineno"> 1189</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01190"></a><span class="lineno"> 1190</span>&#160;<span class="comment"># With HTML_INDEX_NUM_ENTRIES one can control the preferred number of entries</span></div>
<div class="line"><a name="l01191"></a><span class="lineno"> 1191</span>&#160;<span class="comment"># shown in the various tree structured indices initially; the user can expand</span></div>
<div class="line"><a name="l01192"></a><span class="lineno"> 1192</span>&#160;<span class="comment"># and collapse entries dynamically later on. Doxygen will expand the tree to</span></div>
<div class="line"><a name="l01193"></a><span class="lineno"> 1193</span>&#160;<span class="comment"># such a level that at most the specified number of entries are visible (unless</span></div>
<div class="line"><a name="l01194"></a><span class="lineno"> 1194</span>&#160;<span class="comment"># a fully collapsed tree already exceeds this amount). So setting the number of</span></div>
<div class="line"><a name="l01195"></a><span class="lineno"> 1195</span>&#160;<span class="comment"># entries 1 will produce a full collapsed tree by default. 0 is a special value</span></div>
<div class="line"><a name="l01196"></a><span class="lineno"> 1196</span>&#160;<span class="comment"># representing an infinite number of entries and will result in a full expanded</span></div>
<div class="line"><a name="l01197"></a><span class="lineno"> 1197</span>&#160;<span class="comment"># tree by default.</span></div>
<div class="line"><a name="l01198"></a><span class="lineno"> 1198</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 9999, default value: 100.</span></div>
<div class="line"><a name="l01199"></a><span class="lineno"> 1199</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01200"></a><span class="lineno"> 1200</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01201"></a><span class="lineno"> 1201</span>&#160;<span class="comment">HTML_INDEX_NUM_ENTRIES = 100</span></div>
<div class="line"><a name="l01202"></a><span class="lineno"> 1202</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01203"></a><span class="lineno"> 1203</span>&#160;<span class="comment"># If the GENERATE_DOCSET tag is set to YES, additional index files will be</span></div>
<div class="line"><a name="l01204"></a><span class="lineno"> 1204</span>&#160;<span class="comment"># generated that can be used as input for Apple&#39;s Xcode 3 integrated development</span></div>
<div class="line"><a name="l01205"></a><span class="lineno"> 1205</span>&#160;<span class="comment"># environment (see: http://developer.apple.com/tools/xcode/), introduced with</span></div>
<div class="line"><a name="l01206"></a><span class="lineno"> 1206</span>&#160;<span class="comment"># OSX 10.5 (Leopard). To create a documentation set, doxygen will generate a</span></div>
<div class="line"><a name="l01207"></a><span class="lineno"> 1207</span>&#160;<span class="comment"># Makefile in the HTML output directory. Running make will produce the docset in</span></div>
<div class="line"><a name="l01208"></a><span class="lineno"> 1208</span>&#160;<span class="comment"># that directory and running make install will install the docset in</span></div>
<div class="line"><a name="l01209"></a><span class="lineno"> 1209</span>&#160;<span class="comment"># ~/Library/Developer/Shared/Documentation/DocSets so that Xcode will find it at</span></div>
<div class="line"><a name="l01210"></a><span class="lineno"> 1210</span>&#160;<span class="comment"># startup. See http://developer.apple.com/tools/creatingdocsetswithdoxygen.html</span></div>
<div class="line"><a name="l01211"></a><span class="lineno"> 1211</span>&#160;<span class="comment"># for more information.</span></div>
<div class="line"><a name="l01212"></a><span class="lineno"> 1212</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01213"></a><span class="lineno"> 1213</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01214"></a><span class="lineno"> 1214</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01215"></a><span class="lineno"> 1215</span>&#160;<span class="comment">GENERATE_DOCSET        = NO</span></div>
<div class="line"><a name="l01216"></a><span class="lineno"> 1216</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01217"></a><span class="lineno"> 1217</span>&#160;<span class="comment"># This tag determines the name of the docset feed. A documentation feed provides</span></div>
<div class="line"><a name="l01218"></a><span class="lineno"> 1218</span>&#160;<span class="comment"># an umbrella under which multiple documentation sets from a single provider</span></div>
<div class="line"><a name="l01219"></a><span class="lineno"> 1219</span>&#160;<span class="comment"># (such as a company or product suite) can be grouped.</span></div>
<div class="line"><a name="l01220"></a><span class="lineno"> 1220</span>&#160;<span class="comment"># The default value is: Doxygen generated docs.</span></div>
<div class="line"><a name="l01221"></a><span class="lineno"> 1221</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_DOCSET is set to YES.</span></div>
<div class="line"><a name="l01222"></a><span class="lineno"> 1222</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01223"></a><span class="lineno"> 1223</span>&#160;<span class="comment">DOCSET_FEEDNAME        = &quot;Doxygen generated docs&quot;</span></div>
<div class="line"><a name="l01224"></a><span class="lineno"> 1224</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01225"></a><span class="lineno"> 1225</span>&#160;<span class="comment"># This tag specifies a string that should uniquely identify the documentation</span></div>
<div class="line"><a name="l01226"></a><span class="lineno"> 1226</span>&#160;<span class="comment"># set bundle. This should be a reverse domain-name style string, e.g.</span></div>
<div class="line"><a name="l01227"></a><span class="lineno"> 1227</span>&#160;<span class="comment"># com.mycompany.MyDocSet. Doxygen will append .docset to the name.</span></div>
<div class="line"><a name="l01228"></a><span class="lineno"> 1228</span>&#160;<span class="comment"># The default value is: org.doxygen.Project.</span></div>
<div class="line"><a name="l01229"></a><span class="lineno"> 1229</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_DOCSET is set to YES.</span></div>
<div class="line"><a name="l01230"></a><span class="lineno"> 1230</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01231"></a><span class="lineno"> 1231</span>&#160;<span class="comment">DOCSET_BUNDLE_ID       = org.doxygen.Project</span></div>
<div class="line"><a name="l01232"></a><span class="lineno"> 1232</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01233"></a><span class="lineno"> 1233</span>&#160;<span class="comment"># The DOCSET_PUBLISHER_ID tag specifies a string that should uniquely identify</span></div>
<div class="line"><a name="l01234"></a><span class="lineno"> 1234</span>&#160;<span class="comment"># the documentation publisher. This should be a reverse domain-name style</span></div>
<div class="line"><a name="l01235"></a><span class="lineno"> 1235</span>&#160;<span class="comment"># string, e.g. com.mycompany.MyDocSet.documentation.</span></div>
<div class="line"><a name="l01236"></a><span class="lineno"> 1236</span>&#160;<span class="comment"># The default value is: org.doxygen.Publisher.</span></div>
<div class="line"><a name="l01237"></a><span class="lineno"> 1237</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_DOCSET is set to YES.</span></div>
<div class="line"><a name="l01238"></a><span class="lineno"> 1238</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01239"></a><span class="lineno"> 1239</span>&#160;<span class="comment">DOCSET_PUBLISHER_ID    = org.doxygen.Publisher</span></div>
<div class="line"><a name="l01240"></a><span class="lineno"> 1240</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01241"></a><span class="lineno"> 1241</span>&#160;<span class="comment"># The DOCSET_PUBLISHER_NAME tag identifies the documentation publisher.</span></div>
<div class="line"><a name="l01242"></a><span class="lineno"> 1242</span>&#160;<span class="comment"># The default value is: Publisher.</span></div>
<div class="line"><a name="l01243"></a><span class="lineno"> 1243</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_DOCSET is set to YES.</span></div>
<div class="line"><a name="l01244"></a><span class="lineno"> 1244</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01245"></a><span class="lineno"> 1245</span>&#160;<span class="comment">DOCSET_PUBLISHER_NAME  = Publisher</span></div>
<div class="line"><a name="l01246"></a><span class="lineno"> 1246</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01247"></a><span class="lineno"> 1247</span>&#160;<span class="comment"># If the GENERATE_HTMLHELP tag is set to YES then doxygen generates three</span></div>
<div class="line"><a name="l01248"></a><span class="lineno"> 1248</span>&#160;<span class="comment"># additional HTML index files: index.hhp, index.hhc, and index.hhk. The</span></div>
<div class="line"><a name="l01249"></a><span class="lineno"> 1249</span>&#160;<span class="comment"># index.hhp is a project file that can be read by Microsoft&#39;s HTML Help Workshop</span></div>
<div class="line"><a name="l01250"></a><span class="lineno"> 1250</span>&#160;<span class="comment"># (see: http://www.microsoft.com/en-us/download/details.aspx?id=21138) on</span></div>
<div class="line"><a name="l01251"></a><span class="lineno"> 1251</span>&#160;<span class="comment"># Windows.</span></div>
<div class="line"><a name="l01252"></a><span class="lineno"> 1252</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01253"></a><span class="lineno"> 1253</span>&#160;<span class="comment"># The HTML Help Workshop contains a compiler that can convert all HTML output</span></div>
<div class="line"><a name="l01254"></a><span class="lineno"> 1254</span>&#160;<span class="comment"># generated by doxygen into a single compiled HTML file (.chm). Compiled HTML</span></div>
<div class="line"><a name="l01255"></a><span class="lineno"> 1255</span>&#160;<span class="comment"># files are now used as the Windows 98 help format, and will replace the old</span></div>
<div class="line"><a name="l01256"></a><span class="lineno"> 1256</span>&#160;<span class="comment"># Windows help format (.hlp) on all Windows platforms in the future. Compressed</span></div>
<div class="line"><a name="l01257"></a><span class="lineno"> 1257</span>&#160;<span class="comment"># HTML files also contain an index, a table of contents, and you can search for</span></div>
<div class="line"><a name="l01258"></a><span class="lineno"> 1258</span>&#160;<span class="comment"># words in the documentation. The HTML workshop also contains a viewer for</span></div>
<div class="line"><a name="l01259"></a><span class="lineno"> 1259</span>&#160;<span class="comment"># compressed HTML files.</span></div>
<div class="line"><a name="l01260"></a><span class="lineno"> 1260</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01261"></a><span class="lineno"> 1261</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01262"></a><span class="lineno"> 1262</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01263"></a><span class="lineno"> 1263</span>&#160;<span class="comment">GENERATE_HTMLHELP      = NO</span></div>
<div class="line"><a name="l01264"></a><span class="lineno"> 1264</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01265"></a><span class="lineno"> 1265</span>&#160;<span class="comment"># The CHM_FILE tag can be used to specify the file name of the resulting .chm</span></div>
<div class="line"><a name="l01266"></a><span class="lineno"> 1266</span>&#160;<span class="comment"># file. You can add a path in front of the file if the result should not be</span></div>
<div class="line"><a name="l01267"></a><span class="lineno"> 1267</span>&#160;<span class="comment"># written to the html output directory.</span></div>
<div class="line"><a name="l01268"></a><span class="lineno"> 1268</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTMLHELP is set to YES.</span></div>
<div class="line"><a name="l01269"></a><span class="lineno"> 1269</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01270"></a><span class="lineno"> 1270</span>&#160;<span class="comment">CHM_FILE               = </span></div>
<div class="line"><a name="l01271"></a><span class="lineno"> 1271</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01272"></a><span class="lineno"> 1272</span>&#160;<span class="comment"># The HHC_LOCATION tag can be used to specify the location (absolute path</span></div>
<div class="line"><a name="l01273"></a><span class="lineno"> 1273</span>&#160;<span class="comment"># including file name) of the HTML help compiler (hhc.exe). If non-empty,</span></div>
<div class="line"><a name="l01274"></a><span class="lineno"> 1274</span>&#160;<span class="comment"># doxygen will try to run the HTML help compiler on the generated index.hhp.</span></div>
<div class="line"><a name="l01275"></a><span class="lineno"> 1275</span>&#160;<span class="comment"># The file has to be specified with full path.</span></div>
<div class="line"><a name="l01276"></a><span class="lineno"> 1276</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTMLHELP is set to YES.</span></div>
<div class="line"><a name="l01277"></a><span class="lineno"> 1277</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01278"></a><span class="lineno"> 1278</span>&#160;<span class="comment">HHC_LOCATION           = </span></div>
<div class="line"><a name="l01279"></a><span class="lineno"> 1279</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01280"></a><span class="lineno"> 1280</span>&#160;<span class="comment"># The GENERATE_CHI flag controls if a separate .chi index file is generated</span></div>
<div class="line"><a name="l01281"></a><span class="lineno"> 1281</span>&#160;<span class="comment"># (YES) or that it should be included in the master .chm file (NO).</span></div>
<div class="line"><a name="l01282"></a><span class="lineno"> 1282</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01283"></a><span class="lineno"> 1283</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTMLHELP is set to YES.</span></div>
<div class="line"><a name="l01284"></a><span class="lineno"> 1284</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01285"></a><span class="lineno"> 1285</span>&#160;<span class="comment">GENERATE_CHI           = NO</span></div>
<div class="line"><a name="l01286"></a><span class="lineno"> 1286</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01287"></a><span class="lineno"> 1287</span>&#160;<span class="comment"># The CHM_INDEX_ENCODING is used to encode HtmlHelp index (hhk), content (hhc)</span></div>
<div class="line"><a name="l01288"></a><span class="lineno"> 1288</span>&#160;<span class="comment"># and project file content.</span></div>
<div class="line"><a name="l01289"></a><span class="lineno"> 1289</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTMLHELP is set to YES.</span></div>
<div class="line"><a name="l01290"></a><span class="lineno"> 1290</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01291"></a><span class="lineno"> 1291</span>&#160;<span class="comment">CHM_INDEX_ENCODING     = </span></div>
<div class="line"><a name="l01292"></a><span class="lineno"> 1292</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01293"></a><span class="lineno"> 1293</span>&#160;<span class="comment"># The BINARY_TOC flag controls whether a binary table of contents is generated</span></div>
<div class="line"><a name="l01294"></a><span class="lineno"> 1294</span>&#160;<span class="comment"># (YES) or a normal table of contents (NO) in the .chm file. Furthermore it</span></div>
<div class="line"><a name="l01295"></a><span class="lineno"> 1295</span>&#160;<span class="comment"># enables the Previous and Next buttons.</span></div>
<div class="line"><a name="l01296"></a><span class="lineno"> 1296</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01297"></a><span class="lineno"> 1297</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTMLHELP is set to YES.</span></div>
<div class="line"><a name="l01298"></a><span class="lineno"> 1298</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01299"></a><span class="lineno"> 1299</span>&#160;<span class="comment">BINARY_TOC             = NO</span></div>
<div class="line"><a name="l01300"></a><span class="lineno"> 1300</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01301"></a><span class="lineno"> 1301</span>&#160;<span class="comment"># The TOC_EXPAND flag can be set to YES to add extra items for group members to</span></div>
<div class="line"><a name="l01302"></a><span class="lineno"> 1302</span>&#160;<span class="comment"># the table of contents of the HTML help documentation and to the tree view.</span></div>
<div class="line"><a name="l01303"></a><span class="lineno"> 1303</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01304"></a><span class="lineno"> 1304</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTMLHELP is set to YES.</span></div>
<div class="line"><a name="l01305"></a><span class="lineno"> 1305</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01306"></a><span class="lineno"> 1306</span>&#160;<span class="comment">TOC_EXPAND             = NO</span></div>
<div class="line"><a name="l01307"></a><span class="lineno"> 1307</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01308"></a><span class="lineno"> 1308</span>&#160;<span class="comment"># If the GENERATE_QHP tag is set to YES and both QHP_NAMESPACE and</span></div>
<div class="line"><a name="l01309"></a><span class="lineno"> 1309</span>&#160;<span class="comment"># QHP_VIRTUAL_FOLDER are set, an additional index file will be generated that</span></div>
<div class="line"><a name="l01310"></a><span class="lineno"> 1310</span>&#160;<span class="comment"># can be used as input for Qt&#39;s qhelpgenerator to generate a Qt Compressed Help</span></div>
<div class="line"><a name="l01311"></a><span class="lineno"> 1311</span>&#160;<span class="comment"># (.qch) of the generated HTML documentation.</span></div>
<div class="line"><a name="l01312"></a><span class="lineno"> 1312</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01313"></a><span class="lineno"> 1313</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01314"></a><span class="lineno"> 1314</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01315"></a><span class="lineno"> 1315</span>&#160;<span class="comment">GENERATE_QHP           = NO</span></div>
<div class="line"><a name="l01316"></a><span class="lineno"> 1316</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01317"></a><span class="lineno"> 1317</span>&#160;<span class="comment"># If the QHG_LOCATION tag is specified, the QCH_FILE tag can be used to specify</span></div>
<div class="line"><a name="l01318"></a><span class="lineno"> 1318</span>&#160;<span class="comment"># the file name of the resulting .qch file. The path specified is relative to</span></div>
<div class="line"><a name="l01319"></a><span class="lineno"> 1319</span>&#160;<span class="comment"># the HTML output folder.</span></div>
<div class="line"><a name="l01320"></a><span class="lineno"> 1320</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01321"></a><span class="lineno"> 1321</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01322"></a><span class="lineno"> 1322</span>&#160;<span class="comment">QCH_FILE               = </span></div>
<div class="line"><a name="l01323"></a><span class="lineno"> 1323</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01324"></a><span class="lineno"> 1324</span>&#160;<span class="comment"># The QHP_NAMESPACE tag specifies the namespace to use when generating Qt Help</span></div>
<div class="line"><a name="l01325"></a><span class="lineno"> 1325</span>&#160;<span class="comment"># Project output. For more information please see Qt Help Project / Namespace</span></div>
<div class="line"><a name="l01326"></a><span class="lineno"> 1326</span>&#160;<span class="comment"># (see: http://qt-project.org/doc/qt-4.8/qthelpproject.html#namespace).</span></div>
<div class="line"><a name="l01327"></a><span class="lineno"> 1327</span>&#160;<span class="comment"># The default value is: org.doxygen.Project.</span></div>
<div class="line"><a name="l01328"></a><span class="lineno"> 1328</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01329"></a><span class="lineno"> 1329</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01330"></a><span class="lineno"> 1330</span>&#160;<span class="comment">QHP_NAMESPACE          = org.doxygen.Project</span></div>
<div class="line"><a name="l01331"></a><span class="lineno"> 1331</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01332"></a><span class="lineno"> 1332</span>&#160;<span class="comment"># The QHP_VIRTUAL_FOLDER tag specifies the namespace to use when generating Qt</span></div>
<div class="line"><a name="l01333"></a><span class="lineno"> 1333</span>&#160;<span class="comment"># Help Project output. For more information please see Qt Help Project / Virtual</span></div>
<div class="line"><a name="l01334"></a><span class="lineno"> 1334</span>&#160;<span class="comment"># Folders (see: http://qt-project.org/doc/qt-4.8/qthelpproject.html#virtual-</span></div>
<div class="line"><a name="l01335"></a><span class="lineno"> 1335</span>&#160;<span class="comment"># folders).</span></div>
<div class="line"><a name="l01336"></a><span class="lineno"> 1336</span>&#160;<span class="comment"># The default value is: doc.</span></div>
<div class="line"><a name="l01337"></a><span class="lineno"> 1337</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01338"></a><span class="lineno"> 1338</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01339"></a><span class="lineno"> 1339</span>&#160;<span class="comment">QHP_VIRTUAL_FOLDER     = doc</span></div>
<div class="line"><a name="l01340"></a><span class="lineno"> 1340</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01341"></a><span class="lineno"> 1341</span>&#160;<span class="comment"># If the QHP_CUST_FILTER_NAME tag is set, it specifies the name of a custom</span></div>
<div class="line"><a name="l01342"></a><span class="lineno"> 1342</span>&#160;<span class="comment"># filter to add. For more information please see Qt Help Project / Custom</span></div>
<div class="line"><a name="l01343"></a><span class="lineno"> 1343</span>&#160;<span class="comment"># Filters (see: http://qt-project.org/doc/qt-4.8/qthelpproject.html#custom-</span></div>
<div class="line"><a name="l01344"></a><span class="lineno"> 1344</span>&#160;<span class="comment"># filters).</span></div>
<div class="line"><a name="l01345"></a><span class="lineno"> 1345</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01346"></a><span class="lineno"> 1346</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01347"></a><span class="lineno"> 1347</span>&#160;<span class="comment">QHP_CUST_FILTER_NAME   = </span></div>
<div class="line"><a name="l01348"></a><span class="lineno"> 1348</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01349"></a><span class="lineno"> 1349</span>&#160;<span class="comment"># The QHP_CUST_FILTER_ATTRS tag specifies the list of the attributes of the</span></div>
<div class="line"><a name="l01350"></a><span class="lineno"> 1350</span>&#160;<span class="comment"># custom filter to add. For more information please see Qt Help Project / Custom</span></div>
<div class="line"><a name="l01351"></a><span class="lineno"> 1351</span>&#160;<span class="comment"># Filters (see: http://qt-project.org/doc/qt-4.8/qthelpproject.html#custom-</span></div>
<div class="line"><a name="l01352"></a><span class="lineno"> 1352</span>&#160;<span class="comment"># filters).</span></div>
<div class="line"><a name="l01353"></a><span class="lineno"> 1353</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01354"></a><span class="lineno"> 1354</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01355"></a><span class="lineno"> 1355</span>&#160;<span class="comment">QHP_CUST_FILTER_ATTRS  = </span></div>
<div class="line"><a name="l01356"></a><span class="lineno"> 1356</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01357"></a><span class="lineno"> 1357</span>&#160;<span class="comment"># The QHP_SECT_FILTER_ATTRS tag specifies the list of the attributes this</span></div>
<div class="line"><a name="l01358"></a><span class="lineno"> 1358</span>&#160;<span class="comment"># project&#39;s filter section matches. Qt Help Project / Filter Attributes (see:</span></div>
<div class="line"><a name="l01359"></a><span class="lineno"> 1359</span>&#160;<span class="comment"># http://qt-project.org/doc/qt-4.8/qthelpproject.html#filter-attributes).</span></div>
<div class="line"><a name="l01360"></a><span class="lineno"> 1360</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01361"></a><span class="lineno"> 1361</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01362"></a><span class="lineno"> 1362</span>&#160;<span class="comment">QHP_SECT_FILTER_ATTRS  = </span></div>
<div class="line"><a name="l01363"></a><span class="lineno"> 1363</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01364"></a><span class="lineno"> 1364</span>&#160;<span class="comment"># The QHG_LOCATION tag can be used to specify the location of Qt&#39;s</span></div>
<div class="line"><a name="l01365"></a><span class="lineno"> 1365</span>&#160;<span class="comment"># qhelpgenerator. If non-empty doxygen will try to run qhelpgenerator on the</span></div>
<div class="line"><a name="l01366"></a><span class="lineno"> 1366</span>&#160;<span class="comment"># generated .qhp file.</span></div>
<div class="line"><a name="l01367"></a><span class="lineno"> 1367</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_QHP is set to YES.</span></div>
<div class="line"><a name="l01368"></a><span class="lineno"> 1368</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01369"></a><span class="lineno"> 1369</span>&#160;<span class="comment">QHG_LOCATION           = </span></div>
<div class="line"><a name="l01370"></a><span class="lineno"> 1370</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01371"></a><span class="lineno"> 1371</span>&#160;<span class="comment"># If the GENERATE_ECLIPSEHELP tag is set to YES, additional index files will be</span></div>
<div class="line"><a name="l01372"></a><span class="lineno"> 1372</span>&#160;<span class="comment"># generated, together with the HTML files, they form an Eclipse help plugin. To</span></div>
<div class="line"><a name="l01373"></a><span class="lineno"> 1373</span>&#160;<span class="comment"># install this plugin and make it available under the help contents menu in</span></div>
<div class="line"><a name="l01374"></a><span class="lineno"> 1374</span>&#160;<span class="comment"># Eclipse, the contents of the directory containing the HTML and XML files needs</span></div>
<div class="line"><a name="l01375"></a><span class="lineno"> 1375</span>&#160;<span class="comment"># to be copied into the plugins directory of eclipse. The name of the directory</span></div>
<div class="line"><a name="l01376"></a><span class="lineno"> 1376</span>&#160;<span class="comment"># within the plugins directory should be the same as the ECLIPSE_DOC_ID value.</span></div>
<div class="line"><a name="l01377"></a><span class="lineno"> 1377</span>&#160;<span class="comment"># After copying Eclipse needs to be restarted before the help appears.</span></div>
<div class="line"><a name="l01378"></a><span class="lineno"> 1378</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01379"></a><span class="lineno"> 1379</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01380"></a><span class="lineno"> 1380</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01381"></a><span class="lineno"> 1381</span>&#160;<span class="comment">GENERATE_ECLIPSEHELP   = NO</span></div>
<div class="line"><a name="l01382"></a><span class="lineno"> 1382</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01383"></a><span class="lineno"> 1383</span>&#160;<span class="comment"># A unique identifier for the Eclipse help plugin. When installing the plugin</span></div>
<div class="line"><a name="l01384"></a><span class="lineno"> 1384</span>&#160;<span class="comment"># the directory name containing the HTML and XML files should also have this</span></div>
<div class="line"><a name="l01385"></a><span class="lineno"> 1385</span>&#160;<span class="comment"># name. Each documentation set should have its own identifier.</span></div>
<div class="line"><a name="l01386"></a><span class="lineno"> 1386</span>&#160;<span class="comment"># The default value is: org.doxygen.Project.</span></div>
<div class="line"><a name="l01387"></a><span class="lineno"> 1387</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_ECLIPSEHELP is set to YES.</span></div>
<div class="line"><a name="l01388"></a><span class="lineno"> 1388</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01389"></a><span class="lineno"> 1389</span>&#160;<span class="comment">ECLIPSE_DOC_ID         = org.doxygen.Project</span></div>
<div class="line"><a name="l01390"></a><span class="lineno"> 1390</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01391"></a><span class="lineno"> 1391</span>&#160;<span class="comment"># If you want full control over the layout of the generated HTML pages it might</span></div>
<div class="line"><a name="l01392"></a><span class="lineno"> 1392</span>&#160;<span class="comment"># be necessary to disable the index and replace it with your own. The</span></div>
<div class="line"><a name="l01393"></a><span class="lineno"> 1393</span>&#160;<span class="comment"># DISABLE_INDEX tag can be used to turn on/off the condensed index (tabs) at top</span></div>
<div class="line"><a name="l01394"></a><span class="lineno"> 1394</span>&#160;<span class="comment"># of each HTML page. A value of NO enables the index and the value YES disables</span></div>
<div class="line"><a name="l01395"></a><span class="lineno"> 1395</span>&#160;<span class="comment"># it. Since the tabs in the index contain the same information as the navigation</span></div>
<div class="line"><a name="l01396"></a><span class="lineno"> 1396</span>&#160;<span class="comment"># tree, you can set this option to YES if you also set GENERATE_TREEVIEW to YES.</span></div>
<div class="line"><a name="l01397"></a><span class="lineno"> 1397</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01398"></a><span class="lineno"> 1398</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01399"></a><span class="lineno"> 1399</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01400"></a><span class="lineno"> 1400</span>&#160;<span class="comment">DISABLE_INDEX          = NO</span></div>
<div class="line"><a name="l01401"></a><span class="lineno"> 1401</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01402"></a><span class="lineno"> 1402</span>&#160;<span class="comment"># The GENERATE_TREEVIEW tag is used to specify whether a tree-like index</span></div>
<div class="line"><a name="l01403"></a><span class="lineno"> 1403</span>&#160;<span class="comment"># structure should be generated to display hierarchical information. If the tag</span></div>
<div class="line"><a name="l01404"></a><span class="lineno"> 1404</span>&#160;<span class="comment"># value is set to YES, a side panel will be generated containing a tree-like</span></div>
<div class="line"><a name="l01405"></a><span class="lineno"> 1405</span>&#160;<span class="comment"># index structure (just like the one that is generated for HTML Help). For this</span></div>
<div class="line"><a name="l01406"></a><span class="lineno"> 1406</span>&#160;<span class="comment"># to work a browser that supports JavaScript, DHTML, CSS and frames is required</span></div>
<div class="line"><a name="l01407"></a><span class="lineno"> 1407</span>&#160;<span class="comment"># (i.e. any modern browser). Windows users are probably better off using the</span></div>
<div class="line"><a name="l01408"></a><span class="lineno"> 1408</span>&#160;<span class="comment"># HTML help feature. Via custom style sheets (see HTML_EXTRA_STYLESHEET) one can</span></div>
<div class="line"><a name="l01409"></a><span class="lineno"> 1409</span>&#160;<span class="comment"># further fine-tune the look of the index. As an example, the default style</span></div>
<div class="line"><a name="l01410"></a><span class="lineno"> 1410</span>&#160;<span class="comment"># sheet generated by doxygen has an example that shows how to put an image at</span></div>
<div class="line"><a name="l01411"></a><span class="lineno"> 1411</span>&#160;<span class="comment"># the root of the tree instead of the PROJECT_NAME. Since the tree basically has</span></div>
<div class="line"><a name="l01412"></a><span class="lineno"> 1412</span>&#160;<span class="comment"># the same information as the tab index, you could consider setting</span></div>
<div class="line"><a name="l01413"></a><span class="lineno"> 1413</span>&#160;<span class="comment"># DISABLE_INDEX to YES when enabling this option.</span></div>
<div class="line"><a name="l01414"></a><span class="lineno"> 1414</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01415"></a><span class="lineno"> 1415</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01416"></a><span class="lineno"> 1416</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01417"></a><span class="lineno"> 1417</span>&#160;<span class="comment">GENERATE_TREEVIEW      = NO</span></div>
<div class="line"><a name="l01418"></a><span class="lineno"> 1418</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01419"></a><span class="lineno"> 1419</span>&#160;<span class="comment"># The ENUM_VALUES_PER_LINE tag can be used to set the number of enum values that</span></div>
<div class="line"><a name="l01420"></a><span class="lineno"> 1420</span>&#160;<span class="comment"># doxygen will group on one line in the generated HTML documentation.</span></div>
<div class="line"><a name="l01421"></a><span class="lineno"> 1421</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01422"></a><span class="lineno"> 1422</span>&#160;<span class="comment"># Note that a value of 0 will completely suppress the enum values from appearing</span></div>
<div class="line"><a name="l01423"></a><span class="lineno"> 1423</span>&#160;<span class="comment"># in the overview section.</span></div>
<div class="line"><a name="l01424"></a><span class="lineno"> 1424</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 20, default value: 4.</span></div>
<div class="line"><a name="l01425"></a><span class="lineno"> 1425</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01426"></a><span class="lineno"> 1426</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01427"></a><span class="lineno"> 1427</span>&#160;<span class="comment">ENUM_VALUES_PER_LINE   = 4</span></div>
<div class="line"><a name="l01428"></a><span class="lineno"> 1428</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01429"></a><span class="lineno"> 1429</span>&#160;<span class="comment"># If the treeview is enabled (see GENERATE_TREEVIEW) then this tag can be used</span></div>
<div class="line"><a name="l01430"></a><span class="lineno"> 1430</span>&#160;<span class="comment"># to set the initial width (in pixels) of the frame in which the tree is shown.</span></div>
<div class="line"><a name="l01431"></a><span class="lineno"> 1431</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 1500, default value: 250.</span></div>
<div class="line"><a name="l01432"></a><span class="lineno"> 1432</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01433"></a><span class="lineno"> 1433</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01434"></a><span class="lineno"> 1434</span>&#160;<span class="comment">TREEVIEW_WIDTH         = 250</span></div>
<div class="line"><a name="l01435"></a><span class="lineno"> 1435</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01436"></a><span class="lineno"> 1436</span>&#160;<span class="comment"># If the EXT_LINKS_IN_WINDOW option is set to YES, doxygen will open links to</span></div>
<div class="line"><a name="l01437"></a><span class="lineno"> 1437</span>&#160;<span class="comment"># external symbols imported via tag files in a separate window.</span></div>
<div class="line"><a name="l01438"></a><span class="lineno"> 1438</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01439"></a><span class="lineno"> 1439</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01440"></a><span class="lineno"> 1440</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01441"></a><span class="lineno"> 1441</span>&#160;<span class="comment">EXT_LINKS_IN_WINDOW    = NO</span></div>
<div class="line"><a name="l01442"></a><span class="lineno"> 1442</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01443"></a><span class="lineno"> 1443</span>&#160;<span class="comment"># Use this tag to change the font size of LaTeX formulas included as images in</span></div>
<div class="line"><a name="l01444"></a><span class="lineno"> 1444</span>&#160;<span class="comment"># the HTML documentation. When you change the font size after a successful</span></div>
<div class="line"><a name="l01445"></a><span class="lineno"> 1445</span>&#160;<span class="comment"># doxygen run you need to manually remove any form_*.png images from the HTML</span></div>
<div class="line"><a name="l01446"></a><span class="lineno"> 1446</span>&#160;<span class="comment"># output directory to force them to be regenerated.</span></div>
<div class="line"><a name="l01447"></a><span class="lineno"> 1447</span>&#160;<span class="comment"># Minimum value: 8, maximum value: 50, default value: 10.</span></div>
<div class="line"><a name="l01448"></a><span class="lineno"> 1448</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01449"></a><span class="lineno"> 1449</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01450"></a><span class="lineno"> 1450</span>&#160;<span class="comment">FORMULA_FONTSIZE       = 10</span></div>
<div class="line"><a name="l01451"></a><span class="lineno"> 1451</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01452"></a><span class="lineno"> 1452</span>&#160;<span class="comment"># Use the FORMULA_TRANPARENT tag to determine whether or not the images</span></div>
<div class="line"><a name="l01453"></a><span class="lineno"> 1453</span>&#160;<span class="comment"># generated for formulas are transparent PNGs. Transparent PNGs are not</span></div>
<div class="line"><a name="l01454"></a><span class="lineno"> 1454</span>&#160;<span class="comment"># supported properly for IE 6.0, but are supported on all modern browsers.</span></div>
<div class="line"><a name="l01455"></a><span class="lineno"> 1455</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01456"></a><span class="lineno"> 1456</span>&#160;<span class="comment"># Note that when changing this option you need to delete any form_*.png files in</span></div>
<div class="line"><a name="l01457"></a><span class="lineno"> 1457</span>&#160;<span class="comment"># the HTML output directory before the changes have effect.</span></div>
<div class="line"><a name="l01458"></a><span class="lineno"> 1458</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01459"></a><span class="lineno"> 1459</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01460"></a><span class="lineno"> 1460</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01461"></a><span class="lineno"> 1461</span>&#160;<span class="comment">FORMULA_TRANSPARENT    = YES</span></div>
<div class="line"><a name="l01462"></a><span class="lineno"> 1462</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01463"></a><span class="lineno"> 1463</span>&#160;<span class="comment"># Enable the USE_MATHJAX option to render LaTeX formulas using MathJax (see</span></div>
<div class="line"><a name="l01464"></a><span class="lineno"> 1464</span>&#160;<span class="comment"># http://www.mathjax.org) which uses client side Javascript for the rendering</span></div>
<div class="line"><a name="l01465"></a><span class="lineno"> 1465</span>&#160;<span class="comment"># instead of using pre-rendered bitmaps. Use this if you do not have LaTeX</span></div>
<div class="line"><a name="l01466"></a><span class="lineno"> 1466</span>&#160;<span class="comment"># installed or if you want to formulas look prettier in the HTML output. When</span></div>
<div class="line"><a name="l01467"></a><span class="lineno"> 1467</span>&#160;<span class="comment"># enabled you may also need to install MathJax separately and configure the path</span></div>
<div class="line"><a name="l01468"></a><span class="lineno"> 1468</span>&#160;<span class="comment"># to it using the MATHJAX_RELPATH option.</span></div>
<div class="line"><a name="l01469"></a><span class="lineno"> 1469</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01470"></a><span class="lineno"> 1470</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01471"></a><span class="lineno"> 1471</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01472"></a><span class="lineno"> 1472</span>&#160;<span class="comment">USE_MATHJAX            = NO</span></div>
<div class="line"><a name="l01473"></a><span class="lineno"> 1473</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01474"></a><span class="lineno"> 1474</span>&#160;<span class="comment"># When MathJax is enabled you can set the default output format to be used for</span></div>
<div class="line"><a name="l01475"></a><span class="lineno"> 1475</span>&#160;<span class="comment"># the MathJax output. See the MathJax site (see:</span></div>
<div class="line"><a name="l01476"></a><span class="lineno"> 1476</span>&#160;<span class="comment"># http://docs.mathjax.org/en/latest/output.html) for more details.</span></div>
<div class="line"><a name="l01477"></a><span class="lineno"> 1477</span>&#160;<span class="comment"># Possible values are: HTML-CSS (which is slower, but has the best</span></div>
<div class="line"><a name="l01478"></a><span class="lineno"> 1478</span>&#160;<span class="comment"># compatibility), NativeMML (i.e. MathML) and SVG.</span></div>
<div class="line"><a name="l01479"></a><span class="lineno"> 1479</span>&#160;<span class="comment"># The default value is: HTML-CSS.</span></div>
<div class="line"><a name="l01480"></a><span class="lineno"> 1480</span>&#160;<span class="comment"># This tag requires that the tag USE_MATHJAX is set to YES.</span></div>
<div class="line"><a name="l01481"></a><span class="lineno"> 1481</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01482"></a><span class="lineno"> 1482</span>&#160;<span class="comment">MATHJAX_FORMAT         = HTML-CSS</span></div>
<div class="line"><a name="l01483"></a><span class="lineno"> 1483</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01484"></a><span class="lineno"> 1484</span>&#160;<span class="comment"># When MathJax is enabled you need to specify the location relative to the HTML</span></div>
<div class="line"><a name="l01485"></a><span class="lineno"> 1485</span>&#160;<span class="comment"># output directory using the MATHJAX_RELPATH option. The destination directory</span></div>
<div class="line"><a name="l01486"></a><span class="lineno"> 1486</span>&#160;<span class="comment"># should contain the MathJax.js script. For instance, if the mathjax directory</span></div>
<div class="line"><a name="l01487"></a><span class="lineno"> 1487</span>&#160;<span class="comment"># is located at the same level as the HTML output directory, then</span></div>
<div class="line"><a name="l01488"></a><span class="lineno"> 1488</span>&#160;<span class="comment"># MATHJAX_RELPATH should be ../mathjax. The default value points to the MathJax</span></div>
<div class="line"><a name="l01489"></a><span class="lineno"> 1489</span>&#160;<span class="comment"># Content Delivery Network so you can quickly see the result without installing</span></div>
<div class="line"><a name="l01490"></a><span class="lineno"> 1490</span>&#160;<span class="comment"># MathJax. However, it is strongly recommended to install a local copy of</span></div>
<div class="line"><a name="l01491"></a><span class="lineno"> 1491</span>&#160;<span class="comment"># MathJax from http://www.mathjax.org before deployment.</span></div>
<div class="line"><a name="l01492"></a><span class="lineno"> 1492</span>&#160;<span class="comment"># The default value is: http://cdn.mathjax.org/mathjax/latest.</span></div>
<div class="line"><a name="l01493"></a><span class="lineno"> 1493</span>&#160;<span class="comment"># This tag requires that the tag USE_MATHJAX is set to YES.</span></div>
<div class="line"><a name="l01494"></a><span class="lineno"> 1494</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01495"></a><span class="lineno"> 1495</span>&#160;<span class="comment">MATHJAX_RELPATH        = http://www.mathjax.org/mathjax</span></div>
<div class="line"><a name="l01496"></a><span class="lineno"> 1496</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01497"></a><span class="lineno"> 1497</span>&#160;<span class="comment"># The MATHJAX_EXTENSIONS tag can be used to specify one or more MathJax</span></div>
<div class="line"><a name="l01498"></a><span class="lineno"> 1498</span>&#160;<span class="comment"># extension names that should be enabled during MathJax rendering. For example</span></div>
<div class="line"><a name="l01499"></a><span class="lineno"> 1499</span>&#160;<span class="comment"># MATHJAX_EXTENSIONS = TeX/AMSmath TeX/AMSsymbols</span></div>
<div class="line"><a name="l01500"></a><span class="lineno"> 1500</span>&#160;<span class="comment"># This tag requires that the tag USE_MATHJAX is set to YES.</span></div>
<div class="line"><a name="l01501"></a><span class="lineno"> 1501</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01502"></a><span class="lineno"> 1502</span>&#160;<span class="comment">MATHJAX_EXTENSIONS     = </span></div>
<div class="line"><a name="l01503"></a><span class="lineno"> 1503</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01504"></a><span class="lineno"> 1504</span>&#160;<span class="comment"># The MATHJAX_CODEFILE tag can be used to specify a file with javascript pieces</span></div>
<div class="line"><a name="l01505"></a><span class="lineno"> 1505</span>&#160;<span class="comment"># of code that will be used on startup of the MathJax code. See the MathJax site</span></div>
<div class="line"><a name="l01506"></a><span class="lineno"> 1506</span>&#160;<span class="comment"># (see: http://docs.mathjax.org/en/latest/output.html) for more details. For an</span></div>
<div class="line"><a name="l01507"></a><span class="lineno"> 1507</span>&#160;<span class="comment"># example see the documentation.</span></div>
<div class="line"><a name="l01508"></a><span class="lineno"> 1508</span>&#160;<span class="comment"># This tag requires that the tag USE_MATHJAX is set to YES.</span></div>
<div class="line"><a name="l01509"></a><span class="lineno"> 1509</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01510"></a><span class="lineno"> 1510</span>&#160;<span class="comment">MATHJAX_CODEFILE       = </span></div>
<div class="line"><a name="l01511"></a><span class="lineno"> 1511</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01512"></a><span class="lineno"> 1512</span>&#160;<span class="comment"># When the SEARCHENGINE tag is enabled doxygen will generate a search box for</span></div>
<div class="line"><a name="l01513"></a><span class="lineno"> 1513</span>&#160;<span class="comment"># the HTML output. The underlying search engine uses javascript and DHTML and</span></div>
<div class="line"><a name="l01514"></a><span class="lineno"> 1514</span>&#160;<span class="comment"># should work on any modern browser. Note that when using HTML help</span></div>
<div class="line"><a name="l01515"></a><span class="lineno"> 1515</span>&#160;<span class="comment"># (GENERATE_HTMLHELP), Qt help (GENERATE_QHP), or docsets (GENERATE_DOCSET)</span></div>
<div class="line"><a name="l01516"></a><span class="lineno"> 1516</span>&#160;<span class="comment"># there is already a search function so this one should typically be disabled.</span></div>
<div class="line"><a name="l01517"></a><span class="lineno"> 1517</span>&#160;<span class="comment"># For large projects the javascript based search engine can be slow, then</span></div>
<div class="line"><a name="l01518"></a><span class="lineno"> 1518</span>&#160;<span class="comment"># enabling SERVER_BASED_SEARCH may provide a better solution. It is possible to</span></div>
<div class="line"><a name="l01519"></a><span class="lineno"> 1519</span>&#160;<span class="comment"># search using the keyboard; to jump to the search box use &lt;access key&gt; + S</span></div>
<div class="line"><a name="l01520"></a><span class="lineno"> 1520</span>&#160;<span class="comment"># (what the &lt;access key&gt; is depends on the OS and browser, but it is typically</span></div>
<div class="line"><a name="l01521"></a><span class="lineno"> 1521</span>&#160;<span class="comment"># &lt;CTRL&gt;, &lt;ALT&gt;/&lt;option&gt;, or both). Inside the search box use the &lt;cursor down</span></div>
<div class="line"><a name="l01522"></a><span class="lineno"> 1522</span>&#160;<span class="comment"># key&gt; to jump into the search results window, the results can be navigated</span></div>
<div class="line"><a name="l01523"></a><span class="lineno"> 1523</span>&#160;<span class="comment"># using the &lt;cursor keys&gt;. Press &lt;Enter&gt; to select an item or &lt;escape&gt; to cancel</span></div>
<div class="line"><a name="l01524"></a><span class="lineno"> 1524</span>&#160;<span class="comment"># the search. The filter options can be selected when the cursor is inside the</span></div>
<div class="line"><a name="l01525"></a><span class="lineno"> 1525</span>&#160;<span class="comment"># search box by pressing &lt;Shift&gt;+&lt;cursor down&gt;. Also here use the &lt;cursor keys&gt;</span></div>
<div class="line"><a name="l01526"></a><span class="lineno"> 1526</span>&#160;<span class="comment"># to select a filter and &lt;Enter&gt; or &lt;escape&gt; to activate or cancel the filter</span></div>
<div class="line"><a name="l01527"></a><span class="lineno"> 1527</span>&#160;<span class="comment"># option.</span></div>
<div class="line"><a name="l01528"></a><span class="lineno"> 1528</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01529"></a><span class="lineno"> 1529</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_HTML is set to YES.</span></div>
<div class="line"><a name="l01530"></a><span class="lineno"> 1530</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01531"></a><span class="lineno"> 1531</span>&#160;<span class="comment">SEARCHENGINE           = YES</span></div>
<div class="line"><a name="l01532"></a><span class="lineno"> 1532</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01533"></a><span class="lineno"> 1533</span>&#160;<span class="comment"># When the SERVER_BASED_SEARCH tag is enabled the search engine will be</span></div>
<div class="line"><a name="l01534"></a><span class="lineno"> 1534</span>&#160;<span class="comment"># implemented using a web server instead of a web client using Javascript. There</span></div>
<div class="line"><a name="l01535"></a><span class="lineno"> 1535</span>&#160;<span class="comment"># are two flavors of web server based searching depending on the EXTERNAL_SEARCH</span></div>
<div class="line"><a name="l01536"></a><span class="lineno"> 1536</span>&#160;<span class="comment"># setting. When disabled, doxygen will generate a PHP script for searching and</span></div>
<div class="line"><a name="l01537"></a><span class="lineno"> 1537</span>&#160;<span class="comment"># an index file used by the script. When EXTERNAL_SEARCH is enabled the indexing</span></div>
<div class="line"><a name="l01538"></a><span class="lineno"> 1538</span>&#160;<span class="comment"># and searching needs to be provided by external tools. See the section</span></div>
<div class="line"><a name="l01539"></a><span class="lineno"> 1539</span>&#160;<span class="comment"># &quot;External Indexing and Searching&quot; for details.</span></div>
<div class="line"><a name="l01540"></a><span class="lineno"> 1540</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01541"></a><span class="lineno"> 1541</span>&#160;<span class="comment"># This tag requires that the tag SEARCHENGINE is set to YES.</span></div>
<div class="line"><a name="l01542"></a><span class="lineno"> 1542</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01543"></a><span class="lineno"> 1543</span>&#160;<span class="comment">SERVER_BASED_SEARCH    = NO</span></div>
<div class="line"><a name="l01544"></a><span class="lineno"> 1544</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01545"></a><span class="lineno"> 1545</span>&#160;<span class="comment"># When EXTERNAL_SEARCH tag is enabled doxygen will no longer generate the PHP</span></div>
<div class="line"><a name="l01546"></a><span class="lineno"> 1546</span>&#160;<span class="comment"># script for searching. Instead the search results are written to an XML file</span></div>
<div class="line"><a name="l01547"></a><span class="lineno"> 1547</span>&#160;<span class="comment"># which needs to be processed by an external indexer. Doxygen will invoke an</span></div>
<div class="line"><a name="l01548"></a><span class="lineno"> 1548</span>&#160;<span class="comment"># external search engine pointed to by the SEARCHENGINE_URL option to obtain the</span></div>
<div class="line"><a name="l01549"></a><span class="lineno"> 1549</span>&#160;<span class="comment"># search results.</span></div>
<div class="line"><a name="l01550"></a><span class="lineno"> 1550</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01551"></a><span class="lineno"> 1551</span>&#160;<span class="comment"># Doxygen ships with an example indexer (doxyindexer) and search engine</span></div>
<div class="line"><a name="l01552"></a><span class="lineno"> 1552</span>&#160;<span class="comment"># (doxysearch.cgi) which are based on the open source search engine library</span></div>
<div class="line"><a name="l01553"></a><span class="lineno"> 1553</span>&#160;<span class="comment"># Xapian (see: http://xapian.org/).</span></div>
<div class="line"><a name="l01554"></a><span class="lineno"> 1554</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01555"></a><span class="lineno"> 1555</span>&#160;<span class="comment"># See the section &quot;External Indexing and Searching&quot; for details.</span></div>
<div class="line"><a name="l01556"></a><span class="lineno"> 1556</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01557"></a><span class="lineno"> 1557</span>&#160;<span class="comment"># This tag requires that the tag SEARCHENGINE is set to YES.</span></div>
<div class="line"><a name="l01558"></a><span class="lineno"> 1558</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01559"></a><span class="lineno"> 1559</span>&#160;<span class="comment">EXTERNAL_SEARCH        = NO</span></div>
<div class="line"><a name="l01560"></a><span class="lineno"> 1560</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01561"></a><span class="lineno"> 1561</span>&#160;<span class="comment"># The SEARCHENGINE_URL should point to a search engine hosted by a web server</span></div>
<div class="line"><a name="l01562"></a><span class="lineno"> 1562</span>&#160;<span class="comment"># which will return the search results when EXTERNAL_SEARCH is enabled.</span></div>
<div class="line"><a name="l01563"></a><span class="lineno"> 1563</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01564"></a><span class="lineno"> 1564</span>&#160;<span class="comment"># Doxygen ships with an example indexer (doxyindexer) and search engine</span></div>
<div class="line"><a name="l01565"></a><span class="lineno"> 1565</span>&#160;<span class="comment"># (doxysearch.cgi) which are based on the open source search engine library</span></div>
<div class="line"><a name="l01566"></a><span class="lineno"> 1566</span>&#160;<span class="comment"># Xapian (see: http://xapian.org/). See the section &quot;External Indexing and</span></div>
<div class="line"><a name="l01567"></a><span class="lineno"> 1567</span>&#160;<span class="comment"># Searching&quot; for details.</span></div>
<div class="line"><a name="l01568"></a><span class="lineno"> 1568</span>&#160;<span class="comment"># This tag requires that the tag SEARCHENGINE is set to YES.</span></div>
<div class="line"><a name="l01569"></a><span class="lineno"> 1569</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01570"></a><span class="lineno"> 1570</span>&#160;<span class="comment">SEARCHENGINE_URL       = </span></div>
<div class="line"><a name="l01571"></a><span class="lineno"> 1571</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01572"></a><span class="lineno"> 1572</span>&#160;<span class="comment"># When SERVER_BASED_SEARCH and EXTERNAL_SEARCH are both enabled the unindexed</span></div>
<div class="line"><a name="l01573"></a><span class="lineno"> 1573</span>&#160;<span class="comment"># search data is written to a file for indexing by an external tool. With the</span></div>
<div class="line"><a name="l01574"></a><span class="lineno"> 1574</span>&#160;<span class="comment"># SEARCHDATA_FILE tag the name of this file can be specified.</span></div>
<div class="line"><a name="l01575"></a><span class="lineno"> 1575</span>&#160;<span class="comment"># The default file is: searchdata.xml.</span></div>
<div class="line"><a name="l01576"></a><span class="lineno"> 1576</span>&#160;<span class="comment"># This tag requires that the tag SEARCHENGINE is set to YES.</span></div>
<div class="line"><a name="l01577"></a><span class="lineno"> 1577</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01578"></a><span class="lineno"> 1578</span>&#160;<span class="comment">SEARCHDATA_FILE        = searchdata.xml</span></div>
<div class="line"><a name="l01579"></a><span class="lineno"> 1579</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01580"></a><span class="lineno"> 1580</span>&#160;<span class="comment"># When SERVER_BASED_SEARCH and EXTERNAL_SEARCH are both enabled the</span></div>
<div class="line"><a name="l01581"></a><span class="lineno"> 1581</span>&#160;<span class="comment"># EXTERNAL_SEARCH_ID tag can be used as an identifier for the project. This is</span></div>
<div class="line"><a name="l01582"></a><span class="lineno"> 1582</span>&#160;<span class="comment"># useful in combination with EXTRA_SEARCH_MAPPINGS to search through multiple</span></div>
<div class="line"><a name="l01583"></a><span class="lineno"> 1583</span>&#160;<span class="comment"># projects and redirect the results back to the right project.</span></div>
<div class="line"><a name="l01584"></a><span class="lineno"> 1584</span>&#160;<span class="comment"># This tag requires that the tag SEARCHENGINE is set to YES.</span></div>
<div class="line"><a name="l01585"></a><span class="lineno"> 1585</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01586"></a><span class="lineno"> 1586</span>&#160;<span class="comment">EXTERNAL_SEARCH_ID     = </span></div>
<div class="line"><a name="l01587"></a><span class="lineno"> 1587</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01588"></a><span class="lineno"> 1588</span>&#160;<span class="comment"># The EXTRA_SEARCH_MAPPINGS tag can be used to enable searching through doxygen</span></div>
<div class="line"><a name="l01589"></a><span class="lineno"> 1589</span>&#160;<span class="comment"># projects other than the one defined by this configuration file, but that are</span></div>
<div class="line"><a name="l01590"></a><span class="lineno"> 1590</span>&#160;<span class="comment"># all added to the same external search index. Each project needs to have a</span></div>
<div class="line"><a name="l01591"></a><span class="lineno"> 1591</span>&#160;<span class="comment"># unique id set via EXTERNAL_SEARCH_ID. The search mapping then maps the id of</span></div>
<div class="line"><a name="l01592"></a><span class="lineno"> 1592</span>&#160;<span class="comment"># to a relative location where the documentation can be found. The format is:</span></div>
<div class="line"><a name="l01593"></a><span class="lineno"> 1593</span>&#160;<span class="comment"># EXTRA_SEARCH_MAPPINGS = tagname1=loc1 tagname2=loc2 ...</span></div>
<div class="line"><a name="l01594"></a><span class="lineno"> 1594</span>&#160;<span class="comment"># This tag requires that the tag SEARCHENGINE is set to YES.</span></div>
<div class="line"><a name="l01595"></a><span class="lineno"> 1595</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01596"></a><span class="lineno"> 1596</span>&#160;<span class="comment">EXTRA_SEARCH_MAPPINGS  = </span></div>
<div class="line"><a name="l01597"></a><span class="lineno"> 1597</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01598"></a><span class="lineno"> 1598</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01599"></a><span class="lineno"> 1599</span>&#160;<span class="comment"># Configuration options related to the LaTeX output</span></div>
<div class="line"><a name="l01600"></a><span class="lineno"> 1600</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01601"></a><span class="lineno"> 1601</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01602"></a><span class="lineno"> 1602</span>&#160;<span class="comment"># If the GENERATE_LATEX tag is set to YES, doxygen will generate LaTeX output.</span></div>
<div class="line"><a name="l01603"></a><span class="lineno"> 1603</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01604"></a><span class="lineno"> 1604</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01605"></a><span class="lineno"> 1605</span>&#160;<span class="comment">GENERATE_LATEX         = NO</span></div>
<div class="line"><a name="l01606"></a><span class="lineno"> 1606</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01607"></a><span class="lineno"> 1607</span>&#160;<span class="comment"># The LATEX_OUTPUT tag is used to specify where the LaTeX docs will be put. If a</span></div>
<div class="line"><a name="l01608"></a><span class="lineno"> 1608</span>&#160;<span class="comment"># relative path is entered the value of OUTPUT_DIRECTORY will be put in front of</span></div>
<div class="line"><a name="l01609"></a><span class="lineno"> 1609</span>&#160;<span class="comment"># it.</span></div>
<div class="line"><a name="l01610"></a><span class="lineno"> 1610</span>&#160;<span class="comment"># The default directory is: latex.</span></div>
<div class="line"><a name="l01611"></a><span class="lineno"> 1611</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01612"></a><span class="lineno"> 1612</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01613"></a><span class="lineno"> 1613</span>&#160;<span class="comment">LATEX_OUTPUT           = latex</span></div>
<div class="line"><a name="l01614"></a><span class="lineno"> 1614</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01615"></a><span class="lineno"> 1615</span>&#160;<span class="comment"># The LATEX_CMD_NAME tag can be used to specify the LaTeX command name to be</span></div>
<div class="line"><a name="l01616"></a><span class="lineno"> 1616</span>&#160;<span class="comment"># invoked.</span></div>
<div class="line"><a name="l01617"></a><span class="lineno"> 1617</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01618"></a><span class="lineno"> 1618</span>&#160;<span class="comment"># Note that when enabling USE_PDFLATEX this option is only used for generating</span></div>
<div class="line"><a name="l01619"></a><span class="lineno"> 1619</span>&#160;<span class="comment"># bitmaps for formulas in the HTML output, but not in the Makefile that is</span></div>
<div class="line"><a name="l01620"></a><span class="lineno"> 1620</span>&#160;<span class="comment"># written to the output directory.</span></div>
<div class="line"><a name="l01621"></a><span class="lineno"> 1621</span>&#160;<span class="comment"># The default file is: latex.</span></div>
<div class="line"><a name="l01622"></a><span class="lineno"> 1622</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01623"></a><span class="lineno"> 1623</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01624"></a><span class="lineno"> 1624</span>&#160;<span class="comment">LATEX_CMD_NAME         = latex</span></div>
<div class="line"><a name="l01625"></a><span class="lineno"> 1625</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01626"></a><span class="lineno"> 1626</span>&#160;<span class="comment"># The MAKEINDEX_CMD_NAME tag can be used to specify the command name to generate</span></div>
<div class="line"><a name="l01627"></a><span class="lineno"> 1627</span>&#160;<span class="comment"># index for LaTeX.</span></div>
<div class="line"><a name="l01628"></a><span class="lineno"> 1628</span>&#160;<span class="comment"># The default file is: makeindex.</span></div>
<div class="line"><a name="l01629"></a><span class="lineno"> 1629</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01630"></a><span class="lineno"> 1630</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01631"></a><span class="lineno"> 1631</span>&#160;<span class="comment">MAKEINDEX_CMD_NAME     = makeindex</span></div>
<div class="line"><a name="l01632"></a><span class="lineno"> 1632</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01633"></a><span class="lineno"> 1633</span>&#160;<span class="comment"># If the COMPACT_LATEX tag is set to YES, doxygen generates more compact LaTeX</span></div>
<div class="line"><a name="l01634"></a><span class="lineno"> 1634</span>&#160;<span class="comment"># documents. This may be useful for small projects and may help to save some</span></div>
<div class="line"><a name="l01635"></a><span class="lineno"> 1635</span>&#160;<span class="comment"># trees in general.</span></div>
<div class="line"><a name="l01636"></a><span class="lineno"> 1636</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01637"></a><span class="lineno"> 1637</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01638"></a><span class="lineno"> 1638</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01639"></a><span class="lineno"> 1639</span>&#160;<span class="comment">COMPACT_LATEX          = NO</span></div>
<div class="line"><a name="l01640"></a><span class="lineno"> 1640</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01641"></a><span class="lineno"> 1641</span>&#160;<span class="comment"># The PAPER_TYPE tag can be used to set the paper type that is used by the</span></div>
<div class="line"><a name="l01642"></a><span class="lineno"> 1642</span>&#160;<span class="comment"># printer.</span></div>
<div class="line"><a name="l01643"></a><span class="lineno"> 1643</span>&#160;<span class="comment"># Possible values are: a4 (210 x 297 mm), letter (8.5 x 11 inches), legal (8.5 x</span></div>
<div class="line"><a name="l01644"></a><span class="lineno"> 1644</span>&#160;<span class="comment"># 14 inches) and executive (7.25 x 10.5 inches).</span></div>
<div class="line"><a name="l01645"></a><span class="lineno"> 1645</span>&#160;<span class="comment"># The default value is: a4.</span></div>
<div class="line"><a name="l01646"></a><span class="lineno"> 1646</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01647"></a><span class="lineno"> 1647</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01648"></a><span class="lineno"> 1648</span>&#160;<span class="comment">PAPER_TYPE             = a4wide</span></div>
<div class="line"><a name="l01649"></a><span class="lineno"> 1649</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01650"></a><span class="lineno"> 1650</span>&#160;<span class="comment"># The EXTRA_PACKAGES tag can be used to specify one or more LaTeX package names</span></div>
<div class="line"><a name="l01651"></a><span class="lineno"> 1651</span>&#160;<span class="comment"># that should be included in the LaTeX output. The package can be specified just</span></div>
<div class="line"><a name="l01652"></a><span class="lineno"> 1652</span>&#160;<span class="comment"># by its name or with the correct syntax as to be used with the LaTeX</span></div>
<div class="line"><a name="l01653"></a><span class="lineno"> 1653</span>&#160;<span class="comment"># \usepackage command. To get the times font for instance you can specify :</span></div>
<div class="line"><a name="l01654"></a><span class="lineno"> 1654</span>&#160;<span class="comment"># EXTRA_PACKAGES=times or EXTRA_PACKAGES={times}</span></div>
<div class="line"><a name="l01655"></a><span class="lineno"> 1655</span>&#160;<span class="comment"># To use the option intlimits with the amsmath package you can specify:</span></div>
<div class="line"><a name="l01656"></a><span class="lineno"> 1656</span>&#160;<span class="comment"># EXTRA_PACKAGES=[intlimits]{amsmath}</span></div>
<div class="line"><a name="l01657"></a><span class="lineno"> 1657</span>&#160;<span class="comment"># If left blank no extra packages will be included.</span></div>
<div class="line"><a name="l01658"></a><span class="lineno"> 1658</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01659"></a><span class="lineno"> 1659</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01660"></a><span class="lineno"> 1660</span>&#160;<span class="comment">EXTRA_PACKAGES         = </span></div>
<div class="line"><a name="l01661"></a><span class="lineno"> 1661</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01662"></a><span class="lineno"> 1662</span>&#160;<span class="comment"># The LATEX_HEADER tag can be used to specify a personal LaTeX header for the</span></div>
<div class="line"><a name="l01663"></a><span class="lineno"> 1663</span>&#160;<span class="comment"># generated LaTeX document. The header should contain everything until the first</span></div>
<div class="line"><a name="l01664"></a><span class="lineno"> 1664</span>&#160;<span class="comment"># chapter. If it is left blank doxygen will generate a standard header. See</span></div>
<div class="line"><a name="l01665"></a><span class="lineno"> 1665</span>&#160;<span class="comment"># section &quot;Doxygen usage&quot; for information on how to let doxygen write the</span></div>
<div class="line"><a name="l01666"></a><span class="lineno"> 1666</span>&#160;<span class="comment"># default header to a separate file.</span></div>
<div class="line"><a name="l01667"></a><span class="lineno"> 1667</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01668"></a><span class="lineno"> 1668</span>&#160;<span class="comment"># Note: Only use a user-defined header if you know what you are doing! The</span></div>
<div class="line"><a name="l01669"></a><span class="lineno"> 1669</span>&#160;<span class="comment"># following commands have a special meaning inside the header: $title,</span></div>
<div class="line"><a name="l01670"></a><span class="lineno"> 1670</span>&#160;<span class="comment"># $datetime, $date, $doxygenversion, $projectname, $projectnumber,</span></div>
<div class="line"><a name="l01671"></a><span class="lineno"> 1671</span>&#160;<span class="comment"># $projectbrief, $projectlogo. Doxygen will replace $title with the empty</span></div>
<div class="line"><a name="l01672"></a><span class="lineno"> 1672</span>&#160;<span class="comment"># string, for the replacement values of the other commands the user is referred</span></div>
<div class="line"><a name="l01673"></a><span class="lineno"> 1673</span>&#160;<span class="comment"># to HTML_HEADER.</span></div>
<div class="line"><a name="l01674"></a><span class="lineno"> 1674</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01675"></a><span class="lineno"> 1675</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01676"></a><span class="lineno"> 1676</span>&#160;<span class="comment">LATEX_HEADER           = </span></div>
<div class="line"><a name="l01677"></a><span class="lineno"> 1677</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01678"></a><span class="lineno"> 1678</span>&#160;<span class="comment"># The LATEX_FOOTER tag can be used to specify a personal LaTeX footer for the</span></div>
<div class="line"><a name="l01679"></a><span class="lineno"> 1679</span>&#160;<span class="comment"># generated LaTeX document. The footer should contain everything after the last</span></div>
<div class="line"><a name="l01680"></a><span class="lineno"> 1680</span>&#160;<span class="comment"># chapter. If it is left blank doxygen will generate a standard footer. See</span></div>
<div class="line"><a name="l01681"></a><span class="lineno"> 1681</span>&#160;<span class="comment"># LATEX_HEADER for more information on how to generate a default footer and what</span></div>
<div class="line"><a name="l01682"></a><span class="lineno"> 1682</span>&#160;<span class="comment"># special commands can be used inside the footer.</span></div>
<div class="line"><a name="l01683"></a><span class="lineno"> 1683</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01684"></a><span class="lineno"> 1684</span>&#160;<span class="comment"># Note: Only use a user-defined footer if you know what you are doing!</span></div>
<div class="line"><a name="l01685"></a><span class="lineno"> 1685</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01686"></a><span class="lineno"> 1686</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01687"></a><span class="lineno"> 1687</span>&#160;<span class="comment">LATEX_FOOTER           = </span></div>
<div class="line"><a name="l01688"></a><span class="lineno"> 1688</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01689"></a><span class="lineno"> 1689</span>&#160;<span class="comment"># The LATEX_EXTRA_STYLESHEET tag can be used to specify additional user-defined</span></div>
<div class="line"><a name="l01690"></a><span class="lineno"> 1690</span>&#160;<span class="comment"># LaTeX style sheets that are included after the standard style sheets created</span></div>
<div class="line"><a name="l01691"></a><span class="lineno"> 1691</span>&#160;<span class="comment"># by doxygen. Using this option one can overrule certain style aspects. Doxygen</span></div>
<div class="line"><a name="l01692"></a><span class="lineno"> 1692</span>&#160;<span class="comment"># will copy the style sheet files to the output directory.</span></div>
<div class="line"><a name="l01693"></a><span class="lineno"> 1693</span>&#160;<span class="comment"># Note: The order of the extra style sheet files is of importance (e.g. the last</span></div>
<div class="line"><a name="l01694"></a><span class="lineno"> 1694</span>&#160;<span class="comment"># style sheet in the list overrules the setting of the previous ones in the</span></div>
<div class="line"><a name="l01695"></a><span class="lineno"> 1695</span>&#160;<span class="comment"># list).</span></div>
<div class="line"><a name="l01696"></a><span class="lineno"> 1696</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01697"></a><span class="lineno"> 1697</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01698"></a><span class="lineno"> 1698</span>&#160;<span class="comment">LATEX_EXTRA_STYLESHEET = </span></div>
<div class="line"><a name="l01699"></a><span class="lineno"> 1699</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01700"></a><span class="lineno"> 1700</span>&#160;<span class="comment"># The LATEX_EXTRA_FILES tag can be used to specify one or more extra images or</span></div>
<div class="line"><a name="l01701"></a><span class="lineno"> 1701</span>&#160;<span class="comment"># other source files which should be copied to the LATEX_OUTPUT output</span></div>
<div class="line"><a name="l01702"></a><span class="lineno"> 1702</span>&#160;<span class="comment"># directory. Note that the files will be copied as-is; there are no commands or</span></div>
<div class="line"><a name="l01703"></a><span class="lineno"> 1703</span>&#160;<span class="comment"># markers available.</span></div>
<div class="line"><a name="l01704"></a><span class="lineno"> 1704</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01705"></a><span class="lineno"> 1705</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01706"></a><span class="lineno"> 1706</span>&#160;<span class="comment">LATEX_EXTRA_FILES      = </span></div>
<div class="line"><a name="l01707"></a><span class="lineno"> 1707</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01708"></a><span class="lineno"> 1708</span>&#160;<span class="comment"># If the PDF_HYPERLINKS tag is set to YES, the LaTeX that is generated is</span></div>
<div class="line"><a name="l01709"></a><span class="lineno"> 1709</span>&#160;<span class="comment"># prepared for conversion to PDF (using ps2pdf or pdflatex). The PDF file will</span></div>
<div class="line"><a name="l01710"></a><span class="lineno"> 1710</span>&#160;<span class="comment"># contain links (just like the HTML output) instead of page references. This</span></div>
<div class="line"><a name="l01711"></a><span class="lineno"> 1711</span>&#160;<span class="comment"># makes the output suitable for online browsing using a PDF viewer.</span></div>
<div class="line"><a name="l01712"></a><span class="lineno"> 1712</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01713"></a><span class="lineno"> 1713</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01714"></a><span class="lineno"> 1714</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01715"></a><span class="lineno"> 1715</span>&#160;<span class="comment">PDF_HYPERLINKS         = NO</span></div>
<div class="line"><a name="l01716"></a><span class="lineno"> 1716</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01717"></a><span class="lineno"> 1717</span>&#160;<span class="comment"># If the USE_PDFLATEX tag is set to YES, doxygen will use pdflatex to generate</span></div>
<div class="line"><a name="l01718"></a><span class="lineno"> 1718</span>&#160;<span class="comment"># the PDF file directly from the LaTeX files. Set this option to YES, to get a</span></div>
<div class="line"><a name="l01719"></a><span class="lineno"> 1719</span>&#160;<span class="comment"># higher quality PDF documentation.</span></div>
<div class="line"><a name="l01720"></a><span class="lineno"> 1720</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01721"></a><span class="lineno"> 1721</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01722"></a><span class="lineno"> 1722</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01723"></a><span class="lineno"> 1723</span>&#160;<span class="comment">USE_PDFLATEX           = YES</span></div>
<div class="line"><a name="l01724"></a><span class="lineno"> 1724</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01725"></a><span class="lineno"> 1725</span>&#160;<span class="comment"># If the LATEX_BATCHMODE tag is set to YES, doxygen will add the \batchmode</span></div>
<div class="line"><a name="l01726"></a><span class="lineno"> 1726</span>&#160;<span class="comment"># command to the generated LaTeX files. This will instruct LaTeX to keep running</span></div>
<div class="line"><a name="l01727"></a><span class="lineno"> 1727</span>&#160;<span class="comment"># if errors occur, instead of asking the user for help. This option is also used</span></div>
<div class="line"><a name="l01728"></a><span class="lineno"> 1728</span>&#160;<span class="comment"># when generating formulas in HTML.</span></div>
<div class="line"><a name="l01729"></a><span class="lineno"> 1729</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01730"></a><span class="lineno"> 1730</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01731"></a><span class="lineno"> 1731</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01732"></a><span class="lineno"> 1732</span>&#160;<span class="comment">LATEX_BATCHMODE        = NO</span></div>
<div class="line"><a name="l01733"></a><span class="lineno"> 1733</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01734"></a><span class="lineno"> 1734</span>&#160;<span class="comment"># If the LATEX_HIDE_INDICES tag is set to YES then doxygen will not include the</span></div>
<div class="line"><a name="l01735"></a><span class="lineno"> 1735</span>&#160;<span class="comment"># index chapters (such as File Index, Compound Index, etc.) in the output.</span></div>
<div class="line"><a name="l01736"></a><span class="lineno"> 1736</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01737"></a><span class="lineno"> 1737</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01738"></a><span class="lineno"> 1738</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01739"></a><span class="lineno"> 1739</span>&#160;<span class="comment">LATEX_HIDE_INDICES     = NO</span></div>
<div class="line"><a name="l01740"></a><span class="lineno"> 1740</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01741"></a><span class="lineno"> 1741</span>&#160;<span class="comment"># If the LATEX_SOURCE_CODE tag is set to YES then doxygen will include source</span></div>
<div class="line"><a name="l01742"></a><span class="lineno"> 1742</span>&#160;<span class="comment"># code with syntax highlighting in the LaTeX output.</span></div>
<div class="line"><a name="l01743"></a><span class="lineno"> 1743</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01744"></a><span class="lineno"> 1744</span>&#160;<span class="comment"># Note that which sources are shown also depends on other settings such as</span></div>
<div class="line"><a name="l01745"></a><span class="lineno"> 1745</span>&#160;<span class="comment"># SOURCE_BROWSER.</span></div>
<div class="line"><a name="l01746"></a><span class="lineno"> 1746</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01747"></a><span class="lineno"> 1747</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01748"></a><span class="lineno"> 1748</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01749"></a><span class="lineno"> 1749</span>&#160;<span class="comment">LATEX_SOURCE_CODE      = NO</span></div>
<div class="line"><a name="l01750"></a><span class="lineno"> 1750</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01751"></a><span class="lineno"> 1751</span>&#160;<span class="comment"># The LATEX_BIB_STYLE tag can be used to specify the style to use for the</span></div>
<div class="line"><a name="l01752"></a><span class="lineno"> 1752</span>&#160;<span class="comment"># bibliography, e.g. plainnat, or ieeetr. See</span></div>
<div class="line"><a name="l01753"></a><span class="lineno"> 1753</span>&#160;<span class="comment"># http://en.wikipedia.org/wiki/BibTeX and \cite for more info.</span></div>
<div class="line"><a name="l01754"></a><span class="lineno"> 1754</span>&#160;<span class="comment"># The default value is: plain.</span></div>
<div class="line"><a name="l01755"></a><span class="lineno"> 1755</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_LATEX is set to YES.</span></div>
<div class="line"><a name="l01756"></a><span class="lineno"> 1756</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01757"></a><span class="lineno"> 1757</span>&#160;<span class="comment">LATEX_BIB_STYLE        = plain</span></div>
<div class="line"><a name="l01758"></a><span class="lineno"> 1758</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01759"></a><span class="lineno"> 1759</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01760"></a><span class="lineno"> 1760</span>&#160;<span class="comment"># Configuration options related to the RTF output</span></div>
<div class="line"><a name="l01761"></a><span class="lineno"> 1761</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01762"></a><span class="lineno"> 1762</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01763"></a><span class="lineno"> 1763</span>&#160;<span class="comment"># If the GENERATE_RTF tag is set to YES, doxygen will generate RTF output. The</span></div>
<div class="line"><a name="l01764"></a><span class="lineno"> 1764</span>&#160;<span class="comment"># RTF output is optimized for Word 97 and may not look too pretty with other RTF</span></div>
<div class="line"><a name="l01765"></a><span class="lineno"> 1765</span>&#160;<span class="comment"># readers/editors.</span></div>
<div class="line"><a name="l01766"></a><span class="lineno"> 1766</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01767"></a><span class="lineno"> 1767</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01768"></a><span class="lineno"> 1768</span>&#160;<span class="comment">GENERATE_RTF           = NO</span></div>
<div class="line"><a name="l01769"></a><span class="lineno"> 1769</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01770"></a><span class="lineno"> 1770</span>&#160;<span class="comment"># The RTF_OUTPUT tag is used to specify where the RTF docs will be put. If a</span></div>
<div class="line"><a name="l01771"></a><span class="lineno"> 1771</span>&#160;<span class="comment"># relative path is entered the value of OUTPUT_DIRECTORY will be put in front of</span></div>
<div class="line"><a name="l01772"></a><span class="lineno"> 1772</span>&#160;<span class="comment"># it.</span></div>
<div class="line"><a name="l01773"></a><span class="lineno"> 1773</span>&#160;<span class="comment"># The default directory is: rtf.</span></div>
<div class="line"><a name="l01774"></a><span class="lineno"> 1774</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_RTF is set to YES.</span></div>
<div class="line"><a name="l01775"></a><span class="lineno"> 1775</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01776"></a><span class="lineno"> 1776</span>&#160;<span class="comment">RTF_OUTPUT             = glm.rtf</span></div>
<div class="line"><a name="l01777"></a><span class="lineno"> 1777</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01778"></a><span class="lineno"> 1778</span>&#160;<span class="comment"># If the COMPACT_RTF tag is set to YES, doxygen generates more compact RTF</span></div>
<div class="line"><a name="l01779"></a><span class="lineno"> 1779</span>&#160;<span class="comment"># documents. This may be useful for small projects and may help to save some</span></div>
<div class="line"><a name="l01780"></a><span class="lineno"> 1780</span>&#160;<span class="comment"># trees in general.</span></div>
<div class="line"><a name="l01781"></a><span class="lineno"> 1781</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01782"></a><span class="lineno"> 1782</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_RTF is set to YES.</span></div>
<div class="line"><a name="l01783"></a><span class="lineno"> 1783</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01784"></a><span class="lineno"> 1784</span>&#160;<span class="comment">COMPACT_RTF            = NO</span></div>
<div class="line"><a name="l01785"></a><span class="lineno"> 1785</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01786"></a><span class="lineno"> 1786</span>&#160;<span class="comment"># If the RTF_HYPERLINKS tag is set to YES, the RTF that is generated will</span></div>
<div class="line"><a name="l01787"></a><span class="lineno"> 1787</span>&#160;<span class="comment"># contain hyperlink fields. The RTF file will contain links (just like the HTML</span></div>
<div class="line"><a name="l01788"></a><span class="lineno"> 1788</span>&#160;<span class="comment"># output) instead of page references. This makes the output suitable for online</span></div>
<div class="line"><a name="l01789"></a><span class="lineno"> 1789</span>&#160;<span class="comment"># browsing using Word or some other Word compatible readers that support those</span></div>
<div class="line"><a name="l01790"></a><span class="lineno"> 1790</span>&#160;<span class="comment"># fields.</span></div>
<div class="line"><a name="l01791"></a><span class="lineno"> 1791</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01792"></a><span class="lineno"> 1792</span>&#160;<span class="comment"># Note: WordPad (write) and others do not support links.</span></div>
<div class="line"><a name="l01793"></a><span class="lineno"> 1793</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01794"></a><span class="lineno"> 1794</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_RTF is set to YES.</span></div>
<div class="line"><a name="l01795"></a><span class="lineno"> 1795</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01796"></a><span class="lineno"> 1796</span>&#160;<span class="comment">RTF_HYPERLINKS         = YES</span></div>
<div class="line"><a name="l01797"></a><span class="lineno"> 1797</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01798"></a><span class="lineno"> 1798</span>&#160;<span class="comment"># Load stylesheet definitions from file. Syntax is similar to doxygen&#39;s config</span></div>
<div class="line"><a name="l01799"></a><span class="lineno"> 1799</span>&#160;<span class="comment"># file, i.e. a series of assignments. You only have to provide replacements,</span></div>
<div class="line"><a name="l01800"></a><span class="lineno"> 1800</span>&#160;<span class="comment"># missing definitions are set to their default value.</span></div>
<div class="line"><a name="l01801"></a><span class="lineno"> 1801</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01802"></a><span class="lineno"> 1802</span>&#160;<span class="comment"># See also section &quot;Doxygen usage&quot; for information on how to generate the</span></div>
<div class="line"><a name="l01803"></a><span class="lineno"> 1803</span>&#160;<span class="comment"># default style sheet that doxygen normally uses.</span></div>
<div class="line"><a name="l01804"></a><span class="lineno"> 1804</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_RTF is set to YES.</span></div>
<div class="line"><a name="l01805"></a><span class="lineno"> 1805</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01806"></a><span class="lineno"> 1806</span>&#160;<span class="comment">RTF_STYLESHEET_FILE    = </span></div>
<div class="line"><a name="l01807"></a><span class="lineno"> 1807</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01808"></a><span class="lineno"> 1808</span>&#160;<span class="comment"># Set optional variables used in the generation of an RTF document. Syntax is</span></div>
<div class="line"><a name="l01809"></a><span class="lineno"> 1809</span>&#160;<span class="comment"># similar to doxygen&#39;s config file. A template extensions file can be generated</span></div>
<div class="line"><a name="l01810"></a><span class="lineno"> 1810</span>&#160;<span class="comment"># using doxygen -e rtf extensionFile.</span></div>
<div class="line"><a name="l01811"></a><span class="lineno"> 1811</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_RTF is set to YES.</span></div>
<div class="line"><a name="l01812"></a><span class="lineno"> 1812</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01813"></a><span class="lineno"> 1813</span>&#160;<span class="comment">RTF_EXTENSIONS_FILE    = </span></div>
<div class="line"><a name="l01814"></a><span class="lineno"> 1814</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01815"></a><span class="lineno"> 1815</span>&#160;<span class="comment"># If the RTF_SOURCE_CODE tag is set to YES then doxygen will include source code</span></div>
<div class="line"><a name="l01816"></a><span class="lineno"> 1816</span>&#160;<span class="comment"># with syntax highlighting in the RTF output.</span></div>
<div class="line"><a name="l01817"></a><span class="lineno"> 1817</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01818"></a><span class="lineno"> 1818</span>&#160;<span class="comment"># Note that which sources are shown also depends on other settings such as</span></div>
<div class="line"><a name="l01819"></a><span class="lineno"> 1819</span>&#160;<span class="comment"># SOURCE_BROWSER.</span></div>
<div class="line"><a name="l01820"></a><span class="lineno"> 1820</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01821"></a><span class="lineno"> 1821</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_RTF is set to YES.</span></div>
<div class="line"><a name="l01822"></a><span class="lineno"> 1822</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01823"></a><span class="lineno"> 1823</span>&#160;<span class="comment">RTF_SOURCE_CODE        = NO</span></div>
<div class="line"><a name="l01824"></a><span class="lineno"> 1824</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01825"></a><span class="lineno"> 1825</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01826"></a><span class="lineno"> 1826</span>&#160;<span class="comment"># Configuration options related to the man page output</span></div>
<div class="line"><a name="l01827"></a><span class="lineno"> 1827</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01828"></a><span class="lineno"> 1828</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01829"></a><span class="lineno"> 1829</span>&#160;<span class="comment"># If the GENERATE_MAN tag is set to YES, doxygen will generate man pages for</span></div>
<div class="line"><a name="l01830"></a><span class="lineno"> 1830</span>&#160;<span class="comment"># classes and files.</span></div>
<div class="line"><a name="l01831"></a><span class="lineno"> 1831</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01832"></a><span class="lineno"> 1832</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01833"></a><span class="lineno"> 1833</span>&#160;<span class="comment">GENERATE_MAN           = NO</span></div>
<div class="line"><a name="l01834"></a><span class="lineno"> 1834</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01835"></a><span class="lineno"> 1835</span>&#160;<span class="comment"># The MAN_OUTPUT tag is used to specify where the man pages will be put. If a</span></div>
<div class="line"><a name="l01836"></a><span class="lineno"> 1836</span>&#160;<span class="comment"># relative path is entered the value of OUTPUT_DIRECTORY will be put in front of</span></div>
<div class="line"><a name="l01837"></a><span class="lineno"> 1837</span>&#160;<span class="comment"># it. A directory man3 will be created inside the directory specified by</span></div>
<div class="line"><a name="l01838"></a><span class="lineno"> 1838</span>&#160;<span class="comment"># MAN_OUTPUT.</span></div>
<div class="line"><a name="l01839"></a><span class="lineno"> 1839</span>&#160;<span class="comment"># The default directory is: man.</span></div>
<div class="line"><a name="l01840"></a><span class="lineno"> 1840</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_MAN is set to YES.</span></div>
<div class="line"><a name="l01841"></a><span class="lineno"> 1841</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01842"></a><span class="lineno"> 1842</span>&#160;<span class="comment">MAN_OUTPUT             = man</span></div>
<div class="line"><a name="l01843"></a><span class="lineno"> 1843</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01844"></a><span class="lineno"> 1844</span>&#160;<span class="comment"># The MAN_EXTENSION tag determines the extension that is added to the generated</span></div>
<div class="line"><a name="l01845"></a><span class="lineno"> 1845</span>&#160;<span class="comment"># man pages. In case the manual section does not start with a number, the number</span></div>
<div class="line"><a name="l01846"></a><span class="lineno"> 1846</span>&#160;<span class="comment"># 3 is prepended. The dot (.) at the beginning of the MAN_EXTENSION tag is</span></div>
<div class="line"><a name="l01847"></a><span class="lineno"> 1847</span>&#160;<span class="comment"># optional.</span></div>
<div class="line"><a name="l01848"></a><span class="lineno"> 1848</span>&#160;<span class="comment"># The default value is: .3.</span></div>
<div class="line"><a name="l01849"></a><span class="lineno"> 1849</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_MAN is set to YES.</span></div>
<div class="line"><a name="l01850"></a><span class="lineno"> 1850</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01851"></a><span class="lineno"> 1851</span>&#160;<span class="comment">MAN_EXTENSION          = .3</span></div>
<div class="line"><a name="l01852"></a><span class="lineno"> 1852</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01853"></a><span class="lineno"> 1853</span>&#160;<span class="comment"># The MAN_SUBDIR tag determines the name of the directory created within</span></div>
<div class="line"><a name="l01854"></a><span class="lineno"> 1854</span>&#160;<span class="comment"># MAN_OUTPUT in which the man pages are placed. If defaults to man followed by</span></div>
<div class="line"><a name="l01855"></a><span class="lineno"> 1855</span>&#160;<span class="comment"># MAN_EXTENSION with the initial . removed.</span></div>
<div class="line"><a name="l01856"></a><span class="lineno"> 1856</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_MAN is set to YES.</span></div>
<div class="line"><a name="l01857"></a><span class="lineno"> 1857</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01858"></a><span class="lineno"> 1858</span>&#160;<span class="comment">MAN_SUBDIR             = </span></div>
<div class="line"><a name="l01859"></a><span class="lineno"> 1859</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01860"></a><span class="lineno"> 1860</span>&#160;<span class="comment"># If the MAN_LINKS tag is set to YES and doxygen generates man output, then it</span></div>
<div class="line"><a name="l01861"></a><span class="lineno"> 1861</span>&#160;<span class="comment"># will generate one additional man file for each entity documented in the real</span></div>
<div class="line"><a name="l01862"></a><span class="lineno"> 1862</span>&#160;<span class="comment"># man page(s). These additional files only source the real man page, but without</span></div>
<div class="line"><a name="l01863"></a><span class="lineno"> 1863</span>&#160;<span class="comment"># them the man command would be unable to find the correct page.</span></div>
<div class="line"><a name="l01864"></a><span class="lineno"> 1864</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01865"></a><span class="lineno"> 1865</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_MAN is set to YES.</span></div>
<div class="line"><a name="l01866"></a><span class="lineno"> 1866</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01867"></a><span class="lineno"> 1867</span>&#160;<span class="comment">MAN_LINKS              = NO</span></div>
<div class="line"><a name="l01868"></a><span class="lineno"> 1868</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01869"></a><span class="lineno"> 1869</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01870"></a><span class="lineno"> 1870</span>&#160;<span class="comment"># Configuration options related to the XML output</span></div>
<div class="line"><a name="l01871"></a><span class="lineno"> 1871</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01872"></a><span class="lineno"> 1872</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01873"></a><span class="lineno"> 1873</span>&#160;<span class="comment"># If the GENERATE_XML tag is set to YES, doxygen will generate an XML file that</span></div>
<div class="line"><a name="l01874"></a><span class="lineno"> 1874</span>&#160;<span class="comment"># captures the structure of the code including all documentation.</span></div>
<div class="line"><a name="l01875"></a><span class="lineno"> 1875</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01876"></a><span class="lineno"> 1876</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01877"></a><span class="lineno"> 1877</span>&#160;<span class="comment">GENERATE_XML           = NO</span></div>
<div class="line"><a name="l01878"></a><span class="lineno"> 1878</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01879"></a><span class="lineno"> 1879</span>&#160;<span class="comment"># The XML_OUTPUT tag is used to specify where the XML pages will be put. If a</span></div>
<div class="line"><a name="l01880"></a><span class="lineno"> 1880</span>&#160;<span class="comment"># relative path is entered the value of OUTPUT_DIRECTORY will be put in front of</span></div>
<div class="line"><a name="l01881"></a><span class="lineno"> 1881</span>&#160;<span class="comment"># it.</span></div>
<div class="line"><a name="l01882"></a><span class="lineno"> 1882</span>&#160;<span class="comment"># The default directory is: xml.</span></div>
<div class="line"><a name="l01883"></a><span class="lineno"> 1883</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_XML is set to YES.</span></div>
<div class="line"><a name="l01884"></a><span class="lineno"> 1884</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01885"></a><span class="lineno"> 1885</span>&#160;<span class="comment">XML_OUTPUT             = xml</span></div>
<div class="line"><a name="l01886"></a><span class="lineno"> 1886</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01887"></a><span class="lineno"> 1887</span>&#160;<span class="comment"># If the XML_PROGRAMLISTING tag is set to YES, doxygen will dump the program</span></div>
<div class="line"><a name="l01888"></a><span class="lineno"> 1888</span>&#160;<span class="comment"># listings (including syntax highlighting and cross-referencing information) to</span></div>
<div class="line"><a name="l01889"></a><span class="lineno"> 1889</span>&#160;<span class="comment"># the XML output. Note that enabling this will significantly increase the size</span></div>
<div class="line"><a name="l01890"></a><span class="lineno"> 1890</span>&#160;<span class="comment"># of the XML output.</span></div>
<div class="line"><a name="l01891"></a><span class="lineno"> 1891</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01892"></a><span class="lineno"> 1892</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_XML is set to YES.</span></div>
<div class="line"><a name="l01893"></a><span class="lineno"> 1893</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01894"></a><span class="lineno"> 1894</span>&#160;<span class="comment">XML_PROGRAMLISTING     = YES</span></div>
<div class="line"><a name="l01895"></a><span class="lineno"> 1895</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01896"></a><span class="lineno"> 1896</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01897"></a><span class="lineno"> 1897</span>&#160;<span class="comment"># Configuration options related to the DOCBOOK output</span></div>
<div class="line"><a name="l01898"></a><span class="lineno"> 1898</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01899"></a><span class="lineno"> 1899</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01900"></a><span class="lineno"> 1900</span>&#160;<span class="comment"># If the GENERATE_DOCBOOK tag is set to YES, doxygen will generate Docbook files</span></div>
<div class="line"><a name="l01901"></a><span class="lineno"> 1901</span>&#160;<span class="comment"># that can be used to generate PDF.</span></div>
<div class="line"><a name="l01902"></a><span class="lineno"> 1902</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01903"></a><span class="lineno"> 1903</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01904"></a><span class="lineno"> 1904</span>&#160;<span class="comment">GENERATE_DOCBOOK       = NO</span></div>
<div class="line"><a name="l01905"></a><span class="lineno"> 1905</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01906"></a><span class="lineno"> 1906</span>&#160;<span class="comment"># The DOCBOOK_OUTPUT tag is used to specify where the Docbook pages will be put.</span></div>
<div class="line"><a name="l01907"></a><span class="lineno"> 1907</span>&#160;<span class="comment"># If a relative path is entered the value of OUTPUT_DIRECTORY will be put in</span></div>
<div class="line"><a name="l01908"></a><span class="lineno"> 1908</span>&#160;<span class="comment"># front of it.</span></div>
<div class="line"><a name="l01909"></a><span class="lineno"> 1909</span>&#160;<span class="comment"># The default directory is: docbook.</span></div>
<div class="line"><a name="l01910"></a><span class="lineno"> 1910</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_DOCBOOK is set to YES.</span></div>
<div class="line"><a name="l01911"></a><span class="lineno"> 1911</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01912"></a><span class="lineno"> 1912</span>&#160;<span class="comment">DOCBOOK_OUTPUT         = docbook</span></div>
<div class="line"><a name="l01913"></a><span class="lineno"> 1913</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01914"></a><span class="lineno"> 1914</span>&#160;<span class="comment"># If the DOCBOOK_PROGRAMLISTING tag is set to YES, doxygen will include the</span></div>
<div class="line"><a name="l01915"></a><span class="lineno"> 1915</span>&#160;<span class="comment"># program listings (including syntax highlighting and cross-referencing</span></div>
<div class="line"><a name="l01916"></a><span class="lineno"> 1916</span>&#160;<span class="comment"># information) to the DOCBOOK output. Note that enabling this will significantly</span></div>
<div class="line"><a name="l01917"></a><span class="lineno"> 1917</span>&#160;<span class="comment"># increase the size of the DOCBOOK output.</span></div>
<div class="line"><a name="l01918"></a><span class="lineno"> 1918</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01919"></a><span class="lineno"> 1919</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_DOCBOOK is set to YES.</span></div>
<div class="line"><a name="l01920"></a><span class="lineno"> 1920</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01921"></a><span class="lineno"> 1921</span>&#160;<span class="comment">DOCBOOK_PROGRAMLISTING = NO</span></div>
<div class="line"><a name="l01922"></a><span class="lineno"> 1922</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01923"></a><span class="lineno"> 1923</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01924"></a><span class="lineno"> 1924</span>&#160;<span class="comment"># Configuration options for the AutoGen Definitions output</span></div>
<div class="line"><a name="l01925"></a><span class="lineno"> 1925</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01926"></a><span class="lineno"> 1926</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01927"></a><span class="lineno"> 1927</span>&#160;<span class="comment"># If the GENERATE_AUTOGEN_DEF tag is set to YES, doxygen will generate an</span></div>
<div class="line"><a name="l01928"></a><span class="lineno"> 1928</span>&#160;<span class="comment"># AutoGen Definitions (see http://autogen.sf.net) file that captures the</span></div>
<div class="line"><a name="l01929"></a><span class="lineno"> 1929</span>&#160;<span class="comment"># structure of the code including all documentation. Note that this feature is</span></div>
<div class="line"><a name="l01930"></a><span class="lineno"> 1930</span>&#160;<span class="comment"># still experimental and incomplete at the moment.</span></div>
<div class="line"><a name="l01931"></a><span class="lineno"> 1931</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01932"></a><span class="lineno"> 1932</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01933"></a><span class="lineno"> 1933</span>&#160;<span class="comment">GENERATE_AUTOGEN_DEF   = NO</span></div>
<div class="line"><a name="l01934"></a><span class="lineno"> 1934</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01935"></a><span class="lineno"> 1935</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01936"></a><span class="lineno"> 1936</span>&#160;<span class="comment"># Configuration options related to the Perl module output</span></div>
<div class="line"><a name="l01937"></a><span class="lineno"> 1937</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01938"></a><span class="lineno"> 1938</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01939"></a><span class="lineno"> 1939</span>&#160;<span class="comment"># If the GENERATE_PERLMOD tag is set to YES, doxygen will generate a Perl module</span></div>
<div class="line"><a name="l01940"></a><span class="lineno"> 1940</span>&#160;<span class="comment"># file that captures the structure of the code including all documentation.</span></div>
<div class="line"><a name="l01941"></a><span class="lineno"> 1941</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l01942"></a><span class="lineno"> 1942</span>&#160;<span class="comment"># Note that this feature is still experimental and incomplete at the moment.</span></div>
<div class="line"><a name="l01943"></a><span class="lineno"> 1943</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01944"></a><span class="lineno"> 1944</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01945"></a><span class="lineno"> 1945</span>&#160;<span class="comment">GENERATE_PERLMOD       = NO</span></div>
<div class="line"><a name="l01946"></a><span class="lineno"> 1946</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01947"></a><span class="lineno"> 1947</span>&#160;<span class="comment"># If the PERLMOD_LATEX tag is set to YES, doxygen will generate the necessary</span></div>
<div class="line"><a name="l01948"></a><span class="lineno"> 1948</span>&#160;<span class="comment"># Makefile rules, Perl scripts and LaTeX code to be able to generate PDF and DVI</span></div>
<div class="line"><a name="l01949"></a><span class="lineno"> 1949</span>&#160;<span class="comment"># output from the Perl module output.</span></div>
<div class="line"><a name="l01950"></a><span class="lineno"> 1950</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01951"></a><span class="lineno"> 1951</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_PERLMOD is set to YES.</span></div>
<div class="line"><a name="l01952"></a><span class="lineno"> 1952</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01953"></a><span class="lineno"> 1953</span>&#160;<span class="comment">PERLMOD_LATEX          = NO</span></div>
<div class="line"><a name="l01954"></a><span class="lineno"> 1954</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01955"></a><span class="lineno"> 1955</span>&#160;<span class="comment"># If the PERLMOD_PRETTY tag is set to YES, the Perl module output will be nicely</span></div>
<div class="line"><a name="l01956"></a><span class="lineno"> 1956</span>&#160;<span class="comment"># formatted so it can be parsed by a human reader. This is useful if you want to</span></div>
<div class="line"><a name="l01957"></a><span class="lineno"> 1957</span>&#160;<span class="comment"># understand what is going on. On the other hand, if this tag is set to NO, the</span></div>
<div class="line"><a name="l01958"></a><span class="lineno"> 1958</span>&#160;<span class="comment"># size of the Perl module output will be much smaller and Perl will parse it</span></div>
<div class="line"><a name="l01959"></a><span class="lineno"> 1959</span>&#160;<span class="comment"># just the same.</span></div>
<div class="line"><a name="l01960"></a><span class="lineno"> 1960</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01961"></a><span class="lineno"> 1961</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_PERLMOD is set to YES.</span></div>
<div class="line"><a name="l01962"></a><span class="lineno"> 1962</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01963"></a><span class="lineno"> 1963</span>&#160;<span class="comment">PERLMOD_PRETTY         = YES</span></div>
<div class="line"><a name="l01964"></a><span class="lineno"> 1964</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01965"></a><span class="lineno"> 1965</span>&#160;<span class="comment"># The names of the make variables in the generated doxyrules.make file are</span></div>
<div class="line"><a name="l01966"></a><span class="lineno"> 1966</span>&#160;<span class="comment"># prefixed with the string contained in PERLMOD_MAKEVAR_PREFIX. This is useful</span></div>
<div class="line"><a name="l01967"></a><span class="lineno"> 1967</span>&#160;<span class="comment"># so different doxyrules.make files included by the same Makefile don&#39;t</span></div>
<div class="line"><a name="l01968"></a><span class="lineno"> 1968</span>&#160;<span class="comment"># overwrite each other&#39;s variables.</span></div>
<div class="line"><a name="l01969"></a><span class="lineno"> 1969</span>&#160;<span class="comment"># This tag requires that the tag GENERATE_PERLMOD is set to YES.</span></div>
<div class="line"><a name="l01970"></a><span class="lineno"> 1970</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01971"></a><span class="lineno"> 1971</span>&#160;<span class="comment">PERLMOD_MAKEVAR_PREFIX = </span></div>
<div class="line"><a name="l01972"></a><span class="lineno"> 1972</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01973"></a><span class="lineno"> 1973</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01974"></a><span class="lineno"> 1974</span>&#160;<span class="comment"># Configuration options related to the preprocessor</span></div>
<div class="line"><a name="l01975"></a><span class="lineno"> 1975</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l01976"></a><span class="lineno"> 1976</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01977"></a><span class="lineno"> 1977</span>&#160;<span class="comment"># If the ENABLE_PREPROCESSING tag is set to YES, doxygen will evaluate all</span></div>
<div class="line"><a name="l01978"></a><span class="lineno"> 1978</span>&#160;<span class="comment"># C-preprocessor directives found in the sources and include files.</span></div>
<div class="line"><a name="l01979"></a><span class="lineno"> 1979</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l01980"></a><span class="lineno"> 1980</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01981"></a><span class="lineno"> 1981</span>&#160;<span class="comment">ENABLE_PREPROCESSING   = YES</span></div>
<div class="line"><a name="l01982"></a><span class="lineno"> 1982</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01983"></a><span class="lineno"> 1983</span>&#160;<span class="comment"># If the MACRO_EXPANSION tag is set to YES, doxygen will expand all macro names</span></div>
<div class="line"><a name="l01984"></a><span class="lineno"> 1984</span>&#160;<span class="comment"># in the source code. If set to NO, only conditional compilation will be</span></div>
<div class="line"><a name="l01985"></a><span class="lineno"> 1985</span>&#160;<span class="comment"># performed. Macro expansion can be done in a controlled way by setting</span></div>
<div class="line"><a name="l01986"></a><span class="lineno"> 1986</span>&#160;<span class="comment"># EXPAND_ONLY_PREDEF to YES.</span></div>
<div class="line"><a name="l01987"></a><span class="lineno"> 1987</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01988"></a><span class="lineno"> 1988</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l01989"></a><span class="lineno"> 1989</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01990"></a><span class="lineno"> 1990</span>&#160;<span class="comment">MACRO_EXPANSION        = NO</span></div>
<div class="line"><a name="l01991"></a><span class="lineno"> 1991</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01992"></a><span class="lineno"> 1992</span>&#160;<span class="comment"># If the EXPAND_ONLY_PREDEF and MACRO_EXPANSION tags are both set to YES then</span></div>
<div class="line"><a name="l01993"></a><span class="lineno"> 1993</span>&#160;<span class="comment"># the macro expansion is limited to the macros specified with the PREDEFINED and</span></div>
<div class="line"><a name="l01994"></a><span class="lineno"> 1994</span>&#160;<span class="comment"># EXPAND_AS_DEFINED tags.</span></div>
<div class="line"><a name="l01995"></a><span class="lineno"> 1995</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l01996"></a><span class="lineno"> 1996</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l01997"></a><span class="lineno"> 1997</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l01998"></a><span class="lineno"> 1998</span>&#160;<span class="comment">EXPAND_ONLY_PREDEF     = NO</span></div>
<div class="line"><a name="l01999"></a><span class="lineno"> 1999</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02000"></a><span class="lineno"> 2000</span>&#160;<span class="comment"># If the SEARCH_INCLUDES tag is set to YES, the include files in the</span></div>
<div class="line"><a name="l02001"></a><span class="lineno"> 2001</span>&#160;<span class="comment"># INCLUDE_PATH will be searched if a #include is found.</span></div>
<div class="line"><a name="l02002"></a><span class="lineno"> 2002</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02003"></a><span class="lineno"> 2003</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l02004"></a><span class="lineno"> 2004</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02005"></a><span class="lineno"> 2005</span>&#160;<span class="comment">SEARCH_INCLUDES        = YES</span></div>
<div class="line"><a name="l02006"></a><span class="lineno"> 2006</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02007"></a><span class="lineno"> 2007</span>&#160;<span class="comment"># The INCLUDE_PATH tag can be used to specify one or more directories that</span></div>
<div class="line"><a name="l02008"></a><span class="lineno"> 2008</span>&#160;<span class="comment"># contain include files that are not input files but should be processed by the</span></div>
<div class="line"><a name="l02009"></a><span class="lineno"> 2009</span>&#160;<span class="comment"># preprocessor.</span></div>
<div class="line"><a name="l02010"></a><span class="lineno"> 2010</span>&#160;<span class="comment"># This tag requires that the tag SEARCH_INCLUDES is set to YES.</span></div>
<div class="line"><a name="l02011"></a><span class="lineno"> 2011</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02012"></a><span class="lineno"> 2012</span>&#160;<span class="comment">INCLUDE_PATH           = </span></div>
<div class="line"><a name="l02013"></a><span class="lineno"> 2013</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02014"></a><span class="lineno"> 2014</span>&#160;<span class="comment"># You can use the INCLUDE_FILE_PATTERNS tag to specify one or more wildcard</span></div>
<div class="line"><a name="l02015"></a><span class="lineno"> 2015</span>&#160;<span class="comment"># patterns (like *.h and *.hpp) to filter out the header-files in the</span></div>
<div class="line"><a name="l02016"></a><span class="lineno"> 2016</span>&#160;<span class="comment"># directories. If left blank, the patterns specified with FILE_PATTERNS will be</span></div>
<div class="line"><a name="l02017"></a><span class="lineno"> 2017</span>&#160;<span class="comment"># used.</span></div>
<div class="line"><a name="l02018"></a><span class="lineno"> 2018</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l02019"></a><span class="lineno"> 2019</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02020"></a><span class="lineno"> 2020</span>&#160;<span class="comment">INCLUDE_FILE_PATTERNS  = </span></div>
<div class="line"><a name="l02021"></a><span class="lineno"> 2021</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02022"></a><span class="lineno"> 2022</span>&#160;<span class="comment"># The PREDEFINED tag can be used to specify one or more macro names that are</span></div>
<div class="line"><a name="l02023"></a><span class="lineno"> 2023</span>&#160;<span class="comment"># defined before the preprocessor is started (similar to the -D option of e.g.</span></div>
<div class="line"><a name="l02024"></a><span class="lineno"> 2024</span>&#160;<span class="comment"># gcc). The argument of the tag is a list of macros of the form: name or</span></div>
<div class="line"><a name="l02025"></a><span class="lineno"> 2025</span>&#160;<span class="comment"># name=definition (no spaces). If the definition and the &quot;=&quot; are omitted, &quot;=1&quot;</span></div>
<div class="line"><a name="l02026"></a><span class="lineno"> 2026</span>&#160;<span class="comment"># is assumed. To prevent a macro definition from being undefined via #undef or</span></div>
<div class="line"><a name="l02027"></a><span class="lineno"> 2027</span>&#160;<span class="comment"># recursively expanded use the := operator instead of the = operator.</span></div>
<div class="line"><a name="l02028"></a><span class="lineno"> 2028</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l02029"></a><span class="lineno"> 2029</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02030"></a><span class="lineno"> 2030</span>&#160;<span class="comment">PREDEFINED             = </span></div>
<div class="line"><a name="l02031"></a><span class="lineno"> 2031</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02032"></a><span class="lineno"> 2032</span>&#160;<span class="comment"># If the MACRO_EXPANSION and EXPAND_ONLY_PREDEF tags are set to YES then this</span></div>
<div class="line"><a name="l02033"></a><span class="lineno"> 2033</span>&#160;<span class="comment"># tag can be used to specify a list of macro names that should be expanded. The</span></div>
<div class="line"><a name="l02034"></a><span class="lineno"> 2034</span>&#160;<span class="comment"># macro definition that is found in the sources will be used. Use the PREDEFINED</span></div>
<div class="line"><a name="l02035"></a><span class="lineno"> 2035</span>&#160;<span class="comment"># tag if you want to use a different macro definition that overrules the</span></div>
<div class="line"><a name="l02036"></a><span class="lineno"> 2036</span>&#160;<span class="comment"># definition found in the source code.</span></div>
<div class="line"><a name="l02037"></a><span class="lineno"> 2037</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l02038"></a><span class="lineno"> 2038</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02039"></a><span class="lineno"> 2039</span>&#160;<span class="comment">EXPAND_AS_DEFINED      = </span></div>
<div class="line"><a name="l02040"></a><span class="lineno"> 2040</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02041"></a><span class="lineno"> 2041</span>&#160;<span class="comment"># If the SKIP_FUNCTION_MACROS tag is set to YES then doxygen&#39;s preprocessor will</span></div>
<div class="line"><a name="l02042"></a><span class="lineno"> 2042</span>&#160;<span class="comment"># remove all references to function-like macros that are alone on a line, have</span></div>
<div class="line"><a name="l02043"></a><span class="lineno"> 2043</span>&#160;<span class="comment"># an all uppercase name, and do not end with a semicolon. Such function macros</span></div>
<div class="line"><a name="l02044"></a><span class="lineno"> 2044</span>&#160;<span class="comment"># are typically used for boiler-plate code, and will confuse the parser if not</span></div>
<div class="line"><a name="l02045"></a><span class="lineno"> 2045</span>&#160;<span class="comment"># removed.</span></div>
<div class="line"><a name="l02046"></a><span class="lineno"> 2046</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02047"></a><span class="lineno"> 2047</span>&#160;<span class="comment"># This tag requires that the tag ENABLE_PREPROCESSING is set to YES.</span></div>
<div class="line"><a name="l02048"></a><span class="lineno"> 2048</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02049"></a><span class="lineno"> 2049</span>&#160;<span class="comment">SKIP_FUNCTION_MACROS   = YES</span></div>
<div class="line"><a name="l02050"></a><span class="lineno"> 2050</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02051"></a><span class="lineno"> 2051</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l02052"></a><span class="lineno"> 2052</span>&#160;<span class="comment"># Configuration options related to external references</span></div>
<div class="line"><a name="l02053"></a><span class="lineno"> 2053</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l02054"></a><span class="lineno"> 2054</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02055"></a><span class="lineno"> 2055</span>&#160;<span class="comment"># The TAGFILES tag can be used to specify one or more tag files. For each tag</span></div>
<div class="line"><a name="l02056"></a><span class="lineno"> 2056</span>&#160;<span class="comment"># file the location of the external documentation should be added. The format of</span></div>
<div class="line"><a name="l02057"></a><span class="lineno"> 2057</span>&#160;<span class="comment"># a tag file without this location is as follows:</span></div>
<div class="line"><a name="l02058"></a><span class="lineno"> 2058</span>&#160;<span class="comment"># TAGFILES = file1 file2 ...</span></div>
<div class="line"><a name="l02059"></a><span class="lineno"> 2059</span>&#160;<span class="comment"># Adding location for the tag files is done as follows:</span></div>
<div class="line"><a name="l02060"></a><span class="lineno"> 2060</span>&#160;<span class="comment"># TAGFILES = file1=loc1 &quot;file2 = loc2&quot; ...</span></div>
<div class="line"><a name="l02061"></a><span class="lineno"> 2061</span>&#160;<span class="comment"># where loc1 and loc2 can be relative or absolute paths or URLs. See the</span></div>
<div class="line"><a name="l02062"></a><span class="lineno"> 2062</span>&#160;<span class="comment"># section &quot;Linking to external documentation&quot; for more information about the use</span></div>
<div class="line"><a name="l02063"></a><span class="lineno"> 2063</span>&#160;<span class="comment"># of tag files.</span></div>
<div class="line"><a name="l02064"></a><span class="lineno"> 2064</span>&#160;<span class="comment"># Note: Each tag file must have a unique name (where the name does NOT include</span></div>
<div class="line"><a name="l02065"></a><span class="lineno"> 2065</span>&#160;<span class="comment"># the path). If a tag file is not located in the directory in which doxygen is</span></div>
<div class="line"><a name="l02066"></a><span class="lineno"> 2066</span>&#160;<span class="comment"># run, you must also specify the path to the tagfile here.</span></div>
<div class="line"><a name="l02067"></a><span class="lineno"> 2067</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02068"></a><span class="lineno"> 2068</span>&#160;<span class="comment">TAGFILES               = </span></div>
<div class="line"><a name="l02069"></a><span class="lineno"> 2069</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02070"></a><span class="lineno"> 2070</span>&#160;<span class="comment"># When a file name is specified after GENERATE_TAGFILE, doxygen will create a</span></div>
<div class="line"><a name="l02071"></a><span class="lineno"> 2071</span>&#160;<span class="comment"># tag file that is based on the input files it reads. See section &quot;Linking to</span></div>
<div class="line"><a name="l02072"></a><span class="lineno"> 2072</span>&#160;<span class="comment"># external documentation&quot; for more information about the usage of tag files.</span></div>
<div class="line"><a name="l02073"></a><span class="lineno"> 2073</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02074"></a><span class="lineno"> 2074</span>&#160;<span class="comment">GENERATE_TAGFILE       = </span></div>
<div class="line"><a name="l02075"></a><span class="lineno"> 2075</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02076"></a><span class="lineno"> 2076</span>&#160;<span class="comment"># If the ALLEXTERNALS tag is set to YES, all external class will be listed in</span></div>
<div class="line"><a name="l02077"></a><span class="lineno"> 2077</span>&#160;<span class="comment"># the class index. If set to NO, only the inherited external classes will be</span></div>
<div class="line"><a name="l02078"></a><span class="lineno"> 2078</span>&#160;<span class="comment"># listed.</span></div>
<div class="line"><a name="l02079"></a><span class="lineno"> 2079</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02080"></a><span class="lineno"> 2080</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02081"></a><span class="lineno"> 2081</span>&#160;<span class="comment">ALLEXTERNALS           = NO</span></div>
<div class="line"><a name="l02082"></a><span class="lineno"> 2082</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02083"></a><span class="lineno"> 2083</span>&#160;<span class="comment"># If the EXTERNAL_GROUPS tag is set to YES, all external groups will be listed</span></div>
<div class="line"><a name="l02084"></a><span class="lineno"> 2084</span>&#160;<span class="comment"># in the modules index. If set to NO, only the current project&#39;s groups will be</span></div>
<div class="line"><a name="l02085"></a><span class="lineno"> 2085</span>&#160;<span class="comment"># listed.</span></div>
<div class="line"><a name="l02086"></a><span class="lineno"> 2086</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02087"></a><span class="lineno"> 2087</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02088"></a><span class="lineno"> 2088</span>&#160;<span class="comment">EXTERNAL_GROUPS        = YES</span></div>
<div class="line"><a name="l02089"></a><span class="lineno"> 2089</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02090"></a><span class="lineno"> 2090</span>&#160;<span class="comment"># If the EXTERNAL_PAGES tag is set to YES, all external pages will be listed in</span></div>
<div class="line"><a name="l02091"></a><span class="lineno"> 2091</span>&#160;<span class="comment"># the related pages index. If set to NO, only the current project&#39;s pages will</span></div>
<div class="line"><a name="l02092"></a><span class="lineno"> 2092</span>&#160;<span class="comment"># be listed.</span></div>
<div class="line"><a name="l02093"></a><span class="lineno"> 2093</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02094"></a><span class="lineno"> 2094</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02095"></a><span class="lineno"> 2095</span>&#160;<span class="comment">EXTERNAL_PAGES         = YES</span></div>
<div class="line"><a name="l02096"></a><span class="lineno"> 2096</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02097"></a><span class="lineno"> 2097</span>&#160;<span class="comment"># The PERL_PATH should be the absolute path and name of the perl script</span></div>
<div class="line"><a name="l02098"></a><span class="lineno"> 2098</span>&#160;<span class="comment"># interpreter (i.e. the result of &#39;which perl&#39;).</span></div>
<div class="line"><a name="l02099"></a><span class="lineno"> 2099</span>&#160;<span class="comment"># The default file (with absolute path) is: /usr/bin/perl.</span></div>
<div class="line"><a name="l02100"></a><span class="lineno"> 2100</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02101"></a><span class="lineno"> 2101</span>&#160;<span class="comment">PERL_PATH              = /usr/bin/perl</span></div>
<div class="line"><a name="l02102"></a><span class="lineno"> 2102</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02103"></a><span class="lineno"> 2103</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l02104"></a><span class="lineno"> 2104</span>&#160;<span class="comment"># Configuration options related to the dot tool</span></div>
<div class="line"><a name="l02105"></a><span class="lineno"> 2105</span>&#160;<span class="comment">#---------------------------------------------------------------------------</span></div>
<div class="line"><a name="l02106"></a><span class="lineno"> 2106</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02107"></a><span class="lineno"> 2107</span>&#160;<span class="comment"># If the CLASS_DIAGRAMS tag is set to YES, doxygen will generate a class diagram</span></div>
<div class="line"><a name="l02108"></a><span class="lineno"> 2108</span>&#160;<span class="comment"># (in HTML and LaTeX) for classes with base or super classes. Setting the tag to</span></div>
<div class="line"><a name="l02109"></a><span class="lineno"> 2109</span>&#160;<span class="comment"># NO turns the diagrams off. Note that this option also works with HAVE_DOT</span></div>
<div class="line"><a name="l02110"></a><span class="lineno"> 2110</span>&#160;<span class="comment"># disabled, but it is recommended to install and use dot, since it yields more</span></div>
<div class="line"><a name="l02111"></a><span class="lineno"> 2111</span>&#160;<span class="comment"># powerful graphs.</span></div>
<div class="line"><a name="l02112"></a><span class="lineno"> 2112</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02113"></a><span class="lineno"> 2113</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02114"></a><span class="lineno"> 2114</span>&#160;<span class="comment">CLASS_DIAGRAMS         = YES</span></div>
<div class="line"><a name="l02115"></a><span class="lineno"> 2115</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02116"></a><span class="lineno"> 2116</span>&#160;<span class="comment"># You can define message sequence charts within doxygen comments using the \msc</span></div>
<div class="line"><a name="l02117"></a><span class="lineno"> 2117</span>&#160;<span class="comment"># command. Doxygen will then run the mscgen tool (see:</span></div>
<div class="line"><a name="l02118"></a><span class="lineno"> 2118</span>&#160;<span class="comment"># http://www.mcternan.me.uk/mscgen/)) to produce the chart and insert it in the</span></div>
<div class="line"><a name="l02119"></a><span class="lineno"> 2119</span>&#160;<span class="comment"># documentation. The MSCGEN_PATH tag allows you to specify the directory where</span></div>
<div class="line"><a name="l02120"></a><span class="lineno"> 2120</span>&#160;<span class="comment"># the mscgen tool resides. If left empty the tool is assumed to be found in the</span></div>
<div class="line"><a name="l02121"></a><span class="lineno"> 2121</span>&#160;<span class="comment"># default search path.</span></div>
<div class="line"><a name="l02122"></a><span class="lineno"> 2122</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02123"></a><span class="lineno"> 2123</span>&#160;<span class="comment">MSCGEN_PATH            = </span></div>
<div class="line"><a name="l02124"></a><span class="lineno"> 2124</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02125"></a><span class="lineno"> 2125</span>&#160;<span class="comment"># You can include diagrams made with dia in doxygen documentation. Doxygen will</span></div>
<div class="line"><a name="l02126"></a><span class="lineno"> 2126</span>&#160;<span class="comment"># then run dia to produce the diagram and insert it in the documentation. The</span></div>
<div class="line"><a name="l02127"></a><span class="lineno"> 2127</span>&#160;<span class="comment"># DIA_PATH tag allows you to specify the directory where the dia binary resides.</span></div>
<div class="line"><a name="l02128"></a><span class="lineno"> 2128</span>&#160;<span class="comment"># If left empty dia is assumed to be found in the default search path.</span></div>
<div class="line"><a name="l02129"></a><span class="lineno"> 2129</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02130"></a><span class="lineno"> 2130</span>&#160;<span class="comment">DIA_PATH               = </span></div>
<div class="line"><a name="l02131"></a><span class="lineno"> 2131</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02132"></a><span class="lineno"> 2132</span>&#160;<span class="comment"># If set to YES the inheritance and collaboration graphs will hide inheritance</span></div>
<div class="line"><a name="l02133"></a><span class="lineno"> 2133</span>&#160;<span class="comment"># and usage relations if the target is undocumented or is not a class.</span></div>
<div class="line"><a name="l02134"></a><span class="lineno"> 2134</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02135"></a><span class="lineno"> 2135</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02136"></a><span class="lineno"> 2136</span>&#160;<span class="comment">HIDE_UNDOC_RELATIONS   = YES</span></div>
<div class="line"><a name="l02137"></a><span class="lineno"> 2137</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02138"></a><span class="lineno"> 2138</span>&#160;<span class="comment"># If you set the HAVE_DOT tag to YES then doxygen will assume the dot tool is</span></div>
<div class="line"><a name="l02139"></a><span class="lineno"> 2139</span>&#160;<span class="comment"># available from the path. This tool is part of Graphviz (see:</span></div>
<div class="line"><a name="l02140"></a><span class="lineno"> 2140</span>&#160;<span class="comment"># http://www.graphviz.org/), a graph visualization toolkit from AT&amp;T and Lucent</span></div>
<div class="line"><a name="l02141"></a><span class="lineno"> 2141</span>&#160;<span class="comment"># Bell Labs. The other options in this section have no effect if this option is</span></div>
<div class="line"><a name="l02142"></a><span class="lineno"> 2142</span>&#160;<span class="comment"># set to NO</span></div>
<div class="line"><a name="l02143"></a><span class="lineno"> 2143</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02144"></a><span class="lineno"> 2144</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02145"></a><span class="lineno"> 2145</span>&#160;<span class="comment">HAVE_DOT               = NO</span></div>
<div class="line"><a name="l02146"></a><span class="lineno"> 2146</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02147"></a><span class="lineno"> 2147</span>&#160;<span class="comment"># The DOT_NUM_THREADS specifies the number of dot invocations doxygen is allowed</span></div>
<div class="line"><a name="l02148"></a><span class="lineno"> 2148</span>&#160;<span class="comment"># to run in parallel. When set to 0 doxygen will base this on the number of</span></div>
<div class="line"><a name="l02149"></a><span class="lineno"> 2149</span>&#160;<span class="comment"># processors available in the system. You can set it explicitly to a value</span></div>
<div class="line"><a name="l02150"></a><span class="lineno"> 2150</span>&#160;<span class="comment"># larger than 0 to get control over the balance between CPU load and processing</span></div>
<div class="line"><a name="l02151"></a><span class="lineno"> 2151</span>&#160;<span class="comment"># speed.</span></div>
<div class="line"><a name="l02152"></a><span class="lineno"> 2152</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 32, default value: 0.</span></div>
<div class="line"><a name="l02153"></a><span class="lineno"> 2153</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02154"></a><span class="lineno"> 2154</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02155"></a><span class="lineno"> 2155</span>&#160;<span class="comment">DOT_NUM_THREADS        = 0</span></div>
<div class="line"><a name="l02156"></a><span class="lineno"> 2156</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02157"></a><span class="lineno"> 2157</span>&#160;<span class="comment"># When you want a differently looking font in the dot files that doxygen</span></div>
<div class="line"><a name="l02158"></a><span class="lineno"> 2158</span>&#160;<span class="comment"># generates you can specify the font name using DOT_FONTNAME. You need to make</span></div>
<div class="line"><a name="l02159"></a><span class="lineno"> 2159</span>&#160;<span class="comment"># sure dot is able to find the font, which can be done by putting it in a</span></div>
<div class="line"><a name="l02160"></a><span class="lineno"> 2160</span>&#160;<span class="comment"># standard location or by setting the DOTFONTPATH environment variable or by</span></div>
<div class="line"><a name="l02161"></a><span class="lineno"> 2161</span>&#160;<span class="comment"># setting DOT_FONTPATH to the directory containing the font.</span></div>
<div class="line"><a name="l02162"></a><span class="lineno"> 2162</span>&#160;<span class="comment"># The default value is: Helvetica.</span></div>
<div class="line"><a name="l02163"></a><span class="lineno"> 2163</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02164"></a><span class="lineno"> 2164</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02165"></a><span class="lineno"> 2165</span>&#160;<span class="comment">DOT_FONTNAME           = Helvetica</span></div>
<div class="line"><a name="l02166"></a><span class="lineno"> 2166</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02167"></a><span class="lineno"> 2167</span>&#160;<span class="comment"># The DOT_FONTSIZE tag can be used to set the size (in points) of the font of</span></div>
<div class="line"><a name="l02168"></a><span class="lineno"> 2168</span>&#160;<span class="comment"># dot graphs.</span></div>
<div class="line"><a name="l02169"></a><span class="lineno"> 2169</span>&#160;<span class="comment"># Minimum value: 4, maximum value: 24, default value: 10.</span></div>
<div class="line"><a name="l02170"></a><span class="lineno"> 2170</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02171"></a><span class="lineno"> 2171</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02172"></a><span class="lineno"> 2172</span>&#160;<span class="comment">DOT_FONTSIZE           = 10</span></div>
<div class="line"><a name="l02173"></a><span class="lineno"> 2173</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02174"></a><span class="lineno"> 2174</span>&#160;<span class="comment"># By default doxygen will tell dot to use the default font as specified with</span></div>
<div class="line"><a name="l02175"></a><span class="lineno"> 2175</span>&#160;<span class="comment"># DOT_FONTNAME. If you specify a different font using DOT_FONTNAME you can set</span></div>
<div class="line"><a name="l02176"></a><span class="lineno"> 2176</span>&#160;<span class="comment"># the path where dot can find it using this tag.</span></div>
<div class="line"><a name="l02177"></a><span class="lineno"> 2177</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02178"></a><span class="lineno"> 2178</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02179"></a><span class="lineno"> 2179</span>&#160;<span class="comment">DOT_FONTPATH           = </span></div>
<div class="line"><a name="l02180"></a><span class="lineno"> 2180</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02181"></a><span class="lineno"> 2181</span>&#160;<span class="comment"># If the CLASS_GRAPH tag is set to YES then doxygen will generate a graph for</span></div>
<div class="line"><a name="l02182"></a><span class="lineno"> 2182</span>&#160;<span class="comment"># each documented class showing the direct and indirect inheritance relations.</span></div>
<div class="line"><a name="l02183"></a><span class="lineno"> 2183</span>&#160;<span class="comment"># Setting this tag to YES will force the CLASS_DIAGRAMS tag to NO.</span></div>
<div class="line"><a name="l02184"></a><span class="lineno"> 2184</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02185"></a><span class="lineno"> 2185</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02186"></a><span class="lineno"> 2186</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02187"></a><span class="lineno"> 2187</span>&#160;<span class="comment">CLASS_GRAPH            = YES</span></div>
<div class="line"><a name="l02188"></a><span class="lineno"> 2188</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02189"></a><span class="lineno"> 2189</span>&#160;<span class="comment"># If the COLLABORATION_GRAPH tag is set to YES then doxygen will generate a</span></div>
<div class="line"><a name="l02190"></a><span class="lineno"> 2190</span>&#160;<span class="comment"># graph for each documented class showing the direct and indirect implementation</span></div>
<div class="line"><a name="l02191"></a><span class="lineno"> 2191</span>&#160;<span class="comment"># dependencies (inheritance, containment, and class references variables) of the</span></div>
<div class="line"><a name="l02192"></a><span class="lineno"> 2192</span>&#160;<span class="comment"># class with other documented classes.</span></div>
<div class="line"><a name="l02193"></a><span class="lineno"> 2193</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02194"></a><span class="lineno"> 2194</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02195"></a><span class="lineno"> 2195</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02196"></a><span class="lineno"> 2196</span>&#160;<span class="comment">COLLABORATION_GRAPH    = YES</span></div>
<div class="line"><a name="l02197"></a><span class="lineno"> 2197</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02198"></a><span class="lineno"> 2198</span>&#160;<span class="comment"># If the GROUP_GRAPHS tag is set to YES then doxygen will generate a graph for</span></div>
<div class="line"><a name="l02199"></a><span class="lineno"> 2199</span>&#160;<span class="comment"># groups, showing the direct groups dependencies.</span></div>
<div class="line"><a name="l02200"></a><span class="lineno"> 2200</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02201"></a><span class="lineno"> 2201</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02202"></a><span class="lineno"> 2202</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02203"></a><span class="lineno"> 2203</span>&#160;<span class="comment">GROUP_GRAPHS           = YES</span></div>
<div class="line"><a name="l02204"></a><span class="lineno"> 2204</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02205"></a><span class="lineno"> 2205</span>&#160;<span class="comment"># If the UML_LOOK tag is set to YES, doxygen will generate inheritance and</span></div>
<div class="line"><a name="l02206"></a><span class="lineno"> 2206</span>&#160;<span class="comment"># collaboration diagrams in a style similar to the OMG&#39;s Unified Modeling</span></div>
<div class="line"><a name="l02207"></a><span class="lineno"> 2207</span>&#160;<span class="comment"># Language.</span></div>
<div class="line"><a name="l02208"></a><span class="lineno"> 2208</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02209"></a><span class="lineno"> 2209</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02210"></a><span class="lineno"> 2210</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02211"></a><span class="lineno"> 2211</span>&#160;<span class="comment">UML_LOOK               = NO</span></div>
<div class="line"><a name="l02212"></a><span class="lineno"> 2212</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02213"></a><span class="lineno"> 2213</span>&#160;<span class="comment"># If the UML_LOOK tag is enabled, the fields and methods are shown inside the</span></div>
<div class="line"><a name="l02214"></a><span class="lineno"> 2214</span>&#160;<span class="comment"># class node. If there are many fields or methods and many nodes the graph may</span></div>
<div class="line"><a name="l02215"></a><span class="lineno"> 2215</span>&#160;<span class="comment"># become too big to be useful. The UML_LIMIT_NUM_FIELDS threshold limits the</span></div>
<div class="line"><a name="l02216"></a><span class="lineno"> 2216</span>&#160;<span class="comment"># number of items for each type to make the size more manageable. Set this to 0</span></div>
<div class="line"><a name="l02217"></a><span class="lineno"> 2217</span>&#160;<span class="comment"># for no limit. Note that the threshold may be exceeded by 50% before the limit</span></div>
<div class="line"><a name="l02218"></a><span class="lineno"> 2218</span>&#160;<span class="comment"># is enforced. So when you set the threshold to 10, up to 15 fields may appear,</span></div>
<div class="line"><a name="l02219"></a><span class="lineno"> 2219</span>&#160;<span class="comment"># but if the number exceeds 15, the total amount of fields shown is limited to</span></div>
<div class="line"><a name="l02220"></a><span class="lineno"> 2220</span>&#160;<span class="comment"># 10.</span></div>
<div class="line"><a name="l02221"></a><span class="lineno"> 2221</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 100, default value: 10.</span></div>
<div class="line"><a name="l02222"></a><span class="lineno"> 2222</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02223"></a><span class="lineno"> 2223</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02224"></a><span class="lineno"> 2224</span>&#160;<span class="comment">UML_LIMIT_NUM_FIELDS   = 10</span></div>
<div class="line"><a name="l02225"></a><span class="lineno"> 2225</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02226"></a><span class="lineno"> 2226</span>&#160;<span class="comment"># If the TEMPLATE_RELATIONS tag is set to YES then the inheritance and</span></div>
<div class="line"><a name="l02227"></a><span class="lineno"> 2227</span>&#160;<span class="comment"># collaboration graphs will show the relations between templates and their</span></div>
<div class="line"><a name="l02228"></a><span class="lineno"> 2228</span>&#160;<span class="comment"># instances.</span></div>
<div class="line"><a name="l02229"></a><span class="lineno"> 2229</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02230"></a><span class="lineno"> 2230</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02231"></a><span class="lineno"> 2231</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02232"></a><span class="lineno"> 2232</span>&#160;<span class="comment">TEMPLATE_RELATIONS     = NO</span></div>
<div class="line"><a name="l02233"></a><span class="lineno"> 2233</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02234"></a><span class="lineno"> 2234</span>&#160;<span class="comment"># If the INCLUDE_GRAPH, ENABLE_PREPROCESSING and SEARCH_INCLUDES tags are set to</span></div>
<div class="line"><a name="l02235"></a><span class="lineno"> 2235</span>&#160;<span class="comment"># YES then doxygen will generate a graph for each documented file showing the</span></div>
<div class="line"><a name="l02236"></a><span class="lineno"> 2236</span>&#160;<span class="comment"># direct and indirect include dependencies of the file with other documented</span></div>
<div class="line"><a name="l02237"></a><span class="lineno"> 2237</span>&#160;<span class="comment"># files.</span></div>
<div class="line"><a name="l02238"></a><span class="lineno"> 2238</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02239"></a><span class="lineno"> 2239</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02240"></a><span class="lineno"> 2240</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02241"></a><span class="lineno"> 2241</span>&#160;<span class="comment">INCLUDE_GRAPH          = YES</span></div>
<div class="line"><a name="l02242"></a><span class="lineno"> 2242</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02243"></a><span class="lineno"> 2243</span>&#160;<span class="comment"># If the INCLUDED_BY_GRAPH, ENABLE_PREPROCESSING and SEARCH_INCLUDES tags are</span></div>
<div class="line"><a name="l02244"></a><span class="lineno"> 2244</span>&#160;<span class="comment"># set to YES then doxygen will generate a graph for each documented file showing</span></div>
<div class="line"><a name="l02245"></a><span class="lineno"> 2245</span>&#160;<span class="comment"># the direct and indirect include dependencies of the file with other documented</span></div>
<div class="line"><a name="l02246"></a><span class="lineno"> 2246</span>&#160;<span class="comment"># files.</span></div>
<div class="line"><a name="l02247"></a><span class="lineno"> 2247</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02248"></a><span class="lineno"> 2248</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02249"></a><span class="lineno"> 2249</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02250"></a><span class="lineno"> 2250</span>&#160;<span class="comment">INCLUDED_BY_GRAPH      = YES</span></div>
<div class="line"><a name="l02251"></a><span class="lineno"> 2251</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02252"></a><span class="lineno"> 2252</span>&#160;<span class="comment"># If the CALL_GRAPH tag is set to YES then doxygen will generate a call</span></div>
<div class="line"><a name="l02253"></a><span class="lineno"> 2253</span>&#160;<span class="comment"># dependency graph for every global function or class method.</span></div>
<div class="line"><a name="l02254"></a><span class="lineno"> 2254</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l02255"></a><span class="lineno"> 2255</span>&#160;<span class="comment"># Note that enabling this option will significantly increase the time of a run.</span></div>
<div class="line"><a name="l02256"></a><span class="lineno"> 2256</span>&#160;<span class="comment"># So in most cases it will be better to enable call graphs for selected</span></div>
<div class="line"><a name="l02257"></a><span class="lineno"> 2257</span>&#160;<span class="comment"># functions only using the \callgraph command. Disabling a call graph can be</span></div>
<div class="line"><a name="l02258"></a><span class="lineno"> 2258</span>&#160;<span class="comment"># accomplished by means of the command \hidecallgraph.</span></div>
<div class="line"><a name="l02259"></a><span class="lineno"> 2259</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02260"></a><span class="lineno"> 2260</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02261"></a><span class="lineno"> 2261</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02262"></a><span class="lineno"> 2262</span>&#160;<span class="comment">CALL_GRAPH             = YES</span></div>
<div class="line"><a name="l02263"></a><span class="lineno"> 2263</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02264"></a><span class="lineno"> 2264</span>&#160;<span class="comment"># If the CALLER_GRAPH tag is set to YES then doxygen will generate a caller</span></div>
<div class="line"><a name="l02265"></a><span class="lineno"> 2265</span>&#160;<span class="comment"># dependency graph for every global function or class method.</span></div>
<div class="line"><a name="l02266"></a><span class="lineno"> 2266</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l02267"></a><span class="lineno"> 2267</span>&#160;<span class="comment"># Note that enabling this option will significantly increase the time of a run.</span></div>
<div class="line"><a name="l02268"></a><span class="lineno"> 2268</span>&#160;<span class="comment"># So in most cases it will be better to enable caller graphs for selected</span></div>
<div class="line"><a name="l02269"></a><span class="lineno"> 2269</span>&#160;<span class="comment"># functions only using the \callergraph command. Disabling a caller graph can be</span></div>
<div class="line"><a name="l02270"></a><span class="lineno"> 2270</span>&#160;<span class="comment"># accomplished by means of the command \hidecallergraph.</span></div>
<div class="line"><a name="l02271"></a><span class="lineno"> 2271</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02272"></a><span class="lineno"> 2272</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02273"></a><span class="lineno"> 2273</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02274"></a><span class="lineno"> 2274</span>&#160;<span class="comment">CALLER_GRAPH           = YES</span></div>
<div class="line"><a name="l02275"></a><span class="lineno"> 2275</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02276"></a><span class="lineno"> 2276</span>&#160;<span class="comment"># If the GRAPHICAL_HIERARCHY tag is set to YES then doxygen will graphical</span></div>
<div class="line"><a name="l02277"></a><span class="lineno"> 2277</span>&#160;<span class="comment"># hierarchy of all classes instead of a textual one.</span></div>
<div class="line"><a name="l02278"></a><span class="lineno"> 2278</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02279"></a><span class="lineno"> 2279</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02280"></a><span class="lineno"> 2280</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02281"></a><span class="lineno"> 2281</span>&#160;<span class="comment">GRAPHICAL_HIERARCHY    = YES</span></div>
<div class="line"><a name="l02282"></a><span class="lineno"> 2282</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02283"></a><span class="lineno"> 2283</span>&#160;<span class="comment"># If the DIRECTORY_GRAPH tag is set to YES then doxygen will show the</span></div>
<div class="line"><a name="l02284"></a><span class="lineno"> 2284</span>&#160;<span class="comment"># dependencies a directory has on other directories in a graphical way. The</span></div>
<div class="line"><a name="l02285"></a><span class="lineno"> 2285</span>&#160;<span class="comment"># dependency relations are determined by the #include relations between the</span></div>
<div class="line"><a name="l02286"></a><span class="lineno"> 2286</span>&#160;<span class="comment"># files in the directories.</span></div>
<div class="line"><a name="l02287"></a><span class="lineno"> 2287</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02288"></a><span class="lineno"> 2288</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02289"></a><span class="lineno"> 2289</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02290"></a><span class="lineno"> 2290</span>&#160;<span class="comment">DIRECTORY_GRAPH        = YES</span></div>
<div class="line"><a name="l02291"></a><span class="lineno"> 2291</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02292"></a><span class="lineno"> 2292</span>&#160;<span class="comment"># The DOT_IMAGE_FORMAT tag can be used to set the image format of the images</span></div>
<div class="line"><a name="l02293"></a><span class="lineno"> 2293</span>&#160;<span class="comment"># generated by dot. For an explanation of the image formats see the section</span></div>
<div class="line"><a name="l02294"></a><span class="lineno"> 2294</span>&#160;<span class="comment"># output formats in the documentation of the dot tool (Graphviz (see:</span></div>
<div class="line"><a name="l02295"></a><span class="lineno"> 2295</span>&#160;<span class="comment"># http://www.graphviz.org/)).</span></div>
<div class="line"><a name="l02296"></a><span class="lineno"> 2296</span>&#160;<span class="comment"># Note: If you choose svg you need to set HTML_FILE_EXTENSION to xhtml in order</span></div>
<div class="line"><a name="l02297"></a><span class="lineno"> 2297</span>&#160;<span class="comment"># to make the SVG files visible in IE 9+ (other browsers do not have this</span></div>
<div class="line"><a name="l02298"></a><span class="lineno"> 2298</span>&#160;<span class="comment"># requirement).</span></div>
<div class="line"><a name="l02299"></a><span class="lineno"> 2299</span>&#160;<span class="comment"># Possible values are: png, jpg, gif, svg, png:gd, png:gd:gd, png:cairo,</span></div>
<div class="line"><a name="l02300"></a><span class="lineno"> 2300</span>&#160;<span class="comment"># png:cairo:gd, png:cairo:cairo, png:cairo:gdiplus, png:gdiplus and</span></div>
<div class="line"><a name="l02301"></a><span class="lineno"> 2301</span>&#160;<span class="comment"># png:gdiplus:gdiplus.</span></div>
<div class="line"><a name="l02302"></a><span class="lineno"> 2302</span>&#160;<span class="comment"># The default value is: png.</span></div>
<div class="line"><a name="l02303"></a><span class="lineno"> 2303</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02304"></a><span class="lineno"> 2304</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02305"></a><span class="lineno"> 2305</span>&#160;<span class="comment">DOT_IMAGE_FORMAT       = png</span></div>
<div class="line"><a name="l02306"></a><span class="lineno"> 2306</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02307"></a><span class="lineno"> 2307</span>&#160;<span class="comment"># If DOT_IMAGE_FORMAT is set to svg, then this option can be set to YES to</span></div>
<div class="line"><a name="l02308"></a><span class="lineno"> 2308</span>&#160;<span class="comment"># enable generation of interactive SVG images that allow zooming and panning.</span></div>
<div class="line"><a name="l02309"></a><span class="lineno"> 2309</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l02310"></a><span class="lineno"> 2310</span>&#160;<span class="comment"># Note that this requires a modern browser other than Internet Explorer. Tested</span></div>
<div class="line"><a name="l02311"></a><span class="lineno"> 2311</span>&#160;<span class="comment"># and working are Firefox, Chrome, Safari, and Opera.</span></div>
<div class="line"><a name="l02312"></a><span class="lineno"> 2312</span>&#160;<span class="comment"># Note: For IE 9+ you need to set HTML_FILE_EXTENSION to xhtml in order to make</span></div>
<div class="line"><a name="l02313"></a><span class="lineno"> 2313</span>&#160;<span class="comment"># the SVG files visible. Older versions of IE do not have SVG support.</span></div>
<div class="line"><a name="l02314"></a><span class="lineno"> 2314</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02315"></a><span class="lineno"> 2315</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02316"></a><span class="lineno"> 2316</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02317"></a><span class="lineno"> 2317</span>&#160;<span class="comment">INTERACTIVE_SVG        = NO</span></div>
<div class="line"><a name="l02318"></a><span class="lineno"> 2318</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02319"></a><span class="lineno"> 2319</span>&#160;<span class="comment"># The DOT_PATH tag can be used to specify the path where the dot tool can be</span></div>
<div class="line"><a name="l02320"></a><span class="lineno"> 2320</span>&#160;<span class="comment"># found. If left blank, it is assumed the dot tool can be found in the path.</span></div>
<div class="line"><a name="l02321"></a><span class="lineno"> 2321</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02322"></a><span class="lineno"> 2322</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02323"></a><span class="lineno"> 2323</span>&#160;<span class="comment">DOT_PATH               = </span></div>
<div class="line"><a name="l02324"></a><span class="lineno"> 2324</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02325"></a><span class="lineno"> 2325</span>&#160;<span class="comment"># The DOTFILE_DIRS tag can be used to specify one or more directories that</span></div>
<div class="line"><a name="l02326"></a><span class="lineno"> 2326</span>&#160;<span class="comment"># contain dot files that are included in the documentation (see the \dotfile</span></div>
<div class="line"><a name="l02327"></a><span class="lineno"> 2327</span>&#160;<span class="comment"># command).</span></div>
<div class="line"><a name="l02328"></a><span class="lineno"> 2328</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02329"></a><span class="lineno"> 2329</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02330"></a><span class="lineno"> 2330</span>&#160;<span class="comment">DOTFILE_DIRS           = </span></div>
<div class="line"><a name="l02331"></a><span class="lineno"> 2331</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02332"></a><span class="lineno"> 2332</span>&#160;<span class="comment"># The MSCFILE_DIRS tag can be used to specify one or more directories that</span></div>
<div class="line"><a name="l02333"></a><span class="lineno"> 2333</span>&#160;<span class="comment"># contain msc files that are included in the documentation (see the \mscfile</span></div>
<div class="line"><a name="l02334"></a><span class="lineno"> 2334</span>&#160;<span class="comment"># command).</span></div>
<div class="line"><a name="l02335"></a><span class="lineno"> 2335</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02336"></a><span class="lineno"> 2336</span>&#160;<span class="comment">MSCFILE_DIRS           = </span></div>
<div class="line"><a name="l02337"></a><span class="lineno"> 2337</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02338"></a><span class="lineno"> 2338</span>&#160;<span class="comment"># The DIAFILE_DIRS tag can be used to specify one or more directories that</span></div>
<div class="line"><a name="l02339"></a><span class="lineno"> 2339</span>&#160;<span class="comment"># contain dia files that are included in the documentation (see the \diafile</span></div>
<div class="line"><a name="l02340"></a><span class="lineno"> 2340</span>&#160;<span class="comment"># command).</span></div>
<div class="line"><a name="l02341"></a><span class="lineno"> 2341</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02342"></a><span class="lineno"> 2342</span>&#160;<span class="comment">DIAFILE_DIRS           = </span></div>
<div class="line"><a name="l02343"></a><span class="lineno"> 2343</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02344"></a><span class="lineno"> 2344</span>&#160;<span class="comment"># When using plantuml, the PLANTUML_JAR_PATH tag should be used to specify the</span></div>
<div class="line"><a name="l02345"></a><span class="lineno"> 2345</span>&#160;<span class="comment"># path where java can find the plantuml.jar file. If left blank, it is assumed</span></div>
<div class="line"><a name="l02346"></a><span class="lineno"> 2346</span>&#160;<span class="comment"># PlantUML is not used or called during a preprocessing step. Doxygen will</span></div>
<div class="line"><a name="l02347"></a><span class="lineno"> 2347</span>&#160;<span class="comment"># generate a warning when it encounters a \startuml command in this case and</span></div>
<div class="line"><a name="l02348"></a><span class="lineno"> 2348</span>&#160;<span class="comment"># will not generate output for the diagram.</span></div>
<div class="line"><a name="l02349"></a><span class="lineno"> 2349</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02350"></a><span class="lineno"> 2350</span>&#160;<span class="comment">PLANTUML_JAR_PATH      = </span></div>
<div class="line"><a name="l02351"></a><span class="lineno"> 2351</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02352"></a><span class="lineno"> 2352</span>&#160;<span class="comment"># When using plantuml, the specified paths are searched for files specified by</span></div>
<div class="line"><a name="l02353"></a><span class="lineno"> 2353</span>&#160;<span class="comment"># the !include statement in a plantuml block.</span></div>
<div class="line"><a name="l02354"></a><span class="lineno"> 2354</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02355"></a><span class="lineno"> 2355</span>&#160;<span class="comment">PLANTUML_INCLUDE_PATH  = </span></div>
<div class="line"><a name="l02356"></a><span class="lineno"> 2356</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02357"></a><span class="lineno"> 2357</span>&#160;<span class="comment"># The DOT_GRAPH_MAX_NODES tag can be used to set the maximum number of nodes</span></div>
<div class="line"><a name="l02358"></a><span class="lineno"> 2358</span>&#160;<span class="comment"># that will be shown in the graph. If the number of nodes in a graph becomes</span></div>
<div class="line"><a name="l02359"></a><span class="lineno"> 2359</span>&#160;<span class="comment"># larger than this value, doxygen will truncate the graph, which is visualized</span></div>
<div class="line"><a name="l02360"></a><span class="lineno"> 2360</span>&#160;<span class="comment"># by representing a node as a red box. Note that doxygen if the number of direct</span></div>
<div class="line"><a name="l02361"></a><span class="lineno"> 2361</span>&#160;<span class="comment"># children of the root node in a graph is already larger than</span></div>
<div class="line"><a name="l02362"></a><span class="lineno"> 2362</span>&#160;<span class="comment"># DOT_GRAPH_MAX_NODES then the graph will not be shown at all. Also note that</span></div>
<div class="line"><a name="l02363"></a><span class="lineno"> 2363</span>&#160;<span class="comment"># the size of a graph can be further restricted by MAX_DOT_GRAPH_DEPTH.</span></div>
<div class="line"><a name="l02364"></a><span class="lineno"> 2364</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 10000, default value: 50.</span></div>
<div class="line"><a name="l02365"></a><span class="lineno"> 2365</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02366"></a><span class="lineno"> 2366</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02367"></a><span class="lineno"> 2367</span>&#160;<span class="comment">DOT_GRAPH_MAX_NODES    = 50</span></div>
<div class="line"><a name="l02368"></a><span class="lineno"> 2368</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02369"></a><span class="lineno"> 2369</span>&#160;<span class="comment"># The MAX_DOT_GRAPH_DEPTH tag can be used to set the maximum depth of the graphs</span></div>
<div class="line"><a name="l02370"></a><span class="lineno"> 2370</span>&#160;<span class="comment"># generated by dot. A depth value of 3 means that only nodes reachable from the</span></div>
<div class="line"><a name="l02371"></a><span class="lineno"> 2371</span>&#160;<span class="comment"># root by following a path via at most 3 edges will be shown. Nodes that lay</span></div>
<div class="line"><a name="l02372"></a><span class="lineno"> 2372</span>&#160;<span class="comment"># further from the root node will be omitted. Note that setting this option to 1</span></div>
<div class="line"><a name="l02373"></a><span class="lineno"> 2373</span>&#160;<span class="comment"># or 2 may greatly reduce the computation time needed for large code bases. Also</span></div>
<div class="line"><a name="l02374"></a><span class="lineno"> 2374</span>&#160;<span class="comment"># note that the size of a graph can be further restricted by</span></div>
<div class="line"><a name="l02375"></a><span class="lineno"> 2375</span>&#160;<span class="comment"># DOT_GRAPH_MAX_NODES. Using a depth of 0 means no depth restriction.</span></div>
<div class="line"><a name="l02376"></a><span class="lineno"> 2376</span>&#160;<span class="comment"># Minimum value: 0, maximum value: 1000, default value: 0.</span></div>
<div class="line"><a name="l02377"></a><span class="lineno"> 2377</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02378"></a><span class="lineno"> 2378</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02379"></a><span class="lineno"> 2379</span>&#160;<span class="comment">MAX_DOT_GRAPH_DEPTH    = 1000</span></div>
<div class="line"><a name="l02380"></a><span class="lineno"> 2380</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02381"></a><span class="lineno"> 2381</span>&#160;<span class="comment"># Set the DOT_TRANSPARENT tag to YES to generate images with a transparent</span></div>
<div class="line"><a name="l02382"></a><span class="lineno"> 2382</span>&#160;<span class="comment"># background. This is disabled by default, because dot on Windows does not seem</span></div>
<div class="line"><a name="l02383"></a><span class="lineno"> 2383</span>&#160;<span class="comment"># to support this out of the box.</span></div>
<div class="line"><a name="l02384"></a><span class="lineno"> 2384</span>&#160;<span class="comment">#</span></div>
<div class="line"><a name="l02385"></a><span class="lineno"> 2385</span>&#160;<span class="comment"># Warning: Depending on the platform used, enabling this option may lead to</span></div>
<div class="line"><a name="l02386"></a><span class="lineno"> 2386</span>&#160;<span class="comment"># badly anti-aliased labels on the edges of a graph (i.e. they become hard to</span></div>
<div class="line"><a name="l02387"></a><span class="lineno"> 2387</span>&#160;<span class="comment"># read).</span></div>
<div class="line"><a name="l02388"></a><span class="lineno"> 2388</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02389"></a><span class="lineno"> 2389</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02390"></a><span class="lineno"> 2390</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02391"></a><span class="lineno"> 2391</span>&#160;<span class="comment">DOT_TRANSPARENT        = NO</span></div>
<div class="line"><a name="l02392"></a><span class="lineno"> 2392</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02393"></a><span class="lineno"> 2393</span>&#160;<span class="comment"># Set the DOT_MULTI_TARGETS tag to YES to allow dot to generate multiple output</span></div>
<div class="line"><a name="l02394"></a><span class="lineno"> 2394</span>&#160;<span class="comment"># files in one run (i.e. multiple -o and -T options on the command line). This</span></div>
<div class="line"><a name="l02395"></a><span class="lineno"> 2395</span>&#160;<span class="comment"># makes dot run faster, but since only newer versions of dot (&gt;1.8.10) support</span></div>
<div class="line"><a name="l02396"></a><span class="lineno"> 2396</span>&#160;<span class="comment"># this, this feature is disabled by default.</span></div>
<div class="line"><a name="l02397"></a><span class="lineno"> 2397</span>&#160;<span class="comment"># The default value is: NO.</span></div>
<div class="line"><a name="l02398"></a><span class="lineno"> 2398</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02399"></a><span class="lineno"> 2399</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02400"></a><span class="lineno"> 2400</span>&#160;<span class="comment">DOT_MULTI_TARGETS      = NO</span></div>
<div class="line"><a name="l02401"></a><span class="lineno"> 2401</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02402"></a><span class="lineno"> 2402</span>&#160;<span class="comment"># If the GENERATE_LEGEND tag is set to YES doxygen will generate a legend page</span></div>
<div class="line"><a name="l02403"></a><span class="lineno"> 2403</span>&#160;<span class="comment"># explaining the meaning of the various boxes and arrows in the dot generated</span></div>
<div class="line"><a name="l02404"></a><span class="lineno"> 2404</span>&#160;<span class="comment"># graphs.</span></div>
<div class="line"><a name="l02405"></a><span class="lineno"> 2405</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02406"></a><span class="lineno"> 2406</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02407"></a><span class="lineno"> 2407</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02408"></a><span class="lineno"> 2408</span>&#160;<span class="comment">GENERATE_LEGEND        = YES</span></div>
<div class="line"><a name="l02409"></a><span class="lineno"> 2409</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02410"></a><span class="lineno"> 2410</span>&#160;<span class="comment"># If the DOT_CLEANUP tag is set to YES, doxygen will remove the intermediate dot</span></div>
<div class="line"><a name="l02411"></a><span class="lineno"> 2411</span>&#160;<span class="comment"># files that are used to generate the various graphs.</span></div>
<div class="line"><a name="l02412"></a><span class="lineno"> 2412</span>&#160;<span class="comment"># The default value is: YES.</span></div>
<div class="line"><a name="l02413"></a><span class="lineno"> 2413</span>&#160;<span class="comment"># This tag requires that the tag HAVE_DOT is set to YES.</span></div>
<div class="line"><a name="l02414"></a><span class="lineno"> 2414</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l02415"></a><span class="lineno"> 2415</span>&#160;<span class="comment">DOT_CLEANUP            = YES</span></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
