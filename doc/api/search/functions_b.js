var searchData=
[
  ['next_5ffloat_2830',['next_float',['../a00915.html#gab21fbe69182da4f378862feeffe24b16',1,'glm::next_float(genType x)'],['../a00915.html#gaf8540f4caeba5037dee6506184f360b0',1,'glm::next_float(genType x, int ULPs)'],['../a00915.html#ga72c18d50df8ef360960ddf1f5d09c728',1,'glm::next_float(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00915.html#ga78b63ddacacb9e0e8f4172d85f4373aa',1,'glm::next_float(vec&lt; L, T, Q &gt; const &amp;x, int ULPs)'],['../a00915.html#ga48e17607989d47bc99e16cce74543e19',1,'glm::next_float(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, int, Q &gt; const &amp;ULPs)']]],
  ['nextfloat_2831',['nextFloat',['../a00865.html#ga30bc0280e7cefd159867b1aa5050b94a',1,'glm::nextFloat(genType x)'],['../a00865.html#ga54eb5916c5250c8f0ad8224fb8e0d392',1,'glm::nextFloat(genType x, int ULPs)'],['../a00887.html#gadbd6e5dff9c0ae4567b3edd9019c1bee',1,'glm::nextFloat(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00887.html#ga92f82c4f45b5b43ccc29533990db079d',1,'glm::nextFloat(vec&lt; L, T, Q &gt; const &amp;x, int ULPs)'],['../a00887.html#ga48e9b73c50fcf589e0032b8dbed9a3f9',1,'glm::nextFloat(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, int, Q &gt; const &amp;ULPs)']]],
  ['nextmultiple_2832',['nextMultiple',['../a00860.html#gab770a3835c44c8a6fd225be4f4e6b317',1,'glm::nextMultiple(genIUType v, genIUType Multiple)'],['../a00878.html#gace38d00601cbf49cd4dc03f003ab42b7',1,'glm::nextMultiple(vec&lt; L, T, Q &gt; const &amp;v, T Multiple)'],['../a00878.html#gacda365edad320c7aff19cc283a3b8ca2',1,'glm::nextMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)']]],
  ['nextpoweroftwo_2833',['nextPowerOfTwo',['../a00860.html#ga3a37c2f2fd347886c9af6a3ca3db04dc',1,'glm::nextPowerOfTwo(genIUType v)'],['../a00878.html#gabba67f8aac9915e10fca727277274502',1,'glm::nextPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['nlz_2834',['nlz',['../a00939.html#ga78dff8bdb361bf0061194c93e003d189',1,'glm']]],
  ['normalize_2835',['normalize',['../a00853.html#gabf30e3263fffe8dcc6659aea76ae8927',1,'glm::normalize(qua&lt; T, Q &gt; const &amp;q)'],['../a00888.html#ga3b8d3dcae77870781392ed2902cce597',1,'glm::normalize(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00926.html#ga299b8641509606b1958ffa104a162cfe',1,'glm::normalize(tdualquat&lt; T, Q &gt; const &amp;q)']]],
  ['normalizedot_2836',['normalizeDot',['../a00954.html#gacb140a2b903115d318c8b0a2fb5a5daa',1,'glm']]],
  ['not_5f_2837',['not_',['../a00985.html#ga610fcd175791fd246e328ffee10dbf1e',1,'glm']]],
  ['notequal_2838',['notEqual',['../a00827.html#ga8504f18a7e2bf315393032c2137dad83',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)'],['../a00827.html#ga29071147d118569344d10944b7d5c378',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, T epsilon)'],['../a00827.html#gad7959e14fbc35b4ed2617daf4d67f6cd',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, T, Q &gt; const &amp;epsilon)'],['../a00827.html#gaa1cd7fc228ef6e26c73583fd0d9c6552',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, int ULPs)'],['../a00827.html#gaa5517341754149ffba742d230afd1f32',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, int, Q &gt; const &amp;ULPs)'],['../a00854.html#gab441cee0de5867a868f3a586ee68cfe1',1,'glm::notEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)'],['../a00854.html#ga5117a44c1bf21af857cd23e44a96d313',1,'glm::notEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T epsilon)'],['../a00863.html#ga835ecf946c74f3d68be93e70b10821e7',1,'glm::notEqual(genType const &amp;x, genType const &amp;y, genType const &amp;epsilon)'],['../a00863.html#gabd21e65b2e4c9d501d51536c4a6ef7cb',1,'glm::notEqual(genType const &amp;x, genType const &amp;y, int ULPs)'],['../a00881.html#ga4a99cc41341567567a608719449c1fac',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T epsilon)'],['../a00881.html#ga417cf51304359db18e819dda9bce5767',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;epsilon)'],['../a00881.html#ga8b5c2c3f83422ae5b71fa960d03b0339',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, int ULPs)'],['../a00881.html#ga0b15ffe32987a6029b14398eb0def01a',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; const &amp;ULPs)'],['../a00985.html#ga17c19dc1b76cd5aef63e9e7ff3aa3c27',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)']]]
];
