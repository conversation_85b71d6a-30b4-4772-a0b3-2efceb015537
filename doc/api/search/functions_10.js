var searchData=
[
  ['saturate_2990',['saturate',['../a00924.html#ga744b98814a35336e25cc0d1ba30f63f7',1,'glm::saturate(T x)'],['../a00924.html#gaee97b8001c794a78a44f5d59f62a8aba',1,'glm::saturate(const vec&lt; 2, T, Q &gt; &amp;x)'],['../a00924.html#ga39bfe3a421286ee31680d45c31ccc161',1,'glm::saturate(const vec&lt; 3, T, Q &gt; &amp;x)'],['../a00924.html#ga356f8c3a7e7d6376d3d4b0a026407183',1,'glm::saturate(const vec&lt; 4, T, Q &gt; &amp;x)']]],
  ['saturation_2991',['saturation',['../a00921.html#ga01a97152b44e1550edcac60bd849e884',1,'glm::saturation(T const s)'],['../a00921.html#ga2156cea600e90148ece5bc96fd6db43a',1,'glm::saturation(T const s, vec&lt; 3, T, Q &gt; const &amp;color)'],['../a00921.html#gaba0eacee0736dae860e9371cc1ae4785',1,'glm::saturation(T const s, vec&lt; 4, T, Q &gt; const &amp;color)']]],
  ['scale_2992',['scale',['../a00828.html#ga05051adbee603fb3c5095d8cf5cc229b',1,'glm::scale(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)'],['../a00950.html#gadb47d2ad2bd984b213e8ff7d9cd8154e',1,'glm::scale(mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 2, T, Q &gt; const &amp;v)'],['../a00973.html#gafbeefee8fec884d566e4ada0049174d7',1,'glm::scale(vec&lt; 3, T, Q &gt; const &amp;v)']]],
  ['scalebias_2993',['scaleBias',['../a00974.html#gabf249498b236e62c983d90d30d63c99c',1,'glm::scaleBias(T scale, T bias)'],['../a00974.html#gae2bdd91a76759fecfbaef97e3020aa8e',1,'glm::scaleBias(mat&lt; 4, 4, T, Q &gt; const &amp;m, T scale, T bias)']]],
  ['sec_2994',['sec',['../a00862.html#ga225db01831b8a4b5a3d9bd3e486ed21c',1,'glm']]],
  ['sech_2995',['sech',['../a00862.html#ga0e16a0de56f2bf9a432dc2776020fc7a',1,'glm']]],
  ['shear_2996',['shear',['../a00828.html#ga391e0142852ab4139dcea0d9b1bbc048',1,'glm']]],
  ['shearx_2997',['shearX',['../a00950.html#ga2a118ece5db1e2022112b954846012af',1,'glm']]],
  ['shearx2d_2998',['shearX2D',['../a00974.html#gabf714b8a358181572b32a45555f71948',1,'glm']]],
  ['shearx3d_2999',['shearX3D',['../a00974.html#ga73e867c6cd4d700fe2054437e56106c4',1,'glm']]],
  ['sheary_3000',['shearY',['../a00950.html#ga717f1833369c1ac4a40e4ac015af885e',1,'glm']]],
  ['sheary2d_3001',['shearY2D',['../a00974.html#gac7998d0763d9181550c77e8af09a182c',1,'glm']]],
  ['sheary3d_3002',['shearY3D',['../a00974.html#gade5bb65ffcb513973db1a1314fb5cfac',1,'glm']]],
  ['shearz3d_3003',['shearZ3D',['../a00974.html#ga6591e0a3a9d2c9c0b6577bb4dace0255',1,'glm']]],
  ['shortmix_3004',['shortMix',['../a00962.html#gadc576cc957adc2a568cdcbc3799175bc',1,'glm']]],
  ['sign_3005',['sign',['../a00803.html#ga589807f35ad0a1d173762bfac3288929',1,'glm::sign(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00942.html#ga04ef803a24f3d4f8c67dbccb33b0fce0',1,'glm::sign(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;base)']]],
  ['simplex_3006',['simplex',['../a00906.html#ga8122468c69015ff397349a7dcc638b27',1,'glm']]],
  ['sin_3007',['sin',['../a00984.html#ga29747fd108cb7292ae5a284f69691a69',1,'glm']]],
  ['sineeasein_3008',['sineEaseIn',['../a00927.html#gafb338ac6f6b2bcafee50e3dca5201dbf',1,'glm']]],
  ['sineeaseinout_3009',['sineEaseInOut',['../a00927.html#gaa46e3d5fbf7a15caa28eff9ef192d7c7',1,'glm']]],
  ['sineeaseout_3010',['sineEaseOut',['../a00927.html#gab3e454f883afc1606ef91363881bf5a3',1,'glm']]],
  ['sinh_3011',['sinh',['../a00984.html#gac7c39ff21809e281552b4dbe46f4a39d',1,'glm']]],
  ['slerp_3012',['slerp',['../a00847.html#gae7fc3c945be366b9942b842f55da428a',1,'glm::slerp(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)'],['../a00847.html#ga8514da9c52cfee86f716cc0933ce118a',1,'glm::slerp(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a, S k)'],['../a00966.html#ga8b11b18ce824174ea1a5a69ea14e2cee',1,'glm::slerp(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y, T const &amp;a)']]],
  ['smoothstep_3013',['smoothstep',['../a00803.html#ga562edf7eca082cc5b7a0aaf180436daf',1,'glm']]],
  ['sorteigenvalues_3014',['sortEigenvalues',['../a00958.html#ga86e8b8630d5731bb385a979038c62258',1,'glm::sortEigenvalues(vec&lt; 2, T, Q &gt; &amp;eigenvalues, mat&lt; 2, 2, T, Q &gt; &amp;eigenvectors)'],['../a00958.html#gaafa0226e6e0d0d8c5cb47bc9466f8eac',1,'glm::sortEigenvalues(vec&lt; 3, T, Q &gt; &amp;eigenvalues, mat&lt; 3, 3, T, Q &gt; &amp;eigenvectors)'],['../a00958.html#gaf23e35406e7e192520bd670b20235d5b',1,'glm::sortEigenvalues(vec&lt; 4, T, Q &gt; &amp;eigenvalues, mat&lt; 4, 4, T, Q &gt; &amp;eigenvectors)']]],
  ['sphericalrand_3015',['sphericalRand',['../a00909.html#ga22f90fcaccdf001c516ca90f6428e138',1,'glm']]],
  ['sqrt_3016',['sqrt',['../a00804.html#gaa83e5f1648b7ccdf33b87c07c76cb77c',1,'glm::sqrt(vec&lt; L, T, Q &gt; const &amp;v)'],['../a00855.html#ga64b7b255ed7bcba616fe6b44470b022e',1,'glm::sqrt(qua&lt; T, Q &gt; const &amp;q)'],['../a00939.html#ga7ce36693a75879ccd9bb10167cfa722d',1,'glm::sqrt(int x)'],['../a00939.html#ga1975d318978d6dacf78b6444fa5ed7bc',1,'glm::sqrt(uint x)']]],
  ['squad_3017',['squad',['../a00962.html#ga0b9bf3459e132ad8a18fe970669e3e35',1,'glm']]],
  ['step_3018',['step',['../a00803.html#ga015a1261ff23e12650211aa872863cce',1,'glm::step(genType edge, genType x)'],['../a00803.html#ga8f9a911a48ef244b51654eaefc81c551',1,'glm::step(T edge, vec&lt; L, T, Q &gt; const &amp;x)'],['../a00803.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f',1,'glm::step(vec&lt; L, T, Q &gt; const &amp;edge, vec&lt; L, T, Q &gt; const &amp;x)']]]
];
