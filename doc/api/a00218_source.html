<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_int2x4_sized.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_int2x4_sized.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00218.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../mat2x4.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_int_sized.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_int2x4_sized extension included&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160; </div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;{</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="a00812.html#ga16f963e233d40cae586c0670605acf64">   31</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int8, defaultp&gt;                               <a class="code" href="a00812.html#ga16f963e233d40cae586c0670605acf64">i8mat2x4</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="a00812.html#ga928a7def71f463e661f197ba9620208b">   36</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int16, defaultp&gt;                              <a class="code" href="a00812.html#ga928a7def71f463e661f197ba9620208b">i16mat2x4</a>;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="a00812.html#gac91b19cd25751d92107963f2eb819477">   41</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int32, defaultp&gt;                              <a class="code" href="a00812.html#gac91b19cd25751d92107963f2eb819477">i32mat2x4</a>;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00812.html#ga2b589f8bcde400f09bd606476c715f28">   46</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int64, defaultp&gt;                              <a class="code" href="a00812.html#ga2b589f8bcde400f09bd606476c715f28">i64mat2x4</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;}<span class="comment">//namespace glm</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00812_html_ga928a7def71f463e661f197ba9620208b"><div class="ttname"><a href="a00812.html#ga928a7def71f463e661f197ba9620208b">glm::i16mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int16, defaultp &gt; i16mat2x4</div><div class="ttdoc">16 bit signed integer 2x4 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00218_source.html#l00036">matrix_int2x4_sized.hpp:36</a></div></div>
<div class="ttc" id="aa00812_html_ga16f963e233d40cae586c0670605acf64"><div class="ttname"><a href="a00812.html#ga16f963e233d40cae586c0670605acf64">glm::i8mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int8, defaultp &gt; i8mat2x4</div><div class="ttdoc">8 bit signed integer 2x4 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00218_source.html#l00031">matrix_int2x4_sized.hpp:31</a></div></div>
<div class="ttc" id="aa00812_html_gac91b19cd25751d92107963f2eb819477"><div class="ttname"><a href="a00812.html#gac91b19cd25751d92107963f2eb819477">glm::i32mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int32, defaultp &gt; i32mat2x4</div><div class="ttdoc">32 bit signed integer 2x4 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00218_source.html#l00041">matrix_int2x4_sized.hpp:41</a></div></div>
<div class="ttc" id="aa00812_html_ga2b589f8bcde400f09bd606476c715f28"><div class="ttname"><a href="a00812.html#ga2b589f8bcde400f09bd606476c715f28">glm::i64mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int64, defaultp &gt; i64mat2x4</div><div class="ttdoc">64 bit signed integer 2x4 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00218_source.html#l00046">matrix_int2x4_sized.hpp:46</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
