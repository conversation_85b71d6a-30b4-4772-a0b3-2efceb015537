<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: bit.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">bit.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00587.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../gtc/bitfield.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_bit is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_bit extension included&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;{</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="a00918.html#ga0dcc8fe7c3d3ad60dea409281efa3d05">   31</a></span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00918.html#ga898ef075ccf809a1e480faab48fe96bf">highestBitValue</a>(genIUType Value);</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="a00918.html#ga2ff6568089f3a9b67f5c30918855fc6f">   35</a></span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00918.html#ga2ff6568089f3a9b67f5c30918855fc6f">lowestBitValue</a>(genIUType Value);</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="a00918.html#ga898ef075ccf809a1e480faab48fe96bf">   41</a></span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00918.html#ga898ef075ccf809a1e480faab48fe96bf">highestBitValue</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="a00918.html#ga8cda2459871f574a0aecbe702ac93291">   49</a></span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL genIUType <a class="code" href="a00918.html#ga2bbded187c5febfefc1e524ba31b3fab">powerOfTwoAbove</a>(genIUType Value);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="a00918.html#ga2bbded187c5febfefc1e524ba31b3fab">   57</a></span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00918.html#ga2bbded187c5febfefc1e524ba31b3fab">powerOfTwoAbove</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160; </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="a00918.html#ga3de7df63c589325101a2817a56f8e29d">   65</a></span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL genIUType <a class="code" href="a00918.html#gaf78ddcc4152c051b2a21e68fecb10980">powerOfTwoBelow</a>(genIUType Value);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="a00918.html#gaf78ddcc4152c051b2a21e68fecb10980">   73</a></span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00918.html#gaf78ddcc4152c051b2a21e68fecb10980">powerOfTwoBelow</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160; </div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="a00918.html#ga5f65973a5d2ea38c719e6a663149ead9">   81</a></span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL genIUType <a class="code" href="a00918.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">powerOfTwoNearest</a>(genIUType Value);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="a00918.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">   89</a></span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00918.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">powerOfTwoNearest</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160; </div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="preprocessor">#include &quot;bit.inl&quot;</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00918_html_gac87e65d11e16c3d6b91c3bcfaef7da0b"><div class="ttname"><a href="a00918.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">glm::powerOfTwoNearest</a></div><div class="ttdeci">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt; powerOfTwoNearest(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Return the power of two number which value is the closet to the input value.</div></div>
<div class="ttc" id="aa00918_html_ga898ef075ccf809a1e480faab48fe96bf"><div class="ttname"><a href="a00918.html#ga898ef075ccf809a1e480faab48fe96bf">glm::highestBitValue</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; highestBitValue(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Find the highest bit set to 1 in a integer variable and return its value.</div></div>
<div class="ttc" id="aa00918_html_ga2ff6568089f3a9b67f5c30918855fc6f"><div class="ttname"><a href="a00918.html#ga2ff6568089f3a9b67f5c30918855fc6f">glm::lowestBitValue</a></div><div class="ttdeci">GLM_FUNC_DECL genIUType lowestBitValue(genIUType Value)</div></div>
<div class="ttc" id="aa00918_html_ga2bbded187c5febfefc1e524ba31b3fab"><div class="ttname"><a href="a00918.html#ga2bbded187c5febfefc1e524ba31b3fab">glm::powerOfTwoAbove</a></div><div class="ttdeci">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt; powerOfTwoAbove(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Return the power of two number which value is just higher the input value.</div></div>
<div class="ttc" id="aa00918_html_gaf78ddcc4152c051b2a21e68fecb10980"><div class="ttname"><a href="a00918.html#gaf78ddcc4152c051b2a21e68fecb10980">glm::powerOfTwoBelow</a></div><div class="ttdeci">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt; powerOfTwoBelow(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Return the power of two number which value is just lower the input value.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
