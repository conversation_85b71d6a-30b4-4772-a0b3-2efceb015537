<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_GTX_quaternion</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_quaternion<div class="ingroups"><a class="el" href="a00896.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gacbbbf93d24828d6bd9ba48d43abc985e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacbbbf93d24828d6bd9ba48d43abc985e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#gacbbbf93d24828d6bd9ba48d43abc985e">cross</a> (qua&lt; T, Q &gt; const &amp;q, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gacbbbf93d24828d6bd9ba48d43abc985e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a cross product between a quaternion and a vector.  <a href="a00962.html#gacbbbf93d24828d6bd9ba48d43abc985e">More...</a><br /></td></tr>
<tr class="separator:gacbbbf93d24828d6bd9ba48d43abc985e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7877a1ec00e43bbfccf6dd11894c0536"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7877a1ec00e43bbfccf6dd11894c0536"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga7877a1ec00e43bbfccf6dd11894c0536">cross</a> (vec&lt; 3, T, Q &gt; const &amp;v, qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga7877a1ec00e43bbfccf6dd11894c0536"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a cross product between a vector and a quaternion.  <a href="a00962.html#ga7877a1ec00e43bbfccf6dd11894c0536">More...</a><br /></td></tr>
<tr class="separator:ga7877a1ec00e43bbfccf6dd11894c0536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga321953c1b2e7befe6f5dcfddbfc6b76b">extractRealComponent</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract the real component of a quaternion.  <a href="a00962.html#ga321953c1b2e7befe6f5dcfddbfc6b76b">More...</a><br /></td></tr>
<tr class="separator:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga264e10708d58dd0ff53b7902a2bd2561"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga264e10708d58dd0ff53b7902a2bd2561"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga264e10708d58dd0ff53b7902a2bd2561">fastMix</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T const &amp;a)</td></tr>
<tr class="memdesc:ga264e10708d58dd0ff53b7902a2bd2561"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion normalized linear interpolation.  <a href="a00962.html#ga264e10708d58dd0ff53b7902a2bd2561">More...</a><br /></td></tr>
<tr class="separator:ga264e10708d58dd0ff53b7902a2bd2561"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacc5cd5f3e78de61d141c2355417424de"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacc5cd5f3e78de61d141c2355417424de"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#gacc5cd5f3e78de61d141c2355417424de">intermediate</a> (qua&lt; T, Q &gt; const &amp;prev, qua&lt; T, Q &gt; const &amp;curr, qua&lt; T, Q &gt; const &amp;next)</td></tr>
<tr class="memdesc:gacc5cd5f3e78de61d141c2355417424de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an intermediate control point for squad interpolation.  <a href="a00962.html#gacc5cd5f3e78de61d141c2355417424de">More...</a><br /></td></tr>
<tr class="separator:gacc5cd5f3e78de61d141c2355417424de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08eb643306c5ba9211a81b322bc89543"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga08eb643306c5ba9211a81b322bc89543"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga08eb643306c5ba9211a81b322bc89543">length2</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga08eb643306c5ba9211a81b322bc89543"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared length of x.  <a href="a00962.html#ga08eb643306c5ba9211a81b322bc89543">More...</a><br /></td></tr>
<tr class="separator:ga08eb643306c5ba9211a81b322bc89543"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77d9e2c313b98a1002a1b12408bf5b45"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga77d9e2c313b98a1002a1b12408bf5b45"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga77d9e2c313b98a1002a1b12408bf5b45">quat_identity</a> ()</td></tr>
<tr class="memdesc:ga77d9e2c313b98a1002a1b12408bf5b45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create an identity quaternion.  <a href="a00962.html#ga77d9e2c313b98a1002a1b12408bf5b45">More...</a><br /></td></tr>
<tr class="separator:ga77d9e2c313b98a1002a1b12408bf5b45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07da6ef58646442efe93b0c273d73776"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga07da6ef58646442efe93b0c273d73776"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga07da6ef58646442efe93b0c273d73776">rotate</a> (qua&lt; T, Q &gt; const &amp;q, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga07da6ef58646442efe93b0c273d73776"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns quarternion square root.  <a href="a00962.html#ga07da6ef58646442efe93b0c273d73776">More...</a><br /></td></tr>
<tr class="separator:ga07da6ef58646442efe93b0c273d73776"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">rotate</a> (qua&lt; T, Q &gt; const &amp;q, vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates a 4 components vector by a quaternion.  <a href="a00962.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">More...</a><br /></td></tr>
<tr class="separator:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga03e61282831cc3f52cc76f72f52ad2c5">rotation</a> (vec&lt; 3, T, Q &gt; const &amp;orig, vec&lt; 3, T, Q &gt; const &amp;dest)</td></tr>
<tr class="memdesc:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the rotation between two vectors.  <a href="a00962.html#ga03e61282831cc3f52cc76f72f52ad2c5">More...</a><br /></td></tr>
<tr class="separator:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc576cc957adc2a568cdcbc3799175bc"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gadc576cc957adc2a568cdcbc3799175bc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#gadc576cc957adc2a568cdcbc3799175bc">shortMix</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T const &amp;a)</td></tr>
<tr class="memdesc:gadc576cc957adc2a568cdcbc3799175bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion interpolation using the rotation short path.  <a href="a00962.html#gadc576cc957adc2a568cdcbc3799175bc">More...</a><br /></td></tr>
<tr class="separator:gadc576cc957adc2a568cdcbc3799175bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga0b9bf3459e132ad8a18fe970669e3e35">squad</a> (qua&lt; T, Q &gt; const &amp;q1, qua&lt; T, Q &gt; const &amp;q2, qua&lt; T, Q &gt; const &amp;s1, qua&lt; T, Q &gt; const &amp;s2, T const &amp;h)</td></tr>
<tr class="memdesc:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a point on a path according squad equation.  <a href="a00962.html#ga0b9bf3459e132ad8a18fe970669e3e35">More...</a><br /></td></tr>
<tr class="separator:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac228e3fcfa813841804362fcae02b337"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac228e3fcfa813841804362fcae02b337"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#gac228e3fcfa813841804362fcae02b337">toMat3</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gac228e3fcfa813841804362fcae02b337"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 3 * 3 matrix.  <a href="a00962.html#gac228e3fcfa813841804362fcae02b337">More...</a><br /></td></tr>
<tr class="separator:gac228e3fcfa813841804362fcae02b337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0d0c14d7d3c852b41d6a6e4d1da6606"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad0d0c14d7d3c852b41d6a6e4d1da6606"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#gad0d0c14d7d3c852b41d6a6e4d1da6606">toMat4</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gad0d0c14d7d3c852b41d6a6e4d1da6606"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 4 * 4 matrix.  <a href="a00962.html#gad0d0c14d7d3c852b41d6a6e4d1da6606">More...</a><br /></td></tr>
<tr class="separator:gad0d0c14d7d3c852b41d6a6e4d1da6606"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b2be33d948db631c8815e9f2953a451"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7b2be33d948db631c8815e9f2953a451"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga7b2be33d948db631c8815e9f2953a451">toQuat</a> (mat&lt; 3, 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga7b2be33d948db631c8815e9f2953a451"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a 3 * 3 matrix to a quaternion.  <a href="a00962.html#ga7b2be33d948db631c8815e9f2953a451">More...</a><br /></td></tr>
<tr class="separator:ga7b2be33d948db631c8815e9f2953a451"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4cf12d456770d716b590fd498bce6136"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4cf12d456770d716b590fd498bce6136"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00962.html#ga4cf12d456770d716b590fd498bce6136">toQuat</a> (mat&lt; 4, 4, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga4cf12d456770d716b590fd498bce6136"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a 4 * 4 matrix to a quaternion.  <a href="a00962.html#ga4cf12d456770d716b590fd498bce6136">More...</a><br /></td></tr>
<tr class="separator:ga4cf12d456770d716b590fd498bce6136"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a01591.html" title="GLM_GTX_quaternion">glm/gtx/quaternion.hpp</a>&gt; to use the features of this extension.</p>
<p>Extended quaternion types and functions </p>
<h2 class="groupheader">Function Documentation</h2>
<a id="gacbbbf93d24828d6bd9ba48d43abc985e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacbbbf93d24828d6bd9ba48d43abc985e">&#9670;&nbsp;</a></span>cross() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; glm::cross </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a cross product between a quaternion and a vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga7877a1ec00e43bbfccf6dd11894c0536"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7877a1ec00e43bbfccf6dd11894c0536">&#9670;&nbsp;</a></span>cross() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; glm::cross </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a cross product between a vector and a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga321953c1b2e7befe6f5dcfddbfc6b76b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga321953c1b2e7befe6f5dcfddbfc6b76b">&#9670;&nbsp;</a></span>extractRealComponent()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::extractRealComponent </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extract the real component of a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga264e10708d58dd0ff53b7902a2bd2561"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga264e10708d58dd0ff53b7902a2bd2561">&#9670;&nbsp;</a></span>fastMix()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::fastMix </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Quaternion normalized linear interpolation. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="gacc5cd5f3e78de61d141c2355417424de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacc5cd5f3e78de61d141c2355417424de">&#9670;&nbsp;</a></span>intermediate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::intermediate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>prev</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>curr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>next</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an intermediate control point for squad interpolation. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga08eb643306c5ba9211a81b322bc89543"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga08eb643306c5ba9211a81b322bc89543">&#9670;&nbsp;</a></span>length2()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR T glm::length2 </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the squared length of x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga77d9e2c313b98a1002a1b12408bf5b45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga77d9e2c313b98a1002a1b12408bf5b45">&#9670;&nbsp;</a></span>quat_identity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR qua&lt;T, Q&gt; glm::quat_identity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Create an identity quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga07da6ef58646442efe93b0c273d73776"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga07da6ef58646442efe93b0c273d73776">&#9670;&nbsp;</a></span>rotate() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns quarternion square root. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> Rotates a 3 components vector by a quaternion.</dd>
<dd>
<a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="gafcb78dfff45fbf19a7fcb2bd03fbf196"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafcb78dfff45fbf19a7fcb2bd03fbf196">&#9670;&nbsp;</a></span>rotate() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotates a 4 components vector by a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga03e61282831cc3f52cc76f72f52ad2c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga03e61282831cc3f52cc76f72f52ad2c5">&#9670;&nbsp;</a></span>rotation()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::rotation </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>orig</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>dest</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the rotation between two vectors. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">orig</td><td>vector, needs to be normalized </td></tr>
    <tr><td class="paramname">dest</td><td>vector, needs to be normalized</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="gadc576cc957adc2a568cdcbc3799175bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadc576cc957adc2a568cdcbc3799175bc">&#9670;&nbsp;</a></span>shortMix()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::shortMix </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Quaternion interpolation using the rotation short path. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="ga0b9bf3459e132ad8a18fe970669e3e35"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0b9bf3459e132ad8a18fe970669e3e35">&#9670;&nbsp;</a></span>squad()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::squad </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>s1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>s2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>h</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a point on a path according squad equation. </p>
<p>q1 and q2 are control points; s1 and s2 are intermediate control points.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a id="gac228e3fcfa813841804362fcae02b337"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac228e3fcfa813841804362fcae02b337">&#9670;&nbsp;</a></span>toMat3()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; glm::toMat3 </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a quaternion to a 3 * 3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a01591_source.html#l00111">111</a> of file <a class="el" href="a01591_source.html">gtx/quaternion.hpp</a>.</p>

<p class="reference">References <a class="el" href="a00908.html#ga333ab70047fbe4132406100c292dbc89">glm::mat3_cast()</a>.</p>

</div>
</div>
<a id="gad0d0c14d7d3c852b41d6a6e4d1da6606"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad0d0c14d7d3c852b41d6a6e4d1da6606">&#9670;&nbsp;</a></span>toMat4()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;4, 4, T, Q&gt; glm::toMat4 </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a quaternion to a 4 * 4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a01591_source.html#l00118">118</a> of file <a class="el" href="a01591_source.html">gtx/quaternion.hpp</a>.</p>

<p class="reference">References <a class="el" href="a00908.html#ga1113212d9bdefc2e31ad40e5bbb506f3">glm::mat4_cast()</a>.</p>

</div>
</div>
<a id="ga7b2be33d948db631c8815e9f2953a451"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7b2be33d948db631c8815e9f2953a451">&#9670;&nbsp;</a></span>toQuat() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER qua&lt;T, Q&gt; glm::toQuat </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a 3 * 3 matrix to a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a01591_source.html#l00125">125</a> of file <a class="el" href="a01591_source.html">gtx/quaternion.hpp</a>.</p>

<p class="reference">References <a class="el" href="a00908.html#ga1108a4ab88ca87bac321454eea7702f8">glm::quat_cast()</a>.</p>

</div>
</div>
<a id="ga4cf12d456770d716b590fd498bce6136"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4cf12d456770d716b590fd498bce6136">&#9670;&nbsp;</a></span>toQuat() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER qua&lt;T, Q&gt; glm::toQuat </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a 4 * 4 matrix to a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00962.html">GLM_GTX_quaternion</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a01591_source.html#l00132">132</a> of file <a class="el" href="a01591_source.html">gtx/quaternion.hpp</a>.</p>

<p class="reference">References <a class="el" href="a00908.html#ga1108a4ab88ca87bac321454eea7702f8">glm::quat_cast()</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
