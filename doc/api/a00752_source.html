<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: vector_query.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">vector_query.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00752.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &lt;cfloat&gt;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_vector_query is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_vector_query extension included&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="a00979.html#ga13da4a787a2ff70e95d561fb19ff91b4">   34</a></span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> <a class="code" href="a00979.html#ga13da4a787a2ff70e95d561fb19ff91b4">areCollinear</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v0, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v1, T <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="a00979.html#gac7b95b3f798e3c293262b2bdaad47c57">   39</a></span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> <a class="code" href="a00979.html#gac7b95b3f798e3c293262b2bdaad47c57">areOrthogonal</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v0, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v1, T <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="a00979.html#gac3c974f459fd75453134fad7ae89a39e">   44</a></span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> <a class="code" href="a00979.html#gac3c974f459fd75453134fad7ae89a39e">isNormalized</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, T <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160; </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="a00979.html#gab4a3637dbcb4bb42dc55caea7a1e0495">   49</a></span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> <a class="code" href="a00979.html#gab4a3637dbcb4bb42dc55caea7a1e0495">isNull</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, T <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="a00979.html#gaf6ec1688eab7442fe96fe4941d5d4e76">   54</a></span>&#160;        GLM_FUNC_DECL vec&lt;L, bool, Q&gt; <a class="code" href="a00979.html#gaf6ec1688eab7442fe96fe4941d5d4e76">isCompNull</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, T <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160; </div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="a00979.html#ga1b091c3d7f9ee3b0708311c001c293e3">   59</a></span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> <a class="code" href="a00979.html#ga1b091c3d7f9ee3b0708311c001c293e3">areOrthonormal</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v0, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v1, T <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;}<span class="comment">// namespace glm</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160; </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="preprocessor">#include &quot;vector_query.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00979_html_ga13da4a787a2ff70e95d561fb19ff91b4"><div class="ttname"><a href="a00979.html#ga13da4a787a2ff70e95d561fb19ff91b4">glm::areCollinear</a></div><div class="ttdeci">GLM_FUNC_DECL bool areCollinear(vec&lt; L, T, Q &gt; const &amp;v0, vec&lt; L, T, Q &gt; const &amp;v1, T const &amp;epsilon)</div><div class="ttdoc">Check whether two vectors are collinears.</div></div>
<div class="ttc" id="aa00979_html_gac3c974f459fd75453134fad7ae89a39e"><div class="ttname"><a href="a00979.html#gac3c974f459fd75453134fad7ae89a39e">glm::isNormalized</a></div><div class="ttdeci">GLM_FUNC_DECL bool isNormalized(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)</div><div class="ttdoc">Check whether a vector is normalized.</div></div>
<div class="ttc" id="aa00979_html_gaf6ec1688eab7442fe96fe4941d5d4e76"><div class="ttname"><a href="a00979.html#gaf6ec1688eab7442fe96fe4941d5d4e76">glm::isCompNull</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, bool, Q &gt; isCompNull(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)</div><div class="ttdoc">Check whether a each component of a vector is null.</div></div>
<div class="ttc" id="aa00858_html_ga2a1e57fc5592b69cfae84174cbfc9429"><div class="ttname"><a href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">glm::epsilon</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType epsilon()</div><div class="ttdoc">Return the epsilon constant for floating point types.</div></div>
<div class="ttc" id="aa00979_html_gab4a3637dbcb4bb42dc55caea7a1e0495"><div class="ttname"><a href="a00979.html#gab4a3637dbcb4bb42dc55caea7a1e0495">glm::isNull</a></div><div class="ttdeci">GLM_FUNC_DECL bool isNull(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)</div><div class="ttdoc">Check whether a vector is null.</div></div>
<div class="ttc" id="aa00979_html_gac7b95b3f798e3c293262b2bdaad47c57"><div class="ttname"><a href="a00979.html#gac7b95b3f798e3c293262b2bdaad47c57">glm::areOrthogonal</a></div><div class="ttdeci">GLM_FUNC_DECL bool areOrthogonal(vec&lt; L, T, Q &gt; const &amp;v0, vec&lt; L, T, Q &gt; const &amp;v1, T const &amp;epsilon)</div><div class="ttdoc">Check whether two vectors are orthogonals.</div></div>
<div class="ttc" id="aa00979_html_ga1b091c3d7f9ee3b0708311c001c293e3"><div class="ttname"><a href="a00979.html#ga1b091c3d7f9ee3b0708311c001c293e3">glm::areOrthonormal</a></div><div class="ttdeci">GLM_FUNC_DECL bool areOrthonormal(vec&lt; L, T, Q &gt; const &amp;v0, vec&lt; L, T, Q &gt; const &amp;v1, T const &amp;epsilon)</div><div class="ttdoc">Check whether two vectors are orthonormal.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
