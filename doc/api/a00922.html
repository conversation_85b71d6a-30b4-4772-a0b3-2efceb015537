<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_GTX_color_space_YCoCg</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_color_space_YCoCg<div class="ingroups"><a class="el" href="a00896.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00922.html#ga0606353ec2a9b9eaa84f1b02ec391bc5">rgb2YCoCg</a> (vec&lt; 3, T, Q &gt; const &amp;rgbColor)</td></tr>
<tr class="memdesc:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from RGB color space to YCoCg color space.  <a href="a00922.html#ga0606353ec2a9b9eaa84f1b02ec391bc5">More...</a><br /></td></tr>
<tr class="separator:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00922.html#ga0389772e44ca0fd2ba4a79bdd8efe898">rgb2YCoCgR</a> (vec&lt; 3, T, Q &gt; const &amp;rgbColor)</td></tr>
<tr class="memdesc:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from RGB color space to YCoCgR color space.  <a href="a00922.html#ga0389772e44ca0fd2ba4a79bdd8efe898">More...</a><br /></td></tr>
<tr class="separator:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga163596b804c7241810b2534a99eb1343"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga163596b804c7241810b2534a99eb1343"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00922.html#ga163596b804c7241810b2534a99eb1343">YCoCg2rgb</a> (vec&lt; 3, T, Q &gt; const &amp;YCoCgColor)</td></tr>
<tr class="memdesc:ga163596b804c7241810b2534a99eb1343"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from YCoCg color space to RGB color space.  <a href="a00922.html#ga163596b804c7241810b2534a99eb1343">More...</a><br /></td></tr>
<tr class="separator:ga163596b804c7241810b2534a99eb1343"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf8d30574c8576838097d8e20c295384a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf8d30574c8576838097d8e20c295384a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00922.html#gaf8d30574c8576838097d8e20c295384a">YCoCgR2rgb</a> (vec&lt; 3, T, Q &gt; const &amp;YCoCgColor)</td></tr>
<tr class="memdesc:gaf8d30574c8576838097d8e20c295384a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from YCoCgR color space to RGB color space.  <a href="a00922.html#gaf8d30574c8576838097d8e20c295384a">More...</a><br /></td></tr>
<tr class="separator:gaf8d30574c8576838097d8e20c295384a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00596.html" title="GLM_GTX_color_space_YCoCg">glm/gtx/color_space_YCoCg.hpp</a>&gt; to use the features of this extension.</p>
<p>RGB to YCoCg conversions and operations </p>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga0606353ec2a9b9eaa84f1b02ec391bc5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0606353ec2a9b9eaa84f1b02ec391bc5">&#9670;&nbsp;</a></span>rgb2YCoCg()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rgb2YCoCg </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgbColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from RGB color space to YCoCg color space. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00922.html">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
<a id="ga0389772e44ca0fd2ba4a79bdd8efe898"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0389772e44ca0fd2ba4a79bdd8efe898">&#9670;&nbsp;</a></span>rgb2YCoCgR()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rgb2YCoCgR </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgbColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from RGB color space to YCoCgR color space. </p>
<dl class="section see"><dt>See also</dt><dd>"YCoCg-R: A Color Space with RGB Reversibility and Low Dynamic Range" </dd>
<dd>
<a class="el" href="a00922.html">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
<a id="ga163596b804c7241810b2534a99eb1343"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga163596b804c7241810b2534a99eb1343">&#9670;&nbsp;</a></span>YCoCg2rgb()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::YCoCg2rgb </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>YCoCgColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from YCoCg color space to RGB color space. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00922.html">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
<a id="gaf8d30574c8576838097d8e20c295384a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf8d30574c8576838097d8e20c295384a">&#9670;&nbsp;</a></span>YCoCgR2rgb()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::YCoCgR2rgb </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>YCoCgColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from YCoCgR color space to RGB color space. </p>
<dl class="section see"><dt>See also</dt><dd>"YCoCg-R: A Color Space with RGB Reversibility and Low Dynamic Range" </dd>
<dd>
<a class="el" href="a00922.html">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
