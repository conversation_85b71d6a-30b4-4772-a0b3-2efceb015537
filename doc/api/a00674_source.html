<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_transform_2d.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_transform_2d.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00674.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../vec2.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_matrix_transform_2d is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_matrix_transform_2d extension included&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="a00950.html#gaf4573ae47c80938aa9053ef6a33755ab">   36</a></span>&#160;        GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; <a class="code" href="a00950.html#gaf4573ae47c80938aa9053ef6a33755ab">translate</a>(</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="a00950.html#gad5c84a4932a758f385a87098ce1b1660">   45</a></span>&#160;        GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; <a class="code" href="a00950.html#gad5c84a4932a758f385a87098ce1b1660">rotate</a>(</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                T <a class="code" href="a00856.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="a00950.html#gadb47d2ad2bd984b213e8ff7d9cd8154e">   54</a></span>&#160;        GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; <a class="code" href="a00950.html#gadb47d2ad2bd984b213e8ff7d9cd8154e">scale</a>(</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="a00950.html#ga2a118ece5db1e2022112b954846012af">   63</a></span>&#160;        GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; <a class="code" href="a00950.html#ga2a118ece5db1e2022112b954846012af">shearX</a>(</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                T y);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="a00950.html#ga717f1833369c1ac4a40e4ac015af885e">   72</a></span>&#160;        GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; <a class="code" href="a00950.html#ga717f1833369c1ac4a40e4ac015af885e">shearY</a>(</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                T x);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#include &quot;matrix_transform_2d.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00856_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00856.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle.</div></div>
<div class="ttc" id="aa00950_html_ga2a118ece5db1e2022112b954846012af"><div class="ttname"><a href="a00950.html#ga2a118ece5db1e2022112b954846012af">glm::shearX</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt; shearX(mat&lt; 3, 3, T, Q &gt; const &amp;m, T y)</div><div class="ttdoc">Builds an horizontal (parallel to the x axis) shear 3 * 3 matrix.</div></div>
<div class="ttc" id="aa00950_html_gaf4573ae47c80938aa9053ef6a33755ab"><div class="ttname"><a href="a00950.html#gaf4573ae47c80938aa9053ef6a33755ab">glm::translate</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt; translate(mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a translation 3 * 3 matrix created from a vector of 2 components.</div></div>
<div class="ttc" id="aa00950_html_gadb47d2ad2bd984b213e8ff7d9cd8154e"><div class="ttname"><a href="a00950.html#gadb47d2ad2bd984b213e8ff7d9cd8154e">glm::scale</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt; scale(mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a scale 3 * 3 matrix created from a vector of 2 components.</div></div>
<div class="ttc" id="aa00950_html_gad5c84a4932a758f385a87098ce1b1660"><div class="ttname"><a href="a00950.html#gad5c84a4932a758f385a87098ce1b1660">glm::rotate</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt; rotate(mat&lt; 3, 3, T, Q &gt; const &amp;m, T angle)</div><div class="ttdoc">Builds a rotation 3 * 3 matrix created from an angle.</div></div>
<div class="ttc" id="aa00950_html_ga717f1833369c1ac4a40e4ac015af885e"><div class="ttname"><a href="a00950.html#ga717f1833369c1ac4a40e4ac015af885e">glm::shearY</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt; shearY(mat&lt; 3, 3, T, Q &gt; const &amp;m, T x)</div><div class="ttdoc">Builds a vertical (parallel to the y axis) shear 3 * 3 matrix.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
