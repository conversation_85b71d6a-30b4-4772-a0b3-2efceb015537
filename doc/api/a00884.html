<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_vector_uint2_sized</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_vector_uint2_sized<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga2a78447eb9d66a114b193f4a25899c16"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, <a class="el" href="a00864.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00884.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a></td></tr>
<tr class="memdesc:ga2a78447eb9d66a114b193f4a25899c16"><td class="mdescLeft">&#160;</td><td class="mdescRight">16 bit unsigned integer vector of 2 components type.  <a href="a00884.html#ga2a78447eb9d66a114b193f4a25899c16">More...</a><br /></td></tr>
<tr class="separator:ga2a78447eb9d66a114b193f4a25899c16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a266e46ee218d0c680f12b35c500cc0"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, <a class="el" href="a00864.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00884.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a></td></tr>
<tr class="memdesc:ga2a266e46ee218d0c680f12b35c500cc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit unsigned integer vector of 2 components type.  <a href="a00884.html#ga2a266e46ee218d0c680f12b35c500cc0">More...</a><br /></td></tr>
<tr class="separator:ga2a266e46ee218d0c680f12b35c500cc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef3824ed4fe435a019c5b9dddf53fec5"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, <a class="el" href="a00864.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00884.html#gaef3824ed4fe435a019c5b9dddf53fec5">u64vec2</a></td></tr>
<tr class="memdesc:gaef3824ed4fe435a019c5b9dddf53fec5"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit unsigned integer vector of 2 components type.  <a href="a00884.html#gaef3824ed4fe435a019c5b9dddf53fec5">More...</a><br /></td></tr>
<tr class="separator:gaef3824ed4fe435a019c5b9dddf53fec5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga518b8d948a6b4ddb72f84d5c3b7b6611"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, <a class="el" href="a00864.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00884.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a></td></tr>
<tr class="memdesc:ga518b8d948a6b4ddb72f84d5c3b7b6611"><td class="mdescLeft">&#160;</td><td class="mdescRight">8 bit unsigned integer vector of 2 components type.  <a href="a00884.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">More...</a><br /></td></tr>
<tr class="separator:ga518b8d948a6b4ddb72f84d5c3b7b6611"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes sized unsigned integer vector of 2 components type.</p>
<p>Include &lt;<a class="el" href="a00500.html" title="GLM_EXT_vector_uint2_sized">glm/ext/vector_uint2_sized.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00864.html">GLM_EXT_scalar_uint_sized</a> </dd>
<dd>
<a class="el" href="a00875.html">GLM_EXT_vector_int2_sized</a> </dd></dl>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga2a78447eb9d66a114b193f4a25899c16"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2a78447eb9d66a114b193f4a25899c16">&#9670;&nbsp;</a></span>u16vec2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, <a class="el" href="a00913.html#gaa2d7acc0adb536fab71fe261232a40ff">u16</a>, defaultp &gt; <a class="el" href="a00884.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>16 bit unsigned integer vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00884.html">GLM_EXT_vector_uint2_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00500_source.html#l00036">36</a> of file <a class="el" href="a00500_source.html">vector_uint2_sized.hpp</a>.</p>

</div>
</div>
<a id="ga2a266e46ee218d0c680f12b35c500cc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2a266e46ee218d0c680f12b35c500cc0">&#9670;&nbsp;</a></span>u32vec2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, <a class="el" href="a00913.html#ga8165913e068444f7842302d40ba897b9">u32</a>, defaultp &gt; <a class="el" href="a00884.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>32 bit unsigned integer vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00884.html">GLM_EXT_vector_uint2_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00500_source.html#l00041">41</a> of file <a class="el" href="a00500_source.html">vector_uint2_sized.hpp</a>.</p>

</div>
</div>
<a id="gaef3824ed4fe435a019c5b9dddf53fec5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaef3824ed4fe435a019c5b9dddf53fec5">&#9670;&nbsp;</a></span>u64vec2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, <a class="el" href="a00913.html#gaf3f312156984c365e9f65620354da70b">u64</a>, defaultp &gt; <a class="el" href="a00884.html#gaef3824ed4fe435a019c5b9dddf53fec5">u64vec2</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>64 bit unsigned integer vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00884.html">GLM_EXT_vector_uint2_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00500_source.html#l00046">46</a> of file <a class="el" href="a00500_source.html">vector_uint2_sized.hpp</a>.</p>

</div>
</div>
<a id="ga518b8d948a6b4ddb72f84d5c3b7b6611"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga518b8d948a6b4ddb72f84d5c3b7b6611">&#9670;&nbsp;</a></span>u8vec2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, <a class="el" href="a00913.html#gaecc7082561fc9028b844b6cf3d305d36">u8</a>, defaultp &gt; <a class="el" href="a00884.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>8 bit unsigned integer vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00884.html">GLM_EXT_vector_uint2_sized</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00500_source.html#l00031">31</a> of file <a class="el" href="a00500_source.html">vector_uint2_sized.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
