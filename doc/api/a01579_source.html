<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_transform.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">ext/matrix_transform.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a01579.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../geometric.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../trigonometric.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../matrix.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_transform extension included&quot;)</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;{</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="a00828.html#ga81696f2b8d1db02ea1aff8da8f269314">   39</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00828.html#ga81696f2b8d1db02ea1aff8da8f269314">identity</a>();</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">   64</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">translate</a>(</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="a00828.html#gaee9e865eaa9776370996da2940873fd4">   80</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#gaee9e865eaa9776370996da2940873fd4">rotate</a>(</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m, T <a class="code" href="a00856.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00856.html#ga764254f10248b505e936e5309a88c23d">axis</a>);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="a00828.html#ga05051adbee603fb3c5095d8cf5cc229b">   95</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#ga05051adbee603fb3c5095d8cf5cc229b">scale</a>(</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="a00828.html#ga391e0142852ab4139dcea0d9b1bbc048">  122</a></span>&#160;    GLM_FUNC_QUALIFIER mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#ga391e0142852ab4139dcea0d9b1bbc048">shear</a>(</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span> &amp;m, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; p, vec&lt;2, T, Q&gt; <span class="keyword">const</span> &amp;l_x, vec&lt;2, T, Q&gt; <span class="keyword">const</span> &amp;l_y, vec&lt;2, T, Q&gt; <span class="keyword">const</span> &amp;l_z);</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="a00828.html#gacfa12c8889c754846bc20c65d9b5c701">  136</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#gacfa12c8889c754846bc20c65d9b5c701">lookAtRH</a>(</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; eye, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160; </div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="a00828.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">  150</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">lookAtLH</a>(</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; eye, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="a00828.html#gaa64aa951a0e99136bba9008d2b59c78e">  165</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00828.html#gaa64aa951a0e99136bba9008d2b59c78e">lookAt</a>(</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; eye, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160; </div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160; </div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;<span class="preprocessor">#include &quot;matrix_transform.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00856_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00856.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle.</div></div>
<div class="ttc" id="aa00828_html_ga81696f2b8d1db02ea1aff8da8f269314"><div class="ttname"><a href="a00828.html#ga81696f2b8d1db02ea1aff8da8f269314">glm::identity</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType identity()</div><div class="ttdoc">Builds an identity matrix.</div></div>
<div class="ttc" id="aa00828_html_gaee9e865eaa9776370996da2940873fd4"><div class="ttname"><a href="a00828.html#gaee9e865eaa9776370996da2940873fd4">glm::rotate</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; rotate(mat&lt; 4, 4, T, Q &gt; const &amp;m, T angle, vec&lt; 3, T, Q &gt; const &amp;axis)</div><div class="ttdoc">Builds a rotation 4 * 4 matrix created from an axis vector and an angle.</div></div>
<div class="ttc" id="aa00828_html_gaa64aa951a0e99136bba9008d2b59c78e"><div class="ttname"><a href="a00828.html#gaa64aa951a0e99136bba9008d2b59c78e">glm::lookAt</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; lookAt(vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a look at view matrix based on the default handedness.</div></div>
<div class="ttc" id="aa00828_html_ga05051adbee603fb3c5095d8cf5cc229b"><div class="ttname"><a href="a00828.html#ga05051adbee603fb3c5095d8cf5cc229b">glm::scale</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; scale(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a scale 4 * 4 matrix created from 3 scalars.</div></div>
<div class="ttc" id="aa00828_html_gac6b494bda2f47615b2fd3e70f3d2c912"><div class="ttname"><a href="a00828.html#gac6b494bda2f47615b2fd3e70f3d2c912">glm::translate</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR mat&lt; 4, 4, T, Q &gt; translate(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a translation 4 * 4 matrix created from a vector of 3 components.</div></div>
<div class="ttc" id="aa00856_html_ga764254f10248b505e936e5309a88c23d"><div class="ttname"><a href="a00856.html#ga764254f10248b505e936e5309a88c23d">glm::axis</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; axis(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the q rotation axis.</div></div>
<div class="ttc" id="aa00828_html_gab2c09e25b0a16d3a9d89cc85bbae41b0"><div class="ttname"><a href="a00828.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">glm::lookAtLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; lookAtLH(vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a left handed look at view matrix.</div></div>
<div class="ttc" id="aa00828_html_gacfa12c8889c754846bc20c65d9b5c701"><div class="ttname"><a href="a00828.html#gacfa12c8889c754846bc20c65d9b5c701">glm::lookAtRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; lookAtRH(vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a right handed look at view matrix.</div></div>
<div class="ttc" id="aa00828_html_ga391e0142852ab4139dcea0d9b1bbc048"><div class="ttname"><a href="a00828.html#ga391e0142852ab4139dcea0d9b1bbc048">glm::shear</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER mat&lt; 4, 4, T, Q &gt; shear(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;p, vec&lt; 2, T, Q &gt; const &amp;l_x, vec&lt; 2, T, Q &gt; const &amp;l_y, vec&lt; 2, T, Q &gt; const &amp;l_z)</div><div class="ttdoc">Builds a scale 4 * 4 matrix created from point referent 3 shearers.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
