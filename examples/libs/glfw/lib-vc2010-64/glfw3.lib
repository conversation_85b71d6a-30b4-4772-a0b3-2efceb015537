!<arch>
/               1459697978              0       17267     `
  �  �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷  芷 贺 贺 贺 贺 贺 渺 渺 渺 � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT lT 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 玴 � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> c> Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ Κ n n n n n n n n n n n n n n n n n n n /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /� /�??_C@_0BB@GHMBNOIP@wglCreateContext?$AA@ ??_C@_0BB@OGAIJNNO@wglDeleteContext?$AA@ ??_C@_0BC@FFKMKEGM@wglGetProcAddress?$AA@ ??_C@_0BD@IINOPBDD@wglSwapIntervalEXT?$AA@ ??_C@_0BE@BMOJHNDI@WGL_ARB_multisample?$AA@ ??_C@_0BF@NPDKPIKE@WGL_EXT_swap_control?$AA@ ??_C@_0BF@OGGDOHFN@WGL_ARB_pixel_format?$AA@ ??_C@_0BH@NCJDMLMG@WGL_ARB_create_context?$AA@ ??_C@_0BJ@CIMJOHKL@WGL_ARB_framebuffer_sRGB?$AA@ ??_C@_0BJ@ODLLHDDH@WGL_EXT_framebuffer_sRGB?$AA@ ??_C@_0BK@HGDBIG@wglGetExtensionsStringEXT?$AA@ ??_C@_0BK@JOBLJGNA@wglGetExtensionsStringARB?$AA@ ??_C@_0BL@PDDHMPGF@wglCreateContextAttribsARB?$AA@ ??_C@_0BN@GDKHKIAN@wglGetPixelFormatAttribivARB?$AA@ ??_C@_0BO@FABLLHKK@WGL_ARB_context_flush_control?$AA@ ??_C@_0BP@JAOBDJGN@WGL_ARB_create_context_profile?$AA@ ??_C@_0CB@FLGENKMO@WGL?3?5Failed?5to?5load?5opengl32?4dll@ ??_C@_0CC@BFGHOMNL@WGL_ARB_create_context_robustnes@ ??_C@_0CD@FOMFMBIL@WGL_EXT_create_context_es2_profi@ ??_C@_0CE@FEGPCKPN@WGL?3?5Failed?5to?5make?5context?5curr@ ??_C@_0CF@EKIADPFB@WGL?3?5Failed?5to?5clear?5current?5con@ ??_C@_0CF@GFMDJIA@WGL?3?5Failed?5to?5create?5OpenGL?5con@ ??_C@_0CG@GMPEPNKL@WGL?3?5Failed?5to?5retrieve?5DC?5for?5w@ ??_C@_0CJ@HAGOIHNO@WGL?3?5Failed?5to?5set?5selected?5pixe@ ??_C@_0CM@DGJKGOGD@WGL?3?5Failed?5to?5find?5a?5suitable?5p@ ??_C@_0DC@HFHALHIB@WGL?3?5The?5driver?5does?5not?5appear?5@ ??_C@_0DC@ICHAJFGA@WGL?3?5Failed?5to?5retrieve?5pixel?5fo@ ??_C@_0DG@EPNPJJLB@WGL?3?5Failed?5to?5retrieve?5PFD?5for?5@ ??_C@_0DM@FCAOPKO@WGL?3?5Failed?5to?5enable?5sharing?5wi@ ??_C@_0EP@EKHGPGGO@WGL?3?5OpenGL?5ES?5requested?5but?5WGL@ ??_C@_0FA@OHOPKHFK@WGL?3?5OpenGL?5profile?5requested?5bu@ ??_C@_0FN@NPOMMKHN@WGL?3?5A?5forward?5compatible?5OpenGL@ ??_C@_0N@DOKAJOHF@opengl32?4dll?$AA@ ??_C@_0O@GCGJMHDL@wglShareLists?$AA@ ??_C@_0P@BBBFNDGG@wglMakeCurrent?$AA@ _glfwAnalyzeContextWGL _glfwCreateContextWGL _glfwDestroyContextWGL _glfwInitWGL _glfwPlatformExtensionSupported _glfwPlatformGetProcAddress _glfwPlatformMakeContextCurrent _glfwPlatformSwapBuffers _glfwPlatformSwapInterval _glfwTerminateWGL glfwGetWGLContext ??_C@_0BF@COCMEDFO@VK_KHR_win32_surface?$AA@ ??_C@_0BI@INMBJMIJ@vkCreateWin32SurfaceKHR?$AA@ ??_C@_0BN@HJHDCKKP@Win32?3?5Failed?5to?5create?5icon?$AA@ ??_C@_0BP@BFHGHOIO@Win32?3?5Failed?5to?5create?5window?$AA@ ??_C@_0BP@OHFKOFFD@Win32?3?5Failed?5to?5create?5cursor?$AA@ ??_C@_0CA@FNCFBEIG@Win32?3?5Failed?5to?5open?5clipboard?$AA@ ??_C@_0CE@GCBEILKL@Win32?3?5Failed?5to?5create?5RGBA?5bit@ ??_C@_0CE@PKGOIGHB@Win32?3?5Failed?5to?5create?5mask?5bit@ ??_C@_0CH@GAPNPCHF@Win32?3?5Failed?5to?5register?5window@ ??_C@_0CI@LDEHPBDB@Win32?3?5Failed?5to?5create?5standard@ ??_C@_0CK@JIIJNNEM@Win32?3?5Failed?5to?5convert?5string?5@ ??_C@_0CL@LJNEEKEI@Win32?3?5Failed?5to?5create?5Vulkan?5s@ ??_C@_0CN@FGAGPCCP@Win32?3?5Failed?5to?5convert?5clipboa@ ??_C@_0CO@MCBPBAMK@Win32?3?5Failed?5to?5convert?5wide?5st@ ??_C@_0CP@FOBJNCLA@vkGetPhysicalDeviceWin32Presenta@ ??_C@_0DA@CDIFIIJI@Win32?3?5Failed?5to?5convert?5window?5@ ??_C@_0DG@CGDLBFFB@Win32?3?5Failed?5to?5allocate?5global@ ??_C@_0DO@PGEACBCJ@Win32?3?5Vulkan?5instance?5missing?5V@ ??_C@_0P@IENCOMCD@VK_KHR_surface?$AA@ ??_C@_19JBPKICJI@?$AAG?$AAL?$AAF?$AAW?$AA?$AA@ ??_C@_1BE@LDFMCOHA@?$AAG?$AAL?$AAF?$AAW?$AA_?$AAI?$AAC?$AAO?$AAN?$AA?$AA@ ??_C@_1O@IBNLCBOC@?$AAG?$AAL?$AAF?$AAW?$AA3?$AA0?$AA?$AA@ __mask@@NegDouble@ __real@405e000000000000 __real@408f400000000000 _glfwPlatformCreateCursor _glfwPlatformCreateStandardCursor _glfwPlatformCreateWindow _glfwPlatformCreateWindowSurface _glfwPlatformDestroyCursor _glfwPlatformDestroyWindow _glfwPlatformFocusWindow _glfwPlatformGetClipboardString _glfwPlatformGetCursorPos _glfwPlatformGetFramebufferSize _glfwPlatformGetKeyName _glfwPlatformGetPhysicalDevicePresentationSupport _glfwPlatformGetRequiredInstanceExtensions _glfwPlatformGetWindowFrameSize _glfwPlatformGetWindowPos _glfwPlatformGetWindowSize _glfwPlatformHideWindow _glfwPlatformIconifyWindow _glfwPlatformMaximizeWindow _glfwPlatformPollEvents _glfwPlatformPostEmptyEvent _glfwPlatformRestoreWindow _glfwPlatformSetClipboardString _glfwPlatformSetCursor _glfwPlatformSetCursorMode _glfwPlatformSetCursorPos _glfwPlatformSetWindowAspectRatio _glfwPlatformSetWindowIcon _glfwPlatformSetWindowMonitor _glfwPlatformSetWindowPos _glfwPlatformSetWindowSize _glfwPlatformSetWindowSizeLimits _glfwPlatformSetWindowTitle _glfwPlatformShowWindow _glfwPlatformWaitEvents _glfwPlatformWaitEventsTimeout _glfwPlatformWindowFocused _glfwPlatformWindowIconified _glfwPlatformWindowMaximized _glfwPlatformWindowVisible _glfwRegisterWindowClassWin32 _glfwUnregisterWindowClassWin32 glfwGetWin32Window ??_C@_0CE@MEIEKJIC@Win32?3?5Failed?5to?5allocate?5TLS?5in@ _glfwInitThreadLocalStorageWin32 _glfwPlatformGetCurrentContext _glfwPlatformSetCurrentContext _glfwTerminateThreadLocalStorageWin32 _glfwInitTimerWin32 _glfwPlatformGetTimerFrequency _glfwPlatformGetTimerValue ??_C@_0CA@CNJHNGPE@Win32?3?5Failed?5to?5set?5video?5mode?$AA@ ??_C@_0CD@EGEEPFHF@Win32?3?5Gamma?5ramp?5size?5must?5be?52@ ??_C@_0CJ@EFIFNOJC@Win32?3?5Failed?5to?5convert?5string?5@ ??_C@_1BA@PICGEGJB@?$AAD?$AAI?$AAS?$AAP?$AAL?$AAA?$AAY?$AA?$AA@ _glfwPlatformGetGammaRamp _glfwPlatformGetMonitorPos _glfwPlatformGetMonitors _glfwPlatformGetVideoMode _glfwPlatformGetVideoModes _glfwPlatformIsSameMonitor _glfwPlatformSetGammaRamp _glfwRestoreVideoModeWin32 _glfwSetVideoModeWin32 glfwGetWin32Adapter glfwGetWin32Monitor ??_C@_03HAFBONIK@IG_?$AA@ ??_C@_0BA@LCMKGGHA@XInput?5Drum?5Kit?$AA@ ??_C@_0BB@JDDMDGFO@XInput?5Dance?5Pad?$AA@ ??_C@_0BE@FABDLJKH@XInput?5Arcade?5Stick?$AA@ ??_C@_0BE@HBNNGLHP@XInput?5Flight?5Stick?$AA@ ??_C@_0BE@JNNBBIGB@Xbox?5360?5Controller?$AA@ ??_C@_0BG@DGJLFNBN@Unknown?5XInput?5Device?$AA@ ??_C@_0BM@NAGDGOBA@DI?3?5Failed?5to?5create?5device?$AA@ ??_C@_0BN@OIHKANJ@Wireless?5Xbox?5360?5Controller?$AA@ ??_C@_0BP@JPGGHMBM@DI?3?5Failed?5to?5create?5interface?$AA@ ??_C@_0CD@BFBAJDII@DI?3?5Failed?5to?5set?5device?5axis?5mo@ ??_C@_0CF@KNHHCLIO@DI?3?5Failed?5to?5set?5device?5data?5fo@ ??_C@_0CH@OEOICFHI@DI?3?5Failed?5to?5enumerate?5device?5o@ ??_C@_0CI@BJIPONFG@DI?3?5Failed?5to?5query?5device?5capab@ ??_C@_0CJ@HDJDHLNO@Failed?5to?5enumerate?5DirectInput8@ ??_C@_0N@JENFOGFI@XInput?5Wheel?$AA@ ??_C@_0O@IFDGNEHH@XInput?5Guitar?$AA@ GUID_Button GUID_POV GUID_RxAxis GUID_RyAxis GUID_RzAxis GUID_Slider GUID_XAxis GUID_YAxis GUID_ZAxis IID_IDirectInput8W __real@3f000000 __real@3f800000 __real@40bea90000000000 __real@40c0f88000000000 __real@42ff0000 __real@46fffe00 __real@46ffff00 _glfwDetectJoystickConnectionWin32 _glfwDetectJoystickDisconnectionWin32 _glfwInitJoysticksWin32 _glfwPlatformGetJoystickAxes _glfwPlatformGetJoystickButtons _glfwPlatformGetJoystickName _glfwPlatformJoystickPresent _glfwTerminateJoysticksWin32 ??_C@_08PEMNBIMD@DwmFlush?$AA@ ??_C@_09CBHKFKGB@winmm?4dll?$AA@ ??_C@_0BA@GHGALGFD@xinput9_1_0?4dll?$AA@ ??_C@_0BD@ENICNPLM@SetProcessDPIAware?$AA@ ??_C@_0BD@IJCCAMHG@DirectInput8Create?$AA@ ??_C@_0BG@ELBNFOBA@XInputGetCapabilities?$AA@ ??_C@_0BH@DGFLCCMF@SetProcessDpiAwareness?$AA@ ??_C@_0BI@DOABJMKC@3?42?40?5Win32?5WGL?5VisualC?$AA@ ??_C@_0BI@KJPLJBHH@DwmIsCompositionEnabled?$AA@ ??_C@_0BM@OCGPLDNC@ChangeWindowMessageFilterEx?$AA@ ??_C@_0CA@EDJENDDE@Win32?3?5Failed?5to?5load?5winmm?4dll?$AA@ ??_C@_0CB@HHJIGJHK@Win32?3?5Failed?5to?5load?5user32?4dll@ ??_C@_0CG@LDJIJKNI@Win32?3?5Failed?5to?5create?5helper?5w@ ??_C@_0L@DMFDIJCG@shcore?4dll?$AA@ ??_C@_0L@FACOJKPJ@dwmapi?4dll?$AA@ ??_C@_0L@GMPLCCII@user32?4dll?$AA@ ??_C@_0M@DEPJBPP@timeGetTime?$AA@ ??_C@_0M@ONOEDIJO@dinput8?4dll?$AA@ ??_C@_0O@GLACEBNI@xinput1_2?4dll?$AA@ ??_C@_0O@KAFOJCHN@xinput1_3?4dll?$AA@ ??_C@_0O@LNFLKCMF@xinput1_4?4dll?$AA@ ??_C@_0O@ONJGDDHG@xinput1_1?4dll?$AA@ ??_C@_0P@FCCEJHCC@XInputGetState?$AA@ ??_C@_1CG@OEOKOPIA@?$AAG?$AAL?$AAF?$AAW?$AA?5?$AAh?$AAe?$AAl?$AAp?$AAe?$AAr?$AA?5?$AAw?$AAi?$AAn?$AAd?$AAo?$AAw?$AA?$AA@ GUID_DEVINTERFACE_HID UIntToPtr _glfwCreateUTF8FromWideStringWin32 _glfwCreateWideStringFromUTF8Win32 _glfwPlatformGetVersionString _glfwPlatformInit _glfwPlatformTerminate ??_C@_0BA@KMGHNAFM@Invalid?5time?5?$CFf?$AA@ ??_C@_0BH@LBCIHIMD@Invalid?5window?5hint?5?$CFi?$AA@ ??_C@_0BK@KJGCMOHN@Invalid?5window?5size?5?$CFix?$CFi?$AA@ ??_C@_0BL@PFPABDCG@Denominator?5cannot?5be?5zero?$AA@ ??_C@_0BM@IFCGFMAI@Invalid?5window?5attribute?5?$CFi?$AA@ __real@7fefffffffffffff _glfwInputFramebufferSize _glfwInputWindowCloseRequest _glfwInputWindowDamage _glfwInputWindowFocus _glfwInputWindowIconify _glfwInputWindowMonitorChange _glfwInputWindowPos _glfwInputWindowSize glfwCreateWindow glfwDefaultWindowHints glfwDestroyWindow glfwFocusWindow glfwGetFramebufferSize glfwGetWindowAttrib glfwGetWindowFrameSize glfwGetWindowMonitor glfwGetWindowPos glfwGetWindowSize glfwGetWindowUserPointer glfwHideWindow glfwIconifyWindow glfwMaximizeWindow glfwPollEvents glfwPostEmptyEvent glfwRestoreWindow glfwSetFramebufferSizeCallback glfwSetWindowAspectRatio glfwSetWindowCloseCallback glfwSetWindowFocusCallback glfwSetWindowIcon glfwSetWindowIconifyCallback glfwSetWindowMonitor glfwSetWindowPos glfwSetWindowPosCallback glfwSetWindowRefreshCallback glfwSetWindowShouldClose glfwSetWindowSize glfwSetWindowSizeCallback glfwSetWindowSizeLimits glfwSetWindowTitle glfwSetWindowUserPointer glfwShowWindow glfwWaitEvents glfwWaitEventsTimeout glfwWindowHint glfwWindowShouldClose ??_C@_07PBILKAFL@Success?$AA@ ??_C@_0BD@DKPJMBMG@VK_KHR_xcb_surface?$AA@ ??_C@_0BD@GLEDICEG@VK_KHR_mir_surface?$AA@ ??_C@_0BE@EFOBHOAB@VK_KHR_xlib_surface?$AA@ ??_C@_0BF@NEMNBLEB@An?5event?5is?5signaled?$AA@ ??_C@_0BG@HBCIDECD@vkGetInstanceProcAddr?$AA@ ??_C@_0BH@FDAKHMHL@VK_KHR_wayland_surface?$AA@ ??_C@_0BH@MDCGBFEN@An?5event?5is?5unsignaled?$AA@ ??_C@_0BJ@PGPMJKGJ@Vulkan?3?5Loader?5not?5found?$AA@ ??_C@_0BM@OMEGJAIK@ERROR?3?5UNKNOWN?5VULKAN?5ERROR?$AA@ ??_C@_0CB@EJFBPGFM@A?5surface?5is?5no?5longer?5available@ ??_C@_0CC@HDNEDJNI@A?5validation?5layer?5found?5an?5erro@ ??_C@_0CE@HKNBGEPI@A?5host?5memory?5allocation?5has?5fai@ ??_C@_0CF@OFADCJLJ@A?5requested?5feature?5is?5not?5suppo@ ??_C@_0CG@CHEOMKGJ@A?5device?5memory?5allocation?5has?5f@ ??_C@_0CG@CHGOPGBD@Mapping?5of?5a?5memory?5object?5has?5f@ ??_C@_0CH@DNHGEPME@vkEnumerateInstanceExtensionProp@ ??_C@_0CH@GBHCOIGC@A?5fence?5or?5query?5has?5not?5yet?5com@ ??_C@_0CH@NEGEBDM@A?5requested?5extension?5is?5not?5sup@ ??_C@_0CM@GKIHKJDL@A?5return?5array?5was?5too?5small?5for@ ??_C@_0CN@IHJGMBHI@The?5logical?5or?5physical?5device?5h@ ??_C@_0DA@LIIDOJC@Vulkan?3?5Failed?5to?5query?5instance@ ??_C@_0DD@OOKDLCED@A?5requested?5format?5is?5not?5suppor@ ??_C@_0DF@FCIHEFHD@Vulkan?3?5Window?5surface?5creation?5@ ??_C@_0DF@FEPNJFGL@Vulkan?3?5Loader?5does?5not?5export?5v@ ??_C@_0DF@KADNFCFI@Vulkan?3?5Failed?5to?5query?5instance@ ??_C@_0DH@EKEHMEFD@Too?5many?5objects?5of?5the?5type?5hav@ ??_C@_0DI@GNLCAHHB@A?5requested?5layer?5is?5not?5present@ ??_C@_0DJ@KAONABFK@A?5wait?5operation?5has?5not?5complet@ ??_C@_0EC@MCALKGHI@Vulkan?3?5Failed?5to?5retrieve?5vkEnu@ ??_C@_0EP@OJIEDFOO@The?5display?5used?5by?5a?5swapchain?5@ ??_C@_0FE@ILJLDKCD@A?5swapchain?5no?5longer?5matches?5th@ ??_C@_0FH@FIPMKLID@A?5surface?5has?5changed?5in?5such?5a?5@ ??_C@_0FH@MCDKNJAI@Initialization?5of?5an?5object?5coul@ ??_C@_0FM@BBONGFMN@The?5requested?5version?5of?5Vulkan?5@ ??_C@_0FN@PJBCJBIL@The?5requested?5window?5is?5already?5@ ??_C@_0N@IILPFLFM@vulkan?91?4dll?$AA@ _glfwGetVulkanResultString _glfwInitVulkan _glfwTerminateVulkan glfwCreateWindowSurface glfwGetInstanceProcAddress glfwGetPhysicalDevicePresentationSupport glfwGetRequiredInstanceExtensions glfwVulkanSupported ??_C@_0BH@BAPIEFAH@Invalid?5gamma?5value?5?$CFf?$AA@ __real@3fe0000000000000 __real@3ff0000000000000 __real@406fe00000000000 __real@40efffe000000000 __real@7f7fffff _glfwAllocGammaArrays _glfwAllocMonitor _glfwChooseVideoMode _glfwCompareVideoModes _glfwFreeGammaArrays _glfwFreeMonitor _glfwFreeMonitors _glfwInputMonitorChange _glfwInputMonitorWindowChange _glfwSplitBPP glfwGetGammaRamp glfwGetMonitorName glfwGetMonitorPhysicalSize glfwGetMonitorPos glfwGetMonitors glfwGetPrimaryMonitor glfwGetVideoMode glfwGetVideoModes glfwSetGamma glfwSetGammaRamp glfwSetMonitorCallback ??_C@_0BE@MDOOLHEE@Invalid?5joystick?5?$CFi?$AA@ ??_C@_0BG@CGJALDFA@Invalid?5input?5mode?5?$CFi?$AA@ ??_C@_0BH@FEEOFJLC@Invalid?5cursor?5mode?5?$CFi?$AA@ ??_C@_0BI@POJNLJDO@Invalid?5mouse?5button?5?$CFi?$AA@ ??_C@_0BL@JCGOGHAB@Invalid?5standard?5cursor?5?$CFi?$AA@ ??_C@_0P@PKABKPLF@Invalid?5key?5?$CFi?$AA@ __real@42112e0be8240000 __real@43e0000000000000 __real@43f0000000000000 _glfwInputChar _glfwInputCursorEnter _glfwInputCursorMotion _glfwInputDrop _glfwInputJoystickChange _glfwInputKey _glfwInputMouseClick _glfwInputScroll _glfwIsPrintable glfwCreateCursor glfwCreateStandardCursor glfwDestroyCursor glfwGetClipboardString glfwGetCursorPos glfwGetInputMode glfwGetJoystickAxes glfwGetJoystickButtons glfwGetJoystickName glfwGetKey glfwGetKeyName glfwGetMouseButton glfwGetTime glfwGetTimerFrequency glfwGetTimerValue glfwJoystickPresent glfwSetCharCallback glfwSetCharModsCallback glfwSetClipboardString glfwSetCursor glfwSetCursorEnterCallback glfwSetCursorPos glfwSetCursorPosCallback glfwSetDropCallback glfwSetInputMode glfwSetJoystickCallback glfwSetKeyCallback glfwSetMouseButtonCallback glfwSetScrollCallback glfwSetTime ??_C@_0BK@PMMAHJAK@ERROR?3?5UNKNOWN?5GLFW?5ERROR?$AA@ ??_C@_0BM@BADCNPOM@Invalid?5value?5for?5parameter?$AA@ ??_C@_0BM@PKHPFLOM@There?5is?5no?5current?5context?$AA@ ??_C@_0CD@HJEBEGLD@A?5platform?9specific?5error?5occurr@ ??_C@_0CE@BEELAIPG@The?5GLFW?5library?5is?5not?5initiali@ ??_C@_0CE@KDBKFLEE@Invalid?5argument?5for?5enum?5parame@ ??_C@_0CE@OJFJMLEI@The?5specified?5window?5has?5no?5cont@ ??_C@_0CE@PCLEMLLO@The?5requested?5format?5is?5unavaila@ ??_C@_0CI@GDIOIKBB@The?5requested?5client?5API?5is?5unav@ ??_C@_0DA@NMGNJHAE@The?5requested?5client?5API?5version@ ??_C@_0O@NALGGDJF@Out?5of?5memory?$AA@ _glfw _glfwInitialized _glfwInputError glfwGetVersion glfwGetVersionString glfwInit glfwSetErrorCallback glfwTerminate ??_C@_07IBAPPAHJ@glClear?$AA@ ??_C@_08FOJKHHJA@?$CFd?4?$CFd?4?$CFd?$AA@ ??_C@_0BC@CLDFFND@GL_EXT_robustness?$AA@ ??_C@_0BC@EOBKJDCM@GL_ARB_robustness?$AA@ ??_C@_0BE@OHIPHHMJ@GL_ARB_debug_output?$AA@ ??_C@_0BF@CJMIIMBG@GL_ARB_compatibility?$AA@ ??_C@_0BG@CEONMDO@Invalid?5client?5API?5?$CFi?$AA@ ??_C@_0BK@IEJBEGLB@Invalid?5OpenGL?5profile?5?$CFi?$AA@ ??_C@_0BN@DHKEBMCG@Invalid?5OpenGL?5version?5?$CFi?4?$CFi?$AA@ ??_C@_0BN@FNDGKECA@GL_KHR_context_flush_control?$AA@ ??_C@_0BP@MMGGFAGN@Extension?5name?5is?5empty?5string?$AA@ ??_C@_0CA@LDAANPIM@Entry?5point?5retrieval?5is?5broken?$AA@ ??_C@_0CA@LFIDGAHJ@Invalid?5OpenGL?5ES?5version?5?$CFi?4?$CFi?$AA@ ??_C@_0CD@GAIKDKHK@Invalid?5context?5robustness?5mode?5@ ??_C@_0CE@HHGIDEAE@Invalid?5context?5release?5behavior@ ??_C@_0CF@KMMHNOMI@Extension?5string?5retrieval?5is?5br@ ??_C@_0CO@DBDPCKJA@Client?5API?5version?5string?5retrie@ ??_C@_0CO@DNIGMAHN@No?5version?5found?5in?5client?5API?5v@ ??_C@_0DG@HKHFODHE@Requested?5client?5API?5version?5?$CFi?4@ ??_C@_0ED@FEABOOIM@Context?5profiles?5are?5only?5define@ ??_C@_0EH@CMDCLIEA@Forward?9compatibility?5is?5only?5de@ ??_C@_0L@JLFHOCGA@OpenGL?5ES?5?$AA@ ??_C@_0M@OPOBFDCB@glGetString?$AA@ ??_C@_0N@PILJCBOD@glGetStringi?$AA@ ??_C@_0O@DHHDCCLF@glGetIntegerv?$AA@ ??_C@_0O@EMKFPNOG@OpenGL?5ES?9CL?5?$AA@ ??_C@_0O@ENGHJHNB@OpenGL?5ES?9CM?5?$AA@ _glfwChooseFBConfig _glfwIsValidContextConfig _glfwRefreshContextAttribs _glfwStringInExtensionString glfwExtensionSupported glfwGetCurrentContext glfwGetProcAddress glfwMakeContextCurrent glfwSwapBuffers glfwSwapInterval 
/               1459697978              0       16447     `

    �  栖  睾 烀 � � Tl p� � >c  n �/ �   
 	 
         
 
  	  	    	     
 
  	   
    	 
  	   	        	  
           	  
 
        
     
 
 	    	    
     
 	        
  	 	 	   	  	 	           	  	 
 
    	    	 	 	 	   
 	 	 	   	 
 
  	  	 	 	 	  	    
   
  	  
 
 
 
                                
 
  
    
       
   
 
  
 
 
       
 
 
 	    	           
 
           
                                                                
    
 
   	      	    
   
   
  	      
 
 
 
  	 
 
 	      
 
               
               
 
    
                    
 
  	     ??_C@_03HAFBONIK@IG_?$AA@ ??_C@_07IBAPPAHJ@glClear?$AA@ ??_C@_07PBILKAFL@Success?$AA@ ??_C@_08FOJKHHJA@?$CFd?4?$CFd?4?$CFd?$AA@ ??_C@_08PEMNBIMD@DwmFlush?$AA@ ??_C@_09CBHKFKGB@winmm?4dll?$AA@ ??_C@_0BA@GHGALGFD@xinput9_1_0?4dll?$AA@ ??_C@_0BA@KMGHNAFM@Invalid?5time?5?$CFf?$AA@ ??_C@_0BA@LCMKGGHA@XInput?5Drum?5Kit?$AA@ ??_C@_0BB@GHMBNOIP@wglCreateContext?$AA@ ??_C@_0BB@JDDMDGFO@XInput?5Dance?5Pad?$AA@ ??_C@_0BB@OGAIJNNO@wglDeleteContext?$AA@ ??_C@_0BC@CLDFFND@GL_EXT_robustness?$AA@ ??_C@_0BC@EOBKJDCM@GL_ARB_robustness?$AA@ ??_C@_0BC@FFKMKEGM@wglGetProcAddress?$AA@ ??_C@_0BD@DKPJMBMG@VK_KHR_xcb_surface?$AA@ ??_C@_0BD@ENICNPLM@SetProcessDPIAware?$AA@ ??_C@_0BD@GLEDICEG@VK_KHR_mir_surface?$AA@ ??_C@_0BD@IINOPBDD@wglSwapIntervalEXT?$AA@ ??_C@_0BD@IJCCAMHG@DirectInput8Create?$AA@ ??_C@_0BE@BMOJHNDI@WGL_ARB_multisample?$AA@ ??_C@_0BE@EFOBHOAB@VK_KHR_xlib_surface?$AA@ ??_C@_0BE@FABDLJKH@XInput?5Arcade?5Stick?$AA@ ??_C@_0BE@HBNNGLHP@XInput?5Flight?5Stick?$AA@ ??_C@_0BE@JNNBBIGB@Xbox?5360?5Controller?$AA@ ??_C@_0BE@MDOOLHEE@Invalid?5joystick?5?$CFi?$AA@ ??_C@_0BE@OHIPHHMJ@GL_ARB_debug_output?$AA@ ??_C@_0BF@CJMIIMBG@GL_ARB_compatibility?$AA@ ??_C@_0BF@COCMEDFO@VK_KHR_win32_surface?$AA@ ??_C@_0BF@NEMNBLEB@An?5event?5is?5signaled?$AA@ ??_C@_0BF@NPDKPIKE@WGL_EXT_swap_control?$AA@ ??_C@_0BF@OGGDOHFN@WGL_ARB_pixel_format?$AA@ ??_C@_0BG@CEONMDO@Invalid?5client?5API?5?$CFi?$AA@ ??_C@_0BG@CGJALDFA@Invalid?5input?5mode?5?$CFi?$AA@ ??_C@_0BG@DGJLFNBN@Unknown?5XInput?5Device?$AA@ ??_C@_0BG@ELBNFOBA@XInputGetCapabilities?$AA@ ??_C@_0BG@HBCIDECD@vkGetInstanceProcAddr?$AA@ ??_C@_0BH@BAPIEFAH@Invalid?5gamma?5value?5?$CFf?$AA@ ??_C@_0BH@DGFLCCMF@SetProcessDpiAwareness?$AA@ ??_C@_0BH@FDAKHMHL@VK_KHR_wayland_surface?$AA@ ??_C@_0BH@FEEOFJLC@Invalid?5cursor?5mode?5?$CFi?$AA@ ??_C@_0BH@LBCIHIMD@Invalid?5window?5hint?5?$CFi?$AA@ ??_C@_0BH@MDCGBFEN@An?5event?5is?5unsignaled?$AA@ ??_C@_0BH@NCJDMLMG@WGL_ARB_create_context?$AA@ ??_C@_0BI@DOABJMKC@3?42?40?5Win32?5WGL?5VisualC?$AA@ ??_C@_0BI@INMBJMIJ@vkCreateWin32SurfaceKHR?$AA@ ??_C@_0BI@KJPLJBHH@DwmIsCompositionEnabled?$AA@ ??_C@_0BI@POJNLJDO@Invalid?5mouse?5button?5?$CFi?$AA@ ??_C@_0BJ@CIMJOHKL@WGL_ARB_framebuffer_sRGB?$AA@ ??_C@_0BJ@ODLLHDDH@WGL_EXT_framebuffer_sRGB?$AA@ ??_C@_0BJ@PGPMJKGJ@Vulkan?3?5Loader?5not?5found?$AA@ ??_C@_0BK@HGDBIG@wglGetExtensionsStringEXT?$AA@ ??_C@_0BK@IEJBEGLB@Invalid?5OpenGL?5profile?5?$CFi?$AA@ ??_C@_0BK@JOBLJGNA@wglGetExtensionsStringARB?$AA@ ??_C@_0BK@KJGCMOHN@Invalid?5window?5size?5?$CFix?$CFi?$AA@ ??_C@_0BK@PMMAHJAK@ERROR?3?5UNKNOWN?5GLFW?5ERROR?$AA@ ??_C@_0BL@JCGOGHAB@Invalid?5standard?5cursor?5?$CFi?$AA@ ??_C@_0BL@PDDHMPGF@wglCreateContextAttribsARB?$AA@ ??_C@_0BL@PFPABDCG@Denominator?5cannot?5be?5zero?$AA@ ??_C@_0BM@BADCNPOM@Invalid?5value?5for?5parameter?$AA@ ??_C@_0BM@IFCGFMAI@Invalid?5window?5attribute?5?$CFi?$AA@ ??_C@_0BM@NAGDGOBA@DI?3?5Failed?5to?5create?5device?$AA@ ??_C@_0BM@OCGPLDNC@ChangeWindowMessageFilterEx?$AA@ ??_C@_0BM@OMEGJAIK@ERROR?3?5UNKNOWN?5VULKAN?5ERROR?$AA@ ??_C@_0BM@PKHPFLOM@There?5is?5no?5current?5context?$AA@ ??_C@_0BN@DHKEBMCG@Invalid?5OpenGL?5version?5?$CFi?4?$CFi?$AA@ ??_C@_0BN@FNDGKECA@GL_KHR_context_flush_control?$AA@ ??_C@_0BN@GDKHKIAN@wglGetPixelFormatAttribivARB?$AA@ ??_C@_0BN@HJHDCKKP@Win32?3?5Failed?5to?5create?5icon?$AA@ ??_C@_0BN@OIHKANJ@Wireless?5Xbox?5360?5Controller?$AA@ ??_C@_0BO@FABLLHKK@WGL_ARB_context_flush_control?$AA@ ??_C@_0BP@BFHGHOIO@Win32?3?5Failed?5to?5create?5window?$AA@ ??_C@_0BP@JAOBDJGN@WGL_ARB_create_context_profile?$AA@ ??_C@_0BP@JPGGHMBM@DI?3?5Failed?5to?5create?5interface?$AA@ ??_C@_0BP@MMGGFAGN@Extension?5name?5is?5empty?5string?$AA@ ??_C@_0BP@OHFKOFFD@Win32?3?5Failed?5to?5create?5cursor?$AA@ ??_C@_0CA@CNJHNGPE@Win32?3?5Failed?5to?5set?5video?5mode?$AA@ ??_C@_0CA@EDJENDDE@Win32?3?5Failed?5to?5load?5winmm?4dll?$AA@ ??_C@_0CA@FNCFBEIG@Win32?3?5Failed?5to?5open?5clipboard?$AA@ ??_C@_0CA@LDAANPIM@Entry?5point?5retrieval?5is?5broken?$AA@ ??_C@_0CA@LFIDGAHJ@Invalid?5OpenGL?5ES?5version?5?$CFi?4?$CFi?$AA@ ??_C@_0CB@EJFBPGFM@A?5surface?5is?5no?5longer?5available@ ??_C@_0CB@FLGENKMO@WGL?3?5Failed?5to?5load?5opengl32?4dll@ ??_C@_0CB@HHJIGJHK@Win32?3?5Failed?5to?5load?5user32?4dll@ ??_C@_0CC@BFGHOMNL@WGL_ARB_create_context_robustnes@ ??_C@_0CC@HDNEDJNI@A?5validation?5layer?5found?5an?5erro@ ??_C@_0CD@BFBAJDII@DI?3?5Failed?5to?5set?5device?5axis?5mo@ ??_C@_0CD@EGEEPFHF@Win32?3?5Gamma?5ramp?5size?5must?5be?52@ ??_C@_0CD@FOMFMBIL@WGL_EXT_create_context_es2_profi@ ??_C@_0CD@GAIKDKHK@Invalid?5context?5robustness?5mode?5@ ??_C@_0CD@HJEBEGLD@A?5platform?9specific?5error?5occurr@ ??_C@_0CE@BEELAIPG@The?5GLFW?5library?5is?5not?5initiali@ ??_C@_0CE@FEGPCKPN@WGL?3?5Failed?5to?5make?5context?5curr@ ??_C@_0CE@GCBEILKL@Win32?3?5Failed?5to?5create?5RGBA?5bit@ ??_C@_0CE@HHGIDEAE@Invalid?5context?5release?5behavior@ ??_C@_0CE@HKNBGEPI@A?5host?5memory?5allocation?5has?5fai@ ??_C@_0CE@KDBKFLEE@Invalid?5argument?5for?5enum?5parame@ ??_C@_0CE@MEIEKJIC@Win32?3?5Failed?5to?5allocate?5TLS?5in@ ??_C@_0CE@OJFJMLEI@The?5specified?5window?5has?5no?5cont@ ??_C@_0CE@PCLEMLLO@The?5requested?5format?5is?5unavaila@ ??_C@_0CE@PKGOIGHB@Win32?3?5Failed?5to?5create?5mask?5bit@ ??_C@_0CF@EKIADPFB@WGL?3?5Failed?5to?5clear?5current?5con@ ??_C@_0CF@GFMDJIA@WGL?3?5Failed?5to?5create?5OpenGL?5con@ ??_C@_0CF@KMMHNOMI@Extension?5string?5retrieval?5is?5br@ ??_C@_0CF@KNHHCLIO@DI?3?5Failed?5to?5set?5device?5data?5fo@ ??_C@_0CF@OFADCJLJ@A?5requested?5feature?5is?5not?5suppo@ ??_C@_0CG@CHEOMKGJ@A?5device?5memory?5allocation?5has?5f@ ??_C@_0CG@CHGOPGBD@Mapping?5of?5a?5memory?5object?5has?5f@ ??_C@_0CG@GMPEPNKL@WGL?3?5Failed?5to?5retrieve?5DC?5for?5w@ ??_C@_0CG@LDJIJKNI@Win32?3?5Failed?5to?5create?5helper?5w@ ??_C@_0CH@DNHGEPME@vkEnumerateInstanceExtensionProp@ ??_C@_0CH@GAPNPCHF@Win32?3?5Failed?5to?5register?5window@ ??_C@_0CH@GBHCOIGC@A?5fence?5or?5query?5has?5not?5yet?5com@ ??_C@_0CH@NEGEBDM@A?5requested?5extension?5is?5not?5sup@ ??_C@_0CH@OEOICFHI@DI?3?5Failed?5to?5enumerate?5device?5o@ ??_C@_0CI@BJIPONFG@DI?3?5Failed?5to?5query?5device?5capab@ ??_C@_0CI@GDIOIKBB@The?5requested?5client?5API?5is?5unav@ ??_C@_0CI@LDEHPBDB@Win32?3?5Failed?5to?5create?5standard@ ??_C@_0CJ@EFIFNOJC@Win32?3?5Failed?5to?5convert?5string?5@ ??_C@_0CJ@HAGOIHNO@WGL?3?5Failed?5to?5set?5selected?5pixe@ ??_C@_0CJ@HDJDHLNO@Failed?5to?5enumerate?5DirectInput8@ ??_C@_0CK@JIIJNNEM@Win32?3?5Failed?5to?5convert?5string?5@ ??_C@_0CL@LJNEEKEI@Win32?3?5Failed?5to?5create?5Vulkan?5s@ ??_C@_0CM@DGJKGOGD@WGL?3?5Failed?5to?5find?5a?5suitable?5p@ ??_C@_0CM@GKIHKJDL@A?5return?5array?5was?5too?5small?5for@ ??_C@_0CN@FGAGPCCP@Win32?3?5Failed?5to?5convert?5clipboa@ ??_C@_0CN@IHJGMBHI@The?5logical?5or?5physical?5device?5h@ ??_C@_0CO@DBDPCKJA@Client?5API?5version?5string?5retrie@ ??_C@_0CO@DNIGMAHN@No?5version?5found?5in?5client?5API?5v@ ??_C@_0CO@MCBPBAMK@Win32?3?5Failed?5to?5convert?5wide?5st@ ??_C@_0CP@FOBJNCLA@vkGetPhysicalDeviceWin32Presenta@ ??_C@_0DA@CDIFIIJI@Win32?3?5Failed?5to?5convert?5window?5@ ??_C@_0DA@LIIDOJC@Vulkan?3?5Failed?5to?5query?5instance@ ??_C@_0DA@NMGNJHAE@The?5requested?5client?5API?5version@ ??_C@_0DC@HFHALHIB@WGL?3?5The?5driver?5does?5not?5appear?5@ ??_C@_0DC@ICHAJFGA@WGL?3?5Failed?5to?5retrieve?5pixel?5fo@ ??_C@_0DD@OOKDLCED@A?5requested?5format?5is?5not?5suppor@ ??_C@_0DF@FCIHEFHD@Vulkan?3?5Window?5surface?5creation?5@ ??_C@_0DF@FEPNJFGL@Vulkan?3?5Loader?5does?5not?5export?5v@ ??_C@_0DF@KADNFCFI@Vulkan?3?5Failed?5to?5query?5instance@ ??_C@_0DG@CGDLBFFB@Win32?3?5Failed?5to?5allocate?5global@ ??_C@_0DG@EPNPJJLB@WGL?3?5Failed?5to?5retrieve?5PFD?5for?5@ ??_C@_0DG@HKHFODHE@Requested?5client?5API?5version?5?$CFi?4@ ??_C@_0DH@EKEHMEFD@Too?5many?5objects?5of?5the?5type?5hav@ ??_C@_0DI@GNLCAHHB@A?5requested?5layer?5is?5not?5present@ ??_C@_0DJ@KAONABFK@A?5wait?5operation?5has?5not?5complet@ ??_C@_0DM@FCAOPKO@WGL?3?5Failed?5to?5enable?5sharing?5wi@ ??_C@_0DO@PGEACBCJ@Win32?3?5Vulkan?5instance?5missing?5V@ ??_C@_0EC@MCALKGHI@Vulkan?3?5Failed?5to?5retrieve?5vkEnu@ ??_C@_0ED@FEABOOIM@Context?5profiles?5are?5only?5define@ ??_C@_0EH@CMDCLIEA@Forward?9compatibility?5is?5only?5de@ ??_C@_0EP@EKHGPGGO@WGL?3?5OpenGL?5ES?5requested?5but?5WGL@ ??_C@_0EP@OJIEDFOO@The?5display?5used?5by?5a?5swapchain?5@ ??_C@_0FA@OHOPKHFK@WGL?3?5OpenGL?5profile?5requested?5bu@ ??_C@_0FE@ILJLDKCD@A?5swapchain?5no?5longer?5matches?5th@ ??_C@_0FH@FIPMKLID@A?5surface?5has?5changed?5in?5such?5a?5@ ??_C@_0FH@MCDKNJAI@Initialization?5of?5an?5object?5coul@ ??_C@_0FM@BBONGFMN@The?5requested?5version?5of?5Vulkan?5@ ??_C@_0FN@NPOMMKHN@WGL?3?5A?5forward?5compatible?5OpenGL@ ??_C@_0FN@PJBCJBIL@The?5requested?5window?5is?5already?5@ ??_C@_0L@DMFDIJCG@shcore?4dll?$AA@ ??_C@_0L@FACOJKPJ@dwmapi?4dll?$AA@ ??_C@_0L@GMPLCCII@user32?4dll?$AA@ ??_C@_0L@JLFHOCGA@OpenGL?5ES?5?$AA@ ??_C@_0M@DEPJBPP@timeGetTime?$AA@ ??_C@_0M@ONOEDIJO@dinput8?4dll?$AA@ ??_C@_0M@OPOBFDCB@glGetString?$AA@ ??_C@_0N@DOKAJOHF@opengl32?4dll?$AA@ ??_C@_0N@IILPFLFM@vulkan?91?4dll?$AA@ ??_C@_0N@JENFOGFI@XInput?5Wheel?$AA@ ??_C@_0N@PILJCBOD@glGetStringi?$AA@ ??_C@_0O@DHHDCCLF@glGetIntegerv?$AA@ ??_C@_0O@EMKFPNOG@OpenGL?5ES?9CL?5?$AA@ ??_C@_0O@ENGHJHNB@OpenGL?5ES?9CM?5?$AA@ ??_C@_0O@GCGJMHDL@wglShareLists?$AA@ ??_C@_0O@GLACEBNI@xinput1_2?4dll?$AA@ ??_C@_0O@IFDGNEHH@XInput?5Guitar?$AA@ ??_C@_0O@KAFOJCHN@xinput1_3?4dll?$AA@ ??_C@_0O@LNFLKCMF@xinput1_4?4dll?$AA@ ??_C@_0O@NALGGDJF@Out?5of?5memory?$AA@ ??_C@_0O@ONJGDDHG@xinput1_1?4dll?$AA@ ??_C@_0P@BBBFNDGG@wglMakeCurrent?$AA@ ??_C@_0P@FCCEJHCC@XInputGetState?$AA@ ??_C@_0P@IENCOMCD@VK_KHR_surface?$AA@ ??_C@_0P@PKABKPLF@Invalid?5key?5?$CFi?$AA@ ??_C@_19JBPKICJI@?$AAG?$AAL?$AAF?$AAW?$AA?$AA@ ??_C@_1BA@PICGEGJB@?$AAD?$AAI?$AAS?$AAP?$AAL?$AAA?$AAY?$AA?$AA@ ??_C@_1BE@LDFMCOHA@?$AAG?$AAL?$AAF?$AAW?$AA_?$AAI?$AAC?$AAO?$AAN?$AA?$AA@ ??_C@_1CG@OEOKOPIA@?$AAG?$AAL?$AAF?$AAW?$AA?5?$AAh?$AAe?$AAl?$AAp?$AAe?$AAr?$AA?5?$AAw?$AAi?$AAn?$AAd?$AAo?$AAw?$AA?$AA@ ??_C@_1O@IBNLCBOC@?$AAG?$AAL?$AAF?$AAW?$AA3?$AA0?$AA?$AA@ GUID_Button GUID_DEVINTERFACE_HID GUID_POV GUID_RxAxis GUID_RyAxis GUID_RzAxis GUID_Slider GUID_XAxis GUID_YAxis GUID_ZAxis IID_IDirectInput8W UIntToPtr __mask@@NegDouble@ __real@3f000000 __real@3f800000 __real@3fe0000000000000 __real@3ff0000000000000 __real@405e000000000000 __real@406fe00000000000 __real@408f400000000000 __real@40bea90000000000 __real@40c0f88000000000 __real@40efffe000000000 __real@42112e0be8240000 __real@42ff0000 __real@43e0000000000000 __real@43f0000000000000 __real@46fffe00 __real@46ffff00 __real@7f7fffff __real@7fefffffffffffff _glfw _glfwAllocGammaArrays _glfwAllocMonitor _glfwAnalyzeContextWGL _glfwChooseFBConfig _glfwChooseVideoMode _glfwCompareVideoModes _glfwCreateContextWGL _glfwCreateUTF8FromWideStringWin32 _glfwCreateWideStringFromUTF8Win32 _glfwDestroyContextWGL _glfwDetectJoystickConnectionWin32 _glfwDetectJoystickDisconnectionWin32 _glfwFreeGammaArrays _glfwFreeMonitor _glfwFreeMonitors _glfwGetVulkanResultString _glfwInitJoysticksWin32 _glfwInitThreadLocalStorageWin32 _glfwInitTimerWin32 _glfwInitVulkan _glfwInitWGL _glfwInitialized _glfwInputChar _glfwInputCursorEnter _glfwInputCursorMotion _glfwInputDrop _glfwInputError _glfwInputFramebufferSize _glfwInputJoystickChange _glfwInputKey _glfwInputMonitorChange _glfwInputMonitorWindowChange _glfwInputMouseClick _glfwInputScroll _glfwInputWindowCloseRequest _glfwInputWindowDamage _glfwInputWindowFocus _glfwInputWindowIconify _glfwInputWindowMonitorChange _glfwInputWindowPos _glfwInputWindowSize _glfwIsPrintable _glfwIsValidContextConfig _glfwPlatformCreateCursor _glfwPlatformCreateStandardCursor _glfwPlatformCreateWindow _glfwPlatformCreateWindowSurface _glfwPlatformDestroyCursor _glfwPlatformDestroyWindow _glfwPlatformExtensionSupported _glfwPlatformFocusWindow _glfwPlatformGetClipboardString _glfwPlatformGetCurrentContext _glfwPlatformGetCursorPos _glfwPlatformGetFramebufferSize _glfwPlatformGetGammaRamp _glfwPlatformGetJoystickAxes _glfwPlatformGetJoystickButtons _glfwPlatformGetJoystickName _glfwPlatformGetKeyName _glfwPlatformGetMonitorPos _glfwPlatformGetMonitors _glfwPlatformGetPhysicalDevicePresentationSupport _glfwPlatformGetProcAddress _glfwPlatformGetRequiredInstanceExtensions _glfwPlatformGetTimerFrequency _glfwPlatformGetTimerValue _glfwPlatformGetVersionString _glfwPlatformGetVideoMode _glfwPlatformGetVideoModes _glfwPlatformGetWindowFrameSize _glfwPlatformGetWindowPos _glfwPlatformGetWindowSize _glfwPlatformHideWindow _glfwPlatformIconifyWindow _glfwPlatformInit _glfwPlatformIsSameMonitor _glfwPlatformJoystickPresent _glfwPlatformMakeContextCurrent _glfwPlatformMaximizeWindow _glfwPlatformPollEvents _glfwPlatformPostEmptyEvent _glfwPlatformRestoreWindow _glfwPlatformSetClipboardString _glfwPlatformSetCurrentContext _glfwPlatformSetCursor _glfwPlatformSetCursorMode _glfwPlatformSetCursorPos _glfwPlatformSetGammaRamp _glfwPlatformSetWindowAspectRatio _glfwPlatformSetWindowIcon _glfwPlatformSetWindowMonitor _glfwPlatformSetWindowPos _glfwPlatformSetWindowSize _glfwPlatformSetWindowSizeLimits _glfwPlatformSetWindowTitle _glfwPlatformShowWindow _glfwPlatformSwapBuffers _glfwPlatformSwapInterval _glfwPlatformTerminate _glfwPlatformWaitEvents _glfwPlatformWaitEventsTimeout _glfwPlatformWindowFocused _glfwPlatformWindowIconified _glfwPlatformWindowMaximized _glfwPlatformWindowVisible _glfwRefreshContextAttribs _glfwRegisterWindowClassWin32 _glfwRestoreVideoModeWin32 _glfwSetVideoModeWin32 _glfwSplitBPP _glfwStringInExtensionString _glfwTerminateJoysticksWin32 _glfwTerminateThreadLocalStorageWin32 _glfwTerminateVulkan _glfwTerminateWGL _glfwUnregisterWindowClassWin32 glfwCreateCursor glfwCreateStandardCursor glfwCreateWindow glfwCreateWindowSurface glfwDefaultWindowHints glfwDestroyCursor glfwDestroyWindow glfwExtensionSupported glfwFocusWindow glfwGetClipboardString glfwGetCurrentContext glfwGetCursorPos glfwGetFramebufferSize glfwGetGammaRamp glfwGetInputMode glfwGetInstanceProcAddress glfwGetJoystickAxes glfwGetJoystickButtons glfwGetJoystickName glfwGetKey glfwGetKeyName glfwGetMonitorName glfwGetMonitorPhysicalSize glfwGetMonitorPos glfwGetMonitors glfwGetMouseButton glfwGetPhysicalDevicePresentationSupport glfwGetPrimaryMonitor glfwGetProcAddress glfwGetRequiredInstanceExtensions glfwGetTime glfwGetTimerFrequency glfwGetTimerValue glfwGetVersion glfwGetVersionString glfwGetVideoMode glfwGetVideoModes glfwGetWGLContext glfwGetWin32Adapter glfwGetWin32Monitor glfwGetWin32Window glfwGetWindowAttrib glfwGetWindowFrameSize glfwGetWindowMonitor glfwGetWindowPos glfwGetWindowSize glfwGetWindowUserPointer glfwHideWindow glfwIconifyWindow glfwInit glfwJoystickPresent glfwMakeContextCurrent glfwMaximizeWindow glfwPollEvents glfwPostEmptyEvent glfwRestoreWindow glfwSetCharCallback glfwSetCharModsCallback glfwSetClipboardString glfwSetCursor glfwSetCursorEnterCallback glfwSetCursorPos glfwSetCursorPosCallback glfwSetDropCallback glfwSetErrorCallback glfwSetFramebufferSizeCallback glfwSetGamma glfwSetGammaRamp glfwSetInputMode glfwSetJoystickCallback glfwSetKeyCallback glfwSetMonitorCallback glfwSetMouseButtonCallback glfwSetScrollCallback glfwSetTime glfwSetWindowAspectRatio glfwSetWindowCloseCallback glfwSetWindowFocusCallback glfwSetWindowIcon glfwSetWindowIconifyCallback glfwSetWindowMonitor glfwSetWindowPos glfwSetWindowPosCallback glfwSetWindowRefreshCallback glfwSetWindowShouldClose glfwSetWindowSize glfwSetWindowSizeCallback glfwSetWindowSizeLimits glfwSetWindowTitle glfwSetWindowUserPointer glfwShowWindow glfwSwapBuffers glfwSwapInterval glfwTerminate glfwVulkanSupported glfwWaitEvents glfwWaitEventsTimeout glfwWindowHint glfwWindowShouldClose 
//              1459697978              0       400       `
glfw.dir\Release\wgl_context.obj glfw.dir\Release\win32_window.obj glfw.dir\Release\win32_tls.obj glfw.dir\Release\win32_time.obj glfw.dir\Release\win32_monitor.obj glfw.dir\Release\win32_joystick.obj glfw.dir\Release\win32_init.obj glfw.dir\Release\window.obj glfw.dir\Release\vulkan.obj glfw.dir\Release\monitor.obj glfw.dir\Release\input.obj glfw.dir\Release\init.obj glfw.dir\Release\context.obj /0              1459697977              100666  22154     `
d哯 99WK3  +      .drectve        s   $               
 .debug$S        �   �              @ B.text           e   #  �          P`.pdata             �  �         @0@.xdata             �              @0@.rdata          2   �              @@@.text           �  
  �      (    P`.pdata             L  X         @0@.xdata             v  �         @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata                        @0@.xdata             2  N         @0@.rdata          2   X              @@@.rdata          ,   �              @@@.text           .   �  �          P`.pdata             �  �         @0@.xdata                           @0@.text           �      �          P`.pdata             �  �         @0@.xdata             �              @0@.rdata                           @@@.rdata                           @@@.rdata             !              @@@.rdata             3              @@@.rdata             D              @@@.rdata          !   U              @@@.rdata          
   v              @@@.text              �  �          P`.pdata             �  �         @0@.xdata             �              @0@.text             �  �          P`.pdata             �  �         @0@.xdata              �           @0@.rdata          <                 @@@.rdata          %   [              @@@.rdata          )   �              @@@.rdata          6   �              @@@.rdata          &   �              @@@.text           ,      1           P`.pdata             ;   G          @0@.xdata             e               @0@.text           g   m   �           P`.pdata             !  &!         @0@.xdata             D!              @0@.rdata          %   L!              @@@.rdata          $   q!              @@@.text           c   �!  �!          P`.pdata             "  ""         @0@.xdata             @"  P"         @0@.pdata             n"  z"         @0@.xdata             �"  �"         @0@.pdata             �"  �"         @0@.xdata             �"              @0@.text           Z   �"  V#          P`.pdata             ~#  �#         @0@.xdata             �#              @0@.text           }   �#  1$          P`.pdata             c$  o$         @0@.xdata             �$              @0@.text           *   �$  �$          P`.pdata             �$  �$         @0@.xdata             %              @0@.text           H   %  [%          P`.pdata             y%  �%         @0@.xdata             �%              @0@.text           �  �%  �*      c    P`.pdata             �.  �.         @0@.xdata             �.              @0@.rdata             �.              @@@.rdata             �.              @@@.rdata             �.              @@@.rdata          "   �.              @@@.rdata          #   !/              @@@.rdata             D/              @@@.rdata             c/              @@@.rdata             z/              @@@.rdata             �/              @@@.rdata             �/              @@@.rdata             �/              @@@.rdata             �/              @@@.rdata             �/              @@@.rdata             0              @@@.rdata             %0              @@@.text           6  ?0  u1          P`.pdata             2  !2         @0@.xdata             ?2              @0@.rdata          O   O2              @P@.rdata          P   �2              @P@.rdata          ]   �2              @P@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �      A     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\wgl_context.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler  D塂$H冹8H媺0  H岲$@A�   H塂$(H岲$PE3繦塂$ 荄$@    � 	  吚uD婦$PH�    �  �    3繦兡8脣D$@H兡8�7       G       Q           e                         
    	 	b  WGL: Failed to retrieve pixel format attribute %i @UVATAVAWH峫$蒆侅�   H�    H3腍塃�=P	   H嬹H媺0  L塃颒塙鐃VH岴逜�   E3繦塂$(H岴譇嬔荅�    荅�    H塂$ � 	  吚uD婨譎�    �  �    E3潆D媏唠E3葾峇E岮(�    D嬥Ic毯H   �    E3鯨孁E呬幍  H墱$�   H壖$�   �   L壃$�   H峏D嬶�    �=P	   H嫀0  嬜刟  H岴譇�   E3繦塂$(H岴咔E�   H塂$ 荅�    � 	  吚剬   儅� 勍  H嫀0  H岴譇�   H塂$(H岴逧3缷浊E�   荅�    H塂$ � 	  吚tF儅� 剣  H嫀0  H岴譇�   H塂$(H岴逧3缷浊E�   荅�    H塂$ � 	  吚uD婨逪�    �  �    �1  亇�+   �$  A�   嬜H嬑�    =%   �	  A�   嬜H嬑�    A�   嬜H嬑塁    A�   嬜H嬑��    A�   嬜H嬑塁�    A�"   嬜H嬑塁�    A�#   嬜H嬑塁�    A�   嬜H嬑塁�    A�   嬜H嬑塁�    A�    嬜H嬑塁�    A�!   嬜H嬑塁�    A�$   嬜H嬑塁 �    A�   嬜H嬑塁$�    吚t荂(   A�   嬜H嬑�    吚t荂4   �=D	   tA窧   嬜H嬑�    塁,�=H	   u
�=L	   勂   A俯   嬜H嬑�    吚劗   荂0   棰   L峂鰽�(   �    吚剷   婱�剦   隽 剙   横r隽@uu�}� uo禘塁�禘�禘塁禘塁禘塁禘塁禘
塁禘塁禘塁禘
塁 禘塁$隽t荂(   隽t荂4   L塳<A�艸兠H�荌�臝��呂��L嫭$�   H嫾$�   H嫓$�   E咑t;H婱鏓嬈I嬜�    H吚uH�    �	  �%婬@H婨飰I嬒�    �   �H�    �  �    I嬒�    3繦婱H3惕    H伳�   A_A^A\^]�   3    %       l       {       �       �   2    �   1    �       1      {      �      �      �      �            *      <      O      b      u      �      �      �      �      �      �                  &      /      8      O      s  2    J  0    V  /    o  ,    }  +    �      �  ,    �  4    ?  �                            !       �                        '    �   ?                        !    ! � t 4     �                        '        �                         '    #  	���`P      x      (    WGL: The driver does not appear to support OpenGL WGL: Failed to find a suitable pixel format H冹(H��  H吚uH兡(肏峀$0�袐L$03覅�E蕥罤兡(�           .           7       7       =     B  H冹(H�
    �    H��  H吚uH�    �  �    3繦兡(肏�    H嬋�    H�
�  H�    H��  �    H�
�  H�    H��  �    H�
�  H�    H��  �    H�
�  H�    H� 	  �    H�	  �   H兡(�   ]    
   Z               Y    *       8   V    A   S    H       O   R    V       \   S    c       j   O    q       w   S    ~       �   L    �       �   S    �       �   I    �       �   S    �           �           ^       ^       F     B  wglShareLists wglMakeCurrent wglGetProcAddress wglDeleteContext wglCreateContext WGL: Failed to load opengl32.dll opengl32.dll H冹(H�
�  H吷t�    H兡(�          h                   i       i       g     B  H塡$ UVWATAUH峫$蒆侅   H�    H3腍塃'E3鞩嬸H嬟E嬪H孂D塴$ D9"劆  H婤(H吚tL嫚8  H媺�  �    H墖0  H吚uH�    �  �    3篱d  L岲$ H嬛H嬒�    吚t鍕t$ H嫃0  L峀$(嬛A�(   �    吚uH�    �  �    3篱  H嫃0  L岲$(嬛�    吚uH�    �  �    3篱�  D9-T	  剳  �;  A嬚E嬐E嬇�   u#9S婥DE�=  uD岻��=  uD嬌�A�   9StA內9StA內婥吚t39`	  t+= u	荅媋�  �= u荅婻�  嬔荅嘨�  A內婥 吚tHD9-d	  t?=P uHc�虑D厙�   Hc翫塴厙�=P uHc�虑D厙�   Hc虑D厙�   �聥K凒uD9kt/Hc�虑D厙�   Hc�聣L厙Hc�虑D厙�   婥Hc蕢D崌�翬吚tHc�虑D厙�   Hc�翫塂厙E吷tHc�虑D厙&�  Hc�翫塋厙H嫃0  Hc翷岴嘍塴厙I嬙D塴厠�8	  H墖8  H吚uXH�    �  �    3离HH嫃0  ��  H墖8  H吚t襇呬t%H嬓I嬏�	  吚uH�    �  �    3离�   H婱'H3惕    H嫓$H  H伳   A]A\_^]�   3    \   �    o   �    y       �       �   2    �       �       �   |    �   {    �             i      �      {      �  x    �      �      �      �  u    �      �  4                  �       �       r    &	 4)   �
�p`P      �      (    WGL: Failed to enable sharing with specified OpenGL context WGL: Failed to create OpenGL context WGL: Failed to set selected pixel format WGL: Failed to retrieve PFD for selected pixel format WGL: Failed to retrieve DC for window @SH冹 H嬞H媺8  H吷t��  H莾8      H兡 [�           ,           �       �       �     20@SH冹 H嬞H吷t.H嫅8  H媺0  � 	  吚t
H嬎H兡 [�    H�    �3�3� 	  吚uH�    �  �    3蒆兡 [�           /   �    6   �    B       M   �    W       c   �        g           �       �       �     20WGL: Failed to clear current context WGL: Failed to make context current @WH冹 H��  H孂H吚t;H峀$0�袇纔09D$0t*H�@ u#媷@  H塡$8檵�3�+趖
��  �藆鯤媆$8H嫃0  H兡 _H�%    	       C       _   �    P   c           �       �       �    !       3          �       �       �    3   P           �       �       �    ! 4     3          �       �       �        3           �       �       �     2pH塡$WH冹 嬞�    墭@  H孁H��  H吚tH峀$0�袇纔
9D$0tH9G@D貎=@	   t嬎�	  H媆$8H兡 _�
   �           @       K           Z           �       �       �    
 
4 
2pH塡$WH冹 H嬞�    H�(	  H孁H呉t&�襀吚tH嬓H嬎�    吚t�   H媆$0H兡 _肏�0	  H吚tH嫃0  �蠬吚tH嬓H嬎�    吚u�3繦媆$0H兡 _�   �           /   �    J       h   �        }           �       �       �    
 
4 
2p@SH冹 H嬞��  H吚uH�
�  H嬘�    H兡 [�                  S        *           �       �       �     20H冹(�=     u3夜  �    3繦兡(脙桂   u3夜
  �    3繦兡(肏媮8  H兡(�   �           1           H           �       �       �     B  @SH冹 H�
    ��  H�
    H�(	  ��  H�
    H�0	  ��  H�
    H�8	  ��  H�
    H�	  ��  H� 	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐D	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐H	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐L	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐T	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐X	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐\	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐`	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐@	  �    H�
(	  H嬝H吷t!�袶吚tH�
    H嬓�    吚t�   �6H�0	  H吚t(H媼0  �蠬吚tH�
    H嬓�    吚t�   �3缐P	  �    H�
(	  H嬝H吷t4�袶吚t-H�
    H嬓�    吚t�d	     �	     H兡 [肏�0	  H吚t+H媼0  �蠬吚tH�
    H嬓�    �d	     吚u
�d	      �	     H兡 [�	                          #       *      1       7       >      E       K       R      Y       _       f       k   �    r       �      �   �    �       �      �   �    �       �   �    �       �        �          -     5  �    H      M  �    T      j     r  �    �      �     �  �    �      �  �    �      �  �    �  �    �        �      �    *      /  �    6      L  �    T  �    f      �  �    �  �    �      �  �    �      �  �    �  �    �      �  �    �  �            �          .  �    6  �    H      b  �    j  �    }      �  �    �      �  �    �  �    �      �  �    �  �    �      �  �    �        �      �    *      D  �    L  �    _      d  �    k      �  �    �  �    �      �      �      �  �    �  �    �      �      �          �          �       �       �     20WGL_ARB_context_flush_control WGL_ARB_pixel_format WGL_EXT_swap_control WGL_ARB_create_context_robustness WGL_EXT_create_context_es2_profile WGL_ARB_create_context_profile WGL_ARB_create_context WGL_EXT_framebuffer_sRGB WGL_ARB_framebuffer_sRGB WGL_ARB_multisample wglGetPixelFormatAttribivARB wglSwapIntervalEXT wglCreateContextAttribsARB wglGetExtensionsStringARB wglGetExtensionsStringEXT H塡$H塼$WH冹 3跧嬸H孃9	  呟   �    �    �?  �
T	  uR岰9_t吷uH�    �  槟   嬝� t�=X	   uH�    �  棰   嬝�  t&�=d	   t�吷t9X	  tw9\	  to�   嬝�u� t吷E貎 t吷E貎~0 �
P	  ~�=D	   t吷E貎~4 t�=H	   u	�=L	   t吷u,呟u(3繦媆$0H媡$8H兡 _肏�    �  �    �   H媆$0H媡$8H兡 _�       $   �    )   �    5       J   )   b       l   &   �       �       �       �       �       �       �         #             6          *      *           d 4 2pWGL: OpenGL ES requested but WGL_ARB_create_context_es2_profile is unavailable WGL: OpenGL profile requested but WGL_ARB_create_context_profile is unavailable WGL: A forward compatible OpenGL context requested but WGL_ARB_create_context is unavailable @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          e      �"&�                    .pdata               弋�                   .xdata                ,�5�        5               R            .rdata         2       �b         b           _glfw            .text          �  (   FJ�         �           .pdata               >/        �           .xdata      	         浚�        �       	    .pdata      
         率        �       
    .xdata               艌告                   .pdata               SIF2                  .xdata      
         稩        4      
        N           .rdata         2       `4�         _              �           .rdata         ,       �鰽J         �              �               �                                             /           .text          .      !�         G          .pdata               dp        \          .xdata                �9�        x          .text          �      鲺痗         �          .pdata               J@�8        �          .xdata                �9�        �          .rdata                A-L         �          .rdata                ~/99         �          .rdata                迶H�                   .rdata                湏�         @              i           .rdata                托裃         ~          .rdata         !       �甾         �              �           .rdata         
       '+�         �          $LN4            .text                C��                   .pdata               }-�!        ,          .xdata                �9�        E              _           $LN4            .text                (莳c         q           .pdata      !         �俵         �      !    .xdata      "          a         �      "    .rdata      #   <       Y櫷�         �      #    .rdata      $   %       箁�         �      $    .rdata      %   )       孁�         6      %        q           .rdata      &   6       �儏         �      &    .rdata      '   &       鐧�         �      '        �           $LN41            .text       (   ,      5jA-         
      (    .pdata      )         w佼(        !      )    .xdata      *          （亵(        ?      *    $LN4        (    .text       +   g      臷bO         ^      +    .pdata      ,         ⅸ.�+        ~      ,    .xdata      -          （亵+        �      -    .rdata      .   %       士畱         �      .    .rdata      /   $       譯&�               /        C           $LN10       +    .text       0   c      ��          b      0    .pdata      1         T�b0        {      1    .xdata      2         r��0        �      2    .pdata      3         2�60        �      3    .xdata      4         巎�0        �      4    .pdata      5         濼B0              5    .xdata      6          3��0        #      6        D           $LN14       0    .text       7   Z      i8鴾         V      7    .pdata      8         镦�7        p      8    .xdata      9          ��7        �      9        �           $LN9        7    .text       :   }      湵         �      :    .pdata      ;         A刄7:        �      ;    .xdata      <          %蚘%:        	      <        A	           $LN10       :    .text       =   *      赎鉈         ^	      =    .pdata      >         瀪秇=        z	      >    .xdata      ?          （亵=        �	      ?    $LN4        =    .text       @   H      坞獤         �	      @    .pdata      A         X賦鶣        �	      A    .xdata      B          �9�@        �	      B        
           $LN5        @    .text       C   �  c   綶枬         
      C    .pdata      D         %媆C        &
      D    .xdata      E          （亵C        <
      E    .rdata      F          鄓喁         S
      F    .rdata      G          )輼X         �
      G    .rdata      H          新羇         �
      H    .rdata      I   "       楂         �
      I    .rdata      J   #       Ci篓               J    .rdata      K          "j         M      K    .rdata      L          }蘝         �      L    .rdata      M          G倠�         �      M    .rdata      N          ��>         �      N    .rdata      O          J渺               O    .rdata      P          話�         A      P    .rdata      Q          Ｇ)�         v      Q    .rdata      R          )b�         �      R    .rdata      S          ��         �      S    .rdata      T          KpeP         
      T    .text       U   6     ��         6
      U    .pdata      V         NQ斟U        M
      V    .xdata      W          O鞺        k
      W    .rdata      X   O       *f'         �
      X    .rdata      Y   P       C@3�         �
      Y    .rdata      Z   ]       �教         �
      Z    $LN29       U    9  getPixelFormatAttrib $pdata$getPixelFormatAttrib $unwind$getPixelFormatAttrib _glfwInputError ??_C@_0DC@ICHAJFGA@WGL?3?5Failed?5to?5retrieve?5pixel?5fo@ choosePixelFormat $pdata$3$choosePixelFormat $chain$3$choosePixelFormat $pdata$2$choosePixelFormat $chain$2$choosePixelFormat $pdata$choosePixelFormat $unwind$choosePixelFormat __GSHandlerCheck ??_C@_0DC@HFHALHIB@WGL?3?5The?5driver?5does?5not?5appear?5@ __imp_free ??_C@_0CM@DGJKGOGD@WGL?3?5Failed?5to?5find?5a?5suitable?5p@ _glfwChooseFBConfig __imp_calloc __imp_DescribePixelFormat __security_cookie __security_check_cookie isCompositionEnabled $pdata$isCompositionEnabled $unwind$isCompositionEnabled _glfwInitWGL $pdata$_glfwInitWGL $unwind$_glfwInitWGL ??_C@_0O@GCGJMHDL@wglShareLists?$AA@ ??_C@_0P@BBBFNDGG@wglMakeCurrent?$AA@ ??_C@_0BC@FFKMKEGM@wglGetProcAddress?$AA@ ??_C@_0BB@OGAIJNNO@wglDeleteContext?$AA@ __imp_GetProcAddress ??_C@_0BB@GHMBNOIP@wglCreateContext?$AA@ ??_C@_0CB@FLGENKMO@WGL?3?5Failed?5to?5load?5opengl32?4dll@ __imp_LoadLibraryA ??_C@_0N@DOKAJOHF@opengl32?4dll?$AA@ _glfwTerminateWGL $pdata$_glfwTerminateWGL $unwind$_glfwTerminateWGL __imp_FreeLibrary _glfwCreateContextWGL $pdata$_glfwCreateContextWGL $unwind$_glfwCreateContextWGL ??_C@_0DM@FCAOPKO@WGL?3?5Failed?5to?5enable?5sharing?5wi@ ??_C@_0CF@GFMDJIA@WGL?3?5Failed?5to?5create?5OpenGL?5con@ ??_C@_0CJ@HAGOIHNO@WGL?3?5Failed?5to?5set?5selected?5pixe@ __imp_SetPixelFormat ??_C@_0DG@EPNPJJLB@WGL?3?5Failed?5to?5retrieve?5PFD?5for?5@ ??_C@_0CG@GMPEPNKL@WGL?3?5Failed?5to?5retrieve?5DC?5for?5w@ __imp_GetDC _glfwDestroyContextWGL $pdata$_glfwDestroyContextWGL $unwind$_glfwDestroyContextWGL _glfwPlatformMakeContextCurrent $pdata$_glfwPlatformMakeContextCurrent $unwind$_glfwPlatformMakeContextCurrent ??_C@_0CF@EKIADPFB@WGL?3?5Failed?5to?5clear?5current?5con@ ??_C@_0CE@FEGPCKPN@WGL?3?5Failed?5to?5make?5context?5curr@ _glfwPlatformSetCurrentContext _glfwPlatformSwapBuffers $pdata$1$_glfwPlatformSwapBuffers $chain$1$_glfwPlatformSwapBuffers $pdata$0$_glfwPlatformSwapBuffers $chain$0$_glfwPlatformSwapBuffers $pdata$_glfwPlatformSwapBuffers $unwind$_glfwPlatformSwapBuffers __imp_SwapBuffers _glfwPlatformSwapInterval $pdata$_glfwPlatformSwapInterval $unwind$_glfwPlatformSwapInterval _glfwPlatformGetCurrentContext _glfwPlatformExtensionSupported $pdata$_glfwPlatformExtensionSupported $unwind$_glfwPlatformExtensionSupported _glfwStringInExtensionString _glfwPlatformGetProcAddress $pdata$_glfwPlatformGetProcAddress $unwind$_glfwPlatformGetProcAddress glfwGetWGLContext $pdata$glfwGetWGLContext $unwind$glfwGetWGLContext _glfwInitialized loadExtensions $pdata$loadExtensions $unwind$loadExtensions ??_C@_0BO@FABLLHKK@WGL_ARB_context_flush_control?$AA@ ??_C@_0BF@OGGDOHFN@WGL_ARB_pixel_format?$AA@ ??_C@_0BF@NPDKPIKE@WGL_EXT_swap_control?$AA@ ??_C@_0CC@BFGHOMNL@WGL_ARB_create_context_robustnes@ ??_C@_0CD@FOMFMBIL@WGL_EXT_create_context_es2_profi@ ??_C@_0BP@JAOBDJGN@WGL_ARB_create_context_profile?$AA@ ??_C@_0BH@NCJDMLMG@WGL_ARB_create_context?$AA@ ??_C@_0BJ@ODLLHDDH@WGL_EXT_framebuffer_sRGB?$AA@ ??_C@_0BJ@CIMJOHKL@WGL_ARB_framebuffer_sRGB?$AA@ ??_C@_0BE@BMOJHNDI@WGL_ARB_multisample?$AA@ ??_C@_0BN@GDKHKIAN@wglGetPixelFormatAttribivARB?$AA@ ??_C@_0BD@IINOPBDD@wglSwapIntervalEXT?$AA@ ??_C@_0BL@PDDHMPGF@wglCreateContextAttribsARB?$AA@ ??_C@_0BK@JOBLJGNA@wglGetExtensionsStringARB?$AA@ ??_C@_0BK@HGDBIG@wglGetExtensionsStringEXT?$AA@ _glfwAnalyzeContextWGL $pdata$_glfwAnalyzeContextWGL $unwind$_glfwAnalyzeContextWGL ??_C@_0EP@EKHGPGGO@WGL?3?5OpenGL?5ES?5requested?5but?5WGL@ ??_C@_0FA@OHOPKHFK@WGL?3?5OpenGL?5profile?5requested?5bu@ ??_C@_0FN@NPOMMKHN@WGL?3?5A?5forward?5compatible?5OpenGL@ /33             1459697977              100666  56789     `
d嗘 99W陏  h      .drectve        s   $               
 .debug$S        �   w$              @ B.text           %   %               P`.text              (%               P`.text           Q   A%               P`.pdata             �%  �%         @0@.xdata             �%              @0@.text           "  �%  �'          P`.pdata             �(  �(         @0@.xdata              �(  �(         @0@.rdata             �(              @@@.rdata             
)              @@@.rdata          $   *)              @@@.rdata          $   N)              @@@.text           u   r)  �)          P`.pdata             *  *         @0@.xdata             /*  C*         @0@.text           E  M*  �+          P`.pdata             �+  �+         @0@.xdata             �+  �+         @0@.text           l   �+  h,          P`.pdata             �,  �,         @0@.xdata             �,  �,         @0@.text           `   �,  H-          P`.text           z   �-  .          P`.pdata             D.  P.         @0@.xdata             n.              @0@.text           z   z.  �.          P`.pdata             &/  2/         @0@.xdata             P/              @0@.text           �   \/  0          P`.pdata             :0  F0         @0@.xdata             d0  t0         @0@.pdata             �0  �0         @0@.xdata             �0  �0         @0@.pdata             �0  �0         @0@.xdata             1  ,1         @0@.pdata             J1  V1         @0@.xdata             t1              @0@.text           �   |1  $2          P`.pdata             j2  v2         @0@.xdata             �2  �2         @0@.text           )   �2  �2          P`.pdata             �2  �2         @0@.xdata             3              @0@.text           =  !3  ^5          P`.pdata             &6  26         @0@.xdata          $   P6  t6         @0@.rdata          
   ~6              @@@.rdata             �6              @@@.rdata             �6              @@@.rdata          0   �6              @@@.text           @   �6  %7          P`.pdata             C7  O7         @0@.xdata             m7              @0@.text           !   u7  �7          P`.pdata             �7  �7         @0@.xdata             �7              @0@.text           �   �7  z8          P`.pdata             �8  �8         @0@.xdata             �8              @0@.text           \   �8  X9          P`.pdata             �9  �9         @0@.xdata             �9              @0@.text           �  �9  ~;          P`.pdata             �;  <         @0@.xdata              <  0<         @0@.pdata             N<  Z<         @0@.xdata             x<  �<         @0@.pdata             �<  �<         @0@.xdata             �<  �<         @0@.pdata             
=  =         @0@.xdata             4=  H=         @0@.pdata             f=  r=         @0@.xdata             �=              @0@.text           J   �=  �=          P`.pdata             �=  �=         @0@.xdata             >              @0@.text           �   &>  �>          P`.pdata             �>  
?         @0@.xdata             (?  8?         @0@.text           _   B?  �?          P`.pdata             �?  �?         @0@.xdata             �?  �?         @0@.text           �   @  酅          P`.pdata             A  )A         @0@.xdata             GA  WA         @0@.text           �   aA  霢          P`.pdata             B   B         @0@.xdata             >B  NB         @0@.text             XB  vC          P`.pdata             –  碈         @0@.xdata             褻  釩         @0@.pdata              D  D         @0@.xdata             *D  >D         @0@.pdata             \D  hD         @0@.xdata             咲  朌         @0@.text           _   燚  �D          P`.pdata             E  )E         @0@.xdata             GE  [E         @0@.text           �   eE  bF          P`.pdata             擣  燜         @0@.xdata             綟  贔         @0@.text              銯  鱂          P`.text              G  G          P`.text              G  1G          P`.text              ;G  NG          P`.text              XG  hG          P`.text           6   rG  ℅          P`.pdata             艷  褿         @0@.xdata             餑              @0@.text           �  鳪  餔          P`.pdata               癒         @0@.xdata             蜬  轐         @0@.pdata             麷  L         @0@.xdata             &L  :L         @0@.pdata             XL  dL         @0@.xdata             侺  歀         @0@.text           #     荓          P`.pdata             袻  軱         @0@.xdata             鸏              @0@.text              M  M          P`.text              M  )M          P`.text              3M  AM          P`.text              KM  hM          P`.text           o   |M  隡          P`.pdata             �M  N         @0@.xdata             )N              @0@.text           E   9N  ~N          P`.pdata             扤  濶         @0@.xdata             糔              @0@.text           �   腘  gO          P`.pdata             稯  肙         @0@.xdata             酧              @0@.text           �   鞳  甈          P`.pdata               
Q         @0@.xdata             (Q  8Q         @0@.text           7   BQ  yQ          P`.pdata             僎  廞         @0@.xdata             璔              @0@.text           �   礠  aR          P`.pdata             賀  錜         @0@.xdata             S              @0@.rdata          (   S              @@@.text              3S  KS          P`.pdata             US  aS         @0@.xdata             S              @0@.text           �   嘢  vT          P`.pdata             銽  餞         @0@.xdata             U  U         @0@.pdata             <U  HU         @0@.xdata             fU  zU         @0@.pdata             楿           @0@.xdata             耈  諹         @0@.text             郩  黇          P`.pdata             腤  蠾         @0@.xdata             頦  X         @0@.pdata             $X  0X         @0@.xdata             NX  fX         @0@.pdata             刋  怷         @0@.xdata             甔  芚         @0@.pdata             鋁  餢         @0@.xdata             Y              @0@.rdata              Y              @@@.rdata          6   6Y              @@@.rdata          *   lY              @@@.text           �   朰  [Z          P`.pdata             [  [         @0@.xdata             9[  M[         @0@.pdata             k[  w[         @0@.xdata             昜           @0@.pdata             荹  覽         @0@.xdata             馵              @0@.rdata          .   鵞              @@@.rdata          -   '\              @@@.text           k   T\  縗          P`.pdata             鸤  ]         @0@.xdata             %]              @0@.rdata             1]              @@@.rdata             F]              @@@.text           R   U]            P`.pdata             蟏  踋         @0@.xdata             鵠              @0@.rdata          >   ^              @@@.rdata          /   C^              @@@.text           �   r^  N_          P`.pdata             瀇  猒         @0@.xdata             萠              @0@.rdata          +   郷              @@@.rdata             `              @@@.text           ,   #`  O`          P`.pdata             c`  o`         @0@.xdata             峘              @0@.text           x  昤  
l      ]    P`.pdata             痮  籵         @0@.xdata              賝  鵲         @0@.pdata             p  #p         @0@.xdata             Ap  Qp         @0@.pdata             op  {p         @0@.xdata              檖  筽         @0@.pdata             譸  鉷         @0@.xdata             q  q         @0@.rdata             #q              @P@.rdata             3q              @@@.text           �   ;q  /r          P`.pdata               硆         @0@.xdata             裷              @0@.rdata          '   賠              @@@.rdata              s              @@@.text             s  t      
    P`.pdata             梩           @0@.xdata             羣              @0@.text           �  誸  皏          P`.pdata             Zw  fw         @0@.xdata             剋  攚         @0@.pdata             瞱  緒         @0@.xdata             躻  靪         @0@.pdata             
x  x         @0@.xdata             4x  Hx         @0@.pdata             fx  rx         @0@.xdata             恱           @0@.pdata             聏  蝬         @0@.xdata             靫   y         @0@.text              
y  y          P`.pdata             1y  =y         @0@.xdata             [y              @0@.text           /   cy  抷          P`.pdata             皔  紋         @0@.xdata             趛              @0@.rdata             鈟              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   �   B     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\win32_window.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler H儁@ u儁 t儁 t�  �酶  �酶   喢H儁@ u儁 �   t�  竺H塡$E3跘嬝L嬕A���吷~0A嬌L嬍A婤A�+脵3�+翧;纝M嬞D嬂I兞I兟H�蓇貶媆$I嬅�    Q           
       
            4 @USVWATAUAVAWH峫$酘侅�   H�    H3腍塃L媦3鯠塂$<H孂塗$8D岶|H峂�3褽嬹H塼$0�    �塃媼G3慎厍E噟   D峟荅�   荅�   荅�  � 塃徢E� �  荅�   荅�   ��    L峀$0H峌嘐3繦嬋塼$(H嬝H塼$ �    H嬘3蒐嬭�    M呿uH�    �  �    3篱  媁�E嬏E嬆H塼$ �    L嬥H吚u!H�    �  �    I嬐�    3篱�   �疓吚~YH婰$0I峎�     禕�艸兟�禞麳婦$0圚禞鸋婦$0圚禞﨟婦$0圚H婰$0�疓H兞H塋$0;饇�3繦峀$@H塂$@H塂$HH塂$PH塂$X婦$8D塼$@塂$D婦$<L塪$P塂$HL塴$X�    I嬐H嬝�    I嬏�    H呟u�  H�    E咑uH�    �    H嬅H婱H3惕    H伳�   A_A^A]A\_^[]�   1    L   3    �   0    �   /    �   .    �   -    �   *    �   )      (      *      %    �  $    �  %    �  %    �  #    �       �  *    
  2        "                            '
  
��	��p`0P    �          Win32: Failed to create cursor Win32: Failed to create icon Win32: Failed to create mask bitmap Win32: Failed to create RGBA bitmap @SWH冹HH�    H3腍塂$0H嫓$�   H嫾$�   D塋$,D嬍嬔3繢塂$(H峀$ E3繦塂$ �    D媆$(D+\$ 婦$,+D$$D��H婰$0H3惕    H兡H_[�
   1    F   =    j   2        u           6       6       <     �p0      0          H塡$WH冹P)t$@H�    H3腍塂$0fnq`fnAdH婣@I孁嬟A�   [�[荔^餒吚u9AtA�  H吚u9At�  �9At�  ���   �3繦峀$ E3繦塂$ H塂$(�    D媆$(婽$,D+\$ +T$$凔tg凔tb凔t]凔tX凔t0凔t+凔t凔uc婫+G+耭n�[荔Y企,�A脡G隑婫婳+A+胒n�[荔^企,�+�+蕢O�婫+A+胒n�[荔^企,�G聣GH婰$0H3惕    H媆$`(t$@H兡P_�   1    �   =    1  2        E          @       @       F     h 
4 
�p    0          @SH冹@H�    H3腍塂$0H嬞H媺�  H峊$ �    H媼�  H峊$ �    H媼�  H峊$(�    H峀$ �    H婰$0H3惕    H兡@[�	   1    &   S    8   R    J   R    U   Q    b   2        l           J       J       P     r0    0          伭�燑�凒w:H�    Hc翄寕    H�岣   酶  酶  酶�  酶�  酶�  �3烂                           ^       ]    H   \    L   [    P   Z    T   Y    X   X    \   W    H塡$WH冹 3�峅�    咳�   髁   �岺E�    f吚y兿�   �    f吚y兿筡   �    筟   控�    咳藋兿嬊H媆$0H兡 _�   h    +   h    >   h    Q   h    _   h        z           a       a       g    
 
4 
2pH塡$WH冹 3�峅�    咳�   髁   �岺E�    f吚y兿�   �    f吚y兿筡   �    筟   控�    咳藋兿嬊H媆$0H兡 _�   r    +   r    >   r    Q   r    _   r        z           k       k       q    
 
4 
2pH冹hH凒厴   H衡s
竃  H兡h肏塡$`�    H峀$0E3蒃3�3覌厍D$     �    吚tL婰$8侚   t侚  t侚  t侚  u(H億$@u H鱀$H   t9\$Pu羹���H媆$`H兡h酶U  H媆$`H兡h肏陵H��  佲�  �PH兡h�&   �    C   �    �   �    �   �           u       u       {    !                 u       u       �    �   �           u       u       �    !   4               u       u       �       �           u       u       �    ! 4               u       u       �                   u       u       �     �  H塡$WH冹pH�    H3腍塂$`H孂H峇(H婭@�    H婳@H峊$H嬝�    H婳@L岲$DH峊$@�    婰$LD婰$DD婦$@荄$0  塋$(婰$HH兪�塋$ H嫃�  �    H婳@H嬜�    嬅H婰$`H3惕    H嫓$�   H兡p_�
   1    %   �    5   �    H   �    {   �    �   �    �   2        �           �       �       �     
4 
�p    `          @SH冹 H嬞H婭@H9Yu3诣    H婯@�    H兡 [�   �       �        )           �       �       �     20@SUVWAVH侅�   H�    H3腍墑$�   H孂H婭@H嬺H吷u9Ot�  �9Ot�  ���   喗   H吷u9Ot�  E3鯨墹$�   L壃$�   H吷t)L岲$dH峊$`�    H婳@H峊$h�    D媎$hD媗$l隨荄$`   �荄$d   �D9r(t弘�H峀$hD嬐塂$p婤E3缷覮塼$h塂$t�    D媎$pD媗$tD+d$hD+l$lH婲�    H嬸H吚uH�    �  �    3篱�   3�    L塼$XH�    H塂$P婦$dL塼$HL塼$@D塴$8D塪$0塂$(婦$`D嬎L嬈嬐塂$ �    H嬑H墖�  �    H嫃�  H吷uH�    �  �    3离xH�    L嬊�    H��  H吚tEH嫃�  E3珊3  E岮�蠬嫃�  E3葾峇JE岮��  H嫃�  E3葾峇IE岮��  H嫃�  �   �    �   L嫭$�   L嫟$�   H媽$�   H3惕    H伳�   A^_^][�   1    �   �    �   �    �   =    �   �      �      *      �    +  �    b  �    r  �    �  �    �  *    �  �    �  �    �  �    �  �    �  �      �    +  2        =          �       �       �    q q� i�  �p`P0      �          G L F W   Win32: Failed to create window G L F W 3 0   Win32: Failed to convert window title to UTF-16 @SH冹 H嬞H媺�  H吷t%H�    �    H媼�  �    H莾�      H兡 [�   �       �    +   �        @           �       �       �     20H冹(3�    H�
    H嬓H兡(H�%       �       �       �        !           �       �       �     B  @SH冹 H嬞H婭@H吷tH9Yu3诣    H婯@�    兓�   tH嬎�    H媼�  H吷t%H�    �    H媼�  �    H莾�      H媼�  H吷t�    H媼�  H吷t�    H兡 [�   �    $   �    5   �    H   �    N   �    [   �    x   �    �   �        �           �       �       �     20H塡$WH冹 H孂H嬍�    H嬝H吚uH�    �  H媆$0H兡 _�    H嫃�  H嬓�    H嬎H媆$0H兡 _H�%       �        �    4   *    D   �    X   �        \           �       �       �    
 
4 
2p@SVWH冹 H塴$@I嬝H孂嬯呉�  L塪$H�   L塴$PL塼$X�    �   D嬥�    E3砭���D嬝E嬽D嬛呿~>EL嬅H嬎D嬐f�     A婡A� A+脵3�+翧;聖L嬹D嬓H兞I兝I�蓇毓2   �    �1   D嬥�    D嬝呿~.EL嬅L嬐婥�A+脵3�+�;苶M嬭嬸I兝H兠I�蓇蹵�   E3�3襂嬑�    A�   E3�3襂嬐H嬝�    L媡$XL媗$PL媎$H�'H媺�  候����    H嫃�  恨���H嬝�    H嫃�  簚   L嬎D岯丠嬸�    H嫃�  L嬑E3篮�   �    H嫃�  H吷t�    H嫃�  H吷t�    呿H媗$@tH墴�  H壏�  H兡 _^[�3      A      �      �      �             .     C     _     w     �  �    �  �    �  �                      �    !                                   �                      �    !                                                            ! � �
 �	                                                          ! T                                                             2p`0H塡$WH冹 H媺�  H孃3繦峊$0I嬝H塂$0�    H�t婦$0�H呟t婦$4�H媆$8H兡 _�%   R        J                          
 
4 
2p@SH冹`H�    H3腍塂$PH婣@H嬞塗$@D塂$D塗$HD塂$LA�   H吚u9AtA�  H吚u9At�  �9At�  ���   咹峀$@E3�    D婰$DD婦$@H媼�  3狼D$0   塂$(3覊D$ �    H婰$PH3惕    H兡`[�	   1    n   =    �   �    �   2        �           )      )      (    �0    P          H塡$ WH冹@H�    H3腍塂$0H媺�  H孃H峊$ I嬝�    H�t婦$(�H呟t婦$,�H婰$0H3惕    H媆$hH兡@_�
   1    -   S    P   2        _           3      3      2    
4
 
rp    0          @SH冹`H�    H3腍塂$PH婣@H嬞H吚t"H9H厹   �    H婰$PH3惕    H兡`[脙y H塂$@A�   �  塗$HD塂$LDE葍y t儁 �  �t�  ���   咹峀$@E3�    D媆$L婦$HD+\$D+D$@H媼�  荄$0  D塡$(E3蒃3�3覊D$ �    H婰$PH3惕    H兡`[�	   1    ,   �    9   2    �   =    �   �    �   2        �           =      =      <    �0    P          @SH冹PH�    H3腍塂$@H嬞凓�tA凐�uA凒�tO兗$�   �tEH媺�  H峊$0�    D媆$<D婦$4D婰$8婽$0H媼�  E+谼+是D$(   D塡$ �    H婰$@H3惕    H兡P[�	   1    A   H   t   G   �   2        �           I      I      F    �0    @          凓��  SH冹pH�    H3腍塂$PH嬞A凐�勦   H媺�  H峊$@)t$`�    H婥@A�   fns`fnCd[�[荔^餒吚u9CtA�  H吚u9Ct�  �9Ct�  ���   �3繦峀$0E3繦塂$0H塂$8�    婽$@D婰$HD媆$0D+\$8D婦$DH媼�  D+谇D$(   E貲+蔲An�[荔^企,�+D$4D$<A缐D$LA+缐D$ �    (t$`H婰$PH3惕    H兡p[竺   1    =   H   �   =      G     2    
            _      _      R   !       6          _      _      ^   6   
          _      _      X   ! h     6          _      _      ^       6           _      _      ^    �
0    P          H塡$ WH冹@H�    H3腍塂$0H媺�  H孃H峊$ I嬝�    H�t婦$(�H呟t婦$,�H婰$0H3惕    H媆$hH兡@_�
   1    -   S    P   2        _           i      i      h    
4
 
rp    0          @SUVWATAUH冹hH�    H3腍塂$PH嫾$�   H嬞H媺�  L嬧H峊$@I嬹I嬭�    D媗$LD婰$HH峀$0E3�3褼塴$ �    H婥@A�   H吚u9CtA�  H吚u9Ct�  �9Ct�  ���   咹峀$0E3�    M呬t
婦$0髫A�$H呿t	婦$4髫塃 H咑t
婦$8+D$H�H�t	婦$<A+艍H婰$PH3惕    H兡hA]A\_^][�   1    >   S    ]   s   �   =    �   2        �           t      t      r    
�	��p`P0      P          H媺�  �   H�%       x   H媺�  �	   H�%       x   H媺�  �   H�%       x   H媺�  �   H�%       x   H媺�  3襀�%       x   @SH冹 H嬞H媺�  �    H媼�  �    H媼�  H兡 [H�%       �      �   2   �       6           �      �      �    20@USVWATH嬱H侅�   H�    H3腍塃郋嬓D塃腖婣@H嬺H嬞D塎繪;�叿   H呉tH9J厭  �    閳  婨PD塎藾塙華�婨X塎蠥�A�   塎訫吚uD9CtA�  M吚uD9Ct�  �D9Ct�  ���   咹峂菶3缷�    D媇訢婱虌E蠨婨菻媼�  E+貯+狼D$0  D塡$(3覊D$ �    殛  M吚tI9Hu3襂嬋�    H婯@�    H嬛H嬎�    H咑劤   H媼�  吼����    儃 縋  tB%��0�H儃@ t�   嗠儃 �  �A�  �AE麳媼�  ���D嬊�    縫  H峌菻嬑�    L岴繦峌腍嬑�    婨藾婱繢婨腍媼�  墊$0塂$(婨菻兪�塂$ �    H嬎�    �  婱膵U缷EX塎�MP塙虊M袓吼���塎訦媼�  L塴$x�    儃 �   咥�  A�  �t9吼H儃@ tD嬊�儃 A�  �EE腍媼�  D篮����    A�0  婥H婯@3鰠繟�   @暺H吷u吚tA�  H吷u9Kt
�  �9KtA孅H峂菶3缷�    D媇訢婱虌E蠨婨菻媼�  E+貯+繢塴$0D塡$(H峍D$ �    L媗$xH婱郒3惕    H伳�   A\_^[]�   1    M   �    �   =    �   �      �      �      �   4  �   |  �   �  �    �  �    �  �    �  �      �   Q  �   �  =    �  �    �  2    �  �          �      �      �   !                 �      �      �      �          �      �      �   ! �               �      �      �                  �      �      �    ��p`0P    `          @SH冹 H嬞�    3襀9兝  斅嬄H兡 [�   �       #           �      �      �    20H媺�  H�%    
   �   H媺�  H�%    
   �   H媺�  H�%    
   �   H�
�   E3蒃3繦媺�  3襀�%       �       �   H塡$H塼$WH冹 H嬹H峀$HI嬝H孃�    吚t8H嫀�  H峊$H�    H�tfnD$H�胬�H呟tfnD$L�胬�H媆$0H媡$8H兡 _�   �   5   �       o           �      �      �    d 4 2pH冹(�,羊,聣戉  墎�  H媺�  塗$0H峊$0塂$4�    婽$4婰$0�    H兡(�.   R    <   �       E           �      �      �    B  H塡$WH冹 孃H嬞侜@ u�    �3�    H峀$@�    吚tcH婰$@�    H;兝  uO�@ u?H婯HH吷tH婭�    H媆$0H兡 _煤   3�    H嬋�    H媆$0H兡 _�3�    H媆$0H兡 _�   J    "   Q    -   �   <   �   `   �   x   �   �   �   �   �       �           �      �      �   
 
4 
2p@SH冹pH�    H3腍塂$`嬟H�    凒�tHc�繙B�  Hc�繉B�  �    吚u3繦婰$`H3惕    H兡p[昧�H峊$@A�   嬎�    吚t�3繦�`  L岲$@H塂$8H塂$0A兩�3夜辇  荄$(@   H塡$ �    吚t桯嬅H婰$`H3惕    H兡p[�	   1       �    :   �   M   2    i   �   v   �    �   �   �   2        �           �      �      �    �0    `          @SH冹 A嬃E嬓L嬟H嬞E3蒁嬂A嬕I嬎�    3蒆吚H塁暳嬃H兡 [�           7                         �    20@SH冹 伮�燑�H嬞凓w@H�
    Hc聥攣    H�夂   �%�  ��  �簤  �簞  �	簠  �3�3�    H嬋�    H塁H吚uH�    �  �    3繦兡 [酶   H兡 [�                           ^    !      Z   �   c   
   s      }   *    �      �      �      �      �      �          �                       	    20Win32: Failed to create standard cursor H冹(H婭H吷t�    H兡(�   �                                   B  H塡$ WH冹@H�    H3腍塂$8H9
�   H孃H嬞叓   伖�   @ 厸   H峀$ �    吚剤   H婰$ H塼$`H嫵�  �    H;苪gH峊$(H嬑�    H媼�  H峊$(�    H媼�  H峊$0�    H婽$ H峀$(�    吚t!H�tH婳��   3�    H嬋�    H媡$`H婰$8H3惕    H媆$hH兡@_�
   1       �    C   �   b   �   u   S    �   R    �   R    �   5   �   �   �   �   �   2    �   �           6      6      (   !       T          6      6      4   T   �           6      6      .   ! d     T          6      6      4       T           6      6      4    
4
 
rp    8          @SH冹 H嬍�    H嬝H吚uH�    �  H兡 [�    H兩�3繦塼$0H墊$8H孄f虔H餮H�4	岺H嬛�    H孁H吚u)H嬎�    H�    �  H媡$0H媩$8H兡 [�    H嬋�    L嬈H嬘H嬋�    H嬒�    H�
H  �    吚u2H嬒�    H嬎�    H�    �  H媡$0H媩$8H兡 [�    �    H嬜�
   �    �    H嬎H媡$0H媩$8H兡 [H�%    
   �       b   (   *    Q   _   b   �    i   ^   �   *    �   [   �   c   �   Z   �   �    �   Y   �   X   �   �    �   W   �   *    �   T   �   S   �   R     �    �             d      d      ?   !   t  d     2          d      d      Q   �   �           d      d      E   !   t  d     2          d      d      Q   2   �           d      d      K   !
 
t d     2          d      d      Q       2           d      d      Q    20Win32: Failed to open clipboard Win32: Failed to allocate global handle for clipboard Win32: Failed to convert string to UTF-16 H冹(H�
H  �    吚uH�    �  �    3繦兡(霉
   H塡$ �    H嬝H吚u#�    H�    �	  �    H媆$ 3繦兡(肏�
X  �    H嬎�    H嬋�    H嬎H�X  �    �    H�X  H吚uH�    �  �    3繦媆$ H兡(�   �    
   Y      W   "   *    9   �   G   R   N   �   X   *    k   �    q   �    z   [   �   }   �   �    �   Z   �   R   �   �    �   |   �   *    h   �           �      �      m   !   4     2          �      �      y   2   h           �      �      s   ! 4     2          �      �      y       2           �      �      y    B  Win32: Failed to convert wide string to UTF-8 Win32: Failed to convert clipboard to string @WH冹 �    �=$   H孂u3繦兡 _煤   H塡$0岼�    H�
    H嬝�    H�
    H��    �   H塁H嬅H媆$0H兡 _�   �    /   �   6   �   ?   �   F   �   O   �       k           �      �      �   * *4 2pVK_KHR_win32_surface VK_KHR_surface H塡$WH冹 H孃H�    A嬝�  H吚uH�    �  �    3繦媆$0H兡 _脣親嬒H媆$0H兡 _H��   �      �    %   �   /   *        R           �      �      �   
 
4 
2pWin32: Vulkan instance missing VK_KHR_win32_surface extension vkGetPhysicalDeviceWin32PresentationSupportKHR H塡$H塴$H塼$H墊$ ATH冹PL嬧H�    I嬹I嬭H嬞�  H孁H吚uH�    �  �    岹p3�3蒆塂$ 荄$ (須;H塂$(H塂$0H塂$8H塂$@�    H峊$ L嬑H塂$8I媱$�  L嬇H嬎H塂$@�讒貐纓嬋�    H�    �  L嬂�    嬅H媆$`H媗$hH媡$pH媩$xH兡PA\�    �   /   �    >   �   H   *    x   �    �   �   �   �   �   *        �           �      �      �   
 t d T
 4 �繵in32: Failed to create Vulkan surface: %s vkCreateWin32SurfaceKHR H冹(�=     u3夜  �    3繦兡(肏媮�  H兡(�   �      *        ,           �      �      �    B  @SVWATH侅�   H�    H3腍塂$x嬟H�    I孂M嬥H塋$H�    H嬸H塂$`H吚ug侞  uGI凕u
�    岶�!  I侟 �  uH�t&�u �    �I侟�  uH�t�u�    H婰$HL嬒M嬆嬘�    檎  H壃$�   L壃$�   L壌$�   L壖$�   凔�  匁  嬅冭劙  冭勷   冭剶   ��匩  伨�   @ uP3�    H峀$@�    吚t9H婰$@�    H;喞  u%H婲HH吷tH婭��   3�    H嬋�    H儈@ t儈 tH嫀�  �   �    3襀嬑�    3篱�  伨�   @ u3H嬑�    H峀$@�    吚tH婰$@�    H;喞  u3�    �   H嬑�    3篱�  H95�   u伨�   @ uH嬑�    媶�  吚u7I凕u-H婲@D墻�  H吷tH9qu3诣    H婲@�    �   �'吚t+M呬tI凕u 3韷  H9n@tH嬑�    3襀嬑�    H嬊�H嬑H凌嬜坟D嬅�    D嬅嬜H嬑�    3篱�  H95�   u伨�   @ uH嬑�    H嬊孔H嬑H凌D坷�    3篱�  H嬋�    閝  侞   嚦  勪  侞  �6  剼  岰�=�   �?  H�    秳    媽�    H�酘嬑�    3篱(  H儈@ �
  儈 �  A�   DE葍~ t儈 �  �t�  ���   �3鞨峀$`E3繦塴$`H塴$h�    D媆$h婽$l婩PD+\$`+T$d凐�t儈T�tA脡G婩T�塐婩X凐�t儈\�tA脡G 婩\�塐$3篱�  H95�   卄  f�匳  媶�   =@ t=@ tH婲HH吷�5  H婭�3�    �   �0  侞  �  侞  啇   侞  tJ侞	  tB侞  呴   I嬆%�  H- �  勴  H冭@t
H凐0吪   H儈@ 労   3篱�   3韥�  @暸侞	  u
I侟��  刴  �    D嬐A嬙D嬂H嬑�    3篱�   H嬜I嬏�    L嬶H嬶I笼H笼D孁A髡佸�  A冨�    D嬸A�>E呿厒   I凕u|E3蒁嬇篢  H嬑塂$ �    D塼$ E3蒁嬇篨  H嬑�    H婰$HL嬒M嬆嬘�    L嫶$�   L嫭$�   H嫭$�   L嫾$�   H婰$xH3惕    H伳�   A\_^[脡D$ D嬇A嬜H嬑I凕,uE峀$砧    D塼$ E3蒁嬇A嬜閧���E嬐関���H嬊匡H凌伨�   @ D胯u(H95�   匱���A嬇+嗕  fn袐�+嗋  fn入	fAn說n腕嬉�嫔H嬑�    兙�   壆�  D壆�  uQ3繦峀$`A�   H塂$`H塂$hH塂$pH媶�  荄$`   D塴$dH塂$h�    A峌�H嬑菃�     �    3篱毗��崈���=�   嚒��H�    秳    媽�    H��3韥�  tN侞  tF侞  t6侞  t.侞  t侞  tI领D嬳fA凕A暸A兣�A�   �A�   �D嬳侞  t 侞  t侞  t侞  t�    �H婰$H�   �    �    D嬇A嬚D嬋H嬑�    侞  劕   侞  劆   3篱妖��3�3襀嬑壆�  �    3篱庚��fW蒊领H嬑A磕fn畜嬉�^    �    3篱嶟��fW襂领H嬑A磕fn润嫔�^
    fW
    �    3篱Z��儈`��=��儈d��3��L嬊A嬙H嬑�    �   �.��婳D婳D�婫A+汕D$0   塋$(H嫀�  A+�3覊D$ �    殇��E3蒃3纼�I嬏�    �   Lc鴫D$HI嬒�    H峊$@I嬏L嬸�    H嬑fnT$DfnL$@�嬉�嫔�    3韷l$PM�~bD峬嬽 E3蒃3缷誌嬏�    I嬚峏嬎�    D嬎嬚L嬂I嬏H孁�    H嬒�    H嬒I��    H��臝;鱸瓾媡$`3韹T$HM嬈H嬑�    M�~ I��    H�臝;飢領嬑�    I嬏�    3篱酐���                         f�                                           1    !   �    2      R   
   s   	   �      �        Q      �   (  �   M  �   V  �   u  x        �  J    �  �   �  �   �  �   �     �  �    �  J    #  �    ,  �    X  �    b     |     �     �  �    �  J    �     �       ^            �   ,  �   �  =    �  �    !  �   �  a    �  �   �  u      a    0  �   H  �   [     �  2    �  �   �  �    (  �   y  �   �  �   �  ^    �  �   �  �   C  �   U  �   Z  a    k  �   �  �   �  �   �  �   �  �   �  �   �  �   #  @    _  �    v  �   �  �   �  �   �  �   �  �   �  �   �  �   	  }   	  �    4	  �   F	  �    W	  �    `	  �   l	  �   p	  �   t	  �   x	  �   |	  �   �	  �   x
  �   |
  �   �
  �   �
  �   �
  �   �
  �   �
  �   �
  �   �  x          �      �      �   !   �  �  �  T     �          �      �      �     �          �      �      �   !       �          �      �      �   �             �      �      �   !   � � � T     �          �      �      �       �           �      �      �    
 �p`0    x                 �       �      ^@H侅�   3襀峀$0D岯P�    H�    3汕D$0P   荄$4#   H塂$8�    �   3蒆塂$H�    3蒆塂$XH�    H塂$p�    E3蒆�    E岮H嬋荄$(@�  荄$     �    H塂$PH吚u%D岪E3珊   3汕D$(@�  塂$ �    H塂$PH峀$0�    f吚uH�    �  �    3繦伳�   酶   H伳�   �   3       �   7   �    I   �   W   �    b   �    l      �      �      �      �      �   *        �                            Win32: Failed to register window class G L F W _ I C O N   H塡$H塴$H塼$WH冹 I嬹I孁H嬯H嬞�    吚u3篱�   �? tbL嬈H嬜H嬎�    吚t釲嬈H嬜H嬎�    凐t蟽�u83设    H嬎�    H嬎�    H嬚H嬎�    吚t嬈H嬜H嬎�    吚t扝儃@ tIH媼�  �   �    H媼�  �    H媼�  �    H媼�  �    H嬎�    吚凚����   H媆$0H媗$8H媡$@H兡 _�!   �    ?   )   Q   (   b   '   j   �    r   �    }   �    �   )   �   x   �   �   �   �   �   �   �   �                  *      *      &    d T 4 2pH塡$WH侅�   H�    H3腍塂$pH峀$0E3蒃3�3仪D$    �    吚t_D  億$8uH��   H呟t(H嬎�    H�H呟u痣H峀$0�    H峀$0�    H峀$0E3蒃3�3仪D$    �    吚u�=�   H��  H壃$�   H壌$�   �    範   嬭�    埂   坟岭�    佛令呟u �苦  uE3蒃3篮T  H嬒塴$ �    咑H嫶$�   u �夸  uE3蒃3篮X  H嬒塴$ �    伩�   @ H嫭$�   uvH嫃�  H峊$`�    婦$h婰$l�+卵鳧嬂9囙  u嬃�+卵�9囦  t=嬃H嫃�  D塂$`橠墖�  +翲峊$`养塂$d墖�  �    婽$d婰$`�    H婰$pH3惕    H嫓$�   H伳�   _�   1    3   �    J   �    W   �   l   M   w   L   �   �    �   �    �   k    �   r    �   r      �   /  �   U  S    �  R    �  �   �  2    G  �          N      N      3   !       �          N      N      K     G          N      N      9   !   �   �          N      N      E   �             N      N      ?   ! d �   �          N      N      E   �   �           N      N      E   ! T     �          N      N      K       �           N      N      K    
4 
�p    p          H冹(�    H兡(�       X      -                  Y      Y      W    B  H冹8�Y    E3�3�3汕D$ �  騆,�    H兡8�       f   "   c   +   -       /           g      g      b    b       @廆@comp.idov� ��   .drectve       s                 .debug$S       �                 .text          %       7榌d                    .text                 T1秒                    .text          Q       Υ=n         $           .pdata               X髮�        0           .xdata                
        C           .text          "     �	�         W           .pdata      	         t�,        b       	    .xdata      
          罇Yv        t       
        �            .rdata                冬q         �           .rdata                v貚         �                              '           .rdata      
   $       [�'o         :      
        u               �           .rdata         $       伷]�         �              �               �               �                                         memset           .text          u      鐫<�         0          .pdata               魺颁        B          .xdata               坒u�        [              u           .text          E     >'密         �          .pdata               =�
�        �          .xdata               揎頨        �          _fltused         .text          l      ��         �          .pdata               舻D�        �          .xdata               祍K>        �                                            2           .text          `      竮稁         F          $LN1    ?       $LN2    9       $LN3    3       $LN4    -       $LN5    '       $LN6    !       $LN11   H           [           .text          z      
h踓         g          .pdata               X崘=        r          .xdata                %蚘%        �              �           .text          z      
h踓         �          .pdata               X崘=        �          .xdata                %蚘%        �              �           .text          �      �*
�         �          .pdata                蟅m�                   .xdata      !         k商        "      !    .pdata      "         槄et        8      "    .xdata      #         講        N      #    .pdata      $         e仙[        d      $    .xdata      %         z&)�        z      %    .pdata      &         �#洢        �      &    .xdata      '          Q"痤        �      '    _glfw                �               �           .text       (   �      bMD         �      (    .pdata      )         9y(        �      )    .xdata      *         i欖(              *                       ;               N               i               �           .text       +   )      `� �         �      +    .pdata      ,         }y9�+        �      ,    .xdata      -          （亵+        �      -        �           .text       .   =     矤�	         �      .    .pdata      /         匴W�.        �      /    .xdata      0   $      k瞶�.              0        '               =           .rdata      1   
       弽         L      1    .rdata      2          �-��         {      2        �               �           .rdata      3          讧煰         �      3                   .rdata      4   0       蚰�.         )      4        d           .text       5   @      -�0         �      5    .pdata      6         砺�)5        �      6    .xdata      7          （亵5        �      7        �               �           .text       8   !      .t
�         �      8    .pdata      9         萣�58              9    .xdata      :          �9�8        -      :        U           $LN3        8    .text       ;   �      sJ         l      ;    .pdata      <         菏�;        �      <    .xdata      =          （亵;        �      =        �               �           $LN13       ;    .text       >   \      ��         �      >    .pdata      ?         夋�>        	      ?    .xdata      @          %蚘%>        4	      @        X	           $LN4        >    .text       A   �     8鐏�         m	      A    .pdata      B         �贏        �	      B    .xdata      C         Y癫jA        �	      C    .pdata      D         刁`A        �	      D    .xdata      E         韪錢A        �	      E    .pdata      F         饏锻A        
      F    .xdata      G         瑔恼A        <
      G    .pdata      H         R册A        `
      H    .xdata      I         霸炮A        �
      I    .pdata      J         �逵A        �
      J    .xdata      K          �6Z鐰        �
      K        �
                                          $LN25       A    .text       L   J      劏         .      L    .pdata      M         %轢窵        H      M    .xdata      N          �頛        i      N    $LN5        L    .text       O   �      )Ic"         �      O    .pdata      P         е鲋O        �      P    .xdata      Q         屁膜O        �      Q    $LN14       O    .text       R   _      ��9�         �      R    .pdata      S         j�孯              S    .xdata      T         畾R        %      T    $LN5        R    .text       U   �      懤S         H      U    .pdata      V         詊輻U        c      V    .xdata      W         屁膜U        �      W    $LN17       U    .text       X   �      1!呛         �      X    .pdata      Y         寵QX        �      Y    .xdata      Z         巔ArX        �      Z        
               +
           $LN6        X    .text       [        ~\阵         ?
      [    .pdata      \         �鋄        a
      \    .xdata      ]         磅鶾        �
      ]    .pdata      ^         槃Ｌ[        �
      ^    .xdata      _         J菿[        �
      _    .pdata      `         鶽[        
      `    .xdata      a         s腂.[        6      a    $LN29       [    .text       b   _      ��9�         `      b    .pdata      c         j�宐        �      c    .xdata      d         畾b        �      d    $LN7        b    .text       e   �      F��         �      e    .pdata      f         �nde        �      f    .xdata      g         辍抏              g        >           $LN23       e    .text       h         埵G�         L      h        g           .text       i         N>縤         x      i    .text       j         蔲�         �      j    .text       k         壃         �      k    .text       l         「b�         �      l    .text       m   6      �         �      m    .pdata      n         鶽m        �      n    .xdata      o          （亵m              o        9               H               b           $LN3        m    .text       p   �     鼚劳         y      p    .pdata      q         錋餠p        �      q    .xdata      r         披�.p        �      r    .pdata      s         铺d躳        �      s    .xdata      t         W匁祊              t    .pdata      u         )煑p        3      u    .xdata      v         7橇Ep        X      v        ~               �               �           $LN53       p    .text       w   #      K暀�         �      w    .pdata      x         礶鵺w        �      x    .xdata      y          （亵w              y        &           $LN3        w    .text       z         殛p�         <      z        Y           .text       {         殛p�         h      {        �           .text       |         殛p�         �      |        �           .text       }         W湌V         �      }        �           .text       ~   o      �/F�         �      ~    .pdata               菜	~                  .xdata      �          O韣        /      �        Q               f           $LN6        ~    .text       �   E      -d剏         y      �    .pdata      �         壧}a�        �      �    .xdata      �          �9��        �      �        �           $LN3        �    .text       �   �      � �.         �      �    .pdata      �         o嗦$�              �    .xdata      �          %蚘%�        &      �        I               [               k           $LN12       �    .text       �   �      悤馻         �      �    .pdata      �         〨秶        �      �    .xdata      �         _�;�        �      �        �               �                          $LN8        �    .text       �   7      @敇               �    .pdata      �         dZ強        3      �    .xdata      �          （亵�        T      �    $LN4        �    .text       �   �      頬�         v      �    .pdata      �         抿恺�        �      �    .xdata      �          （亵�        �      �    .rdata      �   (       3T�         �      �        %           $LN4    M   �    $LN5    F   �    $LN6    ?   �    $LN7    8   �    $LN8    1   �    $LN9    *   �    $LN14   �   �    $LN15       �    .text       �         �鄢         4      �    .pdata      �         �*^瘧        O      �    .xdata      �          �9��        q      �    $LN4        �    .text       �   �      �5鵑         �      �    .pdata      �         詒�        �      �    .xdata      �         �;h�        �      �    .pdata      �         v�3�        �      �    .xdata      �         �G
�              �    .pdata      �         <齦褦        +      �    .xdata      �         JY_�        I      �        h           $LN12       �    .text       �        ��         w      �    .pdata      �         �_X�        �      �    .xdata      �         蹜菠�        �      �    .pdata      �         J鬳�        �      �    .xdata      �         蹜菠�              �    .pdata      �         �$Fm�        ;      �    .xdata      �         <|裠�        d      �    .pdata      �          T枨�        �      �    .xdata      �          （亵�        �      �        �               �                          .rdata      �           跃谢               �        Z               k                              �           .rdata      �   6       S噂�         �      �        �           .rdata      �   *       H
�         �      �    memcpy           $LN8        �    .text       �   �      8择          *      �    .pdata      �         �        J      �    .xdata      �         g锈�        s      �    .pdata      �         �        �      �    .xdata      �         薕Η        �      �    .pdata      �          T枨�        �      �    .xdata      �          �9��              �    .rdata      �   .       掮�>         =      �        x           .rdata      �   -       t歽o         �      �        �           $LN8        �    .text       �   k      2箄�         �      �    .pdata      �         砑亶�              �    .xdata      �          Z	t�        I      �    .rdata      �          *y讗         |      �        �           .rdata      �          ;         �      �        �           $LN4        �    .text       �   R      f7�         �      �    .pdata      �         霍�              �    .xdata      �          %蚘%�        U      �    .rdata      �   >       穣�         �      �    .rdata      �   /       :�?D         �      �    $LN4        �    .text       �   �      ��         �      �    .pdata      �         Sc睶�              �    .xdata      �          a幚`�        G      �    .rdata      �   +       �⒕         p      �        �           .rdata      �          V��         �      �    $LN5        �    .text       �   ,      u�         �      �    .pdata      �         w佼�        	      �    .xdata      �          �9��        #      �        >           $LN4        �    .text       �   x  ]   �zo         O      �    .pdata      �         蕮        Z      �    .xdata      �          @8#�        n      �    .pdata      �         �2~E�        �      �    .xdata      �         �趥�        �      �    .pdata      �         怵壍�        �      �    .xdata      �          h迅,�        �      �    .pdata      �         D麔;�        �      �    .xdata      �         >n#�        �      �    $LN82   L  �        �                                               ,            $LN7    h  �    $LN8    1  �    $LN44   '  �    $LN23     �    .rdata      �          o冺�         A       �    $LN36   �  �        T            .rdata      �          J1銻         e       �    $LN37   �  �    $LN38   �  �        }                �                �            $LN60   �  �    $LN145  x
  �    $LN144  �
  �        �                �                �                �            $LN66   �  �        	!           $LN14   �  �    $LN20   7  �        !           $LN69   (  �    $LN147  l	  �    $LN146  �	  �        5!               L!               `!               u!               �!               �!               �!               �!               �!               "               3"           .text       �   �      伝         B"      �    .pdata      �         �!{�        `"      �    .xdata      �          咁	嬐        �"      �    .rdata      �   '       �         �"      �        �"               �"           .rdata      �          JvC         
#      �    $LN5        �    .text       �     
   �%鉹         W#      �    .pdata      �         *輏�        q#      �    .xdata      �          嘋c粢        �#      �        �#               �#               �#           $LN17       �    .text       �   �     �=dO         $      �    .pdata      �         A鄒x�        $      �    .xdata      �         �趥�        :$      �    .pdata      �         � '?�        [$      �    .xdata      �         OgE空        |$      �    .pdata      �         鷸陶        �$      �    .xdata      �         �6~哉        �$      �    .pdata      �         �        �$      �    .xdata      �         � I刚         %      �    .pdata      �         D麔;�        !%      �    .xdata      �         kCT	�        @%      �        `%               w%           $LN25       �    .text       �         厶�         �%      �    .pdata      �         �64蜞        �%      �    .xdata      �          �9��        �%      �        �%           $LN3        �    .text       �   /      n訍         �%      �    .pdata      �         鷓V �        &      �    .xdata      �          1�7�        <&      �        c&           .rdata      �          訦]�         �&      �    $LN3        �    �&  getWindowStyle getWindowExStyle chooseImage $pdata$chooseImage $unwind$chooseImage createIcon $pdata$createIcon $unwind$createIcon __GSHandlerCheck ??_C@_0BP@OHFKOFFD@Win32?3?5Failed?5to?5create?5cursor?$AA@ ??_C@_0BN@HJHDCKKP@Win32?3?5Failed?5to?5create?5icon?$AA@ __imp_CreateIconIndirect __imp_DeleteObject ??_C@_0CE@PKGOIGHB@Win32?3?5Failed?5to?5create?5mask?5bit@ __imp_CreateBitmap _glfwInputError ??_C@_0CE@GCBEILKL@Win32?3?5Failed?5to?5create?5RGBA?5bit@ __imp_ReleaseDC __imp_CreateDIBSection __imp_GetDC __security_cookie __security_check_cookie getFullWindowSize $pdata$getFullWindowSize $unwind$getFullWindowSize __imp_AdjustWindowRectEx applyAspectRatio $pdata$applyAspectRatio $unwind$applyAspectRatio updateClipRect $pdata$updateClipRect $unwind$updateClipRect __imp_ClipCursor __imp_ClientToScreen __imp_GetClientRect translateCursorShape __ImageBase getKeyMods $pdata$getKeyMods $unwind$getKeyMods __imp_GetKeyState getAsyncKeyMods $pdata$getAsyncKeyMods $unwind$getAsyncKeyMods __imp_GetAsyncKeyState translateKey $pdata$3$translateKey $chain$3$translateKey $pdata$2$translateKey $chain$2$translateKey $pdata$0$translateKey $chain$0$translateKey $pdata$translateKey $unwind$translateKey __imp_PeekMessageW __imp_GetMessageTime acquireMonitor $pdata$acquireMonitor $unwind$acquireMonitor _glfwInputMonitorWindowChange __imp_SetWindowPos _glfwPlatformGetMonitorPos _glfwPlatformGetVideoMode _glfwSetVideoModeWin32 releaseMonitor $pdata$releaseMonitor $unwind$releaseMonitor _glfwRestoreVideoModeWin32 createWindow $pdata$createWindow $unwind$createWindow __imp_DragAcceptFiles __imp_SetPropW ??_C@_19JBPKICJI@?$AAG?$AAL?$AAF?$AAW?$AA?$AA@ ??_C@_0BP@BFHGHOIO@Win32?3?5Failed?5to?5create?5window?$AA@ __imp_free __imp_CreateWindowExW ??_C@_1O@IBNLCBOC@?$AAG?$AAL?$AAF?$AAW?$AA3?$AA0?$AA?$AA@ __imp_GetModuleHandleW ??_C@_0DA@CDIFIIJI@Win32?3?5Failed?5to?5convert?5window?5@ _glfwCreateWideStringFromUTF8Win32 destroyWindow $pdata$destroyWindow $unwind$destroyWindow __imp_DestroyWindow __imp_RemovePropW _glfwUnregisterWindowClassWin32 $pdata$_glfwUnregisterWindowClassWin32 $unwind$_glfwUnregisterWindowClassWin32 __imp_UnregisterClassW _glfwPlatformDestroyWindow $pdata$_glfwPlatformDestroyWindow $unwind$_glfwPlatformDestroyWindow __imp_DestroyIcon _glfwDestroyContextWGL _glfwPlatformSetWindowTitle $pdata$_glfwPlatformSetWindowTitle $unwind$_glfwPlatformSetWindowTitle __imp_SetWindowTextW _glfwPlatformSetWindowIcon $pdata$5$_glfwPlatformSetWindowIcon $chain$5$_glfwPlatformSetWindowIcon $pdata$4$_glfwPlatformSetWindowIcon $chain$4$_glfwPlatformSetWindowIcon $pdata$3$_glfwPlatformSetWindowIcon $chain$3$_glfwPlatformSetWindowIcon $pdata$0$_glfwPlatformSetWindowIcon $chain$0$_glfwPlatformSetWindowIcon $pdata$_glfwPlatformSetWindowIcon $unwind$_glfwPlatformSetWindowIcon __imp_SendMessageW __imp_GetClassLongPtrW __imp_GetSystemMetrics _glfwPlatformGetWindowPos $pdata$_glfwPlatformGetWindowPos $unwind$_glfwPlatformGetWindowPos _glfwPlatformSetWindowPos $pdata$_glfwPlatformSetWindowPos $unwind$_glfwPlatformSetWindowPos _glfwPlatformGetWindowSize $pdata$_glfwPlatformGetWindowSize $unwind$_glfwPlatformGetWindowSize _glfwPlatformSetWindowSize $pdata$_glfwPlatformSetWindowSize $unwind$_glfwPlatformSetWindowSize _glfwPlatformSetWindowSizeLimits $pdata$_glfwPlatformSetWindowSizeLimits $unwind$_glfwPlatformSetWindowSizeLimits __imp_MoveWindow __imp_GetWindowRect _glfwPlatformSetWindowAspectRatio $pdata$1$_glfwPlatformSetWindowAspectRatio $chain$1$_glfwPlatformSetWindowAspectRatio $pdata$0$_glfwPlatformSetWindowAspectRatio $chain$0$_glfwPlatformSetWindowAspectRatio $pdata$_glfwPlatformSetWindowAspectRatio $unwind$_glfwPlatformSetWindowAspectRatio _glfwPlatformGetFramebufferSize $pdata$_glfwPlatformGetFramebufferSize $unwind$_glfwPlatformGetFramebufferSize _glfwPlatformGetWindowFrameSize $pdata$_glfwPlatformGetWindowFrameSize $unwind$_glfwPlatformGetWindowFrameSize __imp_SetRect _glfwPlatformIconifyWindow __imp_ShowWindow _glfwPlatformRestoreWindow _glfwPlatformMaximizeWindow _glfwPlatformShowWindow _glfwPlatformHideWindow _glfwPlatformFocusWindow $pdata$_glfwPlatformFocusWindow $unwind$_glfwPlatformFocusWindow __imp_SetFocus __imp_SetForegroundWindow __imp_BringWindowToTop _glfwPlatformSetWindowMonitor $pdata$1$_glfwPlatformSetWindowMonitor $chain$1$_glfwPlatformSetWindowMonitor $pdata$0$_glfwPlatformSetWindowMonitor $chain$0$_glfwPlatformSetWindowMonitor $pdata$_glfwPlatformSetWindowMonitor $unwind$_glfwPlatformSetWindowMonitor __imp_SetWindowLongW __imp_GetWindowLongW _glfwInputWindowMonitorChange _glfwPlatformWindowFocused $pdata$_glfwPlatformWindowFocused $unwind$_glfwPlatformWindowFocused __imp_GetActiveWindow _glfwPlatformWindowIconified __imp_IsIconic _glfwPlatformWindowVisible __imp_IsWindowVisible _glfwPlatformWindowMaximized __imp_IsZoomed _glfwPlatformPostEmptyEvent __imp_PostMessageW _glfwPlatformGetCursorPos $pdata$_glfwPlatformGetCursorPos $unwind$_glfwPlatformGetCursorPos __imp_ScreenToClient __imp_GetCursorPos _glfwPlatformSetCursorPos $pdata$_glfwPlatformSetCursorPos $unwind$_glfwPlatformSetCursorPos __imp_SetCursorPos _glfwPlatformSetCursorMode $pdata$_glfwPlatformSetCursorMode $unwind$_glfwPlatformSetCursorMode __imp_LoadCursorW __imp_SetCursor __imp_WindowFromPoint _glfwPlatformGetKeyName $pdata$_glfwPlatformGetKeyName $unwind$_glfwPlatformGetKeyName __imp_WideCharToMultiByte __imp_GetKeyNameTextW _glfwIsPrintable _glfwPlatformCreateCursor $pdata$_glfwPlatformCreateCursor $unwind$_glfwPlatformCreateCursor _glfwPlatformCreateStandardCursor $pdata$_glfwPlatformCreateStandardCursor $unwind$_glfwPlatformCreateStandardCursor ??_C@_0CI@LDEHPBDB@Win32?3?5Failed?5to?5create?5standard@ __imp_CopyIcon _glfwPlatformDestroyCursor $pdata$_glfwPlatformDestroyCursor $unwind$_glfwPlatformDestroyCursor _glfwPlatformSetCursor $pdata$1$_glfwPlatformSetCursor $chain$1$_glfwPlatformSetCursor $pdata$0$_glfwPlatformSetCursor $chain$0$_glfwPlatformSetCursor $pdata$_glfwPlatformSetCursor $unwind$_glfwPlatformSetCursor __imp_PtInRect _glfwPlatformSetClipboardString $pdata$5$_glfwPlatformSetClipboardString $chain$5$_glfwPlatformSetClipboardString $pdata$3$_glfwPlatformSetClipboardString $chain$3$_glfwPlatformSetClipboardString $pdata$1$_glfwPlatformSetClipboardString $chain$1$_glfwPlatformSetClipboardString $pdata$_glfwPlatformSetClipboardString $unwind$_glfwPlatformSetClipboardString __imp_CloseClipboard __imp_SetClipboardData __imp_EmptyClipboard ??_C@_0CA@FNCFBEIG@Win32?3?5Failed?5to?5open?5clipboard?$AA@ __imp_GlobalFree __imp_OpenClipboard __imp_GlobalUnlock __imp_GlobalLock ??_C@_0DG@CGDLBFFB@Win32?3?5Failed?5to?5allocate?5global@ __imp_GlobalAlloc ??_C@_0CK@JIIJNNEM@Win32?3?5Failed?5to?5convert?5string?5@ _glfwPlatformGetClipboardString $pdata$2$_glfwPlatformGetClipboardString $chain$2$_glfwPlatformGetClipboardString $pdata$0$_glfwPlatformGetClipboardString $chain$0$_glfwPlatformGetClipboardString $pdata$_glfwPlatformGetClipboardString $unwind$_glfwPlatformGetClipboardString ??_C@_0CO@MCBPBAMK@Win32?3?5Failed?5to?5convert?5wide?5st@ _glfwCreateUTF8FromWideStringWin32 ??_C@_0CN@FGAGPCCP@Win32?3?5Failed?5to?5convert?5clipboa@ __imp_GetClipboardData _glfwPlatformGetRequiredInstanceExtensions $pdata$_glfwPlatformGetRequiredInstanceExtensions $unwind$_glfwPlatformGetRequiredInstanceExtensions ??_C@_0BF@COCMEDFO@VK_KHR_win32_surface?$AA@ __imp__strdup ??_C@_0P@IENCOMCD@VK_KHR_surface?$AA@ __imp_calloc _glfwPlatformGetPhysicalDevicePresentationSupport $pdata$_glfwPlatformGetPhysicalDevicePresentationSupport $unwind$_glfwPlatformGetPhysicalDevicePresentationSupport ??_C@_0DO@PGEACBCJ@Win32?3?5Vulkan?5instance?5missing?5V@ ??_C@_0CP@FOBJNCLA@vkGetPhysicalDeviceWin32Presenta@ _glfwPlatformCreateWindowSurface $pdata$_glfwPlatformCreateWindowSurface $unwind$_glfwPlatformCreateWindowSurface ??_C@_0CL@LJNEEKEI@Win32?3?5Failed?5to?5create?5Vulkan?5s@ _glfwGetVulkanResultString ??_C@_0BI@INMBJMIJ@vkCreateWin32SurfaceKHR?$AA@ glfwGetWin32Window $pdata$glfwGetWin32Window $unwind$glfwGetWin32Window _glfwInitialized windowProc $pdata$5$windowProc $chain$5$windowProc $pdata$4$windowProc $chain$4$windowProc $pdata$3$windowProc $chain$3$windowProc $pdata$windowProc $unwind$windowProc __imp_DragFinish _glfwInputDrop __imp_DragQueryPoint __imp_DragQueryFileW __mask@@NegDouble@ _glfwInputScroll __real@405e000000000000 _glfwInputMouseClick __imp_SetCapture __imp_ReleaseCapture _glfwInputCursorEnter __imp_TrackMouseEvent _glfwInputCursorMotion _glfwInputKey _glfwInputChar _glfwInputWindowCloseRequest _glfwInputWindowDamage _glfwInputWindowPos _glfwInputWindowSize _glfwInputFramebufferSize _glfwInputWindowIconify _glfwInputWindowFocus __imp_DefWindowProcW _glfwDetectJoystickDisconnectionWin32 _glfwDetectJoystickConnectionWin32 _glfwInputMonitorChange __imp_GetPropW _glfwRegisterWindowClassWin32 $pdata$_glfwRegisterWindowClassWin32 $unwind$_glfwRegisterWindowClassWin32 ??_C@_0CH@GAPNPCHF@Win32?3?5Failed?5to?5register?5window@ __imp_RegisterClassExW __imp_LoadImageW ??_C@_1BE@LDFMCOHA@?$AAG?$AAL?$AAF?$AAW?$AA_?$AAI?$AAC?$AAO?$AAN?$AA?$AA@ _glfwPlatformCreateWindow $pdata$_glfwPlatformCreateWindow $unwind$_glfwPlatformCreateWindow _glfwPlatformMakeContextCurrent _glfwAnalyzeContextWGL _glfwCreateContextWGL _glfwPlatformPollEvents $pdata$3$_glfwPlatformPollEvents $chain$3$_glfwPlatformPollEvents $pdata$2$_glfwPlatformPollEvents $chain$2$_glfwPlatformPollEvents $pdata$1$_glfwPlatformPollEvents $chain$1$_glfwPlatformPollEvents $pdata$0$_glfwPlatformPollEvents $chain$0$_glfwPlatformPollEvents $pdata$_glfwPlatformPollEvents $unwind$_glfwPlatformPollEvents __imp_DispatchMessageW __imp_TranslateMessage _glfwPlatformWaitEvents $pdata$_glfwPlatformWaitEvents $unwind$_glfwPlatformWaitEvents __imp_WaitMessage _glfwPlatformWaitEventsTimeout $pdata$_glfwPlatformWaitEventsTimeout $unwind$_glfwPlatformWaitEventsTimeout __imp_MsgWaitForMultipleObjects __real@408f400000000000 
/67             1459697977              100666  2264      `
d� 99WG  (       .drectve        s   �               
 .debug$S        �   ?              @ B.text           A   �            P`.pdata             >  J         @0@.xdata             h              @0@.rdata          $   p              @@@.text              �  �          P`.pdata             �  �         @0@.xdata             �              @0@.text                          P`.text           
   &  3          P`   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   }   ?     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\win32_tls.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler    H冹(�    �|  凐�uH�    �  �    3繦兡(们x     �   H兡(�                     "       /           A                         
     B  Win32: Failed to allocate TLS index H冹(�=x   t�
|  �    H兡(�                                                       B  H嬔�
|  H�%              #    �
|  H�%           	   '    @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          A      槫!                    .pdata               s�7�        %           .xdata                �9�        M               v            .rdata         $       ㄤ蚎         �           _glfw                �            $LN4            .text                1蝍�         �           .pdata               #1i        �           .xdata      	          �9�        #      	        Q           $LN4            .text       
         际W�         _      
        ~           .text          
      E0>F         �              �           �  _glfwInitThreadLocalStorageWin32 $pdata$_glfwInitThreadLocalStorageWin32 $unwind$_glfwInitThreadLocalStorageWin32 _glfwInputError ??_C@_0CE@MEIEKJIC@Win32?3?5Failed?5to?5allocate?5TLS?5in@ __imp_TlsAlloc _glfwTerminateThreadLocalStorageWin32 $pdata$_glfwTerminateThreadLocalStorageWin32 $unwind$_glfwTerminateThreadLocalStorageWin32 __imp_TlsFree _glfwPlatformSetCurrentContext __imp_TlsSetValue _glfwPlatformGetCurrentContext __imp_TlsGetValue /98             1459697977              100666  1778      `
d�	 99W�         .drectve        s   |               
 .debug$S        �   �              @ B.text           H   {  �          P`.pdata             �           @0@.xdata                           @0@.text           /   '  V          P`.pdata             t  �         @0@.xdata             �              @0@.text              �  �          P`   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   ~   @     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\win32_time.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler   H冹(H峀$0�    吚tH婦$0�h	     H�p	  H兡(们h	      H�p	  �  H兡(�              %       0       ;           H                         
     B  H冹(�=h	   tH峀$0�    H婦$0H兡(�h  嬂H兡(�              $           /                              B  H�p	  �       @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          H      豠X                    .pdata               X賦�                   .xdata                �9�        3           _glfw                O            $LN5            .text          /      +sR`         o           .pdata               鷓V         �           .xdata                �9�        �               �            $LN5            .text       	         �         �       	      _glfwInitTimerWin32 $pdata$_glfwInitTimerWin32 $unwind$_glfwInitTimerWin32 __imp_QueryPerformanceFrequency _glfwPlatformGetTimerValue $pdata$_glfwPlatformGetTimerValue $unwind$_glfwPlatformGetTimerValue __imp_QueryPerformanceCounter _glfwPlatformGetTimerFrequency /130            1459697978              100666  13869     `
d�6 :9W�  �       .drectve        s   �               
 .debug$S        �   �              @ B.text           �  �	            P`.pdata             �  �         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata               ,         @0@.pdata             J  V         @0@.xdata             t              @0@.rdata             �              @@@.rdata          )   �              @@@.text           @   �  
          P`.pdata             
  
         @0@.xdata             5
              @0@.text           �  =
            P`.pdata             8  D         @0@.xdata          (   b  �         @0@.text           �   �               P`.pdata               #         @0@.xdata             A              @0@.text           �   I  �          P`.pdata                        @0@.xdata             2  N         @0@.text           �   X            P`.pdata             >  J         @0@.xdata             h  �         @0@.text           �   �  S      
    P`.pdata             �  �         @0@.xdata             �  �         @0@.text           �     �          P`.pdata             H  T         @0@.xdata             r  �         @0@.rdata          #   �              @@@.text           ,   �  �          P`.pdata             �           @0@.xdata             !              @0@.text           ,   )  U          P`.pdata             i  u         @0@.xdata             �              @0@.text           2  �  �      	    P`.pdata             '  3         @0@.xdata             Q  a         @0@.pdata               �         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata                        @0@.rdata              '              @@@.text             G  M          P`.pdata             �  �         @0@.xdata             �  �         @0@.pdata               )         @0@.xdata             G  c         @0@.pdata             �  �         @0@.xdata             �  �         @0@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   �   C     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\win32_monitor.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler    @UVAUH冹@H嬺L嬮H呉tH岼D�H兞D�    H嬭H吚uH�    �  �    3繦兡@A]^]肏塡$`H墊$hL塪$pM峞H�
    E3蒊嬙E3�    �   H嬋H孁�    �   H嬒嬝�    D嬅嬓H嬐�    H嬒H嬝�    H嬐�    A鲄D     t
莾�     H崜�   I嬏I+詅�     �H兞f塂
吚u�3�H崈   A兩�H墊$8H墊$0M嬆3夜辇  荄$(@   H塂$ �    L媎$pH咑tRL岶L崜�   I嬓M+��H兟fA塂吚u頗墊$8H崈@  H墊$0A兩�3夜辇  荄$(@   H塂$ �    H媩$hH嬅H媆$`H兡@A]^]�   '    .   &    8   #    ]   "    l       }       �       �       �       �       !      }      /  �                        
    !       Q                            Q   /                            ! �     Q                                Q                             Q Qt
 L4 	r�`PD I S P L A Y   Win32: Failed to convert string to UTF-8 @SH冹0児�   H嬞t(H冮�A�   E3�3襀荄$     �    莾�      H兡0[�,   1        @           2       2       0     R0H塡$H塼$H墊$ UATAUAVAWH崿$瘗��H侅  H�    H3腍墔   H塋$P3�3覊1H峀$`A窰  E3�3�E3�3坭    L岲$`E3�3�3汕D$`H  �    吚剕   愽叅   t9H崓�  3褹窰  �    L崊�  H峀$dE3�3仪叞  H  �    吚u4H峀$`3褹窰  �描    L岲$`E3蓩�3汕D$`H  �    吚u岆�   H峀$`E3�3褹窰  D塼$@�    L岲$`Hc逧3�3�3汕D$`H  E3銱塡$H�    吚凙  鰠�   匃  鰠�   DE﨟呟劸   H崓�  3褹窰  3��    L崊�  H峀$dE3�3仪叞  H  �    吚劋  J�屮���fD  �艻嬐I�腍c諬兠H菱�    H崟�  H峀$`L嬭�    H崓�  3褹窰  �荍�+�    L崊�  H峀$dE3蓩浊叞  H  �    吚u旈   �艻嬐I�腍c諬菱�    H峂嬭�    L嬸H吚uH�    �  �    3坶�   H峊$dH�
    E3蒃3�    �   H嬋H孁�    �   H嬒嬝�    D嬅嬓I嬑�    H嬒H嬝�    I嬑�    鲄�      t
莾�     H岲$dH嬘H峀$dH+衒ffff�     �H兞f塂
~f吚u�3蒆崈   L岲$dH塋$8H塋$0A兩�归�  3仪D$(@   H塂$ �    D媡$@K塡屮H媆$HH峀$`A��3褹窰  D塼$@�    L岲$`E3葾嬛3汕D$`H  �    吚吙��K婦� I婾 I塃 H婦$PK塗� �0I嬇H媿   H3惕    L崪$  I媅8I媠@I媨HI嬨A_A^A]A\]�*   ?    Y   A    s   >    �   A    �   >    �   A    �   >      A    6  >    u  A    �  >    �  =    �      �  A      >    2  =    >  '    M  &    W  #    j  "    v      �      �      �      �      �      3      \  A    w  >    �  @        �          B       B       ;    8
 't� 'd� '4� '� ����P             <    H墊$L崄�   L嬌H兩�3繧孁f虔H餮H�蓆/H伮�   I+� A�A�+萿I兝吚u�3绤�斃H媩$肏嵑�   I崄�   H+���8+製H兝吷u鞨媩$3绤�斃�    �           L       L       K     t H塡$ UVWH侅  H�    H3腍墑$   H嬞I孁H嬺杰   H峀$ 3褼嬇�    H崑�   L岲$ A�   兪�f塴$d�    H咑t婦$l�H�t婦$p�H媽$   H3惕    H嫓$H  H伳  _^]�   ?    :   A    Z   V    �   @        �           W       W       U    ! 4) " p`P            <    H塡$H塼$ WH侅  H�    H3腍墑$   H嬞H孃拒   H峀$ 3褼嬈�    H崑�   L岲$ 兪�f塼$d�    媱$�   D嫓$�   媽$�   塆媱$�   L峅L岹H峎塆D��    H媽$   H3惕    L崪$  I媅 I媠(I嬨_�   ?    :   A    T   b    �   a    �   @        �           c       c       `    $ d' 4& " p            <    H塡$WH侅0  H�    H3腍墑$   H孃H崙�   H�
    E3蒃3�    H峊$ H嬋H嬝�    H嬎�    �   H嬒�    H�H峊$ A�   �    H婳H崝$   A�   �    H婳H崝$   A�   �    H媽$   H3惕    H嫓$P  H伳0  _�   ?    ,   "    8       I   n    R       _   m    r   o    �   o    �   o    �   @        �           p       p       l     
4� 
� p            <    H塡$WH侅0  H�    H3腍墑$   亃   H嬟H孂tH�    �  �    雞H�H峀$ A�   �    H婼H崒$   A�   �    H婼H崒$   A�   �    H崡�   H�
    E3蒃3�    H峊$ H嬋H嬝�    H嬎�    H媽$   H3惕    H嫓$P  H伳0  _�   ?    1   }    ;   #    P   o    g   o    ~   o    �   "    �       �   z    �       �   @        �           ~       ~       y     
4� 
� p            <    Win32: Gamma ramp size must be 256 H冹(�=     u3夜  �    3繦兡(肏崄   H兡(�   �       #        ,           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏崄@  H兡(�   �       #        ,           �       �       �     B  H塡$ WH侅@  H�    H3腍墑$0  H嬞�    H峊$0H嬎H孁�    H峀$0H嬜�    吚劺   H壌$`  拒   H峀$PD嬈3诣    f壌$�   H嫶$`  莿$�     \ �墑$�   婫墑$   婳OO墝$�   婫墑$  凒r凒r莿$�       H崑�   H峊$PA�   E3繦荄$     �    吚tH�    �  �    3离莾�     �   H媽$0  H3惕    H嫓$h  H伳@  _�   ?    #   �    3   Z    @   �    d   A    �   1    �   �    �   #      @    �   2          �       �       �    !       L          �       �       �    L   �           �       �       �    ! d,     L          �       �       �        L           �       �       �     
4- 
( p      0     <    Win32: Failed to set video mode L嬡USVWAVI峩圚侅P  H�    H3腍塃0M塩M塳蠱墈萀嬹H孃3鰤2A寇   H峀$P3褽嬊E3鞥3滂    L岲$PI崕�   3襢D墋�    吚�1  A嬤f�     婱鳤�艃�傛   婨麹峀$@L岲$<塂$0婨 H峊$8塂$4婨塂$D�    3�;})f怘c肏峊$0H�@H�舞    吚t��;|�;寢   A兙�   t(H峊$PI崕�   A�   E3繦荄$     �    吚uYD9'uA冹�H嬑Ic腍�@H菱�    H嬸�HcH�@婦$0塂舞婦$4塂戊婦$8塂勿婦$<塂昔婦$@塂硒婦$D塂吸I嬤H峀$PL嬅3诣    L岲$PI崕�   A嬚f塢�    吚呟���? L嫾$@  L嫭$H  L嫟$�  u'�   岼�    I嬑H嬓H嬝�    �   H嬅�H嬈H婱0H3惕    H伳P  A^_^[]�   ?    O   A    h   b    �   a    �   �      1    2  =    �  A    �  b    �  �    �  Z    �  @    �            �       �       �    !       "          �       �       �    "   �          �       �       �    ! �( �) �2     "          �       �       �        "           �       �       �    " * 	�p`0P      0     <    @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          �     鱾P                    .pdata               -(                   .xdata               ��         )           .pdata               -m�!        @           .xdata               媌        W           .pdata               X髮�        n           .xdata      	          K,�        �       	        �                �                �                �                �                �            .rdata      
          ;騜�               
        C           .rdata         )       恰髟         S              �           .text          @      t頔�         �          .pdata      
         砺�)        �      
    .xdata                僣�        �                         $LN4            .text          �     孠�         0          .pdata               On�        I          .xdata         (      C	紇        i              �               �               �               �               �           memset           $LN44           .text          �       7�         �          .pdata               9偞�                  .xdata                蛷g�        *          $LN10           .text          �      -浂�         M          .pdata               杞E%        h          .xdata               !�        �              �           $LN5            .text          �      /4/	         �          .pdata               ]丶R        �          .xdata               ,P觼                      '               5           $LN3            .text          �   
   觩         P          .pdata               SIF2        j          .xdata               w=C        �              �               �           memcpy           $LN3            .text          �      �7DC         �          .pdata               �        �          .xdata                w=C                       9           .rdata      !   #       絔C�         R      !    $LN4            .text       "   ,      澸驨         �      "    .pdata      #         w佼"        �      #    .xdata      $          �9�"        �      $        �           $LN4        "    .text       %   ,      氋a�         �      %    .pdata      &         w佼%        �      &    .xdata      '          �9�%              '    $LN4        %    .text       (   2  	   5�2d         5      (    .pdata      )         @q覎(        L      )    .xdata      *         N=e�(        l      *    .pdata      +         �"�(        �      +    .xdata      ,         訙Q(        �      ,    .pdata      -         ⒆2~(        �      -    .xdata      .         �'衵(        �      .    .rdata      /           b�         	      /        G               ^           $LN8        (    .text       0        掽S�         s      0    .pdata      1         醽�,0        �      1    .xdata      2         菑0        �      2    .pdata      3         �0        �      3    .xdata      4         "0        �      4    .pdata      5         +eS�0              5    .xdata      6         �z�0        @      6        c           $LN32       0    p  createMonitor $pdata$3$createMonitor $chain$3$createMonitor $pdata$2$createMonitor $chain$2$createMonitor $pdata$createMonitor $unwind$createMonitor __imp_WideCharToMultiByte __imp_free __imp_DeleteDC _glfwAllocMonitor __imp_GetDeviceCaps __imp_CreateDCW ??_C@_1BA@PICGEGJB@?$AAD?$AAI?$AAS?$AAP?$AAL?$AAA?$AAY?$AA?$AA@ _glfwInputError ??_C@_0CJ@EFIFNOJC@Win32?3?5Failed?5to?5convert?5string?5@ _glfwCreateUTF8FromWideStringWin32 _glfwRestoreVideoModeWin32 $pdata$_glfwRestoreVideoModeWin32 $unwind$_glfwRestoreVideoModeWin32 __imp_ChangeDisplaySettingsExW _glfwPlatformGetMonitors $pdata$_glfwPlatformGetMonitors $unwind$_glfwPlatformGetMonitors __GSHandlerCheck __imp_realloc __imp_EnumDisplayDevicesW __security_cookie __security_check_cookie _glfwPlatformIsSameMonitor $pdata$_glfwPlatformIsSameMonitor $unwind$_glfwPlatformIsSameMonitor _glfwPlatformGetMonitorPos $pdata$_glfwPlatformGetMonitorPos $unwind$_glfwPlatformGetMonitorPos __imp_EnumDisplaySettingsExW _glfwPlatformGetVideoMode $pdata$_glfwPlatformGetVideoMode $unwind$_glfwPlatformGetVideoMode _glfwSplitBPP __imp_EnumDisplaySettingsW _glfwPlatformGetGammaRamp $pdata$_glfwPlatformGetGammaRamp $unwind$_glfwPlatformGetGammaRamp _glfwAllocGammaArrays __imp_GetDeviceGammaRamp _glfwPlatformSetGammaRamp $pdata$_glfwPlatformSetGammaRamp $unwind$_glfwPlatformSetGammaRamp __imp_SetDeviceGammaRamp ??_C@_0CD@EGEEPFHF@Win32?3?5Gamma?5ramp?5size?5must?5be?52@ glfwGetWin32Adapter $pdata$glfwGetWin32Adapter $unwind$glfwGetWin32Adapter _glfwInitialized glfwGetWin32Monitor $pdata$glfwGetWin32Monitor $unwind$glfwGetWin32Monitor _glfwSetVideoModeWin32 $pdata$1$_glfwSetVideoModeWin32 $chain$1$_glfwSetVideoModeWin32 $pdata$0$_glfwSetVideoModeWin32 $chain$0$_glfwSetVideoModeWin32 $pdata$_glfwSetVideoModeWin32 $unwind$_glfwSetVideoModeWin32 ??_C@_0CA@CNJHNGPE@Win32?3?5Failed?5to?5set?5video?5mode?$AA@ _glfwCompareVideoModes _glfwChooseVideoMode _glfwPlatformGetVideoModes $pdata$3$_glfwPlatformGetVideoModes $chain$3$_glfwPlatformGetVideoModes $pdata$2$_glfwPlatformGetVideoModes $chain$2$_glfwPlatformGetVideoModes $pdata$_glfwPlatformGetVideoModes $unwind$_glfwPlatformGetVideoModes __imp_calloc 
/165            1459697978              100666  27283     `
d唞 :9W"?  �      .drectve        s   $               
 .debug$S        �   �              @ B.rdata             '              @@@.rdata             7              @@@.rdata             G              @@@.rdata             W              @@@.rdata             g              @@@.rdata             w              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.data              �  �         @ P�.rdata              _           @ @@.text           �   �            P`.rdata             �              @@@.rdata             �              @@@.rdata                           @@@.rdata                           @@@.rdata             .              @@@.rdata             <              @@@.rdata             M              @@@.rdata             a              @@@.rdata          
   u              @@@.text              �               P`.text           �  �  R          P`.pdata             �  �         @0@.xdata             �           @0@.pdata             "  .         @0@.xdata             L  \         @0@.pdata             z  �         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �           @0@.pdata             .  :         @0@.xdata             X  l         @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata                (          @0@.rdata             2               @0@.text           �   6   �           P`.pdata             
!  !         @0@.xdata             7!              @0@.text           �  ?!  �#          P`.pdata             �$  �$         @0@.xdata             �$  �$         @0@.text             �$  (          P`.pdata             �(  )         @0@.xdata             ))  9)         @0@.pdata             W)  c)         @0@.xdata             �)  �)         @0@.pdata             �)  �)         @0@.xdata             �)  �)         @0@.pdata             *  *         @0@.xdata             9*  Q*         @0@.pdata             o*  {*         @0@.xdata             �*  �*         @0@.rdata          '   �*              @@@.rdata          #   �*              @@@.rdata          (   	+              @@@.rdata          %   1+              @@@.rdata             V+              @@@.text           �  r+  :-          P`.pdata             p.  |.         @0@.xdata             �.  �.         @0@.pdata             �.  �.         @0@.xdata             �.  /         @0@.pdata             4/  @/         @0@.xdata             ^/  r/         @0@.pdata             �/  �/         @0@.xdata             �/  �/         @0@.pdata             �/  �/         @0@.xdata             0  *0         @0@.pdata             H0  T0         @0@.xdata             r0  �0         @0@.text           �  �0  5          P`.pdata             �5  �5         @0@.xdata             �5  �5         @0@.pdata             6  6         @0@.xdata             -6  =6         @0@.pdata             [6  g6         @0@.xdata             �6  �6         @0@.pdata             �6  �6         @0@.xdata          $   �6  	7         @0@.pdata             '7  37         @0@.xdata             Q7  i7         @0@.rdata             s7              @0@.rdata             w7              @0@.rdata             {7              @@@.rdata             �7              @@@.rdata             �7              @0@.rdata             �7              @0@.rdata             �7              @0@.text           �   �7  l8      	    P`.pdata             �8  �8         @0@.xdata             �8              @0@.text           j   9  n9          P`.pdata             �9  �9         @0@.xdata             �9  �9         @0@.pdata             :  :         @0@.xdata             ,:  @:         @0@.pdata             ^:  j:         @0@.xdata             �:              @0@.rdata          )   �:              @@@.text           �   �:  �;          P`.pdata             �;  �;         @0@.xdata             <  +<         @0@.text              5<  T<          P`.text           T   h<  �<          P`.pdata             �<  �<         @0@.xdata             �<              @0@.text           T   =  Z=          P`.pdata             n=  z=         @0@.xdata             �=              @0@.text           >   �=  �=          P`.pdata             �=  >         @0@.xdata              >              @0@.text           Y   (>  �>          P`.pdata             �>  �>         @0@.xdata             �>              @0@.rdata             ?              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   �   D     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\win32_joystick.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler   1�y�:H獧]d�6� �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST  �mｓ上壳DEST              ���                  ���                  ���                  ���                  ���                  ���                  ���                  ���                   ���                $   ���                (   ���                ,   ���                0   ���                1   ���                2   ���                3   ���                4   ���                5   ���                6   ���                7   ���                8   ���                9   ���                :   ���                ;   ���                <   ���                =   ���                >   ���                ?   ���                @   ���                A   ���                B   ���                C   ���                D   ���                E   ���                F   ���                G   ���                H   ���                I   ���                J   ���                K   ���                L   ���                M   ���                N   ���                O   ���            
       
    0       H       `       x       �       �       �   "    �   "    �   "      "              P   ,                  %    禔�葍�w\L�    H楢嫈�    I�釮�    肏�    肏�    肏�    肏�    肏�    闽AtH�    肏�    肏�    脨                                   P       O    $   M    ,   I    4   E    <   A    D   =    L   9    Z   5    b   2    j   /    p   6    t   N    x   J    |   F    �   B    �   >    �   ,    �   :    Unknown XInput Device Xbox 360 Controller Wireless Xbox 360 Controller XInput Drum Kit XInput Guitar XInput Dance Pad XInput Flight Stick XInput Arcade Stick XInput Wheel 婣D婤A;纓A+烂�+肏塴$ ATH侅`  H�    H3腍墑$P  L嬦3鞨峊$ D岴3�    吚t3篱X  婰$ �   H壌$x  �    H峊$ A�   H嬋H嬸�    凐�uH嬑�    3篱
  婦$ H壖$�  孆吚勪   H墱$p  H嬣儃叢   H�3繪峀$$H塂$(L岲$(�   H塂$0H塂$8H塂$@荄$(    荄$$    �    凐�tk稬$4稤$0玲華;$uVH峀$P3褹�   �    H�L峀$$L岲$P�   荄$$   �    凐�t8H�    H峀$P@埇$O  �    H吚u婦$ �荋兠;��6�����   H嫓$p  H嬑�    H嫾$�  嬇H嫶$x  H媽$P  H3惕    H嫭$�  H伳`  A\�   �    2   �    T   �    k   �    y   �    �   �      �    5  �    A  �    T  �    �  �    �  �    �  �          V       V       \    !       J          V       V       �    �  �          V       V       b    !   J   �          V       V       z    ~  �          V       V       h    !   �   �          V       V       t    �   ~          V       V       n    ! 4. �   �          V       V       t    �   �           V       V       t    ! t0 J   �          V       V       z    J   �           V       V       z    ! d/     J          V       V       �        J           V       V       �      T1 , �      P     �    IG_ @SH冹 H儁@ H嬞tH婭@H��P@H婯@H��PH婯8�    H婯�    H婯�    H婯(�    3襀嬎D岯`�    L�x	  H斧*I+跦麟H嬍�  H六H嬃H凌?H菻兡 [�    *   �    4   �    >   �    H   �    V   �    ]   �    �   �        �           �       �       �     20H塡$H塴$ VWAUH冹@H�    H3腍塂$8禔HczH媟H嬟��  H婹H峣L�-    I;�    uH婾I;�   u3离纼�吚u婥��   �'  H婾 I;�    uH婾I;�   u3离纼�吚u��   H婾 I;�    uH婾I;�   u3离纼�吚u��   榕   H婾 I;�    uH婾I;�   u3离纼�吚u��   閽   H婾 I;�    uH婾I;�   u3离纼�吚u	��   隻H婾 I;�    uH婾I;�   u3离纼�吚u	��   �2H婾 I;�    uH婾I;�   u3离纼�吚呚   ��   3繪岲$ �   H塂$ H塂$(H塂$0婣H�荄$    荄$$   塂$(荄$,   荄$0 ���荄$4�  H��P0吚xzH婱 I;�    uH婱I;�   u3离纼�吚u
荄�   �C隒荄�    �C�6�t婤荄�   兝0��B��t婤 荄�   ��    ��B �C�   H婰$8H3惕    H媆$pH媗$xH兡@A]_^�   �    C   P    J       W       �   
    �   
    �   
    �   
    �       �             %      H      U      x      �                  �  �        �          �       �       �    ! T 4 r�p`    8      �    H塡$ UVATH峫$笻侅�   H�    H3腍塃?3跮嬦L�
�	  D嬅ffffff�     Ic繦�@H菱J�
I;L$uJ婰
I;L$u嬅�纼�吚劉  A�繟凐~腍壖$�   L壃$�   L�-x	  孄H�
  I嬇�9tH兝`�荋;羱駜�~3离sI峀$�    吚u`H�
�  L岴荌峊$H�E3�P吚yH�    �  �    �0H婱荋�    H��PX吚yPH�    �  �    H婱荋��P�   H嫾$�   L嫭$�   H婱?H3惕    H嫓$�   H伳�   A\^]肏婱�3繦峌鱄塃鱄塃�H塃H塃H塃塃荅�,   H��P吚yH�    閥���H婱�3繪岴'H塃'H塃/H塃3荅'   荅+   塢7H��   �P0吚yH�    �7���婱H媇�3�MH塃蠉PMH塃譎塃逪塃鏗塃颒塢�    L岴螲�    H塃譎�A�   H嬎�P 吚y*H�    �  �    H婱荋��PH婱�    橄��HcU逪婱譒�
    A�   �    H婨菋U�U鉒c逰�[Hc蔋零I軭塁@A婦$塁LA婦$塁PA婦$塁TA婦$塖�   塁X�    婱牒   H塁婨飴�K HcK �    I峀$(H塁H婨譎塁(婨邏C0�    �  嬒�   H塁8�    ����   ���   �    +   �    �   �    �   �    �   V    �   �    �   �    �   �    �   (      �      �    C  �    �  �    �  �      �      �    0  �    :  �    N  �    b  S    n  �    �  �    �  �    �  �      �                �       �       �    !       |          �       �       �    [            �       �       �    !   �  t     |          �       �       �    ;  [          �       �       �    !       |          �       �       �    |   ;          �       �       �    ! � t     |          �       �       �        |           �       �       �    # 4  	�`P      �      �    DI: Failed to enumerate device objects DI: Failed to set device axis mode DI: Failed to query device capabilities DI: Failed to set device data format DI: Failed to create device @WH冹@H�    H3腍塂$8孂H��	  H�
X  儀� tH�8 u9xtYH兝`H;羱鍴塡$XH塴$`H�-x	  3跦�
  H嬇@ �8 tH兝`�肏;羱饍�L岲$ 3覌��  吚t3篱�   3繦婰$8H3惕    H兡@_肏塼$hHc煤   H�4@岼H伶H跚F   �    �   岼
H塅荈    �    �   H塅禗$!�葍�wdH�    H構寕    H�酘�
    隡H�
    隓H�
    �;H�
    �2H�
    �)H�
    � 鯠$"H�
    H�    HD入H�
    �    �  嬎H塅8墌H�    H媡$h�   H媆$XH媗$`H婰$8H3惕    H兡@_胒�                                	   �       �    !   �    L   �    U   �    �   �    �   �    �   �    �   �    �   P           M      I    %  E    .  A    7  =    @  9    N  5    U  2    b  /    h  �    {  �    �  �    �  �    �     �     �     �      �  �    �  �    �  �    �  �          �       �       �    !   d
  T  4     ?          �       �       �    �  �          �       �       �    !   T  4     ?          �       �       �    �   �          �       �       �    ! d
 �  �         �       �       �    �   �           �       �       �    !       ?          �       �       �    ?   �           �       �       �    !
 
T 4     ?          �       �       �        ?           �       �       �     rp    8      �    @USWH峫$笻侅�   H�    H3腍塃�9 孃H嬞u3篱M  H儁@ H壌$  L墹$  L壃$  L壌$�   L壖$�   劅  H婭@H��惾   H婯@L岴桯�篜   �PH= �t= �u*H婯@H��P8H婯@H��惾   H婯@L岴桯�篜   �PH吚埮  �u嬊閱  3�D嬤D嬊D峠9{0巒  �
    �    嬿D嬜D�D峸D峯ffff�     H婥(A婰Ic吷埱   A;�帯   A;蟭凒叞   塎�稬椄k)倀D塭鏒墋锴E�   鏖龙D塽髑E�   嬄D塵�H荅	   凌衳A;諂A嬚Hc翧嬏I嬛D婰呯H婥D吷tE�$ �A�< I�姥罤�蕌汶:鯠梹H婥t	E�$ I�离&A�< I�离fnD桯婥[荔X馏^麦I鯝�肕誅;[0����镻  婭H�   H峌噁塃绺    A�   f塃楦 @  荅鶂  f塃敫 �  荅�  f塃砀   fD塽f塃锔   f塃窀    f塃蟾   f塃醺@   D峘羏塃��  吚t=�  uH嬎�    3篱�  A;�劜  縰�縈�)�$�   嬈)�$�   羏n荔胬�    �5    �=    H婥3�f/    v1fn�[荔X企^求 縀慺n菻婥[审X误^象H�	�8H婥墄縰�縈晪�羏n荔胬�    H婥f/    v2fn�[荔X企^求@縀昮n菻婥[审X误^象H�
墄H婥墄禘嶓
    �    (�$�   (�$�   <v独fn繦婥[荔^馏\麦@�H婥茾  ��禘�<v独fn繦婥[荔^馏\麦@�H婥茾  �縡ffffff�     稤}鏵匛婬婥暳�稤}閒匛婬婥暳H兦圠�H�|螦嬆L嫶$�   L嫭$  L嫟$  H嫶$  L嫾$�   H婱H3惕    H伳�   _[]�   �    �   :   �   7   x  �    �  �    �  <   �  :   �  4   �  1   C  <   O  .   �  +   �  (   �  �    |  �                      
   !       4                      %   �  |                         !   4   �                        �  �                         ! x h 4   �                        4   �                         !(
 (�  � �# �" d!     4                       %       4                       %     p0P      �      �      �?  �B    �@     ┚@ �F ��F   ?H塡$H塴$H塼$WH冹 H��	  H精*H�-X  H孄H鬟H�H吷tH��P@H�H��PH婯�    H婯�    H婯�    H婯�    3襀岾繢岯`�    L�H嬈I麟H嬍�  H六H嬃H凌?H辱    H兠`H;輣凥�
�  H吷tH��PH媆$0H媗$8H媡$@H兡 _�   �    (   �    O   �    Y   �    c   �    m   �    |   �    �   �    �   �        �           F      F      E    d T 4 2pH冹8H�=�   tH塡$03蹕髓    �脙�r騂媆$0H�
�  H吷t1H�E3蒐�    A峇荄$     �P 吚yH�    �  �    H兡8�   �       �    +   �    =   �    W   ^   a   �    (   j           _      _      O   !                 _      _      [      (           _      _      U   ! 4               _      _      [                  _      _      [    b  Failed to enumerate DirectInput8 devices H塡$H塼$WH侅�   H�    H3腍墑$�   H��	  H�5X  儃� 剛   H�H吷tXH��惾   H�L岲$0H�篜   �PH= �t= �u(H�H��P8H�H��惾   H�L岲$0H�篜   �PH吚y$�婯H峊$ ��  吚t=�  u	H岾黎    H兠`H;�巊���H媽$�   H3惕    L崪$�   I媅I媠I嬨_�   �    '   �    .   �    �   �    �   �    �   �        �           i      i      h   $ d 4  p      �      �    Hc梁   H�@H�x	  H玲H乳       �          H塡$WH冹 Hc罤孃�   H�@H�x	  H零H豀嬎�    吚u
3繦媆$0H兡 _脣K�H婥H媆$0H兡 _�   �    +          T           v      v      u   
 
4 
2pH塡$WH冹 Hc罤孃�   H�@H�x	  H零H豀嬎�    吚u
3繦媆$0H兡 _脣K �H婥H媆$0H兡 _�   �    +          T           �      �         
 
4 
2p@SH冹 Hc梁   H�@H�x	  H零H豀嬎�    吚u3繦兡 [肏婥8H兡 [�   �    $          >           �      �      �    20H冹8H�=p   tB3�    L�
�  L�    �   H嬋H荄$     �x  吚yH�    �  �    H兡8�       �       �      �            7   �    B   �   L   �    U   I       Y           �      �      �    b  DI: Failed to create interface @comp.idov� ��   .drectve       s                 .debug$S       �                 .rdata                腷                    .rdata                掭�3                    .rdata                OL铦         "           .rdata                进 �         -           .rdata                钳�         8           .rdata                V>         D           .rdata      	          ,iH         P       	    .rdata      
          Y懕�         \       
    .rdata                @悃�         h           .rdata                #�R     GUID_POV        .data       
         Q6          t       
    .rdata                c媄�          �           .text          �      �#觘         �           $LN1    g       .rdata                Ma         �           .rdata                |鹠         �           .rdata                 �,�                   $LN3    Q       .rdata                谝帯         E          $LN4    I       .rdata                ORr�         o          $LN5    A       .rdata                8,�         �          $LN6    9       .rdata                
鱽         �          $LN7    1       .rdata                蛰9�         �          $LN8    )       .rdata         
       %_^d                   $LN9    !       $LN14   p           A           .text                 @w�/         M          .text          �     hH呍         d          .pdata               �        s          .xdata               �4
        �          .pdata               莸L�        �          .xdata               +葶        �          .pdata               �2↖        �          .xdata                薣躠        �           .pdata      !         k釫�              !    .xdata      "         哨R�              "    .pdata      #         '眿�        3      #    .xdata      $         伕亇        K      $    .pdata      %         奪        c      %    .xdata      &         彴�        {      &    .pdata      '         %轢�        �      '    .xdata      (         NzT6        �      (        �               �           .rdata      )          i完�         �      )        �                                              -               I               [           memset           .text       *   �      �O\         s      *    .pdata      +         o�*�*        �      +    .xdata      ,          （亵*        �      ,        �           _glfw            .text       -   �     簹�&         �      -    .pdata      .         缶P�-        �      .    .xdata      /         z<酒-        �      /    .text       0        悠麟               0    .pdata      1         髋*�0        "      1    .xdata      2         3n˙0        :      2    .pdata      3         詨0        R      3    .xdata      4         fk|�0        j      4    .pdata      5         瞟
0        �      5    .xdata      6         3n˙0        �      6    .pdata      7         陥恄0        �      7    .xdata      8         @氽0        �      8    .pdata      9         邉��0        �      9    .xdata      :         瀇�0        �      :                       2           .rdata      ;   '       �.鍥         >      ;    .rdata      <   #       @;�         y      <    .rdata      =   (       /�         �      =    .rdata      >   %       玒v         �      >        ,           .rdata      ?          骒�         <      ?    .text       @   �     
2稸         u      @    .pdata      A         J@癅        �      A    .xdata      B         雏�@        �      B    .pdata      C         <}?鉆        �      C    .xdata      D         囕@        �      D    .pdata      E         "髴@        �      E    .xdata      F         厎              F    .pdata      G         �T<@        "      G    .xdata      H         =錊        <      H    .pdata      I         >@        V      I    .xdata      J         �@        p      J    .pdata      K         袮韁@        �      K    .xdata      L         ]Xm%@        �      L    $LN13   _  @        �           $LN15   F  @    $LN16   =  @    $LN17   4  @    $LN18   +  @    $LN19   "  @    $LN20     @    $LN21     @    $LN32   �  @    .text       M   �     �S          �      M    .pdata      N         �(倌M        �      N    .xdata      O         k沸窶        �      O    .pdata      P         弗M        	      P    .xdata      Q         +o橫        ,	      Q    .pdata      R         G3S琈        G	      R    .xdata      S         M:o滿        b	      S    .pdata      T         球8 M        }	      T    .xdata      U   $      煨*郙        �	      U    .pdata      V         嘳�M        �	      V    .xdata      W         旸�;M        �	      W    .rdata      X          v靛�         �	      X    .rdata      Y          屋�         �	      Y    .rdata      Z          �         
      Z    .rdata      [          I�3�         
      [    .rdata      \          
G脸         6
      \    .rdata      ]          :-�         F
      ]    .rdata      ^          =-f�         V
      ^    _fltused         sqrt             .text       _   �   	   59埽         f
      _    .pdata      `         xx齆_        �
      `    .xdata      a          嘋c鬫        �
      a    $LN12       _    .text       b   j      |�T         �
      b    .pdata      c         u_b        �
      c    .xdata      d         搌莠b              d    .pdata      e         ⒚b        G      e    .xdata      f         T�昩        s      f    .pdata      g         2�b        �      g    .xdata      h          1�7b        �      h    .rdata      i   )       �徕         �      i    $LN11       b    .text       j   �      btY         ,      j    .pdata      k         Jk�j        R      k    .xdata      l         =V椏j              l    $LN49       j    .text       m         �9j�         �      m    .text       n   T      扟�         �      n    .pdata      o         <齦裯        �      o    .xdata      p          %蚘%n        
      p    $LN4        n    .text       q   T      FC%l         0
      q    .pdata      r         <齦裶        P
      r    .xdata      s          %蚘%q        w
      s    $LN4        q    .text       t   >      茛�-         �
      t    .pdata      u         OAG恡        �
      u    .xdata      v          （亵t        �
      v    $LN4        t    .text       w   Y      E＠_               w    .pdata      x         龛iJw              x    .xdata      y          1�7w        <      y    .rdata      z          S/�	         \      z        �           $LN5        w    �  IID_IDirectInput8W GUID_XAxis GUID_YAxis GUID_ZAxis GUID_RxAxis GUID_RyAxis GUID_RzAxis GUID_Slider GUID_Button _glfwObjectDataFormats _glfwDataFormat getDeviceDescription ??_C@_0BG@DGJLFNBN@Unknown?5XInput?5Device?$AA@ ??_C@_0BE@JNNBBIGB@Xbox?5360?5Controller?$AA@ ??_C@_0BN@OIHKANJ@Wireless?5Xbox?5360?5Controller?$AA@ ??_C@_0BA@LCMKGGHA@XInput?5Drum?5Kit?$AA@ ??_C@_0O@IFDGNEHH@XInput?5Guitar?$AA@ ??_C@_0BB@JDDMDGFO@XInput?5Dance?5Pad?$AA@ ??_C@_0BE@HBNNGLHP@XInput?5Flight?5Stick?$AA@ ??_C@_0BE@FABDLJKH@XInput?5Arcade?5Stick?$AA@ ??_C@_0N@JENFOGFI@XInput?5Wheel?$AA@ __ImageBase compareJoystickObjects supportsXInput $pdata$5$supportsXInput $chain$5$supportsXInput $pdata$4$supportsXInput $chain$4$supportsXInput $pdata$3$supportsXInput $chain$3$supportsXInput $pdata$2$supportsXInput $chain$2$supportsXInput $pdata$1$supportsXInput $chain$1$supportsXInput $pdata$0$supportsXInput $chain$0$supportsXInput $pdata$supportsXInput $unwind$supportsXInput __GSHandlerCheck __imp_strstr ??_C@_03HAFBONIK@IG_?$AA@ __imp_GetRawInputDeviceInfoA __imp_free __imp_calloc __imp_GetRawInputDeviceList __security_cookie __security_check_cookie closeJoystick $pdata$closeJoystick $unwind$closeJoystick _glfwInputJoystickChange deviceObjectCallback $pdata$deviceObjectCallback $unwind$deviceObjectCallback deviceCallback $pdata$4$deviceCallback $chain$4$deviceCallback $pdata$3$deviceCallback $chain$3$deviceCallback $pdata$2$deviceCallback $chain$2$deviceCallback $pdata$1$deviceCallback $chain$1$deviceCallback $pdata$deviceCallback $unwind$deviceCallback _glfwCreateUTF8FromWideStringWin32 __imp_qsort ??_C@_0CH@OEOICFHI@DI?3?5Failed?5to?5enumerate?5device?5o@ ??_C@_0CD@BFBAJDII@DI?3?5Failed?5to?5set?5device?5axis?5mo@ ??_C@_0CI@BJIPONFG@DI?3?5Failed?5to?5query?5device?5capab@ ??_C@_0CF@KNHHCLIO@DI?3?5Failed?5to?5set?5device?5data?5fo@ _glfwInputError ??_C@_0BM@NAGDGOBA@DI?3?5Failed?5to?5create?5device?$AA@ openXinputDevice $pdata$6$openXinputDevice $chain$6$openXinputDevice $pdata$3$openXinputDevice $chain$3$openXinputDevice $pdata$4$openXinputDevice $chain$4$openXinputDevice $pdata$2$openXinputDevice $chain$2$openXinputDevice $pdata$1$openXinputDevice $chain$1$openXinputDevice $pdata$openXinputDevice $unwind$openXinputDevice __imp__strdup pollJoystickState $pdata$8$pollJoystickState $chain$8$pollJoystickState $pdata$7$pollJoystickState $chain$7$pollJoystickState $pdata$6$pollJoystickState $chain$6$pollJoystickState $pdata$4$pollJoystickState $chain$4$pollJoystickState $pdata$pollJoystickState $unwind$pollJoystickState __real@3f800000 __real@42ff0000 __real@40c0f88000000000 __real@40bea90000000000 __real@46fffe00 __real@46ffff00 __real@3f000000 _glfwTerminateJoysticksWin32 $pdata$_glfwTerminateJoysticksWin32 $unwind$_glfwTerminateJoysticksWin32 _glfwDetectJoystickConnectionWin32 $pdata$1$_glfwDetectJoystickConnectionWin32 $chain$1$_glfwDetectJoystickConnectionWin32 $pdata$0$_glfwDetectJoystickConnectionWin32 $chain$0$_glfwDetectJoystickConnectionWin32 $pdata$_glfwDetectJoystickConnectionWin32 $unwind$_glfwDetectJoystickConnectionWin32 ??_C@_0CJ@HDJDHLNO@Failed?5to?5enumerate?5DirectInput8@ _glfwDetectJoystickDisconnectionWin32 $pdata$_glfwDetectJoystickDisconnectionWin32 $unwind$_glfwDetectJoystickDisconnectionWin32 _glfwPlatformJoystickPresent _glfwPlatformGetJoystickAxes $pdata$_glfwPlatformGetJoystickAxes $unwind$_glfwPlatformGetJoystickAxes _glfwPlatformGetJoystickButtons $pdata$_glfwPlatformGetJoystickButtons $unwind$_glfwPlatformGetJoystickButtons _glfwPlatformGetJoystickName $pdata$_glfwPlatformGetJoystickName $unwind$_glfwPlatformGetJoystickName _glfwInitJoysticksWin32 $pdata$_glfwInitJoysticksWin32 $unwind$_glfwInitJoysticksWin32 ??_C@_0BP@JPGGHMBM@DI?3?5Failed?5to?5create?5interface?$AA@ __imp_GetModuleHandleW 
/201            1459697978              100666  16095     `
d�> :9W�$  �       .drectve        s   �	               
 .debug$S        �   7
              @ B.rdata             �
              @@@.text              �
               P`.text             �
  �      8    P`.pdata             
           @0@.xdata             7  G         @0@.pdata             e  q         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �              @0@.rdata             �              @@@.rdata             
              @@@.rdata          	                 @@@.rdata                           @@@.rdata             6              @@@.rdata             A              @@@.rdata             P              @@@.rdata             f              @@@.rdata             t              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.rdata          !   �              @@@.rdata                           @@@.rdata             (              @@@.rdata              4              @@@.rdata          
   T              @@@.text           u   ^  �          P`.pdata             K  W         @0@.xdata             u              @0@.text           �  }  W      y    P`.pdata                        @0@.xdata             ;              @0@.text             G  S      
    P`.pdata             �  �         @0@.xdata             �           @0@.rdata          &                 @@@.rdata             C              @@@.rdata          &   Q              @@@.text           �   w             P`.pdata             D   P          @0@.xdata             n   �          @0@.pdata             �   �          @0@.xdata             �   �          @0@.pdata             �   !         @0@.xdata             &!              @0@.text           �   2!  �!          P`.pdata             "   "         @0@.xdata             >"              @0@.text           �   R"  #          P`.pdata             �#  �#         @0@.xdata             �#              @0@.text           X   �#  %$          P`.pdata             �$  �$         @0@.xdata             �$              @0@.text              �$  �$          P`.rdata             �$              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   ~   @     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\win32_init.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler   睻Mo裣埶   0嬃肏冹XH�
    �    H�`  H吚uH�    �  �    3繦兡X肏�    H嬋�    H�
    H�h  �    H��  H吚uH�    �  �    3繦兡X肏�    H嬋H塡$P�    H�
�  H�    H��  �    H�
    H��  �    H�p  H吚tH�    H嬋�    H�x  H�
    H�    3跦塋$(H�
    H塂$ H塋$0H�
    H塡$HH塋$8H�
    H塋$@H嬋�    H��  H吚uH婦�(H�肏吚u揠2H�    H嬋�    H�
�  H�    H��  �    H��  H�
    �    H媆$PH��  H吚t2H�    H嬋�    H�
�  H�    H��  �    H��  H�
    �    H��  H吚tH�    H嬋�    H��  �   H兡X�   b    
   _       ^        ]    *   Z    8   Y    A   V    H   U    O   ^    U   _    \   ^    h   R    r   Z    �   O    �   V    �   ^    �   L    �   ^    �   V    �   I    �   ^    �   _    �   ^    �   F    �   V    �   ^    �   C    �   @    �   =    
  :      7    ,  _    3  ^    N  4    W  V    ^  ^    e  1    l  ^    r  V    y  ^    �  .    �  _    �  ^    �  +    �  V    �  ^    �  (    �  ^    �  V    �  ^    �  %    �  _    �  ^    �  "    �  V    �  ^    �            
       
           !       �          
       
           �   �          
       
           ! 4
     �          
       
               �           
       
            �  SetProcessDpiAwareness shcore.dll DwmFlush DwmIsCompositionEnabled dwmapi.dll XInputGetState XInputGetCapabilities xinput1_1.dll xinput1_2.dll xinput9_1_0.dll xinput1_4.dll xinput1_3.dll DirectInput8Create dinput8.dll ChangeWindowMessageFilterEx SetProcessDPIAware Win32: Failed to load user32.dll user32.dll timeGetTime Win32: Failed to load winmm.dll winmm.dll H冹(H�
�  H吷t�    H�
p  H吷t�    H�
`  H吷t�    H�
�  H吷t�    H�
�  H吷t�    H�
�  H吷t�    H兡(�   ^       l       ^    $   l    +   ^    6   l    =   ^    H   l    O   ^    Z   l    a   ^    l   l        u           e       e       k     B  H塡$WH冹 H��  A�   兪�H嬎�    H�=�  A负  H嬒兪��    �1   ��  9 0 f��  �2   f��  �3   f��  �4   f��  �5   f��  �6   f��  �7   f��  �8   f��  窤   f��  窧   f�   窩   f��  窪   f��  窫   f��  窮   f��  窯   f��  窰   f��  窱   f��  窲   f��  窴   f��  窵   f��  窶   f�  窷   f�  窸   f��  窹   f��  窺   f��  窻   f��  窼   f��  窽   f��  窾   f��  竀   f��  竁   f��  竂   f��  竃   f��  竄   f��  �'   f��  竆   f��  �,   f�  �=   f��  竊   f��  竅   f��  �-   f��  �.   f�  竇   f��  �;   f��  �/   f�
  涪   f�L  �  f��  �  f�F  �
  f�>  �  f��  �   f��  �  f�.  �  f�D  竆  f�Z  �  f�B  �
  f�2  �  f�*  f�,  �    f�  �  f��  �  f�  �  f�*  �  f�,  �"  f�  �#  f�  �$  f�  �%  f�  �&  f�  �'  f�   �(  f�"  �)  f�$  �*  f�&  �+  f�(  �,  f�N  �-  f�P  �.  f�h  �/  3蒮�j  �0  f�l  �1  f�n  �2  f�p  �3  f�r  �4  f�t  �5  f�v  �6  f�x  �7  f�z  �8  f�|  �9  f��  竀  f�  窾  f��  窽  f��  竁  f�V  �  f�  竄  f�  竃  f��  竂  f�  竅  f�X  �  f�@  �  f�6  �  f�:  �	  f�0  窣  f�D  窤  f�>  窧  f�@  窩  f�B  窪  f�6  窫  f�8  窮  f�:  窯  f�.  窰  f�0  窱  f�2  窷  f�<  窲  f�F  窴  f�
  窸  f��  窵  f�  窶  f�4  �f吚~H坷f�G�罤兠侚   |釮媆$0H兡 _�
   ^       v    %   ^    6   v    A   ^    L   ^    X   ^    d   ^    p   ^    |   ^    �   ^    �   ^    �   ^    �   ^    �   ^    �   ^    �   ^    �   ^    �   ^    �   ^       ^      ^      ^    $  ^    0  ^    <  ^    H  ^    T  ^    `  ^    l  ^    x  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^      ^      ^       ^    ,  ^    8  ^    D  ^    P  ^    \  ^    h  ^    t  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^      ^      ^    #  ^    /  ^    ;  ^    G  ^    S  ^    _  ^    k  ^    w  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    	  ^      ^    !  ^    -  ^    9  ^    E  ^    Q  ^    ]  ^    i  ^    u  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^    �  ^      ^      ^      ^    )  ^    5  ^    A  ^    M  ^    Y  ^    e  ^    q  ^    }  ^    �  ^    �  ^    �  ^    �  ^        �          o       o       u    
 
4 
2p@SH侅�   H�    H3腍墑$�   3�    3蒐�    H塋$XH塂$PH塋$HH荄$@���荄$8   荄$0   塋$(塋$ H�    �   A�   �    H嬝H吚uH�    �  �    3离b3繦峊$`E3繦塂$`H塂$pH塂$hH塂$x�    H嬎塂$l�   荄$`    塂$p�   荄$d   塂$t�   塂$x�    H嬅H媽$�   H3惕    H伳�   [�   �       �    (   �    _   �    p   �       �    �   Z    �       �       �       �       �   �    �   �                  y       y            	 0      �      �    Win32: Failed to create helper window G L F W 3 0   G L F W   h e l p e r   w i n d o w   H塼$WH冹03繦嬹L嬃塂$(A兩�3夜辇  H塂$ �    Hc鴧纔
3繦媡$HH兡0_肏嬒�   H塡$@�    A兩�L嬈3夜辇  墊$(H嬝H塂$ �    吚uH嬎�    H媆$@3繦媡$HH兡0_肏媡$HH嬅H媆$@H兡0_�(   �    O   �    o   �    |   �    �   �           �       �       �    !   4     H          �       �       �    H   �           �       �       �    ! 4     H          �       �       �        H           �       �       �    
 
d	 
RpH塴$H塼$WH冹@3鞨嬹L嬃H塴$8H塴$0A兩�3夜辇  塴$(H塴$ �    Hc鴧纔3繦媗$XH媡$`H兡@_肏嬒�   H塡$P�    H塴$8H塴$0A兩�L嬈3夜辇  墊$(H嬝H塂$ �    吚u
H嬎�    3离H嬅H媆$PH媗$XH媡$`H兡@_�7   �    c   �    �   �    �   �        �           �       �       �    a a4
 d T rpH冹(�    吚u3繦兡(肔�P  E3�3夜    �    A�   E3�3夜   �    �    吚t描    L��  M呟t
�   A�与H��  H吚t�需    吚t戣    H�H  H吚t��    �    吚刵����    �    �   H兡(�   �       ^    '   �    =   �    B   
    K   o    R   ^    h   ^    t   �    }   y    �   ^    �   �    �   �    �   �    �   �        �           �       �       �     B  H冹(H�
H  H吷t�    �    D�P  3褼岼�   �    H�
X  �    �    �    �    H兡(�       ^       �       �       ^    /   �    6   ^    <   �    A   �    F   �    K   �    T   e        X           �       �       �     B  H�    �   �    3.2.0 Win32 WGL VisualC @comp.idov� ��   .drectve       s                 .debug$S       �                 .rdata                E靜=                    .text                 d侅=                    .text            8   .�=�         $           .pdata               忉橝        2           .xdata               /f┼        I           .pdata               |%吕        `           .xdata      	         埪庴        w       	    .pdata      
         脤        �       
    .xdata                q�'�        �           .rdata                ~��         �           .rdata      
          5�+�         �       
    .rdata         	       掦;�                   .rdata                à捧         *          .rdata                暝V�         Z          .rdata                :kz         }          .rdata                �         �          .rdata                N狄�         �          .rdata                嗲FE         �          .rdata                �$t                   .rdata                �$�         F          .rdata                E�         l          .rdata                �:宅         �          .rdata                蝘         �          .rdata                �;酀         �          .rdata                ,閡h                   .rdata         !       ��         @          .rdata                沴凐         {              �           .rdata                o╡�         �              �           .rdata                 fya�         �          _glfw                #           .rdata          
       柰=         6           .text       !   u      脵9         W      !    .pdata      "         魺颁!        e      "    .xdata      #          �9�!        z      #        �           .text       $   �  y   %螋R         �      $    .pdata      %         sE迮$        �      %    .xdata      &          %蚘%$        �      &    memset           .text       '     
   榡�         �      '    .pdata      (         �伛'        �      (    .xdata      )         趗�'              )        )               :           .rdata      *   &       旫炘         \      *        �           .rdata      +          讧煰         �      +    .rdata      ,   &       虌靸         �      ,        `               w               �           .text       -   �      茦cJ         �      -    .pdata      .         k鮅b-        �      .    .xdata      /         吊-        �      /    .pdata      0         俽蔥-              0    .xdata      1         維f�-        H      1    .pdata      2         X賦�-        t      2    .xdata      3          g]
�-        �      3        �               �               �           $LN6        -    .text       4   �      囋�=         �      4    .pdata      5         o�6G4              5    .xdata      6           <4        H      6        s           $LN6        4    .text       7   �      l�v         �      7    .pdata      8         D褃X7        �      8    .xdata      9          �9�7        �      9        �               �               �               	               #	               A	               ]	           $LN14       7    .text       :   X      Vl�6         ~	      :    .pdata      ;         s杳�:        �	      ;    .xdata      <          �9�:        �	      <        �	               �	               
               '
               G
           $LN6        :    .text       =         覲A         [
      =    .rdata      >          }�?b         y
      >    �
  GUID_DEVINTERFACE_HID UIntToPtr loadLibraries $pdata$1$loadLibraries $chain$1$loadLibraries $pdata$0$loadLibraries $chain$0$loadLibraries $pdata$loadLibraries $unwind$loadLibraries ??_C@_0BH@DGFLCCMF@SetProcessDpiAwareness?$AA@ ??_C@_0L@DMFDIJCG@shcore?4dll?$AA@ ??_C@_08PEMNBIMD@DwmFlush?$AA@ ??_C@_0BI@KJPLJBHH@DwmIsCompositionEnabled?$AA@ ??_C@_0L@FACOJKPJ@dwmapi?4dll?$AA@ ??_C@_0P@FCCEJHCC@XInputGetState?$AA@ ??_C@_0BG@ELBNFOBA@XInputGetCapabilities?$AA@ ??_C@_0O@ONJGDDHG@xinput1_1?4dll?$AA@ ??_C@_0O@GLACEBNI@xinput1_2?4dll?$AA@ ??_C@_0BA@GHGALGFD@xinput9_1_0?4dll?$AA@ ??_C@_0O@LNFLKCMF@xinput1_4?4dll?$AA@ ??_C@_0O@KAFOJCHN@xinput1_3?4dll?$AA@ ??_C@_0BD@IJCCAMHG@DirectInput8Create?$AA@ ??_C@_0M@ONOEDIJO@dinput8?4dll?$AA@ ??_C@_0BM@OCGPLDNC@ChangeWindowMessageFilterEx?$AA@ ??_C@_0BD@ENICNPLM@SetProcessDPIAware?$AA@ ??_C@_0CB@HHJIGJHK@Win32?3?5Failed?5to?5load?5user32?4dll@ ??_C@_0L@GMPLCCII@user32?4dll?$AA@ __imp_GetProcAddress ??_C@_0M@DEPJBPP@timeGetTime?$AA@ _glfwInputError ??_C@_0CA@EDJENDDE@Win32?3?5Failed?5to?5load?5winmm?4dll?$AA@ __imp_LoadLibraryA ??_C@_09CBHKFKGB@winmm?4dll?$AA@ freeLibraries $pdata$freeLibraries $unwind$freeLibraries __imp_FreeLibrary createKeyTables $pdata$createKeyTables $unwind$createKeyTables createHelperWindow $pdata$createHelperWindow $unwind$createHelperWindow __GSHandlerCheck __imp_RegisterDeviceNotificationW ??_C@_0CG@LDJIJKNI@Win32?3?5Failed?5to?5create?5helper?5w@ __imp_CreateWindowExW ??_C@_1O@IBNLCBOC@?$AAG?$AAL?$AAF?$AAW?$AA3?$AA0?$AA?$AA@ ??_C@_1CG@OEOKOPIA@?$AAG?$AAL?$AAF?$AAW?$AA?5?$AAh?$AAe?$AAl?$AAp?$AAe?$AAr?$AA?5?$AAw?$AAi?$AAn?$AAd?$AAo?$AAw?$AA?$AA@ __imp_GetModuleHandleW __security_cookie __security_check_cookie _glfwCreateWideStringFromUTF8Win32 $pdata$2$_glfwCreateWideStringFromUTF8Win32 $chain$2$_glfwCreateWideStringFromUTF8Win32 $pdata$0$_glfwCreateWideStringFromUTF8Win32 $chain$0$_glfwCreateWideStringFromUTF8Win32 $pdata$_glfwCreateWideStringFromUTF8Win32 $unwind$_glfwCreateWideStringFromUTF8Win32 __imp_free __imp_calloc __imp_MultiByteToWideChar _glfwCreateUTF8FromWideStringWin32 $pdata$_glfwCreateUTF8FromWideStringWin32 $unwind$_glfwCreateUTF8FromWideStringWin32 __imp_WideCharToMultiByte _glfwPlatformInit $pdata$_glfwPlatformInit $unwind$_glfwPlatformInit _glfwInitJoysticksWin32 _glfwInitTimerWin32 _glfwInitWGL _glfwPlatformPollEvents _glfwRegisterWindowClassWin32 __imp_SystemParametersInfoW _glfwInitThreadLocalStorageWin32 _glfwPlatformTerminate $pdata$_glfwPlatformTerminate $unwind$_glfwPlatformTerminate _glfwTerminateThreadLocalStorageWin32 _glfwTerminateJoysticksWin32 _glfwTerminateWGL _glfwUnregisterWindowClassWin32 __imp_DestroyWindow _glfwPlatformGetVersionString ??_C@_0BI@DOABJMKC@3?42?40?5Win32?5WGL?5VisualC?$AA@ 
/233            1459697978              100666  28496     `
d唫 :9W�8        .drectve        s   d               
 .debug$S        �   �              @ B.text           �   _             P`.pdata             H  T         @0@.xdata             r  �         @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata             �           @0@.xdata             &  >         @0@.pdata             \  h         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �              @0@.text              �               P`.pdata               
         @0@.xdata             +              @0@.text              3               P`.pdata             J  V         @0@.xdata             t              @0@.text              |               P`.pdata             �  �         @0@.xdata             �              @0@.text              �               P`.pdata             �  �         @0@.xdata                           @0@.text                             P`.pdata             %  1         @0@.xdata             O              @0@.text              W               P`.pdata             u  �         @0@.xdata             �              @0@.text              �               P`.text           �   �  y          P`.pdata             7  C         @0@.xdata             a              @0@.text           t  i  �      J    P`.rdata             �               @@@.text           �   �   u!          P`.pdata             �!  �!         @0@.xdata             
"              @0@.text           (   "  ="          P`.pdata             Q"  ]"         @0@.xdata             {"              @0@.text              �"  �"          P`.text              �"  �"          P`.text              �"  #          P`.text           *    #  J#          P`.text           .   h#  �#          P`.pdata             �#  �#         @0@.xdata             �#              @0@.text           *   �#  $          P`.text           !   .$  O$          P`.text           J   m$  �$          P`.pdata             �$  �$         @0@.xdata             �$              @0@.text           U   %  \%          P`.pdata             �%  �%         @0@.xdata             �%              @0@.rdata             �%              @@@.text           *   �%  &          P`.text           T   #&  w&          P`.pdata             �&  �&         @0@.xdata             �&              @0@.text              �&  �&          P`.text              �&  '          P`.text              7'  Q'          P`.text           =   o'  �'          P`.pdata             �'  �'         @0@.xdata             �'              @0@.text           .   (  4(          P`.pdata             R(  ^(         @0@.xdata             |(              @0@.text              �(  �(          P`.text           |  �(  8*          P`.pdata             Z+  f+         @0@.xdata             �+              @0@.rdata             �+              @@@.text           )   �+  �+          P`.pdata             �+  �+         @0@.xdata             ,              @0@.text           Y   ,  p,          P`.pdata             �,  �,         @0@.xdata             �,              @0@.text              �,  �,          P`.text           )   �,  -          P`.pdata             +-  7-         @0@.xdata             U-              @0@.text           3   ]-  �-          P`.pdata             �-  �-         @0@.xdata             �-              @0@.text           3   �-  	.          P`.pdata             .  ).         @0@.xdata             G.              @0@.text           3   O.  �.          P`.pdata             �.  �.         @0@.xdata             �.              @0@.text           3   �.  �.          P`.pdata             /  /         @0@.xdata             9/              @0@.text           3   A/  t/          P`.pdata             �/  �/         @0@.xdata             �/              @0@.text           3   �/  �/          P`.pdata             0  
0         @0@.xdata             +0              @0@.text           3   30  f0          P`.pdata             z0  �0         @0@.xdata             �0              @0@.text              �0  �0          P`.text           1   �0  1          P`.pdata             =1  I1         @0@.xdata             g1              @0@.text           T   o1  �1          P`.rdata             �1              @@@.rdata             2              @@@.text           1   2  H2          P`.pdata             p2  |2         @0@.xdata             �2              @0@.text           �  �2  �5      (    P`.pdata             &7  27         @0@.xdata             P7  `7         @0@.pdata             ~7  �7         @0@.xdata             �7  �7         @0@.pdata             �7  �7         @0@.xdata             8  8         @0@.pdata             28  >8         @0@.xdata             \8  p8         @0@.pdata             �8  �8         @0@.xdata             �8  �8         @0@.rdata             �8              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   z   <     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\window.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler   @UH冹0H嬮呉tH�
�   H媮h  H吚剹   H兡0]H�郒塡$@3跦塼$HH��   H媮h  H墊$PH吚t3�袐鸋嵉�    �>uE3蒃3缷譎嬐塡$ �    �荋�苼�\  ~贖媡$HH嵔�   �?uE3蒃3缷親嬐�    �肏�莾�~酘媩$PH媆$@H兡0]�   (    ;   (    u   '    �   &    �   �           )       )       
    !       ,          )       )       %    �   �           )       )           !   t
 ,   3          )       )           3   �           )       )           ! t
 d	 ,   3          )       )           ,   3           )       )           ! 4     ,          )       )       %        ,           )       )       %     RPH冹(H媮H  H吚t�蠬兡(�               3       3       2     B  H冹(H媮P  H吚t�蠬兡(�               =       =       <     B  H冹(H媮p  H吚t�蠬兡(�               G       G       F     B  H冹(H媮x  H吚t�蠬兡(�               Q       Q       P     B  H冹(H媮`  H吚t�蠬兡(�               [       [       Z     B  H冹(H媮X  茿   H吚t�蠬兡(�               e       e       d     B  H塓@肏冹(�=     u3夜  H兡(�    H�
    3褹赴   �    �x     H�|      �X      �\      �`      �d      �h      �       �      �      �      �      �      �8      ��   ����H兡(�   s       r        (    -   t    3   (    >   (    H   (    R   (    \   (    f   (    p   (    z   (    �   (    �   (    �   (    �   (    �   (    �   (    �   (        �           u       u       q     B  �=     u3夜  �    侚 彅   剣   崄���凐嚛  L�    H楢媱�    I��3绤�暲�X   �3绤�暲�`   �3绤�暲�d   �3绤�暲�h   �3绤�暲�l   �3绤�暲�p   �3绤�暲�\   脡    脕�  彴   劊   崄�凐�	  L�    H楢媱�    I�鄩   脡   脡   脡   脡   脡   脡   脡    脡$   脡(   �3绤�暲�,   �3绤�暲�8   脡0   �3绤�暲�4   脡�   脡x   脥侢啐�凐wcL�    H楢媱�    I�鄩|   脡�   脡�   �3绤�暲��   �3绤�暲��   �3绤�暲��   脡�   脡�   肈嬃H�    �  �    �                                                                                                                                   s       r    9   �    C   �    U   (    c   (    q   (       (    �   (    �   (    �   (    �   (    �   �    �   �    �   (    �   (    �   (      (    
  (      (      (      (    &  (    -  (    ;  (    I  (    P  (    ^  (    e  (    l  (      �    �  �    �  (    �  (    �  (    �  (    �  (    �  (    �  (    �  (    �  |    �  r    �  �    �  y    �  �       �      �      �      �      �      �      �      �       �    $  �    (  �    ,  �    0  �    4  �    8  �    <  �    @  �    D  �    H  �    L  �    P  �    T  �    X  y    \  �    `  �    d  �    h  ~    l  }    p      Invalid window hint %i @SH冹 �=     H嬞u3夜  H兡 [�    H吷to3襀伭H  D岯x�    �    H;豼3设    H��   3蒆;肏D罤嬎H��   �    H9�   L��   tfD  M�I9u鳫�H嬎I��    H兡 [�   s       r    6   t    ;   �    G   �    N   (    a   (    f   �    m   (    t   (    �   �        �           �       �       �     20H冹(�=     u3夜  �    3繦兡(脣AH兡(�   s       r        (           �       �       �     B  �=     u3夜  �    塓�   s       r    �=     u3夜  �    �       s       r       �    �=     u3夜  �    �       s       r       �    3繦呉t�M吚tA� 9    u3夜  �    �       s    !   r    &   �    H冹(�=     u3夜  H兡(�    H儁@ u�    H兡(�   s       r    %   �        .           �       �       �     B  3繦呉t�M吚tA� 9    u3夜  �    �       s    !   r    &   �    �=     u3夜  �    塓(D堿,�       s       r       �    H冹8�=     u3夜  H兡8�    H儁@ 婦$`塓P堿\D堿TD塈Xu儁 t	塂$ �    H兡8�   s       r    A   �        J           �       �       �     b  H冹(�=     u3夜  H兡(�    H儁@ 塓`D堿du%儁 tE吚uH�    �  H兡(�    �    H兡(�   s       r    9   �    G   r    L   �        U           �       �       �     B  Denominator cannot be zero 3繦呉t�M吚tA� 9    u3夜  �    �       s    !   r    &   �    H冹8E3襀呉tD�M吚tE�M吷tE�H婦$`H吚tD�D9    u3夜  H兡8�    H塂$ �    H兡8�/   s    A   r    K   �        T                         �     b  �=     u3夜  �    �       s       r          �=     u3夜  �    �       s       r          �=     u3夜  �    �       s       r          @SH冹 �=     H嬞u3夜  H兡 [�    H儁@ u
�    H嬎�    H兡 [�   s       r    +      3          =                           20H冹(�=     u3夜  H兡(�    H儁@ u�    H兡(�   s       r    %   "       .           #      #      !    B  �=     u3夜  �    �       s       r          H冹(�=     u3夜  �    3繦兡(脕�  nta崅���凐囜   L�    H楢媱�    I�郒兡(�    H兡(�    H兡(�    H兡(�    婣H兡(脣AH兡(脣AH兡(脣侌  H兡(脥傼啐�凐wyL�    H楢媱�    I�鄫侓  H兡(脣侙  H兡(脣侟  H兡(脣�  H兡(脣�   H兡(脣�  H兡(脣�  H兡(脣�  H兡(脣�  H兡(肈嬄H�    �  �    3繦兡(�                                                                        s       r    <   �    F   I   T   G   ]   E   f   C   o   A   �   �    �   =      3   *  r    8  H   <  F   @  @   D  D   H  ?   L  0   P  >   T  B   X  <   \  ;   `  :   d  9   h  8   l  7   p  6   t  5   x  4       |          J      J      /    B  Invalid window attribute %i H冹(�=     u3夜  �    3繦兡(肏婣@H兡(�   s       r        )           T      T      S    B  H冹H�=     L嬟L嬔u3夜  H兡H�    婽$p媱$�   塓(婰$x塂$0塋$(塗$ A塉,I嬘I嬍A塀<�    H兡H�   s       r    P   ^       Y           _      _      ]    �  �=     u3夜  �    H塓 �   s       r    H冹(�=     u3夜  �    3繦兡(肏婣 H兡(�   s       r        )           l      l      k    B  H冹(�=     u3夜  �    3繦兡(肏媮H  H墤H  H兡(�   s       r        3           v      v      u    B  H冹(�=     u3夜  �    3繦兡(肏媮P  H墤P  H兡(�   s       r        3           �      �          B  H冹(�=     u3夜  �    3繦兡(肏媮X  H墤X  H兡(�   s       r        3           �      �      �    B  H冹(�=     u3夜  �    3繦兡(肏媮`  H墤`  H兡(�   s       r        3           �      �      �    B  H冹(�=     u3夜  �    3繦兡(肏媮h  H墤h  H兡(�   s       r        3           �      �      �    B  H冹(�=     u3夜  �    3繦兡(肏媮p  H墤p  H兡(�   s       r        3           �      �      �    B  H冹(�=     u3夜  �    3繦兡(肏媮x  H墤x  H兡(�   s       r        3           �      �      �    B  �=     u3夜  �    �       s       r       �   H冹(�=     u3夜  H兡(�    H�=�    t�    H兡(�   s       r        (    (   �       1           �      �      �    B  �=     f(衭3夜  �    f.纙ufW纅/聎f/    w	f(麻    fI~蠬�    �  �       s       r    /   �   :   �   F   �   P   r    Invalid time %f �������H冹(�=     u3夜  H兡(�    H�=�    t�    H兡(�   s       r        (    (   �       1           �      �      �    B  @UVWATH峫$蒆侅�   H�    H3腍塃�=     孂H婱M嬦嬺u3夜  �    3篱�  �巼  呉�  H�x       
   H塃桯��   H塃烪��   )E�)M�    
0   H塃��   H塃疕��   )E�)M��@   H塃稨��   �EH塃縃�H   H塎縃塂$(H�P   墊$(H塂$0H�X   塗$,H塂$8H�`   L塂$0H塂$@H�h   H塃嘓�p   H塃廐吷t児�   u3夜
  �    3篱�   H峂楄    吚勫��鸿  �   H墱$�   �    H�
�   H嬝H�H��   墄(塸,婱蠅H0婱訅H4婱讐H8�
�   L塦@塇<婦$8塁婦$@塁婨噳C婨嬊儉   @ H荂P����H荂X����H荂`����塁�    L峂螸岴桯峊$(H嬎H孁�    吚t儅� tPH嬎�    H峂楄    吚u3H嬎�    H嬒�    3繦嫓$�   H婱H3惕    H伳�   A\_^]肏嬒�    H儃@ tKL岲$$H峊$ H嬎�    婦$ H嬎�+卵鴉n葖D$$�+卵嫔fn序Kp�嬉�Sx�    H嬅雲億$< tH嬎�    儅� tH嬎�    H嬅閌���D嬍H�    D嬊�  �    3篱J���      "   s    <   r    Z   (    a   (    h   (    s   (    ~   (    �   (    �   (    �   (    �   (    �   (    �   (    �   (    �   (    �   (      (      (    !  (    ?  r    O     o      v  (    �  (    �  (    �  �      �     �      �   (  �    0  �    F     [  �    t  �    �  �   �     �     �  �   �  r    �  �                      �   !       e                     �   W  �                      �   !   4     e                     �   >  W                      �   !       e                     �   e  >                      �   ! 4     e                     �       e                      �      �p`P    �      �   Invalid window size %ix%i @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          �      鮴>�                    .pdata               C簆�                   .xdata               鯘�        9           .pdata               r謝        X           .xdata               }�!2        w           .pdata               xW        �           .xdata      	         �撝        �       	    .pdata      
         缏y        �       
    .xdata               ��        �           .pdata               w佼                  .xdata      
          �捡        /      
        M               b           _glfw            $LN19           .text                 鑩4         p          .pdata               28~v        �          .xdata                �9�        �          $LN4            .text                 覫M�         �          .pdata               28~v        �          .xdata                �9�        �          $LN4            .text                 �2r�         	          .pdata               28~v        !          .xdata                �9�        @          $LN4            .text                 岘a�         `          .pdata               28~v        z          .xdata                �9�        �          $LN4            .text                 0UP         �          .pdata               28~v        �          .xdata                �9�        �          $LN4            .text                 aq.�                   .pdata               #1i        .          .xdata                �9�        R          $LN4            .text                  眗竒         w           .text       !   �      SB1         �      !    .pdata      "         鍾ａ!        �      "    .xdata      #          �9�!        �      #        �               �           memset           $LN4        !    .text       $   t  J   旟�         
      $    $LN1    �  $    .rdata      %          x<               %    $LN3    �  $    $LN4    �  $    $LN5    �  $    $LN6    �  $    $LN7    �  $    $LN8    �  $    $LN9    �  $    $LN10   �  $    $LN41   P  $    $LN2    c  $    $LN19   U  $    $LN20   N  $    $LN21   @  $    $LN22   2  $    $LN23   +  $    $LN24   $  $    $LN25     $    $LN26     $    $LN27     $    $LN28     $    $LN29     $    $LN30   �   $    $LN31   �   $    $LN32   �   $    $LN42     $    $LN12   �   $    $LN13   �   $    $LN14   �   $    $LN15   v   $    $LN16   h   $    $LN17   Z   $    $LN18   L   $    $LN43   �  $        N           .text       &   �      K泋         Z      &    .pdata      '          �&        l      '    .xdata      (          （亵&        �      (        �               �               �               �           $LN11       &    .text       )   (      鮔               )    .pdata      *         銀�*)              *    .xdata      +          �9�)        7      +    $LN4        )    .text       ,         郾Z         U      ,    .text       -         p葘         n      -        �           .text       .         p葘         �      .        �           .text       /   *      じ         �      /        �           .text       0   .      y         �      0    .pdata      1         dp0              1    .xdata      2          �9�0              2        7           $LN5        0    .text       3   *      じ         Q      3        c           .text       4   !      O/�         ~      4        �           .text       5   J      ��         �      5    .pdata      6         %轢�5        �      6    .xdata      7          1�75        �      7                   $LN6        5    .text       8   U      ^4         #      8    .pdata      9         �8        <      9    .xdata      :          �9�8        \      :        }           .rdata      ;          j緐         �      ;    $LN7        8    .text       <   *      じ         �      <        �           .text       =   T      <Z砧               =    .pdata      >         <齦�=        #      >    .xdata      ?          1�7=        A      ?        `           $LN8        =    .text       @         p葘         �      @        �           .text       A         p葘         �      A        �           .text       B         p葘         �      B        �           .text       C   =      栯健         		      C    .pdata      D         現�C        	      D    .xdata      E          （亵C        .	      E        E	               ^	           $LN5        C    .text       F   .      y         v	      F    .pdata      G         dpF        �	      G    .xdata      H          �9�F        �	      H        �	           $LN5        F    .text       I         p葘         �	      I    .text       J   |     H諰r         �	      J    .pdata      K         k張]J        �	      K    .xdata      L          �9�J        	
      L    $LN18     J    .rdata      M          冤�         %
      M    $LN1      J    $LN2      J    $LN3    �   J    $LN4    �   J    $LN5    �   J    $LN6    �   J    $LN7    �   J    $LN8    �   J    $LN9    �   J    $LN24   X  J    $LN11   �   J    $LN12   {   J    $LN13   s   J        _
           $LN14   j   J        |
           $LN15   a   J        �
           $LN16   X   J        �
           $LN17   O   J    $LN25   8  J    $LN27       J    .text       N   )      逻         �
      N    .pdata      O         }y9鍺        �
      O    .xdata      P          �9�N               P    $LN4        N    .text       Q   Y      �oy               Q    .pdata      R         龛iJQ        2      R    .xdata      S          懐j濹        N      S        k           $LN4        Q    .text       T         萾
"         �      T    .text       U   )      樞
�         �      U    .pdata      V         }y9鎁        �      V    .xdata      W          �9�U        �      W    $LN4        U    .text       X   3      汬�         �      X    .pdata      Y         濼BX              Y    .xdata      Z          �9�X        5      Z    $LN4        X    .text       [   3      #	         V      [    .pdata      \         濼B[        p      \    .xdata      ]          �9�[        �      ]    $LN4        [    .text       ^   3      t4jH         �      ^    .pdata      _         濼B^        �      _    .xdata      `          �9�^        �      `    $LN4        ^    .text       a   3      寛_         
      a    .pdata      b         濼Ba        0
      b    .xdata      c          �9�a        T
      c    $LN4        a    .text       d   3      E盋�         y
      d    .pdata      e         濼Bd        �
      e    .xdata      f          �9�d        �
      f    $LN4        d    .text       g   3      o         �
      g    .pdata      h         濼Bg        �
      h    .xdata      i          �9�g              i    $LN4        g    .text       j   3      ぽ         ?      j    .pdata      k         濼Bj        ^      k    .xdata      l          �9�j        �      l    $LN4        j    .text       m         p葘         �      m        �           .text       n   1      ~翕;         �      n    .pdata      o         鉙gIn        �      o    .xdata      p          �9�n        �      p                   $LN5        n    .text       q   T      �:]         &      q    .rdata      r          鰀#�         <      r        i           .rdata      s          ��         �      s    _fltused         .text       t   1      ~翕;         �      t    .pdata      u         鉙gIt        �      u    .xdata      v          �9�t        �      v        �           $LN5        t    .text       w   �  (   苾W6               w    .pdata      x         T"Sw              x    .xdata      y         嘜+噖        /      y    .pdata      z         餯bGw        I      z    .xdata      {         �怜w        c      {    .pdata      |         -�mw        }      |    .xdata      }         嘜+噖        �      }    .pdata      ~         緮w        �      ~    .xdata               1妔誻        �          .pdata      �         k>w        �      �    .xdata      �         ;PF妛        �      �                   .rdata      �          皬q�         '      �        b               |               �               �               �               �               �           $LN19       w      _glfwInputWindowFocus $pdata$4$_glfwInputWindowFocus $chain$4$_glfwInputWindowFocus $pdata$3$_glfwInputWindowFocus $chain$3$_glfwInputWindowFocus $pdata$2$_glfwInputWindowFocus $chain$2$_glfwInputWindowFocus $pdata$0$_glfwInputWindowFocus $chain$0$_glfwInputWindowFocus $pdata$_glfwInputWindowFocus $unwind$_glfwInputWindowFocus _glfwInputMouseClick _glfwInputKey _glfwInputWindowPos $pdata$_glfwInputWindowPos $unwind$_glfwInputWindowPos _glfwInputWindowSize $pdata$_glfwInputWindowSize $unwind$_glfwInputWindowSize _glfwInputWindowIconify $pdata$_glfwInputWindowIconify $unwind$_glfwInputWindowIconify _glfwInputFramebufferSize $pdata$_glfwInputFramebufferSize $unwind$_glfwInputFramebufferSize _glfwInputWindowDamage $pdata$_glfwInputWindowDamage $unwind$_glfwInputWindowDamage _glfwInputWindowCloseRequest $pdata$_glfwInputWindowCloseRequest $unwind$_glfwInputWindowCloseRequest _glfwInputWindowMonitorChange glfwDefaultWindowHints $pdata$glfwDefaultWindowHints $unwind$glfwDefaultWindowHints _glfwInputError _glfwInitialized glfwWindowHint ??_C@_0BH@LBCIHIMD@Invalid?5window?5hint?5?$CFi?$AA@ __ImageBase glfwDestroyWindow $pdata$glfwDestroyWindow $unwind$glfwDestroyWindow __imp_free _glfwPlatformDestroyWindow _glfwPlatformMakeContextCurrent _glfwPlatformGetCurrentContext glfwWindowShouldClose $pdata$glfwWindowShouldClose $unwind$glfwWindowShouldClose glfwSetWindowShouldClose glfwSetWindowTitle _glfwPlatformSetWindowTitle glfwSetWindowIcon _glfwPlatformSetWindowIcon glfwGetWindowPos _glfwPlatformGetWindowPos glfwSetWindowPos $pdata$glfwSetWindowPos $unwind$glfwSetWindowPos _glfwPlatformSetWindowPos glfwGetWindowSize _glfwPlatformGetWindowSize glfwSetWindowSize _glfwPlatformSetWindowSize glfwSetWindowSizeLimits $pdata$glfwSetWindowSizeLimits $unwind$glfwSetWindowSizeLimits _glfwPlatformSetWindowSizeLimits glfwSetWindowAspectRatio $pdata$glfwSetWindowAspectRatio $unwind$glfwSetWindowAspectRatio _glfwPlatformSetWindowAspectRatio ??_C@_0BL@PFPABDCG@Denominator?5cannot?5be?5zero?$AA@ glfwGetFramebufferSize _glfwPlatformGetFramebufferSize glfwGetWindowFrameSize $pdata$glfwGetWindowFrameSize $unwind$glfwGetWindowFrameSize _glfwPlatformGetWindowFrameSize glfwIconifyWindow _glfwPlatformIconifyWindow glfwRestoreWindow _glfwPlatformRestoreWindow glfwMaximizeWindow _glfwPlatformMaximizeWindow glfwShowWindow $pdata$glfwShowWindow $unwind$glfwShowWindow _glfwPlatformFocusWindow _glfwPlatformShowWindow glfwHideWindow $pdata$glfwHideWindow $unwind$glfwHideWindow _glfwPlatformHideWindow glfwFocusWindow glfwGetWindowAttrib $pdata$glfwGetWindowAttrib $unwind$glfwGetWindowAttrib ??_C@_0BM@IFCGFMAI@Invalid?5window?5attribute?5?$CFi?$AA@ _glfwPlatformWindowMaximized _glfwPlatformWindowVisible _glfwPlatformWindowIconified _glfwPlatformWindowFocused glfwGetWindowMonitor $pdata$glfwGetWindowMonitor $unwind$glfwGetWindowMonitor glfwSetWindowMonitor $pdata$glfwSetWindowMonitor $unwind$glfwSetWindowMonitor _glfwPlatformSetWindowMonitor glfwSetWindowUserPointer glfwGetWindowUserPointer $pdata$glfwGetWindowUserPointer $unwind$glfwGetWindowUserPointer glfwSetWindowPosCallback $pdata$glfwSetWindowPosCallback $unwind$glfwSetWindowPosCallback glfwSetWindowSizeCallback $pdata$glfwSetWindowSizeCallback $unwind$glfwSetWindowSizeCallback glfwSetWindowCloseCallback $pdata$glfwSetWindowCloseCallback $unwind$glfwSetWindowCloseCallback glfwSetWindowRefreshCallback $pdata$glfwSetWindowRefreshCallback $unwind$glfwSetWindowRefreshCallback glfwSetWindowFocusCallback $pdata$glfwSetWindowFocusCallback $unwind$glfwSetWindowFocusCallback glfwSetWindowIconifyCallback $pdata$glfwSetWindowIconifyCallback $unwind$glfwSetWindowIconifyCallback glfwSetFramebufferSizeCallback $pdata$glfwSetFramebufferSizeCallback $unwind$glfwSetFramebufferSizeCallback glfwPollEvents _glfwPlatformPollEvents glfwWaitEvents $pdata$glfwWaitEvents $unwind$glfwWaitEvents _glfwPlatformWaitEvents glfwWaitEventsTimeout ??_C@_0BA@KMGHNAFM@Invalid?5time?5?$CFf?$AA@ _glfwPlatformWaitEventsTimeout __real@7fefffffffffffff glfwPostEmptyEvent $pdata$glfwPostEmptyEvent $unwind$glfwPostEmptyEvent _glfwPlatformPostEmptyEvent glfwCreateWindow $pdata$3$glfwCreateWindow $chain$3$glfwCreateWindow $pdata$2$glfwCreateWindow $chain$2$glfwCreateWindow $pdata$1$glfwCreateWindow $chain$1$glfwCreateWindow $pdata$0$glfwCreateWindow $chain$0$glfwCreateWindow $pdata$glfwCreateWindow $unwind$glfwCreateWindow __GSHandlerCheck ??_C@_0BK@KJGCMOHN@Invalid?5window?5size?5?$CFix?$CFi?$AA@ _glfwPlatformSetCursorPos _glfwRefreshContextAttribs _glfwPlatformCreateWindow __imp_calloc _glfwIsValidContextConfig __security_cookie __security_check_cookie /261            1459697978              100666  18437     `
d哢 :9W}%  "      .drectve        s   \
               
 .debug$S        �   �
              @ B.text           m   W  �          P`.pdata                         @0@.xdata             >  N         @0@.pdata             l  x         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �           @0@.pdata                ,         @0@.xdata             J  ^         @0@.pdata             |  �         @0@.xdata             �              @0@.text           h  �        *    P`.rdata             �              @@@.rdata          T   �              @P@.rdata          7   *              @@@.rdata          \   a              @P@.rdata          %   �              @@@.rdata          '   �              @@@.rdata          8   	              @@@.rdata          &   A              @@@.rdata          -   g              @@@.rdata          W   �              @P@.rdata          &   �              @@@.rdata          $                 @@@.rdata          ,   5              @@@.rdata             a              @@@.rdata             x              @@@.rdata          9   �              @@@.rdata          '   �              @@@.rdata             �              @@@.rdata          3   �              @@@.rdata          "   (              @@@.rdata          O   J              @P@.rdata          W   �              @P@.rdata          ]   �              @P@.rdata          !   M              @@@.text           +   n  �          P`.pdata             �  �         @0@.xdata             �              @0@.text           [   �  D          P`.pdata             �  �         @0@.xdata             �              @0@.rdata             �              @@@.text           i   �  >          P`.pdata             �  �         @0@.xdata             �              @0@.text           l   �  ,      	    P`.pdata             �  �         @0@.xdata             �              @0@.rdata          5   �              @@@.text           |   �  i      	    P`.pdata             �  �         @0@.xdata             �              @0@.text           u  �  j      -    P`.pdata             ,!  8!         @0@.xdata             V!  f!         @0@.pdata             �!  �!         @0@.xdata             �!  �!         @0@.pdata             �!  �!         @0@.xdata             "  "         @0@.pdata             4"  @"         @0@.xdata              ^"  ~"         @0@.pdata             �"  �"         @0@.xdata             �"  �"         @0@.pdata             �"  #         @0@.xdata             "#  6#         @0@.pdata             T#  `#         @0@.xdata             ~#  �#         @0@.pdata             �#  �#         @0@.xdata             �#              @0@.rdata             �#              @@@.rdata             �#              @@@.rdata             $              @@@.rdata             $              @@@.rdata             3$              @@@.rdata             H$              @@@.rdata          0   W$              @@@.rdata          5   �$              @@@.rdata          B   �$              @P@.rdata          '   �$              @@@.rdata          5   %%              @@@.rdata             Z%              @@@.rdata          
   p%              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   z   <     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\vulkan.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler   H冹(H塡$03�9  v1H墊$ 孄fD  H�
   H��    �肏兦;  r酘媩$ H�
   �    H�
�   H媆$0H吷t�    H兡(�
   (    #   (    -   '    9   (    G   (    M   '    T   (    d   &    b   m           )       )       
    !                 )       )       %    D   b           )       )           !                )       )              D           )       )           ! t              )       )                         )       )           ! 4               )       )       %                   )       )       %     B  凒�^tT侚e膖D侚G*e膖4侚2e膖$侚�5e膖侚 6e�呾   H�    肏�    肏�    肏�    肏�    肏�    脕蜌;彴   劉   兞
凒嚍   H�    Hc翄寕    H�酘�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    脨                                                                6   �    >   �    F   �    N   }    V   z    ^   w    �   t    �   s    �   q    �   m    �   i    �   e    �   a    �   ]    �   Y    �   U    �   Q    �   M    �   I    �   E    �   A      =    
  9      5      2    "  /    (  6    ,  :    0  >    4  B    8  F    <  J    @  N    D  R    H  V    L  Z    P  r    T  n    X  j    \  f    `  b    d  ^    ERROR: UNKNOWN VULKAN ERROR A swapchain no longer matches the surface properties exactly, but can still be used Too many objects of the type have already been created The requested version of Vulkan is not supported by the driver or is otherwise incompatible A requested feature is not supported A requested extension is not supported A requested layer is not present or could not be loaded Mapping of a memory object has failed The logical or physical device has been lost Initialization of an object could not be completed for implementation-specific reasons A device memory allocation has failed A host memory allocation has failed A return array was too small for the result An event is unsignaled An event is signaled A wait operation has not completed in the specified time A fence or query has not yet completed Success A requested format is not supported on this device A validation layer found an error The display used by a swapchain does not use the same presentable image layout A surface has changed in such a way that it is no longer compatible with the swapchain The requested window is already connected to a VkSurfaceKHR, or to some other non-Vulkan API A surface is no longer available H冹(�=     u3夜  �    3繦兡(脣�   H兡(�   �       �    "   (        +           �       �       �     B  H冹(�    �=     u3夜  �    3繦兡(脙=�    uH�    �  �    3繦兡(脣  �H�   H兡(�   �       �    (   (    2   �    <   �    I   (    R   (        [           �       �       �     B  Vulkan: Loader not found @SH冹 �=     H嬟u3夜  �    3繦兡 [脙=�    uH�    �  �    3繦兡 [�  H吚uH�
�   H嬘�    H兡 [�   �       �    (   (    2   �    <   �    J   (    V   (    _   �        i           �       �       �     20H冹(�=     u3夜  �    3繦兡(脙=�    uH�    �  �    3繦兡(肏�=    uH�    �  �    3繦兡(肏兡(�       �       �    "   (    ,   �    6   �    D   (    N   �    X   �    h   �        l           �       �       �     B  Vulkan: Window surface creation extensions not found H冹(I�    �=     u3夜  �    庚���H兡(脙=�    uH�    �  �    庚���H兡(肏�=    uH�    �  �    根���H兡(肏兡(�    
   �       �    ,   (    6   �    @   �    Q   (    [   �    e   �    x   �        |           �       �       �     B  H冹8H�
    �    H��   H吚凮  H�    H嬋�    H�  H吚uH�    �  �    H兡8肏�    3�蠬�  H吚uH�    �  �    H兡8肏峊$@E3�3�袇纓 嬋�    H�    �  L嬂�    H兡8脣L$@�  H塡$H�    H峊$@3蒐嬂H嬝�  吚t.嬋�    H�    �  L嬂�    H嬎�    H媆$HH兡8脣D$@L塴$ A�   吚�  D�0  D�
,  D�(  D�$  H塴$P�-   H塼$XH墊$0L塪$(D嬥�4  H嬘ffffff�     H嬺H�=    �   螃H嬺H�=    AD砉   �-   螃H嬺ED軭�=    �   D�$  螃H嬺ED誋�=    �   D�(  螃H嬺ED虷�=    �   D�
,  螃H嬺ED臜�=    �   D�0  螃AD臜伮  I�虊4  匤���L媎$(H媩$0H媡$XH媗$PH嬎�    �=    D�-�   L媗$ tH�
  �    H�   H媆$HH兡8�       
         (    $      -   �    4   (    @      J   �    V      a   (    m      w   �    �   ,    �      �   �    �   
   �   (    �   ,    �      �   �    �   '    #  (    *  (    1  (    8  (    C  (    [  (    v  	   �     �  (    �     �  (    �      �  (    �  �    �  (    �  �      (      (    ?  '    E  (    M  (    [  (    `  �    g  (    p  u          !      !      �    !       �          !      !      �    X  p          !      !      �    !   	  
         !      !      �    :  X          !      !      �    !   
  <         !      !      �    <  :          !      !      �    ! � t d T
 
  <         !      !      �    
  <          !      !      �    ! � 	  
         !      !      �    	  
          !      !      �    !   4	     �          !      !      �    �   	          !      !      �    ! 4	     �          !      !      �        �           !      !      �     b  VK_KHR_mir_surface VK_KHR_wayland_surface VK_KHR_xcb_surface VK_KHR_xlib_surface VK_KHR_win32_surface VK_KHR_surface Vulkan: Failed to query instance extensions: %s Vulkan: Failed to query instance extension count: %s Vulkan: Failed to retrieve vkEnumerateInstanceExtensionProperties vkEnumerateInstanceExtensionProperties Vulkan: Loader does not export vkGetInstanceProcAddr vkGetInstanceProcAddr vulkan-1.dll @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          m      縰�*                    .pdata               鼼噺                   .xdata               �=        7           .pdata               I+        U           .xdata               [鏴H        s           .pdata               驫�        �           .xdata      	         m"Z        �       	    .pdata      
         �
2�        �       
    .xdata               溠�        �           .pdata               �J�        	          .xdata      
          �9�        %      
        B               T           _glfw            $LN9            .text          h  *   牟N         _          .rdata                �蓳         z          .rdata         T       �/�          �          .rdata         7       睐儰         �          $LN9          .rdata         \       占�,         '          $LN10         .rdata         %       "�->         a          $LN11   �       .rdata         '       鉐Kr         �          $LN12   �       .rdata         8       荾匒         �          $LN13   �       .rdata         &       _抙@                   $LN14   �       .rdata         -       #╅�         I          $LN15   �       .rdata         W       巭护         �          $LN16   �       .rdata         &       %瓾@         �          $LN17   �       .rdata         $       �)橈         �          $LN18   �       .rdata         ,       �0\         0          $LN19   �       .rdata                鑫yN         k          $LN20   �       .rdata                5!6j         �          $LN21   �       .rdata         9       � 脗         �          $LN22   �       .rdata         '       姐                   $LN23   �       .rdata                 蛝Vk         C           $LN24   �       $LN31   (          a           .rdata      !   3       .N9         m      !    .rdata      "   "       
猌�         �      "    .rdata      #   O       鎰         �      #    .rdata      $   W       }>               $    .rdata      %   ]       CC�         X      %    .rdata      &   !       .3姨         �      &    .text       '   +      ]pf         �      '    .pdata      (          ~�'        �      (    .xdata      )          �9�'        �      )                       '           $LN4        '    .text       *   [      柊,         8      *    .pdata      +         愶L*        Z      +    .xdata      ,          �9�*        �      ,    .rdata      -          k锑         �      -    $LN5        *    .text       .   i      N闓6         �      .    .pdata      /         惢は.        �      /    .xdata      0          （亵.              0        B           $LN6        .    .text       1   l   	   %W7�         W      1    .pdata      2         舻D�1        �      2    .xdata      3          �9�1        �      3        �           .rdata      4   5       偼状         	      4    $LN6        1    .text       5   |   	   襾�         M	      5    .pdata      6         邉��5        e	      6    .xdata      7          �9�5        �	      7        �	           $LN6        5    .text       8   u  -   S�         �	      8    .pdata      9         瓒	8        �	      9    .xdata      :         `'頿8        �	      :    .pdata      ;         �訷8        	
      ;    .xdata      <         ��8        "
      <    .pdata      =         .�8        ;
      =    .xdata      >         Dyc�8        T
      >    .pdata      ?         羸?S8        m
      ?    .xdata      @          `	�	8        �
      @    .pdata      A         〒4+8        �
      A    .xdata      B         8        �
      B    .pdata      C         4鸘a8        �
      C    .xdata      D         蚌{[8        �
      D    .pdata      E         阺P8              E    .xdata      F         Θ8              F    .pdata      G         屚股8        5      G    .xdata      H          1�78        L      H        d           .rdata      I          执碞         �      I    .rdata      J          困U�         �      J    .rdata      K          V�         �      K    .rdata      L          s说               L    .rdata      M          *y讗         @      M    .rdata      N          ;         m      N    .rdata      O   0       鴕�         �      O        �           .rdata      P   5       ┶mF         �      P    .rdata      Q   B       b煀!         
      Q    .rdata      R   '       D{B         M
      R    .rdata      S   5       �         �
      S    .rdata      T          <z�&         �
      T        �
           .rdata      U   
       !�4x         �
      U    $LN21       8    $  _glfwTerminateVulkan $pdata$3$_glfwTerminateVulkan $chain$3$_glfwTerminateVulkan $pdata$2$_glfwTerminateVulkan $chain$2$_glfwTerminateVulkan $pdata$1$_glfwTerminateVulkan $chain$1$_glfwTerminateVulkan $pdata$0$_glfwTerminateVulkan $chain$0$_glfwTerminateVulkan $pdata$_glfwTerminateVulkan $unwind$_glfwTerminateVulkan __imp_FreeLibrary __imp_free _glfwGetVulkanResultString ??_C@_0BM@OMEGJAIK@ERROR?3?5UNKNOWN?5VULKAN?5ERROR?$AA@ ??_C@_0FE@ILJLDKCD@A?5swapchain?5no?5longer?5matches?5th@ ??_C@_0DH@EKEHMEFD@Too?5many?5objects?5of?5the?5type?5hav@ ??_C@_0FM@BBONGFMN@The?5requested?5version?5of?5Vulkan?5@ ??_C@_0CF@OFADCJLJ@A?5requested?5feature?5is?5not?5suppo@ ??_C@_0CH@NEGEBDM@A?5requested?5extension?5is?5not?5sup@ ??_C@_0DI@GNLCAHHB@A?5requested?5layer?5is?5not?5present@ ??_C@_0CG@CHGOPGBD@Mapping?5of?5a?5memory?5object?5has?5f@ ??_C@_0CN@IHJGMBHI@The?5logical?5or?5physical?5device?5h@ ??_C@_0FH@MCDKNJAI@Initialization?5of?5an?5object?5coul@ ??_C@_0CG@CHEOMKGJ@A?5device?5memory?5allocation?5has?5f@ ??_C@_0CE@HKNBGEPI@A?5host?5memory?5allocation?5has?5fai@ ??_C@_0CM@GKIHKJDL@A?5return?5array?5was?5too?5small?5for@ ??_C@_0BH@MDCGBFEN@An?5event?5is?5unsignaled?$AA@ ??_C@_0BF@NEMNBLEB@An?5event?5is?5signaled?$AA@ ??_C@_0DJ@KAONABFK@A?5wait?5operation?5has?5not?5complet@ ??_C@_0CH@GBHCOIGC@A?5fence?5or?5query?5has?5not?5yet?5com@ ??_C@_07PBILKAFL@Success?$AA@ __ImageBase ??_C@_0DD@OOKDLCED@A?5requested?5format?5is?5not?5suppor@ ??_C@_0CC@HDNEDJNI@A?5validation?5layer?5found?5an?5erro@ ??_C@_0EP@OJIEDFOO@The?5display?5used?5by?5a?5swapchain?5@ ??_C@_0FH@FIPMKLID@A?5surface?5has?5changed?5in?5such?5a?5@ ??_C@_0FN@PJBCJBIL@The?5requested?5window?5is?5already?5@ ??_C@_0CB@EJFBPGFM@A?5surface?5is?5no?5longer?5available@ glfwVulkanSupported $pdata$glfwVulkanSupported $unwind$glfwVulkanSupported _glfwInputError _glfwInitialized glfwGetRequiredInstanceExtensions $pdata$glfwGetRequiredInstanceExtensions $unwind$glfwGetRequiredInstanceExtensions ??_C@_0BJ@PGPMJKGJ@Vulkan?3?5Loader?5not?5found?$AA@ glfwGetInstanceProcAddress $pdata$glfwGetInstanceProcAddress $unwind$glfwGetInstanceProcAddress __imp_GetProcAddress glfwGetPhysicalDevicePresentationSupport $pdata$glfwGetPhysicalDevicePresentationSupport $unwind$glfwGetPhysicalDevicePresentationSupport _glfwPlatformGetPhysicalDevicePresentationSupport ??_C@_0DF@FCIHEFHD@Vulkan?3?5Window?5surface?5creation?5@ glfwCreateWindowSurface $pdata$glfwCreateWindowSurface $unwind$glfwCreateWindowSurface _glfwPlatformCreateWindowSurface _glfwInitVulkan $pdata$10$_glfwInitVulkan $chain$10$_glfwInitVulkan $pdata$9$_glfwInitVulkan $chain$9$_glfwInitVulkan $pdata$8$_glfwInitVulkan $chain$8$_glfwInitVulkan $pdata$7$_glfwInitVulkan $chain$7$_glfwInitVulkan $pdata$3$_glfwInitVulkan $chain$3$_glfwInitVulkan $pdata$2$_glfwInitVulkan $chain$2$_glfwInitVulkan $pdata$0$_glfwInitVulkan $chain$0$_glfwInitVulkan $pdata$_glfwInitVulkan $unwind$_glfwInitVulkan _glfwPlatformGetRequiredInstanceExtensions ??_C@_0BD@GLEDICEG@VK_KHR_mir_surface?$AA@ ??_C@_0BH@FDAKHMHL@VK_KHR_wayland_surface?$AA@ ??_C@_0BD@DKPJMBMG@VK_KHR_xcb_surface?$AA@ ??_C@_0BE@EFOBHOAB@VK_KHR_xlib_surface?$AA@ ??_C@_0BF@COCMEDFO@VK_KHR_win32_surface?$AA@ ??_C@_0P@IENCOMCD@VK_KHR_surface?$AA@ ??_C@_0DA@LIIDOJC@Vulkan?3?5Failed?5to?5query?5instance@ __imp_calloc ??_C@_0DF@KADNFCFI@Vulkan?3?5Failed?5to?5query?5instance@ ??_C@_0EC@MCALKGHI@Vulkan?3?5Failed?5to?5retrieve?5vkEnu@ ??_C@_0CH@DNHGEPME@vkEnumerateInstanceExtensionProp@ ??_C@_0DF@FEPNJFGL@Vulkan?3?5Loader?5does?5not?5export?5v@ ??_C@_0BG@HBCIDECD@vkGetInstanceProcAddr?$AA@ __imp_LoadLibraryA ??_C@_0N@IILPFLFM@vulkan?91?4dll?$AA@ 
/289            1459697978              100666  17199     `
d哢 :9W!%  #      .drectve        s   \
               
 .debug$S        �   �
              @ B.text           B   W               P`.text           }   �            P`.pdata             >  J         @0@.xdata             h  |         @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata             �           @0@.xdata                            @0@.text              (               P`.text           Y   -  �          P`.pdata             �  �         @0@.xdata             �              @0@.text           Y   �  1          P`.pdata             O  [         @0@.xdata             y              @0@.text           >   �  �          P`.pdata             �  �         @0@.xdata                           @0@.text           /    F          P`.pdata             P  \         @0@.xdata             z  �         @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata                        @0@.xdata             2              @0@.text           B   F               P`.text           <   �               P`.text           :   �  �          P`.pdata             &  2         @0@.xdata             P              @0@.text           8   X  �          P`.pdata             �  �         @0@.xdata             �              @0@.text           *   �            P`.text           <   2  n          P`.text           (   �  �          P`.pdata             �  �         @0@.xdata             �              @0@.text           3   �  #          P`.pdata             K  W         @0@.xdata             u              @0@.text           U   }  �          P`.pdata             �  �         @0@.xdata                           @0@.text           >   &  d          P`.pdata             �  �         @0@.xdata             �              @0@.text           w   �  /          P`.pdata             k  w         @0@.xdata             �              @0@.text           S   �  �          P`.pdata               (         @0@.xdata             F              @0@.text           �   R  �      	    P`.pdata             B  N         @0@.xdata             l              @0@.text           �   t  I      
    P`.pdata             �  �         @0@.xdata             �  �         @0@.pdata                        @0@.xdata             /  C         @0@.pdata             a  m         @0@.xdata             �              @0@.text           �  �  Y          P`.pdata             �  �         @0@.xdata                          @0@.pdata             =   I          @0@.xdata          (   g   �          @0@.pdata             �   �          @0@.xdata             �   �          @0@.rdata             �               @@@.rdata             !              @@@.rdata             !              @@@.rdata             !              @@@.rdata             $!              @@@.rdata             ,!              @0@.text             0!  5#          P`.pdata             /$  ;$         @0@.xdata             Y$  i$         @0@.pdata             �$  �$         @0@.xdata             �$  �$         @0@.pdata             �$  �$         @0@.xdata             
%              @0@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   {   =     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\monitor.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler  D婣婣D婮�DADJL嬔DA婮DJ�
E;羣E+罙嬂�;羣+撩A婤+B聾SH冹 H儁 H嬞t�   H兡 [肏峊$0H墊$8�    H孁H吚uH媩$8H兡 [肏cT$0L�
    A�   H嬋�    H婯�    婦$0H墈H媩$8塁 �   H兡 [�&       E       T       ^       =   }           
       
           !   t                
       
               =           
       
           ! t                
       
                           
       
            20H塓肏塡$H塴$H塼$WH冹 嬺H嬞簣  �   A嬭�    H嬎H孁�    H媆$0墂H媡$@塷H媗$8H�H嬊H兡 _�(   -    4   ,        Y           .       .       +     d T 4 2pH塡$H塼$WH冹 嬺H孂�   嬑�    �   嬑H��    �   嬑H塆�    H媆$0墂H媡$8H塆H兡 _�   -    -   -    >   -        Y           8       8       7     d 4 2p@SH冹 H嬞H�	�    H婯�    H婯�    E3跮�L塠L塠L塠H兡 [�              "           >           B       B       A     20@SUVWATAWH冹(兾�H嬟H孂D�頔3��    吚u3繦兡(A_A\_^][脣G 吚庌   婯D媅L婳塋$p�L塴$`D媖媩$pL塼$ D媠媅塋$xD嬓E3繟凔�tA婣A+脵D嬂D3翫+翧凖�tA婣A+艡3�+翫繟凗�tA婣A+茩3�+翫繟�A婭+D$x+�翙嬋3�+蕛�t
A婣+脵3�+码內�A+AD;舝
u;蝦uA;膕M孂嬹D嬥A嬭I兞I��匽���L媡$ L媗$`I嬊H兡(A_A\_^][�   
      /          X       X       K    !       R          X       X       W    R             X       X       Q    ! � �     R          X       X       W        R           X       X       W    
 
B	��p`P0  D婣婣D婮�DADJL嬔DA婮DJ�
E;羣E+罙嬂�;羣+撩A婤+B脙� L嬕�   D雀VUUU鏖嬄凌蠥�A�A��R+葍�|A� 凒uA�竺H冹(�    �=     u3夜  �    3繦兡(脣�   �H��   H兡(�   j       i    (   h    1   h        :           k       k       g     B  H冹(�=     u3夜  �    3繦兡(脙=�    t餒��   H� H兡(�   j       i    "   h    ,   h        8           u       u       t     B  3繦呉t�M吚tA� 9    u3夜  �    �       j    !   i    &   y    3繦呉t�M吚tA� 9    u3夜  �    H呉t婣�M吚t婣A� 竺   j    !   i    H冹(�=     u3夜  �    3繦兡(肏�H兡(�   j       i        (           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏�8  H�
8  H兡(�   j       i    #   h    *   h        3           �       �       �     B  H塡$WH冹 �    �=     H孃H嬞u3夜  �    3繦媆$0H兡 _描    吚t陭C �H婥H媆$0H兡 _�   j    '   i    9   
        U           �       �       �    
 
4 
2pH冹(�=     u3夜  �    3繦兡(肏塡$ H峐$H嬘�    H嬅H媆$ H兡(�   j       i    -   �        >           �       �       �    % %4 B  @WH冹 �=     H孂u3夜  �    3繦兡 _肏婭`H塡$0�    H婳h�    H婳p�    E3跦峎`L塤`L塤hL塤pH嬒L塤x�    H媆$0H岹`H兡 _�   j       i    1       ;       E       d   �        w           �       �       �    / /4 2pH塡$WH冹 �=     H孃H嬞u3夜  H媆$0H兡 _�    儁X u	H峇@�    H嬜H嬎H媆$0H兡 _�       j    +   i    :   �    O   �        S           �       �       �    
 
4 
2pH吷剫   SH冹 H嬞H婭@�    H婯H�    H婯P�    E3跮塠@L塠HL塠PL塠XH婯`�    H婯h�    H婯p�    E3跮塠`L塠hL塠pL塠xH婯�    H��    H嬎�    H兡 [竺       !       +       H       R       \       y       �       �           �           �       �       �     2
0H塡$H塴$VH冹 3跦嬹Hc陞�帩   H墊$0H�<轍�t~H婳@�    H婳H�    H婳P�    E3跮塤@L塤HL塤PL塤XH婳`�    H婳h�    H婳p�    E3跮塤`L塤hL塤pL塤xH婳�    H��    H嬒�    H�肏;�宮���H媩$0H嬑H媆$8H媗$@H兡 ^H�%    3       =       G       d       n       x       �       �       �       �       �   �           �       �       �    !                 �       �       �       �           �       �       �    ! t               �       �       �                   �       �       �     T 4 2`@VH侅�  H�    H3腍墑$@  �=     H嬹u3夜  �    閙  .�奌  匓  W�/��6  /
    �)  H墱$�  H壖$�  )�$�  �5    )�$p  D)�$`  �=    駾    �3跦峾$@D)�$P  駾
    Z硫^鴉ff�     fn胒(象胬駻^黎    �Y乞AX羏/苬f(乞H,�肏兦f塆�   |纼~X D(�$P  D(�$`  H嫾$�  H嫓$�  H岲$@(�$p  (�$�  H塂$ H岲$@荄$8   H塂$(H岲$@H塂$0uH峍@H嬑�    H峊$ H嬑�    �蒆�    �  Z裦I~需    H媽$@  H3惕    H伳�  ^�         j    /   i    V      |       �   �    �   �    �   �    �      v  �    �  �    �  �    �  i    �     n  �                      �    !       `                      �    `   n                      �    !U U�% 1�& (x' h( tW 4V     `                    $   �        `                       �     	R `      @     �    Invalid gamma value %f       �?     鄌@      �?    �顯��H塡$ VWATAUAVH冹@Lc-�   L�%�   H�
�   H壃$�   �    ��   E3鯤��   A嬵呉~gA孇 I嬣E呿~O�     H�
�   I�蹾��    吚u
H�肐;輡嚯H�
�   H��    H��   M�躄���   �臜兦;陓烢呿幆   I孅I嬳A嬈呉~L�H�
�   L;t�繦兞;聕耠;聕vH��   H呟tNH�H9C@u7L岲$pH峊$xH嬎�    婦$pD塼$0塂$(婦$xE3蒃3�3襀嬎塂$ �    H�H呟u笅�   H�8  H吚tH��  �袐�   H兦H��匴���H嫭$�   A孇呉~nI嬣D  E嬈I嬑E呿~(H��   L�M;蘴
H�罙�繧;蛗镫
M�4虌�   E;舼$H�8  H吚tH�
�   �  H��袐�   �荋兠;鷟欰嬚I嬏H嫓$�   H兡@A^A]A\_^�       h       h    "   h    /   !   5   h    ?   h    c   h    p       �   h    �   �    �   h    �   h    �   h    �   h    	     .     <  h    C  h    X  h    �  h    �  h    �  h    �  h    �  h      �    x            "      "         !       &          "      "         &   x          "      "         ! T     &          "      "             &           "      "          4 r
��	�p`@comp.idov� ��   .drectve       s                 .debug$S       �                 .text          B       sγ;                    .text          }      J羷q                    .pdata               咏        (           .xdata               梬詊        C           .pdata               T濝        ^           .xdata               ;'.        y           .pdata      	         Vbv�        �       	    .xdata      
          （亵        �       
        �                �                �            .text                 �/�         �           .text          Y      �8                   .pdata      
         龛iJ        )      
    .xdata                嘋c�        B              \               j           $LN3            .text          Y      hPy�         w          .pdata               龛iJ        �          .xdata                O�        �          $LN3            .text          >      �         �          .pdata               OAG�        �          .xdata                （亵        �          $LN3            .text          /     嶓F+                   .pdata               �#9        +          .xdata               WT�        I          .pdata               犾        g          .xdata               逞.�        �          .pdata               霍        �          .xdata                t�?�        �          $LN17           .text          B       sγ;         �          .text          <       bか�         �          .text          :      x齠�                   .pdata               礝
                  .xdata                 �9�        (           _glfw                @               P           $LN4            .text       !   8      |y"�         a      !    .pdata      "         菻(V!        w      "    .xdata      #          �9�!        �      #    $LN6        !    .text       $   *      じ         �      $        �           .text       %   <      mP         �      %    .text       &   (      W&虐         �      &    .pdata      '         銀�*&        
      '    .xdata      (          �9�&        '      (    $LN4        &    .text       )   3      嵮         B      )    .pdata      *         濼B)        Y      *    .xdata      +          �9�)        w      +    $LN4        )    .text       ,   U      {h         �      ,    .pdata      -         �,        �      -    .xdata      .          %蚘%,        �      .    $LN6        ,    .text       /   >      x剈�         �      /    .pdata      0         OAG�/        �      0    .xdata      1          e�
/              1                   $LN4        /    .text       2   w      �54         7      2    .pdata      3         墭暒2        H      3    .xdata      4          >M2        `      4        y           $LN6        2    .text       5   S      �*Y2         �      5    .pdata      6         %舂�5        �      6    .xdata      7          %蚘%5        �      7        �           $LN5        5    .text       8   �   	   弜窜         �      8    .pdata      9         v斤�8               9    .xdata      :          #D[�8              :    $LN9        8    .text       ;   �   
   T�         1      ;    .pdata      <         l?;        C      <    .xdata      =         k商;        ^      =    .pdata      >         e闭�;        y      >    .xdata      ?         渿屣;        �      ?    .pdata      @         �#洢;        �      @    .xdata      A          鱎赔;        �      A    $LN15       ;    .text       B   �     膅/         �      B    .pdata      C         鳢鎒B        �      C    .xdata      D         WJ糹B              D    .pdata      E         5誅KB              E    .xdata      F   (      琹姜B        1      F    .pdata      G         粻胄B        G      G    .xdata      H         ge油B        [      H        p           .rdata      I          紴         �      I    .rdata      J          艳�         �      J    .rdata      K          X鎇=         �      K    .rdata      L          �腾�         �      L    .rdata      M          f圯�         �      M    .rdata      N          �;�               N        &           _fltused             8           pow              $LN16       B    .text       O        �?�
         P      O    .pdata      P         �7�<O        h      P    .xdata      Q         =丯哋        �      Q    .pdata      R         ��.O        �      R    .xdata      S         鑄砞O        �      S    .pdata      T         裬?O        �      T    .xdata      U          r棂WO        	      U        +	               I	               d	               	           $LN61       O    �	  compareVideoModes refreshVideoModes $pdata$2$refreshVideoModes $chain$2$refreshVideoModes $pdata$0$refreshVideoModes $chain$0$refreshVideoModes $pdata$refreshVideoModes $unwind$refreshVideoModes __imp_free __imp_qsort _glfwPlatformGetVideoModes _glfwInputMonitorWindowChange _glfwAllocMonitor $pdata$_glfwAllocMonitor $unwind$_glfwAllocMonitor __imp__strdup __imp_calloc _glfwAllocGammaArrays $pdata$_glfwAllocGammaArrays $unwind$_glfwAllocGammaArrays _glfwFreeGammaArrays $pdata$_glfwFreeGammaArrays $unwind$_glfwFreeGammaArrays _glfwChooseVideoMode $pdata$2$_glfwChooseVideoMode $chain$2$_glfwChooseVideoMode $pdata$1$_glfwChooseVideoMode $chain$1$_glfwChooseVideoMode $pdata$_glfwChooseVideoMode $unwind$_glfwChooseVideoMode _glfwCompareVideoModes _glfwSplitBPP glfwGetMonitors $pdata$glfwGetMonitors $unwind$glfwGetMonitors _glfwInputError _glfwInitialized glfwGetPrimaryMonitor $pdata$glfwGetPrimaryMonitor $unwind$glfwGetPrimaryMonitor glfwGetMonitorPos _glfwPlatformGetMonitorPos glfwGetMonitorPhysicalSize glfwGetMonitorName $pdata$glfwGetMonitorName $unwind$glfwGetMonitorName glfwSetMonitorCallback $pdata$glfwSetMonitorCallback $unwind$glfwSetMonitorCallback glfwGetVideoModes $pdata$glfwGetVideoModes $unwind$glfwGetVideoModes glfwGetVideoMode $pdata$glfwGetVideoMode $unwind$glfwGetVideoMode _glfwPlatformGetVideoMode glfwGetGammaRamp $pdata$glfwGetGammaRamp $unwind$glfwGetGammaRamp _glfwPlatformGetGammaRamp glfwSetGammaRamp $pdata$glfwSetGammaRamp $unwind$glfwSetGammaRamp _glfwPlatformSetGammaRamp _glfwFreeMonitor $pdata$_glfwFreeMonitor $unwind$_glfwFreeMonitor _glfwFreeMonitors $pdata$1$_glfwFreeMonitors $chain$1$_glfwFreeMonitors $pdata$0$_glfwFreeMonitors $chain$0$_glfwFreeMonitors $pdata$_glfwFreeMonitors $unwind$_glfwFreeMonitors glfwSetGamma $pdata$6$glfwSetGamma $chain$6$glfwSetGamma $pdata$5$glfwSetGamma $chain$5$glfwSetGamma $pdata$glfwSetGamma $unwind$glfwSetGamma __GSHandlerCheck ??_C@_0BH@BAPIEFAH@Invalid?5gamma?5value?5?$CFf?$AA@ __real@3fe0000000000000 __real@406fe00000000000 __real@3ff0000000000000 __real@40efffe000000000 __real@7f7fffff __security_cookie __security_check_cookie _glfwInputMonitorChange $pdata$1$_glfwInputMonitorChange $chain$1$_glfwInputMonitorChange $pdata$0$_glfwInputMonitorChange $chain$0$_glfwInputMonitorChange $pdata$_glfwInputMonitorChange $unwind$_glfwInputMonitorChange _glfwPlatformSetWindowMonitor _glfwPlatformGetWindowSize _glfwPlatformIsSameMonitor _glfwPlatformGetMonitors 
/318            1459697978              100666  27015     `
d啋 :9W6  �      .drectve        s   �               
 .debug$S        �   W              @ B.text           �   �  �      
    P`.pdata             Z  f         @0@.xdata             �              @0@.rdata             �              @@@.text           =   �               P`.text           =   �               P`.text           z                  P`.pdata             �  �         @0@.xdata             �              @0@.text           _   �               P`.pdata             (  4         @0@.xdata             R              @0@.text              b               P`.pdata             y  �         @0@.xdata             �              @0@.text           ?   �               P`.pdata             �  �         @0@.xdata                           @0@.text           K                  P`.pdata             g  s         @0@.xdata             �              @0@.text              �               P`.pdata             �  �         @0@.xdata             �              @0@.text              �               P`.pdata             �           @0@.xdata             #              @0@.text              +  B          P`.pdata             L  X         @0@.xdata             v              @0@.text           .   ~               P`.text           n   �            P`.pdata             B  N         @0@.xdata             l              @0@.rdata             t              @@@.text           �   �  x          P`.pdata             �  �         @0@.xdata             �              @0@.text           )   �             P`.pdata             #   /          @0@.xdata             M               @0@.text           m   U   �           P`.pdata             �   �          @0@.xdata             !              @0@.rdata             !              @@@.text           j   +!  �!          P`.pdata             �!  �!         @0@.xdata             �!              @0@.rdata             �!              @@@.text           a   "  h"          P`.pdata             �"  �"         @0@.xdata             �"              @0@.text           K   �"  #          P`.pdata             +#  7#         @0@.xdata             U#              @0@.text           C   ]#  �#          P`.pdata             �#  �#         @0@.xdata             �#              @0@.text           3   �#  '$          P`.pdata             ;$  G$         @0@.xdata             e$              @0@.text           3   m$  �$          P`.pdata             �$  �$         @0@.xdata             �$              @0@.text           3   �$  %          P`.pdata             -%  9%         @0@.xdata             W%              @0@.text           3   _%  �%          P`.pdata             �%  �%         @0@.xdata             �%              @0@.text           3   �%  &          P`.pdata             &  +&         @0@.xdata             I&              @0@.text           3   Q&  �&          P`.pdata             �&  �&         @0@.xdata             �&              @0@.text           3   �&  �&          P`.pdata             '  '         @0@.xdata             ;'              @0@.text           3   C'  v'          P`.pdata             �'  �'         @0@.xdata             �'              @0@.text           I   �'  (          P`.pdata             7(  C(         @0@.xdata             a(              @0@.rdata             i(              @@@.text           O   }(  �(          P`.pdata             �(  
)         @0@.xdata             ()              @0@.text           O   0)  )          P`.pdata             �)  �)         @0@.xdata             �)              @0@.text           I   �)  ,*          P`.pdata             ^*  j*         @0@.xdata             �*              @0@.text           3   �*  �*          P`.pdata             �*  �*         @0@.xdata             +              @0@.text              +  7+          P`.text           )   U+  ~+          P`.pdata             �+  �+         @0@.xdata             �+              @0@.text           s   �+  A,          P`.pdata             �,  �,         @0@.xdata             �,              @0@.rdata             �,              @@@.text           �   �,  �-      
    P`.pdata             .  .         @0@.xdata             6.  F.         @0@.pdata             d.  p.         @0@.xdata             �.  �.         @0@.pdata             �.  �.         @0@.xdata             �.              @0@.rdata             �.              @@@.rdata             /              @@@.rdata             /              @@@.text           )   /  ?/          P`.pdata             ]/  i/         @0@.xdata             �/              @0@.text           )   �/  �/          P`.pdata             �/  �/         @0@.xdata              0              @0@.text           �   0  �0      
    P`.pdata             1  +1         @0@.xdata             I1  Y1         @0@.pdata             w1  �1         @0@.xdata             �1  �1         @0@.pdata             �1  �1         @0@.xdata             �1  
2         @0@.pdata             +2  72         @0@.xdata             U2  i2         @0@.pdata             �2  �2         @0@.xdata             �2              @0@.text           �   �2  W3          P`.pdata             �3  �3         @0@.xdata             �3              @0@.text           �   �3  �4      	    P`.pdata             �4  
5         @0@.xdata             (5  <5         @0@.pdata             Z5  f5         @0@.xdata             �5  �5         @0@.pdata             �5  �5         @0@.xdata             �5              @0@.rdata             �5              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   y   ;     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\input.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler    @SH冹 媮�   H嬞侜@ t)侜@ t!侜@ tD嬄H�    �  H兡 [�    ;�劔   墤�   H9
�   厴   =@ u��   �
�   雔侜@ uiL��   H��   �    L岲$8H峊$0�-�   �kp��   H嬎�Cx�    婦$8橦嬎+卵鴉n袐D$0�+卵嬉fn润嫔�    嫇�   H嬎�    H兡 [�-       <       Q       f       n              �       �       �       �       �       �       �           �                         
     20Invalid cursor mode %i 9Qht6呉u/L崄�   A筣  E3��    A�8A� AD翴�繧�葾園�u鑹Qh竺9Qlt6呉u/L崄�   D岼E3襢�     A�8A� AD翴�繧�葾園�u鑹Ql竺H冹8侜\  wRE3褽吷uHc翫8��   tTD9Qht 苿�   �-A凒uHc翫8��   uE嬔Hc翬呉D垖�   �   DE萀嫅�  M呉t婦$`塂$ A�襀兡8�    z           '       '       &     b  凓 rXH塡$H塼$WH冹 A嬹嬟H孂凓~v侜�   r%H媮�  H吚t�袇鰐H媷�  H吚t嬘H嬒�蠬媆$0H媡$8H兡 _竺    _           1       1       0     d 4 2pH冹(H媮�  H吚t�蠬兡(�               ;       ;       :     B  H冹(凓w1E吚uD9Alt
Hc缕��   �Hc翫垊�   H媮�  H吚t�蠬兡(�    ?           E       E       D     B  H冹(伖�   @ u(fW纅.葄
uf.衵t"�XIp�XQx�Ip�QxH媮�  H吚t�蠬兡(�    K           O       O       N     B  H冹(H媮�  H吚t�蠬兡(�               Y       Y       X     B  H冹(H媮�  H吚t�蠬兡(�               c       c       b     B  H冹(H�@  H吚t�蠬兡(�                      m       m       l     B  凒'|侚�   ~侚@  |侚N  ~侚P  t3烂�   肏冹(�=     L嬃u3夜  �    3繦兡(脣蕘�0 t5�蓆(�蓆D嬄H�    �  �    3繦兡(肁婡lH兡(肁婡hH兡(肁媭�   H兡(�   }           ;   |    E           n           ~       ~       y     B  Invalid input mode %i H冹(�=     E嬓L嬌u3夜  H兡(�    嬍侀0 劖   �蓆h�蓆D嬄H�    �  H兡(�    3褽呉D嬄A暲E9Al剛   E吚u(I崏�   D峈f�     �9�D翲�罥�蕡A�u霦堿lH兡(�3褽呉D嬄A暲E9Aht:E吚u!I崏�   A篯  �9�D翲�罥�蕡A�u霦堿hH兡(肁嬓I嬌�    H兡(�   }           ?   |    M       �           �           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏兡(�       }           %   �        )           �       �       �     B  H冹(�=     u3夜  �    3繦兡(脕鶿  w*Hc�稊�   ��u苿�    �   H兡(�韭H兡(肈嬄H�    �  �    3繦兡(�   }           X   �    b           m           �       �       �     B  Invalid key %i H冹(�=     u3夜  �    3繦兡(脙�w*Hc�稊�   ��u苿�    �   H兡(�韭H兡(肈嬄H�    �  �    3繦兡(�   }           U   �    _           j           �       �       �     B  Invalid mouse button %i H冹(3繦呉tH�M吚tI� 9    u3夜  H兡(�    伖�   @ uH呉tH婣pH�M吚tH婣xI� H兡(描    H兡(�   }    *       X           a           �       �       �     B  H冹(�=     u3夜  H兡(�    H9
�   u 伖�   @ u�Ip�QxH兡(描    H兡(�   }                   B           K           �       �       �     B  H塡$WH冹 �=     H嬟H孂u3夜  H媆$0H兡 _�    �    H塤HH媆$0H兡 _�   }    +       0   �        C           �       �       �    
 
4 
2pH冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3           �       �       �     B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3                           B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3                           B  H冹(�=     u3夜  �    3繦兡(肏媮�  H墤�  H兡(�   }               3                           B  H冹(�=     u3夜  �    3繦兡(脙�w	H兡(�    D嬃H�    �  �    3繦兡(�   }           *   )   4   (   >           I           *      *      %    B  Invalid joystick %i H冹(�    �=     u3夜  �    3繦兡(脙�w	H兡(�    D嬃H�    �  �    3繦兡(�   }           0   4   :   (   D           O           5      5      3    B  H冹(�    �=     u3夜  �    3繦兡(脙�w	H兡(�    D嬃H�    �  �    3繦兡(�   }           0   ?   :   (   D           O           @      @      >    B  H冹(�=     u3夜  �    3繦兡(脙�w	H兡(�    D嬃H�    �  �    3繦兡(�   }           *   J   4   (   >           I           K      K      I    B  H冹(�=     u3夜  �    3繦兡(肏�@  H�
@  H兡(�   }           #       *           3           U      U      T    B  �=     u3夜  �    �       }              Y   H冹(�=     u3夜  �    3繦兡(肏兡(�       }           %   c       )           d      d      b    B  H冹8�=     u3夜  �    fW繦兡8�)t$ �    f秭H+�   騂*饄�X5    �    f锢騂*繦吚y�X    �^餱(�(t$ H兡8�   }           (   r   3       B   q   G   n   ]   q       s           s      s      m   ' 'h b        餋H冹8�=     )t$ f(饀3夜  (t$ H兡8�    f.�妺   厖   fW纅/苭{f/5    wqH塡$0�    f锢騂*繦吚y�X    �
    �Y�3纅/羦�\羏/羢
H�       �H嬃騂,豀罔    H+肏媆$0H��   (t$ H兡8胒(諬�    �  fI~�(t$ H兡8�       }    '       I   �   U   n   k   q   s   �   �   r   �       �   �   �       �   �           �      �      |   !       O          �      �      �   O   �           �      �      �   ! 4     O          �      �      �       O           �      �      �    h b  Invalid time %f       郈  $�.BH冹(�=     u3夜  �    3繦兡(肏兡(�       }           %   r       )           �      �      �    B  H冹(�=     u3夜  �    3繦兡(肏兡(�       }           %   n       )           �      �      �    B  @WH冹 �=     H孂u3夜  H兡 _�    H吷剚   H塡$0H��   H呟t<H塼$83鯤9{Hu"3�95    u�  �    �H嬎�    H塻HH�H呟u蠬媡$8H嬒�    H9=�   H媆$0L��   tM�I9;u鳫�H嬒I��    H兡 _�   }           4       N   }    Z       d   �    }   �   �       �       �   �   �   �           �      �      �   !       ,          �      �      �   y   �           �      �      �   !   ,   =          �      �      �   =   y           �      �      �   ! d ,   =          �      �      �   ,   =           �      �      �   ! 4     ,          �      �      �       ,           �      �      �    2pH塴$H塼$WH冹 �=     A孁嬺H嬮u3夜  �    3繦媗$8H媡$@H兡 _煤   H塡$0岼�    H�
�   D嬒H�D嬈H嬚H嬋H嬝H��   �    吚uH嬎�    3离H嬅H媆$0H媗$8H媡$@H兡 _�   }    (       M   �   T       m       r   �   ~   �       �           �      �      �   H H4 d T 2p@SH冹 �=     嬞u3夜  �    3繦兡 [脕�` tD侚` t<侚` t4侚` t,侚` t$侚` tD嬃H�    �  �    3繦兡 [煤   H墊$0岼�    H�
�   嬘H�H嬋H孁H��   �    吚uH嬒�    H媩$03繦兡 [肏嬊H媩$0H兡 [�   }           [   �   e       �   �   �       �       �   �   �   �   �   �           �      �      �   !   t     v          �      �      �   v   �           �      �      �   ! t     v          �      �      �       v           �      �      �    20Invalid standard cursor %i @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          �   
   �R�(                    .pdata               a$�                   .xdata                （亵        '               =                X                r                �            _glfw                �            .rdata                	��         �           _fltused         .text          =       潩y         �           .text          =       4榋8         �           .text       	   z       a4盞               	    .pdata      
         X崘=	              
    .xdata                1�7	        3          $LN11       	    .text          _       �9瞞         I          .pdata      
         j��        X      
    .xdata                澢�        n          $LN9            .text                 捦轄         �          .pdata               28~v        �          .xdata                �9�        �          $LN4            .text          ?       g伦         �          .pdata               袮韁        �          .xdata                �9�        �          $LN8            .text          K       �厯                   .pdata               晦鱰        ,          .xdata                �9�        J          $LN7            .text                 軷斡         i          .pdata               28~v                  .xdata                �9�        �          $LN4            .text                 锒夥         �          .pdata               28~v        �          .xdata                �9�        �          $LN4            .text                駬�         �          .pdata               28~v                  .xdata                 �9�        /           $LN4            .text       !   .       %膁[         P      !    .text       "   n      ,S��         a      "    .pdata      #         壊a�"        r      #    .xdata      $          �9�"        �      $    .rdata      %          O鼺q         �      %        �           $LN10       "    .text       &   �      je课         �      &    .pdata      '         S7Z�&        �      '    .xdata      (          �9�&              (    $LN30       &    .text       )   )      � 疞         *      )    .pdata      *         }y9�)        9      *    .xdata      +          �9�)        O      +        f           $LN4        )    .text       ,   m      萹a         ~      ,    .pdata      -         j殿K,        �      -    .xdata      .          �9�,        �      .    .rdata      /          璖-�         �      /    $LN8        ,    .text       0   j      h礅*         �      0    .pdata      1         s�+A0        �      1    .xdata      2          �9�0              2    .rdata      3          釋＂         !      3    $LN8        0    .text       4   a      ▔T�         W      4    .pdata      5         %燗4        h      5    .xdata      6          �9�4        �      6    $LN10       4    .text       7   K      熭燯         �      7    .pdata      8         晦鱰7        �      8    .xdata      9          �9�7        �      9    $LN7        7    .text       :   C      哮濈         �      :    .pdata      ;         ��:        �      ;    .xdata      <          %蚘%:        �      <                   $LN4        :    .text       =   3      T斕�         +      =    .pdata      >         濼B=        >      >    .xdata      ?          �9�=        X      ?    $LN4        =    .text       @   3      �]         s      @    .pdata      A         濼B@        �      A    .xdata      B          �9�@        �      B    $LN4        @    .text       C   3      昏+�         �      C    .pdata      D         濼BC        �      D    .xdata      E          �9�C        �      E    $LN4        C    .text       F   3      妋                F    .pdata      G         濼BF        0      G    .xdata      H          �9�F        R      H    $LN4        F    .text       I   3      軵扇         u      I    .pdata      J         濼BI        �      J    .xdata      K          �9�I        �      K    $LN4        I    .text       L   3      e錔         �      L    .pdata      M         濼BL        �      M    .xdata      N          �9�L              N    $LN4        L    .text       O   3      2,.�         /      O    .pdata      P         濼BO        E      P    .xdata      Q          �9�O        b      Q    $LN4        O    .text       R   3      煺�         �      R    .pdata      S         濼BR        �      S    .xdata      T          �9�R        �      T    $LN4        R    .text       U   I      仧         �      U    .pdata      V         瀑�6U        �      V    .xdata      W          �9�U        �      W    .rdata      X          6幽3         	      X        G	           $LN7        U    .text       Y   O      h �         d	      Y    .pdata      Z         A薪餣        x	      Z    .xdata      [          �9�Y        �	      [        �	           $LN7        Y    .text       \   O      h �         �	      \    .pdata      ]         A薪餦        �	      ]    .xdata      ^          �9�\        
      ^         
           $LN7        \    .text       _   I      仧         @
      _    .pdata      `         瀑�6_        T
      `    .xdata      a          �9�_        o
      a        �
           $LN7        _    .text       b   3      湩G         �
      b    .pdata      c         濼Bb        �
      c    .xdata      d          �9�b        �
      d    $LN4        b    .text       e         p葘         �
      e                   .text       f   )      � 疞         6      f    .pdata      g         }y9鎓        M      g    .xdata      h          �9�f        k      h        �           $LN4        f    .text       i   s      +旕         �      i    .pdata      j         s栠"i        �      j    .xdata      k          �-&i        �      k        �           .rdata      l          楍i�         �      l                   $LN6        i    .text       m   �   
   �1�         /      m    .pdata      n         掹鸧m        ;      n    .xdata      o         �:闕m        P      o    .pdata      p         秓Q8m        e      p    .xdata      q         '8pm        z      q    .pdata      r         A薪餸        �      r    .xdata      s          )搈        �      s    .rdata      t          鰀#�         �      t    .rdata      u          沏         �      u    .rdata      v          y$@�         �      v    $LN8        m    .text       w   )      � 疞         
      w    .pdata      x         }y9鎤        %
      x    .xdata      y          �9�w        >
      y    $LN4        w    .text       z   )      � 疞         X
      z    .pdata      {         }y9鎧        n
      {    .xdata      |          �9�z        �
      |    $LN4        z    .text       }   �   
   協         �
      }    .pdata      ~         个涆}        �
      ~    .xdata               鯘�}        �
          .pdata      �         P}        �
      �    .xdata      �         9:ず}              �    .pdata      �         Q綑9}        '      �    .xdata      �         �
鄲}        B      �    .pdata      �         招�}        ]      �    .xdata      �         ╘.}        x      �    .pdata      �         w佼}        �      �    .xdata      �          3�倉        �      �        �               �           $LN18       }    .text       �   �      熯�
         �      �    .pdata      �         忙
:�        �      �    .xdata      �          嗈g媹              �        .               H           $LN6        �    .text       �   �   	   &u柷         U      �    .pdata      �         觥昤�        n      �    .xdata      �         爈抎�        �      �    .pdata      �         孱隌�        �      �    .xdata      �         <A �        �      �    .pdata      �         �?j�        �      �    .xdata      �          （亵�              �        7           .rdata      �          M孰w         Y      �    $LN7        �    �  setCursorMode $pdata$setCursorMode $unwind$setCursorMode _glfwPlatformSetCursorMode _glfwPlatformSetCursorPos _glfwPlatformGetWindowSize _glfwPlatformGetCursorPos _glfwInputError ??_C@_0BH@FEEOFJLC@Invalid?5cursor?5mode?5?$CFi?$AA@ setStickyKeys setStickyMouseButtons _glfwInputKey $pdata$_glfwInputKey $unwind$_glfwInputKey _glfwInputChar $pdata$_glfwInputChar $unwind$_glfwInputChar _glfwInputScroll $pdata$_glfwInputScroll $unwind$_glfwInputScroll _glfwInputMouseClick $pdata$_glfwInputMouseClick $unwind$_glfwInputMouseClick _glfwInputCursorMotion $pdata$_glfwInputCursorMotion $unwind$_glfwInputCursorMotion _glfwInputCursorEnter $pdata$_glfwInputCursorEnter $unwind$_glfwInputCursorEnter _glfwInputDrop $pdata$_glfwInputDrop $unwind$_glfwInputDrop _glfwInputJoystickChange $pdata$_glfwInputJoystickChange $unwind$_glfwInputJoystickChange _glfwIsPrintable glfwGetInputMode $pdata$glfwGetInputMode $unwind$glfwGetInputMode ??_C@_0BG@CGJALDFA@Invalid?5input?5mode?5?$CFi?$AA@ _glfwInitialized glfwSetInputMode $pdata$glfwSetInputMode $unwind$glfwSetInputMode glfwGetKeyName $pdata$glfwGetKeyName $unwind$glfwGetKeyName _glfwPlatformGetKeyName glfwGetKey $pdata$glfwGetKey $unwind$glfwGetKey ??_C@_0P@PKABKPLF@Invalid?5key?5?$CFi?$AA@ glfwGetMouseButton $pdata$glfwGetMouseButton $unwind$glfwGetMouseButton ??_C@_0BI@POJNLJDO@Invalid?5mouse?5button?5?$CFi?$AA@ glfwGetCursorPos $pdata$glfwGetCursorPos $unwind$glfwGetCursorPos glfwSetCursorPos $pdata$glfwSetCursorPos $unwind$glfwSetCursorPos glfwSetCursor $pdata$glfwSetCursor $unwind$glfwSetCursor _glfwPlatformSetCursor glfwSetKeyCallback $pdata$glfwSetKeyCallback $unwind$glfwSetKeyCallback glfwSetCharCallback $pdata$glfwSetCharCallback $unwind$glfwSetCharCallback glfwSetCharModsCallback $pdata$glfwSetCharModsCallback $unwind$glfwSetCharModsCallback glfwSetMouseButtonCallback $pdata$glfwSetMouseButtonCallback $unwind$glfwSetMouseButtonCallback glfwSetCursorPosCallback $pdata$glfwSetCursorPosCallback $unwind$glfwSetCursorPosCallback glfwSetCursorEnterCallback $pdata$glfwSetCursorEnterCallback $unwind$glfwSetCursorEnterCallback glfwSetScrollCallback $pdata$glfwSetScrollCallback $unwind$glfwSetScrollCallback glfwSetDropCallback $pdata$glfwSetDropCallback $unwind$glfwSetDropCallback glfwJoystickPresent $pdata$glfwJoystickPresent $unwind$glfwJoystickPresent ??_C@_0BE@MDOOLHEE@Invalid?5joystick?5?$CFi?$AA@ _glfwPlatformJoystickPresent glfwGetJoystickAxes $pdata$glfwGetJoystickAxes $unwind$glfwGetJoystickAxes _glfwPlatformGetJoystickAxes glfwGetJoystickButtons $pdata$glfwGetJoystickButtons $unwind$glfwGetJoystickButtons _glfwPlatformGetJoystickButtons glfwGetJoystickName $pdata$glfwGetJoystickName $unwind$glfwGetJoystickName _glfwPlatformGetJoystickName glfwSetJoystickCallback $pdata$glfwSetJoystickCallback $unwind$glfwSetJoystickCallback glfwSetClipboardString _glfwPlatformSetClipboardString glfwGetClipboardString $pdata$glfwGetClipboardString $unwind$glfwGetClipboardString _glfwPlatformGetClipboardString glfwGetTime $pdata$glfwGetTime $unwind$glfwGetTime _glfwPlatformGetTimerFrequency __real@43f0000000000000 _glfwPlatformGetTimerValue glfwSetTime $pdata$1$glfwSetTime $chain$1$glfwSetTime $pdata$0$glfwSetTime $chain$0$glfwSetTime $pdata$glfwSetTime $unwind$glfwSetTime ??_C@_0BA@KMGHNAFM@Invalid?5time?5?$CFf?$AA@ __real@43e0000000000000 __real@42112e0be8240000 glfwGetTimerValue $pdata$glfwGetTimerValue $unwind$glfwGetTimerValue glfwGetTimerFrequency $pdata$glfwGetTimerFrequency $unwind$glfwGetTimerFrequency glfwDestroyCursor $pdata$3$glfwDestroyCursor $chain$3$glfwDestroyCursor $pdata$2$glfwDestroyCursor $chain$2$glfwDestroyCursor $pdata$1$glfwDestroyCursor $chain$1$glfwDestroyCursor $pdata$0$glfwDestroyCursor $chain$0$glfwDestroyCursor $pdata$glfwDestroyCursor $unwind$glfwDestroyCursor __imp_free _glfwPlatformDestroyCursor glfwCreateCursor $pdata$glfwCreateCursor $unwind$glfwCreateCursor _glfwPlatformCreateCursor __imp_calloc glfwCreateStandardCursor $pdata$2$glfwCreateStandardCursor $chain$2$glfwCreateStandardCursor $pdata$0$glfwCreateStandardCursor $chain$0$glfwCreateStandardCursor $pdata$glfwCreateStandardCursor $unwind$glfwCreateStandardCursor _glfwPlatformCreateStandardCursor ??_C@_0BL@JCGOGHAB@Invalid?5standard?5cursor?5?$CFi?$AA@ 
/345            1459697978              100666  7925      `
d�# :9W�  �       .drectve        s   �               
 .debug$S        �   �              @ B.bss                               � @�.text           �   �  '          P`.rdata             
              @@@.rdata          $   '              @@@.rdata          $   K              @@@.rdata          #   o              @@@.rdata          0   �              @@@.rdata          (   �              @@@.rdata             �              @@@.rdata             �              @@@.rdata          $   	              @@@.rdata             8	              @@@.rdata          $   T	              @@@.text           �   x	  
          P`.pdata             e
  q
         @0@.xdata             �
  �
         @0@.text           s   �
         
    P`.pdata             �  �         @0@.xdata             �              @0@.text             �  �          P`.pdata             �
  �
         @0@.xdata             �
  �
         @0@.pdata               &         @0@.xdata             D  T         @0@.pdata             r  ~         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �           @0@.pdata             *  6         @0@.xdata             T              @0@.text           $   \               P`.text              �  �          P`.text              �  �          P`   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   x   :     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\init.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler 伭���凒	wfH�    Hc翄寕    H�酘�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    �                                            9       8    $   6    ,   2    4   .    <   *    D   &    L   "    T       \       d       l       t       |   7    �   3    �   /    �   +    �   '    �   #    �       �       �       �       ERROR: UNKNOWN GLFW ERROR The specified window has no context The requested format is unavailable A platform-specific error occurred The requested client API version is unavailable The requested client API is unavailable Out of memory Invalid value for parameter Invalid argument for enum parameter There is no current context The GLFW library is not initialized H塗$L塂$L塋$ S�0   �    H+郒�    H3腍墑$    L�    嬞M吚tQH呉t?L嬄L崒$P   H峀$ �    �    秾$   L�    3覅繦岲$ H蕡�$   ��    H嬓嬎A�蠬媽$    H3惕    H伳0   [�   G        E    2   	    Y   D    h   	    �       �   F        �           H       H       B    / 0             C    H冹(�=     u\H�
    3褹竴  �    �    吚u�    3繦兡(描    H�
�   �    �       H��   �    H��   �    �   H兡(�                 X    "   W    +   V    7   U    >       C   T    I       T       Y   S    `       e   R        s           Y       Y       Q     B  H冹(�=     勻   3繦塡$0H�8  H�@  H��   H吚tH嬋�    H��   H吚u霩��   H吚tH嬋�    H��   H吚u鞁�   3蹍襼=H墊$ 3��    H��   H�儁X tH峇@�    ��   �肏兦;趞諬媩$ �    ��   H�
�   �    H��       ��       �    H�
    3褹竴  �    H媆$0�        H兡(�              "       )       6       =       I       V   ~    ]       h       �       �   }    �       �   |    �       �       �   {    �       �       �   V    �       �   X    �                   �       �       b    !                 �       �       z    �             �       �       h    !      r          �       �       t    r   �           �       �       n    ! t    r          �       �       t       r           �       �       t    ! 4               �       �       z                   �       �       z     B  H吷t�   H呉t�   M吚tA�     竺�       �    H�    H�
    �   	    
   	    @comp.idov� ��   .drectve       s                 .debug$S       �                 _glfw   �       .bss                                                         .text          �      � �         (           .rdata                �8蝇         7           .rdata         $       b�|         m           $LN2    i       .rdata         $       攩齡         �           $LN3    a       .rdata         #       {頕�         �           $LN4    Y       .rdata      	   0       n��               	    $LN5    Q       .rdata      
   (       _H漸         R      
    $LN6    I       .rdata                蟒         �          $LN7    A       .rdata                鶺給         �          $LN8    9       .rdata      
   $       nS6         �      
    $LN9    1       .rdata                饏         #          $LN10   )       .rdata         $       蹺�         [          $LN11   !       $LN16   |           �           .text          �      巌�         �          .pdata               曨垹        �          .xdata               X�E        �              �               �                                         __chkstk         $LN7            .text          s   
   +n     glfwInit        .pdata               s栠"        +          .xdata                �9�        ;              L               c               ~               �               �               �           memset           $LN6            .text               躴俨         �          .pdata               1偯        �          .xdata               $躢K        �          .pdata               >'�"                  .xdata               
C�,        #          .pdata               K��        :          .xdata               W摄>        Q          .pdata               岍髸        h          .xdata               眗                  .pdata               �64�        �          .xdata                 �9�        �               �               �               �                                         $LN18           .text       !   $       ]��         &      !    .text       "         �%         5      "        J           .text       #         K&�         h      #    }  _glfwInitialized _glfwErrorCallback getErrorString ??_C@_0BK@PMMAHJAK@ERROR?3?5UNKNOWN?5GLFW?5ERROR?$AA@ ??_C@_0CE@OJFJMLEI@The?5specified?5window?5has?5no?5cont@ ??_C@_0CE@PCLEMLLO@The?5requested?5format?5is?5unavaila@ ??_C@_0CD@HJEBEGLD@A?5platform?9specific?5error?5occurr@ ??_C@_0DA@NMGNJHAE@The?5requested?5client?5API?5version@ ??_C@_0CI@GDIOIKBB@The?5requested?5client?5API?5is?5unav@ ??_C@_0O@NALGGDJF@Out?5of?5memory?$AA@ ??_C@_0BM@BADCNPOM@Invalid?5value?5for?5parameter?$AA@ ??_C@_0CE@KDBKFLEE@Invalid?5argument?5for?5enum?5parame@ ??_C@_0BM@PKHPFLOM@There?5is?5no?5current?5context?$AA@ ??_C@_0CE@BEELAIPG@The?5GLFW?5library?5is?5not?5initiali@ __ImageBase _glfwInputError $pdata$_glfwInputError $unwind$_glfwInputError __GSHandlerCheck __imp_vsnprintf __security_cookie __security_check_cookie $pdata$glfwInit $unwind$glfwInit glfwDefaultWindowHints _glfwPlatformGetTimerValue _glfwPlatformGetMonitors _glfwInitVulkan _glfwPlatformTerminate _glfwPlatformInit glfwTerminate $pdata$3$glfwTerminate $chain$3$glfwTerminate $pdata$2$glfwTerminate $chain$2$glfwTerminate $pdata$1$glfwTerminate $chain$1$glfwTerminate $pdata$0$glfwTerminate $chain$0$glfwTerminate $pdata$glfwTerminate $unwind$glfwTerminate _glfwFreeMonitors _glfwTerminateVulkan _glfwPlatformSetGammaRamp glfwDestroyCursor glfwDestroyWindow glfwGetVersion glfwGetVersionString _glfwPlatformGetVersionString glfwSetErrorCallback 
/371            1459697978              100666  16724     `
d哃 :9W�$  �       .drectve        s   �               
 .debug$S        �   g              @ B.text           +  �            P`.pdata             �  �         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             
           @0@.pdata             <  H         @0@.xdata             f              @0@.rdata          .   ~              @@@.rdata          	   �              @@@.rdata          .   �              @@@.rdata             �              @@@.rdata             �              @@@.rdata             �              @@@.text           �  
  �          P`.pdata             �  �         @0@.xdata             �              @0@.rdata          $   �              @@@.rdata          #   �              @@@.rdata              �              @@@.rdata                           @@@.rdata          G   <              @P@.rdata          C   �              @P@.rdata             �              @@@.rdata             �              @@@.text           �  �               P`.pdata             �  �         @0@.xdata               *         @0@.pdata             H  T         @0@.xdata              r  �         @0@.pdata             �  �         @0@.xdata             �              @0@.text           �   �  |          P`.pdata             �  �         @0@.xdata             �              @0@.text           4   �  �          P`.text           )   &  O          P`.pdata             m  y         @0@.xdata             �              @0@.text           /   �  �          P`.text           I   �  ?          P`.pdata             q  }         @0@.xdata             �              @0@.text           M  �  �      
    P`.pdata             T  `         @0@.xdata             ~  �         @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata                         @0@.xdata             >  V         @0@.pdata             t  �         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �           @0@.pdata             ,  8         @0@.xdata             V              @0@.rdata          %   ^              @@@.rdata             �              @@@.text           Q   �  �          P`.pdata             %  1         @0@.xdata             O              @0@.text           �  W  �!      )    P`.pdata             r#  ~#         @0@.xdata             �#              @0@.rdata          6   �#              @@@.rdata             �#              @@@.rdata             �#              @@@.rdata             $              @@@.rdata             !$              @@@.rdata             6$              @@@.rdata             H$              @@@.rdata              \$              @@@.rdata          
   |$              @@@.rdata             �$              @@@.rdata             �$              @@@   /DEFAULTLIB:"msvcprt" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"MSVCRT" /DEFAULTLIB:"OLDNAMES"    �   {   =     D:\T-Work\GitHub\glfw\src\glfw.dir\Release\context.obj : < b  �    ov    ov Microsoft (R) Optimizing Compiler  H塡$H塴$VATAUAVAWH冹PH�    H�5    3跦塂$8H�    M嬮M嬸L孃L嬦H塂$@H塼$0H塡$H�  �    �  ��(  H嬭H吚uH�    �  �    3篱�   H壖$�   @ H兩�3繦孇虍H嬛H餮L岮�H峺�H嬐�    吚tH媡�8H�肏咑u碗H顰�$  H�    M嬑M嬊H嬐L塴$ �    H嫾$�   吚uH�    �  �    3离�   L峔$PI媅8I媖@I嬨A_A^A]A\^�   /    !   ,    /   )    U   &    o   %    y   "    �   !    �        �       �         "    �   +                        
    !       �                            �   �                             ! t     �                                �                             
 T 4 ����
�`No version found in client API version string %d.%d.%d Client API version string retrieval is broken OpenGL ES  OpenGL ES-CM  OpenGL ES-CL  H冹(�吚t)=  t-=  tH�    D嬂�  �    3繦兡(�=  呭   D婣A凐尰   婣吚埌   A凐u	凐彙   A凐u	凐彃   A凐u	A;�弮   婹呉tT侜  t#侜  tD嬄H�    �  �    3繦兡(肁凐~A凐u凐}H�    �  �    3繦兡(脙y 剝   A凐}H�    �  �    3繦兡(肈婭H�    �  �    3繦兡(�=  uBD婣A凐|婣吚xA凐uA;�
A凐u 吚~D婭H�    �  �    3繦兡(肈婣E吚t*A侙 t!A侙 tH�    �  �    3繦兡(肈婣 E吚t*A侙P t!A侙P tH�    �  �    3繦兡(酶   H兡(�   P    (   "    �   M    �   "    �   J    �   "    �   G    �   "      D      "    W  A    a  "    �  >    �  "    �  ;    �  "        �          Q       Q       8     B  Invalid context release behavior %i Invalid context robustness mode %i Invalid OpenGL ES version %i.%i Invalid OpenGL version %i.%i Forward-compatibility is only defined for OpenGL version 3.0 and above Context profiles are only defined for OpenGL version 3.2 and above Invalid OpenGL profile %i Invalid client API %i @UATAUAWA兲�3鞮嬌E嬱E孅E吚勏  L塼$@H塡$(H塼$0H墊$8H兟(E嬸峂f怉儁, ~
儂 剗  婤A9A8卲  A媃E3覅踾D9R銬D袮媦�~	儂� uA�翧媞咑~	儂� uA�翧婣(吚~�
;葈+罝蠥婭0吷~	儂 uA�翧�E3蹆�tD嬝D+Z谽A儁�t
A婣+B�D谹儁�t
A婣+B�D谽3纼�tD嬅D+B銭��t嬊+B�D纼�t嬈+B�D繟儁�t
A婣+B�D繟儁�t
A婣+B�D繟儁 �t
A婣 +B�D繟儁$�t
A婣$+B�D纼�t嬃+BD繟儁4 t	儂 uA�繣;詓H峧仉uE;輗uE;莝H峧仉	H岼豀;蛈	E嬧E嬰E孁�   H兟HI��卐��L媡$@H媩$8H媡$0H媆$(H嬇A_A]A\]蔑  �          g       g       Z    !                  g       g       f        �          g       g       `    ! t d 
4 �                g       g       f                    g       g       f     ���PH塡$H塼$WH冹 H嬟H嬹H嬔H嬎�    L嬝H吚tF�    H兩�3繦孇虍H餮H�蒊薒;踭A�{� u�< t*劺t&H嬛H嬞�    L嬝H吚u�3繦媆$0H媡$8H兡 _肏媆$0H媡$8�   H兡 _�   q    c   q        �           r       r       p     d 4 2p�=     u3夜  �    H吷t児�   u3夜
  �    �       w       "    +   "    0   v    H冹(�=     u3夜  �    3繦兡(肏兡(�       w       "    %   &        )           �       �       �     B  �=     u3夜  �    児�   u3夜
  �    �       w       "    &   "    +   �    @SH冹 �=     嬞u3夜  H兡 [�    �    H吚u3夜  H兡 [�    嬎H兡 [�       w       "    #   &    9   "    E   �        I           �       �       �     20@VH冹 �=     H嬹u3夜  �    3繦兡 ^肏墊$@�    H孁H吚u3夜  �    H媩$@3繦兡 ^脌> uH�    �  �    H媩$@3繦兡 ^脙隔  H塡$0|tH峊$8��  ��   3�9\$8~EfD  嬘�  ��  H吚tTL嬈L+��     �B� +製H�绤蓇韰襱a��;\$8|罤嬑H媆$0H媩$@H兡 ^�    �  ��(  H吚u#H�    �  �    H媆$0H媩$@3繦兡 ^肏嬓H嬑�    吚t媆$0H媩$@�   H兡 ^�   w       "    ,   &    @   "    Y   �    c   "    �   �    	  �      "    0  j    )  M          �       �       �    !   t  4     &          �       �       �    �   )          �       �       �    !   t  4     &          �       �       �    t   �           �       �       �    ! 4  t     &          �       �       �    Q   t           �       �       �    !   t     &          �       �       �    &   Q           �       �       �    ! t     &          �       �       �        &           �       �       �     2`Extension string retrieval is broken Extension name is empty string @SH冹 �=     H嬞u3夜  �    3繦兡 [描    H吚u3夜  �    3繦兡 [肏嬎H兡 [�       w       "    '   &    8   "    M   �        Q           �       �       �     20H塡$H塴$H塼$ WATAUH冹0H嬹�    3�9=    H嬝u3夜  �    嬊�'�    H吚u3夜  �    H嬊�H�
    �    H墐   9=    u3夜  �    H嬊�'�    H吚u3夜  �    H嬊�H�
    �    L崑�  L崈�  H崜�  H崑�  H墐(  �    吚剫  媼�  D婩A;�孶  u婩9凐  孌  凒|b9=    u3夜  �    H嬊�'�    H吚u3夜  �    H嬊�H�
    �    H墐  H吚uH�    �  �    �   伝�    呬   兓�  |kH峊$X��  ��   D媆$XA雒t
莾      A雒t莾     �$H�
    �    吚t9~t
莾     D媆$XA雒t
莾     媰�  凐}凐uW兓�  |NH峊$X�&�  ��   D媆$XA雒u)A雒t莾    H�
    �*H�
    �    吚t
莾    H�
    �H�
    �    吚t=H峊$X筕�  ��   D媆$XA侞R�  u莾   �A侞a�  u
莾   H�
    �    吚t9H峊$X果�  ��   D媆$XE呟u莾  P �A侞鼈  u
莾  P 9=    u3夜  �    �'�    H吚u3夜  �    �H�
    �    H孁� @  �譎嬎�    �   �%媰�  D婲H�    塂$(塋$ �  �    3繦媆$PH媗$`H媡$hH兡0A]A\_�   &    $   w    5   "    >   &    O   "    [   �    `   �    m   w    {   "    �   &    �   "    �   �    �   �    �         w      "      &    /  "    ;  �    @  �    S  �    ]  "    �  �    �  �    1  �    :  �    ?  �    T  �    ]  �    b  �    �  �    �  �    �  w       "      &      "    !  �    &  �    8  �    P  �    b  "        �          �       �       �    
 d
 T 4
 R��pRequested client API version %i.%i, got version %i.%i glClear GL_KHR_context_flush_control GL_EXT_robustness GL_ARB_compatibility GL_ARB_robustness GL_ARB_debug_output Entry point retrieval is broken glGetStringi glGetString glGetIntegerv @comp.idov� ��   .drectve       s                 .debug$S       �                 .text          +     &�                    .pdata               /r~2                   .xdata               蘟&n        3           .pdata               蘄o�        O           .xdata               噴=        k           .pdata                媞�        �           .xdata      	          \矖�        �       	    .rdata      
   .       i7�         �       
        �            .rdata         	       翜lG                       .               <           .rdata         .       勢客         L              �           .rdata      
          s�/         �      
    .rdata                �#c         �          .rdata                辿醔         �          .text          �     媲=�                   .pdata               鏷坯        2          .xdata                �9�        S          .rdata         $       .y!�         u          .rdata         #       矑崠         �          .rdata                 +蕍S         �          .rdata                �&�         )          .rdata         G       蠶�         h          .rdata         C       蠔��         �          .rdata                |傇         �          .rdata                !挊U                   $LN24           .text          �      7�         E          .pdata               o@S3        Y          .xdata               簣!@        v          .pdata               佺朵        �          .xdata                灝
        �          .pdata                Vbv�        �           .xdata      !          �*�        �      !    $LN37           .text       "   �      2繼C               "    .pdata      #         v斤�"        !      #    .xdata      $          O�"        E      $        j           $LN14       "    .text       %   4      &唓         w      %        �               �           .text       &   )      � 疞         �      &    .pdata      '         }y9�&        �      '    .xdata      (          �9�&        �      (    $LN4        &    .text       )   /      簼K�               )                    .text       *   I      鑑	�         9      *    .pdata      +         瀑�6*        J      +    .xdata      ,          （亵*        b      ,        {           $LN5        *    .text       -   M  
   M\         �      -    .pdata      .         !怡P-        �      .    .xdata      /         P睽$-        �      /    .pdata      0         =9-        �      0    .xdata      1         P睽$-              1    .pdata      2         F-        ,      2    .xdata      3         R�3�-        L      3    .pdata      4         	壞�-        l      4    .xdata      5         媸/�-        �      5    .pdata      6         @崁n-        �      6    .xdata      7         J汓�-        �      7    .pdata      8         裬?-        �      8    .xdata      9          W�-        
      9    .rdata      :   %       S^閣         )      :        b           .rdata      ;          "怹         �      ;    $LN23       -    .text       <   Q      .� �         �      <    .pdata      =         X髮�<        �      =    .xdata      >          （亵<        �      >        	           $LN5        <    .text       ?   �  )   コ         !	      ?    .pdata      @         贌y�?        <	      @    .xdata      A          zo衈?        ^	      A    .rdata      B   6       vq)�         �	      B    .rdata      C          镄�         �	      C    .rdata      D          鶠潻         �	      D    .rdata      E          aeW�         
      E    .rdata      F          b�3�         :
      F    .rdata      G          灒         g
      G    .rdata      H          ��         �
      H    .rdata      I           辵鮑         �
      I    .rdata      J   
       灅2         �
      J    .rdata      K          眏薻               K    .rdata      L          崵7         @      L    $LN59       ?    e  parseVersionString $pdata$1$parseVersionString $chain$1$parseVersionString $pdata$0$parseVersionString $chain$0$parseVersionString $pdata$parseVersionString $unwind$parseVersionString ??_C@_0CO@DNIGMAHN@No?5version?5found?5in?5client?5API?5v@ __imp_sscanf ??_C@_08FOJKHHJA@?$CFd?4?$CFd?4?$CFd?$AA@ __imp_strncmp _glfwInputError ??_C@_0CO@DBDPCKJA@Client?5API?5version?5string?5retrie@ _glfwPlatformGetCurrentContext ??_C@_0L@JLFHOCGA@OpenGL?5ES?5?$AA@ ??_C@_0O@ENGHJHNB@OpenGL?5ES?9CM?5?$AA@ ??_C@_0O@EMKFPNOG@OpenGL?5ES?9CL?5?$AA@ _glfwIsValidContextConfig $pdata$_glfwIsValidContextConfig $unwind$_glfwIsValidContextConfig ??_C@_0CE@HHGIDEAE@Invalid?5context?5release?5behavior@ ??_C@_0CD@GAIKDKHK@Invalid?5context?5robustness?5mode?5@ ??_C@_0CA@LFIDGAHJ@Invalid?5OpenGL?5ES?5version?5?$CFi?4?$CFi?$AA@ ??_C@_0BN@DHKEBMCG@Invalid?5OpenGL?5version?5?$CFi?4?$CFi?$AA@ ??_C@_0EH@CMDCLIEA@Forward?9compatibility?5is?5only?5de@ ??_C@_0ED@FEABOOIM@Context?5profiles?5are?5only?5define@ ??_C@_0BK@IEJBEGLB@Invalid?5OpenGL?5profile?5?$CFi?$AA@ ??_C@_0BG@CEONMDO@Invalid?5client?5API?5?$CFi?$AA@ _glfwChooseFBConfig $pdata$4$_glfwChooseFBConfig $chain$4$_glfwChooseFBConfig $pdata$3$_glfwChooseFBConfig $chain$3$_glfwChooseFBConfig $pdata$_glfwChooseFBConfig $unwind$_glfwChooseFBConfig _glfwStringInExtensionString $pdata$_glfwStringInExtensionString $unwind$_glfwStringInExtensionString __imp_strstr glfwMakeContextCurrent _glfwPlatformMakeContextCurrent _glfwInitialized glfwGetCurrentContext $pdata$glfwGetCurrentContext $unwind$glfwGetCurrentContext glfwSwapBuffers _glfwPlatformSwapBuffers glfwSwapInterval $pdata$glfwSwapInterval $unwind$glfwSwapInterval _glfwPlatformSwapInterval glfwExtensionSupported $pdata$9$glfwExtensionSupported $chain$9$glfwExtensionSupported $pdata$7$glfwExtensionSupported $chain$7$glfwExtensionSupported $pdata$5$glfwExtensionSupported $chain$5$glfwExtensionSupported $pdata$2$glfwExtensionSupported $chain$2$glfwExtensionSupported $pdata$0$glfwExtensionSupported $chain$0$glfwExtensionSupported $pdata$glfwExtensionSupported $unwind$glfwExtensionSupported ??_C@_0CF@KMMHNOMI@Extension?5string?5retrieval?5is?5br@ _glfwPlatformExtensionSupported ??_C@_0BP@MMGGFAGN@Extension?5name?5is?5empty?5string?$AA@ glfwGetProcAddress $pdata$glfwGetProcAddress $unwind$glfwGetProcAddress _glfwPlatformGetProcAddress _glfwRefreshContextAttribs $pdata$_glfwRefreshContextAttribs $unwind$_glfwRefreshContextAttribs ??_C@_0DG@HKHFODHE@Requested?5client?5API?5version?5?$CFi?4@ ??_C@_07IBAPPAHJ@glClear?$AA@ ??_C@_0BN@FNDGKECA@GL_KHR_context_flush_control?$AA@ ??_C@_0BC@CLDFFND@GL_EXT_robustness?$AA@ ??_C@_0BF@CJMIIMBG@GL_ARB_compatibility?$AA@ ??_C@_0BC@EOBKJDCM@GL_ARB_robustness?$AA@ ??_C@_0BE@OHIPHHMJ@GL_ARB_debug_output?$AA@ ??_C@_0CA@LDAANPIM@Entry?5point?5retrieval?5is?5broken?$AA@ ??_C@_0N@PILJCBOD@glGetStringi?$AA@ ??_C@_0M@OPOBFDCB@glGetString?$AA@ ??_C@_0O@DHHDCCLF@glGetIntegerv?$AA@ 