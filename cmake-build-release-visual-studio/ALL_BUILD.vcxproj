<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BF8E45D4-804F-339C-85FD-31C3C98BC65B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/RTXPT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystem.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/RTXPT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystem.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\ZERO_CHECK.vcxproj">
      <Project>{58F23B37-BE28-30E3-A181-C3BE205A48B0}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\streamline\CopyStreamlineDLLs.vcxproj">
      <Project>{7E621EEB-02E4-3761-9C3A-5C91D5D076FC}</Project>
      <Name>CopyStreamlineDLLs</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\DirectX-Guids.vcxproj">
      <Project>{A58D43BF-C508-34A3-B16F-AFEA1A56C387}</Project>
      <Name>DirectX-Guids</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\DirectX-Headers.vcxproj">
      <Project>{B2FB3632-CD03-3F1A-8F9D-7356D959E51E}</Project>
      <Name>DirectX-Headers</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build\MathLib.vcxproj">
      <Project>{31C424FF-024E-35CE-9E3B-381364AAA2AC}</Project>
      <Name>MathLib</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Nrd\NRD.vcxproj">
      <Project>{5845BBDA-C990-3DD7-A1D1-9DD8B901FD1A}</Project>
      <Name>NRD</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Nrd\NRDIntegration.vcxproj">
      <Project>{E12E05CE-B89F-3CB8-B710-3407428C6B1C}</Project>
      <Name>NRDIntegration</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.vcxproj">
      <Project>{9EFF3150-68AB-32DE-8EF5-72EB40C85EB2}</Project>
      <Name>Rtxpt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\ShaderDynamicAssets.vcxproj">
      <Project>{4AD1DEEA-E04D-35AB-9DBC-5350051E3F87}</Project>
      <Name>ShaderDynamicAssets</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\ShaderMake\ShaderMake.vcxproj">
      <Project>{4B4D5747-A079-3907-9F47-690F3E909967}</Project>
      <Name>ShaderMake</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\ShaderMake\ShaderMakeBlob.vcxproj">
      <Project>{D45AEDFB-0892-35C2-9752-99555E1EE837}</Project>
      <Name>ShaderMakeBlob</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\glfw.vcxproj">
      <Project>{23D11E95-733E-3C24-A29F-E130FD052183}</Project>
      <Name>glfw</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\glm\glm\glm.vcxproj">
      <Project>{C3907F60-9DC9-3CD2-B57F-1B505CF66EAA}</Project>
      <Name>glm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\imgui.vcxproj">
      <Project>{F78FE880-491C-3675-888A-87ADBCCEA77C}</Project>
      <Name>imgui</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\src\lib_json\jsoncpp_static.vcxproj">
      <Project>{4C92DFFC-49AC-34B6-A86C-810BBEF3D39C}</Project>
      <Name>jsoncpp_static</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\lz4\build\cmake\lz4_static.vcxproj">
      <Project>{B1224FE2-79E6-34A6-90E7-D54952731B31}</Project>
      <Name>lz4_static</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\miniz\miniz.vcxproj">
      <Project>{8F6EC7C1-44B5-3893-B15D-7BEC6665665F}</Project>
      <Name>miniz</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\nvrhi.vcxproj">
      <Project>{6B247773-3AD0-3804-80D4-58B109DC712F}</Project>
      <Name>nvrhi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\nvrhi_d3d12.vcxproj">
      <Project>{F0155E33-9BA4-3B64-BD3E-2E832D3FA9E5}</Project>
      <Name>nvrhi_d3d12</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\omm-gpu-nvrhi.vcxproj">
      <Project>{A7F00D95-BC26-3E64-BFFC-EE5548716431}</Project>
      <Name>omm-gpu-nvrhi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-lib\omm-lib.vcxproj">
      <Project>{72FCE207-945F-3B03-B614-AB3880082C5C}</Project>
      <Name>omm-lib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\xxHash\cmake_unofficial\xxhash.vcxproj">
      <Project>{7B810F7C-4762-34AC-B6B0-C1504DA3EA29}</Project>
      <Name>xxhash</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>