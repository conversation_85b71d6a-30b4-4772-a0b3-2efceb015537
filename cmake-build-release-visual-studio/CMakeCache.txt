# This is the CMakeCache file.
# For build in directory: d:/RTXPT/cmake-build-release-visual-studio
# It was generated by CMake: C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Amalgamate sources into miniz.h/c
AMALGAMATE_SOURCES:BOOL=OFF

//Build examples
BUILD_EXAMPLES:BOOL=OFF

//Build fuzz targets
BUILD_FUZZERS:BOOL=OFF

//Build a header-only version
BUILD_HEADER_ONLY:BOOL=OFF

//OFF
BUILD_NO_STDIO" Build a without stdio version":BOOL=OFF

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Build tests
BUILD_TESTS:BOOL=OFF

//Path to a program.
CMAKE_AR:FILEPATH=D:/1softwares/VS2022/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/lib.exe

CMAKE_ARCHIVE_OUTPUT_DIRECTORY:STRING=

//Enable colored diagnostics throughout.
CMAKE_COLOR_DIAGNOSTICS:BOOL=ON

CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG /Zi /Zi

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG /Zi /Zi

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO /DEBUG /DEBUG

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//default install path
CMAKE_INSTALL_PREFIX:PATH=D:/RTXPT/cmake-build-release-visual-studio/install

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

CMAKE_LIBRARY_OUTPUT_DIRECTORY:STRING=

//Path to a program.
CMAKE_LINKER:FILEPATH=D:/1softwares/VS2022/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

CMAKE_PDB_OUTPUT_DIRECTORY:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=RTXPathTracing

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.9.6

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=9

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=6

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

CMAKE_RUNTIME_OUTPUT_DIRECTORY:STRING=D:/RTXPT/bin

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO /DEBUG /DEBUG

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

CMAKE_WARN_DEPRECATED:BOOL=OFF

//Path to the coverage program that CTest uses for performing coverage
// inspection
COVERAGE_COMMAND:FILEPATH=COVERAGE_COMMAND-NOTFOUND

//Extra command line flags to pass to the coverage tool
COVERAGE_EXTRA_FLAGS:STRING=-l

//Enable to build 7-Zip packages
CPACK_BINARY_7Z:BOOL=OFF

//Enable to build IFW packages
CPACK_BINARY_IFW:BOOL=OFF

//Enable to build Inno Setup packages
CPACK_BINARY_INNOSETUP:BOOL=OFF

//Enable to build NSIS packages
CPACK_BINARY_NSIS:BOOL=ON

//Enable to build NuGet packages
CPACK_BINARY_NUGET:BOOL=OFF

//Enable to build WiX packages
CPACK_BINARY_WIX:BOOL=OFF

//Enable to build ZIP packages
CPACK_BINARY_ZIP:BOOL=OFF

//Enable to build 7-Zip source packages
CPACK_SOURCE_7Z:BOOL=ON

//Enable to build ZIP source packages
CPACK_SOURCE_ZIP:BOOL=ON

//How many times to retry timed-out CTest submissions.
CTEST_SUBMIT_RETRY_COUNT:STRING=3

//How long to wait between timed-out CTest submissions.
CTEST_SUBMIT_RETRY_DELAY:STRING=5

//Set to ON to build examples
CXXOPTS_BUILD_EXAMPLES:BOOL=OFF

//Set to ON to build tests
CXXOPTS_BUILD_TESTS:BOOL=OFF

//Generate the install target
CXXOPTS_ENABLE_INSTALL:BOOL=OFF

//Add warnings to CMAKE_CXX_FLAGS
CXXOPTS_ENABLE_WARNINGS:BOOL=OFF

//Use ICU Unicode library
CXXOPTS_USE_UNICODE_HELP:BOOL=OFF

//Maximum time allowed before CTest will kill the test.
DART_TESTING_TIMEOUT:STRING=1500

//Embed shader PDBs with shader binary files
DONUT_EMBED_SHADER_PDBS:BOOL=ON

//Declare symbols to make the OS run the app on discrete GPU on
// laptops
DONUT_FORCE_DISCRETE_GPU:BOOL=OFF

DONUT_SHADER_INCLUDE_DIR:PATH=D:/RTXPT/External/Donut/include

//Directory to fetch streamline to, empty uses build directory
// default
DONUT_STREAMLINE_FETCH_DIR:STRING=D:/RTXPT/External/Donut/thirdparty/streamline

//Tag of streamline git repo
DONUT_STREAMLINE_FETCH_TAG:STRING=

//Url to streamline git repo to fetch
DONUT_STREAMLINE_FETCH_URL:STRING=

//Search paths for streamline package
DONUT_STREAMLINE_SEARCH_PATHS:STRING=D:/RTXPT/External/Streamline

//Enable Aftermath crash dump generation with Donut
DONUT_WITH_AFTERMATH:BOOL=OFF

//Include Audio features (XAudio2)
DONUT_WITH_AUDIO:BOOL=OFF

//Enable the DX11 version of Donut
DONUT_WITH_DX11:BOOL=OFF

//Enable the DX12 version of Donut
DONUT_WITH_DX12:BOOL=ON

//Include LZ4
DONUT_WITH_LZ4:BOOL=OFF

//Include miniz (support for zip archives)
DONUT_WITH_MINIZ:BOOL=ON

//Enable NVRHI and related projects
DONUT_WITH_NVRHI:BOOL=ON

//Build Donut with statically linked shaders
DONUT_WITH_STATIC_SHADERS:BOOL=OFF

//Enable streamline, separate package required
DONUT_WITH_STREAMLINE:BOOL=ON

//Include TaskFlow
DONUT_WITH_TASKFLOW:BOOL=ON

//Include TinyEXR
DONUT_WITH_TINYEXR:BOOL=ON

//Donut unit-tests (see CMake/CTest documentation)
DONUT_WITH_UNIT_TESTS:BOOL=OFF

//Enable the Vulkan version of Donut
DONUT_WITH_VULKAN:BOOL=OFF

//Path to embedded dxc
DXC_CUSTOM_PATH:STRING=D:/RTXPT/External/dxc/bin/x64/

//Path to embedded dxc file for DX12
DXC_DXIL_EXECUTABLE:STRING=D:/RTXPT/External/dxc/bin/x64//dxc.exe

//Path to embedded dxc file for DX12
DXC_PATH:STRING=D:/RTXPT/External/dxc/bin/x64//dxc.exe

//Path to a program.
DXC_SPIRV_PATH:FILEPATH=C:/VulkanSDK/1.4.309.0/Bin/dxc.exe

//Build the google test suite
DXHEADERS_BUILD_GOOGLE_TEST:BOOL=OFF

//Build the test
DXHEADERS_BUILD_TEST:BOOL=OFF

//Installation logic
DXHEADERS_INSTALL:BOOL=OFF

//Dependencies for the target
DirectX-Guids_LIB_DEPENDS:STATIC=general;DirectX-Headers;

//Value Computed by CMake
DirectX-Headers_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut/nvrhi/thirdparty/DirectX-Headers

//Value Computed by CMake
DirectX-Headers_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
DirectX-Headers_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=D:/RTXPT/cmake-build-release-visual-studio/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for mathlib
FETCHCONTENT_SOURCE_DIR_MATHLIB:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of mathlib
FETCHCONTENT_UPDATES_DISCONNECTED_MATHLIB:BOOL=OFF

//Path to a program.
GITCOMMAND:FILEPATH=C:/Users/<USER>/AppData/Local/UGit/app-5.37.0/resources/app/git/cmd/git.exe

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Users/<USER>/AppData/Local/UGit/app-5.37.0/resources/app/git/cmd/git.exe

//Value Computed by CMake
GLFW_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/glfw

//Build the GLFW documentation
GLFW_BUILD_DOCS:BOOL=OFF

//Build the GLFW example programs
GLFW_BUILD_EXAMPLES:BOOL=OFF

//Build the GLFW test programs
GLFW_BUILD_TESTS:BOOL=OFF

//Build support for Win32
GLFW_BUILD_WIN32:BOOL=ON

//Generate installation target
GLFW_INSTALL:BOOL=OFF

//Value Computed by CMake
GLFW_IS_TOP_LEVEL:STATIC=OFF

//Library type override for GLFW (SHARED, STATIC, OBJECT, or empty
// to follow BUILD_SHARED_LIBS)
GLFW_LIBRARY_TYPE:STRING=

//Value Computed by CMake
GLFW_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut/thirdparty/glfw

//Force use of high-performance GPU on hybrid systems
GLFW_USE_HYBRID_HPG:BOOL=OFF

//Generate the install target
GLM_BUILD_INSTALL:BOOL=OFF

//Build dynamic/static library
GLM_BUILD_LIBRARY:BOOL=ON

//Build the test programs
GLM_BUILD_TESTS:BOOL=OFF

//Disable platform, compiler, arch and C++ language detection
GLM_DISABLE_AUTO_DETECTION:BOOL=OFF

//Enable C++ 11
GLM_ENABLE_CXX_11:BOOL=OFF

//Enable C++ 14
GLM_ENABLE_CXX_14:BOOL=OFF

//Enable C++ 17
GLM_ENABLE_CXX_17:BOOL=OFF

//Enable C++ 20
GLM_ENABLE_CXX_20:BOOL=OFF

//Enable C++ 98
GLM_ENABLE_CXX_98:BOOL=OFF

//Enable fast math optimizations
GLM_ENABLE_FAST_MATH:BOOL=OFF

//Enable language extensions
GLM_ENABLE_LANG_EXTENSIONS:BOOL=OFF

//Enable AVX optimizations
GLM_ENABLE_SIMD_AVX:BOOL=OFF

//Enable AVX2 optimizations
GLM_ENABLE_SIMD_AVX2:BOOL=OFF

//Enable ARM NEON optimizations
GLM_ENABLE_SIMD_NEON:BOOL=OFF

//Enable SSE2 optimizations
GLM_ENABLE_SIMD_SSE2:BOOL=OFF

//Enable SSE3 optimizations
GLM_ENABLE_SIMD_SSE3:BOOL=OFF

//Enable SSE 4.1 optimizations
GLM_ENABLE_SIMD_SSE4_1:BOOL=OFF

//Enable SSE 4.2 optimizations
GLM_ENABLE_SIMD_SSE4_2:BOOL=OFF

//Enable SSSE3 optimizations
GLM_ENABLE_SIMD_SSSE3:BOOL=OFF

//Force 'pure' instructions
GLM_FORCE_PURE:BOOL=OFF

GLOBAL_BIN_OUTPUT_PATH:STRING=D:/RTXPT/cmake-build-release-visual-studio

//Install project
INSTALL_PROJECT:BOOL=OFF

//Use static (MT/MTd) Windows runtime
JSONCPP_STATIC_WINDOWS_RUNTIME:BOOL=OFF

//Generate and install cmake package files
JSONCPP_WITH_CMAKE_PACKAGE:BOOL=OFF

//Compile JsonCpp example
JSONCPP_WITH_EXAMPLE:BOOL=OFF

//Generate and install .pc files
JSONCPP_WITH_PKGCONFIG_SUPPORT:BOOL=OFF

//Automatically run unit-tests as a post build step
JSONCPP_WITH_POST_BUILD_UNITTEST:BOOL=OFF

//Issue all the warnings demanded by strict ISO C and ISO C++
JSONCPP_WITH_STRICT_ISO:BOOL=ON

//Compile and (for jsoncpp_check) run JsonCpp test executables
JSONCPP_WITH_TESTS:BOOL=OFF

//Force compilation to fail if a warning occurs
JSONCPP_WITH_WARNING_AS_ERROR:BOOL=OFF

//Value Computed by CMake
LZ4_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/lz4/build/cmake

LZ4_BUILD_CLI:BOOL=OFF

//Value Computed by CMake
LZ4_IS_TOP_LEVEL:STATIC=OFF

//Use position independent code for static library (if applicable)
LZ4_POSITION_INDEPENDENT_LIB:BOOL=ON

//Value Computed by CMake
LZ4_SOURCE_DIR:STATIC=D:/RTXPT/External/Omm/external/lz4/build/cmake

//Command to build the project
MAKECOMMAND:STRING="C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" --build . --config "${CTEST_CONFIGURATION_TYPE}"

//Path to the memory checking command, used for memory error detection.
MEMORYCHECK_COMMAND:FILEPATH=MEMORYCHECK_COMMAND-NOTFOUND

//File that contains suppressions for the memory checker
MEMORYCHECK_SUPPRESSIONS_FILE:FILEPATH=

//Value Computed by CMake
NRD_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Nrd

//Disable shader compilation
NRD_DISABLE_SHADER_COMPILATION:BOOL=ON

//Custom DXC to use if Vulkan SDK is not installed
NRD_DXC_CUSTOM_PATH:STRING=custom/path/to/dxc

//DXC shader compiler path for NRD
NRD_DXC_PATH:STRING=D:/RTXPT/External/dxc/bin/x64//dxc.exe

//NRD embeds DXBC shaders
NRD_EMBEDS_DXBC_SHADERS:BOOL=OFF

//NRD embeds DXIL shaders
NRD_EMBEDS_DXIL_SHADERS:BOOL=OFF

//NRD embeds SPIRV shaders
NRD_EMBEDS_SPIRV_SHADERS:BOOL=OFF

//Value Computed by CMake
NRD_IS_TOP_LEVEL:STATIC=OFF

//Normal encoding variant (0-4, matches nrd::NormalEncoding)
NRD_NORMAL_ENCODING:STRING=2

//Roughness encoding variant (0-2, matches nrd::RoughnessEncoding)
NRD_ROUGHNESS_ENCODING:STRING=1

NRD_SHADERS_PATH:STRING=D:/RTXPT/cmake-build-release-visual-studio/NRDShaders

NRD_SHADER_OUTPUT_PATH:STRING=

//Value Computed by CMake
NRD_SOURCE_DIR:STATIC=D:/RTXPT/External/Nrd

//Build static library
NRD_STATIC_LIBRARY:BOOL=OFF

NRD_USE_PRECOMPILED_SHADERS:BOOL=OFF

//Path to NVAPI include headers/shaders
NVAPI_INCLUDE_DIR:STRING=D:/RTXPT/External/nvapi/

//Path to NVAPI .lib file
NVAPI_LIBRARY:STRING=D:/RTXPT/External/nvapi/amd64/nvapi64.lib

//Build NVRHI as a shared library (DLL or .so)
NVRHI_BUILD_SHARED:BOOL=OFF

//OFF
NVRHI_INSTALL:BOOL=ON

//Install CMake exports
NVRHI_INSTALL_EXPORTS:BOOL=OFF

//Include Aftermath support (requires NSight Aftermath SDK)
NVRHI_WITH_AFTERMATH:BOOL=OFF

//Build the NVRHI D3D11 backend
NVRHI_WITH_DX11:BOOL=OFF

//Build the NVRHI D3D12 backend
NVRHI_WITH_DX12:BOOL=ON

//Include NVAPI support (requires NVAPI SDK)
NVRHI_WITH_NVAPI:BOOL=ON

//Use RTXMU for acceleration structure management
NVRHI_WITH_RTXMU:BOOL=OFF

//Build NVRHI the validation layer
NVRHI_WITH_VALIDATION:BOOL=ON

//Build the NVRHI Vulkan backend
NVRHI_WITH_VULKAN:BOOL=OFF

//Build omm viewer tool
OMM_BUILD_VIEWER:BOOL=OFF

//cross compilation for aarch64
OMM_CROSSCOMPILE_AARCH64:BOOL=OFF

//cross compilation for x86_64
OMM_CROSSCOMPILE_X86_64:BOOL=OFF

//disable interprocedural optimization
OMM_DISABLE_INTERPROCEDURAL_OPTIMIZATION:BOOL=OFF

OMM_ENABLE_BENCHMARK:BOOL=OFF

//Enable fast math optimizations()
OMM_ENABLE_FAST_MATH:BOOL=ON

//enable openmp
OMM_ENABLE_OPENMP:BOOL=ON

//Embedded precompiled DXIL shaders. Require path to dxc.exe (normally
// located in Window SDK).
OMM_ENABLE_PRECOMPILED_SHADERS_DXIL:BOOL=OFF

//Embedded precompiled SPIRV shaders. Require path to Vulkan SDK.
OMM_ENABLE_PRECOMPILED_SHADERS_SPIRV:BOOL=OFF

OMM_ENABLE_TESTS:BOOL=OFF

//Build nvrhi integration layer
OMM_INTEGRATION_LAYER_NVRHI:BOOL=ON

//Generate install rules for OMM
OMM_LIB_INSTALL:BOOL=ON

//enable embedded shader debug info
OMM_SHADER_DEBUG_INFO:BOOL=OFF

//build static lib
OMM_STATIC_LIBRARY:BOOL=OFF

//Use the legacy target name of omm-lib: "omm-sdk"
OMM_USE_LEGACY_OMM_LIB_NAME:BOOL=OFF

//OMM_VK_B_SHIFT
OMM_VK_B_SHIFT:STRING=300

//OMM_VK_S_SHIFT
OMM_VK_S_SHIFT:STRING=100

//OMM_VK_T_SHIFT
OMM_VK_T_SHIFT:STRING=200

//OMM_VK_U_SHIFT
OMM_VK_U_SHIFT:STRING=400

//Value Computed by CMake
Opacity Micro-Map SDK_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Omm

//Value Computed by CMake
Opacity Micro-Map SDK_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Opacity Micro-Map SDK_SOURCE_DIR:STATIC=D:/RTXPT/External/Omm

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-openmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=-openmp

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=

//Skip RTXDI shader validation
RTXDI_SKIP_SHADER_VALIDATION:STRING=ON

//AgilitySDKPath
RTXPT_D3D_AGILITY_SDK_PATH:STRING=

//AgilitySDKVersion
RTXPT_D3D_AGILITY_SDK_VERSION:STRING=

//Attempt to automatically download DirectX AgilitySDK and enable
// experimental features
RTXPT_DOWNLOAD_AND_ENABLE_AGILITY_SDK:BOOL=

//Local user initials for user-specific settings
RTXPT_LOCAL_CONFIG_ID:STRING=NONAME

//Value Computed by CMake
RTXPathTracing_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio

//Value Computed by CMake
RTXPathTracing_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
RTXPathTracing_SOURCE_DIR:STATIC=D:/RTXPT

SHADERMAKE_BIN_OUTPUT_PATH:STRING=

//Toggles whether to search for DXC for DXIL
SHADERMAKE_FIND_DXC:BOOL=ON

//Toggles whether to search for DXC for SPIR-V
SHADERMAKE_FIND_DXC_SPIRV:BOOL=ON

//Toggles whether to search for FXC
SHADERMAKE_FIND_FXC:BOOL=OFF

//Toggles whether to search for dxc.exe and fxc.exe
SHADERMAKE_SEARCH_FOR_COMPILERS:BOOL=ON

//Name of the computer/site where compile is being run
SITE:STRING=SCOLU-PC0

//Path to a file.
STREAMLINE_CMAKE_FILE:FILEPATH=D:/RTXPT/External/Streamline/CMakeLists.txt

//Include DEEPDVC dll
STREAMLINE_FEATURE_DEEPDVC:BOOL=OFF

//Include DirectSR dll
STREAMLINE_FEATURE_DIRECTSR:BOOL=OFF

//Include DLSS-FG dll
STREAMLINE_FEATURE_DLSS_FG:BOOL=ON

//Include DLSS-RR dll
STREAMLINE_FEATURE_DLSS_RR:BOOL=ON

//Include DLSS-SR dll
STREAMLINE_FEATURE_DLSS_SR:BOOL=ON

//Include Imgui dll
STREAMLINE_FEATURE_IMGUI:BOOL=ON

//Include NIS dll
STREAMLINE_FEATURE_NIS:BOOL=OFF

//Include NRD dll
STREAMLINE_FEATURE_NRD:BOOL=OFF

//Include NSight Perf SDK dll
STREAMLINE_FEATURE_NVPERF:BOOL=OFF

//Include Reflex dll
STREAMLINE_FEATURE_REFLEX:BOOL=ON

//Import Streamline as an Interface without lib
STREAMLINE_IMPORT_AS_INTERFACE:BOOL=OFF

//Streamline Install Dir
STREAMLINE_INSTALL_DIR:STRING=C:/Program Files (x86)/RTXPathTracing

//SL SDK Root Directory
STREAMLINE_SDK_ROOT:STRING=D:/RTXPT/External/Streamline

//Value Computed by CMake
ShaderMake_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut/ShaderMake

//Value Computed by CMake
ShaderMake_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
ShaderMake_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut/ShaderMake

//Use MSVC runtime library DLL
USE_MSVC_RUNTIME_LIBRARY_DLL:BOOL=ON

XXHASH_BUNDLED_MODE:BOOL=ON

//Value Computed by CMake
cxxopts_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/cxxopts

//Value Computed by CMake
cxxopts_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
cxxopts_SOURCE_DIR:STATIC=D:/RTXPT/External/cxxopts

//Value Computed by CMake
donut_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut

//Value Computed by CMake
donut_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
donut_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut

//Dependencies for the target
donut_app_LIB_DEPENDS:STATIC=general;streamline;general;donut_core;general;donut_engine;general;glfw;general;imgui;general;nvrhi_d3d12;general;d3d12;general;dxgi;general;dxguid;general;nvrhi;

//Dependencies for the target
donut_core_LIB_DEPENDS:STATIC=general;jsoncpp_static;general;miniz;

//Dependencies for the target
donut_engine_LIB_DEPENDS:STATIC=general;donut_core;general;nvrhi;general;jsoncpp_static;general;ShaderMakeBlob;

//Dependencies for the target
donut_render_LIB_DEPENDS:STATIC=general;donut_core;general;donut_engine;

//Value Computed by CMake
glm_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/glm

//Value Computed by CMake
glm_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
glm_SOURCE_DIR:STATIC=D:/RTXPT/External/Omm/external/glm

//Value Computed by CMake
jsoncpp_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/jsoncpp

//Value Computed by CMake
jsoncpp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
jsoncpp_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut/thirdparty/jsoncpp

//Value Computed by CMake
miniz_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz

//Value Computed by CMake
miniz_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
miniz_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut/thirdparty/miniz

//Value Computed by CMake
nvrhi_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Donut/nvrhi

//Value Computed by CMake
nvrhi_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
nvrhi_SOURCE_DIR:STATIC=D:/RTXPT/External/Donut/nvrhi

//Dependencies for the target
nvrhi_d3d12_LIB_DEPENDS:STATIC=general;Microsoft::DirectX-Headers;general;Microsoft::DirectX-Guids;general;d3d12;general;nvapi;

//Value Computed by CMake
xxHash_BINARY_DIR:STATIC=D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/xxHash/cmake_unofficial

//Value Computed by CMake
xxHash_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
xxHash_SOURCE_DIR:STATIC=D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial


########################
# INTERNAL cache entries
########################

//Build shared libraries
BUILD_SHARED_LIBS:INTERNAL=OFF
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/RTXPT/cmake-build-release-visual-studio
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cpack.exe
//ADVANCED property for variable: CMAKE_CTEST_COMMAND
CMAKE_CTEST_COMMAND-ADVANCED:INTERNAL=1
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=D:/1softwares/VS2022
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/RTXPT
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=31
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Test COMPILER_HAS_DEPRECATED
COMPILER_HAS_DEPRECATED:INTERNAL=1
//Test COMPILER_HAS_DEPRECATED_ATTR
COMPILER_HAS_DEPRECATED_ATTR:INTERNAL=
//ADVANCED property for variable: COVERAGE_COMMAND
COVERAGE_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: COVERAGE_EXTRA_FLAGS
COVERAGE_EXTRA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_7Z
CPACK_BINARY_7Z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_IFW
CPACK_BINARY_IFW-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_INNOSETUP
CPACK_BINARY_INNOSETUP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_NSIS
CPACK_BINARY_NSIS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_NUGET
CPACK_BINARY_NUGET-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_WIX
CPACK_BINARY_WIX-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_ZIP
CPACK_BINARY_ZIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_7Z
CPACK_SOURCE_7Z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_ZIP
CPACK_SOURCE_ZIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_COUNT
CTEST_SUBMIT_RETRY_COUNT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_DELAY
CTEST_SUBMIT_RETRY_DELAY-ADVANCED:INTERNAL=1
CXXOPTS__VERSION_MAJOR:INTERNAL=3
CXXOPTS__VERSION_MINOR:INTERNAL=2
CXXOPTS__VERSION_PATCH:INTERNAL=1
//ADVANCED property for variable: DART_TESTING_TIMEOUT
DART_TESTING_TIMEOUT-ADVANCED:INTERNAL=1
//Details about finding NVAPI
FIND_PACKAGE_MESSAGE_DETAILS_NVAPI:INTERNAL=[D:/RTXPT/External/nvapi/][D:/RTXPT/External/nvapi/amd64/nvapi64.lib][v()]
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][TRUE][ ][v2.0()]
//Details about finding OpenMP_C
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C:INTERNAL=[-openmp][v2.0()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-openmp][v2.0()]
//Details about finding STREAMLINE
FIND_PACKAGE_MESSAGE_DETAILS_STREAMLINE:INTERNAL=[D:/RTXPT/External/Streamline][v()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: GITCOMMAND
GITCOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//GLFW pkg-config Libs.private
GLFW_PKG_CONFIG_LIBS_PRIVATE:INTERNAL= -lgdi32
//GLFW pkg-config Requires.private
GLFW_PKG_CONFIG_REQUIRES_PRIVATE:INTERNAL=
//Have include clocale
HAVE_CLOCALE:INTERNAL=1
//Test HAVE_DECIMAL_POINT
HAVE_DECIMAL_POINT:INTERNAL=1
//Result of TRY_COMPILE
HAVE_LCONV_SIZE:INTERNAL=TRUE
//Have symbol localeconv
HAVE_LOCALECONV:INTERNAL=1
//Have include stddef.h
HAVE_STDDEF_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//CHECK_TYPE_SIZE: sizeof(lconv)
LCONV_SIZE:INTERNAL=152
//ADVANCED property for variable: MAKECOMMAND
MAKECOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_COMMAND
MEMORYCHECK_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_SUPPRESSIONS_FILE
MEMORYCHECK_SUPPRESSIONS_FILE-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_openmp:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_openmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=200203
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//C compiler's OpenMP specification date
OpenMP_C_SPEC_DATE:INTERNAL=200203
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_SPECTEST_C_:INTERNAL=TRUE
//ADVANCED property for variable: SITE
SITE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: XXHASH_BUNDLED_MODE
XXHASH_BUNDLED_MODE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=D:/RTXPT/cmake-build-release-visual-studio/install

