d�铖Gh�4 p      .drectve        j  許               
 .debug$S        `O >U  灓        @ B.debug$T        l   瞍             @ B.rdata          @   Z�             @ @@.text$mn        :   殽 豫         P`.debug$S          颔         @B.text$mn        q   姩              P`.debug$S        �   铽        @B.text$mn        �  � 甬     	    P`.debug$S        <
  J� 喖     T   @B.text$x         =   慰 �         P`.text$mn        d   )� 嵗         P`.debug$S        h  椑 ��        @B.text$mn        s   悄 :�         P`.debug$S          N� f�     
   @B.text$mn            是              P`.debug$S        8  昵 "�        @B.text$mn           滤              P`.debug$S          退 萏        @B.text$mn        �  �          P`.debug$S        P  K� 涙     �   @B.text$x            秒 想         P`.text$x            匐 殡         P`.text$x            箅 �         P`.text$x            
� �         P`.text$x            '� 7�         P`.text$x            A� Q�         P`.text$x            [� k�         P`.text$x            u� 呾         P`.text$x            忟 熿         P`.text$x            ╈ 轨         P`.text$x            渺 屿         P`.text$x            蒽 痨         P`.text$x             
�         P`.text$x            � $�         P`.text$x            .� >�         P`.text$x            H� X�         P`.text$x            b� r�         P`.text$x            |� 岉         P`.text$x            栱          P`.text$x            绊 理         P`.text$mn        n  薯 8�         P`.debug$S        �  ~� R�     (   @B.text$x            怡 蝙         P`.text$x             �         P`.text$x            � ,�         P`.text$mn        �   6�              P`.debug$S        �    柠        @B.text$mn           禧              P`.debug$S        �    迭        @B.text$mn        <   蔹 �         P`.debug$S        0  7� g�     
   @B.text$mn        <   塔 �         P`.debug$S        L  %� q      
   @B.text$mn        !   �  �          P`.debug$S        <  
 F        @B.text$mn        2   � �         P`.debug$S        <  �         @B.text$mn        "   |              P`.debug$S        �  � 6        @B.text$mn        "   �              P`.debug$S        �  � �        @B.text$mn        "   $	              P`.debug$S        �  F	 �
        @B.text$mn        "   z              P`.debug$S        �  � (
        @B.text$mn        "   �
              P`.debug$S        �  �
 �        @B.text$mn        "   *              P`.debug$S        �  L �        @B.text$mn        "   �              P`.debug$S        �  � .        @B.text$mn        "   �              P`.debug$S        �  � |        @B.text$mn        "                 P`.debug$S        �  > �        @B.text$mn        e   j �         P`.debug$S        �  � �        @B.text$mn        [   a �         P`.debug$S          � �#        @B.text$mn           �$ �$         P`.debug$S          �$ �%        @B.text$mn           & &         P`.debug$S        $  && J'        @B.text$mn        }   �' (         P`.debug$S        �  ( �,        @B.text$mn        K   �-              P`.debug$S        �  �- �/        @B.text$mn        K   .0              P`.debug$S        �  y0 92        @B.text$mn        K   �2              P`.debug$S        �  3 �4        @B.text$mn        K   l5              P`.debug$S        �  �5 �7        @B.text$mn        .   8 E8         P`.debug$S        �  Y8 �9        @B.text$mn        z   : �:         P`.debug$S        �  �: �=        @B.text$mn        j   c> �>         P`.debug$S        p  �> [B        @B.text$mn        �   7C 鍯         P`.debug$S        �  鶦 綠     *   @B.text$mn           bI              P`.debug$S        �   eI EJ        @B.text$mn          丣  L         P`.debug$S        P  2L 俆     Z   @B.text$mn        �   X 淴         P`.debug$S          蝀 襔        @B.text$mn           ^[              P`.debug$S        �   a[ A\        @B.text$mn           }\ 怽         P`.debug$S        �    圿        @B.text$mn           癩 胅         P`.debug$S        �   譣 穅        @B.text$mn        +   骬 _         P`.debug$S        �   2_ `        @B.text$mn        +   V` 乣         P`.debug$S        �   昤 塧        @B.text$mn        !   臿 鎍         P`.debug$S        �   餫 蘠        @B.text$mn        �   c 纁         P`.debug$S        �  點 鴈        @B.text$mn        B   pf 瞗         P`.debug$S           衒 術        @B.text$mn        B   h Nh         P`.debug$S          lh |i        @B.text$mn        B   竔 鷌         P`.debug$S        �   j k        @B.text$mn        H   Pk              P`.debug$S        �  榢 \m        @B.text$mn           tn              P`.debug$S        0  wn         @B.text$mn        1  鱫 (s         P`.debug$S        �  緎 Jx        @B.text$mn           vy              P`.debug$S        �  yy {        @B.text$mn           晎              P`.debug$S        �   榹 ||        @B.text$mn           竱 苵         P`.debug$S        $  衸 魙        @B.text$mn        �   0~ 羱         P`.debug$S        �   麁        @B.text$mn        �  s� B�         P`.debug$S        �	  j� &�     4   @B.text$x            .� :�         P`.text$x            D� P�         P`.text$mn        (   Z� 倧         P`.debug$S        �  審 4�        @B.text$mn        (   瑧 詰         P`.debug$S        �  迲 挀        @B.text$mn          
� #�     	    P`.debug$S          }� 櫌     H   @B.text$x            i� u�         P`.text$x            � 嫢         P`.text$x             暐 单         P`.text$mn        Q  骏 �     
    P`.debug$S        �  t� �        @B.text$mn           酞              P`.debug$S        `  溪 /�     
   @B.text$mn        �  摤 u�         P`.debug$S        t  女 9�        @B.text$mn        �   � 毑         P`.debug$S        �  瓴 捍        @B.text$mn           F� ]�         P`.debug$S          q� 壎        @B.text$mn        �  哦 惛         P`.debug$S        �  指      (   @B.text$x            6� B�         P`.text$x            L� X�         P`.text$x            b� �         P`.text$mn           壛              P`.debug$S        \  屃 杪     
   @B.text$mn           L�              P`.debug$S        d  O� 衬     
   @B.text$mn           �              P`.debug$S        \  � v�     
   @B.text$mn        0   谄 
�         P`.debug$S        �  � 馊        @B.text$mn           偵              P`.debug$S        �  吷 ]�        @B.text$mn        R   樗 ;�         P`.debug$S        X  E� 澪        @B.text$mn           y�              P`.debug$S        �  |� �        @B.text$mn        y   屟 �         P`.debug$S        �  � 咴        @B.text$mn           阏              P`.debug$S        T  嬲 :�     
   @B.text$mn        M   炞 胱         P`.debug$S        �  踝 呝        @B.text$mn                         P`.debug$S        d   � d�     
   @B.text$mn        9   熔 �         P`.debug$S        �  �         @B.text$mn        	   #�              P`.debug$S        �   ,� �        @B.text$mn           X�              P`.debug$S           [� {�        @B.text$mn        �   肃 傖         P`.debug$S        T  犪 翥        @B.text$mn           ㄤ              P`.debug$S        �    忓        @B.text$mn           隋              P`.debug$S        �   五 舵        @B.text$mn           蜴              P`.debug$S          � �        @B.text$mn           X�              P`.debug$S          j� z�        @B.text$mn        
   书              P`.debug$S        @  组 �     
   @B.text$mn        K   {�              P`.debug$S        �  齐 卷        @B.text$mn           J�              P`.debug$S        ,  M� y�        @B.text$mn            娠 轱         P`.debug$S        �   � 损        @B.text$mn           � �         P`.debug$S        $  ,� P�        @B.text$mn        A   岒 万         P`.debug$S        �  狎 ヵ        @B.text$mn            荐         P`.debug$S        �   砌 汍        @B.xdata             主             @0@.pdata             犄 鲼        @0@.xdata             �             @0@.pdata             � (�        @0@.xdata             F�             @0@.pdata             R� ^�        @0@.xdata             |�             @0@.pdata             匄 慀        @0@.xdata                          @0@.pdata             壶 气        @0@.xdata             澍             @0@.pdata             禅         @0@.xdata             �             @0@.pdata             "� .�        @0@.xdata             L�             @0@.pdata             T� `�        @0@.xdata             ~� 廀        @0@.pdata             Ⅸ         @0@.xdata          	   贴 争        @@.xdata             轾 稆        @@.xdata                          @@.xdata              �        @0@.pdata              � ,�        @0@.xdata          	   J� S�        @@.xdata             g� m�        @@.xdata             w�             @@.xdata             z� 婜        @0@.pdata             烔         @0@.xdata          	   鳃 漾        @@.xdata             妃 膪        @@.xdata             斛             @@.xdata              �        @0@.pdata              � ,�        @0@.xdata          	   J� S�        @@.xdata             g� m�        @@.xdata             w�             @@.xdata             }�             @0@.pdata             咞 扄        @0@.xdata              葵        @0@.pdata             欲 啕        @0@.xdata          	    �        @@.xdata             �  �        @@.xdata             *�             @@.xdata             -� =�        @0@.pdata             Q� ]�        @0@.xdata          	   {� 匋        @@.xdata             橖 烖        @@.xdata                          @@.xdata              稽        @0@.pdata             宵 埸        @0@.xdata          	    �        @@.xdata             � �        @@.xdata             &�             @@.xdata             )� 9�        @0@.pdata             M� Y�        @0@.xdata          	   w� ��        @@.xdata             旪 汖        @@.xdata                          @@.xdata              俘        @0@.pdata             她         @0@.xdata          	   觚         @@.xdata             � �        @@.xdata             "�             @@.xdata             %� 5�        @0@.pdata             I� U�        @0@.xdata          	   s� |�        @@.xdata             慆 桛        @@.xdata             狛             @@.xdata             ￣ 傀        @0@.pdata             誉 唼        @0@.xdata          	    �        @@.xdata             �  �        @@.xdata             *�             @@.xdata             -�             @0@.pdata             5� A�        @0@.xdata             _�             @0@.pdata             g� s�        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata          	     "         @@.xdata          l   6  �         @@.xdata          	   t             @@.xdata             } �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata                          @0@.pdata                      @0@.xdata             5 I        @0@.pdata             g s        @0@.xdata             � �        @0@.pdata             � �        @0@.voltbl            �               .xdata             �         @0@.pdata             
         @0@.xdata             7 K        @0@.pdata             i u        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             �         @0@.pdata             ! -        @0@.xdata             K             @0@.pdata             S _        @0@.xdata             }             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata              '        @0@.xdata             E Y        @0@.pdata             w �        @0@.xdata             � �        @0@.pdata             � �        @0@.voltbl            �               .xdata             �         @0@.pdata             + 7        @0@.xdata          	   U ^        @@.xdata             r �        @@.xdata             �             @@.voltbl            �                .xdata             � �        @0@.pdata             � �        @0@.xdata                      @@.xdata                          @@.voltbl                           .xdata              .        @0@.pdata             B N        @0@.xdata          	   l u        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.voltbl            �                .xdata              �         @0@.pdata             1 =        @0@.xdata          	   [ d        @@.xdata          
   x �        @@.xdata          
   �             @@.voltbl            �                .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             	             @0@.pdata             %	 1	        @0@.xdata             O	             @0@.pdata             [	 g	        @0@.xdata             �	             @0@.pdata             �	 �	        @0@.xdata             �	             @0@.pdata             �	 �	        @0@.xdata             �	             @0@.pdata             
 
        @0@.xdata             /
 G
        @0@.pdata             Q
 ]
        @0@.xdata              {
 �
        @0@.pdata             �
 �
        @0@.xdata          0   �
         @0@.pdata             1 =        @0@.xdata             [ k        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata              #        @0@.pdata             A M        @0@.xdata             k             @0@.pdata             w �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � 
        @0@.pdata             
 +
        @0@.xdata             I
 Y
        @0@.pdata             w
 �
        @0@.xdata             �
             @0@.pdata             �
 �
        @0@.xdata             �
             @0@.pdata             �
 �
        @0@.xdata             	             @0@.pdata                      @0@.xdata              ; [        @0@.pdata             o {        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             �         @0@.voltbl            $                .xdata             2 N        @0@.pdata             b n        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �         @0@.pdata             " .        @0@.xdata             L \        @0@.pdata             z �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata              "        @0@.pdata             @ L        @0@.xdata             j z        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �         @0@.pdata             , 8        @0@.xdata             V f        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                       @0@.xdata             > N        @0@.pdata             l x        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �         @0@.pdata             , 8        @0@.xdata          
   V c        @@.xdata             �             @@.xdata             � �        @@.xdata             � �        @@.xdata          	   �             @@.xdata             �             @0@.pdata             � �        @0@.voltbl            �                .voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata             # /        @0@.rdata             M e        @@@.rdata             �             @@@.rdata             � �        @@@.rdata             � �        @@@.rdata                          @@@.xdata$x            2        @@@.xdata$x           F b        @@@.data$r         /   � �        @@�.xdata$x        $   � �        @@@.data$r         $   �         @@�.xdata$x        $    C        @@@.data$r         $   W {        @@�.xdata$x        $   � �        @@@.data               �             @ @�.rdata          �   � ]        @@@.rdata          �   � �        @@@.rdata             /             @0@.rdata             1             @@@.rdata          (   A i        @@@.rdata          (   � �        @@@.rdata$r        $   �         @@@.rdata$r           7 K        @@@.rdata$r           U a        @@@.rdata$r        $   k �        @@@.rdata$r        $   � �        @@@.rdata$r           � �        @@@.rdata$r                    @@@.rdata$r        $   + O        @@@.rdata$r        $   c �        @@@.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   � !        @@@.data$rs        *   5 _        @@�.rdata$r           i }        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $   � �        @@@.data$rs        ,    C        @@�.rdata$r           M a        @@@.rdata$r           k w        @@@.rdata$r        $   � �        @@@.rdata$r        $   � �        @@@.data$rs        /   � *        @@�.rdata$r           4 H        @@@.rdata$r           R f        @@@.rdata$r        $   z �        @@@.rdata$r        $   � �        @@@.data$rs        H   � <         @P�.rdata$r           F  Z         @@@.rdata$r           d  x         @@@.rdata$r        $   �  �         @@@.rdata$r        $   �  �         @@@.data$rs        >   ! D!        @@�.rdata$r           N! b!        @@@.rdata$r           l! �!        @@@.rdata$r        $   �! �!        @@@.rdata             �!             @0@.rdata             �!             @0@.rdata             �!             @0@.debug$S        X   �! 0"        @B.debug$S        L   D" �"        @B.debug$S        @   �" �"        @B.debug$S        4   �" ,#        @B.debug$S        4   @# t#        @B.debug$S        @   �# �#        @B.debug$S        @   �# $        @B.chks64         �  0$              
     /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   7  i     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_app.dir\Release\imgui_renderer.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $app  $vfs 	 $status  $math 	 $colors  $log 	 $stdext  $ImGui  �   *  $ 鱲   TP_CALLBACK_PRIORITY_NORMAL % 鱲   TP_CALLBACK_PRIORITY_INVALID 3 @�    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED - @�   D3D12_MESSAGE_CATEGORY_MISCELLANEOUS . @�   D3D12_MESSAGE_CATEGORY_INITIALIZATION ' @�   D3D12_MESSAGE_CATEGORY_CLEANUP + @�   D3D12_MESSAGE_CATEGORY_COMPILATION . @�   D3D12_MESSAGE_CATEGORY_STATE_CREATION - @�   D3D12_MESSAGE_CATEGORY_STATE_SETTING - @�   D3D12_MESSAGE_CATEGORY_STATE_GETTING 5 @�   D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION ) @�  	 D3D12_MESSAGE_CATEGORY_EXECUTION * c�    D3D12_MESSAGE_SEVERITY_CORRUPTION % c�   D3D12_MESSAGE_SEVERITY_ERROR ' c�   D3D12_MESSAGE_SEVERITY_WARNING $ c�   D3D12_MESSAGE_SEVERITY_INFO 5 =    std::filesystem::_File_time_clock::is_steady 6 =   std::_Iterator_base0::_Unwrap_when_unverified � e   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IFileSystem> > >::_Minimum_asan_allocation_alignment 7 =   std::_Iterator_base12::_Unwrap_when_unverified   �   D   3   nvrhi::c_HeaderVersion " 3   nvrhi::c_MaxRenderTargets  3   nvrhi::c_MaxViewports % 3   nvrhi::c_MaxVertexAttributes # 3   nvrhi::c_MaxBindingLayouts & 3  � nvrhi::c_MaxBindingsPerLayout 5 3   nvrhi::c_MaxVolatileConstantBuffersPerLayout , 3    nvrhi::c_MaxVolatileConstantBuffers % 3  � nvrhi::c_MaxPushConstantSize 3 3   nvrhi::c_ConstantBufferOffsetSizeAlignment  6  ��I@donut::math::PI_f " 2  
�-DT�!	@donut::math::PI_d ! 6  ��7�5donut::math::epsilon " 6  �  �donut::math::infinity  6  �  �donut::math::NaN �   �   / U�    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION - U�   D3D12_RESOURCE_BARRIER_TYPE_ALIASING '=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable � e   std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment � =   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified W =   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified  =    std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi #=   std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard : T�  ED3D12_MESSAGE_ID_ALPHA_BLEND_FACTOR_NOT_SUPPORTED R =   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ' 墔  �   CLSCTX_ACTIVATE_X86_SERVER 7 3  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 3  �����nvrhi::TextureSubresourceSet::AllArraySlices $ g   std::_Locbase<int>::collate " g   std::_Locbase<int>::ctype % g   std::_Locbase<int>::monetary $ g   std::_Locbase<int>::numeric ! g   std::_Locbase<int>::time % g    std::_Locbase<int>::messages   g  ? std::_Locbase<int>::all � e   std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment ! g    std::_Locbase<int>::none # �        nvrhi::AllSubresources + 踲   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 踲   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - 踲   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 踲   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable D e   ��std::basic_string_view<char,std::char_traits<char> >::npos * 遶   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 遶   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 遶   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A 遶   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP ! 噮    COINITBASE_MULTITHREADED J e   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos ) 3   donut::math::vector<bool,2>::DIM V e   std::allocator<donut::app::IRenderPass *>::_Minimum_asan_allocation_alignment   �        nvrhi::EntireBuffer ) 3   donut::math::vector<bool,3>::DIM ) 3   donut::math::vector<bool,4>::DIM 3e   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size 3e   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets  �/   std::_Consume_header -=    std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi  �/   std::_Generate_header L e   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos o e   std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >::_Minimum_asan_allocation_alignment  媴   VT_I2  媴   VT_I4  媴   VT_BSTR  媴  	 VT_DISPATCH  媴  
 VT_ERROR  媴   VT_VARIANT  媴  
 VT_UNKNOWN 3 �   DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED  媴   VT_I1  媴   VT_I8  媴  $ VT_RECORD  媴  � �VT_RESERVED L e   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos  (�    TYSPEC_CLSID  (�   TYSPEC_FILEEXT  (�   TYSPEC_MIMETYPE  (�   TYSPEC_FILENAME  (�   TYSPEC_PROGID  (�   TYSPEC_PACKAGENAME . =   std::integral_constant<bool,1>::value � e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment / 駞    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV + 駞   D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER ' 駞   D3D12_DESCRIPTOR_HEAP_TYPE_RTV ' 駞   D3D12_DESCRIPTOR_HEAP_TYPE_DSV ( 殕    D3D12_DESCRIPTOR_RANGE_TYPE_SRV ( 殕   D3D12_DESCRIPTOR_RANGE_TYPE_UAV ( 殕   D3D12_DESCRIPTOR_RANGE_TYPE_CBV 3 }�    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE 2 }�   D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS & }�   D3D12_ROOT_PARAMETER_TYPE_CBV & }�   D3D12_ROOT_PARAMETER_TYPE_SRV 4 4�    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK / 4�   D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK e e   std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >::_Minimum_asan_allocation_alignment G e   std::allocator<ImDrawVert>::_Minimum_asan_allocation_alignment , 搮   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL % e   std::ctype<char>::table_size _ e   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment * 髤    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW 2 髤   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED . 髤   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH 8 髤   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW 7 髤   D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW . 髤   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT : 髤   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW : 髤   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW ; 髤   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW  I�   PowerUserMaximum 3 髤  	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS  ]�    NODE_INVALID  ]�   NODE_ELEMENT  ]�   NODE_ATTRIBUTE  ]�   NODE_TEXT  ]�   NODE_CDATA_SECTION  ]�   NODE_ENTITY_REFERENCE  ]�   NODE_ENTITY $ ]�   NODE_PROCESSING_INSTRUCTION  ]�   NODE_COMMENT  ]�  	 NODE_DOCUMENT  ]�  
 NODE_DOCUMENT_TYPE  ]�   NODE_DOCUMENT_FRAGMENT  y�    XMLELEMTYPE_ELEMENT  y�   XMLELEMTYPE_TEXT  y�   XMLELEMTYPE_COMMENT  y�   XMLELEMTYPE_DOCUMENT  y�   XMLELEMTYPE_DTD  y�   XMLELEMTYPE_PI K e   std::allocator<unsigned short>::_Minimum_asan_allocation_alignment c e   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment A e   std::allocator<char>::_Minimum_asan_allocation_alignment   �   i3  a e   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ? e   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A e   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L e   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * 3   donut::math::vector<float,3>::DIM X e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE   g   std::_Iosb<int>::skipws Z e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask ! g   std::_Iosb<int>::unitbuf e e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity # g   std::_Iosb<int>::uppercase e e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size " g   std::_Iosb<int>::showbase # g   std::_Iosb<int>::showpoint ! g    std::_Iosb<int>::showpos ^ =   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val  g  @ std::_Iosb<int>::left  g  � std::_Iosb<int>::right " g   std::_Iosb<int>::internal  g   std::_Iosb<int>::dec a e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset  g   std::_Iosb<int>::oct  g   std::_Iosb<int>::hex _ e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size $ g   std::_Iosb<int>::scientific  g    std::_Iosb<int>::fixed " g   0std::_Iosb<int>::hexfloat # g   @std::_Iosb<int>::boolalpha " g  � �std::_Iosb<int>::_Stdio % g  �std::_Iosb<int>::adjustfield # g   std::_Iosb<int>::basefield $ g   0std::_Iosb<int>::floatfield ! g    std::_Iosb<int>::goodbit   g   std::_Iosb<int>::eofbit ! g   std::_Iosb<int>::failbit   g   std::_Iosb<int>::badbit  g   std::_Iosb<int>::in  g   std::_Iosb<int>::out  g   std::_Iosb<int>::ate  g   std::_Iosb<int>::app  g   std::_Iosb<int>::trunc # g  @ std::_Iosb<int>::_Nocreate $ g  � std::_Iosb<int>::_Noreplace   g    std::_Iosb<int>::binary  g    std::_Iosb<int>::beg  g   std::_Iosb<int>::cur  g   std::_Iosb<int>::end , g  @ std::_Iosb<int>::_Default_open_prot T e   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos : e    std::integral_constant<unsigned __int64,0>::value . g   donut::math::box<float,3>::numCorners ; 6  ���donut::app::StreamlineInterface::kInvalidFloat * 3   donut::math::vector<float,4>::DIM : 3  �����donut::app::StreamlineInterface::kInvalidUint * �        donut::math::lumaCoefficients ) GV    std::_Invoker_functor::_Strategy , GV   std::_Invoker_pmf_object::_Strategy - GV   std::_Invoker_pmf_refwrap::_Strategy + J        nvrhi::rt::c_IdentityTransform * 3   donut::math::vector<float,2>::DIM - GV   std::_Invoker_pmf_pointer::_Strategy , GV   std::_Invoker_pmd_object::_Strategy ( 3   donut::math::vector<int,2>::DIM - GV   std::_Invoker_pmd_refwrap::_Strategy - GV   std::_Invoker_pmd_pointer::_Strategy - =    std::chrono::system_clock::is_steady $    std::ratio<1,10000000>::num  0�    DVEXTENT_CONTENT (   ��枠 std::ratio<1,10000000>::den D e   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment # W�   BINDSTATUS_FINDINGRESOURCE  W�   BINDSTATUS_CONNECTING  W�   BINDSTATUS_REDIRECTING % W�   BINDSTATUS_BEGINDOWNLOADDATA # W�   BINDSTATUS_DOWNLOADINGDATA # W�   BINDSTATUS_ENDDOWNLOADDATA + W�   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( W�   BINDSTATUS_INSTALLINGCOMPONENTS ) W�  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # W�  
 BINDSTATUS_USINGCACHEDCOPY " W�   BINDSTATUS_SENDINGREQUEST $ W�   BINDSTATUS_CLASSIDAVAILABLE % W�  
 BINDSTATUS_MIMETYPEAVAILABLE * W�   BINDSTATUS_CACHEFILENAMEAVAILABLE & W�   BINDSTATUS_BEGINSYNCOPERATION $ W�   BINDSTATUS_ENDSYNCOPERATION # W�   BINDSTATUS_BEGINUPLOADDATA ! W�   BINDSTATUS_UPLOADINGDATA ! W�   BINDSTATUS_ENDUPLOADDATA # W�   BINDSTATUS_PROTOCOLCLASSID  W�   BINDSTATUS_ENCODING - W�   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( W�   BINDSTATUS_CLASSINSTALLLOCATION  W�   BINDSTATUS_DECODING & W�   BINDSTATUS_LOADINGMIMEHANDLER , W�   BINDSTATUS_CONTENTDISPOSITIONATTACH     std::ratio<1,1>::num ( W�   BINDSTATUS_FILTERREPORTMIMETYPE ' W�   BINDSTATUS_CLSIDCANINSTANTIATE     std::ratio<1,1>::den % W�   BINDSTATUS_IUNKNOWNAVAILABLE  W�   BINDSTATUS_DIRECTBIND  W�   BINDSTATUS_RAWMIMETYPE " W�    BINDSTATUS_PROXYDETECTING   W�  ! BINDSTATUS_ACCEPTRANGES  W�  " BINDSTATUS_COOKIE_SENT + W�  # BINDSTATUS_COMPACT_POLICY_RECEIVED % W�  $ BINDSTATUS_COOKIE_SUPPRESSED ( W�  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' W�  & BINDSTATUS_COOKIE_STATE_ACCEPT ' W�  ' BINDSTATUS_COOKIE_STATE_REJECT ' W�  ( BINDSTATUS_COOKIE_STATE_PROMPT & W�  ) BINDSTATUS_COOKIE_STATE_LEASH * W�  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  W�  + BINDSTATUS_POLICY_HREF  W�  , BINDSTATUS_P3P_HEADER + W�  - BINDSTATUS_SESSION_COOKIE_RECEIVED . W�  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + W�  / BINDSTATUS_SESSION_COOKIES_ALLOWED   W�  0 BINDSTATUS_CACHECONTROL . W�  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) W�  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & W�  3 BINDSTATUS_PUBLISHERAVAILABLE ( W�  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ W�  5 BINDSTATUS_SSLUX_NAVBLOCKED , W�  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , W�  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " W�  8 BINDSTATUS_64BIT_PROGRESS  W�  8 BINDSTATUS_LAST  W�  9 BINDSTATUS_RESERVED_0  W�  : BINDSTATUS_RESERVED_1  W�  ; BINDSTATUS_RESERVED_2  W�  < BINDSTATUS_RESERVED_3  W�  = BINDSTATUS_RESERVED_4  W�  > BINDSTATUS_RESERVED_5  W�  ? BINDSTATUS_RESERVED_6  W�  @ BINDSTATUS_RESERVED_7  W�  A BINDSTATUS_RESERVED_8  W�  B BINDSTATUS_RESERVED_9  W�  C BINDSTATUS_RESERVED_A  W�  D BINDSTATUS_RESERVED_B  W�  E BINDSTATUS_RESERVED_C  W�  F BINDSTATUS_RESERVED_D  W�  G BINDSTATUS_RESERVED_E  W�  H BINDSTATUS_RESERVED_F  W�  I BINDSTATUS_RESERVED_10  W�  J BINDSTATUS_RESERVED_11  W�  K BINDSTATUS_RESERVED_12  W�  L BINDSTATUS_RESERVED_13  W�  M BINDSTATUS_RESERVED_14 B e   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) 3   donut::math::frustum::numCorners D e   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O e   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask $ 軈    D3D12_LIFETIME_STATE_IN_USE n e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n e  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size T e   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment g =   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j e    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset J    std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 h e    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size N   ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 (   ��枠 std::ratio<10000000,1>::num $    std::ratio<10000000,1>::den P   ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy <   ��枠 std::integral_constant<__int64,10000000>::value 1    std::integral_constant<__int64,1>::value ] e   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos - =   std::chrono::steady_clock::is_steady &    std::ratio<1,1000000000>::num $ @t   ImGuiWindowFlags_NoTitleBar *   � 蕷;std::ratio<1,1000000000>::den " @t   ImGuiWindowFlags_NoResize % @t   ImGuiWindowFlags_NoScrollbar $ @t    ImGuiWindowFlags_NoCollapse ' @t   ImGuiWindowFlags_NoMouseInputs ) @t  �   ImGuiWindowFlags_NoNavInputs ( @t  �   ImGuiWindowFlags_NoNavFocus  憛    CIP_DISK_FULL  憛   CIP_ACCESS_DENIED ! 憛   CIP_NEWER_VERSION_EXISTS ! 憛   CIP_OLDER_VERSION_EXISTS & @t  �  ImGuiWindowFlags_NoInputs  憛   CIP_NAME_CONFLICT 1 憛   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + 憛   CIP_EXE_SELF_REGISTERATION_TIMEOUT  憛   CIP_UNSAFE_TO_ABORT  憛   CIP_NEED_REBOOT # Yt  � ImGuiChildFlags_FrameStyle " 蕝    Uri_PROPERTY_ABSOLUTE_URI  蕝   Uri_PROPERTY_USER_NAME " 蛃   ImGuiTreeNodeFlags_Framed ( 蛃   ImGuiTreeNodeFlags_AllowOverlap  蕝   Uri_PROPERTY_HOST_TYPE , 蛃   ImGuiTreeNodeFlags_NoTreePushOnOpen + 蛃   ImGuiTreeNodeFlags_NoAutoOpenOnLog  蕝   Uri_PROPERTY_ZONE ? <�   D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG1  :�    Uri_HOST_UNKNOWN  :�   Uri_HOST_DNS  :�   Uri_HOST_IPV4  :�   Uri_HOST_IPV6 # t   ImGuiPopupFlags_AnyPopupId & t   ImGuiPopupFlags_AnyPopupLevel * Lt   ImGuiSelectableFlags_AllowOverlap $ <t   ImGuiComboFlags_HeightSmall & <t   ImGuiComboFlags_HeightRegular $ <t   ImGuiComboFlags_HeightLarge & <t   ImGuiComboFlags_HeightLargest 1 [t  @ ImGuiTabBarFlags_FittingPolicyResizeDown - [t  � ImGuiTabBarFlags_FittingPolicyScroll ' Jt   ImGuiFocusedFlags_ChildWindows % Jt   ImGuiFocusedFlags_RootWindow ' Dt   ImGuiHoveredFlags_ChildWindows % Dt   ImGuiHoveredFlags_RootWindow 2 Dt    ImGuiHoveredFlags_AllowWhenBlockedByPopup 7 Dt  � ImGuiHoveredFlags_AllowWhenBlockedByActiveItem 4 Dt   ImGuiHoveredFlags_AllowWhenOverlappedByItem 6 Dt   ImGuiHoveredFlags_AllowWhenOverlappedByWindow . Dt   ImGuiHoveredFlags_AllowWhenOverlapped 0 Nt   ImGuiDragDropFlags_AcceptBeforeDelivery 3 Nt   ImGuiDragDropFlags_AcceptNoDrawDefaultRect     std::ratio<3600,1>::num      std::ratio<3600,1>::den : g   std::_Floating_type_traits<float>::_Mantissa_bits : g   std::_Floating_type_traits<float>::_Exponent_bits D g   std::_Floating_type_traits<float>::_Maximum_binary_exponent E g   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : g   std::_Floating_type_traits<float>::_Exponent_bias 7 g   std::_Floating_type_traits<float>::_Sign_shift  f   ImGuiKey_Tab  f  ImGuiKey_LeftArrow  f  ImGuiKey_RightArrow ; g   std::_Floating_type_traits<float>::_Exponent_shift  f  ImGuiKey_UpArrow  f  ImGuiKey_DownArrow  f  ImGuiKey_PageUp : 3  � std::_Floating_type_traits<float>::_Exponent_mask  f  ImGuiKey_PageDown  f  ImGuiKey_Home E 3  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask  f  ImGuiKey_End G 3  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask  f  
ImGuiKey_Delete  f  ImGuiKey_Backspace J 3  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask  f  
ImGuiKey_Enter B 3  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask  f  ImGuiKey_Escape F 3  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; g  5 std::_Floating_type_traits<double>::_Mantissa_bits ; g   std::_Floating_type_traits<double>::_Exponent_bits E g  �std::_Floating_type_traits<double>::_Maximum_binary_exponent  f  "ImGuiKey_A G g  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent  f  $ImGuiKey_C ; g  �std::_Floating_type_traits<double>::_Exponent_bias 8 g  ? std::_Floating_type_traits<double>::_Sign_shift < g  4 std::_Floating_type_traits<double>::_Exponent_shift ; e  �std::_Floating_type_traits<double>::_Exponent_mask J e  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L e  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O e  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G e  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask  f  7ImGuiKey_V K e  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask  f  9ImGuiKey_X  f  :ImGuiKey_Y  f  ;ImGuiKey_Z . =    std::integral_constant<bool,0>::value  f  �ImGuiKey_COUNT  f   ImGuiMod_Ctrl  f    ImGuiMod_Shift  f   @ImGuiMod_Alt  f  � �ImGuiMod_Super   f   ImGuiKey_NamedKey_BEGIN  f  �ImGuiKey_NamedKey_END  f  �ImGuiKey_KeysData_SIZE  Ht   ImGuiNavInput_COUNT 4 e  @ _Mtx_internal_imp_t::_Critical_section_size    �   *  5 e   _Mtx_internal_imp_t::_Critical_section_align + =    std::_Aligned_storage<64,8>::_Fits * =    std::_Aligned<64,8,char,0>::_Fits + =    std::_Aligned<64,8,short,0>::_Fits ) =   std::_Aligned<64,8,int,0>::_Fits  Pt  5 ImGuiCol_COUNT ' 莅   ImGuiStyleVar_WindowBorderSize   �   �   ) Wt   ImGuiButtonFlags_MouseButtonLeft * Wt   ImGuiButtonFlags_MouseButtonRight + Wt   ImGuiButtonFlags_MouseButtonMiddle �   鑵  + :t  �   ImGuiColorEditFlags_DisplayRGB + :t  �    ImGuiColorEditFlags_DisplayHSV + :t  �  @ ImGuiColorEditFlags_DisplayHex & :t  �  � ImGuiColorEditFlags_Uint8 & :t  �   ImGuiColorEditFlags_Float - :t  �   ImGuiColorEditFlags_PickerHueBar / :t  �   ImGuiColorEditFlags_PickerHueWheel ) :t  �   ImGuiColorEditFlags_InputRGB ) :t  �   ImGuiColorEditFlags_InputHSV 1     D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES  甙   ImGuiCond_Always & t  � ImGuiTableFlags_BordersInnerH & t   ImGuiTableFlags_BordersOuterH & t   ImGuiTableFlags_BordersInnerV & t   ImGuiTableFlags_BordersOuterV % t  �ImGuiTableFlags_BordersInner % t   ImGuiTableFlags_BordersOuter ' t    ImGuiTableFlags_SizingFixedFit ( t   @ImGuiTableFlags_SizingFixedSame E e   std::allocator<char16_t>::_Minimum_asan_allocation_alignment * t   `ImGuiTableFlags_SizingStretchProp , t  � �ImGuiTableFlags_SizingStretchSame  L�   BINDSTRING_HEADERS   L�   BINDSTRING_ACCEPT_MIMES  L�   BINDSTRING_EXTRA_URL  L�   BINDSTRING_LANGUAGE  L�   BINDSTRING_USERNAME  L�   BINDSTRING_PASSWORD  L�   BINDSTRING_UA_PIXELS + >t   ImGuiTableColumnFlags_WidthStretch  L�   BINDSTRING_UA_COLOR ) >t   ImGuiTableColumnFlags_WidthFixed  L�  	 BINDSTRING_OS  L�  
 BINDSTRING_USER_AGENT $ L�   BINDSTRING_ACCEPT_ENCODINGS  L�   BINDSTRING_POST_COOKIE " L�  
 BINDSTRING_POST_DATA_MIME  L�   BINDSTRING_URL  L�   BINDSTRING_IID ' L�   BINDSTRING_FLAG_BIND_TO_OBJECT $ L�   BINDSTRING_PTR_BIND_CONTEXT  L�   BINDSTRING_XDR_ORIGIN   L�   BINDSTRING_DOWNLOADPATH / >t  �   ImGuiTableColumnFlags_IndentEnable  L�   BINDSTRING_ROOTDOC_URL $ L�   BINDSTRING_INITIAL_FILENAME 0 >t  �   ImGuiTableColumnFlags_IndentDisable " L�   BINDSTRING_PROXY_USERNAME " L�   BINDSTRING_PROXY_PASSWORD , >t  �   ImGuiTableColumnFlags_IsEnabled , >t  �   ImGuiTableColumnFlags_IsVisible ! L�   BINDSTRING_ENTERPRISE_ID  L�   BINDSTRING_DOC_URL  |O    std::denorm_absent + >t  �   ImGuiTableColumnFlags_IsSorted  |O   std::denorm_present , >t  �   ImGuiTableColumnFlags_IsHovered  O    std::round_toward_zero  O   std::round_to_nearest # |O    std::_Num_base::has_denorm ( =    std::_Num_base::has_denorm_loss % =    std::_Num_base::has_infinity & =    std::_Num_base::has_quiet_NaN * =    std::_Num_base::has_signaling_NaN # =    std::_Num_base::is_bounded % 塓    _Atomic_memory_order_relaxed % 塓   _Atomic_memory_order_consume ! =    std::_Num_base::is_exact % 塓   _Atomic_memory_order_acquire % 塓   _Atomic_memory_order_release " =    std::_Num_base::is_iec559 % 塓   _Atomic_memory_order_acq_rel % 塓   _Atomic_memory_order_seq_cst # =    std::_Num_base::is_integer " =    std::_Num_base::is_modulo " =    std::_Num_base::is_signed    < std::ratio<60,1>::num     std::ratio<60,1>::den ' =    std::_Num_base::is_specialized ( =    std::_Num_base::tinyness_before  =    std::_Num_base::traps $ O    std::_Num_base::round_style  g    std::_Num_base::digits ! g    std::_Num_base::digits10 % g    std::_Num_base::max_digits10 % g    std::_Num_base::max_exponent ' g    std::_Num_base::max_exponent10 % g    std::_Num_base::min_exponent ' g    std::_Num_base::min_exponent10  g    std::_Num_base::radix ' =   std::_Num_int_base::is_bounded % =   std::_Num_int_base::is_exact ' =   std::_Num_int_base::is_integer + =   std::_Num_int_base::is_specialized " g   std::_Num_int_base::radix ) |O   std::_Num_float_base::has_denorm + =   std::_Num_float_base::has_infinity , =   std::_Num_float_base::has_quiet_NaN 0 =   std::_Num_float_base::has_signaling_NaN ) =   std::_Num_float_base::is_bounded ( =   std::_Num_float_base::is_iec559 ( =   std::_Num_float_base::is_signed - =   std::_Num_float_base::is_specialized * O   std::_Num_float_base::round_style $ g   std::_Num_float_base::radix C e   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E e   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P e   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity "     std::memory_order_relaxed "    std::memory_order_consume "    std::memory_order_acquire "    std::memory_order_release "    std::memory_order_acq_rel "    std::memory_order_seq_cst * g   std::numeric_limits<bool>::digits - =   std::numeric_limits<char>::is_signed d e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE - =    std::numeric_limits<char>::is_modulo f e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask * g   std::numeric_limits<char>::digits q e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity , g   std::numeric_limits<char>::digits10 q e  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j =   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m e    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k e    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 4 =   std::numeric_limits<signed char>::is_signed 1 g   std::numeric_limits<signed char>::digits 3 g   std::numeric_limits<signed char>::digits10 6 =   std::numeric_limits<unsigned char>::is_modulo 3 g   std::numeric_limits<unsigned char>::digits 5 g   std::numeric_limits<unsigned char>::digits10 1 =   std::numeric_limits<char16_t>::is_modulo . g   std::numeric_limits<char16_t>::digits 0 g   std::numeric_limits<char16_t>::digits10 1 =   std::numeric_limits<char32_t>::is_modulo . g    std::numeric_limits<char32_t>::digits 0 g  	 std::numeric_limits<char32_t>::digits10  e�   PARSE_CANONICALIZE  e�   PARSE_FRIENDLY  e�   PARSE_SECURITY_URL  e�   PARSE_ROOTDOCUMENT  e�   PARSE_DOCUMENT  e�   PARSE_ANCHOR ! e�   PARSE_ENCODE_IS_UNESCAPE  e�   PARSE_DECODE_IS_ESCAPE  e�  	 PARSE_PATH_FROM_URL  e�  
 PARSE_URL_FROM_PATH  e�   PARSE_MIME  e�   PARSE_SERVER  e�  
 PARSE_SCHEMA ` e   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos  e�   PARSE_SITE  e�   PARSE_DOMAIN  e�   PARSE_LOCATION  e�   PARSE_SECURITY_DOMAIN  e�   PARSE_ESCAPE  V�   PSU_DEFAULT  厖   QUERY_EXPIRATION_DATE 0 =   std::numeric_limits<wchar_t>::is_modulo " 厖   QUERY_TIME_OF_LAST_CHANGE  厖   QUERY_CONTENT_ENCODING  厖   QUERY_CONTENT_TYPE - g   std::numeric_limits<wchar_t>::digits  厖   QUERY_REFRESH / g   std::numeric_limits<wchar_t>::digits10  厖   QUERY_RECOMBINE  厖   QUERY_CAN_NAVIGATE  厖   QUERY_USES_NETWORK      std::ratio<1,1000>::num  厖  	 QUERY_IS_CACHED   厖  
 QUERY_IS_INSTALLEDENTRY     �std::ratio<1,1000>::den " 厖   QUERY_IS_CACHED_OR_MAPPED  厖   QUERY_USES_CACHE  厖  
 QUERY_IS_SECURE  厖   QUERY_IS_SAFE ! 厖   QUERY_USES_HISTORYFOLDER  .�    ServerApplication  x�    IdleShutdown . =   std::numeric_limits<short>::is_signed + g   std::numeric_limits<short>::digits - g   std::numeric_limits<short>::digits10 , =   std::numeric_limits<int>::is_signed ) g   std::numeric_limits<int>::digits + g  	 std::numeric_limits<int>::digits10 - =   std::numeric_limits<long>::is_signed * g   std::numeric_limits<long>::digits , g  	 std::numeric_limits<long>::digits10  儏    FEATURE_OBJECT_CACHING  儏   FEATURE_ZONE_ELEVATION  儏   FEATURE_MIME_HANDLING  儏   FEATURE_MIME_SNIFFING $ 儏   FEATURE_WINDOW_RESTRICTIONS & 儏   FEATURE_WEBOC_POPUPMANAGEMENT  儏   FEATURE_BEHAVIORS $ 儏   FEATURE_DISABLE_MK_PROTOCOL & 儏   FEATURE_LOCALMACHINE_LOCKDOWN 0 =   std::numeric_limits<__int64>::is_signed  儏  	 FEATURE_SECURITYBAND - g  ? std::numeric_limits<__int64>::digits ( 儏  
 FEATURE_RESTRICT_ACTIVEXINSTALL & 儏   FEATURE_VALIDATE_NAVIGATE_URL & 儏   FEATURE_RESTRICT_FILEDOWNLOAD / g   std::numeric_limits<__int64>::digits10 ! 儏  
 FEATURE_ADDON_MANAGEMENT " 儏   FEATURE_PROTOCOL_LOCKDOWN / 儏   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE " 儏   FEATURE_SAFE_BINDTOOBJECT # 儏   FEATURE_UNC_SAVEDFILECHECK / 儏   FEATURE_GET_URL_DOM_FILEPATH_UNENCODED   儏   FEATURE_TABBED_BROWSING  儏   FEATURE_SSLUX * 儏   FEATURE_DISABLE_NAVIGATION_SOUNDS + 儏   FEATURE_DISABLE_LEGACY_COMPRESSION & 儏   FEATURE_FORCE_ADDR_AND_STATUS  儏   FEATURE_XMLHTTP ( 儏   FEATURE_DISABLE_TELNET_PROTOCOL  儏   FEATURE_FEEDS $ 儏   FEATURE_BLOCK_INPUT_PROMPTS   o�    D3D_DRIVER_TYPE_UNKNOWN ! o�   D3D_DRIVER_TYPE_HARDWARE " o�   D3D_DRIVER_TYPE_REFERENCE  o�   D3D_DRIVER_TYPE_NULL ! o�   D3D_DRIVER_TYPE_SOFTWARE 7 =   std::numeric_limits<unsigned short>::is_modulo 4 g   std::numeric_limits<unsigned short>::digits 6 g   std::numeric_limits<unsigned short>::digits10 ) 崊    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED ) 崊   D3D_PRIMITIVE_TOPOLOGY_POINTLIST ( 崊   D3D_PRIMITIVE_TOPOLOGY_LINELIST ) 崊   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP , 崊   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST - 崊   D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP , 崊  
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ - 崊   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 0 崊   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 1 崊  
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 9 崊  ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST 9 崊  " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 9 崊  # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 9 崊  $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 9 崊  % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 9 崊  & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST 9 崊  ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 9 崊  ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 9 崊  ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST : 崊  * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST : 崊  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST : 崊  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST : 崊  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST 5 =   std::numeric_limits<unsigned int>::is_modulo : 崊  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST : 崊  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST 2 g    std::numeric_limits<unsigned int>::digits : 崊  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST 4 g  	 std::numeric_limits<unsigned int>::digits10 : 崊  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST : 崊  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST : 崊  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST : 崊  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST : 崊  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST : 崊  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST : e   std::integral_constant<unsigned __int64,2>::value : 崊  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST #    std::ratio<1,1000000>::num : 崊  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST : 崊  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST '   �@B std::ratio<1,1000000>::den : 崊  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST : 崊  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST : 崊  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST : 崊  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST : 崊  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST : 崊  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST : 崊  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST 6 =   std::numeric_limits<unsigned long>::is_modulo 3 g    std::numeric_limits<unsigned long>::digits 5 g  	 std::numeric_limits<unsigned long>::digits10 9 =   std::numeric_limits<unsigned __int64>::is_modulo   [�    D3D_PRIMITIVE_UNDEFINED 6 g  @ std::numeric_limits<unsigned __int64>::digits  [�   D3D_PRIMITIVE_POINT  [�   D3D_PRIMITIVE_LINE 8 g   std::numeric_limits<unsigned __int64>::digits10  [�   D3D_PRIMITIVE_TRIANGLE  [�   D3D_PRIMITIVE_LINE_ADJ # [�   D3D_PRIMITIVE_TRIANGLE_ADJ , [�   D3D_PRIMITIVE_1_CONTROL_POINT_PATCH , [�  	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH , [�  
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH , [�   D3D_PRIMITIVE_4_CONTROL_POINT_PATCH , [�   D3D_PRIMITIVE_5_CONTROL_POINT_PATCH , [�  
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH , [�   D3D_PRIMITIVE_7_CONTROL_POINT_PATCH , [�   D3D_PRIMITIVE_8_CONTROL_POINT_PATCH , [�   D3D_PRIMITIVE_9_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_10_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_11_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_12_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_13_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_14_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_15_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_16_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_17_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_18_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_19_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_20_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_21_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_22_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_23_CONTROL_POINT_PATCH - [�   D3D_PRIMITIVE_24_CONTROL_POINT_PATCH - [�    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH - [�  ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH - [�  " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH - [�  # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH + g   std::numeric_limits<float>::digits - [�  $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH - [�  % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH - g   std::numeric_limits<float>::digits10 - [�  & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH - [�  ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH 1 g  	 std::numeric_limits<float>::max_digits10 1 g  � std::numeric_limits<float>::max_exponent 3 g  & std::numeric_limits<float>::max_exponent10 2 g   �僺td::numeric_limits<float>::min_exponent 4 g   �踫td::numeric_limits<float>::min_exponent10 , g  5 std::numeric_limits<double>::digits . g   std::numeric_limits<double>::digits10 2 g   std::numeric_limits<double>::max_digits10 2 g   std::numeric_limits<double>::max_exponent " �    D3D_SRV_DIMENSION_UNKNOWN ! �   D3D_SRV_DIMENSION_BUFFER 4 g  4std::numeric_limits<double>::max_exponent10 $ �   D3D_SRV_DIMENSION_TEXTURE1D ) �   D3D_SRV_DIMENSION_TEXTURE1DARRAY 4 g  �黶td::numeric_limits<double>::min_exponent $ �   D3D_SRV_DIMENSION_TEXTURE2D ) �   D3D_SRV_DIMENSION_TEXTURE2DARRAY 6 g  �威std::numeric_limits<double>::min_exponent10 & �   D3D_SRV_DIMENSION_TEXTURE2DMS + �   D3D_SRV_DIMENSION_TEXTURE2DMSARRAY $ �   D3D_SRV_DIMENSION_TEXTURE3D & �  	 D3D_SRV_DIMENSION_TEXTURECUBE + �  
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY # �   D3D_SRV_DIMENSION_BUFFEREX 1 g  5 std::numeric_limits<long double>::digits 3 g   std::numeric_limits<long double>::digits10 7 g   std::numeric_limits<long double>::max_digits10 7 g   std::numeric_limits<long double>::max_exponent 9 g  4std::numeric_limits<long double>::max_exponent10 9 g  �黶td::numeric_limits<long double>::min_exponent ; g  �威std::numeric_limits<long double>::min_exponent10  G�    URLZONE_LOCAL_MACHINE  G�   URLZONE_INTRANET  G�   URLZONE_TRUSTED  G�   URLZONE_INTERNET  殔    D3D_INCLUDE_LOCAL  殔   D3D_INCLUDE_SYSTEM  弲    D3D_SVC_SCALAR  弲   D3D_SVC_VECTOR  弲   D3D_SVC_MATRIX_ROWS  弲   D3D_SVC_MATRIX_COLUMNS  弲   D3D_SVC_OBJECT  弲   D3D_SVC_STRUCT   弲   D3D_SVC_INTERFACE_CLASS " 弲   D3D_SVC_INTERFACE_POINTER  虆   D3D_SVF_USERPACKED  虆   D3D_SVF_USED " 虆   D3D_SVF_INTERFACE_POINTER  R�    URLZONEREG_DEFAULT $ 虆   D3D_SVF_INTERFACE_PARAMETER  R�   URLZONEREG_HKLM  g�    D3D_SVT_VOID  g�   D3D_SVT_BOOL  g�   D3D_SVT_INT  g�   D3D_SVT_FLOAT  g�   D3D_SVT_STRING  g�   D3D_SVT_TEXTURE  g�   D3D_SVT_TEXTURE1D  g�   D3D_SVT_TEXTURE2D  g�   D3D_SVT_TEXTURE3D  g�  	 D3D_SVT_TEXTURECUBE  g�  
 D3D_SVT_SAMPLER  g�   D3D_SVT_SAMPLER1D  g�   D3D_SVT_SAMPLER2D  g�  
 D3D_SVT_SAMPLER3D  g�   D3D_SVT_SAMPLERCUBE  g�   D3D_SVT_PIXELSHADER  g�   D3D_SVT_VERTEXSHADER  g�   D3D_SVT_PIXELFRAGMENT  g�   D3D_SVT_VERTEXFRAGMENT  g�   D3D_SVT_UINT  g�   D3D_SVT_UINT8  g�   D3D_SVT_GEOMETRYSHADER  g�   D3D_SVT_RASTERIZER  g�   D3D_SVT_DEPTHSTENCIL  g�   D3D_SVT_BLEND  g�   D3D_SVT_BUFFER  g�   D3D_SVT_CBUFFER  g�   D3D_SVT_TBUFFER  g�   D3D_SVT_TEXTURE1DARRAY  g�   D3D_SVT_TEXTURE2DARRAY ! g�   D3D_SVT_RENDERTARGETVIEW ! g�   D3D_SVT_DEPTHSTENCILVIEW  g�    D3D_SVT_TEXTURE2DMS ! g�  ! D3D_SVT_TEXTURE2DMSARRAY ! g�  " D3D_SVT_TEXTURECUBEARRAY  g�  # D3D_SVT_HULLSHADER  g�  $ D3D_SVT_DOMAINSHADER " g�  % D3D_SVT_INTERFACE_POINTER  g�  & D3D_SVT_COMPUTESHADER  g�  ' D3D_SVT_DOUBLE  g�  ( D3D_SVT_RWTEXTURE1D ! g�  ) D3D_SVT_RWTEXTURE1DARRAY  g�  * D3D_SVT_RWTEXTURE2D ! g�  + D3D_SVT_RWTEXTURE2DARRAY  g�  , D3D_SVT_RWTEXTURE3D  g�  - D3D_SVT_RWBUFFER # g�  . D3D_SVT_BYTEADDRESS_BUFFER % g�  / D3D_SVT_RWBYTEADDRESS_BUFFER " g�  0 D3D_SVT_STRUCTURED_BUFFER $ g�  1 D3D_SVT_RWSTRUCTURED_BUFFER ) g�  2 D3D_SVT_APPEND_STRUCTURED_BUFFER * g�  3 D3D_SVT_CONSUME_STRUCTURED_BUFFER E e   std::allocator<char32_t>::_Minimum_asan_allocation_alignment  s�   D3D_SIF_USERPACKED # s�   D3D_SIF_COMPARISON_SAMPLER $ s�   D3D_SIF_TEXTURE_COMPONENT_0 $ s�   D3D_SIF_TEXTURE_COMPONENT_1 # s�   D3D_SIF_TEXTURE_COMPONENTS  H�    D3D_SIT_CBUFFER  H�   D3D_SIT_TBUFFER  H�   D3D_SIT_TEXTURE  H�   D3D_SIT_SAMPLER  H�   D3D_SIT_UAV_RWTYPED  H�   D3D_SIT_STRUCTURED ! H�   D3D_SIT_UAV_RWSTRUCTURED  H�   D3D_SIT_BYTEADDRESS " H�   D3D_SIT_UAV_RWBYTEADDRESS & H�  	 D3D_SIT_UAV_APPEND_STRUCTURED ' H�  
 D3D_SIT_UAV_CONSUME_STRUCTURED . H�   D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER ( H�   D3D_SIT_RTACCELERATIONSTRUCTURE  [�   D3D_CBF_USERPACKED  趨    D3D_CT_CBUFFER  \�   COR_VERSION_MAJOR_V2  趨   D3D_CT_TBUFFER " 趨   D3D_CT_INTERFACE_POINTERS " 趨   D3D_CT_RESOURCE_BIND_INFO J    std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2  {�    D3D_NAME_UNDEFINED J    std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2  {�   D3D_NAME_POSITION  {�   D3D_NAME_CLIP_DISTANCE  {�   D3D_NAME_CULL_DISTANCE + {�   D3D_NAME_RENDER_TARGET_ARRAY_INDEX & {�   D3D_NAME_VIEWPORT_ARRAY_INDEX 1 韰    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED  {�   D3D_NAME_VERTEX_ID  {�   D3D_NAME_PRIMITIVE_ID F 韰   D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS A 韰   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK  {�   D3D_NAME_INSTANCE_ID  {�  	 D3D_NAME_IS_FRONT_FACE  {�  
 D3D_NAME_SAMPLE_INDEX , {�   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR + 枀    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 1 枀   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS . {�   D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR ? 枀   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY + {�  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR - {�   D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR O=   std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Same_size_and_compatible . {�   D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR / {�   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR  {�   D3D_NAME_BARYCENTRICS  {�   D3D_NAME_SHADINGRATE  {�   D3D_NAME_CULLPRIMITIVE L=    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_constructible  {�  @ D3D_NAME_TARGET  {�  A D3D_NAME_DEPTH L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1  {�  B D3D_NAME_COVERAGE % {�  C D3D_NAME_DEPTH_GREATER_EQUAL P   ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 " {�  D D3D_NAME_DEPTH_LESS_EQUAL  {�  E D3D_NAME_STENCIL_REF   {�  F D3D_NAME_INNER_COVERAGE L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 I=    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_assignable L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy  Y�   D3D_RETURN_TYPE_UNORM  Y�   D3D_RETURN_TYPE_SNORM  Y�   D3D_RETURN_TYPE_SINT  Y�   D3D_RETURN_TYPE_UINT  Y�   D3D_RETURN_TYPE_FLOAT  Y�   D3D_RETURN_TYPE_MIXED  Y�   D3D_RETURN_TYPE_DOUBLE " Y�   D3D_RETURN_TYPE_CONTINUED ' w�    D3D_REGISTER_COMPONENT_UNKNOWN & w�   D3D_REGISTER_COMPONENT_UINT32 & w�   D3D_REGISTER_COMPONENT_SINT32 ' w�   D3D_REGISTER_COMPONENT_FLOAT32 C e   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE ) �    D3D_TESSELLATOR_DOMAIN_UNDEFINED ' �   D3D_TESSELLATOR_DOMAIN_ISOLINE # �   D3D_TESSELLATOR_DOMAIN_TRI E e   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask $ �   D3D_TESSELLATOR_DOMAIN_QUAD P e   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity / 釁    D3D_TESSELLATOR_PARTITIONING_UNDEFINED - 釁   D3D_TESSELLATOR_PARTITIONING_INTEGER * 釁   D3D_TESSELLATOR_PARTITIONING_POW2 4 釁   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 5 釁   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN ) 鯀    D3D_TESSELLATOR_OUTPUT_UNDEFINED % 鯀   D3D_TESSELLATOR_OUTPUT_POINT $ 鯀   D3D_TESSELLATOR_OUTPUT_LINE + 鯀   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW , 鯀   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW d e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j =   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m e    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset ' 菂    D3D12_COMMAND_LIST_TYPE_DIRECT ' 菂   D3D12_COMMAND_LIST_TYPE_BUNDLE ( 菂   D3D12_COMMAND_LIST_TYPE_COMPUTE k e    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size % 菂   D3D12_COMMAND_LIST_TYPE_COPY - 菂   D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE . 菂   D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS 3 E  \ std::filesystem::path::preferred_separator - 菂   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE / =   std::atomic<long>::is_always_lock_free # B�   BINDHANDLETYPES_DEPENDENCY 8 K�    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD 9 K�   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE 6 K�   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR � =   std::_Trivial_cat<std::shared_ptr<donut::app::RegisteredFont>,std::shared_ptr<donut::app::RegisteredFont>,std::shared_ptr<donut::app::RegisteredFont> &&,std::shared_ptr<donut::app::RegisteredFont> &>::_Same_size_and_compatible � =    std::_Trivial_cat<std::shared_ptr<donut::app::RegisteredFont>,std::shared_ptr<donut::app::RegisteredFont>,std::shared_ptr<donut::app::RegisteredFont> &&,std::shared_ptr<donut::app::RegisteredFont> &>::_Bitcopy_constructible � =    std::_Trivial_cat<std::shared_ptr<donut::app::RegisteredFont>,std::shared_ptr<donut::app::RegisteredFont>,std::shared_ptr<donut::app::RegisteredFont> &&,std::shared_ptr<donut::app::RegisteredFont> &>::_Bitcopy_assignable 5 a�    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD 6 a�   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE 5 a�   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE i e   std::allocator<std::shared_ptr<donut::app::RegisteredFont> >::_Minimum_asan_allocation_alignment ) 3   nvrhi::ObjectTypes::SharedHandle - 3  �  nvrhi::ObjectTypes::D3D11_Device 4 3  �  nvrhi::ObjectTypes::D3D11_DeviceContext / 3  �  nvrhi::ObjectTypes::D3D11_Resource - 3  �  nvrhi::ObjectTypes::D3D11_Buffer 7 3  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 3  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 3  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : 3  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - 3  �  nvrhi::ObjectTypes::D3D12_Device 3 3  �  nvrhi::ObjectTypes::D3D12_CommandQueue : 3  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / 3  �  nvrhi::ObjectTypes::D3D12_Resource A 3  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A 3  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F 3  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` e   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G 3  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 3  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 3  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 3  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * 3  �  nvrhi::ObjectTypes::VK_Device 2 3  �  nvrhi::ObjectTypes::VK_PhysicalDevice , 3  �  nvrhi::ObjectTypes::VK_Instance ) 3  �  nvrhi::ObjectTypes::VK_Queue 1 3  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 3  �  nvrhi::ObjectTypes::VK_DeviceMemory * 3  �  nvrhi::ObjectTypes::VK_Buffer ) 3  �  nvrhi::ObjectTypes::VK_Image - 3  �	  nvrhi::ObjectTypes::VK_ImageView < 3  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + 3  �  nvrhi::ObjectTypes::VK_Sampler 0 3  �  nvrhi::ObjectTypes::VK_ShaderModule . 3  �
  nvrhi::ObjectTypes::VK_RenderPass / 3  �  nvrhi::ObjectTypes::VK_Framebuffer 2 3  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 3  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 3  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 3  �  nvrhi::ObjectTypes::VK_PipelineLayout , 3  �  nvrhi::ObjectTypes::VK_Pipeline , 3  �  nvrhi::ObjectTypes::VK_Micromap ( Bt   ImDrawFlags_RoundCornersTopLeft ) Bt    ImDrawFlags_RoundCornersTopRight 3 3  �  nvrhi::ObjectTypes::VK_ImageCreateInfo + Bt  @ ImDrawFlags_RoundCornersBottomLeft , Bt  � ImDrawFlags_RoundCornersBottomRight % Bt   ImDrawFlags_RoundCornersNone $ Bt  � ImDrawFlags_RoundCornersAll  M�    TKIND_ENUM  M�   TKIND_RECORD  M�   TKIND_MODULE  M�   TKIND_INTERFACE  M�   TKIND_DISPATCH  M�   TKIND_COCLASS  M�   TKIND_ALIAS  M�   TKIND_UNION % 飬   D3D12_COLOR_WRITE_ENABLE_RED ' 飬   D3D12_COLOR_WRITE_ENABLE_GREEN & 飬   D3D12_COLOR_WRITE_ENABLE_BLUE ' 飬   D3D12_COLOR_WRITE_ENABLE_ALPHA  J�    D3D12_LOGIC_OP_CLEAR  J�   D3D12_LOGIC_OP_SET  U�    PIDMSI_STATUS_NORMAL  J�   D3D12_LOGIC_OP_COPY % J�   D3D12_LOGIC_OP_COPY_INVERTED  U�   PIDMSI_STATUS_NEW  U�   PIDMSI_STATUS_PRELIM  J�   D3D12_LOGIC_OP_NOOP  J�   D3D12_LOGIC_OP_INVERT  U�   PIDMSI_STATUS_DRAFT ! U�   PIDMSI_STATUS_INPROGRESS  J�   D3D12_LOGIC_OP_AND  J�   D3D12_LOGIC_OP_NAND  U�   PIDMSI_STATUS_EDIT  U�   PIDMSI_STATUS_REVIEW  J�   D3D12_LOGIC_OP_OR  J�  	 D3D12_LOGIC_OP_NOR  U�   PIDMSI_STATUS_PROOF  J�  
 D3D12_LOGIC_OP_XOR  J�   D3D12_LOGIC_OP_EQUIV # J�   D3D12_LOGIC_OP_AND_REVERSE $ J�  
 D3D12_LOGIC_OP_AND_INVERTED " J�   D3D12_LOGIC_OP_OR_REVERSE � =   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � =    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � =    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable ' 8�    D3D12_SHADER_CACHE_MODE_MEMORY % S�    D3D12_BARRIER_LAYOUT_PRESENT * S�   D3D12_BARRIER_LAYOUT_GENERIC_READ + S�   D3D12_BARRIER_LAYOUT_RENDER_TARGET . S�   D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS 1 S�   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE 0 S�   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ - S�   D3D12_BARRIER_LAYOUT_SHADER_RESOURCE ) S�   D3D12_BARRIER_LAYOUT_COPY_SOURCE ' S�   D3D12_BARRIER_LAYOUT_COPY_DEST , S�  	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE * S�  
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST  槄   CC_CDECL 1 S�   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE / S�   D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ  槄   CC_MSCPASCAL 0 S�  
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE  槄   CC_PASCAL  槄   CC_MACPASCAL 0 S�   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ 1 S�   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE  槄   CC_STDCALL  槄   CC_FPFASTCALL / S�   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ 0 S�   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE  槄   CC_SYSCALL 1 S�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON  槄   CC_MPWCDECL  槄   CC_MPWPASCAL 7 S�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ ; S�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS : S�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE 6 S�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE 4 S�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST  亝    FUNC_VIRTUAL 2 S�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON  亝   FUNC_PUREVIRTUAL  亝   FUNC_NONVIRTUAL 8 S�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ  亝   FUNC_STATIC < S�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS ; S�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 7 S�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 5 S�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST  q�    VAR_PERINSTANCE  q�   VAR_STATIC  q�   VAR_CONST 8 =   std::atomic<unsigned long>::is_always_lock_free � =   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � =    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � =    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable / 3  � nvrhi::rt::cluster::kClasByteAlignment . 3   nvrhi::rt::cluster::kClasMaxTriangles ; k�    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS - 3   nvrhi::rt::cluster::kClasMaxVertices 2 3  ���� nvrhi::rt::cluster::kMaxGeometryIndex / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS : k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT 2 k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND 8 k�  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 7 k�  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER : k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL 9 k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT ? k�  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE ? k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY B k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS A k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 8 =    std::_False_trivial_cat::_Bitcopy_constructible 8 k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 5 =    std::_False_trivial_cat::_Bitcopy_assignable 6 k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK 7 k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 2 k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS ; k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 - g    std::integral_constant<int,0>::value / k�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS " �    D3D12_BARRIER_TYPE_GLOBAL # �   D3D12_BARRIER_TYPE_TEXTURE H =    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified � =   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � =    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � =    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable �   忿   g    donut::vfs::status::OK $ g   ��donut::vfs::status::Failed * g   �onut::vfs::status::PathNotFound , g   �齞onut::vfs::status::NotImplemented Z e   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment  _�    DESCKIND_NONE  _�   DESCKIND_FUNCDESC  _�   DESCKIND_VARDESC  _�   DESCKIND_TYPECOMP   _�   DESCKIND_IMPLICITAPPOBJ � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable � e   std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Minimum_asan_allocation_alignment  6�  g D3D_SHADER_MODEL_6_7  謪    SYS_WIN16  謪   SYS_WIN32  謪   SYS_MAC 3 螀    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS C 螀   D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS \ e   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 3 鲄    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 3 鲄   D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1 : 袇    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE I 袇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY J 袇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION H 袇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION  u�    CHANGEKIND_ADDMEMBER   u�   CHANGEKIND_DELETEMEMBER  u�   CHANGEKIND_SETNAMES $ u�   CHANGEKIND_SETDOCUMENTATION  u�   CHANGEKIND_GENERAL  u�   CHANGEKIND_INVALIDATE   u�   CHANGEKIND_CHANGEFAILED R =    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified x e   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment  覉  LPPARAMDESCEX  亝  FUNCKIND  謭  tagPARAMDESCEX  詧  PARAMDESC  詧  tagPARAMDESC  袌  tagARRAYDESC  槄  CALLCONV  _�  DESCKIND  崍  ELEMDESC  虉  BINDPTR  葓  tagFUNCDESC  +�  INVOKEKIND  儓  TLIBATTR  虉  tagBINDPTR  瓐  tagSTATSTG  攬  tagTYPEDESC  葓  FUNCDESC  "   HREFTYPE  謪  SYSKIND    tagVARDESC  M�  TYPEKIND  脠  IEnumSTATSTG  瓐  STATSTG  珗  ITypeComp  攬  TYPEDESC  妶  IDLDESC  崍  tagELEMDESC  妶  tagIDLDESC  歸  VARIANTARG  垐  EXCEPINFO  垐  tagEXCEPINFO 
    DISPID     MEMBERID  ≦  _CatchableType  u   UINT ' 韰  D3D12_BACKGROUND_PROCESSING_MODE  f  ImNewWrapper  S�  D3D12_BARRIER_LAYOUT  癹  ImVector<ImFont *>    tagCAUL  儓  tagTLIBATTR  鱲  _TP_CALLBACK_PRIORITY " 俀  _s__RTTIBaseClassDescriptor  甙  ImGuiCond_  t   ImGuiHoveredFlags ? s  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  Ek  ImFontConfig & 扱  $_TypeDescriptor$_extraBytes_24 6 ve  __vcrt_va_list_is_reference<char const * const>  f  ImGuiKey  崊  D3D_PRIMITIVE_TOPOLOGY  x�  tagShutdownType  q   OLECHAR G |  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �.  _Ctypevec  莅  ImGuiStyleVar_  Yt  ImGuiChildFlags_  "�  tagCABSTR  �  D3D12_BARRIER_TYPE  槄  tagCALLCONV  M�  tagTYPEKIND   4�  D3D12_STATIC_BORDER_COLOR & 
e  $_TypeDescriptor$_extraBytes_28  歸  VARIANT     ImS16  #   uintmax_t  7�  ISequentialStream     int64_t  z�  BSTRBLOB    _Smtx_t  穎  ImGuiTextBuffer  t   ImGuiStyleVar  #   rsize_t  殔  _D3D_INCLUDE_TYPE  #   DWORD_PTR  j�  TYPEATTR  韍  ImVector<ImDrawVert>     VARIANT_BOOL - ne  __vc_attributes::event_sourceAttribute 9 ge  __vc_attributes::event_sourceAttribute::optimize_e 5 ee  __vc_attributes::event_sourceAttribute::type_e > ce  __vc_attributes::helper_attributes::v1_alttypeAttribute F ^e  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 [e  __vc_attributes::helper_attributes::usageAttribute B We  __vc_attributes::helper_attributes::usageAttribute::usage_e * Te  __vc_attributes::threadingAttribute 7 Me  __vc_attributes::threadingAttribute::threading_e - Je  __vc_attributes::aggregatableAttribute 5 Ce  __vc_attributes::aggregatableAttribute::type_e / @e  __vc_attributes::event_receiverAttribute 7 7e  __vc_attributes::event_receiverAttribute::type_e ' 4e  __vc_attributes::moduleAttribute / +e  __vc_attributes::moduleAttribute::type_e  卛  ImVector<ImVec2>  �1  __std_fs_find_data & 翾  $_TypeDescriptor$_extraBytes_23 
 漺  PUWSTR - R  $_s__CatchableTypeArray$_extraBytes_32 # E7  __std_fs_reparse_data_buffer Z (e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ %e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` #e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �1  __std_fs_dir_handle  0f  ImGuiOnceUponAFrame ( 踲  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  -w  AR_STATE  `�  tagCADBL  ;w  _DEVICE_DATA_SET_RANGE  �-  __std_access_rights  q�  VARKIND 3 袇  D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE  繧  _TypeDescriptor & 譗  $_TypeDescriptor$_extraBytes_34 ! 駞  D3D12_DESCRIPTOR_HEAP_TYPE  V�  _tagPSUACTION  Cj  ImFontAtlasCustomRect 
 b�  tagDEC  d�  CALPSTR     LONG_PTR  L�  tagBINDSTRING 	   tm   g�  _D3D_SHADER_VARIABLE_TYPE ! 弲  _D3D_SHADER_VARIABLE_CLASS  ▏  tagCACLIPDATA  #   ULONG_PTR " U�  D3D12_RESOURCE_BARRIER_TYPE % }Q  _s__RTTICompleteObjectLocator2 " 殕  D3D12_DESCRIPTOR_RANGE_TYPE  G�  tagURLZONE  齟  ImGuiTableSortSpecs    PUWSTR_C  雟  PTP_CLEANUP_GROUP  憛  __MIDL_ICodeInstall_0001  p  PCHAR  W�  tagBINDSTATUS    _GUID  R�  _URLZONEREG  雲  _LARGE_INTEGER ' f�  _LARGE_INTEGER::<unnamed-type-u>  Jt  ImGuiFocusedFlags_ & 
R  $_TypeDescriptor$_extraBytes_30  飬  D3D12_COLOR_WRITE_ENABLE  *�  CLIPDATA     ImS8  邊  CAFILETIME  d�  tagCALPSTR  渾  CALPWSTR 
 N�  CAL  m�  tagCABSTRBLOB      ImU8  W�  tagSAFEARRAYBOUND  Ep  ImDrawChannel  奼  ImDrawCallback  _�  tagCAFLT A e  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & Z�  $_TypeDescriptor$_extraBytes_46 
 
k  ImFont  !   ImU16 
 鋮  tagCAH  b�  DECIMAL  ,�  tagCAUI  !   WORD  ≦  _s__CatchableType    ImDrawListSplitter  6j  ImVector<unsigned int>  ,�  ImGuiStyle  x�  CAUH  {�  D3D_NAME  m�  tagCADATE  Nt  ImGuiDragDropFlags_  6�  D3D_SHADER_MODEL  `�  CADBL  F  LPCOLESTR  蛃  ImGuiTreeNodeFlags_    PCUWSTR  貐  CAPROPVARIANT  Dt  ImGuiHoveredFlags_  t   ImGuiCond  _�  CAFLT & 禥  $_TypeDescriptor$_extraBytes_19 & 赒  $_TypeDescriptor$_extraBytes_21  #   uint64_t  t   ImGuiMouseCursor ' 遶  JOB_OBJECT_IO_RATE_CONTROL_FLAGS 9 e  __vcrt_va_list_is_reference<wchar_t const * const>  I�  _USER_ACTIVITY_PRESENCE  5  __std_fs_filetime  t   ImGuiDir E a  __vcrt_assert_va_start_is_not_reference<wchar_t const * const>  t   ImGuiConfigFlags  tg  ImColor    PLONG & 朡  $_TypeDescriptor$_extraBytes_20  Y�  DISPPARAMS  覅  _FILETIME  p  va_list  Kh  ImDrawList  Kw  FS_BPIO_INFLAGS - 闝  $_s__CatchableTypeArray$_extraBytes_16   k7  __std_fs_copy_file_result  �1  __std_code_page  =w  PDEVICE_DSM_DEFINITION      BYTE . K�  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE %   D3D12_RAYTRACING_GEOMETRY_TYPE 
 F  PCWSTR  U�  IStream Q 鰢  std::_Default_allocator_traits<std::allocator<donut::app::IRenderPass *> > @ 縹  std::_Arg_types<donut::app::DeviceManager &,unsigned int> j -�  std::_Default_allocator_traits<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > > f   std::_Simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > F 軂  std::_Func_class<void,donut::app::DeviceManager &,unsigned int> P 杺  std::_Func_class<void,donut::app::DeviceManager &,unsigned int>::_Storage 4 
�  std::_Simple_types<donut::app::IRenderPass *> a 鷩  std::allocator_traits<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > > � 峿  std::_Compressed_pair<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >,std::_List_val<std::_List_simple_types<donut::app::IRenderPass *> >,1> . 坱  std::_Ptr_base<donut::vfs::IFileSystem> H 鴩  std::allocator_traits<std::allocator<donut::app::IRenderPass *> > � 穝  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > ] H�  std::unique_ptr<donut::app::ImGui_NVRHI,std::default_delete<donut::app::ImGui_NVRHI> > ' ZC  std::default_delete<wchar_t [0]> . +-  std::_Conditionally_enabled_hash<int,1> A (6  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> f w�  std::_Compressed_pair<std::default_delete<donut::app::ImGui_NVRHI>,donut::app::ImGui_NVRHI *,1> ? 筄  std::_Default_allocator_traits<std::allocator<wchar_t> >  v  std::_Lockit * cH  std::hash<enum nvrhi::ResourceType> - F2  std::reverse_iterator<wchar_t const *> " 諳  std::_Char_traits<char,int>  .  std::_Fs_file  "   std::_Atomic_counter_t  侽  std::_Num_base & 7-  std::hash<std::error_condition> K 盝  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 	卲  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � 適  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0>  |*  std::_Big_uint128 � qu  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ) 酧  std::_Narrow_char_traits<char,int>  �  std::hash<float> 6 rJ  std::allocator<nvrhi::rt::PipelineHitGroupDesc> } F�  std::_Default_allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > 3 p�  std::default_delete<donut::app::ImGui_NVRHI>  /-  std::hash<int>  凮  std::_Num_int_base  �0  std::ctype<wchar_t> " �-  std::_System_error_category / 丠  std::_Conditionally_enabled_hash<bool,1> 1 衄  std::_Ptr_base<donut::app::RegisteredFont>  |O  std::float_denorm_style u WO  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 赹  std::allocator_traits<std::allocator<wchar_t> > �   std::list<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  S  std::bad_cast  WI  std::equal_to<void> 3 )l  std::_Ptr_base<donut::engine::ShaderFactory> � �3  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > t Io  std::_Compressed_pair<std::allocator<unsigned short>,std::_Vector_val<std::_Simple_types<unsigned short> >,1> B 藄  std::_Default_allocator_traits<std::allocator<ImDrawVert> > " ㎡  std::numeric_limits<double>  �  std::__non_rtti_object ( �  std::_Basic_container_proxy_ptr12 : 搉  std::vector<ImDrawVert,std::allocator<ImDrawVert> > P bn  std::vector<ImDrawVert,std::allocator<ImDrawVert> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8>    std::_Num_float_base  �+  std::logic_error 7 <H  std::_Conditionally_enabled_hash<unsigned int,1> G kH  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety ! I]  std::char_traits<char32_t>  $/  std::locale  Y/  std::locale::_Locimp  5/  std::locale::facet   =/  std::locale::_Facet_guard  �.  std::locale::id l uo  std::_Compressed_pair<std::allocator<ImDrawVert>,std::_Vector_val<std::_Simple_types<ImDrawVert> >,1> s qP  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   哋  std::numeric_limits<bool> # ≒  std::_WChar_traits<char16_t> P 廋  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T '  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   �  std::_Fake_proxy_ptr_impl F 羢  std::_Default_allocator_traits<std::allocator<unsigned short> > * 淥  std::numeric_limits<unsigned short> ' '  std::hash<nvrhi::BindingSetDesc> Z 扤  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M 02  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � zJ  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  i,  std::overflow_error % 楧  std::_One_then_variadic_args_t D 貶  std::_Constexpr_immortalize_impl<std::_System_error_category> W 窷  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > E 	7  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j R\  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > ' �  std::_Ptr_base<donut::vfs::Blob>   P\  std::char_traits<wchar_t> � 皅  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >   �  std::pmr::memory_resource i 舭  std::initializer_list<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > ! -  std::array<nvrhi::Rect,16> 4 滼  std::allocator<nvrhi::rt::PipelineShaderDesc> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  v[  std::false_type  O  std::float_round_style T    std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy \ 荨  std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  j  std::string B \  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �2  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , r!  std::array<nvrhi::BindingSetItem,128> �   std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �"  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � 璼  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > | gp  std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � 蠮  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> ,   std::numeric_limits<unsigned __int64>  �.  std::_Locinfo 6 85  std::_Ptr_base<std::filesystem::_Dir_enum_impl> # Br  std::hash<nvrhi::ITexture *> \ BQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s 橫  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > k   std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � B�  std::_Func_impl_no_alloc<<lambda_be0255e1888a8684391b3d47a840c833>,void,std::basic_string_view<char,std::char_traits<char> > > $ 嶰  std::numeric_limits<char16_t> 0 $  std::array<nvrhi::VertexBufferBinding,16>  �  std::string_view  &  std::wstring_view % 穁  std::integral_constant<bool,1>   �  std::_Leave_proxy_unbound z }p  std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >  x1  std::money_base  甗  std::money_base::pattern s 垄  std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  T.  std::_Timevec ? �  std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >   �,  std::_Init_once_completer j �6  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � i6  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + �/  std::codecvt<wchar_t,char,_Mbstatet> h 岲  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> ! mo  std::allocator<ImDrawVert> Q 漑  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % �  std::array<nvrhi::Viewport,16>  �  std::_Iterator_base12  咵  std::_Pocma_values 7 Z'  std::_Array_const_iterator<enum nvrhi::Format,8> ! %-  std::hash<std::error_code> N 93  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ 'Q  std::_Default_allocator_traits<std::allocator<char32_t> >  �2  std::allocator<char32_t> ? =7  std::unique_ptr<char [0],std::default_delete<char [0]> > $ K  std::_Atomic_integral<long,4>  匟  std::hash<bool>     std::streamsize 6 ~D  std::_String_val<std::_Simple_types<char32_t> > = rF  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` 滶  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> M 挌  std::_Arg_types<std::basic_string_view<char,std::char_traits<char> > > | 鰌  std::_Uhash_choose_transparency<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *>,void>    std::hash<long double> � �3  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 3  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l Z  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k V  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy  mA  std::array<bool,3> U kQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # 扥  std::numeric_limits<wchar_t>  3  std::_Container_base0 F Zr  std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1> J 瀩  std::_List_val<std::_List_simple_types<donut::app::IRenderPass *> >  	  std::hash<double> L 鮺  std::function<void __cdecl(donut::app::DeviceManager &,unsigned int)> O uQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & 烼  std::bidirectional_iterator_tag . �&  std::hash<nvrhi::TextureSubresourceSet> � 檘  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > < Uo  std::_Vector_val<std::_Simple_types<unsigned short> > / 匬  std::_Char_traits<char32_t,unsigned int>  O-  std::_System_error ( ?'  std::hash<nvrhi::FramebufferInfo> i 無  std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> 9 {C  std::allocator<std::filesystem::_Find_file_handle>  -  std::error_condition % v[  std::integral_constant<bool,0>  a  std::bad_exception & 錌  std::_Zero_then_variadic_args_t  �  std::u32string � 蓃  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *>  4  std::_Fake_allocator / t   std::array<nvrhi::BindingLayoutItem,128>  
,  std::invalid_argument N H[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U 襈  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 9 �  std::_List_simple_types<donut::app::IRenderPass *> S F[  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R eD  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > + �7  std::pair<enum __std_win_error,bool> S  2  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  $,  std::length_error F XM  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 僀  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> !   std::numeric_limits<float>  �1  std::time_base   ~1  std::time_base::dateorder ) \  std::_Atomic_integral_facade<long> % 滺  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " KH  std::hash<unsigned __int64>  馴  std::ratio<60,1> S 蕷  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > > ] 崵  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > >::_Storage  �  std::exception_ptr  颶  std::ratio<1,1000000> [ 榘  std::allocator_traits<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > 8 乷  std::_Vector_val<std::_Simple_types<ImDrawVert> > ) 慔  std::hash<enum nvrhi::BlendFactor> $ 怬  std::numeric_limits<char32_t>  �,  std::once_flag   -  std::error_code  6  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 遉  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  1  std::_Iosb<int>   1  std::_Iosb<int>::_Seekdir ! �0  std::_Iosb<int>::_Openmode   �0  std::_Iosb<int>::_Iostate ! �0  std::_Iosb<int>::_Fmtflags # �0  std::_Iosb<int>::_Dummy_enum 7 軿  std::allocator_traits<std::allocator<char32_t> >  訴  std::nano ( h�  std::_Ptr_base<donut::vfs::IBlob>  ?  std::_Iterator_base0 d 敯  std::_Uninitialized_backout_al<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > C Q�  std::allocator<std::shared_ptr<donut::app::RegisteredFont> > M 咼  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > #刴  std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 1 橮  std::_Char_traits<char16_t,unsigned short> $ �&  std::hash<nvrhi::BufferRange> T )3  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  �.  std::_Locbase<int> ! 臵  std::char_traits<char16_t>  �  std::tuple<>  ^  std::_Container_base12  -  std::io_errc  ;1  std::ios_base  L1  std::ios_base::_Fnarray  F1  std::ios_base::_Iosarray  �0  std::ios_base::Init  �0  std::ios_base::failure  1  std::ios_base::event E 笻  std::_Constexpr_immortalize_impl<std::_Generic_error_category> ) 孫  std::numeric_limits<unsigned char> % Ao  std::allocator<unsigned short> � XD  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  穁  std::true_type  n�  std::array<bool,349>   極  std::numeric_limits<long> " 瞆  std::initializer_list<char>  GV  std::_Invoker_strategy  7  std::nothrow_t $ 甇  std::_Default_allocate_traits z su  std::allocator_traits<std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > N 
3  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 SZ  std::allocator_traits<std::allocator<char> > ! 擮  std::numeric_limits<short> . 菾  std::allocator<nvrhi::rt::GeometryDesc> ; �  std::basic_string_view<char,std::char_traits<char> > � 襮  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > ' @�  std::equal_to<nvrhi::ITexture *> ! �0  std::ctype<unsigned short> C f  std::basic_string_view<char16_t,std::char_traits<char16_t> > | Kr  std::_Compressed_pair<std::hash<nvrhi::ITexture *>,std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1>,1> < 6'  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 0 
�  std::allocator<donut::app::IRenderPass *> 6 珼  std::_String_val<std::_Simple_types<char16_t> > = |F  std::_String_val<std::_Simple_types<char16_t> >::_Bxty �  std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> O 鶫  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P �2  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . oH  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock  w  std::bad_alloc  �,  std::underflow_error B 岺  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> J aC  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D PC  std::_Compressed_pair<std::default_delete<char [0]>,char *,1> Z e�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::app::RegisteredFont> > >  �1  std::messages_base  ;,  std::out_of_range # 歄  std::numeric_limits<__int64> i 訡  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  ^0  std::ctype<char>    std::memory_order ` A�  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � 耽  std::_Compressed_pair<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> } 莪  std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > �   std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::_Reallocation_policy  �  std::nullopt_t  �  std::nullopt_t::_Tag  mX  std::ratio<3600,1> # C  std::_Atomic_storage<long,4> # w'  std::hash<nvrhi::BlendState> /   std::shared_ptr<donut::vfs::IFileSystem> d 郯  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >  1  std::atomic_flag f 釪  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  q-  std::system_error = st  std::allocator_traits<std::allocator<unsigned short> > (  �  std::shared_ptr<donut::vfs::Blob> < 罯  std::_Default_allocator_traits<std::allocator<char> > W aQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  ]X  std::ratio<1,1> I 倉  std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >   漈  std::forward_iterator_tag  R,  std::runtime_error   �  std::bad_array_new_length � Y�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::app::RegisteredFont> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::app::RegisteredFont> > >,1> E 躂  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 qt  std::allocator_traits<std::allocator<ImDrawVert> >  �.  std::_Yarn<char>  M  std::_Container_proxy ( X  std::_Facetptr<std::ctype<char> > Z 砅  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  �  std::u16string  �  std::nested_exception    std::_Distance_unknown ( 濷  std::numeric_limits<unsigned int> < ;M  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> ) ~�  std::shared_ptr<donut::vfs::IBlob> , �/  std::codecvt<char32_t,char,_Mbstatet> @   std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> �簈  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> ) �#  std::array<nvrhi::IBindingSet *,5> K j  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> & 鵙  std::initializer_list<char32_t> d �%  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �%  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 颲  std::initializer_list<char16_t> % 錠  std::initializer_list<wchar_t> C _H  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>     std::hash<std::nullptr_t> � �  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,1> ' 玂  std::numeric_limits<long double>  -  std::errc V ��  std::list<donut::app::IRenderPass *,std::allocator<donut::app::IRenderPass *> > V +�  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > ,   std::default_delete<std::_Facet_base>  �,  std::range_error  k  std::bad_typeid > 橦  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  訴  std::ratio<1,1000000000>  |2  std::allocator<char16_t> $ IC  std::default_delete<char [0]> ` B%  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v %  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J �2  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  諹  std::ratio<1,1000>  蔝  std::ratio<1,10000000> ; JD  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  �.  std::_Crt_new_delete b ot  std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � at  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > % �-  std::_Iostream_error_category2 * 齌  std::_String_constructor_concat_tag - 洴  std::_Ref_count_obj2<donut::vfs::Blob>  e2  std::allocator<char> � 蒻  std::unordered_map<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *>,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > G 蒆  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t &   std::random_access_iterator_tag 4 Al  std::shared_ptr<donut::engine::ShaderFactory> ; GH  std::_Conditionally_enabled_hash<unsigned __int64,1> R 繡  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  �  std::bad_weak_ptr ) 燨  std::numeric_limits<unsigned long>   鸈  std::_Atomic_padded<long> @ 6  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  �.  std::_Yarn<wchar_t> = vH  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> # 钳  std::_Wrap<donut::vfs::Blob>  '  std::wstring } 触  std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > ' 奜  std::numeric_limits<signed char> � �3  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �+  std::domain_error  �  std::u32string_view  3  std::_Container_base  �3  std::allocator<wchar_t> � _t  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > L 歍  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > 2 
�  std::shared_ptr<donut::app::RegisteredFont> $ WH  std::hash<nvrhi::IResource *> "   std::_Nontrivial_dummy_type 1 b'  std::hash<nvrhi::BlendState::RenderTarget>   圤  std::numeric_limits<char> 9 r+  std::chrono::duration<__int64,std::ratio<1,1000> >  �*  std::chrono::nanoseconds y 0.  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �*  std::chrono::duration<__int64,std::ratio<1,1000000000> > , 癘  std::chrono::duration_values<__int64>  �*  std::chrono::seconds 3 0+  std::chrono::duration<int,std::ratio<60,1> > 6 �*  std::chrono::duration<__int64,std::ratio<1,1> > s �*  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >    T  std::chrono::steady_clock   T  std::chrono::system_clock 6 E+  std::chrono::duration<double,std::ratio<60,1> > ; �+  std::chrono::duration<double,std::ratio<1,1000000> > > �+  std::chrono::duration<double,std::ratio<1,1000000000> > = �*  std::chrono::duration<__int64,std::ratio<1,10000000> > q �*  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 +  std::chrono::duration<int,std::ratio<3600,1> > 8 �+  std::chrono::duration<double,std::ratio<1,1000> > < �+  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 \+  std::chrono::duration<double,std::ratio<1,1> > 8 +  std::chrono::duration<double,std::ratio<3600,1> >  30  std::ctype_base  �4  std::filesystem::perms ' �4  std::filesystem::directory_entry $ �4  std::filesystem::copy_options ( r4  std::filesystem::filesystem_error 7 zI  std::filesystem::_Path_iterator<wchar_t const *> ) �1  std::filesystem::_Find_file_handle & �1  std::filesystem::_Is_slash_oper . �5  std::filesystem::_Should_recurse_result $ �7  std::filesystem::perm_options 4 �6  std::filesystem::recursive_directory_iterator . �4  std::filesystem::_File_status_and_error & i5  std::filesystem::_Dir_enum_impl 0 {5  std::filesystem::_Dir_enum_impl::_Creator @ �5  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! �4  std::filesystem::file_type . �5  std::filesystem::_Directory_entry_proxy " �7  std::filesystem::space_info * �5  std::filesystem::directory_iterator & 0.  std::filesystem::file_time_type 0 �5  std::filesystem::_Recursive_dir_enum_impl ) 5  std::filesystem::directory_options # �4  std::filesystem::file_status u ,4  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( T  std::filesystem::_File_time_clock  �2  std::filesystem::path $ �1  std::filesystem::path::format * II  std::filesystem::_Normal_conversion < kM  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �/  std::codecvt<char16_t,char,_Mbstatet>  T  std::char_traits<char> � 菴  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �,  std::error_category ) �,  std::error_category::_Addr_storage ! �-  std::_System_error_message  
  std::_Unused_parameter h 籇  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>    std::bad_optional_access A &  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > W 趪  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > u 5~  std::vector<nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � ~  std::vector<nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Reallocation_policy 7 Q5  std::shared_ptr<std::filesystem::_Dir_enum_impl> - 嚡  std::_Wrap<donut::app::RegisteredFont> = SH  std::_Conditionally_enabled_hash<nvrhi::IResource *,1>  �/  std::_Codecvt_mode  A   std::max_align_t @ 0Q  std::_Default_allocator_traits<std::allocator<char16_t> > � C4  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > < �  std::_Conditionally_enabled_hash<nvrhi::ITexture *,1> 0 鯫  std::_Char_traits<wchar_t,unsigned short> '    std::array<enum nvrhi::Format,8> \ 錗  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 諨  std::_String_val<std::_Simple_types<wchar_t> > < 匜  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  C.  std::_Facet_base ' '  std::hash<nvrhi::BindingSetItem> " P  std::_WChar_traits<wchar_t> 2 0  std::codecvt<unsigned short,char,_Mbstatet> # �-  std::_Generic_error_category  翺  std::streampos  蔛  std::input_iterator_tag 2 PM  std::_Wrap<std::filesystem::_Dir_enum_impl> X ㎏  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> B o  std::vector<unsigned short,std::allocator<unsigned short> > X 觧  std::vector<unsigned short,std::allocator<unsigned short> >::_Reallocation_policy ' 〩  std::hash<enum nvrhi::ColorMask> 7 o�  std::_Ref_count_obj2<donut::app::RegisteredFont>  �/  std::codecvt_base t Q�  std::allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  q*  std::bad_function_call O 鞱  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > � 倸  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � R�  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Reallocation_policy X 驓  std::function<void __cdecl(std::basic_string_view<char,std::char_traits<char> >)> ' �7  std::hash<std::filesystem::path> Q n  std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > R   std::_Func_base<void,std::basic_string_view<char,std::char_traits<char> > >  @H  std::hash<unsigned int> 7 篠  std::allocator_traits<std::allocator<char16_t> > l 蟑  std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> > "    std::_Asan_aligned_pointers F 窼  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �   std::array<nvrhi::BindingLayoutItem,16> � 苢  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > $ zH  std::hash<enum nvrhi::Format>  朞  std::numeric_limits<int> 2 疎  std::_String_val<std::_Simple_types<char> > 9 岶  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access 
 !   wint_t  t   ImDrawFlags  g  ImGuiStorage % 鮢  ImGuiStorage::ImGuiStoragePair  !   ImWchar & 惆  $_TypeDescriptor$_extraBytes_56  ▏  CACLIPDATA    VARDESC # �%  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  I!  nvrhi::BindingSetDesc  禨  nvrhi::SubresourceTiling $ 鰈  nvrhi::GraphicsPipelineHandle  %   nvrhi::ResourceType  u   nvrhi::ObjectType ) f"  nvrhi::RefCountPtr<nvrhi::IShader>  1"  nvrhi::InputLayoutHandle   X#  nvrhi::IndexBufferBinding   睸  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �   nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " ;   nvrhi::VulkanBindingOffsets  x#  nvrhi::GraphicsState * 蔿  nvrhi::RefCountPtr<nvrhi::ISampler> /   nvrhi::static_vector<nvrhi::Viewport,16>  "  nvrhi::ShaderDesc  a$  nvrhi::IComputePipeline : 0$  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  #  nvrhi::Rect  !  nvrhi::BindingSetItem $ �   nvrhi::BindingLayoutItemArray ) 琒  nvrhi::RefCountPtr<nvrhi::IDevice> ! �!  nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �#  nvrhi::IGraphicsPipeline ! &  nvrhi::ShaderLibraryHandle  J  nvrhi::FramebufferInfoEx  p"  nvrhi::IShader  o  nvrhi::TextureDesc  "!  nvrhi::ISampler ! G#  nvrhi::VertexBufferBinding !  #  nvrhi::ComputePipelineDesc  _  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # B&  nvrhi::MeshletPipelineHandle  P  nvrhi::Format  8$  nvrhi::DrawArguments  {$  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + N  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �!  nvrhi::static_vector<nvrhi::BindingSetItem,128>  K   nvrhi::BindingLayoutDesc   R  nvrhi::SamplerAddressMode  �&  nvrhi::IDevice ! �"  nvrhi::BindingLayoutHandle ! �!  nvrhi::BindingSetItemArray . l  nvrhi::RefCountPtr<nvrhi::ICommandList>  琒  nvrhi::DeviceHandle   僑  nvrhi::TiledTextureRegion  �$  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & �!  nvrhi::VariableRateShadingState  S  nvrhi::IStagingTexture . 1"  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " 6  nvrhi::ShaderSpecialization 8 -  nvrhi::ShaderSpecialization::<unnamed-type-value>  R  nvrhi::TextureDimension 0 �"  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' j$  nvrhi::DispatchIndirectArguments  蔿  nvrhi::SamplerHandle * I$  nvrhi::DrawIndexedIndirectArguments # P&  nvrhi::DescriptorTableHandle  0&  nvrhi::TimerQueryHandle   �   nvrhi::BindlessLayoutDesc    nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! 8#  nvrhi::MeshletPipelineDesc 9 �   nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �%  nvrhi::HeapHandle # @&  nvrhi::ComputePipelineHandle  vS  nvrhi::PackedMipDesc  x  nvrhi::RasterFillMode  u   nvrhi::ArraySlice ! �!  nvrhi::VariableShadingRate  �  nvrhi::IResource  �#  nvrhi::IBindingSet  rS  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - !S  nvrhi::RefCountPtr<nvrhi::IBindingSet> * 
&  nvrhi::SamplerFeedbackTextureHandle # �!  nvrhi::SinglePassStereoState % ;!  nvrhi::ISamplerFeedbackTexture  �%  nvrhi::CommandQueue  A  nvrhi::BlendFactor  '&  nvrhi::EventQueryHandle  0   nvrhi::BindingLayoutItem  ;&  nvrhi::FramebufferHandle 1 @  nvrhi::static_vector<enum nvrhi::Format,8>  RS  nvrhi::BufferHandle  �  nvrhi::StencilOp  #  nvrhi::IBindingLayout  <  nvrhi::ColorMask  �  nvrhi::FramebufferInfo  絉  nvrhi::TextureHandle  nS  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  hS  nvrhi::IMessageCallback  �!  nvrhi::PrimitiveType  p  nvrhi::BlendState & U  nvrhi::BlendState::RenderTarget 3 鰈  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 �#  nvrhi::static_vector<nvrhi::IBindingSet *,5> " "  nvrhi::GraphicsPipelineDesc H �"  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) RS  nvrhi::RefCountPtr<nvrhi::IBuffer> $ ,S  nvrhi::TiledTextureCoordinate  (S  nvrhi::IHeap # u  nvrhi::FramebufferAttachment  �#  nvrhi::BindingSetVector  !S  nvrhi::BindingSetHandle ( 鸕  nvrhi::SamplerFeedbackTextureDesc ! �"  nvrhi::BindingLayoutVector " �%  nvrhi::StagingTextureHandle  �  nvrhi::Object  ;"  nvrhi::IInputLayout  z  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  v  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags ! +  nvrhi::rt::GeometrySpheres # 蒖  nvrhi::rt::ShaderTableHandle +   nvrhi::rt::OpacityMicromapUsageCount $ �$  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   E&  nvrhi::rt::PipelineHandle ! E  nvrhi::rt::AffineTransform & �$  nvrhi::rt::PipelineHitGroupDesc  >  nvrhi::rt::GeometryLss 3 馬  nvrhi::rt::cluster::OperationBlasBuildParams . 鞷  nvrhi::rt::cluster::OperationMoveParams ( 鏡  nvrhi::rt::cluster::OperationDesc 3 鉘  nvrhi::rt::cluster::OperationClasBuildParams , 逺  nvrhi::rt::cluster::OperationSizeInfo * 跼  nvrhi::rt::cluster::OperationParams  G  nvrhi::rt::GeometryType ' X&  nvrhi::rt::OpacityMicromapHandle  a  nvrhi::rt::GeometryDesc - f  nvrhi::rt::GeometryDesc::GeomTypeUnion % n  nvrhi::rt::OpacityMicromapDesc #   nvrhi::rt::GeometryTriangles  -!  nvrhi::rt::IAccelStruct # Z&  nvrhi::rt::AccelStructHandle  �%  nvrhi::rt::IShaderTable ' �%  nvrhi::rt::DispatchRaysArguments  �%  nvrhi::rt::State     nvrhi::rt::GeometryAABBs  �$  nvrhi::rt::PipelineDesc  蠷  nvrhi::rt::IPipeline  l  nvrhi::CommandListHandle # @$  nvrhi::DrawIndirectArguments ! 臨  nvrhi::TextureTilesMapping  E  nvrhi::HeapDesc  �&  nvrhi::ICommandList  �  nvrhi::BufferDesc  (  nvrhi::IDescriptorTable * 絉  nvrhi::RefCountPtr<nvrhi::ITexture>  C  nvrhi::BlendOp  V$  nvrhi::ComputeState  �#  nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc    nvrhi::Viewport  �!  nvrhi::RenderState  f"  nvrhi::ShaderHandle  �  nvrhi::ITexture  pR  nvrhi::ITimerQuery & 鎗  ImVector<ImFontAtlasCustomRect>     LONG    ITypeLib    tagCACY  z�  tagBSTRBLOB  x�  tagCAUH  鵹  _TP_CALLBACK_ENVIRON_V3 0 w  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> B w  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s>  爢  _ULARGE_INTEGER ( u�  _ULARGE_INTEGER::<unnamed-type-u>  �-  __std_win_error  攚  LPVARIANT  麉  SAFEARRAY  �.  lconv  �  D3D_SRV_DIMENSION  p�  tagCABOOL   俀  __RTTIBaseClassDescriptor  8�  D3D12_SHADER_CACHE_MODE  >k  ImVector<float>  槅  tagBLOB 
 p�  CABOOL   }�  D3D12_ROOT_PARAMETER_TYPE 
    _off_t  #   ULONG64 
 �  SNB  儏  _tagINTERNETFEATURELIST  m�  CABSTRBLOB 
 #   SIZE_T  j�  tagTYPEATTR  �  stat  t   ImFontAtlasFlags  <t  ImGuiComboFlags_  t   int32_t    timespec  z7  __std_fs_file_id 
 !   _ino_t  [t  ImGuiTabBarFlags_  A   DATE # \�  ReplacesCorHdrNumericDefines  Ow  FS_BPIO_OUTFLAGS  :t  ImGuiColorEditFlags_  "   DWORD  駐  PTP_CALLBACK_INSTANCE 
   PSHORT ' c7  __std_fs_create_directory_result  T�  D3D12_MESSAGE_ID  "   TP_VERSION  q  BSTR  o�  D3D_DRIVER_TYPE  !   uint16_t  t   ImGuiViewportFlags  �-  __std_fs_stats  鏵  ImVector<char>  O�  CAUB  P�  ITypeInfo $ �?  donut::engine::ICompositeView  �?  donut::engine::IView   	@  donut::engine::PlanarView & 巪  donut::app::StreamlineInterface 6 �  donut::app::StreamlineInterface::DLSSRRSettings 5  �  donut::app::StreamlineInterface::DLSSRROptions A i|  donut::app::StreamlineInterface::DLSSRRNormalRoughnessMode 4 g|  donut::app::StreamlineInterface::DLSSRRPreset 2 鼏  donut::app::StreamlineInterface::DLSSGState 3 ]|  donut::app::StreamlineInterface::DLSSGStatus 4 鴨  donut::app::StreamlineInterface::DLSSGOptions A Z|  donut::app::StreamlineInterface::DLSSGQueueParallelismMode 2 X|  donut::app::StreamlineInterface::DLSSGFlags 1 V|  donut::app::StreamlineInterface::DLSSGMode 3 魡  donut::app::StreamlineInterface::ReflexState 4 飭  donut::app::StreamlineInterface::ReflexReport 5 雴  donut::app::StreamlineInterface::ReflexOptions 2 I|  donut::app::StreamlineInterface::ReflexMode 6 鐔  donut::app::StreamlineInterface::DeepDVCOptions 3 @|  donut::app::StreamlineInterface::DeepDVCMode 2 銌  donut::app::StreamlineInterface::NISOptions . 9|  donut::app::StreamlineInterface::NISHDR / 7|  donut::app::StreamlineInterface::NISMode 4 邌  donut::app::StreamlineInterface::DLSSSettings 3 蹎  donut::app::StreamlineInterface::DLSSOptions 2 '|  donut::app::StreamlineInterface::DLSSPreset 0 %|  donut::app::StreamlineInterface::DLSSMode 1 讍  donut::app::StreamlineInterface::Constants . 訂  donut::app::StreamlineInterface::Extent  y  donut::app::IRenderPass   鈞  donut::app::DeviceManager 3 箏  donut::app::DeviceManager::PipelineCallbacks + 駒  donut::app::DeviceCreationParameters % 離  donut::app::InstanceParameters  沰  donut::app::ImGui_NVRHI ! 岈  donut::app::ImGui_Renderer ! 番  donut::app::RegisteredFont ' 諝  donut::vfs::enumerate_callback_t  帛  donut::vfs::Blob % k�  donut::vfs::RelativeFileSystem  �  donut::vfs::IBlob  N�  donut::vfs::IFileSystem  釧  donut::math::float4x4 " 9  donut::math::vector<bool,4>  �  donut::math::float3  X  donut::math::affine3  HA  donut::math::float2 # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane # �  donut::math::vector<float,4>    donut::math::frustum $    donut::math::frustum::Corners # �  donut::math::frustum::Planes  �  donut::math::float4 % 釧  donut::math::matrix<float,4,4> # X  donut::math::affine<float,3>   o8  donut::math::box<float,3> " �  donut::math::vector<bool,2>  o8  donut::math::box3 % �  donut::math::matrix<float,3,3> "   donut::math::vector<bool,3> # HA  donut::math::vector<float,2>  抴  tagPROPVARIANT    CAUL M ]  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>    CACY  �  _Mbstatet  爢  ULARGE_INTEGER  Wt  ImGuiButtonFlags_  鱲  TP_CALLBACK_PRIORITY  H  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; R  __vcrt_va_list_is_reference<__crt_locale_pointers *>  媴  VARENUM  C�  tagCASCODE     intmax_t # 釁  D3D_TESSELLATOR_PARTITIONING  Nk  ImGuiViewport  �  terminate_handler  観  _s__RTTIBaseClassArray  i�  tagCACLSID  w  MACHINE_ATTRIBUTES  Y�  D3D_RESOURCE_RETURN_TYPE  xj  ImFontAtlas 
   ldiv_t 0 塮  ImVector<ImGuiTextFilter::ImGuiTextRange>  渾  tagCALPWSTR  �-  __std_fs_file_flags  �.  _Cvtvec  !   ImDrawIdx  裬  ImGuiIO  槅  BLOB  #   DWORD64  t   ImDrawListFlags  !   PROPVAR_PAD1 - 烸  $_s__RTTIBaseClassArray$_extraBytes_24  魐  PTP_SIMPLE_CALLBACK  @�  D3D12_MESSAGE_CATEGORY 
 t   INT  綫  _CatchableTypeArray  枂  IStorage  Vk  ImGuiPlatformImeData  歸  tagVARIANT 
 E�  tagCAI 
 A   DOUBLE      UCHAR  $f  ImGuiPayload   [�  _D3D_SHADER_CBUFFER_FLAGS  "   LCID      BOOLEAN  鐅  PTP_CALLBACK_ENVIRON  �-  __std_fs_copy_options     ptrdiff_t  (�  tagTYSPEC  rw  LPVERSIONEDSTREAM  �  _stat64i32  J�  D3D12_LOGIC_OP  Y�  tagDISPPARAMS  廹  ImDrawCmd 
 !   USHORT  鬛  _PMD   i  ImVector<ImVec4>      uint8_t  漺  LPUWSTR  秇  ImVector<unsigned short>  q�  tagVARKIND     type_info  Ut  ImFontGlyph    PVOID  W�  SAFEARRAYBOUND ' 嶲  _s__RTTIClassHierarchyDescriptor  ,{  IUnknown  t   errno_t  q   WCHAR     PBYTE  �  D3D_TESSELLATOR_DOMAIN  t   ImGuiWindowFlags  �-  __std_fs_reparse_tag  ?w  _DEVICE_DSM_DEFINITION 
 2�  tagCAC  O�  tagCAUB    _lldiv_t 
   IID 
 騟  ImVec4 ! 虆  _D3D_SHADER_VARIABLE_FLAGS  Pt  ImGuiCol_  厖  _tagQUERYOPTION  @t  ImGuiWindowFlags_  q  LPOLESTR  [�  D3D_PRIMITIVE  0�  tagExtentMode  :�  __MIDL_IUri_0002     HRESULT  H�  _D3D_SHADER_INPUT_TYPE 
 E�  CAI  7  __std_type_info_data  5w  PDEVICE_DSM_INPUT & 嘠  $_TypeDescriptor$_extraBytes_27  Bt  ImDrawFlags_  C�  CASCODE  �  _s__ThrowInfo  !.  __std_fs_convert_result / 鲄  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER ! B�  __MIDL_IGetBindHandle_0001  !   ImWchar16  �-  __std_fs_stats_flags  詤  tagCY 
    LONG64  乭  ImVector<ImDrawCmd>  噮  tagCOINITBASE    LPCUWSTR  "   ULONG  観  __RTTIBaseClassArray ! <�  D3D12_STATE_SUBOBJECT_TYPE  !   VARTYPE  t   BOOL 
 2�  CAC / Tg  ImVector<ImGuiStorage::ImGuiStoragePair>  �  __crt_locale_data_public  .�  tagApplicationType  j  ImFontGlyphRangesBuilder 0 Gw  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES_OUTPUT  杋  ImDrawVert  F  LPCWSTR & �  DISPLAYCONFIG_SCANLINE_ORDERING - 蔘  $_s__CatchableTypeArray$_extraBytes_24  @   FLOAT  !   PROPVAR_PAD3  ]�  tagDOMNodeType  ,�  CAUI  *�  tagCLIPDATA  Lt  ImGuiSelectableFlags_  麉  tagSAFEARRAY & 琎  $_TypeDescriptor$_extraBytes_25  "   DEVICE_DSM_ACTION % 嶲  __RTTIClassHierarchyDescriptor  �.  _Collvec   �6  __std_fs_volume_name_kind  $�  tagVersionedStream 
 "�  CABSTR     __time64_t  u�  tagCHANGEKIND 
 u   UINT32  U  FILE  謪  tagSYSKIND  鎖  ImVector<ImDrawList *>  u   ImGuiID  t   ImGuiBackendFlags 3 螿  __vcrt_va_list_is_reference<wchar_t const *>  �  IDispatch    CLSID  �  mbstate_t  �  _PMFN  #   uintptr_t 
 q  LPWSTR  抴  PROPVARIANT  tw  LPSAFEARRAY  #   UINT_PTR  >t  ImGuiTableColumnFlags_  関  PTP_POOL  綫  _s__CatchableTypeArray   f  ImGuiTableColumnSortSpecs  _7  __std_fs_remove_result    GUID * 鍁  _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG ' 鯀  D3D_TESSELLATOR_OUTPUT_PRIMITIVE # 髤  D3D12_INDIRECT_ARGUMENT_TYPE  菂  D3D12_COMMAND_LIST_TYPE  鵹  TP_CALLBACK_ENVIRON_V3  亝  tagFUNCKIND  t   ImGuiKeyChord  u   ImU32  Ht  ImGuiNavInput  媔  ImDrawCmdHeader  雲  LARGE_INTEGER 
 鋮  CAH  t   ImGuiChildFlags  Ft  ImGuiKeyData  t   INT32  邊  tagCAFILETIME 
   HANDLE  軈  D3D12_LIFETIME_STATE  U�  PIDMSI_STATUS_VALUE  趨  _D3D_CBUFFER_TYPE  #   ULONGLONG    ImTextureID  貐  tagCAPROPVARIANT ( 韛  PTP_CLEANUP_GROUP_CANCEL_CALLBACK  t   ImGuiSortDirection 	 詤  CY  覅  FILETIME  9w  PDEVICE_DSM_RANGE ( 螀  D3D12_DEBUG_DEVICE_PARAMETER_TYPE - 決  $_s__RTTIBaseClassArray$_extraBytes_16  蕝  __MIDL_IUri_0001  痠  ImDrawData 
 Sw  REGCLS  8t  ImVector<ImFontGlyph> -   $_s__RTTIBaseClassArray$_extraBytes_32  u   DXGI_USAGE  艆  IRecordInfo  t   ImGuiInputTextFlags 
 #   size_t  Ew  PDEVICE_DSM_OUTPUT 
    time_t  �-  __std_fs_file_attr     LONGLONG   枀  D3D12_MEASUREMENTS_ACTION  =  __std_exception_data * k�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE 
 u   _dev_t  �6  __std_ulong_and_error ) 7w  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES  t  ImGuiTableFlags_  搮  tagGLOBALOPT_EH_VALUES 
 鏴  ImVec2 * 鉽  PCUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG  Of  ImGuiTextFilter & 攆  ImGuiTextFilter::ImGuiTextRange    lldiv_t     SHORT  `g  ImGuiListClipper    PLONG64    _ldiv_t  Ww  COWAIT_FLAGS     SCODE  墔  tagCLSCTX  t  ImGuiPopupFlags_  遟  ImVector<ImDrawChannel>    _timespec64     intptr_t     INT_PTR  s�  _D3D_SHADER_INPUT_FLAGS   t  ImVector<ImFontConfig>  u   uint32_t  y�  tagXMLEMEM_TYPE " w�  D3D_REGISTER_COMPONENT_TYPE 
 U  _iobuf 
 m�  CADATE ! f  ImGuiInputTextCallbackData  p   CHAR  i�  CACLSID  !   PROPVAR_PAD2  e�  _tagPARSEACTION  c�  D3D12_MESSAGE_SEVERITY + a�  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE  p  LPSTR  Ri  ImVector<void *>  _�  tagDESCKIND  Q  __crt_locale_pointers 
 N�  tagCAL  #   DWORDLONG  �   0/      zY{���睃R焤�0聃
扨-瘜}  :    �2梉�7伆鼂:E囂!2Wｔ孵>羑�輡  �    擐�0阅累~-�X澐媆P 舋gD�  �    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�     鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  _   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �   泭盨p榩,^藎�髈V尦�懰?v��`  *   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  h   蓏翰bsF讍E熑j]�0萛b*xs*q翻惲�  �   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  �   �暊M茀嚆{�嬦0亊2�;i[C�/a\  .   副謐�斦=犻媨铩0
龉�3曃譹5D   p   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  5   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �    栀��綔&@�.�)�C�磍萘k  �   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�      d蜯�:＠T邱�"猊`�?d�B�#G騋  B    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  �   d2軇L沼vK凔J!女計j儨杹3膦���  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇     溶�$椉�
悇� 騐`菚y�0O腖悘T  o   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  �   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  	   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  B   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   �咹怓%旗t暐GL慚ヌ��\T鳃�  �   繃S,;fi@`騂廩k叉c.2狇x佚�  	   bRè1�5捘:.z錨{娯啹}坬麺P  V   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  �   6��7@L�.�梗�4�檕�!Q戸�$�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  9	   (鄁盯J錭澥A��/�!c� ;b卹  �	   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  �	   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  
   G髼*悭�2睆�侻皣軁舃裄樘珱)  k
   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �
   襋钡�/翤㏄^觜�藔膛vЮ7��37嵇  
   馒{蓱蕩L蘯e�LW�疗譄幗IR:;a�  Z   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �   禿辎31�;添谞擎�.H闄(岃黜��  �   戹�j-�99檽=�8熈讠鳖铮�  5   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   �7穲碶⒖鍉鸻�:怉婤莞b=竱�  
   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  Y
   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �
   ,�<鈬獿鍢憁�g$��8`�"�  �
   �-考X韵歎K�fUi2�'跎Zq@锍�2aJ�  4   8蟴B或绢溵9"C dD揭鞧Vm5TB�  �   f扥�,攇(�
}2�祛浧&Y�6橵�  �   曀"�H枩U传嫘�"繹q�>窃�8  �   嫎V濺蕠&kZ峠l甩u8蝬QW蹚z`k
+  F   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   鹰杩@坓!)IE搒�;puY�'i憷n!  �   Eム聂�
C�?潗'{胿D'x劵;釱�  4   [届T藎秏1潴�藠?鄧j穊亘^a  s   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L     揾配饬`vM|�%
犕�哝煹懿鏈椸  G   チ畴�
�&u?�#寷K�資 +限^塌>�j  {   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈     �0�*е彗9釗獳+U叅[4椪 P"��  <   0T砞獃钎藰�0逪喌I窐G(崹�  �   穫農�.伆l'h��37x,��
fO��  �   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  :   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  }   dhl12� 蒑�3L� q酺試\垉R^{i�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   齛|)3h�2%籨糜/N_燿C虺r_�9仌  G   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �   Y� 怶~静o膁5@@o灦o弢/��3銡爜�  �   鏀q�N�&}
;霂�#�0ncP抝  �   謷蘋愦	豢]麸Md�觙 G澿f1e
T奞~  ;   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  	   � 罟)M�:J榊?纸i�6R�CS�7膧俇  \   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  �   僘u鹋� !敒99DK汜簯�叮瀒蛂  �   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  7   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  �   W躊��:(蚏濠迤鵢僛L生N!g`璣{  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  <   `k�"�1�^�`�d�.	*貎e挖芺
脑�  ~   K�:荳)a懃J�拌� ,寨吙u⑺�  �   V� c鯐鄥杕me綻呥EG磷扂浝W)     !m�#~6蠗4璟飜陷]�絨案翈T3骮�  V   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  �   潝(綊r�*9�6}颞7V竅\剫�8値�#  �   傠3UX�<頹%O磳me鸘 涒雋xW5叒z迦  4   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  }   �l{`G2
`0m襭皗犿鷕g貋⒌joゥ  �   ^憖�眜蘓�y冊日/缁ta铁6殔     魯f�u覬n\��zx騖笹笾骊q*砎�,�  ]    萾箒�$.潆�j閖i转pf-�稃陞��  �   渐袿.@=4L笴速婒m瑜;_琲M %q�  �   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  U   L�9[皫zS�6;厝�楿绷]!��t  �   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   跻]�	隗[鼀hF鰘廰)鋥Le6�攬R7  /   iu�	��&? 蔬釥�#簤Ηu-塘b@遍迭  u   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/     语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  R   蕰уt厅隍2GeE升QU"柬�)忲嗅{≌洮  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��     峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  \   �>2
^�﨟2W酟傲X{b?荼猲�;  �   t	*=Pr,�8qQ镯椅鯘�=咽Bz  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  	    }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  L    B�睃娏�,棒繪�1舸麂筓1�0m蘉##  �    F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �    蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  
!   聤�苮g8鄞<aZ�%4)闪�|袉uh�  V!   �*o驑瓂a�(施眗9歐湬

�  �!   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �!   哳訵a醴泉撬4wO�)鹁疜�)誊)�=跹�  +"    I嘛襨签.濟;剕��7啧�)煇9触�.  k"   �(M↙溋�
q�2,緀!蝺屦碄F觡  �"   G�膢刉^O郀�/耦��萁n!鮋W VS  �"   �3掍S\絧飦戀轝
5鍄0d
妺XE��
  ?#   閯�価=�<酛皾u漑O�髦jx`-�4睲�  �#   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  �#   o�椨�4梠"愜��
}z�$ )鰭荅珽X  $   �="V�A�D熈fó 喦坭7b曉叼o1  X$   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  �$   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �$   c�#�'�縌殹龇D兺f�$x�;]糺z�  )%   v-�+鑟臻U裦@驍�0屽锯
砝簠@  d%   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �%   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �%   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  -&   r�L剟FsS鏴醼+E千I呯贄0鬬/�  x&   o藾錚\F鄦泭|嚎醖b&惰�_槮  �&   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  '   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  N'   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏  �'   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �'   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  .(   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�  z(   5�\營	6}朖晧�-w氌rJ籠騳榈  �(   豊+�丟uJo6粑'@棚荶v�g毩笨C  )   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  ?)   �
bH<j峪w�/&d[荨?躹耯=�  ~)   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �)   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �)   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  B*   ��8/�
0躚/﨤h盙裉餠G怤爛��]�  �*   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �*   澏泼寊H"鱄LUHFy]厽I媢h�J赫Cp   +   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  g+   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �+   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �+   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  I,   �:2K] �
j�苊赁e�
湿�3k椨�  �,   樸7 忁�珨��3]"Fキ�:�,郩�  �,   覽s鴧罪}�'v,�*!�
9E汲褑g;  (-   鹴y�	宯N卮洗袾uG6E灊搠d�  p-   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �-   犢x琿儳莻k鬿锘HC閷�!(/捱&eM殙�  .    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  ].   掴'圭,@H4sS裬�!泉:莠й�"fE)  �.   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �.   <瑓�傻a鱼张隥T$ vJ黠I鯝�0  F/   �茬� 霚FmH暃蜽.駢宫誖�6?靵oP  �/   U恂{榸冾�fⅢ��Hb釃"�6e`a  �/   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  0   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  a0   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �0   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �0   �fE液}髢V壥~�?"浬�^PEΡ4L�  C1   嶹栢ABZC凂U久Gk�!貟~龡单癉Q  �1   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  �1   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  /2   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  x2   葦鮆培XDW駐鰄�W{�+ノ2$<F$帰B  �2   t$�>喰k柸窣鄾埉笺%撙>2 pg�,  	3   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  R3   妇舠幸佦郒]泙茸餈u)	�位剎  �3   8�'预P�憖�0R�(3銖� pN*�  �3   sL&%�znOdz垗�M,�:吶1B滖  ,4   靋!揕�H|}��婡欏B箜围紑^@�銵  l4   �颠喲津,嗆y�%\峤'找_廔�Z+�  �4   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �4   t�j噾捴忊��
敟秊�
渷lH�#  25   v�%啧4壽/�.A腔$矜!洎\,Jr敎  |5   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �5   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  
6   D���0�郋鬔G5啚髡J竆)俻w��  \6   �"睱建Bi圀対隤v��cB�'窘�n  �6   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  7   *u\{┞稦�3壅阱\繺ěk�6U�  @7   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �7   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �7   �-�雧n�5L屯�:I硾�鮎访~(梱  8   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  _8   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �8   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �8   .�-髳�o2o~翵4D�8鷗a殔氰3籃G  C9   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  �9   _%1糠7硘籺蚻q5饶昈v纪嗈�  �9   丩{F*}皦N誫l雘啫椊�梮,圶`�  #:   �n儹`
舔�	Y氀�:b
#p:  v:   "�挨	b�'+舒�5<O�呱_歲+/�P�?  �:   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  	;   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  S;   
捃閺嚞?� 龀�*�煾/踈0�R璷�  �;   窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  �;   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   K<   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �<    狾闘�	C縟�&9N�┲蘻c蟝2  �<   �'稌� 变邯D)\欅)	@'1:A:熾/�  =   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  ^=   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �=   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �=   衠琪槡铟钭}_XO>�蛭X�7Mp处d  />   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  |>   �~鴧傳.P怬WsP-"焫#N�:�&場璁  �>   �fwv鋽砻毆�經�⒂k秼芴襚扉w  
?   k�8.s��鉁�-[粽I*1O鲠-8H� U  O?   伙a蕑堐v!@曓�+梭�'驂蒀飫鴐  �?   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  �?   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  #@   交�,�;+愱`�3p炛秓ee td�	^,  d@   _O縋[HU-銌�鼪根�鲋薺篮�j��  瑻   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  魼   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  5A   _臒~I��歌�0蘏嘺QU5<蝪祰S  zA   l籴靈LN~噾2u�< 嵓9z0iv&jザ  藺   駒og暼硚~鮼_5屏�8BL.9妖"�r/�-R  B   5睔`&N_鏃|�<�$�獖�!銸]}"  `B   ┫緞A$窄�0� NG�%+�*�
!7�=b  疊   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�   C   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  XC   $^IXV嫓進OI蔁
�;T6T@佮m琦�  廋   b骺_�(4参♁� N�z陾Ia癓t�&醇  贑   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  &D   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  yD   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  蠨   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  E   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  aE   郖�Χ葦'S詍7,U若眤�M进`  睧   頒牛/�	� G犨韈圂J�.山o楾鐴  麰   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  LF   喲8s7痺撆�&!林qA>ブ�Y~a璳`N3�  朏   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  隖   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  7G   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  G   +椬恡�
	#G許�/G候Mc�蜀煟-  縂   qAp�6敁p銋�,c .諵輕底髫L灇	9�  H   ��(`.巑QEo"焷�"娧汝l毮89fб�  UH   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱     6觏v畿S倂9紵"�%��;_%z︹  镠   �8��/X昋旒�.胱#h=J"髈篒go#  5I   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  ~I   �儔14褥緅�3]饃鹷�hK3g搋bA竑  蔍   綔)\�谑U⒊磒'�!W磼B0锶!;  J   E縄�7�g虩狱呂�/y蛨惏l斋�笵  eJ   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  睯   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  騄   ��嵉氒髅嘁棭够*ヅ�
�'徺p4  5K   �呾��+h7晃O枖��*谵|羓嗡捬  }K   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  籏   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  L   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  @L   2W瓓�<X	綧]�龐IE?'笼t唰��  嶭   +4[(広
倬禼�溞K^洞齹誇*f�5  鞮    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  +M   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  cM   D,y邥鞃黎v)�8%遾1�*8赩�婯�  狹   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  驧   +FK茂c�G1灈�7ほ��F�鳺彷餃�  $N   蜅�萷l�/费�	廵崹
T,W�&連芿  aN   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  ㎞   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  鸑   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  =O   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  vO   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  礝   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  鶲   蠯3
掽K謈 � l�6襕鞜��H#�  7P   �	玮媔=zY沚�c簐P`尚足,\�>:O  xP   匐衏�$=�"�3�a旬SY�
乢�骣�  翽   �F9�6K�v�/亅S诵]t婻F廤2惶I  Q   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  MQ   	{Z�范�F�m猉	痹缠!囃ZtK�T�  孮   悯R痱v 瓩愿碀"禰J5�>xF痧  貿   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  $R   猯�諽!~�:gn菾�]騈购����'  `R   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  烺   矨�陘�2{WV�y紥*f�u龘��  鍾   ﹪冓�嗈�1蓌�)eF�;珉5� �橌5偧  -S   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  mS   錵s铿勃砓b棬偡遯鮓尛�9泂惻  窼   $G\|R_熖泤煡4勄颧绖�?(�~�:  �   �      �  �  B   �  �  H   �  �  Y   �  �  �   �  �-  U   �  �-  �   �    �  �    �  �    �  �    x  �  �   �   �  �   �   �  �   �   �  �   �   F  �   �   G  �   �   �  �-  �   �  �  �   ,  �-  @   -  �-  �   H  �-  @   �  �-  �      �    ^   �   �   d     �  n   �   �   r   �   �   u   �   �   x   �   �   |   �   �   �   �   �   �     t  �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   !  .  %   	!  �   �   C!  x-    Q!  x-    R!  x-  
  U!  �-  �  V!  �-  �  r!  �-  �  z!  �-  �  |!  8  �  }!  .  C  ~!  .  <  �!  �-  F  �!  �-  �  �!  �-  �  �!  �-  �  �!  �-  R  �!  .  3  �!  �-  @   �!  �-  �  :(  �   �   B)  �   �   /,  p
  �  0,  p
  �  �.  p
    �.  �  X   �.  �  _   �.  �  g   �.       �.    5  �.       �.    5  �.  �  A   �.  �  l  �.    �  �.    Z  �.    �  �.    �  �.    d  �.    Z  �.  �
  f  �.  �
  `  �.    �  �.    �  �.    �  �.    �  �.    Z  �.    5  �.    t  �.    �  �.    t  �.  �
  �  �.    t  �.    z  �.  �-  �   /  �
  �  /  x-  >  /  �
  �  /  �
  �  /  x-  4  /  .  �  /    �  /    �
  
/    �
  /    �  /    �
  /    �
  /    �
  /    D  /    :  /    D  /  �
    /  �-  �  /    D  (/  �  �   )/       */    5  2/    n  3/    n  5/  �
  �  7/    �  8/    �  9/    n  :/  �
  `  ;/  �
  `  </  �,  j   =/  �   �   >/  �   �   ?/  �   �   @/  �   �   A/   
  5   B/  �   �   C/  �   �   I/  x-  u  P/  .  "  Q/  �,  1   R/  .  '  V/  �-  �  W/    &  Y/    9  Z/    �  \/    &  _/    &  b/    &  d/    9  f/    &  h/  �
    l/  �-  �  n/  �-  /  o/  �-  /  q/    :  r/  �  �  s/  �  �  t/  �-  �  u/  �-  �  w/  x-    x/  �-  �  z/  �-  �  �/  �
  �  �/  �
  �  �/  �
  �  �/  �
  �  �/  .  a  �/  �
  ]  �/  �-  �  �/  8  �   �/  �-  �  �/  8  �   �/  8  �   �/  8  �   �/  8  �   �/  8  �   �/  �-  �  �/  �-  �  �/  �-  �  �/  �-    �/  �-  �  �/  �-  �  �/  �
  �  �/    _  �/  �-    �/    D  �/  �-  �  �/    n  �   T   D:\RTXPT\External\Donut\include\donut\core\math\sphere.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\cderr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dde.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h D:\RTXPT\External\Donut\include\donut\app\StreamlineInterface.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ddeml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\vfs\VFS.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\inaddr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgitype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleauto.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\commdlg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\src\app\imgui_renderer.cpp D:\RTXPT\External\Donut\include\donut\core\math\affine.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winsmcrd.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\RTXPT\External\Donut\include\donut\app\imgui_renderer.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h D:\RTXPT\External\Donut\include\donut\app\DeviceManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\DXGI.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\shellapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsock.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\nb30.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\include\donut\app\imgui_nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\include\donut\core\math\box.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\RTXPT\External\Donut\thirdparty\imgui\imgui.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\RTXPT\External\Donut\thirdparty\imgui\imconfig.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\joystickapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winperf.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dlgs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winioctl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ncrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mciapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\lzexpand.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ole2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winspool.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\prsht.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\playsoundapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3d12sdklayers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\bcrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3dcommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winscard.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsystem.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsyscom.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dpapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h   �       L�/  W  �   [  �  
 �  �   �  �  
 �3      �3     
 �4      �4     
    f i吓 �6qN櫿_7镾%   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_app.pdb 谐Y>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >e   _Bytes  AJ        9  $  >e    _Block_size  AH       1 
   >e    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        �  
 Z      N Z   �  �   (                      H 
 h   �         $LN14  0   e  O_Bytes  O   �   h           :   �-  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   V   0   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
   V     V  
 s  �   w  �  
 �  V   �  V  
 H;蕋kH塼$WH冹 H嬺H塡$0H孂fD  H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;縃媆$0H媡$8H兡 _�   �   �  x G            q      q   #/        �std::_Destroy_range<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >  >�   _First  AJ          AM       V  AJ p       >   _Last  AK          AL       Y  AK p       >原   _Al  AP           AP       Q     D@    M        l/  8  M        �.  8  M        �.   /	 M        �  )/
 >�   this  AI  $     B  AI         M        �  @	 N N N N N                       @� " h   �  �  �.  �.  j/  l/  |/   0   �  O_First  8     O_Last  @   原  O_Al  9>       �   9U       �   O�   P           q   �-     D       > �    B �   > �   B �    C �X   B �f   F �,   F   0   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 �   F     F  
   F   !  F  
 -  F   1  F  
 �  F   �  F  
 �  F   �  F  
 |  F   �  F  
 �  F   �  F  
 �  F   �  F  
 L塂$H塋$SVWATAUAVAWH冹0L嬯H孂H�L嬧L+郘媦L+鳬�I�������M;�剹  I�荋婭H+菻六H嬔H殃I嬃H+翲;�噉  H�
M嬿I;荓C餗;�嘩  I嬾H伶L塼$xH侢   r1H峃'H;��5  �    H吚�3  H峏'H冦郒塁鳯媱$�   �!H咑tH嬑�    H嬝L媱$�   �3跮塼$xH墱$�   I冧餗�4M峟L塪$(I�    I荈    I婡H吚t�@I� I�I婡I塅L塼$ H媁H�L;陁L嬨�L嬒L嬅I嬚�    H塡$ I嬐H媁L嬒M嬆�    怘�H吷t@L嬊H媁�    H�H媁H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I羚L鸏�H�H塐I嬈H兡0A_A^A]A\_^[描    惕    惕    蹋   �   �   �   J  U   a  U   v  F   �  �   �  �   �  *   �  �      �   �  � G            �     �  i/        �std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::_Emplace_reallocate<std::shared_ptr<donut::app::RegisteredFont> const &> 
 >|�   this  AJ          AM       ��  Dp    >   _Whereptr  AK          AU       ��  >   <_Val_0>  AP        ��  �  F� �  AP Z      D�    >#     _Newcapacity  AV  p     �  AV �        Bx   �     ]Q  >e    _Newsize  AW  I     �l" �  >e    _Whereoff  AT  %       >    _Constructed_last  AV  �     	  D(    >e    _Oldsize  AW  ,     �   � >�    _Constructed_first  D     >    _Newvec  AI  �      
   AI �     � 
  B�   �     � �   M        �/  um乯 M        �/  um乯& M        -  ��)
1%��( M        �  ��$	%)
�
 Z   �   >e    _Block_size  AJ  �       AJ �      >e    _Ptr_container  AH  �       AH �     , � 
 >�    _Ptr  AI  �       AI �     � 
  M        �  ��
 Z      N N M        �  ��
 Z      N N M        ,  
m
 N N N M        �/  Ik >e    _Oldcapacity  AJ  M     �   L - �   AJ �     � T �  >e    _Geometric  AH  m     u :  f 
  AH �     � 1 �  M        �/  I N N M        �/  *�  M        �.  �  M        /  �M M        3/  �	 M        �  � N N N M        �.  ��  N N N' M        �/  乫(L4#'
 Z   #/   M        �.  *亜_ M        �  亪):
 Z   !  
 >   _Ptr  AJ �      >#    _Bytes  AK  �    -    AK �     % M        �  亼d#
=
 Z   �   >e    _Ptr_container  AP  �      AP �    ?  5  >e    _Back_shift  AJ  }    ,  AJ �    ?  5  N N N N Z   �/  �/  �/   0           8         0@ f h   �  �  �  �  �  �  ,  -  �  �.  �.  �.  �.  /  3/  g/  j/  �/  �/  �/  �/  �/  �/  �/         $LN84  p   |�  Othis  x     O_Whereptr  �     O<_Val_0>  (     O_Constructed_last      �  O_Constructed_first  O   �   �           �  �
     �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   > �   B �*  C �/  E �;  G �>  K �@  L �N  M �S  N �f  V ��  W ��  X ��  = ��  7 ��  V ��   (  � F            =      =             �`std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::_Emplace_reallocate<std::shared_ptr<donut::app::RegisteredFont> const &>'::`1'::catch$0 
 >|�   this  EN  p         =  >   <_Val_0>  EN  �         =  Z   #/  �.   (                    �        __catch$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z$0        $LN84  p   |�  Nthis  x     N_Whereptr  �     N<_Val_0>  (     N_Constructed_last      �  N_Constructed_first  O�   8           =   �
     ,       P �   Q �   R �3   S �,   S   0   S  
   S     S  
   S     S  
 K  S   O  S  
 [  S   _  S  
 �  S   �  S  
 �  S   �  S  
 �  S   �  S  
 �  S   �  S  
 �  S   �  S  
   S      S  
 H  S   L  S  
 t  S   x  S  
 �  S   �  S  
 �  S   �  S  
   S     S  
   S     S  
 �  S   �  S  
 �  S   �  S  
 	  S   
  S  
   S     S  
 <  S   @  S  
 L  S   P  S  
   S     S  
 '  S   +  S  
 P  S   T  S  
 h  S   l  S  
 �  S   �  S  
 �  S   �  S  
   S   	  S  
 a  S   e  S  
 q  S   u  S  
 �  S   �  S  
 �  S   �  S  
 e  �   i  �  
 	  S   	  S  
 �	  W   �	  W  
 �
  W   �
  W  
   W     W  
 H  �   L  �  
 h  �   l  �  
 
  W   
  W  
 H塗$SUH冹(H嬯L婨pH婾(H婱 �    L婨xH嫊�   H婱p�    3�3设    �   F   /   (   8   w   @SH冹 I嬝H;蕋?H+薊3�@ �     L�L塁H�H�H婦H塁L�L塂H兠H�H;聈覯嬃H嬘H嬎�    H嬅H兡 [肳   F      �     � G            d      ^   �/        �std::_Uninitialized_move<std::shared_ptr<donut::app::RegisteredFont> *,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >  >   _First  AJ          AJ M     	  >   _Last  AK        S  >�   _Dest  AP          AP M       >原   _Al  AQ        [  >敯   _Backout  CI     D     	  CI          C $ 	  M        �/   N M        �/  M
 Z   #/   N M        �/     M        �/     M        �.    M        /  ''I N M        �.  �  N N N N                       @ : h
   �  �.  �.  /  /  j/  �/  �/  �/  �/  �/  �/  �/   0     O_First  8     O_Last  @   �  O_Dest  H   原  O_Al  O�   P           d   �-     D       � �	   � �   � �    � �D   � �M   � �^   � �,   U   0   U  
 �   U   �   U  
 �   U   �   U  
   U     U  
 "  U   &  U  
 2  U   6  U  
 P  U   T  U  
 w  U   {  U  
 �  U   �  U  
   U     U  
 H塡$WH冹0H嬞3�峅0�    H塂$@H嬓H吚t3W� 茾   茾   H�    H�H墇H墇f墇 墇$H墇(�H嬜H岯H塖H�H嬅H媆$HH兡0_�   �   ;   �      �   �  R G            s   
   h   /        �std::make_shared<donut::app::RegisteredFont>  >i�    _Rx  AK       T  B@        W  M        Y/  ^ N M        \/  ' M        �  	* N M        �/  B M        �.  Bx M        �.  B M        �.  �B N N N N N
 Z      0                     @ 2 h   �  �  �.  �.  �.  �.  �.  X/  Y/  \/  �/   @   �  O_Ret  ^      h�   O �   @           s        4       �
 �   �
 �Z   �
 �e   �
 �h   �
 �,   D   0   D  
 v   D   z   D  
 �   D   �   D  
 �  D   �  D  
 �  D   �  D  
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  mG                       z!        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >╭   _First  AJ          AJ       
   >╭   _Last  AK          
 >瀜   _Val  AP           >蓃   _Backout  CJ            CJ          
   M        �!    N M        �!   N                        H & h   {!  �!  �!  �!  �!  �!  �!  �!      ╭  O_First     ╭  O_Last     瀜  O_Val  O�   H               �-     <       � �    � �   � �   � �   � �   � �,   T   0   T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
   T     T  
 "  T   &  T  
 �  T   �  T  
 H�    H嬃�   �   �   s G                   
   �         �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >�"   this  AJ                                 H�     �"  Othis  O   �   0              �      $       �  �    �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塋$SUVWAVH冹0H嬹3鞨�)H塱H塱H塱H塱 H塱(H塱0H塱8H塱@H塱HH峺PH墊$h�塷H塷H塷H塷H塷 H塷(H塷0H峅8�    壇�   H伹�   H墊$pW�3�GH塆 H�    H塂$ L�
    峌D岴H嬒�    怘塷(H壆P  L嵍X  L塼$hA�.I塶I塶峂 �    H� H堾I塅I塶I塶 I塶(I荈0   I荈8   A�  �?I媈嬐嬇H柳H凐su箑   �    H孁I婲I婩(H+罤柳H吚t.H��    H侜   rH兟'L婣鳬+菻岮鳫凐w{I嬋�    I墌H崌�   I塅 I塅(H;鴗#H�H兦H;鴘綦H兞H灵H吷t3�H嬅驢獺壆�  H壆�  H壆�  H壆�  H壆�  H壆�  H嬈H兡0A^_^][描    恎   �   �   �   �   �   �   �   �   �      �   e  �   �  �      �   �	  J G            �     �  /        �donut::app::ImGui_NVRHI::ImGui_NVRHI 
 >{k   this  AJ          AL       ��  D`    M        :/  伖 M        t/  伖 M        �/  伖 N N N M        ;/  仱 M        u/  仱 M        �/  仱 N N N M        </  �輤��
 >宮   this  AV  �       Bh   �     ! M        I/  ��*H����7 M        C!  �h&M/E.$'$$M >e   _Oldsize  AH      �  k  AH �    -  C             >╭    _Newend  AH  t      AH �    -  >e    _Oldcapacity  AH  /    ,    AH d      >╭    _Newvec  AM  '    � Z [  AM �    W    M        R!  � N M        Q!  �' N M        U!  
� M        -  
� M        �  
�
 Z      N N N M        z!  亅#" >蓃   _Backout  CM     �      CM    �    W    M        �!  亅 N M        �!  亖 N N M        V!  .�;�� M        �  丆)x
 Z   !  
 >   _Ptr  AJ d      >#    _Bytes  AK  C    � & s % M        �  丩d#
{
 Z   �   >e    _Ptr_container  AP  T    �  x  AP d      >e    _Back_shift  AJ  +    � 9 x  AJ d    x +   N N N M        |!  .仐 N N M        w/  �� M        �/  �� M        �/  �� N N N M        P/  �� M        R/  ��(# >m    _Newhead  AH  �     6  M        �!  �� M        -  �� M        �  ��
 Z      N N N N M        z/  �� M        �/  �� N N N M        Q/  �� N N N M        =/  �� N M        A/  
��0
 >�"   this  AM  x       Bp   }     e N M        B/  ^ N M        B/  Z N M        B/  V N M        B/  R N M        B/  N N M        C/  J N M        �   7 N M        >/  3 N M        >/  / N M        ?/  + N M        B)  ' N M        C/  # N M        B/   N M        B/   N M        @/   N M        :(   N 0           (         0@ 2hK   �  �  �  �  �  �  �  �  -  H  �  U   �   �   !  (!  C!  P!  Q!  R!  U!  V!  Y!  \!  y!  z!  {!  |!  �!  �!  �!  �!  �!  �!  �!  �!  �!  :(  B)  {+  /  :/  ;/  </  =/  >/  ?/  @/  A/  B/  C/  I/  J/  K/  O/  P/  Q/  R/  t/  u/  w/  x/  z/  {/  �/  �/  �/  �/  �/  �/  �/  �/  �/  �/  �/         $LN226  `   {k  Othis  O �   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$0 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$1 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$2 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$3 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$4 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$5 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$6 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$7 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$8 
 >{k   this  EN  `                                  �  O�   �   Y F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$9 
 >{k   this  EN  `                                  �  O�   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$15 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$16 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$17 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$18 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$19 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$20 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$10 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$11 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$25 
 >{k   this  EN  `                                  �  O   �   �   Z F                                �`donut::app::ImGui_NVRHI::ImGui_NVRHI'::`1'::dtor$26 
 >{k   this  EN  `                                  �  O   ,   "   0   "  
 o   "   s   "  
    "   �   "  
 S  "   W  "  
 c  "   g  "  
 �  "   �  "  
 �  "   �  "  
 
  "     "  
 ,  "   0  "  
 <  "   @  "  
 c  "   g  "  
 w  "   {  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 <  "   @  "  
 ]  "   a  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
   "     "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �	  �   �	  �  
 0
  X   4
  X  
 �
  X   �
  X  
 �
  c   �
  c  
 .  c   2  c  
 �  i   �  i  
 �  i   �  i  
 (  n   ,  n  
 ~  n   �  n  
 �  q   �  q  
 &
  q   *
  q  
 x
  r   |
  r  
 �
  r   �
  r  
    s   $  s  
 v  s   z  s  
 �  t   �  t  
   t   "  t  
 p  u   t  u  
 �  u   �  u  
   v     v  
 n  v   r  v  
 �  ^   �  ^  
   ^     ^  
 l  _   p  _  
 �  _   �  _  
   `     `  
 o  `   s  `  
 �  a   �  a  
   a     a  
 p  b   t  b  
 �  b   �  b  
   f      f  
 s  f   w  f  
 �  \   �  \  
   \   #  \  
 t  ]   x  ]  
 �  ]   �  ]  
    g   $  g  
 w  g   {  g  
 �  h   �  h  
 #  h   '  h  
 H媻`   �          H媻`   H兞�          H媻`   H兞�       �   H媻`   H兞�       �   H媻`   H兞 �       �   H媻`   H兞(�          H媻`   H兞0�          H媻`   H兞8�          H媻`   H兞@�          H媻`   H兞H�       �   H媻`   H兞P�       �   H媻`   H伭P  �          H媻h   H兞�       �   H媻h   H兞�       �   H媻h   H兞�       �   H媻h   H兞 �       �   H媻h   H兞(�       �   H媻h   H兞0�       �   H媻h   H兞�          H媻h   H兞�          H塡$H塋$UVWAVAWH冹 H嬟L孂3鞨塓H�    H�H塱3纅堿圓H兞3褹竇  �    M嵎x  I�.I塶I塶I嵖�  H�/H塷H嬎�    禜LA垙�  A埊�  3设    峂0�    H嬋H塂$XH吚t9W� 茾   茾   H�    H�H塱H塱f茿  茿$  PAH塱(�H嬐H岮H�H媉H塐H呟t,����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PI媀I;Vt)H�*H塲H婫H吚t�@H�H�H婫H塀I僃�L嬊I嬑�    怚嬊H媆$`H兡 A_A^_^]�$   �   E   |   m   �   �   �   �   �   �   �   U  S      �   8  P G            n     ]  �.        �donut::app::ImGui_Renderer::ImGui_Renderer 
 >努   this  AJ          AW       M DP    >晉   devManager  AI       �  AK          M        �.  >� M        /  
�*)
 Z   i/   M        h/  �%" M        �/  "�% M        �.  �% M        /  �,M M        3/  �,	 M        �  �5 N N N M        �.  ��% N N N N N N M        �.  <�� M        �.  .�� M        �.  ��, M        �  �� M        �  �	
 N N N N M        �.  �� M        �.  �� M        �  �� N M        (/  �� N N N N M        /  ��9
 Z      >i�    _Rx  AJ  �     q  AJ     S 9   BX   �     �  M        W/  �� M        �  	�� N M        �/  �� M        �.  ��xF! M        �.  �� M        �.  ��� N N N N N N M        �.  b
 >   this  AM  b     	 M        �.  �b N N M        �.  P
 >|�   this  AV  P      M        /  P M        5/  P N N N M        /  + M        V/  + N N M        /,   N Z   $  �/               (         @ � h.   �  �  �  �  �  �  �  �  "  /,  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  /  /  /  /  /  /  /  (/  3/  5/  6/  V/  W/  X/  Y/  g/  h/  m/  /  �/  �/  �/   P   努  Othis  X   晉  OdevManager  ^�      h�   9      �   9      �   O�   x           n  �     ,       9  �   7  �!   9  �i   8  ��        |   �  ��     ,   �   :  ��   <  �  =  �Z  >  ��   �   _ F                                �`donut::app::ImGui_Renderer::ImGui_Renderer'::`1'::dtor$0 
 >努   this  EN  P                                  �  O  �   �   _ F                                �`donut::app::ImGui_Renderer::ImGui_Renderer'::`1'::dtor$1 
 >努   this  EN  P                                  �  O  �   �   _ F                                �`donut::app::ImGui_Renderer::ImGui_Renderer'::`1'::dtor$2 
 >努   this  EN  P                                  �  O  ,   ,   0   ,  
 u   ,   y   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
   ,      ,  
 ,  ,   0  ,  
 @  ,   D  ,  
 !  ,   %  ,  
 u  ,   y  ,  
   ,     ,  
 $  ,   (  ,  
 4  ,   8  ,  
 L  ,   P  ,  
 �  Y   �  Y  
 H  Y   L  Y  
 �  d   �  d  
 �  d   �  d  
 L  j   P  j  
 �  j   �  j  
 H媻P   H兞�       '   H媻P   H伭x  �       )   H媻P   H伭�  �       +   � 3狼AW狼A W汕A茿 茿茿 茿茿  茿$茿( 茿,茿0 茿4茿8 茿<茿@ H堿\H堿d茿D �� 茿H 茿L艫P茿T    f茿X  艫Z AlI|墎�   H嬃�   �   �   E G            �       �   @         �nvrhi::RenderState::RenderState 
 >�!   this  AJ        �                         @ " h   �  ;   <   =   >   ?   A       �!  Othis  O ,   �   0   �  
 j   �   n   �  
 � H嬃茿�   �   �   S G                      <         �nvrhi::BlendState::RenderTarget::RenderTarget 
 >?   this  AJ                                 H�     ?  Othis  O   ,   �   0   �  
 x   �   |   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >e   this  AI  	     2  AJ        	  >j   __that  AH         AK          M        �  :$
 Z   Z   N                       H� 
 h   �   0   e  Othis  8   j  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >|   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   Z   N                       @�  h   �  �   0   |  Othis  8   �  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >|   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      |  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �      �   �   ? G            2      ,   �        �std::exception::exception 
 >!   this  AI  	     (  AJ        	  >%   _Other  AH         AK         
 Z   Z                         H�  0   !  Othis  8   %  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >�"   this  AH         AJ          AH        M        �  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �"  Othis  9       �   O�   0           "   �      $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 P  �   T  �  
 h  �   l  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         r         �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >0S   this  AH         AJ          AH        M        �   GCE
 >R    temp  AJ  
       AJ        N (                     0H� 
 h   �    0   0S  Othis  9       �   O  �   0           "   �      $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 B     F    
 \     `    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         |         �nvrhi::RefCountPtr<nvrhi::ICommandList>::~RefCountPtr<nvrhi::ICommandList> 
 >辦   this  AH         AJ          AH        M        �   GCE
 >�&    temp  AJ  
       AJ        N (                     0H� 
 h   �    0   辦  Othis  9       �   O�   0           "   �      $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
 L     P    
 d     h    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �         �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >嘢   this  AH         AJ          AH        M        	!  GCE
 >�&    temp  AJ  
       AJ        N (                     0H� 
 h   	!   0   嘢  Othis  9       �   O  �   0           "   �      $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 B     F    
 \     `    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   Z  z G            "         n         �nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>::~RefCountPtr<nvrhi::IGraphicsPipeline> 
 >觢   this  AH         AJ          AH        M        �   GCE
 >^#    temp  AJ  
       AJ        N (                     0H� 
 h   �    0   觢  Othis  9       �   O  �   0           "   �      $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 	     
    
 V     Z    
 p     t    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �        �nvrhi::RefCountPtr<nvrhi::IInputLayout>::~RefCountPtr<nvrhi::IInputLayout> 
 >"   this  AH         AJ          AH        M        G  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   G   0   "  Othis  9       �   O�   0           "   �      $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 L  �   P  �  
 d  �   h  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         u         �nvrhi::RefCountPtr<nvrhi::ISampler>::~RefCountPtr<nvrhi::ISampler> 
 >   this  AH         AJ          AH        M        �   GCE
 >�     temp  AJ  
       AJ        N (                     0H� 
 h   �    0     Othis  9       �   O�   0           "   �      $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 D     H    
 \     `    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >A"   this  AH         AJ          AH        M        F  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   F   0   A"  Othis  9       �   O  �   0           "   �      $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         x         �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >汻   this  AH         AJ          AH        M        �   GCE
 >f    temp  AJ  
       AJ        N (                     0H� 
 h   �    0   汻  Othis  9       �   O�   0           "   �      $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 D     H    
 \     `    
 @SH冹 H嬞H婭H吷t?H婼(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁H塁 H塁(H岾H兡 [�    �    �?   �   [      `   �      �   �  \G            e      e   �.        �std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::~_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 
 >黮   this  AI  	     \ Q   AJ        	  M        /  H	V" M        /  )I1& M        V!  *F M        �  )!
 Z   !  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        �  
&#
$
 Z   �   >e    _Ptr_container  AP  *     :  !  AP >       >e    _Back_shift  AJ  
     W 1 !  AJ >         N N N M        Q!   N N N                       @� & h   �  �  P!  Q!  V!  y!  /  /         $LN33  0   黮  Othis  O ,      0     
 �     �    
 �     �    
 4     8    
 U     Y    
 �     �    
 �     �    
 �     �    
          
 �  �   �  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �   V   �      �   �  �G            [      [   /        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 
 >妐   this  AI  	     R K   AJ        	 " M        /  )H1%
 M        V!  *= M        �  )
 Z   !  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z   �   >e    _Ptr_container  AP  )     1    AP =       >e    _Back_shift  AJ       N 1   AJ =       
  N N N M        Q!   N N                       H� " h   �  �  P!  Q!  V!  y!  /         $LN30  0   妐  Othis  O   �   8           [   x-     ,       > �	   ? �O   D �U   ? �,      0     
 �     �    
 �     �    
 R     V    
 s     w    
 �     �    
 �     �    
          
 "     &    
 �  �   �  �  
 �     �    
 H�    H��   �      �   �   p G                   
   -/        �std::_Ref_count_obj2<donut::vfs::Blob>::~_Ref_count_obj2<donut::vfs::Blob> 
 >暞   this  AJ                                 H� 
 h   �      暞  Othis  O  �   (                          2 �
   8 �,   M   0   M  
 �   M   �   M  
 �   M   �   M  
 H�    H��   �      �   �   � G                   
   0/        �std::_Ref_count_obj2<donut::app::RegisteredFont>::~_Ref_count_obj2<donut::app::RegisteredFont> 
 >i�   this  AJ                                 H� 
 h   �      i�  Othis  O  �   (                          2 �
   8 �,   G   0   G  
 �   G   �   G  
 �   G      G  
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯H吷tH塳H��P惡    H嬎�    H嬤H�u院    H�H媆$0H媗$8H媡$@H兡 _�    P   �   y   �      �   Z  �G            }      d   /        �std::list<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~list<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 >   this  AJ          AL       \   M        /   M        �!  
\ M        �!  \ M        �  \ N N N' M        }!  I*
 >m   _Head  AI         >m    _Pnode  AI  &     C  >m    _Pnext  AM  3     )  AM 0     H  )  M        ~!  3
 M        �!  

G M        �!  
G M        �  
G
 Z   !   N N N M        r!  3 M        ^   3 M        �   3DE
 >p#    temp  AJ  7       AJ G       N N N N N N                      0@� F h   �  �  �  ^   �   !  (!  r!  }!  ~!  !  �!  �!  �!  �!  /   0     Othis  9C       �   O  �   8           }   .     ,        �    �d    �x    �,      0     
 �     �    
 �     �    
 |     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 V     Z    
 p     t    
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   x  f G            K      E   �.        �std::shared_ptr<donut::vfs::Blob>::~shared_ptr<donut::vfs::Blob> 
 >�   this  AJ        +  AJ @       M        �.  &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �.   0   �  Othis  9+       �   9=       �   O�   0           K        $       � �   � �E   � �,   E   0   E  
 �   E   �   E  
 �   E   �   E  
 �   E   �   E  
 d  E   h  E  
 t  E   x  E  
 �  E   �  E  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   z  h G            K      E   �.        �std::shared_ptr<donut::vfs::IBlob>::~shared_ptr<donut::vfs::IBlob> 
 >l�   this  AJ        +  AJ @       M        �.  &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �.   0   l�  Othis  9+       �   9=       �   O  �   0           K        $       � �   � �E   � �,   $   0   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 f  $   j  $  
 v  $   z  $  
 �  $   �  $  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  z G            K      E   �.        �std::shared_ptr<donut::app::RegisteredFont>::~shared_ptr<donut::app::RegisteredFont> 
 >   this  AJ        +  AJ @       M        �.  &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �.   0     Othis  9+       �   9=       �   O�   0           K        $       � �   � �E   � �,   +   0   +  
 �   +   �   +  
 �   +   �   +  
   +     +  
 x  +   |  +  
 �  +   �  +  
 �  +   �  +  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   d         �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >l   this  AJ        +  AJ @       M        �   &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �    0   l  Othis  9+       �   9=       �   O�   0           K        $       � �   � �E   � �,   C   0   C  
 �   C   �   C  
 �   C   �   C  
   C   
  C  
 |  C   �  C  
 �  C   �  C  
 �  C   �  C  
 @SH冹 H�H呟tH嬎�    喝  H嬎H兡 [�    H兡 [�   #   $   �      �   >  � G            .      (   �.        �std::unique_ptr<donut::app::ImGui_NVRHI,std::default_delete<donut::app::ImGui_NVRHI> >::~unique_ptr<donut::app::ImGui_NVRHI,std::default_delete<donut::app::ImGui_NVRHI> > 
 >/�   this  AJ        .                          H�  h   �.  �.  �.   0   /�  Othis  O  �   0           .        $       � �   � �   � �,   '   0   '  
 �   '   �   '  
 T  '   X  '  
 @SH冹 H嬞H�	H吷t]H婼H竒fffffffH+袶麝H龙H嬄H凌?H蠬�扝菱H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    蘛   �   u   �      �   �  � G            z      z   /        �std::vector<ImDrawVert,std::allocator<ImDrawVert> >::~vector<ImDrawVert,std::allocator<ImDrawVert> > 
 >0n   this  AI  	     q j   AJ        	 $ M        /  	hP%	
 M        �!  .3A M        �  ;)
 Z   !  
 >   _Ptr  AJ \       >#    _Bytes  AK  ;     > &  " M        �  
D#

 Z   �   >e    _Ptr_container  AP  H     1    AP \       >e    _Back_shift  AJ       m P   AJ \       
  N N N N                       @� " h   �  �  �  "!  h!  �!  /         $LN28  0   0n  Othis  O  �   8           z   �
     ,       � �	   � �n    �t   � �,      0     
 �      �     
 �      �     
 L     P    
 m     q    
 �     �    
 �     �    
          
          
 z  �   ~  �  
 �     �    
 @SH冹 H嬞H�	H吷tMH婼L嬅�    H�H婼H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �   F   M   �   e   �      �   "  G            j      j   �.        �std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::~vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > 
 >|�   this  AI  	     a Z   AJ        	 & M        �.  	(L4%	

 Z   #/   M        �.  *'= M        �  +)
 Z   !  
 >   _Ptr  AJ L       >#    _Bytes  AK  $     E   -  " M        �  
4#

 Z   �   >e    _Ptr_container  AP  8     1    AP L       >e    _Back_shift  AJ        I ,   AJ L       N N N N                       @�  h   �  �  �  �.  �.  �.         $LN28  0   |�  Othis  O  �   8           j   �
     ,       � �	   � �^    �d   � �,   )   0   )  
 5  )   9  )  
 I  )   M  )  
 �  )   �  )  
   )     )  
 b  )   f  )  
 v  )   z  )  
 �  )   �  )  
 �  )   �  )  
 
  �     �  
 8  )   <  )  
 H塡$WH冹 H嬞H伭�   L�
    �   D岯    怘婯03�H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘媆$0H兡 _�   �   %   �      �   �  X G            �   
   �   �.        �nvrhi::GraphicsPipelineDesc::~GraphicsPipelineDesc 
 >�!   this  AI  
     �  AJ        
  M        �  �� M        G  ��DE
 >�!    temp  AJ  �       AJ �       N N M        �  | M        F  |DE
 >�!    temp  AJ  �       AJ �       N N M        �  h M        F  hDE
 >�!    temp  AJ  l       AJ |       N N M        �  T M        F  TDE
 >�!    temp  AJ  X       AJ h       N N M        �  @ M        F  @DE
 >�!    temp  AJ  D       AJ T       N N M        �  * M        F  *DG
 >�!    temp  AJ  .       AJ @       N N                      0@�  h   �  �  F  G  E   I    0   �!  Othis  9<       �   9P       �   9d       �   9x       �   9�       �   9�       �   O  ,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 I  �   M  �  
 Y  �   ]  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 +  �   /  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 b  �   f  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �     �   �   K G                       �#        �donut::app::IRenderPass::~IRenderPass 
 >魓   this  AJ          D                           H�     魓  Othis  O   �                  p
            � �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 H塡$WH冹 H嬞H媺�  3�H吷tPH媰�  H+罤养H� H侜   rH兟'L婣鳬+菻岮鳫凐�+  I嬋�    H壔�  H壔�  H壔�  H崑�  �    H崑X  �    怘媼P  H吷tH壔P  H��P怘岾P�    怘婯HH吷tH墈HH��P怘婯@H吷tH墈@H��P怘婯8H吷tH墈8H��P怘婯0H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘�H吷t
H�;H��P怘媆$0H兡 _描    蘎   �   s            �   �   z  �      �   D  K G              
     �.        �donut::app::ImGui_NVRHI::~ImGui_NVRHI 
 >{k   this  AI  
     rf  AJ        
  M        �   乗 M        	!  乗CE
 >�&    temp  AJ  _      AJ n      N N M        |   丠 M        �   丠DE
 >�&    temp  AJ  L      AJ \      N N M        �  �4 M        F  �4DE
 >�!    temp  AJ  8      AJ H      N N M        �  �  M        F  � DE
 >�!    temp  AJ  $      AJ 4      N N M        �  � M        G  �DE
 >�!    temp  AJ        AJ        N N M        x   �� M        �   ��DE
 >f    temp  AJ  �       AJ       N N M        u   �� M        �   ��DE
 >�     temp  AJ  �       AJ �       N N M        r   �� M        �   ��DE
 >R    temp  AJ  �       AJ �       N N M        r   �� M        �   ��DE
 >R    temp  AJ  �       AJ �       N N M        �  �� M        �  ��DE
 >�!    temp  AJ  �       AJ �       N N M        n   �� M        �   ��GE
 >^#    temp  AJ  �       AJ �       N N M         /  ^
乴% M        /  
n;'	� M        �!  .(丵 >e   _Count  AH  "     "    AH Q       M        �  ,)�(
 Z   !  
 >   _Ptr  AJ Q       >#    _Bytes  AK  ,     R* #% M        �  5d#�+
 Z   �   >e    _Ptr_container  AP  =     A ( AP Q       >e    _Back_shift  AJ       j= ( AJ Q     !    N N N N N                      0@� r h   �  �  �  �  �  �  F  G  �  n   r   u   x   |   �   �   �   �   �   �   	!   !  e!  �!  �.   /  /         $LN97  0   {k  Othis  9�       �   9�       �   9�       �   9�       �   9�       �   9      �   9      �   90      �   9D      �   9X      �   9j      �   O,   #   0   #  
 p   #   t   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 B  #   F  #  
 R  #   V  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
 (  #   ,  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #     #  
 Y  #   ]  #  
 i  #   m  #  
 �  #   �  #  
 �  #   �  #  
 /  #   3  #  
 ?  #   C  #  
 �  #   �  #  
 �  #   �  #  
   #   	  #  
   #     #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
 x  #   |  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 |  �   �  �  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
    #     #  
   #     #  
    #   $  #  
 0  #   4  #  
 @  #   D  #  
 H塡$H塼$WH冹 H孂H�    H�3设    H嫙�  H呟t,����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH崗x  �    H媉H呟tH嬎�    喝  H嬎�    怘媆$0H媡$8H兡 _�   �      �   c   )   t   #   �   �      �   �  Q G            �      �   �.        �donut::app::ImGui_Renderer::~ImGui_Renderer 
 >努   this  AJ          AM       �  M        �.  	g N M        �.  8# M        �.  #, M        �  /
 >�   this  AI  *     A  M        �  H	
 N N N N Z   �/  �.                        H� * h	   �  �  �#  �.  �.  �.  �.  �.  �.   0   努  Othis  9F       �   9X       �   O �   0           �   �     $       A  �   B  �#   C  �,   -   0   -  
 v   -   z   -  
 �   -   �   -  
 	  -   
  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �     �   �   L G                       �        �std::_Ref_count_base::~_Ref_count_base 
 >�   this  AJ          D                           H�     �  Othis  O  �                              ~ �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 H�    H�H兞�       �      �      �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >|   this  AJ          M        �   	
 N                        H�  h   �  �      |  Othis  O ,   �   0   �  
 {   �      �  
 H�    H�H兞�       �      �      �   �   @ G                      �        �std::exception::~exception 
 >!   this  AJ         
 Z   r                          H�     !  Othis  O  �   (              �            Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
�(   �    H嬅H兡 [�	   �      �      �   �   j G            +      %   G/        �std::_Ref_count_obj2<donut::vfs::Blob>::`scalar deleting destructor' 
 >暞   this  AI         AJ                                @� 
 h   -/   0   暞  Othis  O,   P   0   P  
 �   P   �   P  
 �   P   �   P  
 @SH冹 H�    H嬞H�雎t
�0   �    H嬅H兡 [�	   �      �      �   �   t G            +      %   H/        �std::_Ref_count_obj2<donut::app::RegisteredFont>::`scalar deleting destructor' 
 >i�   this  AI         AJ                                @� 
 h   0/   0   i�  Othis  O  ,   J   0   J  
 �   J   �   J  
 �   J   �   J  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   [ G            !         �#        �donut::app::IRenderPass::`scalar deleting destructor' 
 >魓   this  AI  	       AJ        	                        @� 
 h   �#   0   魓  Othis  O   ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塴$H塼$WH冹 嬯H嬞H�    H�3设    H嫽�  H�t,����嬈�罣凐uH�H嬒��羨凗u	H�H嬒�PH崑x  �    H媨H�tH嬒�    喝  H嬒�    怈雠t
酣  H嬎�    H嬅H媆$0H媗$8H媡$@H兡 _�   �   &   �   j   )   {   #   �   �   �   �      �   �  ^ G            �      �   �.        �donut::app::ImGui_Renderer::`scalar deleting destructor' 
 >努   this  AI       �  AJ          M        �.  *M Z   �/  �.   M        �.  	n N M        �.  8* M        �.  *, M        �  6
 >�   this  AM  1     A  M        �  O	
 N N N N N                      0@� . h
   �  �  �#  �.  �.  �.  �.  �.  �.  �.   0   努  Othis  9M       �   9_       �   O   ,   ?   0   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 ?  ?   C  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >e   this  AJ          AM       -  M        �  

	
 Z   r   N                       @�  h   �  �   0   e  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >|   this  AJ          AM       -  M        �  

	
 Z   r   N                       @�  h   �  �  �   0   |  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >!   this  AJ          AM       -  M        �  

	
 Z   r   N                       @� 
 h   �   0   !  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >    __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H      O__f  9(           O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 �     �   �   F G                       �#        �donut::app::IRenderPass::Animate 
 >魓   this  AJ          D    >@    fElapsedTimeSeconds  A�           D                           @     魓  Othis      @   OfElapsedTimeSeconds  O�                  p
            � �,   �   0   �  
 k   �   o   �  
 �   �   �   �  
   �     �  
 L嬡UH侅`  E)泋���H�    H3腍墑$�   H儁 D(貶嬮勜  �埂   吽  H婣M塩餖嫛�  M墈豅嫻x  A){阁笜   E)S報D悳   M;�剭  I塻M塳鐴3鞰塻郋)CD    E)K楨W蒊塠I墈 A)s萬ff�     I�7L9n咉   D8瓲  t(麟A(餒峀$@�    �YvL塶�t$TL9.剭   D坙$LD8ntA�    H�H媥0H��PH�H嬝H��RH峀$@L塴$(H塋$ A(貶嬒D嬅H嬓�    隯�    H�H媥0H��PH�H嬝H��RH峀$@L塴$(H塋$ A(貶嬒D嬅H嬓�    �D8nt,�    H峊$@H婬0�    H塅H嬋H吚t
�    H婬0L塱I兦M;�咉��D(�$   D(�$  (�$0  L嫶$H  L嫭$P  H嫾$�  H嫶$�  H嫓$x  H婱�    H婱L岲$4H峊$0�    �    fnD$0fnL$4L嫾$@  L嫟$X  [�[审@�H�綘   u�xH驞PL�弗   D(�$�   (�$   u
�俯   u2呻��袱   垐T  u
�辅   u2呻��甫   垐U  u
�釜   u2呻��抚   垐V  u
�斧   u2呻�驞X垐W  艪P �    茀�  H媽$�   H3惕    D(�$�   H伳`  ]�   ^   �   Z   �      
  �   G     N  �   �     �  �   �     �  �        '  �   ,  �     �     z      �   v  I G            1  %     �.        �donut::app::ImGui_Renderer::Animate 
 >努   this  AJ        1  AN  1     � >@    elapsedTimeSeconds  A�         �  A�  �     q) � X�  >@     scaleX  A�   k      >@     scaleY  A  y     
 >t     w  D0   
 >t     h  D4    >薻    io  AH  0    �  >�    <begin>$L0  AW  ^     � >�    <end>$L0  AT  S     � M        �.  % N M        �.  c	
 N6 M        �.  ��*EJI%F?b?"F
& Z   �/  �!  �/  �!  �/  �!  �/  �!   >Ek    fontConfig  D@    M        �.  �� N N Z   6   $  �!  �/   `                    A : h
      �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  /  /  
 :�   O  p  努  Othis  x  @   OelapsedTimeSeconds  0   t   Ow  4   t   Oh  9         9'      龤   9\         9h      龤   O  �              1  �     �       �  �%   �  �*   �  �1   �  �D   �  �L   �  �c   �  �k   �  �p   �  �y   �  ��   �  ��   �  ��  �  �  �  �   �+   �<   �\   �e   �j   �p   ��   ��  
 ��   ��   ��   �   �   �   �,   7   0   7  
 n   7   r   7  
 ~   7   �   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
   7   	  7  
 J  7   N  7  
 o  7   s  7  
 �  7   �  7  
 B  7   F  7  
 R  7   V  7  
 b  7   f  7  
 r  7   v  7  
 �  7   �  7  
 �     �   p  P G                       �#        �donut::app::IRenderPass::BackBufferResized 
 >魓   this  AJ          D    >3   width  A           D    >3   height  Ah          D    >3   sampleCount  Ai          D                            @     魓  Othis     3  Owidth     3  Oheight      3  OsampleCount  O�                  p
            � �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �     �   �   Q G                       �#        �donut::app::IRenderPass::BackBufferResizing 
 >魓   this  AJ          D                           @     魓  Othis  O �                  p
            � �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 H婭H吷�    �	   !      �   �   T G                   
   �.        �donut::app::ImGui_Renderer::BackBufferResizing 
 >努   this  AJ          M        �.    N
 Z   X                           @  h   �.  �.      努  Othis  O �   0              �     $       7 �    8 �
   9 �,   9   0   9  
 y   9   }   9  
 �   9   �   9  
 @SH冹 �    L岲$8H荄$8    �   H荄$@    H峀$@H嬝�    �KH峀$8�S�   �^KH�^SL�L$8�T$<�    W晒   �    W黎    3襀�
    A�	 H兡 [�       �   0      _      l      t      }   �   �         �   }  W G            �      �   �.        �donut::app::ImGui_Renderer::BeginFullScreenWindow 
 >努   this  AJ          D0    >舓    io  AI  /     ]  M           R >@    _x  A�   M       >@    _y  A�   R       N M           	 N M           	 N Z   �!  �/  �/  �/  �/  �/                         @ 
 h       0   努  Othis  O   �   `           �   �  	   T       P �   Q �   R �4   S �f   W �s   X �x   Y ��   Z ��   Y �,   ;   0   ;  
 |   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �  ;   �  ;  
 H塡$H塴$H塼$ WAVAWH冹PM嬓H孃H嬮E3�I� M嬃H峊$(I嬍�P怢9|$(u H嬒�    怘媆$0H呟刓  A峸��,  �0   �    H嬝H墑$�   ����H吚剾   W� 茾   茾   H�    H�L媡$0M咑t
餉�FL媡$0H婦$(L墈L墈M咑t餉�FH塁L塻f荂   ��$�   �C$L墈(M咑t0嬈餉罠凐u#I�I嬑�嬈餉罠凐uI�I嬑�P�I嬤H岰H塂$8H塡$@H崓x  H嫊�  H;晥  t!L�:L墇H呟t�CH�H塟H儏�  �
L岲$8�    H婦$8H�H婦$@H塆H媆$0H呟t'嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH嬊H媆$pH媗$xH嫶$�   H兡PA_A^_肁   D   c   �   �   �   j  S      �   �  T G            �     �  �.        �donut::app::ImGui_Renderer::CreateFontFromFile 
 >努   this  AJ        !  AN  !     � >C�   fs  AP          AR         >�1   fontFile  AQ        5  >@    fontSize  EO  (           D�    >~�    fontData  D(   
 >
�    font  D8    M        �.  
� M        �.  
� N N M        �.  乶 M        /  乶	 N N M        �.  ;�3 M        /  
�3
!
 Z   i/   M        h/  丆 M        �/  丆 M        �.  丆 M        /  丣I M        3/  丣 M        �  丱 N N N M        �.  �丆 N N N N N N M        /  (X+��
 Z      >i�    _Rx  AI  j      B�   r     ] M        Y/  �" N M        _/  X+{ M        �  	�� N M        �/  X{F$ M        �.  	X_F120 M        �.  0�� M        �.  ��+ M        �  ��-
 >�   this  AV  �     x  AV     �  M        �  �	
 N N N N M        �.  X_ M        /  XgJ M        9/  Xg M        �  Xl N N N M        �.  ��� N N N M        �.  �� M        /  �� M        9/  ��

 M        �  
�� N N N N N N N M        �.  F M        �.  F M        �  T
 >�   this  AI  K       AI �    /  N N N
 Z   /   P                    @ � h'   �  �  �  �  �  �  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  /  /  /  /  /  /  3/  9/  X/  Y/  ]/  ^/  _/  g/  h/  /  �/  �/  �/   p   努  Othis  �   C�  Ofs  �   �1  OfontFile  �   @   OfontSize  (   ~�  OfontData  8   
�  Ofont  92       *�   ^b      h�   9      �   9      �   9�      �   9�      �   O  �   X           �  �     L       e  �$   f  �6   g  �=   h  �X   j  �,  k  �n  m  ��  n  ��   �   c F                                �`donut::app::ImGui_Renderer::CreateFontFromFile'::`1'::dtor$0  >~�    fontData  EN  (          
 >
�    font  EN  8                                  �  O   �   �   c F                                �`donut::app::ImGui_Renderer::CreateFontFromFile'::`1'::dtor$2  >~�    fontData  EN  (          
 >
�    font  EN  8                                  �  O   ,   /   0   /  
 y   /   }   /  
 �   /   �   /  
 �   /   �   /  
 �   /   �   /  
 �   /   �   /  
    /     /  
    /     /  
   /     /  
   /     /  
 #  /   '  /  
 �  /   �  /  
 �  /   �  /  
 B  /   F  /  
 R  /   V  /  
 b  /   f  /  
 r  /   v  /  
 �  /   �  /  
 �  /   �  /  
 �  /   �  /  
 ,  Z   0  Z  
 �  Z   �  Z  
 �  Z   �  Z  
 	  k   	  k  
 l	  k   p	  k  
 �	  k   �	  k  
 H崐(   �       $   H崐8   �       +   @SH冹@�D$pH嬟�D$(艱$  �    H嬅H兡@[�   >      �   d  V G            (      "   �.        �donut::app::ImGui_Renderer::CreateFontFromMemory 
 >努   this  AJ          >�   pData  AP         
 >#    size  AQ          >@    fontSize  EO  (           Dp   
 Z   �.   @                     @  P   努  Othis  `   �  OpData  h   #   Osize  p   @   OfontSize  O�   0           (   �     $       �  �   �  �"   �  �,   0   0   0  
 {   0      0  
 �   0   �   0  
 �   0   �   0  
 �   0   �   0  
 x  0   |  0  
 @SH冹@�D$pH嬟�D$(艱$ �    H嬅H兡@[�   >      �   n  ` G            (      "   �.        �donut::app::ImGui_Renderer::CreateFontFromMemoryCompressed 
 >努   this  AJ          >�   pData  AP         
 >#    size  AQ          >@    fontSize  EO  (           Dp   
 Z   �.   @                     @  P   努  Othis  `   �  OpData  h   #   Osize  p   @   OfontSize  O  �   0           (   �     $       �  �   �  �"   �  �,   1   0   1  
 �   1   �   1  
 �   1   �   1  
 �   1   �   1  
 �   1   �   1  
 �  1   �  1  
 H塡$H塴$VWATAVAWH冹PI孂I嬝H嬺L嬹E3銶吚勄  M吷劸  I嬌�    H嬭L嬊H嬘H嬋�    A峀$(�    H嬝H墑$�   H吚t/W� 茾   茾   H�    H�H岾L嬊H嬚�    �I嬡L峽L墊$8H塡$@�0   �    H孁H墑$�   ����H吚剸   W� 茾   茾   H�    H�H呟t�CL塯L塯�C�L塯L塯L�H塤艷  秳$�   圙!��$�   �G$L塯(H呟t.嬇�罜凐u"H�H嬎�嬇�罜凐uH�H嬎�P�I孅H岹H塂$(H墊$0I崕x  I嫋�  I;枅  t!L�"L塨H�t�GH�H墇I儐�  �
L岲$(�    H婦$(H�H婦$0H塅H呟t1嬇�罜凐u%H�H嬎��羕凖uH�H嬎�P�H嬑�    H嬈L峔$PI媅0I媖8I嬨A_A^A\_^�;   �   L   {   V   �   �   �   �   B   �   �   �   �   �  S   �  D      �   �  ^ G                    �.        �donut::app::ImGui_Renderer::CreateFontFromMemoryInternal 
 >努   this  AJ        "  AV  "     � >�   pData  AI       �A � AP          AI �     
 >#    size  AM       �� > AQ          AM �      B�   �     %  o    >0    compressed  EO  (           D�    >@    fontSize  EO  0           D�    >    dataCopy  AN  B     � 
 > �    blob  D8   
 >
�    font  D(    M        �.  ,伹 M        �.  伹' M        �  佁, M        �  佮	
 N N N N M        �.  伓 M        /  伓	 N N M        �.  ;亄 M        /  
亄
!
 Z   i/   M        h/  亱 M        �/  亱 M        �.  亱 M        /  亽I M        3/  亽 M        �  仐 N N N M        �.  �亱 N N N N N N M        /  #��&��
 Z      >i�    _Rx  AM  �     > AM �      B�   �     Z6  M        Y/  乯 N M        f/  ��v M        �  	�� N M        �/  v��% M        �.  	��($+12. M        �.  .�3 M        �.  �3) M        �  �8, M        �  丩	 N N N N M        �.  �� M        /  ��N M        9/  �� M        �  �� N N N N N M        �/  �� M        �/  �� M        �/  �� M        �  �� N N N N N N N M        /  P/
 Z      >暞    _Rx  AI  ]     � AI �      B�   e     Z  M        d/  �� N M        b/  m M        �  	p N M        �/  ��
 Z   �/   N N N Z   u  /   P           (         @ � h3   �  �  �  �  �  �  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  �.  /  /  /  /  /  /  /  )/  */  3/  9/  X/  Y/  ^/  `/  a/  b/  c/  d/  e/  f/  g/  h/  /  �/  �/  �/  �/  �/  �/  �/   �   努  Othis  �   �  OpData  �   #   Osize  �   0   Ocompressed  �   @   OfontSize  8    �  Oblob  (   
�  Ofont  ^:          ^U      敮   ^�      h�   9J      �   9^      �   9�      �   9�      �   O   �   h             �  
   \       r  �%   s  �7   w  �B   x  �P   y  ��   {  �t  |  ��  ~  ��  t  ��    ��   �   m F                                �`donut::app::ImGui_Renderer::CreateFontFromMemoryInternal'::`1'::dtor$3 
 > �    blob  EN  8          
 >
�    font  EN  (                                 �  O �   �   m F                                �`donut::app::ImGui_Renderer::CreateFontFromMemoryInternal'::`1'::dtor$1 
 > �    blob  EN  8          
 >
�    font  EN  (                                  �  O �   �   m F                                �`donut::app::ImGui_Renderer::CreateFontFromMemoryInternal'::`1'::dtor$2 
 > �    blob  EN  8          
 >
�    font  EN  (                                  �  O ,   >   0   >  
 �   >   �   >  
 �   >   �   >  
 �   >   �   >  
 �   >   �   >  
 �   >   �   >  
 �   >   �   >  
 
  >     >  
   >     >  
 *  >   .  >  
 _  >   c  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
    >     >  
   >     >  
 [  >   _  >  
 k  >   o  >  
 {  >     >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 	  >   	  >  
 �	  o   �	  o  
 
  o   
  o  
 1
  o   5
  o  
 �
  e   �
  e  
 �
  e   �
  e  
   e     e  
 d  l   h  l  
 �  l   �  l  
 �  l   �  l  
 H崐8   �       E   H崐(   �       +   @UH冹 H嬯�(   H媿�   �    H兡 ]�   �   @UVAVH侅�   H�    H3腍墑$�   H嬹)�$�   H峀$0(耔    �Yv3鞨塶�t$D(�$�   H9.劔   H墱$  H壖$  @坙$<@8nt@�    H�H媥0H��PH�H嬝H��RH峀$0H塴$(H塋$ W跦嬒D嬅H嬓�    �>�    H�H媥0H��PH�H嬝H��RH峀$0H塴$(H塋$ W跦嬒D嬅H嬓�    H嫾$  H嫓$  �@8nt,�    H峊$0H婬0�    H塅H嬋H吚t
�    H婬0H塱H媽$�   H3惕    H伳�   A^^]�   ^   2      t   �   �      �   �   �      
  �        )  �   A  z      �   �  R G            Q     5  �.        �donut::app::RegisteredFont::CreateScaledFont 
 >   this  AJ        !  AL  !     . >@    displayScale  A�         6  A�   ;       >Ek    fontConfig  D0    M        �.  O N& Z   �/  �!  �/  �!  �/  �!  �/  �!   �                     I  h   �.  �.  /  
 :�   O       Othis    @   OdisplayScale  0   Ek  OfontConfig  9�          9�       龤   9�          9�       龤   O   �   �           Q  �     �       l �)   m �6   n �;   p �O   r �X   t �m   u �s   w ��   y ��   | ��    �  � �	  � �  � �(  � �5  � �,   %   0   %  
 w   %   {   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �     �   ,  R G                       �#        �donut::app::IRenderPass::DisplayScaleChanged 
 >魓   this  AJ          D    >@    scaleX  A�           D    >@    scaleY  A�           D                           @     魓  Othis     @   OscaleX     @   OscaleY  O�                  p
            � �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 @  �   D  �  
 L嬡WH侅�  A)s鐷�    H3腍墑$�  �範   (馠孂剬  I塠�    H嬝H婬0�    H婥0E3繪堾H嫍�  H嫃x  H;蕋�     H�H兞L堾H;蕌餒崒$p  �    �   H峀$ 嬘�    H崏�    HH崁�   A�@�I�H�A�@�I�H�A�@�I�H�A�I餒冴u� H@ IH0H婡@A I0H堿@�    H嬋H岲$ ff�     H崏�    HH崁�   A�@�I�H�A�@�I�H�A�@�I�H�A�I餒冸u� H嫓$ 	  P@ QP0H婡@A Q0H堿@�    (蜨嬋�    H媽$�  H3惕    (�$�  H伳�  _�   ^   :   �   F      �        �   �  �   �  
   �  z      �   �  U G            �  "   �  �.        �donut::app::ImGui_Renderer::DisplayScaleChanged 
 >努   this  AJ        /  AM  /     � >@    scaleX  A�         >  A�  �    !  >@    scaleY  A�         >  A�  �    !  D 	   >薻    io  AI  A     Q  >�    <begin>$L0  AJ  c     %  >�    <end>$L0  AK  \     1  Z   �!  �/  �/  �/  �/  �/   �                    A  h   �.  �.  �.  /  
 :�  O  �  努  Othis  �  @   OscaleX   	  @   OscaleY  O�   h           �  �  
   \       < �"   > �9   A �A   E �J   F �U   H �p   I ��   K ��  L ��  M �,   :   0   :  
 z   :   ~   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
   :     :  
 6  :   :  :  
 Y  :   ]  :  
   :     :  
 H塡$WH冹0H孃�    �    H峀$PE3审D$ E3繦嬜H嬝�    �C�^CH�\D$P�Y    �    �C�^CL�\D$T�Y    �    3襀嬒�    H媆$@H兡0_�   �      ]   2      J   W   O      g   W   l   	   v   
      �   k  X G            �   
   z   �.        �donut::app::ImGui_Renderer::DrawScreenCenteredText 
 >努   this  AJ          D@   
 >�   text  AK        
  AM  
     w  >舓    io  AI  1     N  >鏴    textSize  BP        s  Z   �!  �/  �/  �/  �/   0                     @  @   努  Othis  H   �  Otext  P   鏴  OtextSize  O �   P           �   �     D       ] �
   ^ �   _ �6   ` �S   a �p   b �z   c �,   <   0   <  
 }   <   �   <  
 �   <   �   <  
 �   <   �   <  
 �   <   �   <  
 �   <   �   <  
 �  <   �  <  
 H冹(�    �   H兡(�                   �   �   U G                     �.        �donut::app::ImGui_Renderer::EndFullScreenWindow 
 >努   this  AJ        	  D0    Z   �/  �/   (                      @  0   努  Othis  O �   @              �     4       f �   g �	   h �   i �   h �,   =   0   =  
 z   =   ~   =  
 �   =   �   =  
 H塡$H塗$VWAVH冹0L嬺H孂3鲨    莯�    莯�    莯�    莯�  	  莯�    莯�  
  莯 	    莯	    莯	  
  莯	    莯	    莯	    莯 	     莯p	  A   莯x	  C   莯�	  V   莯�	  X   莯�	  Y   莯�	  Z   谷  �    H嬝H塂$PH吚t3褹溉  H嬋�    H嬎�    H嬸H媉H墂H呟tH嬎�    喝  H嬎�    H媉H岲$ H塂$PW荔D$ I婩H吚t�@I�H塂$ I婩H塂$(H婳H��P@怢岲$ H嬓H嬎�    娥I媈H呟t0����嬜�罶凓uH�H嬎��羬�u	H�H嬎�P@镀H媆$`H兡0A^_^�   �   �   �      |     "      #   -  �   }        �   �  F G            �     �  �.        �donut::app::ImGui_Renderer::Init 
 >努   this  AJ          AM       z AM �      >薼   shaderFactory  AK          AV       � DX    >薻    io  AH       �  M        d   5亜 M        �   亜,	 M        �  亶
 >�   this  AI  �    :  M        �  仸	
 N N N N M        0,  
乫 N M        �.  丅 M        /  丠M	 M        2/  丠	 M        �  丵 N N N M        �.  �丅 N N M        /  
� M        7/  � M        r/  � >觡    _Old_val  AI      "  N N N M        
/  2�� Z     /   N Z   �!  9    0                    @ j h   �  �  �  d   �   0,  �.  �.  �.  �.  �.  �.  �.  
/  /  /  2/  7/  8/  Z/  [/  r/  s/  �/  �/   P   努  Othis  X   薼  OshaderFactory  ^�      zk   9m      紉   9�      �   9�      �   O  �   �           �  �     �       F  �   J  �   K  �)   L  �3   M  �=   N  �G   O  �Q   P  �[   Q  �e   R  �o   S  �y   T  ��   U  ��   V  ��   W  ��   X  ��   Y  ��   Z  ��   [  ��   \  ��   ]  ��   _  �1  `  ��  a  ��   �   U F                                �`donut::app::ImGui_Renderer::Init'::`1'::dtor$0  >薼   shaderFactory  EN  X                                  �  O   �   �   U F                               �`donut::app::ImGui_Renderer::Init'::`1'::dtor$3  >薼   shaderFactory  EN  X                                 �  O   �   �   U F                                �`donut::app::ImGui_Renderer::Init'::`1'::dtor$2  >薼   shaderFactory  EN  X                                  �  O   ,   .   0   .  
 k   .   o   .  
 {   .      .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 U  .   Y  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  [   �  [  
 C  [   G  [  
 �  p   �  p  
 �  p   �  p  
 H  m   L  m  
 �  m   �  m  
 H媻X   �       C   H媻P   �       C   @UH冹 H嬯喝  H婱P�    H兡 ]�   �   2烂   �   %  Q G                      �#        �donut::app::IRenderPass::JoystickAxisUpdate 
 >魓   this  AJ          D   
 >t    axis  A           D    >@    value  A�           D                           @     魓  Othis     t   Oaxis     @   Ovalue  O   �                  p
            � �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 <  �   @  �  
 2烂   �   /  S G                      �#        �donut::app::IRenderPass::JoystickButtonUpdate 
 >魓   this  AJ          D    >t    button  A           D    >0    pressed  AX          D                           @     魓  Othis     t   Obutton     0   Opressed  O �                  p
            � �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 2烂   �   (  P G                      �#        �donut::app::IRenderPass::KeyboardCharInput 
 >魓   this  AJ          D    >u    unicode  A           D   
 >t    mods  Ah          D                           @     魓  Othis     u   Ounicode     t   Omods  O�                  p
            � �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 <  �   @  �  
 H塡$WH冹 孃�    嬜H嬋H嬝�    秲�   H媆$0H兡 _�
   �            �   p  S G            0   
   %   �.        �donut::app::ImGui_Renderer::KeyboardCharInput 
 >努   this  AJ          D0    >u    unicode  A           A        # 
 >t    mods  Ah          D@    >薻    io  AH         AI         Z   �!  �/                         @  0   努  Othis  8   u   Ounicode  @   t   Omods  O�   @           0   �     4       �  �   �  �   �  �   �  �%   �  �,   3   0   3  
 x   3   |   3  
 �   3   �   3  
 �   3   �   3  
 �   3   �   3  
 �   3   �   3  
   3   
  3  
 �  3   �  3  
 2烂   �   �  M G                      �#        �donut::app::IRenderPass::KeyboardUpdate 
 >魓   this  AJ          D    >t    key  A           D    >t    scancode  Ah          D    >t    action  Ai          D    
 >t    mods  D(    EO  (                                  @     魓  Othis     t   Okey     t   Oscancode      t   Oaction  (   t   Omods  O   �                  p
            � �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   #  �  
 �  �   �  �  
 H塡$H塼$WH冹 A嬞Hc鶫嬹�    D岰�A凐v艱7 �
艱7苿P  秬�   H媆$0H媡$8H兡 _�   �      �   �  P G            R      B   �.        �donut::app::ImGui_Renderer::KeyboardUpdate 
 >努   this  AJ          AL       4  >t    key  A           AM       <  >t    scancode  Ah          D@    >t    action  A        5  Ai         
 >t    mods  EO  (           DP    >薻    io  AH       % 
 Z   �!                         @ 
 h   �.   0   努  Othis  8   t   Okey  @   t   Oscancode  H   t   Oaction  P   t   Omods  O   �   P           R   �     D       �  �   �  �   �  �'   �  �.   �  �3   �  �;   �  �,   2   0   2  
 u   2   y   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
 �   2     2  
   2     2  
 2  2   6  2  
 W  2   [  2  
   2     2  
 2烂   �   d  P G                      �#        �donut::app::IRenderPass::MouseButtonUpdate 
 >魓   this  AJ          D    >t    button  A           D    >t    action  Ah          D   
 >t    mods  Ai          D                            @     魓  Othis     t   Obutton     t   Oaction      t   Omods  O�                  p
            � �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 x  �   |  �  
 H塡$H塼$WH冹 A嬝孃H嬹�    D岰�H嬓A凐A柫�t冿t�u岹�婦$8�	�   �3繦c菵圠1E勆t苿@  秱�   H媆$0H媡$@H兡 _�   �      �   ;  S G            y      i   �.        �donut::app::ImGui_Renderer::MouseButtonUpdate 
 >努   this  AJ          AL       \  >t    button  A           A        9    A  M     +  >t    action  A        \  Ah         
 >t    mods  Ai          DH    >0     buttonIsDown  AY  +     N  >t     buttonIndex  A   <     -        >薻    io  AK  #     V 
 Z   �!                         @ 
 h   Y   0   努  Othis  8   t   Obutton  @   t   Oaction  H   t   Omods  8   t   ObuttonIndex  O �   �           y   �  
   t       �  �   �  �   �  �+   �  �9   �  �>   �  �D   �  �I   �  �K   �  �M   �  �U   �  �Z   �  �b   �  �,   6   0   6  
 x   6   |   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �   6     6  
   6   !  6  
 L  6   P  6  
 r  6   v  6  
 �  6   �  6  
 P  6   T  6  
 2烂   �     M G                      �#        �donut::app::IRenderPass::MousePosUpdate 
 >魓   this  AJ          D   
 >A    xpos  A�           D   
 >A    ypos  A�           D                           @     魓  Othis     A   Oxpos     A   Oypos  O �                  p
            � �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �   �   �  
 4  �   8  �  
 H冹H)t$0(�)|$ (    W�W沈Z�(t$0�Z�(|$ ��8  ��<  秬�   H兡H�   �      �   ;  P G            M      #   �.        �donut::app::ImGui_Renderer::MousePosUpdate 
 >努   this  AJ          DP   
 >A    xpos  A�          
 >A    ypos  A�           >薻    io  AH       / 
 Z   �!   H                      @  P   努  Othis  X   A   Oxpos  `   A   Oypos  O �   @           M   �     4       �  �   �  �   �  �   �  �#   �  �,   4   0   4  
 u   4   y   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 P  4   T  4  
 2烂   �   .  P G                      �#        �donut::app::IRenderPass::MouseScrollUpdate 
 >魓   this  AJ          D    >A    xoffset  A�           D    >A    yoffset  A�           D                           @     魓  Othis     A   Oxoffset     A   Oyoffset  O  �                  p
            � �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 H冹8)t$ (蜩    W莉Z�(t$ �X�H  ��H  秬�   H兡8�
   �      �   R  S G            9   	      �.        �donut::app::ImGui_Renderer::MouseScrollUpdate 
 >努   this  AJ          D@    >A    xoffset  A�           DH    >A    yoffset  A�           >薻    io  AH       # 
 Z   �!   8                      @  @   努  Othis  H   A   Oxoffset  P   A   Oyoffset  O  �   8           9   �     ,       �  �   �  �   �  �   �  �,   5   0   5  
 x   5   |   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 h  5   l  5  
 H茿    �   �   �   S G            	          �.        �donut::app::RegisteredFont::ReleaseScaledFont 
 >   this  AJ        	                         H       Othis  O   �   0           	   �     $       � �    � �   � �,   &   0   &  
 x   &   |   &  
 �   &   �   &  
 �     �   �   E G                       �#        �donut::app::IRenderPass::Render 
 >魓   this  AJ          D    >`#   framebuffer  AK          D                           @     魓  Othis     `#  Oframebuffer  O �                  p
            � �,   �   0   �  
 j   �   n   �  
 �   �   �   �  
    �     �  
 H塡$WH冹 H儁 H孃H嬞剳   H��Px�    H婯H嬜�    苾�   �    H嬋�窣  u
�{ u苺@   �窤  u
�{ u苺A   �窧  u
�{ u苺B   HP  H+俟]  �8u
��唆�� u�  H�繦冮u錒媆$0H兡 _�"       .       :   �      �   �  H G            �   
   �   �.        �donut::app::ImGui_Renderer::Render 
 >努   this  AI       w  AJ          AI �       >`#   framebuffer  AK          AM       �  >薻    io  AJ  A     P  M        �.  
 N Z   �/  Q   �!                         @  h   Y  �.  �.  �.  �.  �.   0   努  Othis  8   `#  Oframebuffer  9       佻   O�   �           �   �     �        �
    �    �    �    �!    �&    �2     �9   # �A   & �P   ( �W   & �f   ( �m   & �|   ( ��   - ��   / ��   1 ��   - ��   4 �,   8   0   8  
 m   8   q   8  
 }   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �  8   �  8  
 �  8   �  8  
 �     �   �   Q G                       �#        �donut::app::IRenderPass::SetLatewarpOptions 
 >魓   this  AJ          D                           @     魓  Othis  O �                  p
            � �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 2烂   �   �   T G                      �#        �donut::app::IRenderPass::ShouldRenderUnfocused 
 >魓   this  AJ          D                           @     魓  Othis  O  �                  p
            � �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   Z G                      +/        �std::_Ref_count_obj2<donut::vfs::Blob>::_Delete_this 
 >暞   this  AJ                                 @�     暞  Othis  9
       櫙   O�   0                   $       C �    D �   E �,   O   0   O  
    O   �   O  
 �   O   �   O  
 �   O   �   O  
 H吷tH��   H�`�   �   �   d G                      ./        �std::_Ref_count_obj2<donut::app::RegisteredFont>::_Delete_this 
 >i�   this  AJ                                 @�     i�  Othis  9
       m�   O  �   0                   $       C �    D �   E �,   I   0   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
 H婣H兞3襀�    �     V G            
       
   ,/        �std::_Ref_count_obj2<donut::vfs::Blob>::_Destroy 
 >暞   this  AJ          M        n/   
 >   _Obj  AJ         N                        @� 
 h   n/      暞  Othis  9
       隘   O �   (           
               ? �    @ �,   N   0   N  
 {   N      N  
 �   N   �   N  
 �   N     N  
   N     N  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ` G            K      E   //        �std::_Ref_count_obj2<donut::app::RegisteredFont>::_Destroy 
 >i�   this  AJ        +  AJ @       M        o/  : M        �.  : M        �.  &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N N N                       @� " h   �  �  �.  �.  o/  }/  ~/   0   i�  Othis  9+       �   9=       �   O�   0           K        $       ? �   @ �E   A �,   H   0   H  
 �   H   �   H  
 �   H   �   H  
   H     H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 3烂   �   �   H G                      �        �std::_Ref_count_base::_Get_deleter 
 >�   this  AJ          D    >   __formal  AK          D                           @�     �  Othis       O__formal  O�   0                   $       � �    � �   � �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 �   �      �  
 H冹HH峀$ �    H�    H峀$ �    �
   �      �      w      �   �   F G                       �        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �
            J �   K �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H�
    �    �   �      �      �   �   � G                     �/        坰td::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::_Xlength 
 Z   �   (                      @        $LN3  O  �   (              �
            a �   b �,   *   0   *  
 �   �   �   �  
 �   *      *  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �   <   �      �   v  n G            A      A   �.        �std::allocator<std::shared_ptr<donut::app::RegisteredFont> >::deallocate 
 >H�   this  AJ          AJ ,       D0   
 >   _Ptr  AK        @ /   >e   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        �  
#

 Z   �   >e    _Ptr_container  AJ       (    AJ ,       >e    _Back_shift  AH         AH ,       N N (                      H  h   �  �         $LN18  0   H�  Othis  8     O_Ptr  @   e  O_Count  O  �   8           A   �-     ,       � �   � �2   � �6   � �,   (   0   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 0  (   4  (  
 Q  (   U  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 6  �   :  �  
 �  (   �  (  
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >1   this  AJ                                 @     1  Othis  O�   0              �     $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H           }      }      �    20    2           ~      ~      �   
 
4 
2p    B                       �    20    <           �      �      �   
 
4 
2p    B           �      �      �    20    <           �      �      �   
 
4 
2p    B           �      �      �    �                  �      �      �    B             x      �       "           �      �      �   h                           �   2 B             x      
       "           �      �         h                           �   2 B             x             "           �      �         h                 "          �   2
 
4 
2p           x      +       �           �      �      %   h           .      1          �   H  20    !           �      �      4    B             x      @       "           �      �      :   h           C      F          �   2 B             x      O       "           �      �      I   h           R      U          �   2 B             x      ^       "           �      �      X   h           a      d          �   2 B             x      m       "           �      �      g   h           p      s          �   2 B             x      |       "           �      �      v   h                 �          �   2 B             x      �       "           �      �      �   h           �      �          �   2 d T 4 2p           x      �       }           �      �      �   h           �      �          �   � 20    [           �      �      �    20    e           �      �      �    20    z           �      �      �    R�	p`P0           x      �       �          �      �      �   (           �      �   *    �6    .    .    .    .    .    .    .    .    .    .    .    .    .    .    .    �    6    .    .             c   
   i      n      q      r   !   s   &   t   +   u   0   v   5   ^   :   _   ?   `   D   a   I   b   N   f   S   �   Y   \   ^   ]   c   g   h   h   � �"V&�*
 
4 
2p           x      �                 �      �      �   h           �      �          �   iQ  20               �      �      �   ! t               �      �      �      E           �      �      �   !                 �      �      �   E   K           �      �      �   -  �`P      �      y       !           �      �      �   ! h
     !          �      �      �   !   X           �      �      �   ! t# 4"   Q         �      �      �   X             �      �      �   !       !          �      �      �     Q          �      �      �    20    .           �      �           B      A           �      �          20    j           �      �          B                 �      �          20               �      �         ! t               �      �            E           �      �         !                 �      �         E   K           �      �      $   - 4 2��
p`P           x      3       n          �      �      -   (           6      9       .    .       Y      d      j   �� � 5 d 4 2p           x      E       �           �      �      ?   `       H   < 6H 4 R�p`           x      T       �          �      �      N   (           W      Z       �6    \    �   C      p   
   C   
4U@� 2P               p      p      ]   Q��
 d T 4 ���p           x      l       �          �      �      f   (           o      r   
    P2    p   $      +   d �� � � � O�� r0    (           �      �      x    r0    (           �      �      ~    d 4 2p    R           �      �      �   
 
4 
2p    0           �      �      �    x 	h �      M           �      �      �   	 	h b      9           �      �      �    d 4 2p    y           �      �      �   % � , P      �      y       H           �      �      �   !( (� x �( �+     H          �      �      �   H   �           �      �      �   !3 3h .t1 *4/ "� � �) �* d0 H   �       $   �   (   �   ,   �   �             �      �      �   !   H   �          �      �      �     e          �      �      �   !   �  x     H          �      �      �   e  �          �      �      �   !       H          �      �      �   �  1          �      �      �   
 
4 
2p    �           �      �      �   " h� p      �     y       5           �      �      �   ! 4     5          �      �      �   5   �          �      �      �   !       5          �      �      �   �  �          �      �      �    20    �           �      �      �   
 
4 
Rp    �           �      �      �    B                 �      �      �   
 T 4 ����p`           x      �                 �      �      �   (           �      �       2    p2    P   o      E   
   +   
� z:	�  2P                o      o      �   � � :N��� d T 4 2p           x             �           �      �         h                           �   J=O 20               �      �         ! t               �      �            E           �      �          !                 �      �         E   K           �      �      &   -
 
4	 
Rp    s           �      �      /    20               �      �      5   ! t               �      �      5      E           �      �      ;   !                 �      �      5   E   K           �      �      A   - d 2p               �      �      J   ! 4               �      �      J      p           �      �      P   !                 �      �      J   p   q           �      �      V   .E 20               �      �      _   ! t               �      �      _      E           �      �      e   !                 �      �      _   E   K           �      �      k   - 20    +           �      �      t    20    +           �      �      z    R����
p`0           x      �       �          �      �      �   8               �      �   	   �            �   �       W   � �]  BP0      =           W      W      �     20    d           �      �      �    B      :           �      �      �                               �      �      �   Unknown exception                             �      �      �                                     �      �   bad array new length                                �      �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                   .?AVbad_alloc@std@@     �              ����                      �      �                   .?AVexception@std@@     �               ����                      �      �       ����    ����        ��������                                                                                                                                          �      �      �       �   (   �   0   �   8   �   @   �   H   �   P   �   X   �   `   �   h   �   p   �   x   �                                                                                                                                               *      @      �      �       8   (   7   0   9   8   �   @   :   H   2   P   3   X   4   `   5   h   6   p   �   x   �   �   �     vector too long                                             9      H      I      K       �                                               H      N      O      Q       �                                         �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                         �                                  �              ����    @                   �      �                                         �                                     	                                                �              ����    @                   �                         .?AV_Ref_count_base@std@@     �                                                           ����    @                                                                        !                         .?AVIRenderPass@app@donut@@     �                         $                   '               ����    @                         !                                         -      0      *                   .?AVImGui_Renderer@app@donut@@     �                         3                           6      '              ����    @                   -      0                                         <      ?      9                   .?AV?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@     �                         B                           E                    ����    @                   <      ?                                         K      N      H                   .?AV?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@     �                         Q                           T                    ����    @                   K      N      ?  �?  ��   �   J   H 
鵔        std::_Ref_count_obj2<donut::app::RegisteredFont>::`vftable'      �      �  
    �   @   > 
鵔        std::_Ref_count_obj2<donut::vfs::Blob>::`vftable'    �      �  
    �   1   / 
fR        donut::app::IRenderPass::`vftable'       �      �  
    �   (   & 
僎        std::exception::`vftable'    �      �  
    �   (   & 
僎        std::bad_alloc::`vftable'    �      �  
    �   3   1 
僎        std::bad_array_new_length::`vftable'     �      �  
    �   4   2 
喟        donut::app::ImGui_Renderer::`vftable'    �      �  
 �済碎/>
藟�り瑩シ)叜月o"0h�K蜌�(U!a�6�,牘嶈笎�偛+弮�8%�;jQ圗�
筙<M揅�)?ê�!w筀:\[U"枡k[Kw�	�Oo�(！
Z�kN4鹿＊3>飖9屓╘媉�Y鵋�(鴖I!f欷{隒躅�$phxl67獄呏M烀�<�68曤m'+撫娟P鼇葰hc5綃uB刚-悼6�=π-伬&�'m繅鷍�3L$稓懬Nyg逰.�棷��:P
��
橎咑粎擕�9彲X鳀D鬝X�蒝�)�kⅢ鯏艥]$C鼅@靓.�
橎咑籜鳀D鬝X傩u�	紌+UZ飏甬�/��$B�隻e湩鈘<着盹嚡�V[墺RQk�参磗�(�-
暦�&;冚臂ep禭84玴�嚤踖p禭;l笽蚧�6萪O�ecn鵎霵婬(�V蝔航n镧'項j跂`穫�'項j?�埬�'項jKe
仱g.�'項j�	
蘜4!�'項j3�恌罇债�'項j彙莏qD鑟�'項j&愉}�J5�'項j鎍ｕ=J闻�'項jOr瓠径鋲+^{3矮祫�6厅漒夂嫑�笐鵘竫锶劫Fk{省D�-e劫Fk{4vЪL��臉
孷迃2枂�$愜w獛啯軰hz殌氋$愜w獛啯�}巾舐$愜w獛啯^暼0擎�$愜w獛啯x:�>kO�:~�&╥|9w�愄邻
磑涋碿-Ugp0C�7
脦僖"mZ繡)�鳪�哦遂祚皛t'牿r+iM禷L粆F誁�-!n&>鉜GUmE葛翈}1
傂k鞫遂祚皛t�)+嵖<嶀預棊膬搃捊z炁嶀預棊膬�(!面�,Fql#�'邯<?3⒔s⑶暳�#�僒O齿B�嶿JI怣�9H眭br�! 帴ou銅毋箩邆5>湶螨篟
熉徇�5>&创笠\徇�5>�9:� �蹰k¨d蛻\*u端祆癜~t:�9�.N-兙?>癌焁K龆遂祚皛t續k(s�*端祆癜~t視粑�靌<�桻���稞�Q玍�:�?P^VG�灖痣靛玴`樋厪
岭進♁恗昷+ 埚巌;�緧?諑�|QwI阺1X併�	7湑婍K.睷+喙睏屽�9+ 埚巌;�進♁恗昷�!
濣e	�2x=1綪囓�6铹輲端祆癜~tJ黯鉞$ZN
�耈jj菲x�-z_捛4�7幈o涝汘Ⅹ{暖g�-焊d簚审誥6初B5伻��:>Gf佥8s|積�/�;镩垓浄1,�5G薌衳磩dek琮V薌衳磩h俯��薌衳磩+�:c�&箅 x�S(蒃�9*岕薌衳磩漳A�檾 掴:o椳荖髋H+繊薌衳磩�"�轭�1駏5揎�"L�薌衳磩@凍>!炥L檈3(y翲GE]�>屾薌衳磩蕓擗渗{惖筂�嫗n峬�J�	+�06 D"謿I<<蘟端祆癜~t谤���8SE8�/~ITG5遂祚皛tK%'鯕藅薌衳磩IU炞�5X躔7颦硣K膶N鹈<躔7颦硣K;貺韰y旔k癙纟/摃)蕳o7�螶湭:�.y奆
K�$蟊惺�?辠�v%I栶賑?T�
C魞
e鑖]{謑p茁�潏鮧�讘婣0�"-nN鵘J馐厐ftq F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埻﨏帲晗Di＄燤�'鹴aR�,F_棢杻#Q噤釺�
鮱雵J-WV8oc8曀黩6了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊嘕-WV8om�M%>mb雵J-WV8o.*~襠[
B雵J-WV8o嗹啴r殅W�'鸯t禣質'A瓙dd�a�:6"戕卐]�-:<秌威C帲晗D蓚江埼鑕咞taR�,F_棢杻#Qt~婿4A雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛_鵑lk坈8曀黩6�禎慵╉嗫w>營_V7i1懦蘛攮縵�喟x睟�'Tn嚙Q雵J-WV8o�
,騤-坓�(鬄鮳�>i,夿雵J-WV8o譑iv褍| -坓�(鬄�汬'这栯嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛4(2a�[G`嵇莚Ydd�a�:芍g纋接e玍:Oe'�=>孎搧o觛楟迺�%嶝骰M廢kH挎驻趀钏�;Z]�<訖z哘亝�r�0G^/耰渨鏠edd�a�:疀;婷�毒轫W�8樢閣yQ E<礼\+b鶔�(疬邷kg莖宱� �=r#
dd�a�:笈w末楼龌橀@y�蜷+X�
6e<F\飦i5絚_}46e<F\飦i5絚_}4c闲�
墸g蹃灬腤e�9E\$L釉��E光蛰糘�*L垣"�/郔x
Z梋f�蝿壒eV2牊,觎�'Q鶘2m46縌蓡�6妱愛褜斃才廱埨憇V佁C膸x�-鈲Nhb碉!Re禄笗萦b��.擜�/踭虲b{氌栟沰UG]z�?囟~�=唁T矷v5o�9E\$L釉抻@JstTK|Ne囀侯鄏俼�5vp?h鋼y�	�0�9	�+F�$砾-骿$雵J-WV8o鸗鍊/绷暋m-u"塣d\蜩kU-坓�(鬄� WJv�.�:奷鞥曞xP},"dd�a�:(<�0�#枑>朓;紭议wyQ5R犵�喪t[谍漲�.�?覡spU炡W穮鹴aR�,F_棢杻#Qe鼄�н慷^蓳k踟)雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛萧�埨嘇F�辛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛?M钚^再�(i9x�奝k6G磌缘:逻戇�9桸瘟妬
啶鳺菈ouK�=雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)獗G黋p脢�&!y*�杜`颀l+�鞯.r擣�0G#盱谑G
.'穤s;嗐8�1�8]Z��梾�n4�硓橂嘕-WV8o�&9�=NB-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H8r:.^�$E&啛麢欵/�%G>禡h�,4��;儗,4��;儗�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2渇"h孟�/O"�:邍A愦靮鸬2�>料C5��\&2�汪菻鳿M%ZZ�$为赞G刹~赣 "^惋砤��\&2湕竤^榹%ZZ�$为赞G刹~赣 "^惋砤��\&2淿H�!T愥M%ZZ�$为赞G刹~赣 "^惋砤荣瀀CRC冼�^笵A傮茍0牊+坚嬮鴢諱�%a?銸瞂-a�5曎$�N鷃0�$跆隫索�!;氛�!�\eL
        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       j                .debug$S       `O              .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn       q       
z鈕     .debug$S       �             .text$mn    	   �  	   
墟     .debug$S    
   <
  T       	    .text$x        =      M錌�	    .text$mn       d      "�
V     .debug$S    
   h             .text$mn       s      �?珿     .debug$S         
           .text$mn               _葓�     .debug$S       8             .text$mn              �邆     .debug$S                    .text$mn       �     遂%     .debug$S       P  �           .text$x              �/�    .text$x              
@獕    .text$x              /f    .text$x              朆|�    .text$x              V荱b    .text$x              ;E�    .text$x              团僡    .text$x              燝袓    .text$x              取j    .text$x              aJ騿    .text$x               検wi    .text$x     !         黈�*    .text$x     "         `摡�    .text$x     #         �,<    .text$x     $         麘�    .text$x     %         ;V8    .text$x     &         V��    .text$x     '         ��;    .text$x     (         `摡�    .text$x     )         麘�    .text$mn    *   n     狕%c     .debug$S    +   �  (       *    .text$x     ,         �-Va*    .text$x     -         �2勨*    .text$x     .         Wjc*    .text$mn    /   �       (回     .debug$S    0   �          /    .text$mn    1          袁z\     .debug$S    2   �          1    .text$mn    3   <      .ズ     .debug$S    4   0  
       3    .text$mn    5   <      .ズ     .debug$S    6   L  
       5    .text$mn    7   !      :著�     .debug$S    8   <         7    .text$mn    9   2      X于     .debug$S    :   <         9    .text$mn    ;   "       坼	     .debug$S    <   �         ;    .text$mn    =   "       坼	     .debug$S    >   �         =    .text$mn    ?   "       坼	     .debug$S    @   �         ?    .text$mn    A   "       坼	     .debug$S    B   �         A    .text$mn    C   "       坼	     .debug$S    D   �         C    .text$mn    E   "       坼	     .debug$S    F   �         E    .text$mn    G   "       坼	     .debug$S    H   �         G    .text$mn    I   "       坼	     .debug$S    J   �         I    .text$mn    K   "       坼	     .debug$S    L   �         K    .text$mn    M   e      D远     .debug$S    N   �         M    .text$mn    O   [       荘�     .debug$S    P            O    .text$mn    Q         峦諡     .debug$S    R            Q    .text$mn    S         峦諡     .debug$S    T   $         S    .text$mn    U   }      1�-�     .debug$S    V   �         U    .text$mn    W   K       }'     .debug$S    X   �         W    .text$mn    Y   K       }'     .debug$S    Z   �         Y    .text$mn    [   K       }'     .debug$S    \   �         [    .text$mn    ]   K       }'     .debug$S    ^   �         ]    .text$mn    _   .      顎�     .debug$S    `   �         _    .text$mn    a   z      �.�     .debug$S    b   �         a    .text$mn    c   j      ︳4}     .debug$S    d   p         c    .text$mn    e   �      4;�     .debug$S    f   �  *       e    .text$mn    g          .B+�     .debug$S    h   �          g    .text$mn    i        摫x     .debug$S    j   P  Z       i    .text$mn    k   �      随拷     .debug$S    l            k    .text$mn    m          .B+�     .debug$S    n   �          m    .text$mn    o         ��#     .debug$S    p   �          o    .text$mn    q         ��#     .debug$S    r   �          q    .text$mn    s   +      n鯬7     .debug$S    t   �          s    .text$mn    u   +      z�     .debug$S    v   �          u    .text$mn    w   !      -嵎     .debug$S    x   �          w    .text$mn    y   �      稊3z     .debug$S    z   �         y    .text$mn    {   B      贘S     .debug$S    |             {    .text$mn    }   B      贘S     .debug$S    ~            }    .text$mn       B      贘S     .debug$S    �   �              .text$mn    �   H       襶.      .debug$S    �   �         �    .text$mn    �          .B+�     .debug$S    �   0         �    .text$mn    �   1     业p9     .debug$S    �   �         �    .text$mn    �          .B+�     .debug$S    �   �         �    .text$mn    �          .B+�     .debug$S    �   �          �    .text$mn    �         aH     .debug$S    �   $         �    .text$mn    �   �      Ⅻw�     .debug$S    �   �         �    .text$mn    �   �     K9     .debug$S    �   �	  4       �    .text$x     �         Kバg�    .text$x     �         :�藦    .text$mn    �   (      ?鸗�     .debug$S    �   �         �    .text$mn    �   (      � 瘂     .debug$S    �   �         �    .text$mn    �     	   醄齦     .debug$S    �     H       �    .text$x     �         :�藯    .text$x     �         Kバg�    .text$x     �          �j)�    .text$mn    �   Q  
   蔪     .debug$S    �   �         �    .text$mn    �          .B+�     .debug$S    �   `  
       �    .text$mn    �   �     l%鲋     .debug$S    �   t         �    .text$mn    �   �      島C�     .debug$S    �   �         �    .text$mn    �         V牉     .debug$S    �            �    .text$mn    �   �     佯]�     .debug$S    �   �  (       �    .text$x     �         莠��    .text$x     �         �摩    .text$x     �         鶖v    .text$mn    �          簎x�     .debug$S    �   \  
       �    .text$mn    �          簎x�     .debug$S    �   d  
       �    .text$mn    �          簎x�     .debug$S    �   \  
       �    .text$mn    �   0      e茞�     .debug$S    �   �         �    .text$mn    �          簎x�     .debug$S    �   �         �    .text$mn    �   R      0�     .debug$S    �   X         �    .text$mn    �          簎x�     .debug$S    �   �         �    .text$mn    �   y      r薿K     .debug$S    �   �         �    .text$mn    �          簎x�     .debug$S    �   T  
       �    .text$mn    �   M      i憷�     .debug$S    �   �         �    .text$mn    �          簎x�     .debug$S    �   d  
       �    .text$mn    �   9      n%蔃     .debug$S    �   �         �    .text$mn    �   	        偨     .debug$S    �   �          �    .text$mn    �          .B+�     .debug$S    �             �    .text$mn    �   �      戶     .debug$S    �   T         �    .text$mn    �          .B+�     .debug$S    �   �          �    .text$mn    �          簎x�     .debug$S    �   �          �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �   
       肷瞰     .debug$S    �   @  
       �    .text$mn    �   K       �)羈     .debug$S    �   �         �    .text$mn    �          �猴     .debug$S    �   ,         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   $         �    .text$mn    �   A      俙Z%     .debug$S    �   �         �    .text$mn    �         崪覩     .debug$S    �   �          �        \       �        x                �                �                �                �                �                �            malloc                                             4      9        U      q        o      �        �              �          i�                   �      3        �      {        
          i�                   ,      7        Q      o        v      5        �      }        �          i�                   �      �                       =      1        g      /        �      E        �      I        �              $      ;        \      e        �      m        �      �        �                              l      g        �      �        �      �        �      �        7      �        `      �        �      �        �      �        �      �        2      �        g      �        �      �        �      �              �        =      �        s      w        �          i�                   �               �               .	               N	               t	               �	               �	               �	               �	               �	               +
               O
               l
               �
               �
               �
               �
                              (               L               q               �               �                              z               �      A        �      ?        �      K        /
      G        a
      =        �
      C        �
      U        �      O        N      M        �      a        �               5               h               �               �              �      i              Y        O      �        �      �        �      _              �        �      c        -      �        �      [        �      *        -      k        R      �        �      �        ?      �        �      �        '      �        ^      �        �      �        �      �              �        <      �        h      �        �      �        �      �              �        R      �        �      �        �      �        @      y        j          i?                   �               �      ]        �              V      W        �              ]      S        �      �        �      �        )      u        l          iJ                   �      Q        �      �              �        ]      s        �          iP                   �      	        �              =!              P"              �"              �#              �#      ,        :$      �        �$      �        E%               w%      !        �%      "        �%      #        
&      $        ?&      %        q&      &        �&              �&      -        '      �        �'      '        �'      (        	(      )        ;(              l(      .        �(      �        W)      �        �)      �        H*              y*      �        +      �        j+              �+              �+              �+              .,              _,              �,               �,               �,               �,           memcpy           memset           $LN13       �    $LN5        9    $LN10           $LN7        3    $LN13       {    $LN10       5    $LN16       }    $LN3        �    $LN4        �    $LN10       E    $LN10       I    $LN10       ;    $LN48       e    $LN8        w    $LN10       A    $LN10       ?    $LN10       K    $LN10       G    $LN10       =    $LN10       C    $LN77       U    $LN30   [   O    $LN33       O    $LN33   e   M    $LN36       M    $LN28   z   a    $LN31       a    $LN226  �      $LN229          $LN97     i    $LN100      i    $LN18       Y    $LN18       �    $LN14       _    $LN18   A   �    $LN21       �    $LN28   j   c    $LN31       c    $LN3       �    $LN4        �    $LN18       [    $LN110      *    $LN26       k    $LN74       �    $LN136      �    $LN7        �    $LN7        �    $LN16       �    $LN4        �    $LN4        �    $LN4        �    $LN24       �    $LN60       �    $LN41       �    $LN20       �    $LN10       �    $LN4        �    $LN4        �    $LN135      �    $LN31       y    $LN18       ]    $LN29           $LN18       W    $LN33           $LN28       �    $LN8        u    $LN8        s    $LN84   �  	        �,         $LN89       	    $LN30           $LN14   :       $LN17           .xdata      �          F┑@�        �-      �    .pdata      �         X賦鷣        .      �    .xdata      �          （亵9        ;.      �    .pdata      �          T枨9        d.      �    .xdata      �          %蚘%        �.      �    .pdata      �         惻竗        �.      �    .xdata      �          （亵3        �.      �    .pdata      �         2Fb�3        /      �    .xdata      �          %蚘%{        */      �    .pdata      �         惻竗{        Q/      �    .xdata      �          （亵5        w/      �    .pdata      �         2Fb�5        �/      �    .xdata      �          %蚘%}        �/      �    .pdata      �         惻竗}        0      �    .xdata      �          懐j炞        A0      �    .pdata      �         Vbv        q0      �    .xdata      �         /
        �0      �    .pdata      �         +eS籈        �0      �    .xdata      �   	      �#荤E        1      �    .xdata      �         jE        [1      �    .xdata      �          3狷 E        �1      �    .xdata      �         /
        �1      �    .pdata      �         +eS籌        2      �    .xdata      �   	      �#荤I        R2      �    .xdata      �         jI        �2      �    .xdata      �          3狷 I        �2      �    .xdata      �         /
�;        	3      �    .pdata      �         +eS�;        I3      �    .xdata      �   	      �#荤;        �3      �    .xdata      �         j;        �3      �    .xdata      �          3狷 ;        4      �    .xdata      �         �酑e        T4      �    .pdata      �          鮩se        �4      �    .xdata         	      �#荤e        �4          .xdata              je        �4         .xdata               爲飆e        5         .xdata               （亵w        J5         .pdata              萣�5w        y5         .xdata              /
        �5         .pdata              +eS籄        �5         .xdata        	      �#荤A        6         .xdata              jA        S6         .xdata      	         3狷 A        �6      	   .xdata      
        /
�?        �6      
   .pdata              +eS�?        
7         .xdata        	      �#荤?        J7         .xdata      
        j?        �7      
   .xdata               3狷 ?        �7         .xdata              /
        8         .pdata              +eS籏        J8         .xdata        	      �#荤K        �8         .xdata              jK        �8         .xdata               3狷 K        9         .xdata              /
        =9         .pdata              +eS籊        w9         .xdata        	      �#荤G        �9         .xdata              jG        �9         .xdata               3狷 G        .:         .xdata              /
�=        j:         .pdata              +eS�=        �:         .xdata        	      �#荤=        �:         .xdata              j=        ;         .xdata               3狷 =        W;         .xdata              /
        �;         .pdata              +eS籆        �;         .xdata         	      �#荤C        <          .xdata      !        jC        \<      !   .xdata      "         3狷 C        �<      "   .xdata      #        vQ9	U        �<      #   .pdata      $        A刄7U        �=      $   .xdata      %  	      �#荤U        k>      %   .xdata      &        jU        -?      &   .xdata      '         強S�U        �?      '   .xdata      (         （亵O        稝      (   .pdata      )        愶LO        圓      )   .xdata      *         （亵M        XB      *   .pdata      +        弋楳        朇      +   .xdata      ,         （亵a        覦      ,   .pdata      -        X崘=a        "E      -   .xdata      .        LA銺        pE      .   .pdata      /        �)俫        欵      /   .xdata      0  	      � )9        肊      0   .xdata      1  l      � 1-        顴      1   .xdata      2  	       上�        !F      2   .xdata      3        �酑i        MF      3   .pdata      4        垐觟        wF      4   .xdata      5  	      �#荤i        燜      5   .xdata      6        ji        蘁      6   .xdata      7         .�.Mi        﨔      7   .xdata      8         （亵Y        *G      8   .pdata      9        � 資        bG      9   .xdata      :        范^揧        橤      :   .pdata      ;        鳶�Y        褿      ;   .xdata      <        @鴚`Y        H      <   .pdata      =        [7躖        DH      =   .voltbl     >         飾殪Y    _volmd      >   .xdata      ?        Ｄ�[�        }H      ?   .pdata      @        萣�5�        篐      @   .xdata      A        �l�        鯤      A   .pdata      B        �W�        4I      B   .xdata      C        纩﹖�        rI      C   .pdata      D        � ��        癐      D   .xdata      E        $垕寽        領      E   .pdata      F        荃雺�        ,J      F   .xdata      G         （亵_        jJ      G   .pdata      H        dp_        豃      H   .xdata      I         �9��        EK      I   .pdata      J        s�7遨        覭      J   .xdata      K         （亵c        `L      K   .pdata      L        s�+Ac        餖      L   .xdata      M         �9��        M      M   .pdata      N        �1百        N      N   .xdata      O         （亵[        ∟      O   .pdata      P        � 賉        镹      P   .xdata      Q        范^揫        )O      Q   .pdata      R        鳶�[        kO      R   .xdata      S        @鴚`[        璒      S   .pdata      T        [7躘        颫      T   .voltbl     U         飾殪[    _volmd      U   .xdata      V        犦�*        1P      V   .pdata      W        =�c*        sP      W   .xdata      X  	      � )9*        碢      X   .xdata      Y        椨噾*        鳳      Y   .xdata      Z         枯�*        BQ      Z   .voltbl     [         麶嬕*    _volmd      [   .xdata      \        圇�
k        哘      \   .pdata      ]        v斤閗        砆      ]   .xdata      ^        Mw2檏        逹      ^   .xdata      _         懎wpk        R      _   .voltbl     `         譞駅    _volmd      `   .xdata      a        謕5鳓        =R      a   .pdata      b        誔粊�        烺      b   .xdata      c  	      � )9�         S      c   .xdata      d        g蟼�        dS      d   .xdata      e         d瓓(�        蜸      e   .xdata      f         k功        2T      f   .pdata      g        �$剧�              g   .voltbl     h         吲F�    _volmd      h   .xdata      i         �g嫗�        U      i   .pdata      j        /^��        甎      j   .xdata      k  	      � )9�        HV      k   .xdata      l  
      诩��        錠      l   .xdata      m  
       ?�        圵      m   .voltbl     n         镯+洀    _volmd      n   .xdata      o         c%C創        %X      o   .pdata      p        銀�*�        淴      p   .xdata      q         c%C剷        Y      q   .pdata      r        銀�*�        揧      r   .xdata      s         O淼        Z      s   .pdata      t        霍�        RZ      t   .xdata      u         %蚘%�        怹      u   .pdata      v        }S蛥�        衂      v   .xdata      w         齁狶�        [      w   .pdata      x        <讟步        L[      x   .xdata      y         R�0%�        圼      y   .pdata      z        VH倸�        萚      z   .xdata      {         *贒�        \      {   .pdata      |        粖彻        H\      |   .xdata      }        t琲W�        圽      }   .pdata      ~        X賦鷧        糪      ~   .xdata               w�>�        颸         .pdata      �        〤q'�        $]      �   .xdata      �  0      \	U�        Y]      �   .pdata      �        C聟        廬      �   .xdata      �        E�&瀰        臸      �   .pdata      �        F檒%�        鸧      �   .xdata      �        F(h詤        1^      �   .pdata      �        �*�        g^      �   .xdata      �        �3/C�        漗      �   .pdata      �        >w獏        觀      �   .xdata      �         %蚘%�        	_      �   .pdata      �        具3芮        S_      �   .xdata      �        @Y牋        淿      �   .pdata      �        ]-蜖        輄      �   .xdata      �        ]y軤        `      �   .pdata      �        H墹珷        _`      �   .xdata      �        醴zt�              �   .pdata      �        
忤谞        鉦      �   .xdata      �         （亵�        %a      �   .pdata      �        o�*銔        fa      �   .xdata      �         ug刉�              �   .pdata      �        緥��        靉      �   .xdata      �         �9��        1b      �   .pdata      �        28~v�        pb      �   .xdata      �         ;罈        産      �   .pdata      �        );k魲        /c      �   .xdata      �  	      � )9�        痗      �   .xdata      �        袼鹹�        2d      �   .xdata      �         览0j�        籨      �   .xdata      �         k箺        >e      �   .pdata      �        Vbv鶙        蝒      �   .voltbl     �         �{Y�    _volmd      �   .xdata      �        vQ9	y        ]f      �   .pdata      �        �y        廸      �   .xdata      �  	      �#荤y        纅      �   .xdata      �        jy        鬴      �   .xdata      �         �鐈        .g      �   .voltbl     �         �媽y    _volmd      �   .xdata      �         （亵]        bg      �   .pdata      �        � 賋              �   .xdata      �        范^揮        鏶      �   .pdata      �        鳶�]        +h      �   .xdata      �        @鴚`]        oh      �   .pdata      �        [7躚        砲      �   .voltbl     �         飾殪]    _volmd      �   .xdata      �         写販        鱤      �   .pdata      �        s栠"        ei      �   .xdata      �         （亵W        襥      �   .pdata      �        � 賅        	j      �   .xdata      �        范^揥        ?j      �   .pdata      �        鳶�W        wj      �   .xdata      �        @鴚`W        痡      �   .pdata      �        [7躓        鏹      �   .voltbl     �         飾殪W    _volmd      �   .xdata      �         n�v        k      �   .pdata      �        V6�>        �k      �   .xdata      �        0��        辧      �   .pdata      �        筲sL        縨      �   .xdata      �        很蓢        爊      �   .pdata      �        �癉        乷      �   .voltbl     �         3急
    _volmd      �   .xdata      �         （亵�        bp      �   .pdata      �        � 儆        痯      �   .xdata      �        范^撚        鹥      �   .pdata      �        鳶��        Iq      �   .xdata      �        @鴚`�        梣      �   .pdata      �        [7苡        錻      �   .voltbl     �         飾殪�    _volmd      �   .xdata      �         （亵u        3r      �   .pdata      �         ~        ~r      �   .xdata      �         （亵s        萺      �   .pdata      �         ~        	s      �   .xdata      �        u苩�	        Is      �   .pdata      �        �5�:	        \t      �   .xdata      �  
      B>z]	        nu      �   .xdata      �         �2g�	        僾      �   .xdata      �        T�8	        瀢      �   .xdata      �        r%�	        眡      �   .xdata      �  	       �5啒	        葃      �   .xdata      �         M[�	        輟      �   .pdata      �        現�	         |      �   .voltbl     �         熄�	    _volmd      �   .voltbl     �                 _volmd      �   .xdata      �         （亵        "}      �   .pdata      �        AT        =~      �   .xdata      �         �9�        W      �   .pdata      �        礝
        �      �   .rdata      �                     �     �   .rdata      �         �;�         '�      �   .rdata      �                     N�     �   .rdata      �                     e�     �   .rdata      �         �)         噣      �   .xdata$x    �                     硛      �   .xdata$x    �        虼�)         諃      �   .data$r     �  /      嶼�         鴢      �   .xdata$x    �  $      4��         �      �   .data$r     �  $      鎊=         r�      �   .xdata$x    �  $      銸E�         寔      �   .data$r     �  $      騏糡         藖      �   .xdata$x    �  $      4��         鍋      �       $�           .data       �          烀�          7�      �       k�     �   .rdata      �  �                   拏     �   .rdata      �  �                   眰     �   .rdata      �         �$剷         觽      �   .rdata      �         IM         鑲      �   .rdata      �  (                   �     �   .rdata      �  (                   I�     �   .rdata$r    �  $      'e%�         z�      �   .rdata$r    �        �          拑      �   .rdata$r    �                     ▋      �   .rdata$r    �  $      Gv�:         緝      �   .rdata$r    �  $      'e%�         輧      �   .rdata$r    �        }%B         鮾      �   .rdata$r    �                     �      �   .rdata$r    �  $      `         !�      �   .rdata$r    �  $      'e%�         @�      �   .rdata$r    �        �弾         c�      �   .rdata$r    �                     剟      �   .rdata$r    �  $      H衡�               �   .data$rs    �  *      8V綊         蟿      �   .rdata$r    �        �          飫      �   .rdata$r    �                     �      �   .rdata$r    �  $      Gv�:         '�      �   .rdata$r    �  $      'e%�         L�      �   .data$rs    �  ,      ?氯p         l�      �   .rdata$r    �        �          巺      �   .rdata$r    �                     瑓      �   .rdata$r    �  $      Gv�:         蕝      �   .rdata$r    �  $      'e%�         駞      �   .data$rs       /      sm�         �          .rdata$r            }%B         9�         .rdata$r                         Z�         .rdata$r      $      `         {�         .rdata$r      $      'e%�                  .data$rs      H      w(         釂         .rdata$r            }%B         �         .rdata$r                         Y�         .rdata$r      $      `         搰         .rdata$r    	  $      'e%�         謬      	   .data$rs    
  >      �,�         �      
   .rdata$r            }%B         <�         .rdata$r                         l�         .rdata$r    
  $      `         湀      
   .rdata               =-f�         請         .rdata               v靛�         鍒         .rdata               V6]`         鯃             �           _fltused         .debug$S      X          �   .debug$S      L          �   .debug$S      @          �   .debug$S      4          �   .debug$S      4          �   .debug$S      @          �   .debug$S      @          �   .chks64       �                �  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ??0RenderState@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ?GetWindowDimensions@DeviceManager@app@donut@@QEAAXAEAH0@Z ?GetDeviceParams@DeviceManager@app@donut@@QEAAAEBUDeviceCreationParameters@23@XZ ??1IRenderPass@app@donut@@UEAA@XZ ?SetLatewarpOptions@IRenderPass@app@donut@@UEAAXXZ ?ShouldRenderUnfocused@IRenderPass@app@donut@@UEAA_NXZ ?Render@IRenderPass@app@donut@@UEAAXPEAVIFramebuffer@nvrhi@@@Z ?Animate@IRenderPass@app@donut@@UEAAXM@Z ?BackBufferResizing@IRenderPass@app@donut@@UEAAXXZ ?BackBufferResized@IRenderPass@app@donut@@UEAAXIII@Z ?DisplayScaleChanged@IRenderPass@app@donut@@UEAAXMM@Z ?KeyboardUpdate@IRenderPass@app@donut@@UEAA_NHHHH@Z ?KeyboardCharInput@IRenderPass@app@donut@@UEAA_NIH@Z ?MousePosUpdate@IRenderPass@app@donut@@UEAA_NNN@Z ?MouseScrollUpdate@IRenderPass@app@donut@@UEAA_NNN@Z ?MouseButtonUpdate@IRenderPass@app@donut@@UEAA_NHHH@Z ?JoystickButtonUpdate@IRenderPass@app@donut@@UEAA_NH_N@Z ?JoystickAxisUpdate@IRenderPass@app@donut@@UEAA_NHM@Z ??_GIRenderPass@app@donut@@UEAAPEAXI@Z ??_EIRenderPass@app@donut@@UEAAPEAXI@Z ?CreateContext@ImGui@@YAPEAUImGuiContext@@PEAUImFontAtlas@@@Z ?DestroyContext@ImGui@@YAXPEAUImGuiContext@@@Z ?GetIO@ImGui@@YAAEAUImGuiIO@@XZ ?GetStyle@ImGui@@YAAEAUImGuiStyle@@XZ ?NewFrame@ImGui@@YAXXZ ?Render@ImGui@@YAXXZ ?Begin@ImGui@@YA_NPEBDPEA_NH@Z ?End@ImGui@@YAXXZ ?SetNextWindowPos@ImGui@@YAXAEBUImVec2@@H0@Z ?SetNextWindowSize@ImGui@@YAXAEBUImVec2@@H@Z ?SetNextWindowBgAlpha@ImGui@@YAXM@Z ?PushStyleVar@ImGui@@YAXHM@Z ?PopStyleVar@ImGui@@YAXH@Z ?SetCursorPosX@ImGui@@YAXM@Z ?SetCursorPosY@ImGui@@YAXM@Z ?TextUnformatted@ImGui@@YAXPEBD0@Z ?CalcTextSize@ImGui@@YA?AUImVec2@@PEBD0_NM@Z ??0ImGuiStyle@@QEAA@XZ ?ScaleAllSizes@ImGuiStyle@@QEAAXM@Z ?AddInputCharacter@ImGuiIO@@QEAAXI@Z ??0ImFontConfig@@QEAA@XZ ?AddFontDefault@ImFontAtlas@@QEAAPEAUImFont@@PEBUImFontConfig@@@Z ?AddFontFromMemoryTTF@ImFontAtlas@@QEAAPEAUImFont@@PEAXHMPEBUImFontConfig@@PEBG@Z ?AddFontFromMemoryCompressedTTF@ImFontAtlas@@QEAAPEAUImFont@@PEBXHMPEBUImFontConfig@@PEBG@Z ?Clear@ImFontAtlas@@QEAAXXZ ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$list@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ ??1?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@QEAA@XZ ?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z ?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ ?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z ?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ ??0ImGui_NVRHI@app@donut@@QEAA@XZ ??1ImGui_NVRHI@app@donut@@QEAA@XZ ??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ ?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z ?ReleaseScaledFont@RegisteredFont@app@donut@@IEAAXXZ ??1?$unique_ptr@UImGui_NVRHI@app@donut@@U?$default_delete@UImGui_NVRHI@app@donut@@@std@@@std@@QEAA@XZ ?deallocate@?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VRegisteredFont@app@donut@@@2@_K@Z ??1?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@QEAA@XZ ?_Xlength@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@CAXXZ ??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ ??0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z ??1ImGui_Renderer@app@donut@@UEAA@XZ ?Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z ?CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z ?CreateFontFromMemory@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_KM@Z ?CreateFontFromMemoryCompressed@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_KM@Z ?KeyboardUpdate@ImGui_Renderer@app@donut@@UEAA_NHHHH@Z ?KeyboardCharInput@ImGui_Renderer@app@donut@@UEAA_NIH@Z ?MousePosUpdate@ImGui_Renderer@app@donut@@UEAA_NNN@Z ?MouseScrollUpdate@ImGui_Renderer@app@donut@@UEAA_NNN@Z ?MouseButtonUpdate@ImGui_Renderer@app@donut@@UEAA_NHHH@Z ?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z ?Render@ImGui_Renderer@app@donut@@UEAAXPEAVIFramebuffer@nvrhi@@@Z ?BackBufferResizing@ImGui_Renderer@app@donut@@UEAAXXZ ?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z ?BeginFullScreenWindow@ImGui_Renderer@app@donut@@IEAAXXZ ?DrawScreenCenteredText@ImGui_Renderer@app@donut@@IEAAXPEBD@Z ?EndFullScreenWindow@ImGui_Renderer@app@donut@@IEAAXXZ ?CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z ??_GImGui_Renderer@app@donut@@UEAAPEAXI@Z ??_EImGui_Renderer@app@donut@@UEAAPEAXI@Z ??0Blob@vfs@donut@@QEAA@PEAX_K@Z ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ??$make_shared@VRegisteredFont@app@donut@@$$V@std@@YA?AV?$shared_ptr@VRegisteredFont@app@donut@@@0@XZ ??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ ??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z ??1?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z ??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??$_Uninitialized_move@PEAV?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z@4HA ?dtor$0@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$0@?0???0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z@4HA ?dtor$0@?0??CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z@4HA ?dtor$0@?0??Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$10@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$11@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$15@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$16@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$17@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$18@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$19@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$1@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$1@?0???0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z@4HA ?dtor$1@?0??CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z@4HA ?dtor$20@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$25@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$26@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$2@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$2@?0???0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z@4HA ?dtor$2@?0??CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z@4HA ?dtor$2@?0??CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z@4HA ?dtor$2@?0??Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$3@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$3@?0??CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z@4HA ?dtor$3@?0??Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$4@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$5@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$6@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$7@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$8@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA ?dtor$9@?0???0ImGui_NVRHI@app@donut@@QEAA@XZ@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __security_check_cookie __catch$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $pdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $unwind$??_GIRenderPass@app@donut@@UEAAPEAXI@Z $pdata$??_GIRenderPass@app@donut@@UEAAPEAXI@Z $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ $unwind$??1?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@QEAA@XZ $unwind$??0ImGui_NVRHI@app@donut@@QEAA@XZ $pdata$??0ImGui_NVRHI@app@donut@@QEAA@XZ $cppxdata$??0ImGui_NVRHI@app@donut@@QEAA@XZ $stateUnwindMap$??0ImGui_NVRHI@app@donut@@QEAA@XZ $ip2state$??0ImGui_NVRHI@app@donut@@QEAA@XZ $unwind$??1ImGui_NVRHI@app@donut@@QEAA@XZ $pdata$??1ImGui_NVRHI@app@donut@@QEAA@XZ $cppxdata$??1ImGui_NVRHI@app@donut@@QEAA@XZ $stateUnwindMap$??1ImGui_NVRHI@app@donut@@QEAA@XZ $ip2state$??1ImGui_NVRHI@app@donut@@QEAA@XZ $unwind$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $unwind$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $pdata$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $chain$0$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $pdata$0$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $chain$3$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $pdata$3$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $chain$1$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $pdata$1$?CreateScaledFont@RegisteredFont@app@donut@@IEAAXM@Z $unwind$??1?$unique_ptr@UImGui_NVRHI@app@donut@@U?$default_delete@UImGui_NVRHI@app@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$unique_ptr@UImGui_NVRHI@app@donut@@U?$default_delete@UImGui_NVRHI@app@donut@@@std@@@std@@QEAA@XZ $unwind$?deallocate@?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VRegisteredFont@app@donut@@@2@_K@Z $pdata$?deallocate@?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VRegisteredFont@app@donut@@@2@_K@Z $unwind$??1?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@CAXXZ $unwind$??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VRegisteredFont@app@donut@@@std@@QEAA@XZ $unwind$??0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z $pdata$??0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z $cppxdata$??0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z $stateUnwindMap$??0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z $ip2state$??0ImGui_Renderer@app@donut@@QEAA@PEAVDeviceManager@12@@Z $unwind$??1ImGui_Renderer@app@donut@@UEAA@XZ $pdata$??1ImGui_Renderer@app@donut@@UEAA@XZ $cppxdata$??1ImGui_Renderer@app@donut@@UEAA@XZ $ip2state$??1ImGui_Renderer@app@donut@@UEAA@XZ $unwind$?Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $pdata$?Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $cppxdata$?Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $stateUnwindMap$?Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $ip2state$?Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $unwind$?dtor$3@?0??Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA $pdata$?dtor$3@?0??Init@ImGui_Renderer@app@donut@@QEAA_NV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA $unwind$?CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z $pdata$?CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z $cppxdata$?CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z $stateUnwindMap$?CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z $ip2state$?CreateFontFromFile@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@AEAVIFileSystem@vfs@3@AEBVpath@filesystem@5@M@Z $unwind$?CreateFontFromMemory@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_KM@Z $pdata$?CreateFontFromMemory@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_KM@Z $unwind$?CreateFontFromMemoryCompressed@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_KM@Z $pdata$?CreateFontFromMemoryCompressed@ImGui_Renderer@app@donut@@QEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_KM@Z $unwind$?KeyboardUpdate@ImGui_Renderer@app@donut@@UEAA_NHHHH@Z $pdata$?KeyboardUpdate@ImGui_Renderer@app@donut@@UEAA_NHHHH@Z $unwind$?KeyboardCharInput@ImGui_Renderer@app@donut@@UEAA_NIH@Z $pdata$?KeyboardCharInput@ImGui_Renderer@app@donut@@UEAA_NIH@Z $unwind$?MousePosUpdate@ImGui_Renderer@app@donut@@UEAA_NNN@Z $pdata$?MousePosUpdate@ImGui_Renderer@app@donut@@UEAA_NNN@Z $unwind$?MouseScrollUpdate@ImGui_Renderer@app@donut@@UEAA_NNN@Z $pdata$?MouseScrollUpdate@ImGui_Renderer@app@donut@@UEAA_NNN@Z $unwind$?MouseButtonUpdate@ImGui_Renderer@app@donut@@UEAA_NHHH@Z $pdata$?MouseButtonUpdate@ImGui_Renderer@app@donut@@UEAA_NHHH@Z $unwind$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $pdata$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $chain$3$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $pdata$3$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $chain$11$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $pdata$11$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $chain$12$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $pdata$12$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $chain$13$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $pdata$13$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $chain$14$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $pdata$14$?Animate@ImGui_Renderer@app@donut@@UEAAXM@Z $unwind$?Render@ImGui_Renderer@app@donut@@UEAAXPEAVIFramebuffer@nvrhi@@@Z $pdata$?Render@ImGui_Renderer@app@donut@@UEAAXPEAVIFramebuffer@nvrhi@@@Z $unwind$?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z $pdata$?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z $chain$0$?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z $pdata$0$?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z $chain$1$?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z $pdata$1$?DisplayScaleChanged@ImGui_Renderer@app@donut@@UEAAXMM@Z $unwind$?BeginFullScreenWindow@ImGui_Renderer@app@donut@@IEAAXXZ $pdata$?BeginFullScreenWindow@ImGui_Renderer@app@donut@@IEAAXXZ $unwind$?DrawScreenCenteredText@ImGui_Renderer@app@donut@@IEAAXPEBD@Z $pdata$?DrawScreenCenteredText@ImGui_Renderer@app@donut@@IEAAXPEBD@Z $unwind$?EndFullScreenWindow@ImGui_Renderer@app@donut@@IEAAXXZ $pdata$?EndFullScreenWindow@ImGui_Renderer@app@donut@@IEAAXXZ $unwind$?CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z $pdata$?CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z $cppxdata$?CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z $stateUnwindMap$?CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z $ip2state$?CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z $unwind$?dtor$3@?0??CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z@4HA $pdata$?dtor$3@?0??CreateFontFromMemoryInternal@ImGui_Renderer@app@donut@@AEAA?AV?$shared_ptr@VRegisteredFont@app@donut@@@std@@PEBX_K_NM@Z@4HA $unwind$??_GImGui_Renderer@app@donut@@UEAAPEAXI@Z $pdata$??_GImGui_Renderer@app@donut@@UEAAPEAXI@Z $cppxdata$??_GImGui_Renderer@app@donut@@UEAAPEAXI@Z $stateUnwindMap$??_GImGui_Renderer@app@donut@@UEAAPEAXI@Z $ip2state$??_GImGui_Renderer@app@donut@@UEAAPEAXI@Z $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $unwind$??$make_shared@VRegisteredFont@app@donut@@$$V@std@@YA?AV?$shared_ptr@VRegisteredFont@app@donut@@@0@XZ $pdata$??$make_shared@VRegisteredFont@app@donut@@$$V@std@@YA?AV?$shared_ptr@VRegisteredFont@app@donut@@@0@XZ $unwind$??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VBlob@vfs@donut@@@std@@QEAA@XZ $unwind$??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $chain$0$??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $pdata$0$??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $chain$1$??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $pdata$1$??$_Destroy_range@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $unwind$?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ $pdata$?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ $chain$0$?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ $pdata$0$?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ $chain$1$?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ $pdata$1$?_Destroy@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@EEAAXXZ $unwind$??_G?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z $unwind$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $pdata$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $cppxdata$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $tryMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $handlerMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $ip2state$??$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VRegisteredFont@app@donut@@@std@@@?$vector@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@1@QEAV21@AEBV21@@Z@4HA $unwind$??$_Uninitialized_move@PEAV?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $pdata$??$_Uninitialized_move@PEAV?$shared_ptr@VRegisteredFont@app@donut@@@std@@V?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VRegisteredFont@app@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VRegisteredFont@app@donut@@@std@@@0@@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7IRenderPass@app@donut@@6B@ ??_7ImGui_Renderer@app@donut@@6B@ ??_C@_01CLKCMJKC@?5@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_7?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@6B@ ??_7?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@6B@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4IRenderPass@app@donut@@6B@ ??_R0?AVIRenderPass@app@donut@@@8 ??_R3IRenderPass@app@donut@@8 ??_R2IRenderPass@app@donut@@8 ??_R1A@?0A@EA@IRenderPass@app@donut@@8 ??_R4ImGui_Renderer@app@donut@@6B@ ??_R0?AVImGui_Renderer@app@donut@@@8 ??_R3ImGui_Renderer@app@donut@@8 ??_R2ImGui_Renderer@app@donut@@8 ??_R1A@?0A@EA@ImGui_Renderer@app@donut@@8 ??_R4?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VRegisteredFont@app@donut@@@std@@8 ??_R4?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@8 __real@3f000000 __real@3f800000 __real@bf800000 __security_cookie 