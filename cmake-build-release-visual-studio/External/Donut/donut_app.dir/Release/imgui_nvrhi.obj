d�9眈GhD. �      .drectve        <  �0               
 .debug$S        剬 82  季        @ B.debug$T        l                @ B.rdata          0   d�             @ @@.text$mn        :   斂 慰         P`.debug$S          炜         @B.text$mn        0   劼 绰         P`.debug$S        �  韭 n�        @B.text$mn        0    *�         P`.debug$S        �  4� 芷        @B.text$mn        c   h�              P`.debug$S        |  饲 G�        @B.text$mn        p   可              P`.debug$S        �  /� 盟        @B.text$mn        U  ;� 愅         P`.debug$S        �
  嗤 胸     X   @B.text$x         (   @� h�         P`.text$mn        �  |� [�         P`.debug$S        �
  ∞ u�     R   @B.text$x         (   ╈ 鸯         P`.text$mn        +  屐 �         P`.debug$S        t  L� �     Z   @B.text$x            D P         P`.text$x            Z f         P`.text$mn           p �         P`.debug$S        @  � �     
   @B.text$mn        �   =              P`.debug$S        �  � �        @B.text$mn            A	              P`.debug$S        8  a	 �        @B.text$mn           9
              P`.debug$S          D
 T        @B.text$mn           �              P`.debug$S        �   � l        @B.text$mn        
   �              P`.debug$S        �   � y        @B.text$mn           �              P`.debug$S        �   � ~        @B.text$mn           �              P`.debug$S        �   � i        @B.text$mn           �              P`.debug$S        �   � t        @B.text$mn        <   � �         P`.debug$S        0  
 :     
   @B.text$mn        <   � �         P`.debug$S        L  � D     
   @B.text$mn        !   � �         P`.debug$S        <  �         @B.text$mn        2   U �         P`.debug$S        <  � �        @B.text$mn        "   O              P`.debug$S        �  q 	        @B.text$mn        "   �              P`.debug$S        �  � _        @B.text$mn           �           P`.debug$S        L    i"        @B.text$mn        J   �" #         P`.debug$S        h  
# u&        @B.text$mn        K   ='              P`.debug$S        �  �' \)        @B.text$mn        `   �) H*         P`.debug$S        �  \* -        @B.text$mn        `   �- 0.         P`.debug$S        �  D. 1        @B.text$mn        ^   �1 2         P`.debug$S        �  .2 5        @B.text$mn           �5 �5         P`.debug$S        �   �5 �6        @B.text$mn           �6 �6         P`.debug$S        �   7 �7        @B.text$mn        B   ,8 n8         P`.debug$S           �8 �9        @B.text$mn        B   �9 
:         P`.debug$S          (: 8;        @B.text$mn        B   t; �;         P`.debug$S        �   �; �<        @B.text$mn        H   =              P`.debug$S        �  T= ?        @B.text$mn        �  0@ C         P`.debug$S        �  WC 颯     �   @B.text$mn            颴 Y         P`.debug$S        �   -Y 馳        @B.text$mn           -Z >Z         P`.debug$S        �   RZ [        @B.text$mn           B[ S[         P`.debug$S        �   g[ O\        @B.text$mn           媆 淺         P`.debug$S        �   癨 怾        @B.text$mn        `  蘛 ,_         P`.debug$S        �  |_  h     B   @B.text$mn        *   磈              P`.debug$S        �  辥 rl        @B.text$mn        >   m Pm         P`.debug$S        �  dm p        @B.text$mn        B   q Jq         P`.debug$S        �  ^q 鷖        @B.text$mn        �   眞         P`.debug$S        
  韜 鶃     L   @B.text$x            駝 齽         P`.text$mn        �   �              P`.debug$S        T  媴 邎     &   @B.text$mn        �
  [� �         P`.debug$S        �$  � 尯     .  @B.text$x            X� d�         P`.text$x            n� z�         P`.text$x            勂 斊         P`.text$x         -   炂 似         P`.text$mn        �  咂 慈     	    P`.debug$S        �
  � τ     X   @B.text$x            � "�         P`.text$mn        J  ,� v�         P`.debug$S        D  钴 2�     h   @B.text$mn        �  B� 仡     
    P`.debug$S          <� T�     P   @B.text$x            t� ��         P`.text$mn        �  婟 j          P`.debug$S        �  �  R     J   @B.text$mn           6 I         P`.debug$S        �   S '        @B.xdata             c             @0@.pdata             w �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             	
             @0@.pdata             
 
        @0@.xdata             ;
             @0@.pdata             G
 S
        @0@.xdata             q
             @0@.pdata             y
 �
        @0@.xdata             �
             @0@.pdata             �
 �
        @0@.xdata             �
             @0@.pdata             �
 �
        @0@.xdata                          @0@.pdata                      @0@.xdata             =             @0@.pdata             Q ]        @0@.xdata             { �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata             / C        @0@.pdata             a m        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             �         @0@.xdata             ! 1        @0@.pdata             E Q        @0@.xdata          	   o x        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata              '        @0@.xdata             E             @0@.pdata             M Y        @0@.xdata             w             @0@.pdata              �        @0@.xdata          (   � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata          #   , O        @@.xdata          D   �             @@.xdata             �             @0@.pdata             � �        @0@.voltbl                            .xdata          $   	 -        @0@.pdata             A M        @0@.xdata          	   k t        @@.xdata             � �        @@.xdata          
   �             @@.xdata          (   � �        @0@.pdata             � �        @0@.xdata          	    $        @@.xdata             8 >        @@.xdata             H             @@.xdata             P `        @0@.pdata             t �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata          $   � �        @0@.pdata                      @0@.xdata          	   0 9        @@.xdata             M Y        @@.xdata          	   m             @@.xdata             v �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata          $   �         @0@.pdata             0 <        @0@.xdata          	   Z c        @@.xdata             w �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.xdata              �         @0@.pdata             . :        @0@.xdata             X h        @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                       @0@.xdata             > N        @0@.pdata             l x        @0@.voltbl            �               .xdata             � �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata              	        @@.xdata                          @@.xdata               6        @0@.pdata             J V        @0@.xdata          	   t }        @@.xdata             � �        @@.xdata          
   �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          
             @@.xdata             >             @@.xdata             A I        @@.xdata             S Z        @@.xdata             d             @@.xdata             l             @0@.pdata             t �        @0@.voltbl            �               .xdata             � �        @0@.pdata             � �        @0@.xdata          
   �         @@.xdata             $             @@.xdata             ' /        @@.xdata             9 @        @@.xdata          	   J             @@.xdata             S             @0@.pdata             [ g        @0@.voltbl            �               .xdata             � �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata                          @@.xdata                          @0@.pdata                      @0@.xdata             6 J        @0@.pdata             h t        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata             $ 0        @0@.xdata             N             @0@.pdata             Z f        @0@.xdata             �             @0@.pdata             � �        @0@.rdata             � �        @@@.rdata             �             @@@.rdata                      @@@.rdata             8 P        @@@.rdata             n             @@@.xdata$x           � �        @@@.xdata$x           � �        @@@.data$r         /   �         @@�.xdata$x        $   & J        @@@.data$r         $   ^ �        @@�.xdata$x        $   � �        @@@.data$r         $   � �        @@�.xdata$x        $   �          @@@.rdata             *              @@@.data               :              @ @�.rdata             Z              @@@.rdata             m              @0@.rdata             r              @@@.rdata             �              @@@.rdata          !   �              @@@.rdata             �              @0@.rdata             �              @@@.rdata             �              @@@.rdata             �              @0@.rdata             �              @@@.rdata             �              @@@.rdata             !             @@@.rdata$r        $   0! T!        @@@.rdata$r           r! �!        @@@.rdata$r           �! �!        @@@.rdata$r        $   �! �!        @@@.rdata$r        $   �! "        @@@.rdata$r            " 4"        @@@.rdata$r           >" R"        @@@.rdata$r        $   f" �"        @@@.rdata$r        $   �" �"        @@@.rdata$r           �" �"        @@@.rdata$r           �" #        @@@.rdata$r        $   8# \#        @@@.rdata             p#             @0@.rdata             t#             @0@.rdata             x#             @P@.rdata             �#             @P@.debug$S        4   �# �#        @B.debug$S        4   �# $        @B.debug$S        @   ($ h$        @B.chks64         �	  |$              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �     f     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_app.dir\Release\imgui_nvrhi.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $app  $vfs  $log 	 $stdext  $ImGui    �   �&  � e   std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment  |O    std::denorm_absent  |O   std::denorm_present  O    std::round_toward_zero  O   std::round_to_nearest # |O    std::_Num_base::has_denorm ( =    std::_Num_base::has_denorm_loss % =    std::_Num_base::has_infinity & =    std::_Num_base::has_quiet_NaN * =    std::_Num_base::has_signaling_NaN # =    std::_Num_base::is_bounded ! =    std::_Num_base::is_exact " =    std::_Num_base::is_iec559 # =    std::_Num_base::is_integer " =    std::_Num_base::is_modulo " =    std::_Num_base::is_signed ' =    std::_Num_base::is_specialized ( =    std::_Num_base::tinyness_before  =    std::_Num_base::traps $ O    std::_Num_base::round_style  g    std::_Num_base::digits ! g    std::_Num_base::digits10 % g    std::_Num_base::max_digits10 % g    std::_Num_base::max_exponent ' g    std::_Num_base::max_exponent10 % g    std::_Num_base::min_exponent ' g    std::_Num_base::min_exponent10  g    std::_Num_base::radix � =   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible ' =   std::_Num_int_base::is_bounded % =   std::_Num_int_base::is_exact � =    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible ' =   std::_Num_int_base::is_integer + =   std::_Num_int_base::is_specialized � =    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable " g   std::_Num_int_base::radix ) |O   std::_Num_float_base::has_denorm + =   std::_Num_float_base::has_infinity , =   std::_Num_float_base::has_quiet_NaN 0 =   std::_Num_float_base::has_signaling_NaN ) =   std::_Num_float_base::is_bounded ( =   std::_Num_float_base::is_iec559 ( =   std::_Num_float_base::is_signed - =   std::_Num_float_base::is_specialized * O   std::_Num_float_base::round_style $ g   std::_Num_float_base::radix * g   std::numeric_limits<bool>::digits - =   std::numeric_limits<char>::is_signed - =    std::numeric_limits<char>::is_modulo * g   std::numeric_limits<char>::digits , g   std::numeric_limits<char>::digits10 4 =   std::numeric_limits<signed char>::is_signed 1 g   std::numeric_limits<signed char>::digits 3 g   std::numeric_limits<signed char>::digits10 6 =   std::numeric_limits<unsigned char>::is_modulo 3 g   std::numeric_limits<unsigned char>::digits 5 g   std::numeric_limits<unsigned char>::digits10 1 =   std::numeric_limits<char16_t>::is_modulo . g   std::numeric_limits<char16_t>::digits 0 g   std::numeric_limits<char16_t>::digits10 E e   std::allocator<char16_t>::_Minimum_asan_allocation_alignment � =   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � =    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � =    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable 1 =   std::numeric_limits<char32_t>::is_modulo . g    std::numeric_limits<char32_t>::digits 0 g  	 std::numeric_limits<char32_t>::digits10 0 =   std::numeric_limits<wchar_t>::is_modulo - g   std::numeric_limits<wchar_t>::digits / g   std::numeric_limits<wchar_t>::digits10 . =   std::numeric_limits<short>::is_signed + g   std::numeric_limits<short>::digits 3e   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size - g   std::numeric_limits<short>::digits10 3e   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets -=    std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi , =   std::numeric_limits<int>::is_signed ) g   std::numeric_limits<int>::digits + g  	 std::numeric_limits<int>::digits10 C e   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - =   std::numeric_limits<long>::is_signed * g   std::numeric_limits<long>::digits E e   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , g  	 std::numeric_limits<long>::digits10 P e   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity 0 =   std::numeric_limits<__int64>::is_signed - g  ? std::numeric_limits<__int64>::digits / g   std::numeric_limits<__int64>::digits10 % e   std::ctype<char>::table_size d e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q e  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j =   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val � =   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible m e    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k e    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size � =    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible 7 =   std::numeric_limits<unsigned short>::is_modulo 4 g   std::numeric_limits<unsigned short>::digits 6 g   std::numeric_limits<unsigned short>::digits10 � =    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable 5 =   std::numeric_limits<unsigned int>::is_modulo 2 g    std::numeric_limits<unsigned int>::digits 4 g  	 std::numeric_limits<unsigned int>::digits10 _ e   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment 6 =   std::numeric_limits<unsigned long>::is_modulo 3 g    std::numeric_limits<unsigned long>::digits 5 g  	 std::numeric_limits<unsigned long>::digits10 9 =   std::numeric_limits<unsigned __int64>::is_modulo 6 g  @ std::numeric_limits<unsigned __int64>::digits 8 g   std::numeric_limits<unsigned __int64>::digits10 ` e   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + g   std::numeric_limits<float>::digits - g   std::numeric_limits<float>::digits10 1 g  	 std::numeric_limits<float>::max_digits10 1 g  � std::numeric_limits<float>::max_exponent 3 g  & std::numeric_limits<float>::max_exponent10 2 g   �僺td::numeric_limits<float>::min_exponent 4 g   �踫td::numeric_limits<float>::min_exponent10 � e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment , g  5 std::numeric_limits<double>::digits . g   std::numeric_limits<double>::digits10 2 g   std::numeric_limits<double>::max_digits10 2 g   std::numeric_limits<double>::max_exponent 4 g  4std::numeric_limits<double>::max_exponent10 4 g  �黶td::numeric_limits<double>::min_exponent 6 g  �威std::numeric_limits<double>::min_exponent10 1 g  5 std::numeric_limits<long double>::digits 3 g   std::numeric_limits<long double>::digits10 7 g   std::numeric_limits<long double>::max_digits10 � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible 7 g   std::numeric_limits<long double>::max_exponent 9 g  4std::numeric_limits<long double>::max_exponent10 � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible 9 g  �黶td::numeric_limits<long double>::min_exponent ; g  �威std::numeric_limits<long double>::min_exponent10 � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable . =    std::integral_constant<bool,0>::value "     std::memory_order_relaxed "    std::memory_order_consume "    std::memory_order_acquire "    std::memory_order_release "    std::memory_order_acq_rel "    std::memory_order_seq_cst �   0   . =   std::integral_constant<bool,1>::value �   Y   g   std::_Iosb<int>::skipws ! g   std::_Iosb<int>::unitbuf # g   std::_Iosb<int>::uppercase " g   std::_Iosb<int>::showbase # g   std::_Iosb<int>::showpoint ! g    std::_Iosb<int>::showpos  g  @ std::_Iosb<int>::left  g  � std::_Iosb<int>::right " g   std::_Iosb<int>::internal  g   std::_Iosb<int>::dec  g   std::_Iosb<int>::oct  g   std::_Iosb<int>::hex $ g   std::_Iosb<int>::scientific  g    std::_Iosb<int>::fixed " g   0std::_Iosb<int>::hexfloat # g   @std::_Iosb<int>::boolalpha " g  � �std::_Iosb<int>::_Stdio % g  �std::_Iosb<int>::adjustfield # g   std::_Iosb<int>::basefield $ g   0std::_Iosb<int>::floatfield ! g    std::_Iosb<int>::goodbit   g   std::_Iosb<int>::eofbit ! g   std::_Iosb<int>::failbit   g   std::_Iosb<int>::badbit  g   std::_Iosb<int>::in  g   std::_Iosb<int>::out  g   std::_Iosb<int>::ate  g   std::_Iosb<int>::app  g   std::_Iosb<int>::trunc # g  @ std::_Iosb<int>::_Nocreate $ g  � std::_Iosb<int>::_Noreplace G e   std::allocator<ImDrawVert>::_Minimum_asan_allocation_alignment   g    std::_Iosb<int>::binary  g    std::_Iosb<int>::beg  g   std::_Iosb<int>::cur  g   std::_Iosb<int>::end , g  @ std::_Iosb<int>::_Default_open_prot 4 e  @ _Mtx_internal_imp_t::_Critical_section_size 5 e   _Mtx_internal_imp_t::_Critical_section_align + =    std::_Aligned_storage<64,8>::_Fits * =    std::_Aligned<64,8,char,0>::_Fits + =    std::_Aligned<64,8,short,0>::_Fits ) =   std::_Aligned<64,8,int,0>::_Fits E e   std::allocator<char32_t>::_Minimum_asan_allocation_alignment R =    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified C e   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E e   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P e   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity % 塓    _Atomic_memory_order_relaxed % 塓   _Atomic_memory_order_consume % 塓   _Atomic_memory_order_acquire % 塓   _Atomic_memory_order_release % 塓   _Atomic_memory_order_acq_rel % 塓   _Atomic_memory_order_seq_cst d e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j =   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m e    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k e    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size ` e   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos K e   std::allocator<unsigned short>::_Minimum_asan_allocation_alignment - =    std::chrono::system_clock::is_steady $    std::ratio<1,10000000>::num (   ��枠 std::ratio<1,10000000>::den 6 =   std::_Iterator_base0::_Unwrap_when_unverified 7 =   std::_Iterator_base12::_Unwrap_when_unverified     std::ratio<1,1>::num     std::ratio<1,1>::den J    std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N   ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 (   ��枠 std::ratio<10000000,1>::num $    std::ratio<10000000,1>::den P   ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy <   ��枠 std::integral_constant<__int64,10000000>::value 1    std::integral_constant<__int64,1>::value - =   std::chrono::steady_clock::is_steady &    std::ratio<1,1000000000>::num *   � 蕷;std::ratio<1,1000000000>::den Z e   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment '=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible     std::ratio<3600,1>::num      std::ratio<3600,1>::den !=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable : e    std::integral_constant<unsigned __int64,0>::value / =   std::atomic<long>::is_always_lock_free ) 3   nvrhi::ObjectTypes::SharedHandle - 3  �  nvrhi::ObjectTypes::D3D11_Device 4 3  �  nvrhi::ObjectTypes::D3D11_DeviceContext / 3  �  nvrhi::ObjectTypes::D3D11_Resource - 3  �  nvrhi::ObjectTypes::D3D11_Buffer 7 3  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 3  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 3  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : 3  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - 3  �  nvrhi::ObjectTypes::D3D12_Device 3 3  �  nvrhi::ObjectTypes::D3D12_CommandQueue : 3  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / 3  �  nvrhi::ObjectTypes::D3D12_Resource A 3  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A 3  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F 3  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G 3  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 3  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 3  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 3  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * 3  �  nvrhi::ObjectTypes::VK_Device 2 3  �  nvrhi::ObjectTypes::VK_PhysicalDevice , 3  �  nvrhi::ObjectTypes::VK_Instance ) 3  �  nvrhi::ObjectTypes::VK_Queue 1 3  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 3  �  nvrhi::ObjectTypes::VK_DeviceMemory * 3  �  nvrhi::ObjectTypes::VK_Buffer ) 3  �  nvrhi::ObjectTypes::VK_Image - 3  �	  nvrhi::ObjectTypes::VK_ImageView < 3  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + 3  �  nvrhi::ObjectTypes::VK_Sampler 0 3  �  nvrhi::ObjectTypes::VK_ShaderModule . 3  �
  nvrhi::ObjectTypes::VK_RenderPass / 3  �  nvrhi::ObjectTypes::VK_Framebuffer 2 3  �  nvrhi::ObjectTypes::VK_DescriptorPool ) GV    std::_Invoker_functor::_Strategy 7 3  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 3  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 3  �  nvrhi::ObjectTypes::VK_PipelineLayout , 3  �  nvrhi::ObjectTypes::VK_Pipeline , 3  �  nvrhi::ObjectTypes::VK_Micromap 3 3  �  nvrhi::ObjectTypes::VK_ImageCreateInfo , GV   std::_Invoker_pmf_object::_Strategy - GV   std::_Invoker_pmf_refwrap::_Strategy - GV   std::_Invoker_pmf_pointer::_Strategy � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable , GV   std::_Invoker_pmd_object::_Strategy - GV   std::_Invoker_pmd_refwrap::_Strategy - GV   std::_Invoker_pmd_pointer::_Strategy    < std::ratio<60,1>::num     std::ratio<60,1>::den \ e   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 8 =   std::atomic<unsigned long>::is_always_lock_free / 3  � nvrhi::rt::cluster::kClasByteAlignment . 3   nvrhi::rt::cluster::kClasMaxTriangles - 3   nvrhi::rt::cluster::kClasMaxVertices 2 3  ���� nvrhi::rt::cluster::kMaxGeometryIndex      std::ratio<1,1000>::num     �std::ratio<1,1000>::den #    std::ratio<1,1000000>::num '   �@B std::ratio<1,1000000>::den  3   nvrhi::c_HeaderVersion " 3   nvrhi::c_MaxRenderTargets  3   nvrhi::c_MaxViewports % 3   nvrhi::c_MaxVertexAttributes # 3   nvrhi::c_MaxBindingLayouts & 3  � nvrhi::c_MaxBindingsPerLayout 5 3   nvrhi::c_MaxVolatileConstantBuffersPerLayout , 3    nvrhi::c_MaxVolatileConstantBuffers % 3  � nvrhi::c_MaxPushConstantSize 3 3   nvrhi::c_ConstantBufferOffsetSizeAlignment : g   std::_Floating_type_traits<float>::_Mantissa_bits : g   std::_Floating_type_traits<float>::_Exponent_bits D g   std::_Floating_type_traits<float>::_Maximum_binary_exponent E g   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : g   std::_Floating_type_traits<float>::_Exponent_bias 7 g   std::_Floating_type_traits<float>::_Sign_shift ; g   std::_Floating_type_traits<float>::_Exponent_shift : 3  � std::_Floating_type_traits<float>::_Exponent_mask E 3  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G 3  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J 3  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B 3  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F 3  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; g  5 std::_Floating_type_traits<double>::_Mantissa_bits ; g   std::_Floating_type_traits<double>::_Exponent_bits E g  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G g  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; g  �std::_Floating_type_traits<double>::_Exponent_bias 8 g  ? std::_Floating_type_traits<double>::_Sign_shift < g  4 std::_Floating_type_traits<double>::_Exponent_shift ; e  �std::_Floating_type_traits<double>::_Exponent_mask J e  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L e  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O e  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G e  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K e  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask 3 E  \ std::filesystem::path::preferred_separator W =   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified - g    std::integral_constant<int,0>::value : e   std::integral_constant<unsigned __int64,2>::value R =   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 3  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 3  �����nvrhi::TextureSubresourceSet::AllArraySlices D e   ��std::basic_string_view<char,std::char_traits<char> >::npos # �        nvrhi::AllSubresources J e   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos $ @t   ImGuiWindowFlags_NoTitleBar " @t   ImGuiWindowFlags_NoResize % @t   ImGuiWindowFlags_NoScrollbar L e   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos $ @t    ImGuiWindowFlags_NoCollapse ' @t   ImGuiWindowFlags_NoMouseInputs ) @t  �   ImGuiWindowFlags_NoNavInputs ( @t  �   ImGuiWindowFlags_NoNavFocus # Yt  � ImGuiChildFlags_FrameStyle H =    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified " 蛃   ImGuiTreeNodeFlags_Framed ( 蛃   ImGuiTreeNodeFlags_AllowOverlap , 蛃   ImGuiTreeNodeFlags_NoTreePushOnOpen + 蛃   ImGuiTreeNodeFlags_NoAutoOpenOnLog   �        nvrhi::EntireBuffer # t   ImGuiPopupFlags_AnyPopupId & t   ImGuiPopupFlags_AnyPopupLevel * Lt   ImGuiSelectableFlags_AllowOverlap $ <t   ImGuiComboFlags_HeightSmall & <t   ImGuiComboFlags_HeightRegular $ <t   ImGuiComboFlags_HeightLarge & <t   ImGuiComboFlags_HeightLargest 1 [t  @ ImGuiTabBarFlags_FittingPolicyResizeDown - [t  � ImGuiTabBarFlags_FittingPolicyScroll ' Jt   ImGuiFocusedFlags_ChildWindows % Jt   ImGuiFocusedFlags_RootWindow ' Dt   ImGuiHoveredFlags_ChildWindows % Dt   ImGuiHoveredFlags_RootWindow 2 Dt    ImGuiHoveredFlags_AllowWhenBlockedByPopup 7 Dt  � ImGuiHoveredFlags_AllowWhenBlockedByActiveItem 4 Dt   ImGuiHoveredFlags_AllowWhenOverlappedByItem 6 Dt   ImGuiHoveredFlags_AllowWhenOverlappedByWindow . Dt   ImGuiHoveredFlags_AllowWhenOverlapped 0 Nt   ImGuiDragDropFlags_AcceptBeforeDelivery 3 Nt   ImGuiDragDropFlags_AcceptNoDrawDefaultRect L e   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos  f  �ImGuiKey_COUNT  f   ImGuiMod_Ctrl  f    ImGuiMod_Shift  f   @ImGuiMod_Alt  f  � �ImGuiMod_Super   f   ImGuiKey_NamedKey_BEGIN  f  �ImGuiKey_NamedKey_END  f  �ImGuiKey_KeysData_SIZE  Ht   ImGuiNavInput_COUNT  Pt  5 ImGuiCol_COUNT ) Wt   ImGuiButtonFlags_MouseButtonLeft * Wt   ImGuiButtonFlags_MouseButtonRight + Wt   ImGuiButtonFlags_MouseButtonMiddle + :t  �   ImGuiColorEditFlags_DisplayRGB + :t  �    ImGuiColorEditFlags_DisplayHSV + :t  �  @ ImGuiColorEditFlags_DisplayHex & :t  �  � ImGuiColorEditFlags_Uint8 & :t  �   ImGuiColorEditFlags_Float - :t  �   ImGuiColorEditFlags_PickerHueBar / :t  �   ImGuiColorEditFlags_PickerHueWheel ) :t  �   ImGuiColorEditFlags_InputRGB ) :t  �   ImGuiColorEditFlags_InputHSV & t  � ImGuiTableFlags_BordersInnerH � e   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment & t   ImGuiTableFlags_BordersOuterH & t   ImGuiTableFlags_BordersInnerV & t   ImGuiTableFlags_BordersOuterV % t  �ImGuiTableFlags_BordersInner % t   ImGuiTableFlags_BordersOuter ' t    ImGuiTableFlags_SizingFixedFit ( t   @ImGuiTableFlags_SizingFixedSame * t   `ImGuiTableFlags_SizingStretchProp , t  � �ImGuiTableFlags_SizingStretchSame + >t   ImGuiTableColumnFlags_WidthStretch ) >t   ImGuiTableColumnFlags_WidthFixed / >t  �   ImGuiTableColumnFlags_IndentEnable 0 >t  �   ImGuiTableColumnFlags_IndentDisable , >t  �   ImGuiTableColumnFlags_IsEnabled , >t  �   ImGuiTableColumnFlags_IsVisible + >t  �   ImGuiTableColumnFlags_IsSorted , >t  �   ImGuiTableColumnFlags_IsHovered =    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi =   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard � e   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment x e   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment : e   std::integral_constant<unsigned __int64,1>::value 5 =    std::filesystem::_File_time_clock::is_steady -e   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -e   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets '=    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi A e   std::allocator<char>::_Minimum_asan_allocation_alignment e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment ? e   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A e   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L e   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ =   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a e   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment T e   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos � =   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified ( Bt   ImDrawFlags_RoundCornersTopLeft ) Bt    ImDrawFlags_RoundCornersTopRight + Bt  @ ImDrawFlags_RoundCornersBottomLeft , Bt  � ImDrawFlags_RoundCornersBottomRight % Bt   ImDrawFlags_RoundCornersNone $ Bt  � ImDrawFlags_RoundCornersAll $ g   std::_Locbase<int>::collate " g   std::_Locbase<int>::ctype % g   std::_Locbase<int>::monetary $ g   std::_Locbase<int>::numeric ! g   std::_Locbase<int>::time % g    std::_Locbase<int>::messages   g  ? std::_Locbase<int>::all ! g    std::_Locbase<int>::none + J        nvrhi::rt::c_IdentityTransform D e   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment  �/   std::_Consume_header  �/   std::_Generate_header B e   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D e   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O e   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n e  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g =   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j e    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h e    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 8 =    std::_False_trivial_cat::_Bitcopy_constructible w =   std::_Trivial_cat<unsigned short,unsigned short,unsigned short &&,unsigned short &>::_Same_size_and_compatible 5 =    std::_False_trivial_cat::_Bitcopy_assignable t =   std::_Trivial_cat<unsigned short,unsigned short,unsigned short &&,unsigned short &>::_Bitcopy_constructible q =   std::_Trivial_cat<unsigned short,unsigned short,unsigned short &&,unsigned short &>::_Bitcopy_assignable T e   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ] e   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos � e   std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment J    std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J    std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P   ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy  =    std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi #=   std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard g =   std::_Trivial_cat<ImDrawVert,ImDrawVert,ImDrawVert &&,ImDrawVert &>::_Same_size_and_compatible d =   std::_Trivial_cat<ImDrawVert,ImDrawVert,ImDrawVert &&,ImDrawVert &>::_Bitcopy_constructible a =   std::_Trivial_cat<ImDrawVert,ImDrawVert,ImDrawVert &&,ImDrawVert &>::_Bitcopy_assignable  ≦  _CatchableType  f  ImNewWrapper  癹  ImVector<ImFont *> " 俀  _s__RTTIBaseClassDescriptor ? s  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  Ek  ImFontConfig & 扱  $_TypeDescriptor$_extraBytes_24 6 ve  __vcrt_va_list_is_reference<char const * const>  f  ImGuiKey G |  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �.  _Ctypevec  Yt  ImGuiChildFlags_ & 
e  $_TypeDescriptor$_extraBytes_28     ImS16  #   uintmax_t     int64_t  穎  ImGuiTextBuffer    _Smtx_t  #   rsize_t  韍  ImVector<ImDrawVert> - ne  __vc_attributes::event_sourceAttribute 9 ge  __vc_attributes::event_sourceAttribute::optimize_e 5 ee  __vc_attributes::event_sourceAttribute::type_e > ce  __vc_attributes::helper_attributes::v1_alttypeAttribute F ^e  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 [e  __vc_attributes::helper_attributes::usageAttribute B We  __vc_attributes::helper_attributes::usageAttribute::usage_e * Te  __vc_attributes::threadingAttribute 7 Me  __vc_attributes::threadingAttribute::threading_e - Je  __vc_attributes::aggregatableAttribute 5 Ce  __vc_attributes::aggregatableAttribute::type_e / @e  __vc_attributes::event_receiverAttribute 7 7e  __vc_attributes::event_receiverAttribute::type_e ' 4e  __vc_attributes::moduleAttribute / +e  __vc_attributes::moduleAttribute::type_e  卛  ImVector<ImVec2>  �1  __std_fs_find_data & 翾  $_TypeDescriptor$_extraBytes_23 - R  $_s__CatchableTypeArray$_extraBytes_32 # E7  __std_fs_reparse_data_buffer Z (e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ %e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` #e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �1  __std_fs_dir_handle  0f  ImGuiOnceUponAFrame  �-  __std_access_rights  繧  _TypeDescriptor & 譗  $_TypeDescriptor$_extraBytes_34  Cj  ImFontAtlasCustomRect 	   tm % }Q  _s__RTTICompleteObjectLocator2  齟  ImGuiTableSortSpecs  Jt  ImGuiFocusedFlags_     ImS8      ImU8  Ep  ImDrawChannel  奼  ImDrawCallback A e  __vcrt_va_list_is_reference<__crt_locale_pointers * const> 
 
k  ImFont  !   ImU16  ≦  _s__CatchableType    ImDrawListSplitter  6j  ImVector<unsigned int>  Nt  ImGuiDragDropFlags_  蛃  ImGuiTreeNodeFlags_  Dt  ImGuiHoveredFlags_ & 禥  $_TypeDescriptor$_extraBytes_19 & 赒  $_TypeDescriptor$_extraBytes_21  #   uint64_t  t   ImGuiMouseCursor 9 e  __vcrt_va_list_is_reference<wchar_t const * const>  5  __std_fs_filetime E a  __vcrt_assert_va_start_is_not_reference<wchar_t const * const>  t   ImGuiConfigFlags  tg  ImColor & 朡  $_TypeDescriptor$_extraBytes_20  p  va_list  Kh  ImDrawList - 闝  $_s__CatchableTypeArray$_extraBytes_16   k7  __std_fs_copy_file_result  �1  __std_code_page � �v  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � 弙  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > z 﨤  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> i檝  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � 憊  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � Sv  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > f   std::_Simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � 倂  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � }v  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 坱  std::_Ptr_base<donut::vfs::IFileSystem> �qv  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � gv  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 風  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> � Uv  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > Fv  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> &u  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > [ @v  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' ZC  std::default_delete<wchar_t [0]> . +-  std::_Conditionally_enabled_hash<int,1> A (6  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? 筄  std::_Default_allocator_traits<std::allocator<wchar_t> > . dO  std::integer_sequence<unsigned __int64> �  n  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > >  v  std::_Lockit * cH  std::hash<enum nvrhi::ResourceType> - F2  std::reverse_iterator<wchar_t const *> " 諳  std::_Char_traits<char,int>  .  std::_Fs_file � =v  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >  [o  std::_Value_init_tag  "   std::_Atomic_counter_t  侽  std::_Num_base & 7-  std::hash<std::error_condition> K 盝  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 	卲  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � -v  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 適  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0>  |*  std::_Big_uint128 � qu  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ) 酧  std::_Narrow_char_traits<char,int>  �  std::hash<float> 6 rJ  std::allocator<nvrhi::rt::PipelineHitGroupDesc> #v  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >  /-  std::hash<int>  凮  std::_Num_int_base  �0  std::ctype<wchar_t> " �-  std::_System_error_category / 丠  std::_Conditionally_enabled_hash<bool,1>  |O  std::float_denorm_style 跧  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! FJ  std::piecewise_construct_t u WO  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 赹  std::allocator_traits<std::allocator<wchar_t> > �   std::list<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  S  std::bad_cast  WI  std::equal_to<void> 3 )l  std::_Ptr_base<donut::engine::ShaderFactory> � �3  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > t Io  std::_Compressed_pair<std::allocator<unsigned short>,std::_Vector_val<std::_Simple_types<unsigned short> >,1> o vu  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> 6 妉  std::initializer_list<nvrhi::BindingLayoutItem> B 藄  std::_Default_allocator_traits<std::allocator<ImDrawVert> > " ㎡  std::numeric_limits<double>  �  std::__non_rtti_object ( �  std::_Basic_container_proxy_ptr12 : 搉  std::vector<ImDrawVert,std::allocator<ImDrawVert> > P bn  std::vector<ImDrawVert,std::allocator<ImDrawVert> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8>    std::_Num_float_base  �+  std::logic_error 7 <H  std::_Conditionally_enabled_hash<unsigned int,1> G kH  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety ! I]  std::char_traits<char32_t>  $/  std::locale  Y/  std::locale::_Locimp  5/  std::locale::facet   =/  std::locale::_Facet_guard  �.  std::locale::id l uo  std::_Compressed_pair<std::allocator<ImDrawVert>,std::_Vector_val<std::_Simple_types<ImDrawVert> >,1> s qP  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   哋  std::numeric_limits<bool> # ≒  std::_WChar_traits<char16_t> P 廋  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T '  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   �  std::_Fake_proxy_ptr_impl F 羢  std::_Default_allocator_traits<std::allocator<unsigned short> > * 淥  std::numeric_limits<unsigned short> ' '  std::hash<nvrhi::BindingSetDesc> Z 扤  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M 02  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � zJ  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> { 憃  std::pair<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *,bool>  i,  std::overflow_error � 駇  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > % 楧  std::_One_then_variadic_args_t D 貶  std::_Constexpr_immortalize_impl<std::_System_error_category> W 窷  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > E 	7  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j R\  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   P\  std::char_traits<wchar_t> � 皅  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >   �  std::pmr::memory_resource ! -  std::array<nvrhi::Rect,16> 4 滼  std::allocator<nvrhi::rt::PipelineShaderDesc> � 穟  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  v[  std::false_type  O  std::float_round_style T    std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy  j  std::string B \  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �2  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , r!  std::array<nvrhi::BindingSetItem,128> �   std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �"  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � 璼  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > | gp  std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � 蠮  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> ,   std::numeric_limits<unsigned __int64>  �.  std::_Locinfo 6 85  std::_Ptr_base<std::filesystem::_Dir_enum_impl> # Br  std::hash<nvrhi::ITexture *> \ BQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s 橫  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > k   std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � 瀠  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > $ 嶰  std::numeric_limits<char16_t> 0 $  std::array<nvrhi::VertexBufferBinding,16>  �  std::string_view  &  std::wstring_view % 穁  std::integral_constant<bool,1>   �  std::_Leave_proxy_unbound z }p  std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >  x1  std::money_base  甗  std::money_base::pattern  T.  std::_Timevec   �,  std::_Init_once_completer j �6  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � i6  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + �/  std::codecvt<wchar_t,char,_Mbstatet> h 岲  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> ! mo  std::allocator<ImDrawVert> Q 漑  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % �  std::array<nvrhi::Viewport,16>  �  std::_Iterator_base12 � 卽  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  咵  std::_Pocma_values 7 Z'  std::_Array_const_iterator<enum nvrhi::Format,8> ! %-  std::hash<std::error_code> N 93  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ 'Q  std::_Default_allocator_traits<std::allocator<char32_t> >  �2  std::allocator<char32_t> ? =7  std::unique_ptr<char [0],std::default_delete<char [0]> > $ K  std::_Atomic_integral<long,4>  匟  std::hash<bool>     std::streamsize 6 ~D  std::_String_val<std::_Simple_types<char32_t> > = rF  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` 滶  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> 2 o  std::initializer_list<nvrhi::IBindingSet *> | 鰌  std::_Uhash_choose_transparency<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *>,void>    std::hash<long double> � �3  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 3  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l Z  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k V  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy H 沴  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U kQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # 扥  std::numeric_limits<wchar_t>  3  std::_Container_base0 F Zr  std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1>  	  std::hash<double> � #q  std::_Hash_find_last_result<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *> O uQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & 烼  std::bidirectional_iterator_tag . �&  std::hash<nvrhi::TextureSubresourceSet> � 檘  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > < Uo  std::_Vector_val<std::_Simple_types<unsigned short> > / 匬  std::_Char_traits<char32_t,unsigned int>  O-  std::_System_error ( ?'  std::hash<nvrhi::FramebufferInfo> 9 {C  std::allocator<std::filesystem::_Find_file_handle> i 無  std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *>  -  std::error_condition % v[  std::integral_constant<bool,0>  a  std::bad_exception & 錌  std::_Zero_then_variadic_args_t  �  std::u32string h Gs  std::pointer_traits<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > *> � 蓃  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *>  4  std::_Fake_allocator / t   std::array<nvrhi::BindingLayoutItem,128>  
,  std::invalid_argument N H[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U 襈  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S F[  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R eD  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > + �7  std::pair<enum __std_win_error,bool> S  2  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  $,  std::length_error F XM  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 僀  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> !   std::numeric_limits<float>  �1  std::time_base   ~1  std::time_base::dateorder ) \  std::_Atomic_integral_facade<long> % 滺  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " KH  std::hash<unsigned __int64>  馴  std::ratio<60,1>  �  std::exception_ptr  颶  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > 8 乷  std::_Vector_val<std::_Simple_types<ImDrawVert> > ) 慔  std::hash<enum nvrhi::BlendFactor> $ 怬  std::numeric_limits<char32_t>  �,  std::once_flag   -  std::error_code  6  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 遉  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  1  std::_Iosb<int>   1  std::_Iosb<int>::_Seekdir ! �0  std::_Iosb<int>::_Openmode   �0  std::_Iosb<int>::_Iostate ! �0  std::_Iosb<int>::_Fmtflags # �0  std::_Iosb<int>::_Dummy_enum 7 軿  std::allocator_traits<std::allocator<char32_t> >  訴  std::nano  ?  std::_Iterator_base0 M 咼  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > #刴  std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 2kr  std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Range_eraser 1齫  std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Clear_guard � 噓  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 橮  std::_Char_traits<char16_t,unsigned short> $ �&  std::hash<nvrhi::BufferRange> T )3  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  �.  std::_Locbase<int> ! 臵  std::char_traits<char16_t> 1 s  std::_Tuple_val<nvrhi::ITexture * const &>  �  std::tuple<>  ^  std::_Container_base12 W 鍸  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  -  std::io_errc  ;1  std::ios_base  L1  std::ios_base::_Fnarray  F1  std::ios_base::_Iosarray  �0  std::ios_base::Init  �0  std::ios_base::failure  1  std::ios_base::event E 笻  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 hO  std::integer_sequence<unsigned __int64,0> ) 孫  std::numeric_limits<unsigned char> % Ao  std::allocator<unsigned short> � XD  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  穁  std::true_type   極  std::numeric_limits<long> " 瞆  std::initializer_list<char>  GV  std::_Invoker_strategy  7  std::nothrow_t 霫  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ 甇  std::_Default_allocate_traits z su  std::allocator_traits<std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > N 
3  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 SZ  std::allocator_traits<std::allocator<char> > ! 擮  std::numeric_limits<short> . 菾  std::allocator<nvrhi::rt::GeometryDesc> ; �  std::basic_string_view<char,std::char_traits<char> > � 襮  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > ' Rr  std::equal_to<nvrhi::ITexture *> �du  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! �0  std::ctype<unsigned short> C f  std::basic_string_view<char16_t,std::char_traits<char16_t> > | Kr  std::_Compressed_pair<std::hash<nvrhi::ITexture *>,std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1>,1> < 6'  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 珼  std::_String_val<std::_Simple_types<char16_t> > = |F  std::_String_val<std::_Simple_types<char16_t> >::_Bxty q  std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> O 鶫  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P �2  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . oH  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock  w  std::bad_alloc  �,  std::underflow_error B 岺  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> F 塺  std::_Uninitialized_backout_al<std::allocator<unsigned short> > J aC  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D PC  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  �1  std::messages_base  ;,  std::out_of_range # 歄  std::numeric_limits<__int64> i 訡  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  ^0  std::ctype<char>    std::memory_order  mX  std::ratio<3600,1> # C  std::_Atomic_storage<long,4> # w'  std::hash<nvrhi::BlendState> /   std::shared_ptr<donut::vfs::IFileSystem>  1  std::atomic_flag f 釪  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  q-  std::system_error = st  std::allocator_traits<std::allocator<unsigned short> > < 罯  std::_Default_allocator_traits<std::allocator<char> > W aQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  ]X  std::ratio<1,1> 3 n  std::initializer_list<nvrhi::BindingSetItem>   漈  std::forward_iterator_tag  R,  std::runtime_error   �  std::bad_array_new_length E 躂  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 qt  std::allocator_traits<std::allocator<ImDrawVert> > � <q  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >  �.  std::_Yarn<char>  M  std::_Container_proxy ( X  std::_Facetptr<std::ctype<char> > Z 砅  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  �  std::u16string  �  std::nested_exception    std::_Distance_unknown ( 濷  std::numeric_limits<unsigned int> < ;M  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , �/  std::codecvt<char32_t,char,_Mbstatet> @   std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> �簈  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> ) �#  std::array<nvrhi::IBindingSet *,5> K j  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> , eq  std::tuple<nvrhi::ITexture * const &> & 鵙  std::initializer_list<char32_t> d �%  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �%  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 颲  std::initializer_list<char16_t> % 錠  std::initializer_list<wchar_t> C _H  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>     std::hash<std::nullptr_t> ' 玂  std::numeric_limits<long double>  -  std::errc ,   std::default_delete<std::_Facet_base>  �,  std::range_error  k  std::bad_typeid > 橦  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  訴  std::ratio<1,1000000000>  |2  std::allocator<char16_t> $ IC  std::default_delete<char [0]> ` B%  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v %  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J �2  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  諹  std::ratio<1,1000>  蔝  std::ratio<1,10000000> ; JD  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  �.  std::_Crt_new_delete % �-  std::_Iostream_error_category2 b ot  std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � at  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > * 齌  std::_String_constructor_concat_tag  e2  std::allocator<char> � 蒻  std::unordered_map<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *>,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > G 蒆  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t &   std::random_access_iterator_tag 4 Al  std::shared_ptr<donut::engine::ShaderFactory> ; GH  std::_Conditionally_enabled_hash<unsigned __int64,1> R 繡  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  �  std::bad_weak_ptr ) 燨  std::numeric_limits<unsigned long>   鸈  std::_Atomic_padded<long> @ 6  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  �.  std::_Yarn<wchar_t> = vH  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  '  std::wstring ' 奜  std::numeric_limits<signed char> � �3  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �+  std::domain_error  �  std::u32string_view  3  std::_Container_base  �3  std::allocator<wchar_t> � _t  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > L 歍  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ WH  std::hash<nvrhi::IResource *> 1 b'  std::hash<nvrhi::BlendState::RenderTarget>   圤  std::numeric_limits<char> 9 r+  std::chrono::duration<__int64,std::ratio<1,1000> >  �*  std::chrono::nanoseconds y 0.  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �*  std::chrono::duration<__int64,std::ratio<1,1000000000> > , 癘  std::chrono::duration_values<__int64>  �*  std::chrono::seconds 3 0+  std::chrono::duration<int,std::ratio<60,1> > 6 �*  std::chrono::duration<__int64,std::ratio<1,1> > s �*  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >    T  std::chrono::steady_clock   T  std::chrono::system_clock 6 E+  std::chrono::duration<double,std::ratio<60,1> > ; �+  std::chrono::duration<double,std::ratio<1,1000000> > > �+  std::chrono::duration<double,std::ratio<1,1000000000> > = �*  std::chrono::duration<__int64,std::ratio<1,10000000> > q �*  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 +  std::chrono::duration<int,std::ratio<3600,1> > 8 �+  std::chrono::duration<double,std::ratio<1,1000> > < �+  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 \+  std::chrono::duration<double,std::ratio<1,1> > 8 +  std::chrono::duration<double,std::ratio<3600,1> >  30  std::ctype_base  �4  std::filesystem::perms ' �4  std::filesystem::directory_entry $ �4  std::filesystem::copy_options ( r4  std::filesystem::filesystem_error 7 zI  std::filesystem::_Path_iterator<wchar_t const *> ) �1  std::filesystem::_Find_file_handle & �1  std::filesystem::_Is_slash_oper . �5  std::filesystem::_Should_recurse_result $ �7  std::filesystem::perm_options 4 �6  std::filesystem::recursive_directory_iterator . �4  std::filesystem::_File_status_and_error & i5  std::filesystem::_Dir_enum_impl 0 {5  std::filesystem::_Dir_enum_impl::_Creator @ �5  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! �4  std::filesystem::file_type . �5  std::filesystem::_Directory_entry_proxy " �7  std::filesystem::space_info * �5  std::filesystem::directory_iterator & 0.  std::filesystem::file_time_type 0 �5  std::filesystem::_Recursive_dir_enum_impl ) 5  std::filesystem::directory_options # �4  std::filesystem::file_status u ,4  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( T  std::filesystem::_File_time_clock  �2  std::filesystem::path $ �1  std::filesystem::path::format * II  std::filesystem::_Normal_conversion < kM  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �/  std::codecvt<char16_t,char,_Mbstatet> B 爎  std::_Uninitialized_backout_al<std::allocator<ImDrawVert> >  T  std::char_traits<char> � 菴  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �,  std::error_category ) �,  std::error_category::_Addr_storage ! �-  std::_System_error_message  
  std::_Unused_parameter h 籇  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A &  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > � Hq  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 7 Q5  std::shared_ptr<std::filesystem::_Dir_enum_impl> = SH  std::_Conditionally_enabled_hash<nvrhi::IResource *,1>  �/  std::_Codecvt_mode @ 0Q  std::_Default_allocator_traits<std::allocator<char16_t> >  歂  std::_Exact_args_t � C4  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > < ?r  std::_Conditionally_enabled_hash<nvrhi::ITexture *,1> 0 鯫  std::_Char_traits<wchar_t,unsigned short> '    std::array<enum nvrhi::Format,8> \ 錗  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 諨  std::_String_val<std::_Simple_types<wchar_t> > < 匜  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  C.  std::_Facet_base ' '  std::hash<nvrhi::BindingSetItem> " P  std::_WChar_traits<wchar_t> 2 0  std::codecvt<unsigned short,char,_Mbstatet> # �-  std::_Generic_error_category � 穝  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  翺  std::streampos  蔛  std::input_iterator_tag 2 PM  std::_Wrap<std::filesystem::_Dir_enum_impl> X ㎏  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> B o  std::vector<unsigned short,std::allocator<unsigned short> > X 觧  std::vector<unsigned short,std::allocator<unsigned short> >::_Reallocation_policy ' 〩  std::hash<enum nvrhi::ColorMask>  �/  std::codecvt_base  q*  std::bad_function_call O 鞱  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > ' �7  std::hash<std::filesystem::path> Q n  std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >  @H  std::hash<unsigned int> 7 篠  std::allocator_traits<std::allocator<char16_t> > l q  std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> > "    std::_Asan_aligned_pointers F 窼  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �   std::array<nvrhi::BindingLayoutItem,16> � 苢  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > $ zH  std::hash<enum nvrhi::Format>  朞  std::numeric_limits<int> 2 疎  std::_String_val<std::_Simple_types<char> > 9 岶  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access 
 !   wint_t  t   ImDrawFlags  g  ImGuiStorage % 鮢  ImGuiStorage::ImGuiStoragePair  !   ImWchar # �%  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  I!  nvrhi::BindingSetDesc  禨  nvrhi::SubresourceTiling " T  nvrhi::SamplerReductionType $ 鰈  nvrhi::GraphicsPipelineHandle  %   nvrhi::ResourceType  u   nvrhi::ObjectType ) f"  nvrhi::RefCountPtr<nvrhi::IShader>  1"  nvrhi::InputLayoutHandle   X#  nvrhi::IndexBufferBinding   睸  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �   nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " ;   nvrhi::VulkanBindingOffsets  J  nvrhi::ResourceStates  x#  nvrhi::GraphicsState * 蔿  nvrhi::RefCountPtr<nvrhi::ISampler> /   nvrhi::static_vector<nvrhi::Viewport,16> ! L  nvrhi::SharedResourceFlags  "  nvrhi::ShaderDesc  a$  nvrhi::IComputePipeline : 0$  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  #  nvrhi::Rect  !  nvrhi::BindingSetItem $ �   nvrhi::BindingLayoutItemArray ) 琒  nvrhi::RefCountPtr<nvrhi::IDevice> ! �!  nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �#  nvrhi::IGraphicsPipeline ! &  nvrhi::ShaderLibraryHandle  J  nvrhi::FramebufferInfoEx  p"  nvrhi::IShader  o  nvrhi::TextureDesc  "!  nvrhi::ISampler ! G#  nvrhi::VertexBufferBinding !  #  nvrhi::ComputePipelineDesc  _  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # B&  nvrhi::MeshletPipelineHandle  P  nvrhi::Format  8$  nvrhi::DrawArguments  {$  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + N  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �!  nvrhi::static_vector<nvrhi::BindingSetItem,128>  K   nvrhi::BindingLayoutDesc   R  nvrhi::SamplerAddressMode  �&  nvrhi::IDevice ! �"  nvrhi::BindingLayoutHandle ! �!  nvrhi::BindingSetItemArray . l  nvrhi::RefCountPtr<nvrhi::ICommandList>  琒  nvrhi::DeviceHandle   僑  nvrhi::TiledTextureRegion  �$  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & �!  nvrhi::VariableRateShadingState  S  nvrhi::IStagingTexture . 1"  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " 6  nvrhi::ShaderSpecialization 8 -  nvrhi::ShaderSpecialization::<unnamed-type-value>  R  nvrhi::TextureDimension 0 �"  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' j$  nvrhi::DispatchIndirectArguments  蔿  nvrhi::SamplerHandle * I$  nvrhi::DrawIndexedIndirectArguments # P&  nvrhi::DescriptorTableHandle  �  nvrhi::ShaderType  0&  nvrhi::TimerQueryHandle   �   nvrhi::BindlessLayoutDesc    nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! 8#  nvrhi::MeshletPipelineDesc 9 �   nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �%  nvrhi::HeapHandle # @&  nvrhi::ComputePipelineHandle  vS  nvrhi::PackedMipDesc  x  nvrhi::RasterFillMode  u   nvrhi::ArraySlice ! �!  nvrhi::VariableShadingRate  �  nvrhi::IResource  �#  nvrhi::IBindingSet  rS  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - !S  nvrhi::RefCountPtr<nvrhi::IBindingSet> * 
&  nvrhi::SamplerFeedbackTextureHandle # �!  nvrhi::SinglePassStereoState % ;!  nvrhi::ISamplerFeedbackTexture  �%  nvrhi::CommandQueue  A  nvrhi::BlendFactor  '&  nvrhi::EventQueryHandle  0   nvrhi::BindingLayoutItem  ;&  nvrhi::FramebufferHandle 1 @  nvrhi::static_vector<enum nvrhi::Format,8>  RS  nvrhi::BufferHandle  �  nvrhi::StencilOp  #  nvrhi::IBindingLayout  <  nvrhi::ColorMask  �  nvrhi::FramebufferInfo  絉  nvrhi::TextureHandle  nS  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  hS  nvrhi::IMessageCallback  �!  nvrhi::PrimitiveType  p  nvrhi::BlendState & U  nvrhi::BlendState::RenderTarget 3 鰈  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 �#  nvrhi::static_vector<nvrhi::IBindingSet *,5> " "  nvrhi::GraphicsPipelineDesc H �"  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) RS  nvrhi::RefCountPtr<nvrhi::IBuffer> $ ,S  nvrhi::TiledTextureCoordinate  (S  nvrhi::IHeap # u  nvrhi::FramebufferAttachment  �#  nvrhi::BindingSetVector  !S  nvrhi::BindingSetHandle ( 鸕  nvrhi::SamplerFeedbackTextureDesc ! �"  nvrhi::BindingLayoutVector " �%  nvrhi::StagingTextureHandle  �  nvrhi::Object  ;"  nvrhi::IInputLayout  z  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  v  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags ! +  nvrhi::rt::GeometrySpheres # 蒖  nvrhi::rt::ShaderTableHandle +   nvrhi::rt::OpacityMicromapUsageCount $ �$  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   E&  nvrhi::rt::PipelineHandle ! E  nvrhi::rt::AffineTransform & �$  nvrhi::rt::PipelineHitGroupDesc  >  nvrhi::rt::GeometryLss 3 馬  nvrhi::rt::cluster::OperationBlasBuildParams . 鞷  nvrhi::rt::cluster::OperationMoveParams ( 鏡  nvrhi::rt::cluster::OperationDesc 3 鉘  nvrhi::rt::cluster::OperationClasBuildParams , 逺  nvrhi::rt::cluster::OperationSizeInfo * 跼  nvrhi::rt::cluster::OperationParams  G  nvrhi::rt::GeometryType ' X&  nvrhi::rt::OpacityMicromapHandle  a  nvrhi::rt::GeometryDesc - f  nvrhi::rt::GeometryDesc::GeomTypeUnion % n  nvrhi::rt::OpacityMicromapDesc #   nvrhi::rt::GeometryTriangles  -!  nvrhi::rt::IAccelStruct # Z&  nvrhi::rt::AccelStructHandle  �%  nvrhi::rt::IShaderTable ' �%  nvrhi::rt::DispatchRaysArguments  �%  nvrhi::rt::State     nvrhi::rt::GeometryAABBs  �$  nvrhi::rt::PipelineDesc  蠷  nvrhi::rt::IPipeline  �  nvrhi::CpuAccessMode  l  nvrhi::CommandListHandle # @$  nvrhi::DrawIndirectArguments ! 臨  nvrhi::TextureTilesMapping  E  nvrhi::HeapDesc  �&  nvrhi::ICommandList  �  nvrhi::BufferDesc  (  nvrhi::IDescriptorTable * 絉  nvrhi::RefCountPtr<nvrhi::ITexture>  C  nvrhi::BlendOp  V$  nvrhi::ComputeState  �#  nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc    nvrhi::Viewport  �!  nvrhi::RenderState  f"  nvrhi::ShaderHandle  �  nvrhi::ITexture  pR  nvrhi::ITimerQuery & 鎗  ImVector<ImFontAtlasCustomRect>  �-  __std_win_error  �.  lconv   俀  __RTTIBaseClassDescriptor  >k  ImVector<float> 
    _off_t  �  stat  t   ImFontAtlasFlags  <t  ImGuiComboFlags_  t   int32_t    timespec  z7  __std_fs_file_id 
 !   _ino_t  [t  ImGuiTabBarFlags_  :t  ImGuiColorEditFlags_ ' c7  __std_fs_create_directory_result  t   ImGuiViewportFlags  !   uint16_t  �-  __std_fs_stats  鏵  ImVector<char> ! wk  donut::engine::ShaderMacro # xl  donut::engine::ShaderFactory " l  donut::engine::StaticShader  沰  donut::app::ImGui_NVRHI M ]  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  Wt  ImGuiButtonFlags_  H  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; R  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t  Nk  ImGuiViewport  �  terminate_handler  観  _s__RTTIBaseClassArray  xj  ImFontAtlas 
   ldiv_t 0 塮  ImVector<ImGuiTextFilter::ImGuiTextRange>  �-  __std_fs_file_flags  �.  _Cvtvec  !   ImDrawIdx  裬  ImGuiIO  t   ImDrawListFlags - 烸  $_s__RTTIBaseClassArray$_extraBytes_24  綫  _CatchableTypeArray  Vk  ImGuiPlatformImeData  $f  ImGuiPayload  �-  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  廹  ImDrawCmd  鬛  _PMD   i  ImVector<ImVec4>      uint8_t  秇  ImVector<unsigned short>     type_info  Ut  ImFontGlyph ' 嶲  _s__RTTIClassHierarchyDescriptor  t   errno_t  t   ImGuiWindowFlags  �-  __std_fs_reparse_tag    _lldiv_t 
 騟  ImVec4  Pt  ImGuiCol_  @t  ImGuiWindowFlags_  7  __std_type_info_data & 嘠  $_TypeDescriptor$_extraBytes_27  Bt  ImDrawFlags_  �  _s__ThrowInfo  !.  __std_fs_convert_result  !   ImWchar16  �-  __std_fs_stats_flags  乭  ImVector<ImDrawCmd>  観  __RTTIBaseClassArray / Tg  ImVector<ImGuiStorage::ImGuiStoragePair>  �  __crt_locale_data_public  j  ImFontGlyphRangesBuilder  杋  ImDrawVert - 蔘  $_s__CatchableTypeArray$_extraBytes_24  Lt  ImGuiSelectableFlags_ & 琎  $_TypeDescriptor$_extraBytes_25 % 嶲  __RTTIClassHierarchyDescriptor  �.  _Collvec   �6  __std_fs_volume_name_kind     __time64_t  U  FILE  鎖  ImVector<ImDrawList *>  u   ImGuiID  t   ImGuiBackendFlags 3 螿  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  >t  ImGuiTableColumnFlags_  綫  _s__CatchableTypeArray   f  ImGuiTableColumnSortSpecs  _7  __std_fs_remove_result  t   ImGuiKeyChord  u   ImU32  Ht  ImGuiNavInput  媔  ImDrawCmdHeader  t   ImGuiChildFlags  Ft  ImGuiKeyData    ImTextureID  t   ImGuiSortDirection - 決  $_s__RTTIBaseClassArray$_extraBytes_16  痠  ImDrawData  8t  ImVector<ImFontGlyph> -   $_s__RTTIBaseClassArray$_extraBytes_32  t   ImGuiInputTextFlags 
 #   size_t 
    time_t  �-  __std_fs_file_attr  =  __std_exception_data 
 u   _dev_t  �6  __std_ulong_and_error  t  ImGuiTableFlags_ 
 鏴  ImVec2  Of  ImGuiTextFilter & 攆  ImGuiTextFilter::ImGuiTextRange    lldiv_t  `g  ImGuiListClipper    _ldiv_t  t  ImGuiPopupFlags_  遟  ImVector<ImDrawChannel>    _timespec64     intptr_t   t  ImVector<ImFontConfig>  u   uint32_t 
 U  _iobuf ! f  ImGuiInputTextCallbackData  Ri  ImVector<void *>  Q  __crt_locale_pointers    �   h      V� c鯐鄥杕me綻呥EG磷扂浝W)  M    G�膢刉^O郀�/耦��萁n!鮋W VS  �    o�椨�4梠"愜��
}z�$ )鰭荅珽X  �    黸|�
C�%|�,臍稇l裹垓芻喭,vg�     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  f   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   f扥�,攇(�
}2�祛浧&Y�6橵�  �   曀"�H枩U传嫘�"繹q�>窃�8  %   [届T藎秏1潴�藠?鄧j穊亘^a  d   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  ?   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   �
bH<j峪w�/&d[荨?躹耯=�     dhl12� 蒑�3L� q酺試\垉R^{i�  M   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  �   t	*=Pr,�8qQ镯椅鯘�=咽Bz     攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  O   B�睃娏�,棒繪�1舸麂筓1�0m蘉##  �   鹴y�	宯N卮洗袾uG6E灊搠d�  �   繃S,;fi@`騂廩k叉c.2狇x佚�     Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  d   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   蕰уt厅隍2GeE升QU"柬�)忲嗅{≌洮  $   j轲P[塵5m榤g摏癭 鋍1O骺�*�  m   �"睱建Bi圀対隤v��cB�'窘�n  �   �E讻[没�+�(t参TnPV)V V� 
�  �   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  8   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  |   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  	   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  \	   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �	   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �	   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  
   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  j
   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �
   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �
   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  +   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�     +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   交�,�;+愱`�3p炛秓ee td�	^,  >   �*o驑瓂a�(施眗9歐湬

�  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  �    I嘛襨签.濟;剕��7啧�)煇9触�.  
   険L韱#�簀O闚样�4莿Y丳堟3捜狰  H
   L�9[皫zS�6;厝�楿绷]!��t  �
   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �
   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  
   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  K   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �     猯�諽!~�:gn菾�]騈购����'  A   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   +4[(広
倬禼�溞K^洞齹誇*f�5     v-�+鑟臻U裦@驍�0屽锯
砝簠@  Y   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  #   チ畴�
�&u?�#寷K�資 +限^塌>�j  W    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k     溶�$椉�
悇� 騐`菚y�0O腖悘T  f   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   副謐�斦=犻媨铩0
龉�3曃譹5D   �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  1   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   穫農�.伆l'h��37x,��
fO��      п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  X   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  !   豊+�丟uJo6粑'@棚荶v�g毩笨C  d   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  4   匐衏�$=�"�3�a旬SY�
乢�骣�  ~   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   悯R痱v 瓩愿碀"禰J5�>xF痧     矨�陘�2{WV�y紥*f�u龘��  e   妇舠幸佦郒]泙茸餈u)	�位剎  �   靋!揕�H|}��婡欏B箜围紑^@�銵  �   *u\{┞稦�3壅阱\繺ěk�6U�  $   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  f   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   t�j噾捴忊��
敟秊�
渷lH�#  .   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  z   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  B   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  1   v�%啧4壽/�.A腔$矜!洎\,Jr敎  {   D���0�郋鬔G5啚髡J竆)俻w��  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈     _O縋[HU-銌�鼪根�鲋薺篮�j��  T   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  /   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  m    狾闘�	C縟�&9N�┲蘻c蟝2  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  (   �'稌� 变邯D)\欅)	@'1:A:熾/�  q   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^     鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  E   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   郖�Χ葦'S詍7,U若眤�M进`      `k�"�1�^�`�d�.	*貎e挖芺
脑�  Y    J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �    o藾錚\F鄦泭|嚎醖b&惰�_槮  �    吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  1!   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  q!   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �!   �	玮媔=zY沚�c簐P`尚足,\�>:O  �!   +FK茂c�G1灈�7ほ��F�鳺彷餃�  %"   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  u"   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �"   �0�*е彗9釗獳+U叅[4椪 P"��  �"   �=蔑藏鄌�
艼�(YWg懀猊	*)  .#   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  x#   蜅�萷l�/费�	廵崹
T,W�&連芿  �#   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �#   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  +$   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   �      �  (  	  �  �	  B   �  �	  H   �  �	  Y   �  �	  �   �  �  U   �  �  �   �  �     �  �  2   �    �  �    �  �  @  q   �  @  q   �  �  �   �  @  q   �  @  q   �  (   K   7  	  B  :  	  �	  =  	  �	  �  �    �  �    �  �  �   �  �    �  �  �   �  �    �  �  �   �  	  �  �  	  �  �  	  +
  	  	  �    	  �    	  �    	  0   E  �  �   F  �  �   G  �  �   g  	  D
  j  	  O   �  �  �  �  �  �  �  	  L
  �  �  �     �  �     �  �     �  �     	  �  $  	  �  T  	  s  Y  	  �    	  )
  -  �  �   H  �  @   �  �  �  �  �  �     �  �     �  �   +  �  �  V  �  @   o  �  �      �  
     (
  [      (
  \      (
  i      (
  �      (
  �  !   (
  �  #   (
  $  $   (
  )  %   (
  +  &   (
  .  '   (
    +   (
  �  ,   (
  �  -   (
  n  .   (
  x  /   (
  }  0   (
    1   (
  [  2   (
  �  4   (
  r
  M   �  �   \   �    ]   �  �   ^   �  �   _   �  �   d     �  e   �  b  f   �  4  g   �  b  h   �  4  i   �  �  l   �    m   �  �   n   �  �   q   �    r   �  �   t   �    u   �  �   w   �    x   �  �   {   �    |   �  �   ~   �  �      @  5   �   @  @   �   @  5   �   @  @   �   �    �   �  �   �   �    �   �  �   �   @  @   �   @  5   �   @  @   �   @  5   �   @  �   �   @  5   �   @  x   �   @  5   �   	  �  �   �  �  �   �  �  �   �    �   �  �   �   �  �   �   �  �   �   �  �   �     t  �   �  H  �   �    �   �  �   �   �  �   �   �    �   �  �   �   �  �   �   �    �   �  �   �   �  �   �   �    �   �  �   �   �  �   �   �    �   �  �   �   �  �   �   �    �   �  �   �   @  q   �   �  �   �   �  �   �   �    �   	  @
  �   	  <
  !  �  �   !  �  �   !  �  %   	!  �  �   
!  �  �   !  �  �   
!  p  �  !  �  
  !  �  
  !  	  �  $!  p  w  %!  p  q  &!  p  j  '!  p  K  )!  �  `  *!  �  �   .!  �  �  2!  p  �  3!  p  �  <!  �  j   ?!  �  G   @!  �  <   A!  �  1   B!  �  )   C!  p    F!  p  �  G!  p  S  H!  p  '  I!  p    J!  �  �  N!  �  �  Q!  p    R!  p  
  U!  �  �  V!  �  �  W!  p  {  Y!  �  X  Z!  �  P  ^!  �  �  _!  p  �  `!  p  �  a!  p  �  c!  p  �   d!  p  �  g!  �  �  j!  �  �  k!  p    l!  (   �   o!  �  G  q!  (   �   r!  �  �  s!  (   �  w!  p  �   z!  �  �  }!  �  C  ~!  �  <  �!  �  F  �!  �  �  �!  �  |  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  (  ?	  �!  �  �  �!  �  ]  �!  �  ]  �!  �  �  �!  (  Q	  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �    �!  (  a  �!  �  �  �!  �  R  �!  �  3  �!  �  @   �!  (  *	  �!  (  l  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  �  �  �!  (  ;  �!  (   9  �!  (  $	  �!  �  @   �!  �  �  �!  (  �   �!  (   5  �   w$   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\RTXPT\External\Donut\thirdparty\imgui\imgui.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\RTXPT\External\Donut\thirdparty\imgui\imconfig.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\RTXPT\External\Donut\include\donut\app\imgui_nvrhi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\src\app\imgui_nvrhi.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp  �       LP"  襑  n   諻  n  
 綵  o   耑  o  
 Vz      Zz     
    f i吓 �6qN櫿_7镾%   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_app.pdb   �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5         �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >e   _Bytes  AJ        9  $  >e    _Block_size  AH       1 
   >e    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        �  
 Z      N Z   �  �   (                      H 
 h   �         $LN14  0   e  O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   6   0   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
   6     6  
 s  �   w  �  
 �  6   �  6  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   M      �   Z  [ G            0   
   %   �!        �std::_Copy_memmove<unsigned short *,unsigned short *>  >!   _First  AJ          >!   _Last  AK          >!   _Dest  AM         AP          >e    _Count  AI  
                             H 
 h   �!   0   !  O_First  8   !  O_Last  @   !  O_Dest  O  �   @           0   �     4       � �   � �   � �!   � �%   � �,   8   0   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 p  8   t  8  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   M      �   R  S G            0   
   %   �!        �std::_Copy_memmove<ImDrawVert *,ImDrawVert *>  >鑗   _First  AJ          >鑗   _Last  AK          >鑗   _Dest  AM         AP          >e    _Count  AI  
                             H 
 h   �!   0   鑗  O_First  8   鑗  O_Last  @   鑗  O_Dest  O  �   @           0   �     4       � �   � �   � �!   � �%   � �,   9   0   9  
 z   9   ~   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
 h  9   l  9  
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   5  Q G            c       b   �!        �std::_Fnv1a_append_value<nvrhi::ITexture *> 
 >e   _Val  AJ          >6m   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �      e  O_Val     6m  O_Keyval  O   �   0           c   (     $       $	 �    &	 �b   '	 �,   :   0   :  
 v   :   z   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
 L  :   P  :  
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   =  R G            p       o   �!        �std::_Hash_representation<nvrhi::ITexture *>  >6m   _Keyval  AJ          AK       U  M        �!   T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �  �!      6m  O_Keyval  O   �   @           p   (     4       *	 �    +	 �   *	 �   +	 �o   ,	 �,   7   0   7  
 z   7   ~   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
 T  7   X  7  
 H塋$SVWAVAWH冹 L嬺H孂I�������I;��   L媦L+9I�H婭H+H样H嬔H殃I嬂H+翲;�囻   H�
I;苨
I嬈H塂$h�H塂$hI;�囆   H�4 H侢   r)H峃'H;�喍   �    H吚劥   H峏'H冦郒塁H咑t
H嬑�    H嬝�3跦塡$XJ�{M嬈M+荕�3诣    L婫H�L+翲嬎�    怘�H吷t3H媁H+袶漾H襀侜   rH兟'L婣鳬+菻岮鳫凐w3I嬋�    H�J�sH塆H�H塆H兡 A_A^_^[描    惕    惕    處   �    �   �    �   N   �   M      �    D     J  "   P  �       �   3  � G            U     U  f!        �std::vector<unsigned short,std::allocator<unsigned short> >::_Resize_reallocate<std::_Value_init_tag> 
 >   this  AJ          AM       ?*  DP    >e   _Newsize  AK          AV       B, 
 >Xo   _Val  AP           D`    >#     _Newcapacity  AH  ^       AH s     �   C  c m  Bh   c     �   �   >e    _Oldsize  AW  -     (     >#0    _Appended_first  AJ  �       >#0    _Newvec  AI  �       AI �     � � 
  BX   �     � �   M        �!  Sj�� M        �!  Sj��% M        -  w)
)%
��( M        �  ��$	%)
��
 Z   �   >e    _Block_size  AJ  �       AJ C      >e    _Ptr_container  AH  �       AH �     �  y 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z      N N M        �  ��
 Z      N N M        V  
j
	 N N N! M        �!  3jD%
 >e    _Oldcapacity  AJ  7     �   M % z 
  AJ �     � 	 }  >e    _Geometric  AH  V         AH s     �   C  c m  M        �!  
3 N N M        g!  
�� >#    _Count  AP  �       M        �!  �� N N M        �!  �� >#0   _First  AK  �       >#0   _Last  AP  �       M        �!  ��c >e    _Count  AP  �       N N% M        �!  ��h3#  M        �!  )��T >e   _Count  AK  �       M        �  ��)0
 Z   !  
 >   _Ptr  AJ       >#    _Bytes  AK  �     /    AK O     % M        �  �d#
3
 Z   �   >e    _Ptr_container  AP        AP     5  +  >e    _Back_shift  AJ  �     3  AJ     5 $   N N N N
 Z   �!               (         0@ � h!   �  �  �  �  �  -  �  V   !  e!  g!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!         $LN97  P     Othis  X   e  O_Newsize  `   Xo  O_Val  O �   �           U  �  
   t       � �   � �)   � �3   � �j   � ��   � ��   � ��   � ��   	 �7  
 �C  � �I  � �O  	 ��   �  � F            (   
   (             �`std::vector<unsigned short,std::allocator<unsigned short> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >   this  EN  P         ( 
 >Xo   _Val  EN  `         ( 
 Z   �!                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN97  P     Nthis  X   e  N_Newsize  `   Xo  N_Val  O   �   0           (   �     $        �
    �    �,   /   0   /  
 �   /   �   /  
 �   /   �   /  
 �   /   �   /  
 �   /     /  
 "  /   &  /  
 Q  /   U  /  
 a  /   e  /  
 }  /   �  /  
 �  /   �  /  
 �  /   �  /  
    /     /  
   /     /  
 $  /   (  /  
 �  /   �  /  
 �  /   �  /  
   /     /  
 &  /   *  /  
 I  /   M  /  
 Y  /   ]  /  
    /   $  /  
 <  /   @  /  
 e  /   i  /  
 y  /   }  /  
 �  /   �  /  
 .  /   2  /  
 N  /   R  /  
 �  /   �  /  
 �  /   �  /  
 @  /   D  /  
 a  /   e  /  
 u  /   y  /  
 �  /   �  /  
 �  /   �  /  
   /     /  
   /     /  
 �  {   �  {  
 H  /   L  /  
 �  ;   �  ;  
 �	  ;   �	  ;  
 �	  ;   �	  ;  
 �	  |   �	  |  
 g
  {   k
  {  
 �
  ;   �
  ;  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   !   #   F   H塡$H塋$VWATAVAWH冹 H嬺H孂I固烫烫烫I;�嚖  H婭H+I糶fffffffI嬆H鏖L孃I�I嬊H凌?L鳫婳H+I嬆H鏖H龙H嬄H凌?H蠬嬍H验I嬃H+罤;�嘑  H�H;苨
H嬈H塂$h�H塂$hI;��%  H��L�4�    I侢   r+I峃'I;��  �    H吚�  H峏'H冦郒塁�3呻M咑tI嬑�    H嬝3呻3蓩貶塡$XJ��   H嬛I+譼(I荋��@ H茾    H茾�    塇H岪H冴u鉒婫H�L+翲嬎�    怢�M吚tLH婳I+菼嬆H鏖H龙H嬄H凌?H蠬�扝菱H侜   rH兟'I婬鳯+罥岪鳫凐w@L嬃I嬋�    H�H�禜�僅塐I�H塆H媆$`H兡 A_A^A\_^描    惕    惕    趟   �    �   �    K  M   �  �    �     �      �  �       �   �  � G            �     �  i!        �std::vector<ImDrawVert,std::allocator<ImDrawVert> >::_Resize_reallocate<std::_Value_init_tag> 
 >0n   this  AJ          AM       ��  DP    >e   _Newsize  AK          AL       �� 
 >Xo   _Val  AP        ��  �   � �  AP      A  D`    >#     _Newcapacity  AH  �       AH �       Bh   �     G  ;  >e    _Oldsize  AW  I       >搃    _Newvec  AI  �       AI      � �   BX       � �   M        �!  a���. M        �!  a���.& M        -  ��)
+%��( M        �  ��$	%)
��
 Z   �   >e    _Block_size  AJ  �       AJ �      >e    _Ptr_container  AH  �       AH      � 
 � 
 >�    _Ptr  AI  �       AI      � �   M        �  ��
 Z      N N M        �  ��
 Z      N N M        �!  
��
	 N N N# M        �!  WD%
 >e    _Geometric  AH  �         AH �     * ! M        �!  W N N M        j!  �- >#    _Count  AK      4  M        �!  �  M        �!  �  M           �  N M           �( N N N N M        �!  丄 >搃   _First  AK  D      >搃   _Last  AP  A      M        �!  丄c >e    _Count  AP  G      N N% M        �!  丳hL#& M        �!  1乻f M        �  亄)=
 Z   !  
 >   _Ptr  AP �      >#    _Bytes  AK  {    )  AK �     % M        �  亜d#
@
 Z   �   >e    _Ptr_container  AJ  �      AJ �    B  :  >e    _Back_shift  AP  S    I  AP �    B 1   N N N N
 Z   �!               (         0@ � h"   �  �  �  �  �  -  �     "!  h!  j!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!         $LN107  P   0n  Othis  X   e  O_Newsize  `   Xo  O_Val  O�   �           �  �     �       � �   � �/   � �W   � ��   � �   � �
  � �  � �   � �=  � �P  	 ��  
 ��  � ��  � ��  	 ��   �  � F            (   
   (             �`std::vector<ImDrawVert,std::allocator<ImDrawVert> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >0n   this  EN  P         ( 
 >Xo   _Val  EN  `         ( 
 Z   �!                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN107  P   0n  Nthis  X   e  N_Newsize  `   Xo  N_Val  O  �   0           (   �     $        �
    �    �,   0   0   0  
 �   0   �   0  
 �   0   �   0  
 �   0   �   0  
 �   0   �   0  
   0     0  
 :  0   >  0  
 i  0   m  0  
 y  0   }  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0     0  
 "  0   &  0  
 2  0   6  0  
 �  0   �  0  
   0     0  
 m  0   q  0  
   0     0  
 3  0   7  0  
 p  0   t  0  
   0     0  
 %  0   )  0  
 5  0   9  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  ~   �  ~  
   0     0  
 �  <   �  <  
 T	  <   X	  <  
 w	  <   {	  <  
 �	     �	    
 K
  ~   O
  ~  
 �
  <   �
  <  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �      #   F   H塡$UVWATAUAVAWH冹0I嬸L嬺H嬮A�H�%#"勪滘薍3菻撼     HA禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻H墝$�   L岴0I� H#罤繦婱H媆�H峌L�"I;躸
I嬡L塂$pM孅隟H�罤�H;CtH;賢*H媅H;Cu馡�A艶 I嬈H媆$xH兡0A_A^A]A\_^]肔嬨H岴0H塂$pL孄L峬H�������H9E勽  H塗$ H荄$(    �    �    H孁H塂$(H�H塇H茾    H婱�] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繦媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺勼   �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛H嬐�    H婱0L媱$�   I#菻蒆婾H婦�H媇H;胾H荄$(    �$H�蔋婳H;Ht怘嬝H;聇"H婡H;Hu頗�H塡$ L孄L峬H岴0L嬨�-H塂$ H荄$(    L孄L峬H岴0L媎$ �
H婦$pL媱$�   H婼H�EL�'H塛H�:H墈I婱 H� I#繦繪�罫;EuH�<岭M;莡H�<岭H9T�uH墊�I�>A艶橥��H�
    �    �>  �    �  K   �  �   .     !  �   &  �       �   x  kG            +     +  !        �std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<nvrhi::ITexture * const &> 
 >黮   this  AJ          AN       �   >6m   _Keyval_arg  AL       �  t� AP          AL �       >#q   _Target  CH      
      CI      �       CT          � ��  CU          {	 � �W  CW      �     Q  K  CT     �     3 � CU     �     3 � >Hq   _Newnode  CM     E      B    �     3 , t � � 1 M        k!  ��*,'%md	& >m    _Where  AI  �     d  
 =   AI �     @  fi 
 >Sm    _End  AT  �     Q 6   AT �     @  � �  >Sm    _Bucket_lo  AJ  �     G     AJ �     :  R � >e    _Bucket  AH  �       M        w!  �� M        �!  �� N N N M        c!  { M        �!  { M        �!  { M        �!  { M        �!  {5 M        �  >(4(4(4(4(4(4
 >#    _Val  B�   �     � AJ  .     {  AP  >    � | 
  N N N N N N M        o!  �*% M        �!  丣 M        �!  丣 M        �!  丣 M        _   丵 N N N N M        �!  �/	 M        �!  
�8 M        -  
�8 M        �  
�8
 Z      N N N N M        �!  �* N N M        &!  ��
 Z   �   N M        l!  �� N M        %!  乊D5Y >e    _Newsize  AJ  f      AJ �    Z  I �  >e    _Oldsize  AJ  ]    	  M        3!  乚 N N8 M        k!  �2/,$%k
 >m    _Where  AH  M    f H  
 >Sm    _End  AI  Q      AI �     3 }� �-  >Sm    _Bucket_lo  AK  e    U     AK �    F  -  >e    _Bucket  AJ  6      M        w!  俥 M        �!  俥 N N N M        $!  k伹
 Z   1!    M        2!  伹B
 >e   _Req_buckets  AJ  	    $  C             M        F!  6伹 N N N M        q!  
� N2 M        '!  偳$$#$#d$&CJ$"E >╭    _Bucket_array  AJ  �    =  AJ �       >Sm    _Insert_after  AK  �    S  AK �       >e    _Bucket  AH  �      N 0           8         0@ hF   �  �  �  �  �  �  �  �  �  -  �  �  �  `  ^   _   �   !  !  !  $!  %!  &!  '!  (!  .!  2!  3!  4!  6!  E!  F!  N!  c!  k!  l!  m!  n!  o!  p!  q!  r!  s!  t!  w!  !  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!         $LN206  p   黮  Othis  �   6m  O_Keyval_arg      Hq  O_Newnode  O�   �           +  p     �       � �   � ��   � ��   � ��   � �  � �  � �*  � �Y  � ��  � �2  � ��  � ��  � ��  � ��  � �  � ��   �  zF                                �`std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<nvrhi::ITexture * const &>'::`1'::dtor$1                         �  O  �   �  zF                                �`std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<nvrhi::ITexture * const &>'::`1'::dtor$0                         �  O  ,   .   0   .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
   .     .  
 ,  .   0  .  
 @  .   D  .  
 \  .   `  .  
 |  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 K  .   O  .  
 c  .   g  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 
  .     .  
   .     .  
   .     .  
 $  .   (  .  
 �  .   �  .  
 �  .   �  .  
   .     .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
   .     .  
 )  .   -  .  
 �  .   �  .  
 �  .   �  .  
 v	  .   z	  .  
 �	  .   �	  .  
 �	  .   �	  .  
 �	  .   �	  .  
 �	  .   �	  .  
 .  y   2  y  
 �  .   �  .  
 L  C   P  C  
 �
  =   �
  =  
 H崐    �       2   H崐    �       1   @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�   N      �   �   H G                     �!        �std::_Zero_range<unsigned short *>  >#0   _First  AJ          >#0   _Last  AI         AK                                H 
 h   �!   0   #0  O_First  8   #0  O_Last  O  �   8              �     ,       � �   � �   � �   � �,   5   0   5  
 o   5   s   5  
 �   5   �   5  
 �   5   �   5  
   5     5  
 H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   q  _G            �         |!        �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >╭   _First  AJ        0  AJ b     "  >╭   _Last  AK          AR       } 
 >瀜   _Val  AP        �  >卶    _UFirst  AQ       u                        @  h   {!  �!      ╭  O_First     ╭  O_Last      瀜  O_Val  O   �   X           �   �     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,   4   0   4  
 �  4   �  4  
 �  4   �  4  
 �  4   �  4  
 �  4   �  4  
 �  4   �  4  
   4     4  
 �  4   �  4  
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  mG                       z!        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >╭   _First  AJ          AJ       
   >╭   _Last  AK          
 >瀜   _Val  AP           >蓃   _Backout  CJ            CJ          
   M        �!    N M        �!   N                        H & h   {!  �!  �!  �!  �!  �!  �!  �!      ╭  O_First     ╭  O_Last     瀜  O_Val  O�   H               �     <       � �    � �   � �   � �   � �   � �,   3   0   3  
 �  3   �  3  
 �  3   �  3  
 �  3   �  3  
 �  3   �  3  
   3     3  
 "  3   &  3  
 �  3   �  3  
 H�    H嬃�   �   �   s G                   
   �         �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >�"   this  AJ                                 @�     �"  Othis  O   �   0              �     $       �  �    �  �   �  �,      0     
 �      �     
 �      �     
 H嬃�   �   �   K G                      (        �nvrhi::BindingSetItem::BindingSetItem 
 >�    this  AJ                                 H     �   Othis  O   �                  (
             �,      0     
 p      t     
 �      �     
 3繦�H堿H嬃�   �   �   7 G            
                   �nvrhi::Rect::Rect 
 >   this  AJ        
                         H       Othis  O   �               
   (
            �  �,      0     
 \      `     
 �      �     
 � H嬃茿�   �   �   S G                      <         �nvrhi::BlendState::RenderTarget::RenderTarget 
 >?   this  AJ                                 H�     ?  Othis  O   ,      0     
 x      |     
 H�    H嬃�   �   �   U G                   
   R         �nvrhi::VertexBufferBinding::VertexBufferBinding 
 >A#   this  AJ                                 H�     A#  Othis  O ,      0     
 z      ~     
 3狼A  �?H�H堿堿H嬃�   �   �   ? G                               �nvrhi::Viewport::Viewport 
 >   this  AJ                                 H       Othis  O   �                  (
            i  �,      0     
 d      h     
 �      �     
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   C   %   �    ,   I      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >e   this  AI  	     2  AJ        	  >j   __that  AH         AK          M        �  :$
 Z   Z   N                       H� 
 h   �   0   e  Othis  8   j  O__that  O ,      0     
 d      h     
 t      x     
 �      �     
 �      �     
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   C   %   �    ,   L      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >|   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   Z   N                       @�  h   �  �   0   |  Othis  8   �  O__that  O   ,      0     
 z      ~     
 �      �     
 �      �     
 �      �     
 H�    H茿    H堿H�    H�H嬃�   O      L      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >|   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      |  Othis  O   �   8           !   �	     ,       �  �    �  �   �  �   �  �,   
   0   
  
 z   
   ~   
  
   
     
  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   C   %   �       �   �   ? G            2      ,   �        �std::exception::exception 
 >!   this  AI  	     (  AJ        	  >%   _Other  AH         AK         
 Z   Z                         H�  0   !  Othis  8   %  O_Other  O �   0           2   �	     $       H  �   I  �)   J  �,       0      
 d       h      
 t       x      
 �       �      
 �       �      
            
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >�"   this  AH         AJ          AH        M        �  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �"  Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 P     T    
 h     l    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         ^         �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >�R   this  AH         AJ          AH        M        �   GCE
 >p#    temp  AJ  
       AJ        N (                     0H� 
 h   �    0   �R  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   -   0   -  
 �   -   �   -  
 �   -   �   -  
 �   -   �   -  
 �   -   �   -  
 �   -     -  
 J  -   N  -  
 d  -   h  -  
 H婭H吷t
�    �    �   �       �   �  BG                      .!        �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >0q   this  AJ          M        N!  
	 M        �  
	
 >   _Ptr  AJ         N N                        H�  h   �  �  N!      0q  Othis  O   �   8              �     ,       � �    � �	   � �   � �,   1   0   1  
 g  1   k  1  
 �  1   �  1  
   1     1  
 @SH冹 H嬞H婭H吷t2H婹H呉tH茿    H�H嬍�PH婯H吷t�    H兡 [�    H兡 [聾   �       �     FG            J      D   !        �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >+q   this  AI  	     @ 6   AJ        	  M        .!  0
 M        N!  5
 M        �  5

 >   _Ptr  AJ  
       AJ 0       N N N M        r!   M        ^    M        �   DE

 >p#    temp  AK         AK 0      
 
  >"     ref  A  0       N N N                      0H� . h
   �  �  ^   �   !  .!  N!  r!  !  �!   0   +q  Othis  9)       �   O �   8           J   �     ,       L �	   M �   N �0   P �,   2   0   2  
 k  2   o  2  
   2   �  2  
 �  2   �  2  
 �  2   �  2  
 l  2   p  2  
 |  2   �  2  
 �  2   �  2  
   2     2  
 0  2   4  2  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   d         �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >l   this  AJ        +  AJ @       M        �   &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �    0   l  Othis  9+       �   9=       �   O�   0           K        $       � �   � �E   � �,   ,   0   ,  
 �   ,   �   ,  
 �   ,   �   ,  
   ,   
  ,  
 |  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �    [   �       �   �  D G            `      `   L         �nvrhi::BufferDesc::~BufferDesc 
 >�   this  AI  
     S L   AJ        
  M        7  GM) M        �  -(

 M        	   N M        g  -G M        �  &@ M        �  )
 Z   !  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   �   >e    _Ptr_container  AP  '     8    AP ;       >e    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  7  x  �  �  	  g  h  i  �  �  �         $LN37  0   �  Othis  O ,      0     
 i      m     
 }      �     
 ]     a    
 �     �    
 �     �    
 �     �    
          
 �  `   �  `  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂0    H荂8   艭  H兡 [描    �<   �    [   �       �   �  F G            `      `   8         �nvrhi::TextureDesc::~TextureDesc 
 >N   this  AI  
     S L   AJ        
  M        7  GM) M        �  -(

 M        	   N M        g  -G M        �  &@ M        �  )
 Z   !  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   �   >e    _Ptr_container  AP  '     8    AP ;       >e    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  7  x  �  �  	  g  h  i  �  �  �         $LN37  0   N  Othis  O   ,      0     
 k      o     
       �     
 _     c    
 �     �    
 �     �    
 �     �    
          
 �  \   �  \  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �    Y   �       �   �  V G            ^      ^   D         �nvrhi::VertexAttributeDesc::~VertexAttributeDesc 
 >�   this  AI  
     Q J   AJ        
  M        7  EK) M        �  ,(
	 M        	   N M        g  ,E M        �  &? M        �  )
 Z   !  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        �  
"#
!
 Z   �   >e    _Ptr_container  AP  &     7    AP :       >e    _Back_shift  AJ  -     0 
   N N N N N N                       @� : h
   �  �  7  x  �  �  	  g  h  i  �  �  �         $LN37  0   �  Othis  O   ,      0     
 {           
 �      �     
 o     s    
 �     �    
 �     �    
      	    
 +     /    
 �  ^   �  ^  
 H�    H�H兞�       C      �       �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >|   this  AJ          M        �   	
 N                        H�  h   �  �      |  Othis  O ,      0     
 {           
 H�    H�H兞�       C      �       �   �   @ G                      �        �std::exception::~exception 
 >!   this  AJ         
 Z   r                          H�     !  Othis  O  �   (              �	            Y  �
   Z  �,      0     
 e      i     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   C      �    0   �       �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >e   this  AJ          AM       -  M        �  

	
 Z   r   N                       @�  h   �  �   0   e  Othis  O ,      0     
 w      {     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   C      �    0   �       �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >|   this  AJ          AM       -  M        �  

	
 Z   r   N                       @�  h   �  �  �   0   |  Othis  O  ,   
   0   
  
 �   
   �   
  
 �   
   �   
  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   C      �    0   �       �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >!   this  AJ          AM       -  M        �  

	
 Z   r   N                       @� 
 h   �   0   !  Othis  O ,      0     
 w      {     
 �      �     
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >    __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H      O__f  9(           O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H塡$H塴$H塼$ WAVAWH冹 H嬹H�������H饺A�   A嬊H余H;�噯  H岯�I荋饺�罥隅H媈H塡$HO�4?H媀 H婲H嬄H+罤柳I;�兪   H�������L;��5  N�4�    I侢   r)I峃'I;��  �    H吚�  H峹'H冪郒塆M咑t
I嬑�    H孁�3�H婲H婩(H+罤柳H吚t2H��    H侜   rH兟'L婣鳬+菻岮鳫凐嚰  I嬋�    H墌I�>H塅 H塅(H;鴗H�H兦H;鴘綦
L岲$H�    I岹�H塅0L墌8H婩H� H嬋H;��  H�%#"勪滘薍砍      H�	D禜L3蚅禤L3蔐禤L3蔐禤L3蔐禤L3蔐禤L3蔐禤L3蔐禤L3蔐L媈0M#買零L^M�L;藆	I�I塁雡I婼L婡L;Bu/L�L;衪!L婬I�	L婣M�I婻H�M塀L塈H塒I塁�:L;蕋f怘婻L;BtNL;蕌馤婸I�
L婭I�L婤I� L塉L塓L堾I�H嬃H;�����H媆$@H媗$PH媡$XH兡 A_A^_肔�L婬I�	L婣M�I婻H�M塀L塈H塒氲�    蘃�
    �    惕    台   �    �   �      �    F  4   �     �  �   �  �    �  �       �   L  RG            �     �  1!        �std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Forced_rehash 
 >黮   this  AJ          AL       �s
  >#   	 _Buckets  AK        �N v AW  N     �F v
  Co      /       Co     �    
  >e   _Max_storage_buckets  AH  5     �
 � C       2      
 >襮    _End  AI  R     �2 r
  >襮    _Inserted  AH  ]    a >襮    _Next_inserted  AJ  `    ^ >漲    _Bucket_lo  AS  �    � �   AS �    ` �  >e    _Bucket  AS  �      M        �  " N M        I!  乂 M        Z!  乂 M        !  乑 N N N@ M        C!  [)a/E2$$$$
亊

 Z   |!   >e   _Cells  AV  [     � 6 �  AV J    zL 
 >襮   _Val  BH   W     �m
  >e    _Oldsize  AH  f     �    �  >╭    _Newend  AH  %      AH J      >e    _Oldcapacity  AH  �     ,    AH     
  >╭    _Newvec  AM  �       AM �      b  � �:  M        R!  [ N M        Q!  �� N M        U!  av侶& M        -  ��)
)%
侀( M        �  ��$	%)
�
 Z   �   >e    _Block_size  AJ  �       AJ �      >e    _Ptr_container  AH  �       AH �     � �
 >�    _Ptr  AM  �       AM �      b  � �:  M        �  ��
 Z      N N M        �  ��
 Z      N N M        H  
v
 N N M        z!  �-#" >蓃   _Backout  CM     9      CM    2    e  K  M        �!  �- N M        �!  �2 N N M        V!  2��佹  M        �  ��)伖
 Z   !  
 >   _Ptr  AJ       >#    _Bytes  AK  �     *  AK �     & M        �  ��d#伡
 Z   �   >e    _Ptr_container  AP        AP     � � >e    _Back_shift  AJ  �     =  AJ     �( 
 H q N N N N M        H!  N M        Y!  N M        !  N N N N M        �  
> M        �  B  >#    _Value  AH  B     $  N N M        G!  `亙 M        c!  Y亙 M        �!  Y亙 M        �!  Y亙 M        �!  Y亙 M        �!  Y亙4 M        �  亙(4'4'4'4'4'4'4
 >#    _Val  AQ  �    c  N N N N N N N M        <!  亐 M        A!  亐 N N M        ?!  侂 N M        w!  �  M        �!  �  N N M        A!  �
 N& M        J!  �$#$#$c$ >Sm    _Before_prev  AK  $      AK �     �  >Sm    _Last_prev  AP        AP �    � o  >Sm    _First_prev  AQ      #  AQ �     �  N& M        J!  侽$#$#$c$ >Sm    _Before_prev  AP  a      AP �    � o  >Sm    _Last_prev  AQ  Z      AQ �     �  >Sm    _First_prev  AR  S       AR �    � , �    N M        @!  侤 N& M        J!  倹$#$#$c$ >Sm   _First  AR  �    #  AR �    � , �    >Sm    _Before_prev  AK  �      AK �     �  >Sm    _Last_prev  AP  �      AP �    � o  >Sm    _First_prev  AQ  �      AQ �     �  N
 Z   �                         @ hF   �  �  �  �  �  �  �  -  H  �  ^   �   !  !  (!  4!  :!  ;!  <!  =!  >!  ?!  @!  A!  B!  C!  D!  G!  H!  I!  J!  P!  Q!  R!  U!  V!  W!  Y!  Z!  \!  ^!  b!  c!  p!  r!  w!  y!  z!  {!  }!  ~!  !  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!         $LN263  @   黮  Othis  H   #   O_Buckets  O�   8          �  p  $   ,      � �   � �)   � �5   � �>   � �N   � �R   � �J  � �R  � �V  � �]  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �   � �
  � �
  � �  � �3  � �7  � �9  � �@  � �J  � �O  � �p  � �s  � �   ��  � ��  � ��  � ��  � �,      0     
 w     {    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 !     %    
 9     =    
 X     \    
 �     �    
 �     �    
 �     �    
 �     �    
          
 �     �    
 �     �    
          
 >     B    
 h     l    
 x     |    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 
         
 -     1    
 =     A    
          
          
 �     �    
 �     �    
 �     �    
 N     R    
 ^     b    
 �     �    
 �     �    
 H	     L	    
 )
     -
    
 0     4    
 @     D    
 i     m    
 y     }    
 �     �    
 �     �    
 
         
          
 C     G    
 S     W    
 }     �    
 �     �    
 �      
    
 
     
    
 ;
     ?
    
 K
     O
    
 t
     x
    
 �
     �
    
 �
     �
    
 �
     �
    
   c      c  
 `     d    
 H冹HH峀$ �    H�    H峀$ �    �
   
      R      F      �   �   F G                       �        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               x            J �   K �,      0     
 �   V   �   V  
 �      �     
 H冹(H�
    �    �   k      �       �   w   7 G                             坰td::_Xlen_string 
 Z   �   (                      @        $LN3  O �   (              	            		 �   
	 �,      0     
 s   X   w   X  
 �      �     
 H冹(H�
    �    �   �      �       �   �   k G                     �!        坰td::vector<unsigned short,std::allocator<unsigned short> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (              �            a �   b �,   "   0   "  
 �   k   �   k  
 �   "   �   "  
 H冹(H�
    �    �   �      �       �   �   c G                     �!        坰td::vector<ImDrawVert,std::allocator<ImDrawVert> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (              �            a �   b �,       0      
 �   g   �   g  
 �       �      
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   M   �   �    �   �    �   L   ,  �    O     U     [  �       �   '  r G            `     `  �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >�   _Ptr  AK          AW       D/  >e   _Count  AL       G4  AP         B M        !  E
(?SD3$--K
 Z      >#     _New_capacity  AH  �     �  * N  V r  AM  O     =  b �  AH �     G  ,  AJ �       M        �   �� M           �� N N M          ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M        +  ��?�� >e   _Count  AJ  �      * M        -  ��

*%
u- M        �  ��	)
��
 Z   �   >e    _Block_size  AJ  �     �  �  AJ �       >e    _Ptr_container  AH  �       AH �     }  b 
 >�    _Ptr  AV  �       AV �     ~ V "  M        �  ��
 Z      N N M        �  ��
 Z      N N N N N M          X(  M        T  X' >e    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M        g  -�W M        �  �&P M        �  �
)/
 Z   !  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        �  
�#
2
 Z   �   >e    _Ptr_container  AP        AP +    4  *  >e    _Back_shift  AJ      
  AJ Z      N N N N N M        j  L4 N M          $# >p    _Result  AM  '       AM 8      M        	  ' N N                       H n h   �  �  �  �  �  	      g  i  j  �  �  �    !  T      -  �  �  +  X  �   !         $LN93  @   �  Othis  H   �  O_Ptr  P   e  O_Count � 蟨  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_66f57f934f28d61049862f64df852ff0>  O �   h           `  	  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
          
 �     �    
 �     �    
 �     �    
 �     �    
 ]     a    
 m     q    
 �     �    
 Y     ]    
 m     q    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 W     [    
 |     �    
 �     �    
 �     �    
 �     �    
       $    
 0     4    
 �     �    
 �     �    
 a  Z   e  Z  
 <     @    
 H冹(H嬃H媺P  H吷tH莯P      H��P怘兡(�   �   N  Q G            *      %   X         �donut::app::ImGui_NVRHI::backbufferResizing 
 >{k   this  AH         AJ          AH %       M        m    M        �   GGE
 >^#    temp  AJ         AJ %       N N (                     @  h   m   �    0   {k  Othis  9!       �   O  �   0           *   �     $       � �   � �%   � �,   &   0   &  
 v   &   z   &  
 �   &   �   &  
 �   &   �   &  
 �   &   �   &  
 �   &   �   &  
 J  &   N  &  
 d  &   h  &  
 H冹(H嬄K� H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �4   �    9   �       �   T  P G            >      >   �!        �std::allocator<unsigned short>::deallocate 
 >8o   this  AJ          AJ ,       D0   
 >#0   _Ptr  AK          >e   _Count  AP        =   M        �  )
 >   _Ptr  AH ,       >#    _Bytes  AK       2 " M        �  
#

 Z   �   >e    _Ptr_container  AJ       %    AJ ,       >e    _Back_shift  AH         AH ,       N N (                      H  h   �  �         $LN18  0   8o  Othis  8   #0  O_Ptr  @   e  O_Count  O�   8           >   �     ,       � �   � �/   � �3   � �,   !   0   !  
 u   !   y   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
   !     !  
 /  !   3  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
   i     i  
 h  !   l  !  
 H冹(H嬄K��H菱H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   P  L G            B      B   �!        �std::allocator<ImDrawVert>::deallocate 
 >do   this  AJ          AJ 0       D0   
 >搃   _Ptr  AK          >e   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        �  
#

 Z   �   >e    _Ptr_container  AJ       %    AJ 0       >e    _Back_shift  AH          AH 0       N N (                      H  h   �  �         $LN18  0   do  Othis  8   搃  O_Ptr  @   e  O_Count  O�   8           B   �     ,       � �   � �3   � �7   � �,      0     
 q      u     
 �      �     
 �      �     
 �      �     
 
         
 +     /    
 �     �    
 �     �    
 �     �    
 �     �    
   e     e  
 d     h    
 H塡$H塼$ UWAVH崿$ ��膏   �    H+郒�    H3腍墔�  H孃H嬹H塗$8H嬟H岭8H嬄H凌0D敦H嬄H凌(D缎H嬄H凌 D度H嬄H凌D独H嬄H凌缎H嬊H凌度@肚I�%#"勪滘薎3艻境     IH3罥H3翴I3繧I3罥I3翴I3肐H3肐H嫀�  H#菻玲H巔  H婣H嫋`  E3鯤;聇H�	H;xtH;羣H婡H;xu耠I嬈H吚tH;聇	H婡閊  茀�  D塼$X荄$\
   L塼$PL塼$`H荄$h   D塼$x荄$|   H墊$p    )E�H婩0D塽樓E�   H塃怢塽燣塽↖嬈H墔�  H峀$P@ H拎D�IL繦媴�  H�繦墔�  H兞 H峌癏;蕌虯�  H峌癏崓�  �    L塼$0H�H�L婲HL崊�  H峊$ �恅  I嬣H峀$@H;萾H�L�0H塡$0H婰$ H吷tL塼$ H��P怘崕X  L岲$8H峊$ �    H�8H9_t#H呟t
H�H嬎�P怘婳H塤H吷tH��P怘呟t
H�H嬎�R怘嬅H媿�  H3惕    L崪$�   I媅0I媠8I嬨A^_]�   I   &   �   j  n   �  L   J  .   �  J      �   �  L G            �  4   �  N         �donut::app::ImGui_NVRHI::getBindingSet 
 >{k   this  AJ        :  AL  :     q >f   texture  D8    AK        7  AM  7     � AM �    %  B!  
    � 
 >I!    desc  D�   >!S    binding  D0   
 > n    iter  AH     [    M        
!  �杸� M        d!  
��B, M        k!  ��*/'c >m    _Where  AH  �     ,  AH     [   
 >Sm    _End  AK  �     �  AK �    . �  >Sm    _Bucket_lo  AJ        AJ     {� �  >e    _Bucket  AJ  �       N N M        c!  L�� M        �!  L�� M        �!  L�� M        �!  L�� M        �!  L��4 M        �  ��->#4#4#4#4#4#4
 >#    _Val  AH  �     X  N N N N N N N M        ^   倆 M        �   倆	 N N M        ]   俀
 >�R   this  AM  Q    ;  AM �    %  M        ^   俷 M        �   俷
 >p#    temp  AJ  j      AJ z        N N M        �   俧 N M        �   俉 M        !  俉	 N N N M        i   �?
 Z   !   N M        ^   �" M        �   �"HB
 >p#    temp  AJ  '      AJ 8      B    1    �  B#  
    �  N N M        \   �
 M        �   � >p#    tmp  AI  
      AI �      N M        �   �
C
 M        �   � N N N M        _   侀 N M        �   
亶
& >
!    <begin>$L0  AJ  �    H  M        �  仩 N N! M        1   乿$' >�    sampler  AH  v      N M        0   乁%( N M        2   �5%(e	 N �                    A � h8   �  �  (  �  �  0   1   2   O   Z   [   \   ]   ^   _   `   a   i   j   s   }   �   �   �   �   �   �   �   �   �   �   �   �   �   �   !  !  !  !  !  
!  !  )!  c!  d!  k!  p!  u!  w!  �!  �!  �!  �!  �!  �!  �!  
 :�   O   !  {k  Othis  !  f  Otexture  �  I!  Odesc  0   !S  Obinding  9      O&   94      �   9b      �   9v      �   9�      �   O   �   h           �  �  
   \       �  ��   �  �   �  �%  �  �5  �  ��   ��   �8   �z   ��   ��   �   [ F                                �`donut::app::ImGui_NVRHI::getBindingSet'::`1'::dtor$0  >f   texture  EN  8          
 >I!    desc  EN  �          >!S    binding  EN  0                                  �  O  ,   )   0   )  
 q   )   u   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 +  )   /  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
   )     )  
 +  )   /  )  
 Q  )   U  )  
   )      )  
 �  )   �  )  
 �  )   �  )  
   )     )  
   )     )  
 �  )   �  )  
 
  )     )  
   )     )  
 *  )   .  )  
 |  )   �  )  
 �  )   �  )  
 0  )   4  )  
 �  )   �  )  
 Q  )   U  )  
 a  )   e  )  
 q  )   u  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 <	  >   @	  >  
 �	  >   �	  >  
 �	  >   �	  >  
 �	  >   �	  >  
 H崐0   �       -   @SH冹0H嬞H媮P  H吚uiH�	H�L岰PL嬍H峊$@��0  3襀峀$ H;萾
H�H�     H媼P  H墦P  H吷tH��P怘婰$@H吷tH荄$@    H��P怘媰P  H兡0[�   �   �  E G            �      ~   M         �donut::app::ImGui_NVRHI::getPSO 
 >{k   this  AI  	     z  AJ        	  >`#   fb  AK        '  AK ~       M        n   ] M        �   ]HB	
 >^#    temp  AJ  b       AJ w     
  B@   p       Bx   -     W  N N M        l   /- M        n   Q M        �   Q
 >^#    temp  AJ  J       AJ ]       N N M        �   C >^#   tmp  AK  <        AK ]     '    C       /     
  C      C     A   3   N M        �   -B

 M        �   9 N N N 0                    H " h   k   l   n   }   �   �   �    @   {k  Othis  H   `#  Ofb  9'       ?&   9Y       �   9s       �   O �   @           �   �     4       �  �	   �  �   �  �w   �  �~   �  �,   (   0   (  
 j   (   n   (  
 z   (   ~   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
   (     (  
   (     (  
 (  (   ,  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 	  (   
  (  
   (   !  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
 H塡$UVWATAUAVAWH崿$ ��H侅   H�    H3腍墔�
  M孁H嬟H孂L塃@L�	L;蕋&H呉t
H�H嬍�P怘�H�L嬎H吷t	H��PL�I�W�3�E�E菻塎仄E�H荅�   H荅�   H荅�   @圡豅岴窰峊$hI嬌�惃  E3銩嬙H峂HH;萾H�L� H婳H塛H吷tH��P怘婰$hH吷tL塪$hH��P怢塭郘塭鐻塭餖塭鳯塭 L塭A�   fD塼$@L塪$8H岴郒塂$0H岴餒塂$(H岴 H塂$ L�
    L�    H峊$pI��    I嬙H峂PH;萾H�L� H婳H塛H吷tH��P怘婰$pH吷tL塪$pH��P怢塭L塭L塭 L塭(L塭0L塭8�   f塡$@L塪$8H岴H塂$0H岴 H塂$(H岴0H塂$ L�
    L�    H峊$xI��    I嬙H峂XH;萾H�L� H婳H塛H吷tH��P怘婰$xH吷tL塪$xH��P怘媁H呉�  H� �  W�匑
  �   H壍P
  H菂X
     H窹OSITIONH墔@
  茀H
   茀`
  )H菂d
     D墺l
  菂p
     茀t
   厁
  H壍�
  H菂�
     H窽EXCOORDH墔x
  茀�
   茀�
  )H菂�
     壍�
  菂�
     茀�
   叞
  H菂�
     H菂�
     �    墔�
  �   垍�
  茀�
   茀�
  H菂�
     墲�
  菂�
     茀�
   H�H�H塗$ D峃鸏崊@
  H峌��愋   I嬙H峂`H;萾H�L� H婳 H塛 H吷tH��P怘婱�H吷tL塭�H��P惽厾   菂�    菂�   菂�    菂�   菂�    菂�   菂�    菂�   菂�    菂�   菂�    菂�   菂�    菂�   f菂�   茀�    f菂�   菂�      W�W蒮菂�    L墺�   H菂       �  W殷�  茀�    茀�   荄$\��  荄$`荄$d荄$X  呠  嶌  菂�      (厾   )卲  (嵃   )崁  (吚   )厫  (嵭   )崰  茀�   D$X叡  嶐   嵞  �   呍  �  嶄  f因曯  D墺  茀   3褹�   H崓  �    D墺  菂  �   菂      菂$  �  �?  f墔   L塪$P艱$T
f塼$VH婦$PH墔X  L塪$P艱$TH婦$PH墔`  L塪$P艱$TH婦$PH墔h  I嬏H墠0
  H崟X  ff�     H�H墑�0  H媿0
  H�罤墠0
  H兟H崊p  H;衭訦崓  H崊0  H嬛  HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�H�H�L崊   H峊$P�怭  I嬙H峂hH;萾H�L� H婳HH塛HH吷tH��P怘婰$PH吷tL塪$PH��P惼GPH媉 H9_Xt#H呟t
H�H嬎�P怘婳XH塤XH吷tH��P怘媉H9_`t#H呟t
H�H嬎�P怘婳`H塤`H吷tH��P怘媉H9焵   t)H呟t
H�H嬎�P怘嫃�   H墴�   H吷tH��P�(卲  噲   (崁  彉   (厫  嚚   (崰  徃   (叞  嚾   (嵗  徹   (呅  囪   (嵿  忴   (咅  �  H婳HH塎圚吷tH��P怘�    H塂$ L�
    A�   H嬛H崓(  �    怚嬼L墺P  H媇圚呟tH�H嬎�PH嫷P  H9滜(  t2H呟t
H�H嬎�P怘媽�(  H墱�(  H吷tH��P怘嫷P  H�艸壍P  H呟t
H�H嬎�P怘崫(  H崊(  H嵎   H+餉�   I嬆H峂xH;藅H�L�#H�H�H吷tH��P怘兠I冾u蠬媴P  H墖H  L�
    A峍E岶H崓(  �    怢�
    A峍E岶H峂堣    荅�  (
    M楬荅�  �?荅�崁   E�厫   H�H�L崊�   H峌�惾   I嬙H峂pH;萾H�L� H婳0H塛0H吷tH��P怘婱怘吷tL塭怘��P怢�
    �8   D岯薍崓@
  H�0 u�    隠�    怚�H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�P�隓H�
    �    怚�H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�P2繦媿�
  H3惕    H嫓$H  H伳   A_A^A]A\_^]�"   �   6  u   =  x   J     �  u   �  {   �       �   $  �   �  N   5     A     V  �    2	     F	  �    N	     _	  �    m	  �   �	     
  �    
  �    N
  ~   S
  +   �
  J      �     C G            �
  0   �
  9         �donut::app::ImGui_NVRHI::init 
 >{k   this  AJ        9  AM  9     #
�	5  >�&   device  AI  6     q AK        6  >薼   shaderFactory  D@   AP        3  AW  3     |
 DP   >蘬    vertexAttribLayout  D@   >�!    renderState  Dp   >K     layoutDesc  D    >p    blendState  D�  
 >_    desc  D�   M        �  �
 M        F  �
HB
 >�!    temp  AJ        AJ #    />� B  �    � Dx    N N M        �   %佺 M        �  � M        F  �
 >�!    temp  AJ  �      AJ 
      B�  '
    � $ "  N N M        �  侚 >�!    tmp  AK  �    "  AK 
        N M        o  佺C	 M        �  侒 N N N M        �  乼 M        F  乼HB
 >�!    temp  AJ  y      AJ �    X  B�  N    k	 Dp    N N M        �   %丯 M        �  乭 M        F  乭
 >�!    temp  AJ  d      AJ t      B�  '
    � $ "  N N M        �  乣 >�!    tmp  AK  Q    "  AK t    k    N M        o  丯C	 M        �  乑 N N N M        |   �� M        �   ��HB
 >�&    temp  AJ  �       AJ �     Z  B`  �     	
 Dh    N N M        {   (�� M        |   �� M        �   ��
 >�&    temp  AJ  �       AJ �       B@  '
    � $ "  N N M        �   �� >�&   tmp  AK  �       AK �     m    C       �       C      �     �   )   N M        �   ��F	 M        �   �� N N N M        ~   = M        �   Z M        	!  ZC	
 >�&    temp  AJ  W       AQ  ]       AQ k     E  B   '
    � $ "  >"     ref  Ai k     E  N N M        �   T N M        !  E M        *!  E#	 N N N M        �  儰 M        G  儰GB
 >�!    temp  AJ  �      AJ �    � B@  ~    ;�
  D�    N N M        �   %儈 M        �  儤 M        G  儤
 >�!    temp  AJ  �      AJ �      B   '
    � $ "  N N M        �  儛 >�!    tmp  AK  �    "  AK �    �   N M        �   儈C	 M        �  儕 N N N M        :  傴6 M        �  &�+ M           � N N M        �  傴 M        $  傴 M        Y  傴 N N N N M        :  倿* M        �  &偉' M           偡 N N M        �  倿 M        $  倿 M        Y  倿 N N N N M        :  �>/ M        �  &侲, M           俓 N N M        �  �> M        $  �> M        Y  �> N N N N M        �  %堮 M        �  � M        �  �
 >�!    temp  B�  '
    � $ "  N N M        �  � N M        E  堮C	 M        �  堼 N N N  M        �   圼J
 >�"    i  AI  i    q  M        �  埬 M        �  埬	 N N M        �   垀
 M        
!  垀
 M        �  埀 M        �  埀
 >�!    temp  B�  '
    � $ "  N N M        �  垪 N M          垐 M          垐#	 N N N N M          坋 M          坕# N N N M          � M          �&# N N M        �   噅
 M        �  嚁 M        F  嚁
 >�!    temp  AJ  �      AJ �    �  B0  '
    � $ "  N N M        �  噯 >�!    tmp  AI  n    �  N M          噖 M          噖#	 N N N M        �   �=
 M        �  嘵 M        F  嘵
 >�!    temp  AJ  Z      AJ j    �   #   B�  '
    � $ "  N N M        �  嘨 >�!    tmp  AI  A    -  N M          嘒 M          嘒#	 N N N M        �   �
 M        �  �1 M        G  �1
 >�!    temp  AJ  -      AJ =    �     E  P   B�  '
    � $ "  N N M        �  �) >�!    tmp  AI      -  N M        �   � M          �#	 N N N M        �  嗹 M        �  嗹HB
 >�!    temp  AJ  �     & AJ       !  F  N  v  �   BP       �F
  B�  �    �{
  N N M        �  %喰 M        �  嗞 M        �  嗞
 >�!    temp  AJ  �      AJ �      Bx  '
    � $ "  N N M        �  嗏 >�!    tmp  AK  �    " . AK �    X  2  F  _  s  �  �  ;  N M        E  喰C	 M        �  嗆 N N N! M        �   �
 >1     <begin>$L0  AK      G  M        �  �  N N M        .   
咃 >0     result  BP   �    � W
  N M        -   
呝 >0     result  BP   �      N M        /   吘E >0     result  BP   �      N M        �   厃% N M        '   勈 N M        %   劔 N M        &   
刧 N M        $   	剋 N M        #   劋 N M        !   	刕 N M           
兏 N M        u   売 M        �   売GB
 >�     temp  AJ  �	      AJ �	      Bh  �	    � 
  D�    N N M        t   %壄 M        u   壡 M        �   壡
 >�     temp  AJ  �	      AJ �	      BH  '
    � $ "  N N M        �   壙 >�     tmp  AK  �	    "  AK �	         N M        �   壄C	 M        �   壒 N N N M        +   墋 N M        ,   塩 N M           塲 N M        d   5� M        �   �,	 M        �  �
 >�   this  AM  
    5  AM �
    '  M        �  �4	
 N N N N Z   "  "  "  "  "  
"              8         A �h|   �  �  �  �  �  �  �  �  �  :  >  �  �  �  �  �  �  �  �  �  �      E  F  G  i  �  �  �  �          !  $  T  Y      -  �  �      +  X  o               !   "   #   $   %   &   '   (   )   *   +   ,   -   .   /   3   :   ;   <   =   >   ?   @   A   B   C   E   F   G   H   I   b   c   d   s   t   u   {   |   }   ~   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   	!  
!  !  !  *!  
 :�  O  @  {k  Othis  H  �&  Odevice  P  薼  OshaderFactory  @  蘬  OvertexAttribLayout  p  �!  OrenderState     K   OlayoutDesc  �  p  OblendState  �  _  Odesc  9P       �   9e       �   9�       i&   9�       �   9�       �   9p      �   9�      �   9	      �   9      �   9x      &&   9�      �   9�      �   9�      I&   9�      �   9      �   9%      �   99      �   9R      �   9f      �   9�      �   9�      �   9.      �   9t      �   9�      �   9�      �   9�      �   9	      �   9�	      $&   9�	      �   9�	      �   92
      �   9D
      �   9x
      �   9�
      �   O �   0          �
  �  #   $      |  �9   {  �=   }  �k     ��   �  ��  �  �#  �  �>  �  ��  �  ��  �  �^  �  ��  �  �g  �  ��  �  ��  �  �)  �  �5  �  �y  �  ��  �  ��  �  ��  �  �  �  �  �  �=  �  �j  �  ��  �  �  �  �c	  �  ��	  �  ��	  �  �
  �  �
  �  �K
  �  �X
  �  ��
  �  ��   �  R F                                �`donut::app::ImGui_NVRHI::init'::`1'::dtor$0  >薼   shaderFactory  EN  @          EN  P          >蘬    vertexAttribLayout  EN  @          >�!    renderState  EN  p          >K     layoutDesc  EN             >p    blendState  EN  �         
 >_    desc  EN  �                                 �  O  �   �  R F                                �`donut::app::ImGui_NVRHI::init'::`1'::dtor$5  >薼   shaderFactory  EN  @          EN  P          >蘬    vertexAttribLayout  EN  @          >�!    renderState  EN  p          >K     layoutDesc  EN             >p    blendState  EN  �         
 >_    desc  EN  �                                 �  O  �   �  R F                                �`donut::app::ImGui_NVRHI::init'::`1'::dtor$7  >薼   shaderFactory  EN  @          EN  P          >蘬    vertexAttribLayout  EN  @          >�!    renderState  EN  p          >K     layoutDesc  EN             >p    blendState  EN  �         
 >_    desc  EN  �                                 �  O  �   �  S F            -      '             �`donut::app::ImGui_NVRHI::init'::`1'::dtor$10  >薼   shaderFactory  EN  @        '  EN  P        '  >蘬    vertexAttribLayout  EN  @        '  >�!    renderState  EN  p        '  >K     layoutDesc  EN           '  >p    blendState  EN  �        ' 
 >_    desc  EN  �        '                        �  O ,   #   0   #  
 h   #   l   #  
 x   #   |   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �  #   �  #  
 �  #   �  #  
    #     #  
 u  #   y  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
 +  #   /  #  
 ;  #   ?  #  
   #   �  #  
 �  #   �  #  
 ,  #   0  #  
 <  #   @  #  
 L  #   P  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 %  #   )  #  
 5  #   9  #  
 M  #   Q  #  
 a  #   e  #  
   #     #  
 *  #   .  #  
 :  #   >  #  
 J  #   N  #  
 l  #   p  #  
   #   "  #  
 .  #   2  #  
 >  #   B  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 	  #   	  #  
 +	  #   /	  #  
 �  #   �  #  
 j  #   n  #  
 9
  #   =
  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 /  #   3  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
   #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 9  #   =  #  
 z  #   ~  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 0  #   4  #  
 @  #   D  #  
 P  #   T  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #     #  
   #     #  
   #   #  #  
 /  #   3  #  
 ?  #   C  #  
 O  #   S  #  
 _  #   c  #  
 o  #   s  #  
   #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #     #  
   #     #  
   #   #  #  
 /  #   3  #  
 ?  #   C  #  
 O  #   S  #  
 _  #   c  #  
 o  #   s  #  
   #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #     #  
   #     #  
 p  ?   t  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 
  ?     ?  
 7  ?   ;  ?  
 `  ?   d  ?  
 �  ?   �  ?  
 �  ?   �  ?  
     D      D  
 X   D   \   D  
 l   D   p   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 !  D   !  D  
 <!  D   @!  D  
 �!  E   �!  E  
 �!  E   �!  E  
 �!  E    "  E  
 -"  E   1"  E  
 W"  E   ["  E  
 �"  E   �"  E  
 �"  E   �"  E  
 �"  E   �"  E  
  #  B   $#  B  
 y#  B   }#  B  
 �#  B   �#  B  
 �#  B   �#  B  
 �#  B   �#  B  
 $  B   $  B  
 :$  B   >$  B  
 ]$  B   a$  B  
 H媻@  �       ,   H崐@  �          H崐@  H兞8�          @UH冹 H嬯L�
    A�   �8   H崓@  �    H兡 ]�      #   �    H塡$UVWAVAWH峫$袶侅�   H�    H3腍塃M嬹I孁H嬟L孂秛H�
H吷tH��P H98僝  3�H墋�W�E遞o
    fM顯坿逪墋�墋@坿荅   f墋墋A嬈H塃螲�    H�    @匂HE蠭抢����f�     I�繠�< u鯤峂哞    艵  @镀4圗@坲艵 艵
 ��   塃艵I�H�L岴螲峌�悁   H嬜H峂荋;萾H�H�8H�H�H吷tH��P怘婱縃吷tH墋縃��P怘婾鱄�; u7H凓v-H�翲婱逪嬃H侜   rH兟'H婭鳫+罤兝鳫凐wa�    2离5H凓v-H�翲婱逪嬃H侜   rH兟'H婭鳫+罤兝鳫凐w*�    �H婱H3惕    H嫓$�   H伳�   A_A^_^]描    �   �   ^   �   �   �   �   �   �      o  �    �  �    �  J   �  �       �   	  O G            �  &   �  J         �donut::app::ImGui_NVRHI::reallocateBuffer 
 >{k   this  AJ        2  AW  2     ��  ><S   buffer  AI  /     ��  AK        /  >#    requiredSize  AM  ,     #  AP        ,  AM �    "  >#    reallocateSize  AQ        )  AV  )     ��  >=   indexBuffer  A   6     ��  EO  (           D�   
 >�   desc  CK  (   :      CK (   s    \ 
 *  D0    M        r   �" M        �   �"GB
 >R    temp  AJ  &      AJ 6    z  & N &  B  �     �  D     N N M        q   #�� M        r   � M        �   �
 >R    temp  AJ        AJ "      N N M        �   � >R    tmp  AK         AK "        N M        �   ��C	 M        �   �
 N N N M        �   ��
 >�   _Ptr  AK  �     *  M        �   ��
 Z   �   M          ��
 N N N M        =  V M        �  Z

 N M        �  V M        $  V M        Y  V N N N N M        7  3亀X M        �  亀-R M        	  亀 N M        g  -亇R M        �  *亐O M        �  亣)*
 Z   !  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    *  AK �      M        �  亹d
4
 Z   �   >e    _Ptr_container  AH  �      AJ  �      N N N N N N M        7  3丂 M        �  丂- M        	  丂 N M        g  -丗 M        �  *両 M        �  丳)
 Z   !  
 >   _Ptr  AH  P      AJ  M      AH n      >#    _Bytes  AK  I    *  AK �      M        �  乊d
 >e    _Ptr_container  AH  d      AJ  a      N N N N N N �           (         A � h"   �  �  �  7  =  >  x  �  �  �  	    g  h  i  �  �  �  �  �  $  Y  K   L   o   p   q   r   }   �   �   �   �   �   
 :�   O        $LN122  �   {k  Othis  �   <S  Obuffer  �   #   OrequiredSize  �   #   OreallocateSize  �   =  OindexBuffer  0   �  Odesc  9A       �   9�       &   9      �   92      �   O  �   �           �  �     �       �  �6   �  �M   �  �O   �  �V   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �6  �  �@  �  �w  �  ��  �  ��  �  ��  �  ��   �   ^ F                                �`donut::app::ImGui_NVRHI::reallocateBuffer'::`1'::dtor$0 
 >�    desc  EN  0                                  �  O   ,   '   0   '  
 t   '   x   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
   '     '  
 -  '   1  '  
 =  '   A  '  
 g  '   k  '  
   '   �  '  
 �  '   �  '  
 �  '   �  '  
   '   !  '  
 -  '   1  '  
 E  '   I  '  
 �  '   �  '  
 �  '   �  '  
 
  '     '  
   '     '  
 �  '   �  '  
 1  '   5  '  
 A  '   E  '  
 Q  '   U  '  
 r  '   v  '  
 �  '   �  '  
 �  '   �  '  
 �  '   �  '  
 �  '   �  '  
 �  '   �  '  
 �  '   �  '  
 �  '      '  
   '     '  
 Q  '   U  '  
 a  '   e  '  
 ?  r   C  r  
 �  '   �  '  
 �  '   �  '  
 �  '   	  '  
 	  '   	  '  
 (	  '   ,	  '  
 
  @   
  @  
 k
  @   o
  @  
 H崐0   �          H塡$UVWATAUAVAWH崿$利��H侅@  H�    H3腍墔0  L嬺L嬦�    H嬸H塂$0�    H孁I婰$H��R I婰$H�L媯P  H�    A�蠭婽$I嬏�    劺uI婰$H��P(2篱�  H峎HH嬑�    �
    (馏^G�D$P�^O�L$TE3鞮塵�3褹竴  H峂拌    �   嬎H岴窵塰鳯�(D塰茾  �?H岪H冮u銵壄0  3褹�   H崓8  �    H嬎H崊@  @ f�     L塰鳯�(H岪H冮u風壄8  D壄@  L壄D  L壄L  D埈T  W�3�匵  卙  H墔x  L壄�  3褹竫  H崓�  �    H崊�  L�(H岪H冸u驦壄  L壄  L壄   L塽↖媱$P  H吚ugI�$H�M岲$PM嬑H峊$(��0  I嬚H峀$`H;萾H�L�(I媽$P  I墧$P  H吷tH��P怘婰$(H吷tL塴$(H��P怚媱$P  H塃狊WH�G�_L�OH媴0  H�@D塴桶�Y畜T痛D塴透�Y袤\图D塴屠荄湍  �?H��0  H嫊8  H凓v-�   H;蕇[H崊P   L塰鳯�(H�罤岪H;�8  r殡8s6H嬄H拎H崓@  H雀   H+�@ �     L塱鳯�)H岻H冭u颒菂8     I婦$8H塂$8D塴$@L塴$HH媴  H�@D$8勍�  �L$H�屚�  H��  I婦$@H墔  茀  	D壄  A嬐塋$ E孆A嬚塗$(D9n巁  L塴$X�     H婩J�<(3�97�  E3鯠媗$ fD  H媉I轍婥(H吚t
H嬘H嬒�虚�   H婼I嬏�    H塂$hH荅�   D$h匵  L$x峢  E�厁  �K�S�C�,墔8  �,翂�<  �,聣匑  �,缐匘  荄$<   荄$H    婥 塂$8D墊$@D塴$DI婰$H�H峌�惏   I婰$H�A�   H峊$P�惃   I婰$H�H峊$8�惱   D{ �艻兤8;7���L媗$X婰$ 婽$(O 塋$ �聣T$(I兣L塴$XH媡$0;V尞��I婰$H��怷  I婰$H��P(I�$I婦$H塂$0H�E3蒃岮H峊$0�惏  �H媿0  H3惕    H嫓$�  H伳@  A_A^A]A\_^]�"   �   7   �    D   �    h   �   x   *   �   �    �   �   �   N     N   �  N   �  )   +  J      �   	  E G            J  0      Q         �donut::app::ImGui_NVRHI::render 
 >{k   this  AJ        6  AT  6      >`#   framebuffer  AK        3  AV  3     ] AV �    �  >G#    vbufBinding  B8       4� �  >鏸    drawData  AL  >     
\? B0   C     �  >=A    invDisplaySize  DP    >t     vtxOffset  A   k    |?  Am  �     B    o    � >舓    io  AM  K     E AM �    � J >t     idxOffset  Ao  r    � Ao         >x#   drawState  CH      �    p  `  CK  �  �    v  D�   
 >t     n  A   u    {;  A  �    !  
  B(   y    � >単    cmdList  AM  �    J AM �    � J
 >t     i  A   �    ?
 >噂    pCmd  AI  �     AI �    �$ 
 >8$    drawArguments  B8   �    � M        �   俁 N2 M        �   倧
7'7

 >#     i  AJ  �    (  AJ     *  N M        �  � ' N M           亝( N M        �   乬 N M           丱 N M        �   � M           �0 N N M        �   �� M           �� N N  M        M   伷H
I M        n   � M        �   �HB
 >^#    temp  AJ  !      AJ 2    +  B(   +    N B�  �    ] N N M        l   .來 M        n   � M        �   �
 >^#    temp  AJ        AJ       N N M        �   �  >^#    tmp  AK  �    +  AK     z    N M        �   來C
 M        �   侜 N N N N M        �   儮 N M        �   儼 N M        4   �	 >�&   commandList  B0   	    A  N M        �   冝 M        �  冝 N N Z   �!  �!  P   �!  N    @          8         A � h0   �  �  �  �  �  �  �                 4   M   R   S   T   U   V   W   Y   k   l   n   p   y   z   }      �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   !  
 :0  O  �  {k  Othis  �  `#  Oframebuffer  8   G#  OvbufBinding  P   =A  OinvDisplaySize  �   x#  OdrawState  8   8$  OdrawArguments  9S       �&   9l       �&   9�       �&   9�      ?&   9      �   9.      �   9�      o   9m      �&   9�      �&   9�      �&   9�      �&   9�      �&   9      n&   O �   �          J  �  3   �      8 �6   9 �C   : �K   < �V   = �o   ? ��   A ��   B ��   F ��   H ��   K ��   F ��   K ��  M ��  P �>  R ��  T �  W �  X �  Y �   Z �N  \ �Z  ] �a  ^ �h  a �o  b �r  c ��  e ��  f ��  e ��  h ��  j ��  l ��  m ��  n �	  q �P  w �W  x �\  y �a  { �s  | ��  } ��  � ��  f ��  � ��  c ��  � ��  � ��  � �  � �   � �,   %   0   %  
 j   %   n   %  
 z   %   ~   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
   %     %  
 %  %   )  %  
 n  %   r  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
   %   #  %  
 7  %   ;  %  
 [  %   _  %  
 o  %   s  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
   %     %  
 0  %   4  %  
 �  %   �  %  
 �  %   �  %  
   %     %  
 *  %   .  %  
 :  %   >  %  
 J  %   N  %  
 �  %   �  %  
 �  %   �  %  
   %     %  
   %     %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 �  %   �  %  
 	  %   	  %  
 	  %   	  %  
 +	  %   /	  %  
 ;	  %   ?	  %  
 K	  %   O	  %  
 [	  %   _	  %  
 k	  %   o	  %  
 {	  %   	  %  
 �	  %   �	  %  
 H塡$H塼$H墊$ UH峫$〩侅�   H�    H3腍塃GH嬞�    H孁H儃( tH婬0H儁 t��  3鯤塼$ L峂獿岴峌荋婬0�    H9u莡2篱�  荅�   荅�   荅�   H荅�   f荅�W�E鱄塽H荅   @坲髑E   @坲塽f塽#)E'@坲7塽;@坲?婨E讒E珘E酃    �    H荅   H荅        �
   f塇艪 H塃鱄�H�L岴譎峌�P(H嬛H峂螲;萾H�H�0H婯(H塖(H吷tH��P怘婱稨吷tH塽稨��P怘儃( u2坶�   H婯H��P H婯H�    )E稟�   L岴稨婼(�悁  H婯H�L婸h婨�    Lc繦塼$0L塂$(H婨荋塂$ E3蒃3繦婼(A�襀婯H�A�    H婼(�惃  H婯H��惛  H婯H��P(H�H婥H塃稨�E3蒃岮H峌�惏  H婯(H婫0H塇�H婾H凓v-H�翲婱鱄嬃H侜   rH兟'H婭鳫+罤兝鳫凐w-�    睹H婱GH3惕    L崪$�   I媅I媠 I媨(I嬨]描    �   �   .   �    f   �    �   �    �   r     r   �  n   d  �    s  J   �  �       �   �  P G            �  *   �  6         �donut::app::ImGui_NVRHI::updateFontTexture 
 >{k   this  AI  -     <  AJ        -  AI k      >     pixels  D`    >薻    io  AM  5     aV  >t     width  D@    >o   textureDesc  CK  8   5    	  CK 8   h    (  Dp    >t     height  DD    M        x   丩 M        �   丩GB
 >f    temp  AJ  P      AJ `     � � &  BP   Y    =1 �  B�  &    p N N M        w   %�& M        x   丂 M        �   丂
 >f    temp  AJ  <      AJ L      N N M        �   �8 >f    tmp  AK  )    "  AK L    �   , �  N M        �   �&C	 M        �   �2 N N N M        �   7�� M        �   7�� M        �  7��  M        !  ��
((
 M        �   �� M           �� N N M          
�� >p    _Fancy_ptr  AH  �     3  M        �  
�� M        +  
�� M        -  
�� M        �  
��
 Z      N N N N N N N N N M           �� N M        =  �� M        �  ��$ N M        �  �� M        $  �� M        Y  �� N N N N M        4   � >�&   commandList  BP       �  N M        7  7�1_ M        �  �1-
U M        	  �1 N M        g  -�;U M        �  *�>R M        �  侲)-
 Z   !  
 >   _Ptr  AH  E      AJ  B      AH c      >#    _Bytes  AK  >    W * (  M        �  侼d
7
 Z   �   >e    _Ptr_container  AH  Y      AJ  V      N N N N N N Z   �!  
"   �                    A � h8   �  �  �  �  �  7  =  >  x  �  �  �  �  	        g  h  i  j  �  �  �  �  �  �    !  $  T  Y      -  �  �  +  X     4   7   8   v   w   x   y   z   }   �   �   �   �   �   �   !  
 :�   O        $LN184     {k  Othis  `      Opixels  @   t   Owidth  p   o  OtextureDesc  D   t   Oheight  9#      �%   9H      �   9\      �   9u      �&   9�      �&   9�      �&   9�      �&   9�      �&   9      �&   9      n&   O   �   �           �  �     �       P  �-   Q  �5   U  �G   V  �N   [  �j   \  �p   ]  ��   b  ��   _  ��   `  ��   a  ��   c  �  e  �`  g  �n  j  �x  l  ��  n  ��  p  ��  q  ��  s  �  t  �#  v  �/  x  �k  y  ��  x  ��     _ F                                �`donut::app::ImGui_NVRHI::updateFontTexture'::`1'::dtor$0  >     pixels  EN  `           >t     width  EN  @           >o    textureDesc  EN  p           >t     height  EN  D                                  �  O ,   $   0   $  
 u   $   y   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
   $     $  
 )  $   -  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 F  $   J  $  
 V  $   Z  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 +  $   /  $  
 �  $     $  
 
  $     $  
   $   !  $  
 >  $   B  $  
 �  $   �  $  
 �  $   �  $  
 �  n   �  n  
 i  $   m  $  
 y  $   }  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 	  $   	  $  
 
  A    
  A  
 z
  A   ~
  A  
 �
  A   �
  A  
 �
  A   �
  A  
 �
  A   �
  A  
 H崐p   �          H塗$SVWAUH冹XH孂�    H峸8艱$  H嬛H壌$�   H嬒L嬭LcPE崅�  Mc萇�扞拎O�塈玲�    劺剏  IcMH峗@L嬃H墱$�   M榔D$ H嬘崄�  H嬒Lc萂设    劺�?  H�H塴$PL塪$HL崸�  L塼$@H�L墊$8�P M婦$H緂fffffffM�$H嬋H竿烫烫烫�3鞨�!I嬋H嬈I+薒嬍H鏖I灵L嬕I龙I嬄H凌?L蠱;蕇K�塈�僆塋$雂vbI婰$H嬈I+薍鏖H龙H嬄H凌?H蠰;蕍L崉$�   I嬔I嬏�    �,M+蕋"@ f�     I塰I�(A塰I兝I冮u隡塂$H�H伹�  H��P H媤H�H嬑H+蔋�H央H样H;賡H�Z�=v?H婫H+翲养H;豽L崉$�   H嬘H嬒�    �H+�3襀跦嬑L嬅�    H�3H塆M�4$L�?A9m~Z3�D  I婨I嬑H�HcC H婼(L��I拎�    LcCI嬒H婼M黎    HcC H峷�臜��HcCM�4嶮�<GA;m|璍嫶$�   H嫶$�   I�H�H媂xH��P M�$3鞨�I嬑H塴$ L��覫�H嫶$�   H媂xH�H��P L�I嬑H�H塴$ L��覮媩$8�L媡$@L媎$HH媗$PH兡XA]_^[�2繦兡XA]_^[�   �    O   '   �   '   H  0   �  /   �  N     L   /  L      �   �  M G            �     �  P         �donut::app::ImGui_NVRHI::updateGeometry 
 >{k   this  AJ          AM       �sP >�&   commandList  AK          AV  [    e  D�    >鑗    vtxDst  AV  �    k  >!    idxDst  AW  �    �  >鏸    drawData  AU  0     �� 
 >t     n  A   =      A       y =   >単    cmdList  AI      H  AI      m  H  M        e   侌 N M        g   佽 N M        f   
亰K >e   _Newsize  AI  �    :  AI �    � # H / M        !  
亰#%
b"*%
 Z   f!   >鹡    _Al  AM  �    M >e    _Oldsize  AJ  �    H  	 6   AJ �    �  L  >#0    _Newlast  AH  �      AH �    ~  O  >#0    _Oldlast  AL  �    m  AL S      >e    _Oldcapacity  AH  �    -    M        g!  佈 M        �!  佋 N N N N# M        h   �� 	boE M        !  �� 	%%b"%
 Z   i!   >妌    _Al  AT  �      >e    _Oldsize  AR  �       >搃    _Newlast  AJ        AJ z      M        j!  丵/ >#    _Count  AQ  �     �  `  AQ z      >爎   _Backout  CP     o      CP    `    *    M        �!  乣 M        �!  乣 M           乣 N M           乨 N N N N N N Z   �!  J   J    X                      @ � h"   �     e   f   g   h   o   p   �   !  !   !  !!  "!  #!  e!  g!  h!  j!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!   �   {k  Othis  �   �&  OcommandList  9�       �   9�      �   9p      �   9�      �&   9�      �   9�      �&   O�   �           �  �     �        �    �    �[    ��    �z    ��  # ��  $ ��  & �   ( �  * �   + �3  - �A  . �S  1 ��  2 ��  4 ��  5 ��   ��  5 �,   *   0   *  
 r   *   v   *  
 �   *   �   *  
 �   *   �   *  
 �   *   �   *  
 �   *   �   *  
   *   
  *  
 )  *   -  *  
 I  *   M  *  
 Y  *   ]  *  
   *   �  *  
 �  *   �  *  
   *     *  
   *     *  
 r  *   v  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
   *     *  
   *     *  
 B  *   F  *  
   *   #  *  
 B  *   F  *  
 e  *   i  *  
 u  *   y  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
   *     *  
 x  *   |  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
 H婹H�    H呉HE旅   F      �   �   : G                      �        �std::exception::what 
 >1   this  AJ                                 @     1  Othis  O�   0              �	     $       ^  �    _  �   `  �,      0     
 _      c     
 �      �     
  d T 4 2p    H           O      O      �    20    2           P      P      �   
 
4 
2p    B           Q      Q      �    20    <           R      R      �   
 
4 
2p    B           S      S      �    20    <           T      T      �   
 
4 
2p    B           U      U      �    �                  W      W      �    B                 Y      Y      �    T
 4	 2�p`    [           [      [      �   ! �     [          [      [      �   [   8          [      [      �   !       [          [      [      �   8  T          [      [      �   !   �     [          [      [      �   T  `          [      [      �    20    `           ]      ]      �    20    ^           _      _      �    20    `           a      a      �    B             G      �       "           b      b      �   h           �      �          �    2
 d T
 4 2��p    �          d      d      �    B      B           f      f      �    B                 h      h          B      >           j      j          B                 l      l         0 4�����
�p`P          �     H              �
          m      m         (                  #       >    b    ->    �    .       ,   	   �             E      B      �    @�6�D�D�DU
P0
�D
20
*0
0<
9Z
2F
0H
�p
�@V ��  2P    -           B      B      &   "
4
h
z
*	 t# d" 4!  P          �      H      5       �          o      o      /   (           8      ;   
    �6             �    
Z ��@E 0 4� � ���
�p`P          2     H       D       J          p      p      >   (           G      J          �    l iD  B             G      S       *           q      q      M   (           V      Y          �    B&	 4  �
�p`P          �      H      b       �          s      s      \   (           e      h   
    `6             �    � ��@  R0           G      q       �           t      t      k   (           t      w          �    N d< 4	 #d##4"#�pP          �      H      �       �          u      u      z   (           �      �   
    `6    f       -      �    
   �    6 �`*20  �
�p`0      �           v      v      �   ! � � 
�	 T
     �          v      v      �   �   �          v      v      �   !       �          v      v      �   �  �          v      v      �    20               w      w      �   ! t               w      w      �      E           w      w      �   !                 w      w      �   E   K           w      w      �   - B             G      �       "           x      x      �   h           �      �          �    2
 4 R���
�p`P           G      �       +          z      z      �   (           �      �   
    @:    @h   1      2   ���  2�
�p`0           G      �       U          }      }      �   8               �      �   	   �            �   �       ;   5 r� 
 
2P    (           ;      ;      �     4 2���p`           G      �       �          �      �      �   8               �      �   	   �            �   �       <   ) � 
 
2P    (           <      <           20           G             J           �      �      
   h                           �    R       >           �      �         ! t      >          �      �         >   b           �      �         !       >          �      �         b   �           �      �      %    20               �      �      +    B      :           �      �      1   
 
4 
2p    0           �      �      7   
 
4 
2p    0           �      �      =                               �               Unknown exception                             �                                           �               bad array new length                                      U                                 [      a      g                   .?AVbad_array_new_length@std@@     h               ����                      X                         .?AVbad_alloc@std@@     h              ����                      ^                         .?AVexception@std@@     h               ����                      d          string too long     ����    ����        ��������ImGui font texture main donut/imgui_vertex donut/imgui_pixel Failed to create an ImGUI shader COLOR ImGui index buffer ImGui vertex buffer ImGUI vector too long unordered_map/set too long invalid hash bucket count                                       d      �      �                         �                   �               ����    @                   d      �                                         ^      �      �                         �                           �      �              ����    @                   ^      �                                         X      �      �                         �                                   �      �      �              ����    @                   X      �     �?   _                 �?  �?  �?  �?   �   (   & 
僎        std::exception::`vftable'    C      C  
    �   (   & 
僎        std::bad_alloc::`vftable'    I      I  
    �   3   1 
僎        std::bad_array_new_length::`vftable'     L      L  
 �+鷯8}`Wsp7_抖�り瑩シ)1箱�,�	h�K蜌�(E
|甲嘇"R��u菁fJ旰A"R�锻	雾"�
A勽洶N翊呭=2�遂哯峲>跲虝駩旀9渔9mVl��
d戮翁r*O�8骮�\�-7磏
d戮翁r蘴�&y�,j�1祁妽訧)#hv瓯訧)#hv瓯�嚻址ZS痔毝J屢b綩藋T怛�;2F;�(！
Z昄帽鈆�9n3>飖9屓O+3Yq滊=肆峖=f瓵�s(礼5� T(倴
~蹃礼0未s�(�-
暦�&;冚�3>飖9屓�>�"歛-閈原曫擈虋J`埪嚤踖p禭84玴�嚤踖p禭;l笽蚧�6萪O�f�<儅瀠(K霵婬(鋲>45�3�'項j�g崋H�'項j�	_f2膅卍裞儌頂"�彗q傤盬~.`�獉'痻鋚5(�$愜w獛啯CC跪e�s猕up泸锞軔讛!D�:Q蚴0k汬酋�':K�:b8�4n蟍}媖&窛脥犷A棊膬搃捊z炁嶀預棊膬N縅%诼徇�5>湶螨篟
熉徇�5>&创笠\徇�5>�9:� �蹰k¨d蛻\*u�
池B鍎q~T4�棂%I栶賑?TN鞮從櫖雈]{謑p咴.縛攑鬴]{謑p'�o鍄$f]{謑p巾賉垽\丽Υ偾G覎y
軥躏�!7�嘦	sd��適0XEP溌泘uN垞�荷禟娎��4重祜瓂攅&�昸鳐3铚#?�u橨8$d3[�1飌�趄�(>rR����唴╢赞�;>稼胷 �:L炾餈I�缔雇��/ 黼�
?�昸鳐3柢G蹌咒��w齗�2Iga�鍎H[鯧瞴攔+倧A糲�琸;�:�!)Dc�
_:nN鵘J鈬醩iy茨�F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这朸橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 夒嘕-WV8o;き8乿る嘕-WV8o��腫62V雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G�cT 乜o楼�6G-坓�(鬄酲;[純o�-坓�(鬄�汬'这�-坓�(鬄鮐搜n2竌V-坓�(鬄�汬'这�4"簑�-)扂"�2dd�a�:}�"�`蔪＊栶�&樢閣yQ朏菜{.枍�鏆慆脚L搓B袧=dd�a�:门)哂～�'XI|2鯬�枆鞅騪繝�dd�a�:_棢杻#Q蕚匯�8k耮鄔一;想=�dd�a�:_棢杻#Qr轟\	窧譡祧� 齽ョe鍦i钅dd�a�:嘽縅�目獮儣�В焦貖髺4{	1dd�a�:_棢杻#Q��43羱剫�燀酣捖崺壖Kdd�a�:旈o�+K�>Ru鴨iw给tZo;A2�;尼鵉da�擨.鴇[�y苈��$蟹轶hf&$9p�ぜ雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛了5YJq覜垒�咞taR�,F_棢杻#Q`�G圿箅我F詍CD� K^�dd�a�:r�7)}玖焋tjxⅲv暕妝�#(捌廇簤"by*�杜`颀l+�鞯.r擣�0G#盱谑劈儷�2�(��苳乮5絚_}4n4�硓榸�:喍'溃�3Sy*�杜`颀l+�鞯.r擣�0G#盱谑綪R�_佑�(��苳乮5絚_}4n4�硓樕S�8萀D脸v傘]-屾咞taR�,F_棢杻#Q吀qv蕞	ォ�<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦请嘕-WV8o�%-<$�-坓�(鬄�/ｎ	蜍R9E\$L釉��E光9E\$L釉��E光潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H淭鮂Z鷠$樐蜆{絼槊餣�	:n茭� �'o �4�*H徙Cdvh!T厗ó羔�攪x螤 箁⒕澘%G>禡h樬鍏穧 5]叨蝝�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �^笵A傮笖E@wX+]�5]_и龌斱/x�5曎$�N鷃0�$跆隫索�!;氛        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       剬              .debug$T       l                 .rdata         0       ��                     .text$mn       :      眡�     .debug$S                    .text$mn       0      燥"V     .debug$S       �             .text$mn    	   0      燥"V     .debug$S    
   �         	    .text$mn       c       謠锖     .debug$S       |             .text$mn    
   p       '      .debug$S       �         
    .text$mn       U     鼣陿     .debug$S       �
  X           .text$x        (      纥吨    .text$mn       �     騍3b     .debug$S       �
  R           .text$x        (      纥吨    .text$mn       +     L啎     .debug$S       t  Z           .text$x              S�    .text$x              S�    .text$mn             憟⑸     .debug$S       @  
           .text$mn       �       `螏�     .debug$S       �             .text$mn               _葓�     .debug$S       8             .text$mn              �邆     .debug$S                     .text$mn    !          恶Lc     .debug$S    "   �          !    .text$mn    #   
       �9�     .debug$S    $   �          #    .text$mn    %          袁z\     .debug$S    &   �          %    .text$mn    '          �邆     .debug$S    (   �          '    .text$mn    )          痖I     .debug$S    *   �          )    .text$mn    +   <      .ズ     .debug$S    ,   0  
       +    .text$mn    -   <      .ズ     .debug$S    .   L  
       -    .text$mn    /   !      :著�     .debug$S    0   <         /    .text$mn    1   2      X于     .debug$S    2   <         1    .text$mn    3   "       坼	     .debug$S    4   �         3    .text$mn    5   "       坼	     .debug$S    6   �         5    .text$mn    7         �!     .debug$S    8   L         7    .text$mn    9   J      O�0�     .debug$S    :   h         9    .text$mn    ;   K       }'     .debug$S    <   �         ;    .text$mn    =   `      板@�     .debug$S    >   �         =    .text$mn    ?   `      ,     .debug$S    @   �         ?    .text$mn    A   ^      wP�     .debug$S    B   �         A    .text$mn    C         ��#     .debug$S    D   �          C    .text$mn    E         ��#     .debug$S    F   �          E    .text$mn    G   B      贘S     .debug$S    H             G    .text$mn    I   B      贘S     .debug$S    J            I    .text$mn    K   B      贘S     .debug$S    L   �          K    .text$mn    M   H       襶.      .debug$S    N   �         M    .text$mn    O   �     2|�     .debug$S    P   �  �       O    .text$mn    Q          aJ鄔     .debug$S    R   �          Q    .text$mn    S         �ッ     .debug$S    T   �          S    .text$mn    U         �ッ     .debug$S    V   �          U    .text$mn    W         �ッ     .debug$S    X   �          W    .text$mn    Y   `     匮�5     .debug$S    Z   �  B       Y    .text$mn    [   *       Ｄ�     .debug$S    \   �         [    .text$mn    ]   >      Ax9     .debug$S    ^   �         ]    .text$mn    _   B      -堟�     .debug$S    `   �         _    .text$mn    a   �     隱vM     .debug$S    b   
  L       a    .text$x     c         "E萷a    .text$mn    d   �       獊鉩     .debug$S    e   T  &       d    .text$mn    f   �
     赌     .debug$S    g   �$  .      f    .text$x     h         *Lt    .text$x     i         輋    .text$x     j         ﹟苖f    .text$x     k   -      i*�f    .text$mn    l   �  	   鮷鶙     .debug$S    m   �
  X       l    .text$x     n         "E萷l    .text$mn    o   J     憰1     .debug$S    p   D  h       o    .text$mn    q   �  
   勢1-     .debug$S    r     P       q    .text$x     s         %FZ畄    .text$mn    t   �     1頥L     .debug$S    u   �  J       t    .text$mn    v         崪覩     .debug$S    w   �          v        )       M        E                _                x                �                �                �                �                �                               C               y               �               �               �      1        �      E               v               K        ?          i                   ^      +              G        �          i                   �      /        �      C              -        3      I        ]          i
                   �      Q        �      S        �      Y        !      )        <      #        S      ?        q      A        �      =        �      %        �      !        �              7      3        o      '        �               v      O        �      _               W        L      ]        y      U        �      f        	      q        J	      o        �	      [        �	      l        
      d        t
      a        �
      t                       +      ;        f      5        �              �
              �
              {      7              9        �                            k              �              �      
        7              e      	        �                             �                                  c        q      h        �      n        Z      s        �      k                            i        �      j        �                              #           __chkstk             8           ceilf            memcpy           memmove          memset           $LN13       M    $LN5        1    $LN10       K    $LN7        +    $LN13       G    $LN10       -    $LN16       I    $LN3        Q    $LN4        Q    $LN3       S    $LN4        S    $LN93   `  Y    $LN100      Y    $LN37   `   ?    $LN40       ?    $LN37   ^   A    $LN40       A    $LN37   `   =    $LN40       =    $LN10       3    $LN263  �  O    $LN267      O    $LN18   B   _    $LN21       _    $LN3       W    $LN4        W    $LN18   >   ]    $LN21       ]    $LN3       U    $LN4        U    $LN700      f    $LN184  �  q    $LN188      q    $LN171      o    $LN12       [    $LN122  �  l    $LN125      l    $LN29       d    $LN172      a    $LN90       t    $LN18       ;    $LN10       5    $LN206  +      $LN215          $LN97   U          P  
       $LN101          $LN107  �          �  
       $LN111          $LN43       9    $LN20           $LN4            $LN14   :       $LN17           $LN4            $LN4        	    .xdata      x          F┑@M        ^      x    .pdata      y         X賦鶰        �      y    .xdata      z          （亵1        �      z    .pdata      {          T枨1        �      {    .xdata      |          %蚘%K        �      |    .pdata      }         惻竗K              }    .xdata      ~          （亵+        C      ~    .pdata               2Fb�+        l          .xdata      �          %蚘%G        �      �    .pdata      �         惻竗G        �      �    .xdata      �          （亵-        �      �    .pdata      �         2Fb�-              �    .xdata      �          %蚘%I        H      �    .pdata      �         惻竗I        z      �    .xdata      �          懐j濹        �      �    .pdata      �         Vbv鵔        �      �    .xdata      �          �9�S        
       �    .pdata      �         �1癝        +       �    .xdata      �          蔜-錣        K       �    .pdata      �         愶LY        �       �    .xdata      �         �qL僘        !      �    .pdata      �         ~蕉結        n!      �    .xdata      �         |盰        �!      �    .pdata      �         瞚挨Y        2"      �    .xdata      �         S!熐Y        �"      �    .pdata      �         �o圷        �"      �    .xdata      �          （亵?        X#      �    .pdata      �         粻胄?        ~#      �    .xdata      �          （亵A        �#      �    .pdata      �         翎珸A        �#      �    .xdata      �          （亵=        �#      �    .pdata      �         粻胄=        #$      �    .xdata      �         /
�3        G$      �    .pdata      �         +eS�3        �$      �    .xdata      �   	      �#荤3        �$      �    .xdata      �         j3        %      �    .xdata      �          3狷 3        P%      �    .xdata      �          um∞O        �%      �    .pdata      �         ,nG汷        �&      �    .xdata      �          �9�_        +(      �    .pdata      �         惻竗_        x(      �    .xdata      �          �9�W        �(      �    .pdata      �         �1癢        )      �    .xdata      �          �9�]        k)      �    .pdata      �         OAG怾        �)      �    .xdata      �          �9�U        �)      �    .pdata      �         �1癠        *      �    .xdata      �   (      �-}f        K*      �    .pdata      �         聮蒻f        �*      �    .xdata      �   	      � )9f        .+      �    .xdata      �   #      礐@f        �+      �    .xdata      �   D       �7`f        ,      �    .xdata      �          k筬        �,      �    .pdata      �         噖sbf        -      �    .voltbl     �          'Exf    _volmd      �    .xdata      �   $      鰁糞q        �-      �    .pdata      �         _琾~q        �-      �    .xdata      �   	      � )9q        .      �    .xdata      �         _5屬q        E.      �    .xdata      �   
       6�q        �.      �    .xdata      �   (      K粴qo        �.      �    .pdata      �         傫e阰        
/      �    .xdata      �   	      � )9o        T/      �    .xdata      �         jo        �/      �    .xdata      �          黒o        �/      �    .xdata      �         K�襕        80      �    .pdata      �         瀪秇[        s0      �    .xdata      �   	      � )9[        �0      �    .xdata      �         j[        �0      �    .xdata      �          艠�/[        -1      �    .xdata      �   $      譛伿l        j1      �    .pdata      �         蘳婅l        �1      �    .xdata      �   	      � )9l        ;2      �    .xdata      �         �!<莑        �2      �    .xdata      �   	       汔
巐        3      �    .xdata      �         簄餹d        �3      �    .pdata      �          媞譫        �3      �    .xdata      �   	      � )9d        =4      �    .xdata      �         jd        �4      �    .xdata      �          銚睊d        5      �    .xdata      �   $      sㄧ餫        c5      �    .pdata      �         m梨蟖        �5      �    .xdata      �   	      � )9a        6      �    .xdata      �         庪蘰a        u6      �    .xdata      �          V忕a        �6      �    .xdata      �          尚>t        57      �    .pdata      �         D痚黷        �7      �    .xdata      �          t{矁t        �7      �    .pdata      �         -�t        %8      �    .xdata      �         ‥2Et        v8      �    .pdata      �         儥t        �8      �    .xdata      �          （亵;        9      �    .pdata      �         � �;        [9      �    .xdata      �         范^�;        �9      �    .pdata      �         鳶�;        �9      �    .xdata      �         @鴚`;        %:      �    .pdata      �         [7�;        i:      �    .voltbl     �          飾殪;    _volmd      �    .xdata      �         /
�5        �:      �    .pdata      �         +eS�5        �:      �    .xdata      �   	      �#荤5        &;      �    .xdata      �         j5        e;      �    .xdata      �          3狷 5        �;      �    .xdata      �          �"膧        �;      �    .pdata      �         漝魰        �=      �    .xdata      �   	      � )9        �?      �    .xdata      �         �T        碅      �    .xdata      �   
       � 裇        狢      �    .xdata      �         啄qJ        欵      �    .pdata      �         霰�        F      �    .xdata      �   
      B>z]        婩      �    .xdata      �          �2g�        G      �    .xdata      �         T�8        嘒      �    .xdata      �         r%�         H      �    .xdata      �          7�]        }H      �    .xdata      �          3賟P        鳫      �    .pdata      �         銀�*        両      �    .voltbl     �                  _volmd      �    .xdata      �         屐�:        	J      �    .pdata      �         oJy        欽      �    .xdata      �   
      B>z]        *K      �    .xdata      �          �2g�        終      �    .xdata      �         T�8        VL      �    .xdata      �         r%�        鏛      �    .xdata      �   	       '畫        |M      �    .xdata      �          3賟P        N      �    .pdata      �         銀�*        癗      �    .voltbl     �                  _volmd      �    .xdata      �         蚲7M9        PO      �    .pdata      �         %轢�9        鮋      �    .xdata      �   	      �#荤9        橮      �    .xdata      �         j9        @Q      �    .xdata      �          攰e9        鞶      �    .xdata      �          確        擱      �    .pdata      �         OAG�        馭      �    .xdata      �         +縬[        MU      �    .pdata               蹷謔        玍          .xdata              ＋)        	X         .pdata              穣        gY         .xdata               （亵        臵         .pdata              �#洢        鬦         .xdata               �9�        "[         .pdata              礝
        [         .xdata               %蚘%        踇         .pdata              }S蛥        \         .xdata      	         %蚘%	        F\      	   .pdata      
        }S蛥	        榎      
   .rdata                           閈        .rdata               �;�          ]         .rdata      
                     ']     
   .rdata                           >]        .rdata               �)         `]         .xdata$x                         宂         .xdata$x            虼�)         甝         .data$r       /      嶼�         裖         .xdata$x      $      4��         鯹         .data$r       $      鎊=         K^         .xdata$x      $      銸E�         e^         .data$r       $      騏糡                  .xdata$x      $      4��         綹             齘           .rdata               燺渾         _         .data                 烀�          6_             j_        .rdata               謥`$         慱         .rdata               旲^         筥         .rdata               涥�          衉         .rdata               
M�         鱛         .rdata        !       �+涞         `         .rdata               籁鎞         W`         .rdata                ,斚�         o`          .rdata      !         �8q(         榒      !   .rdata      "         藧�         耟      "   .rdata      #         IM         赻      #   .rdata      $         ��          a      $   .rdata      %         藾味         2a      %   .rdata$r    &  $      'e%�         ca      &   .rdata$r    '        �          {a      '   .rdata$r    (                     慳      (   .rdata$r    )  $      Gv�:               )   .rdata$r    *  $      'e%�         芶      *   .rdata$r    +        }%B         轪      +   .rdata$r    ,                     鬭      ,   .rdata$r    -  $      `         
b      -   .rdata$r    .  $      'e%�         )b      .   .rdata$r    /        �弾         Lb      /   .rdata$r    0                     mb      0   .rdata$r    1  $      H衡�         巄      1   .rdata      2         v靛�         竍      2   .rdata      3         eL喳         萣      3       豣           .rdata      4         � �         阞      4   .rdata      5         _�         c      5   _fltused         .debug$S    6  4             .debug$S    7  4          
   .debug$S    8  @             .chks64     9  �	                8c  ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?GetIO@ImGui@@YAAEAUImGuiIO@@XZ ?GetDrawData@ImGui@@YAPEAUImDrawData@@XZ ?ScaleClipRects@ImDrawData@@QEAAXAEBUImVec2@@@Z ?GetTexDataAsRGBA32@ImFontAtlas@@QEAAXPEAPEAEPEAH11@Z ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??0Viewport@nvrhi@@QEAA@XZ ??0Rect@nvrhi@@QEAA@XZ ??1TextureDesc@nvrhi@@QEAA@XZ ??1VertexAttributeDesc@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??0VertexBufferBinding@nvrhi@@QEAA@XZ ?CreateAutoShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0UStaticShader@23@11PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAAX_K@Z ?deallocate@?$allocator@UImDrawVert@@@std@@QEAAXQEAUImDrawVert@@_K@Z ?_Xlength@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@CAXXZ ?deallocate@?$allocator@G@std@@QEAAXQEAG_K@Z ?_Xlength@?$vector@GV?$allocator@G@std@@@std@@CAXXZ ?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z ?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ ?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z ?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ ?reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z ?getPSO@ImGui_NVRHI@app@donut@@AEAAPEAVIGraphicsPipeline@nvrhi@@PEAVIFramebuffer@5@@Z ?getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z ?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z ?error@log@donut@@YAXPEBDZZ ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??$_Zero_range@PEAG@std@@YAPEAGQEAG0@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Hash_representation@PEAVITexture@nvrhi@@@std@@YA_KAEBQEAVITexture@nvrhi@@@Z ??$_Copy_memmove@PEAGPEAG@std@@YAPEAGPEAG00@Z ??$_Copy_memmove@PEAUImDrawVert@@PEAU1@@std@@YAPEAUImDrawVert@@PEAU1@00@Z ??$_Fnv1a_append_value@PEAVITexture@nvrhi@@@std@@YA_K_KAEBQEAVITexture@nvrhi@@@Z ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0???$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z@4HA ?dtor$0@?0??getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z@4HA ?dtor$0@?0??init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$0@?0??reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z@4HA ?dtor$0@?0??updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ@4HA ?dtor$10@?0??init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$1@?0???$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z@4HA ?dtor$5@?0??init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$7@?0??init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1TextureDesc@nvrhi@@QEAA@XZ $pdata$??1TextureDesc@nvrhi@@QEAA@XZ $unwind$??1VertexAttributeDesc@nvrhi@@QEAA@XZ $pdata$??1VertexAttributeDesc@nvrhi@@QEAA@XZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAAX_K@Z $unwind$?deallocate@?$allocator@UImDrawVert@@@std@@QEAAXQEAUImDrawVert@@_K@Z $pdata$?deallocate@?$allocator@UImDrawVert@@@std@@QEAAXQEAUImDrawVert@@_K@Z $unwind$?_Xlength@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@G@std@@QEAAXQEAG_K@Z $pdata$?deallocate@?$allocator@G@std@@QEAAXQEAG_K@Z $unwind$?_Xlength@?$vector@GV?$allocator@G@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@GV?$allocator@G@std@@@std@@CAXXZ $unwind$?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $pdata$?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $cppxdata$?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $stateUnwindMap$?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $ip2state$?init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $unwind$?dtor$10@?0??init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA $pdata$?dtor$10@?0??init@ImGui_NVRHI@app@donut@@QEAA_NPEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA $unwind$?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ $pdata$?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ $cppxdata$?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ $stateUnwindMap$?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ $ip2state$?updateFontTexture@ImGui_NVRHI@app@donut@@QEAA_NXZ $unwind$?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z $pdata$?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z $cppxdata$?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z $stateUnwindMap$?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z $ip2state$?render@ImGui_NVRHI@app@donut@@QEAA_NPEAVIFramebuffer@nvrhi@@@Z $unwind$?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ $pdata$?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ $cppxdata$?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ $stateUnwindMap$?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ $ip2state$?backbufferResizing@ImGui_NVRHI@app@donut@@QEAAXXZ $unwind$?reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z $pdata$?reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z $cppxdata$?reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z $stateUnwindMap$?reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z $ip2state$?reallocateBuffer@ImGui_NVRHI@app@donut@@AEAA_NAEAV?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@_K1_N@Z $unwind$?getPSO@ImGui_NVRHI@app@donut@@AEAAPEAVIGraphicsPipeline@nvrhi@@PEAVIFramebuffer@5@@Z $pdata$?getPSO@ImGui_NVRHI@app@donut@@AEAAPEAVIGraphicsPipeline@nvrhi@@PEAVIFramebuffer@5@@Z $cppxdata$?getPSO@ImGui_NVRHI@app@donut@@AEAAPEAVIGraphicsPipeline@nvrhi@@PEAVIFramebuffer@5@@Z $stateUnwindMap$?getPSO@ImGui_NVRHI@app@donut@@AEAAPEAVIGraphicsPipeline@nvrhi@@PEAVIFramebuffer@5@@Z $ip2state$?getPSO@ImGui_NVRHI@app@donut@@AEAAPEAVIGraphicsPipeline@nvrhi@@PEAVIFramebuffer@5@@Z $unwind$?getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z $pdata$?getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z $cppxdata$?getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z $stateUnwindMap$?getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z $ip2state$?getBindingSet@ImGui_NVRHI@app@donut@@AEAAPEAVIBindingSet@nvrhi@@PEAVITexture@5@@Z $unwind$?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z $pdata$?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z $chain$3$?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z $pdata$3$?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z $chain$4$?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z $pdata$4$?updateGeometry@ImGui_NVRHI@app@donut@@AEAA_NPEAVICommandList@nvrhi@@@Z $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z $pdata$??$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z $cppxdata$??$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z $stateUnwindMap$??$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z $ip2state$??$_Try_emplace@AEBQEAVITexture@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@PEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@V?$_Uhash_compare@PEAVITexture@nvrhi@@U?$hash@PEAVITexture@nvrhi@@@std@@U?$equal_to@PEAVITexture@nvrhi@@@4@@std@@V?$allocator@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBQEAVITexture@nvrhi@@@Z $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@GV?$allocator@G@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UImDrawVert@@V?$allocator@UImDrawVert@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $cppxdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $ip2state$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEAVITexture@nvrhi@@V?$RefCountPtr@VIBindingSet@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??$_Zero_range@PEAG@std@@YAPEAGQEAG0@Z $pdata$??$_Zero_range@PEAG@std@@YAPEAGQEAG0@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Copy_memmove@PEAGPEAG@std@@YAPEAGPEAG00@Z $pdata$??$_Copy_memmove@PEAGPEAG@std@@YAPEAGPEAG00@Z $unwind$??$_Copy_memmove@PEAUImDrawVert@@PEAU1@@std@@YAPEAUImDrawVert@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUImDrawVert@@PEAU1@@std@@YAPEAUImDrawVert@@PEAU1@00@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0BD@BJHLHEG@ImGui?5font?5texture@ ??_C@_04GHJNJNPO@main@ ??_C@_0BD@FGPNMAL@donut?1imgui_vertex@ ??_C@_0BC@IBGHHNLP@donut?1imgui_pixel@ ??_C@_0CB@DAGHOOOF@Failed?5to?5create?5an?5ImGUI?5shade@ ??_C@_05DPNLPLKI@COLOR@ ??_C@_0BD@LCDIKCLM@ImGui?5index?5buffer@ ??_C@_0BE@NIFLFMJH@ImGui?5vertex?5buffer@ ??_C@_05EKMHMOJH@ImGUI@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@3f800000 __real@5f000000 __security_cookie __xmm@000000000000000f0000000000000000 __xmm@3f8000003f8000003f8000003f800000 