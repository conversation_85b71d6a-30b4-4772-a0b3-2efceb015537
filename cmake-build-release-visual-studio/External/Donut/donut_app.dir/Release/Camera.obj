d�眈GhZ�       .drectve        <  �)               
 .debug$S        L 0+  |B        @ B.debug$T        l   藼             @ B.rdata          @   8C             @ @@.text$mn        �   xC              P`.debug$S        �  D 腅        @B.text$mn        &   F              P`.debug$S          :F RG        @B.text$mn        n                P`.debug$S        �  I 豄        @B.text$mn        5  PL              P`.debug$S        �  匨 EO        @B.text$mn        �   錙              P`.debug$S        ,  tP 燪        @B.text$mn        �   餛              P`.debug$S        ,  鏡 T        @B.text$mn        �   cT              P`.debug$S          鬞 V        @B.text$mn           \V              P`.debug$S        �   uV iW        @B.text$mn           筗              P`.debug$S        �   豔 蘕        @B.text$mn        .   Y              P`.debug$S        �   JY >Z        @B.text$mn        .   嶼              P`.debug$S        �   糧 癧        @B.text$mn        ,    \              P`.debug$S        �   ,\  ]        @B.text$mn           p]              P`.debug$S        �   刔 x^        @B.text$mn        7   萟              P`.debug$S        l  �^ k`        @B.text$mn        D   鉦              P`.debug$S        �  'a 玝        @B.text$mn        "   #c              P`.debug$S        (  Ec md        @B.text$mn        j   ヾ e         P`.debug$S        �  1e -g        @B.text$mn        :    遟         P`.debug$S        L  間 5i        @B.text$mn        #   卛              P`.debug$S        �   ╥ 渏        @B.text$mn        �   豭 mk         P`.debug$S        8  wk 痬        @B.text$mn        �   cn              P`.debug$S        �  鴑 磒        @B.text$mn        \  @q 渟         P`.debug$S        t  簊 .x        @B.text$mn        l  鰔 bz         P`.debug$S        �  瞶 n     (   @B.text$mn        3                 P`.debug$S        ,  1� ]�        @B.text$mn        H   檪              P`.debug$S        �  醾         @B.text$mn        �  絽 >�         P`.debug$S        h  瑘 �     (   @B.text$mn        5                 P`.debug$S          購 閼        @B.text$mn        �  u� X�         P`.debug$S        h
  鴹 `�     R   @B.text$mn        �   敟 姦         P`.debug$S        �  拨 F�     
   @B.text$mn        �    f�         P`.debug$S        �  z� R�        @B.text$mn        2  V� 埍     
    P`.debug$S          
� &�     $   @B.text$mn        �  幒 P�         P`.debug$S          n� 偰     >   @B.text$mn        �  钇 徥         P`.debug$S        8  帐 
�     .   @B.text$mn        �  僭 |�         P`.debug$S        8   孢     <   @B.text$mn        0   >�              P`.debug$S        �  n� 
�        @B.text$mn        0   F�              P`.debug$S        �  v� �        @B.text$mn        �   N� �         P`.debug$S          5� M�         @B.text$mn        V   嶌 沆         P`.debug$S        �  盱 濐        @B.text$mn        G   眍              P`.debug$S        |  4� 榜        @B.text$mn        E   P� 曭         P`.debug$S        |   %�     
   @B.text$mn        G   夢              P`.debug$S        l  恤 <�        @B.text$mn           荀              P`.debug$S        l   d�     
   @B.text$mn        �   腮          P`.debug$S        �  耳 Z      (   @B.text$mn        O   �              P`.debug$S        �  9 !        @B.text$mn        �   � �         P`.debug$S        �  � {     (   @B.text$mn        }    �         P`.debug$S        �  � v        @B.text$mn        �    �         P`.debug$S          � �        @B.text$mn        �   � �         P`.debug$S           � �        @B.text$mn        <  ' c         P`.debug$S        �  � 
          @B.text$mn        �   M! "         P`.debug$S        p  %" �'     (   @B.text$mn        G   %)              P`.debug$S        �  l) ,        @B.text$mn        .   �,              P`.debug$S        �  �, �.        @B.text$mn           ~/              P`.debug$S        `  �/ �0     
   @B.text$mn        E   ^1              P`.debug$S        l  �1 4        @B.text$mn           �4              P`.debug$S        `  �4 +6     
   @B.text$mn        E   �6              P`.debug$S        |  �6 P9        @B.text$mn        @   �9 0:         P`.debug$S        �  D: (<        @B.text$mn           �<              P`.debug$S        X  �< 	>     
   @B.text$mn        �   m>              P`.debug$S          =? IA        @B.text$mn        �  鼳 菴         P`.debug$S        �  蹸 孒        @B.text$mn        p   hI              P`.debug$S        (  豂  M        @B.text$mn          燤 絅         P`.debug$S        8  袾 	S        @B.text$mn        Y  鵖 RW         P`.debug$S        �  zW &^     (   @B.text$mn        !  禵 譧         P`.debug$S        X  鮜 Mc        @B.text$mn        �   漜 1d         P`.debug$S        �  Ed 1h        @B.xdata             !i             @0@.pdata             5i Ai        @0@.xdata              _i             @0@.pdata             i 媔        @0@.xdata             ﹊             @0@.pdata             絠 蒳        @0@.xdata             鏸             @0@.pdata             骾 �i        @0@.xdata             j             @0@.pdata             )j 5j        @0@.xdata          ,   Sj             @0@.pdata             j 媕        @0@.xdata          (   ﹋             @0@.pdata             裫 輏        @0@.xdata             鹙             @0@.pdata             k k        @0@.xdata             -k             @0@.pdata             5k Ak        @0@.xdata             _k             @0@.pdata             kk wk        @0@.xdata             昸             @0@.pdata              璳        @0@.xdata          4   薻             @0@.pdata             �k l        @0@.xdata             )l             @0@.pdata             1l =l        @0@.xdata             [l             @0@.pdata             gl sl        @0@.xdata          <   憀             @0@.pdata             蚻 賚        @0@.xdata             鱨             @0@.pdata             m m        @0@.xdata             -m             @0@.pdata             9m Em        @0@.xdata             cm             @0@.pdata             m 媘        @0@.xdata             ﹎             @0@.pdata             羗 蚼        @0@.xdata             雖 �m        @0@.pdata             n )n        @0@.xdata             Gn _n        @0@.pdata             }n 塶        @0@.xdata              縩        @0@.pdata             輓 閚        @0@.xdata             o o        @0@.pdata             9o Eo        @0@.xdata             co so        @0@.pdata             憃 漮        @0@.xdata             籵 薿        @0@.pdata             閛 鮫        @0@.xdata             p #p        @0@.pdata             Ap Mp        @0@.xdata             kp             @0@.pdata             sp p        @0@.xdata             漰 筽        @0@.pdata             蚿 賞        @0@.xdata          	   鱬  q        @@.xdata             q q        @@.xdata             $q             @@.voltbl            'q               .xdata             *q             @0@.pdata             6q Bq        @0@.xdata             `q xq        @0@.pdata             杚         @0@.xdata             纐 詑        @0@.pdata             騫         @0@.xdata             r ,r        @0@.pdata             Jr Vr        @0@.voltbl            tr                .xdata             xr             @0@.pdata             剅 恟        @0@.xdata             畆 聄        @0@.pdata             鄏 靣        @0@.xdata             
s s        @0@.pdata             8s Ds        @0@.voltbl            bs               .xdata             ds             @0@.pdata             ls xs        @0@.xdata             杝 猻        @0@.pdata             萻 詓        @0@.xdata             騭 t        @0@.pdata              t ,t        @0@.voltbl            Jt               .xdata             Mt             @0@.pdata             Ut at        @0@.xdata             t             @0@.pdata             噒 搕        @0@.xdata             眛             @0@.pdata             箃 舤        @0@.xdata             鉻             @0@.pdata             雝 鱰        @0@.xdata             u             @0@.pdata             u )u        @0@.xdata             Gu             @0@.pdata             Ou [u        @0@.xdata             yu             @0@.pdata             塽 晆        @0@.xdata             硊             @0@.pdata             莡 觰        @0@.xdata          8   駏             @0@.pdata             )v 5v        @0@.xdata             Sv             @0@.pdata             cv ov        @0@.xdata          $   峷             @0@.pdata             眝 絭        @0@.xdata             踲 飗        @0@.pdata             w w        @0@.xdata             -w 2w        @@.xdata             <w             @@.voltbl            ?w               .xdata             @w             @0@.pdata             Lw Xw        @0@.xdata             vw             @0@.pdata             倃 巜        @0@.xdata              瑆             @0@.pdata             蘷 豾        @0@.data               鰓             @ @�.rdata              x             @@@.data$r         /   6x ex        @@�.data$r         5   ox         @@�.rdata             畑             @0@.rdata             瞲             @0@.rdata             秞             @0@.rdata             簒             @0@.rdata             緓             @0@.rdata             聏             @0@.rdata             苮             @0@.rdata             蕏             @0@.rdata             蝬             @0@.rdata             襵             @0@.rdata             謝             @0@.rdata             趚             @P@.rdata             陎             @P@.chks64         `  鷛              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   !  a     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_app.dir\Release\Camera.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $app  $math 	 $colors  $Json 	 $stdext    �   0� 7 =   std::atomic<unsigned int>::is_always_lock_free % e   std::ctype<char>::table_size _ e   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment D e   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment B e   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D e   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O e   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n e   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n e  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g =   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j e    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h e    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size ] e   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos x e   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment   g   std::_Iosb<int>::skipws ! g   std::_Iosb<int>::unitbuf # g   std::_Iosb<int>::uppercase " g   std::_Iosb<int>::showbase # g   std::_Iosb<int>::showpoint ! g    std::_Iosb<int>::showpos  g  @ std::_Iosb<int>::left  g  � std::_Iosb<int>::right " g   std::_Iosb<int>::internal  g   std::_Iosb<int>::dec  g   std::_Iosb<int>::oct  g   std::_Iosb<int>::hex $ g   std::_Iosb<int>::scientific  g    std::_Iosb<int>::fixed " g   0std::_Iosb<int>::hexfloat # g   @std::_Iosb<int>::boolalpha " g  � �std::_Iosb<int>::_Stdio % g  �std::_Iosb<int>::adjustfield # g   std::_Iosb<int>::basefield $ g   0std::_Iosb<int>::floatfield ! g    std::_Iosb<int>::goodbit   g   std::_Iosb<int>::eofbit ! g   std::_Iosb<int>::failbit   g   std::_Iosb<int>::badbit  g   std::_Iosb<int>::in  g   std::_Iosb<int>::out  g   std::_Iosb<int>::ate  g   std::_Iosb<int>::app  g   std::_Iosb<int>::trunc # g  @ std::_Iosb<int>::_Nocreate $ g  � std::_Iosb<int>::_Noreplace   g    std::_Iosb<int>::binary  g    std::_Iosb<int>::beg  g   std::_Iosb<int>::cur  g   std::_Iosb<int>::end , g  @ std::_Iosb<int>::_Default_open_prot '=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible O e   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment $=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !=   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable 8 =    std::_False_trivial_cat::_Bitcopy_constructible 5 =    std::_False_trivial_cat::_Bitcopy_assignable  6  ��I@donut::math::PI_f " 2  
�-DT�!	@donut::math::PI_d ! 6  ��7�5donut::math::epsilon " 6  �  �donut::math::infinity  6  �  �donut::math::NaN E e   std::allocator<char16_t>::_Minimum_asan_allocation_alignment � e   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment C e   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E e   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P e   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity Z e   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment d e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q e   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity - =    std::chrono::system_clock::is_steady q e  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j =   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m e    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset a=    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi k e    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size d=   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard $    std::ratio<1,10000000>::num (   ��枠 std::ratio<1,10000000>::den R e   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment � e   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment ` e   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos     std::ratio<1,1>::num     std::ratio<1,1>::den � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � =   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable J    std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N   ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 Z e   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 (   ��枠 std::ratio<10000000,1>::num $    std::ratio<10000000,1>::den P   ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K    std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy <   ��枠 std::integral_constant<__int64,10000000>::value 1    std::integral_constant<__int64,1>::value - =   std::chrono::steady_clock::is_steady &    std::ratio<1,1000000000>::num *   � 蕷;std::ratio<1,1000000000>::den te   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size te   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n=    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi 4 e  @ _Mtx_internal_imp_t::_Critical_section_size 5 e   _Mtx_internal_imp_t::_Critical_section_align + =    std::_Aligned_storage<64,8>::_Fits * =    std::_Aligned<64,8,char,0>::_Fits + =    std::_Aligned<64,8,short,0>::_Fits ) =   std::_Aligned<64,8,int,0>::_Fits ) 3   donut::math::vector<bool,2>::DIM E e   std::allocator<char32_t>::_Minimum_asan_allocation_alignment     std::ratio<3600,1>::num      std::ratio<3600,1>::den ) 3   donut::math::vector<bool,3>::DIM ) 3   donut::math::vector<bool,4>::DIM 6 =   std::_Iterator_base0::_Unwrap_when_unverified % 塓    _Atomic_memory_order_relaxed % 塓   _Atomic_memory_order_consume % 塓   _Atomic_memory_order_acquire % 塓   _Atomic_memory_order_release % 塓   _Atomic_memory_order_acq_rel % 塓   _Atomic_memory_order_seq_cst c e   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment C e   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE 7 =   std::_Iterator_base12::_Unwrap_when_unverified E e   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P e   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity � e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment d e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q e   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j =   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m e    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k e    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size ` e   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos Z e   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment    < std::ratio<60,1>::num     std::ratio<60,1>::den  |O    std::denorm_absent  |O   std::denorm_present  O    std::round_toward_zero  O   std::round_to_nearest # |O    std::_Num_base::has_denorm ( =    std::_Num_base::has_denorm_loss % =    std::_Num_base::has_infinity & =    std::_Num_base::has_quiet_NaN * =    std::_Num_base::has_signaling_NaN # =    std::_Num_base::is_bounded ! =    std::_Num_base::is_exact " =    std::_Num_base::is_iec559 # =    std::_Num_base::is_integer " =    std::_Num_base::is_modulo " =    std::_Num_base::is_signed ' =    std::_Num_base::is_specialized ( =    std::_Num_base::tinyness_before  =    std::_Num_base::traps $ O    std::_Num_base::round_style  g    std::_Num_base::digits ! g    std::_Num_base::digits10 % g    std::_Num_base::max_digits10 % g    std::_Num_base::max_exponent ' g    std::_Num_base::max_exponent10 % g    std::_Num_base::min_exponent ' g    std::_Num_base::min_exponent10  g    std::_Num_base::radix ' =   std::_Num_int_base::is_bounded % =   std::_Num_int_base::is_exact ' =   std::_Num_int_base::is_integer + =   std::_Num_int_base::is_specialized " g   std::_Num_int_base::radix ) |O   std::_Num_float_base::has_denorm + =   std::_Num_float_base::has_infinity , =   std::_Num_float_base::has_quiet_NaN 0 =   std::_Num_float_base::has_signaling_NaN ) =   std::_Num_float_base::is_bounded ( =   std::_Num_float_base::is_iec559 ( =   std::_Num_float_base::is_signed - =   std::_Num_float_base::is_specialized * O   std::_Num_float_base::round_style $ g   std::_Num_float_base::radix Z e   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment * g   std::numeric_limits<bool>::digits - =   std::numeric_limits<char>::is_signed - =    std::numeric_limits<char>::is_modulo * g   std::numeric_limits<char>::digits , g   std::numeric_limits<char>::digits10 4 =   std::numeric_limits<signed char>::is_signed 1 g   std::numeric_limits<signed char>::digits 3 g   std::numeric_limits<signed char>::digits10 6 =   std::numeric_limits<unsigned char>::is_modulo 3 g   std::numeric_limits<unsigned char>::digits 5 g   std::numeric_limits<unsigned char>::digits10 1 =   std::numeric_limits<char16_t>::is_modulo . g   std::numeric_limits<char16_t>::digits 0 g   std::numeric_limits<char16_t>::digits10      std::ratio<1,1000>::num     �std::ratio<1,1000>::den 1 =   std::numeric_limits<char32_t>::is_modulo . g    std::numeric_limits<char32_t>::digits 0 g  	 std::numeric_limits<char32_t>::digits10 0 =   std::numeric_limits<wchar_t>::is_modulo \ e   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment - g   std::numeric_limits<wchar_t>::digits / g   std::numeric_limits<wchar_t>::digits10 . =   std::numeric_limits<short>::is_signed + g   std::numeric_limits<short>::digits � e   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment - g   std::numeric_limits<short>::digits10 , =   std::numeric_limits<int>::is_signed ) g   std::numeric_limits<int>::digits + g  	 std::numeric_limits<int>::digits10 - =   std::numeric_limits<long>::is_signed * g   std::numeric_limits<long>::digits , g  	 std::numeric_limits<long>::digits10 a=    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d=   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 0 =   std::numeric_limits<__int64>::is_signed - g  ? std::numeric_limits<__int64>::digits / g   std::numeric_limits<__int64>::digits10 #    std::ratio<1,1000000>::num '   �@B std::ratio<1,1000000>::den 7 =   std::numeric_limits<unsigned short>::is_modulo 4 g   std::numeric_limits<unsigned short>::digits 6 g   std::numeric_limits<unsigned short>::digits10 5 =   std::numeric_limits<unsigned int>::is_modulo 2 g    std::numeric_limits<unsigned int>::digits 4 g  	 std::numeric_limits<unsigned int>::digits10 � e   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment 6 =   std::numeric_limits<unsigned long>::is_modulo 3 g    std::numeric_limits<unsigned long>::digits 5 g  	 std::numeric_limits<unsigned long>::digits10 B e   std::allocator<float>::_Minimum_asan_allocation_alignment 9 =   std::numeric_limits<unsigned __int64>::is_modulo 6 g  @ std::numeric_limits<unsigned __int64>::digits 8 g   std::numeric_limits<unsigned __int64>::digits10 + g   std::numeric_limits<float>::digits - g   std::numeric_limits<float>::digits10 1 g  	 std::numeric_limits<float>::max_digits10 1 g  � std::numeric_limits<float>::max_exponent 3 g  & std::numeric_limits<float>::max_exponent10 2 g   �僺td::numeric_limits<float>::min_exponent 4 g   �踫td::numeric_limits<float>::min_exponent10 , g  5 std::numeric_limits<double>::digits . g   std::numeric_limits<double>::digits10 2 g   std::numeric_limits<double>::max_digits10 2 g   std::numeric_limits<double>::max_exponent 4 g  4std::numeric_limits<double>::max_exponent10 4 g  �黶td::numeric_limits<double>::min_exponent 6 g  �威std::numeric_limits<double>::min_exponent10 1 g  5 std::numeric_limits<long double>::digits 3 g   std::numeric_limits<long double>::digits10 7 g   std::numeric_limits<long double>::max_digits10 7 g   std::numeric_limits<long double>::max_exponent 9 g  4std::numeric_limits<long double>::max_exponent10 9 g  �黶td::numeric_limits<long double>::min_exponent ; g  �威std::numeric_limits<long double>::min_exponent10 / =   std::atomic<long>::is_always_lock_free . =   std::integral_constant<bool,1>::value ) 3   nvrhi::ObjectTypes::SharedHandle - 3  �  nvrhi::ObjectTypes::D3D11_Device 4 3  �  nvrhi::ObjectTypes::D3D11_DeviceContext / 3  �  nvrhi::ObjectTypes::D3D11_Resource - 3  �  nvrhi::ObjectTypes::D3D11_Buffer 7 3  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 3  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 3  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : 3  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - 3  �  nvrhi::ObjectTypes::D3D12_Device 3 3  �  nvrhi::ObjectTypes::D3D12_CommandQueue : 3  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / 3  �  nvrhi::ObjectTypes::D3D12_Resource A 3  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A 3  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F 3  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G 3  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 3  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 3  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 3  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * 3  �  nvrhi::ObjectTypes::VK_Device 2 3  �  nvrhi::ObjectTypes::VK_PhysicalDevice , 3  �  nvrhi::ObjectTypes::VK_Instance ) 3  �  nvrhi::ObjectTypes::VK_Queue 1 3  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 3  �  nvrhi::ObjectTypes::VK_DeviceMemory * 3  �  nvrhi::ObjectTypes::VK_Buffer ) 3  �  nvrhi::ObjectTypes::VK_Image - 3  �	  nvrhi::ObjectTypes::VK_ImageView < 3  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + 3  �  nvrhi::ObjectTypes::VK_Sampler 0 3  �  nvrhi::ObjectTypes::VK_ShaderModule . 3  �
  nvrhi::ObjectTypes::VK_RenderPass / 3  �  nvrhi::ObjectTypes::VK_Framebuffer 2 3  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 3  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 3  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 3  �  nvrhi::ObjectTypes::VK_PipelineLayout , 3  �  nvrhi::ObjectTypes::VK_Pipeline , 3  �  nvrhi::ObjectTypes::VK_Micromap 3 3  �  nvrhi::ObjectTypes::VK_ImageCreateInfo  3   nvrhi::c_HeaderVersion " 3   nvrhi::c_MaxRenderTargets  3   nvrhi::c_MaxViewports % 3   nvrhi::c_MaxVertexAttributes # 3   nvrhi::c_MaxBindingLayouts & 3  � nvrhi::c_MaxBindingsPerLayout 5 3   nvrhi::c_MaxVolatileConstantBuffersPerLayout , 3    nvrhi::c_MaxVolatileConstantBuffers % 3  � nvrhi::c_MaxPushConstantSize 3 3   nvrhi::c_ConstantBufferOffsetSizeAlignment 3 E  \ std::filesystem::path::preferred_separator te   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size te   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n=    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi . g   donut::math::box<float,3>::numCorners 8 =   std::atomic<unsigned long>::is_always_lock_free / 3  � nvrhi::rt::cluster::kClasByteAlignment . 3   nvrhi::rt::cluster::kClasMaxTriangles - 3   nvrhi::rt::cluster::kClasMaxVertices 2 3  ���� nvrhi::rt::cluster::kMaxGeometryIndex � e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment W =   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R =   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 3  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 3  �����nvrhi::TextureSubresourceSet::AllArraySlices H =    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified # �        nvrhi::AllSubresources j e   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment \ e   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment j e   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment R e   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment   �        nvrhi::EntireBuffer : e   std::integral_constant<unsigned __int64,1>::value * 3   donut::math::vector<float,3>::DIM _ e   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment q e   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment : e    std::integral_constant<unsigned __int64,0>::value x e   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment D e   ��std::basic_string_view<char,std::char_traits<char> >::npos * 3   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients . =    std::integral_constant<bool,0>::value ) GV    std::_Invoker_functor::_Strategy , GV   std::_Invoker_pmf_object::_Strategy - GV   std::_Invoker_pmf_refwrap::_Strategy * 3   donut::math::vector<float,2>::DIM - GV   std::_Invoker_pmf_pointer::_Strategy i e   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment , GV   std::_Invoker_pmd_object::_Strategy - GV   std::_Invoker_pmd_refwrap::_Strategy - GV   std::_Invoker_pmd_pointer::_Strategy =    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "=   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard J e   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos 5 =    std::filesystem::_File_time_clock::is_steady � e   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment q e   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment h e   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment L e   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos � e   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment :=    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi ==   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard L e   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos ) 3   donut::math::frustum::numCorners � e   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment i e   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment 2e   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2e   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,=    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi J    std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J    std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P   ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K    std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy � =   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified l e   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment : g   std::_Floating_type_traits<float>::_Mantissa_bits : g   std::_Floating_type_traits<float>::_Exponent_bits D g   std::_Floating_type_traits<float>::_Maximum_binary_exponent E g   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : g   std::_Floating_type_traits<float>::_Exponent_bias 7 g   std::_Floating_type_traits<float>::_Sign_shift ; g   std::_Floating_type_traits<float>::_Exponent_shift : 3  � std::_Floating_type_traits<float>::_Exponent_mask E 3  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G 3  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J 3  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B 3  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F 3  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask � e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment ; g  5 std::_Floating_type_traits<double>::_Mantissa_bits ; g   std::_Floating_type_traits<double>::_Exponent_bits E g  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G g  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; g  �std::_Floating_type_traits<double>::_Exponent_bias 8 g  ? std::_Floating_type_traits<double>::_Sign_shift < g  4 std::_Floating_type_traits<double>::_Exponent_shift ; e  �std::_Floating_type_traits<double>::_Exponent_mask J e  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L e  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O e  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G e  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K e  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask $ g   std::_Locbase<int>::collate " g   std::_Locbase<int>::ctype % g   std::_Locbase<int>::monetary $ g   std::_Locbase<int>::numeric ! g   std::_Locbase<int>::time % g    std::_Locbase<int>::messages   g  ? std::_Locbase<int>::all ! g    std::_Locbase<int>::none a e   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment Me   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size Me   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets G=    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi c e   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment � =   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � =    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � =    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment W e   std::allocator<std::pair<int const ,int> >::_Minimum_asan_allocation_alignment "     std::memory_order_relaxed "    std::memory_order_consume A e   std::allocator<bool>::_Minimum_asan_allocation_alignment "    std::memory_order_acquire "    std::memory_order_release "    std::memory_order_acq_rel "    std::memory_order_seq_cst � =    std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0>::_Multi � =   std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0>::_Standard + 3   donut::math::vector<double,3>::DIM  �/   std::_Consume_header  �/   std::_Generate_header + J        nvrhi::rt::c_IdentityTransform � =   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � =    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � =    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable I e   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment o e   std::allocator<std::_List_node<std::pair<int const ,int>,void *> >::_Minimum_asan_allocation_alignment A e   std::allocator<char>::_Minimum_asan_allocation_alignment � =   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � =    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � =    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable + 3   donut::math::vector<double,4>::DIM ? e   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A e   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L e   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity : e   std::integral_constant<unsigned __int64,2>::value X e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e e   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ =   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � =   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable T e   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos M e   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment � e   std::_Hash<std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0> >::_Bucket_size � e   std::_Hash<std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0> >::_Min_buckets � =    std::_Hash<std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0> >::_Multi c e   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment T e   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment  g    LightType_None  g   LightType_Directional  g   LightType_Spot  g   LightType_Point R =    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified O e   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment Z g   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size � e   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > >::_Minimum_asan_allocation_alignment M e   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R g   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T g   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size - g    std::integral_constant<int,0>::value  ≦  _CatchableType " 俀  _s__RTTIBaseClassDescriptor ? s  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & 扱  $_TypeDescriptor$_extraBytes_24 6 ve  __vcrt_va_list_is_reference<char const * const> G |  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �.  _Ctypevec & 
e  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - ne  __vc_attributes::event_sourceAttribute 9 ge  __vc_attributes::event_sourceAttribute::optimize_e 5 ee  __vc_attributes::event_sourceAttribute::type_e > ce  __vc_attributes::helper_attributes::v1_alttypeAttribute F ^e  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 [e  __vc_attributes::helper_attributes::usageAttribute B We  __vc_attributes::helper_attributes::usageAttribute::usage_e * Te  __vc_attributes::threadingAttribute 7 Me  __vc_attributes::threadingAttribute::threading_e - Je  __vc_attributes::aggregatableAttribute 5 Ce  __vc_attributes::aggregatableAttribute::type_e / @e  __vc_attributes::event_receiverAttribute 7 7e  __vc_attributes::event_receiverAttribute::type_e ' 4e  __vc_attributes::moduleAttribute / +e  __vc_attributes::moduleAttribute::type_e  �1  __std_fs_find_data & 翾  $_TypeDescriptor$_extraBytes_23 - R  $_s__CatchableTypeArray$_extraBytes_32 # E7  __std_fs_reparse_data_buffer Z (e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ %e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` #e  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �1  __std_fs_dir_handle  �-  __std_access_rights  繧  _TypeDescriptor & 譗  $_TypeDescriptor$_extraBytes_34 	   tm % }Q  _s__RTTICompleteObjectLocator2 & 
R  $_TypeDescriptor$_extraBytes_30 A e  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  ≦  _s__CatchableType & 禥  $_TypeDescriptor$_extraBytes_19 & 赒  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 e  __vcrt_va_list_is_reference<wchar_t const * const>  5  __std_fs_filetime E a  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 朡  $_TypeDescriptor$_extraBytes_20  p  va_list - 闝  $_s__CatchableTypeArray$_extraBytes_16   k7  __std_fs_copy_file_result  �1  __std_code_page � e  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w e  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 琩  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > d 籨  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c 鮠  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h 鱠  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 歝  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y 騞  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > c 俠  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 鎑  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 甤  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ] 載  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ Oc  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 裠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 莇  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 絛  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 甦  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > 焏  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 梔  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � )c  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W巇  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 嘾  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 卆  std::_Default_allocator_traits<std::allocator<float> > ; 鈇  std::hash<std::shared_ptr<donut::engine::Material> > � `c  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > ~}d  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ sd  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � jd  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C 頲  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 鋍  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 衏  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � `  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> �   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 萩  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 竎  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 昪  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 癱  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ?   std::equal_to<std::shared_ptr<donut::engine::Material> > 6 a  std::allocator<donut::engine::SkinnedMeshJoint> M 7c  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L 渃  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 梒  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � c  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 坈  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T vc  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > � lc  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � bc  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U Qc  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 鱚  std::_Ptr_base<donut::engine::LoadedTexture> : Cc  std::_Vector_val<std::_Simple_types<unsigned int> > D 9c  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � *[  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � +c  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 韀  std::_Ptr_base<donut::engine::DescriptorHandle> � c  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~c  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e 孾  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 筧  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > "榑  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W }b  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > d瞏  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > U +`  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w c  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � 鵥  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y 鈈  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 謆  std::allocator<donut::math::vector<float,2> > M 莃  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > =   std::allocator<donut::math::vector<unsigned short,4> > K 絙  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p 砨  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U 衃  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 阓  std::_Ptr_base<donut::engine::BufferGroup> F玝  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ 裐  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 宐  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 刡  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e [  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N b  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > � *a  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > > > { qb  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l [  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > , b  std::allocator<nvrhi::BindingSetItem> K 鯽  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 靉  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # 轪  std::allocator<unsigned int> � 蟖  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 8[  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 馺  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � 禲  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � YX  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > D ca  std::_Default_allocator_traits<std::allocator<unsigned int> > g ?Z  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 籥  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  糩  std::allocator<float> � 璦  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> 朼  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 廰  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t 4 嘺  std::allocator_traits<std::allocator<float> > [ ya  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > l 諷  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > w Y  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > ; ea  std::allocator_traits<std::allocator<unsigned int> > [ Wa  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 5\  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Ma  std::hash<std::shared_ptr<donut::engine::MeshInfo> > WIa  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> � Ba  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � ,a  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > > > H YZ  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ 鯨  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' ZC  std::default_delete<wchar_t [0]> . +-  std::_Conditionally_enabled_hash<int,1> A (6  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> "XK  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > > >,1> � a  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N uZ  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X a  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? 筄  std::_Default_allocator_traits<std::allocator<wchar_t> > . dO  std::integer_sequence<unsigned __int64>  v  std::_Lockit � D>  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > � >  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy * cH  std::hash<enum nvrhi::ResourceType> / H<  std::shared_ptr<donut::engine::Material> - F2  std::reverse_iterator<wchar_t const *> 5 38  std::shared_ptr<donut::engine::SceneGraphNode> 9 �)  std::shared_ptr<donut::engine::animation::Sampler> " 諳  std::_Char_traits<char,int>  .  std::_Fs_file  -A  std::array<bool,14> � a  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  侽  std::_Num_base & 7-  std::hash<std::error_condition> K 盝  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 衈  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >  |*  std::_Big_uint128 / 
;  std::weak_ptr<donut::engine::SceneGraph> 颼  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > + &B  std::_Optional_construct_base<float> 8 F  std::initializer_list<std::pair<int const ,int> > ) 酧  std::_Narrow_char_traits<char,int> L -`  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > LQ  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::hash<float> 6 rJ  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 6;  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > >  /-  std::hash<int>  凮  std::_Num_int_base  �0  std::ctype<wchar_t> " �-  std::_System_error_category � `  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / 丠  std::_Conditionally_enabled_hash<bool,1> 2 `  std::shared_ptr<donut::engine::BufferGroup>  鶰  std::equal_to<int> � 譥  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  |O  std::float_denorm_style 2 �  std::shared_ptr<donut::engine::SceneCamera> 4 _  std::shared_ptr<donut::engine::LoadedTexture> 跧  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! FJ  std::piecewise_construct_t u WO  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 鈄  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . �8  std::_Ptr_base<donut::engine::MeshInfo> 6 赹  std::allocator_traits<std::allocator<wchar_t> > � 豝  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  S  std::bad_cast B �9  std::enable_shared_from_this<donut::engine::SceneGraphNode>  WI  std::equal_to<void> 4 漐  std::allocator<donut::math::vector<float,4> > � �3  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } (?  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � �>  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 羄  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 廭  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o 鉒  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � "D  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > " ㎡  std::numeric_limits<double>  �  std::__non_rtti_object < �'  std::_Ptr_base<donut::engine::DescriptorTableManager> ( �  std::_Basic_container_proxy_ptr12 4 J^  std::allocator<donut::math::vector<float,3> > �  J  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � �:  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � h:  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > <^  std::vector<unsigned int,std::allocator<unsigned int> > T ^  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8> T 襗  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >    std::_Num_float_base � 萞  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  �+  std::logic_error 3 M8  std::weak_ptr<donut::engine::SceneGraphNode> 8 PF  std::_List_node<std::pair<int const ,int>,void *> � ZJ  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � 6L  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 <H  std::_Conditionally_enabled_hash<unsigned int,1> G kH  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety P 碷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 僝  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! I]  std::char_traits<char32_t>  $/  std::locale  Y/  std::locale::_Locimp  5/  std::locale::facet   =/  std::locale::_Facet_guard  �.  std::locale::id ? G]  std::allocator_traits<std::allocator<unsigned __int64> > : 9  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] <C  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s qP  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z E]  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   哋  std::numeric_limits<bool> # ≒  std::_WChar_traits<char16_t> _ ;]  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u 
]  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P 廋  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T '  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   �  std::_Fake_proxy_ptr_impl . <  std::_Ptr_base<donut::engine::Material> * 淥  std::numeric_limits<unsigned short> ' '  std::hash<nvrhi::BindingSetDesc> Z 扤  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M 02  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � zJ  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  i,  std::overflow_error d 薥  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 橽  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy W gP  std::list<std::pair<int const ,int>,std::allocator<std::pair<int const ,int> > > % 楧  std::_One_then_variadic_args_t D 貶  std::_Constexpr_immortalize_impl<std::_System_error_category> W 窷  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * pV  std::_Vb_val<std::allocator<bool> > E 	7  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j R\  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  =B  std::optional<float>   P\  std::char_traits<wchar_t>   �  std::pmr::memory_resource ! -  std::array<nvrhi::Rect,16> 4 滼  std::allocator<nvrhi::rt::PipelineShaderDesc> 7 N\  std::shared_ptr<donut::engine::SceneTypeFactory> �  \  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � \  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 荁  std::allocator<unsigned __int64>  v[  std::false_type  O  std::float_round_style T    std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H 綪  std::_Default_allocator_traits<std::allocator<unsigned __int64> >  j  std::string B \  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �2  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , r!  std::array<nvrhi::BindingSetItem,128> �   std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �"  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � T  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > � 蠮  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 \  std::shared_ptr<donut::engine::DescriptorHandle> ,   std::numeric_limits<unsigned __int64> � 赱  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �.  std::_Locinfo 6 85  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L 襕  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \ BQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s 橫  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H   std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> $ 嶰  std::numeric_limits<char16_t> 0 $  std::array<nvrhi::VertexBufferBinding,16> 5 橻  std::_Simple_types<std::pair<int const ,int> > � 橜  std::_Hash<std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0> >  �  std::string_view  &  std::wstring_view % 穁  std::integral_constant<bool,1>   �  std::_Leave_proxy_unbound � 袺  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > b 腫  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  x1  std::money_base  甗  std::money_base::pattern  T.  std::_Timevec D 琜  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > R 荢  std::_Default_allocator_traits<std::allocator<std::pair<int const ,int> > >   �,  std::_Init_once_completer j �6  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � i6  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + �/  std::codecvt<wchar_t,char,_Mbstatet> h 岲  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 漑  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > � BF  std::_Umap_traits<int,int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,int> >,0> % �  std::array<nvrhi::Viewport,16>  �  std::_Iterator_base12 j >)  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � )  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  咵  std::_Pocma_values 7 Z'  std::_Array_const_iterator<enum nvrhi::Format,8> ! %-  std::hash<std::error_code> N 93  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ 'Q  std::_Default_allocator_traits<std::allocator<char32_t> > : 沎  std::_List_simple_types<std::pair<int const ,int> >  �2  std::allocator<char32_t> ? =7  std::unique_ptr<char [0],std::default_delete<char [0]> > � 鱇  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ K  std::_Atomic_integral<long,4> � 朳  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  匟  std::hash<bool>     std::streamsize 6 ~D  std::_String_val<std::_Simple_types<char32_t> > = rF  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` 滶  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ 嶽  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > > �<  std::enable_shared_from_this<donut::engine::SceneGraph> K �[  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > 8 N  std::_Compressed_pair<std::equal_to<int>,float,1> d刄  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >    std::hash<long double> � �3  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 3  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l Z  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k V  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy 4 岮  std::pair<bool,donut::math::affine<float,3> > / �8  std::shared_ptr<donut::engine::MeshInfo>  mA  std::array<bool,3> U kQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # 扥  std::numeric_limits<wchar_t>  3  std::_Container_base0  	  std::hash<double> 5 �:  std::shared_ptr<donut::engine::SceneGraphLeaf> O uQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & 烼  std::bidirectional_iterator_tag . �&  std::hash<nvrhi::TextureSubresourceSet> , 礏  std::allocator<std::_Container_proxy> / 匬  std::_Char_traits<char32_t,unsigned int>  O-  std::_System_error ( ?'  std::hash<nvrhi::FramebufferInfo> 9 {C  std::allocator<std::filesystem::_Find_file_handle>  -  std::error_condition % v[  std::integral_constant<bool,0>  a  std::bad_exception & 錌  std::_Zero_then_variadic_args_t d 隯  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ q[  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � a[  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string J[  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  4  std::_Fake_allocator / t   std::array<nvrhi::BindingLayoutItem,128>  
,  std::invalid_argument N H[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U 襈  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S F[  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 D[  std::_Vector_val<std::_Simple_types<float> > R eD  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A :[  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + �7  std::pair<enum __std_win_error,bool> � ,[  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > S  2  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  $,  std::length_error \ [  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F XM  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 僀  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> !   std::numeric_limits<float>  �1  std::time_base   ~1  std::time_base::dateorder � 驚  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > >,std::_Iterator_base0> ) \  std::_Atomic_integral_facade<long> 8 s)  std::_Ptr_base<donut::engine::animation::Sampler> B ;F  std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> > % 滺  std::hash<enum nvrhi::BlendOp> c 
[  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B �Z  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  �  std::_Ref_count_base " KH  std::hash<unsigned __int64>  馴  std::ratio<60,1>  �  std::exception_ptr  颶  std::ratio<1,1000000> [ 鞿  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) 慔  std::hash<enum nvrhi::BlendFactor> $L  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M WQ  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ 怬  std::numeric_limits<char32_t>  �,  std::once_flag   -  std::error_code J �'  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  6  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 遉  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  1  std::_Iosb<int>   1  std::_Iosb<int>::_Seekdir ! �0  std::_Iosb<int>::_Openmode   �0  std::_Iosb<int>::_Iostate ! �0  std::_Iosb<int>::_Fmtflags # �0  std::_Iosb<int>::_Dummy_enum 7 軿  std::allocator_traits<std::allocator<char32_t> >  訴  std::nano  ?  std::_Iterator_base0 | 踆  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � J  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U 覼  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0驣  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M 咼  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 橮  std::_Char_traits<char16_t,unsigned short> $ �&  std::hash<nvrhi::BufferRange> T )3  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  �.  std::_Locbase<int> � 繥  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<int const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<int const ,int> > >,1> ! 臵  std::char_traits<char16_t> L  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ 肸  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > > j ,X  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<int const ,int>,void *> > >  �  std::tuple<> � 9K  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > > >  ^  std::_Container_base12 W 鍸  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - ,<  std::weak_ptr<donut::engine::Material>  -  std::io_errc  ;1  std::ios_base  L1  std::ios_base::_Fnarray  F1  std::ios_base::_Iosarray  �0  std::ios_base::Init  �0  std::ios_base::failure  1  std::ios_base::event E 笻  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 hO  std::integer_sequence<unsigned __int64,0> ) 孫  std::numeric_limits<unsigned char> � XD  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  穁  std::true_type   極  std::numeric_limits<long> " 瞆  std::initializer_list<char> N 腡  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X ㈱  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  GV  std::_Invoker_strategy  7  std::nothrow_t �   std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 霫  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T 嶼  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ 甇  std::_Default_allocate_traits � /C  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � UZ  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N 
3  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 SZ  std::allocator_traits<std::allocator<char> > 0 {W  std::_Ptr_base<donut::engine::IShadowMap> ! 擮  std::numeric_limits<short>  u   std::_Vbase . 菾  std::allocator<nvrhi::rt::GeometryDesc> ( �(  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > c 錢  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > ! �0  std::ctype<unsigned short> C f  std::basic_string_view<char16_t,std::char_traits<char16_t> > � QZ  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < 6'  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 珼  std::_String_val<std::_Simple_types<char16_t> > = |F  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 9  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9Q  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � IZ  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ AZ  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O 鶫  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P �2  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . oH  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock Y 3Z  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  w  std::bad_alloc  �,  std::underflow_error B 岺  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  )Z  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � 鱕  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ CJ  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J aC  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D 瞃  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D PC  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  �1  std::messages_base  ;,  std::out_of_range # 歄  std::numeric_limits<__int64> 1   std::allocator<std::pair<int const ,int> > _ 昚  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u dY  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i 訡  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b &Y  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > Y  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  ^0  std::ctype<char> @  Y  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 鶷  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P 鯴  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > | OK  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > > ? 靀  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >    std::memory_order Z 鏧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 賆  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > q �?  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � m?  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  �  std::nullopt_t  �  std::nullopt_t::_Tag  mX  std::ratio<3600,1> # C  std::_Atomic_storage<long,4> # w'  std::hash<nvrhi::BlendState>  1  std::atomic_flag f 釪  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K kX  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >  q-  std::system_error < 罯  std::_Default_allocator_traits<std::allocator<char> > W aQ  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � L  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  ]X  std::ratio<1,1>   漈  std::forward_iterator_tag  R,  std::runtime_error � [X  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >   �  std::bad_array_new_length E 躂  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 LX  std::allocator<donut::engine::animation::Keyframe> K =X  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > k "K  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > a .X  std::allocator_traits<std::allocator<std::_List_node<std::pair<int const ,int>,void *> > >  �.  std::_Yarn<char>  M  std::_Container_proxy ( X  std::_Facetptr<std::ctype<char> > Z 砅  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  X  std::allocator<bool> � D  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  �  std::u16string _ 
X  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 躓  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 鸌  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *>  �  std::nested_exception    std::_Distance_unknown ) 濿  std::allocator<nvrhi::BufferRange> ( 濷  std::numeric_limits<unsigned int> < ;M  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , �/  std::codecvt<char32_t,char,_Mbstatet> 1 峎  std::shared_ptr<donut::engine::IShadowMap> C 誃  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @   std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �#  std::array<nvrhi::IBindingSet *,5> g A  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > K j  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F (C  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > 1 �  std::_Ptr_base<donut::engine::SceneCamera>     std::streamoff  緼  std::array<bool,1> 0 fW  std::vector<float,std::allocator<float> > F 4W  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 �:  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> & 鵙  std::initializer_list<char32_t> d �%  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �%  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 颲  std::initializer_list<char16_t> % 錠  std::initializer_list<wchar_t> C _H  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>     std::hash<std::nullptr_t> ' 玂  std::numeric_limits<long double>  -  std::errc } 郆  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J 霻  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  �=  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � �=  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy � 躒  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> ,   std::default_delete<std::_Facet_base>  �,  std::range_error  k  std::bad_typeid > 橦  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  訴  std::ratio<1,1000000000>  |2  std::allocator<char16_t> $ IC  std::default_delete<char [0]> C 襐  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . 腣  std::vector<bool,std::allocator<bool> > J EV  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` V  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` B%  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v %  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J �2  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  諹  std::ratio<1,1000> i 訳  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >  蔝  std::ratio<1,10000000> 萓  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; JD  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d O  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  �.  std::_Crt_new_delete % �-  std::_Iostream_error_category2 K   std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > * 齌  std::_String_constructor_concat_tag j 黅  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A 頣  std::allocator_traits<std::allocator<unsigned __int64 *> >  e2  std::allocator<char> T 軹  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> G 蒆  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> )   std::allocator<unsigned __int64 *>    std::nullptr_t =*  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > LM  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K嶭  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard &   std::random_access_iterator_tag ; GH  std::_Conditionally_enabled_hash<unsigned __int64,1> I 笹  std::allocator<std::_List_node<std::pair<int const ,int>,void *> > R 繡  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  �  std::bad_weak_ptr ) 燨  std::numeric_limits<unsigned long>   鸈  std::_Atomic_padded<long> 7 ZB  std::_Ptr_base<donut::engine::PerspectiveCamera>    F  std::pair<int const ,int> @ 6  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  �.  std::_Yarn<wchar_t> = vH  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  '  std::wstring p 蹳  std::unordered_map<int,int,std::hash<int>,std::equal_to<int>,std::allocator<std::pair<int const ,int> > > W %F  std::_Hash_find_last_result<std::_List_node<std::pair<int const ,int>,void *> *> ' 奜  std::numeric_limits<signed char> � �3  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � AL  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  �+  std::domain_error  �  std::u32string_view � D  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  3  std::_Container_base 1 �:  std::shared_ptr<donut::engine::SceneGraph>  �3  std::allocator<wchar_t> L 歍  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 楾  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � fT  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z 黃  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; �'  std::weak_ptr<donut::engine::DescriptorTableManager> $ WH  std::hash<nvrhi::IResource *> 4 �:  std::_Ptr_base<donut::engine::SceneGraphLeaf> "   std::_Nontrivial_dummy_type , B  std::_Optional_destruct_base<float,1> � 風  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 b'  std::hash<nvrhi::BlendState::RenderTarget>   圤  std::numeric_limits<char> 9 r+  std::chrono::duration<__int64,std::ratio<1,1000> >  �*  std::chrono::nanoseconds y 0.  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �*  std::chrono::duration<__int64,std::ratio<1,1000000000> > , 癘  std::chrono::duration_values<__int64>  �*  std::chrono::seconds 3 0+  std::chrono::duration<int,std::ratio<60,1> > 6 �*  std::chrono::duration<__int64,std::ratio<1,1> > s �*  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >    T  std::chrono::steady_clock   T  std::chrono::system_clock 6 E+  std::chrono::duration<double,std::ratio<60,1> > ; �+  std::chrono::duration<double,std::ratio<1,1000000> > > �+  std::chrono::duration<double,std::ratio<1,1000000000> > = �*  std::chrono::duration<__int64,std::ratio<1,10000000> > q �*  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 +  std::chrono::duration<int,std::ratio<3600,1> > 8 �+  std::chrono::duration<double,std::ratio<1,1000> > < �+  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 \+  std::chrono::duration<double,std::ratio<1,1> > 8 +  std::chrono::duration<double,std::ratio<3600,1> >  30  std::ctype_base  �4  std::filesystem::perms ' �4  std::filesystem::directory_entry $ �4  std::filesystem::copy_options ( r4  std::filesystem::filesystem_error 7 zI  std::filesystem::_Path_iterator<wchar_t const *> ) �1  std::filesystem::_Find_file_handle & �1  std::filesystem::_Is_slash_oper . �5  std::filesystem::_Should_recurse_result $ �7  std::filesystem::perm_options 4 �6  std::filesystem::recursive_directory_iterator . �4  std::filesystem::_File_status_and_error & i5  std::filesystem::_Dir_enum_impl 0 {5  std::filesystem::_Dir_enum_impl::_Creator @ �5  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! �4  std::filesystem::file_type . �5  std::filesystem::_Directory_entry_proxy " �7  std::filesystem::space_info * �5  std::filesystem::directory_iterator & 0.  std::filesystem::file_time_type 0 �5  std::filesystem::_Recursive_dir_enum_impl ) 5  std::filesystem::directory_options # �4  std::filesystem::file_status u ,4  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( T  std::filesystem::_File_time_clock  �2  std::filesystem::path $ �1  std::filesystem::path::format * II  std::filesystem::_Normal_conversion < kM  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �/  std::codecvt<char16_t,char,_Mbstatet>  T  std::char_traits<char> � 菴  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �,  std::error_category ) �,  std::error_category::_Addr_storage � �>  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � �>  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy R )F  std::_Uhash_choose_transparency<int,std::hash<int>,std::equal_to<int>,void> 8 sB  std::shared_ptr<donut::engine::PerspectiveCamera> ! �-  std::_System_error_message  
  std::_Unused_parameter = T  std::allocator<std::shared_ptr<donut::engine::Light> > h 籇  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>    std::bad_optional_access A &  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 Q5  std::shared_ptr<std::filesystem::_Dir_enum_impl> = SH  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> ` K  std::_Compressed_pair<std::hash<int>,std::_Compressed_pair<std::equal_to<int>,float,1>,1>  �/  std::_Codecvt_mode @ 0Q  std::_Default_allocator_traits<std::allocator<char16_t> > � T  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  歂  std::_Exact_args_t � C4  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > Q   std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 鯫  std::_Char_traits<wchar_t,unsigned short> '    std::array<enum nvrhi::Format,8> � 餝  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ 錗  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 諨  std::_String_val<std::_Simple_types<wchar_t> > < 匜  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  C.  std::_Facet_base b 釹  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' '  std::hash<nvrhi::BindingSetItem> " P  std::_WChar_traits<wchar_t> 2 0  std::codecvt<unsigned short,char,_Mbstatet> c 豐  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z 﨤  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �-  std::_Generic_error_category  翺  std::streampos  蔛  std::input_iterator_tag 2 PM  std::_Wrap<std::filesystem::_Dir_enum_impl> � �<  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � �<  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy � eK  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,int> > > > > > X ㎏  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> I 蒘  std::allocator_traits<std::allocator<std::pair<int const ,int> > > �Z*  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ' 〩  std::hash<enum nvrhi::ColorMask>  �/  std::codecvt_base  q*  std::bad_function_call O 鞱  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 �8  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ' �7  std::hash<std::filesystem::path>  @H  std::hash<unsigned int> 7 篠  std::allocator_traits<std::allocator<char16_t> > "    std::_Asan_aligned_pointers 4 8  std::_Ptr_base<donut::engine::SceneGraphNode> 4 {A  std::pair<bool,donut::math::vector<float,3> > � 8Q  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F 窼  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � :N  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> . �   std::array<nvrhi::BindingLayoutItem,16> $ zH  std::hash<enum nvrhi::Format>  朞  std::numeric_limits<int> �肒  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E �;  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O �;  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U �;  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 疎  std::_String_val<std::_Simple_types<char> > 9 岶  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access 
 !   wint_t & 頠  $_TypeDescriptor$_extraBytes_40 # �%  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  I!  nvrhi::BindingSetDesc  禨  nvrhi::SubresourceTiling $ =&  nvrhi::GraphicsPipelineHandle  %   nvrhi::ResourceType  u   nvrhi::ObjectType ) f"  nvrhi::RefCountPtr<nvrhi::IShader>  1"  nvrhi::InputLayoutHandle   X#  nvrhi::IndexBufferBinding   睸  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �   nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " ;   nvrhi::VulkanBindingOffsets  x#  nvrhi::GraphicsState /   nvrhi::static_vector<nvrhi::Viewport,16>  "  nvrhi::ShaderDesc  a$  nvrhi::IComputePipeline : 0$  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  #  nvrhi::Rect  !  nvrhi::BindingSetItem $ �   nvrhi::BindingLayoutItemArray ) 琒  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  �#  nvrhi::IGraphicsPipeline ! &  nvrhi::ShaderLibraryHandle  J  nvrhi::FramebufferInfoEx  p"  nvrhi::IShader  o  nvrhi::TextureDesc  "!  nvrhi::ISampler ! G#  nvrhi::VertexBufferBinding !  #  nvrhi::ComputePipelineDesc  _  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # B&  nvrhi::MeshletPipelineHandle  P  nvrhi::Format  8$  nvrhi::DrawArguments  {$  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + N  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �!  nvrhi::static_vector<nvrhi::BindingSetItem,128>  K   nvrhi::BindingLayoutDesc   R  nvrhi::SamplerAddressMode  �&  nvrhi::IDevice ! �"  nvrhi::BindingLayoutHandle ! �!  nvrhi::BindingSetItemArray  琒  nvrhi::DeviceHandle   僑  nvrhi::TiledTextureRegion  �$  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & �!  nvrhi::VariableRateShadingState  S  nvrhi::IStagingTexture . 1"  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " 6  nvrhi::ShaderSpecialization 8 -  nvrhi::ShaderSpecialization::<unnamed-type-value>  R  nvrhi::TextureDimension 0 �"  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' j$  nvrhi::DispatchIndirectArguments  "&  nvrhi::SamplerHandle * I$  nvrhi::DrawIndexedIndirectArguments # ?(  nvrhi::DescriptorTableHandle  0&  nvrhi::TimerQueryHandle 2 ?(  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �   nvrhi::BindlessLayoutDesc    nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! 8#  nvrhi::MeshletPipelineDesc 9 �   nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �%  nvrhi::HeapHandle # @&  nvrhi::ComputePipelineHandle  vS  nvrhi::PackedMipDesc  x  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  �  nvrhi::IResource  �#  nvrhi::IBindingSet  rS  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - !S  nvrhi::RefCountPtr<nvrhi::IBindingSet> * 
&  nvrhi::SamplerFeedbackTextureHandle # �!  nvrhi::SinglePassStereoState % ;!  nvrhi::ISamplerFeedbackTexture  �%  nvrhi::CommandQueue  A  nvrhi::BlendFactor  '&  nvrhi::EventQueryHandle  0   nvrhi::BindingLayoutItem  ;&  nvrhi::FramebufferHandle 1 @  nvrhi::static_vector<enum nvrhi::Format,8>  RS  nvrhi::BufferHandle  #  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  絉  nvrhi::TextureHandle  nS  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  hS  nvrhi::IMessageCallback  p  nvrhi::BlendState & U  nvrhi::BlendState::RenderTarget 3 �#  nvrhi::static_vector<nvrhi::IBindingSet *,5> " "  nvrhi::GraphicsPipelineDesc H �"  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) RS  nvrhi::RefCountPtr<nvrhi::IBuffer> $ ,S  nvrhi::TiledTextureCoordinate  (S  nvrhi::IHeap # u  nvrhi::FramebufferAttachment  �#  nvrhi::BindingSetVector  !S  nvrhi::BindingSetHandle ( 鸕  nvrhi::SamplerFeedbackTextureDesc ! �"  nvrhi::BindingLayoutVector " �%  nvrhi::StagingTextureHandle  �  nvrhi::Object  ;"  nvrhi::IInputLayout  z  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  v  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags ! +  nvrhi::rt::GeometrySpheres # 蒖  nvrhi::rt::ShaderTableHandle +   nvrhi::rt::OpacityMicromapUsageCount $ �$  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   E&  nvrhi::rt::PipelineHandle ! E  nvrhi::rt::AffineTransform & �$  nvrhi::rt::PipelineHitGroupDesc  >  nvrhi::rt::GeometryLss 3 馬  nvrhi::rt::cluster::OperationBlasBuildParams . 鞷  nvrhi::rt::cluster::OperationMoveParams ( 鏡  nvrhi::rt::cluster::OperationDesc 3 鉘  nvrhi::rt::cluster::OperationClasBuildParams , 逺  nvrhi::rt::cluster::OperationSizeInfo * 跼  nvrhi::rt::cluster::OperationParams  G  nvrhi::rt::GeometryType ' X&  nvrhi::rt::OpacityMicromapHandle  a  nvrhi::rt::GeometryDesc - f  nvrhi::rt::GeometryDesc::GeomTypeUnion % n  nvrhi::rt::OpacityMicromapDesc #   nvrhi::rt::GeometryTriangles  -!  nvrhi::rt::IAccelStruct # 桼  nvrhi::rt::AccelStructHandle  �%  nvrhi::rt::IShaderTable ' �%  nvrhi::rt::DispatchRaysArguments  �%  nvrhi::rt::State     nvrhi::rt::GeometryAABBs  �$  nvrhi::rt::PipelineDesc  蠷  nvrhi::rt::IPipeline  e&  nvrhi::CommandListHandle # @$  nvrhi::DrawIndirectArguments ! 臨  nvrhi::TextureTilesMapping  E  nvrhi::HeapDesc  �&  nvrhi::ICommandList  �  nvrhi::BufferDesc  (  nvrhi::IDescriptorTable * 絉  nvrhi::RefCountPtr<nvrhi::ITexture>  V$  nvrhi::ComputeState 2 桼  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �#  nvrhi::IFramebuffer    nvrhi::Viewport  �!  nvrhi::RenderState  f"  nvrhi::ShaderHandle  �  nvrhi::ITexture  pR  nvrhi::ITimerQuery  �-  __std_win_error  �.  lconv   俀  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t    timespec  z7  __std_fs_file_id 
 !   _ino_t ' c7  __std_fs_create_directory_result  !   uint16_t  �-  __std_fs_stats * �8  donut::engine::SkinnedMeshReference ! +9  donut::engine::SceneCamera $ �?  donut::engine::ICompositeView  �?  donut::engine::IView $ �9  donut::engine::SceneGraphNode 0 �9  donut::engine::SceneGraphNode::DirtyFlags " �8  donut::engine::MeshInstance   	@  donut::engine::PlanarView ) �8  donut::engine::SkinnedMeshInstance   +=  donut::engine::SceneGraph > _=  donut::engine::ResourceTracker<donut::engine::MeshInfo> ( �;  donut::engine::AnimationAttribute $ 8  donut::engine::SceneGraphLeaf ! c(  donut::engine::BufferGroup  烞  donut::engine::Material * eR  donut::engine::Material::HairParams 0 aR  donut::engine::Material::SubsurfaceParams ' B  donut::engine::PerspectiveCamera  E9  donut::engine::Light ' �7  donut::engine::SceneContentFlags  �(  donut::engine::MeshInfo & V9  donut::engine::DirectionalLight & �;  donut::engine::SceneGraphWalker ( �(  donut::engine::animation::Sampler ) ]R  donut::engine::animation::Keyframe ) `)  donut::engine::animation::Sequence  �(  donut::engine::MeshType  f9  donut::engine::SpotLight & �'  donut::engine::DescriptorHandle , 
(  donut::engine::DescriptorTableManager B �'  donut::engine::DescriptorTableManager::BindingSetItemsEqual B �'  donut::engine::DescriptorTableManager::BindingSetItemHasher % M(  donut::engine::VertexAttribute 0 �;  donut::engine::SceneGraphAnimationChannel % t   donut::engine::DescriptorIndex > E=  donut::engine::ResourceTracker<donut::engine::Material>   v9  donut::engine::PointLight ) e<  donut::engine::SceneGraphAnimation $ z  donut::app::ThirdPersonCamera 2 k  donut::app::ThirdPersonCamera::MouseButtons 6 g  donut::app::ThirdPersonCamera::KeyboardControls $ �  donut::app::FirstPersonCamera 2 �  donut::app::FirstPersonCamera::MouseButtons 6 �  donut::app::FirstPersonCamera::KeyboardControls # �  donut::app::SwitchableCamera  H  donut::app::BaseCamera  B  donut::math::quat  釧  donut::math::float4x4 " 9  donut::math::vector<bool,4>  �  donut::math::float3  X  donut::math::affine3  HA  donut::math::float2  �9  donut::math::dquat # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane  ":  donut::math::daffine3  :  donut::math::double3 # �  donut::math::vector<float,4> $ :  donut::math::vector<double,3>    donut::math::frustum $    donut::math::frustum::Corners # �  donut::math::frustum::Planes % TR  donut::math::matrix<float,3,4> $ :R  donut::math::vector<double,4>  �  donut::math::float4 & C  donut::math::matrix<double,3,3> % 釧  donut::math::matrix<float,4,4> # X  donut::math::affine<float,3>   o8  donut::math::box<float,3> " �  donut::math::vector<bool,2>  �  donut::math::float3x3  o8  donut::math::box3 % �  donut::math::matrix<float,3,3> "   donut::math::vector<bool,3> # HA  donut::math::vector<float,2> $ ":  donut::math::affine<double,3> & �9  donut::math::quaternion<double> % B  donut::math::quaternion<float> M ]  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  H  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; R  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t  �  terminate_handler  観  _s__RTTIBaseClassArray 
   ldiv_t  �-  __std_fs_file_flags  �.  _Cvtvec - 烸  $_s__RTTIBaseClassArray$_extraBytes_24  綫  _CatchableTypeArray  �-  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  鬛  _PMD      uint8_t     type_info ' 嶲  _s__RTTIClassHierarchyDescriptor  t   errno_t  �-  __std_fs_reparse_tag    _lldiv_t  7  __std_type_info_data & 嘠  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  !.  __std_fs_convert_result  �-  __std_fs_stats_flags  観  __RTTIBaseClassArray  �  __crt_locale_data_public - 蔘  $_s__CatchableTypeArray$_extraBytes_24 & 琎  $_TypeDescriptor$_extraBytes_25 % 嶲  __RTTIClassHierarchyDescriptor  �.  _Collvec   �6  __std_fs_volume_name_kind     __time64_t  U  FILE 3 螿  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  綫  _s__CatchableTypeArray  _7  __std_fs_remove_result - 決  $_s__RTTIBaseClassArray$_extraBytes_16 -   $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �-  __std_fs_file_attr  =  __std_exception_data 
 u   _dev_t  �6  __std_ulong_and_error    lldiv_t    _ldiv_t    _timespec64     intptr_t  u   uint32_t 
 U  _iobuf  Q  __crt_locale_pointers �         ;o屮G蕞鍐剑辺a岿;q琂謇:謇  I    zY{���睃R焤�0聃
扨-瘜}  �    o藾錚\F鄦泭|嚎醖b&惰�_槮  �    鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠     yk"&�棎墑�T-亴鏏愐煋搂脠+]�  O   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   f扥�,攇(�
}2�祛浧&Y�6橵�  �   曀"�H枩U传嫘�"繹q�>窃�8     [届T藎秏1潴�藠?鄧j穊亘^a  ]   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   穫農�.伆l'h��37x,��
fO��     譫鰿3鳪v鐇�6瘻x侃�h�3&�  [   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   天e�1濎夑Y%� 褡\�Tā�%&閜�  1   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  ~   dhl12� 蒑�3L� q酺試\垉R^{i�  �   @陉,m="禼U�k軮�!�2��"T劷wV竡  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  5   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  r   蜅�萷l�/费�	廵崹
T,W�&連芿  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  8   D���0�郋鬔G5啚髡J竆)俻w��  �   匐衏�$=�"�3�a旬SY�
乢�骣�  �   悯R痱v 瓩愿碀"禰J5�>xF痧  !   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  j   矨�陘�2{WV�y紥*f�u龘��  �   _O縋[HU-銌�鼪根�鲋薺篮�j��  �   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  M   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   蠯3
掽K謈 � l�6襕鞜��H#�  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  (	   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  q	   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �	   *u\{┞稦�3壅阱\繺ěk�6U�  �	   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  !
    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  _
   �+KcE膒誙嶚@4�V亲圙�i"cc壟f�-  �
   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  �
   鏀q�N�&}
;霂�#�0ncP抝     �7頔碠<晔@岙�撁k4統N絠熙鶳 �  A   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  �   猯�諽!~�:gn菾�]騈购����'  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  =   v-�+鑟臻U裦@驍�0屽锯
砝簠@  x   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  6
   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  u
   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �
   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�     癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  N   5�\營	6}朖晧�-w氌rJ籠騳榈  �   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜      }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  c   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�  %    I嘛襨签.濟;剕��7啧�)煇9触�.  e   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  ?   �暊M茀嚆{�嬦0亊2�;i[C�/a\  s   +椬恡�
	#G許�/G候Mc�蜀煟-  �   �"睱建Bi圀対隤v��cB�'窘�n     A縏 �;面褡8歸�-構�壋馵�2�-R癕  D   妇舠幸佦郒]泙茸餈u)	�位剎  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   靋!揕�H|}��婡欏B箜围紑^@�銵     矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  <   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   �颠喲津,嗆y�%\峤'找_廔�Z+�     �	玮媔=zY沚�c簐P`尚足,\�>:O  T   t�j噾捴忊��
敟秊�
渷lH�#  �   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  %   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  f   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  $   繃S,;fi@`騂廩k叉c.2狇x佚�  m   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇     揾配饬`vM|�%
犕�哝煹懿鏈椸  D   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   豊+�丟uJo6粑'@棚荶v�g毩笨C     桅棙�萑�3�<)-~浰-�?>撎�6=Y}  Y   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   L�9[皫zS�6;厝�楿绷]!��t  /    d蜯�:＠T邱�"猊`�?d�B�#G騋  k   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  Q   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG     煋�	y鋵@$5х葑愔*濋>�( 懪銳  A   v峞M� {�:稚�闙蛂龣 �]<��  �   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  ;   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   郖�Χ葦'S詍7,U若眤�M进`     嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  ^   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  9   鹴y�	宯N卮洗袾uG6E灊搠d�  �   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G     �0�*е彗9釗獳+U叅[4椪 P"��  L   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�      +4[(広
倬禼�溞K^洞齹誇*f�5  n    $^IXV嫓進OI蔁
�;T6T@佮m琦�  �    +YE擋%1r+套捑@鸋MT61' p廝 飨�  �    l籴靈LN~噾2u�< 嵓9z0iv&jザ  8!   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  o!   �
bH<j峪w�/&d[荨?躹耯=�  �!   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �!   =J�(o�'k螓4o奇缃�
黓睆=呄k_  ("   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  f"   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �"   交�,�;+愱`�3p炛秓ee td�	^,  �"   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  $#   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  ]#   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �#   _臒~I��歌�0蘏嘺QU5<蝪祰S  �#   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  =$   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  u$   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �$   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  
%   �(M↙溋�
q�2,緀!蝺屦碄F觡  Y%   G�膢刉^O郀�/耦��萁n!鮋W VS  �%   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �%   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  "&   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  `&   c�#�'�縌殹龇D兺f�$x�;]糺z�  �&    狾闘�	C縟�&9N�┲蘻c蟝2  �&   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  /'   �'稌� 变邯D)\欅)	@'1:A:熾/�  x'   副謐�斦=犻媨铩0
龉�3曃譹5D   �'   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �'   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  7(   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   8        H  X     H  h     H       H    �  �  	  �  �  �  �  �  �  �  �  �  �  �  �  �  �  f   �  �  g   �  �  �   �  �    �  �      h  |     h  }   B    a   E    �   N    ^  P    l  U    �  V    �  c  �  �  d  �  �  e  8  _   Q  �  �  R  �  �  S  �  �  T  �  �  V  �  5  \       ]  �  ?	  ^  �  Q   a  �  �   c  h  �   d  h  �   e  h  �   j  �  Q     �  t    �  �    �  d    �  Z    �  t  "  �  L  #  �  Q	  $  h  �   %  h  �   &  h  �   '  h  �   +  �  %   s  �  z  u  h  �   �  �    �  �  �   �  �  �   �  �  @   �  �  �   �  �  �   7  �  �   8  h  T  :  �  �   ;  �  /  <  �  i  =     �  >  �  �   ?  0  7  A  0  7  B  �  �   C  �  �   D  �  �   E  �  �   F  �  :   G  �  �   I  �  �   J  �  �   K  �  +  L  8  �  M  �  �   N  �
  �  P  �  �  �     �   �       �  �  :  �  �  D  �  �  *	  �  0  �   �  0  �   �  �     �  �  5  �  8  !   '  �  n  (  �  d  �  h  9  �  h  k  �     �  �  0  �   �  h    �  h    �  h  L  �  0  �   �  �  6   �  �  4   �  �  '  �  8  �   �  �  O       �      �  $	  �  �  �  �  h  �   �  h  �   �  �  M  �   t(   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\RTXPT\External\Donut\include\donut\app\Camera.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\src\app\Camera.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime �       L�  ,[  �   0[  �  
 踈  �   運  �  
 a_      e_     
 鯅      鶌     
    f i吓 �6qN櫿_7镾%   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_app.pdb 谐Y>Y7?樰�=      �?                  �?                  �?    3繪+罤�L嬞H堿H兟H堿A�   H堿堿 H嬃I岺D  A�   ff�     驛 �YBX � 驜L �YJX润��Y�X馏 H兝I冮u綡兟H冮I冭I冴u淚嬅�   �   @  I G            �       �   �        �donut::math::operator*<float,3,3,3> 
 >H   a  AK         
 >H   b  AP          M        �     M        �   (
 N N                        H & h   +  ,  [  \  �  �  �  �      H  Oa     H  Ob     	B  Oresult  O�   `           �   h  	   T       9 �    : �   9 �   : �0   < �@   > ��   ; ��   ? ��   @ �,   N   0   N  
 k   N   o   N  
 �   N   �   N  
 T  N   X  N  
 H嬃埔 Y�BY麦YR A�Q �   �   �   G G            &   
   %   �        �donut::math::operator*<float,3,3> 
 >H   a  AK        & 
 >@    b  A�         
                         H 
 h   �      H  Oa     @   Ob     	B  Oresult  O�               &   h             �,   Q   0   Q  
 i   Q   m   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 H塡$WH冹@M嬋)t$0H孂荄$     H�$M嬝L+蒐岯W繦�$W蒆嬟$I峇A�   L$D  驛X   驛`A(ff�     (�(捏BY(腕BYD�X�Y�X畜X洋H兝H冮u薎兝H冴I冮I冴u欝s(�S$(铙AYk(迡D$ �c,(麦AY(蘃媆$P驛YK驛Ys�X梵AY[(麦AYC驛YS�X�(腆AYc 驛YK�X伢AXk$$�X蝮X�L$驛X[(�X�O塆 H嬊塍AXs,�o$�w,(t$0H兡@_�   �   4  E G            n     �   9        �donut::math::operator*<float,3> 
 >�   a  AI  8     �  AK        8 
 >�   b  AP        $  AS  $     J% M        $  ��[

	 N M        �  �" N* M        �  ��6@ N- M        �  


5 M        �  


' M        �  


 N N N @                     @ > h   �  $  (  �  +  ,  [  \  �  �  �  �  �  �   X   �  Oa  `   �  Ob  P   
@  Oresult  O�   �           n  h  
   t       �  �
   �  �   �  �   �  �!   �  �$   �  �5   �  �8   �  ��   �  ��   �  �M  �  �P  �  �c  �  �,   @   0   @  
 g   @   k   @  
 w   @   {   @  
 �   @   �   @  
 �   @   �   @  
 H  @   L  @  
 H冹h驛(H嬃驛`(�)t$P�2)|$@驛xD)D$0(象DBD)L$ A(伢EHD)T$A(馏DR驛Y荔Y諨)$驞Z�\畜AY�(捏Y蒹AY麦\�(腆AY梭\蠥(馏Y企X伢A(翧(芋Y求Y阵X袤EY貯(鼠DY阵AY蒁(L$ �\�(求AYY企Y�X�(捏Y�(t$P驛Y繢(D$0驞X泽X洋DX�(|$@�\畜E\覦($�Q驞QD(T$H兡h�   �   Y  C G            5  f   �   �        �donut::math::operator*<float> 
 >礗   a  AK        5
 >礗   b  AP        5 M        �  ��AA
 >@    w  A�   �      
 >@    x  A�   �     c 
 >@    y  A�         
 >@    z  A        N h                      H 
 h   �   x   礗  Oa  �   礗  Ob  O   �   P           5  8     D       �  �   �  ��   �  ��   �  ��   �  �  �  �*  �  �,   U   0   U  
 e   U   i   U  
 �   U   �   U  
 �   U   �   U  
 �   U   �   U  
 �   U   �   U  
   U     U  
 p  U   t  U  
 H冹�bH嬃�j(泽AYP(腕AYH)4$�2(�(摅AY 驛Yp驛YX�X�(捏AY`驛Y@�X�(腕AYh �X趔AYH�X伢�X躞X袤q(4$�YH兡�   �   �   C G            �   '   �   �        �donut::math::operator*<float> 
 >J   a  AK        � 
 >H   b  AP        �                        H 
 h   (   (   J  Oa  0   H  Ob      �  Oresult  O�   8           �   h     ,       k �   m �	   p ��   q �,   O   0   O  
 e   O   i   O  
 �   O   �   O  
 �   O   �   O  
 H冹(�bH嬃�j(泽AYP(腕AYH )t$�r)<$�:(�(唧AY 驛YX�X�(企AY@0�X�(腕AYH$�X�(捏AY@�X伢(�(企AY@4驛Yx驛YP�X�(腕AYh,驛YH(�X�(捏AY@驛Y`�X畜Y(企AYp<驛Y@8�XX洋XX畜X�(t$�Q�y(<$H兡(�   �   �   C G            �   1   �   H        �donut::math::operator*<float> 
 >�   a  AK        � 
 >b   b  AP        �  (                      @ 
 h   *   8   �  Oa  @   b  Ob  0   �  Oresult  O�   8           �   h     ,       � �   � �	   � ��   � �,   F   0   F  
 e   F   i   F  
 �   F   �   F  
 �   F   �   F  
 �H嬃驛X �J驛XH��B驛X@�I�J驛XH�A�B驛X@�I�J驛XH�A�B驛X@�I�J驛XH�A�B 驛X@ �I�A �   �   �   G G            �       �   �        �donut::math::operator+<float,3,3> 
 >H   a  AK        � 
 >H   b  AP        �                         H 
 h   �      H  Oa     H  Ob     	B  Oresult  O�               �   h             �,   R   0   R  
 i   R   m   R  
 �   R   �   R  
 �   R   �   R  
 (罤嬃�Y�YI��I�   �   �   D G                      E        �donut::math::operator*=<float> 
 >�   a  AJ         
 >@    b  A�                                  H     �  Oa     @   Ob  O�                  �            �  �,   E   0   E  
 f   E   j   E  
 �   E   �   E  
 �   E   �   E  
 �H嬃�X��J�XI�I�   �   �   D G                      C        �donut::math::operator+=<float> 
 >�   a  AJ         
 >�   b  AK                                 H     �  Oa     �  Ob  O�                  �            �  �,   D   0   D  
 f   D   j   D  
 �   D   �   D  
 �   D   �   D  
 �H嬃�X��J�XI�I�B�XA�A�   �   �   D G            .       -   >        �donut::math::operator+=<float> 
 >}   a  AJ        . 
 >J   b  AK        .                         H     }  Oa     J  Ob  O�               .   �            �  �,   B   0   B  
 f   B   j   B  
 �   B   �   B  
 �   B   �   B  
 �H嬃�\�I��\J�A�I�\B�A�   �   �   D G            .       -   J        �donut::math::operator-=<float> 
 >}   a  AJ        . 
 >J   b  AK        .                         H     }  Oa     J  Ob  O�               .   �            �  �,   H   0   H  
 f   H   j   H  
 �   H   �   H  
 �   H   �   H  
 �H嬃�Q�^馏^洋�A�^馏Q�A�   �   �   D G            ,       +   M        �donut::math::operator/=<float> 
 >}   a  AJ        , 
 >@    b  A�         ,                         H     }  Oa     @   Ob  O�               ,   �            �  �,   K   0   K  
 f   K   j   K  
 �   K   �   K  
 �   K   �   K  
 (罤嬃	评 ^�	�   �   �   D G                      I        �donut::math::operator/=<float> 
 >蹵   a  AJ         
 >@    b  A�         	                         H     蹵  Oa     @   Ob  O�                  �            �  �,   G   0   G  
 f   G   j   G  
 �   G   �   G  
 �   G   �   G  
 D�I钩     禕L3�禞MI3繧H3�禞IH3罥�   �   '  C G            7       6            �std::_Fnv1a_append_value<int> 
 >e   _Val  AJ          >�*   _Keyval  AK        7  M        �  @2
 >#    _Val  AH          AP         N                        H� 
 h   �      e  O_Val     �*  O_Keyval  O �   0           7   �     $       $	 �    &	 �6   '	 �,   V   0   V  
 h   V   l   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
 <  V   @  V  
 D�I钩     H�%#"勪滘薍嬔L3�禔禝MI3繧H3�禞IH3罥�   �   /  D G            D       C   �        �std::_Hash_representation<int>  >�*   _Keyval  AJ          AK       )  M            ( M        �  @
 >#    _Val  AH  -       AP         N N                        H�  h   �         �*  O_Keyval  O �   @           D   �     4       *	 �    +	 �   *	 �   +	 �C   ,	 �,   M   0   M  
 l   M   p   M  
 |   M   �   M  
 �   M   �   M  
 �   M   �   M  
 D  M   H  M  
 3荔	H堿堿H堿堿H嬃�I�I �   �   �   D G            "       !   �        �donut::math::diagonal<float,3> 
 >@    a  A�         "  M        u   
 M        '  P*6 N N                        H  h   '  u      @   Oa  O�   0           "   h     $       @ �    A �!   B �,   P   0   P  
 f   P   j   P  
 �   P   �   P  
 H塡$WH冹0H孃H嬞荄$     L�
    L�    3襀��    H�    H荂    H吚tH婳H吷t�AH�H婫H塁H嬅H媆$@H兡0_�   �   "   �   ,   X      �   �  | G            j   
   _   P        �std::dynamic_pointer_cast<donut::engine::PerspectiveCamera,donut::engine::SceneCamera>  >�   _Other  AK        
  AM  
     \ 
 >vB    _Ptr  AH  0     (  AH \       M        �  D M        �  DM M        '  D	 M        �  M N N N N 0                    H� " h   �    �  �  '  �  �   H   �  O_Other  O �   @           j   �     4       � �   � �?   � �D   � �\   � �,   L   0   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 �  L   �  L  
 �A��I�Y殷Y荔Y审X�W荔X�.聎W荔Q旅(麻    6   \      �     B G            :       5   K        �donut::math::length<float,3> 
 >J   a  AJ        :  M          %
 >@    _Xx  A�   %       N M        �   ! M        �   ! N N                        H  h     �  �      J  Oa  O  �               :   �            + �,   I   0   I  
 d   I   h   I  
 �   I   �   I  
 ,  I   0  I  
 �I��Q�Y荔Y审Y殷X馏X旅   �   �   I G            #       "   �        �donut::math::lengthSquared<float,3> 
 >J   a  AJ        #  M        �  "  N                        H 
 h   �      J  Oa  O  �               #   �            ' �,   T   0   T  
 k   T   o   T  
 �   T   �   T  
 @SH冹P)t$@H嬞�2)|$0(煮z(求Y煮Y荄)D$ 驞B�X蠥(润AY�W荔X�.聎	W荔Q码(妈    �^餒嬅�^D^荔3(t$@�{(|$0驞CD(D$ H兡P[肻   \      �     E G            �   0   t   ;        �donut::math::normalize<float,3> 
 >J   a  AK        `  AK `     5  M        �  `

 M        �  p	
 >@    _x  A�   d       >@    _y  A�   k       >@    _z  A  p       N N M        K  	 M          J >@    _Xx  A�   J       A�  `     5  N M        �  	 M        �  	 N N N P                     H  h     �  �  �  K  �   h   J  Oa  O  �               �   �            / �,   A   0   A  
 g   A   k   A  
 w   A   {   A  
 �   A   �   A  
 �   A   �   A  
   A     A  
 Z  A   ^  A  
 j  A   n  A  
   A     A  
 H冹驛 H嬃�(捏Ah)4$(腕Ap�Y麦Y鼠(企Y麦I(腕A(捏Z�Y皿Y梭A(企Y皿I�A�J�Y耋Y狍Y轶a�i�q (4$H兡�   �   f  J G            �      �   �        �donut::math::outerProduct<float,3,3> 
 >J   a  AK        � 
 >J   b  AP        � ) M        �  	


 >@    a  A�   q     $  A�        >  A�   N     #  N                       H  h   �  �  +  [  �  �   (   J  Oa  0   J  Ob      	B  Oresult  O  �   @           �   h     4       L �   O �	   P �   O ��   Q �,   S   0   S  
 l   S   p   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
 |  S   �  S  
 H嬆H塜WH侅�   )p�(�)x�(駾)@菻嬟D)H窰孂D)P―)X楧)`圖)l$pD)t$`D)|$P�    D(�(企D�$   �    驞s(梵CA(审D    D(畜D;驞\朋DY蠨(�W垠�$  �Y薃(AY�E(鐴(润EY逧(骟�$  驞Y囿X�(珞EY伢AY鳤(企AY艫(左AY譇(铙AY郃(趔DY润DY牦�$   (麦Y牦YC驞X轶EY囿X塍\伢AY�(�W殷AY企AX塍X逧W鯝(DC�\畜AY鶤(求X�$   �Y馏AX芋DY馏AX企EX企AX腖崪$�   E祈��狍DX捏D眢AY�3荔鳨祈��企D塍D\馝祈'��$  驛X�$  �'驞闑祈9D/驞X鲶AX审A��9�O H塆$塆,H嬊I媅 A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�D(t$`D(|$PI嬨_肙   [   e   Y   �   �      �   V  B G            \  N   �  @        �donut::math::rotation<float> 
 >J   axis  AI  %     � AK        %  >@    radians  A�         S  >@     cosTheta  A�   r     �  B  �     � >�    mat  E6u  �     � D     >@     sinTheta  A�   S       B   d     �. M        $  	仛
 N! M        �  �,/
( #
3 N$ M        �  �"	"$	C	 N M        �  w0	 N0 M        �  ��'
/ N" M        �  
{			  M        �  
{			 N N M        '  i N M          W
	/ N M          	;
 N �                     @ J h       �  �  f  $  '  u  �  +  [  �  �  �  �  �  �     J  Oaxis    @   Oradians      �  Omat  O  �             \  h     �       t �   v �   t �N   v �W   w �Z   v �d   w �i   z �o   w �r   z �w   � ��   z ��   � ��   w ��   � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �,   C   0   C  
 g   C   k   C  
 w   C   {   C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
   C     C  
 )  C   -  C  
 l  C   p  C  
 H嬆H塜WH侅�   )p鐷嬟�2H孂)x伢=    D)@润Y鱀)H窪)P�(畦    �D$ (畦    �sEW殷Y鱄荄$(    �D$$(畦    D(�(畦    �sD(荔Y�(畦    (�(畦    (螸岲$ 驛Y蒆峊$0H嬒(�(囿AY�(痼A\鼠AY耋AY釧(梵AY闍(洋AY隗\腕AY殷X篌AY荔X\腆X躞XL$0(梭X鼠A\蝮A\AX鼠t$<�|$8�\润L$4�    (|$pL崪$�   I媅H嬊A(s餎(C蠩(K繣(S癐嬨_�%   �   @   Y   N   [   r   Y   ~   [   �   Y   �   [   ;  U      �   �  F G            l  <   D  L        �donut::math::rotationQuat<float>  >J   euler  AI       : AK          >@     sinHalfY  A  �     �  >B   quatZ  C�      �       >@     sinHalfX  A�   R       >@     cosHalfY  A  z     �  >B    quatX  D     >@     cosHalfX  A�   D     	  >@     cosHalfZ  A�   �     (  >@     sinHalfZ  A�   �     #  A�   �     
  M        �  ��u! M        �  ��

 >@    w  A�          A�   �      
 >@    x  A�   4     
 >@    y  A�       % 
 >@    z  A�       >  N N M        �  �� N M        �  	_ N M          �� >@    _Xx  A�   �     6  N M          ��( N M          n >@    _Xx  A�   _     (  N M          z
 N M          < >@    _Xx  A�   2     %  N M          J N
 Z   �   �                     H  h       �  �   �   J  Oeuler      B  OquatX  O   �   �           l  8     �       � �   � �J   � �R   � �_   � �h   � �n   � �z   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �?  � �,   J   0   J  
 l   J   p   J  
 |   J   �   J  
 �   J   �   J  
 �   J   �   J  
 �   J   �   J  
 	  J   
  J  
 D  J   H  J  
 g  J   k  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
   J     J  
   J   "  J  
 :  J   >  J  
 V  J   Z  J  
 �  J   �  J  
   J     J  
 k  J   o  J  
   J     J  
 H�  �?3繦堿H茿  �?H堿茿   �?婤��A$堿,H嬃�   �   �   G G            3       2   8        �donut::math::translation<float,3> 
 >J   a  AK        3  M        $  0  N                        H  h   $  %  '  u      J  Oa     
@  Oresult  O �   8           3   h     ,       T �    U �/   V �2   W �,   ?   0   ?  
 i   ?   m   ?  
 �   ?   �   ?  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >    __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H      O__f  9(           O ,      0     
 g      k     
 w      {     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 !     %    
 1     5    
 A     E    
 �     �    
 H嬆H塜H塸H墄UH峢豀侅   D秮f  H嬞)p�W�)x�W跠)@菶W繢)H窪)P―(袳劺t/�剐    t"�櫊   驞仱   �\櫒   驞\伂   ��2覉懶   @2鲵仩   (=    �仺   D(象    �D$PW�买D$T�T$\E劺劌   .趜uD.聑剴   �箿   H峊$ (浊D$     �Y親峀$pH荄$$  �?W    �    驛Y鳫崜�   H峀$p HW=    D$0@ (�D$PL$@�    H嬓L岲$0H峂犺    @�8DHp �t$P秼b  |$0DL$@t$P勆u@2�@8籧  t_�儨   H峉t秲c  fn蒆峂�[蒮n�[殷Y润Y畜X审X殷\谚    H嬓L岲$0H峀$p�    @�8DHp A(襀峊$`H嬎�    @
|$`@
6�D$dL岲$0婦$lH峊$ H嬎�D$ )|$0D)L$@)t$P塂$(�    L崪$   I媅I媠I媨 A(s餉({郋(C蠩(K繣(S癐嬨]脮      �   �          C   2     M  C   ^  @   �  C   �  @        K         �   l  L G            �  G   O  F        �donut::app::FirstPersonCamera::Animate 
 >�   this  AI  &     5 AJ        &  >@    deltaT  A�         x\  A�  x    � > H  >{A    translateResult  D`    >X    cameraRotation  B0   �     �  >0     cameraDirty  AD  �     � >@     yaw  A�   �       >@     pitch  A�         M        B  "Y N M        d  �� M        $  ��	 N M        %  �� M        u  �� M        '  ��/ N N N N* M        E  亁'{ Z   @  9   >X   cameraRotation  C�       �     eP  � q  C�      �     Y� p  C�       q        C�      �    o  C�     �    y  C�      �    j  B0   �    � �   >0     cameraDirty  AE  �      AE �     
 >@     roll  A�   �      N M        �  ��	 N Z   @  @  9  C  D                        @ N h   �  �  E  Y  Z  ^  _  d  f  $  %  '  u  A  B  �  �  �   0  �  Othis  8  @   OdeltaT  `   {A  OtranslateResult  0   X  OcameraRotation  O�   �           �       �       �  �   �  �P   �  �Y   �  �{   �  �}   �  �   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �b  �  �s  �  �x  �  ��  �  �  �  �  �  �  �  �O  �  �,      0     
 q      u     
 �      �     
 �      �     
 �      �     
      	    
 +     /    
 I     M    
 i     m    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 1     5    
 A     E    
 `     d    
 �     �    
 H嫅   H呉u(8�0  uH呉u8�0  uH伭p  H吷tH�H�`0�   �   �  K G            5       4   b        �donut::app::SwitchableCamera::Animate 
 >�   this  AJ        (  AJ (     
  >@    deltaT  A�         5  >I    activeCamera  AJ (     
  M        U   
 M        �    M        Q  
  N N M        �  
 M        Q   N N N                        @  h   �  �  U  Q        �  Othis     @   OdeltaT  90       6   O   �   @           5        4       � �    � �(   � �-   � �4   � �,   <   0   <  
 p   <   t   <  
 �   <   �   <  
 �   <   �   <  
 �   <   �   <  
 �  <   �  <  
 �  <   �  <  
 H嬆H塜H塸WH侅�   �供   (�)p鐷嬞)x谼)@菵)H窪)P―)X楧)`圖)l$pD)t$`D)|$PtT��(  �\�0  ��,  �櫆   �丳  �Y梭\馏丳  �\�4  �Y泱X  �  �丠  (腕Y
    3荔Y-    �=    D(馏Y塡  �Yー  H墎\  �X塗  驞Y乆  墎X  �X〥  驞X丳  �__朋D丳  �]=    �]丩  �筎  �Y=    �丏  (氰    ��$  (氰    驞Y    D(鳤(黎    D(鐰(黎    (餎W�W黎    D(�W黎    A(虯(諩(阵AY润EY�(伢AY�(�(DY鲶DY祗E\畜XAY伢DX耋Y企DX�(铙AY梵AXDX篌DX眢\D\阵E\痼E\�(求D\芋AY求�$  A(諨(垠AY畜EY贏(腕AY润D\伢D\隗D\貲(薍峊$ (皿EY蜨嬎�Y荅(怏AY蒹EY珞AYDX囿EY蠥(朋EY黧AY荄(塍EX鼠EX牦DX怏DX润DX矬D\狍D\象=    驟\�(茿(荏AY蹺(罙(袮(躞AY泽AY魽(弩EY罙(轶AY銩(润AY塍X误EY泱X审\罙(腕AY梭EY轶D$ (馏X麦\�(梭X误AX伢X荔X殷X审X垠D$$(捏\朋T$,�X祗X荔X眢D$((求\馏l$8�\D$0A(捏AX朋|$@驟\祗X荔EX眢D$4驞l$<�    �汥  �=    �t$8W唧DD$<(企DL$@A(梵Y肁(酘嬎�Y塍X�8  �Y泱Ch�X�<  �kl�X  �cp�D$ �T$$W求L$(W左儗   W象搻   �嫈   �D$,婦$4�儉   (艫莉Ct驞K|墐�   �    ��(  L崪$�   I媠 A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�D(t$`D(|$P��0  I媅I嬨_酶       �   	   �      &     >  �   N  Y   _  [   h  �   u  Y   �  [   �  Y   �  [   �  �   �  .   �     �        �   !	  L G            �  S   �  R        �donut::app::ThirdPersonCamera::Animate 
 >O   this  AI  $     � AJ        $  >@    deltaT  A�         ]  A�  �       >B   orbit  C     �    - C�      �    �  >�   targetRotation  C�           [  C     (    i  D     M        7  �(  N M        �  凖
 N M        �  冃	 
 >@    a  A�   �    �  N M        e  偪
��
I M        '  傑

(2



 >@    _m00  A�         A�   �    1 
 >@    _m01  A�   I     
 >@    _m02  A�   p    
 
 >@    _m10  A�   M    s 
 >@    _m11  A�   �     
 >@    _m12  A�   �     
 >@    _m20  A�   t    L 
 >@    _m21  A  �    
 >@    _m22  A�   �    E  N NR M        L  �:hyd
	<xdue����V^	 >@     sinHalfY  A�   �    [ >B   quatZ  C     �      >@     sinHalfX  A   p    b >@     cosHalfY  A  }    R  >@     cosHalfX  A�   R      >@     cosHalfZ  A  �    1  >@     sinHalfZ  A�   �    !  A�   �    0/ M        �  �
!V^	 M        �  俶	[	
 >@    w  A  U    &  A  {    
 
 >@    x  A  �    X 
 >@    y  A  �    +
 >@    z  A  �    \  N N) M        �  仭
SV	

	! M        �  仴	]
	
 >@    w  A�   �    ^  A      m 
 >@    x  A�       z 
 >@    y  A      � 
 >@    z  A      �  N N M        �  仼	 N M          	亹 N M          仠# N M          
乸 >@    _Xx  A  l    !  N M          亇 N M          丣 >@    _Xx  A�   B    �  N M          乕 N NS M        P  @BHh$xZ$	/Tt
	X M        F  ��K M        �  �" N M        �  ��K N N M        F  ��l M        �  �* N M        �  ��l N N M        B  U8 N N Z   Q  <   �                     @ N h       �  P  e  Y  ^  '  �  �  7  B  F  L  �  �  �  �      O  Othis    @   OdeltaT      �  OtargetRotation  O   �   0          �    #   $      � �   � �   � �S   � �:  � �B  � �J  � �Y  � �^  � �f  � �i  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �	  � �  � �  � �  � �#  � �(  � �[  � �m  � ��  � ��  � ��  � �,   (   0   (  
 q   (   u   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
   (     (  
 +  (   /  (  
 �  (   �  (  
 3  (   7  (  
 C  (   G  (  
 b  (   f  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (     (  
   (      (  
 ;  (   ?  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
 $  (   (  (  
 G  (   K  (  
 j  (   n  (  
 �  (   �  (  
 �  (   �  (  
 
  (     (  
   (     (  
 6  (   :  (  
 R  (   V  (  
 n  (   r  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
 (  (   ,  (  
 D  (   H  (  
 �  (   �  (  
 $  (   (  (  
 8	  (   <	  (  
 �供   (閠T��(  �\�0  ��,  �櫆   �丳  �Y梭\馏丳  �\�4  �Y泱X  �  �
    (阵Y    3荔Y-    (麦Y慭  �Y乆  墎X  �Yー  H墎\  �X丳  �X慣  �X〥  �丳  �丠  �_鼠_朋]
    �]丩  �塗  �丏  胐      o       y   	   �         �   �  Q G            �       �   P        �donut::app::ThirdPersonCamera::AnimateOrbit 
 >O   this  AJ        �  >@    deltaT  A�           A�  `       M        F  `m M        �  �� N M        �  `m N N M        F  �� M        �  �� N M        �  �� N N M        B  8 N                        H  h   Y  ^  B  F  �  �      O  Othis     @   OdeltaT  O �   �           �        �       l �    m �   o �$   p �,   r �D   o �L   r �P   s �`   ~ �h   y �s   � ��   � ��   � ��   | ��   ~ ��   | ��   ~ ��   | ��   ~ ��   � �,   -   0   -  
 v   -   z   -  
 �   -   �   -  
 �   -   �   -  
 �  -   �  -  
 @SH侅�   A H嬟2褹HAP D秮b  D$ L$0T$@E劺u8慶  tb�仠   H峇t秮c  H峀$PfAn�[蒮n�[殷Y润Y畜X审X殷\谚    H嬓L岲$ H崒$�   �    � HP C�H嬅KS$H伳�   [脇   C   �   @      �   i  P G            �   	   �   E        �donut::app::FirstPersonCamera::AnimateRoll 
 >�   this  AJ        X  AJ �       >X   initialRotation  AP        $  >X   cameraRotation  C�       
     �  q  C�           �  p  C�              C�      �       C�     �       C�      �       D     >0     cameraDirty  A        � : K 
 >@     roll  A�   {       M        A  ��	 M        �  ��	 N N Z   @  9   �                     H  h   Z  A  �  �  �   �   �  Othis  �   X  OinitialRotation      X  OcameraRotation  O   �   X           �        L       �  �	   �  �   �  �@   �  �H   �  ��   �  ��   �  ��   �  �,      0     
 u      y     
 �      �     
 �      �     
 �      �     
 �      �     
          
       $    
 4     8    
 H     L    
 v     z    
 �     �    
 �     �    
 H嬆H塜UH峢菻侅0  )p�(馏Y    H嬞)x谼)@菵)H窪)P―(袲)X樿    �籪   H崈�   D(豻D�恍    t6�摛   �嫭   �儬   �\洋\儴   �XP�X �P� A��E2繢垉�   �儬   �    (=    (蓑儴   驛\梭@D(求D(润AY�(怏Y狍DY�W审@W�.狍AY�硫D$T��\$P�L$\zuD.蓏u
t$P閸   �粶   H峊$ (浊D$     �Y訦峀$pH荄$$  �?W    �    驛Y鵋崜�   H峀$p HW=    D$0@ (�D$PL$@�    H嬓L岲$0H峂犺    8D@p 秼b  H嬅|$0DD$@t$P勆u8媍  t\���   H峉t秲c  fn蒆峂�[蒮n�[殷Y润Y畜X审X殷\谚    H嬓L岲$0H峀$p�    8D@p A(襀峊$`H嬎�    �８   L岲$0�摾   H峊$ �\T$l�d$ (荏\d$d欺UH嬎�\\$h驛Y芋AY泱XT$l驛Y垠Xd$d�X\$h�Ｄ   �浫   �撎   �兡   媰�   �兏   墐�   )|$0D)D$@)t$P�D$ 塂$(�    L崪$0  I媅A(s餉({郋(C蠩(K繣(S癊([營嬨]�      B   Z   �   �   �      `     e  C   �     �  C   �  @      C   2  @   O     �         �   �  R G            2  A     G        �donut::app::FirstPersonCamera::AnimateSmooth 
 >�   this  AI  %     � AJ        %  >@    deltaT  A�         F  >{A    translateResult  D`    >X   cameraRotation  C�       �     Z| |  C�      �     F�   C�       .    � �  B0   
    �  >@     dampenWeight  A  X     � >@     yaw  A�   O      >@     pitch  A�   n      M          A N M        C  �� N M        B  $c N  M        d  ��(	
 M        $  ��	= N M        %  �� M        u  �� M        '  �� N N N N M        E  �� N M        D  ��	
 N M        �  丆	 N M        �  俙

' M        �  倷 N M        �  倧 M        �  倧 >@    _x  A�   �      N N M        :  俙

 M        �  倲 >@    _x  A�   �      >@    _y  A�   �      >@    _z  A�   s    !  N N N& M        E  伬&{# Z   @  9   >X    cameraRotation  B0   �    
 >@     roll  A�         N Z   @  @  9  C  D   0                    @ j h     �  �  E  Y  Z  ^  d  f  $  %  '  u  �  �  �  :  A  B  C  D  E  �  �  �   @  �  Othis  H  @   OdeltaT  `   {A  OtranslateResult  0   X  OcameraRotation  O   �   p          2    +   d      �  �   �  �F   �  �T   �  �X   �  �Z   �  �c   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��    ��   �  ��    ��   �  ��   �  ��   �  ��   �  ��    ��   �  �   �
  �  �   �   �)   �3   �;  
 �K   �O  
 �n   ��   �B   �S   �[   �`   �h   �m   ��   ��   ��   ��   �   �,      0     
 w      {     
 �      �     
 �      �     
 �      �     
          
 '     +    
 ;     ?    
 b     f    
 �     �    
 �     �    
 i     m    
 �     �    
 �     �    
 �          
 i     m    
 �     �    
 �     �    
 H冹�Y憳   2�W�)4$W�W�8乨  t�Y    8乪  t�Y    8乗  t)(�(怏YA|(臧�Yat�Yix�X皿X泱X�(貈筣   �5    t2�At��IxW企Y�W误Y鼠X囿A|W企Y麦X轶X貈筞   t;�亴   ��墣   W企Y�W误Y鼠X囿仈   W企Y麦X轶X貈筟   t/(�(鼠Y亴   ��Y墣   �X�(麦Y仈   �X轶X貈筙   t/(�(鼠Y亐   ��Y墑   �X�(麦Y亪   �X轶X貈筜   t;�亐   ��墑   W企Y�W误Y鼠X囿亪   W企Y麦X轶X�(4$弪b�H嬄�ZH兡�'      7   �   w         �   �  W G            �     �  C        �donut::app::FirstPersonCamera::AnimateTranslation 
 >�   this  AJ        � >@    deltaT  A�           >�   cameraMoveVec " C�       �      - X % � % � -  C�       i      " C�      �     �  @ H 4 � 4 � @ " C�      �     � -  i  �  �  & C�      l     V/  p  �  �  $ " C�     �     @  x  �  �   C�     l     V >@     moveStep  A�        � >0     cameraDirty  A        � M        >  ] N M        �  C
 M        �  a >@    _x  A�   X     
  >@    _y  A�   ]       >@    _z  A�   N       N N M        >  �� N M        �  �� M        �  �� >@    _x  A�   �       A�  �      ) S ) � ) � )  N N M        7  } M        �  �� >@    _x  A�   �       >@    _y  A�   �       >@    _z  A�   �       N N M        >  �� N M        �  �� M        �  �� >@    _x  A�   �       A�  �     �  ) G ) � )  N N M        7  ��
 M        �  �� >@    _x  A�   �       >@    _y  A�   �       >@    _z  A�   �       N N M        >  � N M        �  �� N M        >  丩 N M        �  �4 N M        >  亴 N M        �  亪 M        �  亪 >@    _x  A�   �      A�  �      N N M        7  乴
 M        �  亖 >@    _x  A�   �      >@    _y  A�   �      >@    _z  A�   �      N N M        ?  
伀
 M        �  
伀
 N N                       @ . h
   �  �  Z  �  7  >  ?  �  �  �       �  Othis  0   @   OdeltaT  O �              �    !         i  �   k  �   n  �#   o  �+   q  �3   r  �;   t  �C   w  �Q   v  �S   w  �l   z  �}   }  ��   |  ��   }  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �
  �  �  �  �+  �  �4  �  �B  �  �D  �  �c  �  �l  �  �t  �  �v  �  ��  �  ��  �  ��  �  �,      0     
 |      �     
 �      �     
 �      �     
 �      �     
          
 %     )    
 I     M    
 q     u    
 �     �    
 �     �    
 �     �    
 A     E    
 ^     b    
 {         
 �     �    
 �     �    
 [     _    
 x     |    
 �     �    
          
          
 r     v    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 )     -    
 �     �    
 H嬆H塸H墄UH峢侅�   )x豀嬺W�H孂/�   僔  D)PD�$  A/��8  ��,  .�4  D)@菵)H竮u��2荔D�(  驞�0  E.葄
u劺呯  �躬   勞  �丏  L崄�   H墱$�   H峂�)�$�   H峌珞5    �E颒荅�    荅�  �?�    �]泱EX荔�4  驟X审U唧X�(芁崌�   �^親峌譎峂黧D^�   驞^�   驛^鼠^垠\馏U唧D\企]泱D\误U矬]篌E垠�,  �X荔DE左DM珞A^麦\痼u腓    L崌�   H峌鏗峂阻    �e黧^eH嫓$�   �E左^E泱V�^�\囿E垠^E泱DM�(腆D^M驞�8  驞\润Y�(捏Y泱Y驞\畜�<  �\馏D�8  ��<  �嘆  �\捏嘆  �卡   勧   �v驞F (误Y蜛(荔AY荔X象X�W�.羨	W荔Q岭(凌    .莦
u�v驞FA(�(误Y误AY荔X象X�W�.羨	W荔Q岭(凌    �
    �^痼^D^荔AY耋AYEY馏Y耋YDY馏AX蝮X�<  驞X嘆  ��8  ��<  驞嘆  隣�VA(馏^A(审YF�Y鼠DY梭AX麦X�<  驞X廆  ��8  ��<  驞廆  (�$�   D(D$pD(L$`D(T$PL崪$�   I媠I媨 A({郔嬨]闷   �   �   F   �  F   �  F   u  \   �  \   �         �   �  W G            �     �  Q        �donut::app::ThirdPersonCamera::AnimateTranslation 
 >O   this  AJ        $  AM  $     s >H   viewMatrix  AK          AL       u >�   oldViewPos  C�       �       C     �      D@    >�   newViewPos  C�       �      C�      �    $  B    �     >�    newClipPos  B0   Q    P >�   oldClipPos  C�          ,  C�      4      B    �     �  M        �  �� N M        G  I$ N M        J  仱	96 N M        �  
伓 N M        B  伬& N M        I  
伂
 N M        I  
仛4	 N! M        I  ��#
 N M        a  ��' N M        K  侶 M          俢 >@    _Xx  A�   `      A�  y      N M        �  侶 M        �  侶 N N N M        �  �= N M        >  /傢 N M        �  	備 M        �  	備 >@    _x  A�   �      N N M        �  偧 M        �  偧$ >@    _x  A�   �      >@    _y  A�   �    
  >@    _z  A  �    
  N N M        ;  倠
 M        �  
偰 N M        K  倠 M          偊 >@    _Xx  A�   �      A�  �      N M        �  倠 M        �  倠 N N N N M        >  /�> N M        �   � N Z   H  H  H   �                     @ Z h     �  �  X  Y  ^  a  b  j  �  �  �  �  ;  >  B  G  I  J  K  �   �   O  Othis  �   H  OviewMatrix  @   �  OoldViewPos      �  OnewViewPos  0   �  OnewClipPos      �  OoldClipPos  O   �   �          �    1   �      � �$   � �I   � �X   � �b   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �
  � �  � �0  � �4  � �8  � �=  � �B  � �G  � �[  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �0  � �=  � �H  � �y  � ��  � ��  � ��  � ��  � ��  � �  � �  � ��  � �,   .   0   .  
 |   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .     .  
 /  .   3  .  
 C  .   G  .  
 S  .   W  .  
 x  .   |  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
   .     .  
 $  .   (  .  
 �  .   �  .  
 &  .   *  .  
 C  .   G  .  
 `  .   d  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 H塡$WH冹p�I孂婤H嬞)t$`驛0�\2)|$P驛x�\zD)D$@驟@驞\B�Ah(企Y�(讐Ap�Y譇(菵)L$0驛Y菵)T$ �X�W荔X�.聎	W荔Q码(妈    �^痼^D^荔st�{x驞C|�驞(求wA(畜AY�(误Y求Y误X�W荔X�.聎	W荔Q码(妈    �^痼D^荔^D儉   �粍   �硤   �[x�K|D(皿搥   (馏DStD(鼠Y求DY企Y芋D\荔DY葾(麦Y艫(蝮Y黧D\華(荔AY荔\駻(袤AY�(误Y误X�W荔X�.脀	W荔Q秒(描    驞^荔^痼D^润D儗   驞嫄   �硵   �{x�S|(求媽   D(麦Y洋Y企EY馏YD\荔AY蝮EY鼠\駻(荔A\AY�(煮Y�(象Y象X�W荔X�.聎	W荔Q码(妈    驞^繦嬎�^痼^D儉   �硠   �粓   H嫓$�   (t$`(|$PD(D$@D(L$0D(T$ H兡p_�    �   \   �   \   �  \   H  \   �        �   �  H G            �  r   u  =        �donut::app::BaseCamera::BaseLookAt 
 >*   this  AI       f AJ          >�   cameraPos  AK        �  AK �      >�   cameraTarget  AP        �  AP �      >�   cameraUp  AM       � AQ          M        ;  �	" M        �  俆 M        �  俆 >@    _x  A�   X    *  N N M        K  �	 M          �6 >@    _Xx  A�   6      A�  L    W  N M        �  �	 M        �  �	 N N N N M        <  =佉A
 M        �  � >@    _x  A      P  >@    _y  A�       I  >@    _z  A�       D  N N M        ;  	乴( M        �  	伅 M        �  	伅 >@    _x  A  �    S  N N M        K  	乴 M          仈 >@    _Xx  A�   �      A�  �    �  N M        �  	乴 M        �  	乴 N N N N M        <  O�
X M        �  	亂 >@    _x  A  R    ]  >@    _y  A  l    L  >@    _z  A�   y    :  N N M        ;  *��'- M        �  
�� N M        K  *��- M          �� >@    _Xx  A�   �       A�  �     <  N M        �  *��- M        �  *��- N N N N M        ;  L
' M        �  	�� M        �  	�� >@    _x  A�   �       N N M        K  L
 M          } >@    _Xx  A�   }       A�  �     3  N M        �  L
 M        �  L
 N N N N M        :  	 M        �  S >@    _x  A�   %     r  >@    _y  A�   5     f  >@    _z  A  G     Y  N N
 Z   <   p                     @ * h	     �  �  �  :  ;  <  K  �   �   *  Othis  �   �  OcameraPos  �   �  OcameraTarget  �   �  OcameraUp  O  �   �           �       |       &  �
   '  �   (  �G   '  �L   (  �V   '  �Y   (  ��   )  �  *  ��  +  �Q  -  �T  +  �u  .  ��  -  �,      0     
 m      q     
 }      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
          
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 '     +    
 }     �    
 �     �    
 !     %    
 >     B    
 [     _    
 �     �    
 �     �    
 �     �    
 �     �    
 �         
 �     �    
 �     �    
 �     �    
 �     �    
 H嫅   H呉u!8�0  tH嬃肏呉u8�0  uH崄p  �3烂   �   %  W G            0       /   U        �donut::app::SwitchableCamera::GetActiveUserCamera 
 >�   this  AJ        0  M        �    M        Q  
  N N M        �  
 M        Q   N N                        H  h   �  �  Q        �  Othis  O   �   `           0     	   T       � �    � �   � �   � �   � �%   � �,   � �-   � �/   � �,   /   0   /  
 |   /   �   /  
 <  /   @  /  
 H嫅   H呉u!8�0  tH嬃肏呉u8�0  uH崄p  �3烂   �   %  W G            0       /   V        �donut::app::SwitchableCamera::GetActiveUserCamera 
 >   this  AJ        0  M        �    M        Q  
  N N M        �  
 M        Q   N N                        H  h   �  �  Q          Othis  O   �   `           0     	   T       � �    � �   � �   � �   � �%   � �,   � �-   � �/   � �,   0   0   0  
 |   0   �   0  
 <  0   @  0  
 H塡$H塼$H墊$AVH冹0I嬸L嬺H孂3蹓\$ L�
    L�    3襀媺   �    L嬋H吚tH媷(  H吚t�@H嫙(  �L嬎M吷tA婣�A婣A�@��@2�H呟t,����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�R@肚H媆$@H媡$HH媩$PH兡0A^�'   �   .   �   <   X      �   �  b G            �      �   X        �donut::app::SwitchableCamera::GetSceneCameraProjectionParams 
 >   this  AJ          AM       ` [   >�   verticalFov  AK          AV       �  >�   zNear  AL       p  AP          AL �       >sB   perspectiveCamera  CI     _       CQ     d     e 8   CI    d     T  M        P  %%
 >vB    _Ptr  AQ  C     !  AQ d     e 8   M        �  a M        �  �a N N M        �  H M        �  H M        '  H M        �  T N N N N N M        c  d N M        d  1~ M          ~, M        �  �� M        �  ��	
 N N N N 0                    @ B h   �  �  �  c  d    �    P  Q  �  �  '  �  �   @     Othis  H   �  OverticalFov  P   �  OzNear  9�       �   9�       �   O  �   P           �        D       � �     �d    �i    �o    �{    ��    �,   2   0   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
   2     2  
   2     2  
 A  2   E  2  
 U  2   Y  2  
 m  2   q  2  
 �  2   �  2  
 �  2   �  2  
 �  2   �  2  
 �  2   �  2  
 �  2   �  2  
 @SH冹 H媮   H嬟H吚tH嬋�    H嬅H兡 [脌�0   uH伭p  AH嬅IJA(B H兡 [�   >      �   B  X G            V      P   W        �donut::app::SwitchableCamera::GetWorldToViewMatrix 
 >   this  AJ        6    AJ 6        M        Q  
 N M        V  &	 M        �  	& N N
 Z   P                         @ " h   �  �  �  V  Q    O   0     Othis  O  �   X           V        L       � �   � �
   � �   � �   � �   � �&   � �:   � �,   1   0   1  
 }   1   �   1  
 �   1   �   1  
 X  1   \  1  
 H冹(L媺   M吷u0D8�0  uM吷u"D8�0  uH伭p  H吷t
H��P �H兡(�2繦兡(�   �     X G            G      B   `        �donut::app::SwitchableCamera::JoystickButtonUpdate 
 >�   this  AJ        .  AJ .         >t    button  A         G 9   >0    pressed  AX        G 9   >I    activeCamera  AJ .         M        U  $ M        �   M        Q  
 N N M        �   M        Q   N N N (                      @  h   �  �  U  Q     0   �  Othis  8   t   Obutton  @   0   Opressed  96       3   O  �   X           G        L       j �   k �.   l �3   n �9   o �;   r �@   q �B   r �,   :   0   :  
 }   :   �   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
   :     :  
 
  :     :  
 $  :   (  :  
 呉t#凓u;E劺t6�乣  �\    �乣  肊劺t�乣  �X    �乣  �   �   8   �      �     Y G            E       D   M        �donut::app::ThirdPersonCamera::JoystickButtonUpdate 
 >O   this  AJ        E  >t    button  A         E  >0    pressed  AX        E                         @     O  Othis     t   Obutton     0   Opressed  O   �   H           E        <       T �    U �	   W �   [ �'   X �<   [ �,   &   0   &  
 ~   &   �   &  
 �   &   �   &  
 �   &   �   &  
 4  &   8  &  
 H冹(L媮   M吚u0D8�0  uM吚u"D8�0  uH伭p  H吷t
H��P(�H兡(�2繦兡(�   �      R G            G      B   a        �donut::app::SwitchableCamera::JoystickUpdate 
 >�   this  AJ        .  AJ .        
 >t    axis  A         G 9   >@    value  A�         G 9   >I    activeCamera  AJ .         M        U  $ M        �   M        Q  
 N N M        �   M        Q   N N N (                      @  h   �  �  U  Q     0   �  Othis  8   t   Oaxis  @   @   Ovalue  96       5   O�   X           G        L       u �   v �.   w �3   y �9   z �;   } �@   | �B   } �,   ;   0   ;  
 w   ;   {   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �  ;      ;  
   ;     ;  
 冴t凓u�慭  皿慩  �   �     S G                      L        �donut::app::ThirdPersonCamera::JoystickUpdate 
 >O   this  AJ         
 >t    axis  A           >@    value  A�                                  @     O  Othis     t   Oaxis     @   Ovalue  O �   H                   <       J �    K �
   N �   Q �   M �   Q �,   '   0   '  
 x   '   |   '  
 �   '   �   '  
 �   '   �   '  
 $  '   (  '  
 H塡$塗$WH冹 H嬞D堵H钩     H�%#"勪滘薒3繟孂禗$9LL3�禗$:LL3�禗$;LL3繪H媼  I#萀媰�   H玲H嬸   H婣I;纓H�	;PtH;羣H婡;Pu螂3繦吚ID繧;纓7H崑�   H峊$8�    Hc岹�凐v苿X   H媆$0H兡 _闷�X  H媆$0H兡 _茂   !      �   '  S G            �      �   >        �donut::app::FirstPersonCamera::KeyboardUpdate 
 >�   this  AI       � �   AJ          >t    key  A         �  A  �       D8    >t    scancode  Ah          D@    >t    action  A   /     � �   Ai        / 
 >t    mods  EO  (           DP    >t     cameraKey  AJ  �     #  AJ �       M        \  ��	 M        "  ��	 M        +  ��	 N N N M        =  ej M        (  �� M        +  �� N N M        �  
=W+ M        �  WJc >@    _Where  AH  w       AH �      
 >h@    _End  AP  h     K  AP �       >h@    _Bucket_lo  AJ         AJ �     P  0  >e    _Bucket  AJ  ^       M           M        �   N N N N M        �  ( M        ]  ( M        #  ( M        �  ( M           (  M        �  #
 >#    _Val  AP  ,     <  N N N N N N N
 Z   [                         @ b h   �  �  f  Z  \  ]  "  #  )  +  =  �  �  �  �  (  )  �         �  �   0   �  Othis  8   t   Okey  @   t   Oscancode  H   t   Oaction  P   t   Omods  O �   h           �     
   \       1  �   2  �,   1  �/   2  ��   7  ��   8  ��   =  ��   ?  ��   :  ��   ?  �,      0     
 x      |     
 �      �     
 �      �     
 �      �     
 �      �     
          
 "     &    
 E     I    
 q     u    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
       $    
          
 <     @    
 H冹8L嫅   M呉u8D8�0  uM呉u*D8�0  u!H伭p  H吷t婦$`L�塂$ A��H兡8�2繦兡8�   �   |  R G            O      J   \        �donut::app::SwitchableCamera::KeyboardUpdate 
 >�   this  AJ        .  AJ .     !    >t    key  A         O A   >t    scancode  Ah        O A   >t    action  Ai        O A  
 >t    mods  EO  (           D`    >I    activeCamera  AJ .     !    M        U  $ M        �   M        Q  
 N N M        �   M        Q   N N N 8                      @  h   �  �  U  Q     @   �  Othis  H   t   Okey  P   t   Oscancode  X   t   Oaction  `   t   Omods  9>       .   O�   X           O        L       > �   ? �.   @ �3   B �A   C �C   F �H   E �J   F �,   6   0   6  
 w   6   {   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
   6      6  
 K  6   O  6  
 x  6   |  6  
 �  6   �  6  
 H塡$塗$WH冹 H嬞D堵H钩     H�%#"勪滘薒3繟孂禗$9LL3�禗$:LL3�禗$;LL3繪H媼�  I#萀媰p  H玲H媭  H婣I;纓H�	;PtH;羣H婡;Pu螂3繦吚ID繧;纓7H崑h  H峊$8�    Hc岹�凐v苿�   H媆$0H兡 _闷��  H媆$0H兡 _茂   !      �   '  S G            �      �   H        �donut::app::ThirdPersonCamera::KeyboardUpdate 
 >O   this  AI       � �   AJ          >t    key  A         �  A  �       D8    >t    scancode  Ah          D@    >t    action  A   /     � �   Ai        / 
 >t    mods  EO  (           DP    >t     cameraKey  AJ  �     #  AJ �       M        \  ��	 M        "  ��	 M        +  ��	 N N N M        =  ej M        (  �� M        +  �� N N M        �  
=W+ M        �  WJc >@    _Where  AH  w       AH �      
 >h@    _End  AP  h     K  AP �       >h@    _Bucket_lo  AJ         AJ �     P  0  >e    _Bucket  AJ  ^       M           M        �   N N N N M        �  ( M        ]  ( M        #  ( M        �  ( M           (  M        �  #
 >#    _Val  AP  ,     <  N N N N N N N
 Z   [                         @ b h   �  �  f  X  \  ]  "  #  )  +  =  �  �  �  �  (  )  �         �  �   0   O  Othis  8   t   Okey  @   t   Oscancode  H   t   Oaction  P   t   Omods  O �   h           �     
   \       ! �   " �,   ! �/   " ��   ' ��   ( ��   - ��   / ��   * ��   / �,   "   0   "  
 x   "   |   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
   "     "  
 "  "   &  "  
 E  "   I  "  
 q  "   u  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
    "   $  "  
   "     "  
 <  "   @  "  
 @SH冹P駻H嬞A婣L峀$ �D$ 駻 塂$(A婡L岲$0�D$0�塂$8婤H峊$@�D$@塂$H�    3繦墐�   H墐�   墐�   H墐�   墐�   H兡P[肞         �   �  K G            }      w   A        �donut::app::FirstPersonCamera::LookAt 
 >�   this  AI       n  AJ          >�   cameraPos  AK        E  >�   cameraTarget  AP        /  >�   cameraUp  AQ         
 Z   =   P                     @  h   �  _   `   �  Othis  h   �  OcameraPos  p   �  OcameraTarget  x   �  OcameraUp  O   �   H           }        <       X  �   Z  �T   [  �]   \  �j   ]  �w   ^  �,      0     
 p      t     
 �      �     
 �      �     
 �      �     
 �      �     
 �     �    
 H嬆H塜 WH冹0驛 L岺�\驛HI嬝�\JH孂H岺梵@梵A@L岪�\BH峆�H祗@痂    �婥�L$H�X
    H媆$X��8  �D$@�嘍  W
    �D$P�廝  �嘥  墖@  H兡0_肣      f      �         �   )  K G            �      j   S        �donut::app::ThirdPersonCamera::LookAt 
 >O   this  AJ        *  AM  *     �  >�   cameraPos  AK        F  >�   cameraTarget  AI  "     M  AP        "  >@     azimuth  A�   �     "  BH   U     Y  >�    cameraDir  D     >@     elevation  BP   U     Y  >@     dirLength  B@   U     Y  M        N  �� N M        �  U3 N  M        :  
	

	 M        �  .
 >@    _x  A�           >@    _y  A�   '     .  >@    _z  A�   B       N N
 Z   9   0                     @  h   �  �  �  N  :   @   O  Othis  H   �  OcameraPos  P   �  OcameraTarget  H   @   Oazimuth      �  OcameraDir  P   @   Oelevation  @   @   OdirLength  O   �   �           �        �       � �   � �   � �   � �   � �"   � �'   � �*   � �.   � �9   � �=   � �B   � �F   � �P   � �U   � �\   � �j   � �o   � �w   � ��   � ��   � ��   � ��   � �,   +   0   +  
 p   +   t   +  
 �   +   �   +  
 �   +   �   +  
 �   +   �   +  
 �   +   �   +  
 �   +     +  
 
  +     +  
 M  +   Q  +  
 q  +   u  +  
 �  +     +  
   +     +  
 7  +   ;  +  
 @  +   D  +  
 @SH冹P駻H嬞A婣L峀$0�J驛XH�D$0�驛X 塂$8婤�L$$塂$H�D$ �B驛X@L岲$ �D$(�H峊$@�D$@�    3繦墐�   H墐�   墐�   H墐�   墐�   H兡P[胣         �   �  K G            �      �   B        �donut::app::FirstPersonCamera::LookTo 
 >�   this  AI       �  AJ          >�   cameraPos  AK        g  >�   cameraDir  AP        X  >�   cameraUp  AQ          M        �  	  N
 Z   =   P                     H  h   �  �  _  �   `   �  Othis  h   �  OcameraPos  p   �  OcameraDir  x   �  OcameraUp  O�   H           �        <       a  �   b  �r   c  �{   d  ��   e  ��   f  �,      0     
 p      t     
 �      �     
 �      �     
 �      �     
 �      �     
 �     �    
 H嬆L塇 SWH冹x)p豅岺驛0H孃)x菻峆驛x(艱)@窰嬞驟@H岺楧)H↙岪驞
    A(華W罙W审@�(茿W馏H狊@滆    ��$�    ��$�   �^痼^D^纓��$�   ��揇  �G�O�Y蝮YX7驞Y麦X�8  驞X馏�<  驞傽  ��$�   �X    (t$`(|$PD(D$@�揇  AW罝(L$0�働  ��$�   �僒  H兡x_[肗      u      �         �   �  K G            <  E   �   T        �donut::app::ThirdPersonCamera::LookTo 
 >O   this  AI  6      AJ        6  >�   cameraPos  AK          AM        >�   cameraDir  AP        I  >=B   targetDistance  AQ          D�    >@     azimuth  A�         B�   y     �  >@     elevation  B�   y     �  >@     dirLength  B�   y     �  >6    distance  A�   �     �    M        N  � N M        �  ��
 N M        �  
��	 N M        �  �� N M        N  y" N M        M  �� N M        7  	+ M        �  ^
 >@    _x  A�   Z       >@    _y  A�   j       >@    _z  A�   ^       N N
 Z   9   x                     @ 2 h   �  �  �  �  N  �  �  7  M  N  �   �   O  Othis  �   �  OcameraPos  �   �  OcameraDir  �   =B  OtargetDistance  �   @   Oazimuth  �   @   Oelevation  �   @   OdirLength  O  �   �           <       |       � �   � �   � �!   � �.   � �6   � �y   � ��   � ��   � ��   � ��   � ��   � �  � �5  � �,   ,   0   ,  
 p   ,   t   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
   ,     ,  
 +  ,   /  ,  
 ;  ,   ?  ,  
 _  ,   c  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
   ,     ,  
 H塡$塗$WH冹 H嬞D妒H钩     H�%#"勪滘薒3華孁禗$9L媰   LL3�禗$:LL3�禗$;LL3萀H媼H  I#蒆玲H�0  H婣I;纓H�	;PtH;羣H婡;Pu螂3繦吚ID繧;纓!H崑  H峊$8�    �Hc斃垊f  H媆$0H兡 _茂   !      �     V G            �      �   @        �donut::app::FirstPersonCamera::MouseButtonUpdate 
 >�   this  AI       �  AJ          >t    button  A         �  A  �       D8    >t    action  A   /     �  Ah        / 
 >t    mods  Ai          DH    >t     cameraButton  A   �     
  A  �       M        \  ��	 M        "  ��	 M        +  ��	 N N N M        =  ej M        (  �� M        +  �� N N M        �  
46*. M        �  4**Oc >@    _Where  AH  w       AH �      
 >h@    _End  AP  ;     x  AP �       >h@    _Bucket_lo  AJ         AJ �     :    >e    _Bucket  AJ  e       M           M        �   N N N N M        �  # M        ]  # M        #  # M        �  # M           #! M        �  #
 >#    _Val  AQ  ,     �  AQ �       N N N N N N N
 Z   [                         @ b h   �  �  f  Y  \  ]  "  #  )  +  =  �  �  �  �  (  )  �         �  �   0   �  Othis  8   t   Obutton  @   t   Oaction  H   t   Omods  O �   P           �        D       G  �   H  �,   G  �/   H  ��   M  ��   N  ��   U  �,      0     
 {           
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 C     G    
 S     W    
 ^     b    
 n     r    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
          
       $    
 H冹(L嫅   M呉u0D8�0  uM呉u"D8�0  uH伭p  H吷t
H��P�H兡(�2繦兡(�   �   ?  U G            G      B   ^        �donut::app::SwitchableCamera::MouseButtonUpdate 
 >�   this  AJ        .  AJ .         >t    button  A         G 9   >t    action  Ah        G 9  
 >t    mods  Ai        G 9   >I    activeCamera  AJ .         M        U  $ M        �   M        Q  
 N N M        �   M        Q   N N N (                      @  h   �  �  U  Q     0   �  Othis  8   t   Obutton  @   t   Oaction  H   t   Omods  96       1   O �   X           G        L       T �   U �.   V �3   X �9   Y �;   \ �@   [ �B   \ �,   8   0   8  
 z   8   ~   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 "  8   &  8  
 ;  8   ?  8  
 T  8   X  8  
 A凐A斃呉t冴t
凓uD垇�  肈垇�  肈垇�  �   �   �  V G            .       -   J        �donut::app::ThirdPersonCamera::MouseButtonUpdate 
 >O   this  AJ        .  >t    button  A         .    >t    action  Ah         
 >t    mods  Ai        .  D     >=    pressed  AX       &                         @ 
 h   Y      O  Othis     t   Obutton     t   Oaction      t   Omods  O�   `           .     	   T       7 �    8 �   : �   = �   A �   > �%   A �&   < �-   A �,   $   0   $  
 {   $      $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 
  $     $  
 �  $   �  $  
 W莉Z硫Z殷仩   �懁   �   �     S G                      ?        �donut::app::FirstPersonCamera::MousePosUpdate 
 >�   this  AJ         
 >A    xpos  A�          
 >A    ypos  A�                                  @ 
 h   ^      �  Othis     A   Oxpos     A   Oypos  O   �   0                   $       B  �    C  �   D  �,      0     
 x      |     
 �      �     
 �      �     
 0     4    
 H冹(H嫅   H呉u.8�0  uH呉u!8�0  uH伭p  H吷t
H��P�H兡(�2繦兡(�   �   �  R G            E      @   ]        �donut::app::SwitchableCamera::MousePosUpdate 
 >�   this  AJ        ,  AJ ,        
 >A    xpos  A�         E 7  
 >A    ypos  A�         E 7   >I    activeCamera  AJ ,         M        U  $
 M        �   M        Q  
 N N M        �  
 M        Q   N N N (                      @  h   �  �  U  Q     0   �  Othis  8   A   Oxpos  @   A   Oypos  94       /   O  �   X           E        L       I �   J �,   K �1   M �7   N �9   Q �>   P �@   Q �,   7   0   7  
 w   7   {   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
 �  7   �  7  
   7     7  
 W莉Z硫Z殷�(  ��,  �   �     S G                      I        �donut::app::ThirdPersonCamera::MousePosUpdate 
 >O   this  AJ         
 >A    xpos  A�          
 >A    ypos  A�                                  @ 
 h   ^      O  Othis     A   Oxpos     A   Oypos  O   �   0                   $       2 �    3 �   4 �,   #   0   #  
 x   #   |   #  
 �   #   �   #  
 �   #   �   #  
 0  #   4  #  
 H冹(H嫅   H呉u.8�0  uH呉u!8�0  uH伭p  H吷t
H��P�H兡(�2繦兡(�   �   
  U G            E      @   _        �donut::app::SwitchableCamera::MouseScrollUpdate 
 >�   this  AJ        ,  AJ ,         >A    xoffset  A�         E 7   >A    yoffset  A�         E 7   >I    activeCamera  AJ ,         M        U  $
 M        �   M        Q  
 N N M        �  
 M        Q   N N N (                      @  h   �  �  U  Q     0   �  Othis  8   A   Oxoffset  @   A   Oyoffset  94       /   O   �   X           E        L       _ �   ` �,   a �1   c �7   d �9   g �>   f �@   g �,   9   0   9  
 z   9   ~   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
   9     9  
 	  9   
  9  
 $  9   (  9  
 �塇  W纅/聉
�    ��    �Y丏  �_润]塋  �塂  �   �      �      �   �  V G            @       ?   K        �donut::app::ThirdPersonCamera::MouseScrollUpdate 
 >O   this  AJ        @  >A    xoffset  A�           D    >A    yoffset  A�         @  M        F  + >@    value  A�   +       M        �  / N M        �  + N N                        @  h   F  �  �      O  Othis     A   Oxoffset     A   Oyoffset  O   �   0           @        $       D �    F �?   G �,   %   0   %  
 {   %      %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �  %   �  %  
 �塒  �慣  �   �   
  P G                      N        �donut::app::ThirdPersonCamera::SetRotation 
 >O   this  AJ          >@    yaw  A�           >@    pitch  A�                                  H     O  Othis     @   Oyaw     @   Opitch  O  �   8                   ,       ^ �    _ �   ` �   a �,   )   0   )  
 u   )   y   )  
 �   )   �   )  
 �   )   �   )  
    )   $  )  
 H塡$WH侅�   H�H嬟H孂H峊$8E3繦嬎�悩   E3繦峊$8H嬎 嚑   H彴   @ 嚴   H0徯   H��悹    囙   H忦   @ �   H0�  SH嫓$�   (�(�剖U�\�坡�埔\麦�   ��$  H伳�   _�   �   �  L G            �   
   �   O        �donut::app::ThirdPersonCamera::SetView 
 >O   this  AJ          AM       � 
 >\   view  AI       �  AK          >   viewport  C�       �       D     M          �� N M          �� N �                     @  h       ;  ^   �   O  Othis  �   \  Oview        Oviewport  9!       �?   9`       �?   O�   P           �        D       d �
   e �'   f ��   g ��   i ��   h ��   i �,   *   0   *  
 q   *   u   *  
 �   *   �   *  
 �   *   �   *  
 �   *   �   *  
 �   *   �   *  
 �  *   �  *  
 �  *   �  *  
 �  *   �  *  
 H塡$UH嬱H侅�   H嬞H媺   H吷u8�0  厪  H壌$�   3鯤壖$�   勔�  H吷thH峌需    �E躄岴狊e鬑峌绑]U�(梭XM鞁E潋E�(捏XE梵e绑Mん]大U阁E�(麦XE饓E润E~�撠  L岴膀凁  H峌狉涗  (蕥凐  �E�(麦X�粕U塃葖冟  �U狉]狊E�(�评U�X闰U爥EM大嬥  �X嬱  �M窵峂繦嬎�    H壋�   H壋�   壋�   H壋�   壋�   H嫽(  H壋   H壋(  H嫶$�   苾0  H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�PH嫾$�   H嫓$�   H伳�   ]肙   =   8        �     W G            �     �  Y        �donut::app::SwitchableCamera::SwitchToFirstPerson 
 >�   this  AI       x AJ          AI �      >0    copyView  A         � N d  A  ]    n B   >X    viewToWorld  DP    M        �   M        Q  
 N N M        Q  E N M        B  	S)& M        �  e; M        �  e; >@    _x  A�   w     ;  A�  0      N N N M        R  乚1$ M        T  .亜 M          亜, M        �  亞 M        �  仧	
 N N N N M          乚$ M        s  乚7 M        �  乚 N M        �  乨 N N N N M        B  ��"2, M        �  ��# N N Z   O  =   �                     @ v h   �  �  �  �  �  �  �  �  B  Q  R  T  U  V  _          s  �  O  �  �  �  �  "  &   �   �  Othis  �   0   OcopyView  P   X  OviewToWorld  9�      �   9�      �   O  �   �           �    
   t        �    �3    �E    �J    �S    ��    ��    �0   �]   �z   ��   ��   �,   3   0   3  
 |   3   �   3  
 �   3   �   3  
 �   3   �   3  
 �   3   �   3  
 �   3   �   3  
 �  3   �  3  
 �  3   �  3  
   3   
  3  
   3     3  
 0  3   4  3  
 @SH冹 H婤H吚t�@H嫏(  L婤H�H墎   L墎(  H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  W G            p      j   [        �donut::app::SwitchableCamera::SwitchToSceneCamera 
 >�   this  AJ        P  AJ e       >�   sceneCamera  AK        R  AK e       M        S  _ M        T  32 M          2, M        �  9 M        �  R	
 N N N N M           M        s  ;

 M        �  
 N M        �  
 N N N M          
 M        �  
 M        '  &	 M        �   N N N N N                       @ F h   �  �  �  S  T  V        s  �  �  �  '  "  &   0   �  Othis  8   �  OsceneCamera  9P       �   9b       �   O �   0           p        $       7 �   : �j   ; �,   5   0   5  
 |   5   �   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 H塡$WH冹pH孂I嬝H媺   H吷u8�0  勭   勔剱   H壌$�   H吷t>H峊$@�    �D$XL岲$ 婦$`H峊$0�D$ �D$d塂$(婦$l�D$0塂$8�.�GtL岲$0婫|H峊$ �D$0�Gh塂$8婫p�D$ 塂$(L嬎H崗p  �    H嫶$�   H嫙(  3榔�0   H墖   H墖(  H呟t,����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH嫓$�   H兡p_肅   =   �   ,      �   �  W G              
     Z        �donut::app::SwitchableCamera::SwitchToThirdPerson 
 >�   this  AJ        
  AM  
     �  AM     
  >0    copyView  A         � B 9  A  �     ] <   >=B   targetDistance  AI       �  AP          AI       >X    viewToWorld  D@    M        �   M        Q  
 N N M        Q  8 N M        R  	��? M        T  .�� M          ��, M        �  �� M        �  ��	
 N N N N M          	�� M        s  ��	
 M        �  ��
 N M        �  ��	 N N N N Z   O  T   p                     @ ^ h   �  �  �  �  �  Q  R  T  U  V          s  O  �  �  �  �  "  &   �   �  Othis  �   0   OcopyView  �   =B  OtargetDistance  @   X  OviewToWorld  9�       �   9      �   O   �   x                  l       ! �   " �(   % �8   ' �=   ) �G   * �y   + �{   . ��   3 ��   2 ��   3 �  4 �,   4   0   4  
 |   4   �   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
    4     4  
   4     4  
    4   $  4  
 �  4   �  4  
 �  4   �  4  
 �  4   �  4  
 H嬆SH侅�   �H嬞�XAh�J�XIl驛h )p梵Ah�B�XAp)x伢IlD)@菵)H阁Ap�Qt�axD)P―)X楧)`圖)l$@D)t$0D)|$ �Y|驟P驟A(麦E`A(篌EhA(腆Ep驟xA(Y捏Y梭Y蝮YX痼�$�   A(朋Y捏X馎(象XY梭A@��$�   �Y捏XAHD(馏�$�   驞Y�(腕Y梭DX�(�(企Y左Y企DX馏X�W繟(润AY润X�.聎	W荔Q码(妈    ��$�   �^痼^D^荔st�{x驞C|�搫   驞媭   (麦泩   A(耋AY�(梭AY蘀(洋DY�$�   驛Y篌EY煮X�(麦Y�$�   驛Y朋X�(梭DX鼠AY象DX畜Y�(企Y企DX洋DX薃(殷AY褹(审AY审X�W荔X�.聎	W荔Q码(妈    �^痼D^畜D^润硛   A(�(唧D搫   (馏D媹   �st(��搥   驛Y翫(麦Y芋DY馏AY\�(企AY馏AY蝮D\�(求Y求\駻(伢AY�(误Y误X�W荔X�.脀	W荔Q秒(描    �^鳫嬎驞^荔^痼粚   驞儛   �硵   D(t$0L崪$�   A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�D(|$ I嬨[�    ^  \   <  \   �  \   U        �   �  Q G            Y  }     D        �donut::app::FirstPersonCamera::UpdateCamera 
 >�   this  AI       B AJ          >�   cameraMoveVec  AK        b AK k    � >X   cameraRotation  AP        b AP k    � M        ;  偗	! M        �  	傭 M        �  	傭 >@    _x  A  �    :  N N M        K  偗 M          傉 >@    _Xx  A�   �      A�  �    n  N M        �  偗 M        �  偗 N N N N M        <  俈=
D M        �  	偤 >@    _x  A�   �    R  >@    _y  A  �    H  >@    _z  A�   �    A  N N M        ;  侟
( M        �  
侱 M        �  
侱 >@    _x  A  I    �  N N M        K  侟
 M          �* >@    _Xx  A�   *      A�  @    B  N M        �  侟
 M        �  侟
 N N N N% M        c  亪-CJ N M        ;  �(	#
, M        �  	乷 M        �  	乷 >@    _x  A�   s     N N M        K  �(	#, M          丩 >@    _Xx  A�   L      A�  k    %  N M        �  �(	
 M        �  �(	
 N N N N) M        c  !
1+_.b N M        >   N
 Z   <   �                     @ 2 h     �  c  (  �  �  ;  <  >  K  �   �   �  Othis  �   �  OcameraMoveVec  �   X  OcameraRotation  O   �   �           Y       �       �  �   �  �   �  �   �  �!   �  �+   �  �R   �  ��  �  �V  �  �]  �  �f  �  �i  �  �r  �  ��  �  ��  �  �  �  �T  �  �,       0      
 v       z      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
 Y      ]     
 �      �     
 �      �     
 Y      ]     
 v      z     
 �      �     
 �      �     
 P      T     
 `      d     
 :      >     
 �      �     
 �      �     
            
 H嬆SH侅�   �亐   L岮8�ItH峊$ �憪   H嬞�檮   �ax�   )p梵眻   )x貗亴   �y|A� 3荔A@驛H驛P驛X驛`驛h驛p驛x I堾$A堾,(    �    (润IhW是D$@  �?�L$D�IpD$ D$0�AlW�W麦L$LH峀$P�D$H�    (�$�   (�$�    CHK@ C(H伳�   [脺      �      �   @      �   �  O G            !  Q   �   <        �donut::app::BaseCamera::UpdateWorldToView 
 >*   this  AI  ,     �  AJ        , # M        8  ��
+ M        $  ��
 N N M        7  �� N" M        e  
" M        &  
( M        '  

 N N N
 Z   9   �                     @ . h
   �  �  e  $  %  &  '  u  7  8   �   *  Othis  O  �   X           !       L          �   !  �   "  �!   !  �)      �,   !  ��   "  ��   #  �,      0     
 t      x     
 �      �     
           
 H冹(D�L嬌H钩     L嬕H�%#"勪滘薒3�禕LL3�禕LL3�禕I婹0LL3繪I#蠬菱IQH婤I;At$A�
H�;Htf怘;聇H婡;Hu騂兝H兡(肏�
    �    虋   �   �         �   x  � G            �      �   [        �std::unordered_map<int,int,std::hash<int>,std::equal_to<int>,std::allocator<std::pair<int const ,int> > >::at 
 >訞   this  AJ          AQ       �  >�*   _Keyval  AK          AR       { 1 M        �  ?#Ls >@    _Where  AH  ]     6 %   >h@    _Bucket_lo  AK  i       AK �       >e    _Bucket  AK  C       M          c M        �  c N N N M        �  
'
+ M        ]  
'
+ M        #  
'
+ M        �  
'
+ M           
'
+" M        �  
=
 >#    _Val  AP  %     n  N N N N N N
 Z   O   (                      @ 6 h   �  ]  #  �  �  �  )         �  �         $LN47  0   訞  Othis  8   �*  O_Keyval  O�   `           �   �  	   T       � �   � �   � �   � �   � �   � �~   � ��   � ��   � �,   !   0   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
 N  !   R  !  
 w  !   {  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 J  i   N  i  
 �  !   �  !  
  d T 4 2p    H           ]      ]      �   r r� g� ;� *x h 
4 
�p    �          ^      ^      �   Q Qx Eh	  0      !          _      _      �    4 2
p    �           `      `      �    4 2
p    �           a      a      �   G G�
 B� 9� 1x *h t( d' 4& $ P      �          b      b      �   A A�
 8� 3� .� )x h 4( & P      2          c      c      �    �0    }           d      d      �    �0    �           e      e      �   	 	 0      �           f      f      �    h  "      �          g      g      �   } }� w� q� k� f� a� M� H�	 >x
 +h  0      Y          h      h      �    B      �           j      j      �    4 2
p    �           k      k      �   S S� M� G� A� <�	 7�
 2� -� (x
 !h d" 4   p      �          l      l      �   
 
4 
�p    �           m      m      �    4 Rp    �           n      n      �   E E� 3� !x h 
�	p0      <          o      o      �   	 x t d  P      1           p      p      �   ! �     1          p      p      �   1   X           p      p      �   !
 
� � 1   X          p      p      �   X   �           p      p      �   ! h	 4 X   �          p      p      �   �   =          p      p         !   h	 X   �          p      p      �   =  u          p      p         !   X   �          p      p      �   u  �          p      p         !   1   X          p      p      �   �  �          p      p         !       1          p      p      �   �  �          p      p          20    V           q      q      #    t
 d	 4 R�           W      /       �           r      r      )   (           2      5             vT姕 4 �P    +           s      s      ;   ! t d     +          s      s      ;   +   �          s      s      A   !   t     +          s      s      ;   �  �          s      s      G   !       +          s      s      ;   �  �          s      s      M   ��
 
4 
�p    0           t      t      V   ! d     0          t      t      V   0   �           t      t      \   !       0          t      t      V   �             t      t      b   挈 20    4           u      u      k   ! t     4          u      u      k   4   j           u      u      q   !       4          u      u      k   j   p           u      u      w   @R b      O           v      v      �    B      E           w      w      �    B      G           x      x      �    B      E           y      y      �    B      G           z      z      �    B      G           {      {      �    h 
4
 
rp    n          |      |      �   0 0� x h �0    �           }      }      �   N N� H� B� <� 7�	 2�
 *� "� x
 h 4"  p      \          ~      ~      �   1 1x  (h B      �                       �   < <� 7� .� !x h 4  p      l          �      �      �   
 
4 
Rp           W      �       j           �      �      �   `       �   V M' 'h  "      �           �      �      �    h  "      �           �      �      �   f
 f�  N� >� /� #x h �      5          �      �      �       ����    ����        ��������invalid unordered_map<K, T> key                 .?AVSceneCamera@engine@donut@@     �                   .?AVPerspectiveCamera@engine@donut@@     �   吞�=   ?詻^?  �?33�?  �?��?  @@   B�煽  鹄  �?               �   �   �   �E褼5`苁镽CNv束り瑩シ)叜月o"0h嶗寳G質P錞	�"粏竏袯+�0ㄢ骪稒鹼30夦唉魺钛9y)1Xv%鍛禍	m
F鱛�4��j7b,譿�{F?M薃稓? 蓓P槣扈[鰗�-3亓,蕕V稩6�=桹出踾A鋅	礏6{獉潁X�;讠N誒耦珗V鉑�oiv僺洡j磞奷皠嘝�:鉺怐�'煐B:�9錌桔減垌>麣m鵳Wg櫩4bZ�1焩依3袉^囄嶸u�:殃��
i.6zc仗�瘥o'茛惙阋粳鑶撽熼樯湱諲$Pd� R�菳
泅n黢3�#cqb絒彯諔嫼J荌��	�.瓲$眏�`(�嬰酗	ΣSy袎]5�嚸�*埸		*L〥�败]	�=：齠U� �蹰kY糷:u轫
打9t�侴鮏L>p7橭眯0��0亮G�=<忺悄此�眏褙c��喛眷F-詉�.蠇2!�?l	仡	]t�7�办魫I鲙蓩v�7襼qEx揼��;�/:欕�"貑�
�W�X0�=V垓�_UW[鮌�	N2瞙 :眣�[
礟�2瞙 :眣�陻*~�嶮�鯟7奉7�$]趈�溥厦�%潴阩}<昐�.E~踵_F[^
]帶栕颶�'[3v+鞳\傓�3<躨&~ZBX终宨Q[h褤0rr�2/k蛳�崶j�?巆MZ刳O9峲M~翉圸芖厴�/哜珇[菘魁�$zzi,�-[j�獭t爉	醙#幋渳�>Js鏪�>蘱[v絗d悈貸	wn堪'硒k� 1"$砌ら� �P贳鞌l��)缕謒鹍孁鐐�9橞U4盷壇�谹Yaj< 6js广懏靱濧H&*畲C敳p 飻�*�?痳-吏]嬓(�;@恉瓐饙�$�
鹰i喡戭�5澗|q篙K>�?鋛鞓9琠瑚勼豹�0V se�;訅`誁芵�pz@1趎0��I�� 鐐H�咏T犎�!�异w�bq@蜧麣�!�'b貋P�2识p囅�綔)~羠=塻Ty^F{'yZ祼垩寯啦艌餍e�9xfK"莜芯槫@寽绨珗 乎橆I惁偈b�=盥ス騳<珘惁偈b�=瞵腽琾�k
贷�*�@k
习�	o悬蜾}终洝33/�綡攘憵凡K劯埒洼綡攘曼旡~垱怍G�
�3a駽}�鉖嚟ZX�胘%� u�8$���氲愩铚-坓�(鬄鮎x$[�惁偈b�=盥ス騳<珘�衾揌/H鯣虎
'9O筵儎縒籩项扭缄*栊鮴轧(_88§6Y暄�VO僣韤�>~ 讄(幔愘錼|s妠珘伤▕�◣眀霤�x趀薟^L孎F.{掾�(92阥嶺	\焝��茬
魟厭/"DP��q斑65"鼌璹��
kh���8v�岓��炮聁V雵J-WV8oW 9�~&骱羳傁�訑臄1�dd�a�:_棢杻#Q奾Z孻乜�F皒K"届à0^卶y娑�	hQ�)Xa�[(�>钶r�巑#辜3鍘@N/�.攝髣窚釸姅俥>�(�&囹�?=�5��3�R��E光�穹芿檪q[=	�#=3鬃豦#鱽鴶D#I馪�=雵J-WV8oI4u�=嫭i1(��O�:,�9DgT幈rmw	P��&樭錜ㄈe孮.�>J该2>1搢-坓�(鬄�d�-坓�(鬄鮶邘c膋c-坓�(鬄�d�-坓�(鬄鮶邘c膋c-坓�(鬄鮶邘c膋cC甉h嗓斠[G`嵇莚Ycs�487銢TD椣.2漪鱵vA[烄则=揼�捾燝杻澔�5}鋹s搸+鉠?橤%钉�譑iv褍| H挎驻趀钚梆阇�/騫坾镚2倃齼_*窶圹涩�6﹎W鱈懱�汿D椣鰍ü垅L覸|�"u�MK��H联tF^床鯊鯺寓�
!oC劏轧6rA苜瀀CRC冼鸦2鞜S鹮�^笵A傮闍咢过@楞6ンq�"�瑭坶.羸�(E�XV蒥]2窍�,�?VD5'N~f#L譋抽摊o5yI~�sG﹋        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       L              .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       �       港�+     .debug$S       �             .text$mn       &       轡�     .debug$S                    .text$mn    	   n      鐨$�     .debug$S    
   �         	    .text$mn       5      W@9�     .debug$S       �             .text$mn    
   �       f聰s     .debug$S       ,         
    .text$mn       �       旔q     .debug$S       ,             .text$mn       �       鑨(     .debug$S                    .text$mn              畔�     .debug$S       �              .text$mn              媈%     .debug$S       �              .text$mn       .       ^�     .debug$S       �              .text$mn       .       q焛�     .debug$S       �              .text$mn       ,       l?     .debug$S       �              .text$mn              Nm軉     .debug$S       �              .text$mn       7       Zo萅     .debug$S        l             .text$mn    !   D       磚
:     .debug$S    "   �         !    .text$mn    #   "       B�(�     .debug$S    $   (         #    .text$mn    %   j      屇淬     .debug$S    &   �         %    .text$mn    '   :      s懅n     .debug$S    (   L         '    .text$mn    )   #       v^
�     .debug$S    *   �          )    .text$mn    +   �      媾     .debug$S    ,   8         +    .text$mn    -   �       RV     .debug$S    .   �         -    .text$mn    /   \     旮邾     .debug$S    0   t         /    .text$mn    1   l     馒&     .debug$S    2   �  (       1    .text$mn    3   3       盇�     .debug$S    4   ,         3    .text$mn    5   H       襶.      .debug$S    6   �         5    .text$mn    7   �     慤楀     .debug$S    8   h  (       7    .text$mn    9   5       /�}K     .debug$S    :            9    .text$mn    ;   �     驩�     .debug$S    <   h
  R       ;    .text$mn    =   �      8藎�     .debug$S    >   �  
       =    .text$mn    ?   �      珀h�     .debug$S    @   �         ?    .text$mn    A   2  
   �@�     .debug$S    B     $       A    .text$mn    C   �     a婆�     .debug$S    D     >       C    .text$mn    E   �     Y$�     .debug$S    F   8  .       E    .text$mn    G   �     �暔     .debug$S    H   8  <       G    .text$mn    I   0       *ㄍ     .debug$S    J   �         I    .text$mn    K   0       *ㄍ     .debug$S    L   �         K    .text$mn    M   �      ﹤�9     .debug$S    N             M    .text$mn    O   V      嬇�     .debug$S    P   �         O    .text$mn    Q   G       2�:�     .debug$S    R   |         Q    .text$mn    S   E      燂枥     .debug$S    T   |  
       S    .text$mn    U   G       O     .debug$S    V   l         U    .text$mn    W          蔅I     .debug$S    X   l  
       W    .text$mn    Y   �      膢%�     .debug$S    Z   �  (       Y    .text$mn    [   O       � [�     .debug$S    \   �         [    .text$mn    ]   �      鼎'n     .debug$S    ^   �  (       ]    .text$mn    _   }      萊�     .debug$S    `   �         _    .text$mn    a   �      須�     .debug$S    b            a    .text$mn    c   �      &¤�     .debug$S    d             c    .text$mn    e   <     Q顛�     .debug$S    f   �          e    .text$mn    g   �      ど]�     .debug$S    h   p  (       g    .text$mn    i   G       7槻     .debug$S    j   �         i    .text$mn    k   .       b绖     .debug$S    l   �         k    .text$mn    m          熸禾     .debug$S    n   `  
       m    .text$mn    o   E       Yt�     .debug$S    p   l         o    .text$mn    q          祭\�     .debug$S    r   `  
       q    .text$mn    s   E       ��     .debug$S    t   |         s    .text$mn    u   @      撈鵿     .debug$S    v   �         u    .text$mn    w          54QW     .debug$S    x   X  
       w    .text$mn    y   �       �朒1     .debug$S    z            y    .text$mn    {   �     粵�7     .debug$S    |   �         {    .text$mn    }   p       7冶     .debug$S    ~   (         }    .text$mn            	︷�     .debug$S    �   8             .text$mn    �   Y     秪     .debug$S    �   �  (       �    .text$mn    �   !     .轊p     .debug$S    �   X         �    .text$mn    �   �      zz�     .debug$S    �   �         �        \       5        x                �                �                �       G        ,      �        ]      Y        �      m        �      g              7        7      A        l      _        �      c        �      ?        j      C        �      �        +      �        �      ]        �      q        	      k        D      u        ~      S        �      W        �      ;        "      w        V      y        �      a        �      e        7      =        k      E        �      I        
      K        T      O        �      M        �      {        *	              x	      }        �	      [        
      o        S
      i        �
      s        �
      Q              U        =      9        k               �               	      3        W      	        �      +        �              
      /        T
              �
              �
                            J              �      '        �      1        
              A      %        �      !        "              c      
        �      #        �              '              e      -        �      )        �              .              Y               l           cosf             expf             sinf             sqrtf            $LN13       5    $LN85       G    $LN28       �    $LN83       Y    $LN80       g    $LN66       7    $LN85       A    $LN10       _    $LN14       c    $LN22       ?    $LN97       C    $LN66       �    $LN47   �   �    $LN50       �    $LN83       ]    $LN83       ;    $LN10       y    $LN12       a    $LN26       e    $LN110      E    $LN32       O    $LN48       M    $LN94       {    $LN70           $LN43       }    $LN27       [    $LN27       o    $LN27       i    $LN27       s    $LN27       Q    $LN27       U    $LN55       	    $LN20       +    $LN90       /    $LN4            $LN27       1    $LN21       %    $LN4        
    $LN21       -    $LN6            .xdata      �          F┑@5        |      �    .pdata      �         X賦�5        �      �    .xdata      �           0y頖        �      �    .pdata      �         F馷矴        
      �    .xdata      �          O幫鐑        V      �    .pdata      �         |i珦�        �      �    .xdata      �          h2Y        �      �    .pdata      �         �+�Y              �    .xdata      �          h2g        H      �    .pdata      �         U,og        �      �    .xdata      �   ,       鸝l�7        �      �    .pdata      �         m�7              �    .xdata      �   (       �=R        :      �    .pdata      �         漀 鯝        w      �    .xdata      �          悯�-_        �      �    .pdata      �         A刄7_               �    .xdata      �          悯�-c        L      �    .pdata      �         Ж阹c        �      �    .xdata      �          �
�?        �      �    .pdata      �         杳Y�?        a      �    .xdata      �          瘰殫C        �      �    .pdata      �         﨣魹C        J      �    .xdata      �   4       d錒�        �      �    .pdata      �         p騺{�              �    .xdata      �          �9��        |      �    .pdata      �         菏珔        �      �    .xdata      �          h2]        g      �    .pdata      �         �+�]        �      �    .xdata      �   <       洯;        �      �    .pdata      �         ��;              �    .xdata      �          �?槖y        U      �    .pdata      �         vy        �      �    .xdata      �          M�"Da        �      �    .pdata      �         觉强a        <      �    .xdata      �          l骩廵        �      �    .pdata      �         哅te        �      �    .xdata      �          w;$E        D      �    .pdata      �         鉙gIE        �      �    .xdata      �         No擔E        �      �    .pdata      �         扥	E        [      �    .xdata      �         �3�E        �      �    .pdata      �         蹲�E              �    .xdata      �         ;;糆        u      �    .pdata      �         ��E        �      �    .xdata      �         \XE        1       �    .pdata      �         B�E        �       �    .xdata      �         Z=E        �       �    .pdata      �         <r婨        K!      �    .xdata      �         魓癊        �!      �    .pdata      �         杏�
E        "      �    .xdata      �         �0餎        e"      �    .pdata      �         �糙E        �"      �    .xdata      �          （亵O        !#      �    .pdata      �         A鶬揙        z#      �    .xdata      �         ［DM        �#      �    .pdata      �         \閑M        $$      �    .xdata      �   	      � )9M        u$      �    .xdata      �         jM        �$      �    .xdata      �          2k飻M        #%      �    .voltbl     �          �)`SM    _volmd      �    .xdata      �          j� _{        w%      �    .pdata      �          ~        �%      �    .xdata      �         駭�({        �%      �    .pdata      �         鴢Z{        @&      �    .xdata      �         敦燞{        �&      �    .pdata      �         �5瘂        �&      �    .xdata      �         鞌K{        '      �    .pdata      �         �>燹{        P'      �    .voltbl     �          挗/焮    _volmd      �    .xdata      �          �/h�        �'      �    .pdata      �         }S蛥        �'      �    .xdata      �         曂�+        ?(      �    .pdata      �         3q弉        �(      �    .xdata      �         懝�<        �(      �    .pdata      �         ^鱩        D)      �    .voltbl     �          Q    _volmd      �    .xdata      �          （亵}        �)      �    .pdata      �         嘳�}        *      �    .xdata      �         滪鵎}        �*      �    .pdata      �         �V}        �*      �    .xdata      �         k沸竲        h+      �    .pdata      �         �5n}        �+      �    .voltbl     �          �8@~}    _volmd      �    .xdata      �          1�7[        P,      �    .pdata      �         A薪餥        �,      �    .xdata      �          �9�o        �,      �    .pdata      �         壧}ao        -      �    .xdata      �          �9�i        N-      �    .pdata      �         羲X#i        �-      �    .xdata      �          �9�s        �-      �    .pdata      �         壧}as        .      �    .xdata      �          �9�Q        V.      �    .pdata      �         羲X#Q        �.      �    .xdata      �          �9�U        �.      �    .pdata      �         羲X#U         /      �    .xdata      �          ��		        ^/      �    .pdata      �         =�c	        �/      �    .xdata      �          NTo�+        �/      �    .pdata      �         暫`g+        $0      �    .xdata      �   8       	SX/        j0      �    .pdata      �         狑�/        �0      �    .xdata      �          %*1        1      �    .pdata      �         S        _1      �    .xdata      �   $       鰏�1        �1      �    .pdata      �         @�3!1        2      �    .xdata      �         债�%        [2      �    .pdata      �         s�+A%        3      �    .xdata      �         Mw2�%        �3      �    .xdata      �          �.e�%        �4      �    .voltbl     �          -=m%    _volmd      �    .xdata      �          ,t 
        Z5      �    .pdata      �         v��
        �5      �    .xdata      �          ]件-        6      �    .pdata      �         暫`g-        _6      �    .xdata      �           閛彣        �6      �    .pdata      �         璙Zk        �6      �        77           .data       �           烀�          J7      �        ~7     �    .rdata      �           �/*         �7      �    .data$r     �   /      %�)         �7      �    .data$r     �   5      &覜_         8      �    .rdata      �          瑣�#         38      �    .rdata                =-f�         C8          .rdata               8$.�         S8         .rdata               v靛�         c8         .rdata               b(         s8         .rdata               s鷾}         �8         .rdata               �8WV         �8         .rdata               �         �8         .rdata               V
         �8         .rdata               炕锘         �8         .rdata      	         
#貜         �8      	   .rdata      
         z�         �8      
   .rdata               �a�         
9         _fltused         .chks64       `                19  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z __std_terminate ?_Xout_of_range@std@@YAXPEBD@Z ?cartesianToSpherical@math@donut@@YAXAEBU?$vector@M$02@12@AEAM11@Z ?BaseLookAt@BaseCamera@app@donut@@IEAAXU?$vector@M$02@math@3@00@Z ?UpdateWorldToView@BaseCamera@app@donut@@IEAAXXZ ?KeyboardUpdate@FirstPersonCamera@app@donut@@UEAAXHHHH@Z ?MousePosUpdate@FirstPersonCamera@app@donut@@UEAAXNN@Z ?MouseButtonUpdate@FirstPersonCamera@app@donut@@UEAAXHHH@Z ?Animate@FirstPersonCamera@app@donut@@UEAAXM@Z ?AnimateSmooth@FirstPersonCamera@app@donut@@QEAAXM@Z ?LookAt@FirstPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@00@Z ?LookTo@FirstPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@00@Z ?AnimateRoll@FirstPersonCamera@app@donut@@AEAA?AU?$pair@_NU?$affine@M$02@math@donut@@@std@@U?$affine@M$02@math@3@@Z ?AnimateTranslation@FirstPersonCamera@app@donut@@AEAA?AU?$pair@_NU?$vector@M$02@math@donut@@@std@@M@Z ?UpdateCamera@FirstPersonCamera@app@donut@@AEAAXU?$vector@M$02@math@3@U?$affine@M$02@53@@Z ?at@?$unordered_map@HHU?$hash@H@std@@U?$equal_to@H@2@V?$allocator@U?$pair@$$CBHH@std@@@2@@std@@QEBAAEBHAEBH@Z ?KeyboardUpdate@ThirdPersonCamera@app@donut@@UEAAXHHHH@Z ?MousePosUpdate@ThirdPersonCamera@app@donut@@UEAAXNN@Z ?MouseButtonUpdate@ThirdPersonCamera@app@donut@@UEAAXHHH@Z ?MouseScrollUpdate@ThirdPersonCamera@app@donut@@UEAAXNN@Z ?JoystickButtonUpdate@ThirdPersonCamera@app@donut@@UEAAXH_N@Z ?JoystickUpdate@ThirdPersonCamera@app@donut@@UEAAXHM@Z ?Animate@ThirdPersonCamera@app@donut@@UEAAXM@Z ?SetRotation@ThirdPersonCamera@app@donut@@QEAAXMM@Z ?SetView@ThirdPersonCamera@app@donut@@QEAAXAEBVPlanarView@engine@3@@Z ?LookAt@ThirdPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@0@Z ?LookTo@ThirdPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@0V?$optional@M@std@@@Z ?AnimateOrbit@ThirdPersonCamera@app@donut@@AEAAXM@Z ?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z ?GetActiveUserCamera@SwitchableCamera@app@donut@@QEAAPEAVBaseCamera@23@XZ ?GetActiveUserCamera@SwitchableCamera@app@donut@@QEBAPEBVBaseCamera@23@XZ ?GetWorldToViewMatrix@SwitchableCamera@app@donut@@QEBA?AU?$affine@M$02@math@3@XZ ?GetSceneCameraProjectionParams@SwitchableCamera@app@donut@@QEBA_NAEAM0@Z ?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z ?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z ?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z ?KeyboardUpdate@SwitchableCamera@app@donut@@QEAA_NHHHH@Z ?MousePosUpdate@SwitchableCamera@app@donut@@QEAA_NNN@Z ?MouseButtonUpdate@SwitchableCamera@app@donut@@QEAA_NHHH@Z ?MouseScrollUpdate@SwitchableCamera@app@donut@@QEAA_NNN@Z ?JoystickButtonUpdate@SwitchableCamera@app@donut@@QEAA_NH_N@Z ?JoystickUpdate@SwitchableCamera@app@donut@@QEAA_NHM@Z ?Animate@SwitchableCamera@app@donut@@QEAAXM@Z ?GetViewToWorldMatrix@SceneCamera@engine@donut@@QEBA?AU?$affine@M$02@math@3@XZ ?GetWorldToViewMatrix@SceneCamera@engine@donut@@QEBA?AU?$affine@M$02@math@3@XZ ??$translation@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z ??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z ??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z ??$?YM@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@AEBU201@@Z ??$rotation@M@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@M@Z ??$?YM@math@donut@@YAAEAU?$vector@M$01@01@AEAU201@AEBU201@@Z ??$?XM@math@donut@@YAAEAU?$vector@M$01@01@AEAU201@M@Z ??$?DM@math@donut@@YA?AU?$vector@M$03@01@AEBU201@AEBU?$matrix@M$03$03@01@@Z ??$?_0M@math@donut@@YAAEAU?$vector@M$03@01@AEAU201@M@Z ??$?ZM@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@AEBU201@@Z ??$length@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$rotationQuat@M@math@donut@@YA?AU?$quaternion@M@01@AEBU?$vector@M$02@01@@Z ??$?_0M@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@M@Z ??$dynamic_pointer_cast@VPerspectiveCamera@engine@donut@@VSceneCamera@23@@std@@YA?AV?$shared_ptr@VPerspectiveCamera@engine@donut@@@0@AEBV?$shared_ptr@VSceneCamera@engine@donut@@@0@@Z ??$_Hash_representation@H@std@@YA_KAEBH@Z ??$?DM$02$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@0@Z ??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z ??$diagonal@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@M@Z ??$?DM$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@M@Z ??$?HM$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@0@Z ??$outerProduct@M$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU?$vector@M$02@01@0@Z ??$lengthSquared@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$?DM@math@donut@@YA?AU?$quaternion@M@01@AEBU201@0@Z ??$_Fnv1a_append_value@H@std@@YA_K_KAEBH@Z __CxxFrameHandler4 __RTDynamicCast $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$?BaseLookAt@BaseCamera@app@donut@@IEAAXU?$vector@M$02@math@3@00@Z $pdata$?BaseLookAt@BaseCamera@app@donut@@IEAAXU?$vector@M$02@math@3@00@Z $unwind$?UpdateWorldToView@BaseCamera@app@donut@@IEAAXXZ $pdata$?UpdateWorldToView@BaseCamera@app@donut@@IEAAXXZ $unwind$?KeyboardUpdate@FirstPersonCamera@app@donut@@UEAAXHHHH@Z $pdata$?KeyboardUpdate@FirstPersonCamera@app@donut@@UEAAXHHHH@Z $unwind$?MouseButtonUpdate@FirstPersonCamera@app@donut@@UEAAXHHH@Z $pdata$?MouseButtonUpdate@FirstPersonCamera@app@donut@@UEAAXHHH@Z $unwind$?Animate@FirstPersonCamera@app@donut@@UEAAXM@Z $pdata$?Animate@FirstPersonCamera@app@donut@@UEAAXM@Z $unwind$?AnimateSmooth@FirstPersonCamera@app@donut@@QEAAXM@Z $pdata$?AnimateSmooth@FirstPersonCamera@app@donut@@QEAAXM@Z $unwind$?LookAt@FirstPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@00@Z $pdata$?LookAt@FirstPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@00@Z $unwind$?LookTo@FirstPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@00@Z $pdata$?LookTo@FirstPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@00@Z $unwind$?AnimateRoll@FirstPersonCamera@app@donut@@AEAA?AU?$pair@_NU?$affine@M$02@math@donut@@@std@@U?$affine@M$02@math@3@@Z $pdata$?AnimateRoll@FirstPersonCamera@app@donut@@AEAA?AU?$pair@_NU?$affine@M$02@math@donut@@@std@@U?$affine@M$02@math@3@@Z $unwind$?AnimateTranslation@FirstPersonCamera@app@donut@@AEAA?AU?$pair@_NU?$vector@M$02@math@donut@@@std@@M@Z $pdata$?AnimateTranslation@FirstPersonCamera@app@donut@@AEAA?AU?$pair@_NU?$vector@M$02@math@donut@@@std@@M@Z $unwind$?UpdateCamera@FirstPersonCamera@app@donut@@AEAAXU?$vector@M$02@math@3@U?$affine@M$02@53@@Z $pdata$?UpdateCamera@FirstPersonCamera@app@donut@@AEAAXU?$vector@M$02@math@3@U?$affine@M$02@53@@Z $unwind$?at@?$unordered_map@HHU?$hash@H@std@@U?$equal_to@H@2@V?$allocator@U?$pair@$$CBHH@std@@@2@@std@@QEBAAEBHAEBH@Z $pdata$?at@?$unordered_map@HHU?$hash@H@std@@U?$equal_to@H@2@V?$allocator@U?$pair@$$CBHH@std@@@2@@std@@QEBAAEBHAEBH@Z $unwind$?KeyboardUpdate@ThirdPersonCamera@app@donut@@UEAAXHHHH@Z $pdata$?KeyboardUpdate@ThirdPersonCamera@app@donut@@UEAAXHHHH@Z $unwind$?Animate@ThirdPersonCamera@app@donut@@UEAAXM@Z $pdata$?Animate@ThirdPersonCamera@app@donut@@UEAAXM@Z $unwind$?SetView@ThirdPersonCamera@app@donut@@QEAAXAEBVPlanarView@engine@3@@Z $pdata$?SetView@ThirdPersonCamera@app@donut@@QEAAXAEBVPlanarView@engine@3@@Z $unwind$?LookAt@ThirdPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@0@Z $pdata$?LookAt@ThirdPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@0@Z $unwind$?LookTo@ThirdPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@0V?$optional@M@std@@@Z $pdata$?LookTo@ThirdPersonCamera@app@donut@@QEAAXU?$vector@M$02@math@3@0V?$optional@M@std@@@Z $unwind$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$0$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$0$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$2$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$2$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$4$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$4$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$5$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$5$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$6$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$6$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$7$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$7$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $chain$8$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $pdata$8$?AnimateTranslation@ThirdPersonCamera@app@donut@@AEAAXAEBU?$matrix@M$02$02@math@3@@Z $unwind$?GetWorldToViewMatrix@SwitchableCamera@app@donut@@QEBA?AU?$affine@M$02@math@3@XZ $pdata$?GetWorldToViewMatrix@SwitchableCamera@app@donut@@QEBA?AU?$affine@M$02@math@3@XZ $unwind$?GetSceneCameraProjectionParams@SwitchableCamera@app@donut@@QEBA_NAEAM0@Z $pdata$?GetSceneCameraProjectionParams@SwitchableCamera@app@donut@@QEBA_NAEAM0@Z $cppxdata$?GetSceneCameraProjectionParams@SwitchableCamera@app@donut@@QEBA_NAEAM0@Z $stateUnwindMap$?GetSceneCameraProjectionParams@SwitchableCamera@app@donut@@QEBA_NAEAM0@Z $ip2state$?GetSceneCameraProjectionParams@SwitchableCamera@app@donut@@QEBA_NAEAM0@Z $unwind$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $pdata$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $chain$1$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $pdata$1$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $chain$2$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $pdata$2$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $chain$3$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $pdata$3$?SwitchToFirstPerson@SwitchableCamera@app@donut@@QEAAX_N@Z $unwind$?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z $pdata$?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z $chain$0$?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z $pdata$0$?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z $chain$1$?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z $pdata$1$?SwitchToThirdPerson@SwitchableCamera@app@donut@@QEAAX_NV?$optional@M@std@@@Z $unwind$?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z $pdata$?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z $chain$0$?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z $pdata$0$?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z $chain$1$?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z $pdata$1$?SwitchToSceneCamera@SwitchableCamera@app@donut@@QEAAXAEBV?$shared_ptr@VSceneCamera@engine@donut@@@std@@@Z $unwind$?KeyboardUpdate@SwitchableCamera@app@donut@@QEAA_NHHHH@Z $pdata$?KeyboardUpdate@SwitchableCamera@app@donut@@QEAA_NHHHH@Z $unwind$?MousePosUpdate@SwitchableCamera@app@donut@@QEAA_NNN@Z $pdata$?MousePosUpdate@SwitchableCamera@app@donut@@QEAA_NNN@Z $unwind$?MouseButtonUpdate@SwitchableCamera@app@donut@@QEAA_NHHH@Z $pdata$?MouseButtonUpdate@SwitchableCamera@app@donut@@QEAA_NHHH@Z $unwind$?MouseScrollUpdate@SwitchableCamera@app@donut@@QEAA_NNN@Z $pdata$?MouseScrollUpdate@SwitchableCamera@app@donut@@QEAA_NNN@Z $unwind$?JoystickButtonUpdate@SwitchableCamera@app@donut@@QEAA_NH_N@Z $pdata$?JoystickButtonUpdate@SwitchableCamera@app@donut@@QEAA_NH_N@Z $unwind$?JoystickUpdate@SwitchableCamera@app@donut@@QEAA_NHM@Z $pdata$?JoystickUpdate@SwitchableCamera@app@donut@@QEAA_NHM@Z $unwind$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $pdata$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $unwind$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $pdata$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $unwind$??$rotation@M@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@M@Z $pdata$??$rotation@M@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@M@Z $unwind$??$?DM@math@donut@@YA?AU?$vector@M$03@01@AEBU201@AEBU?$matrix@M$03$03@01@@Z $pdata$??$?DM@math@donut@@YA?AU?$vector@M$03@01@AEBU201@AEBU?$matrix@M$03$03@01@@Z $unwind$??$rotationQuat@M@math@donut@@YA?AU?$quaternion@M@01@AEBU?$vector@M$02@01@@Z $pdata$??$rotationQuat@M@math@donut@@YA?AU?$quaternion@M@01@AEBU?$vector@M$02@01@@Z $unwind$??$dynamic_pointer_cast@VPerspectiveCamera@engine@donut@@VSceneCamera@23@@std@@YA?AV?$shared_ptr@VPerspectiveCamera@engine@donut@@@0@AEBV?$shared_ptr@VSceneCamera@engine@donut@@@0@@Z $pdata$??$dynamic_pointer_cast@VPerspectiveCamera@engine@donut@@VSceneCamera@23@@std@@YA?AV?$shared_ptr@VPerspectiveCamera@engine@donut@@@0@AEBV?$shared_ptr@VSceneCamera@engine@donut@@@0@@Z $cppxdata$??$dynamic_pointer_cast@VPerspectiveCamera@engine@donut@@VSceneCamera@23@@std@@YA?AV?$shared_ptr@VPerspectiveCamera@engine@donut@@@0@AEBV?$shared_ptr@VSceneCamera@engine@donut@@@0@@Z $ip2state$??$dynamic_pointer_cast@VPerspectiveCamera@engine@donut@@VSceneCamera@23@@std@@YA?AV?$shared_ptr@VPerspectiveCamera@engine@donut@@@0@AEBV?$shared_ptr@VSceneCamera@engine@donut@@@0@@Z $unwind$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $pdata$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $unwind$??$outerProduct@M$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU?$vector@M$02@01@0@Z $pdata$??$outerProduct@M$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU?$vector@M$02@01@0@Z $unwind$??$?DM@math@donut@@YA?AU?$quaternion@M@01@AEBU201@0@Z $pdata$??$?DM@math@donut@@YA?AU?$quaternion@M@01@AEBU201@0@Z ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0CA@MMOGIFNF@invalid?5unordered_map?$DMK?0?5T?$DO?5key@ ??_R0?AVSceneCamera@engine@donut@@@8 ??_R0?AVPerspectiveCamera@engine@donut@@@8 __real@3dcccccd __real@3f000000 __real@3f5e9bd4 __real@3f800000 __real@3f933333 __real@3fc00000 __real@3fc90fdb __real@40400000 __real@42200000 __real@bfc90fdb __real@c0f00000 __xmm@0000000000000000000000003f800000 __xmm@80000000800000008000000080000000 