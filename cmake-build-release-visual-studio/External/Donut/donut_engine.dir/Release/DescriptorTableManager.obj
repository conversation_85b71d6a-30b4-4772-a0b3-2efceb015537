d啚翀澻 �      .drectve        <  腀               
 .debug$S        ,�   B  ,=        @ B.debug$T        p   h=             @ B.rdata          0   �=             @ @@.text$mn        :   > B>         P`.debug$S          `> l@        @B.text$mn        0   鳣 (A         P`.debug$S        �  2A 轇        @B.text$mn        0   jC 欳         P`.debug$S        �   `E        @B.text$mn           霦 鸈         P`.debug$S        $  F )G        @B.text$mn        c   yG              P`.debug$S        |  蹽 XI        @B.text$mn           蠭              P`.debug$S        l  錓 QK     
   @B.text$mn           礙              P`.debug$S        p  蔏 :M     
   @B.text$mn           濵              P`.debug$S        t  矼 'O     
   @B.text$mn        c   婳              P`.debug$S        x  頞 fQ        @B.text$mn        p   轖              P`.debug$S        �  NR 釹        @B.text$mn           ZT              P`.debug$S        X  yT 裊        @B.text$mn           !V              P`.debug$S        \  @V 淲        @B.text$mn           霿              P`.debug$S        `  X kY        @B.text$mn        p   籝              P`.debug$S        �  +Z 籟        @B.text$mn           3\              P`.debug$S        �   :\ ]        @B.text$mn        �  Z] 靆         P`.debug$S        P  <_ 宩     ^   @B.text$x         (   8n `n         P`.text$mn        =  tn 眔         P`.debug$S        <
  鱫 3z     P   @B.text$x         (   S} {}         P`.text$mn          弣 焵         P`.debug$S        �  鍊 艔     \   @B.text$x            ]� i�         P`.text$x            s� �         P`.text$mn        �   墦              P`.debug$S        �  
� 瓥        @B.text$mn        �   M� 鷹         P`.debug$S        <  � @�        @B.text$mn        �   0�              P`.debug$S        D  艥 	�        @B.text$mn        G   暊              P`.debug$S        �  軤 丌        @B.text$mn        G   P�              P`.debug$S          棧 洢        @B.text$mn        G   �              P`.debug$S          Z� b�        @B.text$mn        �   讪              P`.debug$S        D  o� 倡        @B.text$mn            ?�              P`.debug$S        �  _� [�        @B.text$mn        )                 P`.debug$S        �  $� �     
   @B.text$mn           |�              P`.debug$S        8  惒 瘸        @B.text$mn        �  � 旱         P`.debug$S        �  斓 溑     `   @B.text$x            \� h�         P`.text$x            r� 偵         P`.text$x            屔 溕         P`.text$x            ι 渡         P`.text$x            郎 猩         P`.text$x            谏 晟         P`.text$x            羯 �         P`.text$x            � �         P`.text$mn        <   (� d�         P`.debug$S        0  偸 菜     
   @B.text$mn        <   � R�         P`.debug$S        L  p� 纪     
   @B.text$mn        !    � A�         P`.debug$S        <  U� 懴        @B.text$mn        <   拖 	�         P`.debug$S        8  '� _�     
   @B.text$mn           醚 匮         P`.debug$S        �   庋 忠        @B.text$mn        2   � D�         P`.debug$S        <  X� 斣        @B.text$mn        "   �              P`.debug$S        �  .� 手        @B.text$mn        "   j�              P`.debug$S        �  屪 �        @B.text$mn           纲 藤         P`.debug$S          仲 廑        @B.text$mn        �   2� 哲         P`.debug$S        �   併        @B.text$mn        [   欎 翡         P`.debug$S        �  � 惕        @B.text$mn           ㄩ 奸         P`.debug$S        (  崎 铍        @B.text$mn            >�              P`.debug$S        �  ^� &�     
   @B.text$mn        \   婎 骖         P`.debug$S        �   烌        @B.text$mn        K   R�              P`.debug$S        �  濗 咍        @B.text$mn           � �         P`.debug$S        L   � l�        @B.text$mn        [   旞 秫         P`.debug$S          � �        @B.text$mn        [   稂 J�         P`.debug$S        �  ^� R         @B.text$mn          . 1         P`.debug$S        L	  O �     H   @B.text$mn        L  k �         P`.debug$S        ,  �      B   @B.text$mn           � �         P`.debug$S        �   � �        @B.text$mn           � �         P`.debug$S        �   � �        @B.text$mn           �          P`.debug$S        �    �        @B.text$mn        o   4              P`.debug$S        $  � �      
   @B.text$mn        �  +!              P`.debug$S        8  (# `+        @B.text$mn        B   d, �,         P`.debug$S           �, �-        @B.text$mn        B    . B.         P`.debug$S          `. p/        @B.text$mn        B   �/ �/         P`.debug$S          0 1        @B.text$mn        B   L1 �1         P`.debug$S        �   �1 �2        @B.text$mn          �2 �4         P`.debug$S        �  $5  =     D   @B.text$mn        �   �? 咢         P`.debug$S          欯 濪        @B.text$mn        F   zE              P`.debug$S        �  繣 lG        @B.text$mn          糋              P`.debug$S        �  荋 矼     $   @B.text$x            O 'O         P`.text$mn        W  1O 圥         P`.debug$S        �   2W     $   @B.text$mn        :  歑 訷         P`.debug$S        x  Z 坅     <   @B.text$mn          郼 鑕         P`.debug$S        \  f lq     P   @B.text$mn        )  宼 祐         P`.debug$S        	  觱 �     *   @B.text$mn            � 焷         P`.debug$S        �   絹 亗        @B.text$mn            絺 輦         P`.debug$S        �   麄 穬        @B.text$mn           髢 �         P`.debug$S        �   � 鑴        @B.text$mn           $� 5�         P`.debug$S        �   I� -�        @B.text$mn           i� z�         P`.debug$S        �   巻 唶        @B.text$mn        B   聡 �         P`.debug$S        �  � 笂        @B.text$mn        A   紜 龐         P`.debug$S        �  � 綆        @B.text$mn        q  翉 2�         P`.debug$S        �
  F� 鏈     >   @B.text$mn        �  R� 浴         P`.debug$S          颉 �     6   @B.text$mn           "� *�         P`.debug$S        �   4� �        @B.text$mn           P� c�         P`.debug$S        �   m� A�        @B.xdata             }�             @0@.pdata             叜 懏        @0@.xdata                          @0@.pdata             划 钱        @0@.xdata             瀹             @0@.pdata             懋         @0@.xdata             �             @0@.pdata             #� /�        @0@.xdata             M�             @0@.pdata             U� a�        @0@.xdata             �             @0@.pdata             嫰 棷        @0@.xdata             弹             @0@.pdata             蒋 莎        @0@.xdata             绡             @0@.pdata             锆         @0@.xdata             �             @0@.pdata             %� 1�        @0@.xdata             O�             @0@.pdata             W� c�        @0@.voltbl            伆               .xdata              偘         @0@.pdata             栋 掳        @0@.xdata             喟 灏        @@.xdata             锇             @@.voltbl         
   蟀                .xdata              �        @0@.pdata             )� 5�        @0@.xdata          	   S� \�        @@.xdata             p� w�        @@.xdata             伇             @@.voltbl            劚               .voltbl            姳               .xdata             嫳             @0@.pdata             棻 １        @0@.xdata             帘 驯        @0@.pdata             灞 癖        @0@.xdata          	   � �        @@.xdata             ,� 2�        @@.xdata             <�             @@.xdata             ?� O�        @0@.pdata             c� o�        @0@.xdata          	   嵅 柌        @@.xdata              安        @@.xdata             翰             @@.xdata             讲             @0@.pdata             挪 巡        @0@.xdata             锊             @0@.pdata             鞑 �        @0@.xdata             !�             @0@.pdata             )� 5�        @0@.xdata             S�             @0@.pdata             [� g�        @0@.xdata             叧 櫝        @0@.pdata             烦 贸        @0@.xdata             岢 癯        @0@.pdata             � �        @0@.xdata             9�             @0@.pdata             E� Q�        @0@.xdata             o� 嫶        @0@.pdata             ┐ 荡        @0@.xdata             哟 绱        @0@.pdata             � �        @0@.xdata             /� ?�        @0@.pdata             ]� i�        @0@.xdata              嚨 У        @0@.pdata             诺 训        @0@.xdata             锏 ��        @0@.pdata             � )�        @0@.xdata             G�             @0@.pdata             S� _�        @0@.xdata             }� 暥        @0@.pdata             扯 慷        @0@.xdata             荻 矶        @0@.pdata             � �        @0@.xdata             5� M�        @0@.pdata             k� w�        @0@.xdata             暦             @0@.pdata             澐 ┓        @0@.xdata             欠             @0@.pdata             臃 叻        @0@.xdata                          @0@.pdata             � �        @0@.xdata             /�             @0@.pdata             7� C�        @0@.xdata             a�             @0@.pdata             i� u�        @0@.xdata             摳             @0@.pdata             ８         @0@.xdata             透 岣        @0@.pdata             �� �        @0@.xdata             )� =�        @0@.pdata             [� g�        @0@.xdata             吂 暪        @0@.pdata             彻 抗        @0@.xdata             莨 砉        @0@.pdata             � �        @0@.xdata             5� M�        @0@.pdata             k� w�        @0@.xdata             暫 ズ        @0@.pdata             煤 虾        @0@.xdata             砗 	�        @0@.pdata             � )�        @0@.xdata             G� L�        @@.xdata             V�             @@.xdata             Z�             @0@.pdata             f� r�        @0@.xdata             惢 せ        @0@.pdata             禄 位        @0@.xdata             旎  �        @0@.pdata             � *�        @0@.xdata             H� X�        @0@.pdata             v� 偧        @0@.xdata             牸 凹        @0@.pdata             渭 诩        @0@.xdata              �        @0@.pdata             *� 6�        @0@.xdata             T� d�        @0@.pdata             偨 幗        @0@.xdata                          @0@.pdata             唇 澜        @0@.xdata             藿         @0@.pdata             � �        @0@.xdata          	   8� A�        @@.xdata          4   U� 壘     
   @@.xdata             砭             @@.xdata              �        @0@.pdata             -� 9�        @0@.xdata          	   W� `�        @@.xdata             t� z�        @@.xdata             効             @@.voltbl            尶                .xdata             幙             @0@.pdata             灴         @0@.xdata              瓤 杩        @0@.pdata             � �        @0@.xdata             0� D�        @0@.pdata             b� n�        @0@.xdata             尷 溊        @0@.pdata             豪 评        @0@.xdata             淅         @0@.pdata             � "�        @0@.xdata             @� P�        @0@.pdata             n� z�        @0@.xdata             樍         @0@.pdata             屏 伊        @0@.xdata             鹆             @0@.pdata              � �        @0@.voltbl            *�               .xdata             /�             @0@.pdata             G� S�        @0@.xdata             q�             @0@.pdata             y� 吢        @0@.xdata             Ｂ 仿        @0@.pdata             章 崧        @0@.xdata             �� �        @0@.pdata             -� 9�        @0@.voltbl            W�               .xdata             Y�             @0@.pdata             i� u�        @0@.xdata          $   撁 访        @0@.pdata             嗣 酌        @0@.xdata          	   趺         @@.xdata          
   � �        @@.xdata          
   3�             @@.xdata             =�             @0@.pdata             E� Q�        @0@.xdata             o� 兡        @0@.pdata             ∧         @0@.xdata             四 勰        @0@.pdata              �        @0@.xdata             #� ;�        @0@.pdata             O� [�        @0@.xdata          
   y� 喤        @@.xdata             づ             @@.xdata                      @@.xdata             古 琅        @@.xdata             逝             @@.xdata             遗             @0@.pdata             谂 媾        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             ;� W�        @0@.pdata             k� w�        @0@.xdata          
   暺 ⑵        @@.xdata             榔             @@.xdata             闷 似        @@.xdata             掌 芷        @@.xdata          	   嫫             @@.xdata             锲             @0@.pdata             髌 �        @0@.voltbl            !�               .xdata             "�             @0@.pdata             *� 6�        @0@.xdata             T�             @0@.pdata             `� l�        @0@.rdata             娗 ⑶        @@@.rdata             狼             @@@.rdata             仪 昵        @@@.rdata             �  �        @@@.rdata             >�             @@@.xdata$x           S� o�        @@@.xdata$x           內 熑        @@@.data$r         /   饺 烊        @@�.xdata$x        $   鋈 �        @@@.data$r         $   .� R�        @@�.xdata$x        $   \� ��        @@@.data$r         $   斏 干        @@�.xdata$x        $   律 嫔        @@@.data                            @ @�.rdata             � 2�        @@@.rdata          
   P�             @@@.xdata$x           ]� y�        @@@.xdata$x           嵤 ∈        @@@.data$r         '   凳 苁        @@�.xdata$x        $   媸 
�        @@@.rdata             �             @@@.rdata             4�             @@@.rdata             O�             @@@.rdata             i�             @@@.rdata$r        $   y� 澦        @@@.rdata$r           凰 纤        @@@.rdata$r           偎 逅        @@@.rdata$r        $   锼 �        @@@.rdata$r        $   '� K�        @@@.rdata$r           i� }�        @@@.rdata$r           囂 浱        @@@.rdata$r        $    犹        @@@.rdata$r        $   缣 �        @@@.rdata$r           )� =�        @@@.rdata$r           G� c�        @@@.rdata$r        $   佂 ネ        @@@.rdata$r        $   雇 萃        @@@.rdata$r            �        @@@.rdata$r           � -�        @@@.rdata$r        $   A� e�        @@@.rdata             y�             @0@.debug$S        8   }� 滴        @B.debug$S        4   晌         @B.debug$S        4   � E�        @B.debug$S        @   Y� 櫹        @B.chks64         �                
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �  t     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\DescriptorTableManager.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $literals  $string_literals  $string_view_literals  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  �   ;    �        nvrhi::EntireBuffer L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos A    std::allocator<char>::_Minimum_asan_allocation_alignment ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask  �   �'  e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos + <        nvrhi::rt::c_IdentityTransform D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact R    std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , d  	 std::numeric_limits<long>::digits10 P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity 0 �   std::numeric_limits<__int64>::is_signed - d  ? std::numeric_limits<__int64>::digits / d   std::numeric_limits<__int64>::digits10 d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 7 �   std::numeric_limits<unsigned short>::is_modulo 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 i    std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10 o �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Same_size_and_compatible �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard l �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Bitcopy_constructible i �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Bitcopy_assignable " �    std::memory_order_relaxed " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst  �   �   �    std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment  �   �  4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos :    std::integral_constant<unsigned __int64,1>::value �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment :     std::integral_constant<unsigned __int64,0>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy A    std::allocator<bool>::_Minimum_asan_allocation_alignment I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex � �   std::_Trivial_cat<nvrhi::BindingSetItem,nvrhi::BindingSetItem,nvrhi::BindingSetItem &&,nvrhi::BindingSetItem &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::BindingSetItem,nvrhi::BindingSetItem,nvrhi::BindingSetItem &&,nvrhi::BindingSetItem &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::BindingSetItem,nvrhi::BindingSetItem,nvrhi::BindingSetItem &&,nvrhi::BindingSetItem &>::_Bitcopy_assignable Z E    std::_Vb_iter_base<std::_Wrap_alloc<std::allocator<unsigned int> > >::_VBITS_DIFF � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable :    std::integral_constant<unsigned __int64,2>::value  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos # �        nvrhi::AllSubresources J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos  �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> & �5  $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e  �5  _TypeDescriptor 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const> E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16 G >  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> >  i5  std::input_iterator_tag ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> > . �2  std::integer_sequence<unsigned __int64>  .  std::_Lockit y X<  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > * 2/  std::hash<enum nvrhi::ResourceType> " i3  std::_Char_traits<char,int>  X:  std::_Value_init_tag � :  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �2  std::_Num_base K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > ) v3  std::_Narrow_char_traits<char,int>    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc> M =  std::_Uninitialized_backout_al<std::allocator<nvrhi::BindingSetItem> >  �2  std::_Num_int_base � �=  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > / Q/  std::_Conditionally_enabled_hash<bool,1> � �8  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �2  std::float_denorm_style �  ;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > ! %0  std::piecewise_construct_t 6 �5  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast 6 #>  std::_Wrap_alloc<std::allocator<unsigned int> > " �2  std::numeric_limits<double>  <&  std::__non_rtti_object < �6  std::_Ptr_base<donut::engine::DescriptorTableManager> ( n  std::_Basic_container_proxy_ptr12 > �:  std::vector<unsigned int,std::allocator<unsigned int> > T �:  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety P �7  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f �7  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �5  std::char_traits<char32_t> C G:  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >   �2  std::numeric_limits<bool> J �9  std::_Vb_iterator<std::_Wrap_alloc<std::allocator<unsigned int> > > # �3  std::_WChar_traits<char16_t> T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> % --  std::_One_then_variadic_args_t W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * �8  std::_Vb_val<std::allocator<bool> >   �5  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> K {9  std::_Vb_iter_base<std::_Wrap_alloc<std::allocator<unsigned int> > > n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  �5  std::false_type  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> D ==  std::_Uninitialized_backout_al<std::allocator<unsigned int> > w L<  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , �2  std::numeric_limits<unsigned __int64> \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > P �9  std::_Vb_const_iterator<std::_Wrap_alloc<std::allocator<unsigned int> > > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> � 
;  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > = W7  std::shared_ptr<donut::engine::DescriptorTableManager> % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound C :  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > 0 u<  std::tuple<nvrhi::BindingSetItem const &> h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> � [;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 | �=  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  U/  std::hash<bool> 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> d �=  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  (  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy M �=  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> / �3  std::_Char_traits<char32_t,unsigned int> ( 1$  std::hash<nvrhi::FramebufferInfo> * !>  std::initializer_list<unsigned int> % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t L >  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s >  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > >  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128> N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > ! �2  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  t  std::exception_ptr C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > : �;  std::_Vector_val<std::_Simple_types<unsigned int> > D >  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t> J �6  std::enable_shared_from_this<donut::engine::DescriptorTableManager>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 �5  std::allocator_traits<std::allocator<char32_t> >  �  std::_Iterator_base0 � >  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> � |;  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> $ �#  std::hash<nvrhi::BufferRange> ! �5  std::char_traits<char16_t>  |  std::tuple<>    std::_Container_base12 2 r9  std::pair<nvrhi::BindingSetItem const ,int> 0 �2  std::integer_sequence<unsigned __int64,0> ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char>  x5  std::_Invoker_strategy $ �2  std::_Default_allocate_traits 3 �5  std::allocator_traits<std::allocator<char> > ! �2  std::numeric_limits<short>  u   std::_Vbase . �0  std::allocator<nvrhi::rt::GeometryDesc> ; �  std::basic_string_view<char,std::char_traits<char> > I �=  std::pointer_traits<std::pair<nvrhi::BindingSetItem const ,int> *> "T8  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > 0�<  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Clear_guard C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> � D9  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,std::_Iterator_base0> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock } ,;  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > >  D  std::bad_alloc y V9  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> J >  std::initializer_list<std::pair<nvrhi::BindingSetItem const ,int> > # �2  std::numeric_limits<__int64>  �  std::memory_order # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> X �;  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<unsigned int> > > < T3  std::_Default_allocator_traits<std::allocator<char> > R �;  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<unsigned int> > > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   k5  std::forward_iterator_tag   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>    std::_Container_proxy  �9  std::allocator<bool>  �  std::nested_exception  r  std::_Distance_unknown ( �2  std::numeric_limits<unsigned int> @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double> F;  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � ;:  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> s e9  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > >  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::allocator<char16_t> . 9  std::vector<bool,std::allocator<bool> > ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy a '<  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> * r5  std::_String_constructor_concat_tag  D-  std::allocator<char> { �=  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > 5 a=  std::_Tuple_val<nvrhi::BindingSetItem const &> ] i;  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >    std::nullptr_t & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1>  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long> , 3:  std::allocator<nvrhi::BindingSetItem>   �-  std::_Atomic_padded<long> # �:  std::allocator<unsigned int> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring ' �2  std::numeric_limits<signed char>  �  std::_Container_base � :  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void>  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > ; �6  std::weak_ptr<donut::engine::DescriptorTableManager> $ &/  std::hash<nvrhi::IResource *> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char> D �=  std::_Default_allocator_traits<std::allocator<unsigned int> > J �9  std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *>  K5  std::char_traits<char>  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 8;  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> @ �3  std::_Default_allocator_traits<std::allocator<char16_t> > \ �9  std::pair<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> *,bool>  -2  std::_Exact_args_t 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t> [ O;  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > [ >  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  U3  std::streampos ' y/  std::hash<enum nvrhi::ColorMask> K �9  std::_Vb_reference<std::_Wrap_alloc<std::allocator<unsigned int> > > O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > ; �=  std::allocator_traits<std::allocator<unsigned int> >  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �  std::array<nvrhi::BindingLayoutItem,16> i 5<  std::_Hash_find_last_result<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> *> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> � 09  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � �<  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > *> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # =7  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle 2 =7  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  #  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  &  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  ?#  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # L#  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable  H!  nvrhi::ComputeState  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec 
 !   _ino_t  !   uint16_t & �6  donut::engine::DescriptorHandle , 7  donut::engine::DescriptorTableManager B �6  donut::engine::DescriptorTableManager::BindingSetItemsEqual B �6  donut::engine::DescriptorTableManager::BindingSetItemHasher % t   donut::engine::DescriptorIndex M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  e4  _CatchableTypeArray     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24 % P4  __RTTIClassHierarchyDescriptor     __time64_t  m  FILE 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray 
 #   size_t 
    time_t  
  __std_exception_data 
 u   _dev_t  K  lldiv_t  H  _ldiv_t  �  _timespec64  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers  �   x      譫鰿3鳪v鐇�6瘻x侃�h�3&�  ?    j轲P[塵5m榤g摏癭 鋍1O骺�*�  �    齝D屜u�偫[篔聤>橷�6酀嘧0稈  �    _O縋[HU-銌�鼪根�鲋薺篮�j��     嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  U   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  )   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  i   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  +   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  i   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   繃S,;fi@`騂廩k叉c.2狇x佚�  �   猯�諽!~�:gn菾�]騈购����'  +   悯R痱v 瓩愿碀"禰J5�>xF痧  x   矨�陘�2{WV�y紥*f�u龘��  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮      d蜯�:＠T邱�"猊`�?d�B�#G騋  H   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  6   JXA7霍莃殭c湠9EC啜后部�4乀  t   v峞M� {�:稚�闙蛂龣 �]<��  �   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  .   穫農�.伆l'h��37x,��
fO��  k   5�\營	6}朖晧�-w氌rJ籠騳榈  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   *u\{┞稦�3壅阱\繺ěk�6U�  0   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  m   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   �*o驑瓂a�(施眗9歐湬

�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  A	    I嘛襨签.濟;剕��7啧�)煇9触�.  �	   蜅�萷l�/费�	廵崹
T,W�&連芿  �	   v�%啧4壽/�.A腔$矜!洎\,Jr敎  
   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  R
   D���0�郋鬔G5啚髡J竆)俻w��  �
   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �
   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  /   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  X   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  ;
   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  {
   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �
   o藾錚\F鄦泭|嚎醖b&惰�_槮  �
   匐衏�$=�"�3�a旬SY�
乢�骣�  C   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  *   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  ~   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   �"睱建Bi圀対隤v��cB�'窘�n     �(M↙溋�
q�2,緀!蝺屦碄F觡  e   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  2   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  p   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   �0�*е彗9釗獳+U叅[4椪 P"��  0   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�     �=蔑藏鄌�
艼�(YWg懀猊	*)  E   交�,�;+愱`�3p炛秓ee td�	^,  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L     _臒~I��歌�0蘏嘺QU5<蝪祰S  _   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  &   +4[(広
倬禼�溞K^洞齹誇*f�5  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg     V� c鯐鄥杕me綻呥EG磷扂浝W)  M   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   副謐�斦=犻媨铩0
龉�3曃譹5D      �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  h   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�      唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  =   !m�#~6蠗4璟飜陷]�絨案翈T3骮�     ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z     k�8.s��鉁�-[粽I*1O鲠-8H� U  Z   �
bH<j峪w�/&d[荨?躹耯=�  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  +   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  j   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   �      �  �   	  R  �  6   T  �  B   U  �  H   V  �  Y   [  �  �   v  h  U   w  h  �   �  �  �  �  `     �  `  2   �  �  �  �  �  �  L  `	  �   �  `	  �   �  �	  K   S  �  {
  T  �  {
  U  �  {
  V  �  {
  W  �  {
  y  `	  �   �  h  �   �  h  @   �  h  �   �  h  @   �  �   ?	  �  �   ?	  �  �   ?	  �  �   ?	  �  �   ?	  �  `	  �   �  �   Q	     �   Q	    �   Q	    �   Q	    �   Q	  �  h  �   �  �   *	  �  �   *	  �  �   *	  �  �   *	  �  �   *	    �   $	    �   $	    �   $	    �   $	    �   $	  5  h  @   F  �    H  �  �  I  �  �  L  �  T   O  �  !   S  �  P  Y  �  �   [  X  �	  \  X  �	  ^  X  �   _  �  �  `  �  �  a  X  �  c  X  �  d  X  I  e  �  �  f  �  j   g    >  h      j  X    k  X  b  l  X  S  p  X  4  q  X  �  r  X  `  u  `	    v  `	  �   w  `	  �   x  �  F  y  �  5  z  �    }  �  �  ~  �  �    �  5  �  �     �  X  �
  �  X  �
  �  X  �	  �  X  `	  �  X  �   �  X  �   �  X  �
  �  X    �  X  �  �  X    �  X  �
  �    4  �    u  �  X  �  �  X  H  �  X  �  �  `	    �  `	  �   �  `	  �   �  �  �  �  �  t  �  X  �
  �  X  ;
  �  X  #
  �  X  �	  �  X  ~	  �  X  �   �  X  %   �  X  �
  �  X  1  �  X  �  �  X  d  �      �  h  �  �  X  X  �  X  "  �  �  1   �  h  �  �  X  5  �  X  -
  �  X  �	  �  X  \	  �  X  a  �  X  �  �  X  b  �  X  S  �  X  �  �  X  9  �  X    �    
  �  h  �  �  X  '  �  X  0  �  X  *   �  h  �  �  h  �  �  X  {   �  �  �  �  �	  K   �    b  �  �	  b   �  X  
  �  h  �  �  �  n  �  �  c  �  �  :  �  �  �  �  �  �  �      �  X  C  �  X  3    h  �    �  �    h  �    h  �  
  h  �    �  	    X  
    h  @     h  F    X  �      w      q      j      K      �    X  a    X  `  !  h  �  "  X  �  %  h  �  '    �  (    }  )    �  *    S  ,  X  �  0  X  j   2  X  L   3  X  G   4  X  <   5  X  1   6  X  )   9    �  :    '  ;      <  X  �  @  h  �  D    {  F  X  P  H  X  �  J    �   K  h  �  M  �  �  N    �  O      P  �	  �   S  X  G  U  �	  �   W  h  �  X  X    Z  �  �  ]  h  �  ^  X  <  `  �  �  c  h  R  f  �  �  j  h  �  n  �	  �  q    �   x  h  �  y  h  |  z  X  �
  {  X  �
  |  X  �	  ~  X  �    X  �  �  X  �  �  X  �  �  X  �  �  X  �	  �  X  �	  �  X  ]  �  X  ]  �  X  r
  �  X  c
  �  (
  a  �  h  �  �  h  �  �  h  �  �  h  �  �  h  �  �  �  �  �  �  �  �  h    �  (
  l  �  h  �  �  h  �  �  (
  ;  �  �	  9  �  �  �  �  (
  �   �  �	  5  �  h  �  �      D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\src\engine\DescriptorTableManager.cpp D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h   �       L_l  �  �   �  �  
 �      �     
 榁  �   淰  �  
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb �  �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   Z   /   ]   5   q      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :   h  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 s     w    
 �  �   �  �  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   �      �   V  W G            0   
   %   f        �std::_Copy_memmove<unsigned int *,unsigned int *>  >u   _First  AJ          >u   _Last  AK          >u   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   g   0   u  O_First  8   u  O_Last  @   u  O_Dest  O  �   @           0   �     4       � �   � �   � �!   � �%   � �,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 l  �   p  �  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   �      �   h  i G            0   
   %   �        �std::_Copy_memmove<nvrhi::BindingSetItem *,nvrhi::BindingSetItem *>  >   _First  AJ          >   _Last  AK          >   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   �   0     O_First  8     O_Last  @     O_Dest  O�   @           0   �     4       � �   � �   � �!   � �%   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 |  �   �  �  
 L��    3议       �      �   �   L G                   
   �        �std::_Fill_zero_memset<unsigned int *>  >u   _Dest  AJ          >   _Count  AK        
                         H 
 h   g      u  O_Dest       O_Count  O  �   (              �            � �    � �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �      �  
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   6  R G            c       b           �std::_Fnv1a_append_value<nvrhi::IResource *> 
 >   _Val  AJ          >�#   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �        O_Val     �#  O_Keyval  O  �   0           c   �      $       $	 �    &	 �b   '	 �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 L  �   P  �  
 �H3罤钩     H�   �   &  R G                              �std::_Fnv1a_append_value<enum nvrhi::Format> 
 >   _Val  AJ          >�   _Keyval  AK          M        �  @
 >#    _Val  AH         N                        H� 
 h   �        O_Val     �  O_Keyval  O  �   0              �      $       $	 �    &	 �   '	 �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 <  �   @  �  
 �H3罤钩     H�   �   ,  X G                              �std::_Fnv1a_append_value<enum nvrhi::ResourceType> 
 >   _Val  AJ          >�#   _Keyval  AK          M        �  @
 >#    _Val  AH         N                        H� 
 h   �        O_Val     �#  O_Keyval  O�   0              �      $       $	 �    &	 �   '	 �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 @  �   D  �  
 �H3罤钩     H�   �   0  \ G                              �std::_Fnv1a_append_value<enum nvrhi::TextureDimension> 
 >   _Val  AJ          >$   _Keyval  AK          M        �  @
 >#    _Val  AH         N                        H� 
 h   �        O_Val     $  O_Keyval  O�   0              �      $       $	 �    &	 �   '	 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   4  P G            c       b           �std::_Fnv1a_append_value<unsigned __int64> 
 >   _Val  AJ          >�#   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �        O_Val     �#  O_Keyval  O�   0           c   �      $       $	 �    &	 �b   '	 �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H  �   L  �  
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   >  S G            p       o   �        �std::_Hash_representation<nvrhi::IResource *>  >�#   _Keyval  AJ          AK       U  M           T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �        �#  O_Keyval  O  �   @           p   �      4       *	 �    +	 �   *	 �   +	 �o   ,	 �,   �   0   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 T  �   X  �  
 �H�%#"勪滘薍3罤钩     H�   �     S G                      �        �std::_Hash_representation<enum nvrhi::Format>  >�   _Keyval  AJ        
  M            M        �  @
 >#    _Val  AH         N N                        H�  h   �        �  O_Keyval  O  �   0              �      $       *	 �    +	 �   ,	 �,   �   0   �  
 {   �      �  
 �   �   �   �  
 (  �   ,  �  
 �H�%#"勪滘薍3罤钩     H�   �     Y G                      �        �std::_Hash_representation<enum nvrhi::ResourceType>  >�#   _Keyval  AJ        
  M            M        �  @
 >#    _Val  AH         N N                        H�  h   �        �#  O_Keyval  O�   0              �      $       *	 �    +	 �   ,	 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 ,  �   0  �  
 �H�%#"勪滘薍3罤钩     H�   �     ] G                      �        �std::_Hash_representation<enum nvrhi::TextureDimension>  >$   _Keyval  AJ        
  M            M        �  @
 >#    _Val  AH         N N                        H�  h   �        $  O_Keyval  O�   0              �      $       *	 �    +	 �   ,	 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 0  �   4  �  
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   <  Q G            p       o   �        �std::_Hash_representation<unsigned __int64>  >�#   _Keyval  AJ          AK       U  M           T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �        �#  O_Keyval  O�   @           p   �      4       *	 �    +	 �   *	 �   +	 �o   ,	 �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 P  �   T  �  
 �9 斃�   �   �   J G                      �        �std::_Is_all_bits_zero<unsigned int> 
 >�#   _Val  AJ                                 H     �#  O_Val  O�   0              �     $       � �    � �   � �,   �   0   �  
 o   �   s   �  
 �   �   �   �  
 H塡$H塋$VWATAVAWH冹 M嬸L孃H孂I�������?I;�嘥  L媋L+!I咙H婭H+H六H嬔H殃I嬂H+翲;��#  H�
I;莝
I嬊H塂$h�H塂$hI;��  H�4�    H侢   r)H峃'H;�嗕   �    H吚勨   H峏'H冦郒塁H咑t
H嬑�    H嬝�3跦塡$XJ�嬜I+訟�> uL��    3诣    �H呉tf�     A��H岻H冴u馤婫H�L+翲嬎�    怘�H吷t1H媁H+袶冣麳侜   rH兟'L婣鳬+菻岮鳫凐w9I嬋�    H�J�籋塆H�H塆H媆$`H兡 A_A^A\_^描    惕    惕    虧   Z   �   Z   �   �     �   W  [   �  q   �  �   �  ]      �   �   G            �     �  i        �std::vector<unsigned int,std::allocator<unsigned int> >::_Resize_reallocate<unsigned int> 
 >}:   this  AJ          AM       s_  DP    >   _Newsize  AK          AW       v] 
 >�#   _Val  AP          AV       yb  D`    >#     _Newcapacity  AH  i      " AH ~     #  G  r  �  � _  Bh   n     $    >    _Oldsize  AT  6     \  G P  >�:    _Appended_first  AJ  �     +    AJ       	   >�:    _Newvec  AI  �       AI �     � �   BX   �     � �   M        �  Wu� M        �  Wu�& M        �  ��)
)%
��( M        �  ��$	%)
��
 Z   k   >    _Block_size  AJ  �       AJ �      >    _Ptr_container  AH  �       AH �     � $  7  U k 
 >0    _Ptr  AI  �       AI �     � �   M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N M        5  
u
	 N N N! M          =kD%
 >    _Oldcapacity  AJ  A     �   R %  
  AJ �     � 	 �  >    _Geometric  AH  a        " AH ~     #  G  r  �  � _  M        �  = N N# M        j   ��&/.	 >#    _Count  AK  �     7    AK       >==   _Backout  CJ     	      CJ          	   M        �  �� N M        �  �� N M        �  �  N N M        �  � >�:   _First  AK        >�:   _Last  AP        M        f  �c >    _Count  AP        N N% M        ~  �"h1#& M        �  *�1[ M        �  �5)6
 Z   �  
 >   _Ptr  AJ V      >#    _Bytes  AK  .    -    AK �     % M        w  �>d#
9
 Z   S   >    _Ptr_container  AP  F      AP V    ;  1  >    _Back_shift  AJ  %    1  AJ V    ; *   N N N N
 Z   }               (         0@ � h"   v  w  x  �  �  �  5  �  �  �  
  f  g  h  j  r  s  t  ~    �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN102  P   }:  Othis  X     O_Newsize  `   �#  O_Val  O  �   �           �  X  
   t       � �   � �2   � �=   � �u   � ��   � ��   � �  � �"  	 �n  
 ��  � ��  � ��  	 ��   �  � F            (   
   (             �`std::vector<unsigned int,std::allocator<unsigned int> >::_Resize_reallocate<unsigned int>'::`1'::catch$0 
 >}:   this  EN  P         ( 
 Z   �                        � ]        __catch$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z$0        $LN102  P   }:  Nthis  X     N_Newsize  `   �#  N_Val  O�   0           (   X     $        �
    �    �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 &  �   *  �  
 Y  �   ]  �  
 i  �   m  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 (  �   ,  �  
 8  �   <  �  
 L  �   P  �  
   �   
  �  
   �     �  
 ?  �   C  �  
 O  �   S  �  
 z  �   ~  �  
 �  �   �  �  
 Q  �   U  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 !  �   %  �  
 5  �   9  �  
 \  �   `  �  
 p  �   t  �  
   �     �  
 !  �   %  �  
 ^  �   b  �  
 �  �   �  �  
   �     �  
 '  �   +  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �     �    
  	  �   	  �  
 �	  �   �	  �  
 4
  �   8
  �  
 j
     n
    
 �
     �
    
    �   $  �  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   �   #   �   H塡$H塋$VWAVH冹 H嬺L嬹I�������I;��  H婭I+H六H嬔H殃I嬂H+翲;�囙   H�
H;辳
H嬣H塡$X�H塡$XI;�嚳   H零H侞   r)H岾'H;�啣   �    H吚劊   H峹'H冪郒塆H呟t
H嬎�    H孁�3�H墊$HM婩I�L+翲嬒�    怚�H吷t1I媀H+袶冣郒侜   rH兟'L婣鳬+菻岮鳫凐w8I嬋�    I�>H伶H鱅塿H�;I塅H媆$PH兡 A^_^描    惕    惕    虈   Z   �   Z   �   �     [   ,  q   2  �   8  ]      �   X  � G            =     =  V        �std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Resize_reallocate<std::_Value_init_tag> 
 >k7   this  AJ          AV       %  D@    >   _Newsize  AK          AL       (�  
 >U:   _Val  AP        "  DP    >#     _Newcapacity  AI  W       AI l       BX   \     �   �   >�    _Newvec  AM  �       AM �     � s   BH   �     � p   M        �  Sc�� M        �  Sc��$ M        �  p)
)%
w' M        �  y$	%)
��
 Z   k   >    _Block_size  AJ  }       AJ +      >    _Ptr_container  AH  �       AH �     �  j 
 >0    _Ptr  AM  �       AM �     � s   M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N M        �  
c
	 N N N! M        �  +kD%
 >    _Oldcapacity  AJ  /     �   N % { 
  AJ �     z  c  >    _Geometric  AI  O         AI l     �  �  M        �  + N N M        �  �� >�   _First  AK  �       >�   _Last  AP  �       M        �  ��c >    _Count  AP  �       N N% M        �  ��h1#" M        �  *��Z M        �  ��)5
 Z   �  
 >   _Ptr  AJ       >#    _Bytes  AK  �     -    AK 7     % M        w  ��d#
8
 Z   S   >    _Ptr_container  AP  �       AP     :  0  >    _Back_shift  AJ  �     1  AJ     : )   N N N N
 Z   �                        0@ � h    �  v  w  x  �  �  �  �  �  �    W  u  v  w  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN99  @   k7  Othis  H     O_Newsize  P   U:  O_Val  O�   p           =  X     d       � �   � �+   � �c   � ��   � ��   � ��   	 �  
 �+  � �1  � �7  	 ��     � F            (   
   (             �`std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >k7   this  EN  @         ( 
 >U:   _Val  EN  P         ( 
 Z   �                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN99  @   k7  Nthis  H     N_Newsize  P   U:  N_Val  O �   0           (   X     $        �
    �    �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 
  �     �  
 0  �   4  �  
 _  �   c  �  
 o  �   s  �  
   �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �     �  
 �  �   �  �  
 �  �   �  �  
   �     �  
    �   $  �  
 x  �   |  �  
 �  �   �  �  
 �  �   �  �  
 i  �   m  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 4  �   8  �  
 D  �   H  �  
          
 l  �   p  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 	     	    
 �	     �	    
 
  �   
  �  
 H塗$UH冹 H嬯L婨XH婾HH婱@�    3�3设    �   �   #   �   H塡$H塴$H塼$ WATAUAVAWH冹0I嬸L孃L嬮I嬓�    H塂$`M婨0L#繫繧婱J媆�M峞M�4$I;辵I嬣I嬵雈J�罤�H;Ku;禖8Fu2禖8Fu)禖8F
u 婥 9Fu婥$9Fu婥(9Fu婥,9FtH;趖H媅氪I�A艷 �)  L嬻H嬰H笒$I�$I�I9E�/  L塪$ H荄$(    �8   �    H孁H塂$(@NH 茾0    I婱驛] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繧媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺�  �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛I嬐�    I婨0L婰$`I#罤繧婾H婰�I�$H;藆H塡$ 雔L�翲媁H嬞H;Qu;禔8Gu2禔8Gu)禔8Gu 婣 9G u婣$9G$u婣(9G(u婣,9G,tI;萾H婭氡H�H塡$ H嬰L嬻�H塋$ H荄$(    H嬰L媡$ �L婰$`H婼I�EL�7H塛H�:H墈I婱I婨0I#罤繪�罬;$uH�<岭L;舥H�<岭H9T�uH墊�I�?A艷I嬊H媆$hH媗$pH媡$xH兡0A_A^A]A\_肏�
    �    �)   �   �   Z   ~  �   �  �   �  �     �     ^      �   �
  nG                   �        �std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Try_emplace<nvrhi::BindingSetItem const &> 
 >�7   this  AJ        %  AU  %     ��  >�   _Keyval_arg  AL       �� AP          AL �      >5<   _Target  CI      U      " CN      X     � f  , 8s  CV      �     � ��  CN     �    `  CV     �    m  >    _Hashval  AQ  �    � �   AQ �       B`   2     � >X<   _Newnode  CM     �       B    �     *1 � � 0 M        O  2',(%hdD	 >�7    _Where  AI  E     {    AI �     P@� + 
 >#8    _End  AV  M     p  AV �     P� � =  >#8    _Bucket_lo  AK  ^     b  AK �     O, � >    _Bucket  AP  6       M        q  D^ M        L  D^ M        �   �� N N N N M        J  
%
 Z   K   N M        S  ��% M        �  �� M        �  �� M        �  �� N N N M        x  ��	 M        �  
�� M        �  
�� M        v  
��
 Z   �   N N N N M        y  �� N N M          ��侰
 Z   6   N M        P  �� N M          �
D6Y >    _Newsize  AJ        AJ 9    q  I 	 >    _Oldsize  AJ      
  M        )  � N N3 M        O  佷,,$%gdH$ >�7    _Where  AJ  �    �  AJ �     
 >#8    _End  AI         AI     �  a w   >#8    _Bucket_lo  AP      h  AP x    @    >    _Bucket  AH  �      M        q  �A >�   _Keyval2  AI      a O 
  AI     �  a w   M        L  �A M        �   �8 N N N N M          k亂
 Z   &    M        '  亂B
 >   _Req_buckets  AJ  �    $  C       �      M        9  6亂 N N N M        U  
傊 N2 M          倫$$#$#d$'CJ$"E >;    _Bucket_array  AJ  �    9  AJ �       >#8    _Insert_after  AK  �    O  AK �       >    _Bucket  AH  �      N 0           (         0@ � h=   �  v  w  �  �  �  �  �  W  �  �  �  �  <  L  �  �  �                %  '  )  +  -  8  9  @  J  O  P  Q  R  S  T  U  l  m  n  o  q  x  y  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN180  `   �7  Othis  p   �  O_Keyval_arg      X<  O_Newnode  O �   �                  �       � �%   � �2   � ��   � ��   � ��   � ��   � �
  � �y  � ��  � �q  � �s  � ��  � ��  � ��  � �  � ��   �  }F                                �`std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Try_emplace<nvrhi::BindingSetItem const &>'::`1'::dtor$1                         �  O   �   �  }F                                �`std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Try_emplace<nvrhi::BindingSetItem const &>'::`1'::dtor$0                         �  O   ,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 +  �   /  �  
 O  �   S  �  
 k  �   o  �  
   �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 \  �   `  �  
 p  �   t  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
    �     �  
   �     �  
 ;  �   ?  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 +  �   /  �  
 Q  �   U  �  
 �  �   �  �  
 �  �   �  �  
 S  �   W  �  
 g  �   k  �  
 �  �   	  �  
 
	  �   	  �  
 5	  �   9	  �  
 E	  �   I	  �  
 g	  �   k	  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �  �   �  �  
 \
  �   `
  �  
 H崐    �       �   H崐    �       �   H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   3  !G            �         	        �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >  >;   _First  AJ        0  AJ b     "  >;   _Last  AK          AR       } 
 >�:   _Val  AP        �  >9    _UFirst  AQ       u                        @  h     b      ;  O_First     ;  O_Last      �:  O_Val  O �   X           �   �     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,   �   0   �  
 H  �   L  �  
 X  �   \  �  
 x  �   |  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H  �   L  �  
 H塡$H塼$WH冹 H嬺I孁I嬓H嬞�    H#C0H婯L婯H拎H菻婣I;羣RH�L�L;@u;禜8Ou2禜8Ou)禜8O
u 婬 9Ou婬$9Ou婬(9Ou婬,9Ot
H;聇H婡氪3繦媆$0H吚ID罤�H嬈H媡$8H兡 _�   �      �   �  MG            �      �   �        �std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::find<void> 
 >�7   this  AI       z  AJ          >�   _Keyval  AM       �  AP          M          �� M        �  �� N N M        N  
p u+ M        O   $$/cD >�7    _Where  AH  7     Y  AH �      
 >#8    _End  AQ  ,     �  >#8    _Bucket_lo  AK  ?     O  AK �       >    _Bucket  AH  $       M        q  D? M        L  D? M        �   c N N N N N M        J  
 Z   K   N                       @ B h   {  �  L  �  �  �    J  N  O  T  p  q  �  �   0   �7  Othis  @   �  O_Keyval  O�   P           �        D       � �   � �   � �   � ��   � ��   � ��   � �,   �   0   �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 L  �   P  �  
 \  �   `  �  
 {  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  M G            �       �   T        �nvrhi::hash_combine<nvrhi::IResource *> 
 >�   seed  AJ          AR       � 
 >�#   v  AK        n  M        �   B"E M           B"E M        �   B"E M           B"E+ M        �   ";>
 >#    _Val  AJ  y     
  AQ       [  N N N N N                        H  h   �  �    �        �  Oseed     �#  Ov  O �   @           �   �     4       {
 �    }
 �   {
 �   }
 ��   ~
 �,   s   0   s  
 r   s   v   s  
 �   s   �   s  
 �   s   �   s  
 n  s   r  s  
 ~  s   �  s  
   s     s  
 L�	L嬞�H�%#"勪滘薍3蠱嬃H赋     I凌HI嬃构y7濱菻拎H蠬蔍3蒊��   �   �  M G            G       F   W        �nvrhi::hash_combine<enum nvrhi::Format> 
 >�   seed  AJ          AS       A 
 >�   v  AK        	  M        �  
 M        �  
 M        �  
 M          
 M        �  

 >#    _Val  AK       '  N N N N N                        H  h   �  �  �  �        �  Oseed     �  Ov  O �   0           G   �     $       {
 �    }
 �F   ~
 �,   v   0   v  
 r   v   v   v  
 �   v   �   v  
 �   v   �   v  
 F  v   J  v  
 �  v   �  v  
 L�	L嬞�H�%#"勪滘薍3蠱嬃H赋     I凌HI嬃构y7濱菻拎H蠬蔍3蒊��   �   �  S G            G       F   U        �nvrhi::hash_combine<enum nvrhi::ResourceType> 
 >�   seed  AJ          AS       A 
 >�#   v  AK        	  M        �  
 M          
 M        �  
 M          
 M        �  

 >#    _Val  AK       '  N N N N N                        H  h   �  �    �        �  Oseed     �#  Ov  O   �   0           G   �     $       {
 �    }
 �F   ~
 �,   t   0   t  
 x   t   |   t  
 �   t   �   t  
 �   t   �   t  
 L  t   P  t  
 �  t   �  t  
 L�	L嬞�H�%#"勪滘薍3蠱嬃H赋     I凌HI嬃构y7濱菻拎H蠬蔍3蒊��   �   �  W G            G       F   V        �nvrhi::hash_combine<enum nvrhi::TextureDimension> 
 >�   seed  AJ          AS       A 
 >$   v  AK        	  M        �  
 M           
 M        �  
 M          
 M        �  

 >#    _Val  AK       '  N N N N N                        H  h   �  �     �        �  Oseed     $  Ov  O   �   0           G   �     $       {
 �    }
 �F   ~
 �,   u   0   u  
 |   u   �   u  
 �   u   �   u  
 �   u   �   u  
 P  u   T  u  
 �  u   �  u  
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  K G            �       �   S        �nvrhi::hash_combine<unsigned __int64> 
 >�   seed  AJ          AR       � 
 >�#   v  AK        n  M        �   B"E M           B"E M        �   B"E M           B"E+ M        �   ";>
 >#    _Val  AJ  y     
  AQ       [  N N N N N                        H  h   �  �    �        �  Oseed     �#  Ov  O   �   @           �   �     4       {
 �    }
 �   {
 �   }
 ��   ~
 �,   r   0   r  
 p   r   t   r  
 �   r   �   r  
 �   r   �   r  
 l  r   p  r  
 |  r   �  r  
   r     r  
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  /G                               �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >  >;   _First  AJ          AJ       
   >;   _Last  AK          
 >�:   _Val  AP           >�<   _Backout  CJ            CJ          
   M            N M        c   N                        H & h           b  c  �  �      ;  O_First     ;  O_Last     �:  O_Val  O  �   H               h     <       � �    � �   � �   � �   � �   � �,   �   0   �  
 V  �   Z  �  
 f  �   j  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 3繦�H堿H9BtH�H�H婤H堿�@H嬃D堿�   �   �  W G            )       (   O        �donut::engine::DescriptorHandle::DescriptorHandle 
 >�6   this  AJ        )  D    >�6   managerPtr  AK        )  >t    index  Ah        )  M        �   	 M        M  )&& M        I   N N M          �  N N                        H  h   I    �  M      �6  Othis     �6  OmanagerPtr     t   Oindex  O�   0           )   �     $       !  �      �!   "  �,   �   0   �  
 |   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 3繦�H堿H嬃茿�����   �   �   W G                      N        �donut::engine::DescriptorHandle::DescriptorHandle 
 >�6   this  AJ          D    M        ~  	  M          �  N N                        @  h   ~        �6  Othis  O  �   (              �              �	     �,      0     
 |      �     
          
 H塡$H塋$UVWAVAWH冹0I嬭H孂E3�L�9L墆H塓H呉t
H�H嬍�P怢�L� L�(L�0H峗8H塡$hD�;L墈L墈�8   �    H� H堾H塁H岾L�9L墆L墆H荂0   H荂8   �  �?L婥�   �    怢�xL壙�   L壙�   L壙�   D壙�   H婳H�L嬇H峊$h�恏  I嬜H峀$ H;萾H�L�8H婳H塛H吷tH��P怘婰$hH吷tL墊$hH��P怘婳H��P0嬝E3缷蠬峅x�    H媁(L婫 H嬍I+菻六H;賡嬅H拎I繦塆(�8v6H婫0I+繦柳H;豽L岲$hH嬘H峅 �    �H嬅H+羣H拎H蠬塛(H零L嬅3襀婳 �    怘嬊H媆$pH兡0A_A^_^]胋   Z   �   �   -  �   v  �   �  �      �   �  c G            �     �  R        �donut::engine::DescriptorTableManager::DescriptorTableManager 
 >7   this  AJ          AM       � D`    >{#   device  AK        7  AK 8     .  >�   layout  AN       � AP          >#    capacity  C       #    p  M        p  ^�10 M        �  �1%	$b"+%
 Z   V   >    _Oldsize  AJ  <    S   9   AJ �    
  >�    _Newlast  AH  Q      AH �      >    _Oldcapacity  AH  ]    "      M        W  亗 >#    _Count  AH      	  AH �      N N N M        v  � M        �  �HB
 >E#    temp  AJ        AJ       Bh       �  B�  �     �  N N M        u  &�� M        v  �� M        �  ��
 >E#    temp  AJ  �       AJ       N N M        �  �� >E#    tmp  AK  �     #  AK          N M        �  ��C
 M        �  �� N N N M        d  �� M        �  �� M        �  �� M          �� M          �� N N N N N M        f  SQ
 >X8   this  AI  L       Bh   Q     e�   M        �  Q/H
 Z   �   M        �  u M        ]  u M        �  u N N N M        �  T M        �  \*# >�7    _Newhead  AH  f     >  M        �  
\ M        �  
\ M        v  
\
 Z   �   N N N N M        
  T M          T N N N M        �  Q N N N M        r  < M        �  < M        "  < N N N M        w  8 N M        y  % M        �  )	 N N M        x  
 M        ~  
 M          � N N N
 Z   b   0           (         @ &hH   v  z  {    �  K  y  �  �  y  z  �  d  f  o  p  r  s  u  v  w  x  ~    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �        
          "  #  $  K  L  W  \  ]  a  u  v  w  �  �  �  �  �   `   7  Othis  h   {#  Odevice  p   �  Olayout  94       /   9�       C#   9�       /   9      /   9      �4   O   �   �           �  �     $       >  �%   =  �8   >  ��        �   c  ��     <   �   ?  �  A  �#  B  �1  C  ��  D  ��  E  ��   �   r F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$0 
 >7   this  EN  `                                  �  O   �   �   r F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$1 
 >7   this  EN  `                                  �  O   �   �   r F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$2 
 >7   this  EN  `                                  �  O   �   �   r F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$3 
 >7   this  EN  `                                  �  O   �   �   s F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$10 
 >7   this  EN  `                                  �  O  �   �   s F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$11 
 >7   this  EN  `                                  �  O  �   �   r F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$4 
 >7   this  EN  `                                  �  O   �   �   r F                                �`donut::engine::DescriptorTableManager::DescriptorTableManager'::`1'::dtor$5 
 >7   this  EN  `                                  �  O   ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 )  �   -  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 Q  �   U  �  
 a  �   e  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ]  �   a  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 	  �   	  �  
 �	  �   �	  �  
 '
  �   +
  �  
 |
  �   �
  �  
 �
  �   �
  �  
 @  �   D  �  
 �  �   �  �  
   �     �  
 s  �   w  �  
 �  �   �  �  
 8
  �   <
  �  
 �
  �   �
  �  
 �
  �      �  
 P  �   T  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 H媻`   �       �   H媻`   H兞�       �   H媻`   H兞�       �   H媻`   H兞 �       �   H媻`   H兞8�       �   H媻`   H兞x�       �   H媻h   H兞�       �   H媻h   H兞�       �   @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   \   %   _   ,   b      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   g   0   g  
 d   g   h   g  
 t   g   x   g  
 �   g   �   g  
 �   g   �   g  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   \   %   _   ,   e      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   m   0   m  
 z   m   ~   m  
 �   m   �   m  
 �   m   �   m  
 �   m   �   m  
 H�    H茿    H堿H�    H�H嬃�   h      e      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   k   0   k  
 z   k   ~   k  
   k     k  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   \   %   _   ,   �      �   )  E G            <      6   �        �std::bad_weak_ptr::bad_weak_ptr 
 >A&   this  AI  	     2  AJ        	  >F&   __that  AH         AK          M        U  :$
 Z   �   N                       @� 
 h   U   0   A&  Othis  8   F&  O__that  O   ,   z   0   z  
 j   z   n   z  
 z   z   ~   z  
 �   z   �   z  
 �   z   �   z  
 H�    W�AH�H嬃�   �      �   �   E G                      �        �std::bad_weak_ptr::bad_weak_ptr 
 >A&   this  AJ          M        R  
 N                        @� 
 h   R      A&  Othis  O�                  �            X �,   w   0   w  
 j   w   n   w  
 �   w   �   w  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   \   %   _      �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   a   0   a  
 d   a   h   a  
 t   a   x   a  
 �   a   �   a  
 �   a   �   a  
   a     a  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   X  x G            "         v        �nvrhi::RefCountPtr<nvrhi::IDescriptorTable>::~RefCountPtr<nvrhi::IDescriptorTable> 
 >7   this  AH         AJ          AH        M        �  GCE
 >E#    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   7  Othis  9       /   O�   0           "   `	     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 T  �   X  �  
 l  �   p  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         L        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >%*   this  AH         AJ          AH        M        �  GCE
 >{#    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   %*  Othis  9       /   O  �   0           "   `	     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H婭H吷t
�8   �    �   [      �   �  G                      %        �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > 
 >@<   this  AJ          M        @  
	 M        �  
	
 >   _Ptr  AJ         N N                        H�  h   w  �  @      @<  Othis  O �   8              h     ,       � �    � �	   � �   � �,   �   0   �  
 )  �   -  �  
 r  �   v  �  
 �  �   �  �  
 H塡$WH冹 H孂3跦婭H吷t=H媁(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w\I嬋�    H塤H塤 H塤(H婳H婣H�H�	H吷t�     H��8   �    H嬎H呟u際婳�8   H媆$0H兡 _�    �    蘀   [   y   [   �   [   �   ]      �   u  ZG            �   
   �   U        �std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::~_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > 
 >�7   this  AJ        
  AM  
     � �   M        j  U( M        �  U	 M        �  
	�� M        !  	�� M        �  	�� N N N" M        �  YG-#
 >�7   _Head  AJ  Y     
  >�7    _Pnode  AJ  c     &    >�7    _Pnext  AI  s       AI p     #    M        ^  
s M        �  

s M        !  
s M        �  
s
 Z   �   N N N N N N N M        g  H
��" M        �  -K1$L M        �  *~ M        �  #)Y
 Z   �  
 >   _Ptr  AJ D       >#    _Bytes  AK       �   - T " M        w  
,#
\
 Z   S   >    _Ptr_container  AP  0     r  Y  AP D       >    _Back_shift  AJ       � 1 Y  AJ D         N N N M        �   N N N                       @� R h   w  x  �  g  j  �  �  �  �  �  �  �  �  �  �  !  ^  _  m         $LN93  0   �7  Othis  O   ,   �   0   �  
   �   �  �  
 �  �   �  �  
 h  �   l  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 `  �   d  �  
 t  �   x  �  
 �  �   �  �  
 �  �   �  �  
 ]  �   a  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   [   V   ]      �   w  TG            [      [   g        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 
 >!9   this  AI  	     R K   AJ        	 " M        �  )H1%
 M        �  *= M        �  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        w  
%#

 Z   S   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N M        �   N N                       H� " h   w  �  �  �  �  �  �         $LN30  0   !9  Othis  O �   8           [        ,       > �	   ? �O   D �U   ? �,   �   0   �  
 y  �   }  �  
 �  �   �  �  
   �     �  
 5  �   9  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 _  �   c  �  
 �  �   �  �  
 H婭H吷t
�8   �    �   [      �   �  G                              �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > 
 >;<   this  AJ          M        %   
	 M        @  
	 M        �  
	
 >   _Ptr  AJ         N N N                        H�  h   w  �  �  %  @  m      ;<  Othis  O  �   (              X            L �    P �,   �   0   �  
 -  �   1  �  
 �  �   �  �  
    �     �  
 H婭H吷t�����罙凐uH�H�`�   �   �  � G                       S        �std::enable_shared_from_this<donut::engine::DescriptorTableManager>::~enable_shared_from_this<donut::engine::DescriptorTableManager> 
 >�6   this  AJ          M        }    M        �   	 M        �  )
 >Z&   this  AJ         N N N                        H�  h   �  }  �      �6  Othis  9       [&   O  �                   �            P �,   �   0   �  
 �   �   �   �  
 1  �   5  �  
 �  �   �  �  
 �  �   �  �  
 @WH冹 H�H孂H婤H�     H�
H吷t+H塡$0@ �     H��8   �    H嬎H呟u際媆$0H��8   H兡 _�    9   [   X   [      �   F  G            \      R   j        �std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >::~list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > 
 >9   this  AJ          AM       K   M        �  $
 M        �  
J
 M        !  J
 M        �  J
 N N N" M        �  ,K
#
 >�7   _Head  AK  	     '  AK 0     "    >�7    _Pnode  AJ       3 #   >�7    _Pnext  AI  3       AI 0       M        ^  
3 M        �  

3 M        !  
3 M        �  
3
 Z   �   N N N N N N                       H� 6 h   w  x  �  �  �  �  �  �  !  ^  _  m   0   9  Othis  O  �   H           \   X     <        �    �	    �    �R    �W    �,   �   0   �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
   �     �  
 0  �   4  �  
 U  �   Y  �  
 e  �   i  �  
 \  �   `  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   `        �std::shared_ptr<donut::engine::DescriptorTableManager>::~shared_ptr<donut::engine::DescriptorTableManager> 
 >E7   this  AJ        +  AJ @       M        �  &, M        �  
 >Z&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �   0   E7  Othis  9+       [&   9=       [&   O  �   0           K   �     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �       �      �   @  �G                       T        �std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >::~unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > 
 >X8   this  AJ                                 H�     X8  Othis  O,   �   0   �  
   �     �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣郒侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   [   V   ]      �   �  � G            [      [   q        �std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::~vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > 
 >k7   this  AI  	     R K   AJ        	 $ M        �  	h1%	
 M        �  *= M        �  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        w  
%#

 Z   S   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� " h   w  x  �  �  �  �           $LN28  0   k7  Othis  O  �   8           [   X     ,       � �	   � �O    �U   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 x  �   |  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 4  �   8  �  
 H  �   L  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣麳侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   [   V   ]      �   �  r G            [      [   c        �std::vector<bool,std::allocator<bool> >::~vector<bool,std::allocator<bool> > 
 >�8   this  AI  	     R K   AJ        	  M        �  F	L M        �  F	L$ M        �  	h1%	
 M        �  *= M        �  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        w  
%#

 Z   S   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N N N N                       H� * h	   w  x  �  �  �  �  �  �  
         $LN35  0   �8  Othis  O  �               [   X            � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 d  �   h  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 4  �   8  �  
 �  �   �  �  
 �  �   �  �  
 H塡$UVWATAUAVAWH冹@L嬹����儁 尗  3�H婹H呉刯  婤吚t@ 岺�盝t
吚u駻塿閦  I�.I媬H呿�8  McnM嬪I嬢H零H婨 H肏墑$�   H�H吷tH��PL婨 L肏崝$�   H峂8�    H嫓$�   H;]@toH峉H峂8�    H#EhH拎H婾PH蠬�H9ZuH;胾
H婨@H�H塀�H婥H塀�H;胾H�H�H�H�MHH婥H�H婥H堿�8   H嬎�    D塴$(荄$,    3繦塂$ H塂$0H塂$8D$ L媱$�   A L$0AHH婱H�H婾�恱  I嬏H灵H婨xH�圓冧�D赤�媴�   D;鐰L艍厴   A塿H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PI婲H吷t�羜凗uH��P怘嫓$�   H兡@A_A^A]A\_^]忙   �   �   �   #  [      �   �  X G                 �  P        �donut::engine::DescriptorHandle::~DescriptorHandle 
 >�6   this  AJ          AV       � >W7   managerPtr  CN      Z     E CM     (     � CN     �    c  CM    �    /  M        z  & M        �  (  M        H  
5(( >     _Count  A   8       A  @     �   z�  N N M        �  & M          �
& N N N M        _  ^ N M        }  佈 M        �  佈	 M        �  佢

 >Z&   this  AJ  �      AJ �      N N N M        `  .仯 M        �  仯) M        �  仺, M        �  伡	 N N N N1 M        Y  n(f.o9
 Z   �   >t    index  AU  k       Al  n      B0  8     � >e9    indexMapEntry  B�   8     � >�    descriptor  AH  |       AP  R      AH �       B�   �      M        k  n N M        k  �� N M        h  �� M        �  �� M        �  �� N N N M        �  
亴 N M        \  亜 N M        a  乹 >�9   _It  CT      �      CJ     x    '  CT     �    `  CJ    �    6    M        �  乹 N N M        F  �'%(
 N M        �  o�� >e9   _Plist  AI  �     �  AI �    T  M          ��4-#; >#     _Bucket  AH  �       M        ,  �C$'
 >#8    _Result  AJ        M        ^  
� M        �  

� M        !  
� M        �  
�
 Z   �   N N N N N+ M        (  ��K)%D
VD ><    _Bucket_lo  AK  �     K 
 >#8    _End  AH  �     	  AH       N M        *  
�� M        J  	��
 Z   K   N N N N N @           8         @� h?   w  {  �  �  �  K  �  E  F  H  Y  \  ^  _  `  a  h  k  t  z  }    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         !  (  *  ,  =  J  T  ^  _  m  p  �   �   �6  Othis  9�       /   9k      I#   9�      [&   9�      [&   9�      [&   O�   X             �     L       %  �   &  �&   (  �^   )  �g   *  ��  +  ��  ,  ��  -  �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 q  �   u  �  
 �  �   �  �  
 L  �   P  �  
 \  �   `  �  
 =  �   A  �  
 M  �   Q  �  
 ]  �   a  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H塡$H塴$H塼$WH冹 H孂H媞(H媃 3鞨;辴H�H吷t	H��PH�+H兠 H;辵鍴婳xH吷tRH媷�   H+罤柳H��    H侜   rH兟'L婣鳬+菻岮鳫凐嚺   I嬋�    H塷xH壇�   H壇�   H峅8�    H婳 H吷t=H媁0H+袶冣郒侜   rH兟'L婣鳬+菻岮鳫凐wpI嬋�    H塷 H塷(H塷0H婳H吷tH塷H��P怘婳H吷tH塷H��P怘婳H吷t�����罙凐uH��P怘媆$0H媗$8H媡$@H兡 _描    虆   [   �   �   �   [   G  ]      �   �  d G            L     L  Z        �donut::engine::DescriptorTableManager::~DescriptorTableManager 
 >7   this  AJ          AM       5.  >    <begin>$L0  AI       -  >    <end>$L0  AL       1%  M        S  � M        }  � M        �  �	 M        �  �
 >Z&   this  AJ        AJ 1      N N N N M        L  �� M        �  ��DE
 >{#    temp  AJ        AJ       N N M        v  �� M        �  ��DE
 >E#    temp  AJ  �       AJ �       N N M        q  F����% M        �  ��i1$	` M        �  *���� M        �  ��)m
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     -    AK F     % M        w  ��d#
p
 Z   S   >    _Ptr_container  AP  �       AP �     r  h  >    _Back_shift  AJ  �     1  AJ �     r    X  N N N N N M        c  [@ M        �  [@ M        �  [@ M        �  @i@$ M        �  2W >   _Count  AH  P     '    AH �       M        �  _)
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  _     *  AK F      M        w  hd# >    _Ptr_container  AP  p       AP �     �  �  >    _Back_shift  AJ  D     @  AJ �     �    �  N N N N N N N                      @� n h   w  x  �  L  �  �  S  T  c  m  n  q  v  }  �  �  �  �  �  �  �  �  �  �    
         $LN94  0   7  Othis  91       /   9�       /   9      /   9-      [&   O �   P           L  �     D       �  �   �  �&   �  �.   �  �4   �  �7   �  �@   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 j  �   n  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 H  �   L  �  
 X  �   \  �  
   �     �  
 (  �   ,  �  
 <  �   @  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
   �   !  �  
 g  �   k  �  
 w  �   {  �  
 �  �   �  �  
 �  �   �  �  
 o  �   s  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H�    H�H兞�       \      `      �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   l   0   l  
 {   l      l  
 H�    H�H兞�       \      `      �   �   F G                      �        �std::bad_weak_ptr::~bad_weak_ptr 
 >A&   this  AJ          M        V   	
 N                        H� 
 h   V      A&  Othis  O ,   y   0   y  
 k   y   o   y  
 H�    H�H兞�       \      `      �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   b   0   b  
 e   b   i   b  
 �   b   �   b  
 L嬕M吚y=H婮I嬂H髫H;萻.J�H抢���H嬍I塕H餮H灵H玲H+罥I嬄冣I塕肏婻I蠬嬄I塕H凌冣H拎II嬄I塕�   �   �  t G            o       n   �        �std::_Vb_iterator<std::_Wrap_alloc<std::allocator<unsigned int> > >::operator+ 
 >�9   this  AJ          AJ K     $ 
 >E   _Off  AP        o  M        �  "	&
- M        �  	
 N N                        H�  h   �  �      �9  Othis     E  O_Off     �9  O_Tmp  O �   �           o   X     |       �
 �   �
 �	   �
 �+   �
 �/   �
 �=   �
 �J   �
 �K   �
 �U   �
 �Y   �
 �]   �
 �`   �
 �d   �
 �n   �
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$H墊$禕H砍     D�L嬕禞I�%#"勪滘薎3薓3肏L还y7濴3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LH葫�,(   L3繪M�I拎I菼嬃H凌H翲萀3葾禞I3薎嬔HH陵I嬃H拎H菻�H萀3葾禞
I3薎嬔HI嬃H陵H拎H菻�H華禕L3葾禞I3薍H3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3菼嬔A禕HH陵H3菼嬃HH拎H菻�H華禕L3葾禞I3薎嬔HH陵H3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3菼嬃H拎HH媩$H菻�H媆$H罥3撩   �   C  m G            �  
   �  K        �donut::engine::DescriptorTableManager::BindingSetItemHasher::operator() 
 >�6   this  AJ        #  D   
 >�   item  AK          AR       �
 >#     hash  AH  �      AQ  �     e M        S  乼in  M        �  乼KR  M          乼KR  M        �  乼KR  M          乼KR& M        �  乼8CJ
 >#    _Val  AJ  �    i  N N N N N N M        S  ��sx  M        �  ��K	N
  M          ��K	N
  M        �  ��K	N
  M          ��K	N
. M        �  ��(@
 >#    _Val  AJ  	    d  N N N N N N M        V  ��$ M        �  �� M           �� M        �  �� M          �� M        �  ��
 >#    _Val  AJ  �       N N N N N N M        W  "�� M        �  �� M        �  �� M        �  �� M          �� M        �  ��
 >#    _Val  AJ  �       N N N N N N M        U  -
O M        �  - M          - M        �  - M          - M        �  -
 >#    _Val  AJ  0     n  N N N N N N M        T  


KU" M        �  


B	L" M          


B	L" M        �  


B	L" M          


B	L2 M        �  J

9;E
 >#    _Val  AP  3     h  N N N N N N                        @ n h   �  S  T  U  V  W  �  �  �  �  �  �           �  �  �  �  �                �6  Othis     �  Oitem  O �   �           �  �     �       D  �
   F  �   D  �   G  �#   F  �-   G  �0   F  �3   G  �7   F  ��   G  ��   F  ��   G  ��   H  ��   I  ��   J  ��   I  �  J  �t  K  �y  J  �|  K  ��  M  ��  K  ��  M  ��  K  ��  M  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 [  �   _  �  
 w  �   {  �  
 X  �   \  �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   \      `   0   [      �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   h   0   h  
 w   h   {   h  
 �   h   �   h  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   \      `   0   [      �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   n   0   n  
 �   n   �   n  
 �   n   �   n  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   \      `   0   [      �   �   U G            B   
   4   �        �std::bad_weak_ptr::`scalar deleting destructor' 
 >A&   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  �   0   A&  Othis  O  ,   {   0   {  
 z   {   ~   {  
 �   {   �   {  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   \      `   0   [      �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   d   0   d  
 w   d   {   d  
 �   d   �   d  
 @WAUH冹HL嬯H孂L嬄H兞8H峊$`�    H婦$`H;G@t婡0H兡HA]_肏婳H塡$hH塴$pH塼$xH�L塼$8L墊$0�P0嫙�   H峸xD孁;豷&L�@ 嬅$嬎H灵I��度�Ｈst�肁;遰酘婳C�?H媁H峯L塪$@A�A粿   A;腄G郒�E嬆�恜  E3繟嬙H嬑A嬡�    L婫 L峸 I婲M峃H嬔I+蠬龙H;趕H零I豂�隡H峸xL峸 H峯隻v=I婩I+繦柳H;豽L岲$`H嬘I嬑�    �H+趖H零L峸 H薍峸xH峯I�	E+鏘嬒H玲3襂E嬆I拎�    L媎$@A嬤A塢岰墖�   H�嬎H灵D嬅H�圛拎�嬎冡�H峅8I�H峊$ AE AMA AL M嬇�    M嬇H�塝0H婱 H媁H��恱  I婱 L媩$0L媡$8H媡$xH媗$pH吷tH��P嬅H媆$hH兡HA]_�   �   �   �   (  �   `  �   �  �      �   �  ] G                   V        �donut::engine::DescriptorTableManager::CreateDescriptor 
 >7   this  AJ          AM       �&  
 >�   item  AK          AU       (   >u     capacity  Ao  e     � >u     index  A   ^     1  (  A   b       A  p     �  \ 0 � b  A  p     �   � 
 �   >u     newCapacity  Al  �     � O   M        ^  	 M        �  
	 N N M        [  ~ N M        a  
i	 >�9   _It  CH      t       CJ     z       M        �  r N M        �  i N N M        k  丱 N M        p  ��#?18 M        �  ��%"+%
 Z   V   >�;    _Mylast  AQ  �     h   K   AQ I      >C:    _My_data  AV      B  1  AV F    �  >�7    _Al  AV  �     b #   AV F    �  >    _Oldsize  AK  �     e     @ 
  AK I      >�    _Newlast  AI  �       AI I    #  >    _Oldcapacity  AH      2    AH I      M        W  �1 >#    _Count  AI  1      AI F    &  N N N M        �  
�� N M        e  仩
 Z   �   N M        k  亯 N M        \  亶 N M        a  	亂
 >�9   _It  CJ      �    	  CJ     �      M        �  	亂
 N N Z   �  b   H                     @ � h-   {  K  E  [  \  ]  ^  a  e  h  k  p  s  t  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �       #  W  u  v  w  �  �  �   `   7  Othis  h   �  Oitem  9U       �4   9�       G#   9�      I#   9�      /   O   �               �           H  �   I  �   J  �*   K  �-   r  �5   M  �X   P  �i   R  ��   P  ��   \  ��   [  ��   \  ��   ]  ��   ^  ��   Y  �
  ^  �I  a  �d  c  �l  g  �p  h  �y  i  ��  j  ��  i  ��  k  ��  j  ��  k  ��  l  ��  n  ��  o  ��  q  �  r  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
    �   $  �  
 0  �   4  �  
 L  �   P  �  
 ~  �   �  �  
 	  �   
  �  
   �   !  �  
 �  �   �  �  
   �     �  
 /  �   3  �  
 C  �   G  �  
 a  �   e  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 "  �   &  �  
 Y  �   ]  �  
 i  �   m  �  
 -  �   1  �  
 A  �   E  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H塡$H塼$WH冹PA H孃H峊$0AHH嬞3�)D$0)L$@�    H婼D嬂H呉剶   婤吚剦   悕H�盝t吚tz腽H�H媅H�7H墂H呟tNH�H塤�C����D塆嬈�罜凐u2H�H嬎��羢凗u H�H嬎�PH嬊H媆$`H媡$hH兡P_肈塆H媆$`H嬊H媡$hH兡P_描    �0   �   �   ~      �   �  c G            �      �   W        �donut::engine::DescriptorTableManager::CreateDescriptorHandle 
 >7   this  AI  #     � D q  AJ        # 
 >�   item  AP        4  >t     index  Ah  ;     � ` &  M        `  ~'	 M        �  ~'	 M        �  ~	, M        �  ��	
 N N N N M        O  g> M        �  g>! M        M  n%#	> M        I  z	> N N M          �g N N N M        y  4x! M        �  4x
 Z   G  " M        �  4  M        H  
D)( >     _Count  A   G       A  �       N N N N
 Z   V   P                     @ : h
   �  �  H  I  O  `  y    �  �  �  �  M         $LN64  `   7  Othis  p   �  Oitem  9�       [&   9�       [&   O �   `           �   �  	   T       u  �   v  �4   w  �8   v  �;   w  ��   x  ��   w  ��   x  ��   w  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 D  �   H  �  
 {  �     �  
 �  �   �  �  
 �  �   �  �  
 L婹 H婣(I+翸c菻柳L;萺3繦塀H�H塀H塀H嬄肐玲H嬄CCLJ�   �   P  Z G            F       E   X        �donut::engine::DescriptorTableManager::GetDescriptor 
 >7   this  AJ        F  >t    index  Ah        F  M        l    N M        k  , N M        F  F N                        @  h   �  F  k  l      7  Othis     t   Oindex  O�   H           F   �     <       {  �    |  �   }  �(   �  �,     �0   �  �,   �   0   �  
    �   �   �  
 �   �   �   �  
 d  �   h  �  
 H塡$H塼$WH冹0H嬹����儁 屫   W荔D$ H婹H呉t婤吚tfD  岺�盝tg吚u騂婰$ H媆$(H吷tgH婭H��P8媣餒呟t.嬊�罜凐u"H媆$(H�H嬎��羬�uH婰$(H��R嬈H媆$@H媡$HH兡0_肏�H塋$ H媈H塡$(霐H呟t0嬊�罜凐u$H媆$(H�H嬎�嬒�罧凒uH婰$(H��R嬊H媆$@H媡$HH兡0_�   �   �  U G                 �   Q        �donut::engine::DescriptorHandle::GetIndexInHeap 
 >�6   this  AJ          AL       � X G  >W7   manager  CJ      S     f  S  CI     X     � A $ j   CJ     �     `  T  CJ     �     G    CI    �     a   ;   D     M        z  $q! M        �  *q(	  M        H  
3*( >     _Count  A   6       A  N     �  ` } .  N N M        �  $ M          �$ N N N M        `  5�� M        �  ��0 M        �  ��,
 M        �  �� N N N N M        `  3l M        �  l. M        �  q,
 M        �  ��
 N N N N 0                    @ : h
   �  �  H  M  _  `  z    �  �  �  �  �   @   �6  Othis      W7  Omanager  9d       �4   9�       [&   9�       [&   9�       [&   9�       [&   O  �   `             �  	   T       0  �   1  �$   4  �]   6  ��   :  ��   4  ��   7  ��   9  ��   :  ��   �   d F                                �`donut::engine::DescriptorHandle::GetIndexInHeap'::`1'::dtor$0  >W7    manager  EN                                     �  O  ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
    �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 \  �   `  �  
 �  �   �  �  
 H崐    �       �   H塡$H塴$H塼$ WAVAWH冹@L媦 H嬹Lc騃嬣I嬵H零L鸌�H吷tH��PL婩 H峊$`L肏峃8�    H媆$`H;^@toH峉H峃8�    H#FhH媀PH拎H蠬�H9ZuH;胾
H婩@H�H塀�H婥H塀�H;胾H�H�H��8   H�NHH婥H�H婥H堿H嬎�    3繢塼$(H塂$ M嬊H塂$0H塂$8L$0荄$,    D$ AAOH婲H媀H��恱  H婩xH嬐H媆$h冨H灵H�垕�宠H媗$p�媶�   D;餉L茐啒   H媡$xH兡@A_A^_肗   �   f   �   �   [      �   �  ^ G            W       Y        �donut::engine::DescriptorTableManager::ReleaseDescriptor 
 >7   this  AJ          AL       . >t    index  A         "  AV  "     3 >e9    indexMapEntry  B`   R      >�    descriptor  AW  /     $ M        k  
 N M        k  =	 N M        h  	R M        �  	R M        �  	R N N N M        �  
�5 N M        \  �( N M        a  � >�9   _It  CN             CJ     $    3  M        �  � N N M        F  ��
	 N M        �  o] >e9   _Plist  AI  W     �  M          ]4-#; >#     _Bucket  AH  n      " M        ,  ��
u$' >#8    _Result  AJ  �       M        ^  �� M        �  
�� M        !  �� M        �  ��
 Z   �   N N N N N* M        (  ]K)%D
VD ><    _Bucket_lo  AK  y     8 
 >#8    _End  AH  �     	  AH �       N M        *  
a M        J  	a
 Z   K   N N N N
 Z   �   @                     H � h0   w  {  �  K  �  E  F  \  ^  a  h  k  t  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         !  (  *  ,  =  J  T  ^  _  m  p  �   `   7  Othis  h   t   Oindex  `   e9  OindexMapEntry  9:       /   9      I#   O   �   �           W  �     �       �  �   �  �   �  �"   �  �/   �  �7   �  �=   �  �R   �  �]   �  ��   �  ��   �  ��   �  �   �  �  �  �  �  �  �  �.  �  �3  �  �5  �  �H  �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
   �   
  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
   �     �  
   �   "  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H塡$ L塂$AVH冹 L嬹I嬝H婭H嬃M�I+罤柳H;�冣   H�������H塼$0H墊$8H;�囜   H�4�    H侢   r)H峃'H;�喢   �    H吚劵   H峹'H冪郒塆H咑t
H嬑�    H孁�3�I�I婩H+罤柳H吚t.H��    H侜   rL婣鳫兟'I+菻岮鳫凐w`I嬋�    H�>I�>I塅I塅H;鴗H�H兦H;鴘鬑媡$0H媩$8H媆$HH兡 A^肏嬔L岲$@I嬌�    H媆$HH兡 A^描    惕    蘬   Z   �   Z   �   [     �   /  q   5  ]      �   �  � G            :     :  �        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >::_Assign_grow 
 >!9   this  AJ          AV       '�    >   _Cells  AK        3p  �  � w   AK �     w  & 
 >,;   _Val  AI       $�    AP          D@    >    _Oldsize  AH  '     �  �  >;    _Newend  AH  �     2  >    _Oldcapacity  AH  �     ,    AH �     	  >;    _Newvec  AM  �       AM �     � \  k .  M        �   N M        �  �� N M        �  
0W��% M        �  U)
)%
��' M        �  ^$	%)
��
 Z   k   >    _Block_size  AJ  b       AJ .      >    _Ptr_container  AH  p       AH �     �  � 
 >0    _Ptr  AM  �       AM �     � \  k .  M        v  k
 Z   �   N N M        v  ��
 Z   �   N N M        �  

0
	 N N M          ��#" >�<   _Backout  CM     �       CM    �         M          �� N M        c  �� N N M        �  .���� M        �  ��)]
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     &  AK 4     # M        w  
��#
`
 Z   S   >    _Ptr_container  AP  �       AP �     b  X  >    _Back_shift  AJ  �     9  AJ �     b ; "  N N N
 Z   	                         @ Z h   v  w  �  �  �  �  �  �  �  �  �  �            b  c  �  �         $LN82  0   !9  Othis  8     O_Cells  @   ,;  O_Val  O �   �           :       �        �    �'   ! �0   " ��   $ ��   % ��   ' ��   + ��   , ��   - ��   . �  2 �  0 �"  2 �.  " �4  ' �,   �   0   �  
 �   �   �   �  
   �     �  
 *  �   .  �  
 J  �   N  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
   �     �  
 3  �   7  �  
 C  �   G  �  
   �      �  
 ,  �   0  �  
 U  �   Y  �  
 e  �   i  �  
 �  �   �  �  
 �  �   �  �  
 \  �   `  �  
 p  �   t  �  
   �     �  
 5  �   9  �  
 E  �   I  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SUH冹(H�������H嬮H饺�   嬅H余H;�囆  H岯�H塼$@H媢H肏饺H墊$HL嬈�罫墊$ H鱼H峂H��    H岰�H塢8H塃0H媇H�H孄H;�凚  L塼$P@ ff�     H�?H峉H嬐�    H#E0L婱H拎L菼�H;謚I�I塝殪   I婣L婥L;@uj禜8Kua禜8KuX禜8KuO婬 9K uG婬$9K$u?婬(9K(u7婬,9K,u/L� L;胻!H婼H�:H婳L�I婡H�I塇H塛H塁I塝雝H;衪K怘婡L;@u;禜8Ku2禜8Ku)禜8Ku 婬 9K u婬$9K$u婬(9K(u婬,9K,tPH;衭禠婥I�8H媁H�H婬H�H塒L塆H塊I�H嬤H;�呏��L媡$PH媩$HH媡$@L媩$ H兡(][肔� H婼H�:H婳L�I婡H�I塇H塛H塁氤H�
    �    蘘   �   �   �   �  �     ^      �   �	  QG                   &        �std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Forced_rehash 
 >�7   this  AJ          AN       ��  >#   	 _Buckets  AI  Q       AK        Q � C            4  C      �    
  >    _Max_storage_buckets  AH  "     �
 �
 >,;    _End  AL  8     ��  >,;    _Inserted  AI  �      AI �     k! D  >,;    _Next_inserted  AM  t     �P  >D9    _Insert_before  AH  �     $_  � '  AH �     E � � t  >�:    _Bucket_lo  AQ  �     M  AQ �     E   >    _Bucket  AH  �       M        �  "

 N M        ;  j M        F  j M        �  n N N N M        :  4 M        �  4 M        �  4 N N N M        �  +
 M        �  8  >#    _Value  AH  /     /  N N M        2  t�= M        3  t�= N N M        *  �� M        J  ��
 Z   K   N N M        0  �� M        5  �� N N M        3  �� N M        q  E�� M        L  E�� M        �   �� N N N M        5  � N& M        <  �$#$#$c$ >#8    _Before_prev  AH  %      AH �     E � � t  >#8    _Last_prev  AJ        AJ �     E
 � � t  >#8    _First_prev  AK        AK �     E � � t  N M        3  �:K N& M        <  亰$#$#$c$ >#8    _Before_prev  AJ  �      AJ �     E
  >#8    _Last_prev  AK  �      AK �     E  >#8    _First_prev  AP  �       AP �     E  N M        q  A丏 M        L  A丏 M        �   乪 N N N M        4  丂 N& M        <  佖$#$#$c$ >#8   _First  AP  �    #  AP �     E  >#8    _Before_prev  AH  �      AH �     E  >#8    _Last_prev  AJ  �      AJ �     E
  >#8    _First_prev  AK  �      AK �     E  N Z   �  6   (                     @ � h)   w  {  �  �  �  �  L  �  �  �  �  �  �  �  !  *  .  /  0  1  2  3  4  5  6  7  :  ;  <  D  F  H  I  J  T  ^  _  m  q  �  �         $LN192  @   �7  Othis  H   #   O_Buckets  O   �   P              '   D      � �   � �   � �   � �   � �"   � �+   � �4   � �8   � �D   � �G   � �Q   � �^   � �b   � �j   � �q   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �  � �  � �4  � �8  � �:  � �@  � ��  � ��  � ��  � ��  � ��   ��  � ��  � �,   �   0   �  
 v  �   z  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 ;  �   ?  �  
 c  �   g  �  
 s  �   w  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 *  �   .  �  
 P  �   T  �  
 S  �   W  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 '  �   +  �  
 U  �   Y  �  
 e  �   i  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 &  �   *  �  
 P  �   T  �  
 `  �   d  �  
   �     �  
 )  �   -  �  
 T  �   X  �  
 d  �   h  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �	  �   �	  �  
 
  �   
  �  
 @UWAVH冹0L�I嬭L�2H孂M+騃窿I伶LrM吚劗   H婭H�������H+罥;�傑  H塡$X3襀塼$`I峏H媤H貶嬑H岭I+蕢T$PH六H;賡
I�欻塆階v?H婫I+翲柳H;豽L岲$PH嬘H嬒�    �H+貶嬑H��    L嬅�    H驢墂H婳H媡$`H吷uH�)H塆H媆$XI嬈H兡0A^_]肔�H吷y"H嬃H髫tH嬃M嬘H餍H凌H��   L+须H嬃H凌M�僉�)H嬔冣L塆M吚y'I嬂H髫tI嬂I嬎H餍H凌H��   H+菻塋$ �I嬂H凌I�僅塂$ A冟L塂$(M咑yI嬈H髫tI嬈H餍H凌H��   L+仉I嬈H凌M��(D$ I嬣fD$ 冦L婦$(L婰$ �    H嬍M;趗	H;�����H呉tH�孰�   H吷I岯麹D蠱吚tI�入
A�   I冮H嬍�   余A�A�t	DA�毽D忱A�霛�    虧   �   �   �   $  �      �   T  X G            )  	   )  �        �std::vector<bool,std::allocator<bool> >::_Insert_x 
 >�8   this  AJ          AM       �   >�9   _Where  AK        (N � AK �       >#    _Count  AN       �   AP         
 >E    _Off  AV  $     �   >�9   _Oldend  CH            CR            CH              CR     �      5  CH        =  $  M        �   N M        �  	 M        �  	 M        �  	 N N N M        �  WT6 M          W$%$b"+%
 Z   i   >    _Oldsize  AJ  a     H   ;   AJ �       >�:    _Newlast  AH  y       AH �     T   2   >    _Oldcapacity  AH  �     -    M        j  "�� >#    _Count  AI  W     Z  H  M        �  �� N N N N M        �  S N M        �  �� N M          仦c" M        `  伬P M        �  伬 M        �  伬 N N M        |  �	 M        \  �%' N M        [  �	 N N M        �  
� M        �  
� M        �  
� N N N M        z  佮 M        �  佮  M        �  佮#" N N N M        z  佈 M        �  佈 M        �  佈%# N N N N N M        �  8乫 M        �  8乫" M        �  乫&O" N N N M        �  <�* M        �  <�* M        �  <�*" M        �  �*-#"	 N N N N M        �  5�� M        �  2�� M        �  2��" M        �  ��-#" N N N M        �  �� M        �  �� M        �  �� N N N N
 Z   �   0                     @ � h5   {  E  [  \  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �    
    Y  [  `  g  j  r  s  t  z  |  �  �  �  �  �  �  �  �  �         $LN185  P   �8  Othis  X   �9  O_Where  `   #   O_Count  O�   �           )  X     �       �
 �	   �
 �   �
 �   �
 �   �
 �   �
 �$   �
 �-   �
 �G   �
 ��   �
 ��   �
 ��   �
 ��   �
 ��   �
 �  �
 �*  �
 �#  �
 �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 D  �   H  �  
 X  �   \  �  
 l  �   p  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 C  �   G  �  
   �     �  
 h  �   l  �  
 H冹HH峀$ �    H�    H峀$ �    �
   k      k      �      �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (               �
            J �   K �,   q   0   q  
 �   �   �   �  
 �   q   �   q  
 H冹HH峀$ �    H�    H峀$ �    �
   w      �      �      �   ~   > G                       G        坰td::_Throw_bad_weak_ptr 
 Z   �   H                      @        $LN3  O  �   (               �            ` �   a �,   ~   0   ~  
 z   �   ~   �  
 �   ~   �   ~  
 H冹(H�
    �    �   �      ^      �   �   T G                     �        坰td::vector<bool,std::allocator<bool> >::_Xlen 
 Z   6   (                      @        $LN3  O�   (              X            �
 �   �
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H�
    �    �   �      ^      �   �   g G                     }        坰td::vector<unsigned int,std::allocator<unsigned int> >::_Xlength 
 Z   6   (                      @        $LN3  O �   (              X            a �   b �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H�
    �    �   �      ^      �   �   y G                     �        坰td::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Xlength 
 Z   6   (                      @        $LN3  O   �   (              X            a �   b �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   [   =   ]      �   R  N G            B      B   �        �std::allocator<unsigned int>::deallocate 
 >�:   this  AJ          AJ 0       D0   
 >�:   _Ptr  AK          >   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        w  
#

 Z   S   >    _Ptr_container  AJ       %    AJ 0       >    _Back_shift  AH          AH 0       N N (                      H  h   w  �         $LN18  0   �:  Othis  8   �:  O_Ptr  @     O_Count  O  �   8           B   h     ,       � �   � �3   � �7   � �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 -  �   1  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 h  �   l  �  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   [   <   ]      �   _  W G            A      A   �        �std::allocator<nvrhi::BindingSetItem>::deallocate 
 >*:   this  AJ          AJ ,       D0   
 >�   _Ptr  AK        @ /   >   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        w  
#

 Z   S   >    _Ptr_container  AJ       (    AJ ,       >    _Back_shift  AH         AH ,       N N (                      H  h   w  �         $LN18  0   *:  Othis  8   �  O_Ptr  @     O_Count  O �   8           A   h     ,       � �   � �2   � �6   � �,   �   0   �  
 |   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 :  �   >  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   #  �  
 t  �   x  �  
 H塡$H塴$H塼$WH冹PI嬞H嬯H嬹A L�L媃M呟劦   fI~繫+翴柳I拎fs�fH~繪纘"I嬂H髫tI嬂H餍H凌H��   M嬍L+入I嬂H凌M�侶荄$(    L塗$ A冟fH~翴+襀龙H菱fs�fH~繦衴%H嬄H髫tH嬄H餍H凌H��   I嬍H+葍��$H嬄H凌I�們��A)D$@I嬍3褽3繫嬍H塋$ H塗$(I孂I+鶫�H羚I鳯;蓇	L;��  L塋$0L塂$8M呟yI嬅H髫tI嬅H餍H凌H��   L+须I嬅H凌M�侫冦D$0)D$@(L$ fL$0�   H婰$8L婰$0L婦$HH婽$@f怣;蕌I;藅<嬅余A��tD�D忱�I凐sI�离E3繦兟H凒sH�岭�3蒊兞牒L�I+襀龙H菱I蠬�������H;衱uL岼I灵H婲H嬃I+翲柳L;萻
K�奌;羣H塅H塚冣v嬍鱼�薈!\婞H�H塂$@H荄$H    L嬊H嬚H峀$@�    H嬇H媆$`H媗$hH媡$pH兡P_描    蘋  �   l  �      �   !
  T G            q     q  �        �std::vector<bool,std::allocator<bool> >::erase 
 >�8   this  AJ          AL       TH  >�9   _First_arg  AP        � 6 �  >�9   _Last_arg  AI       c AQ          AI 2    )  >�9   _Last  CH      �       CJ      �       
  CK      �         CH     �     '    CK     �       CP     �       CK     �       CH    �     
  CQ    �     
  B    �     �`   >�9   _First  CH      j       CP      �     T  CQ      p       CH     ^         CP     �     L� �  CQ     }       CH    }     / 
 >     _Off  AM        M        �  �� N M        �  i}r M        �  �� M        �  �� M        �  �� N N N M        �  7��$ M        �  ��*&"' N N M        �  2} N N M        �  !#L
 M        �  ! M        �  ! M        �  ! N N N M        �  /N! M        �  N*&" N N M        �  1 N N M        �  � M        �  � N N M        �  �2 M        �  �2 M        �  �2 N N N- M        �  佺(m$#D
 Z   �   >#    _Size  AK  �    � N I  AK 2      >    _Words  AQ  �    4  AQ 2    !  M        �  侖 N M        �  侢 N M        �  � N M        �  � M        �  � M        �  � N N N N M        �  佡 N M        �  佒 M        �  佒 M        �  佒 N N N M        �  
2乗F4 M        Z  ,亹*' M        �  
亹 M        �  
亹 N N M        {  伱 M        �  伱 M        �  伱&#" N N N M        {  伅 M        �  伅 M        �  伅(#" N N N M        |  仦 M        \  仭$$ N M        [  仦 N N M        �  仛 M        �  仛 M        �  仛 N N N N N M        �  /�- M        �  /�- M        �  /�-  M        �  �--&O" N N N N
 Z   �   P                    0@� � h8   {  E  [  \  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  
      Y  Z  [  f  g  h  {  |  �  �  �         $LN226  `   �8  Othis  p   �9  O_First_arg  x   �9  O_Last_arg      �9  O_Last  O   �   h           q  X  
   \       Y
 �   Z
 �}   [
 ��   \
 �  ^
 �#  g
 ��  h
 �2  k
 �V  l
 �k  h
 �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   "  �  
 2  �   6  �  
 J  �   N  �  
 b  �   f  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 +  �   /  �  
 ?  �   C  �  
 W  �   [  �  
 o  �   s  �  
 �  �   �  �  
 �  �   �  �  
   �   #  �  
 3  �   7  �  
 T  �   X  �  
 d  �   h  �  
 �	  �   �	  �  
 8
  �   <
  �  
 H塡$H墊$ AVH冹PL婭E娥H嬟H孂L;�儝  H�	I+費吷y$I嬃H髫tI嬃H餍H凌H��   H+菻塋$ �I嬃H凌H�丠塂$ A冡H峊$0L塋$(L嬅(D$ H嬒fD$0�    L�H嬓H吚yH嬋H髻tH餍H凌H��   L+离H凌M��冣H呟剺   H塼$hy(H嬅H髫H;衧H贗嬸H嬅H餍H凌H��   H+痣H贖嬅H凌I�4��   H#豅;苪H;觮DE�H嬍����H塴$`嬜3碛釪嬍A餮L;苪9�    *擞顴匂E秣譇�#闍#�顰�(H媗$`H媡$hH媆$pH媩$xH兡PA^肐岺E匂嬇E翬#袮翧� A镀L嬈L+瘤��兑�    H呟t倒    *擞飲趋�#E匂E�艍霘v�M吷y'I嬃H髫tI嬃H嬍H餍H凌H��   H+菻塋$ �I嬃H凌H�侶塂$ A冡L塋$(H呟y$H嬅H髫tH嬅H餍H凌H��   H+蠬塗$0�H嬅H凌H�侶塂$0(D$ L峀$ 冦fD$ H塡$8L岲$0(L$0H峊$@H嬒fL$0�    H媆$pH媩$xH兡PA^脝   �   �  �   m  �      �   �  U G            �     q  b        �std::vector<bool,std::allocator<bool> >::resize 
 >�8   this  AJ          AM       ]�  AM X      >#    _Newsize  AK        �o J AK ]     
 >0    _Val  AX          An       iU ) M        �  f$3#f)/kJI
 Z   �  
 >#     _Off  AK  �     , " M        �  ��8/"IJ >#     _Count  AI       .�  � �  AI ]     @ M        X  �
	O	
0%c")
 >�   _Val  A   
    � S   A  ]      >�:    _VbLast  AK  *    g )   AK S      >u    _VbFirst  AJ  r    '  >�    _FirstDestMask  Ai  -    l &   Ai S      >�    _FirstSourceMask  A   &      >�    _LastDestMask  A   F      >�    _DestMask  A   I      >�    _LastSourceMask  A   >      >    _Count_ch  AP  �      >�    _LastDestMask  A   �      >�    _LastSourceMask  A       �  0  A  S      M        �  �
 N N M        �  8�� M        �  8��" M        �  ��-#"
 N N N N M        �  )�� M        �  )��  M        �  ��-#O" N N N M        �  �� M        �  �� M        �  �� N N N N M        �  '9 M        �  9- M        �  9-! M        �  --&"	 N N N M        �  ' M        �  ' M        �  ' N N N N M        �  B侜 M        �  B侜$ M        �  侜&"	 N N N M        �  ?伝 M        �  <伨 M        �  <伨" M        �  伨-#"	 N N N M        �  伝 M        �  伝 M        �  伝 N N N N
 Z   �   P                     @ F h   {  �  �  �  �  �  �  �  �  �  �  �  �  �  �  X   `   �8  Othis  h   #   O_Newsize  p   0   O_Val  O  �   X           �  X     L       J �   K �'   L �]  P �n  L ��  M ��  N �q  P �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 ^  �   b  �  
 �  �   �  �  
 �  �   �  �  
   �      �  
 0  �   4  �  
 R  �   V  �  
 f  �   j  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 =  �   A  �  
 g  �   k  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H�    �   �      �   �   = G                      �        �std::bad_weak_ptr::what 
 >K&   this  AJ          D                           @�     K&  Othis  O �   0              �     $       Z �    \ �   ] �,   x   0   x  
 b   x   f   x  
 �   x   �   x  
 H婹H�    H呉HE旅   _      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   c   0   c  
 _   c   c   c  
 �   c   �   c  
  20    2           �      �      
   
 
4 
2p    B           �      �          20    <           �      �         
 
4 
2p    B           �      �          20    <           �      �      %   
 
4 
2p    B           �      �      +    �                  �      �      1    20    <           �      �      7   
 
4 
2p    B           �      �      =    �                  �      �      C   
 4 r���
�p`P           �      R                 �      �      L   `       U   = 5 C ��� d	 4 Rp           �      a                 �      �      [   (           d      g   
    @   �   �3Cs娝�
 
t 4     �          �      �      p    B             �      |       "           �      �      v   h                 �          \   2 B             �      �       "           �      �      �   h           �      �          \   2 B      A           �      �      �    20    [           �      �      �    B                 �      �      �    2p               �      �      �   ! 4               �      �      �      J           �      �      �   !                 �      �      �   J   \           �      �      �    BP0      /           �      �      �   ! � t	 d     /          �      �      �   /   }           �      �      �   ! �
 /   }          �      �      �   }   �          �      �      �   !   /   }          �      �      �   �  �          �      �      �   !   �  �
  t	  d     /          �      �      �   �  �          �      �      �   !       /          �      �      �   �            �      �      �    4	 2�    :           �      �      �   !
 
t d     :          �      �      �   :             �      �      �   !       :          �      �      �     .          �      �      �   !   t  d     :          �      �      �   .  :          �      �      �    20    [           �      �      �   
 
4 
2p    �           �      �      �    B      B           �      �           B                 �      �          20    [           �      �          t 4 ��    �           �      �         ! d
     �          �      �         �             �      �         ! T �            �      �           X          �      �         !   �            �      �         X  ]          �      �      $   !       �          �      �         ]  n          �      �      *   !   d
  T     �          �      �         n  �          �      �      0   !       �          �      �         �  �          �      �      6    d T
 4 �p           �      B       q          �      �      <   `       E   �	 	 	R�pP    G           �      �      H   ! 4     G          �      �      H   G   N           �      �      N   ! d G   N          �      �      N   N   �           �      �      T   !   G   N          �      �      N   �   �           �      �      Z   !       G          �      �      H   �   �           �      �      `   !   4     G          �      �      H   �   #          �      �      f   !       G          �      �      H   #  )          �      �      l    B                 �      �      r    4 R��
p`P           �      ~       �          �      �      x   (           �      �       �6    ^    .    .    .    .    ~    .    .       �      \   
   �      �      �      �   !   �   &   �   +   �   0   \   hZ
|jR4-  d T 4 2p           �      �       L          �      �      �   h           �      �          \   b %0  > >4
 ��p      >           �      �      �   ! � � 
d T     >          �      �      �   >   �           �      �      �   ! � >   �          �      �      �   �   �           �      �      �   !   >   �          �      �      �   �   
          �      �      �   !   � >   �          �      �      �   
  l          �      �      �   !   >   �          �      �      �   l  �          �      �      �   !       >          �      �      �   �            �      �      �    d
 4 �p    �           �      �      �   DSz墰
 d T 4
 r��p    W          �      �      �    20               �      �      �   ! t               �      �      �      E           �      �      �   !                 �      �      �   E   K           �      �      �   - d 4 2p    �           �      �      �    d T 4
 R����p           �       �                 �      �      �   (           �      �   
    @:    @   �      �   ���        >                         �   ! t      >                        �   >   b                            !       >                        �   b   �                             4
 2�p`           �             =                         8                        	                   �       �    j� 
 
2P    (           �      �      #    
 
4 
2p    0                       ,    4 2���p`           �      8       �                      2   8               ;      >   	   D            A   �       �   q r� 
 
2P    (           �      �      G     B      :           	      	      P   
 
4 
2p    0           
      
      V                               �      e      c   Unknown exception                             �      i      c                               �      o      c   bad array new length                                l      n                                 t      z      �                   .?AVbad_array_new_length@std@@     �               ����                      q      m                   .?AVbad_alloc@std@@     �              ����                      w      g                   .?AVexception@std@@     �               ����                      }      a       ����    ����        ��������                            �      |      x   bad_weak_ptr                                y      �                         �      �                   .?AVbad_weak_ptr@std@@     �               ����                      �      z   vector<bool> too long unordered_map/set too long invalid hash bucket count vector too long                                       }      �      �                         �                   �               ����    @                   }      �                                         w      �      �                         �                           �      �              ����    @                   w      �                                         q      �      �                         �                                   �      �      �              ����    @                   q      �                                         �      �      �                         �                           �      �              ����    @                   �      �      _   �   +   ) 
34        std::bad_weak_ptr::`vftable'     �      �  
    �   (   & 
34        std::exception::`vftable'    \      \  
    �   (   & 
34        std::bad_alloc::`vftable'    b      b  
    �   3   1 
34        std::bad_array_new_length::`vftable'     e      e  
 �+鷯8}`/�8藯i窭8cI橗cS1箱�,�	h�K蜌�(惜桳诮w蹵"R�恶S垯	s碅"R�禷�<6Gm0"顛E┋�/9嫾�A勽洶N�942訥鏴0穦�1馪簜餏2g"0穦�1馪漋8ck烉20穦�1馪嵌烌S岄A勽洶N镶�6弌桗芩靻Z峲7t\<剳.j<�H匄脠繩蟣�<�H匄肵�!縐�<�H匄芒�(�+遂哯峲�8饒?�"F簓-Jo港Mつ�"燧5膊D	ホ�33僮1U!@
d戮翁r<飴nN琿z澗嚧麗�;￡x瞊��+�-譀K;踉I)#hv瓯訧)#hv瓯屢b綩藋T畊#槴� 霰h詙縓覸Y蹢rL'娗�	J撐�视鶒禞�葹B+汫M�= �葹B+汫�妖g抗�葹B+汫|#&h8�	J撐�,Rk,N��(！
Z�#t��=.f厍獑K﹍仲|晟CyW�-)�?5 鱯! My槱陪鲏l�$phxlM烀�<�68曤m'+撫娟P鼇葰π-伬&�'@c8"嫥>�
橎咑籜鳀D鬝X嚤踖p禭c�&敡?諭嚤踖p禭9"虰錱6萪O�眒TF7郃.嚤踖p禭z嫡u�1脫o�'鷍鲝%R韱�"K霵婬(ζa�磇@�'項j饾s豰靋茬'項jｄ砐�4G9�6>H#梓蹶硫�鞠瓚挮婓凕�漒夂嫑�f晔�(Z�6>H#璜�乆a8iC<*诌枓�&!囋W觖� 鵾}�鴱鰎$愜w獛啯幊彸鸿胴'洋m|��"《閝驚鬅p�?j_�
7陘m^搥!\z繏纊睥y鯡�'郗+s奞秤Wp!/
嶀預棊膬�屓绀?貚犷A棊膬�g盋鳀n嶀預棊膬eDf冱R绣n獓往邝�/佐浽夗_鳻堖.呰5異璮聪箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>,6嬉�箩邆5>~;A嬰t舘^�K	悏
U翠僿*�癙械Y 麒狿|LP摤v9KDM7>;Z?A嫳膃渶J祇k苑訧)#hv瓯剧瑷@�,m诖軐M榦殰漓�oX鼪≥醠^ 礲y�鐊嗛膳nPO��93醠�%I栶賑?T隃�'�>:%I栶賑?T'殯眬0\襢]{謑p绯�5!�*塮]{謑pr^)�薴]{謑p唓B8�"昌X墺鉪d杧��[y1Z
 砌&殴翼柯剞歏,&Ii�瓄Te簨�2x帡K{〆�29B�/铏B3覓'盂Oi
媙N鵘J釯A
w彔�4雵J-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪諴���:蜼胻5Y甽2�5)2#H挎驻趀畈�8畎6DJ諥鬥粧yR2�,�	8壎d{┏dd�a�:画k湻J處�,砈M铩d0 �o洃M{>�0
蟨Tq��E垙U>觚�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G�-坓�(鬄鮳�>i,夿雵J-WV8om�M%>mb-坓�(鬄�汬'这枾骺弨bx旊%-<$澊蘁	!+k不挷孨U
M!维猉�5疃沤鱅�64�4礻n�尔z�#!泔潥�:yWh趓挌�
*�
M诹蕞Y尵羱書:霸珵�%訴熝�f[R鸣眩ot|%rW魨旴Y>�錢荩杫X]绪/ｎ	蜍R僁舴�:kn嶜�:5齵h周洊{= 栰宧霅;�d_f湲z竘雵J-WV8om�M%>mb9E\$L釉�|m�/.L-坓�(鬄酲;[純o�-坓�(鬄�汬'这栯嘕-WV8om�M%>mb孄6氆-=��(_甝�<连!槉覃z~=�蟻g�诸m赵n�lNy�&9�焄撄M唘r 0
跚砢]崝湤欦�
煢5熅轫J妨t	M唘r 0
踵!B<m朻�=滯侴bhthi誋挎驻趀顢重愃據辙WV�"�鄗邘c膋c殼4阐僛�#w(熻=澝懅)[新�6袹,0�%芖>粸d}pOx�,M�63磱6�9r
��篵滓d筨<#貘衵秳�63磱6�9興�<葺K�-坓�(鬄�汬'这朮�
缧猫伞賉�-S粦dd�a�:ev]Im祁�?~譨.�?覡M4Tv週穮鹴aR�,F_棢杻#Q�牁N�%閍�#鳝��葨麋T搜n2竌Vs=�
q餤桹姯}7� qh褷’�濬滲
w�y��"�狯謴稿嫥梸�-�3�塉4x�>�y��"�鄍 ^R黫�3#\2��9Yo�:^JBw��3灿袜OS� 兙軶p尗`�c�3X雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛c闲�
墸g綂硦贳3K炓�	6KRY]�杞掄dd�a�:ZmQ1&徉威嵌�;<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦荘绝�h�@洐飨y*�杜`颀l+�鞯.r擣�0G#盱谑M-�=QgL(��苳乮5絚_}4n4�硓�9E\$L釉��E光z�:喍')I/剑�My*�杜`颀l+�鞯.r擣�0G#盱谑艒鳜~�(��苳乮5絚_}4n4�硓�-坓�(鬄�/ｎ	蜍R9E\$L釉��E光潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H潗幭恫V迚玸鐲髑f:蓰咨那^矧你荋y刊/js輢A+|捩�彫柃鍏穧 5]叨蝝�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2�%ZZ�$为赞G刹~赣 "^惋砤雀擡@wX+]闆籉K�8k!阑,�粰趭+�揃T爃.L�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       ,�               .debug$T       p                 .rdata         0       ��                     .text$mn       :      眡�     .debug$S                    .text$mn       0      燥"V     .debug$S       �             .text$mn    	   0      燥"V     .debug$S    
   �         	    .text$mn             �u鹟     .debug$S       $             .text$mn    
   c       謠锖     .debug$S       |         
    .text$mn              昹	�     .debug$S       l  
           .text$mn              昹	�     .debug$S       p  
           .text$mn              昹	�     .debug$S       t  
           .text$mn       c       謠锖     .debug$S       x             .text$mn       p       '      .debug$S       �             .text$mn              !'�     .debug$S       X             .text$mn              !'�     .debug$S       \             .text$mn              !'�     .debug$S       `             .text$mn       p       '      .debug$S        �             .text$mn    !          4鈋�     .debug$S    "   �          !    .text$mn    #   �     ov     .debug$S    $   P  ^       #    .text$x     %   (      纥吨#    .text$mn    &   =     @3渶     .debug$S    '   <
  P       &    .text$x     (   (      桕Q&    .text$mn    )        轠     .debug$S    *   �  \       )    .text$x     +         S�)    .text$x     ,         S�)    .text$mn    -   �       `螏�     .debug$S    .   �         -    .text$mn    /   �      �l     .debug$S    0   <         /    .text$mn    1   �       Y�     .debug$S    2   D         1    .text$mn    3   G       M>$w     .debug$S    4   �         3    .text$mn    5   G       M>$w     .debug$S    6            5    .text$mn    7   G       M>$w     .debug$S    8            7    .text$mn    9   �       Y�     .debug$S    :   D         9    .text$mn    ;           _葓�     .debug$S    <   �         ;    .text$mn    =   )       {�     .debug$S    >   �  
       =    .text$mn    ?          c     .debug$S    @   8         ?    .text$mn    A   �     冔2�     .debug$S    B   �  `       A    .text$x     C         �/闍    .text$x     D         /fA    .text$x     E         朆|夾    .text$x     F         V荱bA    .text$x     G         燝袓A    .text$x     H         蘉垈A    .text$x     I         `摡覣    .text$x     J         麘蠥    .text$mn    K   <      .ズ     .debug$S    L   0  
       K    .text$mn    M   <      .ズ     .debug$S    N   L  
       M    .text$mn    O   !      :著�     .debug$S    P   <         O    .text$mn    Q   <      .ズ     .debug$S    R   8  
       Q    .text$mn    S         歠     .debug$S    T   �          S    .text$mn    U   2      X于     .debug$S    V   <         U    .text$mn    W   "       坼	     .debug$S    X   �         W    .text$mn    Y   "       坼	     .debug$S    Z   �         Y    .text$mn    [         ��     .debug$S    \            [    .text$mn    ]   �      ^(�     .debug$S    ^   �         ]    .text$mn    _   [       荘�     .debug$S    `   �         _    .text$mn    a         ��     .debug$S    b   (         a    .text$mn    c           哷/�     .debug$S    d   �  
       c    .text$mn    e   \      )t裬     .debug$S    f   �         e    .text$mn    g   K       }'     .debug$S    h   �         g    .text$mn    i         �%     .debug$S    j   L         i    .text$mn    k   [      J败     .debug$S    l            k    .text$mn    m   [      穥�     .debug$S    n   �         m    .text$mn    o        L牮�     .debug$S    p   L	  H       o    .text$mn    q   L     兗,�     .debug$S    r   ,  B       q    .text$mn    s         ��#     .debug$S    t   �          s    .text$mn    u         ��#     .debug$S    v   �          u    .text$mn    w         ��#     .debug$S    x   �          w    .text$mn    y   o       XE�     .debug$S    z   $  
       y    .text$mn    {   �      噬哿     .debug$S    |   8         {    .text$mn    }   B      贘S     .debug$S    ~             }    .text$mn       B      贘S     .debug$S    �                .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   B      贘S     .debug$S    �   �          �    .text$mn    �        �3�     .debug$S    �   �  D       �    .text$mn    �   �      栅N     .debug$S    �            �    .text$mn    �   F       �;g�     .debug$S    �   �         �    .text$mn    �         湱8{     .debug$S    �   �  $       �    .text$x     �         S軏    .text$mn    �   W     ~     .debug$S    �   �  $       �    .text$mn    �   :     愽鉻     .debug$S    �   x  <       �    .text$mn    �        4噶     .debug$S    �   \  P       �    .text$mn    �   )     谔孲     .debug$S    �   	  *       �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �   B      鸮     .debug$S    �   �         �    .text$mn    �   A      o漮     .debug$S    �   �         �    .text$mn    �   q     }x     .debug$S    �   �
  >       �    .text$mn    �   �     	r翍     .debug$S    �     6       �    .text$mn    �         覲A     .debug$S    �   �          �    .text$mn    �         崪覩     .debug$S    �   �          �        )                8                H                X                {                �                �                �       U        �       w              �        "      �        A          id                   `      K        �      }        �          ih                   �      O        �      s        	      M        5              _          in                   �      �        �      9        �      1        &      5        s      7        �      3        	      S        &      �        I      u        f      Q        �      �        �          i{                   �      �        �      ?              =        �      o        �      �        �      c        2      {        �      Y        �      W        �      �        V      k        �      �              e        �      �        �	      �        �
      _        �      ]        �      i        T
      �        �
      �        �
      m        �
      �               �        �      �        S      �        �      A        �      q              �        d      �        �      �        +      �        l      g        �      /        I      y        �      )        :      ;        a      -        z      [        �      a        v      &                      A      #        �              �              (              �              �              *              V      !        }              �              �              N              �      
        �              #      	        �      %        �      (        �      +        :       C        �       �        �       I        O!      J        �!      ,        g#      D        �#      E        5$      F        �$      G        %      H        j%               }%           ceilf            memmove          memset           $LN5        U    $LN10       �    $LN7        K    $LN13       }    $LN10       M    $LN16           $LN3        �    $LN4        �    $LN7        Q    $LN13       �    $LN3        �    $LN4        �    $LN163      o    $LN54       �    $LN142      {    $LN10       Y    $LN10       W    $LN18   A   �    $LN21       �    $LN28   [   k    $LN31       k    $LN3       �    $LN4        �    $LN63       e    $LN192    �    $LN196      �    $LN82   :  �    $LN85       �    $LN30   [   _    $LN33       _    $LN93   �   ]    $LN96       ]    $LN18   B   �    $LN21       �    $LN3       �    $LN4        �    $LN35   [   m    $LN38       m    $LN121      �    $LN226  q  �    $LN233      �    $LN185  )  �    $LN192      �    $LN3       �    $LN4        �    $LN156      A    $LN94   L  q    $LN97       q    $LN125      �    $LN64   �   �    $LN67       �    $LN120      �    $LN18       g    $LN54       /    $LN180    )    $LN188      )    $LN20       -    $LN99   =  &        �%  
   (    $LN103      &    $LN4            $LN102  �  #        7&  
   %    $LN106      #    $LN14   :       $LN17           $LN4        	    .xdata      �          （亵U        �&      �    .pdata      �          T枨U        �&      �    .xdata      �          %蚘%�        �&      �    .pdata      �         惻竗�        '      �    .xdata      �          （亵K        )'      �    .pdata      �         2Fb襅        R'      �    .xdata      �          %蚘%}        z'      �    .pdata      �         惻竗}        �'      �    .xdata      �          （亵M        �'      �    .pdata      �         2Fb襇        �'      �    .xdata      �          %蚘%        .(      �    .pdata      �         惻竗        `(      �    .xdata      �          懐j灃        �(      �    .pdata      �         Vbv鶘        �(      �    .xdata      �          （亵Q        �(      �    .pdata      �         2Fb襋        )      �    .xdata      �          %蚘%�        G)      �    .pdata      �         惻竗�        q)      �    .xdata      �          懐j灅        �)      �    .pdata      �         Vbv鶚        �)      �    .voltbl     �          賚c=    _volmd      �    .xdata      �          璡荢o        �)      �    .pdata      �         �o        *      �    .xdata      �         Mw2檕        L*      �    .xdata      �          受尨o        �*      �    .voltbl     �   
       踪怽o    _volmd      �    .xdata      �         猐�        �*      �    .pdata      �         �麐        �*      �    .xdata      �   	      � )9�        1+      �    .xdata      �         遱谸�        r+      �    .xdata      �          瑭顙        �+      �    .voltbl     �          Z铧噵    _volmd      �    .voltbl     �          -哥c    _volmd      �    .xdata      �          Uqi箋        �+      �    .pdata      �         /
聓        b,      �    .xdata      �         /
        �,      �    .pdata      �         +eS籝        -      �    .xdata      �   	      �#荤Y        :-      �    .xdata      �         jY        u-      �    .xdata      �          3狷 Y        �-      �    .xdata      �         /
        �-      �    .pdata      �         +eS籛        3.      �    .xdata      �   	      �#荤W        t.      �    .xdata      �         jW        �.      �    .xdata      �          3狷 W        /      �    .xdata      �          �9��        F/      �    .pdata      �         s�7澧        �/      �    .xdata      �          （亵k        0      �    .pdata      �         愶Lk        j0      �    .xdata      �          �9��        �0      �    .pdata      �         �1盀        41      �    .xdata      �          3�俥        �1      �    .pdata      �         �#洢e        2      �    .xdata      �         狳%e        �2      �    .pdata      �         ì哑e        '3      �    .xdata      �         k商e        �3      �    .pdata      �         馆豦        14      �    .xdata      �          �皰        �4      �    .pdata      �         鷓V �        �5      �    .xdata      �         �@垝        �6      �    .pdata      �         壤餧�        8      �    .xdata      �         
殲7�        79      �    .pdata      �         D�        X:      �    .xdata      �         $*т�        y;      �    .pdata      �         檍~/�        �<      �    .xdata      �          彤逴�        �=      �    .pdata      �         敒��        �>      �    .xdata      �         �檼        �?      �    .pdata      �         F熾皰        A      �    .xdata      �          ii@�        ?B      �    .pdata      �         礝
�        wC      �    .xdata      �         塯4窅        瓺      �    .pdata      �         囥鱢�        鏓      �    .xdata      �         Y瓙         G      �    .pdata      �         s�&k�        YH      �    .xdata      �         n奧w�        扞      �    .pdata      �         '擊倫        薐      �    .xdata      �          （亵_        L      �    .pdata      �         愶L_        稬      �    .xdata      �          %蚘%]        iM      �    .pdata      �         o嗦$]        zN      �    .xdata      �          �9��        奜      �    .pdata      �         惻竗�        縊      �    .xdata      �          �9��        驩      �    .pdata               �1皽        /P          .xdata               （亵m        jP         .pdata              愶Lm                 .xdata               晫�        跴         .pdata              SIF2�        Q         .xdata              闕bs�        ^Q         .pdata              3囯甩                 .xdata              -莋郐        銺         .pdata              奛稛�        'R         .xdata      	        適紃�        jR      	   .pdata      
        �瓝�        璕      
   .xdata              浚嫤        餜         .pdata              =L �        3S         .xdata      
        G	KN�        vS      
   .pdata              瞀#o�        筍         .xdata              浚嫤        黃         .pdata              ;/H        ?T         .xdata              J�        俆         .pdata              '�e鳏        ?U         .xdata              Mw2櫎        鸘         .xdata               窠k        篤         .xdata               亾%�        yW         .pdata              羲X#�        �W         .xdata              蒬'�        刋         .pdata              爭靫�        Y         .xdata              oo扦�        扽         .pdata              �1~稊        Z         .xdata              Ld涣�        燴         .pdata              Da忖�        '[         .xdata              !殧        甗         .pdata              毥;        5\         .xdata              e4鬚�        糪         .pdata               麨k軘        C]          .xdata      !        !殧        蔧      !   .pdata      "        澵eF�        Q^      "   .xdata      #         �9��        豝      #   .pdata      $        �1皻        _      $   .xdata      %        顰        M_      %   .pdata      &        斣疃A        璤      &   .xdata      '  	      � )9A        `      '   .xdata      (  4   
   '$A        n`      (   .xdata      )         y
F餉        謄      )   .xdata      *        vQ9	q        8a      *   .pdata      +        蹺豵        pa      +   .xdata      ,  	      �#荤q              ,   .xdata      -        jq        醓      -   .xdata      .         L�q        !b      .   .voltbl     /         4冣q    _volmd      /   .xdata      0         杕夯�        [b      0   .pdata      1        OAG悈        筨      1   .xdata      2         亾朿�        c      2   .pdata      3        GC��        uc      3   .xdata      4        躲}�        詂      4   .pdata      5        h彾�        3d      5   .xdata      6        锊�        抎      6   .pdata      7        榇0v�        馾      7   .xdata      8        钞L�        Pe      8   .pdata      9        釅�
�        痚      9   .xdata      :        锊�        f      :   .pdata      ;        滫叒�        mf      ;   .xdata      <        ＋)�        蘤      <   .pdata      =        ^繗�        +g      =   .xdata      >         ho巶�        奼      >   .pdata      ?        .d��        h      ?   .voltbl     @         嵹1爣    _volmd      @   .xdata      A         U媷n�        }h      A   .pdata      B        k駭鶐        苃      B   .xdata      C         （亵g        i      C   .pdata      D        � 賕        Zi      D   .xdata      E        范^揼              E   .pdata      F        鳶�g        騣      F   .xdata      G        @鴚`g        ?j      G   .pdata      H        [7躦        宩      H   .voltbl     I         飾殪g    _volmd      I   .xdata      J         O�/        賘      J   .pdata      K        ]騂1/        zl      K   .xdata      L  $      �$s�)        n      L   .pdata      M        �+SM)        胦      M   .xdata      N  	      � )9)        kq      N   .xdata      O  
      諕附)        s      O   .xdata      P  
       s砺�)        莟      P   .xdata      Q         確-        rv      Q   .pdata      R        OAG�-        搘      R   .xdata      S        +縬[-        硏      S   .pdata      T        蹷謔-        誽      T   .xdata      U        ＋)-        鱶      U   .pdata      V        穣-        |      V   .xdata      W        祕i&        ;}      W   .pdata      X        M扛&        鄛      X   .xdata      Y  
      B>z]&        剘      Y   .xdata      Z         �2g�&        +      Z   .xdata      [        T�8&        �      [   .xdata      \        r%�&        }�      \   .xdata      ]         tA�p&        &�      ]   .xdata      ^         3賟P&        蛠      ^   .pdata      _        銀�*&        倐      _   .voltbl     `             (    _volmd      `   .xdata      a         %蚘%        6�      a   .pdata      b        }S蛥        l�      b   .xdata      c        屐�:#              c   .pdata      d        8敢�#        髢      d   .xdata      e  
      B>z]#        D�      e   .xdata      f         �2g�#        槃      f   .xdata      g        T�8#        騽      g   .xdata      h        r%�#        D�      h   .xdata      i  	       8.騈#        殔      i   .xdata      j         3賟P#        顓      j   .pdata      k        銀�*#        P�      k   .voltbl     l             %    _volmd      l   .xdata      m         �9�        眴      m   .pdata      n        礝
        �      n   .xdata      o         %蚘%	        j�      o   .pdata      p        }S蛥	        覈      p   .rdata      q                     9�     q   .rdata      r         �;�         P�      r   .rdata      s                     w�     s   .rdata      t                     巿     t   .rdata      u         �)         皥      u   .xdata$x    v                     軋      v   .xdata$x    w        虼�)               w   .data$r     x  /      嶼�         !�      x   .xdata$x    y  $      4��         F�      y   .data$r     z  $      鎊=         泬      z   .xdata$x    {  $      銸E�         祲      {   .data$r     |  $      騏糡         魤      |   .xdata$x    }  $      4��         �      }       M�           .data       ~          烀�          `�      ~       攰     ~   .rdata                           粖        .rdata      �  
       x禳�         諍      �   .xdata$x    �                     鯅      �   .xdata$x    �        煘;�         �      �   .data$r     �  '      �!F�         *�      �   .xdata$x    �  $      4��         G�      �   .rdata      �         O         寢      �   .rdata      �         ��         緥      �   .rdata      �         藾味         饗      �   .rdata      �         IM         !�      �   .rdata$r    �  $      'e%�         G�      �   .rdata$r    �        �          _�      �   .rdata$r    �                     u�      �   .rdata$r    �  $      Gv�:         媽      �   .rdata$r    �  $      'e%�         獙      �   .rdata$r    �        }%B         聦      �   .rdata$r    �                     貙      �   .rdata$r    �  $      `         顚      �   .rdata$r    �  $      'e%�         
�      �   .rdata$r    �        �弾         0�      �   .rdata$r    �                     Q�      �   .rdata$r    �  $      H衡�         r�      �   .rdata$r    �  $      'e%�         湇      �   .rdata$r    �        }%B         穽      �   .rdata$r    �                     袓      �   .rdata$r    �  $      `         閸      �   .rdata      �         eL喳         �      �   _fltused         .debug$S    �  8             .debug$S    �  4          q   .debug$S    �  4          s   .debug$S    �  @          t   .chks64     �  �                �  ?c_IdentityTransform@rt@nvrhi@@3QBMB ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ??$hash_combine@_K@nvrhi@@YAXAEA_KAEB_K@Z ??$hash_combine@PEAVIResource@nvrhi@@@nvrhi@@YAXAEA_KAEBQEAVIResource@0@@Z ??$hash_combine@W4ResourceType@nvrhi@@@nvrhi@@YAXAEA_KAEBW4ResourceType@0@@Z ??$hash_combine@W4TextureDimension@nvrhi@@@nvrhi@@YAXAEA_KAEBW4TextureDimension@0@@Z ??$hash_combine@W4Format@nvrhi@@@nvrhi@@YAXAEA_KAEBW4Format@0@@Z ??0bad_weak_ptr@std@@QEAA@XZ ?what@bad_weak_ptr@std@@UEBAPEBDXZ ??1bad_weak_ptr@std@@UEAA@XZ ??0bad_weak_ptr@std@@QEAA@AEBV01@@Z ??_Gbad_weak_ptr@std@@UEAAPEAXI@Z ??_Ebad_weak_ptr@std@@UEAAPEAXI@Z ?_Throw_bad_weak_ptr@std@@YAXXZ ??0DescriptorHandle@engine@donut@@QEAA@XZ ??0DescriptorHandle@engine@donut@@QEAA@AEBV?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@H@Z ??1DescriptorHandle@engine@donut@@QEAA@XZ ?GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ ??1?$enable_shared_from_this@VDescriptorTableManager@engine@donut@@@std@@IEAA@XZ ??RBindingSetItemHasher@DescriptorTableManager@engine@donut@@QEBA_KAEBUBindingSetItem@nvrhi@@@Z ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIDescriptorTable@nvrhi@@@nvrhi@@QEAA@XZ ?deallocate@?$allocator@UBindingSetItem@nvrhi@@@std@@QEAAXQEAUBindingSetItem@nvrhi@@_K@Z ??1?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@CAXXZ ??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ ?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@QEAA@XZ ??1?$unordered_map@UBindingSetItem@nvrhi@@HUBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@QEAA@XZ ?deallocate@?$allocator@I@std@@QEAAXQEAI_K@Z ?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ ??1?$vector@_NV?$allocator@_N@std@@@std@@QEAA@XZ ?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z ?erase@?$vector@_NV?$allocator@_N@std@@@std@@QEAA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@V?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@0@Z ?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z ?_Xlen@?$vector@_NV?$allocator@_N@std@@@std@@SAXXZ ??0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z ??1DescriptorTableManager@engine@donut@@QEAA@XZ ?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z ?CreateDescriptorHandle@DescriptorTableManager@engine@donut@@QEAA?AVDescriptorHandle@23@UBindingSetItem@nvrhi@@@Z ?GetDescriptor@DescriptorTableManager@engine@donut@@QEAA?AUBindingSetItem@nvrhi@@H@Z ?ReleaseDescriptor@DescriptorTableManager@engine@donut@@QEAAXH@Z ??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ ??$find@X@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@1@AEBUBindingSetItem@nvrhi@@@Z ??H?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@std@@QEBA?AV01@_J@Z ??$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z ??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Hash_representation@W4Format@nvrhi@@@std@@YA_KAEBW4Format@nvrhi@@@Z ??$_Hash_representation@W4TextureDimension@nvrhi@@@std@@YA_KAEBW4TextureDimension@nvrhi@@@Z ??$_Hash_representation@W4ResourceType@nvrhi@@@std@@YA_KAEBW4ResourceType@nvrhi@@@Z ??$_Hash_representation@PEAVIResource@nvrhi@@@std@@YA_KAEBQEAVIResource@nvrhi@@@Z ??$_Hash_representation@_K@std@@YA_KAEB_K@Z ??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z ??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z ??$_Fnv1a_append_value@W4Format@nvrhi@@@std@@YA_K_KAEBW4Format@nvrhi@@@Z ??$_Fnv1a_append_value@W4TextureDimension@nvrhi@@@std@@YA_K_KAEBW4TextureDimension@nvrhi@@@Z ??$_Fnv1a_append_value@W4ResourceType@nvrhi@@@std@@YA_K_KAEBW4ResourceType@nvrhi@@@Z ??$_Fnv1a_append_value@PEAVIResource@nvrhi@@@std@@YA_K_KAEBQEAVIResource@nvrhi@@@Z ??$_Fnv1a_append_value@_K@std@@YA_K_KAEB_K@Z ??$_Copy_memmove@PEAUBindingSetItem@nvrhi@@PEAU12@@std@@YAPEAUBindingSetItem@nvrhi@@PEAU12@00@Z ?catch$0@?0???$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0???$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z@4HA ?dtor$0@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$0@?0??GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ@4HA ?dtor$10@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$11@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$1@?0???$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z@4HA ?dtor$1@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$2@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$3@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$4@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA ?dtor$5@?0???0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z@4HA _CxxThrowException __CxxFrameHandler4 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z$0 $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??0bad_weak_ptr@std@@QEAA@AEBV01@@Z $pdata$??0bad_weak_ptr@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_weak_ptr@std@@UEAAPEAXI@Z $pdata$??_Gbad_weak_ptr@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_weak_ptr@std@@YAXXZ $pdata$?_Throw_bad_weak_ptr@std@@YAXXZ $unwind$??1DescriptorHandle@engine@donut@@QEAA@XZ $pdata$??1DescriptorHandle@engine@donut@@QEAA@XZ $cppxdata$??1DescriptorHandle@engine@donut@@QEAA@XZ $ip2state$??1DescriptorHandle@engine@donut@@QEAA@XZ $unwind$?GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ $pdata$?GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ $cppxdata$?GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ $stateUnwindMap$?GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ $ip2state$?GetIndexInHeap@DescriptorHandle@engine@donut@@QEBAHXZ $unwind$??RBindingSetItemHasher@DescriptorTableManager@engine@donut@@QEBA_KAEBUBindingSetItem@nvrhi@@@Z $pdata$??RBindingSetItemHasher@DescriptorTableManager@engine@donut@@QEBA_KAEBUBindingSetItem@nvrhi@@@Z $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIDescriptorTable@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDescriptorTable@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDescriptorTable@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDescriptorTable@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDescriptorTable@nvrhi@@@nvrhi@@QEAA@XZ $unwind$?deallocate@?$allocator@UBindingSetItem@nvrhi@@@std@@QEAAXQEAUBindingSetItem@nvrhi@@_K@Z $pdata$?deallocate@?$allocator@UBindingSetItem@nvrhi@@@std@@QEAAXQEAUBindingSetItem@nvrhi@@_K@Z $unwind$??1?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@CAXXZ $unwind$??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ $chain$0$??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ $pdata$0$??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ $chain$1$??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ $pdata$1$??1?$list@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@2@@std@@QEAA@XZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$4$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$4$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$7$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$7$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $chain$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $pdata$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $chain$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $pdata$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $chain$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $pdata$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@2@@Z $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@QEAA@XZ $unwind$?deallocate@?$allocator@I@std@@QEAAXQEAI_K@Z $pdata$?deallocate@?$allocator@I@std@@QEAAXQEAI_K@Z $unwind$?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ $unwind$??1?$vector@_NV?$allocator@_N@std@@@std@@QEAA@XZ $pdata$??1?$vector@_NV?$allocator@_N@std@@@std@@QEAA@XZ $unwind$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $chain$0$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$0$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $chain$1$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$1$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $chain$2$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$2$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $chain$3$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$3$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $chain$4$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$4$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $chain$5$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $pdata$5$?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z $unwind$?erase@?$vector@_NV?$allocator@_N@std@@@std@@QEAA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@V?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@0@Z $pdata$?erase@?$vector@_NV?$allocator@_N@std@@@std@@QEAA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@V?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@0@Z $cppxdata$?erase@?$vector@_NV?$allocator@_N@std@@@std@@QEAA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@V?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@0@Z $ip2state$?erase@?$vector@_NV?$allocator@_N@std@@@std@@QEAA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@V?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@0@Z $unwind$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $chain$0$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$0$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $chain$1$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$1$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $chain$2$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$2$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $chain$3$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$3$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $chain$4$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$4$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $chain$5$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $pdata$5$?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z $unwind$?_Xlen@?$vector@_NV?$allocator@_N@std@@@std@@SAXXZ $pdata$?_Xlen@?$vector@_NV?$allocator@_N@std@@@std@@SAXXZ $unwind$??0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z $pdata$??0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z $cppxdata$??0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z $stateUnwindMap$??0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z $ip2state$??0DescriptorTableManager@engine@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVIBindingLayout@4@@Z $unwind$??1DescriptorTableManager@engine@donut@@QEAA@XZ $pdata$??1DescriptorTableManager@engine@donut@@QEAA@XZ $cppxdata$??1DescriptorTableManager@engine@donut@@QEAA@XZ $stateUnwindMap$??1DescriptorTableManager@engine@donut@@QEAA@XZ $ip2state$??1DescriptorTableManager@engine@donut@@QEAA@XZ $unwind$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $chain$4$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$4$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $chain$5$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$5$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $chain$6$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$6$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $chain$7$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$7$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $chain$8$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$8$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $chain$9$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $pdata$9$?CreateDescriptor@DescriptorTableManager@engine@donut@@QEAAHUBindingSetItem@nvrhi@@@Z $unwind$?CreateDescriptorHandle@DescriptorTableManager@engine@donut@@QEAA?AVDescriptorHandle@23@UBindingSetItem@nvrhi@@@Z $pdata$?CreateDescriptorHandle@DescriptorTableManager@engine@donut@@QEAA?AVDescriptorHandle@23@UBindingSetItem@nvrhi@@@Z $unwind$?ReleaseDescriptor@DescriptorTableManager@engine@donut@@QEAAXH@Z $pdata$?ReleaseDescriptor@DescriptorTableManager@engine@donut@@QEAAXH@Z $unwind$??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VDescriptorTableManager@engine@donut@@@std@@QEAA@XZ $unwind$??$find@X@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@1@AEBUBindingSetItem@nvrhi@@@Z $pdata$??$find@X@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@1@AEBUBindingSetItem@nvrhi@@@Z $unwind$??$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z $pdata$??$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z $cppxdata$??$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z $stateUnwindMap$??$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z $ip2state$??$_Try_emplace@AEBUBindingSetItem@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UBindingSetItem@nvrhi@@HV?$_Uhash_compare@UBindingSetItem@nvrhi@@UBindingSetItemHasher@DescriptorTableManager@engine@donut@@UBindingSetItemsEqual@456@@std@@V?$allocator@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@PEAX@std@@_N@1@AEBUBindingSetItem@nvrhi@@@Z $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUBindingSetItem@nvrhi@@H@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UBindingSetItem@nvrhi@@V?$allocator@UBindingSetItem@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z $pdata$??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z $unwind$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $pdata$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $cppxdata$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $stateUnwindMap$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $tryMap$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $handlerMap$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $ip2state$??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z $unwind$?catch$0@?0???$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z@4HA $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Copy_memmove@PEAUBindingSetItem@nvrhi@@PEAU12@@std@@YAPEAUBindingSetItem@nvrhi@@PEAU12@00@Z $pdata$??$_Copy_memmove@PEAUBindingSetItem@nvrhi@@PEAU12@@std@@YAPEAUBindingSetItem@nvrhi@@PEAU12@00@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7bad_weak_ptr@std@@6B@ ??_C@_0N@FJHHFFAF@bad_weak_ptr@ _TI2?AVbad_weak_ptr@std@@ _CTA2?AVbad_weak_ptr@std@@ ??_R0?AVbad_weak_ptr@std@@@8 _CT??_R0?AVbad_weak_ptr@std@@@8??0bad_weak_ptr@std@@QEAA@AEBV01@@Z24 ??_C@_0BG@EOMJEIFA@vector?$DMbool?$DO?5too?5long@ ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R4bad_weak_ptr@std@@6B@ ??_R3bad_weak_ptr@std@@8 ??_R2bad_weak_ptr@std@@8 ??_R1A@?0A@EA@bad_weak_ptr@std@@8 __real@5f000000 