d啋 翀}� �      .drectve        <  �               
 .debug$S        p�     �        @ B.debug$T        p   �             @ B.rdata          <   P             @ @@.text$mn        D  �              P`.debug$S        �  � �        @B.text$mn        :   � �         P`.debug$S          � �        @B.text$mn        7   �               P`.debug$S        t  �  /"        @B.text$mn        D   �"              P`.debug$S        �  �" w$        @B.text$mn        �  �$ �'         P`.debug$S        �  �' �7     \   @B.text$x            N; Z;         P`.text$x            d; p;         P`.text$mn        �   z;              P`.debug$S        �  �; �>        @B.text$mn        i   �?              P`.debug$S        @  �? ?B        @B.text$mn            薆              P`.debug$S        T  隑 ?F        @B.text$mn           逨              P`.debug$S        �   﨔 頖        @B.text$mn        <   H RH         P`.debug$S        0  pH 營     
   @B.text$mn        <   J @J         P`.debug$S        L  ^J 狵     
   @B.text$mn        !   L /L         P`.debug$S        <  CL M        @B.text$mn        2   籑 鞰         P`.debug$S        <  N =O        @B.text$mn           礝 蒓         P`.debug$S        d  覱 7R        @B.text$mn        J   嘡 裄         P`.debug$S        �  跼 _V        @B.text$mn           'W :W         P`.debug$S        �   NW 2X        @B.text$mn           ZX mX         P`.debug$S        �   乆 aY        @B.text$mn        B   漎 遈         P`.debug$S           齓 齔        @B.text$mn        B   9[ {[         P`.debug$S          橻 ‐        @B.text$mn        B   錦 ']         P`.debug$S        �   E] A^        @B.text$mn        H   }^              P`.debug$S        �  臹 塦        @B.text$mn        8                 P`.debug$S        d  賏 =c        @B.text$mn          輈 鄁         P`.debug$S        t  g |n     2   @B.text$mn        :  pp 猶         P`.debug$S        �  鎞 妝     <   @B.text$mn        �  鈡 瑌         P`.debug$S          詝 靾     P   @B.text$mn            � ,�         P`.debug$S        �   J� �        @B.text$mn           J� ]�         P`.debug$S        �   g� ;�        @B.xdata             w�             @0@.pdata             嫀 棊        @0@.xdata             祹             @0@.pdata             綆 蓭        @0@.xdata             鐜             @0@.pdata             髱 ��        @0@.xdata             �             @0@.pdata             %� 1�        @0@.xdata             O�             @0@.pdata             [� g�        @0@.xdata             厪             @0@.pdata             崗 檹        @0@.xdata             窂             @0@.pdata             脧 蠌        @0@.xdata             韽             @0@.pdata             鯊 �        @0@.xdata             �             @0@.pdata             ;� G�        @0@.xdata             e�             @0@.pdata             q� }�        @0@.xdata             洂 硱        @0@.pdata             褠 輴        @0@.xdata             麗 �        @0@.pdata             )� 5�        @0@.xdata             S� k�        @0@.pdata             墤 晳        @0@.xdata          (   硲 蹜        @0@.pdata             飸 麘        @0@.xdata          	   � "�        @@.xdata             6� <�        @@.xdata          
   F�             @@.xdata             S�             @0@.pdata             _� k�        @0@.xdata          $   墥 瓛        @0@.pdata             翏 蛼        @0@.xdata          	   霋 魭        @@.xdata             � �        @@.xdata          
   *�             @@.xdata             4�             @0@.pdata             <� H�        @0@.xdata             f� z�        @0@.pdata             槗         @0@.xdata             聯 覔        @0@.pdata             饟 鼡        @0@.xdata             �             @0@.pdata             &� 2�        @0@.xdata             P� `�        @0@.pdata             t� ��        @0@.xdata          	   灁         @@.xdata             粩 翑        @@.xdata             藬             @@.xdata             螖             @0@.pdata             謹 鈹        @0@.rdata              � �        @@@.rdata             6�             @@@.rdata             H� `�        @@@.rdata             ~� 枙        @@@.rdata             磿             @@@.xdata$x           蓵 鍟        @@@.xdata$x           鶗 �        @@@.data$r         /   3� b�        @@�.xdata$x        $   l� 悥        @@@.data$r         $    葨        @@�.xdata$x        $   覗 鰱        @@@.data$r         $   
� .�        @@�.xdata$x        $   8� \�        @@@.data               p�             @ @�.rdata             悧             @@@.rdata             珬             @@@.rdata$r        $   艞 闂        @@@.rdata$r           � �        @@@.rdata$r           %� 1�        @@@.rdata$r        $   ;� _�        @@@.rdata$r        $   s� 棙        @@@.rdata$r           禈 蓸        @@@.rdata$r           訕 鐦        @@@.rdata$r        $   麡 �        @@@.rdata$r        $   3� W�        @@@.rdata$r           u� 墮        @@@.rdata$r           摍 瘷        @@@.rdata$r        $   蜋 駲        @@@.rdata             �             @0@.debug$S        4   	� =�        @B.debug$S        4   Q� 厷        @B.debug$S        @   櫄 贇        @B.chks64         �  須              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �  p     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\FramebufferFactory.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $literals  $string_literals  $string_view_literals  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $math 	 $colors  �   �4    �        nvrhi::EntireBuffer L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos :    std::integral_constant<unsigned __int64,1>::value ) �   donut::math::frustum::numCorners A    std::allocator<char>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity �    std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible Z�    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible ]�   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos �    std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable + <        nvrhi::rt::c_IdentityTransform m   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g�    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment c    std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos . d   donut::math::box<float,3>::numCorners  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN a    std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , d  	 std::numeric_limits<long>::digits10 P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity 0 �   std::numeric_limits<__int64>::is_signed - d  ? std::numeric_limits<__int64>::digits / d   std::numeric_limits<__int64>::digits10 d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 7 �   std::numeric_limits<unsigned short>::is_modulo 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10    ��I@donut::math::PI_f "   
�-DT�!	@donut::math::PI_d !   ��7�5donut::math::epsilon "   �  �donut::math::infinity    �  �donut::math::NaN 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10 " �    std::memory_order_relaxed " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst   �   U�  4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size ) �   donut::math::vector<bool,2>::DIM ) �   donut::math::vector<bool,3>::DIM ) �   donut::math::vector<bool,4>::DIM `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos :     std::integral_constant<unsigned __int64,0>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex :    std::integral_constant<unsigned __int64,2>::value � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified * �   donut::math::vector<float,3>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos # �        nvrhi::AllSubresources J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos * �   donut::math::vector<float,4>::DIM *         donut::math::lumaCoefficients L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos * �   donut::math::vector<float,2>::DIM  �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> & �5  $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e  �5  _TypeDescriptor 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const> E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16 s 	E  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � E  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  i5  std::input_iterator_tag ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> > . �2  std::integer_sequence<unsigned __int64>  .  std::_Lockit * 2/  std::hash<enum nvrhi::ResourceType> " i3  std::_Char_traits<char,int>  "   std::_Atomic_counter_t  �2  std::_Num_base K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > ) v3  std::_Narrow_char_traits<char,int>    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc> R 5B  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > \ 蹹  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >  �2  std::_Num_int_base / Q/  std::_Conditionally_enabled_hash<bool,1> < ZD  std::_Tuple_val<nvrhi::TextureSubresourceSet const &>  �2  std::float_denorm_style ! %0  std::piecewise_construct_t 6 �5  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast " �2  std::numeric_limits<double>  <&  std::__non_rtti_object ( n  std::_Basic_container_proxy_ptr12 � )B  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety ! �5  std::char_traits<char32_t>   �2  std::numeric_limits<bool> # �3  std::_WChar_traits<char16_t> P顱  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> � 夿  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > � E  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> % --  std::_One_then_variadic_args_t W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   �5  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  �5  std::false_type S E  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #C  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , �2  std::numeric_limits<unsigned __int64> � 廋  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> � xB  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void> � 肂  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound � FB  std::pair<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> *,bool> ?  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � �?  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  U/  std::hash<bool> 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> � xC  std::_Hash_find_last_result<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> *> � 
E  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >  (  std::hash<long double> 2 fC  std::equal_to<nvrhi::TextureSubresourceSet> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � 镃  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o sB  std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � 0C  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> x E  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / �3  std::_Char_traits<char32_t,unsigned int> ( 1$  std::hash<nvrhi::FramebufferInfo> % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t � 鉈  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > >  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128> N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > ! �2  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  t  std::exception_ptr C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 �5  std::allocator_traits<std::allocator<char32_t> >  �  std::_Iterator_base0 � 緿  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> $ �#  std::hash<nvrhi::BufferRange> ! �5  std::char_traits<char16_t> �  C  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  |  std::tuple<>    std::_Container_base12 0 �2  std::integer_sequence<unsigned __int64,0> ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char>  x5  std::_Invoker_strategy � E  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > $ �2  std::_Default_allocate_traits 3 �5  std::allocator_traits<std::allocator<char> > ! �2  std::numeric_limits<short> . �0  std::allocator<nvrhi::rt::GeometryDesc> ; �  std::basic_string_view<char,std::char_traits<char> > C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock  D  std::bad_alloc B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> # �2  std::numeric_limits<__int64>  �  std::memory_order # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   k5  std::forward_iterator_tag �稡  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > v 鱀  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >    std::_Container_proxy ]�>  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> > l7D  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Range_eraser kD  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Clear_guard  �  std::nested_exception  r  std::_Distance_unknown ( �2  std::numeric_limits<unsigned int> @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff    std::atomic<long> � C  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double> 7 窩  std::tuple<nvrhi::TextureSubresourceSet const &> ; !B  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::allocator<char16_t> ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> * r5  std::_String_constructor_concat_tag  D-  std::allocator<char>    std::nullptr_t � 汣  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1>  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring ' �2  std::numeric_limits<signed char>  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ &/  std::hash<nvrhi::IResource *> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char>  K5  std::char_traits<char>  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > � 谺  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q oC  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1> @ �3  std::_Default_allocator_traits<std::allocator<char16_t> >  -2  std::_Exact_args_t 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> � 瑽  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t>  U3  std::streampos ^ cB  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > ' y/  std::hash<enum nvrhi::ColorMask> O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > � 袲  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > v TB  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *>  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers � 荄  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m �?  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � R?  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets . 狝  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # B#  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  狝  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  #  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  �?  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  ?#  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # L#  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable * �?  nvrhi::RefCountPtr<nvrhi::ITexture>  H!  nvrhi::ComputeState  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec 
 !   _ino_t  !   uint16_t $ :A  donut::engine::ICompositeView  \A  donut::engine::IView   傾  donut::engine::PlanarView ( :>  donut::engine::FramebufferFactory  驛  donut::math::float4x4 " 2@  donut::math::vector<bool,4>    donut::math::float3  Q@  donut::math::affine3  
B  donut::math::float2 #   donut::math::vector<float,3>  u   donut::math::uint  闌  donut::math::plane # 蜙  donut::math::vector<float,4>  A  donut::math::frustum $ 鰼  donut::math::frustum::Corners # 魼  donut::math::frustum::Planes  蜙  donut::math::float4 % 驛  donut::math::matrix<float,4,4> # Q@  donut::math::affine<float,3>   袮  donut::math::box<float,3> " �?  donut::math::vector<bool,2>  袮  donut::math::box3 % |@  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # 
B  donut::math::vector<float,2> M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  e4  _CatchableTypeArray     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24 % P4  __RTTIClassHierarchyDescriptor     __time64_t  m  FILE 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray 
 #   size_t 
    time_t  
  __std_exception_data 
 u   _dev_t  K  lldiv_t  H  _ldiv_t  �  _timespec64  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers    �   X      譫鰿3鳪v鐇�6瘻x侃�h�3&�  ?    j轲P[塵5m榤g摏癭 鋍1O骺�*�  �    齝D屜u�偫[篔聤>橷�6酀嘧0稈  �    _O縋[HU-銌�鼪根�鲋薺篮�j��     嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  U   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  )   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  i   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲     x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  b   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   繃S,;fi@`騂廩k叉c.2狇x佚�  &   猯�諽!~�:gn菾�]騈购����'  b   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  C    d蜯�:＠T邱�"猊`�?d�B�#G騋     溶�$椉�
悇� 騐`菚y�0O腖悘T  �   �暊M茀嚆{�嬦0亊2�;i[C�/a\     R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  ?   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  x   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱     �|v媀幈磈潎o抳*\�=O葚��  K   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  �   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈     zY{���睃R焤�0聃
扨-瘜}  :   穫農�.伆l'h��37x,��
fO��  w   5�\營	6}朖晧�-w氌rJ籠騳榈  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  ;	   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  t	   �*o驑瓂a�(施眗9歐湬

�  �	    I嘛襨签.濟;剕��7啧�)煇9触�.  �	   蜅�萷l�/费�	廵崹
T,W�&連芿  9
   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �
   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �
   D���0�郋鬔G5啚髡J竆)俻w��     圽Q&4Y3巷B:C �_%aP縀懮��,褻G  W   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  >   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  J
   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �
   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �
   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>     鏀q�N�&}
;霂�#�0ncP抝  H   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  +   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  k   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   匐衏�$=�"�3�a旬SY�
乢�骣�  3   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  }   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  n   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   �"睱建Bi圀対隤v��cB�'窘�n  	   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  ?   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   G�膢刉^O郀�/耦��萁n!鮋W VS     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  X   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/     �0�*е彗9釗獳+U叅[4椪 P"��  V   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  *   �=蔑藏鄌�
艼�(YWg懀猊	*)  k   交�,�;+愱`�3p炛秓ee td�	^,  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  @   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  y   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  =   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5  %   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  c    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  +   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  t   副謐�斦=犻媨铩0
龉�3曃譹5D   �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��     寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  H   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�     ;o屮G蕞鍐剑辺a岿;q琂謇:謇  g   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   �
bH<j峪w�/&d[荨?躹耯=�  9   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  
   鹴y�	宯N卮洗袾uG6E灊搠d�  R   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   x      �  �   	  T  �  B   U  �  H   V  �  Y   [  �  �   v  �  U   w  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  @     �  @  2   u  �  q   �    K   R  �  {
  v  �   *	  �  �  �   �  �  @   �  �  �   �  �   ?	    �   Q	  O  �   $	  �  �  �     �      �      �      �  �
  )  �
    *  �
  �   2  �
  �   3  �
  �   9  �  �  >  �  5   @  �
    A  �
  �   B  �
  �   H  �
  �   I  �
  �   Y  0    Z  �  �  ]  �  X  a  �  %   d  0  
  e  �  �  i  �  �  {  �  C  |  �  3    �  �  �  �  @   �  �  F  �  0  w  �  0  q  �  0  j  �  0  K  �  �  �  �  �  �  �  0  �  �  0  �  �  �  j   �  �  G   �  �  <   �  �  1   �  �  )   �  0  �  �  0  S  �  0  '  �  0    �  �  �  �  �  �  �  0  {  �  �  P  �  �  �  �  0  �  �  0  �  �  0    �    �   �  �  G  �    �   �  �  <  �  �  R  �  �  �  �    �  �  0  �   �  �  �  �  �  |  �  	  �  �  �  a  �  �  �  �  �  l  �  �  ;  �    9  �  �  �   �    5  �  �
  �   �   �   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\src\engine\FramebufferFactory.cpp D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\RTXPT\External\Donut\include\donut\core\math\color.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h   �       Lbl  �  �   �  �  
 4      8     
 ~V  �   俈  �  
 W      W     
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb �  �?                  �?                  �?    谐Y>Y7?樰�=H塡$H墊$禕H砍     D�L嬕禞I�%#"勪滘薎3薓3肏L还y7濴3�禕LL3�禕LL3�禕H3�禕HLH3�禕HM�H葫�,(   H3菼拎HI嬃I菻凌H翲華禕	L3葾禞I3薎嬔HH陵H3華禕
HH3華禕HH3菼嬃HH拎H菻�H華禕
L3葾禞I3薎嬔HH陵H3華禕HH3華禕HH3菼嬃H拎HH媩$H菻�H媆$H罥3撩   �   r  � G            D  
   8  �        �std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >::operator()<nvrhi::TextureSubresourceSet> 
 >丅   this  AJ        #  D    >   _Keyval  AK          AR       %F M          
4*3#4"?$;$%5#C59>
 >#     hash  AH  C      AQ  {     �  M        R  ��9>  M        �  ��"  M          ��"  M        v  ��"  M        O  ��"& M        �  ��8
 >#    _Val  AJ  �     9  N N N N N N M        R  ��CH  M        �  ��  M          ��  M        v  ��  M        O  ��& M        �  ��8
 >#    _Val  AJ  �     4  N N N N N N% M        R  &%*% M        �  &% M          &% M        v  &% M        O  &5 M        �  &8
 >#    _Val  AJ  0     f  N N N N N N" M        R  


"1" M        �  


	(" M          


	(" M        v  


	(" M        O  


	(, M        �  J

9!
 >#    _Val  AP  3     Y  N N N N N N N                        @� & h   �  R  v  �    O    �      丅  Othis       O_Keyval  O  �   `           D  0  	   T       �  �
   �  �   �  �   �  �,  �  �1  �  �8  �  �=  �  �C  �  �,   �    0   �   
 �   �    �   �   
 %  �    )  �   
 5  �    9  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   ~    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 s  �    w  �   
 �  �    �  �   
 D�I钩     禕L3�禞MI3繧H3�禞IH3罥�   �   0  L G            7       6   O        �std::_Fnv1a_append_value<unsigned int> 
 >   _Val  AJ          >�#   _Keyval  AK        7  M        �  @2
 >#    _Val  AH          AP         N                        H� 
 h   �        O_Val     �#  O_Keyval  O�   0           7   �      $       $	 �    &	 �6   '	 �,   �    0   �   
 q   �    u   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 D�I钩     H�%#"勪滘薍嬔L3�禔禝MI3繧H3�禞IH3罥�   �   8  M G            D       C   v        �std::_Hash_representation<unsigned int>  >�#   _Keyval  AJ          AK       )  M        O   ( M        �  @
 >#    _Val  AH  -       AP         N N                        H�  h   �  O      �#  O_Keyval  O�   @           D   �      4       *	 �    +	 �   *	 �   +	 �C   ,	 �,   �    0   �   
 u   �    y   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 L  �    P  �   
 H塡$H塴$H塼$ WATAUAVAWH冹0I嬸L孃L嬮I嬓�    H塂$`M婨0L#繫繧婱J媆�M峞M�4$I;辵I嬣I嬵階J�翄;Su婥9Fu婥9Fu婥9FtH;賢H媅胴I�A艷 �  L嬻H嬰H竑ffffffI9E�
  L塪$ H荄$(    �(   �    H孁H塂$(@H茾     I婱驛] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繧媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺匃   �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛I嬐�    I婨0L婰$`I#罤繧婾H婰�I�$H;藆H塡$ 隥H�翫婫�     H嬞D;Au婣9Gu婣9Gu婣9GtH;蕋H婭朐H�H塡$ H嬰L嬻�H塋$ H荄$(    H嬰L媡$ �L婰$`H婼I�EL�7H塛H�:H墈I婱I婨0I#罤繪�罬;$uH�<岭L;舥H�<岭H9T�uH墊�I�?A艷I嬊H媆$hH媗$pH媡$xH兡0A_A^A]A\_肏�
    �    �)   �    �   ~    R  �    \  �   �  �    �  �   �  �       �   1  �G            �     �  w        �std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Try_emplace<nvrhi::TextureSubresourceSet const &> 
 >V>   this  AJ        %  AU  %     ��  >   _Keyval_arg  AL       �� � AP          AL �      >xC   _Target  CI      U      " CN      X     q A � � �s  CV      �     � ��  CN     I    `  CV     I    m  >    _Hashval  AQ  �    � �   AQ �       B`   2     � >汣   _Newnode  CM     �       B    �     * p } 0 M        �  2',(%hd	 >C>    _Where  AI  E     V    AI �     .9p 	 
 >�>    _End  AV  M     K  AV �     .� �   >�>    _Bucket_lo  AJ  ^     =  AJ �     (' � >    _Bucket  AP  6       M        �  ^ M        �  ^ M        �  ^ N N N N M        �  ��% M        �  �� M        �  �� M        �  �� M        �  �� N N N N M        �  ��	 M        i  
�� M        �  
�� M        v  
��
 Z   �   N N N N M        �  �� N N M        �  ���!
 Z   6   N M        �  �� N M        �  ��D6Y >    _Newsize  AJ  �       AJ 
    V  I �  >    _Oldsize  AJ  �     
  M        �  �� N N8 M        �  伕,,$%g!$ >C>    _Where  AJ  �    t  AJ I     
 >�>    _End  AI  �      AI �    �  > T   >�>    _Bucket_lo  AK  �    M  AK 1        >    _Bucket  AH  �      M        �  佷 >   _Keyval2  AI  �    > , 
  AI �    �  > T   M        �  佷 M        �  佷 N N N N M        �  k丮
 Z   �    M        �  丮B
 >   _Req_buckets  AJ  �    $  C       �      M        �  6丮 N N N M        �  
倧 N2 M        �  侷$$#$#d$'CJ$"E >    _Bucket_array  AJ  c    9  AJ �       >�>    _Insert_after  AK  M    O  AK �       >    _Bucket  AH  g      N
 Z   �   0           (         0@ h@   �  v  w  �  �  �  �  �  W  �  �  �  �  <  *  B  \  i  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN188  `   V>  Othis  p     O_Keyval_arg      汣  O_Newnode  O   �   �           �  0     �       � �%   � �2   � ��   � ��   � ��   � ��   � ��   � �M  � ��  � �*  � �,  � �B  � �I  � ��  � ��  � ��   �  �F                                �`std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Try_emplace<nvrhi::TextureSubresourceSet const &>'::`1'::dtor$1                         �  O �   �  �F                                �`std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Try_emplace<nvrhi::TextureSubresourceSet const &>'::`1'::dtor$0                         �  O ,   �    0   �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 #  �    '  �   
 3  �    7  �   
 Y  �    ]  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 /  �    3  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 *  �    .  �   
 P  �    T  �   
 6  �    :  �   
 F  �    J  �   
 q  �    u  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 )  �    -  �   
 V  �    Z  �   
 f  �    j  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 ;	  �    ?	  �   
 K	  �    O	  �   
 s	  �    w	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �
  �    �
  �   
 H  �    L  �   
   �      �   
 �
  �    �
  �   
 H崐    �       �    H崐    �       �    H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   �  yG            �         �        �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >  >   _First  AJ        0  AJ b     "  >   _Last  AK          AR       } 
 >汢   _Val  AP        �  >�?    _UFirst  AQ       u                        @  h   �  �        O_First       O_Last      汢  O_Val  O �   X           �   	     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 !  �    %  �   
 �  �    �  �   
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  G G            i       h   R        �nvrhi::hash_combine<unsigned int> 
 >�   seed  AJ          AR       b 
 >�#   v  AK        B  M        �   " M           " M        v   " M        O   "+ M        �   "
 >#    _Val  AJ  M     
  AQ       /  N N N N N                        H  h   �  v  �    O      �  Oseed     �#  Ov  O   �   @           i   �     4       {
 �    }
 �   {
 �   }
 �h   ~
 �,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 h  �    l  �   
 x  �    |  �   
    �      �   
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  �G                               �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >  >   _First  AJ          AJ       
   >   _Last  AK          
 >汢   _Val  AP           >镃   _Backout  CJ            CJ          
   M        �    N M        �   N                        H & h   �  �  �  �  �  �  �  �        O_First       O_Last     汢  O_Val  O  �   H               �     <       � �    � �   � �   � �   � �   � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 <  �    @  �   
   �      �   
 3繦茿   H�堿f堿H嬃茿   �   �   �   Y G                      �        �nvrhi::FramebufferAttachment::FramebufferAttachment 
 >V   this  AJ         ! M        �  	 
 N                        H 
 h   �      V  Othis  O  ,   �    0   �   
 ~   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   d   %   �    ,   j      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   d   %   �    ,   m      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   p      m      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   d   %   �       �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H婭H吷t
�(   �    �          �     \G                      �        �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > 
 >僀   this  AJ          M        �  
	 M        �  
	
 >   _Ptr  AJ         N N                        H�  h   w  �  �      僀  Othis  O �   8              �     ,       � �    � �	   � �   � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 ,  �    0  �   
 @SH冹 H嬞H婭H吷t2H婹 H呉tH茿     H�H嬍�PH婯H吷t�(   H兡 [�    H兡 [聾          �   5  `G            J      D   �        �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > 
 >~C   this  AI  	     @ 6   AJ        	  M        �  0
 M        �  5
 M        �  5

 >   _Ptr  AJ  
       AJ 0       N N N M        �   M        *   M        B  DE

 >R     temp  AK         AK 0      
 
  >"     ref  A  0       N N N                      0H� . h
   w  �  *  B  �  �  �  �  �  �   0   ~C  Othis  9)       /   O   �   8           J   �     ,       L �	   M �   N �0   P �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 1  �    5  �   
 L  �    P  �   
 H�    H�H兞�       d      �       �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       d      �       �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   d      �    0          �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   d      �    0          �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   d      �    0          �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   $        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >A   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   A  O__f  9(       A   O ,   }    0   }   
 g   }    k   }   
 w   }    {   }   
 �   }    �   }   
 �   }    �   }   
 �   }    �   }   
 �   }    �   }   
 �   }    �   }   
 �   }    �   }   
   }      }   
 !  }    %  }   
 1  }    5  }   
 A  }    E  }   
 �  }    �  }   
 H塡$WH冹0H�L嬄H孂I嬋H媂H�H峊$ �P0H嬓H嬒�親媆$@H兡0_�   �     W G            8   
   -   '        �donut::engine::FramebufferFactory::GetFramebuffer 
 >%>   this  AJ          AM       $ 
 >1>   view  AK          AP         0                     @  @   %>  Othis  H   1>  Oview  9"       BA   9+       4>   O �   0           8   8     $       3  �
   4  �-   5  �,   �    0   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �      �   
 4  �    8  �   
 H塡$UVWATAUAVAWH崿$@���H侅�  H�    H3腍墔�   L嬧L孂H兞L嬄H峊$P�    L�0I婩 H吚厒  3褹�   H峀$`�    H荄$l   E3砬D$t   fD塴$xL塵�D塵圚荅�   荅�   fD塵楲塵燚塵℉荅�   荅�   fD塵窵塵繢塵菻荅�   荅�   fD塵豅塵郉塵鐷荅�   荅�   fD塵鳯塵 D塵H荅   荅   fD塵L塵 D塵(H荅,   荅4   fD塵8L塵@D塵HH荅L   荅T   fD塵XW纅E`D塵pH荅t   荅|   fD壄�   L壄�   D壄�   H菂�      菂�      fD壄�   I�PI媤XH;fH�H呟t
H�H嬎�P�W�D$ D$0H塡$ A$D$(H婨`H拎D$ D`L$0LpH�E`H呟t
H�H嬎�P怘兦H;欼婫hH吚t.W�D$ D$0H塂$ A$D$(L$ MhD$0ExI婫pH吚t4W�D$ D$0H塂$ A$D$(L$ 崍   D$0厴   I婳H�L岲$`H峊$@��(  H嬋I嬇H峊$HH;裻H�L�)I婲 I塅 H吷t
H��PI婩 H婰$@H吷tL塴$@H��PI婩 H媿�   H3惕    H嫓$  H伳�  A_A^A]A\_^]�"   �   C   �    e   �    �  �       �   �  W G              0   �  %        �donut::engine::FramebufferFactory::GetFramebuffer 
 >%>   this  AJ        6  AW  6     � >   subresources  AK        3  AT  3     �
 >�>    item  AV  J     �
 >�    desc  D`    >?    <begin>$L0  AM  �    @ AM �    '  >?    <end>$L0  AL  �    < AL �    (  >�?    renderTarget  AI  �    c  AI �    N c  M        9  :
 Z   w   N M        �  	乹' N M        �  	丱$ N- M        >  &W0f M        �  i)$$$$$$$ N N M        2  侌 M        H  侌	 N N M          9伔 M        u  佇 N M        �  
伷 N M        �  伭 N N M        3  仮 M        I  仴	 N N M          +� M        �  
�# N M        �  � N N M          1侹 M        �  
俍 N M        �  俇 N N M        *  偫 M        B  偫HB

 >R     temp  AJ  �      AJ �      B�  �    p  D@    >"     ref  A  �    *  N N M        )  *倴 M        *  偙 M        B  偙

 >R     temp  AJ  �      AJ �      >"     ref  A  �    C    N N M        @  偐 N M        A  倴C
 M        @  偅 N N N �          8         A v h   �  �  �  �  K  u  �        $  &  (  )  *  1  2  3  5  6  9  >  ?  @  A  B  H  I  
 :�  O     %>  Othis      Osubresources  `   �  Odesc  9�      /   9�      /   9�      .#   9�      /   9�      /   O   �   �             8  
   t         �6     �J      �W   "  ��  #  ��  $  ��  #  �  &  �  '  �?  )  �K  *  �|  ,  ��  0  �,   �    0   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    "  �   
 .  �    2  �   
 Q  �    U  �   
 a  �    e  �   
 �  �    �  �   
 �  �    �  �   
 ]  �    a  �   
 m  �    q  �   
 }  �    �  �   
 �  �    �  �   
   �      �   
    �    $  �   
 >  �    B  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 H塡$ L塂$AVH冹 L嬹I嬝H婭H嬃M�I+罤柳H;�冣   H�������H塼$0H墊$8H;�囜   H�4�    H侢   r)H峃'H;�喢   �    H吚劵   H峹'H冪郒塆H咑t
H嬑�    H孁�3�I�I婩H+罤柳H吚t.H��    H侜   rL婣鳫兟'I+菻岮鳫凐w`I嬋�    H�>I�>I塅I塅H;鴗H�H兦H;鴘鬑媡$0H媩$8H媆$HH兡 A^肏嬔L岲$@I嬌�    H媆$HH兡 A^描    惕    蘬   ~    �   ~    �         �    /  �    5  �       �   �  � G            :     :  X        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >::_Assign_grow 
 >�?   this  AJ          AV       '�    >   _Cells  AK        3p  �  � w   AK �     w  & 
 >鉈   _Val  AI       $�    AP          D@    >    _Oldsize  AH  '     �  �  >    _Newend  AH  �     2  >    _Oldcapacity  AH  �     ,    AH �     	  >    _Newvec  AM  �       AM �     � \  k .  M        d   N M        Y  �� N M        e  
0W��% M        �  U)
)%
��' M        �  ^$	%)
��
 Z   k   >    _Block_size  AJ  b       AJ .      >    _Ptr_container  AH  p       AH �     �  � 
 >0    _Ptr  AM  �       AM �     � \  k .  M        v  k
 Z   �   N N M        v  ��
 Z   �   N N M        �  

0
	 N N M          ��#" >镃   _Backout  CM     �       CM    �         M        �  �� N M        �  �� N N M        Z  .���� M        �  ��)]
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     &  AK 4     # M        w  
��#
`
 Z   S   >    _Ptr_container  AP  �       AP �     b  X  >    _Back_shift  AJ  �     9  AJ �     b ; "  N N N
 Z   �                         @ Z h   v  w  �  �  �  �  W  Y  Z  d  e  y    �  �  �  �  �  �  �  �         $LN82  0   �?  Othis  8     O_Cells  @   鉈  O_Val  O �   �           :  0     �        �    �'   ! �0   " ��   $ ��   % ��   ' ��   + ��   , ��   - ��   . �  2 �  0 �"  2 �.  " �4  ' �,   �    0   �   
   �    !  �   
 -  �    1  �   
 V  �    Z  �   
 v  �    z  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 )  �    -  �   
 =  �    A  �   
 _  �    c  �   
 o  �    s  �   
 H  �    L  �   
 X  �    \  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 @  �    D  �   
 a  �    e  �   
 q  �    u  �   
 �  �    �  �   
 �  �    �  �   
   �    	  �   
   �      �   
 �  �    �  �   
   �      �   
 H塡$H塴$H塼$WATAUAVAWH冹 L嬮H�������H饺�   嬅H余H;�噠  H岯�H肏饺�罤鱼I媢H�L嬈I峂�    H岰�I塃0I塢8I媇H�H孄H;�匃   @ f�     H�?H峉I嬐�    I#E0H拎M媇L豂�+H;顄I�I塠楝   I婥D媨D;xuG婬9Ku?婬9Ku7婬9Ku/L� L;胻!H婼H�:H婳L�I婡H�I塇H塛H塁I塠隬L嬓H;鑤+怘婡L嬓D;xu婸9Su婸9Su婸9StRH;鑥諬婼H�:H婳L�I婤H�I塉H塛H塁I�H嬤H;�����H媆$PH媗$XH媡$`H兡 A_A^A]A\_肔� H婼H�:H婳L�I婡H�I塇H塛H塁氡H�
    �    蘞   �    �   �    �  �   �  �       �   �	  �G            �     �  �        �std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Forced_rehash 
 >V>   this  AJ          AU       �t  >#   	 _Buckets  AI  P     $  AK        �P m C       2       C      �    
  >   _Max_storage_buckets  AH  7     �
 y C       4      
 >鉈    _End  AL  T     i5  >鉈    _Inserted  AI  w     F  >鉈    _Next_inserted  AM  z     C  >谺    _Insert_before  AH  �     � <  � ;  AH �      r � W  >欱    _Bucket_lo  AS  �     �   AS �      �  >    _Bucket  AH  �       M        �  " N M        �  p M        �  p M        a  t N N N M        �  P M        ]  P M        a  P N N N M        �  
@ M        �  D  >#    _Value  AH  D        N N M        �  ��
 Z   �   N M        �  �� M        �  �� N N M        �  �� N M        �  "�� M        �  "�� M        �  "�� N N N M        �  �� N& M        �  ��$#$#$c$ >�>    _Before_prev  AH        AH �      r � W  >�>    _Last_prev  AJ  �       AJ �     
 w � W  >�>    _First_prev  AK  �       AK �      z � W  N M        �  � N& M        �  丣$#$#$c$ >�>    _Before_prev  AH  \      AH �      �  >�>    _Last_prev  AJ  U      AJ �     
 �  >�>    _First_prev  AK  N       AK �      �  N M        �  �' M        �  �' M        �  �' N N N M        �  �  N& M        �  仛$#$#$c$ >�>   _First  AP  �    #  AP �      �  >�>    _Before_prev  AH  �      AH �      �  >�>    _Last_prev  AJ  �      AJ �     
 �  >�>    _First_prev  AK  �      AK �      �  N Z   X  6               (          @ � h,   w  {  �  �  �  �  *  B  \  ]  `  a  {  |  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN177  P   V>  Othis  X   #   O_Buckets  O�   (          �  0  "         � �   � �-   � �7   � �@   � �P   � �T   � �d   � �l   � �p   � �w   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �  � �  � �   � �E  � �J  � �k  � �n  � �z   ��  � ��  � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 $  �    (  �   
 S  �    W  �   
 k  �    o  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 $  �    (  �   
 Q  �    U  �   
 e  �    i  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    	  �   
 3  �    7  �   
 C  �    G  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 +  �    /  �   
 ;  �    ?  �   
 �  �    �  �   
   �      �   
 /  �    3  �   
 ?  �    C  �   
 h  �    l  �   
 x  �    |  �   
 �  �    �  �   
 �  �    �  �   
 �	  �    �	  �   
 �	  �    �	  �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       s      �       �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H婹H�    H呉HE旅   g      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H           �       �       �     20    2           �       �       �    
 
4 
2p    B           �       �       �     20    <           �       �       �    
 
4 
2p    B           �       �       �     20    <           �       �       �    
 
4 
2p    B           �       �       �     �                  �       �       �     d T 4
 2����p    �          �       �       �     4	 2�    :           �       �       �    !
 
t d     :          �       �       �    :             �       �          !       :          �       �       �      .          �       �          !   t  d     :          �       �       �    .  :          �       �       
   0 4B 8 ���
�p`P          �     �                         �       �          (                           �    
� �� �P 
 
4 
Rp    8           �       �       "    d T 4
 R����p           �        .       �          �       �       (   (           1      4   
    @:    @h   �       �    	�A        >           �       �       7   ! t      >          �       �       7   >   b           �       �       =   !       >          �       �       7   b   �           �       �       C   
 
t 4     D          �       �       I    20           �       U       J           �       �       O   h           X      [          �    R B      :           �       �       ^                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       v                                 |      �      �                   .?AVbad_array_new_length@std@@     �               ����                      y      �                    .?AVbad_alloc@std@@     �              ����                            �                    .?AVexception@std@@     �               ����                      �      �        ����    ����        ��������unordered_map/set too long invalid hash bucket count                                       �      �      �                         �                   �               ����    @                   �      �                                               �      �                         �                           �      �              ����    @                         �                                         y      �      �                         �                                   �      �      �              ����    @                   y      �      _   �   (   & 
34        std::exception::`vftable'    d      d  
    �   (   & 
34        std::bad_alloc::`vftable'    j      j  
    �   3   1 
34        std::bad_array_new_length::`vftable'     m      m  
 �+鷯8}`#悽3慥Ю8cI橗cSk俙:_�;?葌�簕Y阏Ｅh�K蜌�(N滍y臖Wg櫩4b�鱣.颦袉^囄嶸洄慬媥]2�I俑秫uj41j旁I)#hv瓯訧)#hv瓯屢b綩藋T綅5.Y椁y櫹[!8熬.y霅��(！
Z暒D糳灳.S0*>y�戛渥荋嚤踖p禭c�&敡?諭嚤踖p禭9"虰錱6萪O��!\6悑⒂K霵婬(~ǐ'\笈�*�4/. � 濩迄!硄蔯孶孤5儘犷A棊膬�屓绀?貚犷A棊膬獃0_弸c$箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>~;A嬰t舘� �蹰k#@(貝邒_o湅撀O黔穷匕褲h頼x�b僝�.跜溊欤狔o鎧顕�&墅&眒=�#掘P3�)�%I栶賑?T�%蓶茜-gnN鵘J怏熮_d<3�F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪宥d響��_M=硉礀yX]绪/ｎ	蜍R僁舴�:kn嶜�:5齵h周洊{= 栰宧霅;�d_f湲z竘鵿硟ｃ螮線dd�a�:_棢杻#Q鋤~U欒暋m-u"覽目湸炓�	6KR缈猽2忳Xdd�a�:r�7)}玖烿皖~X譾N<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦�Tq���-
�钌S�8萀D脸v傘]-屾咞taR�,F_棢杻#Q吀qv蕞	�-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H阱叿} 5]叨蝝�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 笖E@wX+]!阑,�粰趭+�揃T爃.L�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       p�               .debug$T       p                 .rdata         <       擜犞                         )   0       .text$mn       D      �     .debug$S       �             .text$mn       :      眡�     .debug$S                    .text$mn    	   7       Zo萅     .debug$S    
   t         	    .text$mn       D       磚
:     .debug$S       �             .text$mn    
   �     籮�     .debug$S       �  \       
    .text$x              S�
    .text$x              S�
    .text$mn       �       `螏�     .debug$S       �             .text$mn       i       L}&2     .debug$S       @             .text$mn               _葓�     .debug$S       T             .text$mn              埶*     .debug$S       �              .text$mn       <      .ズ     .debug$S       0  
           .text$mn       <      .ズ     .debug$S       L  
           .text$mn       !      :著�     .debug$S       <             .text$mn       2      X于     .debug$S        <             .text$mn    !         
怷2     .debug$S    "   d         !    .text$mn    #   J      ?�#     .debug$S    $   �         #    .text$mn    %         ��#     .debug$S    &   �          %    .text$mn    '         ��#     .debug$S    (   �          '    .text$mn    )   B      贘S     .debug$S    *             )    .text$mn    +   B      贘S     .debug$S    ,            +    .text$mn    -   B      贘S     .debug$S    .   �          -    .text$mn    /   H       襶.      .debug$S    0   �         /    .text$mn    1   8       戳	     .debug$S    2   d         1    .text$mn    3        @s�     .debug$S    4   t  2       3    .text$mn    5   :     愽鉻     .debug$S    6   �  <       5    .text$mn    7   �     &A     .debug$S    8     P       7    .text$mn    9          aJ鄔     .debug$S    :   �          9    .text$mn    ;         崪覩     .debug$S    <   �          ;        \       /        x                �                �                �                �                �                �                              7      '        Q      ;        q      -        �          i�                    �              �      )        �          i�                                  3      %        X              �      +        �          i�                    �      9                       (              P      7        �      5        W      3        �      1               
        a	              �
              U              1
      !        �
      #        �              �                    	        1              �              �               �               �                          ceilf            memset           $LN13       /    $LN5            $LN10       -    $LN7            $LN13       )    $LN10           $LN16       +    $LN3        9    $LN4        9    $LN177  �  7    $LN181      7    $LN82   :  5    $LN85       5    $LN136      3    $LN4        1    $LN188  �  
    $LN196      
    $LN20           $LN97           $LN43       #    $LN14   :       $LN17           .xdata      =          F┑@/        $      =    .pdata      >         X賦�/        H      >    .xdata      ?          （亵        k      ?    .pdata      @          T枨        �      @    .xdata      A          %蚘%-        �      A    .pdata      B         惻竗-        �      B    .xdata      C          （亵        	      C    .pdata      D         2Fb�        2      D    .xdata      E          %蚘%)        Z      E    .pdata      F         惻竗)        �      F    .xdata      G          （亵        �      G    .pdata      H         2Fb�        �      H    .xdata      I          %蚘%+              I    .pdata      J         惻竗+        @      J    .xdata      K          懐j�9        q      K    .pdata      L         Vbv�9        �      L    .xdata      M          m蹏7        �      M    .pdata      N         KPM7        U      N    .xdata      O          ii@5        �      O    .pdata      P         礝
5        k      P    .xdata      Q         塯4�5        �      Q    .pdata      R         囥鱢5        �      R    .xdata      S         Y�5        "       S    .pdata      T         s�&k5        �!      T    .xdata      U         n奧w5        H#      U    .pdata      V         '擊�5        �$      V    .xdata      W   (      騠�3        n&      W    .pdata      X         ~g�3        �&      X    .xdata      Y   	      � )93        U'      Y    .xdata      Z         j3        �'      Z    .xdata      [   
       k3        G(      [    .xdata      \          ug刉1        �(      \    .pdata      ]         菻(V1        ")      ]    .xdata      ^   $      �$s�
        �)      ^    .pdata      _         5Mv�
        �+      _    .xdata      `   	      � )9
        .      `    .xdata      a         �T
        b0      a    .xdata      b   
       ~$
        �2      b    .xdata      c          確        �4      c    .pdata      d         OAG�        y6      d    .xdata      e         +縬[        �7      e    .pdata      f         蹷謔        o9      f    .xdata      g         ＋)        �:      g    .pdata      h         穣        g<      h    .xdata      i          Uqi�        �=      i    .pdata      j         Ｇ�        �>      j    .xdata      k         蚲7M#        �?      k    .pdata      l         %轢�#        ^@      l    .xdata      m   	      �#荤#        A      m    .xdata      n         j#        茿      n    .xdata      o          攰e#        傿      o    .xdata      p          �9�        9C      p    .pdata      q         礝
        朇      q    .rdata      r                      駽     r    .rdata      s          �;�         	D      s    .rdata      t                      0D     t    .rdata      u                      GD     u    .rdata      v          �)         iD      v    .xdata$x    w                      旸      w    .xdata$x    x         虼�)         稤      x    .data$r     y   /      嶼�         贒      y    .xdata$x    z   $      4��         �D      z    .data$r     {   $      鎊=         TE      {    .xdata$x    |   $      銸E�         nE      |    .data$r     }   $      騏糡         璄      }    .xdata$x    ~   $      4��         荅      ~        F           .data                  烀�          F              MF         .rdata      �          ��         tF      �    .rdata      �          藾味               �    .rdata$r    �   $      'e%�         譌      �    .rdata$r    �         �          颋      �    .rdata$r    �                      G      �    .rdata$r    �   $      Gv�:         G      �    .rdata$r    �   $      'e%�         :G      �    .rdata$r    �         }%B         RG      �    .rdata$r    �                      hG      �    .rdata$r    �   $      `         ~G      �    .rdata$r    �   $      'e%�         滸      �    .rdata$r    �         �弾         繥      �    .rdata$r    �                      酖      �    .rdata$r    �   $      H衡�         H      �    .rdata      �          eL喳         ,H      �        <H           _fltused         .debug$S    �   4          r    .debug$S    �   4          t    .debug$S    �   @          u    .chks64     �   �                NH  ?c_IdentityTransform@rt@nvrhi@@3QBMB ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ??0FramebufferAttachment@nvrhi@@QEAA@XZ ??$hash_combine@I@nvrhi@@YAXAEA_KAEBI@Z ?_Forced_rehash@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAAX_K@Z ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z ?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z ?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z ??$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??$?RUTextureSubresourceSet@nvrhi@@@?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@QEBA_KAEBUTextureSubresourceSet@nvrhi@@@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Hash_representation@I@std@@YA_KAEBI@Z ??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z ?dtor$0@?0???$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z@4HA ?dtor$1@?0???$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAAX_K@Z $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $chain$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $pdata$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $chain$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $pdata$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $chain$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $pdata$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@2@@Z $unwind$?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z $pdata$?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z $cppxdata$?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z $stateUnwindMap$?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z $ip2state$?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z $unwind$?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z $pdata$?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z $unwind$??$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z $pdata$??$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z $cppxdata$??$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z $stateUnwindMap$??$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z $ip2state$??$_Try_emplace@AEBUTextureSubresourceSet@nvrhi@@$$V@?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@_N@1@AEBUTextureSubresourceSet@nvrhi@@@Z $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??$?RUTextureSubresourceSet@nvrhi@@@?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@QEBA_KAEBUTextureSubresourceSet@nvrhi@@@Z $pdata$??$?RUTextureSubresourceSet@nvrhi@@@?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@QEBA_KAEBUTextureSubresourceSet@nvrhi@@@Z $unwind$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $cppxdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $ip2state$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@5f000000 __security_cookie 