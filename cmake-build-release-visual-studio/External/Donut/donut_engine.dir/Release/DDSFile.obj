d咥翀�' �      .drectve        <  <2               
 .debug$S        � x3  慃     
   @ B.debug$T        p   酐             @ B.rdata              d�             @@@.rdata              匋             @@@.rdata                           @@@.rdata              狞             @@@.rdata              潼             @@@.rdata              �             @@@.rdata              $�             @@@.rdata              D�             @@@.rdata              d�             @@@.rdata              匌             @@@.rdata                           @@@.rdata              凝             @@@.rdata              潺             @@@.rdata              �             @@@.rdata              $�             @@@.rdata              D�             @@@.rdata              d�             @@@.rdata              匎             @@@.rdata                           @@@.rdata              宁             @@@.rdata              濑             @@@.rdata              �             @@@.rdata              $�             @@@.rdata              D�             @@@.rdata              d�             @@@.rdata              �             @@@.rdata              �             @@@.rdata              �             @@@.rdata              �             @@@.rdata                            @@@.rdata          l  $              @ P@.text$mn        :   � �         P`.debug$S          � �        @B.text$mn        0   � �         P`.debug$S        �  � �        @B.text$mn        �   "	 �	         P`.debug$S        (  �	 �
     "   @B.text$mn        y  5 �         P`.debug$S        �  � �     X   @B.text$x         (   �           P`.text$mn        6  ,  b"     	    P`.debug$S          �" �2     `   @B.text$x         =   �6 �6         P`.text$mn        <   �6 7         P`.debug$S        0  97 i8     
   @B.text$mn        <   �8 	9         P`.debug$S        L  '9 s:     
   @B.text$mn        !   �: �:         P`.debug$S        <  ; H<        @B.text$mn        2   �< �<         P`.debug$S        <  �< >        @B.text$mn        "   ~>              P`.debug$S        �  �> ,@        @B.text$mn           藹 譆         P`.debug$S          酅 馎        @B.text$mn        K   -B              P`.debug$S        �  xB 8D        @B.text$mn        �   腄 ME         P`.debug$S        d  kE 螴        @B.text$mn        �   獼 zK         P`.debug$S        X   鶳     2   @B.text$mn        j   頡 XS         P`.debug$S        �  lS 鳷        @B.text$mn        `   pU 蠻         P`.debug$S        �  銾 ╔        @B.text$mn           \Y              P`.debug$S        �   _Y ?Z        @B.text$mn           {Z 嶼         P`.debug$S        �    哰        @B.text$mn           甗 羀         P`.debug$S        �   誟 礬        @B.text$mn        +   馶 ]         P`.debug$S        �   0] ^        @B.text$mn        B   T^ 朸         P`.debug$S           碸 確        @B.text$mn        B   餩 2`         P`.debug$S          P` `a        @B.text$mn        B   渁 轪         P`.debug$S        �   黙 鴅        @B.text$mn        H   4c              P`.debug$S        �  |c @e        @B.text$mn        	  Xf aj         P`.debug$S          賘 鮫         @B.text$mn        �  5q 襱     
    P`.debug$S        �  6u 鎴     �   @B.text$x            :� F�         P`.text$x            P� \�         P`.text$x            f� r�         P`.text$mn        �  |� i�         P`.debug$S        �  箳 櫈     d   @B.text$x            仮 崲         P`.text$x            棦 ＂         P`.text$mn        0   荪         P`.debug$S        d  功 �     l   @B.text$mn        X  U�          P`.debug$S        $  9� ]�     2   @B.text$mn        ,  Q� }�         P`.debug$S           w� 椬     �   @B.text$x            椳 ＼         P`.text$x             管         P`.text$x            密 嘬         P`.text$mn           贶              P`.debug$S            �        @B.text$mn        
   P�              P`.debug$S        @  ]� 澾     
   @B.text$mn           �              P`.debug$S        ,  � 0�        @B.text$mn            �� 犪         P`.debug$S        �   踞 傗        @B.text$mn           锯 镶         P`.debug$S        �   汊 椼        @B.text$mn           鱼 溷         P`.debug$S           �        @B.text$mn           L� ]�         P`.debug$S        �  q� �        @B.text$mn        `  M�          P`.debug$S        �   ●     B   @B.text$mn        A   5� v�         P`.debug$S        �  婔 F�        @B.text$mn        B   J� 岠         P`.debug$S        �  狓 滬        @B.text$mn           狘 滁         P`.debug$S        �   近 扆        @B.xdata             妄             @0@.pdata             猃 睚        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             =�             @0@.pdata             I� U�        @0@.xdata             s�             @0@.pdata             {� 圑        @0@.xdata                          @0@.pdata             炳 浸        @0@.xdata             埝             @0@.pdata             泾 稔        @0@.xdata             
�             @0@.pdata             � %�        @0@.xdata             C�             @0@.pdata             K� W�        @0@.xdata             u�             @0@.pdata             }� �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata               #         @0@.xdata             A  Q         @0@.pdata             o  {         @0@.xdata             �  �         @0@.pdata             �  �         @0@.xdata             �              @0@.pdata             �  	        @0@.xdata             '             @0@.pdata             ? K        @0@.xdata          $   i �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             �          @@.xdata             (             @@.voltbl            4                .xdata          ,   F r        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata                          @0@.pdata              $        @0@.voltbl            B                .xdata             F V        @0@.pdata             j v        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             / 5        @@.xdata             ?             @@.xdata             C             @0@.pdata             K W        @0@.xdata             u �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             �         @0@.voltbl            )               .xdata             +             @0@.pdata             3 ?        @0@.xdata             ]             @0@.pdata             e q        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �         @0@.pdata             ) 5        @0@.xdata             S c        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �         @0@.pdata             ! -        @0@.xdata             K             @0@.pdata             S _        @0@.xdata             }             @0@.pdata             � �        @0@.xdata          $   � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             . @        @@.xdata             ^             @@.xdata             o             @0@.pdata             { �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             	 	        @0@.pdata             /	 ;	        @0@.xdata             Y	 m	        @0@.pdata             �	 �	        @0@.xdata             �	             @0@.pdata             �	 �	        @0@.xdata             �	 �	        @0@.pdata             
 
        @0@.xdata          
   =
 J
        @@.xdata             h
             @@.xdata             k
 s
        @@.xdata             }
 �
        @@.xdata          	   �
             @@.xdata             �
             @0@.pdata             �
 �
        @0@.voltbl            �
               .xdata             �
 �
        @0@.pdata             �
         @0@.xdata          
   $ 1        @@.xdata             O             @@.xdata             R Z        @@.xdata             d k        @@.xdata          	   u             @@.xdata             ~             @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.rdata              5        @@@.rdata             S             @@@.rdata             e }        @@@.rdata             � �        @@@.rdata             �             @@@.xdata$x           � 
        @@@.xdata$x           
 2
        @@@.data$r         /   P
 
        @@�.xdata$x        $   �
 �
        @@@.data$r         $   �
 �
        @@�.xdata$x        $   �
         @@@.data$r         $   ' K        @@�.xdata$x        $   U y        @@@.rdata             �             @@@.data               �             @ @�.rdata             �             @@@.rdata          (   � �        @@@.rdata$r        $   ' K        @@@.rdata$r           i }        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $   � �        @@@.rdata$r            +        @@@.rdata$r           5 I        @@@.rdata$r        $   ] �        @@@.rdata$r        $   � �        @@@.rdata$r           � �        @@@.rdata$r           �         @@@.rdata$r        $   / S        @@@.data$rs        *   g �        @@�.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $    +        @@@.data$rs        >   I �        @@�.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata                          @P@.debug$S        <    [        @B.debug$S        <   o �        @B.debug$S        <   � �        @B.debug$S        <    K        @B.debug$S        <   _ �        @B.debug$S        @   � �        @B.debug$S        @    C        @B.debug$S        @   W �        @B.debug$S        @   � �        @B.debug$S        @   � ?        @B.debug$S        @   S �        @B.debug$S        <   � �        @B.debug$S        @   � 7        @B.debug$S        @   K �        @B.debug$S        @   � �        @B.debug$S        @   � 3        @B.debug$S        <   G �        @B.debug$S        <   � �        @B.debug$S        @   � '        @B.debug$S        @   ; {        @B.debug$S        <   � �        @B.debug$S        8   �         @B.debug$S        8   + c        @B.debug$S        <   w �        @B.debug$S        @   �         @B.debug$S        8    S        @B.debug$S        <   g �        @B.debug$S        @   � �        @B.debug$S        <    G        @B.debug$S        <   [ �        @B.debug$S        4   � �        @B.debug$S        4   � '        @B.debug$S        @   ; {        @B.debug$S        L   � �        @B.chks64         
  �              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   I  e     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\DDSFile.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $tf  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $dds  $vfs 	 $status  $math 	 $colors  $log  $Json 	 $stdext    �   �    �        nvrhi::EntireBuffer �    std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >::_Minimum_asan_allocation_alignment ,�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>::_Multi /�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>::_Standard L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos �    std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >::_Minimum_asan_allocation_alignment  僒   std::_Consume_header  僒   std::_Generate_header j    std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment ?   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Bucket_size ?   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Min_buckets 9�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Multi R    std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > >::_Minimum_asan_allocation_alignment  -v    DXGI_FORMAT_UNKNOWN ' -v   DXGI_FORMAT_R32G32B32A32_FLOAT & -v   DXGI_FORMAT_R32G32B32A32_UINT & -v   DXGI_FORMAT_R32G32B32A32_SINT $ -v   DXGI_FORMAT_R32G32B32_FLOAT # -v   DXGI_FORMAT_R32G32B32_UINT # -v   DXGI_FORMAT_R32G32B32_SINT ' -v  
 DXGI_FORMAT_R16G16B16A16_FLOAT ' -v   DXGI_FORMAT_R16G16B16A16_UNORM & -v   DXGI_FORMAT_R16G16B16A16_UINT ' -v  
 DXGI_FORMAT_R16G16B16A16_SNORM & -v   DXGI_FORMAT_R16G16B16A16_SINT ! -v   DXGI_FORMAT_R32G32_FLOAT   -v   DXGI_FORMAT_R32G32_UINT   -v   DXGI_FORMAT_R32G32_SINT - -v   DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS , -v   DXGI_FORMAT_X32_TYPELESS_G8X24_UINT & -v   DXGI_FORMAT_R10G10B10A2_UNORM $ -v   DXGI_FORMAT_R11G11B10_FLOAT # -v   DXGI_FORMAT_R8G8B8A8_UNORM ( -v   DXGI_FORMAT_R8G8B8A8_UNORM_SRGB " -v   DXGI_FORMAT_R8G8B8A8_UINT # -v   DXGI_FORMAT_R8G8B8A8_SNORM " -v    DXGI_FORMAT_R8G8B8A8_SINT ! -v  " DXGI_FORMAT_R16G16_FLOAT ! -v  # DXGI_FORMAT_R16G16_UNORM   -v  $ DXGI_FORMAT_R16G16_UINT ! -v  % DXGI_FORMAT_R16G16_SNORM   -v  & DXGI_FORMAT_R16G16_SINT  -v  ) DXGI_FORMAT_R32_FLOAT  -v  * DXGI_FORMAT_R32_UINT  -v  + DXGI_FORMAT_R32_SINT * -v  . DXGI_FORMAT_R24_UNORM_X8_TYPELESS ) -v  / DXGI_FORMAT_X24_TYPELESS_G8_UINT  -v  1 DXGI_FORMAT_R8G8_UNORM  -v  2 DXGI_FORMAT_R8G8_UINT  -v  3 DXGI_FORMAT_R8G8_SNORM  -v  4 DXGI_FORMAT_R8G8_SINT  -v  6 DXGI_FORMAT_R16_FLOAT  -v  8 DXGI_FORMAT_R16_UNORM  -v  9 DXGI_FORMAT_R16_UINT  -v  : DXGI_FORMAT_R16_SNORM  -v  ; DXGI_FORMAT_R16_SINT  -v  = DXGI_FORMAT_R8_UNORM  -v  > DXGI_FORMAT_R8_UINT  -v  ? DXGI_FORMAT_R8_SNORM  -v  @ DXGI_FORMAT_R8_SINT  -v  G DXGI_FORMAT_BC1_UNORM # -v  H DXGI_FORMAT_BC1_UNORM_SRGB  -v  J DXGI_FORMAT_BC2_UNORM # -v  K DXGI_FORMAT_BC2_UNORM_SRGB  -v  M DXGI_FORMAT_BC3_UNORM # -v  N DXGI_FORMAT_BC3_UNORM_SRGB  -v  P DXGI_FORMAT_BC4_UNORM  -v  Q DXGI_FORMAT_BC4_SNORM  -v  S DXGI_FORMAT_BC5_UNORM  -v  T DXGI_FORMAT_BC5_SNORM ! -v  U DXGI_FORMAT_B5G6R5_UNORM # -v  V DXGI_FORMAT_B5G5R5A1_UNORM # -v  W DXGI_FORMAT_B8G8R8A8_UNORM ( -v  [ DXGI_FORMAT_B8G8R8A8_UNORM_SRGB  -v  _ DXGI_FORMAT_BC6H_UF16  -v  ` DXGI_FORMAT_BC6H_SF16  -v  b DXGI_FORMAT_BC7_UNORM # -v  c DXGI_FORMAT_BC7_UNORM_SRGB # -v  s DXGI_FORMAT_B4G4R4A4_UNORM * �  �DDS donut::engine::dds::DDS_MAGIC  �   '   %    std::ctype<char>::table_size  �   籖  4 寋   donut::engine::dds::DDS_DIMENSION_TEXTURE1D 4 寋   donut::engine::dds::DDS_DIMENSION_TEXTURE2D 4 寋   donut::engine::dds::DDS_DIMENSION_TEXTURE3D < 妠   donut::engine::dds::DDS_MISC_FLAGS2_ALPHA_MODE_MASK _    std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment i    std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<donut::engine::TextureSubresourceData,donut::engine::TextureSubresourceData,donut::engine::TextureSubresourceData &&,donut::engine::TextureSubresourceData &>::_Same_size_and_compatible � �   std::_Trivial_cat<donut::engine::TextureSubresourceData,donut::engine::TextureSubresourceData,donut::engine::TextureSubresourceData &&,donut::engine::TextureSubresourceData &>::_Bitcopy_constructible � �   std::_Trivial_cat<donut::engine::TextureSubresourceData,donut::engine::TextureSubresourceData,donut::engine::TextureSubresourceData &&,donut::engine::TextureSubresourceData &>::_Bitcopy_assignable �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard i    std::allocator<std::shared_ptr<donut::engine::TextureData> >::_Minimum_asan_allocation_alignment ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size - d    std::integral_constant<int,0>::value ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size �    std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment O�   std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Same_size_and_compatible L�    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_constructible I�    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_assignable j    std::allocator<std::shared_ptr<donut::engine::TextureData> *>::_Minimum_asan_allocation_alignment T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos � d   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Minimum_map_size ��   std::_Trivial_cat<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > &&,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > &>::_Same_size_and_compatible    ��I@donut::math::PI_f "   
�-DT�!	@donut::math::PI_d ��    std::_Trivial_cat<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > &&,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > &>::_Bitcopy_constructible !   ��7�5donut::math::epsilon "   �  �donut::math::infinity    �  �donut::math::NaN ��    std::_Trivial_cat<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > &&,std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > &>::_Bitcopy_assignable   d   std::_Iosb<int>::skipws ! d   std::_Iosb<int>::unitbuf # d   std::_Iosb<int>::uppercase " d   std::_Iosb<int>::showbase # d   std::_Iosb<int>::showpoint ! d    std::_Iosb<int>::showpos  d  @ std::_Iosb<int>::left  d  � std::_Iosb<int>::right " d   std::_Iosb<int>::internal  d   std::_Iosb<int>::dec  d   std::_Iosb<int>::oct  d   std::_Iosb<int>::hex $ d   std::_Iosb<int>::scientific  d    std::_Iosb<int>::fixed " d   0std::_Iosb<int>::hexfloat # d   @std::_Iosb<int>::boolalpha " d  � �std::_Iosb<int>::_Stdio % d  �std::_Iosb<int>::adjustfield # d   std::_Iosb<int>::basefield $ d   0std::_Iosb<int>::floatfield ! d    std::_Iosb<int>::goodbit i    std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >::_Bytes   d   std::_Iosb<int>::eofbit n d   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >::_Block_size ! d   std::_Iosb<int>::failbit   d   std::_Iosb<int>::badbit  d   std::_Iosb<int>::in  d   std::_Iosb<int>::out  d   std::_Iosb<int>::ate  d   std::_Iosb<int>::app  d   std::_Iosb<int>::trunc # d  @ std::_Iosb<int>::_Nocreate � d   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Block_size $ d  � std::_Iosb<int>::_Noreplace   d    std::_Iosb<int>::binary  d    std::_Iosb<int>::beg  d   std::_Iosb<int>::cur  d   std::_Iosb<int>::end � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible , d  @ std::_Iosb<int>::_Default_open_prot � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable + <        nvrhi::rt::c_IdentityTransform 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P E  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos ) �   donut::math::vector<bool,2>::DIM ) �   donut::math::vector<bool,3>::DIM ) �   donut::math::vector<bool,4>::DIM - �    std::chrono::system_clock::is_steady A    std::allocator<bool>::_Minimum_asan_allocation_alignment $ E   std::ratio<1,10000000>::num ( E  ��枠 std::ratio<1,10000000>::den  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized  d    donut::vfs::status::OK ( �    std::_Num_base::tinyness_before $ d   ��donut::vfs::status::Failed * d   �onut::vfs::status::PathNotFound  �    std::_Num_base::traps , d   �齞onut::vfs::status::NotImplemented $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix * d   std::numeric_limits<bool>::digits  E   std::ratio<1,1>::num  E   std::ratio<1,1>::den - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 �    std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Minimum_asan_allocation_alignment J E   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 1 �   std::numeric_limits<char16_t>::is_modulo N E  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( E  ��枠 std::ratio<10000000,1>::num $ E   std::ratio<10000000,1>::den 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 P E  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy 0 �   std::numeric_limits<wchar_t>::is_modulo - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 < E  ��枠 std::integral_constant<__int64,10000000>::value . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 1 E   std::integral_constant<__int64,1>::value , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , d  	 std::numeric_limits<long>::digits10 - �   std::chrono::steady_clock::is_steady P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity & E   std::ratio<1,1000000000>::num * E  � 蕷;std::ratio<1,1000000000>::den 0 �   std::numeric_limits<__int64>::is_signed - d  ? std::numeric_limits<__int64>::digits / d   std::numeric_limits<__int64>::digits10 d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 7 �   std::numeric_limits<unsigned short>::is_modulo 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10   E  std::ratio<3600,1>::num 1 d  	 std::numeric_limits<float>::max_digits10   E   std::ratio<3600,1>::den 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10  d    LightType_None  d   LightType_Directional  d   LightType_Spot  d   LightType_Point R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10 " �    std::memory_order_relaxed " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst  �   �  7 �   std::atomic<unsigned int>::is_always_lock_free �    std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IFileSystem> > >::_Minimum_asan_allocation_alignment  E  < std::ratio<60,1>::num  E   std::ratio<60,1>::den 4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment  �   o  �4   _Mtx_try  �4   _Mtx_recursive  C5   std::_INVALID_ARGUMENT  C5   std::_NO_SUCH_PROCESS & C5   std::_OPERATION_NOT_PERMITTED , C5   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - C5   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst   E   std::ratio<1,1000>::num   E  �std::ratio<1,1000>::den C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size , kw        donut::engine::g_FormatMappings '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable # E   std::ratio<1,1000000>::num ' E  �@B std::ratio<1,1000000>::den `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos O    std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment :     std::integral_constant<unsigned __int64,0>::value 3 Q  \ std::filesystem::path::preferred_separator 6 �   std::_Iterator_base0::_Unwrap_when_unverified b    std::allocator<donut::engine::TextureSubresourceData>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits Z    std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified * �   donut::math::vector<float,3>::DIM Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo Z    std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment 2 �  �����std::shared_timed_mutex::_Max_readers * �   donut::math::vector<float,4>::DIM *         donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex c    std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment x    std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment ) �   donut::math::frustum::numCorners :    std::integral_constant<unsigned __int64,2>::value  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment Z    std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment 5 �    std::filesystem::_File_time_clock::is_steady �    std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > >::_Minimum_asan_allocation_alignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified B    std::allocator<float>::_Minimum_asan_allocation_alignment R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos # �        nvrhi::AllSubresources � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos $ d   std::_Locbase<int>::collate " d   std::_Locbase<int>::ctype % d   std::_Locbase<int>::monetary $ d   std::_Locbase<int>::numeric ! d   std::_Locbase<int>::time % d    std::_Locbase<int>::messages   d  ? std::_Locbase<int>::all ! d    std::_Locbase<int>::none L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos . d   donut::math::box<float,3>::numCorners  �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  甋  _Ctypevec & �5  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  �(  _Thrd_result  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e  沄  __std_fs_find_data & i4  $_TypeDescriptor$_extraBytes_23 - �4  $_s__CatchableTypeArray$_extraBytes_32 # 蔥  __std_fs_reparse_data_buffer Z Qi  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ Ni  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` Li  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  烿  __std_fs_dir_handle  軷  __std_access_rights  �5  _TypeDescriptor & �4  $_TypeDescriptor$_extraBytes_34  �5  _Stl_critical_section 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 坽  $_TypeDescriptor$_extraBytes_46  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19  -v  DXGI_FORMAT & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const>  瀁  __std_fs_filetime E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16   餥  __std_fs_copy_file_result  tV  __std_code_page d �=  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > G >  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > a '<  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> d 0}  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> > > ] i;  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ oK  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > z Ci  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> [ >  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � �=  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > �  ;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 
K  std::_Default_allocator_traits<std::allocator<float> > C G:  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 僈  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 
;  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C :  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � [;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | �=  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > M �=  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L >  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s >  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > T {K  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > U qK  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � Q}  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::TextureData> >,std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >,1> 3 旹  std::_Ptr_base<donut::engine::LoadedTexture> _ I}  std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > > : �;  std::_Vector_val<std::_Simple_types<unsigned int> > D >  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � >  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � }  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > 6 濱  std::_Ptr_base<donut::engine::DescriptorHandle> � |;  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> U >K  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > e A}  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> *> > \ C}  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> *> > "T8  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > [ 2}  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> > > U 馢  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > 4 cK  std::allocator<donut::math::vector<float,2> > = *K  std::allocator<donut::math::vector<unsigned short,4> > K TK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U 塈  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 蘆  std::_Ptr_base<donut::engine::BufferGroup> � 鐊  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > F;  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � ;:  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> e I  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > { �=  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > , 3:  std::allocator<nvrhi::BindingSetItem> K JK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > # �:  std::allocator<unsigned int> . 鱢  std::_Ptr_base<donut::vfs::IFileSystem> J I  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � :  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � 鰄  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> D �=  std::_Default_allocator_traits<std::allocator<unsigned int> > L @K  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  uI  std::allocator<float> � 2K  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> K  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � K  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1> 4 K  std::allocator_traits<std::allocator<float> > [ O;  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > ; �=  std::allocator_traits<std::allocator<unsigned int> > [ K  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > C $}  std::allocator<std::shared_ptr<donut::engine::TextureData> > � 09  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > [ 謍  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 焈  std::default_delete<wchar_t [0]> . �'  std::_Conditionally_enabled_hash<int,1> A 癦  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> > �}  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >,1>  .  std::_Lockit  �)  std::timed_mutex * 2/  std::hash<enum nvrhi::ResourceType> - #W  std::reverse_iterator<wchar_t const *> " i3  std::_Char_traits<char,int> � 輝  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >  S  std::_Fs_file  X:  std::_Value_init_tag � 鱆  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �2  std::_Num_base & �'  std::hash<std::error_condition> K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >  y$  std::_Big_uint128  ))  std::condition_variable � }  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > ) v3  std::_Narrow_char_traits<char,int> L 驤  std::allocator_traits<std::allocator<donut::math::vector<float,2> > >    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc> } 剓  std::_Default_allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > " �5  std::_Align_type<double,64>  �'  std::hash<int> � 竫  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *>  �2  std::_Num_int_base  wU  std::ctype<wchar_t> " k(  std::_System_error_category / Q/  std::_Conditionally_enabled_hash<bool,1> 2 錔  std::shared_ptr<donut::engine::BufferGroup> � �8  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > + 弒  std::_Atomic_storage<unsigned int,4> � G|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > >  �2  std::float_denorm_style 4 璄  std::shared_ptr<donut::engine::LoadedTexture> 鈍  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! �)  std::_Ptr_base<std::mutex> u f  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > . 5I  std::_Ptr_base<donut::engine::MeshInfo> 6 �5  std::allocator_traits<std::allocator<wchar_t> >  *  std::shared_timed_mutex  &  std::bad_cast  Fc  std::equal_to<void> 4 際  std::allocator<donut::math::vector<float,4> > � 圶  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > q 笿  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 嘕  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o 鷊  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> " �2  std::numeric_limits<double>  <&  std::__non_rtti_object < �6  std::_Ptr_base<donut::engine::DescriptorTableManager> ( n  std::_Basic_container_proxy_ptr12 4 BJ  std::allocator<donut::math::vector<float,3> > > �:  std::vector<unsigned int,std::allocator<unsigned int> > T �:  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base  �&  std::logic_error 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety P 3J  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f �7  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �5  std::char_traits<char32_t>  T  std::locale  HT  std::locale::_Locimp  $T  std::locale::facet   ,T  std::locale::_Facet_guard  軸  std::locale::id s 塮  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �2  std::numeric_limits<bool> ,�w  std::_Compressed_pair<std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > >,std::_Vector_val<std::_Simple_types<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >,1> # �3  std::_WChar_traits<char16_t> _ /J  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u 蘒  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P 觃  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> Z 磂  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M 
W  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  '  std::overflow_error % --  std::_One_then_variadic_args_t D �/  std::_Constexpr_immortalize_impl<std::_System_error_category> W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * �8  std::_Vb_val<std::allocator<bool> > E 廩  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j Gh  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > ' 漺  std::_Ptr_base<donut::vfs::Blob>   �5  std::char_traits<wchar_t>  �(  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> � 縄  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy "�|  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>  �5  std::false_type 鱸  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy \ 秠  std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ! �(  std::hash<std::thread::id>  X  std::string < 莣  std::allocator<donut::engine::TextureSubresourceData> B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 沇  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  )  std::adopt_lock_t � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 稩  std::shared_ptr<donut::engine::DescriptorHandle> , �2  std::numeric_limits<unsigned __int64>  vS  std::_Locinfo 6 罽  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L 婭  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s [e  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > ] z{  std::_Default_allocator_traits<std::allocator<donut::engine::TextureSubresourceData> > � 鵿  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > > � z  std::_Func_impl_no_alloc<<lambda_be0255e1888a8684391b3d47a840c833>,void,std::basic_string_view<char,std::char_traits<char> > > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> = W7  std::shared_ptr<donut::engine::DescriptorTableManager>  �  std::string_view    std::wstring_view % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound  �(  std::_Mutex_base �
u  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > b }I  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  WV  std::money_base  h  std::money_base::pattern s 蕐  std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  FS  std::_Timevec D gI  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  �5  std::defer_lock_t � 鑭  std::allocator_traits<std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >   a'  std::_Init_once_completer j "[  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � 馴  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  )  std::scoped_lock<> + 諸  std::codecvt<wchar_t,char,_Mbstatet> h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q h  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % �  std::array<nvrhi::Viewport,16> � 鎩  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >  >  std::_Iterator_base12  4a  std::_Pocma_values 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> * [t  std::shared_lock<std::shared_mutex> ! �'  std::hash<std::error_code> N 賅  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �3  std::_Default_allocator_traits<std::allocator<char32_t> > ( x  std::_Atomic_padded<unsigned int> D 軀  std::allocator<std::shared_ptr<donut::engine::TextureData> *>  �,  std::allocator<char32_t> ? 耓  std::unique_ptr<char [0],std::default_delete<char [0]> > $ �  std::_Atomic_integral<long,4>  U/  std::hash<bool>     std::streamsize 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> M   std::_Arg_types<std::basic_string_view<char,std::char_traits<char> > > " �)  std::lock_guard<std::mutex> K XI  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >  (  std::hash<long double> � PX  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � X  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy / NI  std::shared_ptr<donut::engine::MeshInfo>  �5  std::try_to_lock_t U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> " .)  std::_Align_type<double,72> � 
t  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >,std::_Iterator_base0> / �3  std::_Char_traits<char32_t,unsigned int>  �'  std::_System_error ( 1$  std::hash<nvrhi::FramebufferInfo> 9 縚  std::allocator<std::filesystem::_Find_file_handle> T 葇  std::allocator_traits<std::allocator<donut::engine::TextureSubresourceData> >  �'  std::error_condition % �5  std::integral_constant<bool,0> | 苵  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > > �   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Is_bidi �   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Pop_direction  .  std::bad_exception � 舦  std::vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > > 搗  std::vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >::_Reallocation_policy & �,  std::_Zero_then_variadic_args_t  �  std::u32string  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128>  �&  std::invalid_argument N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 1 ╯  std::_Atomic_integral_facade<unsigned int>  ")  std::cv_status S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 !I  std::_Vector_val<std::_Simple_types<float> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A I  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + \  std::pair<enum __std_win_error,bool> p 5w  std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > � w  std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >::_Reallocation_policy  �(  std::thread  �(  std::thread::id S 齎  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �&  std::length_error \ 	I  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F e  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 莀  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �2  std::numeric_limits<float>  cV  std::time_base   ]V  std::time_base::dateorder ) �  std::_Atomic_integral_facade<long>  @)  std::mutex % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  �5  std::ratio<60,1> S 纔  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > > ] Wy  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > >::_Storage � 鱳  std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > >  t  std::exception_ptr  �5  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > � I|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t>  Y'  std::once_flag  �'  std::error_code J �6  std::enable_shared_from_this<donut::engine::DescriptorTableManager>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < h  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  錟  std::_Iosb<int>   酻  std::_Iosb<int>::_Seekdir ! 遀  std::_Iosb<int>::_Openmode   軺  std::_Iosb<int>::_Iostate ! 踀  std::_Iosb<int>::_Fmtflags # 賃  std::_Iosb<int>::_Dummy_enum 7 �5  std::allocator_traits<std::allocator<char32_t> >  {5  std::nano ( Pw  std::_Ptr_base<donut::vfs::IBlob>  �  std::_Iterator_base0 | 鸋  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> $ �#  std::hash<nvrhi::BufferRange> T 蒞  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  銼  std::_Locbase<int> " �)  std::shared_ptr<std::mutex> ! �5  std::char_traits<char16_t> � p{  std::_Default_allocator_traits<std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >  |  std::tuple<>    std::_Container_base12 W 齡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  �)  std::shared_mutex  �'  std::io_errc  V  std::ios_base  ,V  std::ios_base::_Fnarray  &V  std::ios_base::_Iosarray  覷  std::ios_base::Init  芔  std::ios_base::failure  鏤  std::ios_base::event E �/  std::_Constexpr_immortalize_impl<std::_Generic_error_category> ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char> � :|  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >  x5  std::_Invoker_strategy  榌  std::nothrow_t � 驢  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 鬵  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �2  std::_Default_allocate_traits N 玏  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 �5  std::allocator_traits<std::allocator<char> > ! �2  std::numeric_limits<short>  u   std::_Vbase . �0  std::allocator<nvrhi::rt::GeometryDesc> # d)  std::unique_lock<std::mutex> ( aE  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > 1 蘽  std::_Ptr_base<donut::engine::TextureData> ! ︰  std::ctype<unsigned short> C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > � t  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O 隻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P nW  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock  D  std::bad_alloc  /'  std::underflow_error B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  蹾  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � 狧  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy C &|  std::initializer_list<donut::engine::TextureSubresourceData> J   std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D 昣  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>   觭  std::atomic<unsigned int>  nV  std::messages_base  �&  std::out_of_range # �2  std::numeric_limits<__int64> _ eH  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 4H  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i `  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  >U  std::ctype<char> @ 鯣  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >  �  std::memory_order � |  std::queue<std::shared_ptr<donut::engine::TextureData>,std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > > > ! �)  std::recursive_timed_mutex � 緔  std::_Compressed_pair<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> " �)  std::condition_variable_any  �5  std::ratio<3600,1> � 鮷  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState> / g  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>   (  std::system_error ( 磜  std::shared_ptr<donut::vfs::Blob> < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  �5  std::ratio<1,1>   k5  std::forward_iterator_tag  '  std::runtime_error   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > � 閧  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >  峉  std::_Yarn<char>    std::_Container_proxy ( 鈌  std::_Facetptr<std::ctype<char> > Z 攆  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > /襱  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >  �9  std::allocator<bool>  �  std::u16string _ 霨  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 籊  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy  �  std::nested_exception  r  std::_Distance_unknown ) }G  std::allocator<nvrhi::BufferRange> ( �2  std::numeric_limits<unsigned int> < 馾  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> +f{  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > ) fw  std::shared_ptr<donut::vfs::IBlob> , 稵  std::codecvt<char32_t,char,_Mbstatet> @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> ] 6z  std::_Uninitialized_backout_al<std::allocator<donut::engine::TextureSubresourceData> > K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff 0 nG  std::vector<float,std::allocator<float> > F <G  std::vector<float,std::allocator<float> >::_Reallocation_policy S 踳  std::_Vector_val<std::_Simple_types<donut::engine::TextureSubresourceData> >    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double>  �'  std::errc , Ad  std::default_delete<std::_Facet_base>  F'  std::range_error  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  p)  std::_UInt_is_zero  {5  std::ratio<1,1000000000>  �,  std::allocator<char16_t> $ 巁  std::default_delete<char [0]> .  G  std::vector<bool,std::allocator<bool> > J 麱  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 薋  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J ~W  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  v5  std::ratio<1,1000>  t5  std::ratio<1,10000000> ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  鍿  std::_Crt_new_delete % ^(  std::_Iostream_error_category2 � 蟱  std::_Compressed_pair<std::allocator<donut::engine::TextureSubresourceData>,std::_Vector_val<std::_Simple_types<donut::engine::TextureSubresourceData> >,1> * r5  std::_String_constructor_concat_tag - ny  std::_Ref_count_obj2<donut::vfs::Blob>  D-  std::allocator<char> G �/  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1> R `  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> 2 鈡  std::shared_ptr<donut::engine::TextureData> � /t  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > @ 橺  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>    std::_Yarn<wchar_t> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> # 檡  std::_Wrap<donut::vfs::Blob>    std::wstring }   std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > ' �2  std::numeric_limits<signed char> � wX  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �&  std::domain_error  �  std::u32string_view  �  std::_Container_base � 莤  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > ; �6  std::weak_ptr<donut::engine::DescriptorTableManager> $ &/  std::hash<nvrhi::IResource *> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char> 9 o%  std::chrono::duration<__int64,std::ratio<1,1000> >  �$  std::chrono::nanoseconds y $S  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �$  std::chrono::duration<__int64,std::ratio<1,1000000000> > , f  std::chrono::duration_values<__int64>  �$  std::chrono::seconds 3 -%  std::chrono::duration<int,std::ratio<60,1> > 6 �$  std::chrono::duration<__int64,std::ratio<1,1> > s �$  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   U5  std::chrono::steady_clock   R5  std::chrono::system_clock 6 B%  std::chrono::duration<double,std::ratio<60,1> > ; �%  std::chrono::duration<double,std::ratio<1,1000000> > > �%  std::chrono::duration<double,std::ratio<1,1000000000> > = �$  std::chrono::duration<__int64,std::ratio<1,10000000> > q �$  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 %  std::chrono::duration<int,std::ratio<3600,1> > 8 �%  std::chrono::duration<double,std::ratio<1,1000> > < �%  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 Y%  std::chrono::duration<double,std::ratio<1,1> > 8 %  std::chrono::duration<double,std::ratio<3600,1> >  U  std::ctype_base  
Y  std::filesystem::perms ' 俌  std::filesystem::directory_entry $ Y  std::filesystem::copy_options ( 齒  std::filesystem::filesystem_error 7 ic  std::filesystem::_Path_iterator<wchar_t const *> ) 癡  std::filesystem::_Find_file_handle & 俈  std::filesystem::_Is_slash_oper . 乑  std::filesystem::_Should_recurse_result $ 8\  std::filesystem::perm_options 4 K[  std::filesystem::recursive_directory_iterator . DY  std::filesystem::_File_status_and_error & 験  std::filesystem::_Dir_enum_impl 0 Z  std::filesystem::_Dir_enum_impl::_Creator @ 
Z  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! Y  std::filesystem::file_type . (Z  std::filesystem::_Directory_entry_proxy " *\  std::filesystem::space_info * OZ  std::filesystem::directory_iterator & $S  std::filesystem::file_time_type 0 Z  std::filesystem::_Recursive_dir_enum_impl )   std::filesystem::directory_options # /Y  std::filesystem::file_status u 稾  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 輋  std::filesystem::_File_time_clock  RW  std::filesystem::path $ 萔  std::filesystem::path::format * =c  std::filesystem::_Normal_conversion < !e  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , 擳  std::codecvt<char16_t,char,_Mbstatet> � [z  std::_Uninitialized_backout_al<std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >  K5  std::char_traits<char> � `  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �'  std::error_category ) �'  std::error_category::_Addr_storage  �/  std::_Wrap<std::mutex> ! >(  std::_System_error_message  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > , 梥  std::_Atomic_integral<unsigned int,4> 7 赮  std::shared_ptr<std::filesystem::_Dir_enum_impl> = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1>  僒  std::_Codecvt_mode  A   std::max_align_t @ �3  std::_Default_allocator_traits<std::allocator<char16_t> > � 蝀  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> {鍃  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >,1> \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  5S  std::_Facet_base ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t> 2 齌  std::codecvt<unsigned short,char,_Mbstatet> # P(  std::_Generic_error_category  U3  std::streampos  i5  std::input_iterator_tag 2 e  std::_Wrap<std::filesystem::_Dir_enum_impl> X Id  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> ' y/  std::hash<enum nvrhi::ColorMask>  pT  std::codecvt_base t 箋  std::allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � x  std::_Vector_val<std::_Simple_types<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >  淜  std::bad_function_call O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > ' �/  std::_Ref_count_obj2<std::mutex> � 穥  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > � 梪  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � gu  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Reallocation_policy X 賣  std::function<void __cdecl(std::basic_string_view<char,std::char_traits<char> >)> ' _\  std::hash<std::filesystem::path> R 玿  std::_Func_base<void,std::basic_string_view<char,std::char_traits<char> > >  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � 爗  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access 
 !   wint_t # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  :  nvrhi::ResourceStates  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16> ! <  nvrhi::SharedResourceFlags    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray . 巀  nvrhi::RefCountPtr<nvrhi::ICommandList>  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # =7  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle 2 =7  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  岶  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  �?  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 岶  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  ?#  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # gF  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  �  nvrhi::CpuAccessMode  巀  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable * �?  nvrhi::RefCountPtr<nvrhi::ITexture>  H!  nvrhi::ComputeState 2 gF  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery  肦  __std_win_error  稴  lconv   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec  �[  __std_fs_file_id 
 !   _ino_t 
 )  _Cnd_t ' 鑋  __std_fs_create_directory_result  !   uint16_t  誖  __std_fs_stats " |s  donut::engine::TextureCache , 阺  donut::engine::TextureCache::Iterator ! Jv  donut::engine::TextureData # /v  donut::engine::FormatMapping ! :E  donut::engine::BufferGroup * 妠  donut::engine::dds::DDS_MISC_FLAGS2 + <v  donut::engine::dds::DDS_HEADER_DXT10 * 'v  donut::engine::dds::DDS_PIXELFORMAT % 6v  donut::engine::dds::DDS_HEADER 1 寋  donut::engine::dds::DDS_RESOURCE_DIMENSION  zE  donut::engine::MeshInfo & 癊  donut::engine::TextureAlphaMode  iE  donut::engine::MeshType # 糆  donut::engine::LoadedTexture & �6  donut::engine::DescriptorHandle , 7  donut::engine::DescriptorTableManager B �6  donut::engine::DescriptorTableManager::BindingSetItemsEqual B �6  donut::engine::DescriptorTableManager::BindingSetItemHasher , :w  donut::engine::TextureSubresourceData % $E  donut::engine::VertexAttribute % t   donut::engine::DescriptorIndex ' 苪  donut::vfs::enumerate_callback_t  剏  donut::vfs::Blob % !v  donut::vfs::RelativeFileSystem  饀  donut::vfs::IBlob  	v  donut::vfs::IFileSystem  Y@  donut::math::float4x4 " 2@  donut::math::vector<bool,4>    donut::math::float3  Q@  donut::math::affine3 #   donut::math::vector<float,3>  u   donut::math::uint  闌  donut::math::plane # 蜙  donut::math::vector<float,4>  A  donut::math::frustum $ 鰼  donut::math::frustum::Corners # 魼  donut::math::frustum::Planes  蜙  donut::math::float4 # Q@  donut::math::affine<float,3>   袮  donut::math::box<float,3> " �?  donut::math::vector<bool,2>  袮  donut::math::box3 % |@  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # 
B  donut::math::vector<float,2> M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t  釸  __std_fs_file_flags  砈  _Cvtvec  u   _Thrd_id_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  e4  _CatchableTypeArray  鏡  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t & �4  $_TypeDescriptor$_extraBytes_41  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  襌  __std_fs_reparse_tag  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  S  __std_fs_convert_result  蔙  __std_fs_stats_flags  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24  �(  _Mtx_internal_imp_t & _4  $_TypeDescriptor$_extraBytes_25 % P4  __RTTIClassHierarchyDescriptor  ⊿  _Collvec   j[  __std_fs_volume_name_kind     __time64_t  m  FILE 
 �(  _Mtx_t 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray  鋄  __std_fs_remove_result  �(  _Thrd_t - W4  $_s__RTTIBaseClassArray$_extraBytes_16 - G4  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  臨  __std_fs_file_attr  
  __std_exception_data 
 u   _dev_t  b[  __std_ulong_and_error  K  lldiv_t  H  _ldiv_t  �  _timespec64     intptr_t  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers  �   �      U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  7    譫鰿3鳪v鐇�6瘻x侃�h�3&�  u    j轲P[塵5m榤g摏癭 鋍1O骺�*�  �    齝D屜u�偫[篔聤>橷�6酀嘧0稈  �    _O縋[HU-銌�鼪根�鲋薺篮�j��  E   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG     矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  R   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     	{Z�范�F�m猉	痹缠!囃ZtK�T�  Q   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<     
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  K   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   繃S,;fi@`騂廩k叉c.2狇x佚�  �   郖�Χ葦'S詍7,U若眤�M进`  %   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  q   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��     +FK茂c�G1灈�7ほ��F�鳺彷餃�  6   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  �    d蜯�:＠T邱�"猊`�?d�B�#G騋     溶�$椉�
悇� 騐`菚y�0O腖悘T  V   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �    狾闘�	C縟�&9N�┲蘻c蟝2     J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  b   �'稌� 变邯D)\欅)	@'1:A:熾/�  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  5	   :|?龄�*.5蜣侟緥许j竦xT�/罱^  d	   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �	   躙肚捓y�	)孼�湦枪簫hn�/0甦b  �	   チ畴�
�&u?�#寷K�資 +限^塌>�j  
   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  S
   穫農�.伆l'h��37x,��
fO��  �
   5�\營	6}朖晧�-w氌rJ籠騳榈  �
   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1     蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  T   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   蜅�萷l�/费�	廵崹
T,W�&連芿     E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  Y   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   D���0�郋鬔G5啚髡J竆)俻w��  ?
   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  w
   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �
   $^IXV嫓進OI蔁
�;T6T@佮m琦�     c�#�'�縌殹龇D兺f�$x�;]糺z�  T   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   f扥�,攇(�
}2�祛浧&Y�6橵�     曀"�H枩U传嫘�"繹q�>窃�8  T   [届T藎秏1潴�藠?鄧j穊亘^a  �   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  0   檅鋲�1o婈$�;�芯厁%rP�衃K設  l   dhl12� 蒑�3L� q酺試\垉R^{i�  �   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅     鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  k   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   淁U懊X昄O/?漁\Co&I�
 鷕2氠     豊+�丟uJo6粑'@棚荶v�g毩笨C  a   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   o藾錚\F鄦泭|嚎醖b&惰�_槮     匐衏�$=�"�3�a旬SY�
乢�骣�  i   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  P   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   �"睱建Bi圀対隤v��cB�'窘�n  ?   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   zY{���睃R焤�0聃
扨-瘜}  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  2   �(M↙溋�
q�2,緀!蝺屦碄F觡  ~   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  K   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/     �0�*е彗9釗獳+U叅[4椪 P"��  I   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�     �=蔑藏鄌�
艼�(YWg懀猊	*)  ^   交�,�;+愱`�3p炛秓ee td�	^,  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  3   _臒~I��歌�0蘏嘺QU5<蝪祰S  x   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n      o忍x:筞e飴刌ed'�g%X鶩赴5�n�  I   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇     +4[(広
倬禼�溞K^洞齹誇*f�5  ~   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  @   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   �咹怓%旗t暐GL慚ヌ��\T鳃�  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  H   副謐�斦=犻媨铩0
龉�3曃譹5D   �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;     п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  t   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  ;    v峞M� {�:稚�闙蛂龣 �]<��  �    隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �    x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  !   L�9[皫zS�6;厝�楿绷]!��t  U!   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �!   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �!   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  	"   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  J"   猯�諽!~�:gn菾�]騈购����'  �"   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  �"   ii(祍P%舌+;�'縿E╪嫏脈3L籾
�  	#   憒峦锴摦懣苍劇o刦澬z�/s▄![�  H#   W簅r�hc鷟;9攫�.�6Y柦阁��}.J╗  �#   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �#   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  $   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  D$   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �$   �=A�%K鹹圛19振╯鵽C殾錦`蔣  �$   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �$   妇舠幸佦郒]泙茸餈u)	�位剎  ?%   鏀q�N�&}
;霂�#�0ncP抝  x%   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �%   �X�& 嗗�鹄-53腱mN�<杴媽1魫   &   靋!揕�H|}��婡欏B箜围紑^@�銵  @&   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  �&   �颠喲津,嗆y�%\峤'找_廔�Z+�  �&   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  ''   t�j噾捴忊��
敟秊�
渷lH�#  f'   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �'   =J�(o�'k螓4o奇缃�
黓睆=呄k_  �'   �
bH<j峪w�/&d[荨?躹耯=�  (   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  q(   +椬恡�
	#G許�/G候Mc�蜀煟-  �(   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �(   鹴y�	宯N卮洗袾uG6E灊搠d�  8)   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   �      T  �  B   U  �  H   V  �  Y   [  �  �   v  �  U   w  �  �   �  0  �  �  0  �  �  0  x  �  h  B  �  h  �  �  h  �  �  h  �  �  �
  K     h  �    h  D
    h  �  !  h  O   "  h  0   9  �  �  �  �  �  �  �  �   �  �  �   �  �  @   �  h  �  �  h  �    h  s    h  �  t  h  )
  �  �  �   �  �  �    �  �    �
  �   2  p
  �   4  p
  �   H  p
  �   �  0  �  P  �  [   �   h  �  �   h  �	  !  p
    
!  p
  �   )!  h  @
  g&  x  �  h&  x  �  i&  x  �  p&  0     q&  0  5  w&  �  b  x&  �  4  �&  �  b  �&  �  4  �&  �  �  �&  �  `  �&  0  �  �&  0  �  �&  0  "  �&  p
  �   �&  0  t  �&  �  �  �&  0  �  �&  0  Z  �&  0  t  �&  h  <
  �&  �  �  �&  0  z  �&  0  �
  �&  0  i  �&  �  
  �&  �  
  �&  �  �  �&  �  >  �&  0  D  �&  h  �  �&  �
  �   �&  0     �&  0  5  �&  �  �  �&  0  n  �&  0  &  �&  0  9  �&  0  :  �&  �  �  �&  �  �  �&  �  �  �&  �  /   '  0  :  '  �  
  '  �    '  �  �  '  �  �  '  �  �  '  �  �  '  �  �  '  �  �  '  �  �  "'  �  �  #'  �  ]  ,'  �  ]  3'    �   4'  �  �  5'  �  �  7'  �    ?'  �  �  @'  �  �  B'  �    M'  �  �  O'  �  �  X'    �  ['  �  �  a'  �    b'  �  �  d'  �  @   h'  �  `  j'  �  �  l'  �  �  m'  �  �  n'  �  �  o'  �  �  r'  �
  �  s'  �  �  t'  �  �  �   �)   D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\RTXPT\External\Donut\src\engine\DDSFile.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\RTXPT\External\Donut\include\donut\engine\DDSFile.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\include\donut\engine\TextureCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\RTXPT\External\Donut\include\donut\core\math\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\RTXPT\External\Donut\src\engine\dds.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\RTXPT\External\Donut\include\donut\core\vfs\VFS.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\shared_mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\condition_variable D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\queue D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h �       Lhl  h  O   l  O  
 �:  e    �:  e   
 巚  f    抳  f   
 X�  g    \�  g   
 珯  N   瘷  N  
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb �       DXT1                           DXT2                           DXT3                           DXT4                           DXT5                           BC4U                           BC4S                           BC5U                           BC5S                           RGBG                           GRGB                           YUY2                        A             �  �  �      �    @             �  �  �           A           �    �    �    �    @           �    �    �         @           ��    ��            @           �  �             A           |  �      �      A             �       �      @            �  �  �                     �                             ��                           �            �               �            �                            �                 �    �                         �    �    �    �               ��    ��               DX10                      �?                  �?                  �?                   >         @         =         ?         2         4         1         3      	   9      
   ;         8         :      
   6         s         U         V                                                  W                    [                              $          &          #          %          "          *           +       !   )       "      @   #      @   $   
   @   %      @   &   
   @   '      @   (      @   )      @   *      `   +      `   ,      `   -      �   .      �   /      �   0   8      1   .       2   /       3   )       4      @   5      @   6   G      7   H      8   J      9   K      :   M      ;   N      <   P      =   Q      >   S      ?   T      @   _      A   `      B   b      C   c      谐Y>Y7?樰�=H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   !   /   $   5   9      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   [   0   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
   [     [  
 s  �   w  �  
 �  [   �  [  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   k      �   �  � G            0   
   %   X'        �std::_Copy_memmove<donut::engine::TextureSubresourceData *,donut::engine::TextureSubresourceData *>  >蓈   _First  AJ          >蓈   _Last  AK          >蓈   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   Y'   0   蓈  O_First  8   蓈  O_Last  @   蓈  O_Dest  O�   @           0        4       � �   � �   � �!   � �%   � �,   Z   0   Z  
 �   Z   �   Z  
 �   Z   �   Z  
 �   Z   �   Z  
    Z     Z  
 !  Z   %  Z  
 �  Z   �  Z  
 H;蕋wH塡$WH冹 H塼$0H孃3鯤嬞@ H�H吷t<H婼H+袶冣郒侜   rL婣鳫兟'I+菻岮鳫凐w,I嬋�    H�3H塻H塻H兠H;遳矵媡$0H媆$8H兡 _描    蘒   "   ~   $      �   �  � G            �      �   �&        �std::_Destroy_range<std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >  >Rv   _First  AI       g [   AJ          AJ |       >磛   _Last  AK          AM       l e   AK |       >紇   _Al  AP           AP       ]    9   D@    M        �&  D ] M        '  D ]$ M        "'   h1#	 M        '  */N M        �  3))
 Z   �  
 >   _Ptr  AJ T       >#    _Bytes  AK  ,     V   - $ " M        w  
<#
,
 Z   S   >    _Ptr_container  AP  @     B  )  AP T       >    _Back_shift  AJ  #     _ 1 )  AJ       ]  1 9   N N N N N N                       H� 2 h   w  x  �  �&  �&  �&  �&  '  '  '  "'         $LN48  0   Rv  O_First  8   磛  O_Last  @   紇  O_Al  O  �   H           �   �     <       > �    B �    C �d   B �m   F �}   C �,   Q   0   Q  
 �   Q   �   Q  
 �   Q   �   Q  
   Q     Q  
 !  Q   %  Q  
 1  Q   5  Q  
 E  Q   I  Q  
 c  Q   g  Q  
 s  Q   w  Q  
 <  Q   @  Q  
 ]  Q   a  Q  
 �  Q   �  Q  
 �  Q   �  Q  
 �  Q   �  Q  
   Q     Q  
 �  �   �  �  
 �  Q   �  Q  
 H塋$SVWAVAWH冹 H嬺L嬹I�������I;�嘍  L媦L+9I�H婭I+H六H嬔H殃I嬂H+翲;��  H�
H;辳
H嬣H塡$h�H塡$hI;�囼   H零H侞   r+H岾'H;�嗀   �    H吚勚   H峹'H冪郒塆�3呻H呟tH嬎�    H孁3呻3蓩鵋墊$XI嬊H拎H兝H嬛I+譼H荋塇餒塇鳫�H塇H岪 H冴u鏜婩I�L+翲嬒�    怚�H吷t1I媀H+袶冣郒侜   rH兟'L婣鳬+菻岮鳫凐w6I嬋�    I�>H伶H鱅塿H�;I塅H兡 A_A^_^[描    惕    惕    虗   !   �   !     k   A  "   h  9   n  O   t  $      �   F  � G            y     y  �&        �std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >::_Resize_reallocate<std::_Value_init_tag> 
 >磛   this  AJ          AV       cM  DP    >   _Newsize  AK          AL       f9 
 >U:   _Val  AP           D`    >#     _Newcapacity  AI  `       AI u       Bh   e         >    _Oldsize  AW  -     L  4 @  >$w    _Appended_first  AH  �         AH �       >$w    _Newvec  AM  �       AM �     � �   BX   �     � �   M        4'  Yl�� M        M'  Yl��% M        �  y)
+%��( M        �  ��$	%)
��
 Z   k   >    _Block_size  AJ  �       AJ g      >    _Ptr_container  AH  �       AH �     �  � 
 >0    _Ptr  AM  �       AM �     � �   M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N M        �  
l
	 N N N! M        '  4kD%
 >    _Oldcapacity  AJ  8     �   N ' }   AJ g      >    _Geometric  AI  X         AI u     �  �  M        #'  4 N N M        �&  �� >#    _Count  AK  �     (  M        7'  �� N N M        5'  �� >$w   _First  AK         >$w   _Last  AP  �       M        X'  ��c >    _Count  AP        N N% M        '  �h1#  M        '  *�X M        �  �)3
 Z   �  
 >   _Ptr  AJ @      >#    _Bytes  AK      -    AK s     % M        w  �(d#
6
 Z   S   >    _Ptr_container  AP  0      AP @    8  .  >    _Back_shift  AJ      1  AJ @    8 '   N N N N
 Z   '               (         0@ � h!   �  v  w  x  �  �  �  �  �&  �&  �&  '  '  	'  '  '  '  '  #'  4'  5'  6'  7'  M'  V'  W'  X'  Y'  Z'  ['  \'  i'  j'         $LN103  P   磛  Othis  X     O_Newsize  `   U:  O_Val  O  �   �           y  �     �       � �   � �)   � �4   � �l   � ��   � ��   � ��   � ��   � ��   � �  	 �[  
 �g  � �m  � �s  	 ��   ^  � F            (   
   (             �`std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >磛   this  EN  P         ( 
 >U:   _Val  EN  `         ( 
 Z   '                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN103  P   磛  Nthis  X     N_Newsize  `   U:  N_Val  O  �   0           (   �     $        �
    �    �,   X   0   X  
 �   X   �   X  
 �   X   �   X  
   X   !  X  
 -  X   1  X  
 P  X   T  X  
   X   �  X  
 �  X   �  X  
 �  X   �  X  
 �  X   �  X  
    X     X  
   X     X  
 6  X   :  X  
 F  X   J  X  
 Z  X   ^  X  
   X     X  
 #  X   '  X  
 L  X   P  X  
 \  X   `  X  
   X   �  X  
 �  X   �  X  
 V  X   Z  X  
 r  X   v  X  
 �  X   �  X  
 �  X   �  X  
   X     X  
 a  X   e  X  
 �  X   �  X  
 �  X   �  X  
 R  X   V  X  
 s  X   w  X  
 �  X   �  X  
 �  X   �  X  
 �  X   �  X  
   X   !  X  
 -  X   1  X  
   �     �  
 \  X   `  X  
 	  \   	  \  
 �	  \   �	  \  
 �	  \   
  \  
 3
  �   7
  �  
   �     �  
 \  \   `  \  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   N   #   f   H塋$SVWATAUAVAWH冹0L嬺H嬹I躬
I;�圐  H婭H+I将*I嬇H鏖L嬧I咙I嬆H凌?L郒婲H+I嬇H鏖H龙H嬄H凌?H蠬嬍H验I嬃H+罤;�嚐  H�I;苨
I嬈H塂$ �H塂$ I;�噡  H�@L�<�    I�   r+I峅'I;�哱  �    H吚刏  H峹'H冪郒塆�3垭M�tI嬒�    H孁3垭3蹕鸋墊$xK�dH�荋塋$(H墝$�   I嬈I+膖H�H塝H塝H兞H墝$�   H冭u鉒嬈H嬔�    L媀H嬒H�I;聇7f�     L婬H塜L婡H塜H�H�H�L堿L塈H兞H兝I;聈襆嬈H嬔�    怘�H吷t[L嬈H媀�    L�H婲I+菼嬇H鏖H龙H嬄H凌?H蠬�RH菱H侜   rH兟'I婬鳯+罥岪鳫凐w>L嬃I嬋�    H�>K�vH�荋塏I�?H塅H兡0A_A^A]A\_^[描    惕    惕    躺   !   �   !   D  Q   �  Q   �  Q   �  "   %  9   +  I   1  $      �   �  BG            6     6  �&        �std::vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >::_Resize_reallocate<std::_Value_init_tag> 
 >`v   this  AJ          AL         Dp    >   _Newsize  AK          AV        
 >U:   _Val  AP        /�  �  � &)  AP �     B  D�    >#     _Newcapacity  AH  �       AH �       D     >    _Oldsize  AT  G       >磛    _Appended_first  D(    >磛    _Newvec  AM  �       AM �     8#  Bx       3!  M        ?'  a��亣 M        O'  a��亣& M        �  ��)
+%�*( M        �  ��$	%)
丗
 Z   k   >    _Block_size  AJ  �       AJ $      >    _Ptr_container  AH  �       AH �     7	 )
 >0    _Ptr  AM  �       AM �     8#  M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N M        d'  
��
	 N N N# M        '  UD%
 >    _Geometric  AH  �         AH �     � z M        ,'  U N N$ M        �&  �." >#    _Count  AH      -  >[z   _Backout  CJ     /      CJ         (    M        '  � N M        '  �=
 Z   �&   N M        '  丱 N M        B'  �  M        b'  �  M        h'  �  M        m'  �  M        n'  �  N N N N N N$ M        @'  "丩.%	 >磛   _Last  AR  L    M  >Rv    _UFirst  AH  R    G  >[z   _Backout  CJ     O    J  M        '  丩 N M        '  亷
 Z   �&   N M        a'  乣! M        l'  !乣 M        o'  !乣 M        s'  乿 M        t'  乿 N N M        r'  乸 >蓈    _Old_val  AK  s      AK `    4    N M        r'  乭 >蓈    _Old_val  AP  l    "  AP `    1  "  N M        r'  乣 >蓈    _Old_val  AQ  d    *  AQ `    9  *  N N N N N' M        '  仛(LO#$
 Z   �&   M        �&  1佁d M        �  佋);
 Z   �  
 >   _Ptr  AP �      >#    _Bytes  AK  �    )  AK 0     % M        w  佪d#
>
 Z   S   >    _Ptr_container  AJ  �      AJ �    @  8  >    _Back_shift  AP  �    D  AP �    @  3  N N N N
 Z   '   0           8         0@ � h+   �  v  w  x  y  z  �  �  �  �&  �&  �&  �&  �&  '  '  '  '  '   '  ,'  ?'  @'  A'  B'  O'  `'  a'  b'  d'  h'  k'  l'  m'  n'  o'  p'  q'  r'  s'  t'  u'  v'         $LN125  p   `v  Othis  x     O_Newsize  �   U:  O_Val  (   磛  O_Appended_first  O�   �           6  �     �       � �   � �-   � �U   � ��   � ��   � �  � �H  � �O  � �R  � ��  	 �  
 �$  � �*  � �0  	 ��   �  RF            =      =             �`std::vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >`v   this  EN  p         = 
 >U:   _Val  EN  �         =  Z   �&  �&   (                    � ]       __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN125  p   `v  Nthis  x     N_Newsize  �   U:  N_Val  (   磛  N_Appended_first  O�   8           =   �     ,        �    �"    �3    �,   Y   0   Y  
 g  Y   k  Y  
 w  Y   {  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 (  Y   ,  Y  
 8  Y   <  Y  
 c  Y   g  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
   Y     Y  
 �  Y   �  Y  
 �  Y   �  Y  
 G  Y   K  Y  
 n  Y   r  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 7  Y   ;  Y  
 G  Y   K  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 N	  Y   R	  Y  
 o	  Y   s	  Y  
 	  Y   �	  Y  
 �	  Y   �	  Y  
 �	  Y   �	  Y  
 
  Y   
  Y  
 %
  Y   )
  Y  
 #  �   '  �  
 �  Y   �  Y  
 P  ]   T  ]  
 �
  ]   �
  ]  
 �
  ]   �
  ]  
 �
  �      �  
 [  �   _  �  
 �  ]   �  ]  
 H塗$SUH冹(H嬯L婨pH嫊�   H婱(�    L婨 H婾xH婱p�    3�3设    �   Q   /   G   8   f   @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   #   %   '   ,   )      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   /   0   /  
 d   /   h   /  
 t   /   x   /  
 �   /   �   /  
 �   /   �   /  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   #   %   '   ,   ,      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   5   0   5  
 z   5   ~   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 H�    H茿    H堿H�    H�H嬃�   /      ,      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   3   0   3  
 z   3   ~   3  
   3     3  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   #   %   '      �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   )   0   )  
 d   )   h   )  
 t   )   x   )  
 �   )   �   )  
 �   )   �   )  
   )     )  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         2        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >s?   this  AH         AJ          AH        M        H  GCE
 >X    temp  AJ  
       AJ        N (                     0H� 
 h   H   0   s?  Othis  9       /   O�   0           "   p
     $       �  �   �  �   �  �,   D   0   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 D  D   H  D  
 \  D   `  D  
 H�    H��   U      �   �   p G                   
   �&        �std::_Ref_count_obj2<donut::vfs::Blob>::~_Ref_count_obj2<donut::vfs::Blob> 
 >hy   this  AJ                                 H� 
 h   �      hy  Othis  O  �   (              0            2 �
   8 �,   R   0   R  
 �   R   �   R  
 �   R   �   R  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   z  h G            K      E   �&        �std::shared_ptr<donut::vfs::IBlob>::~shared_ptr<donut::vfs::IBlob> 
 >Tw   this  AJ        +  AJ @       M        �&  &, M        �  
 >Z&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �&   0   Tw  Othis  9+       [&   9=       [&   O  �   0           K   0     $       � �   � �E   � �,   F   0   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 f  F   j  F  
 v  F   z  F  
 �  F   �  F  
 @SH冹 H嬞H�	H吷tlH婼L嬅�    H�H斧*H婼H+袶麝H龙H嬄H凌?H蠬�RH菱H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �   Q   l   "   �   $      �     G            �      �   �&        �std::vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >::~vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > > 
 >`v   this  AI  	     � y   AJ        	 & M        �&  	(LS%	

 Z   �&   M        �&  .BA M        �  J)
 Z   �  
 >   _Ptr  AJ k       >#    _Bytes  AK  J     > &  " M        w  
S#

 Z   S   >    _Ptr_container  AP  W     1    AP k       >    _Back_shift  AJ        h K   AJ k       N N N N                       H�  h   w  x  �  �&  �&  �&         $LN28  0   `v  Othis  O  �   8           �   �     ,       � �	   � �}    ��   � �,   H   0   H  
 -  H   1  H  
 A  H   E  H  
 �  H   �  H  
 �  H   �  H  
 V  H   Z  H  
 j  H   n  H  
 �  H   �  H  
 �  H   �  H  
 �  �     �  
 ,  H   0  H  
 @SH冹 H嬞H婹`H凓v1H婭HH�翲侜   rH兟'L婣鳬+菻岮鳫凐噸   I嬋�    H荂X    H荂`   艭H H婼@H凓v-H婯(H�翲侜   rH兟'L婣鳬+菻岮鳫凐wBI嬋�    H荂8    H荂@   艭( H岾�    怘�H吷tH�    H��P怘兡 [描    藹   "   �   "   �   C   �   $      �   L  R G            �      �   r&        �donut::engine::LoadedTexture::~LoadedTexture 
 >矱   this  AI  	     � �   AJ        	  M        2  �� M        H  ��CE
 >X    temp  AJ  �       AJ �       N N M        �  KXq( M        �  XJ)(
*
 >e    _Ptr  AJ  f       AJ �       M          X N M          )fc M        9  &i` M        �  i)?
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  i     &  AK �      $ M        w  rd#
B
 Z   S   >    _Ptr_container  AP  z       AP �     D  :  >    _Back_shift  AJ  }     
  AJ �       N N N N N N M        �  O	! M        �  iJ-(
 >e    _Ptr  AJ         AJ ?       M          	 N M          - M        9  * M        �  )
 Z   �  
 >   _Ptr  AJ ?       >#    _Bytes  AK       *  AK �       M        w  #d# >    _Ptr_container  AP  +       AP ?     �  �  >    _Back_shift  AJ  .       AJ �       N N N N N N                      0H� B h   w  x  �  �  �  �           9  �  �  2  H         $LN70  0   矱  Othis  9�       /   O,   E   0   E  
 w   E   {   E  
 �   E   �   E  
 �   E   �   E  
 �   E   �   E  
 W  E   [  E  
 g  E   k  E  
 �  E   �  E  
   E      E  
 ,  E   0  E  
 �  E   �  E  
 �  E   �  E  
 �  E   �  E  
 �  E   �  E  
 @  E   D  E  
 P  E   T  E  
 �  E   �  E  
 �  E   �  E  
 
  E     E  
 T  E   X  E  
 d  E   h  E  
 �  E   �  E  
 �  E   �  E  
 $  �   (  �  
 H  E   L  E  
 H塡$VH冹 H嬹H伭�   �    H媈pH呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H嬑H媆$8H兡 ^�       H   f   E      �     N G            j   
   [   s&        �donut::engine::TextureData::~TextureData 
 >Av   this  AJ        
  AL  
     X  M        �&  : M        �&  , M        �  '
 >Z&   this  AI       C  M        �  @	
 N N N N                       H�  h   �  �  �&  �&   0   Av  Othis  9>       [&   9P       [&   O ,   J   0   J  
 s   J   w   J  
 �   J   �   J  
 �   J   �   J  
 k  J   o  J  
 {  J     J  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂0    H荂8   艭  H兡 [描    �<   "   [   $      �   �  F G            `      `            �nvrhi::TextureDesc::~TextureDesc 
 >>   this  AI  
     S L   AJ        
  M        �  GM) M        �  -(

 M           N M          -G M        9  &@ M        �  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        w  
##
"
 Z   S   >    _Ptr_container  AP  '     8    AP ;       >    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   w  x  �  �  �  �           9  �  �         $LN37  0   >  Othis  O   ,   <   0   <  
 k   <   o   <  
    <   �   <  
 _  <   c  <  
 �  <   �  <  
 �  <   �  <  
 �  <   �  <  
   <     <  
 �  y   �  y  
 �     �   �   L G                       �        �std::_Ref_count_base::~_Ref_count_base 
 >Z&   this  AJ          D                           H�     Z&  Othis  O  �                  0            ~ �,   =   0   =  
 q   =   u   =  
 �   =   �   =  
 H�    H�H兞�       #      (      �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   4   0   4  
 {   4      4  
 H�    H�H兞�       #      (      �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   *   0   *  
 e   *   i   *  
 �   *   �   *  
 @SH冹 H�    H嬞H�雎t
�(   �    H嬅H兡 [�	   U      "      �   �   j G            +      %   �&        �std::_Ref_count_obj2<donut::vfs::Blob>::`scalar deleting destructor' 
 >hy   this  AI         AJ                                @� 
 h   �&   0   hy  Othis  O,   U   0   U  
 �   U   �   U  
 �   U   �   U  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   #      (   0   "      �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   0   0   0  
 w   0   {   0  
 �   0   �   0  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   #      (   0   "      �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   6   0   6  
 �   6   �   6  
 �   6   �   6  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   #      (   0   "      �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   ,   0   ,  
 w   ,   {   ,  
 �   ,   �   ,  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   $        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >A   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   A  O__f  9(       A   O ,       0      
 g       k      
 w       {      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
            
 !      %     
 1      5     
 A      E     
 �      �     
 婣ˊ刱  婣凐匇   凐 呿   婣=�   u2D婭E嬃A侚 �  uJ亂  � uA亂   �u8勔�   �   E撩=  � uKD婣A侙 �  u亂�   u亂   �t!�=  � u#A侙 �  uy亂�   up儁 uj���U   �=  �?u亂 � uO亂�  uF亂   纔=��=��  u亂  ��u*儁 u$儁 u�脙�u儁 u儁 u
儁 剋  2烂婣= |  u亂�  u陜yu鋪y �  u郯�= �  u亂�  u葍yu聝y u及�=   u瞾y�   u﹥yuy �  u毎�亨優   婣凐u3亂�   厁���儁 卬���儁 卍���儁 t}亂 �  tc2烂凐匢���婣=��  u!儁 �5���儁 �+���儁 �!�����=�   ����儁 �	���儁 ���亂 �  咈���猫t
儁呩����亨儱   婣凐u1亂�   吙��亂 �  叢��儁 叏��儁 厼���脙� 厭��婣=�   u*亂 �  厈��亂  � 卬��亂   �卆����=��  匰��亂  ��匜��儁 �<��儁 �2���猫�'��婣=DXT1u岯6�=DXT3u岯8�=DXT5u岯:�=DXT2u�8�=DXT4u�:�=ATI1tk=BC4Utd=BC4Su�=�=ATI2tP=BC5UtI=BC5Su�?脙儡凐P嚦��H�    秳    媽�    H�岚%冒&冒
冒冒$冒!冒)冒/冒>冒<脨                                     ]  �   e  �   l  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �      �   �  E F            	      	  f&        �donut::engine::ConvertDDSFormat 
 >(v   ddpf  AJ        �X  p  AJ �      >0    forceSRGB  A         ��  a,  A  �                             @ 
            
                    $LN57         $LN56         $LN55         $LN54         $LN53         $LN52         $LN51         $LN50     (v  Oddpf     0   OforceSRGB  O   �   X          	  x  h   L      �  �    �  �   �  �    �  �L   �  �[   � �\   �  ��   �  ��   �  ��   � ��   �  ��   �  ��   � ��   �  ��   �  ��   � ��   �  �
  � �  � �  �  �2  �  �4  � �5  �  �Q  �  �S  � �T  �  �s  �  �u  � �v  �  ��  �  ��  �  ��  �  ��  � ��  � ��   ��   ��   ��  � ��   �  	 �  � �  
 �"   �,   �.  � �/   �9   �A   �o   �q  � �r   �{    ��  " ��  � ��  $ ��  & ��  � ��  , ��  . ��  0 ��  � ��  2 ��  4 ��  � ��  6 �  8 �	  � �
  = �  ? �  � �  A �  C �  � �  F �%  J �,  N �3  P �5  � �6  S �=  W �D  [ �K  ] �M  � �N  r �u  u �w  � �x  x �z  � �{  { �}  � �~  ~ ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  Y ��  � ��  L ��  � �,   L   0   L  
 j   L   n   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 �   �   �   �  
 �   �   �   �  
   �   
  �  
   �     �  
 (  �   ,  �  
 9  �   =  �  
 J  �   N  �  
 [  �   _  �  
 l  �   p  �  
 }  �   �  �  
 �  L   �  L  
 @USVWATAUAVAWH峫$鳫侅  H�    H3腍塃餗嬹M孁H嬺L嬮H塋$8L塋$8L媏p3�I99uIH�9I媃H呟�  ����嬊�罜凐�  H�H嬎��羬�咇  H�H嬎�P殂  H墊$@H墊$HH峀$P�    W�D$hH墊$xH荅�   艱$h E圚墋楬荅�   艵� H墋℉嬤H塢皥]盖E�   荅�   荅�   荅�   荅�   f塢袌]襀墋豧E郔婩H吚t�@H媇癐�H塃↖婩H塃翱����H呟t)嬊�罜凐uH�H嬎�嬊�罜凐u	H�H嬎�P禘x圗襆塪$ L峀$@M嬊H嬛I嬐�    怘媇谽3�H呟劻   H媢郒;辴UH�H吷t@H婼H+袶冣郒侜   rH兟'L婣鳬+菻岮鳫凐嚶  I嬋�    L�;L墈L墈H兠H;辵疕媇豀婱鐷+薍斧*H鏖H龙H嬄H凌?H蠬�RH菱H嬅H侜   rH兟'H媅鳫+肏兝鳫凐嘢  H嬎�    L墋�W纅E郒媇癏呟t)嬊�罜凐uH�H嬎�嬊�罜凐u	H�H嬎�PH婾燞凓v1H�翲婱圚嬃H侜   rH兟'H婭鳫+罤兝鳫凐囎   �    L墋楬荅�   艵� H婾�H凓v2H�翲婰$hH嬃H侜   rH兟'H婭鳫+罤兝鳫凐噵   �    L墊$xH荅�   艱$h H峀$P�    怘婰$@H吷tL墊$@H��P怚媈H呟t'嬊�罜凐uH�H嬎��羬�u	H�H嬎�RI嬇H婱餒3惕    H伳  A_A^A]A\_^[]描    �   �   �   B   �  P   �  "   H  "   �  "   
  "   )  C     i   �  $      �   �  O G            �  '   �  m&        �donut::engine::CreateDDSTextureFromMemory  >{#   device  AK        0  AL  0     k AL X    E<  >r#   commandList  AP        -  AW  -     a AW t     
 >;w   data  B8   =     ` AQ        *  AV  *     sd	  Dh   >_   debugName  AT  A     \Q  EO  (           Dp   >0    forceSRGB  EO  0           Dx  
 >Jv   info  CK  @   �    	  CK  `   �    	  CI  p   �     Z  G  CK @       
  CK `   �      D@    M        �&  0僁 M        �&  僁'	 M        �  僊,
 >Z&   this  AI  H    ,  AI t    !  M        �  僡	
 N N N N M        2  �. M        H  �.HB
 >X    temp  AJ  3      AJ D    7    N N M        �  N傉��' M        �  傉
2%
y M          2傔�� M        9  /傗��  M        �  傟)��
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH       >#    _Bytes  AK  �    /  AK �      M        w  傮d��
 Z   S   >    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  K倞" M        �  倞
1$ M          1倲 M        9  .倵 M        �  倿)
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    .  AK �      M        w  偋d >    _Ptr_container  AH  �      AJ  �      N N N N N N M        �&  2俋 M        �&  俋)	 M        �  俛,
 >Z&   this  AI  \    �  AI �      M        �  倁	 N N N N M        �&  j亣[n^& M        �&  亣Z^W M        �&  5� M        �  �")
 Z   �  
 >   _Ptr  AH  "      AI  �    �  U  AH D      AI �    �L l � ; >#    _Bytes  AK      -  AK �      M        w  �+d >    _Ptr_container  AH  6      AI  3      N N N M        �&  仦"	H >Rv   _First  AI  �    	  AI �    �L � >磛   _Last  AL  �    �  AL X    E<  M        �&  H仩 M        '  H仩  M        "'  仩h5# M        '  .伅 M        �  伋)
 Z   �  
 >   _Ptr  AJ �      >#    _Bytes  AK  �    1    AK �       M        w  伡d# >    _Ptr_container  AP  �      AP �    � � >    _Back_shift  AJ  �    5  AJ �    � 5 =  Y � N N N N N N N N N M        �&  S� M        �&  +�< M        �&  �<) M        �  �>, M        �  丷	 N N N N M        �&  �% M        �&  �% M          �,
 >k&    _Tmp  AH  0      AH g      N M        �&  �% N N N M        �&  � M        �&  � M        �&  �	 M        �  � N N N N N M        �&  	� M        �&  	� M        �&  	� N N N M        p&  �� M        q&  ��� N N M        �   �� M          ��$ N M        �  �� M        �  �� M          �� N N N N M        �   �� M          ��% N M        �  �� M        �  �� M          �� N N N N M        4  �� N M        �&  AK M        �&  K4
 M        �  X
 >Z&   this  AI  O     B  AI t    !  M        �  u	 N N N N M        �&  H N
 Z   l&             @         A h@   w  x  z  �  �  �  �  �  �             9  �  �  �  �  �      e  2  4  H  �  �   n&  o&  p&  q&  r&  s&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  '  '  '  "'  
 :�   O        $LN269  X  {#  Odevice  `  r#  OcommandList  h  ;w  Odata  p  _  OdebugName  x  0   OforceSRGB  @   Jv  Oinfo  9s       [&   9�       [&   9P      [&   9d      [&   9s      [&   9�      [&   9@      /   9_      [&   9q      [&   O  �   `           �  x  	   T        �C    �H    ��    �   �g   �n   �w   ��   ��   �   ^ F                                �`donut::engine::CreateDDSTextureFromMemory'::`1'::dtor$0 
 >;w   data  EN  h         
 >Jv    info  EN  @                                  �  O�   �   ^ F                                �`donut::engine::CreateDDSTextureFromMemory'::`1'::dtor$6 
 >;w   data  EN  h         
 >Jv    info  EN  @                                  �  O�   �   ^ F                                �`donut::engine::CreateDDSTextureFromMemory'::`1'::dtor$2 
 >;w   data  EN  h         
 >Jv    info  EN  @                                  �  O,   @   0   @  
 v   @   z   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 �   @     @  
   @     @  
   @   #  @  
 O  @   S  @  
 g  @   k  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
   @     @  
   @     @  
 �  @   �  @  
 �  @   �  @  
   @     @  
 &  @   *  @  
 �  @   �  @  
   @   
  @  
   @     @  
 7  @   ;  @  
 G  @   K  @  
 �  @   �  @  
 �  @   �  @  
 q  @   u  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
   @     @  
   @     @  
 �  @   �  @  
 �  @   �  @  
 u  @   y  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 '  @   +  @  
 7  @   ;  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 s	  @   w	  @  
 �	  @   �	  @  
 �	  @   �	  @  
 �	  @   �	  @  
 
  @   
  @  
 -
  @   1
  @  
 =
  @   A
  @  
 b  @   f  @  
 r  @   v  @  
   @     @  
 #  @   '  @  
 �  �   �  �  
 F  @   J  @  
 V  @   Z  @  
 f  @   j  @  
 v  @   z  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 h  ^   l  ^  
 �  ^   �  ^  
 �  ^   �  ^  
 8  d   <  d  
 �  d   �  d  
 �  d   �  d  
   b     b  
 c  b   g  b  
 �  b   �  b  
 H媻8   �       F   H崐@   �       J   H崐@   �       D   @USVWATAUAVAWH峫$镠侅�   H�    H3腍塃�M孂M嬭H嬺H嬞H塎嘓塋$PH媫E3銲嬌�    劺uL�#辋   H荅�   W�E痜o
    fM緿坋E�   D坋覦塭譮D塭�)E逥坋顳塭驞坋鰽婫|塃廇媷�   塃揂媷�   塃桝媷�   塃汚秶�   圗珹媷�   塃烝禛x圗獻抢����I�繠�< u鯤嬜H峂    H�L岴廐峊$@H嬑�P(怘婽$@H呉uaL�#H婾荋凓v1H�翲婱疕嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚄  �    H嬅H婱�H3惕    H伳�   A_A^A]A\_^[]肐婨     )D$PA�   L岲$PI嬐�悁  E嬼E9   啠   A媷�   吚t~A嬈H�@H塋$P@ f�     I媷�   A孅H羚H<菼婨 H媝hH�I婳hH��PH媁H蠬荄$0    H塡$(H塗$ E嬏E嬈H婽$@I嬐�諥�腁媷�   D;郒婰$Pr汚�艵;穲   A�    俬���H媇嘔婨 A�    H婽$@I嬐�惃  I婨 I嬐�惛  L�#H岲$@H;豻H婦$@H�I嬏H塋$@�H婰$@H吷tL塪$@H��P怘婾荋凓啚��H�翲婱疕嬃H侜   倐��H兟'H婭鳫+罤兝鳫凐唅���    �   �   G   ?   j   �   �   ;   Q  "   `  i     N   �  $      �   9	  M F            �  '   �  l&        �donut::engine::CreateDDSTextureInternal  >{#   device  AK        0  AL  0     � (  AL P    �% > � K  >r#   commandList  AP        -  AU  -     �D 
 >Fv   info  AQ        *  AW  *     �C  >_   debugName  AM  @     s(  EO  (           D`  
 >`   desc  CK  8       �	 � CK 8   U    #  Dp    >�?    texture  AK      �  Z  D@    >u     arraySlice  An  �    H An P    � x >u     mipLevel  Al  $      Al �    u T   >蛌    layout  AM  �    S  AM P    �$ ? � [  M        �   �� M        )!  ��
 Z   �   M        �  �� N N N M        P  �� N M        �   b M          f

 N M        �  b M        �  b M          b N N N N M        �&  O N M        �  6� M        �  �,
 M          � N M          ,�$ M        9  )�' M        �  �.	
 >   _Ptr  AH  .      AJ  +      AH P      >#    _Bytes  AK  '    )  AK P    � � M        w  �7d >    _Ptr_container  AH  B      AJ  ?      N N N N N N M        �&  � N M        �  C偐 M        �  偐5 M          偐 N M          5偡 M        9  2偤 M        �  偭

 >   _Ptr  AH  �      AJ  �      AH P      >#    _Bytes  AK  �    -  AK P    � � M        w  偽d
 Z   S   >    _Ptr_container  AH  �      AJ  �      N N N N N N M        2  倶 M        H  倶CB
 >X    temp  AJ  �        AJ U    i M N N M        
!  倀CJ M        !  倎 N N M        w&  佢 N M        �&  佇 N Z   k&  �   �           @         A � h$   w  x  z  �  �  �  �  �             9  �  �  �  �  �  �    1  2  H  P        �   �   !  
!  )!  w&  �&  �&  �&  
 :�   O        $LN142  H  {#  Odevice  P  r#  OcommandList  X  Fv  Oinfo  `  _  OdebugName  p   `  Odesc  @   �?  Otexture  9	      �"   9�      �#   9�      鉼   9      �#   9a      �#   9n      |#   9�      /   O   �   �           �  x     �       � �C   � �O   � �b   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �
  � �  � �P   �x   ��   ��   ��   ��  	 �!   �5   �O  
 �g   �t   ��   �   \ F                                �`donut::engine::CreateDDSTextureInternal'::`1'::dtor$1 
 >`    desc  EN  p           >�?    texture  EN  @                                  �  O   �   �   \ F                                �`donut::engine::CreateDDSTextureInternal'::`1'::dtor$2 
 >`    desc  EN  p           >�?    texture  EN  @                                  �  O   ,   P   0   P  
 t   P   x   P  
 �   P   �   P  
 �   P   �   P  
 �   P   �   P  
 �   P   �   P  
 �   P   �   P  
 	  P   
  P  
 1  P   5  P  
 I  P   M  P  
 t  P   x  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
   P   "  P  
 .  P   2  P  
 S  P   W  P  
 c  P   g  P  
 "  P   &  P  
 2  P   6  P  
 B  P   F  P  
 c  P   g  P  
 s  P   w  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P     P  
 S  P   W  P  
 c  P   g  P  
 �  P   �  P  
 �  P   �  P  
 @  �   D  �  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 	  P   		  P  
 	  P   	  P  
 %	  P   )	  P  
 5	  P   9	  P  
 P	  P   T	  P  
 `
  `   d
  `  
 �
  `   �
  `  
 �
  `   �
  `  
 4  c   8  c  
 �  c   �  c  
 �  c   �  c  
 H崐p   �       <   H崐@   �       D   H塡$H塴$ WATAUAVAWH冹 禔xH嬮M嬸H�
    H猾*L嬧E3跠媿�   H�@媱�   L嵔�   塃H嬅I�M�H嬒I+蔋鏖L嬄I柳I嬂H凌?L繫;萻K�IM嬊I�翲嬜H嬎�    I塤雗voI婳H嬅I+蔋鏖H龙H嬄H凌?H蠰;蕍L岲$PI嬔I嬒�    �:M+萾#@ ff�     L�L塤L塤H兦I冮u隡嬊H嬜H嬒�    I�E3蹆綀    E嬰H塼$X喒  A�   fff�     嫊�   媫|嫕�   嫷�   A嬇H�@I�L�萀�<菻婰�L嬃M+罥柳I;衧
H菱I袸塛險vSI婫I+罤柳H;衯L岲$PI嬒�    E3跡峉�-I+衪$fff�     L�L塝L塝L塝H兞 H冴u鏘塐兘�    E嬎嗊    禘x兝蕛�
wdH�    H構寕    H�岷   ��   I嬅H�tH峅I嬄H灵H凒HG罬嬅H呟tH岾M嬄H灵H凒LG罤I嬓H�婨L嬅HH兝H凌H嬓HLA嬌H玲IH塓HL塹L騂�L堿M呬tM;�=H扬ID鶫央ID贖杨ID駻�罝;崒   �$���A�臘;瓐   sL嵔�   閈��3离I嬈H媡$XH媆$`H媗$hH兡 A_A^A]A\_�                                                        $   �   F   f    �   Q   �   Y   �   Q   �  X   �  �   �  �   �  �   �  �      �     �     �     �     �     �     �     �      �   $  �   (  �   ,  �      �   H
  K F            0     0  j&        �donut::engine::FillTextureInfoOffsets  >Fv   textureInfo  AJ          AN       � >#    dataSize  AK        5  AT  5     � >    dataOffset  AP        !  AV  !     � >u     arraySlice  Am      �
 >#     d  AL  E    � AL 0    � �
 >#     w  AM  9    � AM 0    �	 �
 >#     h  AI  ?    � AI 0    � � >Uv    sliceData  AW  W    �x  AW �      >u     mipLevel  Ai  �     Ai 0    �# � >蘶    levelData  AJ  w    a E   AJ 0    � � � T @L �  M        �&  
(
~'�� >   _Newsize  AQ  ?     � T  �   AQ 
    �I �@ M        �&  
(
$%D$b"%' Z   �&  �&   >紇    _Al  AW  Q     �x AW �      >    _Oldsize  AP  j       >磛    _Newlast  AI  �       AI     �8 � M        �&  ��? >#    _Count  AQ  �     3  >[z   _Backout  CM     �       CM    �       Y � M        '  ��
 Z   �&   N M        B'  �� M        b'  �� M        h'  �� M        m'  �� M        n'  �� N N N N N N N N M        g&  
 N M        x&  丱Nh >   _Newsize  AK  6    m 9 	 a 	  AK 0    � � � ] 5W � 2 M        �&  丱/%$b"+%-	
 Z   �&   >    _Oldsize  AP  _    n   0   AP 0    �/ n � % &f �  >$w    _Newlast  AK  r      AK 0    � � � ] 5W �  >    _Oldcapacity  AH  �    H  	  AH 0    � � � � �  M        �&  仯 >#    _Count  AK  �    *  AK 0    � � � ] 5W �  >6z   _Backout  CJ     �     & CJ    0    � d �  � T @L �  M        7'  伩 N N N N M        �&  丠 N M        w&  俻 N> M        h&  佮!%#E#ED'b# >#     numBytes  AK  J    5    >#     rowBytes  AH      �   4  �   AH 0    � � � D u �  >#     numRows  AP  7    2    AP C    * 
   >#     bpe  AK      D    >#     numBlocksWide  AH        AH (      >#     numBlocksHigh  AP  +      AP C    * 
   M        �  � N M        �  �4 N N             (          @ � h#   �  z  �  g&  h&  w&  x&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  '  '  '  '  '  	'  7'  B'  W'  ['  \'  b'  h'  m'  n'  p'  
             P   Fv  OtextureInfo  X   #   OdataSize  `      OdataOffset  O�             0  x     �       	 �   
 �   	 �!   
 �(    �2   	 �5    �?   
 �J    �  
 �0   �H   �O   �S   �W   ��   ��   �i  ! �p   �w   �{  # ��  % ��  ( ��  , ��  - ��  . ��  
 ��  & ��  2 ��  3 �,   M   0   M  
 w   M   {   M  
 �   M   �   M  
 �   M   �   M  
 �   M   �   M  
 �   M   �   M  
 �   M   �   M  
   M     M  
 0  M   4  M  
 @  M   D  M  
 `  M   d  M  
 p  M   t  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M     M  
   M     M  
 7  M   ;  M  
 K  M   O  M  
 �  M   �  M  
 �  M   �  M  
 )  M   -  M  
 =  M   A  M  
 `  M   d  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
    M     M  
   M     M  
 0  M   4  M  
 H  M   L  M  
 �  M   �  M  
 �  M   �  M  
   M   
  M  
   M     M  
 M  M   Q  M  
 a  M   e  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M     M  
 �  M   �  M  
   M     M  
   M   #  M  
 Q  M   U  M  
 e  M   i  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �	  �   
  �  
 \
  M   `
  M  
 H塡$H塴$H塼$H墊$ AVH冹 H嬞H婭hH��P絸   H;�偡  H婯hH��P�8DDS 叀  H婯hH��PH孁儀|厞  儀L 厐  2繢島鯣Pt�TDX10uH婯hH��PI;�俋  �劺�   婫塁|IE顙G墐�   婫吚莾�      E惹儓      墜�   鯣Pt)婫T=DX10ut嫍�   冣嬍冮t冮t冮t凒t3覊SH嬒鯣P窽   刋  �<DX10匥  兛�    労  嫃�   H�    H�0  9HtH兝H;聈螂姬���齯熀   霘� 圕x禖x劺剄  �粧    tG兝韮�/w?H�    H�秳    媽�    H�崞Cx�艭x�艭x7�艭x9�
艭x;�艭xC嫃�   冮td冮t#凒�  鱃  � 匊   婫墐�   �	雃鰢�   媷�   t�@缐儓   兛�   椑�=墐�   兛�   椑�)鯣t
兓�   厽   莾�      兛�   椑H嬘箰   H央Y稉�   H峅L�    圕x劺te鱃  � t
婫墐�   �	�%婫p亨	s% �  = �  u:莾�      ���H崜�   �H婯hH��PL嬇H嬎H嬓�    H吚t��2繦媆$0H媗$8H媡$@H媩$HH兡 A^�                              ;  f    B  f    �  �   �  {   �  |   |  L   �  M     }     ~          �     �      �   $  �      �   �  M G            X     X  k&        �donut::engine::LoadDDSTextureFromMemory  >Fv   textureInfo  AI       ;�  AJ          >    dataOffset  AN  �     �Q  C       ,     j  C      �     b E >0     bDXT10Header  A   n     2    >0v    <begin>$L0  AH  ?    -    AH o     ' M        i&  ��Fj)&
U
 >癊    mode  A   �       A      	C *tu  N Z   f&  j&                         @  h   i&  �&  
            
                    $LN26         $LN25         $LN24         $LN23         $LN22         $LN21  0   Fv  OtextureInfo  9$       鋟   9<       鉼   9R       鉼   9�       鋟   9�      鋟   O  �   8          X  x  D   ,      6 �   7 �5   < �?   = �K   B �X   E �l   L �n   M ��   Q ��   V ��   Y ��   _ ��   ` ��   a ��   b �	  d �%  i �2  p �K  n �T  p �V  b �i  r �o  w �{  } ��   ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �
  � �  � �$  � �&  � �,  � �8  � �:  � �M  � �W  � �c  � �p  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  H ��  � �,   ?   0   ?  
 y   ?   }   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 	  ?   
  ?  
 2  ?   6  ?  
 F  ?   J  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  {   �  {  
   |     |  
   �     �  
 #  �   '  �  
 4  �   8  �  
 E     I    
 V  ~   Z  ~  
 g  }   k  }  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 H塡$ UVWATAUAVAWH崿$ ���H侅   )�$�  H�    H3腍墔�   M嬥L塃楬嬟H塙怢嬹H塎℉塋$XW�3�u�u�)u�)u�u�u )uH塽 塽(W�3繦塂$8D$@塂$PI� I嬋�P H孁H塃犌E皘   荅�  � 塃紜G塃笅G塃膵G塃惹E�    荅�   荅 DX10禬凓	w&L�    A媽�    I�崆D$D   �荄$D   婳塋$L����w斧崃�塗$L僉$HH�    禣H�0  fD  8tH兝H;聈髬D$@�婡塂$@吚uI�6I塿閐  H塽0H荅8    H峂@�    W�EXH荅h    H荅p   艵X ExH菂�       H菂�      艵x 厴   H菂�       H菂�       茀�    菂�      菂�      菂�      菂�      菂�      f菂�     茀�    H菂�       f呅   禛垍�   婫墔�   �墔�   婫墔�   婫墔�   禛垍�   婫墔�   3褹笖   H峂0�    L孁H塂$XH嬋�    L嬭� DDS (E�@(M�Hp$p4(E�@D(M HTpd�E �@t塸|D$@��   H婦$8A墔�   3覌驂T$09W�)   D嫷�   D孃� 嗿   嬈H�@H��    H塃坒f�     H荄$`    塗$hH荄$l����荄$t����塼$|D墊$xH塗$8H�H峀$8H塋$ A�L岲$`I嬙H嬎�PPL嬥H媿�   A�H羚H婨圚<E咑t;3跘嬾 L�I嬋HHOH婽$8HI訧丸    H�肏冾u誋媇悑t$0H�L媏業嬙H嬎�PXA�茿嬈A嬑验A�   凐DC馠媫燚;�    �"����茐t$0;w傘��L媢↙媩$X�(   �    H嬝H塂$XH吚t/W� 茾   茾   H�    H�H岾M嬊I嬚�    �3跧荈    H岰I�I塣H崓�   �    H嫕�   H呟t,����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH峂0�    I嬈H媿�   H3惕    H嫓$X  (�$�  H伳   A_A^A]A\_^]脨                                        *   �   �   �   �   �   +  f    6  f    {  B   ~  M   �  %   �  j   ,  !   T  U   f  K   �  H   �  E   �  i     �     �     �     �     �     �     �      �   $  �   (  �      �   
  L G            ,  8   ,  t&        �donut::engine::SaveStagingTextureAsDDS  >{#   device  B�   F     � AI  B     �X5  AK        B  AI �      >�"   stagingTexture  B�   ?     � AP        ;  AT  ;     �B]  >#     dataSize  AW  �     { & AW      �
 � BX   �    � B�  �    _  >Jv    textureInfo  D0   ><v   dx10header  C       Q    	    C      Z    w% O D@   
 >p    data  AU  �     AU �    8  B�   �    _  >6v    header  D�    >U    textureDesc  AM  �     �x  AM �    ;  B�   �     g >0v    <begin>$L0  AH  /    ' "   >u     arraySlice  A   �    �� 6  A  �    3  B0   �     >u     height  An       An        >u     mipLevel  Ao  
     Ao      
  >蛌    subresourceData  AM  �    l  >#     rowPitch  B8         >_    sliceData  AT  }    ]  >r    slice  D`    >     destOffset  AJ  �        >     srcOffset  AK  �      M        �&  � M        �&  � M        �&  � N N N M        p&  伩 M        q&  �伩 N N M        �   仛 M          仦+ N M        �  仛 M        �  仛 M          仛 N N N N M        �   亗 M          亞( N M        �  亗 M        �  亗 M          亗 N N N N M        4  乯 N M        �&  乛 M        q&  �乛 N N M        w&  儑 N M        �&  儅 N M        �&  8剭 M        �&  剭, M        �  剻
 >Z&   this  AI  �    :  AI �      M        �  劜	
 N N N N M        �&  刵 M        �&  剒 N M        q&  �
刵 N N M        �&  �&.6
 Z   �   >hy    _Rx  AI  3    a  BX   8    �  M        �&  凘 M        �  	凜 N M        3'  刜
 Z   z'   N N N Z   N  j&  r#              8         A � h)   �  z  �  �  �         �  �  �      4  �   p&  q&  s&  u&  v&  w&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  3'  
 :�  O 
                    $LN168         $LN18         $LN16  H  {#  Odevice  P  �"  OstagingTexture  0  Jv  OtextureInfo  @   <v  Odx10header  �   6v  Oheader  8   #   OrowPitch  `   r  Oslice  9�       -5   ^�     p    9w      �"   9�      �"   ^+     gy   9�      [&   9�      [&   O �   �          ,  x     �         �U   ! �}   " ��   # ��   % ��   & ��   ' ��   ( ��   ) ��   * ��   + ��   , ��   - ��   / ��   3 ��   4 ��   : �  K �
  L �  N �#  O �(  R �/  T �D  R �M  T �S  V �Z  [ �^  ^ �j  a � 
        n  6  �x        v  a ��	     L   �  A  ��  B  ��  C  ��  D  ��  E  �  F  �  G  �  I  �x  "       a �1  b �;  c �D  d �L  e �U  f �^  g �h  h �q  n ��  p ��  q ��  r ��  s ��  v �   y �  { �N  ~ �R   �W  � �\  � �}  � ��  � ��  ~ ��  � ��  � ��  � ��  � ��  � ��  { ��  � ��  { �  v �&  � ��  � ��     [ F                                �`donut::engine::SaveStagingTextureAsDDS'::`1'::dtor$1  >Jv    textureInfo  EN  0          ><v    dx10header  EN  @           >6v    header  EN  �           >r    slice  EN  `                                  �  O �     [ F                                �`donut::engine::SaveStagingTextureAsDDS'::`1'::dtor$8  >Jv    textureInfo  EN  0          ><v    dx10header  EN  @           >6v    header  EN  �           >r    slice  EN  `                                  �  O �     \ F                               �`donut::engine::SaveStagingTextureAsDDS'::`1'::dtor$10  >Jv    textureInfo  EN  0          ><v    dx10header  EN  @           >6v    header  EN  �           >r    slice  EN  `                                 �  O,   A   0   A  
 s   A   w   A  
 �   A   �   A  
 �   A   �   A  
 �   A   �   A  
 �   A   �   A  
 �   A   �   A  
 �   A   �   A  
   A     A  
 +  A   /  A  
 ?  A   C  A  
 O  A   S  A  
 �  A   �  A  
 �  A   �  A  
 �  A   �  A  
 �  A   �  A  
 �  A   �  A  
 8  A   <  A  
 L  A   P  A  
 \  A   `  A  
 �  A   �  A  
 �  A   �  A  
 �  A   �  A  
 �  A   �  A  
 �  A   �  A  
 �  A     A  
 "  A   &  A  
 2  A   6  A  
 \  A   `  A  
   A   �  A  
 �  A   �  A  
 �  A   �  A  
   A     A  
 �  A   �  A  
 �  A   �  A  
 Y  A   ]  A  
 i  A   m  A  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �	  A   �	  A  
 �	  A   �	  A  
 �	  A   �	  A  
 �	  A   �	  A  
 �	  A   �	  A  
 �	  A   �	  A  
 
  A   
  A  
 $
  A   (
  A  
 �  a   �  a  
 ;
  a   ?
  a  
 d
  a   h
  a  
 �
  a   �
  a  
 �
  a   �
  a  
    e     e  
 _  e   c  e  
 �  e   �  e  
 �  e   �  e  
 �  e   �  e  
 $  _   (  _  
 �  _   �  _  
 �  _   �  _  
 �  _   �  _  
 �  _   �  _  
 H崐0  �       D   H崐0  �       J   @UH冹 H嬯�(   H婱X�    H兡 ]�   "   H吷tH��   H�`�   �   �   Z G                      �&        �std::_Ref_count_obj2<donut::vfs::Blob>::_Delete_this 
 >hy   this  AJ                                 @�     hy  Othis  9
       ly   O�   0              0     $       C �    D �   E �,   T   0   T  
    T   �   T  
 �   T   �   T  
 �   T   �   T  
 H婣H兞3襀�    �     V G            
       
   �&        �std::_Ref_count_obj2<donut::vfs::Blob>::_Destroy 
 >hy   this  AJ          M        �&   
 >�y   _Obj  AJ         N                        @� 
 h   �&      hy  Othis  9
       倅   O �   (           
   0            ? �    @ �,   S   0   S  
 {   S      S  
 �   S   �   S  
 �   S     S  
   S     S  
 3烂   �   �   H G                      �        �std::_Ref_count_base::_Get_deleter 
 >e&   this  AJ          D    >�%   __formal  AK          D                           @�     e&  Othis     �%  O__formal  O�   0              0     $       � �    � �   � �,   >   0   >  
 m   >   q   >  
 �   >   �   >  
 �   >      >  
 H冹HH峀$ �    H�    H峀$ �    �
   3      2      f      �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (                            J �   K �,   9   0   9  
 �   s   �   s  
 �   9   �   9  
 H冹(H�
    �    �   K      &      �   w   7 G                     �        坰td::_Xlen_string 
 Z   6   (                      @        $LN3  O �   (              h            		 �   
	 �,   :   0   :  
 s   u   w   u  
 �   :   �   :  
 H冹(H�
    �    �   R      &      �   �   � G                     '        坰td::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >::_Xlength 
 Z   6   (                      @        $LN3  O   �   (              �            a �   b �,   O   0   O  
 �   �   �   �  
 �   O   �   O  
 H冹(H�
    �    �   R      &      �   b  "G                     '        坰td::vector<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> >,std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > > >::_Xlength 
 Z   6   (                      @        $LN3  O  �   (              �            a �   b �,   I   0   I  
 ^  �   b  �  
 x  I   |  I  
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   k   �   !   �   !   �   j   ,  "   O  :   U  9   [  $      �   '  r G            `     `  �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >h   _Ptr  AK          AW       D/  >   _Count  AL       G4  AP         B M        �&  E
(?SD3$--K
 Z   �   >#     _New_capacity  AH  �     �  * N  V r  AM  O     =  b �  AH �     G  ,  AJ �       M        �&  �� M        "   �� N N M        t  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M          ��?�� >   _Count  AJ  �      * M        �  ��

*%
u- M        �  ��	)
��
 Z   k   >    _Block_size  AJ  �     �  �  AJ �       >    _Ptr_container  AH  �       AH �     }  b 
 >0    _Ptr  AV  �       AV �     ~ V "  M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N N N N M        �  X(  M          X' >    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M          -�W M        9  �&P M        �  �
)/
 Z   �  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        w  
�#
2
 Z   S   >    _Ptr_container  AP        AP +    4  *  >    _Back_shift  AJ      
  AJ Z      N N N N N M        !  L4 N M        �  $# >p    _Result  AM  '       AM 8      M          ' N N                       @ n h   v  w  x  �  �  �         !  "  9  �  �  �  �  �    t  u  �  �    7  �&  �&         $LN93  @   �  Othis  H   h  O_Ptr  P     O_Count � 6y  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_66f57f934f28d61049862f64df852ff0>  O �   h           `  h  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   ;   0   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;     ;  
   ;     ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 ]  ;   a  ;  
 m  ;   q  ;  
 �  ;   �  ;  
 Y  ;   ]  ;  
 m  ;   q  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 W  ;   [  ;  
 |  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
    ;   $  ;  
 0  ;   4  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 a  w   e  w  
 <  ;   @  ;  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   "   <   $      �   o  g G            A      A   '        �std::allocator<donut::engine::TextureSubresourceData>::deallocate 
 >緒   this  AJ          AJ ,       D0   
 >$w   _Ptr  AK        @ /   >   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        w  
#

 Z   S   >    _Ptr_container  AJ       (    AJ ,       >    _Back_shift  AH         AH ,       N N (                      H  h   w  �         $LN18  0   緒  Othis  8   $w  O_Ptr  @     O_Count  O �   8           A   �     ,       � �   � �2   � �6   � �,   N   0   N  
 �   N   �   N  
 �   N   �   N  
 �   N   �   N  
 �   N   �   N  
 )  N   -  N  
 J  N   N  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 /  �   3  �  
 �  N   �  N  
 H冹(H嬄K�@H菱H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   "   =   $      �   �  � G            B      B   �&        �std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > >::deallocate 
 >顆   this  AJ          AJ 0       D0   
 >磛   _Ptr  AK          >   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        w  
#

 Z   S   >    _Ptr_container  AJ       %    AJ 0       >    _Back_shift  AH          AH 0       N N (                      H  h   w  �         $LN18  0   顆  Othis  8   磛  O_Ptr  @     O_Count  O�   8           B   �     ,       � �   � �3   � �7   � �,   G   0   G  
 �   G   �   G  
 �   G   �   G  
   G     G  
 )  G   -  G  
 j  G   n  G  
 �  G   �  G  
 �  G   �  G  
 �  G   �  G  
   G   "  G  
 .  G   2  G  
 p  �   t  �  
 �  G   �  G  
 H婹H�    H呉HE旅   &      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   +   0   +  
 _   +   c   +  
 �   +   �   +  
  d T 4 2p    H           l      l      �    20    2           m      m      �   
 
4 
2p    B           n      n      �    20    <           o      o      �   
 
4 
2p    B           p      p      �    20    <           q      q      �   
 
4 
2p    B           r      r      �    �                  t      t      �    B                 v      v      �    T
 4	 2�p`    [           x      x      �   ! �     [          x      x      �   [   8          x      x      �   !       [          x      x      �   8  T          x      x      �   !   �     [          x      x      �   T  `          x      x           20    `           z      z         
 t	 d T 4 2�    X          �      �         '
 ! 
��	��p`0P        �      h             �          �      �         (                        p2    �b    ��       F      D      J      #   ���| _ u @TcwOa8
 'h 4K @ ���
�p`P          �      h   $   *       ,          �      �      $   (           -      0   
    �B    �>       D   	   J      _   M �
�l�  2P               _      _      3   �� B             g      B       "           �      �      <   h           E      H          #   2 20           g      Q       �           �      �      K   h           T      W          #   � 20               �      �      Z   ! t               �      �      Z      E           �      �      `   !                 �      �      Z   E   K           �      �      f   - B      B           �      �      o    20    �           �      �      u    B                 �      �      {   
 
4 
2`    "           �      �      �   ! t     "          �      �      �   "   X           �      �      �   !       "          �      �      �   X   j           �      �      �   .@
 T
 4 2���
�p              M      M      �   ! d              M      M      �     0          M      M      �    B      A           �      �      �    B                 �      �      �   '
  
��	��p`0P        �      h      �       �          P      P      �   (           �      �   
    �2    �f       <      D      #   � �0� n	"  4 2p               �      �      �   ! d               �      �      �      |           �      �      �   !                 �      �      �   |   }           �      �      �   !   d               �      �      �   }   �           �      �      �    20    +           �      �      �    2�
�p`0           g      �       y          �      �      �   8               �      �   	   �            �   �       \   = �I 
 
2P    (           \      \      �     R���
�p`0           g      �       6          �      �      �   8                        	                  �       ]   ! �Q  BP0      =           ]      ]          
 
4 
2p    0           �      �          B      :           �      �                                     X      -      +   Unknown exception                             d      1      +                               p      7      +   bad array new length                                4      5                                 ;      A      G                   .?AVbad_array_new_length@std@@     H               ����                      8      5                   .?AVbad_alloc@std@@     H              ����                      >      /                   .?AVexception@std@@     H               ����                      D      )   string too long     ����    ����        ��������vector too long                                             �      S      T      V       >                                         D      [      X                         ^                   a               ����    @                   D      [                                         >      g      d                         j                           m      a              ����    @                   >      g                                         8      s      p                         v                                   y      m      a              ����    @                   8      s                   .?AV_Ref_count_base@std@@     H                         �                   �               ����    @                   |                                               �      �      �                   .?AV?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@     H                         �                           �      �              ����    @                   �      �                     �   -   + 
'v        donut::engine::dds::DDSPF_DXT1                 
    �   -   + 
'v        donut::engine::dds::DDSPF_DXT2                 
    �   -   + 
'v        donut::engine::dds::DDSPF_DXT3                 
    �   -   + 
'v        donut::engine::dds::DDSPF_DXT4                 
    �   -   + 
'v        donut::engine::dds::DDSPF_DXT5                 
    �   2   0 
'v        donut::engine::dds::DDSPF_BC4_UNORM                
    �   2   0 
'v        donut::engine::dds::DDSPF_BC4_SNORM                
    �   2   0 
'v        donut::engine::dds::DDSPF_BC5_UNORM                  
    �   2   0 
'v        donut::engine::dds::DDSPF_BC5_SNORM      #       #   
    �   2   0 
'v        donut::engine::dds::DDSPF_R8G8_B8G8      &       &   
    �   2   0 
'v        donut::engine::dds::DDSPF_G8R8_G8B8      )       )   
    �   -   + 
'v        donut::engine::dds::DDSPF_YUY2       ,       ,   
    �   1   / 
'v        donut::engine::dds::DDSPF_A8R8G8B8       /       /   
    �   1   / 
'v        donut::engine::dds::DDSPF_X8R8G8B8       2       2   
    �   1   / 
'v        donut::engine::dds::DDSPF_A8B8G8R8       5       5   
    �   1   / 
'v        donut::engine::dds::DDSPF_X8B8G8R8       8       8   
    �   /   - 
'v        donut::engine::dds::DDSPF_G16R16     ;       ;   
    �   /   - 
'v        donut::engine::dds::DDSPF_R5G6B5     >       >   
    �   1   / 
'v        donut::engine::dds::DDSPF_A1R5G5B5       A       A   
    �   1   / 
'v        donut::engine::dds::DDSPF_A4R4G4B4       D       D   
    �   /   - 
'v        donut::engine::dds::DDSPF_R8G8B8     G       G   
    �   +   ) 
'v        donut::engine::dds::DDSPF_L8     J       J   
    �   ,   * 
'v        donut::engine::dds::DDSPF_L16    M       M   
    �   -   + 
'v        donut::engine::dds::DDSPF_A8L8       P       P   
    �   1   / 
'v        donut::engine::dds::DDSPF_A8L8_ALT       S       S   
    �   +   ) 
'v        donut::engine::dds::DDSPF_A8     V       V   
    �   -   + 
'v        donut::engine::dds::DDSPF_V8U8       Y       Y   
    �   1   / 
'v        donut::engine::dds::DDSPF_Q8W8V8U8       \       \   
    �   /   - 
'v        donut::engine::dds::DDSPF_V16U16     _       _   
    �   -   + 
'v        donut::engine::dds::DDSPF_DX10       b       b   
    �   (   & 
34        std::exception::`vftable'    #      #  
    �   (   & 
34        std::bad_alloc::`vftable'    )      )  
    �   3   1 
34        std::bad_array_new_length::`vftable'     ,      ,  
    �   @   > 
S4        std::_Ref_count_obj2<donut::vfs::Blob>::`vftable'    U      U  
 �+鷯8}`Бl唋d��8cI橗cS�?妓c蹧楀_8鸆O��顨龤创咶矣眸�:'q鉠0皽贷�P劀>��8T曺�ぐ�窺 z繺�)%UCnWy|苪hw只7X�85'繵�8咊傄*dU夲�D�
�*(菥E鮖Ac%儇n
拥姣鹕摆>H9鍓兰稥�戍泐W室�1F�莑I"M耵�2cL�(弻>	蠌P|閮�躥鼳�& 椶#j飑>龌焋ml8�'畊蔰y�.h�K蜌�(閏烌s{A"R�娥(|�/愀 �H;{湇k墡膭焤E蚮?|y屢醨礟�
d戮翁rJ粖n鯔#囁鐙^P仼h�0Z�z嚤踖p禭c�&敡?諭嚤踖p禭9"虰錱6萪O�秙_萡劔K霵婬(=*眢掔'項j�8粻尳貴k{pS�+�谲�$愜w獛啯v��%�;6犪貓xb遤c梙
� 仲S槩�N皮刎�$甿戚釡齉疡頢>蜀}�:Q蚴0k浾芒R�8k端祆癜~tN邃鐞倓嶀預棊膬�屓绀?貚犷A棊膬闗�7来aFql#�'��臆箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>~;A嬰t舘� �蹰k~�轖	hf挑#KN贘�8�w�	 G:�#乩敚0�3铘?#h塹鞇炟k;�(� �3�;�(� �3襺恏吡mr燄:臞e+倧A糲��;�(� �3噎Y^D0◣}�枩:*麝�蕈鰑晩+:戳鍣!廕`N賰*I荏6�飪�飪�)�=T剏N躔7颦硣KlBj孚�/％旔k癙穇��t!hK�$蟊惺舂櫅﹛�?
%I栶賑?Tb�7闵醡f]{謑p+嚅2�繱f]{謑p9仰 劬囧f]{謑p1�昔俍悮丽Υ馘顫U疐�Z
 砌&�V铒I� 鰡挤羻�`I斘婉nN鵘J恸互佞f鲄F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这朸橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 夒嘕-WV8o;き8乿滑嶕偌�验�=眼袻姢氞o	惊#judd�a�:1G{媖Y=刔76�炼/jn_]=醅劼Qzdd�a�:V�c<�?On懲�&樢閣yQ E<礼\<p7B��5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D潦薒C顓鹴aR�,F_棢杻#Qs刉咻3擬雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛-坓�(鬄酲;[純o穗嘕-WV8o_艮�:_�-坓�(鬄�汬'这枅^裗呔屸�垒��劈�4嵺�緑*�H$P蒿脛顧鱓
�2鋝樳孊W0⒓c+捀憰>B旿怤y懄"�2鱵q�8函簴�漢-坓�(鬄鮳�>i,夿-坓�(鬄�汬'这杤�$�1#舮娝澟旒dd�a�:堥P拇驵太h\�6弝雕
аs1
蒴W�$峾$J-h苪{ b�@佑&湱喫灧NU盓ft7婼)+^�!炟鏝i壊")G嘕-WV8o额	hQ�)v暕妝�#(�6$姆RS,y*�杜`颀l+�鞯.r擣�0G#盱谑4s櫆镇(��苳乮5絚_}4n4�硓樝0;a�l珌棪�2y*�杜`颀l+�鞯.r擣�0G#盱谑鲿;�臖\呋s;嗐8�1�8]Z齨4�硓�9E\$L釉��E光-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H�%G>禡h�,4��;儗�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2淿H�!T愥M%ZZ�$为赞G刹~赣 "^惋砤刃5]_иq<	崡圦U櫘y瀹�4,� 牤Z�-3z蕨
浃e匽蕣H矾場彈y闱獥s}�� 囷��v纏ξ曭誝X凁�钹勦魿KS3檀�o�$ u�"u�r哯荛耫�緯M2鰀cA`o翸鏬引0%7"�鋙Y6�3蕳,怷奛mCE�q躪r犏鐏粉$X�QY坃堕棼c;J�6�隔敳z�=懩淣銗N�s缊H;既绻o&�!阑,�粰趭+�揃T爃.L�(_l(�蕶        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       � 
             .debug$T       p                 .rdata                 :逷                    .rdata                 賋Np         :           .rdata                 G迶�         p           .rdata                 Xs�         �           .rdata                 佦�         �           .rdata      	           B�               	    .rdata      
           m�	         M      
    .rdata                 k萰�         �          .rdata                 W         �          .rdata      
           Q纻�         �      
    .rdata                 =}         9          .rdata                 �<W\         t          .rdata                 b         �          .rdata                 劮蒒         �          .rdata                 纲�                   .rdata                 (丝�         X          .rdata                 浟�         �          .rdata                 蒜l�         �          .rdata                 馕`                   .rdata                 _�_�         <          .rdata                 �()         v          .rdata                 貀R�         �          .rdata                 彭�         �          .rdata                 客b}                   .rdata                 (�          M          .rdata                 z         �          .rdata                 6��         �          .rdata                 YN氆         �          .rdata                  g玀�         +           .rdata      !           `覞         c      !    .rdata      "   l      qW鑞          �      "        �  0   "        �  `  "    .text$mn    #   :      眡�     .debug$S    $            #    .text$mn    %   0      燥"V     .debug$S    &   �         %    .text$mn    '   �      �54�     .debug$S    (   (  "       '    .text$mn    )   y     檸p)     .debug$S    *   �  X       )    .text$x     +   (      纥吨)    .text$mn    ,   6  	   鮌'A     .debug$S    -     `       ,    .text$x     .   =      &��,    .text$mn    /   <      .ズ     .debug$S    0   0  
       /    .text$mn    1   <      .ズ     .debug$S    2   L  
       1    .text$mn    3   !      :著�     .debug$S    4   <         3    .text$mn    5   2      X于     .debug$S    6   <         5    .text$mn    7   "       坼	     .debug$S    8   �         7    .text$mn    9         峦諡     .debug$S    :            9    .text$mn    ;   K       }'     .debug$S    <   �         ;    .text$mn    =   �      s,_     .debug$S    >   d         =    .text$mn    ?   �      �&^     .debug$S    @   X  2       ?    .text$mn    A   j      慩蜯     .debug$S    B   �         A    .text$mn    C   `      ,     .debug$S    D   �         C    .text$mn    E          .B+�     .debug$S    F   �          E    .text$mn    G         ��#     .debug$S    H   �          G    .text$mn    I         ��#     .debug$S    J   �          I    .text$mn    K   +      n鯬7     .debug$S    L   �          K    .text$mn    M   B      贘S     .debug$S    N             M    .text$mn    O   B      贘S     .debug$S    P            O    .text$mn    Q   B      贘S     .debug$S    R   �          Q    .text$mn    S   H       襶.      .debug$S    T   �         S    .text$mn    U   	     x捕     .debug$S    V             U    .text$mn    W   �  
   �$狰     .debug$S    X   �  �       W    .text$x     Y         y-�#W    .text$x     Z         鲉k�W    .text$x     [         鲉k�W    .text$mn    \   �     KY�*     .debug$S    ]   �  d       \    .text$x     ^         %FZ甛    .text$x     _         鲉k�\    .text$mn    `   0     疻{-     .debug$S    a   d  l       `    .text$mn    b   X     &鋠�     .debug$S    c   $  2       b    .text$mn    d   ,     C篲     .debug$S    e      �       d    .text$x     f         糆b糳    .text$x     g         糆b糳    .text$x     h         �刀d    .text$mn    i          c淖�     .debug$S    j            i    .text$mn    k   
       肷瞰     .debug$S    l   @  
       k    .text$mn    m          �猴     .debug$S    n   ,         m    .text$mn    o          aJ鄔     .debug$S    p   �          o    .text$mn    q         �ッ     .debug$S    r   �          q    .text$mn    s         �ッ     .debug$S    t            s    .text$mn    u         �ッ     .debug$S    v   �         u    .text$mn    w   `     匮�5     .debug$S    x   �  B       w    .text$mn    y   A      o漮     .debug$S    z   �         y    .text$mn    {   B      lA     .debug$S    |   �         {    .text$mn    }         崪覩     .debug$S    ~   �          }        (      S        D               S               c               s           malloc               �               �               �               �      5              I              }        =      Q        \          i,                   {      /        �      M        �          i0                   �      3        �      G        $	      1        P	      O        z	          i6                   �	      o        �	      q        �	      w        >
      C        \
      E        |
      m        �
      b        �
      W        �      d                       H               r      7        �      ?        �      ;        �      {              =        	      u              A        ;               \      U        �      `        �      y        k      s        �      \        x      '              9        D      k              i        �      K        �          iU                   0      )        �      ,        5      %        �      #        
      +        �      .        /      Y        �      h        q      ^              f        �      Z        P      _        �      [        �      g        /               B               U               j           memcpy           memmove          $LN13       S    $LN5        5    $LN10       Q    $LN7        /    $LN13       M    $LN10       1    $LN16       O    $LN3        o    $LN4        o    $LN3       q    $LN4        q    $LN93   `  w    $LN100      w    $LN37   `   C    $LN40       C    $LN76   (  b    $LN77     b    $LN21   �  b    $LN22   �  b    $LN23   �  b    $LN24   �  b    $LN25   �  b    $LN26   �  b    $LN27   �  b    $LN112      b    $LN269  �  W    $LN272      W    $LN170    d    $LN16   �   d    $LN18   �   d    $LN168  ^  d    $LN174      d    $LN10       7    $LN70   �   ?    $LN73       ?    $LN18       ;    $LN18   B   {    $LN21       {    $LN28   �   =    $LN31       =    $LN3       u    $LN4        u    $LN20       A    $LN76   �  U    $LN77   �  U    $LN50   u  U    $LN51   x  U    $LN52   {  U    $LN53   ~  U    $LN54   �  U    $LN55   �  U    $LN56   �  U    $LN57   �  U    $LN4    
  U    $LN118  �  `    $LN70     `    $LN74     `    $LN18   A   y    $LN21       y    $LN3       s    $LN4        s    $LN142  �  \    $LN48   �   '    $LN52       '    $LN8        K    $LN103  y  )        �  
   +    $LN107      )    $LN125  6  ,        G      .    $LN129      ,    $LN4        %    $LN14   :   #    $LN17       #    .xdata                F┑@S        �!          .pdata      �         X賦鶶        �!      �    .xdata      �          （亵5        �!      �    .pdata      �          T枨5        "      �    .xdata      �          %蚘%Q        3"      �    .pdata      �         惻竗Q        Z"      �    .xdata      �          （亵/        �"      �    .pdata      �         2Fb�/        �"      �    .xdata      �          %蚘%M        �"      �    .pdata      �         惻竗M        �"      �    .xdata      �          （亵1        #      �    .pdata      �         2Fb�1        R#      �    .xdata      �          %蚘%O        �#      �    .pdata      �         惻竗O        �#      �    .xdata      �          懐j瀘        �#      �    .pdata      �         Vbv鵲        $      �    .xdata      �          �9�q        G$      �    .pdata      �         �1皅        h$      �    .xdata      �          蔜-鍂        �$      �    .pdata      �         愶Lw        �$      �    .xdata      �         �qL僿        I%      �    .pdata      �         ~蕉絯        �%      �    .xdata      �         |眞        
&      �    .pdata      �         瞚挨w        o&      �    .xdata      �         S!熐w        �&      �    .pdata      �         �o坵        3'      �    .xdata      �          （亵C        �'      �    .pdata      �         粻胄C        �'      �    .xdata      �          U费耣        �'      �    .pdata      �         铗+穊        *(      �    .xdata      �   $      *髎襑        s(      �    .pdata      �         交m匴        !)      �    .xdata      �   	      � )9W        �)      �    .xdata      �         r髑轜        ~*      �    .xdata      �          苊F
W        4+      �    .voltbl     �          �W    _volmd      �    .xdata      �   ,      毷g萪        �+      �    .pdata      �         綳狳d        i,      �    .xdata      �   	      � )9d        �,      �    .xdata      �         箪鳸d        t-      �    .xdata      �          ?-|d        .      �    .xdata      �          k筪        �.      �    .pdata      �         �$剧d        /      �    .voltbl     �          鼪d    _volmd      �    .xdata      �         /
�7        �/      �    .pdata      �         +eS�7        �/      �    .xdata      �   	      �#荤7        $0      �    .xdata      �         j7        `0      �    .xdata      �          3狷 7        �0      �    .xdata      �         蚲7M?        �0      �    .pdata      �         沀啠?        
1      �    .xdata      �   	      �#荤?        ;1      �    .xdata      �         j?        l1      �    .xdata      �          �+-�?        �1      �    .xdata      �          （亵;        �1      �    .pdata      �         � �;        2      �    .xdata      �         范^�;        C2      �    .pdata      �         鳶�;        |2      �    .xdata      �         @鴚`;        �2      �    .pdata      �         [7�;        �2      �    .voltbl     �          飾殪;    _volmd      �    .xdata      �          �9�{        '3      �    .pdata      �         惻竗{        54      �    .xdata      �          （亵=        B5      �    .pdata      �         駷tL=        R6      �    .xdata      �          �9�u        a7      �    .pdata      �         �1皍        v8      �    .xdata      �          �搀A        �9      �    .pdata      �         +eS籄        �9      �    .xdata      �         0�-馎        �9      �    .pdata      �         rA        :      �    .xdata      �         菑A        ?:      �    .pdata      �         亹\{A        m:      �    .voltbl     �          糎踼A    _volmd      �    .xdata      �          7`        �:      �    .pdata      �         e4哵`        �:      �    .xdata      �         j`鏯        2;      �    .pdata      �         祕`        ;      �    .xdata      �          �9�y        �;      �    .pdata      �         s�7鍄        K<      �    .xdata      �          �9�s        �<      �    .pdata      �         �1皊        O=      �    .xdata      �   $      偞j        �=      �    .pdata      �         �!J廫        k>      �    .xdata      �   	      � )9\        ?      �    .xdata      �         �\        �?      �    .xdata      �          锎_P\        9@      �    .xdata      �          �2耈'        褸      �    .pdata      �         � �'        rB      �    .xdata      �         �)<�'        D      �    .pdata      �         �k�'        睧      �    .xdata      �         @鴚`'        SG      �    .pdata      �         4H'        鬑      �    .xdata      �         Ty飺'        旿      �    .pdata      �         #Qg'        6L      �    .xdata      �          （亵K        譓      �    .pdata      �          ~        N      �    .xdata      �         啄qJ)        XN      �    .pdata      �         乭)        O      �    .xdata      �   
      B>z])        軴      �    .xdata      �          �2g�)              �    .xdata      �         T�8)        mQ      �    .xdata      �         r%�)        0R      �    .xdata      �   	       趏/i)        鱎      �    .xdata      �          3賟P)        糞      �    .pdata      �         銀�*)        廡      �    .voltbl     �              +    _volmd      �    .xdata      �         C驎�,        aU      �    .pdata      �         覭=�,        砎      �    .xdata      �   
      B>z],        X      �    .xdata      �          �2g�,        XY      �    .xdata      �         T�8,        瞆      �    .xdata      �         r%�,        \      �    .xdata      �   	       莖3,        Z]      �    .xdata      �          M[�,        甞      �    .pdata      �         現�,        `      �    .voltbl     �              .    _volmd      �    .xdata      �          %蚘%%        qa      �    .pdata      �         }S蛥%        鵤      �    .xdata      �          �9�#        �b      �    .pdata      �         礝
#        輇      �    .rdata      �                      9c     �    .rdata      �          �;�         Pc      �    .rdata      �                      wc     �    .rdata      �                      巆     �    .rdata      �          �)         癱      �    .xdata$x    �                      躢      �    .xdata$x    �         虼�)               �    .data$r     �   /      嶼�         !d      �    .xdata$x       $      4��         Fd          .data$r       $      鎊=         沝         .xdata$x      $      銸E�         礵         .data$r       $      騏糡         鬱         .xdata$x      $      4��         e             Me           .rdata               燺渾         `e         .data                 烀�          唀             篹        .rdata               IM         醗         .rdata        (                   f        .rdata$r    	  $      'e%�         8f      	   .rdata$r    
        �          Pf      
   .rdata$r                         ff         .rdata$r      $      Gv�:         |f         .rdata$r    
  $      'e%�         沠      
   .rdata$r            }%B         砯         .rdata$r                         蒮         .rdata$r      $      `         遞         .rdata$r      $      'e%�                  .rdata$r            �弾         !g         .rdata$r                         Bg         .rdata$r      $      H衡�         cg         .data$rs      *      8V綊         峠         .rdata$r            �          璯         .rdata$r                         蒰         .rdata$r      $      Gv�:         錱         .rdata$r      $      'e%�         
h         .data$rs      >      �,�         <h         .rdata$r            }%B         ph         .rdata$r                         爃         .rdata$r      $      `         衕             	i               i           .rdata               � �         'i         _fltused         .debug$S      <              .debug$S       <              .debug$S    !  <              .debug$S    "  <              .debug$S    #  <              .debug$S    $  @          	    .debug$S    %  @          
    .debug$S    &  @              .debug$S    '  @              .debug$S    (  @          
    .debug$S    )  @              .debug$S    *  <              .debug$S    +  @              .debug$S    ,  @              .debug$S    -  @              .debug$S    .  @              .debug$S    /  <              .debug$S    0  <              .debug$S    1  @              .debug$S    2  @              .debug$S    3  <              .debug$S    4  8              .debug$S    5  8              .debug$S    6  <              .debug$S    7  @              .debug$S    8  8              .debug$S    9  <              .debug$S    :  @              .debug$S    ;  <               .debug$S    <  <          !    .debug$S    =  4          �    .debug$S    >  4          �    .debug$S    ?  @          �    .debug$S    @  L             .chks64     A  
                Ni  ?DDSPF_DXT1@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_DXT2@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_DXT3@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_DXT4@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_DXT5@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_BC4_UNORM@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_BC4_SNORM@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_BC5_UNORM@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_BC5_SNORM@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_R8G8_B8G8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_G8R8_G8B8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_YUY2@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A8R8G8B8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_X8R8G8B8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A8B8G8R8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_X8B8G8R8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_G16R16@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_R5G6B5@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A1R5G5B5@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A4R4G4B4@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_R8G8B8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_L8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_L16@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A8L8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A8L8_ALT@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_A8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_V8U8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_Q8W8V8U8@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_V16U16@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?DDSPF_DX10@dds@engine@donut@@3UDDS_PIXELFORMAT@123@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ?g_FormatMappings@engine@donut@@3QBUFormatMapping@12@B ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??1TextureDesc@nvrhi@@QEAA@XZ ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ?LoadDDSTextureFromMemory@engine@donut@@YA_NAEAUTextureData@12@@Z ?CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z ?SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z ??0DescriptorHandle@engine@donut@@QEAA@XZ ??1DescriptorHandle@engine@donut@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??1LoadedTexture@engine@donut@@QEAA@XZ ??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ ?deallocate@?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@QEAAXQEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@2@_K@Z ??1?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@QEAA@XZ ?_Xlength@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@CAXXZ ??1TextureData@engine@donut@@QEAA@XZ ??0Blob@vfs@donut@@QEAA@PEAX_K@Z ?ConvertDDSFormat@engine@donut@@YA?AW4Format@nvrhi@@AEBUDDS_PIXELFORMAT@dds@12@_N@Z ?FillTextureInfoOffsets@engine@donut@@YA_KAEAUTextureData@12@_K_J@Z ?deallocate@?$allocator@UTextureSubresourceData@engine@donut@@@std@@QEAAXQEAUTextureSubresourceData@engine@donut@@_K@Z ?_Xlength@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@CAXXZ ?CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z ??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z ??1?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Copy_memmove@PEAUTextureSubresourceData@engine@donut@@PEAU123@@std@@YAPEAUTextureSubresourceData@engine@donut@@PEAU123@00@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0??CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z@4HA ?dtor$10@?0??SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z@4HA ?dtor$1@?0??CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z@4HA ?dtor$1@?0??SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z@4HA ?dtor$2@?0??CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z@4HA ?dtor$2@?0??CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z@4HA ?dtor$6@?0??CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z@4HA ?dtor$8@?0??SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1TextureDesc@nvrhi@@QEAA@XZ $pdata$??1TextureDesc@nvrhi@@QEAA@XZ $unwind$?LoadDDSTextureFromMemory@engine@donut@@YA_NAEAUTextureData@12@@Z $pdata$?LoadDDSTextureFromMemory@engine@donut@@YA_NAEAUTextureData@12@@Z $unwind$?CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z $pdata$?CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z $cppxdata$?CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z $stateUnwindMap$?CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z $ip2state$?CreateDDSTextureFromMemory@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@V?$shared_ptr@VIBlob@vfs@donut@@@std@@PEBD_N@Z $unwind$?SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z $pdata$?SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z $cppxdata$?SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z $stateUnwindMap$?SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z $ip2state$?SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z $unwind$?dtor$10@?0??SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z@4HA $pdata$?dtor$10@?0??SaveStagingTextureAsDDS@engine@donut@@YA?AV?$shared_ptr@VIBlob@vfs@donut@@@std@@PEAVIDevice@nvrhi@@PEAVIStagingTexture@6@@Z@4HA $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1LoadedTexture@engine@donut@@QEAA@XZ $pdata$??1LoadedTexture@engine@donut@@QEAA@XZ $cppxdata$??1LoadedTexture@engine@donut@@QEAA@XZ $stateUnwindMap$??1LoadedTexture@engine@donut@@QEAA@XZ $ip2state$??1LoadedTexture@engine@donut@@QEAA@XZ $unwind$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VIBlob@vfs@donut@@@std@@QEAA@XZ $unwind$?deallocate@?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@QEAAXQEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@2@_K@Z $pdata$?deallocate@?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@QEAAXQEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@2@_K@Z $unwind$??1?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@CAXXZ $unwind$??1TextureData@engine@donut@@QEAA@XZ $pdata$??1TextureData@engine@donut@@QEAA@XZ $chain$0$??1TextureData@engine@donut@@QEAA@XZ $pdata$0$??1TextureData@engine@donut@@QEAA@XZ $chain$1$??1TextureData@engine@donut@@QEAA@XZ $pdata$1$??1TextureData@engine@donut@@QEAA@XZ $unwind$?FillTextureInfoOffsets@engine@donut@@YA_KAEAUTextureData@12@_K_J@Z $pdata$?FillTextureInfoOffsets@engine@donut@@YA_KAEAUTextureData@12@_K_J@Z $chain$0$?FillTextureInfoOffsets@engine@donut@@YA_KAEAUTextureData@12@_K_J@Z $pdata$0$?FillTextureInfoOffsets@engine@donut@@YA_KAEAUTextureData@12@_K_J@Z $unwind$?deallocate@?$allocator@UTextureSubresourceData@engine@donut@@@std@@QEAAXQEAUTextureSubresourceData@engine@donut@@_K@Z $pdata$?deallocate@?$allocator@UTextureSubresourceData@engine@donut@@@std@@QEAAXQEAUTextureSubresourceData@engine@donut@@_K@Z $unwind$?_Xlength@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@CAXXZ $unwind$?CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z $pdata$?CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z $cppxdata$?CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z $stateUnwindMap$?CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z $ip2state$?CreateDDSTextureInternal@engine@donut@@YA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@PEAVIDevice@4@PEAVICommandList@4@AEAUTextureData@12@PEBD@Z $unwind$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $chain$0$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $pdata$0$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $chain$1$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $pdata$1$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $chain$2$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $pdata$2$??$_Destroy_range@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@std@@@std@@YAXPEAV?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@0@QEAV10@AEAV?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@0@@Z $unwind$??_G?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@UEAAPEAXI@Z $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@V?$allocator@V?$vector@UTextureSubresourceData@engine@donut@@V?$allocator@UTextureSubresourceData@engine@donut@@@std@@@std@@@2@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Copy_memmove@PEAUTextureSubresourceData@engine@donut@@PEAU123@@std@@YAPEAUTextureSubresourceData@engine@donut@@PEAU123@00@Z $pdata$??$_Copy_memmove@PEAUTextureSubresourceData@engine@donut@@PEAU123@@std@@YAPEAUTextureSubresourceData@engine@donut@@PEAU123@00@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_7?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@6B@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VBlob@vfs@donut@@@std@@8 __ImageBase __security_cookie __xmm@000000000000000f0000000000000000 