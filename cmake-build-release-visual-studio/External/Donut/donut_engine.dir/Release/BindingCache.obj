d嗎 翀�& �      .drectve        <  <#               
 .debug$S        � x$  �/        @ B.debug$T        p   80             @ B.rdata          0   �0             @ @@.text$mn        :   �0 1         P`.debug$S          01 <3        @B.text$mn        7   �3              P`.debug$S        t  �3 s5        @B.text$mn        c   �5              P`.debug$S        �  N6 �7        @B.text$mn        c   F8              P`.debug$S        |  �8 %:        @B.text$mn           �:              P`.debug$S        l  �: <     
   @B.text$mn           �<              P`.debug$S        p  �< >     
   @B.text$mn           k>              P`.debug$S        t  �> �?     
   @B.text$mn        c   X@              P`.debug$S        x  籃 3B        @B.text$mn        D   獴              P`.debug$S        �  顱 {D        @B.text$mn        p   驞              P`.debug$S        �  cE 鸉        @B.text$mn        p   sG              P`.debug$S        �  鉍 wI        @B.text$mn           颕              P`.debug$S        X  J fK        @B.text$mn           禟              P`.debug$S        \  誎 1M        @B.text$mn           丮              P`.debug$S        `  燤  O        @B.text$mn        p   PO              P`.debug$S        �  繭 PQ        @B.text$mn        +  萉 骉         P`.debug$S        h  /U 梔     Z   @B.text$x            h 'h         P`.text$x            1h =h         P`.text$mn        �   Gh              P`.debug$S        �  薶 玨        @B.text$mn        i   Kl              P`.debug$S        @  磍 鬾        @B.text$mn        �   �o              P`.debug$S        H  p ]r        @B.text$mn        �   閞              P`.debug$S        D  ~s 聈        @B.text$mn        �   Nv 鉽         P`.debug$S        �  韛 誽        @B.text$mn        8   墇 羫         P`.debug$S        H  藌 |     
   @B.text$mn        G   w|              P`.debug$S        �  緗 簙        @B.text$mn        G   2              P`.debug$S          y }�        @B.text$mn        G   鮼              P`.debug$S          <� D�        @B.text$mn        �   紕              P`.debug$S        D  Q� 晣        @B.text$mn            !�              P`.debug$S        8  A� y�        @B.text$mn        <   � U�         P`.debug$S        0  s�      
   @B.text$mn        <   � C�         P`.debug$S        L  a� 瓘     
   @B.text$mn        !   � 2�         P`.debug$S        <  F� 倯        @B.text$mn        2   緫 饝         P`.debug$S        <  � @�        @B.text$mn        "   笓              P`.debug$S        �  趽 n�        @B.text$mn           � "�         P`.debug$S        L  ,� x�        @B.text$mn        J   葮 �         P`.debug$S        h  � 劀        @B.text$mn           L� _�         P`.debug$S        �   s� W�        @B.text$mn           � 挒         P`.debug$S        �    啛        @B.text$mn        E  聼              P`.debug$S        <	  � C�        @B.text$mn        B   [� 潿         P`.debug$S           滑 画        @B.text$mn        B   鳝 9�         P`.debug$S          W� g�        @B.text$mn        B   ／ 瀵         P`.debug$S        �   � ��        @B.text$mn        2   ;� m�         P`.debug$S        h  嫳 蟛        @B.text$mn        2  C� u�         P`.debug$S        �
  摰 +�     >   @B.text$mn        B  椔 倥         P`.debug$S          � �     n   @B.text$x         )   i� 捹         P`.text$mn        :  溬 众         P`.debug$S        �  �      <   @B.text$mn        �  � 铈         P`.debug$S        p  � |�     P   @B.text$mn            滜 减         P`.debug$S        �   邗 烐        @B.text$mn        {  邛 U�         P`.debug$S        �
  i� �     H   @B.text$mn        �   � �         P`.debug$S           � �        @B.text$mn           �
 �
         P`.debug$S        �   �
 �        @B.xdata             �             @0@.pdata                       @0@.xdata             *             @0@.pdata             6 B        @0@.xdata             `             @0@.pdata             h t        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata                      @0@.xdata             0             @0@.pdata             8 D        @0@.xdata             b             @0@.pdata             n z        @0@.xdata             �             @0@.pdata             � �        @0@.xdata              � �        @0@.pdata             � 
        @0@.xdata          	   ( 1        @@.xdata             E K        @@.xdata             U             @@.xdata             \ x        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.xdata              4        @0@.pdata             R ^        @0@.xdata             | �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata             0 L        @0@.pdata             j v        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             �             @0@.pdata             �         @0@.xdata             " :        @0@.pdata             X d        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata              : Z        @0@.pdata             n z        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata              � �        @0@.pdata             �         @0@.xdata          	   ) 2        @@.xdata             F Q        @@.xdata             e             @@.xdata             w             @0@.pdata              �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata          	   - 6        @@.xdata             J P        @@.xdata             Z             @@.xdata             ]             @0@.pdata             q }        @0@.xdata              � �        @0@.pdata             � �        @0@.xdata          	   �         @@.xdata              $        @@.xdata          
   8             @@.xdata             B             @0@.pdata             J V        @0@.xdata             t �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             � 
        @0@.xdata             ( 8        @0@.pdata             L X        @0@.xdata          	   v         @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.rdata             � �        @@@.rdata                          @@@.rdata               8        @@@.rdata             V n        @@@.rdata             �             @@@.xdata$x           � �        @@@.xdata$x           � �        @@@.data$r         /    :        @@�.xdata$x        $   D h        @@@.data$r         $   | �        @@�.xdata$x        $   � �        @@@.data$r         $   �         @@�.xdata$x        $    4        @@@.data               H             @ @�.rdata             h             @@@.rdata             �             @@@.rdata$r        $   � �        @@@.rdata$r           � �        @@@.rdata$r           � 	        @@@.rdata$r        $    7        @@@.rdata$r        $   K o        @@@.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $    /        @@@.rdata$r           M a        @@@.rdata$r           k �        @@@.rdata$r        $   � �        @@@.rdata             �             @0@.debug$S        4   �         @B.debug$S        4   ) ]        @B.debug$S        @   q �        @B.chks64           �              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �  j     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\BindingCache.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono  $this_thread  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine �   97    �        nvrhi::EntireBuffer L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos :    std::integral_constant<unsigned __int64,1>::value 2 �  �����std::shared_timed_mutex::_Max_readers �    std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi �   std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard �    std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable /   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size /   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets )�    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable - �    std::chrono::system_clock::is_steady $ E   std::ratio<1,10000000>::num ( E  ��枠 std::ratio<1,10000000>::den + <        nvrhi::rt::c_IdentityTransform  E   std::ratio<1,1>::num  E   std::ratio<1,1>::den J E   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N E  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( E  ��枠 std::ratio<10000000,1>::num $ E   std::ratio<10000000,1>::den P E  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < E  ��枠 std::integral_constant<__int64,10000000>::value 1 E   std::integral_constant<__int64,1>::value D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment - �   std::chrono::steady_clock::is_steady & E   std::ratio<1,1000000000>::num * E  � 蕷;std::ratio<1,1000000000>::den B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size   E  std::ratio<3600,1>::num   E   std::ratio<3600,1>::den 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss  E  < std::ratio<60,1>::num % �    std::_Num_base::has_infinity  E   std::ratio<60,1>::den & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10   E   std::ratio<1,1000>::num   E  �std::ratio<1,1000>::den 0 �   std::numeric_limits<wchar_t>::is_modulo - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , d  	 std::numeric_limits<long>::digits10 P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity 0 �   std::numeric_limits<__int64>::is_signed - d  ? std::numeric_limits<__int64>::digits / d   std::numeric_limits<__int64>::digits10 d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 7 �   std::numeric_limits<unsigned short>::is_modulo 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 # E   std::ratio<1,1000000>::num ' E  �@B std::ratio<1,1000000>::den 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10 " �    std::memory_order_relaxed " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst    �   鐨  4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment  �4   _Mtx_try  �4   _Mtx_recursive  C5   std::_INVALID_ARGUMENT  C5   std::_NO_SUCH_PROCESS & C5   std::_OPERATION_NOT_PERMITTED , C5   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - C5   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos :     std::integral_constant<unsigned __int64,0>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P E  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex :    std::integral_constant<unsigned __int64,2>::value � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos # �        nvrhi::AllSubresources J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos  �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> & �5  $_TypeDescriptor$_extraBytes_28     int64_t    _Smtx_t  �(  _Thrd_result  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e & i4  $_TypeDescriptor$_extraBytes_23 - �4  $_s__CatchableTypeArray$_extraBytes_32  �5  _TypeDescriptor & �4  $_TypeDescriptor$_extraBytes_34  �5  _Stl_critical_section 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const> E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16  i5  std::input_iterator_tag . �'  std::_Conditionally_enabled_hash<int,1> ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> > . �2  std::integer_sequence<unsigned __int64> � �/  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > ) �/  std::hash<nvrhi::IBindingLayout *>  .  std::_Lockit  �)  std::timed_mutex * 2/  std::hash<enum nvrhi::ResourceType> " i3  std::_Char_traits<char,int>  "   std::_Atomic_counter_t  �2  std::_Num_base & �'  std::hash<std::error_condition> K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >  y$  std::_Big_uint128  ))  std::condition_variable ) v3  std::_Narrow_char_traits<char,int> i �5  std::initializer_list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >    std::hash<float> E �.  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc> " �5  std::_Align_type<double,64>  �'  std::hash<int>  �2  std::_Num_int_base " k(  std::_System_error_category / Q/  std::_Conditionally_enabled_hash<bool,1>  �2  std::float_denorm_style � �/  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > ! %0  std::piecewise_construct_t ! �)  std::_Ptr_base<std::mutex> �W,  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> 6 �5  std::allocator_traits<std::allocator<wchar_t> >  *  std::shared_timed_mutex & �.  std::equal_to<unsigned __int64>  &  std::bad_cast + "0  std::tuple<unsigned __int64 const &> " �2  std::numeric_limits<double>  <&  std::__non_rtti_object ( n  std::_Basic_container_proxy_ptr12 � d5  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base  �&  std::logic_error 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety ! �5  std::char_traits<char32_t> � �5  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >   �2  std::numeric_limits<bool> # �3  std::_WChar_traits<char16_t> T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  '  std::overflow_error { �+  std::pair<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *,bool> % --  std::_One_then_variadic_args_t D �/  std::_Constexpr_immortalize_impl<std::_System_error_category> W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   �5  std::char_traits<wchar_t> � �+  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > >  �(  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> � +  std::unordered_map<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  �5  std::false_type  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy ! �(  std::hash<std::thread::id>  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  )  std::adopt_lock_t � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , �2  std::numeric_limits<unsigned __int64> � 
4  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> f �5  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound  �(  std::_Mutex_base  �5  std::defer_lock_t   a'  std::_Init_once_completer  )  std::scoped_lock<> h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> ! �'  std::hash<std::error_code> @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> � M,  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > $ �  std::_Atomic_integral<long,4> � I3  std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  U/  std::hash<bool> � �5  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> " �)  std::lock_guard<std::mutex> k �5  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  (  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy �-  std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>  �5  std::try_to_lock_t U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> " .)  std::_Align_type<double,72> / �3  std::_Char_traits<char32_t,unsigned int>  �'  std::_System_error ( 1$  std::hash<nvrhi::FramebufferInfo> b �+  std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �'  std::error_condition % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128>  �&  std::invalid_argument N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >  ")  std::cv_status S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >  �(  std::thread  �(  std::thread::id  �&  std::length_error ! �2  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> i �+  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> > � �+  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > >  @)  std::mutex Q �+  std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  �5  std::ratio<60,1>  t  std::exception_ptr  �5  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t>  Y'  std::once_flag  �'  std::error_code    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 �5  std::allocator_traits<std::allocator<char32_t> >  {5  std::nano  �  std::_Iterator_base0 M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> $ �#  std::hash<nvrhi::BufferRange> " �)  std::shared_ptr<std::mutex> i �+  std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> ! �5  std::char_traits<char16_t> � �3  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >  |  std::tuple<>    std::_Container_base12  �)  std::shared_mutex  �'  std::io_errc E �/  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 �2  std::integer_sequence<unsigned __int64,0> ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char>  x5  std::_Invoker_strategy $ �2  std::_Default_allocate_traits 3 �5  std::allocator_traits<std::allocator<char> > ! �2  std::numeric_limits<short> . �0  std::allocator<nvrhi::rt::GeometryDesc> # d)  std::unique_lock<std::mutex> ; �  std::basic_string_view<char,std::char_traits<char> > C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 0 �2  std::_Tuple_val<unsigned __int64 const &> . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock  D  std::bad_alloc  /'  std::underflow_error B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> � }+  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0>  �&  std::out_of_range # �2  std::numeric_limits<__int64>  �  std::memory_order ! �)  std::recursive_timed_mutex " �)  std::condition_variable_any  �5  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> z ,  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >   (  std::system_error < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  �5  std::ratio<1,1>   k5  std::forward_iterator_tag  '  std::runtime_error   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >    std::_Container_proxy  �  std::nested_exception  r  std::_Distance_unknown ( �2  std::numeric_limits<unsigned int> B �/  std::_Conditionally_enabled_hash<nvrhi::IBindingLayout *,1> � c,  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > | (,  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy � �/  std::_Hash_find_last_result<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *>     std::streamoff    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double>  �'  std::errc  F'  std::range_error  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  p)  std::_UInt_is_zero y �+  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void>  {5  std::ratio<1,1000000000>  �,  std::allocator<char16_t> ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  v5  std::ratio<1,1000>  t5  std::ratio<1,10000000> ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> % ^(  std::_Iostream_error_category2 * r5  std::_String_constructor_concat_tag  D-  std::allocator<char> �*  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > .�-  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Range_eraser -d1  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Clear_guard G �/  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1>  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring z f5  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ' �2  std::numeric_limits<signed char>  �&  std::domain_error  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ &/  std::hash<nvrhi::IResource *> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char> 9 o%  std::chrono::duration<__int64,std::ratio<1,1000> >  �$  std::chrono::nanoseconds ? �$  std::chrono::duration<__int64,std::ratio<1,1000000000> >  �$  std::chrono::seconds 3 -%  std::chrono::duration<int,std::ratio<60,1> > 6 �$  std::chrono::duration<__int64,std::ratio<1,1> > s �$  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   U5  std::chrono::steady_clock   R5  std::chrono::system_clock 6 B%  std::chrono::duration<double,std::ratio<60,1> > ; �%  std::chrono::duration<double,std::ratio<1,1000000> > > �%  std::chrono::duration<double,std::ratio<1,1000000000> > = �$  std::chrono::duration<__int64,std::ratio<1,10000000> > q �$  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 %  std::chrono::duration<int,std::ratio<3600,1> > 8 �%  std::chrono::duration<double,std::ratio<1,1000> > < �%  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 Y%  std::chrono::duration<double,std::ratio<1,1> > 8 %  std::chrono::duration<double,std::ratio<3600,1> > � =+  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > h K3  std::pointer_traits<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > *>  K5  std::char_traits<char>  �'  std::error_category ) �'  std::error_category::_Addr_storage  �/  std::_Wrap<std::mutex> ! >(  std::_System_error_message  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> @ �3  std::_Default_allocator_traits<std::allocator<char16_t> > z .  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1>  -2  std::_Exact_args_t 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t> # P(  std::_Generic_error_category  U3  std::streampos ' y/  std::hash<enum nvrhi::ColorMask> � 1  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *> O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > ' �/  std::_Ref_count_obj2<std::mutex> 	,  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � 9,  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > >  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # B#  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - `+  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  #  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  &  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  `+  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # L#  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable  H!  nvrhi::ComputeState  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec 
 !   _ino_t 
 )  _Cnd_t  !   uint16_t " *  donut::engine::BindingCache M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t  u   _Thrd_id_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  e4  _CatchableTypeArray     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t & �4  $_TypeDescriptor$_extraBytes_41  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24  �(  _Mtx_internal_imp_t & _4  $_TypeDescriptor$_extraBytes_25 % P4  __RTTIClassHierarchyDescriptor     __time64_t  m  FILE 
 �(  _Mtx_t 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray  �(  _Thrd_t - G4  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  
  __std_exception_data 
 u   _dev_t  K  lldiv_t  H  _ldiv_t  �  _timespec64  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers  �   �      譫鰿3鳪v鐇�6瘻x侃�h�3&�  ?    j轲P[塵5m榤g摏癭 鋍1O骺�*�  �    齝D屜u�偫[篔聤>橷�6酀嘧0稈  �    _O縋[HU-銌�鼪根�鲋薺篮�j��     嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  U   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  )   A縏 �;面褡8歸�-構�壋馵�2�-R癕  h   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  %   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  j   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   繃S,;fi@`騂廩k叉c.2狇x佚�  .   猯�諽!~�:gn菾�]騈购����'  j   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  K    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�     翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  W   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  9   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  #   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  p   3p@F璑恤(<��&�+4�"kI噤墒峕沋  �   彲 ㄋ牐媝e哟�<N7%�Yn�52�峟�  �   L�9[皫zS�6;厝�楿绷]!��t  	   チ畴�
�&u?�#寷K�資 +限^塌>�j  R	   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �	   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �	   穫農�.伆l'h��37x,��
fO��  
   5�\營	6}朖晧�-w氌rJ籠騳榈  T
   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �
   ii(祍P%舌+;�'縿E╪嫏脈3L籾
�  �
   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦     W簅r�hc鷟;9攫�.�6Y柦阁��}.J╗  b   �*o驑瓂a�(施眗9歐湬

�  �   �'稌� 变邯D)\欅)	@'1:A:熾/�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  3   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  p   蜅�萷l�/费�	廵崹
T,W�&連芿  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  A
   D���0�郋鬔G5啚髡J竆)俻w��  �
   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �
   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7     c�#�'�縌殹龇D兺f�$x�;]糺z�  q   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  G   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  *   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  j   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   匐衏�$=�"�3�a旬SY�
乢�骣�  2   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  |   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  m   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   �"睱建Bi圀対隤v��cB�'窘�n     鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  L   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   G�膢刉^O郀�/耦��萁n!鮋W VS     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  e   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  (   �0�*е彗9釗獳+U叅[4椪 P"��  c   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  7   �=蔑藏鄌�
艼�(YWg懀猊	*)  x   交�,�;+愱`�3p炛秓ee td�	^,  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗     ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  M   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  Y   +4[(広
倬禼�溞K^洞齹誇*f�5  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  4   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥     副謐�斦=犻媨铩0
龉�3曃譹5D   I   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  3   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  p   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  K   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     k�8.s��鉁�-[粽I*1O鲠-8H� U  W   �
bH<j峪w�/&d[荨?躹耯=�  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  (   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  g   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   p      �  �   	  T  �  B   U  �  H   V  �  Y   [  �  �   v  �  U   w  �  �   �  P  �
  �  �     �  �  2   1  @  #   2  @  +   3  @  /   4  @  7   >  @    ?  @  �   @  @  �   A  @  �   B  @  �   C  @  �   D  �  �  ]  �  :   j  �  ]   l  �  �  �  �  �   �  @    �  @  �   �  @  �   �  0  '  �  �  �  �  �  H  �  �  0   �  �  ^   �  0  �  �  0  �  �  0  �  �  �  �   �  �  %   �  @  �   �  0    �  �  �  �  h  K   �  0  S  �  �  X    �  �   -  0  
  .  �  �  3  �  P   :  �  �  R  P  {
  S  P  {
  T  P  {
  U  P  {
  V  P  {
  W  P  {
  X  P  {
  v  �   *	  z  P  {
  {  P  {
  |  0  �  ~  0  �   �  �  C  �  �  3  �  �  <  �  �  �  �  �  �   �  �  @   �  �  �   �  �  @   �  �  F  �  �   ?	  �  �   ?	  �  �   ?	  �  �   ?	  �  �   ?	  �  �   ?	  �  0  w  �  0  q  �  0  j  �  0  K  �  �  `  �  �  �  �  �   ?	  �  �  �  �  �   Q	  �  �   Q	     �   Q	    �   Q	    �   Q	    �   Q	    0  �    0  �    �   Q	  "  �  j   $  �  L   %  �  G   &  �  <   '  �  1   (  �  )   +  0  �  ,  0    -  �  �  0  �  �  ;  �  P  O  �   $	  R  0  �  S  0    T  h  �   X  �  G  Y  h  �   �  �  �  �  �  R  �  �  �   �  h  �  �  �   *	  �  �   *	  �  �   *	  �  �   *	  �  �   *	  �  0  �   �  �   *	  �  �  �  �  �  |  �  `	  �  �    a  �  �  �  �  �   $	    �   $	    �   $	    �   $	    �   $	    �   $	      l  "    ;  #  h  9  =    �   >  h  5  �   �   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\src\engine\BindingCache.cpp D:\RTXPT\External\Donut\include\donut\engine\BindingCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\shared_mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\condition_variable D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h  �       Lal  �  i   �  i  
 �      �     
 閇  h   韀  h  
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb �  �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  7   w  7  
 �     �    
 D�I钩     禕L3�禞MI3繧H3�禞IH3罥�   �   0  L G            7       6   O        �std::_Fnv1a_append_value<unsigned int> 
 >   _Val  AJ          >�#   _Keyval  AK        7  M        �  @2
 >#    _Val  AH          AP         N                        H� 
 h   �        O_Val     �#  O_Keyval  O�   0           7   �      $       $	 �    &	 �6   '	 �,      0     
 q      u     
 �      �     
 �      �     
 �      �     
 D     H    
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   ;  W G            c       b   �        �std::_Fnv1a_append_value<nvrhi::IBindingLayout *> 
 >   _Val  AJ          >c+   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �        O_Val     c+  O_Keyval  O �   0           c   �      $       $	 �    &	 �b   '	 �,      0     
 |      �     
 �      �     
 �      �     
 �      �     
 P     T    
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   6  R G            c       b           �std::_Fnv1a_append_value<nvrhi::IResource *> 
 >   _Val  AJ          >�#   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �        O_Val     �#  O_Keyval  O  �   0           c   �      $       $	 �    &	 �b   '	 �,      0     
 w      {     
 �      �     
 �      �     
 �      �     
 L     P    
 �H3罤钩     H�   �   &  R G                              �std::_Fnv1a_append_value<enum nvrhi::Format> 
 >   _Val  AJ          >�   _Keyval  AK          M        �  @
 >#    _Val  AH         N                        H� 
 h   �        O_Val     �  O_Keyval  O  �   0              �      $       $	 �    &	 �   '	 �,      0     
 w      {     
 �      �     
 �      �     
 <     @    
 �H3罤钩     H�   �   ,  X G                              �std::_Fnv1a_append_value<enum nvrhi::ResourceType> 
 >   _Val  AJ          >�#   _Keyval  AK          M        �  @
 >#    _Val  AH         N                        H� 
 h   �        O_Val     �#  O_Keyval  O�   0              �      $       $	 �    &	 �   '	 �,      0     
 }      �     
 �      �     
 �      �     
 @     D    
 �H3罤钩     H�   �   0  \ G                              �std::_Fnv1a_append_value<enum nvrhi::TextureDimension> 
 >   _Val  AJ          >$   _Keyval  AK          M        �  @
 >#    _Val  AH         N                        H� 
 h   �        O_Val     $  O_Keyval  O�   0              �      $       $	 �    &	 �   '	 �,      0     
 �      �     
 �      �     
 �      �     
 D     H    
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   4  P G            c       b           �std::_Fnv1a_append_value<unsigned __int64> 
 >   _Val  AJ          >�#   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �        O_Val     �#  O_Keyval  O�   0           c   �      $       $	 �    &	 �b   '	 �,      0     
 u      y     
 �      �     
 �      �     
 �      �     
 H     L    
 D�I钩     H�%#"勪滘薍嬔L3�禔禝MI3繧H3�禞IH3罥�   �   8  M G            D       C   v        �std::_Hash_representation<unsigned int>  >�#   _Keyval  AJ          AK       )  M        O   ( M        �  @
 >#    _Val  AH  -       AP         N N                        H�  h   �  O      �#  O_Keyval  O�   @           D   �      4       *	 �    +	 �   *	 �   +	 �C   ,	 �,   �    0   �   
 u   �    y   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 L  �    P  �   
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   C  X G            p       o   �        �std::_Hash_representation<nvrhi::IBindingLayout *>  >c+   _Keyval  AJ          AK       U  M        �   T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �  �      c+  O_Keyval  O �   @           p   �      4       *	 �    +	 �   *	 �   +	 �o   ,	 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 X     \    
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   >  S G            p       o   �        �std::_Hash_representation<nvrhi::IResource *>  >�#   _Keyval  AJ          AK       U  M           T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �        �#  O_Keyval  O  �   @           p   �      4       *	 �    +	 �   *	 �   +	 �o   ,	 �,      0     
 {           
 �      �     
 �      �     
 �      �     
 T     X    
 �H�%#"勪滘薍3罤钩     H�   �     S G                      �        �std::_Hash_representation<enum nvrhi::Format>  >�   _Keyval  AJ        
  M            M        �  @
 >#    _Val  AH         N N                        H�  h   �        �  O_Keyval  O  �   0              �      $       *	 �    +	 �   ,	 �,   
   0   
  
 {   
      
  
 �   
   �   
  
 (  
   ,  
  
 �H�%#"勪滘薍3罤钩     H�   �     Y G                      �        �std::_Hash_representation<enum nvrhi::ResourceType>  >�#   _Keyval  AJ        
  M            M        �  @
 >#    _Val  AH         N N                        H�  h   �        �#  O_Keyval  O�   0              �      $       *	 �    +	 �   ,	 �,      0     
 �      �     
 �      �     
 ,     0    
 �H�%#"勪滘薍3罤钩     H�   �     ] G                      �        �std::_Hash_representation<enum nvrhi::TextureDimension>  >$   _Keyval  AJ        
  M            M        �  @
 >#    _Val  AH         N N                        H�  h   �        $  O_Keyval  O�   0              �      $       *	 �    +	 �   ,	 �,      0     
 �      �     
 �      �     
 0     4    
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   <  Q G            p       o   �        �std::_Hash_representation<unsigned __int64>  >�#   _Keyval  AJ          AK       U  M           T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �        �#  O_Keyval  O�   @           p   �      4       *	 �    +	 �   *	 �   +	 �o   ,	 �,      0     
 y      }     
 �      �     
 �      �     
 �      �     
 P     T    
 H塡$UVWATAUAVAWH冹0I嬸L嬺H嬮A�H�%#"勪滘薍3菻撼     HA禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻H墝$�   L岴0I� H#罤繦婱H媆�H峌L�"I;躸
I嬡L塂$pM孅隟H�罤�H;CtH;賢*H媅H;Cu馡�A艶 I嬈H媆$xH兡0A_A^A]A\_^]肔嬨H岴0H塂$pL孄L峬H�������H9E勽  H塗$ H荄$(    �    �    H孁H塂$(H�H塇H茾    H婱�] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繦媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺勼   �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛H嬐�    H婱0L媱$�   I#菻蒆婾H婦�H媇H;胾H荄$(    �$H�蔋婳H;Ht怘嬝H;聇"H婡H;Hu頗�H塡$ L孄L峬H岴0L嬨�-H塂$ H荄$(    L孄L峬H岴0L媎$ �
H婦$pL媱$�   H婼H�EL�'H塛H�:H墈I婱 H� I#繦繪�罫;EuH�<岭M;莡H�<岭H9T�uH墊�I�>A艶橥��H�
    �    �>  �    �     �  �   .  �    !  l   &  �       �   s  fG            +     +  }        �std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<unsigned __int64 const &> 
 >f*   this  AJ          AN       �   >�#   _Keyval_arg  AL       �  t� AP          AL �       >�/   _Target  CH      
      CI      �       CT          � ��  CU          {	 � �W  CW      �     Q  K  CT     �     3 � CU     �     3 � >�/   _Newnode  CM     E      B    �     3 , t � � 1 M        S  ��*,'%md	& >S*    _Where  AI  �     d  
 =   AI �     @  fi 
 >�*    _End  AT  �     Q 6   AT �     @  � �  >�*    _Bucket_lo  AJ  �     G     AJ �     :  R � >    _Bucket  AH  �       M        �  �� M        �  �� N N N M        ~  { M        �  { M          { M        �  { M          {5 M        �  >(4(4(4(4(4(4
 >#    _Val  B�   �     � AJ  .     {  AP  >    � | 
  N N N N N N M        X  �*% M        �  丣 M        #  丣 M        >  丣 M        C  丵 N N N N M        �  �/	 M        :  
�8 M        �  
�8 M        v  
�8
 Z   �   N N N N M        �  �* N N M        �  ��
 Z   6   N M        T  �� N M        �  乊D5Y >    _Newsize  AJ  f      AJ �    Z  I �  >    _Oldsize  AJ  ]    	  M          乚 N N8 M        S  �2/,$%k
 >S*    _Where  AH  M    f H  
 >�*    _End  AI  Q      AI �     3 }� �-  >�*    _Bucket_lo  AK  e    U     AK �    F  -  >    _Bucket  AJ  6      M        �  俥 M        �  俥 N N N M        �  k伹
 Z       M          伹B
 >   _Req_buckets  AJ  	    $  C             M        +  6伹 N N N M        Y  
� N2 M        �  偳$$#$#d$&CJ$"E >*,    _Bucket_array  AJ  �    =  AJ �       >�*    _Insert_after  AK  �    S  AK �       >    _Bucket  AH  �      N 0           8         0@ hF   �  �  v  w  �  @  C  �  �  �  �  :  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �          *  +  0  S  T  U  V  W  X  Y  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �      "  #  <  =  >  ?  I         $LN206  p   f*  Othis  �   �#  O_Keyval_arg      �/  O_Newnode  O �   �           +  0     �       � �   � ��   � ��   � ��   � �  � �  � �*  � �Y  � ��  � �2  � ��  � ��  � ��  � ��  � �  � ��   �  uF                                �`std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<unsigned __int64 const &>'::`1'::dtor$1                         �  O   �   �  uF                                �`std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<unsigned __int64 const &>'::`1'::dtor$0                         �  O   ,      0     
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 '     +    
 ;     ?    
 W     [    
 w     {    
 �     �    
 �     �    
 �     �    
 �     �    
 F     J    
 ^     b    
 �     �    
 �     �    
 �     �    
 �     �    
          
 �         
          
      #    
 �     �    
 �     �    
 
         
 �     �    
 �     �    
 �     �    
 �     �    
 �         
 $     (    
 �     �    
 �     �    
 q	     u	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 )  3   -  3  
 �     �    
 H     L    
 �
     �
    
 H崐    �       
   H崐    �       	   H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   q  _G            �                 �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >*,   _First  AJ        0  AJ b     "  >*,   _Last  AK          AR       } 
 >+,   _Val  AP        �  >)+    _UFirst  AQ       u                        @  h   �  Z      *,  O_First     *,  O_Last      +,  O_Val  O   �   X           �   `	     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,      0     
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 �     �    
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  G G            i       h   R        �nvrhi::hash_combine<unsigned int> 
 >�   seed  AJ          AR       b 
 >�#   v  AK        B  M        �   " M           " M        v   " M        O   "+ M        �   "
 >#    _Val  AJ  M     
  AQ       /  N N N N N                        H  h   �  v  �    O      �  Oseed     �#  Ov  O   �   @           i   P     4       {
 �    }
 �   {
 �   }
 �h   ~
 �,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 h  �    l  �   
 x  �    |  �   
    �      �   
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  R G            �       �   {        �nvrhi::hash_combine<nvrhi::IBindingLayout *> 
 >�   seed  AJ          AR       � 
 >c+   v  AK        n  M        �   B"E M        �   B"E M        �   B"E M        �   B"E+ M        �   ";>
 >#    _Val  AJ  y     
  AQ       [  N N N N N                        H  h   �  �  �  �  �      �  Oseed     c+  Ov  O�   @           �   P     4       {
 �    }
 �   {
 �   }
 ��   ~
 �,      0     
 w      {     
 �      �     
 �      �     
 s     w    
 �     �    
          
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  M G            �       �   T        �nvrhi::hash_combine<nvrhi::IResource *> 
 >�   seed  AJ          AR       � 
 >�#   v  AK        n  M        �   B"E M           B"E M        �   B"E M           B"E+ M        �   ";>
 >#    _Val  AJ  y     
  AQ       [  N N N N N                        H  h   �  �    �        �  Oseed     �#  Ov  O �   @           �   P     4       {
 �    }
 �   {
 �   }
 ��   ~
 �,   �    0   �   
 r   �    v   �   
 �   �    �   �   
 �   �    �   �   
 n  �    r  �   
 ~  �    �  �   
   �      �   
 H塡$H塴$VWAVH冹 H嫼   3鯤羚H嬟H鶯嬹焦y7濰;譼0H嬘H峀$@�    H嬑H兠 H玲H罤嬑H灵H臜菻3馠;遳蠱�H媆$HI嬋H玲I嬂H凌H蜨臜媗$PH菼3菼�H兡 A^_^�;   �       �   �  P G            �      e   z        �nvrhi::hash_combine<nvrhi::BindingSetDesc> 
 >�   seed  AJ        (  AV  (     j 
 >2   v  AK        2  AK 2     c  - + M        �  7*$ >#     value  AL       y  >($    <begin>$L0  AI  "     H  >($    <end>$L0  AM         M        j  	 M        �  	 M          	 M        3  	 N N N N M        ]  B N M        X  2
 Z   �   N N                       H : h
   �  \  ]  ^  j  l  �  �  �      3  X   @   �  Oseed  H   2  Ov  O�   P           �   P     D       {
 �   }
 �   {
 �"   }
 �%   {
 �(   }
 �e   ~
 �,      0     
 u      y     
 �      �     
 �      �     
 �      �     
          
 '     +    
 J     N    
 �     �    
 @WH冹 H孂H峀$0�    H�构y7濰菱H翲�H陵H罤蠬1H兡 _�   �       �     P G            8      2   X        �nvrhi::hash_combine<nvrhi::BindingSetItem> 
 >�   seed  AJ        	  AM  	     . 
 >�   v  AK         
 Z   �                         H  0   �  Oseed  8   �  Ov  0   �#  Ohasher  O �   0           8   P     $       {
 �	   }
 �2   ~
 �,   �    0   �   
 u   �    y   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 L�	L嬞�H�%#"勪滘薍3蠱嬃H赋     I凌HI嬃构y7濱菻拎H蠬蔍3蒊��   �   �  M G            G       F   W        �nvrhi::hash_combine<enum nvrhi::Format> 
 >�   seed  AJ          AS       A 
 >�   v  AK        	  M        �  
 M        �  
 M        �  
 M          
 M        �  

 >#    _Val  AK       '  N N N N N                        H  h   �  �  �  �        �  Oseed     �  Ov  O �   0           G   P     $       {
 �    }
 �F   ~
 �,   �    0   �   
 r   �    v   �   
 �   �    �   �   
 �   �    �   �   
 F  �    J  �   
 �  �    �  �   
 L�	L嬞�H�%#"勪滘薍3蠱嬃H赋     I凌HI嬃构y7濱菻拎H蠬蔍3蒊��   �   �  S G            G       F   U        �nvrhi::hash_combine<enum nvrhi::ResourceType> 
 >�   seed  AJ          AS       A 
 >�#   v  AK        	  M        �  
 M          
 M        �  
 M          
 M        �  

 >#    _Val  AK       '  N N N N N                        H  h   �  �    �        �  Oseed     �#  Ov  O   �   0           G   P     $       {
 �    }
 �F   ~
 �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 �   �    �   �   
 L  �    P  �   
 �  �    �  �   
 L�	L嬞�H�%#"勪滘薍3蠱嬃H赋     I凌HI嬃构y7濱菻拎H蠬蔍3蒊��   �   �  W G            G       F   V        �nvrhi::hash_combine<enum nvrhi::TextureDimension> 
 >�   seed  AJ          AS       A 
 >$   v  AK        	  M        �  
 M           
 M        �  
 M          
 M        �  

 >#    _Val  AK       '  N N N N N                        H  h   �  �     �        �  Oseed     $  Ov  O   �   0           G   P     $       {
 �    }
 �F   ~
 �,   �    0   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 P  �    T  �   
 �  �    �  �   
 D�
L嬔H钩     H�%#"勪滘薒3�禕LM�L3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LI嬓L3菻陵II嬂H拎H雀箉7濰翲菼3菼�
�   �   �  K G            �       �   S        �nvrhi::hash_combine<unsigned __int64> 
 >�   seed  AJ          AR       � 
 >�#   v  AK        n  M        �   B"E M           B"E M        �   B"E M           B"E+ M        �   ";>
 >#    _Val  AJ  y     
  AQ       [  N N N N N                        H  h   �  �    �        �  Oseed     �#  Ov  O   �   @           �   P     4       {
 �    }
 �   {
 �   }
 ��   ~
 �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 �   �    �   �   
 l  �    p  �   
 |  �    �  �   
   �      �   
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  mG                       �        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >*,   _First  AJ          AJ       
   >*,   _Last  AK          
 >+,   _Val  AP           >1   _Backout  CJ            CJ          
   M        �    N M        �   N                        H & h   �  �  �  �  Z  �  �  �      *,  O_First     *,  O_Last     +,  O_Val  O�   H               �     <       � �    � �   � �   � �   � �   � �,      0     
 �     �    
 �     �    
 �     �    
 �     �    
          
 "     &    
 �     �    
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   @   %   �    ,   F      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   @   %   �    ,   I      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   L      I      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   @   %   �       �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         @        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >@+   this  AH         AJ          AH        M        �  GCE
 >b     temp  AJ  
       AJ        N (                     0H� 
 h   �   0   @+  Othis  9       /   O  �   0           "   @     $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
 J     N    
 d     h    
 H婭H吷t
�    �    �   �       �   �  BG                      �        �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >�/   this  AJ          M        0  
	 M        �  
	
 >   _Ptr  AJ         N N                        H�  h   w  �  0      �/  Othis  O   �   8              �     ,       � �    � �	   � �   � �,   	   0   	  
 g  	   k  	  
 �  	   �  	  
   	     	  
 @SH冹 H嬞H婭H吷t2H婹H呉tH茿    H�H嬍�PH婯H吷t�    H兡 [�    H兡 [聾   �       �     FG            J      D   �        �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >�/   this  AI  	     @ 6   AJ        	  M        �  0
 M        0  5
 M        �  5

 >   _Ptr  AJ  
       AJ 0       N N N M        �   M        @   M        �  DE

 >b     temp  AK         AK 0      
 
  >"     ref  A  0       N N N                      0H� . h
   w  @  �  �  �  �  0  �  �  �   0   �/  Othis  9)       /   O �   8           J   �     ,       L �	   M �   N �0   P �,   
   0   
  
 k  
   o  
  
   
   �  
  
 �  
   �  
  
 �  
   �  
  
 l  
   p  
  
 |  
   �  
  
 �  
   �  
  
   
     
  
 0  
   4  
  
 H�    H�H兞�       @      �       �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       @      �       �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$H墊$禕H砍     D�L嬕禞I�%#"勪滘薎3薓3肏L还y7濴3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕LL3�禕	H3�禕
HLH3�禕HM�H葫�,(   H3菼拎HI嬃I菻凌H翲萀3葾禞I3薎嬔HH陵I嬃H拎H菻�H萀3葾禞
I3薎嬔HH陵I嬃H拎H菻�H萀3葾禞I3薎嬔HH陵I嬃H拎H菻�H萀3葾禕A禞I嬔I3薍陵HH3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3菼嬃HH拎H菻�H華禕L3葾禞I3薎嬔HH陵H3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3華禕HH3菼嬃H拎HH媩$H菻�H媆$H罥3撩   �   >  R G            E  
   9  �        �std::hash<nvrhi::BindingSetItem>::operator() 
 >�#   this  AJ        #  D   
 >�   s  AK          AR       & >#     value  AH  D      AQ  �     � M        S  伡in  M        �  伡KR  M          伡KR  M        �  伡KR  M          伡KR& M        �  伡8CJ
 >#    _Val  AJ  �    i  N N N N N N M        S  x丏} M        �  
丏
OR M          
丏
OR M        �  
丏
OR M          
丏
OR M        �  
丏
OR
 >#    _Val  AJ  T    a  N N N N N N M        W  "�" M        �  �" M        �  �" M        �  �" M          �" M        �  �"
 >#    _Val  AJ  %      N N N N N N M        V  "�� M        �  �� M           �� M        �  �� M          �� M        �  ��
 >#    _Val  AJ  �       N N N N N N M        U  "�� M        �  �� M          �� M        �  �� M          �� M        �  ��
 >#    _Val  AJ  �       N N N N N N" M        R  R(% M        �  R% M          R% M        v  R% M        O  R5 M        �  R8
 >#    _Val  AJ  0     �  N N N N N N" M        T  


N]" M        �  


E	T" M          


E	T" M        �  


E	T" M          


E	T, M        �  J

9>M
 >#    _Val  AP  3     �  N N N N N N                        @� � h   �  R  S  T  U  V  W  v  �  �  �  �  �  �  �             O  �  �  �  �  �                �#  Othis     �  Os  O  �   �           E  P     �       �
 �
   �
 �   �
 �   �
 �#   �
 �-   �
 �0   �
 �3   �
 �7   �
 ��   �
 ��   �
 ��   �
 ��   �
 ��   �
 ��   �
 ��   �
 �  �
 �D  �
 ��  �
 ��  �
 ��  �
 �-  �
 �2  �
 �9  �
 �>  �
 �D  �
 �,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 N  �    R  �   
   �      �   
 H  �    L  �   
 a  �    e  �   
 T  �    X  �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   @      �    0   �       �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   @      �    0   �       �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   @      �    0   �       �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H嬞H兞H�    H岾�    H岾HH媆$0H兡 _�       �       �    .   �       �     H G            2   
   #   :        �donut::engine::BindingCache::Clear 
 >*   this  AI  
       AJ        
  M        2   N M        1  	
 Z   �   N
 Z   G                         @  h   1  2   0   *  Othis  O �   H           2   �     <       \  �
   ]  �   ^  �   _  �#   `  �-   _  �,      0     
 m      q     
 }      �     
       $    
 H塡$L塋$ UVWATAUAVAWH冹0M嬦I嬝L嬺L孂3鰦蘒嫧   H铃I鐰焦y7濴;舤0H嬘H峀$x�    H嬒H玲H罤嬒H灵I臜菻3鵋兠 H;輚蠮�/A短H�%#"勪滘薍3虸汲     I秳$�   H3菼秳$�   H3菼秳$�   H3菼秳$�   H3菼秳$�   H3菼秳$�   H3菼秳$�   H3菼H嬅H凌H羚H菻根�,(   H荋菻3買峅H�    I�6H嬅H凌缎睹H3臝H3蠭H嬅H凌度H3袸H嬅H凌度H3袸H嬅H凌 度H3袸H嬅H凌(度H3袸H嬅H凌0度H3袸H嬅H凌8H3蠭I婳8H#蔋玲IO H婣I媁H;聇H�	H;Xtf怘;羣
H婡H;Xu馠嬸H咑t*H;騮%H媈H呟tH�H嬎�P怚�I�H吷tH��P怚峅H�    I嬈H媆$pH兡0A_A^A]A\_^]肒   �    $  �      �       �   �	  V G            2       8        �donut::engine::BindingCache::GetCachedBindingSet 
 >*   this  AJ        %  AW  %     
 >2   desc  AP        B  AP B     � 
 #  >�   layout  AQ          AT       u  D�    >�+   it  AL  �    N  C       '       C      B     �
 >#     hash & AH  2    �   (  9  J  [  l  , M        |  �2




,. M        R  
%仼'3 M        S  仼',$ >S*    _Where  AH  �    <  AH     	 
 >�*    _End  AK  �    >  AK     	  >�*    _Bucket_lo  AJ  �      AJ �    3    >    _Bucket  AJ  �      M        �  伻 M        �  伻 N N N N) M        ~  �2




) M        �  �2




) M          �2




) M        �  �2




) M          �2




S M        �  �26#433333;
 >#    _Val  AH  ;      AJ  P    G     %  6  & AK  5    �     ,  =  N  _   N N N N N N N M        B  �(
 N M        3  	�
 Z      N* M        {  �﹙�� M        �  �坴 M        �  �坴 M        �  �坴 M        �  �坴5 M        �  v>+4+4+4+4+4+4
 >#    _Val  AJ  �     �  N N N N N N M        z  O' M        �  '"	' >#     value  AM  )     �  >($    <begin>$L0  AI       W  M        j  ) M        �  ) M          0 M        3  0 N N N N M        ]  i N M        X  'B
 Z   �   N N N M        4  	�
 Z   M   N M        ?  侅		
 M        @  � M        �  �
 >b     temp  AJ        AJ       N N M        �  � >b     tmp  AI  �    !  AI       N M        �  	侕 M        �  	侕 N N N 0           8         @ � h:   �  {  �  3  4  ;  <  =  ?  @  B  H  \  ]  ^  j  l  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �      3  X  z  {  |  ~  �  �  �  �  �  �  �    R  S  �  �  �  �  �  �  �     p   *  Othis  �   2  Odesc  �   �  Olayout  x   h+  Oresult  9�      /   9
      /   O   �   �           2  �     �         �'     �v     �  !  �(  #  �+    �2  $  �5  #  �8  $  �F    �M  $  �W    �^  $  �h    �o  $  �y    ��  $  ��    ��  $  ��    ��  $  ��  %  ��  &  �  (  �  0  �  1  �,   �    0   �   
 {   �       �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 (  �    ,  �   
 <  �    @  �   
 [  �    _  �   
   �      �   
 $  �    (  �   
 C  �    G  �   
 S  �    W  �   
 x  �    |  �   
 �  �    �  �   
 �  �    �  �   
 5  �    9  �   
 E  �    I  �   
 e  �    i  �   
 �  �    �  �   
 "  �    &  �   
 G  �    K  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 L塋$ H塗$SUVWATAUAVAWH冹HM嬦M孁H嬺L嬹E3鞤塴$ A孆I嬝I嫧   H铃I韪箉7濴;舤?D嬭怘嬘H崒$�   �    H嬒H玲H罤嬒H灵I臜菻3鵋兠 H;輚虴3砀箉7濰�A短H�%#"勪滘薍3薍匠     H秳$�   H3菻秳$�   H3菻秳$�   H3菻秳$�   H3菻秳$�   H3菻秳$�   H3菻秳$�   H3菻H嬄H凌H羚H菻根�,(   H荋菻3袶墧$�   I峃H�    L�.荄$    L媱$�   A缎H3親秳$�   H3蠬秳$�   H3蠬秳$�   H3蠬秳$�   H3蠬秳$�   H3蠬秳$�   H3蠬秳$�   H3蠬I婲8H#蔋玲IN H婣I媀H;聇H�	L;@tH;羣H婡L;@u耠I嬇H吚t*H;聇%H媂H呟tH�H嬎�P怘�H�H吷tH��P怚峃H�    H�> 呪   I峃H�    I峃L崉$�   H峊$0�    H�H媨H�厜   I�H�M嬏M嬊H崝$�   �恅  I嬚H峀$(H;萾H�L�(H�H�H吷tH��P怘媽$�   H吷tL壃$�   H��P怘�>H9{tFH�t
H�H嬒�P怘婯H墈H吷tH��P愲!H9>tH�H嬒�P怘�H�>H吷tH��P怚峃H�    H嬈H兡HA_A^A]A\_^][锰\   �    E  �    =  �    P  �    f     )  �       �   �
  X G            B     0  9        �donut::engine::BindingCache::GetOrCreateBindingSet 
 >*   this  AJ        &  AV  &     
 >2   desc  AP           AW         >�   layout  AQ          AT        D�    >�+    it  AH 	    8   
 >#     hash  AP  \    �  B�   @     >+    entry  AI  m    �  AI -      M        |  �簛T M        R  
佇6, M        S  佇',$c >S*    _Where  AH  �    &  AH 	    8   
 >�*    _End  AK  �    >  AK 8    	  >�*    _Bucket_lo  AJ  �      AJ     6    >    _Bucket  AJ  �      N N M        ~  |乀 M        �  |乀 M          |乀 M        �  |乀 M          |乀4 M        �  乀/4+4+4+4+4+4+4
 >#    _Val  AK  c    �  N N N N N N N M        C  両 N M        3  	丂
 Z      N M        {  �眬� M        �  �垁� M        �  �垁� M        �  �垁� M        �  �垁�6 M        �  ��>+4+4+4+4+4+4
 >#    _Val  AJ  �     �  N N N N N N M        z  .3";  M        �  .	* >#     value  AM  1     �  >($    <begin>$L0  AI  4     i  M        j  4 M        �  4 M          ; M        3  ; N N N N M        l  1 M        �  1 N N M        ]  z N M        X  *P
 Z   �   N N N M        ?  �		
 M        @  �, M        �  �,
 >b     temp  AJ  )      AJ 8      N N M        �  �& >b     tmp  AI      !  AI 8    5 �  N M        �  	� M        �  	� N N N M        4  	�8
 Z   M   N M        D  俋
 Z   }   N M        1  	侹
 Z   �   N M        ?  傉	 M        @  傰 M        �  傰
 >b     temp  AJ  �      AJ     '  !  N N M        �  傢 >b     tmp  AM  �    +  AM $      N M        �  傓 M        �  傓#	 N N N M        @  偣 M        �  偣KB
 >b     temp  AJ  �      AJ �    S    3  B�   �    s 5 !  Bx  �    � o !  N N M        >  $倲 M        @  偔 M        �  偔
 >b     temp  AJ  �      AJ �      N N M        �  偋 >b     tmp  AK  �    !  AK �    t   3  G  J !  N M        A  倲C
 M        �  偂 N N N M        ?  �	
 M        @  � M        �  �
 >b     temp  AJ        AJ $      N N M        �  � >b     tmp  AM  q    � g +  AM $      N M        �  	� M        �  	� N N N M        2  	�$
 Z      N H           @         @ h@   �  {  �  1  2  3  4  ;  <  =  >  ?  @  A  C  D  H  K  \  ]  ^  j  l  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �      3  X  z  {  |  ~  �  �  �  �  �  �  �    R  S  �  �  �  �  �  �  �     �   *  Othis  �   2  Odesc  �   �  Olayout  �   #   Ohash  �   h+  Oresult  9"      /   94      /   9�      A#   9�      /   9�      /   9�      /   9�      /   9      /   9       /   O �   �           B  �     �       4  �.   6  ��   7  �@  9  �I  ;  �L  X  �T  <  �  =  �  >  �8  @  �A  B  �K  D  �T  F  �m  G  �z  I  ��  J  �  K  �  M  �$  O  �-  Y  ��   �   g F            )      #             �`donut::engine::BindingCache::GetOrCreateBindingSet'::`1'::dtor$0  >�   layout  EN  �         #                        �  O,       0      
 }       �      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
            
 5      9     
 E      I     
 e      i     
 u      y     
 �      �     
       	     
 (      ,     
 8      <     
 ]      a     
 m      q     
 �      �     
 ^      b     
 �      �     
 �      �     
       "     
 `      d     
 p      t     
 �      �     
 �      �     
 �      �     
 �      �     
 2      6     
 B      F     
 �      �     
 �      �     
 �      	     
 	      	     
 �	      �	     
 �	      �	     
 �	      �	     
 �	      �	     
 �
      �
     
 �
      �
     
 �
      �
     
            
 �      
     
 
      
     
 
      #
     
 /
      3
     
 ?
      C
     
 O
      S
     
 _
      c
     
 o
      s
     
 
      �
     
 �
      �
     
 x     |    
 �     �    
 @UH冹 H嬯婨 冟吚t僥 﨟媿�   �    H兡 ]�      H塡$ L塂$AVH冹 L嬹I嬝H婭H嬃M�I+罤柳H;�冣   H�������H塼$0H墊$8H;�囜   H�4�    H侢   r)H峃'H;�喢   �    H吚劵   H峹'H冪郒塆H咑t
H嬑�    H孁�3�I�I婩H+罤柳H吚t.H��    H侜   rL婣鳫兟'I+菻岮鳫凐w`I嬋�    H�>I�>I塅I塅H;鴗H�H兦H;鴘鬑媡$0H媩$8H媆$HH兡 A^肏嬔L岲$@I嬌�    H媆$HH兡 A^描    惕    蘬   �    �   �    �   �         /  �    5  �       �   �  � G            :     :  �        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::_Assign_grow 
 >.+   this  AJ          AV       '�    >   _Cells  AK        3p  �  � w   AK �     w  & 
 >9,   _Val  AI       $�    AP          D@    >    _Oldsize  AH  '     �  �  >*,    _Newend  AH  �     2  >    _Oldcapacity  AH  �     ,    AH �     	  >*,    _Newvec  AM  �       AM �     � \  k .  M        -   N M        �  �� N M        .  
0W��% M        �  U)
)%
��' M        �  ^$	%)
��
 Z   k   >    _Block_size  AJ  b       AJ .      >    _Ptr_container  AH  p       AH �     �  � 
 >0    _Ptr  AM  �       AM �     � \  k .  M        v  k
 Z   �   N N M        v  ��
 Z   �   N N M        �  

0
	 N N M        �  ��#" >1   _Backout  CM     �       CM    �         M        �  �� N M        �  �� N N M        �  .���� M        �  ��)]
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     &  AK 4     # M        w  
��#
`
 Z   S   >    _Ptr_container  AP  �       AP �     b  X  >    _Back_shift  AJ  �     9  AJ �     b ; "  N N N
 Z                            @ Z h   v  w  �  �  �  -  .  �  �  �  �  �  �  �  �  �  Z  �  �  �  �         $LN82  0   .+  Othis  8     O_Cells  @   9,  O_Val  O  �   �           :  0     �        �    �'   ! �0   " ��   $ ��   % ��   ' ��   + ��   , ��   - ��   . �  2 �  0 �"  2 �.  " �4  ' �,   �    0   �   
   �      �   
    �    $  �   
 I  �    M  �   
 i  �    m  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �       �   
 0  �    4  �   
 R  �    V  �   
 b  �    f  �   
 ;  �    ?  �   
 K  �    O  �   
 t  �    x  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 {  �      �   
 �  �    �  �   
 3  �    7  �   
 T  �    X  �   
 d  �    h  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  ,   �  ,  
    �      �   
 H塼$ WH冹 H�������H嬹H饺�   嬊H余H;�嚤  H岯�H塡$0H媈H荋饺L嬅L塼$@�罤隅H峃H�?�    H岹�H墌8H塅0H婩H� H嬋H;��)  H塴$8H砍     H�%#"勪滘�@ �     禤D禭H�	L3軱L3�禤LL3�禤LL3�禤LL3�禤LL3�禤LL3�禤LL3贚L#^0I零L^M�L;藆	I�I塁雤I婼L婡L;Bu/L�L;衪!L婬I�	L婣M�I婻H�M塀L塈H塒I塁�=L;蕋D  H婻L;BtOL;蕌馤婸I�
L婭I�L婤I� L塉L塓L堾I�H嬃H;�����H媗$8H媆$0L媡$@H媡$HH兡 _肔�L婬I�	L婣M�I婻H�M塀L塈H塒氪H�
    �    蘕   �    �  o   �  �       �   
  NG            �  
   �          �std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Forced_rehash 
 >f*   this  AJ          AL       ��  >#   	 _Buckets  AK        �O � AM  O     ;  AM �      C             /  C      �    
  >    _Max_storage_buckets  AH  %     �
 �
 >9,    _End  AI  ;     �n  >9,    _Inserted  AH  �      AH �     ?�   >9,    _Next_inserted  AJ  r     m >2,    _Bucket_lo  AS      � �   AS �     	 �  >    _Bucket  AS         M        �  "


 N M        ,  h M        ;  h M        �  l N N N M        �  7 M        �  7 M        �  7 N N N M        �  .
 M        �  ;  >#    _Value  AH  2     *  N N M        $  r�$ M        %  r�$ N N M        �  	��T M        ~  	��P M        �  	��P M          	��P M        �  	��P M          	��P M        �  	��M
 >#    _Val  AS  �     Q  N N N N N N N M        "  �� M        '  �� N N M        %  � N M        �  � M        �  � N N M        '  �' N& M        -  �/$#$#$c$ >�*    _Before_prev  AK  A      AK �      �  >�*    _Last_prev  AP  :      AP �     � r  >�*    _First_prev  AQ  3    #  AQ �     k �  N M        %  乂 N& M        -  乷$#$#$c$ >�*    _Before_prev  AP  �      AP �     � r  >�*    _Last_prev  AQ  z      AQ �     k �  >�*    _First_prev  AR  s       AR �     � , �    N M        �  乨 M        �  乨 N N M        &  乣 N& M        -  伡$#$#$c$ >�*   _First  AR  �    #  AR �     � , �    >�*    _Before_prev  AK  �      AK �      �  >�*    _Last_prev  AP  �      AP �     � r  >�*    _First_prev  AQ  �      AQ �     k �  N Z   �  6                         @ � h    �  {  �  �  �  �  �  �  ,  ~  �  �  �       !  "  #  $  %  &  '  (  )  ,  -  ;  �  �  �  �           $LN138  0   f*  Othis  8   #   O_Buckets  O �   X          �  0  (   L      � �
   � �   � �   � �   � �%   � �.   � �7   � �;   � �B   � �J   � �O   � �\   � �`   � �h   � �o   � ��   � ��   � ��   � �   � �  � �  � �  � �  � �  � �  � �'  � �-  � �/  � �P  � �T  � �V  � �`  � �j  � �o  � ��  � ��  � ��   ��  � ��  � �,   �    0   �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 %  �    )  �   
 H  �    L  �   
 p  �    t  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 #  �    '  �   
 *  �    .  �   
 :  �    >  �   
 c  �    g  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 !  �    %  �   
 1  �    5  �   
 Z  �    ^  �   
 j  �    n  �   
 �  �    �  �   
 �  �    �  �   
 G  �    K  �   
 W  �    [  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 		  �    
	  �   
 �	  *   �	  *  
 
  �    
  �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       O            �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �   $   �   $  
 �   �    �   �   
 H塡$UVWATAUAVAWH冹0M嬸L孃H嬹I;��=  L媔H婹H塗$(M媑L墹$�   I嬤A禣H�%#"勪滘薍3菼赋     IA禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼H婩0H#罤拎H翲塂$xH�H墝$�   H婡H塂$  H嬰H孄H�H婳H吷tH荊    H��P惡    H嬒�    H�NH;l$ t I;辵翷9�$�   �/  H婰$xH��"  H婰$xL9�$�   uL�)I嬇�I嬆H堿I;�匊   禟H�%#"勪滘薍3菻撼     H禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻L媐0L#酙龄Ld$(M媩$@ fff�     H嬰H孄H�H婳H吷tH荊    H��P惡    H嬒�    H�NI;飔I;辵腎�$H媱$�   H�H塁�"M�,$M塴$I;��
���L嫟$�   I�$L塩I嬈H媆$pH兡0A_A^A]A\_^]�
  �      �       �   H	  PG            {     f  �        �std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Unchecked_erase 
 >f*   this  AJ          AL       \ >S*   _First  AK          AW       F AW `    � z  >�*   _Last  AP          AV       \ >*,    _Bucket_bounds  AK  .     �  AK �     &     B(   3     H >�-   _Eraser  CI     B     ! CI    c     
 >�*    _End  AU  *     9 AU c      >�-    _Bucket_lo  AH  �       AJ  1    /    AJ `     �  Bx   �     � >    _Bucket  AH  �       >�-    _Bucket_lo  AT  �      AT `    p �  >    _Bucket  AT  �      M        �  3 N M        �  {B M        ~  tB M        �  tB M          tB M        �  tB M          tB5 M        �  B>(4(4(4(4(4(4
 >#    _Val  AJ  T     x  N N N N N N N! M        �  ��#
 >�*    _Oldnext  AM  �     z  AM �     � z e  M        �  ��
 M        �  

� M        �  
� M        �  
�
 Z   �   N N N M        �  �� M        @  �� M        �  ��DE
 >b     temp  AJ  �       AJ       N N N N N M        �  s乣 M        ~  l乣 M        �  l乣 M          l乣 M        �  l乣 M          l乣6 M        �  乣>'4'4'4'4'4'4
 >#    _Val  AJ  q    �  N N N N N N N! M        �  侒#
 >�*    _Oldnext  AM  �    e  AM `    � e  M        �  侚
 M        �  

� M        �  
� M        �  
�
 Z   �   N N N M        �  侚 M        @  侚 M        �  侚DE
 >b     temp  AJ  �      AJ       N N N N N M        �  �0 N 0           8         0@� j h   �  w  @  �  �  �  �  �  �  ~  �  �  �  �  �  �  �  �    x  �  �  �  �     p   f*  Othis  x   S*  O_First  �   �*  O_Last  9�       /   9
      /   O�   0          {  0  #   $      � �     �&    �*    �3    �B   
 ��    ��    ��    ��    ��    �   �   �   �,   �4   �9  ! �H  # �K  $ �N  % �P  & �S  + �`  , ��  . ��  0 ��  2 ��  3 �"  4 �'  8 �,  : �0  ; �A  @ �E  A �J  + �[  E �,   �    0   �   
 u  �    y  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 #  �    '  �   
 3  �    7  �   
 G  �    K  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 
  �      �   
 /  �    3  �   
 T  �    X  �   
 d  �    h  �   
 �  �    �  �   
 u  �    y  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 .  �    2  �   
 >  �    B  �   
    �    $  �   
 0  �    4  �   
 4	  �    8	  �   
 D	  �    H	  �   
 \	  �    `	  �   
 H塡$H塴$H塼$ WH冹 H孂H婭H吷剹   H婫8H凌H;羦H媁L嬄H�H嬒�    雟H婳H婣3鞨�(H�H呟t2fD  H�3H婯H吷tH塳H��P惡    H嬎�    H嬣H咑u訦婫H� H婫H堾H塷H婫H塂$0L岲$0H媁 H婳�    怘媆$8H媗$@H媡$HH兡 _�?   �    �   �    �         �   �  EG            �      �   G        �std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::clear 
 >f*   this  AJ          AM       �  >    _Oldsize  AJ       . #   AJ �       >�*    _Head  AK  5       M        �  	�� M        �  	�� M        �  	�� N N N# M        �  E'' M        �  II+
 >S*    _Pnode  AI  U     f  AI �       >S*    _Pnext  AL  c     )  AL `     j  )  M        �  c
 M        �  

w M        �  
w M        �  
w
 Z   �   N N N M        �  c M        @  c M        �  cDE
 >b     temp  AJ  g       AJ w       N N N N N N Z   �                          0@� ^ h   w  {  @  �  �  �  �  �  �  �  �  ,  �  �  �  �  �  �  x  �  �  �   0   f*  Othis  9s       /   O   �   h           �   0  
   \       { �   � �   � �$   � �1   � �5   � �C   � �E   � ��   � ��   � �,   �    0   �   
 j  �    n  �   
 z  �    ~  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 }  �    �  �   
 �  �    �  �   
 H婹H�    H呉HE旅   C      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  20    2                       ;   
 
4 
2p    B                       A    20    <                         G   
 
4 
2p    B           !      !      M    20    <           "      "      S   
 
4 
2p    B           #      #      Y    �                  %      %      _   
 
t 4     E          &      &      e    2p    8           '      '      k   
 4 R���
�p`P                 w       {          (      (      q   h           z      }          �    �Q  d	 T 4 2p                 �       �           )      )      �   h           �      �          �    �
 
d	 
2p    2           +      +      �   ! � 4     2          +      +      �   2   {           +      +      �   ! T 2   {          +      +      �   {   �          +      +      �   !   2   {          +      +      �   �  �          +      +      �   !   �  T  4     2          +      +      �   �  �          +      +      �   !       2          +      +      �   �  �          +      +      �    4	 2�    :           -      -      �   !
 
t d     :          -      -      �   :             -      -      �   !       :          -      -      �     .          -      -      �   !   t  d     :          -      -      �   .  :          -      -      �   
 4 R����
p`P                 �       2          .      .      �   (           �      �          �    �, 	 �����p
`P0                   �       B          /      /      �   (           �      �       .             �    �,�P(0, 2P    )                       �   
 
4 
2p    2           0      0      �    B                   �       "           1      1      �   h           �                �    2 T
 4	 2�p`    �           2      2         
 4 R���
�p`P                        +          4      4      
   (                    
    @:    @h   	      
   ���        >           5      5         ! t      >          5      5         >   b           5      5         !       >          5      5         b   �           5      5      %    20                 1       J           6      6      +   h           4      7          �    R B      :           8      8      :                               r      �       �    Unknown exception                             ~      �       �                                �      �       �    bad array new length                                �       R                                 X      ^      d                   .?AVbad_array_new_length@std@@     e               ����                      U      �                    .?AVbad_alloc@std@@     e              ����                      [      �                    .?AVexception@std@@     e               ����                      a      �        ����    ����        ��������unordered_map/set too long invalid hash bucket count                                       a      u      r                         x                   {               ����    @                   a      u                                         [      �      ~                         �                           �      {              ����    @                   [      �                                         U      �      �                         �                                   �      �      {              ����    @                   U      �      _   �   (   & 
34        std::exception::`vftable'    @      @  
    �   (   & 
34        std::bad_alloc::`vftable'    F      F  
    �   3   1 
34        std::bad_array_new_length::`vftable'     I      I  
 �+鷯8}`秓亾d淅8cI橗cS1箱�,�	h�K蜌�(N滍y臖Wg櫩4b�鱣.颦A勽洶N騔登挡鎵A勽洶N�942訥鏴0穦�1馪簜餏2g"0穦�1馪漋8ck烉20穦�1馪嵌烌S岄A勽洶N镶�6弌栃哵囄嶸洄慬媥]遂哯峲E�頾q国芩靻Z峲7t\<剳.j<�H匄脠繩蟣�<�H匄肵�!縐�<�H匄芒�(�+遂哯峲�8饒?�"蘴�&y�,j窶wTM訧)#hv瓯訧)#hv瓯屢b綩藋T＠�/槿鈙y櫹[!8yi5q�.;暻�	J撐�烇#�`暗菨	J撐�BVq スZ殿G�緁訾��駶S芏蹡墕�:鉧榑(葹B+汫d"W鐹葹B+汫,拹@~7鰻葹B+汫艽郓sM菨	J撐�泋�=mP�(！
Z�,螚彭嚤踖p禭c�&敡?諭嚤踖p禭9"虰錱6萪O��!\6悑⒂K霵婬(~ǐ'�'項j�<p*@d裞儌頂"譥�康v~.`�獉Ⅵ��"錹嶀預棊膬�屓绀?貚犷A棊膬獃0_弸c$j驱峛�#�+� e�%箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>~;A嬰t舘朽�?軔z蓏沵vV]恵�喤憺斱�,^z>�:贉j[�&+瞹衑sF闇漓�o昮#Zg�2F臮O,鍹觍4�鸆�%I栶賑?T�↘槱A�<:斫FyV懗YW�晤�
骘Y氐瀃w枸鋑nN鵘J怏熮_d<3穗嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪Tq��袴~杀蘢繌�bx斢[目湸寷D0�#*O魕囱Q� 粎鹴aR�,F_棢杻#Q邽dk+p琢犇Li籩项扭咞taR�,F_棢杻#Q蹚�:%UFw壱3,�4q胭o�!骬6�/s.�.:6俙貱霯蝭﹛_韃�)裯 j|�8厢M\筹s,;窇藿啓��=緱�G�6'j职DgCJ8剩杫X]绪/ｎ	蜍R僁舴�:kn嶜�:5齵h周洊{= 栰宧霅;�d_f湲z竘瑲豊B耴驂�dd�a�:_棢杻#Q鸢莉讟4叵�Q徘忙g醞 録�.dd�a�:墧Wk肗向3e剸\M樢閣yQ)傂螨9E\$L釉�3,�4q胭了5YJq覜垒�咞taR�,F_棢杻#Q`�G埜驄疕n\汿D椣]箅我F詍CD� K^�dd�a�:r�7)}玖焋tjxⅲ<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦巧S�8萀D脸v傘]-屾咞taR�,F_棢杻#Q吀qv蕞	�-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H阱叿} 5]叨蝝�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 笖E@wX+]!阑,�粰趭+�揃T爃.L�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       �              .debug$T       p                 .rdata         0       ��                     .text$mn       :      眡�     .debug$S                    .text$mn       7       Zo萅     .debug$S       t             .text$mn    	   c       謠锖     .debug$S    
   �         	    .text$mn       c       謠锖     .debug$S       |             .text$mn    
          昹	�     .debug$S       l  
       
    .text$mn              昹	�     .debug$S       p  
           .text$mn              昹	�     .debug$S       t  
           .text$mn       c       謠锖     .debug$S       x             .text$mn       D       磚
:     .debug$S       �             .text$mn       p       '      .debug$S       �             .text$mn       p       '      .debug$S       �             .text$mn              !'�     .debug$S       X             .text$mn              !'�     .debug$S       \             .text$mn              !'�     .debug$S        `             .text$mn    !   p       '      .debug$S    "   �         !    .text$mn    #   +     L啎     .debug$S    $   h  Z       #    .text$x     %         S�#    .text$x     &         S�#    .text$mn    '   �       `螏�     .debug$S    (   �         '    .text$mn    )   i       L}&2     .debug$S    *   @         )    .text$mn    +   �       Y�     .debug$S    ,   H         +    .text$mn    -   �       Y�     .debug$S    .   D         -    .text$mn    /   �      H!E     .debug$S    0   �         /    .text$mn    1   8      5斕     .debug$S    2   H  
       1    .text$mn    3   G       M>$w     .debug$S    4   �         3    .text$mn    5   G       M>$w     .debug$S    6            5    .text$mn    7   G       M>$w     .debug$S    8            7    .text$mn    9   �       Y�     .debug$S    :   D         9    .text$mn    ;           _葓�     .debug$S    <   8         ;    .text$mn    =   <      .ズ     .debug$S    >   0  
       =    .text$mn    ?   <      .ズ     .debug$S    @   L  
       ?    .text$mn    A   !      :著�     .debug$S    B   <         A    .text$mn    C   2      X于     .debug$S    D   <         C    .text$mn    E   "       坼	     .debug$S    F   �         E    .text$mn    G         �!     .debug$S    H   L         G    .text$mn    I   J      O�0�     .debug$S    J   h         I    .text$mn    K         ��#     .debug$S    L   �          K    .text$mn    M         ��#     .debug$S    N   �          M    .text$mn    O   E      �<x�     .debug$S    P   <	         O    .text$mn    Q   B      贘S     .debug$S    R             Q    .text$mn    S   B      贘S     .debug$S    T            S    .text$mn    U   B      贘S     .debug$S    V   �          U    .text$mn    W   2      2p翫     .debug$S    X   h         W    .text$mn    Y   2     粍6�     .debug$S    Z   �
  >       Y    .text$mn    [   B     亝:G     .debug$S    \     n       [    .text$x     ]   )      iA� [    .text$mn    ^   :     愽鉻     .debug$S    _   �  <       ^    .text$mn    `   �     濷I     .debug$S    a   p  P       `    .text$mn    b          aJ鄔     .debug$S    c   �          b    .text$mn    d   {     -絽     .debug$S    e   �
  H       d    .text$mn    f   �      X瀏x     .debug$S    g             f    .text$mn    h         崪覩     .debug$S    i   �          h        )                8                H                X                {                �                �                �                �                �                                     C        :      M        T      h        t      U        �          i�                    �      =        �      Q        �          i�                          A        6      K        [      ?        �      S        �          i�                    �      b              )        +      9        U      O        �      -        �      5        7      7        �      3        �      1                      F      d        �      f        �      `        �      ^        �	      Y        r
      [              W        ,      E        a      /        �      +              #        r
      '        �      ;                      1      G        �      I        W              �                            P              �                             R      !        ~      	        �      
        $              �              �              )              V      %        �      ]        q      &        �                           ceilf            $LN5        C    $LN10       U    $LN7        =    $LN13       Q    $LN10       ?    $LN16       S    $LN3        b    $LN4        b    $LN164      O    $LN4        1    $LN186      d    $LN67       f    $LN138  �  `    $LN142      `    $LN82   :  ^    $LN85       ^    $LN178      Y    $LN247      [    $LN9        W    $LN10       E    $LN41       /    $LN206  +  #    $LN215      #    $LN20       '    $LN43       I    $LN14   :       $LN17           .xdata      j          （亵C              j    .pdata      k          T枨C        <      k    .xdata      l          %蚘%U        d      l    .pdata      m         惻竗U        �      m    .xdata      n          （亵=        �      n    .pdata      o         2Fb�=        �      o    .xdata      p          %蚘%Q              p    .pdata      q         惻竗Q        )      q    .xdata      r          （亵?        O      r    .pdata      s         2Fb�?        �      s    .xdata      t          %蚘%S        �      t    .pdata      u         惻竗S        �      u    .xdata      v          懐j瀊              v    .pdata      w         Vbv鵥        I      w    .xdata      x          Uqi筄        x      x    .pdata      y         犦怫O        �      y    .xdata      z          3��1              z    .pdata      {         菻(V1        r      {    .xdata      |          /巴刣        �      |    .pdata      }         餃d        -      }    .xdata      ~   	      �#荤d        �      ~    .xdata               jd        �           .xdata      �          a煫Ud        e"      �    .xdata      �         ╬auf        �#      �    .pdata      �         vf        �$      �    .xdata      �   	      �#荤f        �%      �    .xdata      �         jf        �&      �    .xdata      �          (岙錰        �'      �    .xdata      �          G栚鱜        �(      �    .pdata      �          T枨`        �)      �    .xdata      �         0W圫`        �*      �    .pdata      �         ]%(	`        �+      �    .xdata      �         甞淰`        �,      �    .pdata      �         8迁輅        �-      �    .xdata      �         毕癭        �.      �    .pdata      �         煐罢`        �/      �    .xdata      �         �(崚`        �0      �    .pdata      �         庇僎`        �1      �    .xdata      �         炀縹`        �2      �    .pdata      �         荖疂`        �3      �    .xdata      �          ii@^        �4      �    .pdata      �         礝
^        6      �    .xdata      �         塯4穅        }7      �    .pdata      �         囥鱢^        �8      �    .xdata      �         Y璣        C:      �    .pdata      �         s�&k^        �;      �    .xdata      �         n奧w^        	=      �    .pdata      �         '擊俕        l>      �    .xdata      �          _痀        �?      �    .pdata      �         )EwPY        e@      �    .xdata      �   	      � )9Y        鶣      �    .xdata      �         jY        扐      �    .xdata      �          品靁        0B      �    .xdata      �          ,鎛:[        菳      �    .pdata      �         
逷Z[        `C      �    .xdata      �   	      � )9[        鰿      �    .xdata      �         T裔衃        慏      �    .xdata      �          鉄VA[        1E      �    .xdata      �          k筟        薊      �    .pdata      �         }y9鎇        rF      �    .xdata      �          %蚘%W        G      �    .pdata      �          T枨W        JG      �    .xdata      �         /
        {G      �    .pdata      �         +eS籈        窯      �    .xdata      �   	      �#荤E        鬐      �    .xdata      �         jE        3H      �    .xdata      �          3狷 E        xH      �    .xdata      �          H猛�/        稨      �    .pdata      �         暫`g/        I      �    .xdata      �          �"膧#        dI      �    .pdata      �         漝魰#        貸      �    .xdata      �   	      � )9#        ML      �    .xdata      �         �T#        腗      �    .xdata      �   
       � 裇#        AO      �    .xdata      �          確'        窹      �    .pdata      �         OAG�'        R      �    .xdata      �         +縬['        MS      �    .pdata      �         蹷謔'        橳      �    .xdata      �         ＋)'        錟      �    .pdata      �         穣'        1W      �    .xdata      �         蚲7MI        }X      �    .pdata      �         %轢窱        Y      �    .xdata      �   	      �#荤I        碮      �    .xdata      �         jI        RZ      �    .xdata      �          攰eI        鯶      �    .xdata      �          �9�        擺      �    .pdata      �         礝
        馵      �    .rdata      �                      M\     �    .rdata      �          �;�         d\      �    .rdata      �                      媆     �    .rdata      �                           �    .rdata      �          �)         腬      �    .xdata$x    �                      餦      �    .xdata$x    �         虼�)         ]      �    .data$r     �   /      嶼�         5]      �    .xdata$x    �   $      4��         Z]      �    .data$r     �   $      鎊=         痌      �    .xdata$x    �   $      銸E�         蒥      �    .data$r     �   $      騏糡         ^      �    .xdata$x    �   $      4��         "^      �        a^           .data       �           烀�          t^      �        ╚     �    .rdata      �          ��         蟐      �    .rdata      �          藾味         _      �    .rdata$r    �   $      'e%�         2_      �    .rdata$r    �         �          J_      �    .rdata$r    �                      `_      �    .rdata$r    �   $      Gv�:         v_      �    .rdata$r    �   $      'e%�         昣      �    .rdata$r    �         }%B         璤      �    .rdata$r    �                      胈      �    .rdata$r    �   $      `         賍      �    .rdata$r    �   $      'e%�         鴂      �    .rdata$r    �         �弾         `      �    .rdata$r    �                      <`      �    .rdata$r    �   $      H衡�         ]`      �    .rdata      �          eL喳         嘸      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �                   梎  ?c_IdentityTransform@rt@nvrhi@@3QBMB ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z _Smtx_lock_exclusive _Smtx_lock_shared _Smtx_unlock_exclusive _Smtx_unlock_shared __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ??$hash_combine@I@nvrhi@@YAXAEA_KAEBI@Z ??$hash_combine@_K@nvrhi@@YAXAEA_KAEB_K@Z ??R?$hash@UBindingSetItem@nvrhi@@@std@@QEBA_KAEBUBindingSetItem@nvrhi@@@Z ??$hash_combine@PEAVIResource@nvrhi@@@nvrhi@@YAXAEA_KAEBQEAVIResource@0@@Z ??$hash_combine@W4ResourceType@nvrhi@@@nvrhi@@YAXAEA_KAEBW4ResourceType@0@@Z ??$hash_combine@W4TextureDimension@nvrhi@@@nvrhi@@YAXAEA_KAEBW4TextureDimension@0@@Z ??$hash_combine@W4Format@nvrhi@@@nvrhi@@YAXAEA_KAEBW4Format@0@@Z ??$hash_combine@UBindingSetItem@nvrhi@@@nvrhi@@YAXAEA_KAEBUBindingSetItem@0@@Z ??$_Hash_representation@I@std@@YA_KAEBI@Z ?_Unchecked_erase@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z ?clear@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAAXXZ ?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z ?GetCachedBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z ?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z ?Clear@BindingCache@engine@donut@@QEAAXXZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??$hash_combine@UBindingSetDesc@nvrhi@@@nvrhi@@YAXAEA_KAEBUBindingSetDesc@0@@Z ??$hash_combine@PEAVIBindingLayout@nvrhi@@@nvrhi@@YAXAEA_KAEBQEAVIBindingLayout@0@@Z ??$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Hash_representation@PEAVIBindingLayout@nvrhi@@@std@@YA_KAEBQEAVIBindingLayout@nvrhi@@@Z ??$_Hash_representation@W4Format@nvrhi@@@std@@YA_KAEBW4Format@nvrhi@@@Z ??$_Hash_representation@W4TextureDimension@nvrhi@@@std@@YA_KAEBW4TextureDimension@nvrhi@@@Z ??$_Hash_representation@W4ResourceType@nvrhi@@@std@@YA_KAEBW4ResourceType@nvrhi@@@Z ??$_Hash_representation@PEAVIResource@nvrhi@@@std@@YA_KAEBQEAVIResource@nvrhi@@@Z ??$_Hash_representation@_K@std@@YA_KAEB_K@Z ??$_Fnv1a_append_value@PEAVIBindingLayout@nvrhi@@@std@@YA_K_KAEBQEAVIBindingLayout@nvrhi@@@Z ??$_Fnv1a_append_value@W4Format@nvrhi@@@std@@YA_K_KAEBW4Format@nvrhi@@@Z ??$_Fnv1a_append_value@W4TextureDimension@nvrhi@@@std@@YA_K_KAEBW4TextureDimension@nvrhi@@@Z ??$_Fnv1a_append_value@W4ResourceType@nvrhi@@@std@@YA_K_KAEBW4ResourceType@nvrhi@@@Z ??$_Fnv1a_append_value@PEAVIResource@nvrhi@@@std@@YA_K_KAEBQEAVIResource@nvrhi@@@Z ??$_Fnv1a_append_value@_K@std@@YA_K_KAEB_K@Z ?dtor$0@?0???$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z@4HA ?dtor$0@?0??GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z@4HA ?dtor$1@?0???$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z@4HA _CxxThrowException __CxxFrameHandler4 $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??R?$hash@UBindingSetItem@nvrhi@@@std@@QEBA_KAEBUBindingSetItem@nvrhi@@@Z $pdata$??R?$hash@UBindingSetItem@nvrhi@@@std@@QEBA_KAEBUBindingSetItem@nvrhi@@@Z $unwind$??$hash_combine@UBindingSetItem@nvrhi@@@nvrhi@@YAXAEA_KAEBUBindingSetItem@0@@Z $pdata$??$hash_combine@UBindingSetItem@nvrhi@@@nvrhi@@YAXAEA_KAEBUBindingSetItem@0@@Z $unwind$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $pdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $cppxdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $stateUnwindMap$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $ip2state$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $unwind$?clear@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAAXXZ $pdata$?clear@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAAXXZ $cppxdata$?clear@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAAXXZ $stateUnwindMap$?clear@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAAXXZ $ip2state$?clear@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAAXXZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $chain$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $pdata$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $unwind$?GetCachedBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $pdata$?GetCachedBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $cppxdata$?GetCachedBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $stateUnwindMap$?GetCachedBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $ip2state$?GetCachedBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $unwind$?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $pdata$?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $cppxdata$?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $stateUnwindMap$?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $ip2state$?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z $unwind$?dtor$0@?0??GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z@4HA $pdata$?dtor$0@?0??GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z@4HA $unwind$?Clear@BindingCache@engine@donut@@QEAAXXZ $pdata$?Clear@BindingCache@engine@donut@@QEAAXXZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??$hash_combine@UBindingSetDesc@nvrhi@@@nvrhi@@YAXAEA_KAEBUBindingSetDesc@0@@Z $pdata$??$hash_combine@UBindingSetDesc@nvrhi@@@nvrhi@@YAXAEA_KAEBUBindingSetDesc@0@@Z $unwind$??$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z $pdata$??$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z $cppxdata$??$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z $stateUnwindMap$??$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z $ip2state$??$_Try_emplace@AEB_K$$V@?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEB_K@Z $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $cppxdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $ip2state$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@5f000000 