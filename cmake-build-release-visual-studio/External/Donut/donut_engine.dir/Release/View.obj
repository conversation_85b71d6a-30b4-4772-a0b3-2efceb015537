d唗翀蜫 @      .drectve        <  4:               
 .debug$S        刎  p;  H        @ B.debug$T        p   �             @ B.rdata          `  0             @ P@.text$mn        �   �              P`.debug$S        �  ( �        @B.text$mn        n  ,              P`.debug$S        �  � b        @B.text$mn        �   �              P`.debug$S        �  � J         @B.text$mn        �   �               P`.debug$S        ,  )! U"        @B.text$mn        .   �"              P`.debug$S        �   �" �#        @B.text$mn        =   $              P`.debug$S        �   T$ H%        @B.text$mn        ,   �%              P`.debug$S        �   �% �&        @B.text$mn           '              P`.debug$S        �   ' (        @B.text$mn        :   `( �(         P`.debug$S          �( �*        @B.text$mn        q   P+              P`.debug$S        �  �+ �.        @B.text$mn        �  �/ �1     	    P`.debug$S         
  2 ?     T   @B.text$x         =   TB 態         P`.text$mn        d   疊 C         P`.debug$S        \  C yF        @B.text$mn        p   AG              P`.debug$S        ,  盙 軭        @B.text$mn        �   I              P`.debug$S        �   臝 礘        @B.text$mn        "   馢              P`.debug$S        (  K ;L        @B.text$mn        1  wL ∕         P`.debug$S        �  糓 HP        @B.text$mn        �  黀 芕         P`.debug$S        �  鳹 坃        @B.text$mn        A  宍 蚫         P`.debug$S        �  e l        @B.text$mn        a   m              P`.debug$S        �  vm o        @B.text$mn        3   Zo              P`.debug$S        ,  峯 筽        @B.text$mn        
   鮬              P`.debug$S        �   q 苢        @B.text$mn           r              P`.debug$S        �   r 錼        @B.text$mn        �   !s 羢         P`.debug$S        X  誷 -u        @B.text$mn        <   iu          P`.debug$S        0  胾 髒     
   @B.text$mn        <   Ww 搘         P`.debug$S        L  眞 齲     
   @B.text$mn        !   ay 倅         P`.debug$S        <  杫 襷        @B.text$mn        2   { @{         P`.debug$S        <  T{ 恷        @B.text$mn        K   }              P`.debug$S        �  S}         @B.text$mn           � �         P`.debug$S        �   � 畝        @B.text$mn           謤 閫         P`.debug$S        �   齹 輥        @B.text$mn        B   � [�         P`.debug$S           y� y�        @B.text$mn        B   祪 鲀         P`.debug$S          � %�        @B.text$mn        B   a�          P`.debug$S        �   羺 絾        @B.text$mn        H   鶈              P`.debug$S        �  A� �        @B.text$mn        �   � 眾         P`.debug$S        �  粖 硯        @B.text$x            弿 洀         P`.text$mn                         P`.debug$S        �   ◤ 槓        @B.text$mn           詯              P`.debug$S        �   讗 菓        @B.text$mn        B  � E�         P`.debug$S        `  嫍 霛     6   @B.text$mn           �              P`.debug$S        �  �      
   @B.text$mn           �              P`.debug$S        |  -�      
   @B.text$mn           
�              P`.debug$S        l  � }�     
   @B.text$mn           幞 楗         P`.debug$S        �   螗 沪        @B.text$mn           悝              P`.debug$S        �    雩        @B.text$mn        /   2�              P`.debug$S        P  a� 暴        @B.text$mn        �   � 叒         P`.debug$S        \  彧 氆        @B.text$mn        $   ;�              P`.debug$S        �   _� [�        @B.text$mn        $   棴              P`.debug$S        �   画 樊        @B.text$mn        /   螽              P`.debug$S        T  "� v�        @B.text$mn        ;   瓢              P`.debug$S        L  � M�        @B.text$mn        	   澆              P`.debug$S        �   Σ 幊        @B.text$mn        
   食              P`.debug$S        d  壮 ;�        @B.text$mn           嫷              P`.debug$S        <  湹 囟        @B.text$mn           (�              P`.debug$S        <  .� j�        @B.text$mn        	   焊              P`.debug$S          酶 酃        @B.text$mn           �              P`.debug$S        �   '� �        @B.text$mn        P   O� 熁         P`.debug$S        h  郴 �        @B.text$mn        !   W� x�         P`.debug$S          偨 娋        @B.text$mn        /   凭              P`.debug$S        H  蹙 =�        @B.text$mn        {   嵗 �         P`.debug$S        T  � f�        @B.text$mn        !   堵              P`.debug$S          茁 竺        @B.text$mn        !   /�              P`.debug$S          P� h�        @B.text$mn        
   づ              P`.debug$S           浩        @B.text$mn        	   銎              P`.debug$S        �   �� 髑        @B.text$mn           3�              P`.debug$S          ?� W�        @B.text$mn           撋              P`.debug$S        �    ㄊ        @B.text$mn            涫              P`.debug$S        �   �  �        @B.text$mn           P�              P`.debug$S        �   [� C�        @B.text$mn        !   � 犕         P`.debug$S                   @B.text$mn        !   晡 �         P`.debug$S          � �        @B.text$mn        $   U�              P`.debug$S        �   y� e�        @B.text$mn           ⊙              P`.debug$S        �   佳 ひ        @B.text$mn           嘁              P`.debug$S        �    逵        @B.text$mn           !�              P`.debug$S        �   :� .�        @B.text$mn        /   j�              P`.debug$S        L  櫿 逯        @B.text$mn        ;   5�              P`.debug$S        D  p� 簇        @B.text$mn        �   � 椯         P`.debug$S        <  ≠ 蒇        @B.text$mn        
  i� v�         P`.debug$S        �  炤 >�        @B.text$mn        }   庍              P`.debug$S        �  � 俞        @B.text$mn           #� /�         P`.debug$S        ,  9� e�        @B.text$mn           点              P`.debug$S        �   搞         @B.text$mn           桎              P`.debug$S        �   脘 坼        @B.text$mn           �              P`.debug$S        �   � 
�        @B.text$mn           F�              P`.debug$S        �   N� B�        @B.text$mn           ~�              P`.debug$S        �   佽 }�        @B.text$mn           归              P`.debug$S          验 殛        @B.text$mn           %�              P`.debug$S        �   (� �        @B.text$mn           X�              P`.debug$S        �   `� X�        @B.text$mn           旐              P`.debug$S        �   楉 囶        @B.text$mn           妙              P`.debug$S        �   祁 讹        @B.text$mn           蝻              P`.debug$S        (   !�        @B.text$mn        (  q� 欝         P`.debug$S        �   m�        @B.text$mn        H                 P`.debug$S        t  A� 跌     
   @B.text$mn           �              P`.debug$S        (  (� P�        @B.text$mn          狕          P`.debug$S        P  酏 D     0   @B.text$mn           $              P`.debug$S        @  * j        @B.text$mn        �   � O         P`.debug$S        �  w �	     
   @B.text$mn        �  [
 C         P`.debug$S           �        @B.text$mn        )  o �     	    P`.debug$S        (  � "        @B.text$mn            j" �"         P`.debug$S        �   �" l#        @B.text$mn           �# �#         P`.debug$S          �# �$        @B.text$mn        A   !% b%         P`.debug$S        �  v% 2(        @B.text$mn           6) I)         P`.debug$S        �   S) '*        @B.xdata             c*             @0@.pdata             w* �*        @0@.xdata             �*             @0@.pdata             �* �*        @0@.xdata             �*             @0@.pdata             �* �*        @0@.xdata             	+             @0@.pdata             + +        @0@.xdata             ;+             @0@.pdata             G+ S+        @0@.xdata             q+             @0@.pdata             y+ �+        @0@.xdata             �+             @0@.pdata             �+ �+        @0@.xdata             �+             @0@.pdata             �+ �+        @0@.xdata             ,             @0@.pdata             , +,        @0@.xdata          (   I, q,        @0@.pdata             {, �,        @0@.xdata             �,             @0@.pdata             �, �,        @0@.xdata             �, �,        @0@.pdata             - 
-        @0@.xdata             +- ?-        @0@.pdata             ]- i-        @0@.xdata          8   �- �-        @0@.pdata             �- �-        @0@.xdata          0   . 7.        @0@.pdata             U. a.        @0@.xdata             . �.        @0@.pdata             �. �.        @0@.xdata             �. �.        @0@.pdata             �. /        @0@.xdata             #/             @0@.pdata             +/ 7/        @0@.xdata             U/             @0@.pdata             ]/ i/        @0@.xdata             �/             @0@.pdata             �/ �/        @0@.xdata             �/             @0@.pdata             �/ �/        @0@.xdata             �/             @0@.pdata             �/ �/        @0@.xdata             0             @0@.pdata             %0 10        @0@.xdata             O0 c0        @0@.pdata             w0 �0        @0@.xdata          	   �0 �0        @@.xdata             �0 �0        @@.xdata             �0             @@.voltbl            �0               .xdata          4   �0             @0@.pdata             	1 1        @0@.xdata             31             @0@.pdata             G1 S1        @0@.xdata             q1             @0@.pdata             �1 �1        @0@.xdata             �1 �1        @0@.pdata             �1 �1        @0@.xdata             2 '2        @0@.pdata             E2 Q2        @0@.xdata             o2 2        @0@.pdata             �2 �2        @0@.xdata             �2             @0@.pdata             �2 �2        @0@.xdata             �2             @0@.pdata             3 3        @0@.xdata             /3             @0@.pdata             73 C3        @0@.xdata             a3             @0@.pdata             i3 u3        @0@.xdata             �3             @0@.pdata             �3 �3        @0@.xdata          ,   �3             @0@.pdata             �3 �3        @0@.xdata             4             @0@.pdata             #4 /4        @0@.xdata          (   M4             @0@.pdata             u4 �4        @0@.xdata             �4             @0@.pdata             �4 �4        @0@.xdata             �4             @0@.pdata             �4 �4        @0@.xdata             5 #5        @0@.pdata             A5 M5        @0@.xdata             k5 {5        @0@.pdata             �5 �5        @0@.voltbl            �5               .xdata             �5             @0@.pdata             �5 �5        @0@.xdata             �5             @0@.pdata             6 6        @0@.xdata          $   16             @0@.pdata             U6 a6        @0@.xdata             6             @0@.pdata             �6 �6        @0@.xdata             �6 �6        @0@.pdata             �6 �6        @0@.xdata          
   7 7        @@.xdata             :7             @@.xdata             =7 E7        @@.xdata             O7 V7        @@.xdata          	   `7             @@.xdata             i7             @0@.pdata             u7 �7        @0@.voltbl            �7                .voltbl            �7               .xdata             �7             @0@.pdata             �7 �7        @0@.xdata             �7             @0@.pdata             �7 �7        @0@.xdata             
8 8        @0@.pdata             <8 H8        @0@.xdata             f8 v8        @0@.pdata             �8 �8        @0@.voltbl            �8               .xdata             �8             @0@.pdata             �8 �8        @0@.rdata             �8 
9        @@@.rdata             (9             @@@.rdata             :9 R9        @@@.rdata             p9 �9        @@@.rdata             �9             @@@.xdata$x           �9 �9        @@@.xdata$x           �9 :        @@@.data$r         /   %: T:        @@�.xdata$x        $   ^: �:        @@@.data$r         $   �: �:        @@�.xdata$x        $   �: �:        @@@.data$r         $   �:  ;        @@�.xdata$x        $   *; N;        @@@.data               b;             @ @�.rdata             �;             @@@.rdata$r        $   �; �;        @@@.rdata$r           �; �;        @@@.rdata$r           �; �;        @@@.rdata$r        $   < ,<        @@@.rdata$r        $   @< d<        @@@.rdata$r           �< �<        @@@.rdata$r           �< �<        @@@.rdata$r        $   �< �<        @@@.rdata$r        $    = $=        @@@.rdata$r           B= V=        @@@.rdata$r           `= |=        @@@.rdata$r        $   �= �=        @@@.rdata             �=             @0@.rdata             �=             @0@.rdata             �=             @0@.rdata             �=             @0@.rdata             �=             @0@.rdata             �=             @0@.rdata             �=             @P@.rdata             �=             @P@.rdata             
>             @P@.rdata             >             @P@.rdata             *>             @P@.rdata             :>             @P@.debug$S        4   J> ~>        @B.debug$S        4   �> �>        @B.debug$S        @   �> ?        @B.chks64         �  .?              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �  b     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\View.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $literals  $string_literals  $string_view_literals  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $math 	 $colors    �   M  4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align " �    std::memory_order_relaxed + �    std::_Aligned_storage<64,8>::_Fits " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size :    std::integral_constant<unsigned __int64,2>::value `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo 8 �   std::atomic<unsigned long>::is_always_lock_free / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment    ��I@donut::math::PI_f "   
�-DT�!	@donut::math::PI_d !   ��7�5donut::math::epsilon "   �  �donut::math::infinity    �  �donut::math::NaN 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified � �   std::_Trivial_cat<std::shared_ptr<donut::engine::IView>,std::shared_ptr<donut::engine::IView>,std::shared_ptr<donut::engine::IView> &&,std::shared_ptr<donut::engine::IView> &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::shared_ptr<donut::engine::IView>,std::shared_ptr<donut::engine::IView>,std::shared_ptr<donut::engine::IView> &&,std::shared_ptr<donut::engine::IView> &>::_Bitcopy_constructible R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified � �    std::_Trivial_cat<std::shared_ptr<donut::engine::IView>,std::shared_ptr<donut::engine::IView>,std::shared_ptr<donut::engine::IView> &&,std::shared_ptr<donut::engine::IView> &>::_Bitcopy_assignable ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources ) �   donut::math::vector<bool,4>::DIM � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos . �   std::integral_constant<bool,1>::value � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos   �        nvrhi::EntireBuffer L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable A    std::allocator<char>::_Minimum_asan_allocation_alignment c    std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment :     std::integral_constant<unsigned __int64,0>::value ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos . d   donut::math::box<float,3>::numCorners . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM *         donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM + <        nvrhi::rt::c_IdentityTransform  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 "         g_CubemapViewMatrices 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity 0 �   std::numeric_limits<wchar_t>::is_modulo - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits , d  	 std::numeric_limits<long>::digits10 : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - d  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / d   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask  {R    NV_SWIZZLE_POS_X  {R   NV_SWIZZLE_NEG_X  {R   NV_SWIZZLE_POS_Y  {R   NV_SWIZZLE_NEG_Y  {R   NV_SWIZZLE_POS_Z  {R   NV_SWIZZLE_NEG_Z  {R   NV_SWIZZLE_POS_W ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits  }R    NV_SWIZZLE_OFFSET_X  }R   NV_SWIZZLE_OFFSET_Y E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent  }R   NV_SWIZZLE_OFFSET_Z  }R   NV_SWIZZLE_OFFSET_W G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ' ~R        g_CubemapCoordinateSwizzle ; d  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10  �   �  , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent   �   =   ; d  �威std::numeric_limits<long double>::min_exponent10    �   釺   �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> & �5  $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e  �5  _TypeDescriptor 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const> E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16  i5  std::input_iterator_tag ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> >  .  std::_Lockit * 2/  std::hash<enum nvrhi::ResourceType> " i3  std::_Char_traits<char,int>  "   std::_Atomic_counter_t  �2  std::_Num_base K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > ) v3  std::_Narrow_char_traits<char,int>    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc>  �2  std::_Num_int_base / Q/  std::_Conditionally_enabled_hash<bool,1>  �2  std::float_denorm_style 6 �5  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast " �2  std::numeric_limits<double>  <&  std::__non_rtti_object ( n  std::_Basic_container_proxy_ptr12 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety ! �5  std::char_traits<char32_t>   �2  std::numeric_limits<bool> # �3  std::_WChar_traits<char16_t> T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> % --  std::_One_then_variadic_args_t W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   �5  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  �5  std::false_type  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy = 銺  std::allocator<std::shared_ptr<donut::engine::IView> >  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , �2  std::numeric_limits<unsigned __int64> \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  U/  std::hash<bool> 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>  (  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> / �3  std::_Char_traits<char32_t,unsigned int> ( 1$  std::hash<nvrhi::FramebufferInfo> % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128> N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > ! �2  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  t  std::exception_ptr C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t> q 廞  std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > > � ^Q  std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > >::_Reallocation_policy    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 �5  std::allocator_traits<std::allocator<char32_t> >  �  std::_Iterator_base0 M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> $ �#  std::hash<nvrhi::BufferRange> ! �5  std::char_traits<char16_t>  |  std::tuple<>    std::_Container_base12 ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char>  x5  std::_Invoker_strategy $ �2  std::_Default_allocate_traits 3 �5  std::allocator_traits<std::allocator<char> > � 隥  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IView> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IView> > >,1> ! �2  std::numeric_limits<short> . �0  std::allocator<nvrhi::rt::GeometryDesc> ; �  std::basic_string_view<char,std::char_traits<char> > C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock  D  std::bad_alloc B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> # �2  std::numeric_limits<__int64>  �  std::memory_order # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   k5  std::forward_iterator_tag   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > U   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IView> > >    std::_Container_proxy  �  std::nested_exception  r  std::_Distance_unknown ( �2  std::numeric_limits<unsigned int> @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double>  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1> + Q  std::_Ptr_base<donut::engine::IView>  �,  std::allocator<char16_t> ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> * r5  std::_String_constructor_concat_tag T 鳴  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IView> > >  D-  std::allocator<char>    std::nullptr_t & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1> ^ bR  std::_Uninitialized_backout_al<std::allocator<std::shared_ptr<donut::engine::IView> > >  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring ' �2  std::numeric_limits<signed char>  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ &/  std::hash<nvrhi::IResource *> ^ yR  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IView> > > 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char>  K5  std::char_traits<char>  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> @ �3  std::_Default_allocator_traits<std::allocator<char16_t> > , Q  std::shared_ptr<donut::engine::IView> 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t>  U3  std::streampos ' y/  std::hash<enum nvrhi::ColorMask> O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> >  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ! �  nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # B#  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice ! �  nvrhi::VariableShadingRate  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  #  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  &  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  ?#  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # L#  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable  H!  nvrhi::ComputeState  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec 
 !   _ino_t  !   uint16_t  }R  _NV_SWIZZLE_OFFSET $ :A  donut::engine::ICompositeView  \A  donut::engine::IView   傾  donut::engine::PlanarView    donut::engine::ViewType $ 'A  donut::engine::ViewType::Enum # 餚  donut::engine::CompositeView ! 糛  donut::engine::CubemapView  驛  donut::math::float4x4 " 2@  donut::math::vector<bool,4>    donut::math::float3  Q@  donut::math::affine3  
B  donut::math::float2 #   donut::math::vector<float,3>  u   donut::math::uint  闌  donut::math::plane # 蜙  donut::math::vector<float,4>  A  donut::math::frustum $ 鰼  donut::math::frustum::Corners # 魼  donut::math::frustum::Planes % 楻  donut::math::matrix<float,3,4>  蜙  donut::math::float4 % 驛  donut::math::matrix<float,4,4> # Q@  donut::math::affine<float,3>   袮  donut::math::box<float,3> " �?  donut::math::vector<bool,2>  袮  donut::math::box3 % |@  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # 
B  donut::math::vector<float,2> M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>  ]  terminate_handler  �4  _s__RTTIBaseClassArray  礟  PlanarViewConstants 
 H  ldiv_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  e4  _CatchableTypeArray     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24 % P4  __RTTIClassHierarchyDescriptor     __time64_t  m  FILE 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray  {R  _NV_SWIZZLE_MODE 
 #   size_t 
    time_t  
  __std_exception_data 
 u   _dev_t  K  lldiv_t  H  _ldiv_t  �  _timespec64  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers   �   �      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽     M]S噴=泥G)w��!&鍌S硚YQD铢g�/  N   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�      [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  ^   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  )   傊P棼r铞
w爉筫y;H+(皈LL��7縮  v   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �    d蜯�:＠T邱�"猊`�?d�B�#G騋     溶�$椉�
悇� 騐`菚y�0O腖悘T  [   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�      _漹�=薔FCS|q賹I眇軲敬笱<&螉洤  L   �0�*е彗9釗獳+U叅[4椪 P"��  �   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   �=蔑藏鄌�
艼�(YWg懀猊	*)     �暊M茀嚆{�嬦0亊2�;i[C�/a\  :   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  <   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  u   繃S,;fi@`騂廩k叉c.2狇x佚�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  	   +YE擋%1r+套捑@鸋MT61' p廝 飨�  J   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�  	    I嘛襨签.濟;剕��7啧�)煇9触�.  R	   *u\{┞稦�3壅阱\繺ěk�6U�  �	   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �	   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  
   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  f
   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �
   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �
   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  A   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     �(M↙溋�
q�2,緀!蝺屦碄F觡  e   G�膢刉^O郀�/耦��萁n!鮋W VS  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  >
   $^IXV嫓進OI蔁
�;T6T@佮m琦�  u
   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �
   5�\營	6}朖晧�-w氌rJ籠騳榈  �
   豊+�丟uJo6粑'@棚荶v�g毩笨C  :   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  x   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  @   +椬恡�
	#G許�/G候Mc�蜀煟-  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈     a�傌�抣?�g]}拃洘銌刬H-髛&╟  N   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  A   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5      �"鈖@M�骑潆譢aMy1绾鎕瑞lg  [   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁     c�#�'�縌殹龇D兺f�$x�;]糺z�  r   交�,�;+愱`�3p炛秓ee td�	^,  �   zY{���睃R焤�0聃
扨-瘜}  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  1   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  o   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   副謐�斦=犻媨铩0
龉�3曃譹5D   �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  9   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  y   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌     ;o屮G蕞鍐剑辺a岿;q琂謇:謇  J   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   ,┭0甗�+天没2骟Bw蛁�%"艠E�  �   _O縋[HU-銌�鼪根�鲋薺篮�j��     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  l   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰     蜅�萷l�/费�	廵崹
T,W�&連芿  E   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   D���0�郋鬔G5啚髡J竆)俻w��  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷      匐衏�$=�"�3�a旬SY�
乢�骣�  j   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   悯R痱v 瓩愿碀"禰J5�>xF痧  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  +   矨�陘�2{WV�y紥*f�u龘��  r   穫農�.伆l'h��37x,��
fO��  �   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  !   鏀q�N�&}
;霂�#�0ncP抝  Z   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �         T  �  B   U  �  H   V  �  Y   [  �  �   v    U   w    �   �  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  v  (  q   w  (  q   �    �   �    �   �    �     �
  f     �
  g   +  �
  �   /  �
  g   C  P   r   D  x   �  E  �
  P   F  �  �   G  x   �   S  x   d  T  x   �   b  x   �   j  `  8   m  �
  �   q  �
  �   t  �
  �   u  �
  �   �  �
  n   �  �
  �  �  �  i   �  �  |   �  �  }   �  �  �   �  �  �  �  �  `   �  �  l   �  �  �  �  P   7   �  @  l  �  @  S  �  @  f  �  x   �  �  x   �  �  (  5   �  (  5   �  �
  Q   �  �
  �   �  �  t  �  x    �  �  	  �  �
  �      �
  �     �
  �     �
  �     �
  �     �  T    x   9    x   ,  	  �  �   
  �  [    �
  �     @      x   �    x   ^    X  �     �
  �     �
  �     �
  �     �
  �     x   k    x   9    x   @    @      @  �     @  �  "    �  #  �
  �   $  x   �   %  x   �   &  x   �   )  @  ]  .  X  �   /  �
  �   0  �
  �   1    �   2    �  4    �  9      :    �  ;  �  Z  <    �  =  �  5  A      B    �  C  �  D  D    @   F  �  n  H    �  I  �  d  J  �  :  �   D   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\src\engine\View.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\engine\View.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h �       L^l   "  �   "  �  
 ^'  �   b'  �  
 �0      �0     
 1      
1     
 �:  
    �:  
   
 楪      淕     
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb 裥砓>Y7?樰�=      �?                  �?                  �?              �?      �?      ��                  ��      �?      �?          �?                  �?      ��      �?                  ��      �?      �?              �?              �?  ��              �?              ��        %  $  P&  @6   F  !V                                          3繪+罤�L嬞H堿H兟H堿A�   H堿堿 H嬃I岺D  A�   ff�     驛 �YBX � 驜L �YJX润��Y�X馏 H兝I冮u綡兟H冮I冭I冴u淚嬅�   �   @  I G            �       �           �donut::math::operator*<float,3,3,3> 
 >A@   a  AK         
 >A@   b  AP          M        %     M        &   (
 N N                        H & h   �  �  �  �  �  $  %  &      A@  Oa     A@  Ob     B  Oresult  O�   `           �   x   	   T       9 �    : �   9 �   : �0   < �@   > ��   ; ��   ? ��   @ �,      0     
 k      o     
 �      �     
 T     X    
 H塡$WH冹@M嬋)t$0H孂荄$     H�$M嬝L+蒐岯W繦�$W蒆嬟$I峇A�   L$D  驛X   驛`A(ff�     (�(捏BY(腕BYD�X�Y�X畜X洋H兝H冮u薎兝H冴I冮I冴u欝s(�S$(铙AYk(迡D$ �c,(麦AY(蘃媆$P驛YK驛Ys�X梵AY[(麦AYC驛YS�X�(腆AYc 驛YK�X伢AXk$$�X蝮X�L$驛X[(�X�O塆 H嬊塍AXs,�o$�w,(t$0H兡@_�   �   4  E G            n     �   	        �donut::math::operator*<float,3> 
 >   a  AI  8     �  AK        8 
 >   b  AP        $  AS  $     J% M        F  ��[

	 N M        m  �" N* M          ��6@ N- M          


5 M        %  


' M        &  


 N N N @                     H > h     F  m  �  �  �  �  �  �      $  %  &   X     Oa  `     Ob  P   B  Oresult  O�   �           n  �  
   t       �  �
   �  �   �  �   �  �!   �  �$   �  �5   �  �8   �  ��   �  ��   �  �M  �  �P  �  �c  �  �,      0     
 g      k     
 w      {     
 �      �     
 �      �     
 H     L    
 H塡$3繪+罤�L嬞H堿H兟H堿�   H堿H堿 H堿(H堿0H堿8H嬃I岺0@ A�   ff�     驜 �YBX � 驜L �YJX润�T痼Y�X洋��YB�X麦 H兝I冴u獺兟H兞餓冭H冸u塇媆$I嬅�   �   B  I G            �      �           �donut::math::operator*<float,4,4,4> 
 >[@   a  AK         
 >[@   b  AP        
  M          , M          (- N N                        H & h   �  �  �  �  �  �          [@  Oa     [@  Ob     覣  Oresult  O  �   X           �   x      L       9 �   : �
   9 �   : �@   < �P   > ��   ; ��   @ �,      0     
 k      o     
 �      �     
 X     \    
 H冹�bH嬃�j(泽AYP(腕AYH)4$�2(�(摅AY 驛Yp驛YX�X�(捏AY`驛Y@�X�(腕AYh �X趔AYH�X伢�X躞X袤q(4$�YH兡�   �   �   C G            �   '   �           �donut::math::operator*<float> 
 >C@   a  AK        � 
 >A@   b  AP        �                        H 
 h   �   (   C@  Oa  0   A@  Ob        Oresult  O�   8           �   x      ,       k �   m �	   p ��   q �,      0     
 e      i     
 �      �     
 �      �     
 �H嬃�X��J�XI�I�B�XA�A�   �   �   D G            .       -   0        �donut::math::operator+=<float> 
 >v@   a  AJ        . 
 >C@   b  AK        .                         H     v@  Oa     C@  Ob  O�               .   �
            �  �,      0     
 f      j     
 �      �     
 �      �     
 �H嬃�X��J�XI�I�B�XA�A�J�XI�I�   �   �   D G            =       <           �donut::math::operator+=<float> 
 >鞟   a  AJ        = 
 >廆   b  AK        =                         H     鞟  Oa     廆  Ob  O�               =   �
            �  �,      0     
 f      j     
 �      �     
 �      �     
 �H嬃�Q�^馏^洋�A�^馏Q�A�   �   �   D G            ,       +   /        �donut::math::operator/=<float> 
 >v@   a  AJ        , 
 >@    b  A�         ,                         H     v@  Oa     @   Ob  O�               ,   �
            �  �,      0     
 f      j     
 �      �     
 �      �     
 (罤嬃	评 ^�	�   �   �   D G                              �donut::math::operator/=<float> 
 >鞟   a  AJ         
 >@    b  A�         	                         H     鞟  Oa     @   Ob  O�                  �
            �  �,      0     
 f      j     
 �      �     
 �      �     
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   !   0   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
   !     !  
 s  X   w  X  
 �  !   �  !  
 H;蕋kH塼$WH冹 H嬺H塡$0H孂fD  H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;縃媆$0H媡$8H兡 _�   �   �  r G            q      q   7        �std::_Destroy_range<std::allocator<std::shared_ptr<donut::engine::IView> > >  >#Q   _First  AJ          AM       V  AJ p       >馪   _Last  AK          AL       Y  AK p       >哘   _Al  AP           AP       Q     D@    M        B  8  M        �  8  M        �   /	 M        �  )/
 >Z&   this  AI  $     B  AI         M        �  @	 N N N N N                       @� " h   �  �  �  �  3  B  E   0   #Q  O_First  8   馪  O_Last  @   哘  O_Al  9>       [&   9U       [&   O  �   P           q        D       > �    B �   > �   B �    C �X   B �f   F �,       0      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
            
 '      +     
 �      �     
 �      �     
 v      z     
 �      �     
 �      �     
 L塂$H塋$SVWATAUAVAWH冹0L嬯H孂H�L嬧L+郘媦L+鳬�I�������M;�剹  I�荋婭H+菻六H嬔H殃I嬃H+翲;�噉  H�
M嬿I;荓C餗;�嘩  I嬾H伶L塼$xH侢   r1H峃'H;��5  �    H吚�3  H峏'H冦郒塁鳯媱$�   �!H咑tH嬑�    H嬝L媱$�   �3跮塼$xH墱$�   I冧餗�4M峟L塪$(I�    I荈    I婡H吚t�@I� I�I婡I塅L塼$ H媁H�L;陁L嬨�L嬒L嬅I嬚�    H塡$ I嬐H媁L嬒M嬆�    怘�H吷t@L嬊H媁�    H�H媁H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I羚L鸏�H�H塐I嬈H兡0A_A^A]A\_^[描    惕    惕    蹋   �   �   �   J     a     v      �  �   �  �   �  �   �  �      �   �  � G            �     �          �std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > >::_Emplace_reallocate<std::shared_ptr<donut::engine::IView> const &> 
 >.Q   this  AJ          AM       ��  Dp    >馪   _Whereptr  AK          AU       ��  >Q   <_Val_0>  AP        ��  �  F� �  AP Z      D�    >#     _Newcapacity  AV  p     �  AV �        Bx   �     ]Q  >    _Newsize  AW  I     �l" �  >    _Whereoff  AT  %       >馪    _Constructed_last  AV  �     	  D(    >    _Oldsize  AW  ,     �   � >#Q    _Constructed_first  D     >馪    _Newvec  AI  �      
   AI �     � 
  B�   �     � �   M        2  um乯 M        <  um乯& M        �  ��)
1%��( M        �  ��$	%)
�
 Z   k   >    _Block_size  AJ  �       AJ �      >    _Ptr_container  AH  �       AH �     , � 
 >0    _Ptr  AI  �       AI �     � 
  M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N M        D  
m
 N N N M           Ik >    _Oldcapacity  AJ  M     �   L - �   AJ �     � T �  >    _Geometric  AH  m     u :  f 
  AH �     � 1 �  M        )  I N N M        4  *�  M        ;  �  M        C  �M M        F  �	 M        �  � N N N M        =  ��  N N N' M          乫(L4#'
 Z   7   M        "  *亜_ M        �  亪):
 Z   �  
 >   _Ptr  AJ �      >#    _Bytes  AK  �    -    AK �     % M        w  亼d#
=
 Z   S   >    _Ptr_container  AP  �      AP �    ?  5  >    _Back_shift  AJ  }    ,  AJ �    ?  5  N N N N Z   5  5     0           8         0@ f h   �  v  w  x  �  �  �  �           !  "  )  2  3  4  ;  <  =  C  D  F         $LN84  p   .Q  Othis  x   馪  O_Whereptr  �   Q  O<_Val_0>  (   馪  O_Constructed_last      #Q  O_Constructed_first  O �   �           �  @     �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   > �   B �*  C �/  E �;  G �>  K �@  L �N  M �S  N �f  V ��  W ��  X ��  = ��  7 ��  V ��   �  � F            =      =             �`std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > >::_Emplace_reallocate<std::shared_ptr<donut::engine::IView> const &>'::`1'::catch$0 
 >.Q   this  EN  p         =  >Q   <_Val_0>  EN  �         =  Z   7  "   (                    �        __catch$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z$0        $LN84  p   .Q  Nthis  x   馪  N_Whereptr  �   Q  N<_Val_0>  (   馪  N_Constructed_last      #Q  N_Constructed_first  O  �   8           =   @     ,       P �   Q �   R �3   S �,      0     
 �      �     
 	     
    
 9     =    
 I     M    
 p     t    
 �     �    
 �     �    
 �     �    
 �     �    
 
         
 6     :    
 b     f    
 �     �    
 �     �    
 �     �    
          
 �     �    
 �     �    
 �     �    
          
 *     .    
 :     >    
 �     �    
          
 >     B    
 V     Z    
 �     �    
 �     �    
 �     �    
 O     S    
 _     c    
 �     �    
 �     �    
 S  S   W  S  
 �     �    
 �	  "   �	  "  
 �
  "   �
  "  
 �
  "   �
  "  
 "  T   &  T  
 *  S   .  S  
 �  "   �  "  
 H塗$SUH冹(H嬯L婨pH婾(H婱 �    L婨xH嫊�   H婱p�    3�3设    �       /   �   8   $   @SH冹 I嬝H;蕋?H+薊3�@ �     L�L塁H�H�H婦H塁L�L塂H兠H�H;聈覯嬃H嬘H嬎�    H嬅H兡 [肳          �   �  � G            d      ^   5        �std::_Uninitialized_move<std::shared_ptr<donut::engine::IView> *,std::allocator<std::shared_ptr<donut::engine::IView> > >  >馪   _First  AJ          AJ M     	  >馪   _Last  AK        S  >#Q   _Dest  AP          AP M       >哘   _Al  AQ        [  >bR   _Backout  CI     D     	  CI          C $ 	  M        :   N M        9  M
 Z   7   N M        A     M        H     M        I    M        J  ''I N M        =  �  N N N N                       @ : h
   �  3  6  8  9  :  =  @  A  G  H  I  J   0   馪  O_First  8   馪  O_Last  @   #Q  O_Dest  H   哘  O_Al  O�   P           d        D       � �	   � �   � �    � �D   � �M   � �^   � �,      0     
 �      �     
 �      �     
 �      �     
          
 &     *    
 D     H    
 k     o    
      �    
          
 H塡$3跮峇L岮L岼D峓f�     A婣鳰峈A堾鳰岪A婣麺岻A堾霢婣鬉堾餉塟餓冸u覌B$H媆$堿0婤(堿4婤,堿8H嬃茿<  �?�   �   �   O G            p      Q   �        �donut::math::affineToHomogeneous<float,3> 
 >   a  AK        p                         H " h   �  �  �  �  �  �  �        Oa     覣  Oresult  O�   @           p   �     4       	 �     �D    �N    �Q    �,   
   0   
  
 q   
   u   
  
 �   
   �   
  
 H冹8�Q�)(鼠a�A�YAD)D$ 驞A�YAD)L$D(虳)$驞Y驟Y梭DYY驞YI 驛Y润DYA 驞X润Y腆DYA�Ya驞X审DY隗Y弩DX荏EX谼(D$ 驟\薉($A(罝(L$H兡8�   �   �   E G            �   ?   �           �donut::math::determinant<float> 
 >A@   a  AJ        �  8                      H  h   �  �  �   @   A@  Oa  O   �   0           �   x      $       , �   - ��   / �,      0     
 g      k     
 �      �     
 3荔	H堿堿H堿堿H嬃�I�I �   �   �   D G            "       !           �donut::math::diagonal<float,3> 
 >@    a  A�         "  M        T   
 M        b  P*6 N N                        H  h   T  b      @   Oa  O�   0           "   x      $       @ �    A �!   B �,      0     
 f      j     
 �      �     
 H嬆H塜WH侅�   )p鐷孂)x豀峀$ D)@菻嬟D)H窪)P―)X樿    驞K,L崪$�   驞C$H嬊驞[(�    L$ T$0DW谼W菵W繣(芋D$@A(塍DYT$0A(Y|$$A(痼Yt$(A(袤Yl$4A(狍Yd$<驞X�W�G �X躞Y伢DX泽DY�(馏X�屏�驞Y�埔DY鼠EX伢EX袤D_$驞W(�w,I媅A(s餉({郋(C蠩(K繣(S癊([營嬨_�7      \   6      �     C G            1  6   A           �donut::math::inverse<float,3> 
 >   a  AI  '     �  AK        '  >|@   mInverted  C�       e     �  C�      j     �  D     M        F  
`XA N" M          v>J
 N  M          ;	l	 M          ��	 >@    _x  A  v     `  >@    _y  A  n     x  >@    _z  A  r     }  N N
 Z      �                     @  h     F  �       �     Oa      |@  OmInverted  �   B  Oresult  O   �   X           1  �     L       �  �   �  �;     �A    �I     �O    �R     �
   �,      0     
 e      i     
 u      y     
 �      �     
 �      �     
 X     \    
 u     y    
 �     �    
 4     8    
 H嬆H塇USVWATAUAVAWH峢侅�   E3晒   J�   荅�  �?�%    A孂)p‥峺�5    E嬔)x楨嬞�=    D)@垕B 驞    塃疕岴廐塃H岴�E廐+豅塎o(    M烪塎wE�(�E�D  E峚H嬜L岹A凕�+  A�屲   H�9M嬎H峂汬�丄嬆餍L峬汳炅�I鬏�繢嬸E�$��    �A鬕��L�T�T蘃嬺/羦N�)�	K��D廔嬓T�T蘃F�/葀L岻鬖薎峆�IK��D�T�T�/葀I峆L��IK��D�T�T�/葀L岻L薎峆I兝H兞0I冾匸���A凕}A�   A+腖c菾�GI嬂I润L崗H�WH�T腆D崗T�/菻F翴�繦嬓I冮u蔐婱oH�WH鼠D崗T�/�嘪  H;譼\駼L廐�R�D晱B婰楎BD弸D晽�D暦B塂楎L晱駼L穳L晽B婰框BD穻D暱B塂框L暦塋暱H婨�.讂tl驜D忬BL楏^麦^鼠BD忬BD擉^麦BL楏BL惑BD擉BD敷^麦^鼠BD敷BD矿^麦BL惑BD緼�劯   驜l�(�T�/�啒   驜T揂W梵B\�(朋BYD忬M忬Y阵X润Y蒹E擉X麦BT惑Y阵M忬M敷E擉E楏X皿B\矿Y蒹E�(朋BYD敷X润E惑X麦M敷E惑E矿X皿E緼�劮   驜l�(�T�/�啒   驜T揂W梵B\�(朋BYD忬M涹Y阵X润Y蒹E燇X麦BT惑Y阵M涹M皿E燇EｓX皿B\矿Y蒹E�(朋BYD敷X润E求X麦M皿E求E梭X皿E薊�劗   驜l�(�T�/�啒   驜T揂W梵B\�(朋BYD忬MY阵X润Y蒹EX麦BT惑Y阵MM象EEX皿B\矿Y蒹E�(朋BYD敷X润E芋X麦M象E芋E左X皿E譎婱wA�罤兞L塎oH兝H塎wA�螲塃H�荋冸I兟I兠A��弇��H婱g婨�E�M�I堿 H嬃隑H婨g�   �茾  �茾  �茾  �茾  �茾  �茾  �茾  �茾   �(�$�   (|$pD(D$`H伳�   A_A^A]A\_^[]�=   3   P      b      s   6   �   '      �   �  C G            �  k   �          �donut::math::inverse<float,3> 
 >A@   m  AK        �  AK �      
 >|@    b  D0   
 >|@    a  D   
 >t     j  Ai      N  Ai �     & � : cN 
 >t     i  Al  �     �  Al �      �  >@     scale  A�       �� + E*  A�  �     F� �� �� + M        G  $		+ M        T  $		; M        b  $		 N N N3 M        j  � C
 N M        �  伣 N* M        j  �			#			P N M        �  � ,"W N M        $  伷 N M        $  伓 N M        j  	侘 N M        �  侓 N M        $  侅 N M        &  卆 N" M        .  �/%

 >   _Tmp  C      P    M C     �    s N# M        .  �

 >   _Tmp  C      $    ,  N M        $  � N M        /  偍/ N M        /  /倅6 N M        j  
傦
��
�� N, M        t  �;#
��#
��#
 NE M        0  �!		
p		
o		
 N5 M        t  ����� N# M        0  僈%-��%-��%- N �           @          @ > h     G  T  b  j  t  �  �  $  &  .  /  0  >   �   A@  Om  0   |@  Ob     |@  Oa  O �   �          �  x   V   �      � �   � �!   � �$   � �)   � �-   � �D   � �H   � �W   � �[   � �f   � �k   � �n   � �w   � �z   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �   � �  � �!  � �A  � �L  � �f  � �n  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �	  � �  � �/  � �5  � �@  � �G  � �K  � �j  � �y  � ��  � ��  � ��  � ��  � �  � �  � �  � �;  � �F  � �K  � �P  � �^  � �i  � �n  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �!  � �&  � �U  � �t  � �{  � �  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �C  � �a  � ��  � �,      0     
 e      i     
 u      y     
 �      �     
 �      �     
 �      �     
          
 %     )    
 =     A    
 r     v    
 �     �    
 �     �    
 �     �    
 H嬆H塜H塸H墄 UATAUAVAWH峢侅�   E3蒐嬹JL峞�3��%    E嘇峲B E峣M�J0)p润5    E�(    M�(
    )x阁=    E荄)@�(    驞    M�(
    E�M鱉岮I嬔D孇凗�#  A凖尰   K�塋峕�儖器亓��缷谼�<��     驛K餓�戵D厙T�T腗嬓/润AT蘈F襅�戵D厙T�/葀M峆驛KK�戵D厙I岺T腎峆T�/润AK T蘄F蔍�夡D厙T�/菻F袸兝I兠@H冸卬���A�}^L嬟M嬓I零�   I菱A+荓峿嘜�<廐c伢CK�嬻D�T�T腎嬂/菼嬍HF翴�繧兟H嬓/菼F薒嬞H冸u肐�戵D厙T�/�嚀  I;裻+L=嘓�D諊D=�D涨L諊L=�D=�L涨驛$.莦t!(�D=�粕 ^�D=�D=�^�D=�3繦峌嘕��3蒁岶�fD  A;�勁   �*(�T�/�啿   �L=婣W梵T=�(朋YD=圀\=擉Y腕XD
圀Y阵Y蒹D
�(朋XL
嬻YD=求XD
求L
嬻XT
忬L=梭Y腕T
忬T=象X\
擉Y阵\
擉\=芋D
求XL
梭Y蒹L
梭XT
象T
象X\
芋\
�繦兟H兞凐�����艫�虸�罥兡H兦凗嶧��E�M譇E鏏NM鰽F AN0�A�  �A荈  �A荈  �A荈  �A荈  �A荈  �A荈  �A荈  �A荈   �A荈$  �A荈(  �A荈,  �A荈0  �A荈4  �A荈8  �A荈<  �L崪$�   I嬈I媅0I媠@I媨HA(s餉({郋(C蠭嬨A_A^A]A\]�:   3   ^      i   '   t   *   �      �   -   �   6   �   0      �     C G            A  �             �donut::math::inverse<float,4> 
 >[@   m  AK        �  AK �      
 >驛    b  D@   
 >驛    a  D    
 >t     i  Ao  �     �  Ao �     �
 ,
 >t     i  A   \    3 A  �     i! � � O :$ >@     scale  A�   �    �  A�  �     ��� ( M        �  0#( M        D  0#2 M        S  0 N N N* M        j  ��(	Z N' M        j  ��		
	S	 N M        �  ��"(S N M        �  �> N M        j  	侁 N M        �  佹 N  M          儚    N M          �:%
 >蜙    _Tmp  A�   "     " A�  �     �F � � 7 VT �� �  N M          �

 >蜙    _Tmp  A�         N M        �  � N M          
侻 N M        �  �9 N M          �<	 N M        j  
倅 N$ M          偯	

 N$ M          偗	
 N M          倢
 N M          傄> N �           (          @ > h   D  S  j  �  �  �  �            #  -   �   [@  Om  @   驛  Ob      驛  Oa  O �   �          A  x   8   �      � �#   � �0   � �>   � �B   � �F   � �J   � �N   � �Z   � �b   � �f   � �m   � �q   � ��   � ��   � ��   � �*  � �.  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �  � �,  � �9  � �<  � �A  � �E  � �M  � �Z  � �p  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �   � �  � �
  � �>  � �Q  � �j  � ��  � �  � �,      0     
 e      i     
 u      y     
 �      �     
 �      �     
 �      �     
 �      �     
 %     )    
 5     9    
           
          
 g     k    
           
 H冹8(袶荄$    W矍D$    $H荄$    H嬃荄$    L$��W荔�抿A$I�Y,�Q H兡8�   �   Q  C G            a      \   
        �donut::math::scaling<float,3> 
 >@    a  A�         8  M        F   N M          		 M        T  		  M        b  		 N N N 8                      H  h     F  T  b     H   @   Oa  @   B  Oresult  O   �   @           a   �     4       [ �   \ �(   ] �+   \ �\   ^ �,      0     
 e      i     
 h     l    
 H�  �?3繦堿H茿  �?H堿茿   �?婤��A$堿,H嬃�   �   �   G G            3       2           �donut::math::translation<float,3> 
 >C@   a  AK        3  M        F  0  N                        H  h   F  G  T  b      C@  Oa     B  Oresult  O �   8           3   �     ,       T �    U �/   V �2   W �,      0     
 i      m     
 �      �     
 3繦�H堿H嬃�   �   �   7 G            
          �        �nvrhi::Rect::Rect 
 >   this  AJ        
                         H       Othis  O   �               
   �            �  �,   �   0   �  
 \   �   `   �  
 �   �   �   �  
 3狼A  �?H�H堿堿H嬃�   �   �   ? G                      �        �nvrhi::Viewport::Viewport 
 >�   this  AJ                                 H     �  Othis  O   �                  �            i  �,   �   0   �  
 d   �   h   �  
 �   �   �   �  
 H塡$H塴$H塼$WH冹 3褹竴  H孂�    �   H岹嬘3鞨塰鳫�(塰茾  �?H岪H冴u錋�   H壇�  H崗�  �    H崌�  @ H塰鳫�(H岪H冸u颒媆$0H嬊H媡$@H壇�  H媗$8H兡 _�    *   a   *      �   I  I G            �      �   �        �nvrhi::ViewportState::ViewportState 
 >�   this  AJ          AM       �  M        �  L
 M        �  p N N M        �  #3 M        �  1 N N                       @ " h   $  �  �  �  �  �  �   0   �  Othis  O   ,   �   0   �  
 n   �   r   �  
 ~   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �      �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  n G            K      E   �        �std::shared_ptr<donut::engine::IView>::~shared_ptr<donut::engine::IView> 
 >馪   this  AJ        +  AJ @       M        �  &, M        �  
 >Z&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �   0   馪  Othis  9+       [&   9=       [&   O�   0           K   �     $       � �   � �E   � �,      0     
 �      �     
 �      �     
 �      �     
 l     p    
 |     �    
 �     �    
 H�    H�H兞�       �      �      �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   �   0   �  
 {   �      �  
 H�    H�H兞�       �      �      �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   $        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >A   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   A  O__f  9(       A   O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 H塡$H塗$WH冹 H嬟H婹H;Qt+3繦�H塀H婥H吚t�@H�H�H婥H塀H傾�
L嬅H兞�    怘媅H呟t,����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媆$0H兡 _肙         �     K G            �      �   �        �donut::engine::CompositeView::AddView 
 >譖   this  AJ        N  AJ T     @    
 >#Q   view  AI       F  AK          D8    M        �  5T M        �  T,	 M        �  ]
 >Z&   this  AI  X     6  M        �  v	
 N N N N M        �  A M          
*+
 Z      M          $ M        1  $ M        ;  	 M        C  %M M        F  %	 M        �  . N N N M        =  � N N N N N N                      @ J h   �  �  �  �  �  �  �          1  ;  =  ?  C  F   0   譖  Othis  8   #Q  Oview  9t       [&   9�       [&   O  �   0           �   �     $        �    �T    ��   �   Z F                                �`donut::engine::CompositeView::AddView'::`1'::dtor$0 
 >#Q   view  EN  8                                  �  O   ,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 %  �   )  �  
 �  �   �  �  
   �     �  
   �      �  
 t  #   x  #  
 �  #   �  #  
 H媻8   �          �     �   �   T G                       �        �donut::engine::CubemapView::EnsureCacheIsValid 
 >昋   this  AJ          D                           H     昋  Othis  O  �   (              �            4 �    6 �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �     �   �   S G                       �        �donut::engine::PlanarView::EnsureCacheIsValid 
 >"A   this  AJ          D                           H     "A  Othis  O   �   (              �            [  �    ]  �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 H嬆H塜H塸 UWAVH崹h��H侅�  )p�)x菻�    H3腍墔P  H�H嬟H峌圚孂�悎   �   L峀$<H峊$4D嬛E3鯨岪@ ff�     A婬鳰岻塉麳峈A婬麺岪塉餉婬魤J鬍塹餓冴u阵P$H峊$0�X(A��`,H嬒D$0荄$l  �?L$@�=    D$PKL$`C ��粕狍�粕企�粕�K0H�L$`�悩   A�H峊$0H嬒 C@HKP@ C`H0KpH��惃   A�H峊$0H嬒 儉   H嫄   @ 儬   H0嫲   H��悹   H峌圚嬒 兝   H嬓   @ 冟   H0嬸   H��悙   L峀$<H峊$4L岪�    A婬鳰岻塉麳峈A婬麺岪塉餉婬魤J鬍塹餒冾u阵P$H峊$0�X(A��`,H嬒D$0荄$l  �?L$@�   D$P�  L$`�   ��粕狍�粕企�粕��0  H�L$`�惏   E3繦峊$0H嬒 傽  H婸  @ 僠  H0媝  H��悩   E3繦峊$0H嬒 儉  H嫄  @ 儬  H0嫲  H��惃   E3繦峊$0H嬒 兝  H嬓  @ 冟  H0嬸  H��悹   E3繦峊$0H嬒 �   H�  @ �   H0�0  H��惏    傽  H婸  @ 僠  H峌繦嬒H0媝  H��P �]�(象e�(求u腍嬒�m腆\篌泙  �\祗  �^误硤  (煮嫄  �
    �Y耋珜  �Y洋X篌^朋Y-    �摖  �償  �  �泴  �Y�(象^鼠X荏敞  W5    �^洭  W    �Y耋Y唧掣  �嫲  �淮  �浖  H��P@L�H嬒劺tH峊$pA�PpD塼$,�H峊$|A�Ph荄$,  �?� H峊$p�HH嬒�D$ �@H岲$ �D$(�L$$ 兝  H��惱   � �儤  H媿P  H3惕    L崪$�  I媅0I媠8A(s餉({郔嬨A^_]�(   $   �      �          U  6   h  6     '      �   �  S G            B  6     �        �donut::engine::IView::FillPlanarViewConstants 
 >>A   this  AJ        C  AM  C     � ><A   constants  AI  <     � AK        <  >�    viewportState  D�    M        +  劷 >C@   xyz  AH  �      AH �    !  N M        +  劖 >C@   xyz  AH  �      AH �    !  N M          刲 N M          凴 N M        �  �? N M          �; N M           凔8 N M        �  儸(3 N M        �  兎
 N M        �  兛 N' M        �  
佇!


 >   a  AH  �    �  N) M        �  
p!



 >   a  AH  I     �  N �                    A R h   +  �  �  �  �  �  �  �  �  �  �  �  �  �  �           
 :P  O  �  >A  Othis  �  <A  Oconstants  �   �  OviewportState  9C       GA   9      HA   9;      HA   9z      HA   9�      GA   9m      HA   9�      HA   9�      HA   9*      HA   9i      HA   9�      @A   9�      CA   9�      EA   9�      EA   9�      JA   O�   �          B  �  5   �         �6   !  ��   "  ��   !  ��   "  ��   !  ��   "  ��   !  �   "  �  #  �A  $  ��  %  �   &  �  %  �
  &  �
  %  �  &  �  %  �e  &  �s  '  ��  (  ��  )  �0  *  ��  ,  ��  .  ��  0  ��  .  ��  0  ��  /  ��  8  ��  /  ��  .  ��  /  ��  .  ��  0  ��  /  ��  2  ��  3  ��  /  �  2  �  3  �  0  �  2  �3  3  �?  5  �F  3  �R  6  �Y  5  �]  3  �e  6  ��  8  ��  <  �  =  �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
   �   "  �  
 T  �   X  �  
 d  �   h  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 (  �   ,  �  
 8  �   <  �  
 H  �   L  �  
 X  �   \  �  
 h  �   l  �  
 x  �   |  �  
 �  �   �  �  
 �  �   �  �  
 H婣A嬓H襀�忻   �   Q  P G                      �        �donut::engine::CompositeView::GetChildView 
 >軵   this  AJ          >'A   supportedTypes  A           D    >u    index  Ah          M        �   N                        @  h   �  �      軵  Othis     'A  OsupportedTypes     u   Oindex  O   �   0              �     $       
 �     �    �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 h  �   l  �  
 雎tH嬃肁嬂Hi纇  H兝H撩   �     N G                      �        �donut::engine::CubemapView::GetChildView 
 >昋   this  AJ          >'A   supportedTypes  A           >u    index  Ah                                 @     昋  Othis     'A  OsupportedTypes     u   Oindex  O  �   H              �     <        �     �   
 �    �	    �    �,      0     
 s      w     
 �      �     
 �      �     
 4     8    
 H嬃�   �   (  H G                      �        �donut::engine::IView::GetChildView 
 >>A   this  AJ          >'A   supportedTypes  A           D    >u    index  Ah          D                           @     >A  Othis     'A  OsupportedTypes     u   Oindex  O�   0              �     $       �  �    �  �   �  �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 �   �   �   �  
 <  �   @  �  
 H�    �          �   �   ] G                      �        �donut::engine::CubemapView::GetCubemapCoordinateSwizzle                         @  O   �   0              �     $       3 �    4 �   5 �,      0     
 �      �     
 侅  H嬄�夵  �J�   �   �   O G                      �        �donut::engine::CubemapView::GetCullingBox 
 >昋   this  AJ                                 @ 
 h   �      昋  Othis  O   �   0              �     $       w �    y �   z �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �  H嬄�(  �8  J塇  B J0�   �   
  \ G            /       .   �        �donut::engine::CubemapView::GetInverseProjectionMatrix 
 >昋   this  AJ        /  >0    includeOffset  AX        /  D                           @ 
 h   �      昋  Othis     0   OincludeOffset  O  �   0           /   �     $       � �    � �.   � �,      0     
 �      �     
 �      �     
       $    
 @SH冹`H嬟E劺tH崙�   L崄�  H峀$ �    �5佮  H岲$ 夝  D$ �   L$0�  D$@L$P H@ KH0H嬅C K0H兡`[�"         �     [ G            �      ~   �        �donut::engine::PlanarView::GetInverseProjectionMatrix 
 >"A   this  AJ        � !   >0    includeOffset  AX        �   
 Z      `                     @ 
 h   �   p   "A  Othis  �   0   OincludeOffset  O   �   0           �   �     $       �  �	   �  �~   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 ,  �   0  �  
 仺  H嬄壐  伻  JB �   �   �   V G            $       #   �        �donut::engine::CubemapView::GetInverseViewMatrix 
 >昋   this  AJ        $                         @ 
 h   �      昋  Othis  O�   0           $   �     $       � �    � �#   � �,      0     
 {           
 �      �     
 伆  H嬄壚  佇  JB �   �   �   U G            $       #   �        �donut::engine::PlanarView::GetInverseViewMatrix 
 >"A   this  AJ        $                         @ 
 h   �      "A  Othis  O �   0           $   �     $       �  �    �  �#   �  �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 仒  H嬄墾  伕  J壢  B J0�   �     ` G            /       .   �        �donut::engine::CubemapView::GetInverseViewProjectionMatrix 
 >昋   this  AJ        /  >0    includeOffset  AX        /  D                           @ 
 h   �      昋  Othis     0   OincludeOffset  O  �   0           /   �     $       � �    � �.   � �,      0     
 �      �     
 �      �     
 $     (    
 A独H拎�   �0  �@  J�P  H嬄B J0�   �     _ G            ;       :   �        �donut::engine::PlanarView::GetInverseViewProjectionMatrix 
 >"A   this  AJ        ;  >0    includeOffset  AX        ;                         @ 
 h   �      "A  Othis     0   OincludeOffset  O   �   0           ;   �     $       �  �    �  �:   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �      �  
 �佨  �   �   �   N G            	          �        �donut::engine::CubemapView::GetNearPlane 
 >昋   this  AJ        	                         @     昋  Othis  O�   0           	   �     $       r �    s �   t �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 H婣H+AH柳�   �     T G            
          �        �donut::engine::CompositeView::GetNumChildViews 
 >軵   this  AJ        
  >'A   supportedTypes  A         
  D    M        �    N                        @ 
 h   �      軵  Othis     'A  OsupportedTypes  O   �   0           
   �     $        �    	 �   
 �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 4  �   8  �  
 雎�   �   E撩   �   �   R G                      �        �donut::engine::CubemapView::GetNumChildViews 
 >昋   this  AJ        
  D    >'A   supportedTypes  A                                  @     昋  Othis     'A  OsupportedTypes  O  �   0              �     $        �     �    �,   
   0   
  
 w   
   {   
  
 �   
   �   
  
   
     
  
 �   �   �   �   L G                      �        �donut::engine::IView::GetNumChildViews 
 >>A   this  AJ          D    >'A   supportedTypes  A           D                           @     >A  Othis     'A  OsupportedTypes  O�   0              �     $       �  �    �  �   �  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
   �     �  
 3繦�H嬄�   �   �   P G            	          �        �donut::engine::CubemapView::GetPixelOffset 
 >昋   this  AJ        	  D    M        E    N                        @ 
 h   E      昋  Othis  O   �   0           	   �     $       � �    � �     �,   	   0   	  
 u   	   y   	  
 �   	   �   	  
 �仱   H嬄��   �   �   O G                      �        �donut::engine::PlanarView::GetPixelOffset 
 >"A   this  AJ                                 @     "A  Othis  O   �   0              �     $       �  �    �  �   �  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 H嬆SH冹@�壺  H嬟(馏H�W    H峆豀嬎�@伢@荏@囿H梵H扈    H嬅H兡@[�   6   C   �      �   
  V G            P      J   �        �donut::engine::CubemapView::GetProjectionFrustum 
 >昋   this  AJ        ) 
 >袮    b  D     M        C   N
 Z   V   @                     @  h     C  �   P   昋  Othis      袮  Ob  O  �   H           P   �     <       � �   � �"   � �)   � �B   � �J   � �,      0     
 {           
       $    
 @SH冹 H嬟H崙   H嬎�    H嬅H兡 [�   �      �   �   U G            !         �        �donut::engine::PlanarView::GetProjectionFrustum 
 >"A   this  AJ         
 Z   T                         @ 
 h   �   0   "A  Othis  O �   0           !   �     $       �  �	   �  �   �  �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 佖  H嬄夎  侙  J�  B J0�   �     U G            /       .   �        �donut::engine::CubemapView::GetProjectionMatrix 
 >昋   this  AJ        /  >0    includeOffset  AX        /  D                           @ 
 h   �      昋  Othis     0   OincludeOffset  O �   0           /   �     $       � �    � �.   � �,      0     
 z      ~     
 �      �     
          
 @SH冹`H嬟E劺tL崄�   H峇dH峀$ �    �/AdH岲$ ItD$ 亜   L$0墧   D$@L$P H@ KH0H嬅C K0H兡`[�         �     T G            {      u   �        �donut::engine::PlanarView::GetProjectionMatrix 
 >"A   this  AJ        {    >0    includeOffset  AX        {   
 Z      `                     @ 
 h   �   p   "A  Othis  �   0   OincludeOffset  O  �   0           {   �     $       �  �	   �  �u   �  �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 $  �   (  �  
 媮  塀H嬄�    荁   荁   �   �   �   Q G            !           �        �donut::engine::CubemapView::GetSubresources 
 >昋   this  AJ        !  M        �  	 \& N                        @ 
 h   �      昋  Othis  O   �   0           !   �     $       � �    � �    � �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 媮�   塀H嬄�    荁   荁   �   �   �   P G            !           �        �donut::engine::PlanarView::GetSubresources 
 >"A   this  AJ        !  M        �  	 \& N                        @ 
 h   �      "A  Othis  O�   0           !   �     $       �  �    �  �    �  �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �    H嬄�   �   �   ] G            
       	   �        �donut::engine::CubemapView::GetVariableRateShadingState 
 >昋   this  AJ        
  D                           @ 
 h   �      昋  Othis  O �   0           
   �     $       � �   � �	   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 婣0�H嬄�   �   �   \ G            	          �        �donut::engine::PlanarView::GetVariableRateShadingState 
 >"A   this  AJ        	                         @     "A  Othis  O  �   0           	   �     $       �  �    �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 3繦�塀H嬄�   �   �   R G                      �        �donut::engine::CubemapView::GetViewDirection 
 >昋   this  AJ          D    M            N                        @ 
 h         昋  Othis  O �   0              �     $       � �    � �   � �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 媮�  �伻  �塀H嬄�   �   �   Q G                      �        �donut::engine::PlanarView::GetViewDirection 
 >"A   this  AJ                                 @  h   �  �      "A  Othis  O �   0              �     $       �  �    �  �   �  �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 @SH冹 H婣H兞H嬟�惛   H嬅H兡 [�   �   �   O G                      �        �donut::engine::CubemapView::GetViewExtent 
 >昋   this  AJ                                @  0   昋  Othis  9       rA   O   �   0               �     $       � �   � �   � �,      0     
 t      x     
 �      �     
 �      �     
 A H嬄�   �   �   N G                   
   �        �donut::engine::PlanarView::GetViewExtent 
 >"A   this  AJ                                 @     "A  Othis  O�   0              �     $       �  �    �  �
   �  �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 @SH冹 H嬟H崙�  H嬎�    H嬅H兡 [�   �      �   �   P G            !         �        �donut::engine::CubemapView::GetViewFrustum 
 >昋   this  AJ         
 Z   V                         @ 
 h   �   0   昋  Othis  O  �   0           !   �     $       � �	   � �   � �,       0      
 u       y      
 �       �      
 @SH冹 H嬟H崙�  H嬎�    H嬅H兡 [�   �      �   �   O G            !         �        �donut::engine::PlanarView::GetViewFrustum 
 >"A   this  AJ         
 Z   T                         @ 
 h   �   0   "A  Othis  O   �   0           !   �     $       �  �	   �  �   �  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 亁  H嬄増  仒  JB �   �   �   O G            $       #   �        �donut::engine::CubemapView::GetViewMatrix 
 >昋   this  AJ        $                         @     昋  Othis  O   �   0           $   �     $       � �    � �#   � �,      0     
 t      x     
 �      �     
 A4H嬄IDATJB �   �   �   N G                      �        �donut::engine::PlanarView::GetViewMatrix 
 >"A   this  AJ                                 @     "A  Othis  O�   0              �     $       �  �    �  �   �  �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 媮�  �佮  �塀H嬄�   �   �   O G                      �        �donut::engine::CubemapView::GetViewOrigin 
 >昋   this  AJ                                 @     昋  Othis  O   �   0              �     $       � �    � �   � �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 媮�  �佋  �塀H嬄�   �   �   N G                      �        �donut::engine::PlanarView::GetViewOrigin 
 >"A   this  AJ                                 @ 
 h   �      "A  Othis  O�   0              �     $       �  �    �  �   �  �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 乆  H嬄塰  亁  J増  B J0�   �     Y G            /       .   �        �donut::engine::CubemapView::GetViewProjectionMatrix 
 >昋   this  AJ        /  >0    includeOffset  AX        /  D                           @ 
 h   �      昋  Othis     0   OincludeOffset  O �   0           /   �     $       � �    � �.   � �,      0     
 ~      �     
 �      �     
           
 A独H拎�0  �@  �P  J�`  H嬄B J0�   �   �   X G            ;       :   �        �donut::engine::PlanarView::GetViewProjectionMatrix 
 >"A   this  AJ        ;  >0    includeOffset  AX        ;                         @ 
 h   �      "A  Othis     0   OincludeOffset  O  �   0           ;   �     $       �  �    �  �:   �  �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
   �     �  
 H塡$WH冹 H孂H嬟H嬍�    H峎L崅p  I;衪]H兟 @ H媰�  �B�J鐷崚h  H�@蓑D�H�儉  H媰�  倶��H�劽�  H�儓  H岯郔;纔獺嬅H媆$0H兡 _�   �      �   �  R G            �   
   �   �        �donut::engine::CubemapView::GetViewportState 
 >昋   this  AJ        
  AM  
     �  >蚎    <range>$L0  AK         AK �       >凙    <end>$L0  AP  #     p  M        �   \ M        v  \ N N M        �  0 M        w  0 N N
 Z   �                         @ & h   �  �  v  w  �  �  �  �   0   昋  Othis  8   �  Oresult  O �   `           �   �  	   T       } �   ~ �   � �0   � �@   � �G   � �\   � �|   � ��   � �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$WH侅�  H�    H3腍墑$�  H孃H嬞3襀峀$ A笎  �    H峀$ �    �CKH媹�  H�I序D��   H���  H媹�  C H�勅�  H���  H嬒D  H崏�    H崁�   A�H�I�@�A�H�I�@�A�H�I�@�A�H�I餒冴u� H嬊H媽$�  H3惕    H嫓$�  H伳�  _�   $   3   *   =   �   �   '      �   [  Q G            
     �   �        �donut::engine::PlanarView::GetViewportState 
 >"A   this  AI  %     �  AJ        %  M        �  k M        v  k N N M        �  A# M        w  A# N N
 Z   �   �                    A  h   �  �  v  w  �  �  
 :�  O  �  "A  Othis  O �   0           
  �     $       ~  �%     ��   �  �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 p  �   t  �  
 W�/佖  sk�侟  /B��   �侙  A摾/J摾/rBE劺t=劺t9�B/侌  �J�BA摾/夢  摾/侅  rE劺t劺t��2烂   �   j  N G            }       |   �        �donut::engine::CubemapView::IsBoxVisible 
 >昋   this  AJ        } 
 >A   bbox  AK        }  M        �  kn M        �  l N M          +A N M        �  6 N M          * N N                        @  h   /  �  �  �        昋  Othis     A  Obbox  O  �   H           }   �     <       � �   � �   � �w   � �z   � �|   � �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 �  �   �  �  
 H伭�  �       �      �   �   M G                      �        �donut::engine::PlanarView::IsBoxVisible 
 >"A   this  AJ         
 >A   bbox  AK         
 Z   U                          @ 
 h   �      "A  Othis     A  Obbox  O   �   (              �            �  �    �  �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
   �     �  
 ��   �   �   O G                      �        �donut::engine::CubemapView::IsCubemapView 
 >昋   this  AJ          D                           @     昋  Othis  O   �   0              �     $       � �    � �   � �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 2烂   �   �   N G                      �        �donut::engine::PlanarView::IsCubemapView 
 >"A   this  AJ          D                           @     "A  Othis  O�   0              �     $       �  �    �  �   �  �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 2烂   �   �   L G                      �        �donut::engine::CubemapView::IsMirrored 
 >昋   this  AJ          D                           @     昋  Othis  O  �   0              �     $       � �    � �   � �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 秮a  �   �   �   K G                      �        �donut::engine::PlanarView::IsMirrored 
 >"A   this  AJ                                 @ 
 h   �      "A  Othis  O   �   0              �     $       �  �    �  �   �  �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 2烂   �   �   Z G                      �        �donut::engine::CubemapView::IsOrthographicProjection 
 >昋   this  AJ          D                           @     昋  Othis  O�   0              �     $       � �    � �   � �,   �   0   �  
    �   �   �  
 �   �   �   �  
 �墣   W�.葄u��2烂   �   �   Y G                      �        �donut::engine::PlanarView::IsOrthographicProjection 
 >"A   this  AJ                                 @  h   �  �  �      "A  Othis  O �   @              �     4       �  �    �  �   �  �   �  �   �  �,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
 ��   �   �   P G                      �        �donut::engine::CubemapView::IsReverseDepth 
 >昋   this  AJ          D                           @     昋  Othis  O  �   0              �     $       � �    � �   � �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 秮`  �   �   �   O G                      �        �donut::engine::PlanarView::IsReverseDepth 
 >"A   this  AJ                                 @ 
 h   �      "A  Othis  O   �   0              �     $       �  �    �  �   �  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 2烂   �   �   N G                      �        �donut::engine::CubemapView::IsStereoView 
 >昋   this  AJ          D                           @     昋  Othis  O�   0              �     $       � �    � �   � �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 2烂   �   �   M G                      �        �donut::engine::PlanarView::IsStereoView 
 >"A   this  AJ          D                           @     "A  Othis  O �   0              �     $       �  �    �  �   �  �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 墤�   �   �   �   N G                      �        �donut::engine::PlanarView::SetArraySlice 
 >^A   this  AJ          >t    arraySlice  A                                  H     ^A  Othis     t   OarraySlice  O  �   0              �     $       y  �    z  �   {  �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塼$WH冹0)t$ W纅n駻嬸[鯤孂D墎  �    �,�(畦    3蓫F塐�w塐�w塐 荊$  �?塤(�,袎W,塤0塛4垙j  壏�   墢x  �穦  墢�  �穭  墢�  菄�    �?墴�  墬�  墴�  墬�  垙�  墖  岶墢�  �蜂  墢�  �缝  墢�  菄�    �?墴�  墬�  墴   墬  垙:
  墖�  岶墢H
  �稬
  墢P
  �稵
  墢X
  菄\
    �?墴`
  墬d
  墴h
  墬l
  垙�
  墖�
  岶墢�
  �反
  墢�
  �芳
  墢�
  菄�
    �?墴�
  墬�
  墴�
  墬�
  垙
  墖T  墢  ��  墢   ��$  墢(  岶菄,    �?墴0  墬4  墴8  墬<  H媆$@H媡$H(t$ 垙r  墖�  H兡0_�,   )   8   (      �   �  S G            (       �        �donut::engine::CubemapView::SetArrayViewports 
 >橯   this  AJ        $  AM  $      >t    resolution  A         0  >t    firstArraySlice  A        � Ah         c M        �  4	\,_,_,_,"%
' M        �  4 N N 0                     @  h   �  �  �  �   @   橯  Othis  H   t   Oresolution  P   t   OfirstArraySlice  O   �   �           (  �     �       T �$   U �4   Y �>   Z �A   Y �q   Z �w   Y ��   Z ��   Y �  Z �  Y �g  Z �p  Y ��  Z ��  Y ��  Z ��  Y �  \ �  Y �  Z �"  \ �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
    �     �  
 A4JIDB ATA AdAHItA@ 亜   AH0苼b   墧   �   �     L G            H       G   �        �donut::engine::PlanarView::SetMatrices 
 >^A   this  AJ        H  >   viewMatrix  AK        H  >[@   projMatrix  AP        H                         H     ^A  Othis       OviewMatrix     [@  OprojMatrix  O  �   @           H   �     4       l  �    m  �   n  �9   o  �G   p  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 �   �   �   �  
 4  �   8  �  
 H墤�   苼b   �   �   �   O G                      �        �donut::engine::PlanarView::SetPixelOffset 
 >^A   this  AJ          >
B   offset  A�                                  @     ^A  Othis     
B  Ooffset  O �   8              �     ,       s  �    t  �   u  �   v  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 H嬆H塜H塸H墄UAVAWH峢℉侅@  �絸    H嬞J)p�)x�亁  B D)@窪)H―)P楧)X圗W�増  仒  �戃  �欂  t9D(    D(
    D(    H荅�    荅�    (}��殷��呻@�
    H峂狊\$0(袤T$(�    �T$ �    D DHDP x0H峾$LH岲$pH+鳯�    H峴lL�=�   @ f�     AA婥 H嬒AK塂$`H岲$pD$@H荄$d    L崑�  L$P荄$l    A�   W狼E�    W�D$pM�f�     驛Y   驛aA)ff�     (�(捏YT�(腕Y�X�YL�X畜X洋H兝H冴u蘄兞H兞鬒冴u烍摖  I兠$�硿  (麦￥  (铙Yl$@(迡E愺YD$L(虊E荔YL$XD垨�  �Y\$D�X梵Yt$H(麦YD$P�YT$T�X镈(腆X伢YL$\�X蝮Yd$`驛X�U�DN�X�L$pDV N�V囿X趔AX�~0驛X�腧m捏u�E�F餒伷h  M;�宆��L崪$@  D垞  I媅 I媠(I媨0A(s餉({郋(C蠩(K繣(S癊([營嬨A_A^]�   '   �   *   �   0   �       �      �   �     
      
       �   |  N G              W   �  �        �donut::engine::CubemapView::SetTransform 
 >橯   this  AI  )     � AJ        )  >Q@   viewMatrix  AK        �  AK �     �  >@    zNear  A�         �  A�  �     �  >@    cullDistance  A�         �  A�  �     � & >0    useReverseInfiniteProjections  EO  (           D�   >驛   faceProjMatrix  C�       �     i1 2  C�      �     f) 7  C�       �     c! <  C�   0   �       C�  0   �     � B�   �     � @  >Q@   faceViewMatrix  C�       ~    �  C�      p    �  C�             C�          �  B�        �' M        �  �"*$
 N/ M        	  乨	
J4N#; '	%	
" M        F  �
S

$ N M        m  倧	 M          倧	 >@    _x  A�   �    p  A�       f  N N= M          佲	-

 N% M          乨	
4 M        %  乨	
 M        &  乨Z N N N N' M        F  � 		 N
 Z   N   @                    @ R h       F  S  m  �  �  �  �  �  �  �  �  	      $  %  &   `  橯  Othis  h  Q@  OviewMatrix  p  @   OzNear  x  @   OcullDistance * �  0   OuseReverseInfiniteProjections  �   驛  OfaceProjMatrix  �   Q@  OfaceViewMatrix  O�   �             �     �       9 �   ? �{   @ ��   G ��   J �   K ��  I ��  K �"  M �)  K �L  M �P  K �p  M �u  K �~  M ��  K ��  M ��  K ��  M ��  I ��  Q �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
   �   #  �  
 [  �   _  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
 -  �   1  �  
 A  �   E  �  
 U  �   Y  �  
 i  �   m  �  
 y  �   }  �  
 J  �   N  �  
 Z  �   ^  �  
 �  �   �  �  
 �堿0�   �   �   \ G                      �        �donut::engine::PlanarView::SetVariableRateShadingState 
 >^A   this  AJ          >�   shadingRateState  AK                                 @     ^A  Othis     �  OshadingRateState  O�   0              �     $       g  �    h  �   i  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 @SH冹PH嬞)t$@A�J�I��r)|$0�zD)D$ 驞B�    �,�(茐C �    �,�(菈C$�    �,繟(缐C(�    �,缐C,(t$@(|$0D(D$ 苾b   H兡P[�?   )   N   (   ]   )   m   (      �   2  L G            �   8   x   �        �donut::engine::PlanarView::SetViewport 
 >^A   this  AI       �  AJ          >�   viewport  AK        C 4 M        �  	
			 N P                     H 
 h   �   `   ^A  Othis  h   �  Oviewport  O  �   8           �   �     ,       `  �   a  �   b  �x   d  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 �   �   �   �  
 H  �   L  �  
 H塡$H塼$H墊$ UH峫$〩侅�   H峐H孂H嵆p  H;辴怘嬎�    H伱h  H;辵靲�   厏  H嵎x  L壌$�   H嬛)�$�   H峂    �    L岴求^椳  H荅�    H峂壳E�    �   H荅�    H峌�荅�    D嬎E3� W�嚚  H徃  @ M�嚾  E楏鼠U�麦]'E�E�W�抿EM�M@ fff�     婤麺岪堿麳岻�H峈堿饗B鴫A鬍塸餓冮u�E籋崡�  荅�  �?M薍峂�E�JM�B ��粕狍�粕企�粕�M�J0�    L岴H峂�H峍 �  H�(  @ �8  H0廐  婤鳰岪堿麳岻婤麳峈堿饗B魤A鬍塸餒冸u伢F$H峌惑N(H岴籐嫶$�   L岴��E+D峉�F,H嬒�E3H+�W荔M/W汕E7  �?E薍伭�  E�M�M��     驛X   驛 驛h驛p@ (�(芋YT�(腕Y�X�YL�X�(企YD �X洋X畜H兝H冴u縄兝H兞餓冴u�E籋崡X  M薍峂�E�JM�B J0�    H嬛H峂� 嚇  H彣  @ 嚫  H0徣  �    �熦  �@$�囙  婡,墖�  �囙  �忎  (痼楄  �\�(崞�  �X�(牦\梭X泱\芋X��(�趄u�(�$�   �U�E楏m�囲  �E圏  L崪$�   I媅I媠 I媨(I嬨]�4   �   p      x      �     �     1        �     M G            �     �  �        �donut::engine::CubemapView::UpdateCache 
 >橯   this  AJ        #  AM  #     � >臦    <range>$L0  AI          AI 0     �  p / >縋    <begin>$L0  AI  ?       AI 0     �  p / >縋    <end>$L0  AL  *     .  AL �      M        C  儞	 N M        u  僴 N M        q  僑
 N4 M          価			%A& M          価			, M          価			 N N N7 M        �  
佇
			 N M        �  
� 
 N& M        
  ��)!
 >@    a  A�   �      M        F  ��
 N M          �� M        T  ��! M        b  �� N N N N Z   �           �                     @ f h       C  F  T  b  q  u  �  �  �  �  �  �  �  �  �  �  �    
         �   橯  Othis  O   �   �           �  �     �       _ �   ` �0   a �D   c �Q   f �t   g ��   f ��   g ��   f ��   g ��   f ��   g ��   f ��   g ��   f ��   g �]  h ��  i ��  j ��  k �5  l �u  n �|  l ��  n ��  l ��  o �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   #  �  
 /  �   3  �  
 �  �   �  �  
 ,  �   0  �  
 L嬡USI峩窰侅8  H�    H3腍塃纮筨   H嬞呭  (    L岲$l�懁   H峊$$�櫒   �X殷Y    (菼塻I墈�   M塩 D嬒M塳鐴3鞰塻�D$ M墈�D$0荄$@  �?�A�\AA)s菶)C窫)K‥)[橌^星D$L    �A�\AE)c圚峀$d�T$DEW潴^伢\$Hfff�     婤麺岪堿麳岻�H峈堿饗B鴫A鬍塰餓冮u�D$`H崜�   D塵�L$p荅�  �?H峀$`E�JM�B ��粕狍�粕�M�J0�    L岲$,L嬒H峀$$H峉< 凁   H�   @ �  H0�   @ �     婤鳰岪堿麳岻婤麳峈堿饗B魤A鬍塰餓冮u伢CXH峊$`�K\H岲$`�D$PL岲$$�C`E峇H嬎�D$XW荔L$TW汕D$\  �?H+�D$pH兞tE�L$`M�ff�     驛X   驛 驛h驛p@ (�(芋YT�(腕Y�X�YL�X�(企YD �X洋X畜H兝H冴u縄兝H兞餓冴u�D$`H峊$`H嬎L$pH+蔋岲$`�0  L崈4  H伭�   E�A�   婡  M�働  W�媊  W�L$pM�D$`E��    驛X   驛 驛h驛p@ (�(芋YT�(腕Y�X�YL�X�(企YD �X洋X畜H兝H冴u縄兝H兞餓冴u�D$`H峉4L$pH峀$ 僷  E�媭  M�儛  嫚  �    H峉dH峀$` 儼  H嬂  @ 冃  �    L岲$,H峀$$H崜�   冟  H嬸  @ �   H0�  fff�     婤鳰岪堿麳岻婤麳峈堿饗B魤A鬍塰餒冿u伢冊  H峀$0�嬝  H峊$`L嫭$0  H岲$`H嫾$`  L崈�  �D$PH+鼠冘  A�   �D$XW荔L$TW�D$p荄$\  �?E�L$`M�@ fff�     驛X   驛 驛h驛p@ (�(芋YT�(腕Y�X�YL�X�(企YD �X洋X畜H兝H冴u縄兝H兞餓冴u�D$`H峊$`H嬎L$pH+蔋岲$`�   L崈�   H伭0  E�A�   �0  M�傽  W�婸  W�L$pM�D$`E��    驛X   驛 驛h驛p@ (�(芋YT�(腕Y�X�YL�X�(企YD �X洋X畜H兝H冴u縄兝H兞餓冴u�D$`L$p僠  E�媝  M�儉  嫄  �儗   A.膠uA��E2繦崜0  D垉`  H峀$`�    D秲`  H峉dH峀$` 儬  H嫲  @ 兝  H0嬓  @@冟  HP嬸  �    (�$  L嫾$   L嫶$(   L嫟$h  H嫶$X  �   H�  @ �   H0�0  @@傽  HP婸  驞C@驞[D�S<�k4(鼠cPD(腕C8�YCH驟Y梭DY[L驞YKT�YCL驛Y润DYCT驞X润Y腆DYC8�YcH驞X善僢  驞Y隗Y弩DX荏EX谼(�$   驟\薉(�$�   E/酓(�$�   D(�$�   椑垉a  H婱繦3惕    H伳8  []�   $   1   '   W   #   d     �     �     �  �   �  �     '      �   1  L G            )       �        �donut::engine::PlanarView::UpdateCache 
 >^A   this  AI  (     � AJ        (  M          f嘳m N" M          叿A M          叿 M          叿
 N N N7 M          刾





"A) M          刾





- M          刾





 N N N: M        �  
凘



		 N" M          �A M          � M          �
 N N N. M          佪

!)A  M          佪

!& M          佪

!#
	 N N N0 M        �  
伆


		 N" M        �  
��
	 N% M          .-"
	/' M        F  .-"
	/ N N Z         S  S   8                    A Z h     F  G  T  b  �  �  �  �  �  �  �  �  �  �  �            
 :�   O  P  ^A  Othis  O   �   �           )  �     �       @  �   A  �.   D  �^   A  �f   D  ��   A  ��   D  �3  G  ��  I  �  J  ��  L  ��  M  �@  N  ��  O  �m  Q  ��  R  ��  S  ��  W  �]  U  ��  W  ��  U  ��  W  ��  U  ��  W  �  X  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 H  �   L  �  
 H冹HH峀$ �    H�    H峀$ �    �
   �      �      $      �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (               p            J �   K �,   �   0   �  
 �   2   �   2  
 �   �   �   �  
 H冹(H�
    �    �   �      �      �   �   � G                             坰td::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > >::_Xlength 
 Z   6   (                      @        $LN3  O  �   (              @            a �   b �,   �   0   �  
 �   ?   �   ?  
 �   �   �   �  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �   <   �      �   p  h G            A      A   "        �std::allocator<std::shared_ptr<donut::engine::IView> >::deallocate 
 >跶   this  AJ          AJ ,       D0   
 >馪   _Ptr  AK        @ /   >   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        w  
#

 Z   S   >    _Ptr_container  AJ       (    AJ ,       >    _Back_shift  AH         AH ,       N N (                      H  h   w  �         $LN18  0   跶  Othis  8   馪  O_Ptr  @     O_Count  O�   8           A        ,       � �   � �2   � �6   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 *  �   .  �  
 K  �   O  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 0  =   4  =  
 �  �   �  �  
 H婹H�    H呉HE旅   �      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H           +      +      \    20    2           ,      ,      b   
 
4 
2p    B           -      -      h    20    <           .      .      n   
 
4 
2p    B           /      /      t    20    <           0      0      z   
 
4 
2p    B           1      1      �    �                  3      3      �    d T 4 2p    �           4      4      �   6
 %x6 !h7 dw 4v p �
pP      P      &       B          5      5      �   8 8� -x h �0    �           6      6      �    ' 0P    �      &       ^           7      7      �   ! d+     ^          7      7      �   ^   b           7      7      �   !j j�
 O� J� E� @h $�$ �% �& 
�- t, ^   b       ,   7   0   7   4   �   b   �          7      7      �   !   �
  �  �  �  h  �$  �%  �- ^   b       $   7   (   7   ,   �   �            7      7      �   !       ^          7      7      �     )          7      7      �    
4Z 
X p      �     &       
          8      8      �    20    !           9      9      �    20    !           :      :      �    �0    {           ;      ;      �    �0    �           <      <      �    B      A           >      >      �    B                 @      @      �    4 2p           %      �       �           A      A      �   (           �      �       p      �.dvW W� R� M� H� 8x 4h t. d- 4, ( ��P                B      B      �    h d	 4 Rp    (          C      C      �   	 t! d  4  P      X           D      D         ! h
 �     X          D      D         X   `          D      D      
   !   h
     X          D      D         `  �          D      D         !       X          D      D         �  �          D      D         
 
4 
2p    �           E      E          20    !           F      F      "    r0    P           G      G      (    20                H      H      .    4     p           I      I      4   � 崍 |x	 Zh
 #t #d #4 # ����P      A          J      J      :    4     �           K      K      @   6 6� 1� ,� $� x	 h
 4  p      1          L      L      F   ? ?�  6� %� b      �           M      M      L    20               N      N      R   ! t               N      N      R      E           N      N      X   !                 N      N      R   E   K           N      N      ^   - h 
4
 
rp    n          O      O      g    b      a           P      P      m   k k� [x Hh  ���
�p
`	0P    �          Q      Q      s   ' 'h  "      �           R      R      y    R����
p`0           %      �       �          U      U         8               �      �   	   �            �   �       "   � �]  BP0      =           "      "      �     20    d           V      V      �    d 2p               W      W      �   ! 4               W      W      �      p           W      W      �   !                 W      W      �   p   q           W      W      �   .E B      :           Y      Y      �                               �      �      �   Unknown exception                             �      �      �                                     �      �   bad array new length                                �      �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                   .?AVbad_alloc@std@@     �              ����                      �      �                   .?AVexception@std@@     �               ����                      �      �       ����    ����        ��������vector too long                                       �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                                                          �              ����    @                   �      �                                         �                                                                                    �              ����    @                   �         �7�5   ?  �?   �  ��   �  �?                  �?                  �?                  �?������������   �   �   �   �   �   (   & 
34        std::exception::`vftable'    �      �  
    �   (   & 
34        std::bad_alloc::`vftable'    �      �  
    �   3   1 
34        std::bad_array_new_length::`vftable'     �      �  
 噾姏@|#份�]A�!�8cI橗cSD"嵞僪嶗寳G質呭砅阹gd30夦��3銝頁鎴萩壾AD蓍+��j7b,�-�誒耦珗V9&周7s樫,+貤E鄗嫷v岦��:鉺怐┉$7v�)9錌桔減跕�坎
Sh�K蜌�(�*ay韪慀莁阂F
�/%�;jQ皲*圉龡揅�)?ê�!w筀:輄璃�1|爇�2骹Y4�梢B巏恜郦j湦+ 栖苉{�
i.6zc4�M_碴M�.嚼禜昑i缼\IQM�'�E_赨n�潞眜喣1]Xび+{�4�庰鮋峑b�败]	嚜mq繘@� T(倴
椠�;.熑[閈原曫$懃C����?騍滏�荌u�-鲊臂ep禭c�&敡?諭嚤踖p禭9"虰錱6萪O�鎯2熸吻|K霵婬(�;n�x�╒$愜w獛啯;4:,]嶀預棊膬�屓绀?貚犷A棊膬鏒�6眽�9箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>~;A嬰t舘� �蹰k#@(貝邒_;蓻酼鴝宸�$M焿q鞇炟k端祆癜~t]�粴�>端祆癜~t雜Uj��/�9Z;儔峐偷瘲� V>刬$嫇萡hf蕜囪龋�0骈Z�!�'涔肆峖=f瓵�2犜�.v汢�/铏B3页o�6h頴i蹃涇v��.�="RS蕅冯Q爇B.竮ｃ�:T幩�<V*怑;�
y匎l]镏嗺m瘾�9橞e�$澪o<薢瓭>-泛弨瓌然�皗劭�.柀L鰟`�+驗琕|�-稝黥F "�軭�リ锑ェ倅"\磿諒&铍縥汄@躦孛o敕/阩N)-�N	YyF纀��+
傧W�96篅ur5# �40�z�	邍嵡j佒瓲讫筑柉炛庼M/�?W�>�/�*54皆&K�蛖氇 T+玶趉~裺l踗麠;�!徤咻�,$�淣鴿刍 E+硪0岤�	z91	潜濒W4�)'}k剺�%桀bZ痀c塧�.萺X_&y耡?Y(劐ˋ椰鋥�?(匸唩�
�S]�*襰A③7wC诖_wt撁揎=fpo搀��7鋘o�髳a貥箤嬘b槁簜�祫x%� o�誇�SNv!燉&僚凂�蹠赕陖艇q�:劃7颦呅∵悰锧�隠d妳慖煅6Q裿L�=濭鉕垿I噸K辐CD[穜/僎1~l豮Pt[癒n呲萐�?8績RVr〓G�5唽�c|�v毂\p�xs鷾 y搢S,蘬﹢�)禐NT彎釆)蒺Y囑�R薌衳磩K遘T熏堙薌衳磩脴"练�C�
Xe狚z濲&B�h&�薌衳磩F篥剄讍!�q!釼n�)瞏樶姮�)輩リ簲朡�-
f?冘奝Uj駽薌衳磩胷}熪{Q薌衳磩偊1o=銾� 贮$L霴*昞k笠F7勏�:,o闯支T5委lHV曎女饭�*R}�>鼕�賖肋�膚跰.筵衯#槧�+C龅閞q�d浾?T榩�傽箭�:逴w塅?陛�槄豋礠}谄@憁\俽蓥蝶墇�n�%I栶賑?TЦ�鐆璮]{謑pg^膟�8z~鮧�|UA'HynN鵘J鈜c諞狦FF{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪�嫫﹥�,E庄E痃9鵛扰鳃敬横H�'��H魶TD椣夂FDb�3��腫62V�聚3�藸s`��	E>殉`選滢��:匳�发汀醴G鬼躤I!酡mDeQO&Vp譃b
雵J-WV8oc8曀黩6雵J-WV8oc8曀黩6耉慞R楾�u鷩迷缆V慞R楾螉髺4{	1-坓�(鬄鮳�>i,夿-坓�(鬄�汬'这枏苆%P�Rx$[�dd�a�:徉�'醨�>P#掭縉�礨幁q襇8 �
�VT緣f%d赢
訌擐^	�&眐F罅Q艂律巿<筿7鑥@P?Z�0>嵪CG鵿�'潔绨�
_f韌鰮�8g�89汥侑^]痗敏�9E\$L釉�hK嗉Дル嘕-WV8oc8曀黩6{芗鱄] +N癄i�:雵J-WV8o5R犵�喪5*窆H絁媳躻LN婽�(悵q�?5*窆H繈饊�/孓1EL砶贗EU�閂+P7▉$tO�
襕囯嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛C甉h嗓斠[G`嵇莚Yㄈe孮.�>正╡怌椸祜SE涏?虶酥s2倃齼_*窶圹涩�6┾盙黋p脢�&!y*�杜`颀l+�鞯.r擣�0G#盱谑G
.'穤s;嗐8�1�8]Z��梾�n4�硓橂嘕-WV8o�&9�=NB?M钚^再�(i9x�奝k6G磌缘:逻戇�9桸瘟妬
啶鳺菈ouK�=-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� I墐覴>掎贋XCRC冼�^笵A傮肙�粮�茍0牊鋠s�隵/捞痮5yI~hU��('atr。�/z个耼O榖苃V觔.癔�sG﹋!阑,�粰趭+�揃T爃.L�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       刎               .debug$T       p                 .rdata         `      �2�                         7              \   @           �          .text$mn       �       港�+     .debug$S       �             .text$mn       n      鐨$�     .debug$S       �             .text$mn    	   �       膒T     .debug$S    
   �         	    .text$mn       �       f聰s     .debug$S       ,             .text$mn    
   .       ^�     .debug$S       �          
    .text$mn       =       N�*Z     .debug$S       �              .text$mn       ,       l?     .debug$S       �              .text$mn              Nm軉     .debug$S       �              .text$mn       :      眡�     .debug$S                    .text$mn       q       
z鈕     .debug$S       �             .text$mn       �  	   
墟     .debug$S        
  T           .text$x        =      M錌�    .text$mn       d      "�
V     .debug$S       \             .text$mn       p       戢輊     .debug$S       ,             .text$mn        �       謭p�     .debug$S    !   �               .text$mn    "   "       B�(�     .debug$S    #   (         "    .text$mn    $   1     禕Xd     .debug$S    %   �         $    .text$mn    &   �     D�:A     .debug$S    '   �         &    .text$mn    (   A     �9��     .debug$S    )   �         (    .text$mn    *   a       劯R     .debug$S    +   �         *    .text$mn    ,   3       盇�     .debug$S    -   ,         ,    .text$mn    .   
       �9�     .debug$S    /   �          .    .text$mn    0          痖I     .debug$S    1   �          0    .text$mn    2   �      慌�     .debug$S    3   X         2    .text$mn    4   <      .ズ     .debug$S    5   0  
       4    .text$mn    6   <      .ズ     .debug$S    7   L  
       6    .text$mn    8   !      :著�     .debug$S    9   <         8    .text$mn    :   2      X于     .debug$S    ;   <         :    .text$mn    <   K       }'     .debug$S    =   �         <    .text$mn    >         ��#     .debug$S    ?   �          >    .text$mn    @         ��#     .debug$S    A   �          @    .text$mn    B   B      贘S     .debug$S    C             B    .text$mn    D   B      贘S     .debug$S    E            D    .text$mn    F   B      贘S     .debug$S    G   �          F    .text$mn    H   H       襶.      .debug$S    I   �         H    .text$mn    J   �      砳Z     .debug$S    K   �         J    .text$x     L         y-�#J    .text$mn    M          .B+�     .debug$S    N   �          M    .text$mn    O          .B+�     .debug$S    P   �          O    .text$mn    Q   B     >g�=     .debug$S    R   `  6       Q    .text$mn    S          _婋�     .debug$S    T   �  
       S    .text$mn    U          �}�     .debug$S    V   |  
       U    .text$mn    W          恶Lc     .debug$S    X   l  
       W    .text$mn    Y         覲A     .debug$S    Z   �          Y    .text$mn    [          
B簤     .debug$S    \   �          [    .text$mn    ]   /       @�9Y     .debug$S    ^   P         ]    .text$mn    _   �      ~�p�     .debug$S    `   \         _    .text$mn    a   $       =&!�     .debug$S    b   �          a    .text$mn    c   $       'W#     .debug$S    d   �          c    .text$mn    e   /       9��     .debug$S    f   T         e    .text$mn    g   ;       壂`W     .debug$S    h   L         g    .text$mn    i   	       �祈     .debug$S    j   �          i    .text$mn    k   
       �nr     .debug$S    l   d         k    .text$mn    m           邫k     .debug$S    n   <         m    .text$mn    o          �+斏     .debug$S    p   <         o    .text$mn    q   	       ;4�     .debug$S    r            q    .text$mn    s          �l[     .debug$S    t   �          s    .text$mn    u   P      橯n     .debug$S    v   h         u    .text$mn    w   !      葧`     .debug$S    x            w    .text$mn    y   /       n薃     .debug$S    z   H         y    .text$mn    {   {      櫫T�     .debug$S    |   T         {    .text$mn    }   !       ~�     .debug$S    ~            }    .text$mn       !       1.畣     .debug$S    �                .text$mn    �   
       +燈�     .debug$S    �            �    .text$mn    �   	       禡J�     .debug$S    �   �          �    .text$mn    �          K盈�     .debug$S    �            �    .text$mn    �          嬱K�     .debug$S    �   �          �    .text$mn    �           J咤�     .debug$S    �   �          �    .text$mn    �          LSa     .debug$S    �   �          �    .text$mn    �   !      �醔     .debug$S    �            �    .text$mn    �   !      <K墵     .debug$S    �            �    .text$mn    �   $       禈陨     .debug$S    �   �          �    .text$mn    �          盻}�     .debug$S    �   �          �    .text$mn    �          V絘     .debug$S    �   �          �    .text$mn    �          �89�     .debug$S    �   �          �    .text$mn    �   /       岁z~     .debug$S    �   L         �    .text$mn    �   ;       �0Xi     .debug$S    �   D         �    .text$mn    �   �      勀�/     .debug$S    �   <         �    .text$mn    �   
     扑浽     .debug$S    �   �         �    .text$mn    �   }       踡
�     .debug$S    �   �         �    .text$mn    �         駈8�     .debug$S    �   ,         �    .text$mn    �          [\(�     .debug$S    �   �          �    .text$mn    �          簎x�     .debug$S    �   �          �    .text$mn    �          簎x�     .debug$S    �   �          �    .text$mn    �          R豌�     .debug$S    �   �          �    .text$mn    �          簎x�     .debug$S    �   �          �    .text$mn    �          <�)     .debug$S    �            �    .text$mn    �          [\(�     .debug$S    �   �          �    .text$mn    �          怃兤     .debug$S    �   �          �    .text$mn    �          簎x�     .debug$S    �   �          �    .text$mn    �          簎x�     .debug$S    �   �          �    .text$mn    �          Z�|     .debug$S    �   (         �    .text$mn    �   (     劔�     .debug$S    �   �         �    .text$mn    �   H       )/     .debug$S    �   t  
       �    .text$mn    �          
�     .debug$S    �   (         �    .text$mn    �        糼F$     .debug$S    �   P  0       �    .text$mn    �          ��2     .debug$S    �   @         �    .text$mn    �   �      I缼     .debug$S    �   �  
       �    .text$mn    �   �     哊8     .debug$S    �            �    .text$mn    �   )  	   �8愁     .debug$S    �   (         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �            �    .text$mn    �   A      俙Z%     .debug$S    �   �         �    .text$mn    �         崪覩     .debug$S    �   �          �        �       H        �                �                �                               -               E      :        f      @        �      �        �      F        �          i�                   �      4        �      B                  i�                   =      8        b      >        �      6        �      D        �          i�                         �        /               N               �               �               �               .               i      0        �      .        �      2        �      Q        	      o        J      W        �      O        �      �              �        h      �        �      �              �        ?      �        m      �        �      �                      h      �        �      �        �      �        	      �        7	      �        }	      �        �	      �        �	      �        <
      �        ~
      w        �
      �        
      c        [      {        �      _              �        \      g        �      �        �      s        ?
      �        �
      �        :      J        �      k        �      S        +      M        a      �        �      �        �      �              i        A      [        �      �        �      �        2      }        �      �        �      �        �      �        %      �        W      �        �      �        �      �              �        `      �        �      u        �      �        4      a        �      y        �      ]        0      �        �      e        �      �        $      q        m      m        �      U              Y        D              �      ,        �      (        +      	        l      $        �               �      <                      O      *        �              �              �      &        9              �              �      "                       �              *      
        g              b              (              }              �      L        �               �                                           ceilf            floorf           memset           $LN13       H    $LN5        :    $LN10       F    $LN7        4    $LN13       B    $LN10       6    $LN16       D    $LN3        �    $LN4        �    $LN42       2    $LN155      Q    $LN6        �    $LN426      �    $LN15       �    $LN4        �    $LN4        w    $LN7        {    $LN7        _    $LN18   A   �    $LN21       �    $LN3       �    $LN4        �    $LN37       J    $LN76       �    $LN22       �    $LN184      �    $LN23       �    $LN4        �    $LN10       u    $LN4        �    $LN52           $LN202      (    $LN46       	    $LN12       $    $LN76            $LN18       <    $LN55           $LN14       *    $LN152      &    $LN4            $LN84   �          4          $LN89           $LN30           $LN33           $LN14   :       $LN17           .xdata      �          F┑@H        1!      �    .pdata      �         X賦鶫        U!      �    .xdata      �          （亵:        x!      �    .pdata      �          T枨:        �!      �    .xdata      �          %蚘%F        �!      �    .pdata      �         惻竗F        �!      �    .xdata      �          （亵4        "      �    .pdata      �         2Fb�4        ?"      �    .xdata      �          %蚘%B        g"      �    .pdata      �         惻竗B        �"      �    .xdata      �          （亵6        �"      �    .pdata      �         2Fb�6        �"      �    .xdata      �          %蚘%D        #      �    .pdata      �         惻竗D        M#      �    .xdata      �          懐j炈        ~#      �    .pdata      �         Vbv        �#      �    .xdata      �          嘋c�2        �#      �    .pdata      �         岀M�2        $      �    .xdata      �   (      惝鳇Q        ,$      �    .pdata      �         7陘9Q        �$      �    .xdata      �          %纲�        �$      �    .pdata      �         暫`g�        !%      �    .xdata      �         #弗奚        j%      �    .pdata      �         翎珸�        �%      �    .xdata      �         昝�        �%      �    .pdata      �         �.凣�        &      �    .xdata      �   8      辇矨�        C&      �    .pdata      �         温M仙        {&      �    .xdata      �   0      ,痏吷        �&      �    .pdata      �         9鍙5�        �&      �    .xdata      �         �        #'      �    .pdata      �         S閾�        ['      �    .xdata      �         �鎃�        �'      �    .pdata      �         er=�        �'      �    .xdata      �          （亵�        6(      �    .pdata      �         萣�5�        �(      �    .xdata      �          （亵w        �(      �    .pdata      �         萣�5w        )      �    .xdata      �          #踌{        h)      �    .pdata      �         茘:駕        �)      �    .xdata      �          #踌_        *      �    .pdata      �          媞譥        |*      �    .xdata      �          �9��        �*      �    .pdata      �         s�7逑        ^+      �    .xdata      �          �9��        �+      �    .pdata               �1巴        h,          .xdata              l戴J        �,         .pdata              菏獼        N-         .xdata        	      � )9J        �-         .xdata              n燲癑        .         .xdata               Tp~0J        q.         .voltbl              @$奐    _volmd         .xdata        4       睸袽�        �.         .pdata              g⒌�        #/         .xdata      	         尀猆�        t/      	   .pdata      
        蔴交        �/      
   .xdata               Bn        �/         .pdata              s杳喦        (0         .xdata      
        z�T�        ^0      
   .pdata              `�}�        �0         .xdata              �疗�        �0         .pdata              蠚P馇        1         .xdata              ��?�        >1         .pdata              )谇        v1         .xdata               %蚘%�        �1         .pdata              �        2         .xdata               （亵�        S2         .pdata              萣�5�        �2         .xdata               嫀鵥u        �2         .pdata              企&Uu        93         .xdata               （亵�        �3         .pdata              Vbv鶋        �3         .xdata               
        4         .pdata              悜P�        w4         .xdata        ,       喉俛(        �4         .pdata              `鎥(        5         .xdata               
	        f5         .pdata               7G	        �5          .xdata      !  (       呆垐$        �5      !   .pdata      "        WX�$        <6      "   .xdata      #         竆�4         �6      #   .pdata      $        抿恺         �6      $   .xdata      %         （亵<        7      %   .pdata      &        � �<        @7      &   .xdata      '        范^�<        z7      '   .pdata      (        鳶�<        �7      (   .xdata      )        @鴚`<        �7      )   .pdata      *        [7�<        .8      *   .voltbl     +         飾殪<    _volmd      +   .xdata      ,         ��	        j8      ,   .pdata      -        =�c        �8      -   .xdata      .         1�7*        �8      .   .pdata      /        %燗*        '9      /   .xdata      0  $       &&        d9      0   .pdata      1        Xt^�&        �9      1   .xdata      2         ,t         �9      2   .pdata      3        v��        G:      3   .xdata      4        u苩�        �:      4   .pdata      5        �5�:        �;      5   .xdata      6  
      B>z]        �<      6   .xdata      7         �2g�        �=      7   .xdata      8        T�8        �>      8   .xdata      9        r%�        �?      9   .xdata      :  	       �5啒        堾      :   .xdata      ;         M[�        咥      ;   .pdata      <        現�        態      <   .voltbl     =         熄�    _volmd      =   .voltbl     >                 _volmd      >   .xdata      ?         （亵        汣      ?   .pdata      @        AT        濪      @   .xdata      A         n�v        燛      A   .pdata      B        V6�>        nF      B   .xdata      C        0��        ;G      C   .pdata      D        筲sL        
H      D   .xdata      E        很蓢        貶      E   .pdata      F        �癉        ↖      F   .voltbl     G         3急
    _volmd      G   .xdata      H         �9�        wJ      H   .pdata      I        礝
        訨      I   .rdata      J                     0K     J   .rdata      K         �;�         GK      K   .rdata      L                     nK     L   .rdata      M                     匥     M   .rdata      N         �)               N   .xdata$x    O                     覭      O   .xdata$x    P        虼�)         鮇      P   .data$r     Q  /      嶼�         L      Q   .xdata$x    R  $      4��         =L      R   .data$r     S  $      鎊=         扡      S   .xdata$x    T  $      銸E�         琇      T   .data$r     U  $      騏糡         隠      U   .xdata$x    V  $      4��         M      V       DM           .data       W          烀�          WM      W       婱     W   .rdata      X         IM         睲      X   .rdata$r    Y  $      'e%�         豈      Y   .rdata$r    Z        �          餗      Z   .rdata$r    [                     N      [   .rdata$r    \  $      Gv�:         N      \   .rdata$r    ]  $      'e%�         ;N      ]   .rdata$r    ^        }%B         SN      ^   .rdata$r    _                     iN      _   .rdata$r    `  $      `         N      `   .rdata$r    a  $      'e%�         濶      a   .rdata$r    b        �弾         罭      b   .rdata$r    c                     釴      c   .rdata$r    d  $      H衡�         O      d   .rdata      e         紴V�         -O      e   .rdata      f         =-f�         =O      f   .rdata      g         v靛�         MO      g   .rdata      h         [         ]O      h   .rdata      i         V6]`         mO      i   .rdata      j         奥d�         }O      j       峅           .rdata      k         z�         烵      k   .rdata      l         �         芆      l   .rdata      m         �;�=         鞳      m   .rdata      n         v靛�         P      n   .rdata      o         iI         ;P      o   .rdata      p         �a�         bP      p   _fltused         .debug$S    q  4          J   .debug$S    r  4          L   .debug$S    s  @          M   .chks64     t  �                塒  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ?g_CubemapViewMatrices@@3QBU?$matrix@M$02$02@math@donut@@B ?g_CubemapCoordinateSwizzle@@3QBIB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?perspProjD3DStyle@math@donut@@YA?AU?$matrix@M$03$03@12@MMMMMM@Z ??0frustum@math@donut@@QEAA@AEBU012@@Z ??0frustum@math@donut@@QEAA@AEBU?$matrix@M$03$03@12@_N@Z ?intersectsWith@frustum@math@donut@@QEBA_NAEBU?$box@M$02@23@@Z ?fromBox@frustum@math@donut@@SA?AU123@AEBU?$box@M$02@23@@Z ??0Viewport@nvrhi@@QEAA@XZ ??0Rect@nvrhi@@QEAA@XZ ??0ViewportState@nvrhi@@QEAA@XZ ?FillPlanarViewConstants@IView@engine@donut@@UEBAXAEAUPlanarViewConstants@@@Z ?GetNumChildViews@IView@engine@donut@@UEBAIW4Enum@ViewType@23@@Z ?GetChildView@IView@engine@donut@@UEBAPEBV123@W4Enum@ViewType@23@I@Z ?EnsureCacheIsValid@PlanarView@engine@donut@@IEBAXXZ ?SetViewport@PlanarView@engine@donut@@QEAAXAEBUViewport@nvrhi@@@Z ?SetVariableRateShadingState@PlanarView@engine@donut@@QEAAXAEBUVariableRateShadingState@nvrhi@@@Z ?SetMatrices@PlanarView@engine@donut@@QEAAXAEBU?$affine@M$02@math@3@AEBU?$matrix@M$03$03@53@@Z ?SetPixelOffset@PlanarView@engine@donut@@QEAAXU?$vector@M$01@math@3@@Z ?SetArraySlice@PlanarView@engine@donut@@QEAAXH@Z ?UpdateCache@PlanarView@engine@donut@@QEAAXXZ ?GetViewportState@PlanarView@engine@donut@@UEBA?AUViewportState@nvrhi@@XZ ?GetVariableRateShadingState@PlanarView@engine@donut@@UEBA?AUVariableRateShadingState@nvrhi@@XZ ?GetSubresources@PlanarView@engine@donut@@UEBA?AUTextureSubresourceSet@nvrhi@@XZ ?IsReverseDepth@PlanarView@engine@donut@@UEBA_NXZ ?IsOrthographicProjection@PlanarView@engine@donut@@UEBA_NXZ ?IsStereoView@PlanarView@engine@donut@@UEBA_NXZ ?IsCubemapView@PlanarView@engine@donut@@UEBA_NXZ ?IsBoxVisible@PlanarView@engine@donut@@UEBA_NAEBU?$box@M$02@math@3@@Z ?IsMirrored@PlanarView@engine@donut@@UEBA_NXZ ?GetViewOrigin@PlanarView@engine@donut@@UEBA?AU?$vector@M$02@math@3@XZ ?GetViewDirection@PlanarView@engine@donut@@UEBA?AU?$vector@M$02@math@3@XZ ?GetViewFrustum@PlanarView@engine@donut@@UEBA?AUfrustum@math@3@XZ ?GetProjectionFrustum@PlanarView@engine@donut@@UEBA?AUfrustum@math@3@XZ ?GetViewMatrix@PlanarView@engine@donut@@UEBA?AU?$affine@M$02@math@3@XZ ?GetInverseViewMatrix@PlanarView@engine@donut@@UEBA?AU?$affine@M$02@math@3@XZ ?GetProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetInverseProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetViewProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetInverseViewProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetViewExtent@PlanarView@engine@donut@@UEBA?AURect@nvrhi@@XZ ?GetPixelOffset@PlanarView@engine@donut@@UEBA?AU?$vector@M$01@math@3@XZ ?deallocate@?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VIView@engine@donut@@@2@_K@Z ?_Xlength@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@CAXXZ ?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z ?GetNumChildViews@CompositeView@engine@donut@@UEBAIW4Enum@ViewType@23@@Z ?GetChildView@CompositeView@engine@donut@@UEBAPEBVIView@23@W4Enum@ViewType@23@I@Z ?EnsureCacheIsValid@CubemapView@engine@donut@@IEBAXXZ ?SetTransform@CubemapView@engine@donut@@QEAAXU?$affine@M$02@math@3@MM_N@Z ?SetArrayViewports@CubemapView@engine@donut@@QEAAXHH@Z ?UpdateCache@CubemapView@engine@donut@@QEAAXXZ ?GetNearPlane@CubemapView@engine@donut@@QEBAMXZ ?GetCullingBox@CubemapView@engine@donut@@QEBA?AU?$box@M$02@math@3@XZ ?GetViewportState@CubemapView@engine@donut@@UEBA?AUViewportState@nvrhi@@XZ ?GetVariableRateShadingState@CubemapView@engine@donut@@UEBA?AUVariableRateShadingState@nvrhi@@XZ ?GetSubresources@CubemapView@engine@donut@@UEBA?AUTextureSubresourceSet@nvrhi@@XZ ?IsReverseDepth@CubemapView@engine@donut@@UEBA_NXZ ?IsOrthographicProjection@CubemapView@engine@donut@@UEBA_NXZ ?IsStereoView@CubemapView@engine@donut@@UEBA_NXZ ?IsCubemapView@CubemapView@engine@donut@@UEBA_NXZ ?IsBoxVisible@CubemapView@engine@donut@@UEBA_NAEBU?$box@M$02@math@3@@Z ?IsMirrored@CubemapView@engine@donut@@UEBA_NXZ ?GetViewOrigin@CubemapView@engine@donut@@UEBA?AU?$vector@M$02@math@3@XZ ?GetViewDirection@CubemapView@engine@donut@@UEBA?AU?$vector@M$02@math@3@XZ ?GetViewFrustum@CubemapView@engine@donut@@UEBA?AUfrustum@math@3@XZ ?GetProjectionFrustum@CubemapView@engine@donut@@UEBA?AUfrustum@math@3@XZ ?GetViewMatrix@CubemapView@engine@donut@@UEBA?AU?$affine@M$02@math@3@XZ ?GetInverseViewMatrix@CubemapView@engine@donut@@UEBA?AU?$affine@M$02@math@3@XZ ?GetProjectionMatrix@CubemapView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetInverseProjectionMatrix@CubemapView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetViewProjectionMatrix@CubemapView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetInverseViewProjectionMatrix@CubemapView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z ?GetViewExtent@CubemapView@engine@donut@@UEBA?AURect@nvrhi@@XZ ?GetPixelOffset@CubemapView@engine@donut@@UEBA?AU?$vector@M$01@math@3@XZ ?GetNumChildViews@CubemapView@engine@donut@@UEBAIW4Enum@ViewType@23@@Z ?GetChildView@CubemapView@engine@donut@@UEBAPEBVIView@23@W4Enum@ViewType@23@I@Z ?GetCubemapCoordinateSwizzle@CubemapView@engine@donut@@SAPEAIXZ ??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z ??$translation@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z ??$inverse@M$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@@Z ??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z ??$inverse@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@@Z ??$determinant@M@math@donut@@YAMAEBU?$matrix@M$02$02@01@@Z ??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ ??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z ??$scaling@M$02@math@donut@@YA?AU?$affine@M$02@01@M@Z ??$?_0M@math@donut@@YAAEAU?$vector@M$03@01@AEAU201@M@Z ??$?YM@math@donut@@YAAEAU?$vector@M$03@01@AEAU201@AEBU201@@Z ??$inverse@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@@Z ??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z ??$?DM$02$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@0@Z ??$diagonal@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@M@Z ??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z ??$?_0M@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@M@Z ??$?YM@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@AEBU201@@Z ??$_Uninitialized_move@PEAV?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z ??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z@4HA ?dtor$0@?0??AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __security_check_cookie __catch$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??0ViewportState@nvrhi@@QEAA@XZ $pdata$??0ViewportState@nvrhi@@QEAA@XZ $unwind$?FillPlanarViewConstants@IView@engine@donut@@UEBAXAEAUPlanarViewConstants@@@Z $pdata$?FillPlanarViewConstants@IView@engine@donut@@UEBAXAEAUPlanarViewConstants@@@Z $unwind$?SetViewport@PlanarView@engine@donut@@QEAAXAEBUViewport@nvrhi@@@Z $pdata$?SetViewport@PlanarView@engine@donut@@QEAAXAEBUViewport@nvrhi@@@Z $unwind$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $pdata$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $chain$0$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $pdata$0$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $chain$10$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $pdata$10$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $chain$11$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $pdata$11$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $chain$12$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $pdata$12$?UpdateCache@PlanarView@engine@donut@@QEAAXXZ $unwind$?GetViewportState@PlanarView@engine@donut@@UEBA?AUViewportState@nvrhi@@XZ $pdata$?GetViewportState@PlanarView@engine@donut@@UEBA?AUViewportState@nvrhi@@XZ $unwind$?GetViewFrustum@PlanarView@engine@donut@@UEBA?AUfrustum@math@3@XZ $pdata$?GetViewFrustum@PlanarView@engine@donut@@UEBA?AUfrustum@math@3@XZ $unwind$?GetProjectionFrustum@PlanarView@engine@donut@@UEBA?AUfrustum@math@3@XZ $pdata$?GetProjectionFrustum@PlanarView@engine@donut@@UEBA?AUfrustum@math@3@XZ $unwind$?GetProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z $pdata$?GetProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z $unwind$?GetInverseProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z $pdata$?GetInverseProjectionMatrix@PlanarView@engine@donut@@UEBA?AU?$matrix@M$03$03@math@3@_N@Z $unwind$?deallocate@?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VIView@engine@donut@@@2@_K@Z $pdata$?deallocate@?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VIView@engine@donut@@@2@_K@Z $unwind$?_Xlength@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@CAXXZ $unwind$?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z $pdata$?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z $cppxdata$?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z $stateUnwindMap$?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z $ip2state$?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z $unwind$?SetTransform@CubemapView@engine@donut@@QEAAXU?$affine@M$02@math@3@MM_N@Z $pdata$?SetTransform@CubemapView@engine@donut@@QEAAXU?$affine@M$02@math@3@MM_N@Z $unwind$?SetArrayViewports@CubemapView@engine@donut@@QEAAXHH@Z $pdata$?SetArrayViewports@CubemapView@engine@donut@@QEAAXHH@Z $unwind$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $pdata$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $chain$1$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $pdata$1$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $chain$2$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $pdata$2$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $chain$3$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $pdata$3$?UpdateCache@CubemapView@engine@donut@@QEAAXXZ $unwind$?GetViewportState@CubemapView@engine@donut@@UEBA?AUViewportState@nvrhi@@XZ $pdata$?GetViewportState@CubemapView@engine@donut@@UEBA?AUViewportState@nvrhi@@XZ $unwind$?GetViewFrustum@CubemapView@engine@donut@@UEBA?AUfrustum@math@3@XZ $pdata$?GetViewFrustum@CubemapView@engine@donut@@UEBA?AUfrustum@math@3@XZ $unwind$?GetProjectionFrustum@CubemapView@engine@donut@@UEBA?AUfrustum@math@3@XZ $pdata$?GetProjectionFrustum@CubemapView@engine@donut@@UEBA?AUfrustum@math@3@XZ $unwind$?GetViewExtent@CubemapView@engine@donut@@UEBA?AURect@nvrhi@@XZ $pdata$?GetViewExtent@CubemapView@engine@donut@@UEBA?AURect@nvrhi@@XZ $unwind$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $pdata$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $unwind$??$inverse@M$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@@Z $pdata$??$inverse@M$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@@Z $unwind$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $pdata$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $unwind$??$inverse@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@@Z $pdata$??$inverse@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@@Z $unwind$??$determinant@M@math@donut@@YAMAEBU?$matrix@M$02$02@01@@Z $pdata$??$determinant@M@math@donut@@YAMAEBU?$matrix@M$02$02@01@@Z $unwind$??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VIView@engine@donut@@@std@@QEAA@XZ $unwind$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $pdata$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $unwind$??$scaling@M$02@math@donut@@YA?AU?$affine@M$02@01@M@Z $pdata$??$scaling@M$02@math@donut@@YA?AU?$affine@M$02@01@M@Z $unwind$??$inverse@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@@Z $pdata$??$inverse@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@@Z $unwind$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $pdata$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $unwind$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $pdata$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $cppxdata$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $tryMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $handlerMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $ip2state$??$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VIView@engine@donut@@@std@@@?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VIView@engine@donut@@@1@QEAV21@AEBV21@@Z@4HA $unwind$??$_Uninitialized_move@PEAV?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $pdata$??$_Uninitialized_move@PEAV?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $unwind$??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $chain$0$??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $pdata$0$??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $chain$1$??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $pdata$1$??$_Destroy_range@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VIView@engine@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@0@@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@358637bd __real@3f000000 __real@3f800000 __real@bf000000 __real@bf800000 __real@c0000000 __security_cookie __xmm@0000000000000000000000003f800000 __xmm@00000000000000003f80000000000000 __xmm@000000003f8000000000000000000000 __xmm@3f800000000000000000000000000000 __xmm@7fffffff7fffffff7fffffff7fffffff __xmm@80000000800000008000000080000000 