d嘃 翀c�       .drectve        <  t'               
 .debug$S        鳹 �(  �        @ B.debug$T        p   �             @ B.rdata          @   h�             @ @@.text$mn        :   █ 鈥         P`.debug$S           � �        @B.text$mn        0   槂 葍         P`.debug$S        �  覂 矃        @B.text$mn        c   >�              P`.debug$S        �   )�        @B.text$mn        p                 P`.debug$S        �  � 眾        @B.text$mn        +  )� T�         P`.debug$S        p  悗  �     Z   @B.text$x            劉 悽         P`.text$x            殺 Β         P`.text$mn        �   阿              P`.debug$S        �  4� 0�        @B.text$mn            笑              P`.debug$S        T  穰 D�        @B.text$mn           洫              P`.debug$S        �   瑾 阔        @B.text$mn        l   h�         P`.debug$S        \  b� 旧     �   @B.text$x            >� J�         P`.text$x            T� d�         P`.text$x            n� ~�         P`.text$x            堁 樠         P`.text$x            ⒀ 惭         P`.text$x            佳 萄         P`.text$x            盅 嫜         P`.text$x            鹧  �         P`.text$x            
� �         P`.text$mn        <   $� `�         P`.debug$S        0  ~�      
   @B.text$mn        <   � N�         P`.debug$S        L  l� 刚     
   @B.text$mn        !   � =�         P`.debug$S        <  Q� 嵶        @B.text$mn        2   勺          P`.debug$S        <  � K�        @B.text$mn        "   觅              P`.debug$S        �  遒 }�        @B.text$mn        "   �              P`.debug$S        �  ?� 溯        @B.text$mn        "   k�              P`.debug$S        �  嵽 �        @B.text$mn        "   灌              P`.debug$S        �  坂 g�        @B.text$mn           � �         P`.debug$S        h  %� 嶅        @B.text$mn        e   蒎 B�         P`.debug$S        8  `� 橂        @B.text$mn        [   `� 混         P`.debug$S           响 镳        @B.text$mn        J   笋 �         P`.debug$S        �  � ｕ        @B.text$mn        }   k� 桷         P`.debug$S        �   茺        @B.text$mn           更 傈         P`.debug$S          庶 恺        @B.text$mn           � #�         P`.debug$S        �  -� �         @B.text$mn        [    t         P`.debug$S        T  � �        @B.text$mn           � �         P`.debug$S        �   � �        @B.text$mn           � �         P`.debug$S        �    �        @B.text$mn           . 3         P`.debug$S        �   = 	        @B.text$mn        B   )	 k	         P`.debug$S           �	 �
        @B.text$mn        B   �
          P`.debug$S          % 5        @B.text$mn        B   q �         P`.debug$S        �   � �
        @B.text$mn        H   	              P`.debug$S        �  Q         @B.text$mn        a   - �         P`.debug$S          � �     
   @B.text$mn        D  , p         P`.debug$S        x  B �     <   @B.text$mn           !              P`.debug$S        �   ! "        @B.text$mn        �   O" /#         P`.debug$S        �  k# )     *   @B.text$x            �* �*         P`.text$mn        1   �* �*         P`.debug$S        �   + �,     
   @B.text$mn        :  ,- f.         P`.debug$S        �  �. F6     <   @B.text$mn        �  �8 �:         P`.debug$S        �  �: dF     P   @B.text$mn            処          P`.debug$S        �   翴 咼        @B.text$mn        {  翵 =M         P`.debug$S        �
  QM )X     H   @B.text$mn           鵝 
[         P`.debug$S          [ 6\        @B.text$mn        �   r\ B]         P`.debug$S        H  `] ╞        @B.text$mn           纁 觕         P`.debug$S        �   輈 眃        @B.xdata             韉             @0@.pdata             e 
e        @0@.xdata             +e             @0@.pdata             3e ?e        @0@.xdata             ]e             @0@.pdata             ie ue        @0@.xdata             揺             @0@.pdata             沞         @0@.xdata             舉             @0@.pdata             裡 輊        @0@.xdata             鹐             @0@.pdata             f f        @0@.xdata             -f             @0@.pdata             9f Ef        @0@.xdata             cf             @0@.pdata             kf wf        @0@.xdata             昮         @0@.pdata             筬 舊        @0@.xdata          	   鉬 靎        @@.xdata              g g        @@.xdata             g             @@.xdata             g #g        @0@.pdata             7g Cg        @0@.xdata          	   ag jg        @@.xdata             ~g 刧        @@.xdata             巊             @@.xdata             慻         @0@.pdata             礸 羐        @0@.xdata          	   遟 鑗        @@.xdata             黦 h        @@.xdata             h             @@.xdata             h +h        @0@.pdata             ?h Kh        @0@.xdata          	   ih rh        @@.xdata             唄 宧        @@.xdata             杊             @@.xdata              檋 筯        @0@.pdata             蚳 賖        @0@.xdata          	   鱤  i        @@.xdata             i i        @@.xdata             $i             @@.xdata             +i Gi        @0@.pdata             [i gi        @0@.xdata          	   卛 巌        @@.xdata              ╥        @@.xdata             瞚             @@.xdata             礽             @0@.pdata             羒 蚷        @0@.xdata             雐 j        @0@.pdata             !j -j        @0@.xdata             Kj _j        @0@.pdata             }j 塲        @0@.xdata              穓        @0@.pdata             誮 醞        @0@.xdata             �j k        @0@.pdata             9k Ek        @0@.xdata             ck sk        @0@.pdata             慿 漦        @0@.xdata             籯             @0@.pdata             莐 觡        @0@.xdata             駅 	l        @0@.pdata             'l 3l        @0@.xdata             Ql al        @0@.pdata             l 媗        @0@.xdata             ﹍ 羖        @0@.pdata             遧 雔        @0@.xdata             	m             @0@.pdata             m m        @0@.xdata             ;m             @0@.pdata             Cm Om        @0@.xdata             mm             @0@.pdata             um 乵        @0@.xdata             焟             @0@.pdata              砿        @0@.xdata             裮 醡        @0@.pdata             鮩 n        @0@.xdata          	   n (n        @@.xdata             <n Bn        @@.xdata             Ln             @@.xdata          $   On sn        @0@.pdata             }n 塶        @0@.xdata          $    薾        @0@.pdata             遪 雗        @0@.xdata          	   	o o        @@.xdata          C   &o io     
   @@.xdata             雘             @@.xdata             p p        @0@.pdata             3p ?p        @0@.xdata          	   ]p fp        @@.xdata             zp 唒        @@.xdata          	   歱             @@.xdata                          @0@.pdata             痯 籶        @0@.xdata              賞 鵳        @0@.pdata             
q q        @0@.xdata          	   7q @q        @@.xdata             Tq bq        @@.xdata          
   vq             @@.xdata             �q             @0@.pdata             坬 攓        @0@.xdata             瞦 苢        @0@.pdata             鋛 餼        @0@.xdata             r r        @0@.pdata             <r Hr        @0@.xdata             fr vr        @0@.pdata             妑 杛        @0@.xdata          	   磖 絩        @@.xdata             裷 譺        @@.xdata             醨             @@.xdata             鋜             @0@.pdata             靣 鴕        @0@.xdata             s             @0@.pdata             "s .s        @0@.rdata             Ls ds        @@@.rdata             俿             @@@.rdata             攕 瑂        @@@.rdata             蕇 鈙        @@@.rdata              t             @@@.xdata$x           t 1t        @@@.xdata$x           Et at        @@@.data$r         /   t 畉        @@�.xdata$x        $   竧 躷        @@@.data$r         $   餿 u        @@�.xdata$x        $   u Bu        @@@.data$r         $   Vu zu        @@�.xdata$x        $   剈 ╱        @@@.data               紆             @ @�.rdata          :   躸             @@@.rdata             v             @@@.rdata             1v             @@@.rdata             Av             @@@.rdata$r        $   [v v        @@@.rdata$r           漹 眝        @@@.rdata$r           籿 莢        @@@.rdata$r        $   裿 鮲        @@@.rdata$r        $   	w -w        @@@.rdata$r           Kw _w        @@@.rdata$r           iw }w        @@@.rdata$r        $   憌 祑        @@@.rdata$r        $   蓋 韜        @@@.rdata$r           x x        @@@.rdata$r           )x Ex        @@@.rdata$r        $   cx 噚        @@@.rdata             泋             @0@.debug$S        4   焫 觴        @B.debug$S        4   鐇 y        @B.debug$S        @   /y oy        @B.chks64         �  儁              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �     r     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\MaterialBindingCache.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $math 	 $colors  $log  $Json   �   恓  R    std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment 4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align " �    std::memory_order_relaxed " �   std::memory_order_consume + �    std::_Aligned_storage<64,8>::_Fits " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits  �4   _Mtx_try  �4   _Mtx_recursive E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment  C5   std::_INVALID_ARGUMENT  C5   std::_NO_SUCH_PROCESS & C5   std::_OPERATION_NOT_PERMITTED , C5   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - C5   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst - d    std::integral_constant<int,0>::value C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size :    std::integral_constant<unsigned __int64,2>::value � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos i    std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible - �    std::chrono::system_clock::is_steady � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable $ E   std::ratio<1,10000000>::num ( E  ��枠 std::ratio<1,10000000>::den �    std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable  E   std::ratio<1,1>::num  E   std::ratio<1,1>::den J E   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N E  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( E  ��枠 std::ratio<10000000,1>::num $ E   std::ratio<10000000,1>::den P E  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < E  ��枠 std::integral_constant<__int64,10000000>::value 1 E   std::integral_constant<__int64,1>::value 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE - �   std::chrono::steady_clock::is_steady E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity & E   std::ratio<1,1000000000>::num * E  � 蕷;std::ratio<1,1000000000>::den d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource   E  std::ratio<3600,1>::num A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor   E   std::ratio<3600,1>::den F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free  E  < std::ratio<60,1>::num  E   std::ratio<60,1>::den / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable A    std::allocator<bool>::_Minimum_asan_allocation_alignment I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment   E   std::ratio<1,1000>::num   E  �std::ratio<1,1000>::den Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment # E   std::ratio<1,1000000>::num ' E  �@B std::ratio<1,1000000>::den \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment  d    LightType_None  d   LightType_Directional  d   LightType_Spot  d   LightType_Point    ��I@donut::math::PI_f "   
�-DT�!	@donut::math::PI_d !   ��7�5donut::math::epsilon "   �  �donut::math::infinity    �  �donut::math::NaN 7 �   std::atomic<unsigned int>::is_always_lock_free 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment O    std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources ) �   donut::math::vector<bool,4>::DIM J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P E  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos Z    std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos   �        nvrhi::EntireBuffer L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos Z    std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits c    std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable �    std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment f�    std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi i�   std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard �    std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment Z    std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment :     std::integral_constant<unsigned __int64,0>::value ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val y   std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset y   std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size s�    std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy B    std::allocator<float>::_Minimum_asan_allocation_alignment - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment *         donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM + <        nvrhi::rt::c_IdentityTransform  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix . d   donut::math::box<float,3>::numCorners ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 � �   std::_Trivial_cat<donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding &,donut::engine::MaterialResourceBinding &>::_Same_size_and_compatible ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style � �   std::_Trivial_cat<donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding &,donut::engine::MaterialResourceBinding &>::_Bitcopy_constructible $ d   std::_Num_float_base::radix � �   std::_Trivial_cat<donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding &,donut::engine::MaterialResourceBinding &>::_Bitcopy_assignable * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 c    std::allocator<donut::engine::MaterialResourceBinding>::_Minimum_asan_allocation_alignment 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 :    std::integral_constant<unsigned __int64,1>::value 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask 0 �   std::numeric_limits<wchar_t>::is_modulo O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits , d  	 std::numeric_limits<long>::digits10 : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - d  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / d   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 j    std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 �   �  , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent   �   =   ; d  �威std::numeric_limits<long double>::min_exponent10    �   僵   �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> & �5  $_TypeDescriptor$_extraBytes_28     int64_t    _Smtx_t  �(  _Thrd_result  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e & i4  $_TypeDescriptor$_extraBytes_23 - �4  $_s__CatchableTypeArray$_extraBytes_32  �5  _TypeDescriptor & �4  $_TypeDescriptor$_extraBytes_34  �5  _Stl_critical_section 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const> E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16 d �=  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > G >  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > a '<  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> ] i;  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ oK  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > [ >  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � �=  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > �  ;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 
K  std::_Default_allocator_traits<std::allocator<float> > C G:  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 僈  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 
;  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C :  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � [;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | �=  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > M �=  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L >  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s >  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > T {K  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > U qK  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > : �;  std::_Vector_val<std::_Simple_types<unsigned int> > D >  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � >  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 濱  std::_Ptr_base<donut::engine::DescriptorHandle> � |;  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> U >K  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > "T8  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > U 馢  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > 4 cK  std::allocator<donut::math::vector<float,2> > = *K  std::allocator<donut::math::vector<unsigned short,4> > K TK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U 塈  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 蘆  std::_Ptr_base<donut::engine::BufferGroup> F;  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � ;:  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> e I  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > { �=  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > , 3:  std::allocator<nvrhi::BindingSetItem> K JK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > # �:  std::allocator<unsigned int> J I  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � :  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> D �=  std::_Default_allocator_traits<std::allocator<unsigned int> > L @K  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  uI  std::allocator<float> � 2K  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> t 玃  std::_Simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > K  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � K  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1> � 橮  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 4 K  std::allocator_traits<std::allocator<float> > [ O;  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >  i5  std::input_iterator_tag ; �=  std::allocator_traits<std::allocator<unsigned int> > [ K  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > � 09  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > . �'  std::_Conditionally_enabled_hash<int,1> � 睳  std::_Compressed_pair<std::hash<donut::engine::Material const *>,std::_Compressed_pair<std::equal_to<donut::engine::Material const *>,float,1>,1> ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> > . �2  std::integer_sequence<unsigned __int64>  .  std::_Lockit  �)  std::timed_mutex * 2/  std::hash<enum nvrhi::ResourceType> " i3  std::_Char_traits<char,int> � =N  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > � 鱆  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �2  std::_Num_base & �'  std::hash<std::error_condition> K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >  y$  std::_Big_uint128  ))  std::condition_variable ) v3  std::_Narrow_char_traits<char,int> L 驤  std::allocator_traits<std::allocator<donut::math::vector<float,2> > >    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc> y 璓  std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � O  std::_Tidy_guard<std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> > > " �5  std::_Align_type<double,64>  �'  std::hash<int>  �2  std::_Num_int_base " k(  std::_System_error_category / Q/  std::_Conditionally_enabled_hash<bool,1> 2 錔  std::shared_ptr<donut::engine::BufferGroup> � �8  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 4O  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > � 霱  std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >  �2  std::float_denorm_style � gN  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 4 璄  std::shared_ptr<donut::engine::LoadedTexture> ! %0  std::piecewise_construct_t ^ 郞  std::_Uninitialized_backout_al<std::allocator<donut::engine::MaterialResourceBinding> > . 5I  std::_Ptr_base<donut::engine::MeshInfo> 6 �5  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast 4 際  std::allocator<donut::math::vector<float,4> > � yM  std::_Compressed_pair<std::allocator<donut::engine::MaterialResourceBinding>,std::_Vector_val<std::_Simple_types<donut::engine::MaterialResourceBinding> >,1> q 笿  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 嘕  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy " �2  std::numeric_limits<double>  <&  std::__non_rtti_object < �6  std::_Ptr_base<donut::engine::DescriptorTableManager> ( n  std::_Basic_container_proxy_ptr12 4 BJ  std::allocator<donut::math::vector<float,3> > > �:  std::vector<unsigned int,std::allocator<unsigned int> > T �:  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base  �&  std::logic_error 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety P 3J  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f �7  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �5  std::char_traits<char32_t> � mP  std::list<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >   �2  std::numeric_limits<bool> # �3  std::_WChar_traits<char16_t> _ /J  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u 蘒  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> T 哅  std::_Vector_val<std::_Simple_types<donut::engine::MaterialResourceBinding> > � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  '  std::overflow_error % --  std::_One_then_variadic_args_t D �/  std::_Constexpr_immortalize_impl<std::_System_error_category> W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * �8  std::_Vb_val<std::allocator<bool> > &塋  std::unordered_map<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *>,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >   �5  std::char_traits<wchar_t> _ 芃  std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >  �(  std::recursive_mutex J   std::_Conditionally_enabled_hash<donut::engine::Material const *,1>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> � 縄  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  �5  std::false_type  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy ! �(  std::hash<std::thread::id>  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  )  std::adopt_lock_t � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 稩  std::shared_ptr<donut::engine::DescriptorHandle> , �2  std::numeric_limits<unsigned __int64> L 婭  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound  �(  std::_Mutex_base r M  std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> > � 螸  std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> >::_Reallocation_policy b }I  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> D gI  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  �5  std::defer_lock_t   a'  std::_Init_once_completer  )  std::scoped_lock<> h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> ! �'  std::hash<std::error_code> � YM  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > � ∕  std::pair<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *,bool> @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  U/  std::hash<bool> 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> " �)  std::lock_guard<std::mutex> K XI  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >  (  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy / NI  std::shared_ptr<donut::engine::MeshInfo>  �5  std::try_to_lock_t U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> T O  std::_Compressed_pair<std::equal_to<donut::engine::Material const *>,float,1> " .)  std::_Align_type<double,72> � 1N  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0> / �3  std::_Char_traits<char32_t,unsigned int>  �'  std::_System_error ( 1$  std::hash<nvrhi::FramebufferInfo>  �'  std::error_condition % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128>  �&  std::invalid_argument N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >  ")  std::cv_status S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 !I  std::_Vector_val<std::_Simple_types<float> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A I  std::allocator_traits<std::allocator<nvrhi::BufferRange> >  �(  std::thread  �(  std::thread::id  �&  std::length_error \ 	I  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > ! �2  std::numeric_limits<float> ? P  std::_Tuple_val<donut::engine::Material const * const &> � N  std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ) �  std::_Atomic_integral_facade<long>  @)  std::mutex % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  �5  std::ratio<60,1> 3 旹  std::_Ptr_base<donut::engine::LoadedTexture>  t  std::exception_ptr  �5  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t>  Y'  std::once_flag  �'  std::error_code J �6  std::enable_shared_from_this<donut::engine::DescriptorTableManager>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 �5  std::allocator_traits<std::allocator<char32_t> > : ]O  std::tuple<donut::engine::Material const * const &>  {5  std::nano  �  std::_Iterator_base0 | 鸋  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> � O  std::_Hash_find_last_result<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *> $ �#  std::hash<nvrhi::BufferRange> ! �5  std::char_traits<char16_t>  |  std::tuple<>    std::_Container_base12  �'  std::io_errc � ≒  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > E �/  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 �2  std::integer_sequence<unsigned __int64,0> ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char>  x5  std::_Invoker_strategy w 稭  std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> � 驢  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> $ �2  std::_Default_allocate_traits 3 �5  std::allocator_traits<std::allocator<char> > ! �2  std::numeric_limits<short>  u   std::_Vbase . �0  std::allocator<nvrhi::rt::GeometryDesc> # d)  std::unique_lock<std::mutex> ( aE  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 5 O  std::equal_to<donut::engine::Material const *> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock p 誐  std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  D  std::bad_alloc  /'  std::underflow_error B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  蹾  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � 狧  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy  �&  std::out_of_range � QN  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > # �2  std::numeric_limits<__int64> _ eH  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 4H  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy @ 鯣  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >  �  std::memory_order �[N  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> ! �)  std::recursive_timed_mutex  �5  std::ratio<3600,1> = rM  std::allocator<donut::engine::MaterialResourceBinding> # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>   (  std::system_error < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  �5  std::ratio<1,1>   k5  std::forward_iterator_tag  '  std::runtime_error   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >    std::_Container_proxy  �9  std::allocator<bool> _ 霨  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 籊  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy  �  std::nested_exception  r  std::_Distance_unknown ) }G  std::allocator<nvrhi::BufferRange> ( �2  std::numeric_limits<unsigned int> %N  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ^ vP  std::_Default_allocator_traits<std::allocator<donut::engine::MaterialResourceBinding> > ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff 0 nG  std::vector<float,std::allocator<float> > F <G  std::vector<float,std::allocator<float> >::_Reallocation_policy    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double>  �'  std::errc \扤  std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>  F'  std::range_error iDL  std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > x侼  std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Range_eraser w禣  std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Clear_guard  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  p)  std::_UInt_is_zero  {5  std::ratio<1,1000000000>  �,  std::allocator<char16_t> .  G  std::vector<bool,std::allocator<bool> > J 麱  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 薋  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  v5  std::ratio<1,1000>  t5  std::ratio<1,10000000> w   std::initializer_list<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> % ^(  std::_Iostream_error_category2 * r5  std::_String_constructor_concat_tag  D-  std::allocator<char> G �/  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1>  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> � 汸  std::allocator_traits<std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring 1 ∟  std::hash<donut::engine::Material const *> ' �2  std::numeric_limits<signed char>  �&  std::domain_error  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > ; �6  std::weak_ptr<donut::engine::DescriptorTableManager> $ &/  std::hash<nvrhi::IResource *> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char> 9 o%  std::chrono::duration<__int64,std::ratio<1,1000> >  �$  std::chrono::nanoseconds ? �$  std::chrono::duration<__int64,std::ratio<1,1000000000> >  �$  std::chrono::seconds 3 -%  std::chrono::duration<int,std::ratio<60,1> > 6 �$  std::chrono::duration<__int64,std::ratio<1,1> > s �$  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   U5  std::chrono::steady_clock   R5  std::chrono::system_clock 6 B%  std::chrono::duration<double,std::ratio<60,1> > ; �%  std::chrono::duration<double,std::ratio<1,1000000> > > �%  std::chrono::duration<double,std::ratio<1,1000000000> > = �$  std::chrono::duration<__int64,std::ratio<1,10000000> > q �$  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 %  std::chrono::duration<int,std::ratio<3600,1> > 8 �%  std::chrono::duration<double,std::ratio<1,1000> > < �%  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 Y%  std::chrono::duration<double,std::ratio<1,1> > 8 %  std::chrono::duration<double,std::ratio<3600,1> >  K5  std::char_traits<char> � 嶰  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *>  �'  std::error_category ) �'  std::error_category::_Addr_storage ! >(  std::_System_error_message  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> @ �3  std::_Default_allocator_traits<std::allocator<char16_t> >  -2  std::_Exact_args_t � ~P  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty U 孭  std::allocator_traits<std::allocator<donut::engine::MaterialResourceBinding> > ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t> � 圥  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > # P(  std::_Generic_error_category  U3  std::streampos � N  std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > ' y/  std::hash<enum nvrhi::ColorMask>  淜  std::bad_function_call O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > � 贛  std::_Uhash_choose_transparency<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *>,void>  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � @O  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > � 奝  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  j   nvrhi::GraphicsState * 0M  nvrhi::RefCountPtr<nvrhi::ISampler> / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  0M  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # =7  nvrhi::DescriptorTableHandle  �  nvrhi::ShaderType  "#  nvrhi::TimerQueryHandle 2 =7  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - `+  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  岶  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  �?  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 岶  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  `+  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # gF  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable * �?  nvrhi::RefCountPtr<nvrhi::ITexture>  H!  nvrhi::ComputeState 2 gF  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec 
 !   _ino_t 
 )  _Cnd_t  !   uint16_t - 7M  donut::engine::MaterialResourceBinding ! :E  donut::engine::BufferGroup  譋  donut::engine::Material * 駿  donut::engine::Material::HairParams 0 隕  donut::engine::Material::SubsurfaceParams  zE  donut::engine::MeshInfo  iE  donut::engine::MeshType * 稫  donut::engine::MaterialBindingCache & 5M  donut::engine::MaterialResource # 糆  donut::engine::LoadedTexture & �6  donut::engine::DescriptorHandle , 7  donut::engine::DescriptorTableManager B �6  donut::engine::DescriptorTableManager::BindingSetItemsEqual B �6  donut::engine::DescriptorTableManager::BindingSetItemHasher % $E  donut::engine::VertexAttribute % t   donut::engine::DescriptorIndex  Y@  donut::math::float4x4 " 2@  donut::math::vector<bool,4>    donut::math::float3  Q@  donut::math::affine3  
B  donut::math::float2 #   donut::math::vector<float,3>  u   donut::math::uint  闌  donut::math::plane # 蜙  donut::math::vector<float,4>  A  donut::math::frustum $ 鰼  donut::math::frustum::Corners # 魼  donut::math::frustum::Planes  蜙  donut::math::float4 # Q@  donut::math::affine<float,3>   袮  donut::math::box<float,3> " �?  donut::math::vector<bool,2>  袮  donut::math::box3 % |@  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # 
B  donut::math::vector<float,2> M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t  u   _Thrd_id_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  e4  _CatchableTypeArray     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24  �(  _Mtx_internal_imp_t & _4  $_TypeDescriptor$_extraBytes_25 % P4  __RTTIClassHierarchyDescriptor     __time64_t  m  FILE 
 �(  _Mtx_t 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray  �(  _Thrd_t - G4  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  
  __std_exception_data 
 u   _dev_t  K  lldiv_t  H  _ldiv_t  �  _timespec64  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers    �          +FK茂c�G1灈�7ほ��F�鳺彷餃�  2    瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  t    隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  4   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  q   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   �	玮媔=zY沚�c簐P`尚足,\�>:O     �"睱建Bi圀対隤v��cB�'窘�n  T   �'稌� 变邯D)\欅)	@'1:A:熾/�  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�     掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  V   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     V� c鯐鄥杕me綻呥EG磷扂浝W)  _   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  D    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   k�8.s��鉁�-[粽I*1O鲠-8H� U     偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  X   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   E�'鉸鑑m諼�:�鵾�G廥�汄-�8惚     �0�*е彗9釗獳+U叅[4椪 P"��  S   o]Eh堖�--�暝 }DvJ爇羯�郫�  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   煋�	y鋵@$5х葑愔*濋>�( 懪銳     譫鰿3鳪v鐇�6瘻x侃�h�3&�  P   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  	   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  R	   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �	   繃S,;fi@`騂廩k叉c.2狇x佚�  �	   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  
   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  e
   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �
   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �
   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  &   �*o驑瓂a�(施眗9歐湬

�  n    I嘛襨签.濟;剕��7啧�)煇9触�.  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  0   猯�諽!~�:gn菾�]騈购����'  l   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  
   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  G
   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �
   鹴y�	宯N卮洗袾uG6E灊搠d�  �
   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  )   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  i   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   G�膢刉^O郀�/耦��萁n!鮋W VS  3   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�     N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  B   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<     o藾錚\F鄦泭|嚎醖b&惰�_槮  F   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   +椬恡�
	#G許�/G候Mc�蜀煟-     蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  a   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  &   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  x   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹     �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  R   *u\{┞稦�3壅阱\繺ěk�6U�  �   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  .   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  n   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  K   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �   +4[(広
倬禼�溞K^洞齹誇*f�5  4    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  r   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  6   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   L�9[皫zS�6;厝�楿绷]!��t  �   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄     交�,�;+愱`�3p炛秓ee td�	^,  I   zY{���睃R焤�0聃
扨-瘜}  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �     �*M�现.凿萰閱寴诃缶鲍6�#�+�4  C   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   副謐�斦=犻媨铩0
龉�3曃譹5D   �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;     J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  [   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  -   =J�(o�'k螓4o奇缃�
黓睆=呄k_  i   _O縋[HU-銌�鼪根�鲋薺篮�j��  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  Y   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   蜅�萷l�/费�	廵崹
T,W�&連芿  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  )   D���0�郋鬔G5啚髡J竆)俻w��  {   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   匐衏�$=�"�3�a旬SY�
乢�骣�     v峞M� {�:稚�闙蛂龣 �]<��  J   悯R痱v 瓩愿碀"禰J5�>xF痧  �   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   矨�陘�2{WV�y紥*f�u龘��      �$晑�~2]�/
S蟦a� �
}A珈弿V緈  R    炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �    穫農�.伆l'h��37x,��
fO��  �    ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  !   鏀q�N�&}
;霂�#�0ncP抝  :!   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �!   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �!   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   �      �     	  T  �  B   U  �  H   V  �  Y   [  �  �   v  �  U   w  �  �   �  p     �  p  2   #  �   +   $  �   0   %  �   7   '  �   J   (  �   Q   >  (    @  (  �   A  (  �   B  (  �   C  (  �   N  �   �  O  �   �  e  (  �   h  �  q   n  �  q   �  (    �  (  �   �  (  �   �  (    �  (    �  (  �   �  �  K     (  �   y  (  �   �  �  �   �  �  @   �  �  �   �  �  @   �  (  �   �  �  �   
  �   h   H  (  �   I  (  �   &    �  �  p    �  p  L  �  p  [  �    l   �  0  �  �    �  �    j   �  0  >  �  (  �   �  �  5   �  �  5   �  (  �   �  0  �  �  0  4  �  0  '  �  0  u  �  X  �  �  X  �  �  0  �  �  0  �  �  0  �  �  �  �  �  0    �  �  �  �  0  S  �  X  X  �  X  "  �    1   �  X  %   �  0  
  �  �  �  �  X  '  �  �  �  �  (  �   �  (  �   �  �  �  �  0    �  0  �     0      X  C    X  3    �  �  	  X  <  
  �  �    �  �    �  F    (  �     0  �    0  �    0  w    0  q    0  j    0  K    X  a    �  �       ?	    �  �  !  0  �  $  0  �  %  0  �  '     Q	  *  X  j   ,  X  L   -  X  G   .  X  <   /  X  1   0  X  )   4  0  �  5  0    6  X  �  9  �  �  >  X  P  @  �  �  D  �    G  0    H  �  �   K  X  G  L  �  �   O  �  �  R  �  �  S  �  R  V  �  �  X  �  �  Y  0  �   Z     *	  `  �  �  a  �  |  b  �  �  c  0  �  e  p  �  i  p  �  k  �  a  m  �  �  r     $	  s  �  l  v  �  ;  w  �  9  y  �  �   z  �  5  �   �"   D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\src\engine\MaterialBindingCache.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\RTXPT\External\Donut\include\donut\engine\MaterialBindingCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\MaterialBindingCache.obj �       Lcl  �3  �   �3  �  
 �7  �   �7  �  
 YL      ]L     
 盠      礚     
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  ^   w  ^  
 �     �    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   '      �   �  � G            0   
   %   i        �std::_Copy_memmove<donut::engine::MaterialResourceBinding *,donut::engine::MaterialResourceBinding *>  >怢   _First  AJ          >怢   _Last  AK          >怢   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   F   0   怢  O_First  8   怢  O_Last  @   怢  O_Dest  O  �   @           0   p     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
          
 #     '    
 �     �    
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   C  _ G            c       b   r        �std::_Fnv1a_append_value<donut::engine::Material const *> 
 >   _Val  AJ          >鯧   _Keyval  AK        c  M        �  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   �        O_Val     鯧  O_Keyval  O �   0           c         $       $	 �    &	 �b   '	 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 X     \    
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   K  ` G            p       o   Z        �std::_Hash_representation<donut::engine::Material const *>  >鯧   _Keyval  AJ          AK       U  M        r   T M        �  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   �  r      鯧  O_Keyval  O �   @           p         4       *	 �    +	 �   *	 �   +	 �o   ,	 �,      0     
 �      �     
 �      �     
 �      �     
 �          
 `     d    
 H塡$UVWATAUAVAWH冹0I嬸L嬺H嬮A�H�%#"勪滘薍3菻撼     HA禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻H墝$�   L岴0I� H#罤繦婱H媆�H峌L�"I;躸
I嬡L塂$pM孅隟H�罤�H;CtH;賢*H媅H;Cu馡�A艶 I嬈H媆$xH兡0A_A^A]A\_^]肔嬨H岴0H塂$pL孄L峬H�������H9E勽  H塗$ H荄$(    �    �    H孁H塂$(H�H塇H茾    H婱�] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繦媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺勼   �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛H嬐�    H婱0L媱$�   I#菻蒆婾H婦�H媇H;胾H荄$(    �$H�蔋婳H;Ht怘嬝H;聇"H婡H;Hu頗�H塡$ L孄L峬H岴0L嬨�-H塂$ H荄$(    L孄L峬H岴0L媎$ �
H婦$pL媱$�   H婼H�EL�'H塛H�:H墈I婱 H� I#繦繪�罫;EuH�<岭M;莡H�<岭H9T�uH墊�I�>A艶橥��H�
    �    �>  �    �  &   �     .  �    !  �   &  �       �   �  �G            +     +  �        �std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<donut::engine::Material const * const &> 
 >覭   this  AJ          AN       �   >鯧   _Keyval_arg  AL       �  t� AP          AL �       >O   _Target  CH      
      CI      �       CT          � ��  CU          {	 � �W  CW      �     Q  K  CT     �     3 � CU     �     3 � >@O   _Newnode  CM     E      B    �     3 , t � � 1 M        G  ��*,'%md	& >繩    _Where  AI  �     d  
 =   AI �     @  fi 
 >L    _End  AT  �     Q 6   AT �     @  � �  >L    _Bucket_lo  AJ  �     G     AJ �     :  R � >    _Bucket  AH  �       M        Y  �� M        e  �� N N N M        �  { M          { M        '  { M        Z  { M        r  {5 M        �  >(4(4(4(4(4(4
 >#    _Val  B�   �     � AJ  .     {  AP  >    � | 
  N N N N N N M        K  �*% M        m  丣 M        w  丣 M        z  丣 M        C  丵 N N N N M        `  �/	 M        �  
�8 M        �  
�8 M        v  
�8
 Z   �   N N N N M        a  �* N N M          ��
 Z   6   N M        H  �� N M          乊D5Y >    _Newsize  AJ  f      AJ �    Z  I �  >    _Oldsize  AJ  ]    	  M        %  乚 N N8 M        G  �2/,$%k
 >繩    _Where  AH  M    f H  
 >L    _End  AI  Q      AI �     3 }� �-  >L    _Bucket_lo  AK  e    U     AK �    F  -  >    _Bucket  AJ  6      M        Y  俥 M        e  俥 N N N M          k伹
 Z   #    M        $  伹B
 >   _Req_buckets  AJ  	    $  C             M        4  6伹 N N N M        L  
� N2 M          偳$$#$#d$&CJ$"E >N    _Bucket_array  AJ  �    =  AJ �       >L    _Insert_after  AK  �    S  AK �       >    _Bucket  AH  �      N 0           8         0@ hF   �  �  v  w  �  @  C  �  �  �  �  �  W  �  �  �  �  <  �  �  �  �                      $  %  &  '  3  4  9  G  H  I  J  K  L  Q  R  U  V  W  Y  Z  [  \  `  a  d  e  k  l  m  n  r  s  v  w  y  z  {  |         $LN206  p   覭  Othis  �   鯧  O_Keyval_arg      @O  O_Newnode  O�   �           +  0     �       � �   � ��   � ��   � ��   � �  � �  � �*  � �Y  � ��  � �2  � ��  � ��  � ��  � ��  � �  � ��   �  �F                                �`std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<donut::engine::Material const * const &>'::`1'::dtor$1                         �  O  �   �  �F                                �`std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<donut::engine::Material const * const &>'::`1'::dtor$0                         �  O  ,      0     
 �     �    
 �     �    
      "    
 6     :    
 F     J    
 l     p    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
           
 +     /    
 ;     ?    
 �     �    
 �     �    
 �     �    
 �     �    
 #     '    
 7     ;    
 a     e    
 X     \    
 h     l    
 x     |    
 +     /    
 ;     ?    
 f     j    
 �     �    
          
          
 C     G    
 W     [    
 }     �    
  	     $	    
 4	     8	    
 �	     �	    
 �	     �	    
 
     
    
 
     
    
 4
     8
    
 �  Z   �  Z  
 �     �    
 �     �    
 �     �    
 H崐    �          H崐    �          H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   �  {G            �         �        �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >N   _First  AJ        0  AJ b     "  >N   _Last  AK          AR       } 
 >N   _Val  AP        �  >EM    _UFirst  AQ       u                        @  h   �  M      N  O_First     N  O_Last      N  O_Val  O   �   X           �   p     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,      0     
 �     �    
 �     �    
 �     �    
 �     �    
          
 #     '    
 �     �    
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  �G                       
        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >N   _First  AJ          AJ       
   >N   _Last  AK          
 >N   _Val  AP           >嶰   _Backout  CJ            CJ          
   M            N M        S   N                        H & h   �  
      M  S  o  p      N  O_First     N  O_Last     N  O_Val  O�   H               �     <       � �    � �   � �   � �   � �   � �,   
   0   
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 *  
   .  
  
 >  
   B  
  
   
     
  
 H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >�   this  AJ                                 H     �  Othis  O   �                  p             �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 @SUVWATAVAWH侅�  H�    H3腍墑$p  E孂A疯H嬹H塋$(L嫶$�  H�H呉t
H�H嬍�P怑3銵塮H峖H塡$ D�#L塩L塩A峀$ �    H� H堾H塁H岾L�!L塧L塧H荂0   H荂8   �  �?L婥A峊$�    怢塮PL塮XL塮`I媈I+H聋H呟剸   H�������H;�嘩  H��    H侞   r,H岾'H;��?  �    H嬋H吚�"  H兝'H冟郒塇H呟t
H嬎�    �I嬆H塅PH塅XH肏塅`H媬PI媈I�H+贚嬅H嬒�    H聋H�逪塅XH媽$�  H塏hH吷tH��P怘媽$�  H塏pH吷tH��P怘峃xD�!L塧L塧W�AA(A8L塧H�   �    �秳$   垎�   3褹�   H峀$P�    M嬏L墹$P  D墹$X  莿$\  �   莿$`     莿$d  �  f塴$@D墊$D秳$�  圖$HM�M媀M;聇uL�    @ fff�     L塪$ A婡塂$ Ic凓嚲   A媽�    I�崞D$$	�艱$$�艱$$H婦$ J塂蘌L媽$P  I�罫墝$P  I兝M;聈�H�L岲$@H峊$ �怭  I嬙H峀$0H;萾H�L� H婲H塚H吷tH��P怘婰$ H吷tL塪$ H��R怘嬈H媽$p  H3惕    H伳�  A_A^A\_^][肏�
    �    胪�    惕    惕    虗                                          n   �    �   �      �    *  �    W  '   �  �    �  (   =     m  P     %   *  �   /  
   6  �    <     B  �    H  Q   L  S   P  R   T  R   X  R   \  R   `  R   d  R   h  R      �   q  _ G            l  $   l  �        �donut::engine::MaterialBindingCache::MaterialBindingCache 
 >   this  D(    AJ        .  AL  .     >�  D�   >{#   device  AK        L  AK M     %  >�   shaderType  A   +     A�  A`        +  >u    registerSpace  Ai        '  Ao  '     E�	 % >0    registerSpaceIsDescriptorSet  EO  (           D�   >甂   bindings  EO  0           D�   >�   sampler  AJ  �      EO  8           D�   >X   fallbackTexture  AJ  o      EO  @           D�   >0    trackLiveness  EO  H           D    >=   layoutDesc  CQ    �    �  �  D@    >甂    <range>$L0  AV  ;     1�  >扡    <begin>$L0  AP  1    � m  >扡    <end>$L0  AR  5    � � b  >"    layoutItem  B    P    � �   M        �  佉4 N M        
  仜
 >8)   this  AJ  �    (  M        #  仜

 Z   �   N N M        �  � M          亱 N N M        �  乬 M        I  乻 N N# M        �  ��L��俶 >    _Count  AI  �     �  + O AI g    � � & M        �  ��)%q侒& M          ��^俀
 Z      ' M        !  ��O$侜 >餖    _Newvec  AH  .      AH 3      M        X  O��俀 M        b  O��俀) M        �  ��)
,%
�- M        �  ��$	()�	 Z   S  k   >    _Block_size  AJ  �     M : >    _Ptr_container  AJ  
    0  AJ 3    # 
 >0    _Ptr  AH        AH 3      M        v  �
 Z   �   N N M        v  �&
 Z   �   N N M        �  �� N N N N N M        D  0丣 >怢   _First  AK  M      >怢   _Last  AI  J      >怢   _Dest  AH  c      AM  F      AH g    \   ,   M        i  丣c >    _Count  AI  P      N N N M        �  �� M          �� N N N M        �  S]
 >LL   this  AI  X       B    ]     �Z � �  M        �  ]/H
 Z   �   M          �� M        O  �� M        c  �� N N N M        �  ` M        �  h*# >繩    _Newhead  AH  r     >  M        �  
h M        �  
h M        v  
h
 Z   �   N N N N M          ` M          ` N N N M        �  ] N N N M        �  M N M        y  ; M        �  >	 N N M        n  倝
 N M        e  傡 M        �  傡HB
 >�    temp  AJ  �      AJ       B    �    ,  B�  �    b  N N M        �  &偱 M        e  傕 M        �  傕
 >�    temp  AJ  �      AJ �      N N M        �  傌 >�    tmp  AK  �    #  AK �    ;    N M          偱C
 M        �  傄 N N N
 Z   ~   �          8         A ZhU   v  z  {    �  #  )  K  e  n  �  �  �  �    y  �  �  �  �  y  z  �  
  I  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �                    !  "  @  A  B  C  D  E  F  N  O  X  ]  ^  _  b  c  f  g  h  i  j  t  u  
 :p  O 
                   $LN217         $LN15         $LN8         $LN7  �    Othis  �  {#  Odevice  �  �  OshaderType  �  u   OregisterSpace ) �  0   OregisterSpaceIsDescriptorSet  �  甂  Obindings  �  �  Osampler  �  X  OfallbackTexture     0   OtrackLiveness  @   =  OlayoutDesc      "  OlayoutItem  9I       /   9{      /   9�      /   9�      ;#   9�      /   9�      /   O   �   �           l       �       *  �;   %  �M   *  ��   &  �g  '  �  (  ��  *  ��  )  ��  +  �  ,  �  -  �"  .  �.  0  �P  2  �U  3  �]  5  �v  8  �{  9  �}  A  ��  B  ��  D  ��  K  ��  0  ��  N  �  O  �'  G  �3  H  �5  &  ��   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$0 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  �   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$1 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  �   �   o F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$13 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O �   �   o F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$14 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O �   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$2 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  �   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$3 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  �   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$4 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  �   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$5 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  �   �   n F                                �`donut::engine::MaterialBindingCache::MaterialBindingCache'::`1'::dtor$6 
 >   this  EN  (           EN  �          >=    layoutDesc  EN  @                                  �  O  ,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
          
 :     >    
 J     N    
 �     �    
 �     �    
 �     �    
 �     �    
 (     ,    
 <     @    
 p     t    
 �     �    
 �     �    
 �     �    
 "     &    
 K     O    
 �     �    
 �     �    
 �     �    
 W     [    
 g     k    
 1     5    
 ^     b    
 r     v    
 �     �    
 �     �    
 Z     ^    
 z     ~    
 �     �    
 �     �    
 �     �    
 �         
 q     u    
 �     �    
 U	     Y	    
 �
     �
    
 �
     �
    
 �
     �
    
 �
     �
    
 b     f    
 r     v    
 �     �    
 �     �    
 �
  P   �
  P  
 �
  T   �
  T  
 �
  S   �
  S  
 �
  R   �
  R  
 �
  Q   �
  Q  
      !    
 -     1    
 =     A    
 M     Q    
 ]     a    
 m     q    
 �     �    
 �     �    
          
 '     +    
 P     T    
 �     �    
          
 #     '    
 L     P    
 �     �    
          
       $    
 I     M    
 �     �    
          
           
 E     I    
 �     �    
          
          
 @     D    
 �     �    
 �         
          
 <     @    
 �     �    
 �     �    
          
 8     <    
 �     �    
 �     �    
          
 4     8    
 �     �    
 �     �    
          
 0     4    
 H媻(   �       �    H媻(   H兞�       �    H媻(   H兞�           H媻(   H兞P�          H媻(   H兞h�       �    H媻(   H兞p�          H媻(   H兞x�       �    H媻    H兞�       �    H媻    H兞�       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �       �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         e        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >�   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �  Othis  9       /   O�   0           "   (     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 P  �    T  �   
 h  �    l  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         L        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >%*   this  AH         AJ          AH        M        �  GCE
 >{#    temp  AJ  
       AJ        N (                     0@� 
 h   �   0   %*  Othis  9       /   O  �   0           "   (     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ISampler>::~RefCountPtr<nvrhi::ISampler> 
 >M   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0@� 
 h   �   0   M  Othis  9       /   O�   0           "   (     $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 D     H    
 \     `    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         2        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >s?   this  AH         AJ          AH        M        H  GCE
 >X    temp  AJ  
       AJ        N (                     0@� 
 h   H   0   s?  Othis  9       /   O�   0           "   (     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 \  �    `  �   
 H婭H吷t
�    �    �   �       �     ^G                              �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >(O   this  AJ          M        9  
	 M        �  
	
 >   _Ptr  AJ         N N                        H�  h   w  �  9      (O  Othis  O   �   8              �     ,       � �    � �	   � �   � �,      0     
 �     �    
 �     �    
 0     4    
 @SH冹 H嬞H婭H吷t?H婼(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁H塁 H塁(H岾H兡 [�    �    �?   �    [   �    `   �       �   +  �G            e      e   �        �std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::~_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 
 >覭   this  AI  	     \ Q   AJ        	  M        �  H	V" M        �  )I1& M        �  *F M        �  )!
 Z   �  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        w  
&#
$
 Z   S   >    _Ptr_container  AP  *     :  !  AP >       >    _Back_shift  AJ  
     W 1 !  AJ >         N N N M        �   N N N                       @� & h   w  �  �  �  �  �  �           $LN33  0   覭  Othis  O ,   �    0   �   
 
  �      �   
 !  �    %  �   
 �  �    �  �   
 �  �    �  �   
 B  �    F  �   
 V  �    Z  �   
 |  �    �  �   
 �  �    �  �   
   >     >  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �   �  �G            [      [   �        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 
 >JM   this  AI  	     R K   AJ        	 " M        �  )H1%
 M        �  *= M        �  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        w  
%#

 Z   S   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N M        �   N N                       H� " h   w  �  �  �  �  �           $LN30  0   JM  Othis  O   �   8           [   0     ,       > �	   ? �O   D �U   ? �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 n  �    r  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 *  �    .  �   
 >  �    B  �   
 �  <   �  <  
 �  �    �  �   
 @SH冹 H嬞H婭H吷t2H婹H呉tH茿    H�H嬍�PH婯H吷t�    H兡 [�    H兡 [聾   �       �   7  bG            J      D           �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >#O   this  AI  	     @ 6   AJ        	  M          0
 M        9  5
 M        �  5

 >   _Ptr  AJ  
       AJ 0       N N N M        R   M        @   M        �  DE

 >b     temp  AK         AK 0      
 
  >"     ref  A  0       N N N                      0H� . h
   w  @  �  �    9  Q  R  [  \   0   #O  Othis  9)       /   O �   8           J   X     ,       L �	   M �   N �0   P �,      0     
 �     �    
 �     �    
      
    
          
 �     �    
 �     �    
 �     �    
 3     7    
 L     P    
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯H吷tH塳H��P惡    H嬎�    H嬤H�u院    H�H媆$0H媗$8H媡$@H兡 _�    P   �    y   �       �   �  �G            }      d   �        �std::list<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~list<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 >@M   this  AJ          AL       \   M        �   M          
\ M          \ M        �  \ N N N' M          I*
 >繩   _Head  AI         >繩    _Pnode  AI  &     C  >繩    _Pnext  AM  3     )  AM 0     H  )  M        	  3
 M          

G M          
G M        �  
G
 Z   �   N N N M        R  3 M        @  3 M        �  3DE
 >b     temp  AJ  7       AJ G       N N N N N N                      0@� F h   w  x  @  �  �  �  �      	    P  Q  R  [  \   0   @M  Othis  9C       /   O  �   8           }   X     ,        �    �d    �x    �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 H�	�       �       �   �   Z G                      N        �std::lock_guard<std::mutex>::~lock_guard<std::mutex> 
 >�)   this  AJ          M        '    N                        H�  h   '  )      �)  Othis  O   �   (              �             � �    � �,   �    0   �   
    �    �   �   
 �   �    �   �   
 �       �       �   �  bG                       �        �std::unordered_map<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *>,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~unordered_map<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *>,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 >LL   this  AJ                                 @�     LL  Othis  O,       0      
 �      �     
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �     � G            [      [   �        �std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> >::~vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> > 
 >濴   this  AI  	     R K   AJ        	 $ M        �  	h1%	
 M        �  *= M        �  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        w  
%#

 Z   S   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N N                       @� " h   w  x  �  �  �  �            $LN28  0   濴  Othis  O  �   8           [   0     ,       � �	   � �O    �U   � �,      0     
      #    
 3     7    
 �     �    
 �     �    
 >     B    
 R     V    
 x     |    
 �     �    
 �  @   �  @  
           
 H�    H�H兞�       �      �       �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       �      �       �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 �       �       �   �   8 G                       �        �std::mutex::~mutex 
 >8)   this  AJ          M        $    N                        @�  h   $  )      8)  Othis  O ,   �    0   �   
 ]   �    a   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   $        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >A   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   A  O__f  9(       A   O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H塡$WH冹 H孂H兞x�    吚u<媷�   =���tH峅�    H峅xH媆$0H兡 _�    �裙   墖�   �    坦   �    �   �    ,   �    ?   �    Q   �    \   �       �   �  P G            a   
   a   �        �donut::engine::MaterialBindingCache::Clear 
 >   this  AJ        
  AM  
     T 1   M        N  0 M        '  0 N N M        O  
6
) M        %  -


)	
 Z   �  �  �   M        (  
) N N N
 Z   �                         @  h   %  '  (  )  N  O         $LN22  0     Othis  O  �   @           a        4       e  �
   f  �'   h  �0   i  �C   f  �,   	   0   	  
 u   	   y   	  
 �   	   �   	  
 �  X   �  X  
 �  	   �  	  
 H塡$ UVWATAUAVAWH崿$p��笎  �    H+郒�    H3腍墔�  M嬸H塗$@L嬧H孂3襀峀$pA�   3鲨    H媉PL媜X秶�   垍x  H壍p  I;�処      L�    D  Hc凓噊  A媽�    I��    I嫸�   D媨D$@H咑tH�H嬑�P     �x; �
u�	H塼$ L�    3鯠墊$(圖$,f荄$-  艱$/ 閷   婥塂$HH婫pH塂$@荄$L   D$@H塼$PH塼$XL$P雋I婩P�.I婩`�(I婩p�"I媶�   �I媶�   �I媶�   �I媶�   婯H吚tH� H吚uH婫h塋$(荄$,   H塂$ T$@L$@D$ H媴p  H兠H拎DpL�H�卲  I;�吺��H�L岲$pL婳I嬙H��恅  I嬆H媿�  H3惕    H嫓$�  H伳�  A_A^A]A\_^]肏�
    �    I�4$肓                                        $   (      T   (   �   �   �      �   E   �   �   �   �   �      �  %     �     
      F   $  G   (  H   ,  I   0  J   4  K   8  L   <  M   @  N      �   4  c G            D  6   D  �        �donut::engine::MaterialBindingCache::CreateMaterialBindingSet 
 >   this  AJ        D  AM  D     ��  >轊   material  AP        9  AV  9     ��  >;    bindingSetDesc  Dp    >怢    <begin>$L0  AI  \     ��  >怢    <end>$L0  AU  `     ��  >�   setItem  C�       $    �  h  C�      �       C�      3    �  c " C�      �     �  W �  
) N- " C�     �     �  W �  3 N-  E6u@   �     �~ '  M        �  
D N M        �  乥
 >u    slot  A   l    '  A  �     � � 9B  M        &  乥 N N M        �  乊 M        &  乊 N N M        �  丳 M        &  丳 N N M        �  丟 M        &  丟 N N M        �  丄 M        &  丄 N N M        �  �; M        &  �; N N M        �  �5 M        &  �5 N N M        �  �
 N# M        �  ��
^%d N M        h  ;乮? N M        B  � N
 Z   ~  # S�  $"   nvrhi::AllSubresources  A�   �     �J  BE  A�  �      �          8          A ^ h   �  B  K  h  �  1  &  )  -  �  �  �  �  �  �  �  �  �  �  �  �  �  
 :�  O 
                    $LN15         $LN14         $LN13         $LN12         $LN11         $LN10         $LN9         $LN8         $LN7  �    Othis  �  轊  Omaterial  p   ;  ObindingSetDesc  9�       �   9�      A#   O�   0          D    #   $      q  �D   r  �Q   q  �S   r  �X   u  �m   r  �t   u  ��   y  ��   |  ��     ��   |  �    �  �  �3  �  �5  �  �9  �  �;  �  �?  �  �A  �  �E  �  �G  �  �N  �  �P  �  �W  �  �Y  �  �`  �  �b  �  �i  �  ��  u  ��  �  ��  u  ��  �  ��  �  �  �  �  �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 @     D    
 j     n    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 P     T    
 `     d    
 �     �    
 �     �    
 8  E   <  E  
 C  N   G  N  
 T  M   X  M  
 e  L   i  L  
 v  K   z  K  
 �  J   �  J  
 �  I   �  I  
 �  H   �  H  
 �  G   �  G  
 �  F   �  F  
       $    
 0     4    
 H     L    
 H婣�   �   �   T G                      �        �donut::engine::MaterialBindingCache::GetLayout 
 >獽   this  AJ                                 @ 
 h   �      獽  Othis  O  �   0                   $       R  �    S  �   T  �,      0     
 y      }     
 �      �     
 H塡$ H塗$UVWH冹@H嬮H峐xH塡$pH嬎�    吚叏   婥L=���剨   H峂L岲$hH峊$(�    H�8H媤H咑uSL婦$hH峊$`H嬐�    H嬛H峀$ H;萾H�H�0H婳H塛H吷tH��P怘婰$`H吷tH塼$`H��P怘媤H嬎�    H嬈H媆$xH兡@_^]�葔CL�   �    坦   �    �!   �    J      h      �   �    �   �    �   �       �   n  ` G            �      �   �        �donut::engine::MaterialBindingCache::GetMaterialBindingSet 
 >   this  AJ          AN       � �   >轊   material  AK        %  Dh    >�)    lockGuard  AI         Bp        �  >+    bindingSet  AM  Q     q  M        �  ?
 Z   �   N M        O  ��
$ M        %  
��

 Z   �  �  �   M        (  -�� N N N M        N  �� M        '  ��
 Z   W   N N M        @  �� M        �  ��HB
 >b     temp  AJ  �       AJ �       B`   �     #  B�   l     Y  N N M        >  &l M        @  �� M        �  ��
 >b     temp  AJ  �       AJ �       N N M        �   >b     tmp  AK  o     #  AK �     "    N M        A  lC
 M        �  y N N N
 Z   �   @                    @ : h
   %  '  (  )  =  >  @  A  N  O  �  �  �         $LN51  `     Othis  h   轊  Omaterial  p   �)  OlockGuard  9�       /   9�       /   O  �   X           �        L       W  �   X  �;   Z  �Q   \  �Z   _  ��   a  ��   b  ��   X  ��   �   o F                                �`donut::engine::MaterialBindingCache::GetMaterialBindingSet'::`1'::dtor$0  >轊   material  EN  h                                  �  O  ,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
      !    
 R     V    
 b     f    
 r     v    
 �     �    
 �     �    
 �         
 =     A    
 M     Q    
   V     V  
 Z     ^    
 j     n    
 �     �    
          
 t     x    
 H崐p   �       �    I�H吚tH� H吚uH婣h    H�H嬄D塀B荁   �   �      �   �  c G            1       0   �        �donut::engine::MaterialBindingCache::GetTextureBindingSetItem 
 >獽   this  AJ        1 
 >u    slot  Ah        1  >汦   texture  AQ        1  M        �  


 N M        &    N                        H " h   �  1  &  )  -  �  �      獽  Othis     u   Oslot      汦  Otexture  O  �   0           1        $       l  �    m  �0   n  �,      0     
 �      �     
 �      �     
 �      �     
 �     �    
 H塡$ L塂$AVH冹 L嬹I嬝H婭H嬃M�I+罤柳H;�冣   H�������H塼$0H墊$8H;�囜   H�4�    H侢   r)H峃'H;�喢   �    H吚劵   H峹'H冪郒塆H咑t
H嬑�    H孁�3�I�I婩H+罤柳H吚t.H��    H侜   rL婣鳫兟'I+菻岮鳫凐w`I嬋�    H�>I�>I塅I塅H;鴗H�H兦H;鴘鬑媡$0H媩$8H媆$HH兡 A^肏嬔L岲$@I嬌�    H媆$HH兡 A^描    惕    蘬   �    �   �    �   �         /  �    5  �       �   �  � G            :     :  �        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::_Assign_grow 
 >JM   this  AJ          AV       '�    >   _Cells  AK        3p  �  � w   AK �     w  & 
 >=N   _Val  AI       $�    AP          D@    >    _Oldsize  AH  '     �  �  >N    _Newend  AH  �     2  >    _Oldcapacity  AH  �     ,    AH �     	  >N    _Newvec  AM  �       AM �     � \  k .  M        �   N M        �  �� N M        �  
0W��% M        �  U)
)%
��' M        �  ^$	%)
��
 Z   k   >    _Block_size  AJ  b       AJ .      >    _Ptr_container  AH  p       AH �     �  � 
 >0    _Ptr  AM  �       AM �     � \  k .  M        v  k
 Z   �   N N M        v  ��
 Z   �   N N M        �  

0
	 N N M        
  ��#" >嶰   _Backout  CM     �       CM    �         M          �� N M        S  �� N N M        �  .���� M        �  ��)]
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     &  AK 4     # M        w  
��#
`
 Z   S   >    _Ptr_container  AP  �       AP �     b  X  >    _Back_shift  AJ  �     9  AJ �     b ; "  N N N
 Z   �                         @ Z h   v  w  �  �  �  �  �  �  �  �  �  �    
  
      M  S  o  p         $LN82  0   JM  Othis  8     O_Cells  @   =N  O_Val  O�   �           :  0     �        �    �'   ! �0   " ��   $ ��   % ��   ' ��   + ��   , ��   - ��   . �  2 �  0 �"  2 �.  " �4  ' �,   �    0   �   
   �    "  �   
 .  �    2  �   
 W  �    [  �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 *  �    .  �   
 >  �    B  �   
 `  �    d  �   
 p  �    t  �   
 I  �    M  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 A  �    E  �   
 b  �    f  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
   �      �   
 �  :   �  :  
   �      �   
 H塼$ WH冹 H�������H嬹H饺�   嬊H余H;�嚤  H岯�H塡$0H媈H荋饺L嬅L塼$@�罤隅H峃H�?�    H岹�H墌8H塅0H婩H� H嬋H;��)  H塴$8H砍     H�%#"勪滘�@ �     禤D禭H�	L3軱L3�禤LL3�禤LL3�禤LL3�禤LL3�禤LL3�禤LL3贚L#^0I零L^M�L;藆	I�I塁雤I婼L婡L;Bu/L�L;衪!L婬I�	L婣M�I婻H�M塀L塈H塒I塁�=L;蕋D  H婻L;BtOL;蕌馤婸I�
L婭I�L婤I� L塉L塓L堾I�H嬃H;�����H媗$8H媆$0L媡$@H媡$HH兡 _肔�L婬I�	L婣M�I婻H�M塀L塈H塒氪H�
    �    蘕   �    �  �   �  �       �   M
  �G            �  
   �  #        �std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Forced_rehash 
 >覭   this  AJ          AL       ��  >#   	 _Buckets  AK        �O � AM  O     ;  AM �      C             /  C      �    
  >    _Max_storage_buckets  AH  %     �
 �
 >=N    _End  AI  ;     �n  >=N    _Inserted  AH  �      AH �     ?�   >=N    _Next_inserted  AJ  r     m >6N    _Bucket_lo  AS      � �   AS �     	 �  >    _Bucket  AS         M        �  "


 N M        5  h M        >  h M        �  l N N N M        �  7 M        �  7 M        �  7 N N N M        �  .
 M        �  ;  >#    _Value  AH  2     *  N N M        ,  r�$ M        -  r�$ N N M        �  	��T M        �  	��P M          	��P M        '  	��P M        Z  	��P M        r  	��P M        �  	��M
 >#    _Val  AS  �     Q  N N N N N N N M        *  �� M        /  �� N N M        -  � N M        Y  � M        e  � N N M        /  �' N& M        6  �/$#$#$c$ >L    _Before_prev  AK  A      AK �      �  >L    _Last_prev  AP  :      AP �     � r  >L    _First_prev  AQ  3    #  AQ �     k �  N M        -  乂 N& M        6  乷$#$#$c$ >L    _Before_prev  AP  �      AP �     � r  >L    _Last_prev  AQ  z      AQ �     k �  >L    _First_prev  AR  s       AR �     � , �    N M        Y  乨 M        e  乨 N N M        .  乣 N& M        6  伡$#$#$c$ >L   _First  AR  �    #  AR �     � , �    >L    _Before_prev  AK  �      AK �      �  >L    _Last_prev  AP  �      AP �     � r  >L    _First_prev  AQ  �      AQ �     k �  N Z   �  6                         @ � h    �  {  �  �  �  �  �  �  �  �        '  (  )  *  +  ,  -  .  /  0  2  5  6  >  Y  Z  d  e  r         $LN138  0   覭  Othis  8   #   O_Buckets  O   �   X          �  0  (   L      � �
   � �   � �   � �   � �%   � �.   � �7   � �;   � �B   � �J   � �O   � �\   � �`   � �h   � �o   � ��   � ��   � ��   � �   � �  � �  � �  � �  � �  � �  � �'  � �-  � �/  � �P  � �T  � �V  � �`  � �j  � �o  � ��  � ��  � ��   ��  � ��  � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 ,  �    0  �   
 @  �    D  �   
 o  �    s  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �       �   
 0  �    4  �   
 V  �    Z  �   
 Y  �    ]  �   
 m  �    q  �   
 t  �    x  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 k  �    o  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 		  �    
	  �   
 	  �    	  �   
 C	  �    G	  �   
 S	  �    W	  �   
 
  8   !
  8  
 d
  �    h
  �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       �             �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �   0   �   0  
 �   �    �   �   
 H塡$UVWATAUAVAWH冹0M嬸L孃H嬹I;��=  L媔H婹H塗$(M媑L墹$�   I嬤A禣H�%#"勪滘薍3菼赋     IA禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼H婩0H#罤拎H翲塂$xH�H墝$�   H婡H塂$  H嬰H孄H�H婳H吷tH荊    H��P惡    H嬒�    H�NH;l$ t I;辵翷9�$�   �/  H婰$xH��"  H婰$xL9�$�   uL�)I嬇�I嬆H堿I;�匊   禟H�%#"勪滘薍3菻撼     H禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻L媐0L#酙龄Ld$(M媩$@ fff�     H嬰H孄H�H婳H吷tH荊    H��P惡    H嬒�    H�NI;飔I;辵腎�$H媱$�   H�H塁�"M�,$M塴$I;��
���L嫟$�   I�$L塩I嬈H媆$pH兡0A_A^A]A\_^]�
  �      �       �   �	  �G            {     f  �        �std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Unchecked_erase 
 >覭   this  AJ          AL       \ >繩   _First  AK          AW       F AW `    � z  >L   _Last  AP          AV       \ >N    _Bucket_bounds  AK  .     �  AK �     &     B(   3     H >侼   _Eraser  CI     B     ! CI    c     
 >L    _End  AU  *     9 AU c      >嘚    _Bucket_lo  AH  �       AJ  1    /    AJ `     �  Bx   �     � >    _Bucket  AH  �       >嘚    _Bucket_lo  AT  �      AT `    p �  >    _Bucket  AT  �      M        �  3 N M        �  {B M        �  tB M          tB M        '  tB M        Z  tB M        r  tB5 M        �  B>(4(4(4(4(4(4
 >#    _Val  AJ  T     x  N N N N N N N! M        �  ��#
 >L    _Oldnext  AM  �     z  AM �     � z e  M        	  ��
 M          

� M          
� M        �  
�
 Z   �   N N N M        R  �� M        @  �� M        �  ��DE
 >b     temp  AJ  �       AJ       N N N N N M        �  s乣 M        �  l乣 M          l乣 M        '  l乣 M        Z  l乣 M        r  l乣6 M        �  乣>'4'4'4'4'4'4
 >#    _Val  AJ  q    �  N N N N N N N! M        �  侒#
 >L    _Oldnext  AM  �    e  AM `    � e  M        	  侚
 M          

� M          
� M        �  
�
 Z   �   N N N M        R  侚 M        @  侚 M        �  侚DE
 >b     temp  AJ  �      AJ       N N N N N M        �  �0 N 0           8         0@� j h   �  w  @  �  �  �  �  �  �  �  �      	        '  P  Q  R  Z  [  \  r   p   覭  Othis  x   繩  O_First  �   L  O_Last  9�       /   9
      /   O  �   0          {  0  #   $      � �     �&    �*    �3    �B   
 ��    ��    ��    ��    ��    �   �   �   �,   �4   �9  ! �H  # �K  $ �N  % �P  & �S  + �`  , ��  . ��  0 ��  2 ��  3 �"  4 �'  8 �,  : �0  ; �A  @ �E  A �J  + �[  E �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
   �      �   
 4  �    8  �   
 D  �    H  �   
 m  �    q  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    #  �   
 /  �    3  �   
 C  �    G  �   
 W  �    [  �   
 y  �    }  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 !  �    %  �   
 1  �    5  �   
   �      �   
 '  �    +  �   
   �      �   
 x  �    |  �   
 �  �    �  �   
 j  �    n  �   
 z  �    ~  �   
 ~	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 H冹(H�
    �    �   �      �       �   �   � G                              坰td::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> >::_Xlength 
 Z   6   (                      @        $LN3  O �   (              0            a �   b �,      0     
 �   B   �   B  
 �      �     
 H塡$H塴$H塼$ WH冹 H孂H婭H吷剹   H婫8H凌H;羦H媁L嬄H�H嬒�    雟H婳H婣3鞨�(H�H呟t2fD  H�3H婯H吷tH塳H��P惡    H嬎�    H嬣H咑u訦婫H� H婫H堾H塷H婫H塂$0L岲$0H媁 H婳�    怘媆$8H媗$@H媡$HH兡 _�?   �    �   �    �         �   �  �G            �      �   �        �std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::clear 
 >覭   this  AJ          AM       �  >    _Oldsize  AJ       . #   AJ �       >L    _Head  AK  5       M        �  	�� M        �  	�� M        �  	�� N N N# M        �  E'' M          II+
 >繩    _Pnode  AI  U     f  AI �       >繩    _Pnext  AL  c     )  AL `     j  )  M        	  c
 M          

w M          
w M        �  
w
 Z   �   N N N M        R  c M        @  c M        �  cDE
 >b     temp  AJ  g       AJ w       N N N N N N Z   �  �                        0@� ^ h   w  {  @  �  �  �  �  �  �  �  �  �  �      	    P  Q  R  [  \   0   覭  Othis  9s       /   O �   h           �   0  
   \       { �   � �   � �$   � �1   � �5   � �C   � �E   � ��   � ��   � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 H婹H�    H呉HE旅   �      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H           )      )      c    20    2           *      *      i   
 
4 
2p    B           +      +      o    20    <           ,      ,      u   
 
4 
2p    B           -      -      {    20    <           .      .      �   
 
4 
2p    B           /      /      �    �                  1      1      �    B             !      �       "           2      2      �   h           �      �          �    2 B             !      �       "           3      3      �   h           �      �          �    2 B             !      �       "           4      4      �   h           �      �          �    2 d T 4 2p           !      �       }           5      5      �   h           �      �          �    �
 4 R���
�p`P           !      �       {          6      6      �   h           �      �          �    �Q  d	 T 4 2p           !      �       �           7      7      �   h           �      �          �    �
 
d	 
2p    2           9      9      �   ! � 4     2          9      9      �   2   {           9      9      �   ! T 2   {          9      9      �   {   �          9      9      �   !   2   {          9      9      �   �  �          9      9      �   !   �  T  4     2          9      9      �   �  �          9      9         !       2          9      9      �   �  �          9      9          4	 2�    :           ;      ;         !
 
t d     :          ;      ;         :             ;      ;         !       :          ;      ;           .          ;      ;         !   t  d     :          ;      ;         .  :          ;      ;      #    20    [           =      =      )    20    e           ?      ?      /    20    [           A      A      5    B                 C      C      ;    B             !      G       "           D      D      A   h           J      M          �    26 %4%���
�p`P      �     "       D          O      O      P   $	 � �	��p`P0          r     #      \       l          U      U      V   (           _      b       4    P6    .    .    ~    .    .    V    .    V    .    .       �       �    
                        !      &   �    +      0   �    5      :      ?   �    �H|
��(0�R4 J& 4 r
p`P           !      k       �           W      W      e   (           n      q   
    �6       �       �    �|DL 
 
4 
2p    a           Y      Y      t   
 4 R���
�p`P           !      �       +          [      [      z   (           �      �   
    @:    @h            ���        >           \      \      �   ! t      >          \      \      �   >   b           \      \      �   !       >          \      \      �   b   �           \      \      �    20           !      �       J           ]      ]      �   h           �      �          �    R B      :           _      _      �   
 
4 
2p    0           `      `      �                               �      �       �    Unknown exception                             �      �       �                                      �       �    bad array new length                                �       �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                    .?AVbad_alloc@std@@     �              ����                      �      �                    .?AVexception@std@@     �               ����                      �      �        ����    ����        ��������MaterialBindingCache: unknown MaterialResource value (%d) unordered_map/set too long vector too long invalid hash bucket count                                       �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                                                           �              ����    @                   �      �                                         �      	                                                                              �              ����    @                   �      	      _   �   (   & 
34        std::exception::`vftable'    �      �  
    �   (   & 
34        std::bad_alloc::`vftable'    �      �  
    �   3   1 
34        std::bad_array_new_length::`vftable'     �      �  
 噾姏@|#�'_
肜8cI橗cS叜月o"0h�K蜌�(驇営FA"R�禓�/囥jA勽洶N4_+儢^遂哯峲�g�,D`浱t�&y�,jlp鷥x豦訧)#hv瓯訧)#hv瓯屢b綩藋T埭D禘e�(！
Z曕銺霎奜吽翇^=f瓵2�:�爁彮9檉b謎H鶪J綿舷h^b� 懢譶玬S2蔦j�#哥�r退0�+汍R観遞.朘R�'k慞致鳭L蹐沀泱O琏赼e嚤踖p禭c�&敡?諭嚤踖p禭9"虰錱6萪O�眒TF7郃.K霵婬(ζa�磇@�'項jL��1!�'項jLΚJ齘埠�'項j峩(A�6�'項j冸c鵽垳d裞儌頂"*	�'O斩鋲+^{3�橅	講D櫇\夂嫑�茬�$�
蟸.`�獉�4漚>=`臉
孷N僠n葟7%�&坾宇:e鱻桦�'洋m|GLr咥瞔漒夂嫑�fG
'�3嶀預棊膬�屓绀?貚犷A棊膬eDf冱R绣桦�'洋m|脠�糈箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>~;A嬰t舘� �蹰k#@(貝邒aF秺濠9Avj�
=�&�e彺�;V氣麱陫婉f云乀C嘼�:[+蒩伋钫鯉觧��+倧A糲��oW�,�苙A�>]溊欤狔o� o谩蘑斉UO,鍹觍诐L_O�%I栶賑?T觙k埦VeC<:斫FyV�5瘐	cjt焒]{謑p貰~B�P嗖
骘Y伉�K乮V俷N鵘J釯A
w彔�4F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊瓕汥0�#*O魕囱Q� 粎鹴aR�,F_棢杻#Q邽dk+p琢犇Li籩项扭咞taR�,F_棢杻#Q蹚�:%UFw壱3,�4q胭o�!骬6�/s.�.:6俙貱霯蝭﹛_韃�)裯 j|�8厢M\筹s,;窇藿啓��=緱�G�6'j职DgCJ8剩杫X]绪/ｎ	蜍R僁舴�:kn嶜�:5齵h周洊{= 栰宧霅;�d_f湲z竘雵J-WV8om�M%>mb雵J-WV8o.*~襠[
B雵J-WV8om�M%>mb-坓�(鬄�汬'这柫�5YJq覜垒�咞taR�,F_棢杻#Q`�G堖@滖b2q�,O秳I仹�甋;瀣�.Z匦熱dd�a�:/闎A勎躃�诀|C�脫b*�5嬇P�dd�a�:门)哂幦餏韵�9E\$L釉拚�╡怌]箅我F詍CD� K^�dd�a�:r�7)}玖焋tjxⅲ<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦巧S�8萀D脸v傘]-屾咞taR�,F_棢杻#Q吀qv蕞	�-坓�(鬄�/ｎ	蜍R9E\$L釉��E光潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H�8晢訂L阱叿} 5�%G>禡h槷鋆叨蝝�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 笖E@wX+]!阑,�粰趭+�揃T爃.L�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       鳹              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn       0      燥"V     .debug$S       �             .text$mn    	   c       謠锖     .debug$S    
   �         	    .text$mn       p       '      .debug$S       �             .text$mn    
   +     L啎     .debug$S       p  Z       
    .text$x              S�
    .text$x              S�
    .text$mn       �       `螏�     .debug$S       �             .text$mn               _葓�     .debug$S       T             .text$mn              恶Lc     .debug$S       �              .text$mn       l     ]忯�     .debug$S       \  �           .text$x              oe�    .text$x              �$n    .text$x              =偂�    .text$x              Q堸�    .text$x              �
衒    .text$x              g峌�    .text$x              
e    .text$x               ρ'4    .text$x     !         =玉7    .text$mn    "   <      .ズ     .debug$S    #   0  
       "    .text$mn    $   <      .ズ     .debug$S    %   L  
       $    .text$mn    &   !      :著�     .debug$S    '   <         &    .text$mn    (   2      X于     .debug$S    )   <         (    .text$mn    *   "       坼	     .debug$S    +   �         *    .text$mn    ,   "       坼	     .debug$S    -   �         ,    .text$mn    .   "       坼	     .debug$S    /   �         .    .text$mn    0   "       坼	     .debug$S    1   �         0    .text$mn    2         �!     .debug$S    3   h         2    .text$mn    4   e      D远     .debug$S    5   8         4    .text$mn    6   [       荘�     .debug$S    7             6    .text$mn    8   J      O�0�     .debug$S    9   �         8    .text$mn    :   }      1�-�     .debug$S    ;   �         :    .text$mn    <         6摙r     .debug$S    =            <    .text$mn    >         �%     .debug$S    ?   �         >    .text$mn    @   [       荘�     .debug$S    A   T         @    .text$mn    B         ��#     .debug$S    C   �          B    .text$mn    D         ��#     .debug$S    E   �          D    .text$mn    F         �%     .debug$S    G   �          F    .text$mn    H   B      贘S     .debug$S    I             H    .text$mn    J   B      贘S     .debug$S    K            J    .text$mn    L   B      贘S     .debug$S    M   �          L    .text$mn    N   H       襶.      .debug$S    O   �         N    .text$mn    P   a      述pB     .debug$S    Q     
       P    .text$mn    R   D     �+
F     .debug$S    S   x  <       R    .text$mn    T          G�7�     .debug$S    U   �          T    .text$mn    V   �      ^m     .debug$S    W   �  *       V    .text$x     X         %FZ甐    .text$mn    Y   1      凼Q     .debug$S    Z   �  
       Y    .text$mn    [   :     愽鉻     .debug$S    \   �  <       [    .text$mn    ]   �     濷I     .debug$S    ^   �  P       ]    .text$mn    _          aJ鄔     .debug$S    `   �          _    .text$mn    a   {     -絽     .debug$S    b   �
  H       a    .text$mn    c         �ッ     .debug$S    d            c    .text$mn    e   �      X瀏x     .debug$S    f   H         e    .text$mn    g         崪覩     .debug$S    h   �          g        \       N        x                �                �                �                �                �                �       (              D        2      g        R      L        q          i�                    �      "        �      H        �          i�                    �      &              B        9      $        e      J        �          i�                    �      _        �                                              '               1               =               [              |      *        �      ,        �      0              F        -      <        W      :        '      a              e        y      ]        �	      [        o      6        D      4        �
      >        �      @        @      c        �      .        �      R        s      Y                      �      T        :      V        �      P        �               �      
                      �                    2        �      8        U              �                            �      	        �              %                    X        �               �      !        }               �"              �#              �$              �%              �&              �'              w(               �(               �(               �(           __chkstk             �(           ceilf            memmove          memset           $LN13       N    $LN5        (    $LN10       L    $LN7        "    $LN13       H    $LN10       $    $LN16       J    $LN3        _    $LN4        _    $LN10       *    $LN10       ,    $LN10       0    $LN77       :    $LN186      a    $LN67       e    $LN138  �  ]    $LN142      ]    $LN82   :  [    $LN85       [    $LN30   [   6    $LN33       6    $LN33   e   4    $LN36       4    $LN28   [   @    $LN31       @    $LN3       c    $LN4        c    $LN10       .    $LN105     R    $LN7    �   R    $LN8      R    $LN9    5  R    $LN10   ;  R    $LN11   A  R    $LN12   G  R    $LN13   P  R    $LN14   Y  R    $LN15   b  R    $LN125      R    $LN216  H      $LN7    v      $LN8    }      $LN15   �      $LN217  G      $LN222          $LN51   �   V    $LN56       V    $LN22   a   P    $LN27       P    $LN206  +  
    $LN215      
    $LN20           $LN43       8    $LN14   :       $LN17           $LN4            .xdata      i          F┑@N        �(      i    .pdata      j         X賦鶱        �(      j    .xdata      k          （亵(        ")      k    .pdata      l          T枨(        K)      l    .xdata      m          %蚘%L        s)      m    .pdata      n         惻竗L        �)      n    .xdata      o          （亵"        �)      o    .pdata      p         2Fb�"        �)      p    .xdata      q          %蚘%H        *      q    .pdata      r         惻竗H        8*      r    .xdata      s          （亵$        ^*      s    .pdata      t         2Fb�$        �*      t    .xdata      u          %蚘%J        �*      u    .pdata      v         惻竗J        �*      v    .xdata      w          懐j瀇        (+      w    .pdata      x         Vbv鵢        X+      x    .xdata      y         /
�*        �+      y    .pdata      z         +eS�*        �+      z    .xdata      {   	      �#荤*        ,      {    .xdata      |         j*        H,      |    .xdata      }          3狷 *        �,      }    .xdata      ~         /
�,        �,      ~    .pdata               +eS�,        -          .xdata      �   	      �#荤,        C-      �    .xdata      �         j,        ~-      �    .xdata      �          3狷 ,        �-      �    .xdata      �         /
�0        �-      �    .pdata      �         +eS�0        4.      �    .xdata      �   	      �#荤0        m.      �    .xdata      �         j0        �.      �    .xdata      �          3狷 0        �.      �    .xdata      �         vQ9	:        '/      �    .pdata      �         A刄7:        �/      �    .xdata      �   	      �#荤:        �0      �    .xdata      �         j:        �1      �    .xdata      �          強S�:        �2      �    .xdata      �          /巴刟        j3      �    .pdata      �         餃a        ]5      �    .xdata      �   	      �#荤a        O7      �    .xdata      �         ja        D9      �    .xdata      �          a煫Ua        ?;      �    .xdata      �         ╬aue        4=      �    .pdata      �         ve        �>      �    .xdata      �   	      �#荤e        @      �    .xdata      �         je        侫      �    .xdata      �          (岙錯        鵅      �    .xdata      �          G栚鱙        jD      �    .pdata      �          T枨]        銭      �    .xdata      �         0W圫]        ]G      �    .pdata      �         ]%(	]        豀      �    .xdata      �         甞淰]        SJ      �    .pdata      �         8迁輂        蜬      �    .xdata      �         毕癩        IM      �    .pdata      �         煐罢]        腘      �    .xdata      �         �(崚]        ?P      �    .pdata      �         庇僎]        篞      �    .xdata      �         炀縹]        5S      �    .pdata      �         荖疂]        癟      �    .xdata      �          ii@[        +V      �    .pdata      �         礝
[        稺      �    .xdata      �         塯4穂        BY      �    .pdata      �         囥鱢[        蟌      �    .xdata      �         Y璠        \\      �    .pdata      �         s�&k[        閉      �    .xdata      �         n奧w[        v_      �    .pdata      �         '擊俒        a      �    .xdata      �          （亵6        恇      �    .pdata      �         愶L6        mc      �    .xdata      �          （亵4        Id      �    .pdata      �         弋�4        磂      �    .xdata      �          （亵@        g      �    .pdata      �         愶L@              �    .xdata      �          �9�c        #h      �    .pdata      �         �1癱        玥      �    .xdata      �         /
�.        2i      �    .pdata      �         +eS�.        li      �    .xdata      �   	      �#荤.              �    .xdata      �         j.        醝      �    .xdata      �          3狷 .        #j      �    .xdata      �   $      o^        _j      �    .pdata      �         >軭:R        鑚      �    .xdata      �   $      廧�3        pk      �    .pdata      �         i�        ^l      �    .xdata      �   	      � )9        Km      �    .xdata      �   C   
   電h�        ;n      �    .xdata      �          串�        1o      �    .xdata      �          �9峍        !p      �    .pdata      �         a%袃V        憄      �    .xdata      �   	      � )9V         q      �    .xdata      �         _5屬V        rq      �    .xdata      �   	       蕄純V        阸      �    .xdata      �          %蚘%P        \r      �    .pdata      �         %燗P        杛      �    .xdata      �          �"膧
        蟫      �    .pdata      �         漝魰
        u      �    .xdata      �   	      � )9
        8w      �    .xdata      �         �T
        oy      �    .xdata      �   
       � 裇
        瑊      �    .xdata      �          確        銄      �    .pdata      �         OAG�        X      �    .xdata      �         +縬[        虁      �    .pdata      �         蹷謔        B�      �    .xdata      �         ＋)        竷      �    .pdata      �         穣        .�      �    .xdata      �         蚲7M8              �    .pdata      �         %轢�8        U�      �    .xdata      �   	      �#荤8        �      �    .xdata      �         j8        笀      �    .xdata      �          攰e8        q�      �    .xdata      �          �9�        $�      �    .pdata      �         礝
        亰      �    .xdata      �          %蚘%        輮      �    .pdata      �         }S蛥        g�      �    .rdata      �                      饗     �    .rdata      �          �;�         �      �    .rdata      �                      .�     �    .rdata      �                      E�     �    .rdata      �          �)         g�      �    .xdata$x    �                      搶      �    .xdata$x    �         虼�)         祵      �    .data$r     �   /      嶼�         貙      �    .xdata$x    �   $      4��         龑      �    .data$r     �   $      鎊=         R�      �    .xdata$x    �   $      銸E�         l�      �    .data$r     �   $      騏糡         珝      �    .xdata$x    �   $      4��         艒      �        �           .data       �           烀�          �      �        K�     �    .rdata      �   :       ,gR         r�      �    .rdata      �          ��               �    .rdata      �          IM         蹘      �    .rdata      �          藾味         �      �    .rdata$r    �   $      'e%�         2�      �    .rdata$r    �         �          J�      �    .rdata$r    �                      `�      �    .rdata$r    �   $      Gv�:         v�      �    .rdata$r    �   $      'e%�         晱      �    .rdata$r    �         }%B         瓘      �    .rdata$r    �                      脧      �    .rdata$r    �   $      `         購      �    .rdata$r    �   $      'e%�         鴱      �    .rdata$r    �         �弾         �      �    .rdata$r    �                      <�      �    .rdata$r    �   $      H衡�         ]�      �        噽           .rdata      �          eL喳         搻      �                   _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �   �                祼  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z _Mtx_init_in_situ _Mtx_destroy_in_situ _Mtx_lock _Mtx_unlock ?_Throw_Cpp_error@std@@YAXH@Z ??0BindingSetItem@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??1mutex@std@@QEAA@XZ ??1?$lock_guard@Vmutex@std@@@std@@QEAA@XZ ??1?$list@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ ?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z ?clear@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ ?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAA@XZ ??1?$unordered_map@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@7@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@@std@@QEAA@XZ ??1?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@CAXXZ ??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ ?CreateMaterialBindingSet@MaterialBindingCache@engine@donut@@AEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUMaterial@23@@Z ?GetTextureBindingSetItem@MaterialBindingCache@engine@donut@@AEBA?AUBindingSetItem@nvrhi@@IAEBV?$shared_ptr@ULoadedTexture@engine@donut@@@std@@@Z ??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z ?GetLayout@MaterialBindingCache@engine@donut@@QEBAPEAVIBindingLayout@nvrhi@@XZ ?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z ?Clear@MaterialBindingCache@engine@donut@@QEAAXXZ ?error@log@donut@@YAXPEBDZZ ??$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Hash_representation@PEBUMaterial@engine@donut@@@std@@YA_KAEBQEBUMaterial@engine@donut@@@Z ??$_Copy_memmove@PEAUMaterialResourceBinding@engine@donut@@PEAU123@@std@@YAPEAUMaterialResourceBinding@engine@donut@@PEAU123@00@Z ??$_Fnv1a_append_value@PEBUMaterial@engine@donut@@@std@@YA_K_KAEBQEBUMaterial@engine@donut@@@Z ?dtor$0@?0???$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z@4HA ?dtor$0@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$0@?0??GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z@4HA ?dtor$13@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$14@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$1@?0???$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z@4HA ?dtor$1@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$2@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$3@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$4@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$5@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA ?dtor$6@?0???0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $unwind$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $pdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $cppxdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $stateUnwindMap$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $ip2state$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $unwind$?clear@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $pdata$?clear@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $cppxdata$?clear@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $stateUnwindMap$?clear@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $ip2state$?clear@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAA@XZ $unwind$??1?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@CAXXZ $unwind$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $unwind$?CreateMaterialBindingSet@MaterialBindingCache@engine@donut@@AEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUMaterial@23@@Z $pdata$?CreateMaterialBindingSet@MaterialBindingCache@engine@donut@@AEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUMaterial@23@@Z $unwind$??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z $pdata$??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z $cppxdata$??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z $stateUnwindMap$??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z $ip2state$??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z $unwind$?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z $pdata$?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z $cppxdata$?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z $stateUnwindMap$?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z $ip2state$?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z $unwind$?Clear@MaterialBindingCache@engine@donut@@QEAAXXZ $pdata$?Clear@MaterialBindingCache@engine@donut@@QEAAXXZ $unwind$??$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z $pdata$??$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z $cppxdata$??$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z $stateUnwindMap$??$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z $ip2state$??$_Try_emplace@AEBQEBUMaterial@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUMaterial@engine@donut@@U?$hash@PEBUMaterial@engine@donut@@@std@@U?$equal_to@PEBUMaterial@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUMaterial@engine@donut@@@Z $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $cppxdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $ip2state$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUMaterial@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Copy_memmove@PEAUMaterialResourceBinding@engine@donut@@PEAU123@@std@@YAPEAUMaterialResourceBinding@engine@donut@@PEAU123@00@Z $pdata$??$_Copy_memmove@PEAUMaterialResourceBinding@engine@donut@@PEAU123@@std@@YAPEAUMaterialResourceBinding@engine@donut@@PEAU123@00@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0DK@DBHDPKMP@MaterialBindingCache?3?5unknown?5M@ ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __ImageBase __real@5f000000 __security_cookie 