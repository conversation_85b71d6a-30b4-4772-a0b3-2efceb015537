d�$ 翀"> u       .drectve        <  �               
 .debug$S        � �  t        @ B.debug$T        p   �             @ B.rdata          @   4             @ @@.text$mn        H   t              P`.debug$S        �  � �        @B.text$mn        �  � |         P`.debug$S           "        @B.text$mn        8   J#     -    P`.debug$S        d  % p7     Z   @B.xdata             �:             @0@.pdata             ; ;        @0@.xdata             2;             @0@.pdata             J; V;        @0@.data               t;             @ @�.rdata             �;             @@@.rdata             �;             @@@.rdata             �;             @@@.rdata             �;             @@@.rdata          
   �;             @@@.rdata          
   �;             @@@.rdata             �;             @@@.rdata             �;             @@@.rdata             �;             @@@.rdata             <             @@@.rdata             #<             @@@.rdata             5<             @@@.rdata             Q<             @@@.rdata          "   l<             @@@.rdata             �<             @@@.rdata             �<             @@@.rdata             �<             @@@.rdata             �<             @@@.rdata             �<             @@@.rdata             �<             @0@.chks64            =              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �  f     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\Material.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $literals  $string_literals  $string_view_literals  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $math 	 $colors  $Json   �   A�  R    std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment 4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align " �    std::memory_order_relaxed " �   std::memory_order_consume + �    std::_Aligned_storage<64,8>::_Fits " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits  d    MaterialDomain_Opaque # d   MaterialDomain_AlphaTested $ d   MaterialDomain_AlphaBlended $ d   MaterialDomain_Transmissive / d   MaterialDomain_TransmissiveAlphaTested 0 d   MaterialDomain_TransmissiveAlphaBlended , d   MaterialFlags_UseSpecularGlossModel " d   MaterialFlags_DoubleSided 5 d   MaterialFlags_UseMetalRoughOrSpecularTexture . d   MaterialFlags_UseBaseOrDiffuseTexture ) d   MaterialFlags_UseEmissiveTexture ' d    MaterialFlags_UseNormalTexture * d  @ MaterialFlags_UseOcclusionTexture - d  � MaterialFlags_UseTransmissionTexture E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment , d   MaterialFlags_MetalnessInRedChannel ( d   MaterialFlags_UseOpacityTexture + d   MaterialFlags_SubsurfaceScattering  d   MaterialFlags_Hair 1 �   donut::math::vector<unsigned int,3>::DIM % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size :    std::integral_constant<unsigned __int64,2>::value `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos i    std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard �    std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable A    std::allocator<bool>::_Minimum_asan_allocation_alignment I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment  d    LightType_None  d   LightType_Directional  d   LightType_Spot  d   LightType_Point    ��I@donut::math::PI_f "   
�-DT�!	@donut::math::PI_d !   ��7�5donut::math::epsilon "   �  �donut::math::infinity    �  �donut::math::NaN 7 �   std::atomic<unsigned int>::is_always_lock_free 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment O    std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources ) �   donut::math::vector<bool,4>::DIM � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos Z    std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable   �        nvrhi::EntireBuffer L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable Z    std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment c    std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment Z    std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment :     std::integral_constant<unsigned __int64,0>::value ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) x5    std::_Invoker_functor::_Strategy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy B    std::allocator<float>::_Minimum_asan_allocation_alignment - x5   std::_Invoker_pmf_pointer::_Strategy , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy - x5   std::_Invoker_pmd_pointer::_Strategy T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM *         donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM + <        nvrhi::rt::c_IdentityTransform  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix . d   donut::math::box<float,3>::numCorners ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask 0 �   std::numeric_limits<wchar_t>::is_modulo O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits , d  	 std::numeric_limits<long>::digits10 : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - d  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / d   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 j    std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10  �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> & �5  $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e  �5  _TypeDescriptor 	 �  tm % :4  _s__RTTICompleteObjectLocator2 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const> E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16 d �=  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > G >  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > a '<  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> ] i;  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ oK  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > [ >  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � �=  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > �  ;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 
K  std::_Default_allocator_traits<std::allocator<float> > C G:  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 僈  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 
;  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C :  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � [;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | �=  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > M �=  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L >  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s >  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > T {K  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > U qK  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > : �;  std::_Vector_val<std::_Simple_types<unsigned int> > D >  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � >  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 濱  std::_Ptr_base<donut::engine::DescriptorHandle> � |;  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> U >K  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > "T8  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > U 馢  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > 4 cK  std::allocator<donut::math::vector<float,2> > = *K  std::allocator<donut::math::vector<unsigned short,4> > K TK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U 塈  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 蘆  std::_Ptr_base<donut::engine::BufferGroup> F;  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � ;:  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> e I  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > { �=  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > , 3:  std::allocator<nvrhi::BindingSetItem> K JK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > # �:  std::allocator<unsigned int> J I  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � :  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> D �=  std::_Default_allocator_traits<std::allocator<unsigned int> > L @K  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  uI  std::allocator<float> � 2K  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> K  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � K  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1> 4 K  std::allocator_traits<std::allocator<float> > [ O;  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >  i5  std::input_iterator_tag ; �=  std::allocator_traits<std::allocator<unsigned int> > [ K  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > � 09  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> >  .  std::_Lockit * 2/  std::hash<enum nvrhi::ResourceType> " i3  std::_Char_traits<char,int> � 鱆  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �2  std::_Num_base K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > ) v3  std::_Narrow_char_traits<char,int> L 驤  std::allocator_traits<std::allocator<donut::math::vector<float,2> > >    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc>  �2  std::_Num_int_base / Q/  std::_Conditionally_enabled_hash<bool,1> 2 錔  std::shared_ptr<donut::engine::BufferGroup> � �8  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �2  std::float_denorm_style 4 璄  std::shared_ptr<donut::engine::LoadedTexture> . 5I  std::_Ptr_base<donut::engine::MeshInfo> 6 �5  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast 4 際  std::allocator<donut::math::vector<float,4> > q 笿  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 嘕  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy " �2  std::numeric_limits<double>  <&  std::__non_rtti_object < �6  std::_Ptr_base<donut::engine::DescriptorTableManager> ( n  std::_Basic_container_proxy_ptr12 4 BJ  std::allocator<donut::math::vector<float,3> > > �:  std::vector<unsigned int,std::allocator<unsigned int> > T �:  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8>  �2  std::_Num_float_base 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety P 3J  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f �7  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �5  std::char_traits<char32_t>   �2  std::numeric_limits<bool> # �3  std::_WChar_traits<char16_t> _ /J  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u 蘒  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> % --  std::_One_then_variadic_args_t W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * �8  std::_Vb_val<std::allocator<bool> >   �5  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> � 縄  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  �5  std::false_type  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 稩  std::shared_ptr<donut::engine::DescriptorHandle> , �2  std::numeric_limits<unsigned __int64> L 婭  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound b }I  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> D gI  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  U/  std::hash<bool> 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> K XI  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >  (  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy / NI  std::shared_ptr<donut::engine::MeshInfo> U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> / �3  std::_Char_traits<char32_t,unsigned int> ( 1$  std::hash<nvrhi::FramebufferInfo> % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128> N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 !I  std::_Vector_val<std::_Simple_types<float> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A I  std::allocator_traits<std::allocator<nvrhi::BufferRange> > \ 	I  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > ! �2  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> % m/  std::hash<enum nvrhi::BlendOp>  j&  std::_Ref_count_base " .  std::hash<unsigned __int64> 3 旹  std::_Ptr_base<donut::engine::LoadedTexture>  t  std::exception_ptr C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) a/  std::hash<enum nvrhi::BlendFactor> $ �2  std::numeric_limits<char32_t> J �6  std::enable_shared_from_this<donut::engine::DescriptorTableManager>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 �5  std::allocator_traits<std::allocator<char32_t> >  �  std::_Iterator_base0 | 鸋  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �3  std::_Char_traits<char16_t,unsigned short> $ �#  std::hash<nvrhi::BufferRange> ! �5  std::char_traits<char16_t>  |  std::tuple<>    std::_Container_base12 ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type   �2  std::numeric_limits<long> " �5  std::initializer_list<char>  x5  std::_Invoker_strategy � 驢  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> $ �2  std::_Default_allocate_traits 3 �5  std::allocator_traits<std::allocator<char> > ! �2  std::numeric_limits<short>  u   std::_Vbase . �0  std::allocator<nvrhi::rt::GeometryDesc> ( aE  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock  D  std::bad_alloc B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  蹾  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � 狧  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy # �2  std::numeric_limits<__int64> _ eH  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 4H  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy @ 鯣  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >  �  std::memory_order # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   k5  std::forward_iterator_tag   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >    std::_Container_proxy  �9  std::allocator<bool> _ 霨  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 籊  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy  �  std::nested_exception  r  std::_Distance_unknown ) }G  std::allocator<nvrhi::BufferRange> ( �2  std::numeric_limits<unsigned int> @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff 0 nG  std::vector<float,std::allocator<float> > F <G  std::vector<float,std::allocator<float> >::_Reallocation_policy    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double>  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::allocator<char16_t> .  G  std::vector<bool,std::allocator<bool> > J 麱  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 薋  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> * r5  std::_String_constructor_concat_tag  D-  std::allocator<char>    std::nullptr_t & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1>  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring ' �2  std::numeric_limits<signed char>  -  std::allocator<wchar_t> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > ; �6  std::weak_ptr<donut::engine::DescriptorTableManager> $ &/  std::hash<nvrhi::IResource *> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char>  K5  std::char_traits<char>  k  std::_Unused_parameter h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> @ �3  std::_Default_allocator_traits<std::allocator<char16_t> > 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t>  U3  std::streampos ' y/  std::hash<enum nvrhi::ColorMask> O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> >  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # =7  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle 2 =7  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  岶  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  �?  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 岶  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  ?#  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # gF  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable * �?  nvrhi::RefCountPtr<nvrhi::ITexture>  H!  nvrhi::ComputeState 2 gF  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec 
 !   _ino_t  !   uint16_t ! :E  donut::engine::BufferGroup  譋  donut::engine::Material * 駿  donut::engine::Material::HairParams 0 隕  donut::engine::Material::SubsurfaceParams  zE  donut::engine::MeshInfo  iE  donut::engine::MeshType # 糆  donut::engine::LoadedTexture & �6  donut::engine::DescriptorHandle , 7  donut::engine::DescriptorTableManager B �6  donut::engine::DescriptorTableManager::BindingSetItemsEqual B �6  donut::engine::DescriptorTableManager::BindingSetItemHasher % $E  donut::engine::VertexAttribute % t   donut::engine::DescriptorIndex $ 翬  donut::engine::MaterialDomain  Y@  donut::math::float4x4 " 2@  donut::math::vector<bool,4>    donut::math::float3  Q@  donut::math::affine3  
B  donut::math::float2 #   donut::math::vector<float,3> * F  donut::math::vector<unsigned int,3>  u   donut::math::uint  闌  donut::math::plane # 蜙  donut::math::vector<float,4>  A  donut::math::frustum $ 鰼  donut::math::frustum::Corners # 魼  donut::math::frustum::Planes  F  donut::math::uint3  蜙  donut::math::float4 # Q@  donut::math::affine<float,3>   袮  donut::math::box<float,3> " �?  donut::math::vector<bool,2>  袮  donut::math::box3 % |@  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # 
B  donut::math::vector<float,2> M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t - C4  $_s__RTTIBaseClassArray$_extraBytes_24  軪  MaterialConstants  e4  _CatchableTypeArray     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  K  _lldiv_t  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  �  _s__ThrowInfo  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24 % P4  __RTTIClassHierarchyDescriptor     __time64_t  m  FILE 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray 
 #   size_t 
    time_t  
  __std_exception_data 
 u   _dev_t  K  lldiv_t  H  _ldiv_t  �  _timespec64  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers    �   �      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �     婈涠鰞U暔c��� 鍶詘楞e�yぷ悋A  �    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  M   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/     	{Z�范�F�m猉	痹缠!囃ZtK�T�  \   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     V� c鯐鄥杕me綻呥EG磷扂浝W)  e   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�      d蜯�:＠T邱�"猊`�?d�B�#G騋  B   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     `k�"�1�^�`�d�.	*貎e挖芺
脑�  \   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   *Df*煲髙笧�牧w�(
序:憪诰11阶X嘁  �   �0�*е彗9釗獳+U叅[4椪 P"��  	   �=蔑藏鄌�
艼�(YWg懀猊	*)  J   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�     j轲P[塵5m榤g摏癭 鋍1O骺�*�  N   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   繃S,;fi@`騂廩k叉c.2狇x佚�  F   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  	   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  [	   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �	   �*o驑瓂a�(施眗9歐湬

�  �	    I嘛襨签.濟;剕��7啧�)煇9触�.   
   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  e
   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �
   猯�諽!~�:gn菾�]騈购����'  �
   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  (   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  z   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L     鹴y�	宯N卮洗袾uG6E灊搠d�  K   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  '
   G�膢刉^O郀�/耦��萁n!鮋W VS  f
   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �
   o�椨�4梠"愜��
}z�$ )鰭荅珽X      $^IXV嫓進OI蔁
�;T6T@佮m琦�  7   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  u   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  :   o藾錚\F鄦泭|嚎醖b&惰�_槮  y   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�     +椬恡�
	#G許�/G候Mc�蜀煟-  B   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟     双:Pj �>[�.ぷ�<齠cUt5'蠙砥  Y   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�     *u\{┞稦�3壅阱\繺ěk�6U�  A   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  J   +4[(広
倬禼�溞K^洞齹誇*f�5  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G     vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  X   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   交�,�;+愱`�3p炛秓ee td�	^,  ?   zY{���睃R焤�0聃
扨-瘜}  x   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  9   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   副謐�斦=犻媨铩0
龉�3曃譹5D   �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;     J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  Q   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  #   =J�(o�'k螓4o奇缃�
黓睆=呄k_  _   _O縋[HU-銌�鼪根�鲋薺篮�j��  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  O   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   蜅�萷l�/费�	廵崹
T,W�&連芿  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎     D���0�郋鬔G5啚髡J竆)俻w��  q   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   匐衏�$=�"�3�a旬SY�
乢�骣�  �   v峞M� {�:稚�闙蛂龣 �]<��  @   悯R痱v 瓩愿碀"禰J5�>xF痧  �   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   矨�陘�2{WV�y紥*f�u龘��     �$晑�~2]�/
S蟦a� �
}A珈弿V緈  H   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   穫農�.伆l'h��37x,��
fO��  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �   鏀q�N�&}
;霂�#�0ncP抝  0   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  z   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   �       �  @  �  �  @  �    @  �    �  g   t  �  �      H     &  �
  �  .  @  �  0  @  �  1  @     2  @  x  �      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\shaders\material_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\src\engine\Material.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h   �       L`l  ,  3    ",  3   
 �1  4    �1  4   
 �;      �;     
 L<      P<     
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   $        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >A   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   A  O__f  9(       A   O ,       0      
 g       k      
 w       {      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
            
 !      %     
 1      5     
 A      E     
 �      �     
 E3襆嬃D塕A嬍E8�  t�   塉嬃M9PPtE8恜  t內塀嬋M9P`tE8恞  t兩塉嬃M9悁   tE8恠  t內塀嬋M9PptE8恟  t兩 塉嬃M9悙   tE8恡  t內@塀嬋M9悹   tE8恥  t洪塉嬃M9惏   tE8恦  t鸿	塀E8恮  t內塀E8恱  t鸿塀A禓LL�
    塀,駻��   �A媭�   塀駻��   �BA媭�   塀驛橃   驛堜   (皿AY��   驛愯   �Y梭B �Y芋J$�R(A媭�   塀4A媭�   塀8A媭  塀<A媭|  塀A媭  塀@A媭   塀H駻�  �BhA禓L凐w A媽�    I�酇媭�   塀0�荁0  �?A禓L凐w A媽�    I�酇媭�   塀D�荁D  �縀8�  tZ丣   駻�  �個   A媭   墏�   駻�$  �倫   A媭,  墏�   A媭0  塀|A媭4  墏�   E8�8  劄   丣   駻�<  �偁   A媭D  墏�   A媭H  墏�   A媭L  墏�   A媭P  墏�   A媭T  墏�   A媭h  墏�   A媭l  墏�   A媭X  墏�   駻�\  �偫   A媭d  墏�   I婬P����H吷tD婭 �D嬋D塉LI婬`H吷t婭 �嬋塉PI婬pH吷t婭 �嬋塉XI媹�   H吷t婭 �嬋塉TI媹�   H吷t婭 �嬋塉\I媹�   H吷t婭 �嬋塉`I媹�   H吷t婣 塀dL塕pD塕x胒�                                                �   n    �      �  !    �       �       �      �       �       �      �  #    �  "    �  #    �  #    �  "    �  #       �   �  Q G            �      �  !        �donut::engine::Material::FillConstantBuffer 
 >繣   this  AJ          AP       � >菶   constants  AK        � M        &   N M        &  5 N M        &  	L N M        &  f N M        &  	} N M        &  	�� N M        &  	�� N M        t  "�3'
 >@    b  A�   3    � N M           儣 M        &  
儣 N N M           儊 M        &  
儊 N N M           僰 M        &  
僰 N N M           僓 M        &  
僓 N N M           傿 M        &  傿 N N M           �/ M        &  �/ N N M           � M        &  � N N                        @ & h     t       #  &  )  -  
            
                    $LN25         $LN23         $LN18         $LN16     繣  Othis     菶  Oconstants  O   �   (          �  H  B         $  �    '  �
   )  �   *  �   ,  �/   -  �5   /  �F   0  �L   2  �`   3  �f   5  �w   6  �}   8  ��   9  ��   ;  ��   <  ��   >  ��   ?  ��   A  ��   B  ��   D  ��   E  ��   I  ��   U  �3  L  �h  M  �r  N  �|  O  ��  P  ��  Q  ��  R  ��  S  ��  U  ��  Y  ��  Z  ��  `  ��  f  ��  j  ��  k  ��  s  �  y  �  {  �  }  �4  ~  �R    �\  �  �i  �  �v  �  �}  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �/  �  �B  �  �U  �  �k  �  ��  �  ��  �  ��  �  ��  �  �,       0      
 v       z      
 �       �      
 �       �      
 �      �     
 T  !    X  !   
 c      g     
 n  #    r  #   
   "    �  "   
 �       �      
 �      �     
 �      �     
 H塡$H塴$H塼$H墊$ AVH冹 H媕H嬞H媟M嬸H孃H嬍H凖vH�
H凗u8L嬈H�    �    吚u%駻�內   A婩墐�   �苾�  楱  H嬒H凖vH�H凗
u8L嬈H�    �    吚u%駻�冊   A婩墐�   �苾�  閊  H嬒H凖vH�H凗
u8L嬈H�    �    吚u%駻�冟   A婩墐�   �苾�  �  H嬒H凖vH�H凗u*L嬈H�    �    吚uA�墐�   �苾�  樨  H嬒H凖vH�H凗	u*L嬈H�    �    吚uA�墐�   �苾�  闇  H嬒H凖vH�H凗	u*L嬈H�    �    吚uA�墐�   �苾�  閌  H嬒H凖vH�H凗u*L嬈H�    �    吚uA�墐�   �苾�  �$  H嬒H凖vH�H凗u*L嬈H�    �    吚uA�墐�   �苾�  殍  H嬒H凖vH�H凗u*L嬈H�    �    吚uA�墐   �苾�  楝  H嬒H凖vH�H凗u*L嬈H�    �    吚uA�墐  �苾�  閜  H嬒H凖vH�H凗u*L嬈H�    �    吚uA�墐  �苾�  �4  H嬒H凖vH�H凗u.L嬈H�    �    吚u駻���  苾�  轸  H嬒H凖vH�H凗u6L嬈H�    �    吚u#驛/    苾�  椑垉p  �楝  H嬒H凖vH�H凗!u6L嬈H�    �    吚u#驛/    苾�  椑垉q  �閐  H嬒H凖vH�H凗u6L嬈H�    �    吚u#驛/    苾�  椑垉r  ��  H嬒H凖vH�H凗u6L嬈H�    �    吚u#驛/    苾�  椑垉s  �樵   H嬒H凖vH�H凗u6L嬈H�    �    吚u#驛/    苾�  椑垉t  �閷   H嬒H凖vH�H凗u3L嬈H�    �    吚u 驛/    苾�  椑垉u  �隚H凖vH�?H凗u6L嬈H�    H嬒�    吚u 驛/    苾�  椑垉v  ��2繦媆$0H媗$8H媡$@H媩$HH兡 A^肅   7    H       �   :    �       �   =    �       !  @    &      ]  C    b      �  F    �      �  I    �        L          M  O    R      �  R    �      �  U    �        X          A  [    F      V  q    �  ^    �      �  q    �  a    �      �  q      d          .  q    a  g    f      v  q    �  j    �      �  q    �  m    �        q       �   f  J G            8       "        �donut::engine::Material::SetProperty 
 >臙   this  AI  !      AJ        ! 
 >�   name  AK        G V AK u     � . f . �   �   (  d  �  �    T  �$ �, , `, �, �, 8) z,  >廆   value  AP        (  AV  (      M        .  %
 M        0  %
 M        1  7 M        2  = N N M        �  # >_    _Result  AJ  .       AJ u       M           N N N N M        .  %u M        0  %u M        1  �� M        2  �� N N M        �  u# >_    _Result  AJ  x       AJ �       M          x N N N N M        .  %�� M        0  %�� M        1  �� M        2  �� N N M        �  ��# >_    _Result  AJ  �       AJ 	      M          �� N N N N M        .  %�	 M        0  %�	 M        1  � M        2  � N N M        �  �	# >_    _Result  AJ        AJ E      M          � N N N N M        .  %丒 M        0  %丒 M        1  丵 M        2  乄 N N M        �  丒# >_    _Result  AJ  H      AJ �      M          丠 N N N N M        .  %亖 M        0  %亖 M        1  亶 M        2  亾 N N M        �  亖# >_    _Result  AJ  �      AJ �      M          亜 N N N N M        .  %伣 M        0  %伣 M        1  伾 M        2  佅 N N M        �  伣# >_    _Result  AJ  �      AJ �      M          伬 N N N N M        .  %侚 M        0  %侚 M        1  � M        2  � N N M        �  侚# >_    _Result  AJ  �      AJ 5      M          侟 N N N N M        .  %�5 M        0  %�5 M        1  侫 M        2  侴 N N M        �  �5# >_    _Result  AJ  8      AJ q      M          �8 N N N N M        .  %俼 M        0  %俼 M        1  倉 M        2  們 N N M        �  俼# >_    _Result  AJ  t      AJ �      M          倀 N N N N M        .  %偔 M        0  %偔 M        1  偣 M        2  偪 N N M        �  偔# >_    _Result  AJ  �      AJ �      M          偘 N N N N M        .  %傞 M        0  %傞 M        1  傰 M        2  傷 N N M        �  傞# >_    _Result  AJ  �      AJ )      M          傡 N N N N M        .  %�) M        0  %�) M        1  �5 M        2  �; N N M        �  �)# >_    _Result  AJ  ,      AJ q      M          �, N N N N M        .  %僸 M        0  %僸 M        1  儅 M        2  儍 N N M        �  僸# >_    _Result  AJ  t      AJ �      M          僼 N N N N M        .  %児 M        0  %児 M        1  兣 M        2  兯 N N M        �  児# >_    _Result  AJ  �      AJ       M          兗 N N N N M        .  %� M        0  %� M        1  �
 M        2  � N N M        �  �# >_    _Result  AJ        AJ I      M          � N N N N M        .  %処 M        0  %処 M        1  刄 M        2  刐 N N M        �  処# >_    _Result  AJ  L      AJ �      M          凩 N N N N M        .  %剳 M        0  %剳 M        1  劃 M        2  劊 N N M        �  剳# >_    _Result  AJ  �      AJ �    b  )  M          剶 N N N N M        .  %勚 M        0  %勚 M        1  勥 M        2  勫 N N M        �  勚 >_    _Result  AM  +      M          勚 N N N N                       @ . h
   �  �    �  '  (  .  0  1  2   0   臙  Othis  8   �  Oname  @   廆  Ovalue  O  �   �           8  H     �       �  �   �  �   �  �!   �  �%   �  �+   �  �u   �  ��   �  �	  �  �E  �  ��  �  ��  �  ��  �  �5  �  �q  �  ��  �  ��  �  �)  �  �q  �  ��  �  �  �  �I  �  ��  �  ��  �  �  �  �  �  �,       0      
 o       s      
        �      
 �       �      
 �       �      
            
 &      *     
 �      �     
 �      �     
 �      �     
 �      �     
 v      z     
 �      �     
 J      N     
 Z      ^     
       "     
 .      2     
 �      �     
            
 �      �     
 �      �     
 �      �     
 �      �     
 n      r     
 ~      �     
 B	      F	     
 R	      V	     
 
      
     
 &
      *
     
 �
      �
     
 �
      �
     
 �      �     
 �      �     
 �      �     
 �      �     
 f
      j
     
 v
      z
     
 :      >     
 J      N     
            
       "     
 �      �     
 �      �     
 �      �     
 |      �     
  d T 4 2p    H                         '    
 t	 d T 4 2�    8          $       $       -        ����    ����        ��������baseOrDiffuseColor specularColor emissiveColor emissiveIntensity metalness roughness opacity alphaCutoff transmissionFactor normalTextureScale occlusionStrength normalTextureTransformScale enableBaseOrDiffuseTexture enableMetalRoughOrSpecularTexture enableNormalTexture enableEmissiveTexture enableOcclusionTexture enableTransmissionTexture enableOpacityTexture    ?噾姏@|#祰� �#^�8cI橗cS叜月o"0� �蹰k~�轖	hf絽 C"k5崤W攷�
鹦佣鋮4�F{'yZ祼垩寯啦舩滑嶕傺��o-K��H~%!�雩滾.E曊紌�3�	К|藤�
5�陞0.�[0㏎�8z缟腞�	+俈j牞*yw}騰刅�晉K媹Z	冎蟬�<]蹄]q誎鎓+D昮�~懀�#?]騔y@b|鐔�伕�F鷀媉�.9�8駫�K寿瀀CRC冼        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       �              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       H       襶.      .debug$S       �             .text$mn       �     刽g     .debug$S                    .text$mn    	   8  -   I�-     .debug$S    
   d  Z       	        \               x               �       	    memcmp           $LN13           $LN100  �      $LN16   �      $LN18   �      $LN99   �      $LN23   �      $LN25   �      $LN459      	    .xdata                F┑@        I          .pdata               X賦�        m          .xdata      
          U费�	        �      
    .pdata               og	                  .data                  烀�          �              �         .rdata                軾�                   .rdata                �`%         /          .rdata                矊Ip         O          .rdata                汄�         p          .rdata         
       n_0         �          .rdata         
       YI?G         �          .rdata                �	�         �          .rdata                �l�         �          .rdata                \畽�                   .rdata                m0�         .          .rdata                佲θ         U          .rdata                簇眄         {          .rdata                垒殳         �          .rdata         "       謥礯         �          .rdata                栲�?                   .rdata                �:         6          .rdata                 !�a         `           .rdata      !          0��         �      !    .rdata      "          麒3�         �      "        �           .rdata      #          =-f�         �      #    _fltused         .chks64     $                    �  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ?FillConstantBuffer@Material@engine@donut@@QEBAXAEAUMaterialConstants@@@Z ?SetProperty@Material@engine@donut@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$?SetProperty@Material@engine@donut@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $pdata$?SetProperty@Material@engine@donut@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0BD@IDPMGPEN@baseOrDiffuseColor@ ??_C@_0O@LCEJNME@specularColor@ ??_C@_0O@FOANAKIK@emissiveColor@ ??_C@_0BC@JMDEMCCI@emissiveIntensity@ ??_C@_09CMCKPJJJ@metalness@ ??_C@_09FLEKNONA@roughness@ ??_C@_07IJGPCJHC@opacity@ ??_C@_0M@FOEGDCHG@alphaCutoff@ ??_C@_0BD@OPGOJIMM@transmissionFactor@ ??_C@_0BD@NHMHCOPN@normalTextureScale@ ??_C@_0BC@FAECNCDD@occlusionStrength@ ??_C@_0BM@JCGCFAKC@normalTextureTransformScale@ ??_C@_0BL@EKGJHAIM@enableBaseOrDiffuseTexture@ ??_C@_0CC@OBDLBCAD@enableMetalRoughOrSpecularTextu@ ??_C@_0BE@MPPCIEJK@enableNormalTexture@ ??_C@_0BG@GNNJKDAO@enableEmissiveTexture@ ??_C@_0BH@OMFDENJK@enableOcclusionTexture@ ??_C@_0BK@JMOMNMPN@enableTransmissionTexture@ ??_C@_0BF@BNMINCID@enableOpacityTexture@ __ImageBase __real@3f000000 