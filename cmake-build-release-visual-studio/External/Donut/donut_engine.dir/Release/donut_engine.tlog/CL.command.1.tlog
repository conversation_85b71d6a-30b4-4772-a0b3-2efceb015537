^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\AUDIOCACHE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\J<PERSON><PERSON>PP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\AUDIOCACHE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\AUDIOENGINE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\AUDIOENGINE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\BINDINGCACHE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\BINDINGCACHE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\COMMONRENDERPASSES.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\COMMONRENDERPASSES.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\CONSOLEINTERPRETER.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\CONSOLEINTERPRETER.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\CONSOLEOBJECTS.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\CONSOLEOBJECTS.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\DDSFILE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\DDSFILE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\DESCRIPTORTABLEMANAGER.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\DESCRIPTORTABLEMANAGER.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\FRAMEBUFFERFACTORY.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\FRAMEBUFFERFACTORY.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\GLTFIMPORTER.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\GLTFIMPORTER.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\IESPROFILE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\IESPROFILE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\KEYFRAMEANIMATION.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\KEYFRAMEANIMATION.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\MATERIAL.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\MATERIAL.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\MATERIALBINDINGCACHE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\MATERIALBINDINGCACHE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SCENE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SCENE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SCENEGRAPH.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SCENEGRAPH.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SCENETYPES.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SCENETYPES.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SHADERFACTORY.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\SHADERFACTORY.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\TEXTURECACHE.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\TEXTURECACHE.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\VIEW.CPP
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TP D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\VIEW.CPP
^D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\STB_IMPL.C
/c /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /Zi /nologo /W3 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D DONUT_WITH_TASKFLOW /D NOMINMAX /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"DONUT_ENGINE.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\RELEASE\DONUT_ENGINE.PDB" /external:W3 /Gd /TC D:\RTXPT\EXTERNAL\DONUT\SRC\ENGINE\STB_IMPL.C
