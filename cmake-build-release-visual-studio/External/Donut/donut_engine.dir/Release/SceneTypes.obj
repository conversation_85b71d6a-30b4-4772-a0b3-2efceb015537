d喴翀3� �      .drectve        <  銱               
 .debug$S         &  J   p     
   @ B.debug$T        p   刾             @ B.rdata          L   魀             @ @@.text$mn           @q Xq         P`.debug$S           bq 俽     
   @B.text$mn        -   鎟 s         P`.debug$S        8  s Ut     
   @B.text$mn           箃 総         P`.debug$S          萾 衭        @B.text$mn            v %v         P`.debug$S           /v Ow        @B.text$mn        �   焪              P`.debug$S        �  Gx �y        @B.text$mn        �  Oz              P`.debug$S        �  謠 妦        @B.text$mn        �                 P`.debug$S        0  � 羳        @B.text$mn        .   �              P`.debug$S        �   ?� 7�        @B.text$mn            噦              P`.debug$S        �    焹        @B.text$mn        :   飪 )�         P`.debug$S          G� S�        @B.text$mn          邌 飱     
    P`.debug$S        �	  q� !�     \   @B.text$mn        ]  箻 �         P`.debug$S        �  *� 鰷        @B.text$mn        �  緷 ⅲ         P`.debug$S        �	  裕 x�     *   @B.text$mn        8   � T�         P`.debug$S          ^� n�        @B.text$mn        #                 P`.debug$S        �   桶 帘        @B.text$mn        .   +�         P`.debug$S        �  S� �        @B.text$mn        �   ;� 渭         P`.debug$S        �  丶 木        @B.text$mn        (   P�              P`.debug$S        �  x� l�        @B.text$mn        (   剂              P`.debug$S        �  淞 忻        @B.text$mn        (    �              P`.debug$S        �  H� 4�        @B.text$mn        <   勂 榔         P`.debug$S        0  奁 �     
   @B.text$mn        <   r�          P`.debug$S        L  倘 �     
   @B.text$mn        !   |� 澥         P`.debug$S        <  笔 硭        @B.text$mn        2   )� [�         P`.debug$S        <  o�         @B.text$mn           #� .�         P`.debug$S        ,  8� d�        @B.text$mn           犗          P`.debug$S           迪 招        @B.text$mn           � �         P`.debug$S           &� F�        @B.text$mn        �   傄 O�         P`.debug$S        �  c� ?�     "   @B.text$mn        r   撡              P`.debug$S        8  � =�        @B.text$mn            褴              P`.debug$S        p  � 佫     
   @B.text$mn        ^   遛 C�         P`.debug$S        �  W� +�        @B.text$mn           哜              P`.debug$S        �   忖 裸        @B.text$mn            �         P`.debug$S        �   %� 	�        @B.text$mn           1� D�         P`.debug$S        �   X� 8�        @B.text$mn        +   t� 熸         P`.debug$S        �   虫         @B.text$mn        +   珑 �         P`.debug$S        �   &� �        @B.text$mn        +   V� 侀         P`.debug$S        �   曢 呹        @B.text$mn        @   陵 �         P`.debug$S        �   � �        @B.text$mn        4   ?� s�         P`.debug$S        �   囲 S�        @B.text$mn        4   忢 庙         P`.debug$S        �   醉 愁        @B.text$mn        N   镱 =�         P`.debug$S        �  G� 唣        @B.text$mn        4   W� 嬹         P`.debug$S        �   燅 {�        @B.text$mn        B   夫          P`.debug$S           � �        @B.text$mn        B   S� 曯         P`.debug$S          臭 悯        @B.text$mn        B   �� A�         P`.debug$S        �   _� [�        @B.text$mn        H   楓              P`.debug$S        �  喵 ｙ        @B.text$mn        1  机 禧         P`.debug$S           � >        @B.text$mn        1   K         P`.debug$S        �  } 5        @B.text$mn        M  	 ^
         P`.debug$S        �  �
 \        @B.text$mn        �   8          P`.debug$S        h  Q �        @B.text$mn        �   � *         P`.debug$S        �  >         @B.text$mn        �   z =         P`.debug$S        p  [ �        @B.text$mn           �         P`.debug$S        �  � �         @B.text$mn        O  �!              P`.debug$S        �  �" �$        @B.text$mn           F%              P`.debug$S        �   L% ,&        @B.text$mn           h&              P`.debug$S        �   k& S'        @B.text$mn        �   �' ](         P`.debug$S        �  {( g+        @B.text$mn           ,              P`.debug$S        �   
, �,        @B.text$mn           1-              P`.debug$S        �   7- .        @B.text$mn           S.              P`.debug$S        �   Y. 9/        @B.text$mn        3   u/ �/         P`.debug$S        h  �/ $1        @B.text$mn        �   `1              P`.debug$S        P  �1 J4        @B.text$mn        G   �4 15         P`.debug$S        t  ;5 �6        @B.text$mn        \  �6 [8         P`.debug$S          �8 �>     (   @B.text$x            嘆 揁         P`.text$mn        \   滰 鵃         P`.debug$S        L  A OB        @B.text$mn        �   烞 3C     	    P`.debug$S        p  岰 鼸        @B.text$mn        �   盕 eG         P`.debug$S        �  軬 璊        @B.text$mn           uK              P`.debug$S          xK 怢        @B.text$mn        �   郘 訫         P`.debug$S        �  圢  R        @B.text$mn        x   S 圫         P`.debug$S        �  2T V        @B.text$mn        .  鯲 $\         P`.debug$S        �  抃 fh     >   @B.text$mn        �  襧 dl         P`.debug$S        X  杔 頾        @B.text$mn        �   zp Iq         P`.debug$S        �  卶 )v        @B.text$mn        c   -w 恮         P`.debug$S        �   `z        @B.text$mn        
  ({ 5|         P`.debug$S        �  厊 �        @B.text$mn           E�              P`.debug$S        X  H� 爠     
   @B.text$mn        o  � s�         P`.debug$S        t  雴 _�     &   @B.text$mn        �   蹚 d�     
    P`.debug$S        H  鎼 .�        @B.text$mn           鈸              P`.debug$S          鍝 鯏        @B.text$mn        �   E� 闀         P`.debug$S        �  墫 �        @B.text$mn        �   鍣 繗         P`.debug$S        4  洓 蠟        @B.text$mn           繜              P`.debug$S          褵 鍫        @B.text$mn           5�              P`.debug$S          G� W�        @B.text$mn           Б              P`.debug$S          耿 牛        @B.text$mn        
   �              P`.debug$S        P  "� r�     
   @B.text$mn        
   芝              P`.debug$S        H  悭 +�     
   @B.text$mn        
   彠              P`.debug$S        H  湩 洙     
   @B.text$mn           H�              P`.debug$S        ,  K� w�        @B.text$mn            仟 绐         P`.debug$S        �   � 色        @B.text$mn           � �         P`.debug$S        �   *� 蕃        @B.text$mn        `  � z�         P`.debug$S        �  十 n�     B   @B.text$mn           � �         P`.debug$S        �   � 蠛        @B.xdata             /�             @0@.pdata             C� O�        @0@.xdata             m�             @0@.pdata             u� 伝        @0@.xdata             熁             @0@.pdata              坊        @0@.xdata             栈             @0@.pdata             莼 榛        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             =�             @0@.pdata             E� Q�        @0@.xdata             o�             @0@.pdata             {� 嚰        @0@.xdata             ゼ             @0@.pdata              辜        @0@.xdata             准             @0@.pdata             呒 爰        @0@.xdata             	�             @0@.pdata             � )�        @0@.xdata             G� [�        @0@.pdata             y� 吔        @0@.xdata             ＝ 辰        @0@.pdata             呀 萁        @0@.xdata              �        @0@.pdata             -� 9�        @0@.xdata             W�             @0@.pdata             _� k�        @0@.xdata             壘 ゾ        @0@.pdata             咕 啪        @0@.xdata          	   憔 炀        @@.xdata              � �        @@.xdata             �             @@.xdata             �             @0@.pdata              � ,�        @0@.xdata             J�             @0@.pdata             V� b�        @0@.voltbl            ��               .xdata             伩             @0@.pdata             壙 暱        @0@.xdata             晨 丝        @0@.pdata             榭 蹩        @0@.xdata             � '�        @0@.pdata             E� Q�        @0@.voltbl            o�               .xdata             s�             @0@.pdata             � 嬂        @0@.voltbl            ├               .xdata                          @0@.pdata             怖 纠        @0@.xdata             芾             @0@.pdata             枥 衾        @0@.xdata             �             @0@.pdata             � &�        @0@.xdata             D�             @0@.pdata             X� d�        @0@.xdata             偭             @0@.pdata             捔 灹        @0@.xdata             剂 辛        @0@.pdata             盍         @0@.xdata             � (�        @0@.pdata             F� R�        @0@.xdata             p�             @0@.pdata             劼 惵        @0@.xdata          8    媛        @0@.pdata             � �        @0@.xdata             .� F�        @0@.pdata             d� p�        @0@.xdata             幟 灻        @0@.pdata             济 让        @0@.xdata             婷             @0@.pdata             雒 �        @0@.voltbl             �               .xdata             #�             @0@.pdata             /� ;�        @0@.xdata             Y�             @0@.pdata             e� q�        @0@.xdata             從 Ｄ        @0@.pdata             聊 湍        @0@.xdata             肽 ��        @0@.pdata             � )�        @0@.xdata             G� W�        @0@.pdata             u� 伵        @0@.xdata             熍         @0@.pdata             团 倥        @0@.voltbl            髋               .xdata                          @0@.pdata             
� �        @0@.voltbl            7�                .xdata             =�             @0@.pdata             U� a�        @0@.xdata             �             @0@.pdata             徠 浧        @0@.xdata             蛊             @0@.pdata             牌 哑        @0@.xdata             锲             @0@.pdata             � �        @0@.xdata             -�             @0@.pdata             9� E�        @0@.xdata             c�             @0@.pdata             w� 兦        @0@.voltbl            ∏                .xdata                          @0@.pdata             非 们        @0@.xdata             崆             @0@.pdata             袂         @0@.xdata             �             @0@.pdata             '� 3�        @0@.xdata             Q�             @0@.pdata             i� u�        @0@.xdata             撊             @0@.pdata             熑         @0@.xdata             扇             @0@.pdata             萑 槿        @0@.voltbl            �                .xdata             
�             @0@.pdata             � %�        @0@.xdata             C�             @0@.pdata             S� _�        @0@.xdata             }�             @0@.pdata             壣 暽        @0@.xdata             成             @0@.pdata             松 咨        @0@.xdata             跎             @0@.pdata             � 
�        @0@.xdata             +�             @0@.pdata             ?� K�        @0@.xdata          0   i�             @0@.pdata             櫴 ナ        @0@.xdata          4   檬             @0@.pdata             魇 �        @0@.xdata             !�             @0@.pdata             1� =�        @0@.xdata          4   [�             @0@.pdata             徦 浰        @0@.voltbl            顾               .xdata             核             @0@.pdata             滤 嗡        @0@.xdata             焖             @0@.pdata             羲  �        @0@.voltbl            �               .voltbl            �               .xdata          $    �             @0@.pdata             D� P�        @0@.xdata             n�             @0@.pdata             z� 喬        @0@.xdata             ぬ             @0@.pdata              柑        @0@.xdata             痔             @0@.pdata             尢 晏        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             :�             @0@.pdata             B� N�        @0@.rdata             l� 勍        @@@.rdata             ⑼             @@@.rdata             赐 掏        @@@.rdata             晖 �        @@@.rdata              �             @@@.xdata$x           5� Q�        @@@.xdata$x           e� 佄        @@@.data$r         /   熚 挝        @@�.xdata$x        $   匚         @@@.data$r         $   � 4�        @@�.xdata$x        $   >� b�        @@@.data$r         $   v� 毾        @@�.xdata$x        $   は 认        @@@.rdata             芟             @@@.data               煜             @ @�.rdata          8   � D�        @@@.rdata          P   娦 谛     
   @@@.rdata          P   >� 幯     
   @@@.rdata          P   蜓 B�     
   @@@.rdata          P   σ 鲆     
   @@@.rdata             Z�             @0@.rdata             `�             @@@.rdata             k�             @@@.rdata             w�             @@@.rdata             堄             @0@.rdata          
   嵱             @@@.rdata             椨             @@@.rdata             ⒂             @@@.rdata                          @0@.rdata             从             @0@.rdata          
   河             @@@.rdata             挠             @@@.rdata             嫌             @0@.rdata             钟             @@@.rdata          
   庥             @@@.rdata          
   镉             @@@.rdata                          @@@.rdata             �             @@@.rdata             -�             @0@.rdata          
   3�             @@@.rdata          (   =� e�        @@@.rdata          (   椩 吭        @@@.rdata          (   裨 �        @@@.rdata$r        $   K� o�        @@@.rdata$r           嵳 ≌        @@@.rdata$r            氛        @@@.rdata$r        $   琳 逭        @@@.rdata$r        $    �        @@@.rdata$r           ;� O�        @@@.rdata$r           Y� m�        @@@.rdata$r        $   佒 ブ        @@@.rdata$r        $   怪 葜        @@@.rdata$r            �        @@@.rdata$r           � 5�        @@@.rdata$r        $   S� w�        @@@.data$rs        *   嬜 底        @@�.rdata$r           孔 幼        @@@.rdata$r           葑 樽        @@@.rdata$r        $   笞 �        @@@.rdata$r        $   +� O�        @@@.data$rs        2   m� 熦        @@�.rdata$r           ┴ 截        @@@.rdata$r           秦 迂        @@@.rdata$r        $   葚 �        @@@.rdata$r        $   � 9�        @@@.data$rs        )   W� ��        @@�.rdata$r           娰 炠        @@@.rdata$r           ㄙ 假        @@@.rdata$r        $   匈 糍        @@@.rdata$r        $   � ,�        @@@.data$rs        4   J� ~�        @@�.rdata$r           堏 溭        @@@.rdata$r            纶        @@@.rdata$r        $   嘹 �        @@@.rdata$r        $   � <�        @@@.data$rs        -   Z� 囒        @@�.rdata$r           戂 ホ        @@@.rdata$r            僳        @@@.rdata$r        $   檑 
�        @@@.rdata$r        $   !� E�        @@@.data$rs        .   c� 戃        @@�.rdata$r           涇         @@@.rdata$r           管 哲        @@@.rdata$r        $   筌 �        @@@.rdata$r        $   +� O�        @@@.data$rs        M   m� 狠        @P�.rdata$r           妮 剌        @@@.rdata$r           廨 鲚        @@@.rdata$r        $   
� .�        @@@.rdata$r        $   B� f�        @@@.data$rs        F   勣 兽        @P�.rdata$r           赞 柁        @@@.rdata$r           蜣 �        @@@.rdata$r        $   � >�        @@@.rdata$r        $   R� v�        @@@.data$rs        G   斶 圻        @P�.rdata$r           暹         @@@.rdata$r           � �        @@@.rdata$r        $   +� O�        @@@.rdata             c�             @0@.rdata             g�             @@@.rdata             o�             @0@.rdata             s�             @@@.rdata             {�             @@@.rdata             冟             @0@.rdata             囙             @0@.rdata             嬥             @0@.rdata             忇             @P@.rdata             熰             @P@.rdata                          @P@.rdata             苦             @P@.debug$S        D   相 �        @B.debug$S        \   '� 冡        @B.debug$S        T   椺 脶        @B.debug$S        X   �� W�        @B.debug$S        <   k� р        @B.debug$S        H   烩 �        @B.debug$S        @   � W�        @B.debug$S        @   k�         @B.debug$S        4   裤 筱        @B.debug$S        4   � ;�        @B.debug$S        @   O� 忎        @B.chks64         �  ｄ              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   1  h     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\SceneTypes.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $json  $vfs  $math 	 $colors  $Json 	 $stdext    �   �  R    std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible 4   @ _Mtx_internal_imp_t::_Critical_section_size � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible 5    _Mtx_internal_imp_t::_Critical_section_align � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable " �    std::memory_order_relaxed " �   std::memory_order_consume + �    std::_Aligned_storage<64,8>::_Fits " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits �   �  E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment t   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible % 54    _Atomic_memory_order_relaxed % 54   _Atomic_memory_order_consume % 54   _Atomic_memory_order_acquire � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible % 54   _Atomic_memory_order_release % 54   _Atomic_memory_order_acq_rel % 54   _Atomic_memory_order_seq_cst � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size ( �   donut::math::vector<int,4>::DIM :    std::integral_constant<unsigned __int64,2>::value H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified 1 �   donut::math::vector<unsigned int,2>::DIM �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment  d    MaterialDomain_Opaque # d   MaterialDomain_AlphaTested $ d   MaterialDomain_AlphaBlended $ d   MaterialDomain_Transmissive / d   MaterialDomain_TransmissiveAlphaTested 0 d   MaterialDomain_TransmissiveAlphaBlended , d   MaterialFlags_UseSpecularGlossModel " d   MaterialFlags_DoubleSided 5 d   MaterialFlags_UseMetalRoughOrSpecularTexture . d   MaterialFlags_UseBaseOrDiffuseTexture ) d   MaterialFlags_UseEmissiveTexture ' d    MaterialFlags_UseNormalTexture * d  @ MaterialFlags_UseOcclusionTexture - d  � MaterialFlags_UseTransmissionTexture , d   MaterialFlags_MetalnessInRedChannel ( d   MaterialFlags_UseOpacityTexture + d   MaterialFlags_SubsurfaceScattering  d   MaterialFlags_Hair `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos i    std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment 1 �   donut::math::vector<unsigned int,3>::DIM �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard < �   InstanceFlags_CurveDisjointOrthogonalTriangleStrips \    std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment    �   �   �    std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment  �   �  j    std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment _    std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi x    std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer q    std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout 5 �    std::filesystem::_File_time_clock::is_steady , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment �    std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard R    std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free  �   �  / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable �    std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible q    std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable h    std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment A    std::allocator<bool>::_Minimum_asan_allocation_alignment I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment  �   k   i    std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment  �   
  M   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size $ d   std::_Locbase<int>::collate M   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets " d   std::_Locbase<int>::ctype G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi % d   std::_Locbase<int>::monetary $ d   std::_Locbase<int>::numeric ! d   std::_Locbase<int>::time % d    std::_Locbase<int>::messages   d  ? std::_Locbase<int>::all ! d    std::_Locbase<int>::none    �   AT  l    std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment  d    LightType_None  d   LightType_Directional  d   LightType_Spot  d   LightType_Point c    std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment  僒   std::_Consume_header  僒   std::_Generate_header    ��I@donut::math::PI_f "   
�-DT�!	@donut::math::PI_d !   ��7�5donut::math::epsilon "   �  �donut::math::infinity    �  �donut::math::NaN 7 �   std::atomic<unsigned int>::is_always_lock_free 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified + �   donut::math::vector<double,3>::DIM  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment + �   donut::math::vector<double,4>::DIM O    std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment M    std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment - d    std::integral_constant<int,0>::value W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified %    std::ctype<char>::table_size _    std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified O    std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources Z d   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size ) �   donut::math::vector<bool,4>::DIM M    std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R d   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T d   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos Z    std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos   �        nvrhi::EntireBuffer   d   std::_Iosb<int>::skipws ! d   std::_Iosb<int>::unitbuf # d   std::_Iosb<int>::uppercase " d   std::_Iosb<int>::showbase # d   std::_Iosb<int>::showpoint ! d    std::_Iosb<int>::showpos  d  @ std::_Iosb<int>::left  d  � std::_Iosb<int>::right " d   std::_Iosb<int>::internal  d   std::_Iosb<int>::dec  d   std::_Iosb<int>::oct  d   std::_Iosb<int>::hex $ d   std::_Iosb<int>::scientific  d    std::_Iosb<int>::fixed " d   0std::_Iosb<int>::hexfloat # d   @std::_Iosb<int>::boolalpha " d  � �std::_Iosb<int>::_Stdio % d  �std::_Iosb<int>::adjustfield # d   std::_Iosb<int>::basefield $ d   0std::_Iosb<int>::floatfield ! d    std::_Iosb<int>::goodbit   d   std::_Iosb<int>::eofbit ! d   std::_Iosb<int>::failbit   d   std::_Iosb<int>::badbit  d   std::_Iosb<int>::in  d   std::_Iosb<int>::out  d   std::_Iosb<int>::ate  d   std::_Iosb<int>::app  d   std::_Iosb<int>::trunc # d  @ std::_Iosb<int>::_Nocreate $ d  � std::_Iosb<int>::_Noreplace   d    std::_Iosb<int>::binary  d    std::_Iosb<int>::beg  d   std::_Iosb<int>::cur  d   std::_Iosb<int>::end , d  @ std::_Iosb<int>::_Default_open_prot L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos Z    std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment x    std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment  fk    Json::nullValue ' 砶   Json::numberOfCommentPlacement + E  	�       �Json::Value::minLargestInt + E  
��������Json::Value::maxLargestInt %    ��Json::Value::maxLargestUInt   d  �   �Json::Value::minInt   d  ����Json::Value::maxInt ! �  �����Json::Value::maxUInt & E  	�       �Json::Value::minInt64 & E  
��������Json::Value::maxInt64      ��Json::Value::maxUInt64 * �   Json::Value::defaultRealPrecision /   
�      餋Json::Value::maxUInt64AsDouble - �    std::chrono::system_clock::is_steady $ E   std::ratio<1,10000000>::num ( E  ��枠 std::ratio<1,10000000>::den c    std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment :    std::integral_constant<unsigned __int64,1>::value  E   std::ratio<1,1>::num  E   std::ratio<1,1>::den �    std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment J E   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N E  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( E  ��枠 std::ratio<10000000,1>::num $ E   std::ratio<10000000,1>::den P E  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < E  ��枠 std::integral_constant<__int64,10000000>::value 1 E   std::integral_constant<__int64,1>::value �    std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment Z    std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment - �   std::chrono::steady_clock::is_steady & E   std::ratio<1,1000000000>::num * E  � 蕷;std::ratio<1,1000000000>::den W    std::allocator<Json::PathArgument const *>::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment   E  std::ratio<3600,1>::num   E   std::ratio<3600,1>::den :     std::integral_constant<unsigned __int64,0>::value ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) x5    std::_Invoker_functor::_Strategy L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P E  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy , x5   std::_Invoker_pmf_object::_Strategy - x5   std::_Invoker_pmf_refwrap::_Strategy B    std::allocator<float>::_Minimum_asan_allocation_alignment t   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size - x5   std::_Invoker_pmf_pointer::_Strategy t   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi , x5   std::_Invoker_pmd_object::_Strategy - x5   std::_Invoker_pmd_refwrap::_Strategy O    std::allocator<Json::PathArgument>::_Minimum_asan_allocation_alignment - x5   std::_Invoker_pmd_pointer::_Strategy T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos  E  < std::ratio<60,1>::num  E   std::ratio<60,1>::den . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM '         donut::math::colors::white *         donut::math::lumaCoefficients �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment * �   donut::math::vector<float,2>::DIM + <        nvrhi::rt::c_IdentityTransform   E   std::ratio<1,1000>::num   E  �std::ratio<1,1000>::den  �2    std::denorm_absent  �2   std::denorm_present  �2    std::round_toward_zero  �2   std::round_to_nearest # �2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �2    std::_Num_base::round_style  d    std::_Num_base::digits ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix q    std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >::_Minimum_asan_allocation_alignment ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix . d   donut::math::box<float,3>::numCorners ) �2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �2   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix � �    std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0>::_Multi * d   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment # E   std::ratio<1,1000000>::num ' E  �@B std::ratio<1,1000000>::den 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 �    std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> >::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE � �    std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Multi ) �   donut::math::frustum::numCorners � �    std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Is_set D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask 0 �   std::numeric_limits<wchar_t>::is_modulo O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity - d   std::numeric_limits<wchar_t>::digits / d   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits - d   std::numeric_limits<short>::digits10 a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits , d  	 std::numeric_limits<long>::digits10 : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - d  ? std::numeric_limits<__int64>::digits � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / d   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask �    std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 j    std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10    �   Y  �    std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent    �   =   ; d  �威std::numeric_limits<long double>::min_exponent10    �   :: � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable 3 Q  \ std::filesystem::path::preferred_separator  �4  _CatchableType " 24  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & >4  $_TypeDescriptor$_extraBytes_24 6 K6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  甋  _Ctypevec & �5  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - C6  __vc_attributes::event_sourceAttribute 9 <6  __vc_attributes::event_sourceAttribute::optimize_e 5 :6  __vc_attributes::event_sourceAttribute::type_e > 86  __vc_attributes::helper_attributes::v1_alttypeAttribute F 36  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 06  __vc_attributes::helper_attributes::usageAttribute B ,6  __vc_attributes::helper_attributes::usageAttribute::usage_e * )6  __vc_attributes::threadingAttribute 7 "6  __vc_attributes::threadingAttribute::threading_e - 6  __vc_attributes::aggregatableAttribute 5 6  __vc_attributes::aggregatableAttribute::type_e / 6  __vc_attributes::event_receiverAttribute 7 6  __vc_attributes::event_receiverAttribute::type_e ' 	6  __vc_attributes::moduleAttribute /  6  __vc_attributes::moduleAttribute::type_e  沄  __std_fs_find_data & i4  $_TypeDescriptor$_extraBytes_23 - �4  $_s__CatchableTypeArray$_extraBytes_32 # 蔥  __std_fs_reparse_data_buffer Z Qi  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ Ni  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` Li  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  烿  __std_fs_dir_handle  軷  __std_access_rights  �5  _TypeDescriptor & �4  $_TypeDescriptor$_extraBytes_34 	 �  tm % :4  _s__RTTICompleteObjectLocator2  脯  LightConstants & 乹  $_TypeDescriptor$_extraBytes_30 & G6  $_TypeDescriptor$_extraBytes_29 A �5  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �4  _s__CatchableType & �4  $_TypeDescriptor$_extraBytes_19 & �4  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �5  __vcrt_va_list_is_reference<wchar_t const * const>  瀁  __std_fs_filetime E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & K4  $_TypeDescriptor$_extraBytes_20  涓  ShadowConstants  p  va_list - �4  $_s__CatchableTypeArray$_extraBytes_16   餥  __std_fs_copy_file_result  tV  __std_code_page � 薷  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w 喔  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > �   std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > d �=  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c 细  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h 迅  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G >  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y 谈  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > c Y�  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a '<  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 喾  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ] i;  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ oK  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 栏  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 陡  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ >  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > �   std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > 澑  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 暩  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � �=  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W尭  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> �  ;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 
K  std::_Default_allocator_traits<std::allocator<float> > ; >�  std::hash<std::shared_ptr<donut::engine::Material> > � 胺  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > ~吀  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ {�  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � r�  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ �  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C G:  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 龇  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 僈  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � &�  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � 
;  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C :  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � [;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | �=  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 夥  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ? 臃  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 �  std::allocator<donut::engine::SkinnedMeshJoint> M �=  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L >  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s >  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 暦  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 畏  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T {K  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > � 挤  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � 卜  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U qK  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 旹  std::_Ptr_base<donut::engine::LoadedTexture> : �;  std::_Vector_val<std::_Simple_types<unsigned int> > D >  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 虼  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � >  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 濱  std::_Ptr_base<donut::engine::DescriptorHandle> � |;  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~》  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e '�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U >K  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > � 2s  std::_Compressed_pair<std::default_delete<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> >,std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> *,1> "T8  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W T�  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > l (s  std::_Default_allocator_traits<std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > > d亩  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > U 馢  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w 椃  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � 埛  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y q�  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � 鷕  std::_Default_allocator_traits<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> > > c *s  std::allocator_traits<std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > > 4 cK  std::allocator<donut::math::vector<float,2> > M e�  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = *K  std::allocator<donut::math::vector<unsigned short,4> > K TK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U 塈  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 蘆  std::_Ptr_base<donut::engine::BufferGroup> F;  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ 谴  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � ;:  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h [�  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e I  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N V�  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > ' 聄  std::less<Json::Value::CZString> { �=  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l 愦  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > , 3:  std::allocator<nvrhi::BindingSetItem> K JK  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � H�  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # �:  std::allocator<unsigned int> � wr  std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >,std::_Iterator_base0> � :�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J I  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � :  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � 璧  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> �   std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > D �=  std::_Default_allocator_traits<std::allocator<unsigned int> > g c�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L @K  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  uI  std::allocator<float> � 2K  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> o s  std::default_delete<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> > K  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � K  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t � 3r  std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> > � 舚  std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Strategy � 秖  std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Redbl 4 K  std::allocator_traits<std::allocator<float> > c s  std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> > [ O;  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > l 2�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > w 6�  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > ; �=  std::allocator_traits<std::allocator<unsigned int> > [ K  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 H�  std::_Ptr_base<donut::engine::SceneTypeFactory> � 妑  std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > > > ; &�  std::hash<std::shared_ptr<donut::engine::MeshInfo> > � s  std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >,1> W"�  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>  齬  std::_Default_sentinel � 09  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H y�  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ 鄌  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 焈  std::default_delete<wchar_t [0]> . �'  std::_Conditionally_enabled_hash<int,1> A 癦  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> � �  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 暣  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X �  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? �3  std::_Default_allocator_traits<std::allocator<wchar_t> > . �2  std::integer_sequence<unsigned __int64>  .  std::_Lockit � ?�  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > � �  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy * 2/  std::hash<enum nvrhi::ResourceType> / C�  std::shared_ptr<donut::engine::Material> - #W  std::reverse_iterator<wchar_t const *> 5 墺  std::shared_ptr<donut::engine::SceneGraphNode> 9 wj  std::shared_ptr<donut::engine::animation::Sampler> " i3  std::_Char_traits<char,int>  S  std::_Fs_file � 鱆  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �2  std::_Num_base & �'  std::hash<std::error_condition> K �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R �  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >  y$  std::_Big_uint128 / �  std::weak_ptr<donut::engine::SceneGraph> �  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) v3  std::_Narrow_char_traits<char,int> L 驤  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 3q  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >    std::hash<float> 6 B0  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 9�  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > >  �'  std::hash<int>  �2  std::_Num_int_base  wU  std::ctype<wchar_t> " k(  std::_System_error_category � ?�  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / Q/  std::_Conditionally_enabled_hash<bool,1> 2 錔  std::shared_ptr<donut::engine::BufferGroup> � �8  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �2  std::float_denorm_style 4 璄  std::shared_ptr<donut::engine::LoadedTexture> 鈍  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! %0  std::piecewise_construct_t u f  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � #�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . 5I  std::_Ptr_base<donut::engine::MeshInfo> 6 �5  std::allocator_traits<std::allocator<wchar_t> > � �  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1> 0 篆  std::shared_ptr<donut::engine::SpotLight>  &  std::bad_cast B 敠  std::enable_shared_from_this<donut::engine::SceneGraphNode>  Fc  std::equal_to<void> 4 際  std::allocator<donut::math::vector<float,4> > � 圶  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } -�  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > �   std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 笿  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 嘕  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o 踥  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � +m  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > " �2  std::numeric_limits<double>  <&  std::__non_rtti_object < �6  std::_Ptr_base<donut::engine::DescriptorTableManager> ( n  std::_Basic_container_proxy_ptr12 4 BJ  std::allocator<donut::math::vector<float,3> > � 榥  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � 潷  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � l�  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > �:  std::vector<unsigned int,std::allocator<unsigned int> > T �:  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy { 黵  std::allocator_traits<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> > > 1 �  std::array<nvrhi::FramebufferAttachment,8> T �  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �2  std::_Num_float_base �   std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  �&  std::logic_error 3 ％  std::weak_ptr<donut::engine::SceneGraphNode> � 薾  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � io  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 /  std::_Conditionally_enabled_hash<unsigned int,1> G :/  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  r&  std::pointer_safety P 3J  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f �7  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �5  std::char_traits<char32_t>  T  std::locale  HT  std::locale::_Locimp  $T  std::locale::facet   ,T  std::locale::_Facet_guard  軸  std::locale::id ? 娴  std::allocator_traits<std::allocator<unsigned __int64> > : 	�  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 煯  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s 塮  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z 涞  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   �2  std::numeric_limits<bool> # �3  std::_WChar_traits<char16_t> _ /J  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u 蘒  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy � 雛  std::unique_ptr<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3>,std::default_delete<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> > > 藃  std::_Compressed_pair<std::less<Json::Value::CZString>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >,1>,1> P 觃  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl . �  std::_Ptr_base<donut::engine::Material> * �2  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> Z 磂  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M 
W  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � J0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  '  std::overflow_error d 诘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z ǖ  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy 5   std::_Ref_count_obj2<donut::engine::SpotLight> % --  std::_One_then_variadic_args_t D �/  std::_Constexpr_immortalize_impl<std::_System_error_category> W L2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * �8  std::_Vb_val<std::allocator<bool> > E 廩  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j Gh  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �5  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 m0  std::allocator<nvrhi::rt::PipelineShaderDesc> 7 a�  std::shared_ptr<donut::engine::SceneTypeFactory> � 縄  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � $�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' X�  std::allocator<unsigned __int64>  �5  std::false_type < 丿  std::_Ref_count_obj2<donut::engine::DirectionalLight>  �2  std::float_round_style T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ��  std::_Default_allocator_traits<std::allocator<unsigned __int64> >  X  std::string B �5  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 沇  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , d  std::array<nvrhi::BindingSetItem,128> � u0  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � 榪  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > � �0  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 稩  std::shared_ptr<donut::engine::DescriptorHandle> , �2  std::numeric_limits<unsigned __int64> � Em  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  vS  std::_Locinfo 6 罽  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L 婭  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \  4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s [e  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H Q�  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> $ �2  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> e 絩  std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > > m 皉  std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >::_Redbl  �  std::string_view    std::wstring_view % �5  std::integral_constant<bool,1>   _  std::_Leave_proxy_unbound � o  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > b }I  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  WV  std::money_base  h  std::money_base::pattern  FS  std::_Timevec D gI  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >   a'  std::_Init_once_completer 0 癍  std::_Ptr_base<donut::engine::PointLight> j "[  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � 馴  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + 諸  std::codecvt<wchar_t,char,_Mbstatet> h �,  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q h  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 j +j  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � 鵬  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  4a  std::_Pocma_values 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> ! �'  std::hash<std::error_code> N 賅  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �3  std::_Default_allocator_traits<std::allocator<char32_t> >  �,  std::allocator<char32_t> ? 耓  std::unique_ptr<char [0],std::default_delete<char [0]> > � *o  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ �  std::_Atomic_integral<long,4> � 1�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  U/  std::hash<bool>     std::streamsize 6 �-  std::_String_val<std::_Simple_types<char32_t> > = �-  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` M-  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ )�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > > 愆  std::enable_shared_from_this<donut::engine::SceneGraph> K XI  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > d,�  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >  (  std::hash<long double> � PX  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � X  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy / NI  std::shared_ptr<donut::engine::MeshInfo> U 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �2  std::numeric_limits<wchar_t>  �  std::_Container_base0    std::hash<double> 5 僻  std::shared_ptr<donut::engine::SceneGraphLeaf> O (4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & m5  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> , G�  std::allocator<std::_Container_proxy> / �3  std::_Char_traits<char32_t,unsigned int>  �'  std::_System_error ( 1$  std::hash<nvrhi::FramebufferInfo> 9 縚  std::allocator<std::filesystem::_Find_file_handle>  �'  std::error_condition % �5  std::integral_constant<bool,0>  .  std::bad_exception & �,  std::_Zero_then_variadic_args_t K 玶  std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > d 沾  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ �  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � �  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string 歳  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / f  std::array<nvrhi::BindingLayoutItem,128>  �&  std::invalid_argument N �5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U f2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 榬  std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > > > S �5  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 !I  std::_Vector_val<std::_Simple_types<float> > R �,  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A I  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + \  std::pair<enum __std_win_error,bool> � 舸  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > S 齎  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �&  std::length_error \ 	I  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F e  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 莀  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �2  std::numeric_limits<float>  cV  std::time_base   ]V  std::time_base::dateorder � br  std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> ) �  std::_Atomic_integral_facade<long> 8 `j  std::_Ptr_base<donut::engine::animation::Sampler> % m/  std::hash<enum nvrhi::BlendOp> c 宕  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B �  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  j&  std::_Ref_count_base " .  std::hash<unsigned __int64>  �5  std::ratio<60,1>  t  std::exception_ptr  �5  std::ratio<1,1000000> [ 状  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) a/  std::hash<enum nvrhi::BlendFactor> Wo  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 嫳  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �2  std::numeric_limits<char32_t>  Y'  std::once_flag  �'  std::error_code J �6  std::enable_shared_from_this<donut::engine::DescriptorTableManager>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < h  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  錟  std::_Iosb<int>   酻  std::_Iosb<int>::_Seekdir ! 遀  std::_Iosb<int>::_Openmode   軺  std::_Iosb<int>::_Iostate ! 踀  std::_Iosb<int>::_Fmtflags # 賃  std::_Iosb<int>::_Dummy_enum 7 �5  std::allocator_traits<std::allocator<char32_t> >  {5  std::nano  �  std::_Iterator_base0 | 鸋  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � 宯  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U 纱  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0kn  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M V0  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 
�  std::shared_ptr<donut::engine::PointLight> 1 �3  std::_Char_traits<char16_t,unsigned short> $ �#  std::hash<nvrhi::BufferRange> T 蒞  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  銼  std::_Locbase<int> ! �5  std::char_traits<char16_t> Ko  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ 淮  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >  |  std::tuple<>    std::_Container_base12 W 齡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - '�  std::weak_ptr<donut::engine::Material>  �'  std::io_errc  V  std::ios_base  ,V  std::ios_base::_Fnarray  &V  std::ios_base::_Iosarray  覷  std::ios_base::Init  芔  std::ios_base::failure  鏤  std::ios_base::event E �/  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 �2  std::integer_sequence<unsigned __int64,0> ) �2  std::numeric_limits<unsigned char> � �,  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �5  std::true_type 6 }�  std::_Ptr_base<donut::engine::DirectionalLight>   �2  std::numeric_limits<long> " �5  std::initializer_list<char> 2 绡  std::_Wrap<donut::engine::DirectionalLight> N m�  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X 贝  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  x5  std::_Invoker_strategy  榌  std::nothrow_t � 驢  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 鬵  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T   std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �2  std::_Default_allocate_traits � 挳  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � `r  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N 玏  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 �5  std::allocator_traits<std::allocator<char> > 0   std::_Ptr_base<donut::engine::IShadowMap> ! �2  std::numeric_limits<short>  u   std::_Vbase . �0  std::allocator<nvrhi::rt::GeometryDesc> ( aE  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > c 柋  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > ! ︰  std::ctype<unsigned short> C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > � u�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 �-  std::_String_val<std::_Simple_types<char16_t> > = �-  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 "�  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9!q  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � m�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ e�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O 隻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P nW  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . >/  std::hash<enum nvrhi::TextureDimension> ! y&  std::_Shared_ptr_spin_lock Y 0�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  D  std::bad_alloc  /'  std::underflow_error B ]/  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  蹾  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � 狧  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 祅  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J   std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D W�  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 昣  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  nV  std::messages_base  �&  std::out_of_range # �2  std::numeric_limits<__int64> _ eH  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 4H  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i `  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b H�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > >�  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  >U  std::ctype<char> @ 鯣  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s ２  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P Rm  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? "�  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  �  std::memory_order � ^r  std::map<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > > Z �  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � �  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > q が  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � r�  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  si  std::nullopt_t  ui  std::nullopt_t::_Tag  �5  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f -  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >   (  std::system_error < T3  std::_Default_allocator_traits<std::allocator<char> > W 4  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � Ao  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  �5  std::ratio<1,1>   k5  std::forward_iterator_tag  '  std::runtime_error � ﹒  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >   Z  std::bad_array_new_length E �0  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 >m  std::allocator<donut::engine::animation::Keyframe> + �  std::_Wrap<donut::engine::SpotLight> K 〕  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >  峉  std::_Yarn<char> 6 �  std::_Ref_count_obj2<donut::engine::PointLight>    std::_Container_proxy ( 鈌  std::_Facetptr<std::ctype<char> > Z 攆  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  �9  std::allocator<bool> � m  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  �  std::u16string _ 霨  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 籊  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � sn  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *>  �  std::nested_exception  r  std::_Distance_unknown ) }G  std::allocator<nvrhi::BufferRange> ( �2  std::numeric_limits<unsigned int> < 馾  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , 稵  std::codecvt<char32_t,char,_Mbstatet> 1 �  std::shared_ptr<donut::engine::IShadowMap> C f�  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ u/  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F 嫯  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 nG  std::vector<float,std::allocator<float> > F <G  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 荮  std::_Ptr_base<donut::engine::SceneGraph>    std::atomic<long> & �5  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �5  std::initializer_list<char16_t> % �5  std::initializer_list<wchar_t> C ./  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' �2  std::numeric_limits<long double>  �'  std::errc } q�  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J 暡  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  血  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 煪  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy � 惓  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , Ad  std::default_delete<std::_Facet_base>  F'  std::range_error  $&  std::bad_typeid > i/  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  {5  std::ratio<1,1000000000>  �,  std::allocator<char16_t> $ 巁  std::default_delete<char [0]> C 埑  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > .  G  std::vector<bool,std::allocator<bool> > J 麱  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 薋  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J ~W  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  v5  std::ratio<1,1000> i y�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >  t5  std::ratio<1,10000000> o�  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; �,  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d 俻  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  鍿  std::_Crt_new_delete % ^(  std::_Iostream_error_category2 * r5  std::_String_constructor_concat_tag j ゲ  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A 棽  std::allocator_traits<std::allocator<unsigned __int64 *> >  D-  std::allocator<char> T 啿  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> G �/  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> ) ?�  std::allocator<unsigned __int64 *>    std::nullptr_t =k  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > L騩  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K渙  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & o5  std::random_access_iterator_tag ; 
.  std::_Conditionally_enabled_hash<unsigned __int64,1> R `  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  T&  std::bad_weak_ptr ) �2  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> @ 橺  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>    std::_Yarn<wchar_t> = E/  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>    std::wstring ' �2  std::numeric_limits<signed char> � wX  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � to  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  �&  std::domain_error  �  std::u32string_view � m  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool> 7 柇  std::shared_ptr<donut::engine::DirectionalLight>  �  std::_Container_base 1 酾  std::shared_ptr<donut::engine::SceneGraph>  -  std::allocator<wchar_t> / 经  std::_Ptr_base<donut::engine::SpotLight> L W5  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > {   std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 桶  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z =q  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; �6  std::weak_ptr<donut::engine::DescriptorTableManager> $ &/  std::hash<nvrhi::IResource *> 4 哀  std::_Ptr_base<donut::engine::SceneGraphLeaf> , %�  std::_Wrap<donut::engine::PointLight> " 抜  std::_Nontrivial_dummy_type � 鰄  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   �2  std::numeric_limits<char> 9 o%  std::chrono::duration<__int64,std::ratio<1,1000> >  �$  std::chrono::nanoseconds y $S  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �$  std::chrono::duration<__int64,std::ratio<1,1000000000> > , f  std::chrono::duration_values<__int64>  �$  std::chrono::seconds 3 -%  std::chrono::duration<int,std::ratio<60,1> > 6 �$  std::chrono::duration<__int64,std::ratio<1,1> > s �$  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   U5  std::chrono::steady_clock   R5  std::chrono::system_clock 6 B%  std::chrono::duration<double,std::ratio<60,1> > ; �%  std::chrono::duration<double,std::ratio<1,1000000> > > �%  std::chrono::duration<double,std::ratio<1,1000000000> > = �$  std::chrono::duration<__int64,std::ratio<1,10000000> > q �$  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 %  std::chrono::duration<int,std::ratio<3600,1> > 8 �%  std::chrono::duration<double,std::ratio<1,1000> > < �%  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 Y%  std::chrono::duration<double,std::ratio<1,1> > 8 %  std::chrono::duration<double,std::ratio<3600,1> >  U  std::ctype_base  
Y  std::filesystem::perms ' 俌  std::filesystem::directory_entry $ Y  std::filesystem::copy_options ( 齒  std::filesystem::filesystem_error 7 ic  std::filesystem::_Path_iterator<wchar_t const *> ) 癡  std::filesystem::_Find_file_handle & 俈  std::filesystem::_Is_slash_oper . 乑  std::filesystem::_Should_recurse_result $ 8\  std::filesystem::perm_options 4 K[  std::filesystem::recursive_directory_iterator . DY  std::filesystem::_File_status_and_error & 験  std::filesystem::_Dir_enum_impl 0 Z  std::filesystem::_Dir_enum_impl::_Creator @ 
Z  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! Y  std::filesystem::file_type . (Z  std::filesystem::_Directory_entry_proxy " *\  std::filesystem::space_info * OZ  std::filesystem::directory_iterator & $S  std::filesystem::file_time_type 0 Z  std::filesystem::_Recursive_dir_enum_impl )   std::filesystem::directory_options # /Y  std::filesystem::file_status u 稾  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 輋  std::filesystem::_File_time_clock  RW  std::filesystem::path $ 萔  std::filesystem::path::format * =c  std::filesystem::_Normal_conversion < !e  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , 擳  std::codecvt<char16_t,char,_Mbstatet>  K5  std::char_traits<char> � `  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �'  std::error_category ) �'  std::error_category::_Addr_storage � 东  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � 劔  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! >(  std::_System_error_message  k  std::_Unused_parameter = M�  std::allocator<std::shared_ptr<donut::engine::Light> > h �,  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  奿  std::bad_optional_access A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 赮  std::shared_ptr<std::filesystem::_Dir_enum_impl> = "/  std::_Conditionally_enabled_hash<nvrhi::IResource *,1>  僒  std::_Codecvt_mode @ �3  std::_Default_allocator_traits<std::allocator<char16_t> > � 歲  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  -2  std::_Exact_args_t � 蝀  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > Q 晀  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 �3  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> � 搎  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �1  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5  -  std::_String_val<std::_Simple_types<wchar_t> > < �-  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  5S  std::_Facet_base b >�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' �#  std::hash<nvrhi::BindingSetItem> " �3  std::_WChar_traits<wchar_t> 2 齌  std::codecvt<unsigned short,char,_Mbstatet> c 4�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z Ci  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # P(  std::_Generic_error_category  U3  std::streampos  i5  std::input_iterator_tag 2 e  std::_Wrap<std::filesystem::_Dir_enum_impl> � 雪  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 煩  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X Id  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> �<k  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ' y/  std::hash<enum nvrhi::ColorMask>  pT  std::codecvt_base  淜  std::bad_function_call O �2  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 颔  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ' _\  std::hash<std::filesystem::path>  /  std::hash<unsigned int> 7 G5  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 q�  std::_Ptr_base<donut::engine::SceneGraphNode> � )q  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F E5  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � Bp  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> . �  std::array<nvrhi::BindingLayoutItem,16> $ I/  std::hash<enum nvrhi::Format>  �2  std::numeric_limits<int> �鰊  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E 皑  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O 摠  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U 挩  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 _-  std::_String_val<std::_Simple_types<char> > 9 �-  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access 
 !   wint_t & 灡  $_TypeDescriptor$_extraBytes_40 # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  ;  nvrhi::BindingSetDesc  A5  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   =5  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " -  nvrhi::VulkanBindingOffsets  j   nvrhi::GraphicsState / �  nvrhi::static_vector<nvrhi::Viewport,16>    nvrhi::ShaderDesc  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc    nvrhi::Rect  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) I*  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline ! #  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  I*  nvrhi::DeviceHandle   75  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  35  nvrhi::IStagingTexture . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  B  nvrhi::TextureDimension 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments  #  nvrhi::SamplerHandle * ;!  nvrhi::DrawIndexedIndirectArguments # =7  nvrhi::DescriptorTableHandle  "#  nvrhi::TimerQueryHandle 2 =7  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �"  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  *5  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet  &5  nvrhi::TileShape ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - `+  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem  -#  nvrhi::FramebufferHandle 1 2  nvrhi::static_vector<enum nvrhi::Format,8>  岶  nvrhi::BufferHandle  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  �?  nvrhi::TextureHandle  "5  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  5  nvrhi::IMessageCallback  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 岶  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 5  nvrhi::TiledTextureCoordinate  5  nvrhi::IHeap # g  nvrhi::FramebufferAttachment  �   nvrhi::BindingSetVector  `+  nvrhi::BindingSetHandle ( �4  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �4  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 �4  nvrhi::rt::cluster::OperationBlasBuildParams . �4  nvrhi::rt::cluster::OperationMoveParams ( �4  nvrhi::rt::cluster::OperationDesc 3 �4  nvrhi::rt::cluster::OperationClasBuildParams , �4  nvrhi::rt::cluster::OperationSizeInfo * �4  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # gF  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �4  nvrhi::rt::IPipeline  W#  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �4  nvrhi::TextureTilesMapping  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �4  nvrhi::IDescriptorTable * �?  nvrhi::RefCountPtr<nvrhi::ITexture>  H!  nvrhi::ComputeState 2 gF  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �   nvrhi::IFramebuffer  �  nvrhi::Viewport  �  nvrhi::RenderState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �4  nvrhi::ITimerQuery  肦  __std_win_error  稴  lconv   24  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  �  timespec  �[  __std_fs_file_id 
 !   _ino_t ' 鑋  __std_fs_create_directory_result  !   uint16_t  誖  __std_fs_stats * 堀  donut::engine::SkinnedMeshReference   )�  donut::engine::LightProbe ! 1�  donut::engine::SceneCamera   /�  donut::engine::IShadowMap $ 挺  donut::engine::SceneGraphNode 0 枽  donut::engine::SceneGraphNode::DirtyFlags " 触  donut::engine::MeshInstance ) 啸  donut::engine::SkinnedMeshInstance   &�  donut::engine::SceneGraph > Z�  donut::engine::ResourceTracker<donut::engine::MeshInfo> ( 亘  donut::engine::AnimationAttribute $ ^�  donut::engine::SceneGraphLeaf ! :E  donut::engine::BufferGroup  譋  donut::engine::Material * 駿  donut::engine::Material::HairParams 0 隕  donut::engine::Material::SubsurfaceParams  J�  donut::engine::Light ' R�  donut::engine::SceneContentFlags  zE  donut::engine::MeshInfo & [�  donut::engine::DirectionalLight & 台  donut::engine::SceneGraphWalker ( 穒  donut::engine::animation::Sampler ) Dl  donut::engine::animation::Keyframe ) Mj  donut::engine::animation::Sequence  iE  donut::engine::MeshType  k�  donut::engine::SpotLight & �6  donut::engine::DescriptorHandle , 7  donut::engine::DescriptorTableManager B �6  donut::engine::DescriptorTableManager::BindingSetItemsEqual B �6  donut::engine::DescriptorTableManager::BindingSetItemHasher % $E  donut::engine::VertexAttribute 0   donut::engine::SceneGraphAnimationChannel % t   donut::engine::DescriptorIndex > @�  donut::engine::ResourceTracker<donut::engine::Material>   {�  donut::engine::PointLight ) `�  donut::engine::SceneGraphAnimation $ 翬  donut::engine::MaterialDomain  驛  donut::math::float4x4 " 2@  donut::math::vector<bool,4>    donut::math::float3  楻  donut::math::float3x4  Q@  donut::math::affine3  薧  donut::math::box2  
B  donut::math::float2  瘭  donut::math::dquat #   donut::math::vector<float,3> * F  donut::math::vector<unsigned int,3>  蕃  donut::math::int4  u   donut::math::uint  闌  donut::math::plane ! 蕃  donut::math::vector<int,4> &  �  donut::math::matrix<double,4,4>  &�  donut::math::daffine3  �  donut::math::double3 # 蜙  donut::math::vector<float,4> $ �  donut::math::vector<double,3>  A  donut::math::frustum $ 鰼  donut::math::frustum::Corners # 魼  donut::math::frustum::Planes % 楻  donut::math::matrix<float,3,4> &   donut::math::matrix<double,3,4>  贡  donut::math::uint2 $ 荼  donut::math::vector<double,4>  F  donut::math::uint3  蜙  donut::math::float4 & \�  donut::math::matrix<double,3,3>  x�  donut::math::int2 % 驛  donut::math::matrix<float,4,4> # Q@  donut::math::affine<float,3>   袮  donut::math::box<float,3> " �?  donut::math::vector<bool,2>  袮  donut::math::box3 % |@  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # 
B  donut::math::vector<float,2> * 贡  donut::math::vector<unsigned int,2> $ &�  donut::math::affine<double,3> & 瘭  donut::math::quaternion<double> M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet  a  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �4  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t  ]  terminate_handler  �4  _s__RTTIBaseClassArray 
 H  ldiv_t  釸  __std_fs_file_flags  砈  _Cvtvec - C4  $_s__RTTIBaseClassArray$_extraBytes_24  軪  MaterialConstants  e4  _CatchableTypeArray  鏡  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  �4  _PMD      uint8_t  �%  type_info ' P4  _s__RTTIClassHierarchyDescriptor  t   errno_t  襌  __std_fs_reparse_tag  K  _lldiv_t  铂  InstanceData  �%  __std_type_info_data & |4  $_TypeDescriptor$_extraBytes_27  fk  Json::ValueType  l  Json::ValueIteratorBase  #l  Json::ValueConstIterator  砶  Json::CommentPlacement  #   Json::LargestUInt     Json::LargestInt  u   Json::UInt  u   Json::ArrayIndex     Json::Int64  騥  Json::PathArgument  阫  Json::PathArgument::Kind  衚  Json::Value  }q  Json::Value::Comments ( 謐  Json::Value::<unnamed-type-bits_>  dq  Json::Value::ValueHolder  [q  Json::Value::CZString + _q  Json::Value::CZString::StringStorage / ?q  Json::Value::CZString::DuplicationPolicy  Fk  Json::StaticString  X  Json::String  t   Json::Int  7l  Json::ValueIterator  #   Json::UInt64  �  _s__ThrowInfo  S  __std_fs_convert_result  蔙  __std_fs_stats_flags  �4  __RTTIBaseClassArray  �  __crt_locale_data_public - q4  $_s__CatchableTypeArray$_extraBytes_24 & _4  $_TypeDescriptor$_extraBytes_25 % P4  __RTTIClassHierarchyDescriptor  ⊿  _Collvec   j[  __std_fs_volume_name_kind     __time64_t  m  FILE 3 v4  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  �  _PMFN  #   uintptr_t  e4  _s__CatchableTypeArray  鋄  __std_fs_remove_result  0�  LightProbeConstants - W4  $_s__RTTIBaseClassArray$_extraBytes_16 - G4  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  臨  __std_fs_file_attr  
  __std_exception_data 
 u   _dev_t  b[  __std_ulong_and_error  K  lldiv_t  H  _ldiv_t  �  _timespec64     intptr_t  u   uint32_t 
 m  _iobuf  j  __crt_locale_pointers   �   x      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �    f扥�,攇(�
}2�祛浧&Y�6橵�  �    曀"�H枩U传嫘�"繹q�>窃�8  >   天e�1濎夑Y%� 褡\�Tā�%&閜�     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   �	玮媔=zY沚�c簐P`尚足,\�>:O     �"睱建Bi圀対隤v��cB�'窘�n  b   [届T藎秏1潴�藠?鄧j穊亘^a  �   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   揾配饬`vM|�%
犕�哝煹懿鏈椸  +   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  h   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  ,   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  j   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  5   dhl12� 蒑�3L� q酺試\垉R^{i�  t   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�      d蜯�:＠T邱�"猊`�?d�B�#G騋  Q   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   经H臣8v;注诶�#��
夔A17�	迒�#k_  �   存*?\��-矪q7o責覃:},p穿奵�     o忍x:筞e飴刌ed'�g%X鶩赴5�n�  g   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  @   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  	   缱S炦噄�<^敾R}肸(3倁説�  I	   eSO僌rM騮纚坵*L犁�L宵�)魐�  �	   &'迆�衑5r磙払|鯈�1貺#�8}��"�  �	   L�9[皫zS�6;厝�楿绷]!��t  �	   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  1
   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  r
   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  �
   �0�*е彗9釗獳+U叅[4椪 P"��  �
   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  &   憒峦锴摦懣苍劇o刦澬z�/s▄![�  e   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   煋�	y鋵@$5х葑愔*濋>�( 懪銳     譫鰿3鳪v鐇�6瘻x侃�h�3&�  Y   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �   j轲P[塵5m榤g摏癭 鋍1O骺�*�  $
   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  [
   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �
   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �
   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�     <峰隁謠]匔唗~朽k髭妖哹�5麦
T  T   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   繃S,;fi@`騂廩k叉c.2狇x佚�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  !   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  g   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  (   �*o驑瓂a�(施眗9歐湬

�  p   咡68[�沘謎7
瑫,j蟫堢>�`~乐�#  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  .   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  k   猯�諽!~�:gn菾�]騈购����'  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  C   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   鹴y�	宯N卮洗袾uG6E灊搠d�     Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  d   妇舠幸佦郒]泙茸餈u)	�位剎  �   靋!揕�H|}��婡欏B箜围紑^@�銵  �   �颠喲津,嗆y�%\峤'找_廔�Z+�  .   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  n   U聑9�麇鶐崘骙�	姳Y#=縐涂pW  �   t�j噾捴忊��
敟秊�
渷lH�#  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  2    婈涠鰞U暔c��� 鍶詘楞e�yぷ悋A  n   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  =   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   5�\營	6}朖晧�-w氌rJ籠騳榈  >   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �
bH<j峪w�/&d[荨?躹耯=�  =   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`     齝D屜u�偫[篔聤>橷�6酀嘧0稈  W   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  0   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�     觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  D   苶T$k俥獛觐扗諨攱;懤{訳氀�#+詴4  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说     +4[(広
倬禼�溞K^洞齹誇*f�5  p   渒�?^v)f启n?鶚鯼Y|縟痵5恰�]�  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  "   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  Z   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   ����lS窳艻BC唖�n�5鷂;需  &    狾闘�	C縟�&9N�┲蘻c蟝2  d   扝	_u赂-墉MmJ鋉�-;騂钟@  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   �'稌� 变邯D)\欅)	@'1:A:熾/�  /   cS�<蹪�1竰z舣?�[)Gwr �动  p   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   c�#�'�縌殹龇D兺f�$x�;]糺z�       鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  ?    鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �    交�,�;+愱`�3p炛秓ee td�	^,  �    
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  !   zY{���睃R焤�0聃
扨-瘜}  =!   _臒~I��歌�0蘏嘺QU5<蝪祰S  �!   郖�Χ葦'S詍7,U若眤�M进`  �!   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  "   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  O"   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �"   副謐�斦=犻媨铩0
龉�3曃譹5D   �"   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  #   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  g#   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �#   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �#   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  1$   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  y$   =J�(o�'k螓4o奇缃�
黓睆=呄k_  �$   _O縋[HU-銌�鼪根�鲋薺篮�j��  �$   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  R%   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �%   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �%   蜅�萷l�/费�	廵崹
T,W�&連芿  +&   v�%啧4壽/�.A腔$矜!洎\,Jr敎  u&   D���0�郋鬔G5啚髡J竆)俻w��  �&   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  '   匐衏�$=�"�3�a旬SY�
乢�骣�  P'   v峞M� {�:稚�闙蛂龣 �]<��  �'   悯R痱v 瓩愿碀"禰J5�>xF痧  �'   チ畴�
�&u?�#寷K�資 +限^塌>�j  (   矨�陘�2{WV�y紥*f�u龘��  ^(   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �(   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �(   穫農�.伆l'h��37x,��
fO��  )   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  U)   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �)   鏀q�N�&}
;霂�#�0ncP抝  �)   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  *   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  a*   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   �      T  H  B   U  H  H   V  H  Y   [  H  �   v    U   w    �   �  �  �  �  �  �  �  �  x  �  �  B  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   
  K     �  �    �  �    �  D
    �  �  !  �  O   "  �  0   9    �  �    �  �    �  �    �   �    �   �  �  �  �  �  �    �  s    �  �  t  �  )
  �    �   �    �      �  H  �  �    �  f   +  �  �   C  (   r   .  �  �  0  �  �  1  �     2  �  x  �  �  �  �   �  �  �   �  �	  )!  �  @
  �4  �  z   �4  �  ;   �4  �  >   �4  �  /   �4  �  :   �4  �  �  �4  �  �  �4  �  �  �4  �
      �4  �  �   �4  �  �   �4  �  l    5  �  f   5  �  �  	5  �  �  
5  �    5  �  �  5  �  �  
5  �  5  5  (   �   $5  �     &5  �  t  '5  �  t  (5  �  t  -5  �  �   .5  P   �   /5  �  t  55  �  �  65  �  t  ?5  �  <
  M5  P   �   R5  P   �   ]5  �  /  ^5  �  �   `5  �  �  a5  �  �   c5  �  �
  e5  �  �  f5  �  k   g5  �  :   h5    \   i5    \   j5    p   k5    p   l5  �  �
  n5  �  �  o5  �  �
  q5  �  �  x5  �  c  �5  �  :  �5  �  �  �5  �  5  �5  �  5  �5  �  5  �5  �  �   �5  P   �   �5  �  g   �5  P   �   �5  �  n   �5  �  +  �5  �  �   �5  P   k  �5  �  y  �5  �  i  �5  P   9  �5  �  �   �5  �  �   �5  �    �5  �  &  �5  �     �5  �  5  �5  �  9  �5  �  O  �5  �  6   �5  �  4   �5  �  &  �5  �  9  �5  �  O  �5  �  &  �5  �  9  �5  �  O  �5    /  �5    /  �5    /  �5  �  :  �5  �  :  �5  �  :  �5  �  5  �5  P   �   �5  P   �   �5  P   �   �5  �  `  �5  �  �  �5  �  �  �5  �  t   6    �  6  �  '  6  �  8   6   
  �   6  �  �   6  �  �   6  0  �   6  �  M  	6  0  �   
6  �  M  6  0  �   6  �  M  6    �  6    >  6  �  n  6  �  n  6  �  n  6  �  �   6    �  �   +   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtree D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\allocator.h D:\RTXPT\External\Donut\include\donut\shaders\light_cb.h D:\RTXPT\External\Donut\src\engine\SceneTypes.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\include\donut\engine\ShadowMap.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\RTXPT\External\Donut\include\donut\shaders\bindless.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\include\donut\shaders\material_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\version.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\RTXPT\External\Donut\include\donut\core\json.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\value.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\forwards.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_engine.dir\Release\SceneTypes.obj �       Lil  "Q  S   &Q  S  
 鞸  T   馭  T  
 !t  M   %t  M  
 Jt      Nt     
 tu      xu     
    j �oz鋩翹湻�菚蠒   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_engine.pdb 裥砓>Y7?樰�=      �?                  �?                  �?      �?  �?  �?@SH冹 H嬟�    �H兡 [�
   �      �   �   7 G                     i5        �operator>><float> 
 >Ok   node  AJ         
 >�*   dest  AI  	       AK        	 
 Z   96                         H  0   Ok  Onode  8   �*  Odest  O �   0                   $       \  �	   ]  �   ^  �,   �   0   �  
 \   �   `   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 @SH冹0H嬟L嬄H嬔H峀$ �    � �婡塁H兡0[�   �      �   �   O G            -      '   h5        �operator>><donut::math::vector<float,3> > 
 >Ok   node  AJ         
 >v@   dest  AI  	     #  AK        	 
 Z   :6   0                     H  @   Ok  Onode  H   v@  Odest  O �   0           -        $       \  �	   ]  �'   ^  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �       �      �   �   7 G                       k5        �operator<<<float> 
 >痠   node  AJ          >   src  AK         
 Z   76                          H     痠  Onode       Osrc  O   �   (                          p  �    q  �,   �   0   �  
 \   �   `   �  
 z   �   ~   �  
 �   �   �   �  
 �       �      �   �   O G                       j5        �operator<<<donut::math::vector<float,3> > 
 >痠   node  AJ          >C@   src  AK         
 Z   86                          H     痠  Onode     C@  Osrc  O   �   (                          p  �    q  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 3繪+罤�L嬞H堿H兟H堿A�   H堿H堿 H堿(H堿0H堿8H堿@H嬃I岺0@ A�   ff�     駻 �YB痱X � 駼L �YJX闰��Y�X硫 H兝I冮u綡兟H冮I冭I冴u淚嬅�   �   C  J G            �       �   �5        �donut::math::operator*<double,3,3,3> 
 >�   a  AK         
 >�   b  AP          M        �5   1 M        �5    N N                        H & h   �5  �5  �5  �5  �5  �5  �5  �5      �  Oa     �  Ob     3�  Oresult  O �   `           �   P   	   T       9 �    : �   9 �   : �@   < �P   > ��   ; ��   ? ��   @ �,   �   0   �  
 l   �   p   �  
 �   �   �   �  
 X  �   \  �  
 H嬆H塜WH冹pW�)p鐷孃�@郒嬞H峊$I嬋W蒆+蔒嬝A�   L峅@�H癓岮0@�H蠬岪狉AY鸷   駻aA)f�     (�(尿Y(万YD駻Y �X�X序X羊H兝H冴u蘄兞I冭H冮I冴u涷wPH嬅�WH(铗AYk(骝AYs((买AY�_X駻Yc (蓑AYK0�X�(买AYS駻YC�X�(蓑AYK8�X蝌AY[@�X�D$駻XkHD$8�X�L$(�X�K駻XcPL$H駻XsXC �D$XK0f�kH�sX(t$`�C@H嫓$�   H兡p_�   �   0  F G            �     y  a5        �donut::math::operator*<double,3> 
 >   a  AK          AM       p
 >   b  AP        /  AS  /     X, M        -5  �	

 N M        �5  �$ N' M        �5  ��.Pc	 N* M        �5  "(4 M        �5  "$ M        �5   N N N p                     H > h   �4  -5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5   �     Oa  �     Ob  �   ]�  Oresult  O�   p           �  �     d       �  �   �  �   �  �   �  �,   �  �/   �  ��   �  ��   �  �o  �  �t  �  �y  �  �,   �   0   �  
 h   �   l   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 H冹�bH嬃�j(则AYP(万AYH0)4$�2(�(掾AY 駻Yp駻YX�X�(尿AY`(駻Y@ �X�(万AYh@�X趄AYH8�X仳�X躜X衮q(4$�YH兡�   �   �   D G            �   '   �   �5        �donut::math::operator*<double> 
 >B�   a  AK        � 
 >�   b  AP        �                        H 
 h   �4   (   B�  Oa  0   �  Ob      1�  Oresult  O   �   8           �   P      ,       k �   m �	   p ��   q �,   �   0   �  
 f   �   j   �  
 �   �   �   �  
 �   �   �   �  
 �H嬃�X��J�XI�I�B�XA�A�   �   �   E G            .       -   6        �donut::math::operator+=<double> 
 >V�   a  AJ        . 
 >B�   b  AK        .                         H     V�  Oa     B�  Ob  O   �               .   �            �  �,      0     
 g      k     
 �      �     
 �      �     
 H嬃f蒮^��A�^硫A�   �   �   E G                
      �5        �donut::math::operator/=<double> 
 >V�   a  AJ          
 >A    b  A�         
                         H     V�  Oa     A   Ob  O   �                   �            �  �,   �   0   �  
 g   �   k   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        v  
 Z   �   N Z   S  k   (                      H 
 h   v         $LN14  0     O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  U   w  U  
 �     �    
 H嬆SWH侅�   )p豂孂)x菼嬝D)@窪)H―)P楧)X圖)d$PD)l$@D)t$0D)|$ H呉tAH�IX�J@ �A駾駾yA(序Di0駾qA(万y8駾a(駾Q 駾I@��$�   A(球AY球AY序AY万D�$�   �X序�$�   W莉D�$�   �X裦.聎fQ螂(妈    (餉(轆(黍AY�(昨AY�W莉Y昨X衮X趂.脀fQ(描    (�$�   A(�(蒡AY舔Y軦(羊AY�W莉X衮X趂.脀fQ汶(描    ��$�   (郋W踗A/髒駾^乞D^D^頵A/�$�   v!駾^黩^昨D�$�   ��$�   駾^譮A/鉽!�^祢D^潋�$�   駾�$�   駾^藺(臕(唑Y贏(悟AY买AY万\�(买AY繟(因AY序Y蒡\華(球AY乞AY舔\序X衮AY羊X趂D/踲�    DW繢W鳧W�W餒�t�7��gH呟凩  �5    A(X�W�(向A\黍A\沈A_薴.羨EW鲵DQ耠(凌    D(痱D%    駻\�W莉EY�(悟AX黍A\沈A_薴.羨fDQ岭(凌    D(莉A\蝌EY�W莉AX耱A_骹.苭fQ鲭(畦    (痱�$�   A(乞\�$�   駻Y翳    駾\�$�   D(餉(繟(丸    ��$�   D(莉A\�(畦    駻X�W�(痱AXA_鹒.蟱	W莉Q请(氰    駻Y尿駾s駾C�sD(l$@L崪$�   A(s鐰({谽(C菶(K窫(S‥([楨(c圖(t$0D(|$ I嬨_[蔑   
   *  
   n  
   R  v   �  `   �  
   �  ]   �  
   +  
   N  �   i  �   �  �   �  
      �   �  J G              F   �  b5        �donut::math::decomposeAffine<double>  >   transform  AJ        � & AJ �     8  |  � 
 9 \{  >1�   pTranslation  AK        � & AK �     8  |  � 
 9 \{  >d�   pRotation  AI       � AP          >1�   pScaling  AM       � AQ         
 >�   col2  C(     �      C%    �    � E6u �   �     l E6u� �   �     5 >�   scaling  C�       �       C�             C�      .      C�      J      C�      r      C�      e    {$ D
 >�   col1  C�      �      C&     �    + E6u�   �     B E6u �   �     O >�   zAxis  C�      $     
 >�   col0  C+      �    6  C)     �    1  C+     �    A�   C)    �    
�  �q  >瘭   rotation  C�       �      C*     �    �  C$         �  C�      M    �  C�      �    C  C*    �    7  C$    �      C�     �      M        �5  �:		% M        6  �:		 M        �5  �:		 N N N M        �5  ��	,0 M        6  ��	 M        �5  ��	 N N N  M        �5  p
-# M        6  p
- M        �5  p
- N N N M        �5  `)
< N M        �5  z

(	 N M        �5  e N M        �5  
亷
 >A    b  A$  �    ;  A$ �    !�  - ;�  N M        �5  伃
 >A    b  A*  �      A* �    ;�  � 
 N M        �5  佋
 >A    b  A�   �      A�  �    �   > a{  N M        �5  @侎E M        �5  �  >A    _x  A�         >A    _y  A�   (      >A    _z  A�   :    	  N N M        �5  �1
	 N M        ^5  侼 N Z   &6  &6  &6   �                     @ : h
   �4  �4  ^5  �5  �5  �5  �5  �5  �5  �5  �5  �5  6   �     Otransform  �   1�  OpTranslation  �   d�  OpRotation  �   1�  OpScaling  O�   �            �
  1   �      � �F   � �K   � �`   � �e   � �p   � �t   � �z   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �:  � �>  � �A  � �F  � �J  � ��  � ��  � ��  � ��  � �1  � �6  � �:  � �N  � �b  � �e  � �j  � �x  � ��  � ��  � ��  � �  � �2  � �R  � �m  � ��  � ��  � ��  � �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
   �   #  �  
 B  �   F  �  
 R  �   V  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
   �     �  
 '  �   +  �  
 ;  �   ?  �  
 b  �   f  �  
 v  �   z  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 %  �   )  �  
 T  �   X  �  
 h  �   l  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 G  �   K  �  
 W  �   [  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 H嬆H塜WH侅�   )p鐷孂)x豀峀$ D)@菻嬟D)H窪)P―)X楧)`圖)l$p�    駾[HL崪$�   駾kPH嬊駾SX�    �L$HDW�(d$ DW�(\$0E(弪DYd$@E(膨T$`DW序D$@E(蓑DYL$(A(篁Yt$0A(Y|$XA(�'_駾Y硫EX狎DY躥�(L$P駻X�G O0駾X珧W@駾Y裦垓DY腧Y牝EX腧X躜EX牝DoH駾gP�wXI媅A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�I嬨_肂   �   g   v      �   _  D G            ]  A   L   _5        �donut::math::inverse<double,3> 
 >   a  AI  '     	 AK        '  >\�   mInverted  C�       z     �  C�      �     �  C�   0   �     y  D    * M        -5  u	)	
% N@ M        �5  k+

 N# M        ^5  F			h M        �5  	�  >A    _x  A'  ~     ]  >A    _y  A)  u     �  >A    _z  A&  �     d  N N
 Z   �5   �                     @  h   �4  -5  ^5  �5  �5       Oa      \�  OmInverted     ]�  Oresult  O �   X           ]  �     L       �  �   �  �F     �L    �T     �Z    �]     �,   �,   �   0   �  
 f   �   j   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 t  �   x  �  
 H嬆H塇USVWATAUAVAWH峢窰侅  E3晒   J�0   L塎X�%    A孂)p‥峺�5    E嬔)x楨嬞�=    )D$ B H塎`D)@圚岲$ 駾    )L$0J0H塃hH岲$8)D$@H+仳B@�D$`W�)L$P(
    )E�)E狉}�)L$p)M� E峚H嬜L岹A凕�0  A�屶   H�M嬎H峀$8H�罙嬆餍L峫$8M炅�I鬏�繢嬸E�$�D  �A鐺�
�L T�T蘃嬺f/羦N�)�	K�
�D I嬓T�T蘃F謋/葀L岻鐻薎峆�IK�
�D T�T膄/葀I峆L��I0K�
�D T�T膄/葀L岻L薎峆I兝H兞`I冾匴���A凕}B�   A+腖c菾�GI嬂I闰L� H�WH�T舔D� T膄/菻F翴�繦嬓I冮u蒐婱XH�WH黍D� T膄/�嘳  H;譼dB\ H�R駼T0D� BD �L�0D膒駼L0\� B\p�T�0駼T�BDp�L艀駼L�\膒�T艀H婨h�f襢.讂tDBD 駼L0f^买^蔅D BDp駼L0f^翨Dp駼D��^买BD�A�劸   駼l (�T膄/�啙   W葾W�(�(膨BYD((蒡BYT 駼Y\0�D$(D$ �蔲L$(镇BYTpfX硫L$0�X�)D$ (膨BYl�駼YDx�L$0W沈黍D$fL$(D$pfX硫M��X�)D$p�M�A�劵   駼l8(�T膄/�啗   AW�W�(�(镇BYD((蒡BYT 駼Y\0�D$D$8��(說L$駼YTpfX�W�D$8�D$H�黍X抿D$H(膨BYDx駼Yl��D$E坒L$fX�E堯E橋X膨E楨�劜   駼lP(�T膄/�啗   W葾W�(�(膨BYD((蒡BYT 駼Y\0�D$(D$P�蔲L$(镇BYTpfX硫L$`�X�)D$P(膨BYl�駼YDx�L$`W沈黍D$fL$(E爁X硫M膀X�)E狉M癏婱`A�罤兞L塎XH兝 H塎`A�螲塃hH�荋冸I兟I兠A��弔��H婨P(D$p(M� (E�H(M�@ �E�H0�@@�1H婨PH�      �H�H塇H塇H塇H塇 H塇(H塇0H塇8H塇@L崪$  A(s鐰({谽(C菼嬨A_A^A]A\_^[]�:   s   M   W   _   `      v   �   m      �   �  D G            �  u   �  �5        �donut::math::inverse<double,3> 
 >�   m  AK        �  AK �      
 >\�    b  Dp   
 >\�    a  D    
 >t     j  Ai  ,    `  Ai �     & � 
; \` 
 >t     i  Al      �  Al �      �  >A     scale  A�       �Z a ,M . M        .5  $				. M        M5  $				8 M        R5  $				 N N N3 M        6  � 
D
 N M        �5  佱 N* M        6  �)		$			Q N M        �5  �%!-#X N M        �5  侁 N M        �5  佢 N M        6  	� N M        �5  � N M        �5  � N M        �5  厡 N" M        6  俈'
 >�   _Tmp  C�       m    + " C�      �     j^ X� � ��  N% M        6  �4

 >�   _Tmp  C�       :    3  N M        �5  �: N M        �5  偳
 N M        �5  偓! N M        6  
傶
��
�� N+ M        6  
僈}	'
�� M        �5  儐���� >A    _x  A�   U    �Y e d  A�  �     uS �� ��  >A    _y  A�   y    � � � �  >A    _z  A�   r    �< � � �  A�  �     1� �� ��  N N2 M        6  �<$	��

��$ N M        6  $�$��$�� M        �5  �6���� >A    _x  A�   /    � � � �  >A    _y  A�   %    � � � �  >A    _z  A�   6    �x F 4E  A�  �     j^ X� � ��  N N# M        6  儌"
�� %�� 
 N           @          @ > h   .5  M5  R5  �5  �5  �5  �5  �5  �5  6  6  6  6  6   X  �  Om  p   \�  Ob      \�  Oa  O�   �          �  P   P   �      � �   � �!   � �$   � �)   � �-   � �2   � �6   � �A   � �E   � �T   � �X   � �c   � �l   � �p   � �u   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �   � �>  � �B  � �c  � �n  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �/  � �4  � �V  � �[  � �g  � �m  � �s  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �K  � �U  � �h  � �y  � ��  � ��  � ��  � ��  � �  � �  � �  � �  � �%  � �)  � �3  � �j  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �%  � �\  � ��  � ��  � �,   �   0   �  
 f   �   j   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 &  �   *  �  
 Z  �   ^  �  
 n  �   r  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 &  �   *  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 )  �   -  �  
   �     �  
 �A��I�Y因Y莉Y沈X�W莉X裦.聎fQ旅(麻    4   
      �   �   C G            8       3   �5        �donut::math::length<double,3> 
 >B�   a  AJ        8  M        6   ! M        �5   ! N N                        H  h   �5  6      B�  Oa  O   �               8   �            + �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 �I��Q�Y莉Y沈Y因X硫X旅   �   �   J G            #       "   6        �donut::math::lengthSquared<double,3> 
 >B�   a  AJ        #  M        �5  "  N                        H 
 h   �5      B�  Oa  O �               #   �            ' �,       0      
 l       p      
 �       �      
 H嬆SH侅@  )p鐷嬞�r)x�(悟zD)@�(荄)H窪)PDD)X楢(褼)`堯AY因Y荄)▁����Y悟X蠨)癶���W繢)竂����X裦.聎fQ码(妈    �
    EW鲵D    駾^序^痱^鳤(�(�T�T羏/蠨(螪(艵W薊W胿駾L$8H岲$8駾T$@駾t$H�駾t$ H岲$ 駾D$(�|$0D 駾xE(霢(蘤E霢(膨AY舔AY膨X華(球AY球X�W纅.羨	W莉Q岭(凌    駾^郘崪$@  H嬅駾^D^�(艫(因AY�(向AY�W垓AY麰W泸AY向AY誆W�[H�\闰DsXE(硃����\譇({郃(买AY荅W覧W鹒E誆([燛(k��\餱D酓#E(c怐SE(S癴A馝(K�s A(s餱D鶧{0E(籤���駾C@E(C蠭嬨[脠   
   �   s   �   v   R  
      �   v  B G            .  t   [  `5        �donut::math::lookatZ<double> 
 >B�   look  AK        �  AK �     �
 >�   left  C�      �      >�   up  C�      �     , M        �5  仦(	


	, M        -5  仦(	


	 N N M        ^5  仛0 M        �5  仛0 >A    _x  A)  �    ?  N N M        ^5  亴 M        �5  亴 N N M        �4  伷 M         5  伷 N N) M        �5  乸
		  M        �5  	乻" >A    _x  A�   �    �  A�   |    *  >A    _y  A�   �      >A    _z  A�   �    v  N N M        ]5  ,�/
' M        �5  乂
 M        �5  亅 >A    _x  A(  [    5  >A    _y  A)  p    .  >A    _z  A+  k    c  N N M        �5  ,�/ M        6  ,� M        �5  ,� N N N N M        �5  ��b" M        �5  ��
 N M        �5  �� >A    _x  A%  �     8 N M        6  �� N M        6  ��" N N) M        ]5  	


		
- M        �5  
�� N& M        �5  	


		& M        6  	


		( M        �5  	


		 N N N N @                    H F h   �4   5  -5  R5  ]5  ^5  �5  �5  �5  �5  �5  �5  �5  �5  6  6   X  B�  Olook  O  �   0          .  �  #   $      � �   � ��   � ��   � ��   � �[  � �f  � �p  � �|  � �  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �  � �  � �$  � �,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 j  �   n  �  
 1  �   5  �  
 A  �   E  �  
 ^  �   b  �  
 {  �     �  
 �  �   �  �  
   �     �  
 .  �   2  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹P)t$@H嬞�2)|$0(烛z(球Y烛Y荄)D$ 駾BA(闰X序AY�W莉X裦.聎fQ码(妈    �^餒嬅�^D^莉3(t$@�{(|$0駾CD(D$ H兡P[肸   
      �   �  F G            �   0   r   ]5        �donut::math::normalize<double,3> 
 >B�   a  AK        ^  AK ^     5  M        �5  ^

 M        �5  n	
 >A    _x  A�   b       >A    _y  A�   i       >A    _z  A$  n       N N M        �5  	 M        6  	 M        �5  	 N N N P                     H  h   �5  �5  �5  �5  6   h   B�  Oa  O�               �   �            / �,   �   0   �  
 h   �   l   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 L�3繦�H堿H婤H吚t�@H婤H堿H嬃L��   �   �  } G            (       '   e5        �std::static_pointer_cast<donut::engine::SceneGraphLeaf,donut::engine::DirectionalLight>  >劖   _Other  AK        ( 
 >A�    _Ptr  AP       %  M        �5  	 M        6  L
 M        6  ,	 M        �   N N N M        �5  � N N                        H�  h   �  �5  �5  �5  6  6      劖  O_Other  O  �   8           (   �     ,       � �    � �   � �'   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 L�3繦�H堿H婤H吚t�@H婤H堿H嬃L��   �   �  w G            (       '   q5        �std::static_pointer_cast<donut::engine::SceneGraphLeaf,donut::engine::PointLight>  >   _Other  AK        ( 
 >A�    _Ptr  AP       %  M        �5  	 M        6  L
 M        6  ,	 M        �   N N N M        �5  � N N                        H�  h   �  �5  �5  �5  6  6        O_Other  O�   8           (   �     ,       � �    � �   � �'   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 L�3繦�H堿H婤H吚t�@H婤H堿H嬃L��   �   �  v G            (       '   n5        �std::static_pointer_cast<donut::engine::SceneGraphLeaf,donut::engine::SpotLight>  >怒   _Other  AK        ( 
 >A�    _Ptr  AP       %  M        �5  	 M        
6  L
 M        6  ,	 M        �   N N N M        �5  � N N                        H�  h   �  �5  �5  �5  
6  6      怒  O_Other  O �   8           (   �     ,       � �    � �   � �'   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   '   %   �   ,   -      �   #  ? G            <      6   m        �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        U  :$
 Z   �   N                       H� 
 h   U   0   2  Othis  8   7  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   '   %   �   ,   0      �   =  U G            <      6   l        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        U  :$
 Z   �   N                       @�  h   U  m   0   I  Othis  8   N  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�   3      0      �   �   U G            !           ^        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        [    M        T    N N                        @�  h   T  [      I  Othis  O   �   8           !   H     ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   '   %   �      �   �   ? G            2      ,   U        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   H     $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H�    H��   �      �   �   � G                   
   �5        �std::_Ref_count_obj2<donut::engine::DirectionalLight>::~_Ref_count_obj2<donut::engine::DirectionalLight> 
 >爷   this  AJ                                 H� 
 h   �      爷  Othis  O�   (              �            2 �
   8 �,   �   0   �  
 �   �   �   �  
   �     �  
 H�    H��   �      �   �   � G                   
   �5        �std::_Ref_count_obj2<donut::engine::PointLight>::~_Ref_count_obj2<donut::engine::PointLight> 
 >�   this  AJ                                 H� 
 h   �      �  Othis  O�   (              �            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   �      �   �   � G                   
   �5        �std::_Ref_count_obj2<donut::engine::SpotLight>::~_Ref_count_obj2<donut::engine::SpotLight> 
 >蟑   this  AJ                                 H� 
 h   �      蟑  Othis  O  �   (              �            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塼$WH冹 H�9H嬹H�劊   H塴$8H媔H;齮OH塡$0@ H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;齯縃媆$0H�H媀H媗$8H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媡$@H兡 _描    太   �   �   �      �   �  G            �   
   �   �5        �std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::~vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > 
 >澃   this  AJ          AL       � �  0 M        �5  
F9%	 M         6  }*B M        �  ��)
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  }     O   2  # M        w  
��#
 
 Z   S   >    _Ptr_container  AP  �     6    AP �       >    _Back_shift  AJ  y     S 1   AJ �       N N N M        6  
".8 >幇   _First  AM  
     � �   >   _Last  AN  "     `  M         6  80 M        �5  80 M        �5  0/	 M        �  9/
 >Z&   this  AI  4     B  AI 0       M        �  P	 N N N N N N N                       @� > h   w  x  �  �  �  �5  �5  �5  �5   6  6  6   6  #6         $LN57  0   澃  Othis  9N       [&   9e       [&   O�   H           �   �     <       � �
   � �
   � �   � ��    ��   � �,   �   0   �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 �  �     �  
 a  �   e  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �      �  
 �  �   �  �  
 �  �   �  �  
 H  5   L  5  
 l  �   p  �  
 |  �   �  �  
 �  �   �  �  
 H塡$H塼$WH冹 H媃 H嬹����H呟t)嬊�罜凐uH�H嬎�嬊�罜凐u	H�H嬎�PH婲H吷t�羪�uH��PH媆$0H媡$8H兡 _�   �   ,  B G            r      b   �5        �donut::engine::Light::~Light 
 >=�   this  AJ          AL       V  M        �4  I M        5  I M        55  I	 M        �  R

 >Z&   this  AJ  M       AJ b       N N N N M        �5  3 M        �5  )
 M        �   ,
 >Z&   this  AI       T  M        �  4	 N N N N                       @� " h   �  �  �4  5  55  �5  �5   0   =�  Othis  92       [&   9F       [&   9_       [&   O,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
    �     �  
 ~  �   �  �  
   �     �  
   �     �  
 (  �   ,  �  
 H婭H吷t�����罙凐uH�H�`�   �   <  T G                       �4        �donut::engine::SceneGraphLeaf::~SceneGraphLeaf 
 >A�   this  AJ          M        5    M        55   	 M        �  )
 >Z&   this  AJ         N N N                        H�  h   �  5  55      A�  Othis  9       [&   O�                   �            >  �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 8  �   <  �  
 P  �   T  �  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �   Y   �      �   �  V G            ^      ^   �4        �nvrhi::VertexAttributeDesc::~VertexAttributeDesc 
 >�   this  AI  
     Q J   AJ        
  M        �  EK) M        �  ,(
	 M           N M          ,E M        9  &? M        �  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        w  
"#
!
 Z   S   >    _Ptr_container  AP  &     7    AP :       >    _Back_shift  AJ  -     0 
   N N N N N N                       H� : h
   w  x  �  �  �  �           9  �  �         $LN37  0   �  Othis  O   ,   �   0   �  
 {   �      �  
 �   �   �   �  
 o  �   s  �  
 �  �   �  �  
 �  �   �  �  
   �   	  �  
 +  �   /  �  
 �     �    
 �     �   �   L G                       �        �std::_Ref_count_base::~_Ref_count_base 
 >Z&   this  AJ          D                           H�     Z&  Othis  O  �                  �            ~ �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 H�    H�H兞�       '      �      �   �   V G                      `        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        V   	
 N                        H�  h   V  ]      I  Othis  O ,   �   0   �  
 {   �      �  
 H�    H�H兞�       '      �      �   �   @ G                      V        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              H            Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
篽   �    H嬅H兡 [�	   �      �      �   �   y G            +      %   �5        �std::_Ref_count_obj2<donut::engine::DirectionalLight>::`scalar deleting destructor' 
 >爷   this  AI         AJ                                @� 
 h   �5   0   爷  Othis  O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
篨   �    H嬅H兡 [�	   �      �      �   �   s G            +      %   �5        �std::_Ref_count_obj2<donut::engine::PointLight>::`scalar deleting destructor' 
 >�   this  AI         AJ                                @� 
 h   �5   0   �  Othis  O   ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
篳   �    H嬅H兡 [�	   �      �      �   �   r G            +      %   �5        �std::_Ref_count_obj2<donut::engine::SpotLight>::`scalar deleting destructor' 
 >蟑   this  AI         AJ                                @� 
 h   �5   0   蟑  Othis  O,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H孂嬟H兞@�    H嬒�    雒t
篨   H嬒�    H媆$0H嬊H兡 _�   �      �   .   �      �   �   c G            @   
   2   �5        �donut::engine::DirectionalLight::`scalar deleting destructor' 
 >O�   this  AJ        
  AM  
     2                        @� 
 h   �5   0   O�  Othis  O   ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 嬟H孂�    雒t
�8   H嬒�    H媆$0H嬊H兡 _�   �   "   �      �   �   X G            4   
   &   �5        �donut::engine::Light::`scalar deleting destructor' 
 >=�   this  AJ          AM       $                        @�  0   =�  Othis  O  ,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 H塡$WH冹 嬟H孂�    雒t
篐   H嬒�    H媆$0H嬊H兡 _�   �   "   �      �   �   ] G            4   
   &   �5        �donut::engine::PointLight::`scalar deleting destructor' 
 >o�   this  AJ          AM       $                        @� 
 h   �5   0   o�  Othis  O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H嬞孃H婭H吷t�����罙凐uH��P@銮t
�   H嬎�    H嬅H媆$0H兡 _�<   �      �   �  a G            N   
   C   �4        �donut::engine::SceneGraphLeaf::`scalar deleting destructor' 
 >A�   this  AI  
     ;  AJ        
  M        �4   M        5   M        55  /	 M        �  
 >Z&   this  AJ         AJ -     !    N N N N                       @�  h   �  �4  5  55   0   A�  Othis  9*       [&   O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 
  �     �  
   �   !  �  
 �  �   �  �  
 H塡$WH冹 嬟H孂�    雒t
篜   H嬒�    H媆$0H嬊H兡 _�   �   "   �      �   �   \ G            4   
   &   �5        �donut::engine::SpotLight::`scalar deleting destructor' 
 >_�   this  AJ          AM       $                        @� 
 h   �5   0   _�  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   '      �   0   �      �   �   R G            B   
   4   \        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]   0   2  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   '      �   0   �      �     ] G            B   
   4   _        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        V  

	
 Z   �   N                       @�  h   V  ]  `   0   I  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   '      �   0   �      �   �   R G            B   
   4   X        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        V  

	
 Z   �   N                       @� 
 h   V   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   $        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >A   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   A  O__f  9(       A   O ,      0     
 g      k     
 w      {     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 !     %    
 1     5    
 A     E    
 �     �    
 H塡$H塴$VWAVH冹0H嬮E3鯤孃A峃h�    H塂$XH嬝A峷�H吚t|W� 茾   茾   H�    H��   CC C0C@CPL塻�    L塻 L塻(L塻0�C<塁DH�    H塁塻8L塻PL塻XL塻`H荂H  �?�I嬣婨4H岾�E,�C<塁D婨8塁H婨<塁LL�7L墂H呟t4�CH�嬈H塤�罜凐u$H�H嬎��羢凗uH�H嬎�P�H�H塤H媆$PH嬊H媗$`H兡0A^_^�    �   L   �   U   M   u   M   �   ]      �   �  L G            1       �4        �donut::engine::DirectionalLight::Clone 
 >O�   this  AJ          AN       
 >柇   copy  CJ      �     v B   M        �4  ��' M        (5  ��' M        �  ��* M        �  ��
 N N N N M        e5  ��+ M        �5  ��	
+ M        6  	��+ M        6  ��	+ M        �  ��	+ N N N M        �5  ��� N N N$ M        c5  9A"
 Z   �   >爷    _Rx  AI  ,     �  BX   )      M        �5  8&" M        �  	; N M        6  S&" M        �5  �� M        6  �� M        6  �� N N N M        �5  S* M        �5  �
S* N N M        �4  m M        5  m M        
5  �m N N N N N N' S  �*  donut::math::colors::white  C      Y     ;  0                     @ � h!   �  z  �  �  �  �  �4  �4  5  
5  (5  c5  d5  e5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  6  6  6  6  6  6   P   O�  Othis  ^      询   9�       [&   9      [&   O �   `           1  �  	   T       x  �   y  ��   z  ��   y  ��   z  ��   {  ��   |  ��   }  �  ~  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 �   �   �   �  
 8  �   <  �  
 H  �   L  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H塡$H塴$VWAVH冹0H嬮E3鯤孃A峃X�    H塂$XH嬝A峷�H吚tvW� 茾   茾   H�    H�3�CC C0C@H塁P�   �    L塻L塻 L塻(L塻0�C<塁DH�    H塁塻8H荂H  �?D塻P�I嬣婨4H岾�E,�C<塁D婨8塁H婨<塁L婨@塁PL�7L墂H呟t4�CH�嬈H塤�罜凐u$H�H嬎��羢凗uH�H嬎�P�H�H塤H媆$PH嬊H媗$`H兡0A^_^�    �   L   �   k   M   s   M   �   c      �   9  F G            1       �4        �donut::engine::PointLight::Clone 
 >o�   this  AJ          AN       
 >
�   copy  CJ      �     | H   M        �4  ��' M        &5  ��' M        �  ��* M        �  ��
 N N N N M        q5  ��+ M        �5  ��	
+ M        6  	��+ M        6  ��	+ M        �  ��	+ N N N M        �5  ��� N N N! M        o5  7?2
 Z   �   >�    _Rx  AI  ,     �  BX   )      M        �5  8$ M        �  	; N M        6  S$ M        �5  i M        �5  �
i N N M        �4  w M        5  w M        
5  �w N N N N N N' S  �*  donut::math::colors::white  C      o     '  0                     @ v h   �  �  �  �  �  �4  �4  5  
5  &5  o5  p5  q5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  6  6  6   P   o�  Othis  ^      �   9�       [&   9      [&   O   �   h           1  �  
   \        �    ��    ��    ��    ��    ��    ��    ��    �   �,   �   0   �  
 k   �   o   �  
 {   �      �  
 �   �   �   �  
 /  �   3  �  
 ?  �   C  �  
 `  �   d  �  
   �     �  
 %  �   )  �  
 5  �   9  �  
 P  �   T  �  
 H塡$H塴$VWAVH冹0H嬮E3鯤孃A峃`�    H塂$XH嬝A峷�H吚剛   W� 茾   茾   H�    H��   CC C0C@CPL塻�    L塻 L塻(L塻0�C<塁DH�    H塁塻8H荂H  �?D塻P荂T  4C荂X  4C�I嬣婨4H岾�E,�C<塁D婨8塁H婨<塁L婨@塁P婨D塁T婨H塁XL�7L墂H呟t4�CH�嬈H塤�罜凐u$H�H嬎��羢凗uH�H嬎�P�H�H塤H媆$PH嬊H媗$`H兡0A^_^�    �   P   �   Y   M   y   M   �   `      �   ?  E G            M     7  �4        �donut::engine::SpotLight::Clone 
 >_�   this  AJ          AN       /
 >篆   copy  CJ      �     � T   M        �4  �' M        '5  �' M        �  �* M        �  �
 N N N N M        n5  ��+ M        �5  ��	
+ M        
6  	��+ M        6  ��	+ M        �  ��	+ N N N M        �5  ��� N N N! M        l5  9!A<
 Z   �   >蟑    _Rx  AI  ,      BX   )     $ M        �5  <& M        �  	? N M        	6  W& M        �5  W* M        �5  �
W* N N M        �4  q M        5  q M        
5  �q N N N N N N' S  �*  donut::math::colors::white  C      ]     ;  0                     @ v h   �  �  �  �  �  �4  �4  5  
5  '5  l5  m5  n5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  	6  
6  6   P   _�  Othis  ^      虔   9      [&   9+      [&   O �   x           M  �     l       �  �   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �7  �  �,   �   0   �  
 j   �   n   �  
 z   �   ~   �  
 �   �   �   �  
 .  �   2  �  
 >  �   B  �  
 f  �   j  �  
   �     �  
 +  �   /  �  
 ;  �   ?  �  
 T  �   X  �  
 H塡$WH冹p)t$`H嬟)|$PH孂D)D$@�    H峊$ 荂   H嬒�    �x�0(球D@(烛Y諥(闰Y球AY闰X�W莉X裦.聎fQ码(妈    �^痱^D^纅Z蝔AZ荔fZ象KW审C�_O<(t$`(|$PD(D$@�]
    �Y
    �K0婫8塁,H嫓$�   H兡p_�!   �   5   �   z   
   �   c   �   T      �   �  Y G            �       �   �4        �donut::engine::DirectionalLight::FillLightConstants 
 >M�   this  AJ          AM       �  >9�   lightConstants  AI       �  AK          M        �4  �� N M        g5  �� M        �5  �� N M        �5  �� N N M        f5  ��	 N M        ]5  *9)-
 >B�   a  AH  9     E  AH ~     X  M        �5  ~ M        �5  �� >A    _x  A�   �     1  >A    _y  A�   �     2  >A    _z  A$  �     3  N N M        �5  *9- M        6  *9- M        �5  *9- N N N N Z   �4  �4   p                     @ 2 h   �4  ]5  f5  g5  �5  �5  �5  �5  �5  �5  6   �   M�  Othis  �   9�  OlightConstants  O �   `           �   �  	   T       �  �    �  �%   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 w  �   {  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   	  �  
   �     �  
 @SH冹 �A,H嬟�B 婣4塀(H荁@����H荁H����H荁P����H荁X����婣(塀`H荁d����荁l����H婭H吷t+H��PX劺t�    �C<H兡 [�W荔C<H兡 [皿    �B<H兡 [胑   Z   �   Z      �   G  N G            �      �   �4        �donut::engine::Light::FillLightConstants 
 >4�   this  AJ        R  >9�   lightConstants  AI       � e  s   AK          M        5  N N                       @  h   �4  �4  5  15  \5   0   4�  Othis  8   9�  OlightConstants  9Z       !�   O �   h           �   �  
   \       #  �   $  �   %  �)   &  �9   '  �N   (  �W   )  �i   ,  ��   +  ��   ,  �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 �   �   �   �  
 C  �   G  �  
 \  �   `  �  
 H塡$WH侅�   H嬟H孂�    H嬒荂   �    H吚uW�W襢�W垭�   �  (萬�(衒�W莉Z抿Z审CW荔K�Z麦CW缷G<塁�O@/羢�    �^馏C0婫8塁,�G,�C 婫4塁(H嫓$�   H伳�   _�   �   #   �   �   Z      �   �  S G            �   
   �   �4        �donut::engine::PointLight::FillLightConstants 
 >n�   this  AJ          AM       �  >9�   lightConstants  AI       �  AK          M        f5  W N( M        �4  
B
 Z   �4   >&�   localToWorld  C�   @   I       C�   P   B       D    
 >J�    node  AH  '     W  N
 Z   �4   �                     @  h   �4  �4  �4   5  f5   �   n�  Othis  �   9�  OlightConstants  O�   h           �   �  
   \        �    �    �    �"    �{    ��    ��    ��    ��    �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 ?  �   C  �  
 S  �   W  �  
 z  �   ~  �  
   �     �  
 H塡$WH侅�   H嬟H孂�    H峊$ 荂   H嬒�    H嬒� �H�PfZ纅Z审fZ麦K�C�    H吚uW�W襢�W垭�   �  (萬�(衒�W莉Z抿Z审CW荔K�Z麦CW缷G<塁�O@/羢�    �^馏C0婫8塁,�G,�C 婫4塁(�GD�Y    �C4�GH�Y    �C8H嫓$�   H伳�   _�   �   (   �   X   �   �   Z   �   T     T      �   b  R G              
     �4        �donut::engine::SpotLight::FillLightConstants 
 >^�   this  AJ          AM        >9�   lightConstants  AI        AK          M        �4  
�� N M        �4  
�� N M        f5  �� N( M        �4  ,+B
 Z   �4   >&�   localToWorld  C�   @   ~       C�   P   w       D@   
 >J�    node  AH  \     W  N M        f5  3
 >B�   v  AH  ,     0  N Z   �4  �4   �                     @  h   �4  �4  �4  �4   5  f5   �   ^�  Othis  �   9�  OlightConstants  O  �   �             �  
   t       �  �   �  �   �  �,   �  �3   �  �W   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 o  �   s  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 x  �   |  �  
 H塡$WH冹 婣8H嬞塀H孃婣<塀婣@�婣D塀H婭(H吷tH��P W缷H驢*岭W荔G�CP�KT�SX婥L塆 �G$�O(�W,�C`�Kd�Sh婥\塆0�G4�O8�W<�Cp�Kt�Sx婥l塆@�GD�OH�WL�儉   �媱   �搱   婥|塆P�GT�OX�W\�儛   �嫈   �摌   媰�   塆`�Gd�Oh�Wl�儬   �嫟   �摠   媰�   塆p�Gt�Ox�W|H媆$0H兡 _�   �   h  X G            O  
   D  �4        �donut::engine::LightProbe::FillLightProbeConstants 
 >�   this  AI       9 AJ          >�   lightProbeConstants  AK          AM       8  M        +  K$$$-0 N                       @  h   +  1  5   0   �  Othis   8   �  OlightProbeConstants  93       l   O�   X           O  �     L       � �
   � �   � �   � �!   � �'   � �K   � �D  � �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 d  �   h  �  
 |  �   �  �  
 �   �   �   �   K G                      �4        �donut::engine::Light::GetContentFlags 
 >4�   this  AJ          D                           @     4�  Othis  O   �                  �            �  �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 3烂   �   �   T G                      �4        �donut::engine::SceneGraphLeaf::GetContentFlags 
 >=�   this  AJ          D                           @     =�  Othis  O  �                  �            D  �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 @SH冹pH嬟�    H吚uH�H塁H塁H嬅H兡p[�)t$`�   )|$P(﨑)D$@駾�  (蝔�(球Y悟Y球X華(莉AY莉X�W纅.羨	W莉Q岭(凌    �
    H嬅�^痱^D^�W�W鵇W硫3(t$`�{(|$P駾CD(D$@H兡p[�
   �   �   
   �   v      �   w  H G            �      �   �4        �donut::engine::Light::GetDirection 
 >4�   this  AJ         
 >J�    node  AH       y    AH �       M        ^5  ��
 M        �5  ��
 N N M        ]5  8#&" M        �5  �� M        �5  �� >A    _x  A�   �       >A    _y  A�   �       >A    _z  A$  �     
  N N M        �5  8#& M        6  8# M        �5  8# N N N N M        �4   M         5   N N
 Z   �4   p                     @ . h
   �4  �4   5  ]5  ^5  �5  �5  �5  �5  6   �   4�  Othis  O �   `           �   �  	   T       D  �	   E  �   F  �   G  �   K  �,   J  ��   K  ��   J  ��   K  �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 �   �   �   �  
 C  �   G  �  
 `  �   d  �  
 }  �   �  �  
 �  �   �  �  
 �   �   �   �   S G                      �4        �donut::engine::DirectionalLight::GetLightType 
 >M�   this  AJ          D                           @     M�  Othis  O   �                  �            �  �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �   M G                      �4        �donut::engine::PointLight::GetLightType 
 >n�   this  AJ          D                           @     n�  Othis  O �                  �            �  �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �   �   �   L G                      �4        �donut::engine::SpotLight::GetLightType 
 >^�   this  AJ          D                           @     ^�  Othis  O  �                  �            �  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 �    H嬄�    (�买(�抿B�Z�R�   f      i      �   3  X G            3       2   �4        �donut::engine::SceneGraphLeaf::GetLocalBoundingBox 
 >A�   this  AJ        3  D    M        5   ' M        C   N M            N M           N N                        @  h   M      C  5      A�  Othis  O �               3   �            B  �,   �   0   �  
 }   �   �   �  
 H  �   L  �  
 @SH冹 H婹H嬞H呉t婤吚t�    岺�盝t吚u�3繦兡 [肏墊$8H媨H媅H呟tDH塼$0����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�RH媡$0H嬊H媩$8H兡 [肏嬊H媩$8H兡 [�   �     L G            �      �   �4        �donut::engine::SceneGraphLeaf::GetNode 
 >=�   this  AI  
     6 (   AJ        
  M        	5  6C M        65  C,
 M        �  M M        �  f	
 N N N N M        
5  " M        x5    M        H  
+( >     _Count  A          A  .       N N N                       @ . h
   �  �  H  	5  
5  
5  5  $5  65  x5   0   =�  Othis  9d       [&   9v       [&   O �               �   �            @  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 ~  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 0  �   4  �  
 @SH冹 H嬟�    H吚uH�H塁H塁H嬅H兡 [��  ��(  H嬅�KH兡 [�
   �      �     G G            G      A   �4        �donut::engine::Light::GetPosition 
 >4�   this  AJ         
 >J�    node  AH       +    M        �4   M         5   N N
 Z   �4                         H  h   �4  �4   5   0   4�  Othis  O  �   P           G   �     D       :  �	   ;  �   <  �   =  �   A  �'   @  �6   A  �,   �   0   �  
 l   �   p   �  
 �   �   �   �  
 $  �   (  �  
 @SUVWAVH冹pH�    H3腍塂$hA嬮I嬂Hc騂嬞H塋$(E3�W�D$0fo
    �L$@D坱$0D坱$P�   H墊$TL塼$\D坱$dI抢�����    I�繠�<  u鯤嬓H峀$0�    凗w`H�    媽�    H�岜,�   隡�)�   隓��   �;�/�   荄$\   岹m艱$d�"�/�   荄$\@   岹m艱$d�	婦$`禠$PD$0L$@K圞 墈$塳(婰$\塊,塁0禠$d圞4H嬅H婰$hH3惕    H兡pA^_^][�                                   j   :   p   �   �   �   Q   �      -     <     @     D     H     L     P     T     X        �   '  K G            \     \  �4        �donut::engine::GetVertexAttributeDesc  >$E   attribute  A         #  AL  #     
 >_   name  AH        g  AP           >u    bufferIndex  A         Ai          >�   result & C       �     r       2  K 	  C       �       C   $   S     � & C   0   �     ~       -  F   D0    M        �   b M        )!  b
 Z   �   M        �  b
 N N N M        �   1 M          6
 N M        �  1 M        �  1 M          1 N N N N M        �  �� M        �  0�� M          �� N N N p           (         A � h%   w  x  z  �  �  �  �  �  �  �                 "  9  �  �  �  �  �  �  �  �  �  �  �    s  �   �   )!  �4  �4  
 :h   O 
                    $LN11         $LN10         $LN8         $LN6         $LN4  �   $E  Oattribute  �   _  Oname  �   u   ObufferIndex  0   �  Oresult  O �   (          \  �            ? �1   @ ��     ,   I   s �N   t �X   v �]   y ��     �   b   A ��   E ��   I ��   J ��   K ��   N ��   O ��   P ��   S ��   T ��   U ��   W ��   X ��   Y ��   Z ��   [ ��   \ ��   ^ ��   _ ��   ` ��   a ��   b ��   E ��   i �$  j ��   �   Z F                                �`donut::engine::GetVertexAttributeDesc'::`1'::dtor$1  >�    result  EN  0                                  �  O ,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 7  �   ;  �  
 K  �   O  �  
 _  �   c  �  
 |     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 <  �   @  �  
 �     �    
 �     �    
 H崐0   �       �   @SH冹 �yH H嬞tEH兞L�    劺u8�C@W�.羫tH儃  t�H兡 [皿CD.羫tH儃( 暲H兡 [�2繦兡 [�   �      �   �   I G            \      V   �4        �donut::engine::LightProbe::IsActive 
 >�   this  AI  
     N ,  F   AJ        
 
 Z   16                         @ 
 h   1   0   �  Othis  O �   h           \   �  
   \       | �   } �    �   � �2   � �4   � �:   � �N   � �T   � �V   � �,   �   0   �  
 n   �   r   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塼$WH冹0H嬺H孂H嬑H�    �    L岹,H嬓H峀$ �    H�    H嬑� �G,婡塆4�    H峎8H嬋�    H�    �G8H嬑�    H峎<H嬋�    H媆$@H媡$H�G<H兡0_�   f       �   1   �   8   i   O   �   [   �   b   l   o   �   {   �      �   �  K G            �         �4        �donut::engine::DirectionalLight::Load 
 >O�   this  AJ          AM       ~ 
 >Ok   node  AK          AL       w  M        i5  s
 Z   96  
 >Ok   node  AH  s       N M        i5  S
 Z   96  
 >Ok   node  AH  S       N M        h5  $
 Z   :6  
 >Ok   node  AH  $       N Z   %6  %6  %6   0                     @  h   h5  i5   @   O�  Othis  H   Ok  Onode  O�   p           �   �     d       �  �   �  �5   �  �?   �  �N   �  �_   �  �f   �  �k   �  �   �  ��   �  ��   �  �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 9  �   =  �  
 �  �   �  �  
    �     �  
 H塡$H塼$WH冹0H嬺H孂H嬑H�    �    L岹,H嬓H峀$ �    H�    H嬑� �G,婡塆4�    H峎8H嬋�    H�    �G8H嬑�    H峎<H嬋�    H�    �G<H嬑�    H峎@H嬋�    H媆$@H媡$H�G@H兡0_�   f       �   1   �   8   u   O   �   [   �   b   ~   o   �   {   �   �   �   �   �   �   �      �   2  E G            �      �   �4        �donut::engine::PointLight::Load 
 >o�   this  AJ          AM       � 
 >Ok   node  AK          AL       �  M        i5  ��
 Z   96  
 >Ok   node  AH  �       N M        i5  s
 Z   96  
 >Ok   node  AH  s       N M        i5  S
 Z   96  
 >Ok   node  AH  S       N M        h5  $
 Z   :6  
 >Ok   node  AH  $       N Z   %6  %6  %6  %6   0                     @  h   h5  i5   @   o�  Othis  H   Ok  Onode  O  �   �           �   �     |        �    �5    �?    �N    �_    �f    �k    �    ��    ��    ��    ��    ��    �,   �   0   �  
 j   �   n   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 4  �   8  �  
 {  �     �  
 �  �   �  �  
 H  �   L  �  
 �     �   �   I G                       �4        �donut::engine::SceneGraphLeaf::Load 
 >A�   this  AJ          D   
 >Ok   node  AK          D                           @     A�  Othis     Ok  Onode  O   �                  �            G  �,   �   0   �  
 n   �   r   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塼$WH冹0H嬺H孂H嬑H�    �    L岹,H嬓H峀$ �    H�    H嬑� �G,婡塆4�    H峎8H嬋�    H�    �G8H嬑�    H峎DH嬋�    H�    �GDH嬑�    H峎HH嬋�    H�    �GHH嬑�    H峎<H嬋�    H�    �G<H嬑�    H峎@H嬋�    H媆$@H媡$H�G@H兡0_�   f       �   1   �   8   u   O   �   [   �   b   x   o   �   {   �   �   {   �   �   �   �   �   ~   �   �   �   �   �   �   �   �   �   �      �   �  D G            �      �   �4        �donut::engine::SpotLight::Load 
 >_�   this  AJ          AM       � 
 >Ok   node  AK          AL       �  M        i5  ��
 Z   96  
 >Ok   node  AH  �       N M        i5  ��
 Z   96  
 >Ok   node  AH  �       N M        i5  ��
 Z   96  
 >Ok   node  AH  �       N M        i5  s
 Z   96  
 >Ok   node  AH  s       N M        i5  S
 Z   96  
 >Ok   node  AH  S       N M        h5  $
 Z   :6  
 >Ok   node  AH  $       N Z   %6  %6  %6  %6  %6  %6   0                     @  h   h5  i5   @   _�  Othis  H   Ok  Onode  O   �   �           �   �     �       �  �   �  �5   �  �?   �  �N   �  �_   �  �f   �  �k   �  �   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �,   �   0   �  
 i   �   m   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 4  �   8  �  
 |  �   �  �  
 �  �   �  �  
 
  �     �  
 Q  �   U  �  
 �  �   �  �  
 读凐wKH�    媽�    H�酘�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    脨                               Q      !      �   &   �   .   �   6   �   >   �   F   �   N   �   V   �   \   "   `   #   d   $   h   %   l   &   p   '   t   (      �   )  K G            x       x   �4        �donut::engine::MaterialDomainToString  >翬   domain  A         \  =                         @ 
                    $LN10         $LN9         $LN8         $LN7         $LN6         $LN5         $LN4     翬  Odomain  O   �   �           x   �     �       m �    n �   p �"   y �#   q �*   y �+   r �2   y �3   s �:   y �;   t �B   y �C   u �J   y �K   v �R   y �S   w �Z   y �,   �   0   �  
 r   �   v   �  
 �   !   �   !  
 �   (   �   (  
 �   '   �   '  
 �   &   �   &  
 �   %   �   %  
 �   $   �   $  
    #     #  
   "     "  
 @  �   D  �  
 H塡$H墊$UH崿$0��H侅�  H嬟�    H孁H吚勲  �    W襀婬 W�)�$�  W�)�$�  D)�$�  D)�$�  D)�$�  D)�$p  D)�$`  EW銬)�$P  D)�$@  D)�$0  D(=    �D$xA(�)]序Dd$0D)}�)U�)e囹E痱De]�U�e�M鳫吷tP戉   欚   �   D剐   �  �   D)}�U�)U�]�)]�e�)e�)E�)M �s駾(乞{A(因AY�(向Y乞Y向X�W莉X裦.聎fQ码(妈    �
    駾    駾^序^^餉(�(�T�T羏/蠨(蜠(荅W薊W胿駾MH岴駾U駾e �駾d$ H岲$ 駾D$(�t$0D(�hE(躜  fE魽(虯(乞AY万AY乞X�(膨Y膨X�W纅.羨	W莉Q岭(凌    �  駾^鐷峂華�   H峊$8H岲$8H+蔐峂X�^栩D^餉(弪DD$x(万Y鏏(乞Y悟Y茿(�(�$�  EW篁AY�W垓\闰D厫   D(�$�  (膨AY翧W隕W域Dd$xfE諨(�$@  �\郉)U`D(�$�  A(臙W腧Y艱(�$p  (�$�  fD�W沈\衒A酓(�$�  W繢)mPD(�$P  )epf�)瓈   潣   駾エ   D$8L$HD$XL$h@ �     駻Y   駻!駻iff�     (�(尿YT�(万Y�YL�X�X序X羊H兝H冴u蘄兞H兞鐸冴u�]�H崟�   e怘峂PM燗(�(胒抿AY尿AY詅E�駿YX序AY�(膄潋AY尿AY潋X�(硫AY膄沈AY舔XUDXE痱AY尿X�L$H駾X�嵷   L$h駾X} �X�D$8嶘   �X]吶   D$XfA�呰   �D$x��  �  ��   �    (    L崓�   W蒐岴(3襀峂PE(M8�    L崓�   3襆岴(H嬒�    D(�$0  D(�$`  L崪$�  I媅I媨I嬨]�   �   2   `   �   m   z  
   �  s   �  v   A  
   �  �   �  m   �  �     �      �     H G            .       �4        �donut::engine::Light::SetDirection 
 >4�   this  AJ        "  >B�   direction  AI        AK          >&�   parentToWorld  C      �     r  E  C�      �     -  C�      �    ;  C�       �    I  C�       �     t  B  C�   0   �     */ � EO�          EO�          EO�          D�    >�    scaling  D�   >瘭    rotation  D(  
 >J�    node  AM  %      >&�    worldToLocal  BP  �     >J�    parent  AJ  =     A AJ ~    � �   >&�    localToParent  BP  �    d  M        �4  .Eq M        -5  �� N M        .5  .q5 M        M5  .q5# M        R5  .q
- N N N M        �4  �� M         5  �� N N N M        �4  勈 N% M        a5  傄��&FM��! M        -5  刜
) N M        �5  凣. N1 M        �5  冣(+'0 N& M        �5  傄��&4 M        �5  傄�� M        �5  傄��* N N N Nw M        `5  *�9-��
��)$W#8$8H$Y)U)$U)$448$	LU	
 >�   left  C�      �       >�   up  C�      �    y ) M        �5  倆	6)+* M        -5  	偘)+ N M        �5  倆 M        R5  倆 N N N M        ^5  偁* M        �5  偁* >A    _x  A*  �    C  N N M        ^5  傹 M        �5  傹 N N M        �4  偽 M         5  偽 N N/ M        �5  倂% M        �5  倂 >A    _x  A�   �    n  A�   �    (  >A    _y  A�   �    <  >A    _z  A�   "    �  N N$ M        ]5  侚&)	  M        �5  侻	 
 >A    b  A�   E      M        �5  倎 >A    _x  A)  R    �  >A    _y  A*  v    .  >A    _z  A�   q    ]  N N M        �5  侚&) M        6  侚& M        �5  侚& N N N N2 M        �5  亊]aUF	!	%		"	 M        �5  佔
 N M        �5  伩
 >A    _x  A%  �    w N M        6  仩 N M        6  亊 N N M        ]5  *�9-
) M        �5  
亸 N M        �5  *�9- M        6  *�9- M        �5  *�9- N N N N N Z   �4  _5  b5  D6   �                    @ � h$   �4  �4  �4  �4  �4  �4   5  -5  .5  M5  R5  ]5  ^5  `5  a5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  �5  6  6   �  4�  Othis  �  B�  Odirection  �   &�  OparentToWorld  �  �  Oscaling  (  瘭  Orotation  P  &�  OworldToLocal  P  &�  OlocalToParent  O �   �           .  �     �       `  �   a  �%   b  �.   i  �9   h  ��   i  ��   j  ��   k  �9  m  ��  n  ��  m  �`  n  ��  p  ��  r  ��  p  ��  r  ��  t  �  u  �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 
  �     �  
 !  �   %  �  
 9  �   =  �  
 Q  �   U  �  
 e  �   i  �  
 y  �   }  �  
 �  �   �  �  
 �  �      �  
   �   !  �  
 -  �   1  �  
 Y  �   ]  �  
 �  �      �  
   �   !  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 }  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ,  �   0  �  
 H塡$WH侅0  H孃�    H嬝H吚刞  (    W眚%    W蒆婬 W�)�$   W鲵�$�   (序�$�   )D$@)L$P)D$`)\$p��$�   �$�   H吷tN佇   夃   )D$@侌   )L$P�   )D$`�  )L$p�   )�$�   )�$�   H峊$@H崒$�   �    �gH峊$ �7(则o(乞YP(轍嬎�Y (万YH0�X�(尿X�(万XPH�T$ �YX�Y@ �YH8�X仳X衮XXP�\$(�Yp�Y`(�Yh@�X趄X躜XpX�t$0�    (�$   H嫓$@  H伳0  _�   �   $   m   /   `   �   �   u  �      �   �  G G            �  
   �  �4        �donut::engine::Light::SetPosition 
 >4�   this  AJ          >B�   position  AK          AM       � >&�    parentToWorld  D@    >�    translation  D    
 >J�    node  AI       q >J�    parent  AJ  :     �  M        �4  !
: M        -5  	H N M        .5  !
& M        M5  !
&! M        R5  !
&	 N N N N& M        �4  ��
6"7 N Z   �4  _5  E6   0                    @ 2 h   �4  �4  �4  �4  �4  �4   5  -5  .5  M5  R5   @  4�  Othis  H  B�  Oposition  @   &�  OparentToWorld      �  Otranslation  O �   �           �  �     �       N  �   O  �   P  �!   W  �6   V  �H   W  ��   X  ��   Y  ��   [  ��   \  ��   [  �  \  �  [  �t  \  ��  ]  �,   �   0   �  
 l   �   p   �  
 �   �   �   �  
 �   �   �   �  
 �   �      �  
   �   !  �  
 �  �   �  �  
 H塡$H塴$H塼$WH冹 H儂H嬮H媧H嬍I嬸H嬟vH�
H�
uL嬊H�    �    吚u	�塃8�雑H儃H嬎vH�H�uL嬊H�    �    吚u	�塃<��;H儃vH�H�u)L嬊H�    H嬎�    吚u��E,婩塃4��2繦媆$0H媗$8H媡$@H兡 _�:   i   ?      i   l   n      �   f   �         �     R G            �      �   �4        �donut::engine::DirectionalLight::SetProperty 
 >O�   this  AJ          AN       � 
 >�   name  AK        >  AK P        I   >廆   value  AL  &     �  AP        &  M        .  
 M        0  
 M        1  . M        2  4 N N M        �  ) >_    _Result  AJ  #        AJ P       M           N N N N M        .  &P M        0  &P M        1  ] M        2  c N N M        �  P5# >_    _Result  AJ  X       AJ      P    M          P N N N N M        �4  &/ M        .  & M        0  & M        1  �� M        2  �� N N M        �   >_    _Result  AI  )     �  M           N N N N N                       @ 2 h   �  �    �  '  .  0  1  2  �4  �4   0   O�  Othis  8   �  Oname  @   廆  Ovalue  O �   �           �   �  
   t       �  �   �  �   �  �   �  �#   �  �)   �  �G   �  �L   �  �P   �  �v   �  �{   �  �   �  ��   �  �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 i  �   m  �  
 y  �   }  �  
 Z  �   ^  �  
 $  �   (  �  
 H塡$WH冹 H儂I嬝L婤H嬄H孂vH�I凐u/H�    H嬋�    吚u��G,婥塆4�H媆$0H兡 _肏媆$02繦兡 _�*   f   2         �   F  G G            c   
   V   �4        �donut::engine::Light::SetProperty 
 >=�   this  AJ          AM       F 9  
 >�   name  AK        .  AK V     
  >廆   value  AI       I >   AP          M        .  

 M        0  

 M        1  ! M        2  ' N N M        �  
 >_    _Result  AH         AH V       M          
 N N N N                       H . h
   �  �    �  '  .  0  1  2  �4   0   =�  Othis  8   �  Oname  @   廆  Ovalue  O  �   `           c   �  	   T       /  �
   0  �   /  �   0  �   /  �   0  �:   2  �I   3  �K   7  �,   �   0   �  
 l   �   p   �  
 |   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 \  �   `  �  
 H塡$H塴$H塼$H墊$ AVH冹 L媟H嬮H媧I嬸H嬟H嬍I凗vH�
H�	uL嬊H�    �    吚u�塃8�闁   H嬎I凗vH�H�uL嬊H�    �    吚u	�塃<�雋H嬎I凗vH�H�uL嬊H�    �    吚u	�塃@��:I凗vH�H�u)L嬊H�    H嬎�    吚u��E,婩塃4��2繦媆$0H媗$8H媡$@H媩$HH兡 A^肅   u   H      t   ~   y      �   �   �      �   f   �         �   �  L G            
     �   �4        �donut::engine::PointLight::SetProperty 
 >o�   this  AJ        !  AN  !     � 
 >�   name  AK        G  AK \     �   J  u   >廆   value  AL  (     �  AP        (  M        .  %
 M        0  %
 M        1  7 M        2  = N N M        �  # >_    _Result  AJ  .       AJ \       M           N N N N M        .  %\ M        0  %\ M        1  h M        2  n N N M        �  \# >_    _Result  AJ  _       AJ �       M          _ N N N N M        .  %�� M        0  %�� M        1  �� M        2  �� N N M        �  ��# >_    _Result  AJ  �       AJ �     U    M          �� N N N N M        �4  ��%/ M        .  %�� M        0  %�� M        1  �� M        2  �� N N M        �  �� >_    _Result  AI  +     �  M          �� N N N N N                       @ 2 h   �  �    �  '  .  0  1  2  �4  �4   0   o�  Othis  8   �  Oname  @   廆  Ovalue  O  �   �           
  �     �       ( �   ) �   ( �!   ) �%   ( �+   ) �P   + �U   , �\   / ��   1 ��   2 ��   5 ��   7 ��   8 ��   ; ��   < �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 g  �   k  �  
 w  �   {  �  
 :  �   >  �  
 J  �   N  �  
 0  �   4  �  
 �  �      �  
 2烂   �   $  P G                      �4        �donut::engine::SceneGraphLeaf::SetProperty 
 >A�   this  AJ          D   
 >�   name  AK          D    >廆   value  AP          D                           H     A�  Othis     �  Oname     廆  Ovalue  O�                  �            H  �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 8  �   <  �  
 H塡$H塴$H塼$H墊$ AVH冹 L媟H嬮H媧I嬸H嬟H嬍I凗vH�
H�	uL嬊H�    �    吚u�塃8�轼   H嬎I凗vH�H�uL嬊H�    �    吚u�塃<�榍   H嬎I凗vH�H�uL嬊H�    �    吚u�塃@�闁   H嬎I凗vH�H�
uL嬊H�    �    吚u	�塃D�雋H嬎I凗vH�H�
uL嬊H�    �    吚u	�塃H��:I凗vH�H�u)L嬊H�    H嬎�    吚u��E,婩塃4��2繦媆$0H媗$8H媡$@H媩$HH兡 A^肅   u   H      t   ~   y      �   �   �      �   x   �        {   	     /  f   7        �   �  K G            o     T  �4        �donut::engine::SpotLight::SetProperty 
 >_�   this  AJ        !  AN  !     =
 >�   name  AK        G " AK \       M  ~  �  �   >廆   value  AL  (     ; AP        (  M        .  %
 M        0  %
 M        1  7 M        2  = N N M        �  # >_    _Result  AJ  .       AJ \       M           N N N N M        .  %\ M        0  %\ M        1  h M        2  n N N M        �  \# >_    _Result  AJ  _       AJ �       M          _ N N N N M        .  %�� M        0  %�� M        1  �� M        2  �� N N M        �  ��# >_    _Result  AJ  �       AJ �       M          �� N N N N M        .  %�� M        0  %�� M        1  �� M        2  �� N N M        �  ��# >_    _Result  AJ  �       AJ �       M          �� N N N N M        .  %�� M        0  %�� M        1  �� M        2  �� N N M        �  ��# >_    _Result  AJ  �       AJ     U    M          �� N N N N M        �4  �%/ M        .  %� M        0  %� M        1  �# M        2  �) N N M        �  � >_    _Result  AI  +     . M          � N N N N N                       @ 2 h   �  �    �  '  .  0  1  2  �4  �4   0   _�  Othis  8   �  Oname  @   廆  Ovalue  O   �   �           o  �     �       �  �   �  �   �  �!   �  �%   �  �+   �  �P   �  �U   �  �\   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �  �  �  �  �T  �  �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 n  �   r  �  
 ~  �   �  �  
 A  �   E  �  
 Q  �   U  �  
   �     �  
 %  �   )  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H塡$WH冹 H嬟H孂H嬎H�    �    H嬋H�    �    H�    H嬎�    H峎,H嬋�    H�    H嬎�    H峎8H嬋�    H�    H嬎�    H峎<H嬋H媆$0H兡 _�       r      �   %   o   *   �   1   f   9   �   E   �   L   i   T   �   `   �   g   l   o   �   �   �      �   �  L G            �   
   z   �4        �donut::engine::DirectionalLight::Store 
 >M�   this  AJ          AM       t 
 >痠   node  AI  
     r  AK        
  M        k5  w

 >痠   node  AH  s       N M        k5  \
 Z   76  
 >痠   node  AH  X       N M        j5  A
 Z   86  
 >痠   node  AH  =       N Z   �%  �6  �%  �%  �%                         @  h   j5  k5   0   M�  Othis  8   痠  Onode  O �   P           �   �     D       �  �   �  �.   �  �I   �  �d   �  �z   �  ��   �  �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 +  �   /  �  
 o  �   s  �  
 �  �   �  �  
 �     �   �   A G                       �4        �donut::engine::Light::Store 
 >4�   this  AJ          D   
 >痠   node  AK          D                           @     4�  Othis     痠  Onode  O   �                  �            �  �,   �   0   �  
 f   �   j   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H嬟H孂H嬎H�    �    H嬋H�    �    H�    H嬎�    H峎,H嬋�    H�    H嬎�    H峎8H嬋�    H�    H嬎�    H峎<H嬋�    H�    H嬎�    H峎@H嬋H媆$0H兡 _�       r      �   %   �   *   �   1   f   9   �   E   �   L   u   T   �   `   �   g   ~   o   �   {   �   �   �   �   �   �   �      �   &  F G            �   
   �   �4        �donut::engine::PointLight::Store 
 >n�   this  AJ          AM       � 
 >痠   node  AI  
     �  AK        
  M        k5  ��

 >痠   node  AH  �       N M        k5  w
 Z   76  
 >痠   node  AH  s       N M        k5  \
 Z   76  
 >痠   node  AH  X       N M        j5  A
 Z   86  
 >痠   node  AH  =       N Z   �%  �6  �%  �%  �%  �%                         @  h   j5  k5   0   n�  Othis  8   痠  Onode  O  �   X           �   �     L        �     �.   ! �I   " �d   # �   $ ��   % ��   $ �,   �   0   �  
 k   �   o   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 &  �   *  �  
 j  �   n  �  
 �  �   �  �  
 <  �   @  �  
 H塡$WH冹 H嬟H孂H嬎H�    �    H嬋H�    �    H�    H嬎�    H峎,H嬋�    H�    H嬎�    H峎8H嬋�    H�    H嬎�    H峎DH嬋�    H�    H嬎�    H峎HH嬋�    H�    H嬎�    H峎<H嬋�    H�    H嬎�    H峎@H嬋H媆$0H兡 _�       r      �   %   �   *   �   1   f   9   �   E   �   L   u   T   �   `   �   g   x   o   �   {   �   �   {   �   �   �   �   �   ~   �   �   �   �   �   �   �   �   �   �      �   �  E G            �   
   �   �4        �donut::engine::SpotLight::Store 
 >^�   this  AJ          AM       � 
 >痠   node  AI  
     �  AK        
  M        k5  ��

 >痠   node  AH  �       N M        k5  ��
 Z   76  
 >痠   node  AH  �       N M        k5  ��
 Z   76  
 >痠   node  AH  �       N M        k5  w
 Z   76  
 >痠   node  AH  s       N M        k5  \
 Z   76  
 >痠   node  AH  X       N M        j5  A
 Z   86  
 >痠   node  AH  =       N& Z   �%  �6  �%  �%  �%  �%  �%  �%                         @  h   j5  k5   0   ^�  Othis  8   痠  Onode  O �   h           �   �  
   \       �  �   �  �.   �  �I   �  �d   �  �   �  ��   �  ��   �  ��   �  ��   �  �,   �   0   �  
 j   �   n   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 &  �   *  �  
 k  �   o  �  
 �  �   �  �  
 �  �   �  �  
 7  �   ;  �  
 �  �   �  �  
 H吷tH��   H�`�   �   �   i G                      �5        �std::_Ref_count_obj2<donut::engine::DirectionalLight>::_Delete_this 
 >爷   this  AJ                                 @�     爷  Othis  9
       织   O �   0              �     $       C �    D �   E �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   c G                      �5        �std::_Ref_count_obj2<donut::engine::PointLight>::_Delete_this 
 >�   this  AJ                                 @�     �  Othis  9
       �   O   �   0              �     $       C �    D �   E �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   b G                      �5        �std::_Ref_count_obj2<donut::engine::SpotLight>::_Delete_this 
 >蟑   this  AJ                                 @�     蟑  Othis  9
       鳢   O�   0              �     $       C �    D �   E �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H婣H兞3襀�    �     e G            
       
   �5        �std::_Ref_count_obj2<donut::engine::DirectionalLight>::_Destroy 
 >爷   this  AJ          M        �5   
 >莜   _Obj  AJ         N                        @� 
 h   �5      爷  Othis  9
       Y�   O  �   (           
   �            ? �    @ �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 (  �   ,  �  
 H婣H兞3襀�    �     _ G            
       
   �5        �std::_Ref_count_obj2<donut::engine::PointLight>::_Destroy 
 >�   this  AJ          M        �5   
 >�   _Obj  AJ         N                        @� 
 h   �5      �  Othis  9
       y�   O�   (           
   �            ? �    @ �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
    �   $  �  
 H婣H兞3襀�    �     ^ G            
       
   �5        �std::_Ref_count_obj2<donut::engine::SpotLight>::_Destroy 
 >蟑   this  AJ          M        �5   
 >   _Obj  AJ         N                        @� 
 h   �5      蟑  Othis  9
       i�   O �   (           
   �            ? �    @ �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
    �   $  �  
 3烂   �   �   H G                      �        �std::_Ref_count_base::_Get_deleter 
 >e&   this  AJ          D    >�%   __formal  AK          D                           @�     e&  Othis     �%  O__formal  O�   0              �     $       � �    � �   � �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 �   �      �  
 H冹HH峀$ �    H�    H峀$ �    �
   �      6            �   �   F G                       k        坰td::_Throw_bad_array_new_length 
 Z   ^   H                      @        $LN3  O  �   (               �            J �   K �,   �   0   �  
 �      �     
 �   �   �   �  
 H冹(H�
    �    �   P      �      �   w   7 G                     �        坰td::_Xlen_string 
 Z   6   (                      @        $LN3  O �   (              �            		 �   
	 �,   �   0   �  
 s      w     
 �   �   �   �  
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   	   �   �   �   �   �      ,  �   O  �   U  �   [  �      �   '  r G            `     `  �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >h   _Ptr  AK          AW       D/  >   _Count  AL       G4  AP         B M        �5  E
(?SD3$--K
 Z   �   >#     _New_capacity  AH  �     �  * N  V r  AM  O     =  b �  AH �     G  ,  AJ �       M        ?5  �� M        "   �� N N M        t  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M          ��?�� >   _Count  AJ  �      * M        �  ��

*%
u- M        �  ��	)
��
 Z   k   >    _Block_size  AJ  �     �  �  AJ �       >    _Ptr_container  AH  �       AH �     }  b 
 >0    _Ptr  AV  �       AV �     ~ V "  M        v  ��
 Z   �   N N M        v  ��
 Z   �   N N N N N M        �  X(  M          X' >    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M          -�W M        9  �&P M        �  �
)/
 Z   �  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        w  
�#
2
 Z   S   >    _Ptr_container  AP        AP +    4  *  >    _Back_shift  AJ      
  AJ Z      N N N N N M        !  L4 N M        �  $# >p    _Result  AM  '       AM 8      M          ' N N                       @ n h   v  w  x  �  �  �         !  "  9  �  �  �  �  �    t  u  �  �    7  ?5  �5         $LN93  @   �  Othis  H   h  O_Ptr  P     O_Count � 槸  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_66f57f934f28d61049862f64df852ff0>  O �   h           `  �  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ]  �   a  �  
 m  �   q  �  
 �  �   �  �  
 Y  �   ]  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 W  �   [  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 0  �   4  �  
 �  �   �  �  
 �  �   �  �  
 a     e    
 <  �   @  �  
 H婹H�    H呉HE旅   *      �   �   : G                      W        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              H     $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H                       Y    20    2                       _   
 
4 
2p    B           
      
      e    20    <                       k   
 
4 
2p    B                       q    20    <                       w   
 
4 
2p    B                       }    �                              �    B                             �    T
 4	 2�p`    [                       �   ! �     [                      �   [   8                      �   !       [                      �   8  T                      �   !   �     [                      �   T  `                      �    20    ^                       �    ��p`P0        j            �       \                        �   (           �      �   
    `   �   	�  20    \           )      )      �   
 
4 
2p    O          *      *      �    20    6           +      +      �   ! d t     6          +      +      �   6   �           +      +      �   !   t     6          +      +      �   �   �           +      +      �   #Tf
 
4 
2p    N           ,      ,      �    20    �           -      -      �   
 
4 
2p    c           .      .      �    20    G           /      /      �   A A� 8x ,h �0    �           0      0      �   
 
4( 
& p      =           1      1         ! h     =          1      1         =   �          1      1         !       =          1      1         �  �          1      1      
    t] 4\ Z P      @           2      2         !_ _�# V�$ M�% @�& 7�' .�( %�) �* x+ h,     @       ,   2   0   2   4      @   �          2      2         !   �#  �&     @          2      2         �            2      2         !       @          2      2           .          2      2      %    d 4 2p    r           3      3      +   "6R
 
4 
2p    4           4      4      4   
 
d 
2p               6      6      :   ! T               6      6      :      '           6      6      @   ! 4    '          6      6      @   '   v           6      6      F   !      '          6      6      @   v   �           6      6      L   !                 6      6      :   �   �           6      6      R   >U T 4
 R�p`    1          7      7      [   � � �  
  � x h 
4 
�p    �           8      8      d    d	 4 Rp    �           9      9      j   
 
4 
2p    �           :      :      p    d T 4 2p    �           ;      ;      v   
 
4 
2p    @           <      <      |    T 4
 R�p`    M          =      =      �   � 	
 
4 
 p                >      >      �    d	 4 Rp    �           ?      ?      �   
 
4 
2p    �           @      @      �   
 t	 d T 4 2�    o          A      A      �   
 
4 
2p    4           B      B      �    T 4
 R�p`    1          C      C      �   � � � 
 
4 
�p    �           D      D      �    d	 4 Rp    �           E      E      �   
 
4 
2p    �           F      F      �   
 t	 d T 4 2�    
          G      G      �   
 
4 
2p    4           H      H      �   0 0� x h �0    �           I      I      �   A A� ;� 6�	 1�
 ,� $� x
 h 4   p      ]          J      J      �   t t�
 i� Y� H�
 ?� 5� 0� (� x h ( 0      .          K      K      �    h 4 �p    �          L      L      �   F F� @� :� 4� .� )� $� �	 x
 h  p0              M      M      �    R0    -           N      N      �    20               O      O      �   u u�
 Xx Eh ! ���
�p
`	0P    �          P      P         ' 'h  "      �           Q      Q      	    20    +           R      R          20    +           S      S          20    +           T      T          B      :           V      V      !                               �      �      �   Unknown exception                             �      �      �                               �      �      �   bad array new length                                �      9                                 ?      E      K                   .?AVbad_array_new_length@std@@     L               ����                      <      �                   .?AVbad_alloc@std@@     L              ����                      B      �                   .?AVexception@std@@     L               ����                      H      �   string too long     ����    ����        ��������                                                            �      �      �      �       �   (   �   0   �                                                                                       �      �      �      �       �   (   �   0   �   8   �   @   �   H   �                                                                                       �      �      �      �       �   (   �   0   �   8   �   @   �   H   �                                                                                             �      �      �       �   (   �   0   �   8   �   @   �   H   �                                                                                             �      �      �       �   (   �   0   �   8   �   @   �   H   �   color irradiance angularSize DirectionalLight type intensity innerAngle outerAngle radius range SpotLight PointLight Opaque AlphaTested AlphaBlended Transmissive TransmissiveAlphaTested TransmissiveAlphaBlended Count <Invalid>                                             &      �      �      �       �                                               5      �      �      �       �                                               D      �      �      �       �                                         H      �      �                         �                   �               ����    @                   H      �                                         B      �      �                         �                           �      �              ����    @                   B      �                                         <      �      �                         �                                   �      �      �              ����    @                   <      �                   .?AV_Ref_count_base@std@@     L                         �                   �               ����    @                   �      �                                         �      �      �                   .?AVSceneGraphLeaf@engine@donut@@     L                         �                   �               ����    @                   �      �                                         �      �      �                   .?AVLight@engine@donut@@     L                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AVDirectionalLight@engine@donut@@     L                                                                  �      �              ����    @                   �      �                                                                        .?AVSpotLight@engine@donut@@     L                                                                  �      �              ����    @                                                                                                 .?AVPointLight@engine@donut@@     L                                                             #      �      �              ����    @                                                                  )      ,      &                   .?AV?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@     L                         /                           2      �              ����    @                   )      ,                                         8      ;      5                   .?AV?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@     L                         >                           A      �              ����    @                   8      ;                                         G      J      D                   .?AV?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@     L                         M                           P      �              ����    @                   G      J   5鷰<   狑瓢>  �?      �?      �?  碆�����      �?                       ��������������       �       �   �   7   5 
Q4        donut::engine::SceneGraphLeaf::`vftable'     W      W  
    �   O   M 
S4        std::_Ref_count_obj2<donut::engine::DirectionalLight>::`vftable'     �      �  
    �   H   F 
S4        std::_Ref_count_obj2<donut::engine::SpotLight>::`vftable'    �      �  
    �   I   G 
S4        std::_Ref_count_obj2<donut::engine::PointLight>::`vftable'       �      �  
    �   .   , 
棻        donut::engine::Light::`vftable'      Z      Z  
    �   9   7 
棻        donut::engine::DirectionalLight::`vftable'       ]      ]  
    �   2   0 
棻        donut::engine::SpotLight::`vftable'      `      `  
    �   3   1 
棻        donut::engine::PointLight::`vftable'     c      c  
    �   (   & 
34        std::exception::`vftable'    '      '  
    �   (   & 
34        std::bad_alloc::`vftable'    -      -  
    �   3   1 
34        std::bad_array_new_length::`vftable'     0      0  
 噾姏@|#邛bn軫�8cI橗cS\�<@垯僙% �腤\�"�4膱毚\渴墿k �,7`k椭桦�'洋m|贵羛yQA桦�'洋m|巚鷍I捃
絺撴oK口镪颗qㄙT��0曒�'��葙(�q湁�8霫绽4该~(��
唷鷹�#�%9Q;�
l&5�"C筯�K蜌�(u椯I睦u栟綈�i炭�23溞�9��聯蓐趇莿�pg$睤%埧^^守-
9襤钍d�+蛈+極A1G徂fL�"镇弒(酛蛝R}雰�咉鑗鼱qOmmp悩帟n血髅z	G�l绚m;/�Qz	G�l�#飷> z	G�l徖�>橂wz嚤踖p禭c�&敡?諭嚤踖p禭9"虰錱6萪O��"伔狂4K霵婬(掤捊貴k{u�啳医貴k{�+豖韘S壗貴k{<盫�荷dD預!Z�IW谏g
&7峞誝濑s嗦髌\鯼"kD园馈^Y@[�:b8�4n蟤
Hz梎$<端祆癜~tH狊p榈|嶀預棊膬�屓绀?貚犷A棊膬m61CC	Oy膞#� 饨鞺銭�h7q/b茲l�
稉C提;Z弅朏麢め緔絩�'镣GZ坺��_#?囘r1鷊Cl鉟么�7髳竜@_x髦x�3H&犻闗絚�鰋Yj凊�V1貃-OLts�1j諮l箩邆5>鷖坐鴙醉箩邆5>o�9冃n�箩邆5>~;A嬰t舘� �蹰k~�轖	hf�['o鋇y虁�r�r"8豸g�x�"諺仵� 樓�l枦�*劉暦M培�-帒钥^'鷴V[`潯�;チ淖3uV齼c�=�
鰛�osM[zT琌J{恰簂罘c犔m溌y钔29,帅be2螘_喑�矺�$蟊惺鬩��Г叵*E%溰韘7�~龣�-�N	YyFl	鏤Z怪箊�*Tg觪�.蟨>F�q@骫頱 �$繐�6U輈潁瓠
斻

S%X瘿Z齐u{�1狇妃I词脫^W�6亣鬾洪騯飨岟镫7�)獺鬬iC�昸鳐3鑓*,毀Vw�)邏m棶}&xX偪S%qS匏aP驜毠辏f圲╭端祆癜~t�(C譔�w哈�(�$羊丌2�*跒珬KKe�6髹A
盦v浼�ч7w5苦�8猳Dh&殒介軐偵鼕E7亮?YLcwr��~Ц鍐3��隟"湃o�H3L蹸CY贸"薌衳磩侣
疕猣侌—U�揰逿Z匭漘!�8╧�u荏�4６遂祚皛t-Z_纵?8Ο;i旭�1垖�
崥�&w紼Df�棒褃傰躔7颦硣KES�;穴3躔7颦硣K孨kC槸孵�7颦硣KJ<旄闑荊瘊衚癙�0闵3�瘊衚癙嬖NAVe瘊衚癙�*娚胜凨�$蟊惺魨3竄6o稍%I栶賑?T争谿#�pf]{謑p�N铰嚂0丽Υ貳
砹瀙曺nN鵘J庵嵊OO>	tF{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这朸橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 夒嘕-WV8o��腫62V號斘X椂z[褫桦hdd�a�:tq(h魤�?UT獋雵J-WV8o�+蔑�"Q9E\$L釉�嶺�	�錗{>�0
蟨雵J-WV8o覶4�"
�")职F瘔�銖騹侍VHD緔HЪ蔏0mV$Q翜赿e%�9E\$L釉轞�6	褔諴��雵J-WV8o汿D椣9E\$L釉藿�鱞2ぉ雵J-WV8oz邘c膋c徣�h;昔瓞p�k
损醯%裄�1�8]Z�j麐m+奉�K��8�	鱘ˊ懻�%較軝�,紝稡+$�"钞d蜑臈J虉 页��
嗽cFg鸮で箾�様0�.e�=�㏒阔鑓闲�
墸g丢遧�6藢<c覂m9E\$L釉轎4u�=Z�9.楞ч鷃兟+d+眔Aom�昈d婸潤昬*楏W�1嚲\僎&|篇4胎�1� {楚�旲呚�!斈wvu顈騼7RN砛菟�-@贗EU�閝!鲞鞹B閣�)肣磃f黀促]靭PjRx$[�9E\$L釉轤艮�:_嫫﹥�,仕LC�9E\$L釉�+$�"钞d�菟�-@�8勫��8U��41sn篂外瞾賋靭Pj4�m西`9E\$L釉薹_K� �#鄕滑嶕賘?�/閤~�9E\$L釉轎4u�=菟�-@贗EU�閝!鲞鞹B9O筵儎縒芝I蛿;8賋靭Pj盐�`峁s�9E\$L釉蕙郎GG}q鄕滑嶕�&Vp譃b
9E\$L釉轎4u�=cs�487�hK嗉ДヴGR
歃础Y詼p緸�<蟜bt2W倚坥綷�+炝
y/)Vk6甧唢呡鋉倛箮m煯�/谚泹嵴d繅鬮朏菜{.嘕-WV8o曋赛�/谚泹嵴/谚泹嵴�9�3瑞翌炪丹]B2倃齼_*窶圹涩�6╇嘕-WV8o额	hQ�)雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H詠zv(鏪o祹a鶪Y[o祹a鶪Y[o祹a鶪Y[o祹a鶪Y校�"灼K�詘溏眘#醂�.豋M*阀�鰵�/擷;G穌徘	
~●璏G�
c蠬�R訛v轺�:I�uR7P哸�簩Z蟳蹑爄苴憽2�;]衉鄄nm�
尊梠{竕聄廬7�'o�

�o{�4u�4
櫽�>�5镨雔,4��;儗,4��;儗,4��;儗�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2�$l_	-~"�:邍A愦靮鸬2�>料C5��\&2溫户︳嶕�%ZZ�$为赞G刹~赣 "^惋砤��\&2�0雓o頿#qM�5<A:蓰咨难栨熸� �\&2淥K/sM麌#qM�5<A:蓰咨难栨熸� �\&2湪Ｐ
獇0�#qM�5<A:蓰咨难栨熸� �\&2溚覓�蚦�%ZZ�$为赞G刹~赣 "^惋砤��\&2滀嘟 }%ZZ�$为赞G刹~赣 "^惋砤��\&2淯D5y部6%%ZZ�$为赞G刹~赣 "^惋砤腮Ｊ鴸豴�!t鎫祻R�^笵A傮L鶾B蔲�(l<9n值胢7_煓�(M`H#^�?�<溺钼�29癨8�%胄5]_иm磩^Q噴_�={檎�X>T娖�#	z鈨j4亖�(麬
A葁槚恎皸1洐蹇性�/剗螁眥Pj�$逬H弈W�7陴�!阑,�粰趭+�揃T爃.L�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S        & 
             .debug$T       p                 .rdata         L       4G蹾                         7          .text$mn             D悱�     .debug$S          
           .text$mn       -      
首     .debug$S       8  
           .text$mn    	         �%     .debug$S    
            	    .text$mn             �%     .debug$S                     .text$mn    
   �       钽R     .debug$S       �         
    .text$mn       �      q�      .debug$S       �             .text$mn       �       .D�     .debug$S       0             .text$mn       .       ��     .debug$S       �              .text$mn               礻烿     .debug$S       �              .text$mn       :      眡�     .debug$S                    .text$mn         
        .debug$S       �	  \           .text$mn       ]     iV鶻     .debug$S       �             .text$mn       �     
!     .debug$S       �	  *           .text$mn       8      甡�&     .debug$S                     .text$mn    !   #       )A     .debug$S    "   �          !    .text$mn    #   .     )膁     .debug$S    $   �         #    .text$mn    %   �      �4 �     .debug$S    &   �         %    .text$mn    '   (       芌     .debug$S    (   �         '    .text$mn    )   (       芌     .debug$S    *   �         )    .text$mn    +   (       芌     .debug$S    ,   �         +    .text$mn    -   <      .ズ     .debug$S    .   0  
       -    .text$mn    /   <      .ズ     .debug$S    0   L  
       /    .text$mn    1   !      :著�     .debug$S    2   <         1    .text$mn    3   2      X于     .debug$S    4   <         3    .text$mn    5         峦諡     .debug$S    6   ,         5    .text$mn    7         峦諡     .debug$S    8             7    .text$mn    9         峦諡     .debug$S    :             9    .text$mn    ;   �      u`     .debug$S    <   �  "       ;    .text$mn    =   r       陨W2     .debug$S    >   8         =    .text$mn    ?           !(;�     .debug$S    @   p  
       ?    .text$mn    A   ^      wP�     .debug$S    B   �         A    .text$mn    C          .B+�     .debug$S    D   �          C    .text$mn    E         ��#     .debug$S    F   �          E    .text$mn    G         ��#     .debug$S    H   �          G    .text$mn    I   +      嗇�     .debug$S    J   �          I    .text$mn    K   +      &b8�     .debug$S    L   �          K    .text$mn    M   +      (寶     .debug$S    N   �          M    .text$mn    O   @      偑w     .debug$S    P   �          O    .text$mn    Q   4      縑妁     .debug$S    R   �          Q    .text$mn    S   4      U诟     .debug$S    T   �          S    .text$mn    U   N      W	H�     .debug$S    V   �         U    .text$mn    W   4      懍苗     .debug$S    X   �          W    .text$mn    Y   B      贘S     .debug$S    Z             Y    .text$mn    [   B      贘S     .debug$S    \            [    .text$mn    ]   B      贘S     .debug$S    ^   �          ]    .text$mn    _   H       襶.      .debug$S    `   �         _    .text$mn    a   1     蟛ㄆ     .debug$S    b             a    .text$mn    c   1     萾�O     .debug$S    d   �         c    .text$mn    e   M     斀茓     .debug$S    f   �         e    .text$mn    g   �      M%     .debug$S    h   h         g    .text$mn    i   �      @繞�     .debug$S    j   �         i    .text$mn    k   �      擌=!     .debug$S    l   p         k    .text$mn    m        妀     .debug$S    n   �         m    .text$mn    o   O      c堄     .debug$S    p   �         o    .text$mn    q          覫勀     .debug$S    r   �          q    .text$mn    s          �猴     .debug$S    t   �          s    .text$mn    u   �      iｚ�     .debug$S    v   �         u    .text$mn    w          �+斏     .debug$S    x   �          w    .text$mn    y          聏T�     .debug$S    z   �          y    .text$mn    {          rQ4�     .debug$S    |   �          {    .text$mn    }   3      圎     .debug$S    ~   h         }    .text$mn       �       鐤;a     .debug$S    �   P             .text$mn    �   G      〔愎     .debug$S    �   t         �    .text$mn    �   \     <k	�     .debug$S    �     (       �    .text$x     �         "E萷�    .text$mn    �   \      N紶     .debug$S    �   L         �    .text$mn    �   �   	   堳腕     .debug$S    �   p         �    .text$mn    �   �      j褳     .debug$S    �   �         �    .text$mn    �          .B+�     .debug$S    �            �    .text$mn    �   �      2�     .debug$S    �   �         �    .text$mn    �   x      裘4     .debug$S    �   �         �    .text$mn    �   .     盋,p     .debug$S    �   �  >       �    .text$mn    �   �     龓壧     .debug$S    �   X         �    .text$mn    �   �      嶙硫     .debug$S    �   �         �    .text$mn    �   c      跻萿     .debug$S    �   �         �    .text$mn    �   
     *     .debug$S    �   �         �    .text$mn    �          簎x�     .debug$S    �   X  
       �    .text$mn    �   o     e�-o     .debug$S    �   t  &       �    .text$mn    �   �   
   環     .debug$S    �   H         �    .text$mn    �          .B+�     .debug$S    �            �    .text$mn    �   �      E磇S     .debug$S    �   �         �    .text$mn    �   �      	�'     .debug$S    �   4         �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �   
       肷瞰     .debug$S    �   P  
       �    .text$mn    �   
       肷瞰     .debug$S    �   H  
       �    .text$mn    �   
       肷瞰     .debug$S    �   H  
       �    .text$mn    �          �猴     .debug$S    �   ,         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �   `     匮�5     .debug$S    �   �  B       �    .text$mn    �         崪覩     .debug$S    �   �          �        \       _        x                �                �                �            copysign             �                �                �       3              G        ,      �        L      ]        k          i�                   �      -        �      Y        �          i�                   �      1              E        3      /        _      [        �          i�                   �      �        �               �                      �        9      �        �      A        �      C        �      �              �        x      �        �      �        �      o        :      ?        b              �      }        �      s        A      �        |      �        	      U        6          i�                   c      q        �      i        �      �              �        �      �        �      u        $	      �        f	      �        �	      =        �	      Q        �	          i�                   
      ;        �
      a        �
      w        ,      g        {      �        �      �        �      �        �      O        �          i�                   �      e        =
      {        k
      m        �
      �        �
      �               �        �      W        �          i�                   �      c        S      y        �      k        �      �              �        :      �        �      S        �          i�                                  x               �               �                              8               �               �               .               L      %        �              �      #                      G              �      '        i              �              �              7      	        Y      +              )        �              �              )              u      
        �              �      5        0      �        z      �        �      I                  i�                   X      9        �      �        �      �              M        _          i�                   �      7        �      �        !      �        i      K        �          i�                   �      !        *              g              �      �        .               A               V           memcmp           memcpy           memmove          sqrt             $LN13       _    $LN5        3    $LN10       ]    $LN7        -    $LN13       Y    $LN10       /    $LN16       [    $LN3        �    $LN4        �    $LN3       �    $LN4        �    $LN93   `  �    $LN100      �    $LN37   ^   A    $LN40       A    $LN100  <  �    $LN4    �   �    $LN6    �   �    $LN8    �   �    $LN10   �   �    $LN11   �   �    $LN105      �    $LN13   \   �    $LN4       �    $LN5    #   �    $LN6    +   �    $LN7    3   �    $LN8    ;   �    $LN9    C   �    $LN10   K   �    $LN18       �    $LN21       o    $LN52           $LN21       U    $LN19       i    $LN27       �    $LN11       �    $LN30       u    $LN24       �    $LN146      �    $LN32       =    $LN8        Q    $LN57   �   ;    $LN60       ;    $LN84       a    $LN33       g    $LN10       �    $LN10       �    $LN77       �    $LN11       O    $LN78       e    $LN23       m    $LN16       �    $LN16       �    $LN149      �    $LN11       W    $LN78       c    $LN17       k    $LN12       �    $LN12       �    $LN101      �    $LN11       S    $LN17       %    $LN12           $LN71       #    $LN55           $LN124          $LN4            $LN4            $LN152          $LN4            $LN8        I    $LN8        M    $LN8        K    $LN14   :       $LN17           .xdata      �          F┑@_        n      �    .pdata      �         X賦鷂        �      �    .xdata      �          （亵3        �      �    .pdata      �          T枨3        �      �    .xdata      �          %蚘%]              �    .pdata      �         惻竗]        -      �    .xdata      �          （亵-        S      �    .pdata      �         2Fb�-        |      �    .xdata      �          %蚘%Y        �      �    .pdata      �         惻竗Y        �      �    .xdata      �          （亵/        �      �    .pdata      �         2Fb�/        %       �    .xdata      �          %蚘%[        X       �    .pdata      �         惻竗[        �       �    .xdata      �          懐j灦        �       �    .pdata      �         Vbv        �       �    .xdata      �          �9��        !      �    .pdata      �         �1案        ;!      �    .xdata      �          蔜-搴        [!      �    .pdata      �         愶L�        �!      �    .xdata      �         �qL兒        "      �    .pdata      �         ~蕉胶        ~"      �    .xdata      �         |焙        �"      �    .pdata      �         瞚挨�        B#      �    .xdata      �         S!熐�        �#      �    .pdata      �         �o埡        $      �    .xdata      �          （亵A        h$      �    .pdata      �         翎珸A        �$      �    .xdata      �         d鞬�        �$      �    .pdata      �         =睨        .%      �    .xdata      �   	      � )9�        �%      �    .xdata      �         S秢�        &      �    .xdata      �          ]�鐑        x&      �    .xdata      �          （亵�        �&      �    .pdata      �         夋��        '      �    .xdata      �          %蚘%o        L'      �    .pdata      �         踣蔞o        �'      �    .voltbl     �          -哥?    _volmd      �    .xdata      �          （亵        (      �    .pdata      �         鶽        L(      �    .xdata      �         雹�	        �(      �    .pdata      �         q��        �(      �    .xdata      �         ;O �        .)      �    .pdata      �         X妽�        z)      �    .voltbl     �          �;�    _volmd      �    .xdata      �          %蚘%U        �)      �    .pdata      �         咝<U        �)      �    .voltbl     �          賚cU    _volmd      �    .xdata      �          （亵i        /*      �    .pdata      �         暫`gi        {*      �    .xdata      �          %蚘%�        �*      �    .pdata      �         X^�        R+      �    .xdata      �          （亵�        �+      �    .pdata      �         羲X#�        %,      �    .xdata      �          To�?u        l,      �    .pdata      �         U,ou        �,      �    .xdata      �          f8N�        �,      �    .pdata      �         現��        G-      �    .xdata      �         
礮�        �-      �    .pdata      �         筦硵        �-      �    .xdata      �         @瑹        &.      �    .pdata      �         wD>駭        q.      �    .xdata      �          UnKd�        �.      �    .pdata      �         砺�)�        /      �    .xdata      �   8      堊�        Q/      �    .pdata      �         �(癛�        �/      �    .xdata               .�"(�        �/          .pdata              F槿�        60         .xdata              (蕫�        �0         .pdata              �$"^�        �0         .xdata               O�=        1         .pdata              頄u�=        D1         .voltbl              #3�7=    _volmd         .xdata               %蚘%Q        j1         .pdata              嘳�Q        �1         .xdata      	         釫�<;        �1      	   .pdata      
         *鬰;        O2      
   .xdata              驫@<;        �2         .pdata              "�b;        k3         .xdata      
        Dc団;        �3      
   .pdata              `獤;        �4         .xdata              蜼U�;        5         .pdata              槜姚;        �5         .xdata              炖Ｚ;        66         .pdata              eNGa;        �6         .voltbl              灸Z;    _volmd         .xdata               �a        T7         .pdata              WX颽        �7         .voltbl              u    _volmd         .xdata               摢讄g        %8         .pdata              x,塯        |8         .xdata               D[亪        �8         .pdata              菏珗        9         .xdata               %蚘%�        [9         .pdata              駷tL�        �9         .xdata               嘋c魱        �9         .pdata              沀啠�        }:         .xdata               %蚘%O        ;         .pdata               砺�)O        J;          .xdata      !         �e        �;      !   .pdata      "        堒�e        �;      "   .voltbl     #         �e    _volmd      #   .xdata      $         K,�.m        C<      $   .pdata      %        �/c峬        �<      %   .xdata      &         D[亷        �<      &   .pdata      '        �!{�         =      '   .xdata      (         %蚘%�        ]=      (   .pdata      )        詊輻�        �=      )   .xdata      *         U费聻        �=      *   .pdata      +        ９集�        j>      +   .xdata      ,         %蚘%W        �>      ,   .pdata      -        嘳�W        )?      -   .xdata      .         �c        X?      .   .pdata      /        WX颿        �?      /   .voltbl     0         u    _volmd      0   .xdata      1         �?槖k        @      1   .pdata      2        訞)鬹        n@      2   .xdata      3         D[亰        継      3   .pdata      4        ]丶R�        鼲      4   .xdata      5         %蚘%�        ;A      5   .pdata      6        v�.�        {A      6   .xdata      7         U费職        篈      7   .pdata      8        er=�        KB      8   .xdata      9         %蚘%S        跙      9   .pdata      :        嘳�S        C      :   .xdata      ;         NTo�%        <C      ;   .pdata      <        ��%        僀      <   .xdata      =  0       篷崉        蒀      =   .pdata      >        ｍTh        D      >   .xdata      ?  4       稭 #        RD      ?   .pdata      @        Mac{#              @   .xdata      A         `$h�        顳      A   .pdata      B        w噳�        /E      B   .xdata      C  4        曡8        nE      C   .pdata      D        8魣        轊      D   .voltbl     E         脘輒'    _volmd      E   .xdata      F         僣�        MF      F   .pdata      G        噖sb        獸      G   .xdata      H         （亵        G      H   .pdata      I        �*^�        2G      I   .voltbl     J         脘輒+    _volmd      J   .voltbl     K         脘輒)    _volmd      K   .xdata      L  $       「w        [G      L   .pdata      M        <              M   .xdata      N         ,t         闓      N   .pdata      O        v��        >H      O   .xdata      P         （亵I        慔      P   .pdata      Q         ~        酘      Q   .xdata      R         （亵M        0I      R   .pdata      S         ~        yI      S   .xdata      T         （亵K        罥      T   .pdata      U         ~        J      U   .xdata      V         �9�        TJ      V   .pdata      W        礝
        盝      W   .rdata      X                     
K     X   .rdata      Y         �;�         $K      Y   .rdata      Z                     KK     Z   .rdata      [                     bK     [   .rdata      \         �)         凨      \   .xdata$x    ]                     癒      ]   .xdata$x    ^        虼�)         襅      ^   .data$r     _  /      嶼�         鮇      _   .xdata$x    `  $      4��         L      `   .data$r     a  $      鎊=         oL      a   .xdata$x    b  $      銸E�         塋      b   .data$r     c  $      騏糡         萀      c   .xdata$x    d  $      4��         釲      d       !M               4M  @       .rdata      e         燺渾         cM      e   .data       f          烀�          塎      f       組     f   .rdata      g  8                   銶     g   .rdata      h  P   
                	N     h   .rdata      i  P   
                %N     i   .rdata      j  P   
                LN     j   .rdata      k  P   
                lN     k   .rdata      l         築p�         峃      l   .rdata      m         \磧f               m   .rdata      n         S%D�         肗      n   .rdata      o         9Z鴓         釴      o   .rdata      p         玘V         O      p   .rdata      q  
       +莁/         O      q   .rdata      r         l踆�         :O      r   .rdata      s         撸O�         XO      s   .rdata      t         w綦�         vO      t   .rdata      u         @0Sl         廜      u   .rdata      v  
       J幉�               v   .rdata      w         l�
2         肙      w   .rdata      x         � �         酧      x   .rdata      y         悹篺         鶲      y   .rdata      z  
       蠙�         P      z   .rdata      {  
       �         9P      {   .rdata      |         u譮�         XP      |   .rdata      }         OS淗         凱      }   .rdata      ~         (扷�         盤      ~   .rdata        
       �x�         蒔         .rdata      �  (                   隤     �   .rdata      �  (                   +Q     �   .rdata      �  (                   dQ     �   .rdata$r    �  $      'e%�         濹      �   .rdata$r    �        �          禥      �   .rdata$r    �                     蘍      �   .rdata$r    �  $      Gv�:         釷      �   .rdata$r    �  $      'e%�         R      �   .rdata$r    �        }%B         R      �   .rdata$r    �                     /R      �   .rdata$r    �  $      `         ER      �   .rdata$r    �  $      'e%�         dR      �   .rdata$r    �        �弾         嘡      �   .rdata$r    �                     ≧      �   .rdata$r    �  $      H衡�         蒖      �   .data$rs    �  *      8V綊         驲      �   .rdata$r    �        �          S      �   .rdata$r    �                     /S      �   .rdata$r    �  $      Gv�:         KS      �   .rdata$r    �  $      'e%�         pS      �   .data$rs    �  2      詂刑         朣      �   .rdata$r    �        �          維      �   .rdata$r    �                     釹      �   .rdata$r    �  $      Gv�:         T      �   .rdata$r    �  $      'e%�         3T      �   .data$rs    �  )      渞H�         PT      �   .rdata$r    �        }%B         oT      �   .rdata$r    �                     奣      �   .rdata$r    �  $      `               �   .rdata$r    �  $      'e%�         蒚      �   .data$rs    �  4      �6鲥         馮      �   .rdata$r    �        �弾         U      �   .rdata$r    �                     AU      �   .rdata$r    �  $      H衡�         gU      �   .rdata$r    �  $      'e%�         朥      �   .data$rs    �  -      ko揘         稶      �   .rdata$r    �        �弾         赨      �   .rdata$r    �                     鵘      �   .rdata$r    �  $      H衡�         V      �   .rdata$r    �  $      'e%�         @V      �   .data$rs    �  .      堻�         bV      �   .rdata$r    �        �弾         哣      �   .rdata$r    �                           �   .rdata$r    �  $      H衡�         芕      �   .rdata$r    �  $      'e%�         颲      �   .data$rs    �  M      P唔         0W      �   .rdata$r    �        }%B         sW      �   .rdata$r    �                     瞁      �   .rdata$r    �  $      `         馱      �   .rdata$r    �  $      'e%�         9X      �   .data$rs    �  F      6揈�         sX      �   .rdata$r    �        }%B         疿      �   .rdata$r    �                     鏧      �   .rdata$r    �  $      `         Y      �   .rdata$r    �  $      'e%�         `Y      �   .data$rs    �  G      �#B�         沋      �   .rdata$r    �        }%B         豗      �   .rdata$r    �                     Z      �   .rdata$r    �  $      `         JZ      �       孼           .rdata      �         �7甚         榋      �   .rdata      �         i��         ╖      �   .rdata      �         v靛�         繸      �   .rdata      �         艳�         衂      �   .rdata      �         �腾�         鑊      �   .rdata      �          K{          [      �   .rdata      �         �;�         [      �   .rdata      �         ǜ8�          [      �       0[           .rdata      �         殐尼         B[      �   .rdata      �         � �         i[      �   .rdata      �          錢         怺      �   .rdata      �         o冺�         穂      �   _fltused         .debug$S    �  D          g   .debug$S    �  \          �   .debug$S    �  T          �   .debug$S    �  X          �   .debug$S    �  <          h   .debug$S    �  H          i   .debug$S    �  @          j   .debug$S    �  @          k   .debug$S    �  4          X   .debug$S    �  4          Z   .debug$S    �  @          [   .chks64     �  �                轠  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?isempty@frustum@math@donut@@QEBA_NXZ ?_Xlen_string@std@@YAXXZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??1VertexAttributeDesc@nvrhi@@QEAA@XZ ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z ?MaterialDomainToString@engine@donut@@YAPEBDW4MaterialDomain@12@@Z ?IsActive@LightProbe@engine@donut@@QEBA_NXZ ?FillLightProbeConstants@LightProbe@engine@donut@@QEBAXAEAULightProbeConstants@@@Z ??1SceneGraphLeaf@engine@donut@@UEAA@XZ ?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ ?GetLocalBoundingBox@SceneGraphLeaf@engine@donut@@UEAA?AU?$box@M$02@math@3@XZ ?GetContentFlags@SceneGraphLeaf@engine@donut@@UEBA?AW4SceneContentFlags@23@XZ ?Load@SceneGraphLeaf@engine@donut@@UEAAXAEBVValue@Json@@@Z ?SetProperty@SceneGraphLeaf@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??_GSceneGraphLeaf@engine@donut@@UEAAPEAXI@Z ??_ESceneGraphLeaf@engine@donut@@UEAAPEAXI@Z ?GetContentFlags@Light@engine@donut@@UEBA?AW4SceneContentFlags@23@XZ ?FillLightConstants@Light@engine@donut@@UEBAXAEAULightConstants@@@Z ?Store@Light@engine@donut@@UEBAXAEAVValue@Json@@@Z ?SetProperty@Light@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ?GetPosition@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ ?GetDirection@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ ?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z ?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z ??1Light@engine@donut@@UEAA@XZ ??_GLight@engine@donut@@UEAAPEAXI@Z ??_ELight@engine@donut@@UEAAPEAXI@Z ??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ ?Clone@DirectionalLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?GetLightType@DirectionalLight@engine@donut@@UEBAHXZ ?FillLightConstants@DirectionalLight@engine@donut@@UEBAXAEAULightConstants@@@Z ?Load@DirectionalLight@engine@donut@@UEAAXAEBVValue@Json@@@Z ?Store@DirectionalLight@engine@donut@@UEBAXAEAVValue@Json@@@Z ?SetProperty@DirectionalLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??_GDirectionalLight@engine@donut@@UEAAPEAXI@Z ??_EDirectionalLight@engine@donut@@UEAAPEAXI@Z ?Clone@SpotLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?GetLightType@SpotLight@engine@donut@@UEBAHXZ ?FillLightConstants@SpotLight@engine@donut@@UEBAXAEAULightConstants@@@Z ?Load@SpotLight@engine@donut@@UEAAXAEBVValue@Json@@@Z ?Store@SpotLight@engine@donut@@UEBAXAEAVValue@Json@@@Z ?SetProperty@SpotLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??_GSpotLight@engine@donut@@UEAAPEAXI@Z ??_ESpotLight@engine@donut@@UEAAPEAXI@Z ?Clone@PointLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?GetLightType@PointLight@engine@donut@@UEBAHXZ ?FillLightConstants@PointLight@engine@donut@@UEBAXAEAULightConstants@@@Z ?Load@PointLight@engine@donut@@UEAAXAEBVValue@Json@@@Z ?Store@PointLight@engine@donut@@UEBAXAEAVValue@Json@@@Z ?SetProperty@PointLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??_GPointLight@engine@donut@@UEAAPEAXI@Z ??_EPointLight@engine@donut@@UEAAPEAXI@Z ?SetTransform@SceneGraphNode@engine@donut@@QEAAXPEBU?$vector@N$02@math@3@PEBU?$quaternion@N@53@0@Z ?SetTranslation@SceneGraphNode@engine@donut@@QEAAXAEBU?$vector@N$02@math@3@@Z ??AValue@Json@@QEAAAEAV01@PEBD@Z ??AValue@Json@@QEBAAEBV01@PEBD@Z ??$Read@M@json@donut@@YAMAEBVValue@Json@@AEBM@Z ??$Read@U?$vector@M$02@math@donut@@@json@donut@@YA?AU?$vector@M$02@math@1@AEBVValue@Json@@AEBU231@@Z ??$Write@M@json@donut@@YAXAEAVValue@Json@@AEBM@Z ??$Write@U?$vector@M$02@math@donut@@@json@donut@@YAXAEAVValue@Json@@AEBU?$vector@M$02@math@1@@Z ??6@YAXAEAVValue@Json@@PEBD@Z ??$normalize@N$02@math@donut@@YA?AU?$vector@N$02@01@AEBU201@@Z ??$inverse@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@@Z ??$lookatZ@N@math@donut@@YA?AU?$affine@N$02@01@AEBU?$vector@N$02@01@@Z ??$?DN$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@0@Z ??$decomposeAffine@N@math@donut@@YAXAEBU?$affine@N$02@01@PEAU?$vector@N$02@01@PEAU?$quaternion@N@01@1@Z ??$static_pointer_cast@VSceneGraphLeaf@engine@donut@@VDirectionalLight@23@@std@@YA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@AEBV?$shared_ptr@VDirectionalLight@engine@donut@@@0@@Z ??$?5U?$vector@M$02@math@donut@@@@YAXAEBVValue@Json@@AEAU?$vector@M$02@math@donut@@@Z ??$?5M@@YAXAEBVValue@Json@@AEAM@Z ??$?6U?$vector@M$02@math@donut@@@@YAXAEAVValue@Json@@AEBU?$vector@M$02@math@donut@@@Z ??$?6M@@YAXAEAVValue@Json@@AEBM@Z ??$static_pointer_cast@VSceneGraphLeaf@engine@donut@@VSpotLight@23@@std@@YA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@AEBV?$shared_ptr@VSpotLight@engine@donut@@@0@@Z ??$static_pointer_cast@VSceneGraphLeaf@engine@donut@@VPointLight@23@@std@@YA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@AEBV?$shared_ptr@VPointLight@engine@donut@@@0@@Z ??$length@N$02@math@donut@@YANAEBU?$vector@N$02@01@@Z ??$inverse@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@@Z ??$?DN@math@donut@@YA?AU?$vector@N$02@01@AEBU201@AEBU?$matrix@N$02$02@01@@Z ??$?DN$02$02$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@0@Z ??$?_0N@math@donut@@YAAEAU?$vector@N$02@01@AEAU201@N@Z ??1?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@UEAAPEAXI@Z ??$lengthSquared@N$02@math@donut@@YANAEBU?$vector@N$02@01@@Z ??$?YN@math@donut@@YAAEAU?$vector@N$02@01@AEAU201@AEBU201@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?dtor$1@?0??GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z@4HA _CxxThrowException __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1VertexAttributeDesc@nvrhi@@QEAA@XZ $pdata$??1VertexAttributeDesc@nvrhi@@QEAA@XZ $unwind$?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z $pdata$?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z $cppxdata$?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z $stateUnwindMap$?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z $ip2state$?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z $unwind$?IsActive@LightProbe@engine@donut@@QEBA_NXZ $pdata$?IsActive@LightProbe@engine@donut@@QEBA_NXZ $unwind$?FillLightProbeConstants@LightProbe@engine@donut@@QEBAXAEAULightProbeConstants@@@Z $pdata$?FillLightProbeConstants@LightProbe@engine@donut@@QEBAXAEAULightProbeConstants@@@Z $unwind$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $pdata$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $chain$1$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $pdata$1$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $chain$3$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $pdata$3$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $unwind$??_GSceneGraphLeaf@engine@donut@@UEAAPEAXI@Z $pdata$??_GSceneGraphLeaf@engine@donut@@UEAAPEAXI@Z $unwind$?FillLightConstants@Light@engine@donut@@UEBAXAEAULightConstants@@@Z $pdata$?FillLightConstants@Light@engine@donut@@UEBAXAEAULightConstants@@@Z $unwind$?SetProperty@Light@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $pdata$?SetProperty@Light@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $unwind$?GetPosition@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ $pdata$?GetPosition@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ $unwind$?GetDirection@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ $pdata$?GetDirection@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ $unwind$?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $chain$0$?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$0$?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $chain$1$?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$1$?SetPosition@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $unwind$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $chain$9$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$9$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $chain$10$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$10$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $chain$11$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $pdata$11$?SetDirection@Light@engine@donut@@QEBAXAEBU?$vector@N$02@math@3@@Z $unwind$??1Light@engine@donut@@UEAA@XZ $pdata$??1Light@engine@donut@@UEAA@XZ $unwind$??_GLight@engine@donut@@UEAAPEAXI@Z $pdata$??_GLight@engine@donut@@UEAAPEAXI@Z $unwind$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$0$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$0$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$1$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$1$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$2$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$2$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$3$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$3$??1?$vector@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIShadowMap@engine@donut@@@std@@@2@@std@@QEAA@XZ $unwind$?Clone@DirectionalLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@DirectionalLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?FillLightConstants@DirectionalLight@engine@donut@@UEBAXAEAULightConstants@@@Z $pdata$?FillLightConstants@DirectionalLight@engine@donut@@UEBAXAEAULightConstants@@@Z $unwind$?Load@DirectionalLight@engine@donut@@UEAAXAEBVValue@Json@@@Z $pdata$?Load@DirectionalLight@engine@donut@@UEAAXAEBVValue@Json@@@Z $unwind$?Store@DirectionalLight@engine@donut@@UEBAXAEAVValue@Json@@@Z $pdata$?Store@DirectionalLight@engine@donut@@UEBAXAEAVValue@Json@@@Z $unwind$?SetProperty@DirectionalLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $pdata$?SetProperty@DirectionalLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $unwind$??_GDirectionalLight@engine@donut@@UEAAPEAXI@Z $pdata$??_GDirectionalLight@engine@donut@@UEAAPEAXI@Z $unwind$?Clone@SpotLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@SpotLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?FillLightConstants@SpotLight@engine@donut@@UEBAXAEAULightConstants@@@Z $pdata$?FillLightConstants@SpotLight@engine@donut@@UEBAXAEAULightConstants@@@Z $unwind$?Load@SpotLight@engine@donut@@UEAAXAEBVValue@Json@@@Z $pdata$?Load@SpotLight@engine@donut@@UEAAXAEBVValue@Json@@@Z $unwind$?Store@SpotLight@engine@donut@@UEBAXAEAVValue@Json@@@Z $pdata$?Store@SpotLight@engine@donut@@UEBAXAEAVValue@Json@@@Z $unwind$?SetProperty@SpotLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $pdata$?SetProperty@SpotLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $unwind$??_GSpotLight@engine@donut@@UEAAPEAXI@Z $pdata$??_GSpotLight@engine@donut@@UEAAPEAXI@Z $unwind$?Clone@PointLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@PointLight@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?FillLightConstants@PointLight@engine@donut@@UEBAXAEAULightConstants@@@Z $pdata$?FillLightConstants@PointLight@engine@donut@@UEBAXAEAULightConstants@@@Z $unwind$?Load@PointLight@engine@donut@@UEAAXAEBVValue@Json@@@Z $pdata$?Load@PointLight@engine@donut@@UEAAXAEBVValue@Json@@@Z $unwind$?Store@PointLight@engine@donut@@UEBAXAEAVValue@Json@@@Z $pdata$?Store@PointLight@engine@donut@@UEBAXAEAVValue@Json@@@Z $unwind$?SetProperty@PointLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $pdata$?SetProperty@PointLight@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z $unwind$??_GPointLight@engine@donut@@UEAAPEAXI@Z $pdata$??_GPointLight@engine@donut@@UEAAPEAXI@Z $unwind$??$normalize@N$02@math@donut@@YA?AU?$vector@N$02@01@AEBU201@@Z $pdata$??$normalize@N$02@math@donut@@YA?AU?$vector@N$02@01@AEBU201@@Z $unwind$??$inverse@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@@Z $pdata$??$inverse@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@@Z $unwind$??$lookatZ@N@math@donut@@YA?AU?$affine@N$02@01@AEBU?$vector@N$02@01@@Z $pdata$??$lookatZ@N@math@donut@@YA?AU?$affine@N$02@01@AEBU?$vector@N$02@01@@Z $unwind$??$?DN$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@0@Z $pdata$??$?DN$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@0@Z $unwind$??$decomposeAffine@N@math@donut@@YAXAEBU?$affine@N$02@01@PEAU?$vector@N$02@01@PEAU?$quaternion@N@01@1@Z $pdata$??$decomposeAffine@N@math@donut@@YAXAEBU?$affine@N$02@01@PEAU?$vector@N$02@01@PEAU?$quaternion@N@01@1@Z $unwind$??$?5U?$vector@M$02@math@donut@@@@YAXAEBVValue@Json@@AEAU?$vector@M$02@math@donut@@@Z $pdata$??$?5U?$vector@M$02@math@donut@@@@YAXAEBVValue@Json@@AEAU?$vector@M$02@math@donut@@@Z $unwind$??$?5M@@YAXAEBVValue@Json@@AEAM@Z $pdata$??$?5M@@YAXAEBVValue@Json@@AEAM@Z $unwind$??$inverse@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@@Z $pdata$??$inverse@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@@Z $unwind$??$?DN@math@donut@@YA?AU?$vector@N$02@01@AEBU201@AEBU?$matrix@N$02$02@01@@Z $pdata$??$?DN@math@donut@@YA?AU?$vector@N$02@01@AEBU201@AEBU?$matrix@N$02$02@01@@Z $unwind$??_G?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@UEAAPEAXI@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?white@colors@math@donut@@3U?$vector@M$02@23@B ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7SceneGraphLeaf@engine@donut@@6B@ ??_7Light@engine@donut@@6B@ ??_7DirectionalLight@engine@donut@@6B@ ??_7SpotLight@engine@donut@@6B@ ??_7PointLight@engine@donut@@6B@ ??_C@_05PEENBMOG@color@ ??_C@_0L@PCPLPKEP@irradiance@ ??_C@_0M@CIGOBMMD@angularSize@ ??_C@_0BB@EGOIFEHL@DirectionalLight@ ??_C@_04GPMDFGEJ@type@ ??_C@_09DDBFFAKC@intensity@ ??_C@_0L@BKCCJFHP@innerAngle@ ??_C@_0L@BEDHONMM@outerAngle@ ??_C@_06KGHINEPG@radius@ ??_C@_05CCGOGOBM@range@ ??_C@_09OIMHBJMD@SpotLight@ ??_C@_0L@KGHCILHP@PointLight@ ??_C@_06OGJDIEIN@Opaque@ ??_C@_0M@OCJAJJAA@AlphaTested@ ??_C@_0N@GIJOCALC@AlphaBlended@ ??_C@_0N@OEPBDHP@Transmissive@ ??_C@_0BI@NDFIOCKK@TransmissiveAlphaTested@ ??_C@_0BJ@FOKMKCDP@TransmissiveAlphaBlended@ ??_C@_05IJGIMMHE@Count@ ??_C@_09IBANJBEA@?$DMInvalid?$DO@ ??_7?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@6B@ ??_7?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@6B@ ??_7?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@6B@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4SceneGraphLeaf@engine@donut@@6B@ ??_R0?AVSceneGraphLeaf@engine@donut@@@8 ??_R3SceneGraphLeaf@engine@donut@@8 ??_R2SceneGraphLeaf@engine@donut@@8 ??_R1A@?0A@EA@SceneGraphLeaf@engine@donut@@8 ??_R4Light@engine@donut@@6B@ ??_R0?AVLight@engine@donut@@@8 ??_R3Light@engine@donut@@8 ??_R2Light@engine@donut@@8 ??_R1A@?0A@EA@Light@engine@donut@@8 ??_R4DirectionalLight@engine@donut@@6B@ ??_R0?AVDirectionalLight@engine@donut@@@8 ??_R3DirectionalLight@engine@donut@@8 ??_R2DirectionalLight@engine@donut@@8 ??_R1A@?0A@EA@DirectionalLight@engine@donut@@8 ??_R4SpotLight@engine@donut@@6B@ ??_R0?AVSpotLight@engine@donut@@@8 ??_R3SpotLight@engine@donut@@8 ??_R2SpotLight@engine@donut@@8 ??_R1A@?0A@EA@SpotLight@engine@donut@@8 ??_R4PointLight@engine@donut@@6B@ ??_R0?AVPointLight@engine@donut@@@8 ??_R3PointLight@engine@donut@@8 ??_R2PointLight@engine@donut@@8 ??_R1A@?0A@EA@PointLight@engine@donut@@8 ??_R4?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VDirectionalLight@engine@donut@@@std@@8 ??_R4?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VSpotLight@engine@donut@@@std@@8 ??_R4?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VPointLight@engine@donut@@@std@@8 __ImageBase __real@3c8efa35 __real@3eb0c6f7a0000000 __real@3f800000 __real@3fe0000000000000 __real@3ff0000000000000 __real@42b40000 __real@7f7fffff __real@ff7fffff __security_cookie __xmm@00000000000000003ff0000000000000 __xmm@000000000000000f0000000000000000 __xmm@7fffffffffffffff7fffffffffffffff __xmm@80000000000000008000000000000000 