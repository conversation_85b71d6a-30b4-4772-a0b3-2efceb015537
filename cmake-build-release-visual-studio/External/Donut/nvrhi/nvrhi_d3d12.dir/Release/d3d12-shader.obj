d哹u馟h@� g      .drectve        j  d_               
 .debug$S        % 蝋  鎱        @ B.debug$T        t   6�             @ B.rdata          8   獑             @ @@.text$mn        :   鈫 �         P`.debug$S          :� F�        @B.text$mn           覊              P`.debug$S          謮 迠        @B.text$mn        0   � J�         P`.debug$S        �  T� 饘        @B.text$mn        0   |� 瑣         P`.debug$S        �  秿 b�        @B.text$mn        0   顝 �         P`.debug$S        �  (�  �        @B.text$mn        0   寬 紥         P`.debug$S        �  茠 姅        @B.text$mn        0   � F�         P`.debug$S        �  P� �        @B.text$mn        �   敆 �         P`.debug$S          ,� 8�         @B.text$mn        �  x� "�         P`.debug$S        �  r� 
�     f   @B.text$x         (   � .�         P`.text$mn        �  B� 岜         P`.debug$S        �  1� �     f   @B.text$x         (   
� 5�         P`.text$mn        7   I�              P`.debug$S        t  �� 裟        @B.text$mn        D   l�              P`.debug$S        �  芭 <�        @B.text$mn        C  辞 魅         P`.debug$S        �
  G� 7�     \   @B.text$x         (   献 髯         P`.text$mn          � %�         P`.debug$S        0	  u� モ     H   @B.text$x         (   u� 濆         P`.text$mn        1  卞 忡     	    P`.debug$S        �  <� $�     R   @B.text$x         =   X� 書         P`.text$mn        �  厨 p�         P`.debug$S        �
  历 �     X   @B.text$x         (   	 D	         P`.text$mn        �  X	 I         P`.debug$S        P  � �     \   @B.text$x            m y         P`.text$x            � �         P`.text$mn        b   � �         P`.debug$S            "        @B.text$mn           �" �"         P`.debug$S        4  �" �#     
   @B.text$mn           R$ q$         P`.debug$S        <  {$ �%     
   @B.text$mn        �   &              P`.debug$S        �  �& ?)        @B.text$mn            �)              P`.debug$S        �  �) �,        @B.text$mn        <   �- �-         P`.debug$S        0  �- %/     
   @B.text$mn        <   �/ �/         P`.debug$S        L  �/ /1     
   @B.text$mn        !   �1 �1         P`.debug$S        <  �1 3        @B.text$mn        2   @3 r3         P`.debug$S        <  �3 �4        @B.text$mn        "   :5              P`.debug$S        �  \5 �6        @B.text$mn        "   �7              P`.debug$S        �  �7 >9        @B.text$mn        "   �9              P`.debug$S        �   : �;        @B.text$mn           8< L<         P`.debug$S          V< b>        @B.text$mn        �   �> U?         P`.debug$S        �  }? iE        @B.text$mn        [   丗 蹻         P`.debug$S        �  餏 碕        @B.text$mn           怟          P`.debug$S        (  甂 諱        @B.text$mn        ^   &N 凬         P`.debug$S        T  楴 霶        @B.text$mn        \   碦 S         P`.debug$S        �  $S 萔        @B.text$mn        [   |W 譝         P`.debug$S          隬 [        @B.text$mn        �   鉡 h\         P`.debug$S        $  哱 猒        @B.text$mn        y   哷 �`         P`.debug$S          a d        @B.text$mn           骴              P`.debug$S        �   鰀 蔱        @B.text$mn        �   f 眆         P`.debug$S        |  蟜 Kk     $   @B.text$mn           砽 苐         P`.debug$S        �   趌 緈        @B.text$mn           鎚 鵰         P`.debug$S        �   
n 韓        @B.text$mn        !   )o Jo         P`.debug$S        �   To <p        @B.text$mn        !   xp 檖         P`.debug$S        �    噏        @B.text$mn        !   胵 鋛         P`.debug$S        �   顀 趓        @B.text$mn        !   s 7s         P`.debug$S        �   As t        @B.text$mn        !   Ut vt         P`.debug$S        �   �t Tu        @B.text$mn        !   恥 眜         P`.debug$S        �   籾 媣        @B.text$mn        !   莢 鑦         P`.debug$S        �   騰 蕎        @B.text$mn        �   x 杧         P`.debug$S        �  葂 l{        @B.text$mn        Z  4| 巬         P`.debug$S        �  詝 虄     6   @B.text$mn        z   鑵 b�         P`.debug$S        �  �� d�        @B.text$mn        W   h� 繆         P`.debug$S        h  訆 ;�        @B.text$mn        B   硨 鯇         P`.debug$S           � �        @B.text$mn        B   O� 憥         P`.debug$S          瘞 繌        @B.text$mn        B   麖 =�         P`.debug$S        �   [� W�        @B.text$mn        H   搼              P`.debug$S        �  蹜 煋        @B.text$mn        
   窋              P`.debug$S          臄 軙        @B.text$mn        
   �              P`.debug$S          %� 9�        @B.text$mn        
   u�              P`.debug$S          倵 灅        @B.text$mn        +   跇              P`.debug$S        l  � q�        @B.text$mn        +   闅              P`.debug$S        d  � x�        @B.text$mn        +   饻              P`.debug$S        l  � 嚍        @B.text$mn        :  �� 9�         P`.debug$S        x  u� 恣     <   @B.text$mn        �   E� 酞         P`.debug$S        �  嗒 t�     "   @B.text$mn        �  券 劚         P`.debug$S        `  ⒈ �     P   @B.text$mn            "� B�         P`.debug$S        �   `� $�        @B.text$mn           `� q�         P`.debug$S        �   吜 9�        @B.text$mn           u� 喡         P`.debug$S        �   毬 n�        @B.text$mn            幻         P`.debug$S        �   厦 衬        @B.text$mn           锬  �         P`.debug$S          � (�        @B.text$mn           d� u�         P`.debug$S        �   壠 吳        @B.text$mn           燎 仪         P`.debug$S           媲 嫒        @B.text$mn           "� 3�         P`.debug$S        �   G� ;�        @B.text$mn        `  w� 姿         P`.debug$S        �  '� 嗽     B   @B.text$mn        �  _�          P`.debug$S        �  v� "�     l   @B.text$x            Z� w�         P`.text$x            侎 戱         P`.text$x            涶          P`.text$x            雕 篷         P`.text$x            像 唏         P`.text$x         )   轳 �         P`.text$mn        x  � 旟         P`.debug$S        �  历 P     �   @B.text$x              <         P`.text$x            F V         P`.text$x         &   ` �         P`.text$mn        �   � !         P`.debug$S        p  I �        @B.text$x         &   � �         P`.text$mn           � �         P`.debug$S        �  � �        @B.text$mn        =   a �         P`.debug$S        �  � r"        @B.text$mn        B   �# �#         P`.debug$S        �  �# �&        @B.text$mn        B   �' �'         P`.debug$S        �  �' �*        @B.text$mn        A   �+ �+         P`.debug$S        �  �+ �.        @B.text$mn        >   �/ �/         P`.debug$S        �  0 �2        @B.text$mn        A   �3 �3         P`.debug$S        �  
4 �6        @B.text$mn        9   �7              P`.debug$S        �  �7 �9     
   @B.text$mn        &   �9              P`.debug$S        �  
: �;     
   @B.text$mn           �;              P`.debug$S        �  < �=     
   @B.text$mn           >              P`.debug$S        t  > �?        @B.text$mn           �?              P`.debug$S        �   @ 袬        @B.text$mn           
A              P`.debug$S        �   A 預        @B.text$mn           *B              P`.debug$S        D  5B yC        @B.text$mn        '   蒀              P`.debug$S           餋 E        @B.text$mn        "  `E 侳         P`.debug$S        X  燜 鳰        @B.text$x            $O AO         P`.text$x            KO [O         P`.text$x            eO xO         P`.text$x            侽 扥         P`.text$x         &   淥 翺         P`.text$mn        �   蘋 NP         P`.debug$S        (  bP 奡        @B.text$mn           禩 蒚         P`.debug$S        �   覶         @B.xdata             鉛             @0@.pdata             鱑 V        @0@.xdata             !V             @0@.pdata             )V 5V        @0@.xdata             SV             @0@.pdata             [V gV        @0@.xdata             匳             @0@.pdata             慥 漋        @0@.xdata             籚             @0@.pdata             肰 蟅        @0@.xdata             鞻             @0@.pdata             鵙 W        @0@.xdata             #W             @0@.pdata             +W 7W        @0@.xdata             UW             @0@.pdata             aW mW        @0@.xdata             媁             @0@.pdata             揥 焀        @0@.xdata             絎             @0@.pdata             臰 裌        @0@.xdata             颳             @0@.pdata             鱓 X        @0@.xdata             !X             @0@.pdata             )X 5X        @0@.xdata             SX             @0@.pdata             gX sX        @0@.xdata             慩         @0@.pdata             肵 蟈        @0@.xdata             鞽 齒        @0@.pdata             Y 'Y        @0@.xdata             EY YY        @0@.pdata             wY 僘        @0@.xdata                          @0@.pdata             ℡ 礩        @0@.xdata             覻             @0@.pdata             踄 鏨        @0@.xdata             Z             @0@.pdata             
Z Z        @0@.xdata             7Z             @0@.pdata             ?Z KZ        @0@.xdata             iZ yZ        @0@.pdata             峑 橺        @0@.xdata          	   穁 繸        @@.xdata             訸 赯        @@.xdata             鋃             @@.xdata             鏩 鱖        @0@.pdata             [ [        @0@.xdata          	   5[ >[        @@.xdata             R[ X[        @@.xdata             b[             @@.xdata             e[             @0@.pdata             m[ y[        @0@.xdata             梉             @0@.pdata             玔 穂        @0@.xdata             誟             @0@.pdata             輀 閇        @0@.voltbl            \               .xdata             \             @0@.pdata             \ \        @0@.voltbl            :\               .xdata             ;\             @0@.pdata             C\ O\        @0@.xdata             m\             @0@.pdata             }\ 塡        @0@.xdata                          @0@.pdata             痋 籠        @0@.xdata             賊             @0@.pdata             醆 韁        @0@.xdata             ]             @0@.pdata             ] ]        @0@.xdata             =]             @0@.pdata             E] Q]        @0@.xdata             o]             @0@.pdata             w] 僝        @0@.xdata                          @0@.pdata              礭        @0@.xdata             覿             @0@.pdata             鉣 颹        @0@.xdata             
^ ^        @0@.pdata             1^ =^        @0@.xdata          	   [^ d^        @@.xdata             x^ ~^        @@.xdata             坁             @@.xdata             媈 焇        @0@.pdata             砠 縙        @0@.xdata          	   較 鎊        @@.xdata             鷁  _        @@.xdata             
_             @@.voltbl            
_               .xdata             _             @0@.pdata             _ "_        @0@.voltbl            @_               .xdata             A_             @0@.pdata             I_ U_        @0@.xdata          $   s_ 梍        @0@.pdata             玙 穇        @0@.xdata          	   誣 轤        @@.xdata             騙 `        @@.xdata             M`             @@.xdata             U`             @0@.pdata             ]` i`        @0@.xdata             嘸             @0@.pdata             廯 沗        @0@.xdata             筦             @0@.pdata             臽 裛        @0@.voltbl            颼               .xdata             餪             @0@.pdata             鴃 a        @0@.voltbl            "a               .xdata             #a             @0@.pdata             +a 7a        @0@.xdata             Ua             @0@.pdata             ]a ia        @0@.xdata             嘺             @0@.pdata             廰 沘        @0@.xdata             筧             @0@.pdata             羇 蚢        @0@.xdata             隺             @0@.pdata             骯 �a        @0@.xdata             b             @0@.pdata             %b 1b        @0@.xdata             Ob             @0@.pdata             Wb cb        @0@.xdata             乥             @0@.pdata             塨 昩        @0@.xdata             砨 莃        @0@.pdata             錬 馼        @0@.xdata             c c        @0@.pdata             =c Ic        @0@.xdata             gc             @0@.pdata             sc c        @0@.xdata             漜 礳        @0@.pdata             觕 遚        @0@.xdata             齝 d        @0@.pdata             /d ;d        @0@.xdata             Yd id        @0@.pdata             嘾 揹        @0@.xdata             眃 蚫        @0@.pdata             雂 鱠        @0@.xdata             e %e        @0@.pdata             Ce Oe        @0@.xdata             me             @0@.pdata             ye 卐        @0@.xdata              籩        @0@.pdata             賓 錯        @0@.xdata             f f        @0@.pdata             1f =f        @0@.xdata             [f sf        @0@.pdata             慺 漟        @0@.xdata             籪             @0@.pdata             胒 蟜        @0@.xdata             韋             @0@.pdata             鵩 g        @0@.xdata             #g             @0@.pdata             /g ;g        @0@.xdata          $   Yg }g        @0@.pdata             慻 漡        @0@.xdata          	   籫 膅        @@.xdata             豨 鑗        @@.xdata             h             @@.xdata             h             @0@.pdata             h %h        @0@.xdata             Ch             @0@.pdata             Kh Wh        @0@.xdata             uh             @0@.pdata             }h 塰        @0@.xdata              胔        @0@.pdata             議 鉮        @0@.xdata          	   i 
i        @@.xdata             i $i        @@.xdata             .i             @@.xdata             3i             @0@.pdata             ;i Gi        @0@.xdata          $   ei 塱        @0@.pdata             漣 ﹊        @0@.xdata          	   莍 衖        @@.xdata             鋓 j        @@.xdata          
   ?j             @@.xdata             Lj             @0@.pdata             Tj `j        @0@.xdata             ~j             @0@.pdata             唈 抝        @0@.xdata              癹 衘        @0@.pdata             鋔 餵        @0@.xdata          	   k k        @@.xdata          
   +k 8k        @@.xdata          
   Lk             @@.xdata             Vk             @0@.pdata             bk nk        @0@.xdata             宬 爇        @0@.pdata             緆 蔾        @0@.xdata             鑛 鴎        @0@.pdata             l "l        @0@.xdata             @l Tl        @0@.pdata             rl ~l        @0@.xdata             渓             @0@.pdata              發        @0@.xdata             蝜 鈒        @0@.pdata              m m        @0@.xdata             *m :m        @0@.pdata             Xm dm        @0@.xdata             俶 瀖        @0@.pdata             瞞 緈        @0@.xdata          
   躮 閙        @@.xdata             n             @@.xdata             
n n        @@.xdata             n #n        @@.xdata          	   -n             @@.xdata             6n             @0@.pdata             >n Jn        @0@.voltbl            hn               .xdata             in 卬        @0@.pdata             檔         @0@.xdata          
   胣 衝        @@.xdata             頽             @@.xdata             駈 鵱        @@.xdata             o 
o        @@.xdata          	   o             @@.xdata             o             @0@.pdata             )o 5o        @0@.voltbl            So               .xdata             To             @0@.pdata             \o ho        @0@.xdata             唎         @0@.pdata             秓 耾        @0@.xdata          
   鄌 韔        @@.xdata             p             @@.xdata             p p        @@.xdata              p 'p        @@.xdata          	   1p             @@.xdata             :p             @0@.pdata             Bp Np        @0@.voltbl            lp               .xdata             mp 塸        @0@.pdata             漰 ﹑        @0@.xdata          
   莗 詐        @@.xdata             騪             @@.xdata             鮬 齪        @@.xdata             q q        @@.xdata          	   q             @@.xdata             !q             @0@.pdata             )q 5q        @0@.voltbl            Sq               .xdata             Tq lq        @0@.pdata             �q 宷        @0@.xdata          
   猶 穛        @@.xdata             誵             @@.xdata             豵 鄎        @@.xdata             阸 駋        @@.xdata             鹮             @@.xdata             r             @0@.pdata             
r r        @0@.voltbl            4r               .xdata             5r Mr        @0@.pdata             ar mr        @0@.xdata          
   媟 榬        @@.xdata             秗             @@.xdata             箁 羠        @@.xdata             藃 襯        @@.xdata             躵             @@.xdata             鋜             @0@.pdata             靣 鴕        @0@.voltbl            s               .xdata             s             @0@.pdata             s +s        @0@.xdata             Is             @0@.pdata             Qs ]s        @0@.xdata             {s             @0@.pdata             僺 弒        @0@.xdata             璼             @0@.pdata             箂 舠        @0@.xdata             鉺             @0@.pdata             飐 鹲        @0@.xdata             t             @0@.pdata             %t 1t        @0@.xdata             Ot             @0@.pdata             [t gt        @0@.xdata             卼             @0@.pdata             憈 漷        @0@.rdata          (   籺 鉻        @@@.rdata             u -u        @@@.rdata             Ku             @@@.rdata             ]u uu        @@@.rdata             搖 玼        @@@.rdata             蓇             @@@.xdata$x           辵 鷘        @@@.xdata$x           v *v        @@@.data$r         /   Hv wv        @@�.xdata$x        $   乿         @@@.data$r         $   箆 輛        @@�.xdata$x        $   鐅 w        @@@.data$r         $   w Cw        @@�.xdata$x        $   Mw qw        @@@.rdata             厀             @@@.data               晈             @ @�.rdata          8   祑 韜        @@@.rdata          8   3x kx        @@@.rdata          8   眡 閤        @@@.rdata          8   /y gy        @@@.rdata          8   瓂 鍄        @@@.rdata          8   +z cz        @@@.rdata          8   ﹝ 醶        @@@.rdata          8   '{ _{        @@@.rdata          8    輠        @@@.rdata          8   #| [|        @@@.rdata                          @@@.rdata             眧             @@@.rdata             蘾             @@@.rdata$r        $   鎩 
}        @@@.data$rs        &   (} N}        @@�.rdata$r           X} l}        @@@.rdata$r           v} 倉        @@@.rdata$r        $   寎 皚        @@@.rdata$r        $   膤 鑮        @@@.rdata$r           ~ ~        @@@.rdata$r           $~ 0~        @@@.rdata$r        $   :~ ^~        @@@.rdata$r        $   r~ 杶        @@@.rdata$r           磣 葉        @@@.rdata$r           襼 鎫        @@@.rdata$r        $   鷡         @@@.rdata$r        $   2 V        @@@.rdata$r           t �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $   � (�        @@@.data$rs        )   F� o�        @@�.rdata$r           y� 崁        @@@.rdata$r           梹 珋        @@@.rdata$r        $   縺 銆        @@@.rdata$r        $   鱻 �        @@@.data$rs        $   9� ]�        @@�.rdata$r           g� {�        @@@.rdata$r           厑 檨        @@@.rdata$r        $   瓉 褋        @@@.rdata$r        $   鍋 	�        @@@.data$rs        +   '� R�        @@�.rdata$r           \� p�        @@@.rdata$r           z� 巶        @@@.rdata$r        $    苽        @@@.rdata$r        $   趥         @@@.data$rs        )   � E�        @@�.rdata$r           O� c�        @@@.rdata$r        $   m� 憙        @@@.rdata$r        $   箖 輧        @@@.rdata$r        $   駜 �        @@@.data$rs        :   )� c�        @@�.rdata$r           m� 亜        @@@.rdata$r           媱         @@@.rdata$r        $   艅 閯        @@@.rdata$r        $   � +�        @@@.data$rs        0   I� y�        @@�.rdata$r           儏 梾        @@@.rdata$r        $    艆        @@@.rdata$r        $   韰 �        @@@.rdata$r        $   %� I�        @@@.data$rs        A   ]� 瀱        @P�.rdata$r           ▎ 紗        @@@.rdata$r           茊 鈫        @@@.rdata$r        $    � $�        @@@.data$rs        5   B� w�        @@�.rdata$r           亣 晣        @@@.rdata$r        $   焽 脟        @@@.rdata$r        $   雵 �        @@@.rdata$r        $   #� G�        @@@.rdata$r        $   e� 増        @@@.data$rs        .    請        @@�.rdata$r           邎 髨        @@@.rdata$r        $   龍 !�        @@@.rdata$r        $   I� m�        @@@.rdata$r        $   亯         @@@.data$rs        ?   箟 鴫        @@�.rdata$r           � �        @@@.rdata$r            � <�        @@@.rdata$r        $   Z� ~�        @@@.rdata             湂             @0@.debug$S        4   爦 詩        @B.debug$S        <   鑺 $�        @B.debug$S        L   8� 剫        @B.debug$S        @   構 貗        @B.debug$S        4   鞁  �        @B.debug$S        4   4� h�        @B.debug$S        @   |� 紝        @B.debug$S        H   袑 �        @B.debug$S        8   ,� d�        @B.debug$S        <   x� 磵        @B.debug$S        H   葝 �        @B.debug$S        P   $� t�        @B.debug$S        D   垘 處        @B.debug$S        <   鄮 �        @B.chks64           0�              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �     o     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\nvrhi_d3d12.dir\Release\d3d12-shader.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $d3d12  $rt 
 $cluster  $ObjectTypes 	 $stdext  �   l    �        nvrhi::EntireBuffer  鵨    D3D_INCLUDE_LOCAL  鵨   D3D_INCLUDE_SYSTEM 1 鵪    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES - �   std::chrono::steady_clock::is_steady  癳    D3D_SVC_SCALAR  癳   D3D_SVC_VECTOR  癳   D3D_SVC_MATRIX_ROWS  癳   D3D_SVC_MATRIX_COLUMNS  癳   D3D_SVC_OBJECT  癳   D3D_SVC_STRUCT   癳   D3D_SVC_INTERFACE_CLASS " 癳   D3D_SVC_INTERFACE_POINTER & E   std::ratio<1,1000000000>::num  榝   D3D_SVF_USERPACKED * E  � 蕷;std::ratio<1,1000000000>::den  榝   D3D_SVF_USED " 榝   D3D_SVF_INTERFACE_POINTER $ 榝   D3D_SVF_INTERFACE_PARAMETER  杄    D3D_SVT_VOID  杄   D3D_SVT_BOOL  杄   D3D_SVT_INT  杄   D3D_SVT_FLOAT  杄   D3D_SVT_STRING  杄   D3D_SVT_TEXTURE  杄   D3D_SVT_TEXTURE1D  杄   D3D_SVT_TEXTURE2D  杄   D3D_SVT_TEXTURE3D  杄  	 D3D_SVT_TEXTURECUBE  杄  
 D3D_SVT_SAMPLER  杄   D3D_SVT_SAMPLER1D  杄   D3D_SVT_SAMPLER2D  杄  
 D3D_SVT_SAMPLER3D  杄   D3D_SVT_SAMPLERCUBE  杄   D3D_SVT_PIXELSHADER  杄   D3D_SVT_VERTEXSHADER  杄   D3D_SVT_PIXELFRAGMENT  杄   D3D_SVT_VERTEXFRAGMENT  杄   D3D_SVT_UINT  杄   D3D_SVT_UINT8  杄   D3D_SVT_GEOMETRYSHADER  杄   D3D_SVT_RASTERIZER  杄   D3D_SVT_DEPTHSTENCIL  杄   D3D_SVT_BLEND  杄   D3D_SVT_BUFFER  杄   D3D_SVT_CBUFFER  杄   D3D_SVT_TBUFFER  杄   D3D_SVT_TEXTURE1DARRAY  杄   D3D_SVT_TEXTURE2DARRAY ! 杄   D3D_SVT_RENDERTARGETVIEW ! 杄   D3D_SVT_DEPTHSTENCILVIEW  杄    D3D_SVT_TEXTURE2DMS ! 杄  ! D3D_SVT_TEXTURE2DMSARRAY ! 杄  " D3D_SVT_TEXTURECUBEARRAY  杄  # D3D_SVT_HULLSHADER  杄  $ D3D_SVT_DOMAINSHADER " 杄  % D3D_SVT_INTERFACE_POINTER  杄  & D3D_SVT_COMPUTESHADER  杄  ' D3D_SVT_DOUBLE  杄  ( D3D_SVT_RWTEXTURE1D ! 杄  ) D3D_SVT_RWTEXTURE1DARRAY  杄  * D3D_SVT_RWTEXTURE2D ! 杄  + D3D_SVT_RWTEXTURE2DARRAY  杄  , D3D_SVT_RWTEXTURE3D  杄  - D3D_SVT_RWBUFFER # 杄  . D3D_SVT_BYTEADDRESS_BUFFER % 杄  / D3D_SVT_RWBYTEADDRESS_BUFFER " 杄  0 D3D_SVT_STRUCTURED_BUFFER $ 杄  1 D3D_SVT_RWSTRUCTURED_BUFFER ) 杄  2 D3D_SVT_APPEND_STRUCTURED_BUFFER * 杄  3 D3D_SVT_CONSUME_STRUCTURED_BUFFER L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos  +   PARSE_CANONICALIZE  +   PARSE_FRIENDLY  +   PARSE_SECURITY_URL  +   PARSE_ROOTDOCUMENT  +   PARSE_DOCUMENT  +   PARSE_ANCHOR ! +   PARSE_ENCODE_IS_UNESCAPE  +   PARSE_DECODE_IS_ESCAPE  +  	 PARSE_PATH_FROM_URL  +  
 PARSE_URL_FROM_PATH  +   PARSE_MIME  +   PARSE_SERVER  +  
 PARSE_SCHEMA  +   PARSE_SITE  +   PARSE_DOMAIN  +   PARSE_LOCATION  +   PARSE_SECURITY_DOMAIN  +   PARSE_ESCAPE  漞   D3D_SIF_USERPACKED # 漞   D3D_SIF_COMPARISON_SAMPLER  �,   PSU_DEFAULT $ 漞   D3D_SIF_TEXTURE_COMPONENT_0 $ 漞   D3D_SIF_TEXTURE_COMPONENT_1 # 漞   D3D_SIF_TEXTURE_COMPONENTS  '+   QUERY_EXPIRATION_DATE " '+   QUERY_TIME_OF_LAST_CHANGE  '+   QUERY_CONTENT_ENCODING  '+   QUERY_CONTENT_TYPE   d   std::_Iosb<int>::skipws  '+   QUERY_REFRESH  '+   QUERY_RECOMBINE ! d   std::_Iosb<int>::unitbuf  '+   QUERY_CAN_NAVIGATE  '+   QUERY_USES_NETWORK # d   std::_Iosb<int>::uppercase  '+  	 QUERY_IS_CACHED  噀    D3D_SIT_CBUFFER  噀   D3D_SIT_TBUFFER   '+  
 QUERY_IS_INSTALLEDENTRY " d   std::_Iosb<int>::showbase " '+   QUERY_IS_CACHED_OR_MAPPED  噀   D3D_SIT_TEXTURE  '+   QUERY_USES_CACHE  噀   D3D_SIT_SAMPLER  '+  
 QUERY_IS_SECURE # d   std::_Iosb<int>::showpoint  噀   D3D_SIT_UAV_RWTYPED  噀   D3D_SIT_STRUCTURED  '+   QUERY_IS_SAFE ! 噀   D3D_SIT_UAV_RWSTRUCTURED ! d    std::_Iosb<int>::showpos ! '+   QUERY_USES_HISTORYFOLDER  �+    ServerApplication  噀   D3D_SIT_BYTEADDRESS  d  @ std::_Iosb<int>::left " 噀   D3D_SIT_UAV_RWBYTEADDRESS & 噀  	 D3D_SIT_UAV_APPEND_STRUCTURED  d  � std::_Iosb<int>::right ' 噀  
 D3D_SIT_UAV_CONSUME_STRUCTURED  n.    IdleShutdown . 噀   D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER ( 噀   D3D_SIT_RTACCELERATIONSTRUCTURE " d   std::_Iosb<int>::internal  d   std::_Iosb<int>::dec  d   std::_Iosb<int>::oct  d   std::_Iosb<int>::hex $ d   std::_Iosb<int>::scientific  d    std::_Iosb<int>::fixed P    std::allocator<ID3D12CommandList *>::_Minimum_asan_allocation_alignment " d   0std::_Iosb<int>::hexfloat # d   @std::_Iosb<int>::boolalpha " d  � �std::_Iosb<int>::_Stdio  媑   D3D_CBF_USERPACKED % d  �std::_Iosb<int>::adjustfield # d   std::_Iosb<int>::basefield  癴    D3D_CT_CBUFFER $ d   0std::_Iosb<int>::floatfield  癴   D3D_CT_TBUFFER " 癴   D3D_CT_INTERFACE_POINTERS " 癴   D3D_CT_RESOURCE_BIND_INFO ! d    std::_Iosb<int>::goodbit   d   std::_Iosb<int>::eofbit   E  std::ratio<3600,1>::num ! d   std::_Iosb<int>::failbit �   v    E   std::ratio<3600,1>::den   d   std::_Iosb<int>::badbit  〆    D3D_NAME_UNDEFINED  〆   D3D_NAME_POSITION  〆   D3D_NAME_CLIP_DISTANCE  d   std::_Iosb<int>::in  〆   D3D_NAME_CULL_DISTANCE + 〆   D3D_NAME_RENDER_TARGET_ARRAY_INDEX  d   std::_Iosb<int>::out & 〆   D3D_NAME_VIEWPORT_ARRAY_INDEX  〆   D3D_NAME_VERTEX_ID  〆   D3D_NAME_PRIMITIVE_ID  d   std::_Iosb<int>::ate  〆   D3D_NAME_INSTANCE_ID  d   std::_Iosb<int>::app  〆  	 D3D_NAME_IS_FRONT_FACE  〆  
 D3D_NAME_SAMPLE_INDEX , 〆   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR  d   std::_Iosb<int>::trunc Q    std::allocator<nvrhi::BufferBarrier>::_Minimum_asan_allocation_alignment . 〆   D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR # d  @ std::_Iosb<int>::_Nocreate + 〆  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR - 〆   D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR $ d  � std::_Iosb<int>::_Noreplace . 〆   D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR 3    nvrhi::d3d12::BufferChunk::c_sizeAlignment / 〆   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR   d    std::_Iosb<int>::binary  〆   D3D_NAME_BARYCENTRICS  〆   D3D_NAME_SHADINGRATE  〆   D3D_NAME_CULLPRIMITIVE  〆  @ D3D_NAME_TARGET  〆  A D3D_NAME_DEPTH  〆  B D3D_NAME_COVERAGE % 〆  C D3D_NAME_DEPTH_GREATER_EQUAL  d    std::_Iosb<int>::beg " 〆  D D3D_NAME_DEPTH_LESS_EQUAL   �   e   d   std::_Iosb<int>::cur  〆  E D3D_NAME_STENCIL_REF   〆  F D3D_NAME_INNER_COVERAGE  d   std::_Iosb<int>::end , d  @ std::_Iosb<int>::_Default_open_prot < 鏶   NV_GPU_WORKSTATION_FEATURE_TYPE_NVIDIA_RTX_VR_READY  峞   D3D_RETURN_TYPE_UNORM  峞   D3D_RETURN_TYPE_SNORM  峞   D3D_RETURN_TYPE_SINT  峞   D3D_RETURN_TYPE_UINT  峞   D3D_RETURN_TYPE_FLOAT  峞   D3D_RETURN_TYPE_MIXED  峞   D3D_RETURN_TYPE_DOUBLE " 峞   D3D_RETURN_TYPE_CONTINUED ' 焑    D3D_REGISTER_COMPONENT_UNKNOWN & 焑   D3D_REGISTER_COMPONENT_UINT32 & 焑   D3D_REGISTER_COMPONENT_SINT32 ' 焑   D3D_REGISTER_COMPONENT_FLOAT32  %+    FEATURE_OBJECT_CACHING & 焑   D3D_REGISTER_COMPONENT_UINT16  %+   FEATURE_ZONE_ELEVATION & 焑   D3D_REGISTER_COMPONENT_SINT16  %+   FEATURE_MIME_HANDLING ' 焑   D3D_REGISTER_COMPONENT_FLOAT16  %+   FEATURE_MIME_SNIFFING & 焑   D3D_REGISTER_COMPONENT_UINT64 & 焑   D3D_REGISTER_COMPONENT_SINT64 $ %+   FEATURE_WINDOW_RESTRICTIONS ' 焑  	 D3D_REGISTER_COMPONENT_FLOAT64 & %+   FEATURE_WEBOC_POPUPMANAGEMENT  %+   FEATURE_BEHAVIORS $ %+   FEATURE_DISABLE_MK_PROTOCOL & %+   FEATURE_LOCALMACHINE_LOCKDOWN  %+  	 FEATURE_SECURITYBAND ( %+  
 FEATURE_RESTRICT_ACTIVEXINSTALL & %+   FEATURE_VALIDATE_NAVIGATE_URL & %+   FEATURE_RESTRICT_FILEDOWNLOAD ! %+  
 FEATURE_ADDON_MANAGEMENT " %+   FEATURE_PROTOCOL_LOCKDOWN / %+   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE " %+   FEATURE_SAFE_BINDTOOBJECT # %+   FEATURE_UNC_SAVEDFILECHECK ) 璭    D3D_TESSELLATOR_DOMAIN_UNDEFINED / %+   FEATURE_GET_URL_DOM_FILEPATH_UNENCODED ' 璭   D3D_TESSELLATOR_DOMAIN_ISOLINE   %+   FEATURE_TABBED_BROWSING # 璭   D3D_TESSELLATOR_DOMAIN_TRI  %+   FEATURE_SSLUX $ 璭   D3D_TESSELLATOR_DOMAIN_QUAD * %+   FEATURE_DISABLE_NAVIGATION_SOUNDS + %+   FEATURE_DISABLE_LEGACY_COMPRESSION & %+   FEATURE_FORCE_ADDR_AND_STATUS  %+   FEATURE_XMLHTTP ( %+   FEATURE_DISABLE_TELNET_PROTOCOL  %+   FEATURE_FEEDS / 秄    D3D_TESSELLATOR_PARTITIONING_UNDEFINED $ %+   FEATURE_BLOCK_INPUT_PROMPTS - 秄   D3D_TESSELLATOR_PARTITIONING_INTEGER * 秄   D3D_TESSELLATOR_PARTITIONING_POW2 4 秄   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 5 秄   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN ) 萬    D3D_TESSELLATOR_OUTPUT_UNDEFINED % 萬   D3D_TESSELLATOR_OUTPUT_POINT $ 萬   D3D_TESSELLATOR_OUTPUT_LINE + 萬   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW , 萬   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW h    std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> >::_Minimum_asan_allocation_alignment ' 圦    D3D12_COMMAND_LIST_TYPE_DIRECT ' 圦   D3D12_COMMAND_LIST_TYPE_BUNDLE ( 圦   D3D12_COMMAND_LIST_TYPE_COMPUTE % 圦   D3D12_COMMAND_LIST_TYPE_COPY - 圦   D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE . 圦   D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS - 圦   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE Z    std::allocator<nvrhi::RefCountPtr<IUnknown> >::_Minimum_asan_allocation_alignment & 緀   NV_LICENSE_FEATURE_NVIDIA_RTX 3 軉    D3D12_INPUT_CLASSIFICATION_PER_VERTEX_DATA 5 軉   D3D12_INPUT_CLASSIFICATION_PER_INSTANCE_DATA �    std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> >::_Minimum_asan_allocation_alignment M    std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment  �+    URLZONE_LOCAL_MACHINE  �+   URLZONE_INTRANET  �+   URLZONE_TRUSTED  �+   URLZONE_INTERNET  E  < std::ratio<60,1>::num  E   std::ratio<60,1>::den  �,    URLZONEREG_DEFAULT  �,   URLZONEREG_HKLM .   	�       �nvrhi::c_VersionSubmittedFlag # �  < nvrhi::c_VersionQueueShift " �   nvrhi::c_VersionQueueMask '   
��������nvrhi::c_VersionIDMask % 糵   D3D12_COLOR_WRITE_ENABLE_RED ' 糵   D3D12_COLOR_WRITE_ENABLE_GREEN & 糵   D3D12_COLOR_WRITE_ENABLE_BLUE ' 糵   D3D12_COLOR_WRITE_ENABLE_ALPHA  塭    D3D12_LOGIC_OP_CLEAR  塭   D3D12_LOGIC_OP_SET  塭   D3D12_LOGIC_OP_COPY % 塭   D3D12_LOGIC_OP_COPY_INVERTED  塭   D3D12_LOGIC_OP_NOOP  塭   D3D12_LOGIC_OP_INVERT 3 �  �����nvrhi::d3d12::c_InvalidDescriptorIndex  塭   D3D12_LOGIC_OP_AND 1 �        nvrhi::d3d12::c_ResourceStateUnknown  塭   D3D12_LOGIC_OP_NAND  塭   D3D12_LOGIC_OP_OR  塭  	 D3D12_LOGIC_OP_NOR  塭  
 D3D12_LOGIC_OP_XOR  塭   D3D12_LOGIC_OP_EQUIV # 塭   D3D12_LOGIC_OP_AND_REVERSE $ 塭  
 D3D12_LOGIC_OP_AND_INVERTED " 塭   D3D12_LOGIC_OP_OR_REVERSE . 峠    D3D12_LINE_RASTERIZATION_MODE_ALIASED 8 峠   D3D12_LINE_RASTERIZATION_MODE_ALPHA_ANTIALIASED 9 峠   D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_WIDE n    std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> >::_Minimum_asan_allocation_alignment   E   std::ratio<1,1000>::num   E  �std::ratio<1,1000>::den + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits # �+   BINDHANDLETYPES_DEPENDENCY ; 檈    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS : 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT 2 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND 8 檈  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 7 檈  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER : 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL 9 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT ? 檈  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE ? 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY B 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS A 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 8 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 6 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK 7 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 2 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS ; 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 8 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER2 1 篺    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED F 篺   D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS A 篺   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK + 蝒    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 1 蝒   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS ? 蝒   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY    �   餳  # E   std::ratio<1,1000000>::num ' E  �@B std::ratio<1,1000000>::den  �+    TKIND_ENUM  �+   TKIND_RECORD  �+   TKIND_MODULE  �+   TKIND_INTERFACE  �+   TKIND_DISPATCH  �+   TKIND_COCLASS  �+   TKIND_ALIAS  �+   TKIND_UNION W    std::allocator<nvrhi::VertexAttributeDesc>::_Minimum_asan_allocation_alignment  �*    PIDMSI_STATUS_NORMAL  �*   PIDMSI_STATUS_NEW  �*   PIDMSI_STATUS_PRELIM  �*   PIDMSI_STATUS_DRAFT ! �*   PIDMSI_STATUS_INPROGRESS  �*   PIDMSI_STATUS_EDIT  �*   PIDMSI_STATUS_REVIEW  �*   PIDMSI_STATUS_PROOF 8 磃    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD 9 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE 6 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR : 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_NO_ACCESS F 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER C 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_SRV i    std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> >::_Minimum_asan_allocation_alignment f    std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> >::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment 5 慹    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD 6 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE 5 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE 7 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_NO_ACCESS C 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER @ 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_SRV ~    std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >::_Minimum_asan_allocation_alignment  <+   CC_CDECL  <+   CC_MSCPASCAL  <+   CC_PASCAL  <+   CC_MACPASCAL  <+   CC_STDCALL  <+   CC_FPFASTCALL  <+   CC_SYSCALL  <+   CC_MPWCDECL  <+   CC_MPWPASCAL  #+    FUNC_VIRTUAL  #+   FUNC_PUREVIRTUAL  #+   FUNC_NONVIRTUAL  #+   FUNC_STATIC  +    VAR_PERINSTANCE  +   VAR_STATIC  +   VAR_CONST 	�    std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0>::_Multi �   std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0>::_Standard ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size  莋  i D3D_SHADER_MODEL_6_9 a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> >::_Minimum_asan_allocation_alignment A    std::allocator<bool>::_Minimum_asan_allocation_alignment :    std::integral_constant<unsigned __int64,1>::value U    std::allocator<D3D12_INPUT_ELEMENT_DESC>::_Minimum_asan_allocation_alignment T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos [    std::allocator<D3D12_RAYTRACING_INSTANCE_DESC>::_Minimum_asan_allocation_alignment I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment j    std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> >::_Minimum_asan_allocation_alignment '  g    D3D12_SHADER_CACHE_MODE_MEMORY 3 蔲    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 3 蔲   D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1  +    DESCKIND_NONE  +   DESCKIND_FUNCDESC  +   DESCKIND_VARDESC  +   DESCKIND_TYPECOMP   +   DESCKIND_IMPLICITAPPOBJ J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 % 媏    D3D12_BARRIER_LAYOUT_PRESENT L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 * 媏   D3D12_BARRIER_LAYOUT_GENERIC_READ + 媏   D3D12_BARRIER_LAYOUT_RENDER_TARGET P E  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 . 媏   D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS 1 媏   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE 0 媏   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 - 媏   D3D12_BARRIER_LAYOUT_SHADER_RESOURCE L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 ) 媏   D3D12_BARRIER_LAYOUT_COPY_SOURCE ' 媏   D3D12_BARRIER_LAYOUT_COPY_DEST K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx , 媏  	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE * 媏  
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy 1 媏   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE / 媏   D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ 0 媏  
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE 0 媏   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ 1 媏   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE / 媏   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ 0 媏   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE  �,   COR_VERSION_MAJOR_V2 1 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON 7 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ ; 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS : 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE 6 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE 4 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST 2 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON 8 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ < 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS ; 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 7 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 5 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST + <        nvrhi::rt::c_IdentityTransform    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> >::_Bucket_size " 雈    D3D12_BARRIER_TYPE_GLOBAL    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> >::_Min_buckets # 雈   D3D12_BARRIER_TYPE_TEXTURE �    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> >::_Multi  �+    SYS_WIN16  �+   SYS_WIN32  �+   SYS_MAC D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment i    std::allocator<std::pair<unsigned int const ,unsigned int> >::_Minimum_asan_allocation_alignment  +    CHANGEKIND_ADDMEMBER   +   CHANGEKIND_DELETEMEMBER  +   CHANGEKIND_SETNAMES $ +   CHANGEKIND_SETDOCUMENTATION  +   CHANGEKIND_GENERAL  +   CHANGEKIND_INVALIDATE   +   CHANGEKIND_CHANGEFAILED �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > >::_Minimum_asan_allocation_alignment � �    std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0>::_Multi � �   std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0>::_Standard / g   NV_COLOR_SELECTION_POLICY_BEST_QUALITY /    NV_DESKTOP_COLOR_DEPTH_16BPC_FLOAT_HDR B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset �    std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >::_Minimum_asan_allocation_alignment h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size �    std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment 7   � nvrhi::AftermathMarkerTracker::MaxEventStrings 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable R�    std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0>::_Multi U�   std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0>::_Standard W    std::allocator<enum nvrhi::ResourceStates>::_Minimum_asan_allocation_alignment T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos p    std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> >::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> >::_Minimum_asan_allocation_alignment / 羍    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION - 羍   D3D12_RESOURCE_BARRIER_TYPE_ALIASING �    std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> >::_Minimum_asan_allocation_alignment 3 歠    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS C 歠   D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS E 歠   D3D12_DEBUG_DEVICE_PARAMETER_GPU_SLOWDOWN_PERFORMANCE_FACTOR �    std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Minimum_asan_allocation_alignment :     D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE I    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY J    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION H    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION X�    std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0>::_Multi [�   std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0>::_Standard  �)    std::denorm_absent  �)   std::denorm_present  �)    std::round_toward_zero  �)   std::round_to_nearest # �)    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value - d    std::integral_constant<int,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �)    std::_Num_base::round_style  d    std::_Num_base::digits �    std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Bucket_size ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 �    std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Min_buckets � �    std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Multi % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix 3 Q  \ std::filesystem::path::preferred_separator n    std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >::_Minimum_asan_allocation_alignment ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact 8 辠    D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_DISABLED B 辠   D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_WHEN_HASH_BYPASSED ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �)   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �)   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix �    std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >::_Minimum_asan_allocation_alignment * d   std::numeric_limits<bool>::digits � �    std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0>::_Multi � �   std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0>::_Standard - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 e   std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> >::_Bucket_size e   std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> >::_Min_buckets _�    std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> >::_Multi . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 �    std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> >::_Minimum_asan_allocation_alignment E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 3 g    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED - g   D3D12_MESSAGE_CATEGORY_MISCELLANEOUS . g   D3D12_MESSAGE_CATEGORY_INITIALIZATION ' g   D3D12_MESSAGE_CATEGORY_CLEANUP + g   D3D12_MESSAGE_CATEGORY_COMPILATION . g   D3D12_MESSAGE_CATEGORY_STATE_CREATION - g   D3D12_MESSAGE_CATEGORY_STATE_SETTING - g   D3D12_MESSAGE_CATEGORY_STATE_GETTING 5 g   D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION ) g  	 D3D12_MESSAGE_CATEGORY_EXECUTION 0 �   std::numeric_limits<wchar_t>::is_modulo * 揺    D3D12_MESSAGE_SEVERITY_CORRUPTION - d   std::numeric_limits<wchar_t>::digits % 揺   D3D12_MESSAGE_SEVERITY_ERROR ' 揺   D3D12_MESSAGE_SEVERITY_WARNING $ 揺   D3D12_MESSAGE_SEVERITY_INFO / d   std::numeric_limits<wchar_t>::digits10 �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits �    std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >::_Minimum_asan_allocation_alignment - d   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , d  	 std::numeric_limits<long>::digits10 P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity ��    std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0>::_Multi ��   std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0>::_Standard R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified 5 �    std::filesystem::_File_time_clock::is_steady 0 �   std::numeric_limits<__int64>::is_signed - d  ? std::numeric_limits<__int64>::digits / d   std::numeric_limits<__int64>::digits10 d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size # 鱡   NV_MOSAIC_TOPO_BEGIN_BASIC 7 �   std::numeric_limits<unsigned short>::is_modulo 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 ! 鱡   NV_MOSAIC_TOPO_7x1_BASIC , 鱡   NV_MOSAIC_TOPO_BEGIN_PASSIVE_STEREO * 鱡   NV_MOSAIC_TOPO_2x2_PASSIVE_STEREO  鱡  # NV_MOSAIC_TOPO_MAX �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > >::_Minimum_asan_allocation_alignment 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 �    std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> >::_Minimum_asan_allocation_alignment 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits 5 d  	 std::numeric_limits<unsigned long>::digits10 p    std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >::_Minimum_asan_allocation_alignment 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 k   std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Bucket_size k   std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Min_buckets e�    std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Multi `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 $ �$   TP_CALLBACK_PRIORITY_NORMAL % �$   TP_CALLBACK_PRIORITY_INVALID � �    std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0>::_Multi � �   std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0>::_Standard , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10 �   V  �    std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> >::_Bucket_size �    std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> >::_Min_buckets � �    std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> >::_Multi �    std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> >::_Minimum_asan_allocation_alignment " �    std::memory_order_relaxed " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst   �     ! )+    COINITBASE_MULTITHREADED �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >::_Minimum_asan_allocation_alignment   �   2  T    std::allocator<D3D12_DESCRIPTOR_RANGE1>::_Minimum_asan_allocation_alignment �   std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> >::_Bucket_size �   std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> >::_Min_buckets �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > >::_Minimum_asan_allocation_alignment ��    std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> >::_Multi 4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment / f    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV + f   D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER ' f   D3D12_DESCRIPTOR_HEAP_TYPE_RTV ' f   D3D12_DESCRIPTOR_HEAP_TYPE_DSV 3 沢  �D3D12_MESSAGE_ID_BYTECODE_VALIDATION_ERROR  詣   NV_X_RIGHT_SEMANTIC " 詣   NV_VIEWPORT_MASK_SEMANTIC    std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> >::_Bucket_size    std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> >::_Min_buckets � �    std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> >::_Multi  骻   _Mtx_try  骻   _Mtx_recursive ) 脙   NV_PSO_GEOMETRY_SHADER_EXTENSION 4 脙   NV_PSO_SET_SHADER_EXTNENSION_SLOT_AND_SPACE �    std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >::_Minimum_asan_allocation_alignment ' 脙   NV_PSO_VERTEX_SHADER_EXTENSION ( ╣    D3D12_DESCRIPTOR_RANGE_TYPE_SRV ( ╣   D3D12_DESCRIPTOR_RANGE_TYPE_UAV ( ╣   D3D12_DESCRIPTOR_RANGE_TYPE_CBV  蒶   std::_INVALID_ARGUMENT  蒶   std::_NO_SUCH_PROCESS & 蒶   std::_OPERATION_NOT_PERMITTED , 蒶   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蒶   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > >::_Minimum_asan_allocation_alignment % +    _Atomic_memory_order_relaxed % +   _Atomic_memory_order_consume % +   _Atomic_memory_order_acquire % +   _Atomic_memory_order_release % +   _Atomic_memory_order_acq_rel % +   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE 3 玡    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask 2 玡   D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity & 玡   D3D12_ROOT_PARAMETER_TYPE_CBV 9�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0>::_Multi & 玡   D3D12_ROOT_PARAMETER_TYPE_SRV <�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0>::_Standard d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size \    std::allocator<nvrhi::AftermathMarkerTracker *>::_Minimum_asan_allocation_alignment U    std::allocator<nvrhi::BindingLayoutItem>::_Minimum_asan_allocation_alignment j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size 4     D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK /    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK /    D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE 4    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK_UINT   �   �9  �    std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> >::_Minimum_asan_allocation_alignment � �    std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0>::_Multi x    std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<D3D12_INPUT_ELEMENT_DESC,D3D12_INPUT_ELEMENT_DESC,D3D12_INPUT_ELEMENT_DESC &&,D3D12_INPUT_ELEMENT_DESC &>::_Same_size_and_compatible � �   std::_Trivial_cat<D3D12_INPUT_ELEMENT_DESC,D3D12_INPUT_ELEMENT_DESC,D3D12_INPUT_ELEMENT_DESC &&,D3D12_INPUT_ELEMENT_DESC &>::_Bitcopy_constructible � �   std::_Trivial_cat<D3D12_INPUT_ELEMENT_DESC,D3D12_INPUT_ELEMENT_DESC,D3D12_INPUT_ELEMENT_DESC &&,D3D12_INPUT_ELEMENT_DESC &>::_Bitcopy_assignable �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > >::_Minimum_asan_allocation_alignment `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos u    std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> >::_Minimum_asan_allocation_alignment ' ++  �   CLSCTX_ACTIVATE_X86_SERVER � �    std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Multi � �   std::_Trivial_cat<nvrhi::VertexAttributeDesc,nvrhi::VertexAttributeDesc,nvrhi::VertexAttributeDesc &&,nvrhi::VertexAttributeDesc &>::_Same_size_and_compatible � �   std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Is_set � �    std::_Trivial_cat<nvrhi::VertexAttributeDesc,nvrhi::VertexAttributeDesc,nvrhi::VertexAttributeDesc &&,nvrhi::VertexAttributeDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::VertexAttributeDesc,nvrhi::VertexAttributeDesc,nvrhi::VertexAttributeDesc &&,nvrhi::VertexAttributeDesc &>::_Bitcopy_assignable + �$   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 �$   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - �$   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 �$   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS :     std::integral_constant<unsigned __int64,0>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible * �$   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �$   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �$   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �$   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable � �   std::_Trivial_cat<_NV_CUSTOM_SEMANTIC,_NV_CUSTOM_SEMANTIC,_NV_CUSTOM_SEMANTIC &&,_NV_CUSTOM_SEMANTIC &>::_Same_size_and_compatible � �   std::_Trivial_cat<_NV_CUSTOM_SEMANTIC,_NV_CUSTOM_SEMANTIC,_NV_CUSTOM_SEMANTIC &&,_NV_CUSTOM_SEMANTIC &>::_Bitcopy_constructible � �   std::_Trivial_cat<_NV_CUSTOM_SEMANTIC,_NV_CUSTOM_SEMANTIC,_NV_CUSTOM_SEMANTIC &&,_NV_CUSTOM_SEMANTIC &>::_Bitcopy_assignable `    std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>::_Minimum_asan_allocation_alignment 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) �-    std::_Invoker_functor::_Strategy , :+   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL 3 �  � nvrhi::ObjectTypes::Nvrhi_D3D12_Device 8 �  � nvrhi::ObjectTypes::Nvrhi_D3D12_CommandList , �-   std::_Invoker_pmf_object::_Strategy - �-   std::_Invoker_pmf_refwrap::_Strategy - �-   std::_Invoker_pmf_pointer::_Strategy L   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> >::_Bucket_size L   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> >::_Min_buckets F�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> >::_Multi , �-   std::_Invoker_pmd_object::_Strategy �    std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >::_Minimum_asan_allocation_alignment - �-   std::_Invoker_pmd_refwrap::_Strategy � �   std::_Trivial_cat<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 * &&,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 * &>::_Bitcopy_constructible � �   std::_Trivial_cat<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 * &&,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 * &>::_Bitcopy_assignable - �-   std::_Invoker_pmd_pointer::_Strategy S    std::allocator<D3D12_RESOURCE_BARRIER>::_Minimum_asan_allocation_alignment * 苀    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW 2 苀   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED ��    std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0>::_Multi . 苀   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH 8 苀   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW ��   std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0>::_Standard 7 苀   D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW . 苀   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT : 苀   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW : 苀   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW ; 苀   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW 3 苀  	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS 3 苀  
 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_MESH � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable O �   std::_Trivial_cat<char,char,char &&,char &>::_Same_size_and_compatible L �   std::_Trivial_cat<char,char,char &&,char &>::_Bitcopy_constructible I �   std::_Trivial_cat<char,char,char &&,char &>::_Bitcopy_assignable �    std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> >::_Minimum_asan_allocation_alignment  +    NODE_INVALID  +   NODE_ELEMENT  +   NODE_ATTRIBUTE  +   NODE_TEXT  +   NODE_CDATA_SECTION  +   NODE_ENTITY_REFERENCE  +   NODE_ENTITY $ +   NODE_PROCESSING_INSTRUCTION  +   NODE_COMMENT o �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Same_size_and_compatible  +  	 NODE_DOCUMENT  +  
 NODE_DOCUMENT_TYPE  +   NODE_DOCUMENT_FRAGMENT l �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Bitcopy_constructible Z    std::allocator<nvrhi::AftermathMarkerTracker>::_Minimum_asan_allocation_alignment  +    XMLELEMTYPE_ELEMENT  +   XMLELEMTYPE_TEXT    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > >::_Minimum_asan_allocation_alignment  +   XMLELEMTYPE_COMMENT  +   XMLELEMTYPE_DOCUMENT i �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Bitcopy_assignable  +   XMLELEMTYPE_DTD  +   XMLELEMTYPE_PI  2+   VT_I2  2+   VT_I4  2+   VT_BSTR  2+  	 VT_DISPATCH P    std::allocator<_NV_CUSTOM_SEMANTIC>::_Minimum_asan_allocation_alignment  2+  
 VT_ERROR  2+   VT_VARIANT  2+  
 VT_UNKNOWN 3 �+   DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED  2+   VT_I1  2+   VT_I8  2+  $ VT_RECORD  2+  � �VT_RESERVED t d   std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Minimum_map_size  �+    TYSPEC_CLSID  �+   TYSPEC_FILEEXT  �+   TYSPEC_MIMETYPE  �+   TYSPEC_FILENAME  �+   TYSPEC_PROGID  �+   TYSPEC_PACKAGENAME Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment Z   hstd::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> >::_Bytes _ d   std::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> >::_Block_size / �   std::atomic<long>::is_always_lock_free n d   std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Block_size � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo  �   �  $ d   std::_Locbase<int>::collate " d   std::_Locbase<int>::ctype % d   std::_Locbase<int>::monetary $ d   std::_Locbase<int>::numeric ! d   std::_Locbase<int>::time % d    std::_Locbase<int>::messages   d  ? std::_Locbase<int>::all ! d    std::_Locbase<int>::none �   std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> >::_Bucket_size �   std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> >::_Multi � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable    �   �   �    std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >::_Minimum_asan_allocation_alignment �   �  \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ��    std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0>::_Multi ��   std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0>::_Standard � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible   �   鬗  � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable 8 �   std::atomic<unsigned long>::is_always_lock_free : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift    std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >::_Minimum_asan_allocation_alignment ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask  g    NVDX_SWAPCHAIN_NONE    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > >::_Minimum_asan_allocation_alignment G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ]    std::allocator<nvrhi::d3d12::ShaderTable::Entry>::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> >::_Minimum_asan_allocation_alignment / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex �    std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0>::_Multi �   std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0>::_Standard � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable *   std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> >::_Minimum_asan_allocation_alignment  �5   std::_Consume_header  �5   std::_Generate_header  漟    NVDX_OBJECT_NONE  �+   PowerUserMaximum :    std::integral_constant<unsigned __int64,2>::value  �+    DVEXTENT_CONTENT u    std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0>::_Multi �   std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0>::_Standard �    std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> >::_Minimum_asan_allocation_alignment �   std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> >::_Bucket_size �   std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> >::_Multi  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment $ 瞗    D3D12_LIFETIME_STATE_IN_USE + 竐  �   NV_DISPLAY_TV_FORMAT_UHD_4Kp30 + 竐  �   NV_DISPLAY_TV_FORMAT_UHD_4Kp25 + 竐  �    NV_DISPLAY_TV_FORMAT_UHD_4Kp24 # �*   BINDSTATUS_FINDINGRESOURCE  �*   BINDSTATUS_CONNECTING  �*   BINDSTATUS_REDIRECTING % �*   BINDSTATUS_BEGINDOWNLOADDATA # �*   BINDSTATUS_DOWNLOADINGDATA # �*   BINDSTATUS_ENDDOWNLOADDATA + �*   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �*   BINDSTATUS_INSTALLINGCOMPONENTS ) �*  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �*  
 BINDSTATUS_USINGCACHEDCOPY " �*   BINDSTATUS_SENDINGREQUEST $ �*   BINDSTATUS_CLASSIDAVAILABLE % �*  
 BINDSTATUS_MIMETYPEAVAILABLE * �*   BINDSTATUS_CACHEFILENAMEAVAILABLE & �*   BINDSTATUS_BEGINSYNCOPERATION $ �*   BINDSTATUS_ENDSYNCOPERATION # �*   BINDSTATUS_BEGINUPLOADDATA ! �*   BINDSTATUS_UPLOADINGDATA ! �*   BINDSTATUS_ENDUPLOADDATA # �*   BINDSTATUS_PROTOCOLCLASSID  �*   BINDSTATUS_ENCODING - �*   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �*   BINDSTATUS_CLASSINSTALLLOCATION  �*   BINDSTATUS_DECODING & �*   BINDSTATUS_LOADINGMIMEHANDLER , �*   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �*   BINDSTATUS_FILTERREPORTMIMETYPE ' �*   BINDSTATUS_CLSIDCANINSTANTIATE �    std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment % �*   BINDSTATUS_IUNKNOWNAVAILABLE  �*   BINDSTATUS_DIRECTBIND * 蔱   NV_SCALING_GPU_SCALING_TO_CLOSEST  �*   BINDSTATUS_RAWMIMETYPE ) 蔱   NV_SCALING_GPU_SCALING_TO_NATIVE " �*    BINDSTATUS_PROXYDETECTING ) 蔱   NV_SCALING_GPU_SCANOUT_TO_NATIVE   �*  ! BINDSTATUS_ACCEPTRANGES ; 蔱   NV_SCALING_GPU_SCALING_TO_ASPECT_SCANOUT_TO_NATIVE  �*  " BINDSTATUS_COOKIE_SENT + �*  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �*  $ BINDSTATUS_COOKIE_SUPPRESSED ( �*  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �*  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �*  ' BINDSTATUS_COOKIE_STATE_REJECT ' �*  ( BINDSTATUS_COOKIE_STATE_PROMPT & �*  ) BINDSTATUS_COOKIE_STATE_LEASH * �*  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �*  + BINDSTATUS_POLICY_HREF  �*  , BINDSTATUS_P3P_HEADER + �*  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �*  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + �*  / BINDSTATUS_SESSION_COOKIES_ALLOWED   �*  0 BINDSTATUS_CACHECONTROL . �*  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) �*  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & �*  3 BINDSTATUS_PUBLISHERAVAILABLE ( �*  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �*  5 BINDSTATUS_SSLUX_NAVBLOCKED , �*  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �*  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �*  8 BINDSTATUS_64BIT_PROGRESS  �*  8 BINDSTATUS_LAST  �*  9 BINDSTATUS_RESERVED_0  �*  : BINDSTATUS_RESERVED_1  �*  ; BINDSTATUS_RESERVED_2  �*  < BINDSTATUS_RESERVED_3  �*  = BINDSTATUS_RESERVED_4  �*  > BINDSTATUS_RESERVED_5  �*  ? BINDSTATUS_RESERVED_6  �*  @ BINDSTATUS_RESERVED_7  �*  A BINDSTATUS_RESERVED_8  �*  B BINDSTATUS_RESERVED_9  �*  C BINDSTATUS_RESERVED_A  �*  D BINDSTATUS_RESERVED_B  �*  E BINDSTATUS_RESERVED_C  �*  F BINDSTATUS_RESERVED_D  �*  G BINDSTATUS_RESERVED_E  �*  H BINDSTATUS_RESERVED_F  �*  I BINDSTATUS_RESERVED_10  �*  J BINDSTATUS_RESERVED_11  �*  K BINDSTATUS_RESERVED_12  �*  L BINDSTATUS_RESERVED_13  �*  M BINDSTATUS_RESERVED_14 ) 瞘   NV_TIMING_OVERRIDE_NV_PREDEFINED &   std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> >::_Bucket_size &   std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> >::_Min_buckets  �    std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> >::_Multi ; �   std::atomic<unsigned __int64>::is_always_lock_free    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > >::_Minimum_asan_allocation_alignment p    std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> >::_Minimum_asan_allocation_alignment b    std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >::_Minimum_asan_allocation_alignment  8+    CIP_DISK_FULL  8+   CIP_ACCESS_DENIED ! 8+   CIP_NEWER_VERSION_EXISTS ! 8+   CIP_OLDER_VERSION_EXISTS  8+   CIP_NAME_CONFLICT 1 8+   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + 8+   CIP_EXE_SELF_REGISTERATION_TIMEOUT  8+   CIP_UNSAFE_TO_ABORT  8+   CIP_NEED_REBOOT 2 g   D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2 �    std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> >::_Minimum_asan_allocation_alignment " +    Uri_PROPERTY_ABSOLUTE_URI ]   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > >::_Minimum_asan_allocation_alignment  +   Uri_PROPERTY_USER_NAME  +   Uri_PROPERTY_HOST_TYPE  +   Uri_PROPERTY_ZONE  �+    Uri_HOST_UNKNOWN  �+   Uri_HOST_DNS  �+   Uri_HOST_IPV4  �+   Uri_HOST_IPV6 W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified q    std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *>::_Minimum_asan_allocation_alignment "   std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> >::_Bucket_size - �    std::chrono::system_clock::is_steady "   std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> >::_Min_buckets �    std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> >::_Multi R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified $ E   std::ratio<1,10000000>::num ( E  ��枠 std::ratio<1,10000000>::den � d   std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Minimum_map_size 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos %    std::ctype<char>::table_size # �        nvrhi::AllSubresources _    std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment K    std::allocator<unsigned short>::_Minimum_asan_allocation_alignment J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos p    std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Bytes u d   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Block_size   沞    D3D_DRIVER_TYPE_UNKNOWN ! 沞   D3D_DRIVER_TYPE_HARDWARE " 沞   D3D_DRIVER_TYPE_REFERENCE  沞   D3D_DRIVER_TYPE_NULL ! 沞   D3D_DRIVER_TYPE_SOFTWARE � d   std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Block_size ) 罳    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED ) 罳   D3D_PRIMITIVE_TOPOLOGY_POINTLIST ( 罳   D3D_PRIMITIVE_TOPOLOGY_LINELIST  E   std::ratio<1,1>::num ) 罳   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP , 罳   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST - 罳   D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP  E   std::ratio<1,1>::den , 罳  
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ - 罳   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 0 罳   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 1 罳  
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 9 罳  ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment 9 罳  " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 9 罳  # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 9 罳  $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 9 罳  % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 9 罳  & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST 9 罳  ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 9 罳  ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 9 罳  ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST : 罳  * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST : 罳  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST : 罳  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST : 罳  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST : 罳  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST : 罳  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST : 罳  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST : 罳  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST : 罳  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST : 罳  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST : 罳  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST : 罳  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST : 罳  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST : 罳  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST : 罳  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST : 罳  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST : 罳  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST : 罳  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST : 罳  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST R    std::allocator<nvrhi::TextureBarrier>::_Minimum_asan_allocation_alignment : 罳  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST : 罳  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST : 罳  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST : 罳  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST  �*   BINDSTRING_HEADERS   廵    D3D_PRIMITIVE_UNDEFINED   �*   BINDSTRING_ACCEPT_MIMES �   �=  廵   D3D_PRIMITIVE_POINT  廵   D3D_PRIMITIVE_LINE  �*   BINDSTRING_EXTRA_URL  �*   BINDSTRING_LANGUAGE  廵   D3D_PRIMITIVE_TRIANGLE  �*   BINDSTRING_USERNAME  廵   D3D_PRIMITIVE_LINE_ADJ # 廵   D3D_PRIMITIVE_TRIANGLE_ADJ  �*   BINDSTRING_PASSWORD , 廵   D3D_PRIMITIVE_1_CONTROL_POINT_PATCH  �*   BINDSTRING_UA_PIXELS , 廵  	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH  �*   BINDSTRING_UA_COLOR , 廵  
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH  �*  	 BINDSTRING_OS  �*  
 BINDSTRING_USER_AGENT , 廵   D3D_PRIMITIVE_4_CONTROL_POINT_PATCH $ �*   BINDSTRING_ACCEPT_ENCODINGS , 廵   D3D_PRIMITIVE_5_CONTROL_POINT_PATCH  �*   BINDSTRING_POST_COOKIE , 廵  
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH , 廵   D3D_PRIMITIVE_7_CONTROL_POINT_PATCH " �*  
 BINDSTRING_POST_DATA_MIME , 廵   D3D_PRIMITIVE_8_CONTROL_POINT_PATCH  �*   BINDSTRING_URL  �*   BINDSTRING_IID , 廵   D3D_PRIMITIVE_9_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_10_CONTROL_POINT_PATCH ' �*   BINDSTRING_FLAG_BIND_TO_OBJECT $ �*   BINDSTRING_PTR_BIND_CONTEXT - 廵   D3D_PRIMITIVE_11_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_12_CONTROL_POINT_PATCH  �*   BINDSTRING_XDR_ORIGIN - 廵   D3D_PRIMITIVE_13_CONTROL_POINT_PATCH   �*   BINDSTRING_DOWNLOADPATH - 廵   D3D_PRIMITIVE_14_CONTROL_POINT_PATCH  �*   BINDSTRING_ROOTDOC_URL $ �*   BINDSTRING_INITIAL_FILENAME - 廵   D3D_PRIMITIVE_15_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_16_CONTROL_POINT_PATCH " �*   BINDSTRING_PROXY_USERNAME " �*   BINDSTRING_PROXY_PASSWORD - 廵   D3D_PRIMITIVE_17_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_18_CONTROL_POINT_PATCH ! �*   BINDSTRING_ENTERPRISE_ID  �*   BINDSTRING_DOC_URL - 廵   D3D_PRIMITIVE_19_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_20_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_21_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_22_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_23_CONTROL_POINT_PATCH J E   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 - 廵   D3D_PRIMITIVE_24_CONTROL_POINT_PATCH - 廵    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH N E  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 - 廵  ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH - 廵  " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH - 廵  # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH - 廵  $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH - 廵  % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH - 廵  & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH - 廵  ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( E  ��枠 std::ratio<10000000,1>::num $ E   std::ratio<10000000,1>::den P E  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx " 鄁    D3D_SRV_DIMENSION_UNKNOWN K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy ! 鄁   D3D_SRV_DIMENSION_BUFFER $ 鄁   D3D_SRV_DIMENSION_TEXTURE1D ) 鄁   D3D_SRV_DIMENSION_TEXTURE1DARRAY $ 鄁   D3D_SRV_DIMENSION_TEXTURE2D ) 鄁   D3D_SRV_DIMENSION_TEXTURE2DARRAY & 鄁   D3D_SRV_DIMENSION_TEXTURE2DMS L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos + 鄁   D3D_SRV_DIMENSION_TEXTURE2DMSARRAY $ 鄁   D3D_SRV_DIMENSION_TEXTURE3D & 鄁  	 D3D_SRV_DIMENSION_TEXTURECUBE + 鄁  
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY # 鄁   D3D_SRV_DIMENSION_BUFFEREX < E  ��枠 std::integral_constant<__int64,10000000>::value 1 E   std::integral_constant<__int64,1>::value C �  D3D12_RENDER_PASS_BEGINNING_ACCESS_PRESERVE_LOCAL_PARAMETERS E �  D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS : �  D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS  �.  LPPARAMDESCEX 9 �  D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS ) �  D3D12_RENDER_PASS_BEGINNING_ACCESS  �  D3D12_RANGE_UINT64  #+  FUNCKIND @ �  D3D12_RENDER_PASS_ENDING_ACCESS_PRESERVE_LOCAL_PARAMETERS + �  D3D12_RAYTRACING_GEOMETRY_AABBS_DESC  �.  tagPARAMDESCEX     INT8 + �  D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE &   D3D12_RENDER_PASS_ENDING_ACCESS % 鷨  D3D12_RENDER_TARGET_BLEND_DESC + 鴨  D3D12_RENDER_PASS_RENDER_TARGET_DESC + 鰡  D3D12_WRITEBUFFERIMMEDIATE_PARAMETER  �.  PARAMDESC  �.  tagPARAMDESC ! D�  ID3D12GraphicsCommandList5 + 魡  D3D12_RENDER_PASS_DEPTH_STENCIL_DESC / 饐  D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC  �.  tagARRAYDESC  韱  D3D12_TEXCUBE_ARRAY_SRV 2 雴  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV  閱  D3D12_TEXCUBE_SRV  <+  CALLCONV  鑶  D3D12_TEX1D_ARRAY_RTV  鐔  D3D12_TEX1D_ARRAY_SRV  鍐  D3D12_TEX2D_ARRAY_RTV % 鋯  D3D12_SUBRESOURCE_RANGE_UINT64  釂  D3D12_TEX3D_SRV  鄦  D3D12_TEX2D_ARRAY_SRV ! 趩  D3D12_DEPTH_STENCILOP_DESC  貑  D3D12_TEX2D_RTV ' 讍  D3D12_PIPELINE_STATE_STREAM_DESC  諉  D3D12_TEX3D_RTV 7 詥  D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER  覇  D3D12_TEX2DMS_ARRAY_RTV  +  DESCKIND B 褑  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC !   ID3D12GraphicsCommandList3  �.  ELEMDESC  蛦  D3D12_RASTERIZER_DESC  艈  D3D12_TEX1D_SRV % 羻  D3D12_RAYTRACING_GEOMETRY_DESC  �.  BINDPTR  �.  tagFUNCDESC & 絾  D3D12_GPU_VIRTUAL_ADDRESS_RANGE  p,  INVOKEKIND  粏  D3D12_SHADER_BYTECODE  x.  TLIBATTR  箚  D3D12_TEX2D_ARRAY_DSV ! 覅  ID3D12GraphicsCommandList1  竼  D3D12_BUFFER_SRV  秵  D3D12_BUFFER_RTV  拕  ID3D12Device7 ! .�  ID3D12GraphicsCommandList4  �.  tagBINDPTR  磫  ID3D12MetaCommand  �.  tagSTATSTG    D3D12_META_COMMAND_DESC  爢  D3D12_TEX1D_DSV  �.  tagTYPEDESC 9 焼  D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC  潌  D3D12_TEX2DMS_ARRAY_SRV  �.  FUNCDESC  渾  D3D12_BLEND_DESC  "   HREFTYPE  �+  SYSKIND ; 枂  D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS , 媶  D3D12_PROTECTED_RESOURCE_SESSION_DESC  �,  tagVARDESC  墕  D3D12_TEX1D_ARRAY_UAV  昰  RECT  垎  D3D12_DEPTH_STENCIL_DESC  儐  ID3D12LifetimeOwner  q�  D3D12_TEX2DMS_RTV " p�  D3D12_CACHED_PIPELINE_STATE  �+  TYPEKIND  n�  D3D12_DESCRIPTOR_RANGE1  �.  IEnumSTATSTG  鷥  ID3D12Device2  l�  D3D12_MIP_REGION  j�  D3D12_SAMPLE_POSITION  �.  STATSTG  B�  ID3D12ProtectedSession  �.  ITypeComp 
   LPVOID  �.  TYPEDESC  h�  D3D12_TEX1D_RTV  h�  ID3D12Device5  {�  ID3D12Device6 ! g�  D3D12_SO_DECLARATION_ENTRY ( e�  D3D12_META_COMMAND_PARAMETER_DESC  .  IDLDESC  �.  tagELEMDESC  a�  D3D12_BUFFER_UAV 1 _�  D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE  ]�  D3D12_TEX2D_SRV  [�  D3D12_RESOURCE_DESC1  X�  D3D12_STATE_SUBOBJECT  .  tagIDLDESC  V�  D3D12_TEX2DMS_SRV ! 靺  ID3D12GraphicsCommandList2  v%  VARIANTARG ! U�  ID3D12GraphicsCommandList6  }.  EXCEPINFO  g  NVDX_SwapChainHandle__  }.  tagEXCEPINFO 
    DISPID  畢  D3D12_ROOT_CONSTANTS  竒  SECURITY_ATTRIBUTES     MEMBERID  �,  _CatchableType  u   UINT  竐  _NV_DISPLAY_TV_FORMAT  ▍  D3D12_PACKED_MIP_INFO &   D3D12_SHADER_RESOURCE_VIEW_DESC ' 篺  D3D12_BACKGROUND_PROCESSING_MODE  媏  D3D12_BARRIER_LAYOUT " 晠  D3D12_GPU_DESCRIPTOR_HANDLE  H,  tagCAUL  x.  tagTLIBATTR  �$  _TP_CALLBACK_PRIORITY  攨  D3D12_TEX1D_ARRAY_DSV " +  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> % 颵  D3D12_RAYTRACING_INSTANCE_DESC & �+  $_TypeDescriptor$_extraBytes_24  拝  D3D12_TEX2DMS_DSV & 憛  D3D12_CONSTANT_BUFFER_VIEW_DESC  弲  D3D12_INPUT_LAYOUT_DESC 6 r.  __vcrt_va_list_is_reference<char const * const>  妳  ID3D12DescriptorHeap  麯  D3D12_RESOURCE_DESC  罳  D3D_PRIMITIVE_TOPOLOGY , 灡  NVAPI_D3D12_PSO_DOMAIN_SHADER_DESC_V3  "   NvU32  n.  tagShutdownType  q   OLECHAR G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> , 惐  NVAPI_D3D12_PSO_VERTEX_SHADER_DESC_V3  x�  D3D12_TILE_REGION_SIZE  v�  D3D12_TEX2DMS_ARRAY_DSV   u�  D3D12_DEPTH_STENCIL_VALUE  �4  _Ctypevec " 4T  ID3D12StateObjectProperties  �+  tagCABSTR  雈  D3D12_BARRIER_TYPE     off_t  <+  tagCALLCONV  �+  tagTYPEKIND $ q�  D3D12_RENDER_TARGET_VIEW_DESC     D3D12_STATIC_BORDER_COLOR & 痝  $_TypeDescriptor$_extraBytes_28  v%  VARIANT  e�  D3D12_TEX2DMS_ARRAY_UAV ) 惐  NVAPI_D3D12_PSO_VERTEX_SHADER_DESC  #   uintmax_t  c�  ID3D12Heap  �-  ISequentialStream  脙  _NV_PSO_EXTENSION     int64_t % S�  ID3D12ProtectedResourceSession  -�  D3D12_TEX2D_DSV &   $_TypeDescriptor$_extraBytes_58  �,  BSTRBLOB    _Smtx_t  �=  _Thrd_result  ,�  ID3D12QueryHeap  �  D3D12_DISPATCH_RAYS_DESC  #   rsize_t  鵨  _D3D_INCLUDE_TYPE " �  D3D12_SUBRESOURCE_FOOTPRINT  #   DWORD_PTR  �,  TYPEATTR     VARIANT_BOOL - h.  __vc_attributes::event_sourceAttribute 9 a.  __vc_attributes::event_sourceAttribute::optimize_e 5 _.  __vc_attributes::event_sourceAttribute::type_e > ].  __vc_attributes::helper_attributes::v1_alttypeAttribute F X.  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 U.  __vc_attributes::helper_attributes::usageAttribute B Q.  __vc_attributes::helper_attributes::usageAttribute::usage_e * N.  __vc_attributes::threadingAttribute 7 G.  __vc_attributes::threadingAttribute::threading_e - D.  __vc_attributes::aggregatableAttribute 5 =.  __vc_attributes::aggregatableAttribute::type_e / :.  __vc_attributes::event_receiverAttribute 7 1.  __vc_attributes::event_receiverAttribute::type_e ' ..  __vc_attributes::moduleAttribute / %.  __vc_attributes::moduleAttribute::type_e < �  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO  �7  __std_fs_find_data & 蝔  $_TypeDescriptor$_extraBytes_23  ET  ID3D12Pageable  �  ID3D12Device3  �  D3D12_TEX1D_UAV 
 z%  PUWSTR -   $_s__CatchableTypeArray$_extraBytes_32 # =  __std_fs_reparse_data_buffer Z �  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ �  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` �  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �7  __std_fs_dir_handle ( �$  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  	%  AR_STATE  .  tagCADBL  %  _DEVICE_DATA_SET_RANGE  �3  __std_access_rights # 齽  D3D12_INDIRECT_ARGUMENT_DESC W 	�  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-IncrementingConstant> V �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-UnorderedAccessView> U �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-ShaderResourceView> U �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-ConstantBufferView> K �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-Constant> O �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-VertexBuffer>  +  VARKIND 3   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE  鮿  D3D12_QUERY_HEAP_DESC  鍍  ID3D12Device1  ".  _TypeDescriptor & g  $_TypeDescriptor$_extraBytes_34 & 黤  $_TypeDescriptor$_extraBytes_43 ( 駝  D3D12_COMPUTE_PIPELINE_STATE_DESC ! f  D3D12_DESCRIPTOR_HEAP_TYPE  �,  _tagPSUACTION 
 .  tagDEC  .  CALPSTR     LONG_PTR & 飫  D3D12_RESOURCE_ALIASING_BARRIER  �*  tagBINDSTRING  韯  _Stl_critical_section 	 �  tm   杄  _D3D_SHADER_VARIABLE_TYPE ! 癳  _D3D_SHADER_VARIABLE_CLASS  r-  tagCACLIPDATA  #   ULONG_PTR " 羍  D3D12_RESOURCE_BARRIER_TYPE % !+  _s__RTTICompleteObjectLocator2 " ╣  D3D12_DESCRIPTOR_RANGE_TYPE  �+  tagURLZONE  %  PUWSTR_C $ 鐒  D3D12_DEPTH_STENCIL_VIEW_DESC  �$  PTP_CLEANUP_GROUP % 輨  D3D12_RESOURCE_ALLOCATION_INFO  8+  __MIDL_ICodeInstall_0001  p  PCHAR 8 嚤  NVAPI_D3D12_PSO_SET_SHADER_EXTENSION_SLOT_DESC_V1  讋  NV_CUSTOM_SEMANTIC  �*  tagBINDSTATUS  w$  _GUID  �,  _URLZONEREG  �+  _LARGE_INTEGER ' .  _LARGE_INTEGER::<unnamed-type-u>  蹌  D3D12_ROOT_DESCRIPTOR1 & 単  $_TypeDescriptor$_extraBytes_30 - 賱  D3D12_PROTECTED_RESOURCE_SESSION_DESC1  糵  D3D12_COLOR_WRITE_ENABLE  �+  CLIPDATA  讋  _NV_CUSTOM_SEMANTIC  �+  CAFILETIME  .  tagCALPSTR  =,  CALPWSTR 
 �*  CAL  �,  tagCABSTRBLOB  蝿  DXGI_SAMPLE_DESC  �+  tagSAFEARRAYBOUND & l.  $_TypeDescriptor$_extraBytes_29  4�  ID3D12Device4  虅  D3D12_COMMAND_QUEUE_DESC  sg  ID3DBlob  .  tagCAFLT A .  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 糴  $_TypeDescriptor$_extraBytes_46 
 �+  tagCAH  迒  D3D12_INPUT_ELEMENT_DESC  .  DECIMAL  �+  tagCAUI  蕜  D3D12_STATE_OBJECT_DESC & 襢  $_TypeDescriptor$_extraBytes_51  !   WORD  �,  _s__CatchableType  縿  D3D12_HEAP_DESC  �,  CAUH  〆  D3D_NAME  +  tagCADATE  莋  D3D_SHADER_MODEL  .  CADBL  R  LPCOLESTR  %  PCUWSTR  �+  CAPROPVARIANT & 箘  D3D12_TILED_RESOURCE_COORDINATE  .  CAFLT & 纅  $_TypeDescriptor$_extraBytes_19  瞘  _NV_TIMING_OVERRIDE & 穭  D3D12_RESOURCE_ALLOCATION_INFO1 & g  $_TypeDescriptor$_extraBytes_21  #   uint64_t ' �$  JOB_OBJECT_IO_RATE_CONTROL_FLAGS 9 	.  __vcrt_va_list_is_reference<wchar_t const * const>  �+  _USER_ACTIVITY_PRESENCE  �:  __std_fs_filetime E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> ( 祫  D3D12_FEATURE_DATA_D3D12_OPTIONS5    PLONG & 6+  $_TypeDescriptor$_extraBytes_20  瘎  ID3D12Device8  �+  DISPPARAMS  mU  ID3D12Fence  �+  _FILETIME $ 峠  D3D12_LINE_RASTERIZATION_MODE ( 艃  NVAPI_D3D12_PSO_EXTENSION_DESC_V1  p  va_list  '%  FS_BPIO_INFLAGS - �,  $_s__CatchableTypeArray$_extraBytes_16   5=  __std_fs_copy_file_result  羶  D3D12_STREAM_OUTPUT_DESC  �7  __std_code_page  %  PDEVICE_DSM_DEFINITION      BYTE . 磃  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE . ”  NVAPI_D3D12_PSO_GEOMETRY_SHADER_DESC_V5  簝  D3D12_CLEAR_VALUE % 鵪  D3D12_RAYTRACING_GEOMETRY_TYPE 
 R  PCWSTR  .  IStream � 攦  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > �穬  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > >,1> P瘍  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > >,1> U 殎  std::_Default_allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker> > \ 厓  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > � x�  std::_Simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > � 瀮  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > >,1> � 杻  std::allocator_traits<std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > W |  std::_Default_allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker *> > 
  std::_Default_allocator_traits<std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > l 褈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *> > � a�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > a 噧  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > �  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > O H�  std::_Default_allocator_traits<std::allocator<D3D12_DESCRIPTOR_RANGE1> > 們  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > >,1> � z�  std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > i }  std::_Default_allocator_traits<std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > > ;   std::_Conditionally_enabled_hash<nvrhi::IBuffer *,1> � 齸  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> > > E u�  std::_Compressed_pair<std::equal_to<nvrhi::IBuffer *>,float,1> k 穫  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > E l�  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> �   std::_Simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > � c�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > G 3�  std::_Simple_types<std::pair<unsigned int const ,unsigned int> > � 9�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > [G{  std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> > � H�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > � R�  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > >,1> � 莵  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> > > F J�  std::allocator_traits<std::allocator<D3D12_DESCRIPTOR_RANGE1> > � 魝  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > H 蟳  std::_Default_allocator_traits<std::allocator<unsigned __int64> > � <�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > N !�  std::_Simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > 2�  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > >,1> � *�  std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> S #�  std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > � �  std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > b 
�  std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > �  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > >,1> � 辺  std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> > � 
�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> > > c 秚  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > � 鰝  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > � 鐐  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > y 瀞  std::_Uhash_choose_transparency<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT>,void> E 輦  std::_Compressed_pair<std::equal_to<enum DXGI_FORMAT>,float,1> � *z  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> > > � 蟼  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > � 剚  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > > 艂  std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > b 箓  std::allocator_traits<std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > �獋  std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0>   std::allocator_traits<std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > � 攤  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > � ��  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> >,std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > >,1> � �|  std::_Simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > Ex�  std::list<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > z �  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> > > N 齺  std::_Compressed_pair<std::equal_to<nvrhi::rt::IShaderTable *>,float,1> � L~  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > E 魜  std::_Vector_val<std::_Simple_types<D3D12_DESCRIPTOR_RANGE1> > H 鄟  std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > x 褋  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Vector_val<std::_Simple_types<unsigned __int64> >,1> � 蓙  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> > > y ﹣  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > | }  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > > K 邁  std::_Default_allocator_traits<std::allocator<ID3D12CommandList *> > N Z  std::_Default_allocator_traits<std::allocator<D3D12_RESOURCE_BARRIER> > & 簛  std::equal_to<nvrhi::IBuffer *> � 
r  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > > k 趒  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > p 珌  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > L 渷  std::allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker> > F 顈  std::_Simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > � 搎  std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> > � &q  std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Strategy � q  std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Redbl p巵  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > >,1> � 唩  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > > � w�  std::_Compressed_pair<std::allocator<nvrhi::AftermathMarkerTracker>,std::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> >,1> } x  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > %榾  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> > > 7o�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > X聙  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > { ]�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> > > r _�  std::allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> > > 1 鮭  std::less<nvrhi::AftermathMarkerTracker *> NP�  std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> � J�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > O 踳  std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> > � ;�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > . 攟  std::allocator<D3D12_DESCRIPTOR_RANGE1> J ,�  std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > r �  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > d �  std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > z �  std::_Compressed_pair<std::hash<nvrhi::IBuffer *>,std::_Compressed_pair<std::equal_to<nvrhi::IBuffer *>,float,1>,1> 
鷢  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > � 饊  std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > 鄝  std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> > D  ~  std::_Conditionally_enabled_hash<nvrhi::rt::IShaderTable *,1> � r  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > O膧  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > p <x  std::_Default_allocator_traits<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> > > ~ 眬  std::_Compressed_pair<std::allocator<ID3D12CommandList *>,std::_Vector_val<std::_Simple_types<ID3D12CommandList *> >,1> * ﹢  std::allocator<ID3D12CommandList *> 殌  std::allocator_traits<std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> > > � 墍  std::list<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > �  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > � �  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > � �  std::list<std::pair<nvrhi::IBuffer * const,unsigned __int64>,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > y 辯  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void> L o}  std::_Simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > � F  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > N ~  std::allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker *> > � o  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > ; e  std::_Compressed_pair<std::equal_to<void *>,float,1> ; Pv  std::_Conditionally_enabled_hash<enum DXGI_FORMAT,1> 減  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> > E \  std::allocator_traits<std::allocator<D3D12_RESOURCE_BARRIER> > EN  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,1> � jv  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � 銃  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > � 2  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > �   std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > "   std::hash<nvrhi::IBuffer *> �   std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > x �~  std::allocator_traits<std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> > >  饉  std::equal_to<void *> p Tn  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > A 雫  std::_Vector_val<std::_Simple_types<ID3D12CommandList *> > B 醻  std::allocator_traits<std::allocator<ID3D12CommandList *> > c 觺  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *> > " Sv  std::hash<enum DXGI_FORMAT> 4 聗  std::allocator<nvrhi::AftermathMarkerTracker> ` 硚  std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> > b 焴  std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> > - Rw  std::allocator<D3D12_RESOURCE_BARRIER> P 媬  std::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> > + #~  std::hash<nvrhi::rt::IShaderTable *> z 厏  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1> � 皉  std::_Simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > 1 
n  std::_Conditionally_enabled_hash<void *,1> � m~  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > �V~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >,1> > 7~  std::_Vector_val<std::_Simple_types<unsigned __int64> > � -~  std::_Compressed_pair<std::hash<nvrhi::rt::IShaderTable *>,std::_Compressed_pair<std::equal_to<nvrhi::rt::IShaderTable *>,float,1>,1> �~  std::list<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > � At  std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> > d .�  std::_Default_allocator_traits<std::allocator<std::pair<unsigned int const ,unsigned int> > > N  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > � 梷  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > J N}  std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > ` 亇  std::allocator_traits<std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > > Q q}  std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > l}  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > � V}  std::_Compressed_pair<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> >,std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >,1> � @}  std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0>  9}  std::_Default_sentinel ~ 8}  std::_Compressed_pair<std::allocator<_NV_CUSTOM_SEMANTIC>,std::_Vector_val<std::_Simple_types<_NV_CUSTOM_SEMANTIC> >,1> [ e]  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 	W  std::default_delete<wchar_t [0]> � n  std::_Uhash_choose_transparency<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *>,void> . &3  std::_Conditionally_enabled_hash<int,1> A �;  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> i 榶  std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> > � xY  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >,std::_Iterator_base0> ? �*  std::_Default_allocator_traits<std::allocator<wchar_t> > S U  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IResource> > > � }_  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > *> � !}  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > \ 瓲  std::pair<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> *,bool> . 1�  std::integer_sequence<unsigned __int64> ) 躖  std::hash<nvrhi::IBindingLayout *>  .  std::_Lockit d }  std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > s }  std::allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > >  �>  std::timed_mutex X ee  std::_Default_allocator_traits<std::allocator<nvrhi::d3d12::ShaderTable::Entry> > � 鮸  std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > * �'  std::hash<enum nvrhi::ResourceType> � 縏  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > >,1> - c8  std::reverse_iterator<wchar_t const *> � 鍇  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > " A*  std::_Char_traits<char,int>  4  std::_Fs_file Z 鱳  std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> > & 褆  std::equal_to<enum DXGI_FORMAT>  え  std::_Value_init_tag � 2e  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> > > a 隸  std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >  "   std::_Atomic_counter_t  �)  std::_Num_base & 23  std::hash<std::error_condition> K \(  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > � Aw  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > � ２  std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0>   std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > >,1> = YJ  std::array<std::pair<unsigned int,nvrhi::IBuffer *>,6> X 蘾  std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > � cY  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > >  x0  std::_Big_uint128  V>  std::condition_variable b 鱈  std::vector<D3D12_RAYTRACING_INSTANCE_DESC,std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> > x 臠  std::vector<D3D12_RAYTRACING_INSTANCE_DESC,std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> >::_Reallocation_policy f 紎  std::_Compressed_pair<std::hash<void *>,std::_Compressed_pair<std::equal_to<void *>,float,1>,1> � 瞸  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > ) O*  std::_Narrow_char_traits<char,int> 橺  std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> � [  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > � 渱  std::_Compressed_pair<std::allocator<D3D12_DESCRIPTOR_RANGE1>,std::_Vector_val<std::_Simple_types<D3D12_DESCRIPTOR_RANGE1> >,1>    std::hash<float> 6 (  std::allocator<nvrhi::rt::PipelineHitGroupDesc> � w[  std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> > L 榹  std::_Default_allocator_traits<std::allocator<nvrhi::BufferBarrier> > " 厊  std::_Align_type<double,64>  *3  std::hash<int>  *  std::_Num_int_base � 倈  std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  �6  std::ctype<wchar_t> 1 D�  std::allocator<nvrhi::VertexAttributeDesc> " �3  std::_System_error_category 9 XR  std::shared_ptr<nvrhi::d3d12::InternalCommandList> F  std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> > � 繸  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > >,1> / �'  std::_Conditionally_enabled_hash<bool,1> 8 am  std::_Ptr_base<nvrhi::d3d12::CommandListInstance> N 	o  std::_Simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > � Y  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > � }|  std::list<std::pair<enum DXGI_FORMAT const ,unsigned char>,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > >  �)  std::float_denorm_style v 巑  std::_Simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > � )\  std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> > K y~  std::_Default_allocator_traits<std::allocator<_NV_CUSTOM_SEMANTIC> > � 橵  std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> > � |  std::initializer_list<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > � \  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > � |  std::list<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > T  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 缐  std::piecewise_construct_t C 歿  std::allocator_traits<std::allocator<nvrhi::BufferBarrier> > f 寋  std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > u 
c  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 �-  std::allocator_traits<std::allocator<wchar_t> > & 唟  std::equal_to<unsigned __int64> C V  std::_Vector_val<std::_Simple_types<nvrhi::TextureBarrier> > '倇  std::unordered_map<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >  $0  std::bad_cast ] �  std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >  j^  std::equal_to<void> T   std::vector<D3D12_DESCRIPTOR_RANGE1,std::allocator<D3D12_DESCRIPTOR_RANGE1> > j uz  std::vector<D3D12_DESCRIPTOR_RANGE1,std::allocator<D3D12_DESCRIPTOR_RANGE1> >::_Reallocation_policy � �9  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > \ [V  std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > t 鎀  std::_Compressed_pair<std::allocator<unsigned short>,std::_Vector_val<std::_Simple_types<unsigned short> >,1> o $Y  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> , 郑  std::_Tuple_val<unsigned int const &> y 唝  std::_Uhash_choose_transparency<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *>,void> P ;z  std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> > X /z  std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> >::_Redbl  +r  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > > [ P�  std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > " &*  std::numeric_limits<double> � ,z  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> > > $ 轕  std::atomic<unsigned __int64>  T0  std::__non_rtti_object 6 z  std::allocator<nvrhi::AftermathMarkerTracker *> Tz  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > >,1> ( n  std::_Basic_container_proxy_ptr12 
辻  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > [ �  std::_Default_allocator_traits<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > 騰  std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> > � z  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > L 5�  std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > � cV  std::_Compressed_pair<std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> >,std::_Vector_val<std::_Simple_types<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > >,1> > 紽  std::vector<unsigned int,std::allocator<unsigned int> > T 孎  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1 �  std::array<nvrhi::FramebufferAttachment,8> � 鈁  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > >  "*  std::_Num_float_base K 饄  std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > � 難  std::initializer_list<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >  �1  std::logic_error � 騜  std::_Compressed_pair<std::allocator<enum nvrhi::ResourceStates>,std::_Vector_val<std::_Simple_types<enum nvrhi::ResourceStates> >,1> s5\  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > >,1> k m  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > 5 怲  std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> 鄖  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > 7 安  std::_Conditionally_enabled_hash<unsigned int,1> G �'  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  Y4  std::pointer_safety ! �-  std::char_traits<char32_t>  O5  std::locale  �5  std::locale::_Locimp  `5  std::locale::facet   h5  std::locale::_Facet_guard  5  std::locale::id ? 褃  std::allocator_traits<std::allocator<unsigned __int64> > s e  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > : 脃  std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>   *  std::numeric_limits<bool> � l�  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > >,1> �JO  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > J 磞  std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > # �*  std::_WChar_traits<char16_t> P =W  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > � E[  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > >,std::_Iterator_base0> T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy w �  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >   ]  std::_Fake_proxy_ptr_impl 	  std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> F dn  std::_Default_allocator_traits<std::allocator<unsigned short> > � 礯  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > *> * *  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> Z za  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > � 焬  std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> M M8  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > 玠  std::list<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > � %(  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  d2  std::overflow_error B 鑅  std::_Vector_val<std::_Simple_types<nvrhi::BufferBarrier> > . 剏  std::vector<char,std::allocator<char> > D Ty  std::vector<char,std::allocator<char> >::_Reallocation_policy � 鏢  std::_Compressed_pair<std::allocator<nvrhi::d3d12::ShaderTable::Entry>,std::_Vector_val<std::_Simple_types<nvrhi::d3d12::ShaderTable::Entry> >,1> � y  std::unordered_map<enum DXGI_FORMAT,unsigned char,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT>,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > > % V&  std::_One_then_variadic_args_t D 頬  std::_Constexpr_immortalize_impl<std::_System_error_category> W �)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * E?  std::_Vb_val<std::allocator<bool> > �9s  std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> > J 騔  std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> E �<  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j Vx  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �-  std::char_traits<wchar_t>  #>  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> - Mh  std::array<D3D12_DESCRIPTOR_RANGE1,32> 4 H(  std::allocator<nvrhi::rt::PipelineShaderDesc> U 歋  std::unique_ptr<nvrhi::d3d12::Queue,std::default_delete<nvrhi::d3d12::Queue> > � ＃  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *> HTx  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > � O\  std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> g >x  std::allocator_traits<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> > > n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy [ 0�  std::allocator_traits<std::allocator<std::pair<unsigned int const ,unsigned int> > > 甔  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > ' /x  std::allocator<unsigned __int64> C 稵  std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> >  �-  std::false_type 2 臓  std::pair<unsigned int const ,unsigned int> t !x  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > � x  std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> > , V  std::allocator<nvrhi::TextureBarrier>  �)  std::float_round_style � �w  std::_Compressed_pair<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> >,std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > >,1> T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � 鉾  std::_Compressed_pair<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> >,std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> >,1> ! �=  std::hash<std::thread::id> F 苭  std::vector<unsigned __int64,std::allocator<unsigned __int64> > \ 晈  std::vector<unsigned __int64,std::allocator<unsigned __int64> >::_Reallocation_policy  X  std::string B �-  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> � Pe  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> > > T �8  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , d  std::array<nvrhi::BindingSetItem,128> ! ZD  std::atomic<unsigned long> � P(  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> � Zw  std::_Compressed_pair<std::allocator<D3D12_RESOURCE_BARRIER>,std::_Vector_val<std::_Simple_types<D3D12_RESOURCE_BARRIER> >,1> s 舯  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> D ╜  std::_Uninitialized_backout_al<std::allocator<unsigned int> > � 僘  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > >  4>  std::adopt_lock_t � Cw  std::allocator_traits<std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > � {(  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , *  std::numeric_limits<unsigned __int64> �4w  std::unordered_map<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::hash<void *>,std::equal_to<void *>,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > � lv  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � m  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > >  �4  std::_Locinfo 6 ;  std::_Ptr_base<std::filesystem::_Dir_enum_impl> z ]v  std::_Compressed_pair<std::hash<enum DXGI_FORMAT>,std::_Compressed_pair<std::equal_to<enum DXGI_FORMAT>,float,1>,1> \ �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s 芵  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > �WY  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > >,1> [ 觤  std::_Uhash_choose_transparency<void *,std::hash<void *>,std::equal_to<void *>,void> � 猋  std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> > � Hv  std::_Compressed_pair<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> >,std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > >,1> � <V  std::_Compressed_pair<std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> >,std::_Vector_val<std::_Simple_types<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > >,1> $ *  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> /赬  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0>  �  std::string_view    std::wstring_view � 腨  std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > % �-  std::integral_constant<bool,1> L   std::_Vector_val<std::_Simple_types<D3D12_RAYTRACING_INSTANCE_DESC> >   _  std::_Leave_proxy_unbound Z 捅  std::vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> > p 黏  std::vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> >::_Reallocation_policy  >  std::_Mutex_base � Fe  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> > >  �7  std::money_base  @v  std::money_base::pattern � 楾  std::_Compressed_pair<std::allocator<D3D12_RAYTRACING_INSTANCE_DESC>,std::_Vector_val<std::_Simple_types<D3D12_RAYTRACING_INSTANCE_DESC> >,1> ` �  std::_Compressed_pair<std::allocator<char>,std::_Vector_val<std::_Simple_types<char> >,1>  �4  std::_Timevec N 骃  std::_Vector_val<std::_Simple_types<nvrhi::d3d12::ShaderTable::Entry> >  >v  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> > > d pe  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > >  <v  std::defer_lock_t H 縃  std::pair<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,unsigned int> l ;v  std::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > � 	v  std::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >::_Reallocation_policy   �2  std::_Init_once_completer � 艭  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > > � 昒  std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> > K 胾  std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *> L 痷  std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> > b }u  std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> >::_Reallocation_policy j f<  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � 5<  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy L 8u  std::vector<ID3D12CommandList *,std::allocator<ID3D12CommandList *> > b u  std::vector<ID3D12CommandList *,std::allocator<ID3D12CommandList *> >::_Reallocation_policy  2>  std::scoped_lock<> + 6  std::codecvt<wchar_t,char,_Mbstatet> h �%  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> ^ 鮝  std::_Compressed_pair<std::default_delete<nvrhi::TextureState>,nvrhi::TextureState *,1> Q 膖  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 0 [\  std::hash<nvrhi::TextureStateExtension *> � V  std::_Compressed_pair<std::allocator<nvrhi::TextureBarrier>,std::_Vector_val<std::_Simple_types<nvrhi::TextureBarrier> >,1>  JX  std::_Pocma_values 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> D 聇  std::_Vector_val<std::_Simple_types<D3D12_RESOURCE_BARRIER> > !  3  std::hash<std::error_code> Z 竧  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > N 9  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > � q  std::_Simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > @ �*  std::_Default_allocator_traits<std::allocator<char32_t> >  �%  std::allocator<char32_t> ? =  std::unique_ptr<char [0],std::default_delete<char [0]> > ) qX  std::_Atomic_padded<unsigned long> $ �  std::_Atomic_integral<long,4> #e  std::list<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > P  q  std::_Default_allocator_traits<std::allocator<nvrhi::BindingLayoutItem> > I d�  std::allocator_traits<std::allocator<nvrhi::VertexAttributeDesc> > R b�  std::_Default_allocator_traits<std::allocator<nvrhi::VertexAttributeDesc> >  �'  std::hash<bool>     std::streamsize 6 �&  std::_String_val<std::_Simple_types<char32_t> > = �&  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` v&  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ ﹖  std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > � 漷  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > � 唗  std::unordered_map<nvrhi::IBuffer *,unsigned __int64,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *>,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > i 穝  std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> > " �>  std::lock_guard<std::mutex> p 泂  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> > " 詡  std::equal_to<unsigned int> J 範  std::_List_node<std::pair<unsigned int const ,unsigned int>,void *>  (  std::hash<long double> � �9  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � _9  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy R Τ  std::_Uninitialized_backout_al<std::allocator<nvrhi::VertexAttributeDesc> > � 錤  std::_Compressed_pair<std::hash<nvrhi::TextureBindingKey>,std::_Compressed_pair<std::equal_to<nvrhi::TextureBindingKey>,float,1>,1> � E_  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > *>  噑  std::try_to_lock_t r HV  std::_Vector_val<std::_Simple_types<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > > U �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > � 俍  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > >,std::_Iterator_base0> # *  std::numeric_limits<wchar_t>  �  std::_Container_base0 - D  std::_Atomic_integral<unsigned long,4> < 艹  std::_Uninitialized_backout_al<std::allocator<char> >    std::hash<double> O �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �-  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> �  std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> > � 籱  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > F 唖  std::_Vector_val<std::_Simple_types<nvrhi::BindingLayoutItem> > r霡  std::unordered_map<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *>,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > m僛  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > >,1> �|s  std::unordered_map<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *>,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > " [>  std::_Align_type<double,72> g 碯  std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> > � 逨  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � PF  std::unordered_map<nvrhi::TextureBindingKey,unsigned int,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey>,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > + }k  std::array<D3D12_ROOT_PARAMETER1,32> < 騎  std::_Vector_val<std::_Simple_types<unsigned short> > � 腣  std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> > / �*  std::_Char_traits<char32_t,unsigned int>  J3  std::_System_error � 塵  std::_Default_allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> > > . 嶿  std::equal_to<nvrhi::TextureBindingKey> ( 1$  std::hash<nvrhi::FramebufferInfo> 9 )W  std::allocator<std::filesystem::_Find_file_handle>  3  std::error_condition % �-  std::integral_constant<bool,0>  .  std::bad_exception & �%  std::_Zero_then_variadic_args_t � 轄  std::_Compressed_pair<std::allocator<D3D12_INPUT_ELEMENT_DESC>,std::_Vector_val<std::_Simple_types<D3D12_INPUT_ELEMENT_DESC> >,1>  �  std::u32string � 蝀  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > � 瞨  std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >  �  std::_Fake_allocator _ 璻  std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> > i 巖  std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Is_bidi o 峳  std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Pop_direction / f  std::array<nvrhi::BindingLayoutItem,128>  2  std::invalid_argument / 3r  std::equal_to<nvrhi::rt::IShaderTable *> N �-  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U �)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > ] {e  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >  O>  std::cv_status S �-  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > K  [  std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> � 峑  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > R �%  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > /   std::hash<nvrhi::BufferStateExtension *> + S=  std::pair<enum __std_win_error,bool> � -r  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > > � r  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > >  �=  std::thread  �=  std::thread::id S =8  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  2  std::length_error � <e  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> > > � r  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > > � jT  std::_Uhash_choose_transparency<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *>,void> * ?  std::hash<nvrhi::TextureBindingKey> F y`  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 1W  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! $*  std::numeric_limits<float> ^aZ  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > >,1>  �7  std::time_base   �7  std::time_base::dateorder �   std::_Compressed_pair<std::less<nvrhi::AftermathMarkerTracker *>,std::_Compressed_pair<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> >,std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> >,1>,1> j翧  std::unordered_map<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *>,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > ) �  std::_Atomic_integral_facade<long> i 餼  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >  m>  std::mutex � 鮔  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> > % �'  std::hash<enum nvrhi::BlendOp> b 躴  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > � mZ  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > i   std::_Hash_find_last_result<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> *> � 0[  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > >  S4  std::_Ref_count_base " �'  std::hash<unsigned __int64> Q 陙  std::_Vector_val<std::_Simple_types<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >  蝢  std::ratio<60,1> 7 逽  std::allocator<nvrhi::d3d12::ShaderTable::Entry>  t  std::exception_ptr B 蘱  std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> >  緌  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > } 辎  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > � 紂  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> > > : 鞻  std::_Vector_val<std::_Simple_types<unsigned int> > ) �'  std::hash<enum nvrhi::BlendFactor> � a_  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > *> $ 
*  std::numeric_limits<char32_t>  �2  std::once_flag  �2  std::error_code �$[  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > >,1>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 簈  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> > � (�  std::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >  !7  std::_Iosb<int>   7  std::_Iosb<int>::_Seekdir ! 7  std::_Iosb<int>::_Openmode   7  std::_Iosb<int>::_Iostate ! 7  std::_Iosb<int>::_Fmtflags # 7  std::_Iosb<int>::_Dummy_enum c �S  std::array<std::unique_ptr<nvrhi::d3d12::Queue,std::default_delete<nvrhi::d3d12::Queue> >,3> 7 �-  std::allocator_traits<std::allocator<char32_t> > � 竡  std::set<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *> >  n  std::nano  �  std::_Iterator_base0 � P[  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > �\[  std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> M 1(  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > � q  std::initializer_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > � 慬  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > 1 �*  std::_Char_traits<char16_t,unsigned short> � q  std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > G q  std::allocator_traits<std::allocator<nvrhi::BindingLayoutItem> > $ �#  std::hash<nvrhi::BufferRange> T 	9  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > > H V�  std::_Vector_val<std::_Simple_types<nvrhi::VertexAttributeDesc> > � 魀  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> > >   5  std::_Locbase<int> T 騪  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > > r 祦  std::_Compressed_pair<std::hash<unsigned int>,std::_Compressed_pair<std::equal_to<unsigned int>,float,1>,1> � 撷  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > >,std::_Iterator_base0> ! �-  std::char_traits<char16_t> � 鎅  std::_Compressed_pair<std::hash<nvrhi::IBindingLayout *>,std::_Compressed_pair<std::equal_to<nvrhi::IBindingLayout *>,float,1>,1> � 俷  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > 2 ,D  std::_Atomic_integral_facade<unsigned long>  |  std::tuple<> � 餻  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> > > U 鎍  std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >    std::_Container_base12 W 'Y  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  3  std::io_errc  X7  std::ios_base  i7  std::ios_base::_Fnarray  c7  std::ios_base::_Iosarray  7  std::ios_base::Init  7  std::ios_base::failure  #7  std::ios_base::event E 蝅  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 5�  std::integer_sequence<unsigned __int64,0> ) 	*  std::numeric_limits<unsigned char> % 轙  std::allocator<unsigned short> � �%  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �-  std::true_type   *  std::numeric_limits<long> " �-  std::initializer_list<char>  �-  std::_Invoker_strategy  �<  std::nothrow_t y 靝  std::initializer_list<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > � 醦  std::unordered_map<unsigned __int64,nvrhi::d3d12::RootSignature *,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > � L�  std::_Compressed_pair<std::allocator<nvrhi::VertexAttributeDesc>,std::_Vector_val<std::_Simple_types<nvrhi::VertexAttributeDesc> >,1> T  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ +*  std::_Default_allocate_traits H   std::_Vector_val<std::_Simple_types<enum nvrhi::ResourceStates> > y 衁  std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> N �8  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > R p  std::vector<D3D12_RESOURCE_BARRIER,std::allocator<D3D12_RESOURCE_BARRIER> > h 鈕  std::vector<D3D12_RESOURCE_BARRIER,std::allocator<D3D12_RESOURCE_BARRIER> >::_Reallocation_policy 3 �-  std::allocator_traits<std::allocator<char> > <O  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> > ! *  std::numeric_limits<short>  u   std::_Vbase . s(  std::allocator<nvrhi::rt::GeometryDesc> # �>  std::unique_lock<std::mutex> ; �  std::basic_string_view<char,std::char_traits<char> > ! �6  std::ctype<unsigned short> C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > � <B  std::vector<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > � 
B  std::vector<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > >::_Reallocation_policy � 霟  std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> > � e�  std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Range_eraser � �  std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Clear_guard V 爋  std::initializer_list<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > ] z�  std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > y 昽  std::list<std::shared_ptr<nvrhi::d3d12::BufferChunk>,std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > � 榅  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> F立  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >,1> [ 
o  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > > 6 �&  std::_String_val<std::_Simple_types<char16_t> > = �&  std::_String_val<std::_Simple_types<char16_t> >::_Bxty C 錳  std::array<std::pair<unsigned int,D3D12_ROOT_DESCRIPTOR1>,6> , D  std::_Atomic_storage<unsigned long,4> | ~�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > > / -U  std::pair<unsigned int,nvrhi::IBuffer *> O ^  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P �8  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . �'  std::hash<enum nvrhi::TextureDimension> ! `4  std::_Shared_ptr_spin_lock S o  std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > 0 遧  std::_Ptr_base<nvrhi::d3d12::BufferChunk> r eT  std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >  D  std::bad_alloc  {2  std::underflow_error B �'  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> . b  std::default_delete<nvrhi::BufferState> � mO  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > > � 牏  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > > J W  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> � o  std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > � 鏽  std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Is_bidi � 錸  std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Pop_direction � WZ  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > D �V  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  �7  std::messages_base m 獄  std::_Uhash_choose_transparency<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,void> P 灓  std::_Default_allocator_traits<std::allocator<D3D12_INPUT_ELEMENT_DESC> >  62  std::out_of_range # *  std::numeric_limits<__int64> A   std::_Compressed_pair<std::equal_to<unsigned int>,float,1> � 刵  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > i 俉  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  z6  std::ctype<char> � n  std::_Compressed_pair<std::allocator<nvrhi::BindingLayoutItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingLayoutItem> >,1>  �  std::memory_order ! �>  std::recursive_timed_mutex > 鷃  std::pair<nvrhi::TextureBindingKey const ,unsigned int> y �  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >  wn  std::ratio<3600,1> R 蝝  std::_Default_allocator_traits<std::allocator<enum nvrhi::ResourceStates> > # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState> � 搐  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >  �  std::atomic_flag f 7&  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> 3   std::equal_to<nvrhi::BufferStateExtension *> L 僝  std::_Compressed_pair<std::equal_to<nvrhi::IBindingLayout *>,float,1> / un  std::allocator<nvrhi::BindingLayoutItem> f 丳  std::vector<nvrhi::d3d12::ShaderTable::Entry,std::allocator<nvrhi::d3d12::ShaderTable::Entry> > | PP  std::vector<nvrhi::d3d12::ShaderTable::Entry,std::allocator<nvrhi::d3d12::ShaderTable::Entry> >::_Reallocation_policy  k3  std::system_error = fn  std::allocator_traits<std::allocator<unsigned short> > < �*  std::_Default_allocator_traits<std::allocator<char> > { v�  std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > > W �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > [ +�  std::_Uninitialized_backout_al<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > 8 @R  std::_Ptr_base<nvrhi::d3d12::InternalCommandList> K 匠  std::_Uninitialized_backout_al<std::allocator<_NV_CUSTOM_SEMANTIC> >  Xn  std::ratio<1,1> Z 豣  std::vector<enum nvrhi::ResourceStates,std::allocator<enum nvrhi::ResourceStates> > p   std::vector<enum nvrhi::ResourceStates,std::allocator<enum nvrhi::ResourceStates> >::_Reallocation_policy   �-  std::forward_iterator_tag  M2  std::runtime_error � 鉩  std::list<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >   Z  std::bad_array_new_length � 塐  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > 2 蝷  std::_Vector_val<std::_Simple_types<char> > E �(  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 7c  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > / 昉  std::_Atomic_storage<unsigned __int64,8> \ b  std::_Compressed_pair<std::default_delete<nvrhi::BufferState>,nvrhi::BufferState *,1> 1 鏱  std::allocator<enum nvrhi::ResourceStates> g Vn  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > V 驯  std::vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> > l P�  std::vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> >::_Reallocation_policy � 俈  std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > p 酼  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>  �4  std::_Yarn<char>    std::_Container_proxy ( Gn  std::_Facetptr<std::ctype<char> > Z (e  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > M 梋  std::_Compressed_pair<std::equal_to<nvrhi::TextureBindingKey>,float,1>  Dn  std::allocator<bool> H廦  std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> � 垽  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >  �  std::u16string  �  std::nested_exception  r  std::_Distance_unknown ( *  std::numeric_limits<unsigned int> < \`  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> B 資  std::_Conditionally_enabled_hash<nvrhi::IBindingLayout *,1> , �5  std::codecvt<char32_t,char,_Mbstatet> V n  std::_Default_allocator_traits<std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> > 8 I  std::array<nvrhi::RefCountPtr<nvrhi::ITexture>,9> � 睼  std::_Uhash_choose_transparency<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *>,void> � 肵  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > >,std::_Iterator_base0> @ (  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff / 維  std::default_delete<nvrhi::d3d12::Queue> � 3n  std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >    std::atomic<long> � 漑  std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> & �-  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �-  std::initializer_list<char16_t> % �-  std::initializer_list<wchar_t> C �'  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' (*  std::numeric_limits<long double>  3  std::errc � �  std::_Compressed_pair<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>,std::_Vector_val<std::_Simple_types<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >,1> H   std::_Conditionally_enabled_hash<nvrhi::BufferStateExtension *,1> � 嘨  std::_Uhash_choose_transparency<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *>,void> R 玗  std::_Compressed_pair<std::equal_to<nvrhi::BufferStateExtension *>,float,1> , 踎  std::default_delete<std::_Facet_base>  �2  std::range_error + 訳  std::allocator<nvrhi::BufferBarrier> �Y  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > >,1>  <0  std::bad_typeid > �'  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �>  std::_UInt_is_zero N 峧  std::array<nvrhi::d3d12::CommandList::VolatileConstantBufferBinding,32> s oV  std::_Vector_val<std::_Simple_types<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > 驧  std::unordered_map<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *>,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >  n  std::ratio<1,1000000000>  &  std::allocator<char16_t> $ 鳹  std::default_delete<char [0]> � 豟  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> . �?  std::vector<bool,std::allocator<bool> > O ~U  std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J �8  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  n  std::ratio<1,1000> < U  std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >  n  std::ratio<1,10000000> M \l  std::_Default_allocator_traits<std::allocator<nvrhi::TextureBarrier> > )禮  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >,1> ; �%  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  "5  std::_Crt_new_delete R �  std::allocator_traits<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > � MY  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > % �3  std::_Iostream_error_category2 S 續  std::_Simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > � 
a  std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > � 躑  std::_Compressed_pair<std::allocator<nvrhi::BufferBarrier>,std::_Vector_val<std::_Simple_types<nvrhi::BufferBarrier> >,1> * �-  std::_String_constructor_concat_tag  m&  std::allocator<char> ) 	?  std::hash<nvrhi::BufferBindingKey> O n  std::allocator_traits<std::allocator<nvrhi::d3d12::ShaderTable::Entry> > G 轢  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>  
n  std::hash<void *>    std::nullptr_t & �-  std::random_access_iterator_tag ; �'  std::_Conditionally_enabled_hash<unsigned __int64,1> Z 薚  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > > ^ 臩  std::_Compressed_pair<std::default_delete<nvrhi::d3d12::Queue>,nvrhi::d3d12::Queue *,1> R nW  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > V 豘  std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *>  =4  std::bad_weak_ptr � 发  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > ) *  std::numeric_limits<unsigned long>   �&  std::_Atomic_padded<long> %m  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > > @ �;  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> � 廷  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > > N   std::vector<nvrhi::BufferBarrier,std::allocator<nvrhi::BufferBarrier> > d qC  std::vector<nvrhi::BufferBarrier,std::allocator<nvrhi::BufferBarrier> >::_Reallocation_policy # 賄  std::allocator<unsigned int> ~ |T  std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >  �4  std::_Yarn<wchar_t> = �'  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> � e\  std::_Compressed_pair<std::hash<nvrhi::TextureStateExtension *>,std::_Compressed_pair<std::equal_to<nvrhi::TextureStateExtension *>,float,1>,1> M n  std::allocator_traits<std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> > 5 鰉  std::pair<unsigned int,D3D12_ROOT_DESCRIPTOR1>    std::wstring K 靘  std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> > 5 疨  std::_Atomic_integral_facade<unsigned __int64> ' *  std::numeric_limits<signed char> � �9  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �1  std::domain_error P �  std::_Uninitialized_backout_al<std::allocator<D3D12_INPUT_ELEMENT_DESC> >  �  std::u32string_view  �  std::_Container_base I 衜  std::allocator_traits<std::allocator<enum nvrhi::ResourceStates> > X 耺  std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >  .&  std::allocator<wchar_t> L z-  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > y 灞  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > � 絤  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > i 蝂  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > � 釩  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > > � Ze  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> > > $ �'  std::hash<nvrhi::IResource *> b 甿  std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> > �yA  std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> > s 挨  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > > � 鱗  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > >,std::_Iterator_base0> �  a  std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > � 1Y  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   *  std::numeric_limits<char> D 卐  std::_Default_allocator_traits<std::allocator<unsigned int> > - z]  std::equal_to<nvrhi::IBindingLayout *> 9 m1  std::chrono::duration<__int64,std::ratio<1,1000> >  �0  std::chrono::nanoseconds y '4  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �0  std::chrono::duration<__int64,std::ratio<1,1000000000> > , e  std::chrono::duration_values<__int64>  �0  std::chrono::seconds 3 +1  std::chrono::duration<int,std::ratio<60,1> > 6 �0  std::chrono::duration<__int64,std::ratio<1,1> > s �0  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   渕  std::chrono::steady_clock   檓  std::chrono::system_clock 6 @1  std::chrono::duration<double,std::ratio<60,1> > ; �1  std::chrono::duration<double,std::ratio<1,1000000> > > �1  std::chrono::duration<double,std::ratio<1,1000000000> > = �0  std::chrono::duration<__int64,std::ratio<1,10000000> > q �0  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5  1  std::chrono::duration<int,std::ratio<3600,1> > 8 �1  std::chrono::duration<double,std::ratio<1,1000> > < �1  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 W1  std::chrono::duration<double,std::ratio<1,1> > 8 1  std::chrono::duration<double,std::ratio<3600,1> >  O6  std::ctype_base  N:  std::filesystem::perms ' �:  std::filesystem::directory_entry $ S:  std::filesystem::copy_options ( >:  std::filesystem::filesystem_error 7 峖  std::filesystem::_Path_iterator<wchar_t const *> ) �7  std::filesystem::_Find_file_handle & �7  std::filesystem::_Is_slash_oper . �;  std::filesystem::_Should_recurse_result $ �=  std::filesystem::perm_options 4 �<  std::filesystem::recursive_directory_iterator . �:  std::filesystem::_File_status_and_error & 5;  std::filesystem::_Dir_enum_impl 0 G;  std::filesystem::_Dir_enum_impl::_Creator @ M;  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! ]:  std::filesystem::file_type . k;  std::filesystem::_Directory_entry_proxy " q=  std::filesystem::space_info * �;  std::filesystem::directory_iterator & '4  std::filesystem::file_time_type 0 �;  std::filesystem::_Recursive_dir_enum_impl ) �:  std::filesystem::directory_options # p:  std::filesystem::file_status u �9  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 搈  std::filesystem::_File_time_clock  �8  std::filesystem::path $ 8  std::filesystem::path::format * a^  std::filesystem::_Normal_conversion � 礏  std::vector<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > > � 傿  std::vector<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > >::_Reallocation_policy < 宍  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �5  std::codecvt<char16_t,char,_Mbstatet>  x-  std::char_traits<char> � vW  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �2  std::error_category ) �2  std::error_category::_Addr_storage ! �3  std::_System_error_message  k  std::_Unused_parameter h &  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> S 縘  std::_Compressed_pair<std::equal_to<nvrhi::TextureStateExtension *>,float,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > J |�  std::initializer_list<std::pair<unsigned int const ,unsigned int> > 7 ;  std::shared_ptr<std::filesystem::_Dir_enum_impl> � C\  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > �諿  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > >,1> C   std::allocator<std::pair<unsigned int const ,unsigned int> > { 恗  std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > � T  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > * 0}  std::allocator<_NV_CUSTOM_SEMANTIC> G 牑  std::allocator_traits<std::allocator<D3D12_INPUT_ELEMENT_DESC> > = �'  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> � 僓  std::_Uhash_choose_transparency<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey>,void> z 媘  std::allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> > >  �5  std::_Codecvt_mode / 諣  std::allocator<D3D12_INPUT_ELEMENT_DESC> @ �*  std::_Default_allocator_traits<std::allocator<char16_t> > I X\  std::_Conditionally_enabled_hash<nvrhi::TextureStateExtension *,1>  葕  std::_Exact_args_t U狹  std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> > B {~  std::allocator_traits<std::allocator<_NV_CUSTOM_SEMANTIC> > � :  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > 9 zm  std::shared_ptr<nvrhi::d3d12::CommandListInstance> 0 d*  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> ' Lm  std::array<unsigned __int64,128> � )m  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> > > �\  std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> \ C)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 I&  std::_String_val<std::_Simple_types<wchar_t> > < �&  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  q4  std::_Facet_base ' �#  std::hash<nvrhi::BindingSetItem> [ 4V  std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > " r*  std::_WChar_traits<wchar_t> 2 96  std::codecvt<unsigned short,char,_Mbstatet> z n]  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> o 麶  std::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > > � 蔎  std::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >::_Reallocation_policy � 'm  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > > # �3  std::_Generic_error_category  -*  std::streampos  ~-  std::input_iterator_tag � 砙  std::_Compressed_pair<std::hash<nvrhi::BufferStateExtension *>,std::_Compressed_pair<std::equal_to<nvrhi::BufferStateExtension *>,float,1>,1> 2 q`  std::_Wrap<std::filesystem::_Dir_enum_impl> ! eI  std::array<unsigned int,8> X 鉥  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> B pK  std::vector<unsigned short,std::allocator<unsigned short> > X ?K  std::vector<unsigned short,std::allocator<unsigned short> >::_Reallocation_policy � 檁  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > *> � 璙  std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > � m  std::allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > 0 汸  std::_Atomic_integral<unsigned __int64,8> , 蜸  std::_Atomic_padded<unsigned __int64> ' (  std::hash<enum nvrhi::ColorMask> 4 禲  std::equal_to<nvrhi::TextureStateExtension *> A 畗  std::_Vector_val<std::_Simple_types<_NV_CUSTOM_SEMANTIC> >  �5  std::codecvt_base S 萢  std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > / 韆  std::default_delete<nvrhi::TextureState>  l0  std::bad_function_call O �)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > ; 	m  std::allocator_traits<std::allocator<unsigned int> > b m  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > ' �=  std::hash<std::filesystem::path> 1 鴏  std::shared_ptr<nvrhi::d3d12::BufferChunk> V RH  std::array<std::pair<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,unsigned int>,5> P ,C  std::vector<nvrhi::TextureBarrier,std::allocator<nvrhi::TextureBarrier> > f 鶥  std::vector<nvrhi::TextureBarrier,std::allocator<nvrhi::TextureBarrier> >::_Reallocation_policy  �'  std::hash<unsigned int> 7 v-  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F 闋  std::_Vector_val<std::_Simple_types<D3D12_INPUT_ELEMENT_DESC> > � Y  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> � Gd  std::list<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > V 蔿  std::vector<nvrhi::BindingLayoutItem,std::allocator<nvrhi::BindingLayoutItem> > l 榣  std::vector<nvrhi::BindingLayoutItem,std::allocator<nvrhi::BindingLayoutItem> >::_Reallocation_policy F t-  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > ' ;�  std::tuple<unsigned int const &> D ^l  std::allocator_traits<std::allocator<nvrhi::TextureBarrier> > � 蘙  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > . �  std::array<nvrhi::BindingLayoutItem,16> � 
U  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IResource> > >,1> $ �'  std::hash<enum nvrhi::Format>  *  std::numeric_limits<int> � Pl  std::list<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > 2 �&  std::_String_val<std::_Simple_types<char> > 9 �&  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access } ~L  std::vector<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > > � LL  std::vector<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > >::_Reallocation_policy 
 !   wint_t  r-  CACLIPDATA & >S  nvrhi::AftermathCrashDumpHelper # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet ; 覱  nvrhi::RefCountPtr<nvrhi::d3d12::RayTracingPipeline>  ;  nvrhi::BindingSetDesc  p-  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType 1 莐  nvrhi::RefCountPtr<ID3D12CommandAllocator> ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   l-  nvrhi::MemoryRequirements 5 瀔  nvrhi::static_vector<D3D12_ROOT_PARAMETER1,32> 4 鶳  nvrhi::RefCounter<nvrhi::d3d12::ICommandList>  #   nvrhi::GpuVirtualAddress % Uk  nvrhi::RefCountPtr<ID3D12Heap> 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> 6 H  nvrhi::RefCounter<nvrhi::d3d12::IRootSignature> " -  nvrhi::VulkanBindingOffsets  :  nvrhi::ResourceStates . M  nvrhi::RefCounter<nvrhi::rt::IPipeline> 0 滵  nvrhi::RefCountPtr<nvrhi::IShaderLibrary> ) 盙  nvrhi::RefCounter<nvrhi::ISampler> , NU  nvrhi::RefCounter<nvrhi::IEventQuery> / 繁  nvrhi::RefCounter<nvrhi::IShaderLibrary>  j   nvrhi::GraphicsState 1 鬕  nvrhi::RefCounter<nvrhi::rt::IAccelStruct> / �  nvrhi::static_vector<nvrhi::Viewport,16> ` }H  nvrhi::static_vector<std::pair<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,unsigned int>,5>    nvrhi::ShaderDesc - 潜  nvrhi::RefCounter<nvrhi::IInputLayout>  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc 7 猘  nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>    nvrhi::Rect ( +k  nvrhi::RefCountPtr<ID3D12Device8> 1 �j  nvrhi::RefCountPtr<ID3D12CommandSignature>  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline 4 橰  nvrhi::RefCountPtr<ID3D12GraphicsCommandList> ! 滵  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx ' 謏  nvrhi::RefCountPtr<ID3D12Device> ( +S  nvrhi::ShaderBinaryLookupCallback B >I  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::ITexture>,9>  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler % �  nvrhi::FastGeometryShaderFlags ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc G 侸  nvrhi::static_vector<std::pair<unsigned int,nvrhi::IBuffer *>,6>  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice X 甹  nvrhi::static_vector<nvrhi::d3d12::CommandList::VolatileConstantBufferBinding,32> * cj  nvrhi::RefCountPtr<ID3D12QueryHeap> # 4#  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  漇  nvrhi::DxgiFormatMapping  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray ) +E  nvrhi::RefCountPtr<ID3D12Resource>  b  nvrhi::BufferState 6 #J  nvrhi::RefCountPtr<nvrhi::d3d12::BindingLayout> . �?  nvrhi::RefCountPtr<nvrhi::ICommandList>   f-  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  b-  nvrhi::IStagingTexture 5 9j  nvrhi::RefCountPtr<ID3D12GraphicsCommandList6> ) 2S  nvrhi::ShaderHashGeneratorFunction ! �?  nvrhi::utils::ScopedMarker $ ?  nvrhi::utils::BitSetAllocator M 
j  nvrhi::static_vector<std::pair<unsigned int,D3D12_ROOT_DESCRIPTOR1>,6> . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  V  nvrhi::TextureBarrier / 瞚  nvrhi::RefCountPtr<ID3D12DescriptorHeap>  B  nvrhi::TextureDimension , 釯  nvrhi::RefCounter<nvrhi::IBindingSet> 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments 6 N  nvrhi::RefCountPtr<nvrhi::d3d12::RootSignature> / 轌  nvrhi::RefCounter<nvrhi::IBindingLayout>  #  nvrhi::SamplerHandle 1 矷  nvrhi::RefCounter<nvrhi::IComputePipeline> * ;!  nvrhi::DrawIndexedIndirectArguments - 菻  nvrhi::RefCounter<nvrhi::IFramebuffer> # B#  nvrhi::DescriptorTableHandle $ 奿  nvrhi::AftermathMarkerTracker  �  nvrhi::ShaderType - si  nvrhi::RefCountPtr<ID3D12CommandQueue>  "#  nvrhi::TimerQueryHandle + =Z  nvrhi::RefCountPtr<nvrhi::IResource> , 荊  nvrhi::RefCounter<nvrhi::ITimerQuery> & 禗  nvrhi::RefCounter<nvrhi::IHeap>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128> 0 BG  nvrhi::RefCounter<nvrhi::IStagingTexture> ( Ji  nvrhi::RefCountPtr<ID3D12Device2>  F�  nvrhi::FormatInfo  VE  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  /S  nvrhi::ResolvedMarker  X-  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode - @  nvrhi::CommandListResourceStateTracker  u   nvrhi::ArraySlice  8  nvrhi::IResource  �   nvrhi::IBindingSet 1 扥  nvrhi::RefCounter<nvrhi::rt::IShaderTable>  T-  nvrhi::TileShape # �?  nvrhi::TextureStateExtension ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> 1 ~K  nvrhi::RefCounter<nvrhi::IDescriptorTable> - P  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture / 鍷  nvrhi::RefCountPtr<nvrhi::d3d12::Buffer>  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem . 琀  nvrhi::RefCountPtr<ID3D12RootSignature>  -#  nvrhi::FramebufferHandle  鞺  nvrhi::BufferBarrier 1 2  nvrhi::static_vector<enum nvrhi::Format,8> * u   nvrhi::d3d12::OptionalResourceState  pE  nvrhi::d3d12::Context " H  nvrhi::d3d12::RootSignature ' M  nvrhi::d3d12::RayTracingPipeline 9 i  nvrhi::d3d12::RayTracingPipeline::ExportTableEntry  鮅  nvrhi::d3d12::BindingSet  螱  nvrhi::d3d12::TimerQuery + lG  nvrhi::d3d12::SamplerFeedbackTexture  籊  nvrhi::d3d12::Sampler   
�  nvrhi::d3d12::InputLayout ) i  nvrhi::d3d12::StaticDescriptorHeap   L  nvrhi::d3d12::AccelStruct  镈  nvrhi::d3d12::Texture  鸆  nvrhi::d3d12::Shader   跦  nvrhi::d3d12::Framebuffer  砇  nvrhi::d3d12::IDevice % 	i  nvrhi::d3d12::ShaderTableState  LS  nvrhi::d3d12::Queue %   nvrhi::d3d12::GraphicsPipeline $ 糏  nvrhi::d3d12::ComputePipeline # H  nvrhi::d3d12::IRootSignature  﨔  nvrhi::d3d12::Buffer ' mD  nvrhi::d3d12::ShaderLibraryEntry ( oR  nvrhi::d3d12::InternalCommandList $ 奒  nvrhi::d3d12::DescriptorTable $ 丒  nvrhi::d3d12::DeviceResources ( 猘  nvrhi::d3d12::RootSignatureHandle  i  nvrhi::d3d12::DeviceDesc ' u   nvrhi::d3d12::RootParameterIndex $  i  nvrhi::d3d12::IDescriptorHeap " 闓  nvrhi::d3d12::BindingLayout # TG  nvrhi::d3d12::StagingTexture 0 録  nvrhi::d3d12::StagingTexture::SliceRegion  綝  nvrhi::d3d12::Heap " 帽  nvrhi::d3d12::ShaderLibrary $ 諭  nvrhi::d3d12::MeshletPipeline " 醜  nvrhi::d3d12::UploadManager ' 薶  nvrhi::d3d12::DX12_ViewportState  S  nvrhi::d3d12::Device $ u   nvrhi::d3d12::DescriptorIndex ! 騊  nvrhi::d3d12::ICommandList # 鵊  nvrhi::d3d12::BindlessLayout $ 篕  nvrhi::d3d12::OpacityMicromap !   nvrhi::d3d12::TextureState     nvrhi::d3d12::ShaderTable ' 镺  nvrhi::d3d12::ShaderTable::Entry   [Q  nvrhi::d3d12::CommandList ? 舎  nvrhi::d3d12::CommandList::VolatileConstantBufferBinding  #  nvrhi::BufferHandle 6 {N  nvrhi::RefCountPtr<ID3D12StateObjectProperties>  �>  nvrhi::BufferBindingKey ) 蔇  nvrhi::RefCounter<nvrhi::ITexture>  �  nvrhi::IBindingLayout  �  nvrhi::FramebufferInfo  滸  nvrhi::TextureHandle  P-  nvrhi::IEventQuery 1 薎  nvrhi::RefCounter<nvrhi::IMeshletPipeline>  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  J-  nvrhi::IMessageCallback ( 顲  nvrhi::RefCounter<nvrhi::IShader> , MN  nvrhi::RefCountPtr<ID3D12StateObject>  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc " �?  nvrhi::BufferStateExtension H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 4-  nvrhi::TiledTextureCoordinate  0-  nvrhi::IHeap  1S  nvrhi::BinaryBlob # g  nvrhi::FramebufferAttachment + 侷  nvrhi::static_vector<unsigned int,8>  �   nvrhi::BindingSetVector 5 琄  nvrhi::RefCounter<nvrhi::rt::IOpacityMicromap>  P  nvrhi::BindingSetHandle ( )-  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector . 胔  nvrhi::RefCountPtr<ID3D12PipelineState> ' VE  nvrhi::RefCountPtr<nvrhi::IHeap> " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �,  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 -  nvrhi::rt::cluster::OperationBlasBuildParams . -  nvrhi::rt::cluster::OperationMoveParams ( -  nvrhi::rt::cluster::OperationDesc 3 -  nvrhi::rt::cluster::OperationClasBuildParams , 
-  nvrhi::rt::cluster::OperationSizeInfo * 	-  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # Z  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �,  nvrhi::rt::IPipeline 2 桰  nvrhi::RefCounter<nvrhi::IGraphicsPipeline>  �?  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �,  nvrhi::TextureTilesMapping / 籖  nvrhi::RefCounter<nvrhi::d3d12::IDevice>  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �,  nvrhi::IDescriptorTable & 4G  nvrhi::RefCountPtr<ID3D12Fence>  〥  nvrhi::IShaderLibrary * 滸  nvrhi::RefCountPtr<nvrhi::ITexture>  H!  nvrhi::ComputeState 2 Z  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> 8 ^G  nvrhi::RefCounter<nvrhi::ISamplerFeedbackTexture>  �>  nvrhi::TextureBindingKey  �   nvrhi::IFramebuffer  �  nvrhi::Viewport ( 鐵  nvrhi::RefCounter<nvrhi::IBuffer>  �  nvrhi::RenderState ( 沨  nvrhi::RefCountPtr<ID3D12Device5> 7 oh  nvrhi::static_vector<D3D12_DESCRIPTOR_RANGE1,32>  b  nvrhi::TextureState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �,  nvrhi::ITimerQuery 5 %h  nvrhi::RefCountPtr<ID3D12GraphicsCommandList4>  �,  VARDESC     LONG  �,  ITypeLib  F,  tagCACY  �,  tagBSTRBLOB  �,  tagCAUH  �$  _TP_CALLBACK_ENVIRON_V3 0 �$  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> B �$  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s>  D,  _ULARGE_INTEGER ( �,  _ULARGE_INTEGER::<unnamed-type-u>  �3  __std_win_error  p%  LPVARIANT  �+  SAFEARRAY  �4  lconv  鄁  D3D_SRV_DIMENSION  �,  tagCABOOL   +  __RTTIBaseClassDescriptor   g  D3D12_SHADER_CACHE_MODE  @U  ID3D12RootSignature  <,  tagBLOB 
 �,  CABOOL  駁  NVDX_SwapChainHandle   玡  D3D12_ROOT_PARAMETER_TYPE 
    _off_t  雊  D3D12_BOX  #   ULONG64 
 U  SNB ! 間  D3D12_RESOURCE_UAV_BARRIER  %+  _tagINTERNETFEATURELIST ' 鏶  _NV_GPU_WORKSTATION_FEATURE_TYPE  �,  CABSTRBLOB 
 #   SIZE_T  �,  tagTYPEATTR  �  stat ) 錱  D3D12_GRAPHICS_PIPELINE_STATE_DESC  舋  LUID  t   int32_t  �  timespec  譯  D3D12_RANGE & 塯  $_TypeDescriptor$_extraBytes_37  D=  __std_fs_file_id 
 !   _ino_t  焒  NVDX_ObjectHandle__ 
 L>  _Cnd_t  誫  D3D12_HEAP_PROPERTIES  A   DATE # �,  ReplacesCorHdrNumericDefines  蟝  D3D12_TEX3D_UAV  +%  FS_BPIO_OUTFLAGS 
 #   UINT64  "   DWORD 
 !   UINT16  蚲  D3D12_TEX2DMS_UAV  薵  D3D12_TEX2D_UAV 
 _  LPCSTR  �$  PTP_CALLBACK_INSTANCE 
   PSHORT ' -=  __std_fs_create_directory_result * 柋  NVAPI_D3D12_PSO_HULL_SHADER_DESC_V2  蒰  D3D12_TEX2D_ARRAY_UAV  沢  D3D12_MESSAGE_ID  "   TP_VERSION      UINT8  q  BSTR  沞  D3D_DRIVER_TYPE  !   uint16_t  �3  __std_fs_stats , 尡  NVAPI_D3D12_PSO_VERTEX_SHADER_DESC_V1  脙  NV_PSO_EXTENSION  �+  CAUB  �,  ITypeInfo  n%  tagPROPVARIANT  H,  CAUL M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  TT  ID3D12StateObject   #   D3D12_GPU_VIRTUAL_ADDRESS  F,  CACY  舋  _LUID ' 膅  D3D12_FEATURE_DATA_D3D12_OPTIONS  昰  tagRECT  �  _Mbstatet  D,  ULARGE_INTEGER  �$  TP_CALLBACK_PRIORITY  a  _locale_t  竒  _SECURITY_ATTRIBUTES B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; A,  __vcrt_va_list_is_reference<__crt_locale_pointers *>  2+  VARENUM     intmax_t  �+  tagCASCODE # 秄  D3D_TESSELLATOR_PARTITIONING  癵  NVDX_ObjectHandle  ]  terminate_handler  �+  _s__RTTIBaseClassArray  
+  tagCACLSID  緀  _NV_LICENSE_FEATURE_TYPE  �$  MACHINE_ATTRIBUTES ' 柋  NVAPI_D3D12_PSO_HULL_SHADER_DESC  峞  D3D_RESOURCE_RETURN_TYPE 
 H  ldiv_t  =,  tagCALPWSTR  �3  __std_fs_file_flags  �4  _Cvtvec ) 玤  D3D12_PLACED_SUBRESOURCE_FOOTPRINT & q�  $_TypeDescriptor$_extraBytes_49  <,  BLOB ! g  _NV_COLOR_SELECTION_POLICY  #   DWORD64  u   _Thrd_id_t (   D3D12_FEATURE_DATA_D3D12_OPTIONS7  !   PROPVAR_PAD1 - 0+  $_s__RTTIBaseClassArray$_extraBytes_24  �$  PTP_SIMPLE_CALLBACK  談  NvAPI_LongString  g  D3D12_MESSAGE_CATEGORY 
 t   INT  昰  D3D12_RECT  �+  _CatchableTypeArray  :,  IStorage , 幈  NVAPI_D3D12_PSO_VERTEX_SHADER_DESC_V2  v%  tagVARIANT 
 �+  tagCAI 
 A   DOUBLE      UCHAR " 廹  D3D12_CPU_DESCRIPTOR_HANDLE   媑  _D3D_SHADER_CBUFFER_FLAGS  "   LCID      BOOLEAN  �$  PTP_CALLBACK_ENVIRON  uQ  ID3D12Object  �3  __std_fs_copy_options     ptrdiff_t  �+  tagTYSPEC  N%  LPVERSIONEDSTREAM  �  _stat64i32  塭  D3D12_LOGIC_OP  �+  tagDISPPARAMS 
 !   USHORT  �+  _PMD      uint8_t  z%  LPUWSTR  +  tagVARKIND  �/  type_info    PVOID ' g  D3D12_UNORDERED_ACCESS_VIEW_DESC  �+  SAFEARRAYBOUND  sg  ID3D10Blob ' �+  _s__RTTIClassHierarchyDescriptor  Q+  IUnknown  t   errno_t  q   WCHAR     PBYTE  ag  D3D12_VERTEX_BUFFER_VIEW  窾  ID3D12Resource ) 灡  NVAPI_D3D12_PSO_DOMAIN_SHADER_DESC  璭  D3D_TESSELLATOR_DOMAIN  �3  __std_fs_reparse_tag  %  _DEVICE_DSM_DEFINITION 
 �+  tagCAC  �+  tagCAUB  K  _lldiv_t 
 w$  IID ! 榝  _D3D_SHADER_VARIABLE_FLAGS # _g  D3D12_ROOT_DESCRIPTOR_TABLE1  '+  _tagQUERYOPTION  q  LPOLESTR  廵  D3D_PRIMITIVE  Zg  D3D12_TILE_SHAPE  �+  tagExtentMode  �+  __MIDL_IUri_0002 & g  $_TypeDescriptor$_extraBytes_50     HRESULT  噀  _D3D_SHADER_INPUT_TYPE  罳  D3D12_PRIMITIVE_TOPOLOGY  Tg  ID3D12PipelineState 
 �+  CAI  0  __std_type_info_data  %  PDEVICE_DSM_INPUT  ?g  ID3D12CommandSignature & �+  $_TypeDescriptor$_extraBytes_27  �+  CASCODE  �  _s__ThrowInfo  4  __std_fs_convert_result / 蔲  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER ! �+  __MIDL_IGetBindHandle_0001  +g  ID3D12CommandAllocator  哘  ID3D12DeviceChild  �3  __std_fs_stats_flags + ”  NVAPI_D3D12_PSO_GEOMETRY_SHADER_DESC  �+  tagCY 
    LONG64 " g  D3D12_TEXTURE_COPY_LOCATION  )+  tagCOINITBASE & 鴉  $_TypeDescriptor$_extraBytes_47  %  LPCUWSTR  "   ULONG  �+  __RTTIBaseClassArray ! g  D3D12_STATE_SUBOBJECT_TYPE  !   VARTYPE  t   BOOL 
 �+  CAC  �  __crt_locale_data_public  �+  tagApplicationType 0 #%  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES_OUTPUT  R  LPCWSTR & �+  DISPLAYCONFIG_SCANLINE_ORDERING - �+  $_s__CatchableTypeArray$_extraBytes_24  @   FLOAT  !   PROPVAR_PAD3  +  tagDOMNodeType  �+  CAUI  �+  tagCLIPDATA  鱡  NV_MOSAIC_TOPO  
>  _Mtx_internal_imp_t  �+  tagSAFEARRAY & 齟  $_TypeDescriptor$_extraBytes_25  "   DEVICE_DSM_ACTION % �+  __RTTIClassHierarchyDescriptor  �4  _Collvec   �<  __std_fs_volume_name_kind 5 嚤  NVAPI_D3D12_PSO_SET_SHADER_EXTENSION_SLOT_DESC  歈  ID3D12CommandList  �+  tagVersionedStream 
 �+  CABSTR ( 駀  D3D12_FEATURE_DATA_D3D12_OPTIONS6     __time64_t  +  tagCHANGEKIND 
 u   UINT32 ( 韋  D3D12_RESOURCE_TRANSITION_BARRIER  m  FILE ! 閒  D3D12_DESCRIPTOR_HEAP_DESC  �+  tagSYSKIND  錰  D3D12_ROOT_PARAMETER1 & 趂  $_TypeDescriptor$_extraBytes_26 2 辠  D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_MODE    _NV_DESKTOP_COLOR_DEPTH 
 >  _Mtx_t 3 �+  __vcrt_va_list_is_reference<wchar_t const *>  躥  D3D12_VIEWPORT  �+  IDispatch  w$  CLSID  詣  NV_CUSTOM_SEMANTIC_TYPE  �  mbstate_t  �  _PMFN  #   uintptr_t 
 q  LPWSTR  n%  PROPVARIANT  P%  LPSAFEARRAY  #   UINT_PTR  �$  PTP_POOL  �+  _s__CatchableTypeArray  )=  __std_fs_remove_result  w$  GUID * �$  _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG ' 萬  D3D_TESSELLATOR_OUTPUT_PRIMITIVE # 苀  D3D12_INDIRECT_ARGUMENT_TYPE  圦  D3D12_COMMAND_LIST_TYPE  �$  TP_CALLBACK_ENVIRON_V3  #+  tagFUNCKIND  �+  LARGE_INTEGER 
 �+  CAH  竑  D3D12_INDEX_BUFFER_VIEW  t   INT32  �+  tagCAFILETIME 
   HANDLE  瞗  D3D12_LIFETIME_STATE  �*  PIDMSI_STATUS_VALUE  癴  _D3D_CBUFFER_TYPE  #   ULONGLONG  �+  tagCAPROPVARIANT  u   NvAPI_D3D11_SWIZZLE_MODE ( �$  PTP_CLEANUP_GROUP_CANCEL_CALLBACK # 甪  D3D12_COMMAND_SIGNATURE_DESC ( ゝ  D3D12_FEATURE_DATA_D3D12_OPTIONS1 	 �+  CY  �=  _Thrd_t  �+  FILETIME  %  PDEVICE_DSM_RANGE ( 歠  D3D12_DEBUG_DEVICE_PARAMETER_TYPE  杅  D3D12_SUBRESOURCE_TILING - �+  $_s__RTTIBaseClassArray$_extraBytes_16  +  __MIDL_IUri_0001 & 攆  D3D12_STREAM_OUTPUT_BUFFER_VIEW 
 /%  REGCLS - 磂  $_s__RTTIBaseClassArray$_extraBytes_32 , 毐  NVAPI_D3D12_PSO_DOMAIN_SHADER_DESC_V1 % 艃  NVAPI_D3D12_PSO_EXTENSION_DESC  抐  ID3D12Device  |+  IRecordInfo 
 #   size_t  !%  PDEVICE_DSM_OUTPUT 
    time_t  �3  __std_fs_file_attr     LONGLONG  鮡  ID3D12CommandQueue   蝒  D3D12_MEASUREMENTS_ACTION  蔱  _NV_SCALING  
  __std_exception_data * 檈  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE 
 u   _dev_t  芿  D3D12_RESOURCE_BARRIER , 湵  NVAPI_D3D12_PSO_DOMAIN_SHADER_DESC_V2  �<  __std_ulong_and_error ) %  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES * 敱  NVAPI_D3D12_PSO_HULL_SHADER_DESC_V1  :+  tagGLOBALOPT_EH_VALUES * �$  PCUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG  K  lldiv_t     SHORT    PLONG64  秂  D3D12_DISCARD_REGION  H  _ldiv_t ! 軉  D3D12_INPUT_CLASSIFICATION  3%  COWAIT_FLAGS     SCODE  ++  tagCLSCTX  �  _timespec64     intptr_t     INT_PTR  漞  _D3D_SHADER_INPUT_FLAGS  u   uint32_t    D3D12_SAMPLER_DESC  +  tagXMLEMEM_TYPE " 焑  D3D_REGISTER_COMPONENT_TYPE 
 m  _iobuf 
 +  CADATE  p   CHAR  
+  CACLSID  !   PROPVAR_PAD2  +  _tagPARSEACTION  揺  D3D12_MESSAGE_SEVERITY + 慹  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE  p  LPSTR  +  tagDESCKIND  j  __crt_locale_pointers 
 �*  tagCAL  #   DWORDLONG   'R  ID3D12GraphicsCommandList    �   �/      靋!揕�H|}��婡欏B箜围紑^@�銵  A    窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  �    Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �    �!栁闙h闸@}Sx汧=�亏B謉午�     鳘�禽橉g米KD巟陪D D'oe�  `   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   襋钡�/翤㏄^觜�藔膛vЮ7��37嵇  �   択赜Oy圹;铥 N�<鸰蜬,Q雮JR�,�7  6   t�j噾捴忊��
敟秊�
渷lH�#  u   馒{蓱蕩L蘯e�LW�疗譄幗IR:;a�  �   _%1糠7硘籺蚻q5饶昈v纪嗈�     ��8/�
0躚/﨤h盙裉餠G怤爛��]�  Y   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�     澏泼寊H"鱄LUHFy]厽I媢h�J赫Cp   b   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  �   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  2   �:2K] �
j�苊赁e�
湿�3k椨�  |   樸7 忁�珨��3]"Fキ�:�,郩�  �   猯�諽!~�:gn菾�]騈购����'      犢x琿儳莻k鬿锘HC閷�!(/捱&eM殙�  H    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   掴'圭,@H4sS裬�!泉:莠й�"fE)  �   <瑓�傻a鱼张隥T$ vJ黠I鯝�0  .   �茬� 霚FmH暃蜽.駢宫誖�6?靵oP  v   U恂{榸冾�fⅢ��Hb釃"�6e`a  �   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:     �fE液}髢V壥~�?"浬�^PEΡ4L�  V   嶹栢ABZC凂U久Gk�!貟~龡单癉Q  �   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  ,	   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  u	   葦鮆培XDW駐鰄�W{�+ノ2$<F$帰B  �	   t$�>喰k柸窣鄾埉笺%撙>2 pg�,  
   _O縋[HU-銌�鼪根�鲋薺篮�j��  O
   8�'预P�憖�0R�(3銖� pN*�  �
   sL&%�znOdz垗�M,�:吶1B滖  �
   l籴靈LN~噾2u�< 嵓9z0iv&jザ  :   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  }   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS     )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  D    狾闘�	C縟�&9N�┲蘻c蟝2  �   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �   �'稌� 变邯D)\欅)	@'1:A:熾/�  
   	{Z�范�F�m猉	痹缠!囃ZtK�T�  Z
   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �
   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �
   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲     毌-��
(襔  橥轃\|�!�!p牶F  e   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   j蛹�#翎笶另Tu汔
W眡%y徚;Z鬖5  '   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  s   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  �   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�     繃S,;fi@`騂廩k叉c.2狇x佚�  Q   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  �   悯R痱v 瓩愿碀"禰J5�>xF痧  +   渐袿.@=4L笴速婒m瑜;_琲M %q�  }   矨�陘�2{WV�y紥*f�u龘��  �   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶     .�-髳�o2o~翵4D�8鷗a殔氰3籃G  Z   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  �   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  >   6觏v畿S倂9紵"�%��;_%z︹  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  !   郖�Χ葦'S詍7,U若眤�M进`  r   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   �n儹`
舔�	Y氀�:b
#p:  
   "�挨	b�'+舒�5<O�呱_歲+/�P�?  V   �>2
^�﨟2W酟傲X{b?荼猲�;  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   L錁鞾啂�JB媥潉�Z傶oKW榮忷霜�  +   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  u   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   "   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  d   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   � `�6_H蝺�W覶G沁�"Я?=!�     J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  \   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   S烌纎}啤�礴餔馞:E: �y琖�0槑&  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱     J誵
;穗琄鉔IS|狔嫖<漧白孴  P   5ビ�)昶��d腺V� �4禥Z嵫悪�	,%qq,  |   衠琪槡铟钭}_XO>�蛭X�7Mp处d  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�     �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  [   �~鴧傳.P怬WsP-"焫#N�:�&場璁  �   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �   �	玮媔=zY沚�c簐P`尚足,\�>:O  -   伙a蕑堐v!@曓�+梭�'驂蒀飫鴐  y   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  �   ,醕奂鸭uo'暬L�搟q嫾銊燠B馟�  �   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  B   ��#�#苹�1覀鉵6焉BK�1骓傎�  p   �猥��6%s� t鋁r{`玈}畫噺l鑫�  �   駒og暼硚~鮼_5屏�8BL.9妖"�r/�-R  �   チ畴�
�&u?�#寷K�資 +限^塌>�j      �$晑�~2]�/
S蟦a� �
}A珈弿V緈  `   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   穫農�.伆l'h��37x,��
fO��  �   5�\營	6}朖晧�-w氌rJ籠騳榈     }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  a   頒牛/�	� G犨韈圂J�.山o楾鐴  �   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  �   喲8s7痺撆�&!林qA>ブ�Y~a璳`N3�  E   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �   ��(`.巑QEo"焷�"娧汝l毮89fб�      �*o驑瓂a�(施眗9歐湬

�  `    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �    �8��/X昋旒�.胱#h=J"髈篒go#  �    ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  9!    I嘛襨签.濟;剕��7啧�)煇9触�.  y!   [/?KL�5�!k浘猼*�洤	�齍/嬹軍  �!   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  �!   �儔14褥緅�3]饃鹷�hK3g搋bA竑  :"   綔)\�谑U⒊磒'�!W磼B0锶!;  �"   5睔`&N_鏃|�<�$�獖�!銸]}"  �"   E縄�7�g虩狱呂�/y蛨惏l斋�笵  !#   ┫緞A$窄�0� NG�%+�*�
!7�=b  p#   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �#   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  $    栀��綔&@�.�)�C�磍萘k  V$   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �$   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �$    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  D%   d2軇L沼vK凔J!女計j儨杹3膦���  �%   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  �%   �呾��+h7晃O枖��*谵|羓嗡捬  *&   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  {&   蜅�萷l�/费�	廵崹
T,W�&連芿  �&   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  
'   v�%啧4壽/�.A腔$矜!洎\,Jr敎  W'   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �'   D���0�郋鬔G5啚髡J竆)俻w��  �'   2W瓓�<X	綧]�龐IE?'笼t唰��  A(   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  y(   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  �(   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  �(   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  Q)   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �)   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �)   �X�& 嗗�鹄-53腱mN�<杴媽1魫  -*   �F9�6K�v�/亅S诵]t婻F廤2惶I  {*   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  �*   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  +   c�#�'�縌殹龇D兺f�$x�;]糺z�  l+   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �+   �7穲碶⒖鍉鸻�:怉婤莞b=竱�   ,   ﹪冓�嗈�1蓌�)eF�;珉5� �橌5偧  G,   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �,   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �,   錵s铿勃砓b棬偡遯鮓尛�9泂惻  -   `k�"�1�^�`�d�.	*貎e挖芺
脑�  Z-   $G\|R_熖泤煡4勄颧绖�?(�~�:  �-   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �-   嫎V濺蕠&kZ峠l甩u8蝬QW蹚z`k
+  >.   �2梉�7伆鼂:E囂!2Wｔ孵>羑�輡  �.   擐�0阅累~-�X澐媆P 舋gD�  �.   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  /   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  i/   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �/   f扥�,攇(�
}2�祛浧&Y�6橵�  �/   曀"�H枩U传嫘�"繹q�>窃�8  0   窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  p0   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �0   [届T藎秏1潴�藠?鄧j穊亘^a  �0   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  F1   蓏翰bsF讍E熑j]�0萛b*xs*q翻惲�  �1   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  �1   A縏 �;面褡8歸�-構�壋馵�2�-R癕  2   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  f2   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  �2   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  
3   dhl12� 蒑�3L� q酺試\垉R^{i�  I3   bRè1�5捘:.z錨{娯啹}坬麺P  �3   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  �3   6��7@L�.�梗�4�檕�!Q戸�$�  .4   豊+�丟uJo6粑'@棚荶v�g毩笨C  q4   (鄁盯J錭澥A��/�!c� ;b卹  �4   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �4   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  I5   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �5   o藾錚\F鄦泭|嚎醖b&惰�_槮  �5   匐衏�$=�"�3�a旬SY�
乢�骣�  6   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  ]6   G髼*悭�2睆�侻皣軁舃裄樘珱)  �6   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �6   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  @7   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �7   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �7   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  /8   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  v8   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �8   禿辎31�;添谞擎�.H闄(岃黜��  9   戹�j-�99檽=�8熈讠鳖铮�  Q9   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  �9   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �9   �"睱建Bi圀対隤v��cB�'窘�n  ?:   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  �:   覽s鴧罪}�'v,�*!�
9E汲褑g;  �:   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  &;   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  j;   ,�<鈬獿鍢憁�g$��8`�"�  �;   � 罟)M�:J榊?纸i�6R�CS�7膧俇  	<   �-考X韵歎K�fUi2�'跎Zq@锍�2aJ�  S<   8蟴B或绢溵9"C dD揭鞧Vm5TB�  �<   鹰杩@坓!)IE搒�;puY�'i憷n!  �<   Eム聂�
C�?潗'{胿D'x劵;釱�  ;=   聤�苮g8鄞<aZ�%4)闪�|袉uh�  �=   0T砞獃钎藰�0逪喌I窐G(崹�  �=   �(M↙溋�
q�2,緀!蝺屦碄F觡  >   馩圣纸lMO]P桋tA荚�'羮肠曖K  V>   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �>   G�膢刉^O郀�/耦��萁n!鮋W VS  �>   存*?\��-矪q7o責覃:},p穿奵�  ?   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  `?   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �?   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �?   齛|)3h�2%籨糜/N_燿C虺r_�9仌  2@   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  o@   �0�*е彗9釗獳+U叅[4椪 P"��  狜   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  兀   +YE擋%1r+套捑@鸋MT61' p廝 飨�  ?A   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  ~A   L�9[皫zS�6;厝�楿绷]!��t  糀   �=蔑藏鄌�
艼�(YWg懀猊	*)  鼳   交�,�;+愱`�3p炛秓ee td�	^,  >B   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  圔   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  褺   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  C   _臒~I��歌�0蘏嘺QU5<蝪祰S  XC   櫕襩!)G昞�&鞿b凉�豻$峜LTQ�7�  朇   謷蘋愦	豢]麸Md�觙 G澿f1e
T奞~  銫   D,y邥鞃黎v)�8%遾1�*8赩�婯�  +D   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  uD   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  薉   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  	E   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  SE   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  汦   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  贓   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�  &F   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  pF   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  糉   +4[(広
倬禼�溞K^洞齹誇*f�5  G   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  YG    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  桮   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  諫   V� c鯐鄥杕me綻呥EG磷扂浝W)  "H   a�傌�抣?�g]}拃洘銌刬H-髛&╟  `H   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  烪   潝(綊r�*9�6}颞7V竅\剫�8値�#  鞨   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  6I   副謐�斦=犻媨铩0
龉�3曃譹5D   xI   憒峦锴摦懣苍劇o刦澬z�/s▄![�  稩   k�8.s��鉁�-[粽I*1O鲠-8H� U  鵌   �l{`G2
`0m襭皗犿鷕g貋⒌joゥ  CJ   ^憖�眜蘓�y冊日/缁ta铁6殔  慗   魯f�u覬n\��zx騖笹笾骊q*砎�,�  貸   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  +K   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  kK   v-�+鑟臻U裦@驍�0屽锯
砝簠@      萾箒�$.潆�j閖i转pf-�稃陞��  鯧   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  NL   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  婰   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  蚅   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  M   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  TM   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  淢   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  躆   跻]�	隗[鼀hF鰘廰)鋥Le6�攬R7  %N   iu�	��&? 蔬釥�#簤Ηu-塘b@遍迭  kN   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  窷   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  	O   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  UO   K�:荳)a懃J�拌� ,寨吙u⑺�  烵   哳訵a醴泉撬4wO�)鹁疜�)誊)�=跹�  霴   b4鵣r龋遙账y圗D轛磧﹜�$瀬Y+�7�  &P   �3掍S\絧飦戀轝
5鍄0d
妺XE��
  oP   閯�価=�<酛皾u漑O�髦jx`-�4睲�  籔   邃2硂摜_< )D��７"�(咆e泺�  鏟   �="V�A�D熈fó 喦坭7b曉叼o1  -Q   �#i匒U0/��%鷛1,爆簡n)瞰#謺狌�  俀   r�L剟FsS鏴醼+E千I呯贄0鬬/�  蚎   
罬}�(囫Ldh]僘l9-6牜I�.拾R欐佬  #R   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏  tR   �=A�%K鹹圛19振╯鵽C殾錦`蔣  盧   猟涔紳0箆|琋唞o{雏.临mX|珈  軷   �
bH<j峪w�/&d[荨?躹耯=�  S   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  ZS   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  燬   S\颂a毡荊v髟>P鬂{O�0I粪冪艝{  蒘   �-�雧n�5L屯�:I硾�鮎访~(梱  T   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  `T   +椬恡�
	#G許�/G候Mc�蜀煟-  燭   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  逿   妇舠幸佦郒]泙茸餈u)	�位剎   U   鹴y�	宯N卮洗袾uG6E灊搠d�  �   p      j  @  	  �  �"  `   �    B   �    H   �    Y   �    �   
  X  U     X  �   �  �&    �  �&  N  �  �&  B  �  �&  �
  �  �"    �  �"    �  �&  �  �  �&  �  �  �&  �  �  �&  �  �  �"  �   �  �"  �   �  �&  �  �  �&  �  �  �&  D
  �  �&  �    �&  O     �&  0     �"  K     X  �  K  X  �  L  X  �  N  �&  L
  `  X  �   j  @  ?	  {  �&  �  }  �&  �  �  @  Q	  �  �&  s  �  �&  �  �  �&  )
  �  @  *	  5  X  �  @  @  $	  B  X  �   W  X  �  h  X  �   l  X  @     @)     $  �"  �   >  �'  �  ?  �'  `  �  �&  �  �  �&  �	  �  �&  �	  �  P#  �    �"  �      �'  �  `  �&  @
  h  �&  �  l  P#    m  P#    �  X  �  r  �"  �   �  X  �     X  @     X  @   4  �"  �   C  �'  �     �'  �  %   �'  ]  J   X  �  K   X  �  �   X  �  �   �  �  �$  @)  2   �$  �'  S  �(  �'  b  �,  0  �  -  �  w  -  �  q  -  �  j  -  �  K  -  X  �  -  �  �  -  �  �  -  H  j   -  H  L   -  H  G   -  H  <    -  H  1   !-  H  )   %-  �  �  &-  �  S  '-  �  '  (-  �    )-  H  �  --  X  �  0-  �    1-  �  
  4-  X  �  5-  X  �  6-  �  {  8-  H  X  9-  H  P  <-  H  %   >-  H  �  ?-  �  �  @-  �  �  A-  �  �  I-  �  �   J-  �    K-  �"  �   M-  H  G  O-  �"  �   R-  �"  �  V-  �  �   Y-  X  �  \-  H  C  ]-  H  <  `-  X  F  a-  X  �  b-  X  |  j-  �  �  o-  X  �  }-  �#  a  -  X  �  �-  X  R  �-  H  3  �-  X  @   �-  �#  l  �-  X  �  �-  �#  ;  �-  �"  9  �-  �#  �   �-  �"  5  B0  X  �  �0  X  �  �0  X  �  E2  h    I2  �  �   J2       h2  0  j   i2  �  >  k2  H    l2  �'  f  m2  �'  �  n2  �'  `  o2  �'  l  p2  �'  b  q2  �'  S  r2  �'  4  t2  �'  `  y2  �"  \  {2  �'  b  |2  �'  4  ~2  �'  `  2  �'  j  �2  �'  �  �2  �'  `  �2  �'  �  �2  �'  `  �2  �'  4  �2  �"  \  �2  �"  �   �2  �"  \  �2  �  4  �2  �  u  �2  H  �  �2  H  H  �2  �'  �  �2  �'  �  �2  �"  L  �2  �'  �  �2  �'  �  �2  �'  �  �2  �"  L  �2  �"  L  �2  �&  <
  �2  H  "  �2  0  1   �2  X  �  �2  X  �  �2  �"    �2  X  �  �2  X  �  �2  H  '  �2  �  L  �2  �  �  �2  �'    �2  X  �  �2  �'  
  �2  X  �  �2  �'  
  �2  X  �  �2  �'    �2  X  �  �2  �'  
  �2  X  �  �2  �'  
  �2  �    �2  X  �  �2  �&  �  �2  X  �  �2  H  a  �2  H  `  �2  �'  �  �2  �'  �  �2  �'  �  �2  �'  �  �2  �'  �  3  X  �  3  �  �  3  �'    3  X  �  
3  �'    3  X  �  3  X  �  3  X  �  "3  X    #3  X  �  $3  �'  �  '3  �'  �  (3  �'  �  +3  �'  �  ,3  �'  �  /3  �'  �  03  �'  �  43  �'  �  53  �'  �  83  �'  �  93  �'  �  <3  �'  ]  ?3  �'  ]  B3  �'  ]  E3  �'  ]  H3  �'  ]  O3  �  �   P3  X  �  R3  X  �  S3  X  �  U3  X  �  V3  X  �  X3  X    Y3  X  �  Z3  X  �  ^3  X  �  `3  X  �  b3  X  �  d3  X  �  m3  X  �  n3  X  �  o3  X  �  p3  X  �  y3  �  �  }3  X    ~3  X  �  �3  �  �  �3  X  �  �3  �  �  �3  �  �  �3  X  �  �3  X  @   �3  X  @   �3  X  �  �3  X  �  �3  X  �  �   hU   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\RTXPT\External\Donut\nvrhi\src\common\dxgi-format.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resourcebindingmap.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgitype.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\aftermath.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winioctl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ncrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mciapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\lzexpand.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ole2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winspool.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\prsht.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\RTXPT\External\Donut\nvrhi\src\common\versioning.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\playsoundapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12sdklayers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\RTXPT\External\nvapi\nvapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\RTXPT\External\nvapi\nvapi_lite_salstart.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\nvrhi\src\d3d12\d3d12-shader.cpp D:\RTXPT\External\nvapi\nvapi_lite_common.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\bcrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h D:\RTXPT\External\Donut\nvrhi\src\d3d12\d3d12-backend.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h D:\RTXPT\External\nvapi\nvapi_lite_surround.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmeapi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winscard.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsystem.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsyscom.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\nvapi\nvapi_lite_stereo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleauto.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dpapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\commdlg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\cderr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dde.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ddeml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\inaddr.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\set D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtree D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bitset C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winsmcrd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\shellapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsock.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\nb30.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\joystickapi.h D:\RTXPT\External\Donut\nvrhi\src\common\state-tracking.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winperf.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h D:\RTXPT\External\nvapi\nvapi_lite_d3dext.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dlgs.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3dcommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\queue D:\RTXPT\External\nvapi\nvapi_lite_salend.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\RTXPT\External\nvapi\nvapi_lite_sli.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h �       L�3  (  V   ,  V  
 �+      �+     
 SP      WP     
  G R   G R  
    n 摄j�#翅M弫v齴菉   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\Release\nvrhi_d3d12.pdb ����      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   h        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        
  
 Z   �   N Z   �     (                      H 
 h   
         $LN14  0     O_Bytes  O   �   h           :   X  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   <   0   <  
 �   <   �   <  
 �   <   �   <  
 �   <   �   <  
   <     <  
 s  �   w  �  
 �  <   �  <  
 H嬃�   �   �   g G                      �        �std::_Atomic_address_as<long,std::_Atomic_padded<unsigned long> >  >鯸   _Source  AJ                                 H�     鯸  O_Source  O �   0              @$     $       j  �    m  �   n  �,   /   0   /  
 �   /   �   /  
 �   /   �   /  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   _      �   F  G G            0   
   %   �3        �std::_Copy_memmove<char *,char *>  >p   _First  AJ          >p   _Last  AK          >p   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   e3   0   p  O_First  8   p  O_Last  @   p  O_Dest  O  �   @           0   �     4       � �   � �   � �!   � �%   � �,   B   0   B  
 n   B   r   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
 \  B   `  B  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   _      �   V  W G            0   
   %   �         �std::_Copy_memmove<unsigned int *,unsigned int *>  >u   _First  AJ          >u   _Last  AK          >u   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   P    0   u  O_First  8   u  O_Last  @   u  O_Dest  O  �   @           0   �     4       � �   � �   � �!   � �%   � �,   C   0   C  
 ~   C   �   C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
 l  C   p  C  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   _      �   �  � G            0   
   %   �3        �std::_Copy_memmove<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 * *,NVAPI_D3D12_PSO_EXTENSION_DESC_V1 * *>  >萿   _First  AJ          >萿   _Last  AK          >萿   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   �-   0   萿  O_First  8   萿  O_Last  @   萿  O_Dest  O�   @           0   �     4       � �   � �   � �!   � �%   � �,   A   0   A  
 �   A   �   A  
 �   A   �   A  
 �   A   �   A  
 �   A      A  
   A   !  A  
 �  A   �  A  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   _      �   n  o G            0   
   %   y3        �std::_Copy_memmove<D3D12_INPUT_ELEMENT_DESC *,D3D12_INPUT_ELEMENT_DESC *>  >�   _First  AJ          >�   _Last  AK          >�   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   z3   0   �  O_First  8   �  O_Last  @   �  O_Dest  O  �   @           0   �     4       � �   � �   � �!   � �%   � �,   ?   0   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 �   ?   �   ?  
   ?     ?  
 �  ?   �  ?  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   _      �   d  e G            0   
   %   �3        �std::_Copy_memmove<_NV_CUSTOM_SEMANTIC *,_NV_CUSTOM_SEMANTIC *>  ><u   _First  AJ          ><u   _Last  AK          ><u   _Dest  AM         AP          >    _Count  AI  
                             H 
 h   �3   0   <u  O_First  8   <u  O_Last  @   <u  O_Dest  O�   @           0   �     4       � �   � �   � �!   � �%   � �,   @   0   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 �   @     @  
 x  @   |  @  
 H;蕋xH塡$WH冹 H塼$0H孃3鯤嬞@ H婼H凓v,H�H�翲侜   rL婣鳫兟'I+菻岮鳫凐w0I嬋�    H塻H荂   @�3H兠8H;遳睭媡$0H媆$8H兡 _描    蘎   �      �      �   �  f G            �      �   �2        �std::_Destroy_range<std::allocator<nvrhi::VertexAttributeDesc> >  >�   _First  AI       h \   AJ          AJ }       >�   _Last  AK          AM       m f   AK }       >�   _Al  AP           AP       ^    D@    M        3  E ^ M        �  E ^& M        �   
,$
 M        �    N M        �  ,*T M          *&N M        `  0)-
 Z   G  
 >   _Ptr  AJ  -     )  
  >#    _Bytes  AK  0     S & ( " M          
9#
0
 Z   �   >    _Ptr_container  AP  =     F  -  AP Q       >    _Back_shift  AJ  D     ? 
 -  N N N N N N N                       @� J h       �  �  �  �  �  �  �      X  `  3  3  3  3         $LN58  0   �  O_First  8   �  O_Last  @   �  O_Al  O�   H           �   X     <       > �    B �    C �e   B �n   F �~   C �,   .   0   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
   .     .  
 #  .   '  .  
   .   "  .  
 C  .   G  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 p  �   t  �  
 �  .   �  .  
 L塂$H塋$SVWATAUAVAWH冹 L嬯H孂H�L嬧L+郘媞L+餓窿I�������M;�刋  I�艸婭H+菻六H嬔H殃I嬂H+翲;��/  H�
M孇I;芁C鳰;��  I嬿H伶L墊$hH侢   r)H峃'H;�嗹   �    H吚勽   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL墊$h�3跮墊$hH塡$xI冧郙�<H婦$p AHAOL婫H�H嬎M;鑥L+码M嬇L+妈    I峅 L婫M+臝嬚�    怘�H吷t1H媁H+袶冣郒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I伶L驦墂H�H塐I嬊H兡 A_A^A]A\_^[描    惕    惕    蹋   �   �   �     _   1  _   k  �   �  �   �     �  �      �   ,	  � G            �     �  3        �std::vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> >::_Emplace_reallocate<D3D12_INPUT_ELEMENT_DESC const &> 
 >�   this  AJ          AM       �v  D`    >q�   _Whereptr  AK          AU       �v  >�   <_Val_0>  AH  �     /  AP        =  Dp    >#     _Newcapacity  AW  p     ~  AW �        Bh   �       >    _Newsize  AV  I     a-" U  >    _Whereoff  AT  %       >    _Oldsize  AV  ,     x   U >q�    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M        P3  tm�+ M        m3  tm�+& M        B  ��)
)%��( M        h  ��$	%)
��
 Z      >    _Block_size  AJ  �       AJ �      >    _Ptr_container  AH  �       AH �     �  � 
 >0    _Ptr  AI  �       AI �     � � 
  M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N M          
m
 N N N M        (3  Ik >    _Oldcapacity  AJ  M     �   L % y   AJ �     � , �  >    _Geometric  AH  m     t :  ^   AH �     �  �  M        <3  I N N M        R3  �� N M        S3  � M        y3  � >    _Count  AP        AP 0      N N M        S3  �* >q�   _Last  AP  *      >�   _Dest  AJ  &    
  AJ 0      M        y3  �* >    _Count  AP  -      AP 0      N N M        S3  � M        y3  � >h    _First_ch  AK  
      AK 0      >    _Count  AP        N N% M        '3  �6h1#' M        �2  *丒_ M        `  両):
 Z   G  
 >   _Ptr  AJ j      >#    _Bytes  AK  B    -    AK �     % M          丷d#
=
 Z   �   >    _Ptr_container  AP  Z      AP j    ?  5  >    _Back_shift  AJ  9    1  AJ j    ?  #  N N N N
 Z   &3               8         0@ � h   e  
      `  B  h    �2  �2  �2  3  '3  (3  )3  <3  P3  Q3  R3  S3  T3  j3  k3  l3  m3  x3  y3  z3  {3  �3  �3         $LN118  `   �  Othis  h   q�  O_Whereptr  p   �  O<_Val_0>  O�   �           �  �'     �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   A ��   B �  E �  G �  K �  L �"  N �6  V ��  W ��  X ��  = ��  7 ��  V ��   ^  � F            (   
   (             �`std::vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> >::_Emplace_reallocate<D3D12_INPUT_ELEMENT_DESC const &>'::`1'::catch$0 
 >�   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �2                        � �        __catch$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z$0        $LN118  `   �  Nthis  h   q�  N_Whereptr  p   �  N<_Val_0>  O  �   0           (   �'     $       P �
   R �   S �,   5   0   5  
 �   5   �   5  
 �   5   �   5  
   5     5  
 !  5   %  5  
 H  5   L  5  
 X  5   \  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5     5  
 !  5   %  5  
 K  5   O  5  
 _  5   c  5  
 s  5   w  5  
 1  5   5  5  
 A  5   E  5  
 j  5   n  5  
 z  5   ~  5  
 �  5   �  5  
 �  5   �  5  
 l  5   p  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 Q  5   U  5  
 a  5   e  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 
  5     5  
   5     5  
 v  5   z  5  
 �  5   �  5  
 �  5   �  5  
 ;  5   ?  5  
 \  5   `  5  
 p  5   t  5  
 �  5   �  5  
 �  5   �  5  
   5   
  5  
   5     5  
 �  �   �  �  
 @	  5   D	  5  
  
  D   $
  D  
 �
  D   �
  D  
    D     D  
 6  �   :  �  
 
  �     �  
 h  D   l  D  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �      #   [   L塂$H塋$SVWATAUAVAWH冹 L嬧H孂L�	L孃M+鵌�H婣I+罤柳I�������I;�処  L峢H婭I+蒆六H嬔H殃I嬂H+翲;��  H�
M嬽I;臠C餗;��  J�4�    L塼$hH侢   r)H峃'H;�嗗   �    H吚勩   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL塼$h�3跮塼$hH塡$xN�4鸋婦$pH� I�L婫H�H嬎M;鄒L+码M嬆L+妈    I峃L婫M+腎嬙�    怘�H吷t1H媁H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐w:I嬋�    H�J�際塐H�H塐I嬈H兡 A_A^A]A\_^[描    惕    惕    泰   �   �   �     _   )  _   c  �   �  �   �  �   �  �      �   '	  � G            �     �  3        �std::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >::_Emplace_reallocate<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> 
 >謚   this  AJ          AM       �k  D`    >*v   _Whereptr  AK          AT       �m  >騯   <_Val_0>  AH  �       AP        A  Dp    >#     _Newcapacity  AV  u     {  AV �        Bh   �     
  >    _Newsize  AU  N     Q9 E  >    _Whereoff  AW  %     z  ^
  >    _Oldsize  AH  0     h  2 1 >*v    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M        ^3  ur� M        p3  ur�& M        B  ��)
)%��( M        h  ��$	%)
��
 Z      >    _Block_size  AJ  �       AJ �      >    _Ptr_container  AH  �       AH �     �  � 
 >0    _Ptr  AI  �       AI �     � � 
  M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N M           
r

 N N N M        53  Nk >    _Oldcapacity  AJ  R     �   M % z   AJ �     �  �  >    _Geometric  AH  r     u ;  _   AH �     �  �  M        E3  N N N M        `3  �
 M        �3  �
 >    _Count  AP  �       AP (      N N M        `3  �" >*v   _Last  AP  "      >萿   _Dest  AJ      
  AJ (      M        �3  �" >    _Count  AP  %      AP (      N N M        `3  � M        �3  � >h    _First_ch  AK        AK (      >    _Count  AP        N N% M        43  �.h1#' M        �2  *�=\ M        `  丄)7
 Z   G  
 >   _Ptr  AJ b      >#    _Bytes  AK  :    -    AK �     % M          丣d#
:
 Z   �   >    _Ptr_container  AP  R      AP b    <  2  >    _Back_shift  AJ  1    1  AJ b    <  +  N N N N
 Z   33               8         0@ v h   
      `  B  h     S-  �-  �2  �2  �2  �2  3  43  53  63  E3  ^3  _3  `3  a3  g3  h3  i3  p3  �3  �3         $LN110  `   謚  Othis  h   *v  O_Whereptr  p   騯  O<_Val_0>  O �   �           �  �'     �       * �   3 �,   4 �7   6 �J   : �N   ; �r   = ��   A ��   B ��   E �
  G �
  K �  L �  N �.  V �z  W �}  X ��  = ��  7 ��  V ��   �  � F            (   
   (             �`std::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >::_Emplace_reallocate<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>'::`1'::catch$0 
 >謚   this  EN  `         (  >騯   <_Val_0>  EN  p         ( 
 Z   �2                        �        __catch$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z$0        $LN110  `   謚  Nthis  h   *v  N_Whereptr  p   騯  N<_Val_0>  O�   0           (   �'     $       P �
   R �   S �,   9   0   9  
 �   9   �   9  
 �   9   �   9  
 *  9   .  9  
 :  9   >  9  
 a  9   e  9  
 q  9   u  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
   9     9  
 B  9   F  9  
 l  9   p  9  
 �  9   �  9  
 �  9   �  9  
 R  9   V  9  
 b  9   f  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 X  9   \  9  
 h  9   l  9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
   9     9  
 !  9   %  9  
 }  9   �  9  
 �  9   �  9  
 �  9   �  9  
 B  9   F  9  
 c  9   g  9  
 w  9   {  9  
 �  9   �  9  
 �  9   �  9  
 
  9     9  
   9   !  9  
 �  �   �  �  
 <	  9   @	  9  
 
  E    
  E  
 �
  E   �
  E  
   E     E  
 K  �   O  �  
 T  �   X  �  
 �  E   �  E  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �   #   [   D�I钩     禕L3�禞MI3繧H3�禞IH3罥�   �   0  L G            7       6   @        �std::_Fnv1a_append_value<unsigned int> 
 >   _Val  AJ          >�#   _Keyval  AK        7  M        j  @2
 >#    _Val  AH          AP         N                        H� 
 h   j        O_Val     �#  O_Keyval  O�   0           7   @     $       $	 �    &	 �6   '	 �,   2   0   2  
 q   2   u   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
 D  2   H  2  
 D�I钩     H�%#"勪滘薍嬔L3�禔禝MI3繧H3�禞IH3罥�   �   8  M G            D       C   �        �std::_Hash_representation<unsigned int>  >�#   _Keyval  AJ          AK       )  M        @   ( M        j  @
 >#    _Val  AH  -       AP         N N                        H�  h   j  @      �#  O_Keyval  O�   @           D   @     4       *	 �    +	 �   *	 �   +	 �C   ,	 �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 L  �   P  �  
 H塋$SVWAVAWH冹 L嬺H嬹H�������H;��  L媦L+9H婭H+H嬔H殃H嬅H+翲;葀+H塡$XH�&      ��    H吚勌   H峹'H冪郒塆AH�
I;轎B轍塡$XH侞   rH岾'H塡$XH;�啛   敕H呟t
H嬎�    H孁�3�H墊$hJ�?M嬈M+�3诣    L婩H�L+翲嬒�    怘�H吷t-H媀H+袶侜   rH兟'L婣鳬+菻岮鳫凐w'I嬋�    H�>J�7H塅H�H塅H兡 A_A^_^[描    惕    惕    蘕   �   �   �   �   `   �   _     �   2  �   8  �   >  �      �   8  w G            C     C  3        �std::vector<char,std::allocator<char> >::_Resize_reallocate<std::_Value_init_tag> 
 >'y   this  AJ          AL       -  DP    >   _Newsize  AK          AV       0 
 >〃   _Val  AP        B\  �  � � <  AP �       D`    >#     _Newcapacity  AI        #7  W  � �  AI W     �  A �   AJ W       BX   M     � &  �   >    _Oldsize  AW  -     �  
  >e    _Appended_first  AJ  �     
  >e    _Newvec  AM  �       AM �     � z   Bh   �     ~  M        5  W,�� M        W  W,��- M        B  W,	
%
��* M        h  W%)		��
 Z      >    _Block_size  AJ  �     �  �  AJ W       >    _Ptr_container  AH  \       AH �     �  g 
 >0    _Ptr  AM  m       AM �     � z   M        
  W
 Z   �   N N M        
  ��
 Z   �   N N N N" M        93  0g+ >    _Oldcapacity  AJ  4     � #  \  t 
  AJ �     	  >    _Geometric  AI  w       M        H3  0 N N M        3  �� >#    _Count  AP  �     
  M        d3  �� N N M        b3  �� >e   _First  AK  �       >e   _Last  AP  �       M        �3  ��c >    _Count  AP  �       N N% M        83  ��h-# M          &��E >   _Count  AK  �     
  AK 
      M        `  ��)$
 Z   G  
 >   _Ptr  AJ 
      >#    _Bytes  AK  �     $  
  AK 
    )   % M          ��d#
'
 Z   �   >    _Ptr_container  AP  �       AP 
    )    >    _Back_shift  AJ  �     -  AJ 
    )  N N N N
 Z   73               (         0@ � h!   e  
        [  `  5  B  W  h  o  �2  �2  3  3  3  3  83  93  :3  H3  b3  c3  d3  e3  f3  �3  �3  �3  �3  �3  �3         $LN92  P   'y  Othis  X     O_Newsize  `   〃  O_Val  O�   �           C  �'     �       � �   � �)   � �0   � �W   � �s   � ��   � ��   � ��   � ��   � ��   	 �%  
 �1  	 �7  � �=  � ��   �  � F            (   
   (             �`std::vector<char,std::allocator<char> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >'y   this  EN  P         ( 
 >〃   _Val  EN  `         ( 
 Z                           � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN92  P   'y  Nthis  X     N_Newsize  `   〃  N_Val  O   �   0           (   �'     $        �
    �    �,   :   0   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
   :     :  
 .  :   2  :  
 ]  :   a  :  
 y  :   }  :  
 �  :   �  :  
 �  :   �  :  
 �  :   �  :  
 �  :     :  
    :   $  :  
 0  :   4  :  
 D  :   H  :  
   :     :  
 #  :   '  :  
 L  :   P  :  
 \  :   `  :  
   :   �  :  
 �  :   �  :  
 9  :   =  :  
 U  :   Y  :  
 z  :   ~  :  
 �  :   �  :  
 #  :   '  :  
 C  :   G  :  
 �  :   �  :  
 �  :   �  :  
 �  :   �  :  
 E  :   I  :  
 f  :   j  :  
 z  :   ~  :  
 �  :   �  :  
 �  :   �  :  
   :     :  
 $  :   (  :  
 �  �   �  �  
 L  :   P  :  
 	  F   	  F  
 �	  F   �	  F  
 �	  F   �	  F  
 �	  �   �	  �  
 g
  �   k
  �  
 �
  F   �
  F  
 H塗$UH冹 H嬯L婨XH婾hH婱P�    3�3设    �   �   #   [   H塋$SVWAVAWH冹 H嬺L嬹I�������?I;�囧   L媦L+9I�H婭I+H六H嬔H殃I嬂H+翲;�嚧   H�<
H;
H孇H塼$h�H墊$hI;�嚀   H��    H侚   r$H岮'H;羦yH嬋�    H吚txH峏'H冦郒塁H吷t
�    H嬝�3跦塡$XJ�籐嬈M+荖��    3诣    M婩I�L+翲嬎�    怢嬒L嬈H嬘I嬑H兡 A_A^_^[�    �    惕    惕    虛   �   �   �   �   `   �   _     �   	  �     �     �      �   o  � G                   3        �std::vector<unsigned int,std::allocator<unsigned int> >::_Resize_reallocate<std::_Value_init_tag> 
 >[F   this  AJ          AV       �   DP    >   _Newsize  AK          AL       �  
 >〃   _Val  AP           D`    >#     _Newcapacity  AM  `       AM u     � �  �   Bh   e     �   �   >    _Oldsize  AW  -     �   � 
 �   >璅    _Appended_first  AJ  �       >璅    _Newvec  AI  �       AI �     H  BX   �     H  M        J   Ol�� M        �   Ol��' M        B  })
$%
O, M        h  ��$(%
d Z     �   >    _Block_size  AH  �     
  AH       >    _Ptr_container  AH  �     �  j  AH �      
 >0    _Ptr  AI  �       AI �     H  M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N M        l  
l
	 N N N! M           4kD%
 >    _Oldcapacity  AJ  8     E    AJ       >    _Geometric  AM  X         AM u     � �  �   M        %   4 N N M        B0  �� >#    _Count  AP  �       M        �0  �� N N M        K   �� >璅   _First  AK  �       >璅   _Last  AP  �       M        �   ��c >    _Count  AP  �       N N Z                      (         0@ r h   e  
  B  h  l  �                 %   J   K   L   N   P   �   �   �   �   �   �   B0  �0  �0  �0         $LN76  P   [F  Othis  X     O_Newsize  `   〃  O_Val  O �   �             �'     |       � �   � �)   � �4   � �l   � ��   � ��   � ��   � ��   	 ��   
 �  	 �  � �  � �  � ��   �  � F            (   
   (             �`std::vector<unsigned int,std::allocator<unsigned int> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >[F   this  EN  P         ( 
 >〃   _Val  EN  `         ( 
 Z   �                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN76  P   [F  Nthis  X     N_Newsize  `   〃  N_Val  O   �   0           (   �'     $        �
    �    �,   ;   0   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
   ;   "  ;  
 M  ;   Q  ;  
 ]  ;   a  ;  
 u  ;   y  ;  
 �  ;   �  ;  
 �  ;   �  ;  
 �  ;   �  ;  
   ;     ;  
   ;     ;  
 �  ;   �  ;  
 �  ;   �  ;  
   ;     ;  
 *  ;   .  ;  
 I  ;   M  ;  
 Y  ;   ]  ;  
   ;      ;  
 0  ;   4  ;  
 U  ;   Y  ;  
 i  ;   m  ;  
 �  ;   �  ;  
   ;     ;  
 :  ;   >  ;  
 w  ;   {  ;  
 -  �   1  �  
 �  ;   �  ;  
 4  G   8  G  
 �  G   �  G  
 �  G   �  G  
 !  �   %  �  
 �  �   �  �  
  	  G   	  G  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   �   #   [   H塋$SVWATAUAVAWH冹0L孃H孂I箳$I�$I�I;�國  H婭H+I�%I�$I�$II嬇H鏖L嬧I咙I嬆H凌?L郒婳H+I嬇H鏖H龙H嬄H凌?H蠬嬍H验I嬃H+罤;�嚉  H�I;莝
I嬊H墑$�   �H墑$�   I;�噑  Lk�8I侢   r+I峃'I;�哬  �    H吚刉  H峏'H冦郒塁�3鲭M咑tI嬑�    H嬝3鲭3鰦轍塡$xIk�8H薍塋$(I嬜I+訪嬊�    H塂$ L婫H嬎H�I;衪_�W�H塹H塹JIH塺H荁   � 禕 圓 婤$堿$婤(堿(婤,堿,婤0堿0禕4圓4H兞8H兟8I;衭嬊H嬔�    怘�H吷tWL嬊H媁�    L�H婳I+菼嬇H鏖H龙H嬄H凌?H蠬k�8H侜   rH兟'I婬鳯+罥岪鳫凐w=L嬃I嬋�    H�Ik�8H肏塆I�H塆H兡0A_A^A]A\_^[描    惕    惕    糖   �   �   �     7   �  .   �  .   �  �      �   &     ,  �      �   �	  � G            1     1  3        �std::vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> >::_Resize_reallocate<std::_Value_init_tag> 
 >坤   this  AJ          AM         Dp    >   _Newsize  AK          AW       �
 
 >〃   _Val  AP        *�  �  � #$  AP �       D�    >#     _Newcapacity  AH  �     
  AH �     x  E  o  B�   �     �  �  >�    _Appended_last  D     >    _Oldsize  AT  G       >�    _Appended_first  AJ        D(    >�    _Newvec  AI  �       AI �     5"
  Bx       0  M        U3  Y��亅 M        n3  Y��亅& M        B  ��)
+%�'( M        h  ��$	%)
丆
 Z      >    _Block_size  AJ  �       AJ       >    _Ptr_container  AH  �       AH �     4 
 >0    _Ptr  AI  �       AI �     5"
  M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N M        �3  
��
	 N N N# M        ,3  UD%
 >    _Geometric  AH  �     #  
  AH �     x  E  o  M        ?3  U N N' M        V3  "�$(	N4	 >�   _Last  AP  $    m  >�    _UFirst  AK  *    j  >Τ   _Backout  CJ     '    r  M        #3  �$ N M        "3  亷
 Z   �2   N M        }3  (�3N M        �3  (�3 M        �  �3 M        �  0�> M        �  丩$ N M        �  �> N N M        K  �3 M        }  �3�� M        �  �3 N N N N N N N' M        +3  仛(LK#$
 Z   �2   M        �2  -佁_ M        `  佇):
 Z   G  
 >   _Ptr  AP �      >#    _Bytes  AK  �    )  AK +     % M          佡d#
=
 Z   �   >    _Ptr_container  AJ  �      AJ �    ?  7  >    _Back_shift  AP  �    @  AP �    ?  2  N N N N Z   	3  *3   0           8         0@ � h.   e  
        �  �  �  �  �  �  �  �      J  K  Z  ^  _  `  }  �  �  B  h  �2  �2  3  !3  "3  #3  +3  ,3  -3  ?3  U3  V3  W3  n3  |3  }3  �3  �3  �3  �3         $LN116  p   坤  Othis  x     O_Newsize  �   〃  O_Val      �  O_Appended_last  (   �  O_Appended_first  O   �   �           1  �'     �       � �   � �-   � �U   � ��   � ��   � �
  � �   � �'  � �*  � ��  	 �  
 �  � �%  � �+  	 ��   s  � F            =      =             �`std::vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$4 
 >坤   this  EN  p         = 
 >〃   _Val  EN  �         =  Z   �2  �2   (                    � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN116  p   坤  Nthis  x     N_Newsize  �   〃  N_Val      �  N_Appended_last  (   �  N_Appended_first  O �   8           =   �'     ,        �    �    �3    �,   6   0   6  
 �   6   �   6  
 �   6   �   6  
   6     6  
   6     6  
 :  6   >  6  
 Z  6   ^  6  
 �  6   �  6  
 �  6   �  6  
 �  6   �  6  
   6     6  
 +  6   /  6  
 U  6   Y  6  
 e  6   i  6  
 y  6   }  6  
 5  6   9  6  
 E  6   I  6  
 n  6   r  6  
 ~  6   �  6  
 �  6   �  6  
 �  6   �  6  
 y  6   }  6  
 �  6   �  6  
 �  6     6  
 !  6   %  6  
 H  6   L  6  
 "  6   &  6  
 C  6   G  6  
 S  6   W  6  
 �  6   �  6  
 �  6   �  6  
 �  6   �  6  
 �  6   �  6  
 	  �   	  �  
 �	  6   �	  6  
 T
  I   X
  I  
   I     I  
 '  I   +  I  
 a  �   e  �  
   �   !  �  
 �  I   �  I  
 H塗$SUH冹(H嬯L婨pH婾 H婱(�    L媴�   H婾xH婱p�    3�3设    �   .   /      8   [   H塡$H塋$VWATAVAWH冹 L嬺H孂I龟�隊� I;�噦  H婭H+I肌隊隊I嬆H鏖L孃I�I嬊H凌?L鳫婳H+I嬆H鏖H龙H嬄H凌?H蠬嬍H验I嬃H+罤;��$  H�I;苨
I嬈H塂$h�H塂$hI;��  Hi�  H侢   r)H峃'H;�嗘   �    H吚勪   H峏'H冦郒塁H咑t
H嬑�    H嬝�3跦塡$XIi�  H薎嬈I+莟Li�  3诣    L婫H�L+翲嬎�    怢�M吚tKH婳I+菼嬆H鏖H龙H嬄H凌?H蠬i�  H侜   rH兟'I婬鳯+罥岪鳫凐wBL嬃I嬋�    H�Ii�  H肏塆H�H塆H媆$`H兡 A_A^A\_^描    惕    惕    唐   �   �   �     `   (  _   |  �   �  �   �  �   �  �      �   �  � G            �     �  
3        �std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> >::_Resize_reallocate<std::_Value_init_tag> 
 >Ju   this  AJ          AM       ��  DP    >   _Newsize  AK          AV       �� 
 >〃   _Val  AP        ��  �  � � �  AP �     )    D`    >#     _Newcapacity  AH  �       AH �     "  F  _ �  Bh   �     %    >    _Oldsize  AW  I       >瀠    _Appended_first  AJ        AJ     
  >瀠    _Newvec  AI  �       AI �     � �   BX   �     � �   M        Y3  V��� M        o3  V���& M        B  ��)
)%
��( M        h  ��$	%)
��
 Z      >    _Block_size  AJ  �       AJ �      >    _Ptr_container  AH  �       AH �     �  � 
 >0    _Ptr  AI  �       AI �     � �   M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N M        �3  
��
	 N N N# M        03  WD%
 >    _Geometric  AH  �         AH �     "  F  _ �  M        B3  W N N M        3  �
 >#    _Count  AH        AH       N M        Z3  � >瀠   _First  AK  !      >瀠   _Last  AP        M        �3  �c >    _Count  AP  $      N N% M        /3  �-hK#& M        �2  0丳g M        `  乄)?
 Z   G  
 >   _Ptr  AP x      >#    _Bytes  AK  W    )  AK �     % M          乣d#
B
 Z   �   >    _Ptr_container  AJ  h      AJ x    D  <  >    _Back_shift  AP  0    H  AP x    D 3   N N N N
 Z   .3               (         0@ � h    e  
      `  B  h  �2  �2  �2  3  3  3   3  /3  03  13  B3  Y3  Z3  [3  \3  o3  �3  �3  �3  �3  �3  �3  �3  �3  �3         $LN101  P   Ju  Othis  X     O_Newsize  `   〃  O_Val  O  �   �           �  �'     |       � �   � �/   � �W   � ��   � ��   � �  � �  � �  � �-  	 ��  
 ��  � ��  � ��  	 ��     � F            (   
   (             �`std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >Ju   this  EN  P         ( 
 >〃   _Val  EN  `         ( 
 Z   �2                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN101  P   Ju  Nthis  X     N_Newsize  `   〃  N_Val  O  �   0           (   �'     $        �
    �    �,   8   0   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 	  8   
  8  
 ,  8   0  8  
 L  8   P  8  
   8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
    8     8  
   8     8  
 2  8   6  8  
 B  8   F  8  
 V  8   Z  8  
   8     8  
 "  8   &  8  
 K  8   O  8  
 [  8   _  8  
 ~  8   �  8  
 �  8   �  8  
 V  8   Z  8  
 j  8   n  8  
 �  8   �  8  
 �  8   �  8  
   8     8  
 5  8   9  8  
 r  8   v  8  
   8   
  8  
 '  8   +  8  
 7  8   ;  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  �   �  �  
   8     8  
 �  H   �  H  
 Z	  H   ^	  H  
 }	  H   �	  H  
 �	  �   �	  �  
 c
  �   g
  �  
 �
  H   �
  H  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   �   #   [   H塡$UVWATAUAVAWH冹0I嬸L嬺H嬮A�H�%#"勪滘薍3蠬钩     HA禓H3蠬A禓H3蠬A禓H3蠬H墧$�   L岴0I�H#蔋蒆婨H媆�L峂M�!I;躸
I嬡L塂$pM孅際H�葖;CtH;賢)H媅;Cu騃�A艶 I嬈H媆$xH兡0A_A^A]A\_^]肔嬨H岴0H塂$pL孄L峬H釜
H9E勴  L塋$ H荄$(    �   �    H孁H塂$(�塇茾    H婱�] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繦媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺勶   �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛H嬐�    H婾0L媱$�   I#蠬襀婱H婦�H媇H;胾H荄$(    � H�褘O;HtH嬝H;聇!H婡;Hu颒�H塡$ L孄L峬H岴0L嬨�-H塂$ H荄$(    L孄L峬H岴0L媎$ �
H婦$pL媱$�   H婼H�EL�'H塛H�:H墈I婱 H� I#繦繪�罫;EuH�<岭M;莡H�<岭H9T�uH墊�I�>A艶樵��H�
    �    �  �   �  ]   �  G   �      �  w   �  �      �   �
  G            �     �  �,        �std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Try_emplace<unsigned int const &> 
 >}�   this  AJ          AN       ��   >�#   _Keyval_arg  AL       ��  >� AP          AL �       >   _Target  CH      �       CI      �       CT      �     � ��  CU      �     t	 � �W  CW      �     N  H  CT     �     , � CU     �     , � >�   _Newnode  CM           B    �     , , q � � 0 M        J-  h*,'%md% >k�    _Where  AI  ~     a  
 :   AI �     9  ce 
 >簾    _End  AT  �     N 3   AT �     9  � �  >簾    _Bucket_lo  AJ  �     D    AJ �     3  R � >    _Bucket  AJ  o       M        V-  �� M        j-  �� N N N M        I-  K M        j  K M        �  K M        �  K M        @  K% M        j  >(4(4
 >#    _Val  B�   h     � AK  .     ��  � � AP      � x 
  AK �       N N N N N N M        M-  ��% M        -  � M        �-  � M        �-  � N N N M        a-  ��	 M        o-  
� M        B  
� M        
  
�
 Z   �   N N N N M        b-  �� N N M        -  ���
 Z   �   N M        K-  �� N M        -  �#D5Y >    _Newsize  AJ  0      AJ Q    V  I �  >    _Oldsize  AJ  '    	  M        -  �' N N5 M        J-  侟/,$%kd >k�    _Where  AH      b D  
 >簾    _End  AI        AI �     , w� �-  >簾    _Bucket_lo  AK  /    Q    AK K    F  -  >    _Bucket  AK         M        V-  �/ M        j-  �/ N N N M        -  k亼
 Z   -    M        -  亼B
 >   _Req_buckets  AJ  �    $  C       �      M        %-  6亼 N N N M        O-  
傄 N2 M        -  倣$$#$#d$&CJ$"E >    _Bucket_array  AJ  �    =  AJ �       >簾    _Insert_after  AK  �    S  AK �       >    _Bucket  AH  �      N 0           8         0@ 
hA   e  j  
      `  j  �  �  @  B  h  �  �   �$  y%  �%  �%  �%  �,  �,  -  -  -  -  -  -  -  -  -  -  $-  %-  --  I-  J-  K-  L-  M-  N-  O-  P-  Q-  R-  T-  V-  a-  b-  h-  i-  j-  o-  |-  }-  ~-  -  �-  �-  �-  �-  �-  �-  �-  �-  �-         $LN186  p   }�  Othis  �   �#  O_Keyval_arg      �  O_Newnode  O�   �           �  �     �       � �   � �h   � ��   � ��   � ��   � ��   � ��   � �#  � ��  � ��  � �^  � �`  � �~  � ��  � ��  � ��   L  (F                                �`std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Try_emplace<unsigned int const &>'::`1'::dtor$1                         �  O�   L  (F                                �`std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Try_emplace<unsigned int const &>'::`1'::dtor$0                         �  O,   -   0   -  
 >  -   B  -  
 N  -   R  -  
 x  -   |  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 
  -     -  
 *  -   .  -  
 B  -   F  -  
 Z  -   ^  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
   -     -  
 7  -   ;  -  
 K  -   O  -  
 |  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 r  -   v  -  
 �  -   �  -  
 �  -   �  -  
 #  -   '  -  
 F  -   J  -  
 V  -   Z  -  
 �  -   �  -  
 �  -   �  -  
 �  -   �  -  
 d  -   h  -  
 x  -   |  -  
 	  -   	  -  
 	  -   "	  -  
 F	  -   J	  -  
 V	  -   Z	  -  
 x	  -   |	  -  
 �
  �   �
  �  
   -     -  
 �  P   �  P  
 $
  J   (
  J  
 H崐    �       4   H崐    �       3   @SH冹 H嬞H呉t@3纅塁!W缊C#f塁5圕7H塁H荂   �圕 H荂$   H塁,圕4H兠8H冴u翲嬘H嬎�    H嬅H兡 [肬   .      �   �  x G            b      \   	3        �std::_Uninitialized_value_construct_n<std::allocator<nvrhi::VertexAttributeDesc> >  >�   _First  AJ        T  >#    _Count  AK        Q  >�   _Al  AP        Y  >Τ   _Backout  CI     H       CI         Q 8   M        "3  N
 Z   �2   N M        X3  - M        ~3   M        �  ! M        �  $$ N M        L  ! M        }  ! M        �  ! N N N N N N                       @ J h   e    �  �    L  }  ~  �  �  3  !3  "3  #3  X3  ~3  3   0   �  O_First  8   #   O_Count  @   �  O_Al  O�   H           b   X     <       � �	   � �   � �H   � �N   � �\   � �,   7   0   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
   7   	  7  
   7     7  
 �  7   �  7  
 @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�   `      �   �   > G                     d3        �std::_Zero_range<char *>  >e   _First  AJ          >e   _Last  AI         AK                                H 
 h   e3   0   e  O_First  8   e  O_Last  O�   8              X     ,       � �   � �   � �   � �,   =   0   =  
 e   =   i   =  
 �   =   �   =  
 �   =   �   =  
 �   =      =  
 @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�   `      �   �   F G                     �0        �std::_Zero_range<unsigned int *>  >璅   _First  AJ          >璅   _Last  AI         AK                                H 
 h   P    0   璅  O_First  8   璅  O_Last  O�   8              X     ,       � �   � �   � �   � �,   >   0   >  
 m   >   q   >  
 �   >   �   >  
 �   >   �   >  
   >     >  
 H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   3  !G            �         [-        �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >  >   _First  AJ        0  AJ b     "  >   _Last  AK          AR       } 
 >ア   _Val  AP        �  >將    _UFirst  AQ       u                        @  h   Z-  �-        O_First       O_Last      ア  O_Val  O �   X           �   �     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,   1   0   1  
 H  1   L  1  
 X  1   \  1  
 x  1   |  1  
 �  1   �  1  
 �  1   �  1  
 �  1   �  1  
 H  1   L  1  
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  /G                       Y-        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >  >   _First  AJ          AJ       
   >   _Last  AK          
 >ア   _Val  AP           >＃   _Backout  CJ            CJ          
   M        `-    N M        �-   N                        H & h   Z-  ^-  _-  `-  �-  �-  �-  �-        O_First       O_Last     ア  O_Val  O  �   H               X     <       � �    � �   � �   � �   � �   � �,   0   0   0  
 V  0   Z  0  
 f  0   j  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 �  0   �  0  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   '   %   �   ,   -      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        �  :$
 Z   `   N                       H� 
 h   �   0   2  Othis  8   7  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   '   %   �   ,   0      �   =  U G            <      6           �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        �  :$
 Z   `   N                       @�  h   �     0   I  Othis  8   N  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�   3      0      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      I  Othis  O   �   8           !        ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   '   %   �      �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   `                         H�  0   �  Othis  8   �  O_Other  O �   0           2        $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �        �nvrhi::RefCountPtr<nvrhi::IInputLayout>::~RefCountPtr<nvrhi::IInputLayout> 
 >�   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0@� 
 h   �   0   �  Othis  9       /   O�   0           "   �"     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 L  �   P  �  
 d  �   h  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >3   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0@� 
 h   �   0   3  Othis  9       /   O  �   0           "   �"     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         $        �nvrhi::RefCountPtr<nvrhi::IShaderLibrary>::~RefCountPtr<nvrhi::IShaderLibrary> 
 >wD   this  AH         AJ          AH        M          GCE
 >cD    temp  AJ  
       AJ        N (                     0H� 
 h      0   wD  Othis  9       /   O�   0           "   �"     $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 P     T    
 h     l    
 H婭H吷t
�   �    �   �      �   �  G                      -        �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > > 
 >�   this  AJ          M        --  
	 M        `  
	
 >   _Ptr  AJ         N N                        H�  h     `  --      �  Othis  O �   8              X     ,       � �    � �	   � �   � �,   3   0   3  
 )  3   -  3  
 r  3   v  3  
 �  3   �  3  
 H塡$WH冹 H孂3跦婭H吷t=H媁(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w\I嬋�    H塤H塤 H塤(H婳H婣H�H�	H吷t�     H��   �    H嬎H呟u際婳�   H媆$0H兡 _�    �    蘀   �   y   �   �   �   �   �      �   �  �G            �   
   �   a2        �std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::~_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> > 
 >}�   this  AJ        
  AM  
     � �   M        k2  U( M        �2  U	 M        �-  
	�� M        �-  	�� M        `  	�� N N N" M        \-  YG-#
 >k�   _Head  AJ  Y     
  >k�    _Pnode  AJ  c     &    >k�    _Pnext  AI  s       AI p     #    M        ]-  
s M        �-  

s M        �-  
s M        `  
s
 Z   G   N N N N N N N M        i2  H
��" M        �2  -K1$L M        5-  *~ M        `  #)Y
 Z   G  
 >   _Ptr  AJ D       >#    _Bytes  AK       �   - T " M          
,#
\
 Z   �   >    _Ptr_container  AP  0     r  Y  AP D       >    _Back_shift  AJ       � 1 Y  AJ D         N N N M        0-   N N N                       @� R h       `  -  /-  0-  5-  P-  Q-  X-  \-  ]-  �-  �-  �-  i2  k2  �2  �2         $LN93  0   }�  Othis  O   ,   #   0   #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
 &  #   *  #  
 F  #   J  #  
 g  #   k  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
   #     #  
 �  �   �  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �   V   �      �   w  TG            [      [   i2        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > > 
 >憿   this  AI  	     R K   AJ        	 " M        �2  )H1%
 M        5-  *= M        `  )
 Z   G  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M          
%#

 Z   �   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N M        0-   N N                       H� " h     `  /-  0-  5-  X-  �2         $LN30  0   憿  Othis  O �   8           [   �     ,       > �	   ? �O   D �U   ? �,   "   0   "  
 y  "   }  "  
 �  "   �  "  
   "     "  
 5  "   9  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 _  �   c  �  
 �  "   �  "  
 H婭H吷t
�   �    �   �      �   �  G                      �,        �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > > 
 >�   this  AJ          M        -   
	 M        --  
	 M        `  
	
 >   _Ptr  AJ         N N N                        H�  h     `  -  --  P-  Q-      �  Othis  O  �   (              H            L �    P �,   4   0   4  
 -  4   1  4  
 �  4   �  4  
    4     4  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �   Y   �      �   �  � G            ^      ^   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  
     Q J   AJ        
 ) M        �  ,(
	 M        �   N M        �  ,E M          &? M        `  )
 Z   G  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M          
"#
!
 Z   �   >    _Ptr_container  AP  &     7    AP :       >    _Back_shift  AJ  -     0 
   N N N N N                       H� 6 h       �  �  �  �  �  �      X  `         $LN33  0   �  Othis  O�   H           ^   �&     <       B �   C �
   B �
   C �R   J �X   C �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 ,  �   0  �  
 @  �   D  �  
 f  �   j  �  
 �  o   �  o  
   �     �  
 @WH冹 H�H孂H婤H�     H�
H吷t+H塡$0@ �     H��   �    H嬎H呟u際媆$0H��   H兡 _�    9   �   X   �      �   F  G            \      R   k2        �std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > > 
 >?�   this  AJ          AM       K   M        �2  $
 M        �-  
J
 M        �-  J
 M        `  J
 N N N" M        \-  ,K
#
 >k�   _Head  AK  	     '  AK 0     "    >k�    _Pnode  AJ       3 #   >k�    _Pnext  AI  3       AI 0       M        ]-  
3 M        �-  

3 M        �-  
3 M        `  
3
 Z   G   N N N N N N                       H� 6 h       `  -  P-  Q-  \-  ]-  �-  �-  �-  �2   0   ?�  Othis  O  �   H           \   H     <        �    �	    �    �R    �W    �,      0     
 1     5    
 A     E    
 �     �    
          
 0     4    
 U     Y    
 e     i    
 \     `    
 @SH冹 H嬞H�	H吷t>H婼H+袶冣郒侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �   V   �      �   �  � G            [      [   m2        �std::vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> >::~vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> > 
 >�   this  AI  	     R K   AJ        	 $ M        �2  	h1%	
 M        �2  *= M        `  )
 Z   G  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M          
%#

 Z   �   >    _Ptr_container  AP  )     1    AP =       >    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� " h       `  �2  �2  �2  �2         $LN28  0   �  Othis  O  �   8           [   �'     ,       � �	   � �O    �U   � �,      0     
 �      �     
 �      �     
 �     �    
 �     �    
      
    
          
 @     D    
 T     X    
 �  �   �  �  
 �     �    
 @SH冹 H嬞H�	H吷thH婼L嬅�    H�H�%I�$I�$IH婼H+袶麝H龙H嬄H凌?H蠬k�8H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �   .   h   �   �   �      �   �  � G            �      �   s2        �std::vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> >::~vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> > 
 >坤   this  AI  	     | u   AJ        	 & M        �2  	(LO%	

 Z   �2   M        �2  *B= M        `  F)
 Z   G  
 >   _Ptr  AJ g       >#    _Bytes  AK  F     > &  " M          
O#

 Z   �   >    _Ptr_container  AP  S     1    AP g       >    _Back_shift  AJ        d G   AJ g       N N N N                       @�  h       `  �2  �2  �2         $LN28  0   坤  Othis  O�   8           �   �'     ,       � �	   � �y    �   � �,      0     
 �      �     
          
 �     �    
 �     �    
          
 ,     0    
 R     V    
 f     j    
 �  �   �  �  
 �     �    
 @SH冹 H嬞H�	H吷t\H婼H浮隊隊H+袶麝H龙H嬄H凌?H蠬i�  H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    蘚   �   t   �      �   �  � G            y      y   }2        �std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> >::~vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> > 
 >Ju   this  AI  	     p i   AJ        	 $ M        �2  	hO%	
 M        �2  -3@ M        `  :)
 Z   G  
 >   _Ptr  AJ [       >#    _Bytes  AK  :     > &  " M          
C#

 Z   �   >    _Ptr_container  AP  G     1    AP [       >    _Back_shift  AJ       l O   AJ [       
  N N N N                       @� " h       `  �2  �2  �2  �2         $LN28  0   Ju  Othis  O  �   8           y   �'     ,       � �	   � �m    �s   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 p  �   t  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 (  �   ,  �  
 <  �   @  �  
 �  �   �  �  
 �  �   �  �  
 �     �   �   B G                       =        �nvrhi::IResource::~IResource 
 >"   this  AJ          D                           H�     "  Othis  O�                  �"            i  �,   �   0   �  
 g   �   k   �  
 �   �   �   �  
 @SH冹 H婹@H嬞H凓v-H婭(H�翲侜   rL婣鳫兟'I+菻岮鳫凐wmI嬋�    H荂8    H荂@   艭( H婼 H凓v-H婯H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂    H荂    艭 H兡 [描    �<   �   �   �   �   �      �   n  D G            �      �   ?        �nvrhi::ShaderDesc::~ShaderDesc 
 >�   this  AI  
     � �   AJ        
  M        �  KTQ& M        �  T
-(

 M        �  T N M        �  -^G M          ^&@ M        `  e)
 Z   G  
 >   _Ptr  AJ  b     )  
  >#    _Bytes  AK  e     &  AK �      " M          
n#
"
 Z   �   >    _Ptr_container  AP  r       AP �     $    >    _Back_shift  AJ  y     
  AJ �       N N N N N N M        �  G$ M        �  -( M        �   N M        �  - M          & M        `  )
 Z   G  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       &  AK �       M          
##
 >    _Ptr_container  AP  '       AP ;     o  e  >    _Back_shift  AJ  .     
  AJ �       N N N N N N                       @� : h
       �  �  �  �  �  �  �      X  `         $LN70  0   �  Othis  O  ,   �   0   �  
 i   �   m   �  
 }   �   �   �  
 T  �   X  �  
 y  �   }  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �      �  
 ,  �   0  �  
   �     �  
 6  �   :  �  
 F  �   J  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 V  t   Z  t  
 H�    H�H兞�       '      �      �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        �   	
 N                        H�  h   �  �      I  Othis  O ,   �   0   �  
 {   �      �  
 H�    H�H兞�       '      �      �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   2                          H�     �  Othis  O  �   (                          Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   j G            !         �!        �nvrhi::RefCounter<nvrhi::IInputLayout>::`scalar deleting destructor' 
 >QU   this  AI  	       AJ        	                        @� 
 h   \2   0   QU  Othis  O,      0     
 �      �     
 �      �     
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   e G            !         8        �nvrhi::RefCounter<nvrhi::IShader>::`scalar deleting destructor' 
 >鐲   this  AI  	       AJ        	                        @� 
 h   9   0   鐲  Othis  O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   l G            !         �!        �nvrhi::RefCounter<nvrhi::IShaderLibrary>::`scalar deleting destructor' 
 >籙   this  AI  	       AJ        	                        @� 
 h   T2   0   籙  Othis  O  ,      0     
 �      �     
 �      �     
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   W G            !         ^2        �nvrhi::IInputLayout::`scalar deleting destructor' 
 >   this  AI  	       AJ        	                        @� 
 h   ]2   0     Othis  O   ,   �   0   �  
 |   �   �   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   T G            !         :        �nvrhi::IResource::`scalar deleting destructor' 
 >"   this  AI  	       AJ        	                        @� 
 h   =   0   "  Othis  O  ,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   R G            !         <        �nvrhi::IShader::`scalar deleting destructor' 
 >N   this  AI  	       AJ        	                        @� 
 h   ;   0   N  Othis  O,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   Y G            !         V2        �nvrhi::IShaderLibrary::`scalar deleting destructor' 
 >揇   this  AI  	       AJ        	                        @� 
 h   U2   0   揇  Othis  O ,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
 H塡$WH冹 H嬞孃H兞@�    H婯(H吷t?H婼8H+袶冣郒侜   rL婣鳫兟'I+菻岮鳫凐w@I嬋�    3繦塁(H塁0H塁8H岾�    @銮t
簚   H嬎�    H嬅H媆$0H兡 _描    �   #   N   �   e      x   �   �   �      �   �  ] G            �   
   �   Z2        �nvrhi::d3d12::InputLayout::`scalar deleting destructor' 
 >�   this  AI  
     � w   AJ        
  M        m2  Hr$ M        �2  i1&	. M        �2  *(b M        `  ,)=
 Z   G  
 >   _Ptr  AJ M       >#    _Bytes  AK  %     j   - 8 " M          
5#
@
 Z   �   >    _Ptr_container  AP  9     V  =  AP M       >    _Back_shift  AJ       s 1 =  AJ M         N N N N N                       @� 2 h       `  [2  \2  _2  m2  �2  �2  �2  �2         $LN40  0   �  Othis  O   ,   &   0   &  
 �   &   �   &  
 �   &   �   &  
 7  &   ;  &  
 X  &   \  &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
   &     &  
 }  �   �  �  
 H塡$H塼$WH冹 H嬞3鯤媺�   孃H吷tMH嫇�   H+袶冣麳侜   rL婣鳫兟'I+菻岮鳫凐�  I嬋�    H壋�   H壋�   H壋�   H崑�   �    H媼�   H吷tMH嫇�   H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐嚌   I嬋�    H壋�   H壋�   H壋�   H媼�   H吷tEH嫇�   H+袶侜   rL婣鳫兟'I+菻岮鳫凐wLI嬋�    H壋�   H壋�   H壋�   H岾�    @銮t
亨   H嬎�    H媡$8H嬅H媆$0H兡 _描    蘓   �   w   �   �   �     �   *  �   =  �   U  �      �   �  X G            Z     Z  M2        �nvrhi::d3d12::Shader::`scalar deleting destructor' 
 >魿   this  AI       H<  AJ          M        �2  Q����% M        �2  ��l0'	6 M          &��j >   _Count  AK  �       AK       M        `  ��)I
 Z   G  
 >   _Ptr  AJ       >#    _Bytes  AK  �     $    AK     N  D # M          
��#
L
 Z   �   >    _Ptr_container  AP  �       AP     N  D  >    _Back_shift  AJ  �     0  AJ     N    +  N N N N N M        �2  Y{ M        �2  {l8' M        �2  .�� M        `  ��)
 Z   G  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     1    AK T      M          
��# >    _Ptr_container  AP  �       AP �     �  �  >    _Back_shift  AJ  �     8  AJ �     �   ! y  N N N N N M        >  	R" M           	e8' M        �  ., M        `  0)
 Z   G  
 >   _Ptr  AJ U       >#    _Bytes  AK  )     1    AK T      M          
9# >    _Ptr_container  AP  =       AP U      �  >    _Back_shift  AJ       :  AJ U       ! �  N N N N N                       @� V h         `  9  >     �  �  �  N2  �2  �2  �2  �2  �2  �2  �2  �2  �2         $LN91  0   魿  Othis  O,      0     
 }      �     
 �      �     
          
           
 h     l    
 �     �    
 �     �    
 �     �    
          
 5     9    
 E     I    
 �     �    
          
 )     -    
 r     v    
 �     �    
 �     �    
 �     �    
 o     s    
 �     �    
 �     �    
 �     �    
 �          
 &     *    
 6     :    
 �  �   �  �  
 H塡$WH冹 H嬞孃H婭H吷t;H婼 H+袶侜   rL婣鳫兟'I+菻岮鳫凐w7I嬋�    3繦塁H塁H塁 @銮t
�(   H嬎�    H嬅H媆$0H兡 _描    藺   �   b   �   u   �      �   �  _ G            z   
   z   R2        �nvrhi::d3d12::ShaderLibrary::`scalar deleting destructor' 
 >贡   this  AI  
     m a   AJ        
  M        �2  De$ M        �2  i-&	% M          &U >   _Count  AK         AK @       M        `  )4
 Z   G  
 >   _Ptr  AJ @       >#    _Bytes  AK       ]   $ 4  AK @      " M          
(#
7
 Z   �   >    _Ptr_container  AP  ,     M  4  AP @       >    _Back_shift  AJ       f - 4  AJ @     4   !   N N N N N                       @� . h
         `  S2  T2  �2  �2  �2  �2         $LN37  0   贡  Othis  O,      0     
 �      �     
 �      �     
          
      #    
 j     n    
 �     �    
 �     �    
 �          
          
 6     :    
 J     N    
 �  �   �  �  
 H塡$WH冹 孃H嬞H媺�   H吷tH莾�       H��P怘岾�    @銮t
簣   H嬎�    H嬅H媆$0H兡 _�2   �   E   �      �   Y  d G            W   
   L   6        �nvrhi::d3d12::ShaderLibraryEntry::`scalar deleting destructor' 
 >aD   this  AI       B  AJ          M        $   M          OGE
 >cD    temp  AJ         AJ -       N N                      0@�  h   7  9  $     0   aD  Othis  9)       /   O   ,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 U     Y    
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   '      �   0   �      �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        �  

	
 Z   2   N                       @�  h   �  �   0   2  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   '      �   0   �      �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        �  

	
 Z   2   N                       @�  h   �  �  �   0   I  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   '      �   0   �      �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   2   N                       @� 
 h   �   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >U   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   U  O__f  9(       U   O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 �   �罙�烂   �   �   T G            
          v2        �nvrhi::RefCounter<nvrhi::IInputLayout>::AddRef 
 >QU   this  AJ        
  M        m    N                        @  h   m  �      QU  Othis  O �   0           
   �"     $       x �    y �   z �,      0     
 y      }     
 �      �     
 �   �罙�烂   �   �   O G            
          &        �nvrhi::RefCounter<nvrhi::IShader>::AddRef 
 >鐲   this  AJ        
  M        m    N                        @  h   m  �      鐲  Othis  O  �   0           
   �"     $       x �    y �   z �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �罙�烂   �   �   V G            
          x2        �nvrhi::RefCounter<nvrhi::IShaderLibrary>::AddRef 
 >籙   this  AJ        
  M        m    N                        @  h   m  �      籙  Othis  O   �   0           
   �"     $       x �    y �   z �,   
   0   
  
 {   
      
  
 �   
   �   
  
 @SH冹 �����罽冸uH吷t	L�峉A�嬅H兡 [�   �     U G            +      %   u2        �nvrhi::RefCounter<nvrhi::IInputLayout>::Release 
 >QU   this  AJ        #  AJ #       >"     result  A          M        l  
 N                       @  h   l  �   0   QU  Othis  9        VU   O   �   @           +   �"     4       } �   ~ �    �   � �#   � �,      0     
 z      ~     
 �      �     
 �      �     
          
 ,     0    
 @SH冹 �����罽冸uH吷t	L�峉A�嬅H兡 [�   �     P G            +      %   %        �nvrhi::RefCounter<nvrhi::IShader>::Release 
 >鐲   this  AJ        #  AJ #       >"     result  A          M        l  
 N                       @  h   l  �   0   鐲  Othis  9        鞢   O�   @           +   �"     4       } �   ~ �    �   � �#   � �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 $  �   (  �  
 @SH冹 �����罽冸uH吷t	L�峉A�嬅H兡 [�   �     W G            +      %   w2        �nvrhi::RefCounter<nvrhi::IShaderLibrary>::Release 
 >籙   this  AJ        #  AJ #       >"     result  A          M        l  
 N                       @  h   l  �   0   籙  Othis  9        繳   O �   @           +   �"     4       } �   ~ �    �   � �#   � �,      0     
 |      �     
 �      �     
 �      �     
          
 ,     0    
 H塡$ L塂$AVH冹 L嬹I嬝H婭H嬃M�I+罤柳H;�冣   H�������H塼$0H墊$8H;�囜   H�4�    H侢   r)H峃'H;�喢   �    H吚劵   H峹'H冪郒塆H咑t
H嬑�    H孁�3�I�I婩H+罤柳H吚t.H��    H侜   rL婣鳫兟'I+菻岮鳫凐w`I嬋�    H�>I�>I塅I塅H;鴗H�H兦H;鴘鬑媡$0H媩$8H媆$HH兡 A^肏嬔L岲$@I嬌�    H媆$HH兡 A^描    惕    蘬   �   �   �   �   �     1   /  �   5  �      �   �  � G            :     :  "-        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Assign_grow 
 >憿   this  AJ          AV       '�    >   _Cells  AK        3p  �  � w   AK �     w  & 
 >辎   _Val  AI       $�    AP          D@    >    _Oldsize  AH  '     �  �  >    _Newend  AH  �     2  >    _Oldcapacity  AH  �     ,    AH �     	  >    _Newvec  AM  �       AM �     � \  k .  M        1-   N M        0-  �� N M        4-  
0W��% M        B  U)
)%
��' M        h  ^$	%)
��
 Z      >    _Block_size  AJ  b       AJ .      >    _Ptr_container  AH  p       AH �     �  � 
 >0    _Ptr  AM  �       AM �     � \  k .  M        
  k
 Z   �   N N M        
  ��
 Z   �   N N M           

0
	 N N M        Y-  ��#" >＃   _Backout  CM     �       CM    �         M        `-  �� N M        �-  �� N N M        5-  .���� M        `  ��)]
 Z   G  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     &  AK 4     # M          
��#
`
 Z   �   >    _Ptr_container  AP  �       AP �     b  X  >    _Back_shift  AJ  �     9  AJ �     b ; "  N N N
 Z   [-                         @ Z h   
    `  B  h     /-  0-  1-  4-  5-  X-  Y-  Z-  ^-  _-  `-  �-  �-  �-  �-         $LN82  0   憿  Othis  8     O_Cells  @   辎  O_Val  O �   �           :  �     �        �    �'   ! �0   " ��   $ ��   % ��   ' ��   + ��   , ��   - ��   . �  2 �  0 �"  2 �.  " �4  ' �,   !   0   !  
 �   !   �   !  
   !     !  
 *  !   .  !  
 J  !   N  !  
 m  !   q  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  !     !  
   !     !  
 3  !   7  !  
 C  !   G  !  
   !      !  
 ,  !   0  !  
 U  !   Y  !  
 e  !   i  !  
 �  !   �  !  
 �  !   �  !  
 \  !   `  !  
 p  !   t  !  
   !     !  
 5  !   9  !  
 E  !   I  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  �   �  �  
 �  !   �  !  
 H塡$H塴$H塼$WH冹 H嬞I嬹H�	I嬭H孃H吷t1H婼H+袶冣麳侜   rL婣鳫兟'I+菻岮鳫凐w0I嬋�    H�疕�;H媗$8H塁H�稨媡$@H塁H媆$0H兡 _描    蘒   �   �   �      �   6  l G            �      �            �std::vector<unsigned int,std::allocator<unsigned int> >::_Change_array 
 >[F   this  AI       p d   AJ          >璅   _Newvec  AK        #  AM  #     d ]   >   _Newsize  AN        g E   AP           >   _Newcapacity  AL       m X   AQ          M        �  */R M        `  3)-
 Z   G  
 >   _Ptr  AJ T       >#    _Bytes  AK  ,     Z   - ( " M          
<#
0
 Z   �   >    _Ptr_container  AP  @     F  -  AP T       >    _Back_shift  AJ       i 7 -  AJ T     -  N N N                       @  h       `  �  �  �         $LN25  0   [F  Othis  8   璅  O_Newvec  @     O_Newsize  H     O_Newcapacity  O  �   H           �   �'     <       � �   � �(   � �Y   � �`   � ��   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
   �     �  
 9  �   =  �  
 M  �   Q  �  
 �  �   �  �  
 �  �   �  �  
 2  �   6  �  
 F  �   J  �  
 l  �   p  �  
 �  �   �  �  
 �  |   �  |  
 L  �   P  �  
 H塼$ WH冹 H�������H嬹H饺�   嬊H余H;�噥  H岯�H塡$0H媈H荋饺L嬅L塼$@�罤隅H峃H�?�    H岹�H墌8H塅0H婩H� H嬋H;�匇   H塴$8H砍     H�%#"勪滘�@ �     禤D禭H�	L3軱L3�禤LL3�禤LL3贚L#^0I零L^M�L;藆	I�I塁雟I婼D婡D;Bu/L�L;衪!L婬I�	L婣M�I婻H�M塀L塈H塒I塁�9L;蕋怘婻D;BtOL;蕌馤婸I�
L婭I�L婤I� L塉L塓L堾I�H嬃H;��1���H媗$8H媆$0L媡$@H媡$HH兡 _肔�L婬I�	L婣M�I婻H�M塀L塈H塒氪H�
    �    蘕   !   �  z   �  �      �   �	  G            �  
   �  -        �std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Forced_rehash 
 >}�   this  AJ          AL       �l  >#   	 _Buckets  AK        �O ` AM  O     ;  AM t      C             /  C      �    
  >    _Max_storage_buckets  AH  %     �
 }
 >辎    _End  AI  ;     t>  >辎    _Inserted  AH  f      AH �     �   >辎    _Next_inserted  AJ  r     = >あ    _Bucket_lo  AS  �     � �   AS �     � 	 �  >    _Bucket  AS  �       M          "


 N M        (-  h M        9-  h M        <-  l N N N M        '-  7 M        8-  7 M        <-  7 N N N M        �$  .
 M          ;  >#    _Value  AH  2     *  N N M        -  r�� M        -  r�� N N M        &-  	��( M        I-  	��$ M        j  	��$ M        �  	��$ M        �  	��$ M        @  	��$ M        j  	��!
 >#    _Val  AS  �     %  N N N N N N N M        -  �� M         -  �� N N M        -  �� N M        V-  �� M        j-  �� N N M         -  �� N& M        )-  �$#$#$c$ >簾    _Before_prev  AK        AK �     �  �  >簾    _Last_prev  AP        AP �     � U n  >簾    _First_prev  AQ      #  AQ �     � ? �  N M        -  �* N& M        )-  �?$#$#$c$ >簾    _Before_prev  AP  Q      AP �     � U n  >簾    _Last_prev  AQ  J      AQ �     � ? �  >簾    _First_prev  AR  C       AR �     � ^ , �    N M        V-  �4 M        j-  �4 N N M        -  �0 N& M        )-  亴$#$#$c$ >簾   _First  AR  �    #  AR �     � ^ , �    >簾    _Before_prev  AK  �      AK �     �  �  >簾    _Last_prev  AP  �      AP �     � U n  >簾    _First_prev  AQ  �      AQ �     � ? �  N Z   "-  �                         @ � h.   j    `  j  �  �  @  �    �$  -  -  -  -  -  -  -  -  -   -  !-  #-  &-  '-  (-  )-  6-  8-  9-  ;-  <-  >-  B-  I-  N-  P-  Q-  V-  \-  ]-  h-  i-  j-  �-  �-  �-         $LN185  0   }�  Othis  8   #   O_Buckets  O  �   X          �  �  (   L      � �
   � �   � �   � �   � �%   � �.   � �7   � �;   � �B   � �J   � �O   � �\   � �`   � �h   � �o   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �  � �$  � �(  � �*  � �0  � �:  � �?  � �`  � �c  � �~   ��  � ��  � �,       0      
 *      .     
 :      >     
 a      e     
 u      y     
 �      �     
 �      �     
 �      �     
 �      �     
 �           
 '      +     
 7      ;     
 d      h     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
            
 *      .     
 T      X     
 d      h     
 �      �     
 �      �     
            
 !      %     
 K      O     
 [      _     
 �           
            
 =      A     
 M      Q     
 v      z     
 �      �     
 �      �     
 �      �     
 �	  �   �	  �  
 
      
     
 H冹HH峀$ �    H�    H峀$ �    �
   �      6      [      �   �   F G                               坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �$            J �   K �,   �   0   �  
 �   i   �   i  
 �   �   �   �  
 H冹(H�
    �    �   O      �      �   w   7 G                             坰td::_Xlen_string 
 Z   �   (                      @        $LN3  O �   (              �&            		 �   
	 �,   �   0   �  
 s   k   w   k  
 �   �   �   �  
 H冹(H�
    �    �   t      �      �   �   W G                     73        坰td::vector<char,std::allocator<char> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (              �'            a �   b �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H�
    �    �   t      �      �   �   g G                              坰td::vector<unsigned int,std::allocator<unsigned int> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (              �'            a �   b �,   �   0   �  
 �   ~   �   ~  
 �   �   �   �  
 H冹(H�
    �    �   t      �      �   �   � G                     33        坰td::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �'            a �   b �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H�
    �    �   t      �      �   �    G                     &3        坰td::vector<D3D12_INPUT_ELEMENT_DESC,std::allocator<D3D12_INPUT_ELEMENT_DESC> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (              �'            a �   b �,      0     
 �   �   �   �  
 �      �     
 H冹(H�
    �    �   t      �      �   �   � G                     *3        坰td::vector<nvrhi::VertexAttributeDesc,std::allocator<nvrhi::VertexAttributeDesc> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (              �'            a �   b �,      0     
 �   �   �   �  
 �      �     
 H冹(H�
    �    �   t      �      �   �   u G                     .3        坰td::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �'            a �   b �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   _   �   �   �   �   �   ^   ,  �   O  �   U  �   [  �      �   '  r G            `     `  �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >h   _Ptr  AK          AW       D/  >   _Count  AL       G4  AP         B M        �2  E
(?SD3$--K
 Z      >#     _New_capacity  AH  �     �  * N  V r  AM  O     =  b �  AH �     G  ,  AJ �       M        �2  �� M           �� N N M        �  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        5  ��?�� M        W  ��?�� >   _Count  AJ  �      * M        B  ��

*%
u- M        h  ��	)
��
 Z      >    _Block_size  AJ  �     �  �  AJ �       >    _Ptr_container  AH  �       AH �     }  b 
 >0    _Ptr  AV  �       AV �     ~ V "  M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N N N N M        {  X(  M        �  X' >    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M          
~ N N N M        �  -�W M          �&P M        `  �
)/
 Z   G  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M          
�#
2
 Z   �   >    _Ptr_container  AP        AP +    4  *  >    _Back_shift  AJ      
  AJ Z      N N N N N M          L4 N M        �  $# >p    _Result  AM  '       AM 8      M        �  ' N N                       @ n h   
      �  �  �  �            [  `  {  |  �  �  �  5  B  W  h  o  �2  �2         $LN93  @   �  Othis  H   h  O_Ptr  P     O_Count � �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_66f57f934f28d61049862f64df852ff0>  O �   h           `  �&  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ]  �   a  �  
 m  �   q  �  
 �  �   �  �  
 Y  �   ]  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 W  �   [  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 0  �   4  �  
 �  �   �  �  
 �  �   �  �  
 a  q   e  q  
 <  �   @  �  
 H塡$H塴$H塼$ H塗$WATAUAVAWH冹`A孂M嬸H嬯E3�D墊$$箑   �    H嬸H塂$(H吚劏   W� @@ @0@@@P@`@p茾   H�    H�L墌L墌L墌 L墌(L墌0L墌8H峖@H塡$0D�;L墈L墈A峅�    H� H堾H塁H岾L�9L墆L墆H荂0   H荂8   �  �?L婥A峎�    愲I嬿L崀L嬶M婳M�I嬌I+蔍�%I�$I�$II嬅H鏖L嬄I柳I嬂H凌?L繧;鴖Ik�8I贛嬊I嬔H嬎�    I塤隖vDI婳I+蔍嬅H鏖H龙H嬄H凌?H蠰;闕嬚vL岲$ I嬒�    �I+蠱嬊I嬌�    I塆�勥  I峮H抢���I+艸塂$(f怘�<(I?H峌鐷;鷗H儅 vH�L婨鳫嬒�    禡圤 婨塆$婨塆(婨塆,婨塆0禘圙4�    L孁禣 �    L嬥E3葾嬞D9O$啍   H嬊H�vH�H塂$@A禗$G,塂$TA婫塂$L婫(塂$P塡$H�4 t荄$X   荄$\   �	H荄$X    H媀0H;V8tD$@L$PJH僃0 �L岲$@H峃(�    E3��;_$俵���禣(H�%#"勪滘薍3菼撼     I禛)H3菼禛*H3菼禛+H3菼H媀pH#袶襀婲XH婦�L婩HI;纓 H�褘O(;Ht@ H;聇H婡;Hu螂I嬃H吚LE繪;FHu媉0H峃@L岹(H峊$0�    H�塝H兣8I冺L崀H婦$(�>��H嫭$�   H塽 H嬇L峔$`I媅0I媖@I媠HI嬨A_A^A]A\_�8   �   y   q   �   �   �   !   C  .   {  6   �  7   �  �   �  �   	  �   �  5   Q  -      �   �  M G            �  !   �  �0        �nvrhi::d3d12::Device::createInputLayout 
 >漅   this  AJ        7  D�   
 >'   d  AP        '  AV  '     r >u    attributeCount  A   $     � Ai        $  A  �    � � >�   vertexShader  EO  (           D�    >鼫    layout  AL  ?     S B(   D     [j� >G�    formatInfo  AT      j AT �    �` j >�%    formatMapping  AW      c
 >�    attr  AM  �    � AM �    � � >u     semanticIndex  A       - A  �    �f E
 >迒    desc  D@    M        r2  �攢� >   _Newsize  AU  �     �  AU �    �� 0 M        �2  ��.%GN$b"(- Z   �2  3  	3   >�    _Al  AW  �     �c >    _Oldsize  AP        >�    _Newlast  AI  9      AI �    �� d N N M        h2  Q��
 >頍   this  AI  �       B0   �     �T   M        �2  ��.H

 Z   "-   M        �2  �� M        3  �� M        $3  �� N N N M        �2  �� M        �2  ��)# >k�    _Newhead  AH  �     =  M        o-  	�� M        B  	�� M        
  	��
 Z   �   N N N N M        �2  �� M        �2  �� N N N M        �2  �� N N N M        n2  �� M        �2  �� M        �2  �� N N N M        t2  �� M        �2  �� M        �2  �� N N N M        �  o N M        �  伔L	
 Z   �   M        �  伬 >_    _Result  AK  �      N N M        p2  仜 N M        �  
�  M        �  � # >_    _Result  AH  #      N N M        l2  3倂 M        �2  
倂*
 Z   3   M        3  個 M        O3  個 N N N N M        �2  h偞l M        3  
(傯,
/ M        J-  傯',$ >k�    _Where  AH      ,  AH 3    9 "   >簾    _Bucket_lo  AK        AK �    � u�  >    _Bucket  AK  �      M        V-  � M        j-  � N N N N M        I-  @偞 M        j  @偞 M        �  @偞 M        �  @偞 M        @  @偞& M        j  偞>'4'4
 >#    _Val  AJ  �    =  N N N N N N N M        �,  
僈
 Z   �,   N M        �2  儂 M        �2  儂 N N Z   �  m  �*   `           (         @ �hc   e  j  
        �  �  �  �  �  �  H  I  [  j  �  �  @  B  h  �  1  2  3  4  �  �  �  �,  �,  -  8-  ;-  <-  I-  J-  N-  V-  h-  i-  j-  o-  �-  W2  X2  Y2  `2  g2  h2  j2  l2  n2  p2  r2  t2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  3  3  3  3  3  3  3  3  $3  %3  N3  O3  w3   �   漅  Othis  �   '  Od  �   u   OattributeCount  �   �  OvertexShader  @   迒  Odesc  ^7      麥   O  �   �           �       �       �  �2   �  ��   �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �    �2   �B   �J   �Q   �U   �[  
 �c   �k   �m   �v   ��  �  ��   �@   �[  �  �z   ��   ��   �   \ F                               �`nvrhi::d3d12::Device::createInputLayout'::`1'::dtor$0 
 >漅   this  EN  �          
 >迒    desc  EN  @                                 �  O  �   �   \ F                                �`nvrhi::d3d12::Device::createInputLayout'::`1'::dtor$2 
 >漅   this  EN  �          
 >迒    desc  EN  @                                  �  O  �   �   \ F                                �`nvrhi::d3d12::Device::createInputLayout'::`1'::dtor$3 
 >漅   this  EN  �          
 >迒    desc  EN  @                                  �  O  �   �   \ F                                �`nvrhi::d3d12::Device::createInputLayout'::`1'::dtor$8 
 >漅   this  EN  �          
 >迒    desc  EN  @                                  �  O  �   �   \ F                                �`nvrhi::d3d12::Device::createInputLayout'::`1'::dtor$9 
 >漅   this  EN  �          
 >迒    desc  EN  @                                  �  O  �   �   ] F            )      #             �`nvrhi::d3d12::Device::createInputLayout'::`1'::dtor$10 
 >漅   this  EN  �         # 
 >迒    desc  EN  @         #                        �  O ,   ,   0   ,  
 r   ,   v   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
   ,   "  ,  
 G  ,   K  ,  
 W  ,   [  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
   ,     ,  
 '  ,   +  ,  
 |  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
   ,     ,  
 >  ,   B  ,  
 N  ,   R  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 l  ,   p  ,  
 '  ,   +  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 #  ,   '  ,  
 	  ,   	  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  K   �  K  
 M
  K   Q
  K  
 p
  K   t
  K  
 �
  R   �
  R  
   R   !  R  
 @  R   D  R  
 �  T   �  T  
 �  T   �  T  
   T     T  
 d  X   h  X  
 �  X   �  X  
 �  X   �  X  
 4  Y   8  Y  
 �  Y   �  Y  
 �  Y   �  Y  
   M     M  
 ^  M   b  M  
 �  M   �  M  
 @UH冹 H嬯簚   H婱(�    H兡 ]�   �   H媻(   H兞�          H媻(   H兞(�          H媻0   H兞�          H媻0   H兞�       "   @UH冹 H嬯婨$冟吚t僥$鼿媿�   �    H兡 ]�   �   H塡$H塴$H塼$ WATAUAVAWH冹0I嬮M嬸L嬯3�墊$$H嫓$�   H呟uH�:楹  灌   �    H嬸H墑$�   H吚勚   3褹膏   H嬋�    荈   H�    H�H岶H墑$�   f�8W�@H墄H茾    艪 @(H茾8   H茾@   茾(main艪, 茾H����艪L 墄PH墄X艪` H墄hH壘�   H壘�   H壘�   H壘�   H壘�   H壘�   H壘�   H壘�   H壘�   H壘�   H壘�   H壘�   �H嬿H嬘H崕�   �    A�f塅I峍H峃H;蕋L婤H儂vH��    I峍(H峃8H;蕋L婤H儂vH��    A婩H塅XA禙L團\A婩P塅`I婩XH塅hA禙`團pI婩hH塅xL嬅H嬚H嫀�   �    E媐PD塪$ E呬凾  I媈XH墱$�   H呟�?  L嵕�   E嬏I媜M�H嬐I+蔍弧隊隊I嬅H鏖L嬄I柳I嬂H凌?L繫;鄐Ii�  I翴塆隷v]I婳I+蔍嬅H鏖H龙H嬄H凌?H蠰;蕍L崉$�   I嬔I嬒�    �(M+萾Ii�  L嬅3襀嬐�    H際嫓$�   I塷嬶E呬剤   L嬬H兠M�?M麬� I壙  A壙  A壙  L嬅H儃vL�I峅L婯�   �    婯鴧蓆4冮t
凒u*岮��   A塆�臜兠(I伳  ;l$ sL嵕�   雱�    I儈h 剺   L嵕�   I媜I�H嬐H+蔋六H凒vH岯@隟sMI婫H+翲柳H凐sL崉$�   �   I嬒�    �%�   H+貶��    L嬅3襀嬐�    H�+I塆I�I婩h HI@ A H0I0A儈H |b�   �    H墄�   茾   茾  A婲H塇H崕�   H墑$�   H嫋�   H;枿   t
H�H儐�   �
L崉$�   �    A�=   囲  �  =�   圇  H�    秳    媽�    H�酇儈P tt�(   �    H墄H墄H墄墄$�   茾   茾(  H嬓A婲P塇H媶�   H塀A禙L塀 H崕�   H墧$�   H媶�   H;啫   t/H�H儐�   I塽 I嬇H媆$`H媗$pH媡$xH兡0A_A^A]A\_肔崉$�   H嬓�    胧A儈P t霉(   �    H墄H墄H墄墄$�   茾   茾(  镴���A鯢`uA儈P uI儈h 剎���笻   �    H嬓H墄H墄H墄H墄$H墄,H墄4H墄<墄D�   茾   茾H  A婲P塇0A儈P t	H媶�   �H嬊H塀8嬊I9Fh暲塀 I儈h tH嬀�   H墇(A禙`冟塀A禙`谚冟塀A禙`凌冟塀A禙`凌冟塀A禙L塀D閼��=   t#=   t=   t=   t�    I墋 闀��A儈P 剢���    I墋 閨��f�                         F   �   j   `   x   b   B  �   j  �   �  �   �  ^   s  8   �  `   �  �   0  �   �  ;   �  `   �  �   <  9   c  D   k  �   r  �   �  �   &  9   9  �   �  �   X  �   q  �   �  �   �  �   �  �   �  �   �  �   �  �      �   �  H G            x     x  �0        �nvrhi::d3d12::Device::createShader 
 >漅   this  AJ        E  AJ �       D`   
 >   d  AP        "  AV  "     \�  AV �    �  >P   binary  AN       � AQ          AN 4    D � � J  >   binarySize  AI  3     � EO  (           B�         U � >▏    shader  AL  M     1�   AL �    ~ t& B�   U     #6 � �H�* � � )  >П    pExtn  AH  �    X  AH @      >    pExtn  AH  �    #  AK  �      AH �      AK �    Y  >    pExtn  AH  =    (  AH �      >幢    pExtn  AK  �    �  AK �    Y  M        ?  � M        �  � M        C  � N N N M        ~2  � M        �2  � M        �2  � N N N M        �2  �� M        �2  �� M        �2  �� N N N M        �2  �� M        �2  �� M        �2  �� N N N M        �  �� M        N  &��( M           �� N N M        L  �� M        }  �� M        �  �� N N N N M        �  �� M        �  ��$ N M        L  �� M        }  �� M        �  �� N N N N M        �  n N M        �  乿L
 Z   �  
 >�   this  AJ  v      AJ �    ;  >�   _Right  AK  r      AK �    9  M        �  � >_    _Result  AK �      N N M        �  乂L
 Z   �  
 >�   this  AJ  V      AJ n      >�   _Right  AK  R      AK i    	  M        �  乢 >_    _Result  AK i      N N M        �2  8 NB M        J2  �檨���FG''G#!2 
 Z   �!   >u    numSemantics  B    �    ��  Al  �    �  Al 4    D� J  >|�   semantics  B�   �    �** � . �  AI  �    � �   AI 4    D^  � J  >_u   output  AW  �    3� w  AW 4    D � � J 
 >u     i  A   �    �  A  4    D � � J  >?u    dst  AW  �    { t   AW 4    D � � J  M        |2  �杹��� >   _Newsize  AQ  �    } x  6 AQ �    �I J �  	 GX �j y �( �� �	 � 5 M        �2  �.%
$b"%$
 Z   
3   >    _Oldsize  AP        >瀠    _Newlast  AH  <     " AH �    �S @ �  � I  GX  M        3  倈 >#    _Count  AQ  |     6 AQ �    �M J �  
 KX �j } �( �� �	 �  N N N M        �2  傛
 Z   �3   N M        �  
傉 M        �  傉# >_    _Result  AP  �      N N M        {2  偝 N N M        �(  儺 N M        �2  h僃. M        �2  僃&
b"+&!
 Z   3   >>?    _Al  AW  F    �  AW �    �; �  >    _Oldsize  AJ  P    U   6   AJ �      >璅    _Newlast  AH  a      AH �      >    _Oldcapacity  AH  i    A   "   M        B0  儠 >#   	 _Count  AI  �      C       �      C       �      >u    _PFirst  AN  J    �  AN �    �0 �  M        �0  
儩 >璅   _Last  AH �      N N N N M        2  *� M        �2  
�

 Z   3   M        
3  �& N N N M        2  卄 M        �2  卄 N N M        E2  卐 N M        �(  咃 N M        {2  吿 N M        2  �6 M        �2  �6 N N M        E2  �  N M        E2  � N M        E2  
� N M        E2  咜 N M        �2  唘 N M        �2  匂 M        �2  匂 N N M        �2  哱 N* Z	   �  �2  �  �  3  �  �  �!  �!   0           (         @ �hl   e  
        �  �  �  �  �  �  �  �        H  I  L  N  [  {  |  }  ~  �  �  �  �    5  B  W  h  o  /  0  1  2  3  4  >  ?  �  �  �  �  h  �  �  �  C  D  E           N   P   �$  �$  �(  B0  �0  �0  �0  E2  F2  J2  K2  L2  {2  |2  ~2  2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  �2  3  3  
3  3  3   3  23  \3  ]3  �3  �3  
            
                    $LN50         $LN17         $LN14         $LN10         $LN8  `   漅  Othis  p     Od  x   P  Obinary  �     ObinarySize  ^E      鉉   ^�     叡   ^�     埍   ^8     懕   ^�     煴   O �   �          x    5   �      C  �+   D  �8   E  �@   G  �7  H  �V  I  ��  J  ��  O  ��  Q  �4  U  �?  X  ��  Y  ��  \  ��  ^  ��  _  ��  `  ��  a  ��  b  �   d  �  g  �@  j  �{  �  ��  �  �  �  �,  }  �3    �=  �  �L  �  �R  �  �Y  �  �`  �  �e  �  �~  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �   �  �.  �  �6  �  �;  j  �W  �  �\  �  �e  �  �p  �  �u  �  ��   �   W F                                �`nvrhi::d3d12::Device::createShader'::`1'::dtor$1 
 >漅   this  EN  `                                 �  O  �   �   W F                                �`nvrhi::d3d12::Device::createShader'::`1'::dtor$9 
 >漅   this  EN  `                                  �  O  �   �   X F            &                    �`nvrhi::d3d12::Device::createShader'::`1'::dtor$16 
 >漅   this  EN  `                                  �  O ,   )   0   )  
 m   )   q   )  
 }   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
   )   
  )  
 7  )   ;  )  
 K  )   O  )  
 [  )   _  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
   )     )  
    )   $  )  
 0  )   4  )  
 @  )   D  )  
 `  )   d  )  
 p  )   t  )  
 �  )   �  )  
 �  )   �  )  
 B  )   F  )  
 R  )   V  )  
 s  )   w  )  
 �  )   �  )  
 �  )   �  )  
   )     )  
   )      )  
 =  )   A  )  
 M  )   Q  )  
 �  )   �  )  
 !  )   %  )  
 5  )   9  )  
 E  )   I  )  
 q  )   u  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
   )     )  
   )   "  )  
 H  )   L  )  
 \  )   `  )  
 �  )   �  )  
 �  )   �  )  
 H	  )   L	  )  
 k	  )   o	  )  
 {	  )   	  )  
 �	  )   �	  )  
 �	  )   �	  )  
 �
  )   �
  )  
 4  )   8  )  
 D  )   H  )  
 o  )   s  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
    )   $  )  
 4  )   8  )  
 H  )   L  )  
 j  )   n  )  
 z  )   ~  )  
 �  )   �  )  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
 
  �     �  
   �   "  �  
   )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  Q   �  Q  
   Q     Q  
 h  Z   l  Z  
 �  Z   �  Z  
   O     O  
 e  O   i  O  
 @UH冹 H嬯亨   H媿�   �    H兡 ]�   �   H媻�   H兞�       �   @UH冹 H嬯婨$冟吚t
僥$鼿婱h�    H兡 ]�   �   H塡$H塴$VWAVH冹0I嬮M嬸H嬺3蹓\$ 岾(�    H孁H吚t(H茾   H塜H塜 H�    H�H塤H塤H塤 �H孄H嬚H峅�    L嬇I嬛H婳�    怘�>H嬈H媆$PH媗$`H兡0A^_^�%   �   D   k   d   �   s   ^      �   c  O G            �      ~   �0        �nvrhi::d3d12::Device::createShaderLibrary 
 >漅   this  AJ        $  DP    >P   binary  AP          AV       v  >   binarySize  AN       s  AQ          >谋    shaderLibrary  AM  ,     c  M        y2  x M        �2  x N N M        �2  K M        �2  K M        �2  K N N N M        �  1 N Z   �  �2   0                    @ J h     ~  1  2  3  4  �  �$  O2  P2  Q2  y2  �2  �2  �2  �2  �2   P   漅  Othis  `   P  Obinary  h     ObinarySize  ^$      副   O �   H           �        <       �  �!   �  �\   �  �h   �  �x   �  �~   �  ��   �   ^ F            &                    �`nvrhi::d3d12::Device::createShaderLibrary'::`1'::dtor$4 
 >漅   this  EN  P                                  �  O   ,   +   0   +  
 t   +   x   +  
 �   +   �   +  
 �   +   �   +  
 �   +   �   +  
 �   +   �   +  
 
  +     +  
 _  +   c  +  
 x  +   |  +  
 �  V   �  V  
 C  V   G  V  
 @UH冹 H嬯婨 冟吚t
僥 鼿婱X�    H兡 ]�      @SH冹0H嬟�    H嬅H�    H兡0[�
   �      �   �  V G                     �0        �nvrhi::d3d12::Device::createShaderSpecialization 
 >漅   this  AJ          D@    >�   __formal  AP          DP    >#   __formal  AQ          DX    >u    __formal  EO  (           D`    M        �2   N
 Z   �!   0                     @ 
 h   �2   @   漅  Othis  P   �  O__formal  X   #  O__formal  `   u   O__formal  O �   8                   ,       �  �	   �  �   �  �   �  �,   *   0   *  
 {   *      *  
 �   *   �   *  
 �   *   �   *  
    *     *  
 �  *   �  *  
 H冹(H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �3   �   8   �      �   r  F G            =      =           �std::allocator<char>::deallocate 
 >d&   this  AJ          AJ (       D0   
 >e   _Ptr  AK        < +   >   _Count  AP          AP (        M        `  )

 >   _Ptr  AH (       >#    _Bytes  AP       $    AP (      " M          
#

 Z   �   >    _Ptr_container  AJ       (    AJ (       >    _Back_shift  AH         AH (       N N (                      H  h     `         $LN18  0   d&  Othis  8   e  O_Ptr  @     O_Count  O  �   8           =   X     ,       � �   � �.   � �2   � �,   �   0   �  
 k   �   o   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 9  �   =  �  
 M  �   Q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 2  m   6  m  
 �  �   �  �  
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �   =   �      �   R  N G            B      B   �        �std::allocator<unsigned int>::deallocate 
 >蠽   this  AJ          AJ 0       D0   
 >璅   _Ptr  AK          >   _Count  AP        A   M        `  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M          
#

 Z   �   >    _Ptr_container  AJ       %    AJ 0       >    _Back_shift  AH          AH 0       N N (                      H  h     `         $LN18  0   蠽  Othis  8   璅  O_Ptr  @     O_Count  O  �   8           B   X     ,       � �   � �3   � �7   � �,   �   0   �  
 s   �   w   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 -  �   1  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   z     z  
 h  �   l  �  
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �   =   �      �   i  e G            B      B   �2        �std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>::deallocate 
 >簓   this  AJ          AJ 0       D0   
 >*v   _Ptr  AK          >   _Count  AP        A   M        `  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M          
#

 Z   �   >    _Ptr_container  AJ       %    AJ 0       >    _Back_shift  AH          AH 0       N N (                      H  h     `         $LN18  0   簓  Othis  8   *v  O_Ptr  @     O_Count  O   �   8           B   X     ,       � �   � �3   � �7   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 #  �   '  �  
 D  �   H  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 )  �   -  �  
 �  �   �  �  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �   <   �      �   b  Z G            A      A   �2        �std::allocator<D3D12_INPUT_ELEMENT_DESC>::deallocate 
 >虪   this  AJ          AJ ,       D0   
 >q�   _Ptr  AK        @ /   >   _Count  AP           M        `  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M          
#

 Z   �   >    _Ptr_container  AJ       (    AJ ,       >    _Back_shift  AH         AH ,       N N (                      H  h     `         $LN18  0   虪  Othis  8   q�  O_Ptr  @     O_Count  O  �   8           A   X     ,       � �   � �2   � �6   � �,      0     
       �     
 �      �     
 �      �     
 �      �     
           
 =     A    
 �     �    
 �     �    
 �     �    
 �     �    
 "  �   &  �  
 x     |    
 H冹(H嬄Ik�8H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �4   �   9   �      �   `  \ G            >      >   �2        �std::allocator<nvrhi::VertexAttributeDesc>::deallocate 
 >;�   this  AJ          AJ ,       D0   
 >�   _Ptr  AK          >   _Count  AP        =   M        `  )
 >   _Ptr  AH ,       >#    _Bytes  AK       2 " M          
#

 Z   �   >    _Ptr_container  AJ       %    AJ ,       >    _Back_shift  AH         AH ,       N N (                      H  h     `         $LN18  0   ;�  Othis  8   �  O_Ptr  @     O_Count  O�   8           >   X     ,       � �   � �/   � �3   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 ;     ?    
 �     �    
 �     �    
 �     �    
 �     �    
    �   $  �  
 t     x    
 H冹(H嬄Ii�  H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �7   �   <   �      �   Y  U G            A      A   �2        �std::allocator<_NV_CUSTOM_SEMANTIC>::deallocate 
 >'}   this  AJ          AJ /       D0   
 >瀠   _Ptr  AK          >   _Count  AP        @   M        `  )
 >   _Ptr  AH /       >#    _Bytes  AK       2 " M          
#

 Z   �   >    _Ptr_container  AJ       %    AJ /       >    _Back_shift  AH         AH /       N N (                      H  h     `         $LN18  0   '}  Othis  8   瀠  O_Ptr  @     O_Count  O   �   8           A   X     ,       � �   � �2   � �6   � �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 4  �   8  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 p  �   t  �  
 L婭H�%I�$I�$IH婭D嬄I+蒆鏖H龙H嬄H凌?H蠨;聅Ik�8I撩3烂   �   0  Q G            9       8   c2        �nvrhi::d3d12::InputLayout::getAttributeDesc 
 >�   this  AJ          >u    index  A           Ah       $  M        q2    N M        o2  . N                        @  h   o2  q2      �  Othis     u   Oindex  O�   P           9        D       ' �    ( �   ' �   ( �5   * �6   ) �8   * �,   %   0   %  
 v   %   z   %  
 �   %   �   %  
 �   %   �   %  
 D  %   H  %  
 H呉t
H媮�   H�M吚tH媮�   H+亐   I� �   �   8  G G            &       %   d2        �nvrhi::d3d12::Shader::getBytecode 
 >錍   this  AJ        &  >�   ppBytecode  AK        &  >#   pSize  AP        &  M        �$   N                        @  h   �$  �2      錍  Othis     �  OppBytecode     #  OpSize  O�   8           &        ,       - �    . �   / �%   0 �,       0      
 l       p      
 �       �      
 �       �      
 L      P     
 H呉tH婣H�M吚tH婣H+AI� �   �   ?  N G                      e2        �nvrhi::d3d12::ShaderLibrary::getBytecode 
 >槐   this  AJ          >�   ppBytecode  AK          >#   pSize  AP          M        �$   N                        @  h   �$  �2      槐  Othis     �  OppBytecode     #  OpSize  O �   8                   ,       8 �    9 �   : �   ; �,      0     
 s      w     
 �      �     
 �      �     
 T     X    
 H媺�   H�H�`    �   7  S G                   
   �!        �nvrhi::d3d12::ShaderLibraryEntry::getBytecode 
 >gD   this  AJ          >�   ppBytecode  AK          >#   pSize  AP                                 @ 
 h   z2      gD  Othis     �  OppBytecode     #  OpSize  9
          O �   (                          3 �    4 �,      0     
 x      |     
 �      �     
 �      �     
 3     7    
 L     P    
 H岮�   �   �   C G                      H2        �nvrhi::d3d12::Shader::getDesc 
 >錍   this  AJ                                 @     錍  Othis  O   �                  �            �  �,   �   0   �  
 h   �   l   �  
 �   �   �   �  
 H岮�   �   �   O G                      5        �nvrhi::d3d12::ShaderLibraryEntry::getDesc 
 >gD   this  AJ                                 @     gD  Othis  O   �                  �            �  �,      0     
 t      x     
 �      �     
 H�    H嬄�   �     G G                   
   �        �nvrhi::IResource::getNativeObject 
 >"   this  AJ          D    >u    objectType  Ah          D    M        �    N                        @ 
 h   �      "  Othis     u   OobjectType  O  �                  �"            q  �,   �   0   �  
 l   �   p   �  
 �   �   �   �  
 $  �   (  �  
 H嬃H婭H+HH�%I�$I�$IH鏖H龙H嬄H凌?H旅   �   �   Q G            '       &   b2        �nvrhi::d3d12::InputLayout::getNumAttributes 
 >�   this  AH         AJ          M        q2  # N                        @ 
 h   q2      �  Othis  O�   0           '        $       " �   # �&   $ �,   $   0   $  
 v   $   z   $  
 �   $   �   $  
 �   $   �   $  
 H塡$H塴$H塼$ WATAUAVAWH冹@E丰I嬸L孃H嬮E3鞤塴$ 箞   �    H孁H塂$(H吚劘   茾   H�    H�L峸L塼$0fE�.W繟FM塶I荈    E坣AF(I荈8   I荈@   A荈(mainE坣,H敲����A塣HE坣LE塶PM塶XE坣`M塶hH壇�   H呿tH婨 H嬐�P恌E�& H�脌< u鱄峅8L嬅H嬛�    愲I孆I�?I嬊L峔$@I媅0I媖@I媠HI嬨A_A^A]A\_�7   �   V   e   �   �      �   i  L G            "       f2        �nvrhi::d3d12::ShaderLibrary::getShader 
 >贡   this  AJ        )  AN  )     �  >_   entryName  AL  #     �  AP        #  >�   shaderType  Aa           Al          M        �2  �� M        �2  �� N N# M        I2  L=!U M        �  	��
 M        `  	��
 Z   �   M        h  	�� N N N M        r  �� M        4  ��
 N N M        �  ��# M        N  &��( M           �� N N M        L  �� M        }  �� M        �  �� N N N N M        �  m M        �  r$ N M        L  m M        }  m M        �  m N N N N M        �  L N N
 Z   �   @           (         @ � h.   
        �  �  �        L  N  [  {  |  }  ~  �  �  �  �    5  B  W  h  o  /  0  1  2  3  4  >  �  �  �  �  `  h  r  4  �$  I2  �2  �2   p   贡  Othis  �   _  OentryName  �   �  OshaderType  ^6      `D   9�       /   O   �   0           "       $       > �1   ? �  @ ��      [ F                               �`nvrhi::d3d12::ShaderLibrary::getShader'::`1'::dtor$0                        �  O �      [ F                                �`nvrhi::d3d12::ShaderLibrary::getShader'::`1'::dtor$6                         �  O �      [ F                                �`nvrhi::d3d12::ShaderLibrary::getShader'::`1'::dtor$2                         �  O �      [ F                                �`nvrhi::d3d12::ShaderLibrary::getShader'::`1'::dtor$3                         �  O �   �   \ F            &                    �`nvrhi::d3d12::ShaderLibrary::getShader'::`1'::dtor$14                        �  O,      0     
 q      u     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 U     Y    
 e     i    
 �     �    
 �  L   �  L  
 `  W   d  W  
 �  S   �  S  
 p  U   t  U  
 �  N   �  N  
 @UH冹 H嬯簣   H婱(�    H兡 ]�   �   H媻(   H兞�       �   H媻(   H伭�   �          H媻0   H兞�       �   @UH冹 H嬯婨 冟吚t
僥 鼿婱x�    H兡 ]�   �   H塡$H塼$WH冹 H媞H嬟H�H孂H嬑H+蔋;賡H�階vCH婫H+翲;豽L岲$0H嬘H嬒H媆$8H媡$@H兡 _�    H+�3襆嬅H嬑�    H�3H塆H媆$8H媡$@H兡 _肰   :   f   `      �   �  U G            �      r   �2        �std::vector<char,std::allocator<char> >::resize 
 >'y   this  AJ          AM       e 9   >   _Newsize  AI       G 5   AK          AI n     	 2 M        �2  &%
b"' >    _Oldsize  AJ       F '   AJ n       >e    _Newlast  AH  +       AH n       >e    _Oldlast  AL       i = 
  >    _Oldcapacity  AH  6     4  M        3  ]
 >#    _Count  AI  ]       AI n     	  M        d3  
] N N N                       @ > h   e  [  �2  �2  �2  �2  3  3  3  3  d3  e3  f3  �3   0   'y  Othis  8     O_Newsize  O�   `           �   �'  	   T       4 �   6 �   4 �   6 �   4 �   6 �F   7 �U   6 �n   7 �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 ,  �   0  �  
 @  �   D  �  
 c  �   g  �  
 s  �   w  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 H婹H�    H呉HE旅   *      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                   $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H           a      a      �    20    !           b      b      �    20    2           c      c      �   
 
4 
2p    B           d      d      �    20    <           e      e      �   
 
4 
2p    B           f      f      �    20    <           g      g         
 
4 
2p    B           h      h      	    �                  j      j          B                 l      l          B      =           n      n          20    ^           p      p      !    T
 4	 2�p`    [           r      r      '   ! �     [          r      r      '   [   8          r      r      -   !       [          r      r      '   8  T          r      r      3   !   �     [          r      r      '   T  `          r      r      9    20    !           s      s      ?    20    �           u      u      E    20    !           v      v      K    20    !           w      w      Q    B             \      ]       "           x      x      W   h           `      c          �   2 B             \      l       "           y      y      f   h           o      r          �   2 B      B           {      {      u    d T 4 2p    �           }      }      {    B                             �    20    +           �      �      �    20    !           �      �      �    d 4 2p    �           �      �      �    B                 �      �      �    B      B           �      �      �    B                 �      �      �    B      A           �      �      �    20    y           �      �      �    B                 �      �      �    d 4 2p    Z          �      �      �    B             \      �       "           �      �      �   h           �      �          �   2
 
4 
2p           \      �       W           �      �      �   h           �      �          �   R 20    +           �      �      �    20    !           �      �      �    d T 4 r����p           \       �       "          �      �      �   (                        .    V    .    V    �       L      W      S      �      U      N   l }<
 2P               L      L          2P    &           N      N         
 
4 
2p    z           �      �          20    +           �      �          20    !           �      �      &    B      >           �      �      ,    20    �           �      �      2    B                 �      �      8    B      A           �      �      >    20    [           �      �      D    B                 �      �      J    2p               �      �      P   ! 4               �      �      P      J           �      �      V   !                 �      �      P   J   \           �      �      \   
 
d	 
2p    2           �      �      b   ! � 4     2          �      �      b   2   {           �      �      h   ! T 2   {          �      �      h   {   t          �      �      n   !   2   {          �      �      h   t  �          �      �      t   !   �  T  4     2          �      �      b   �  �          �      �      z   !       2          �      �      b   �  �          �      �      �    4	 2�    :           �      �      �   !
 
t d     :          �      �      �   :             �      �      �   !       :          �      �      �     .          �      �      �   !   t  d     :          �      �      �   .  :          �      �      �    20    [           �      �      �   
 
4 
2p    �           �      �      �   
 
4 
2p    �           �      �      �    d T 4 R����p           \       �       x          �      �      �   (           �      �       .    ^       Q      Z      O   � � � 2P                Q      Q      �    2P    &           O      O      �    R0               �      �      �    T 4
 R�p`           \      �       �           �      �      �   (           �      �          V   H � 2P    &           V      V      �   ! !d !T !4 !�����p           \       �       �          �      �      �   (           �      �       .    .    .    .    �       K      R      T      X      Y      M   
n �z
5  2P               K      K      �    2P    )           M      M      �   
 4 R���
�p`P           \             �          �      �         (           
      
   
    @:    @   3      4   )��  4 2p               �      �         ! d               �      �            }           �      �         !                 �      �         }   ~           �      �         !   d               �      �         ~   �           �      �      "          >           �      �      (   ! t      >          �      �      (   >   b           �      �      .   !       >          �      �      (   b   �           �      �      4    2����
p`0           \      @       �          �      �      :   8               C      F   	   L            I   �       D   � �� 
 
2P    (           D      D      O     R���
�p`0           \      ^       1          �      �      X   8               a      d   	   j            g   �       I    �I  BP0      =           I      I      m     20    b           �      �      v    4 2���p`           \      �       �          �      �      |   8               �      �   	   �            �   �       H    ~� 
 
2P    (           H      H      �     2����
p`0           \      �       �          �      �      �   8               �      �   	   �            �   �       E   � �� 
 
2P    (           E      E      �     2�
�p`0           \      �       C          �      �      �   8               �      �   	   �            �   �       F   � �� 
 
2P    (           F      F      �     2�
�p`0           \      �                 �      �      �   8               �      �   	   �            �   �       G   I d� 
 
2P    (           G      G      �     B      :           �      �      �    20               �      �      �    20               �      �          
 
4 
2p    0           �      �         
 
4 
2p    0           �      �         
 
4 
2p    0           �      �         
 
4 
2p    0           �      �         
 
4 
2p    0           �      �                                                     }      �      �      �       �                               �      �      �   Unknown exception                             �      �      �                               �      �      �   bad array new length                                �      9                                 ?      E      K                   .?AVbad_array_new_length@std@@     L               ����                      <      �                   .?AVbad_alloc@std@@     L              ����                      B      �                   .?AVexception@std@@     L               ����                      H      �   string too long     ����    ����        ��������                                                            �      �      �      �       �   (   �   0   �                                                               �      �      �      �       �   (   �   0   �                                                               �      �      �      �       �   (   �   0   �                                                               �      �      �      �       �   (   �   0   �                                                               �            �      �       �   (   �   0                                                                               �      �       �   (      0                                                                  %      
      
             �   (   �   0   �                                                               �            
             �   (      0                                                                  C                         �   (   �   0   �                                                               (      '                   �   (   $   0   %   vector too long unordered_map/set too long invalid hash bucket count                                       �      �      }                   .?AVIResource@nvrhi@@     L                         �                   �               ����    @                   �      �                                         H      �      �                         �                   �               ����    @                   H      �                                         B      �      �                         �                           �      �              ����    @                   B      �                                         <      �      �                         �                                   �      �      �              ����    @                   <      �                                         �      �      �                   .?AVIInputLayout@nvrhi@@     L                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AVIShader@nvrhi@@     L                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AVIShaderLibrary@nvrhi@@     L                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AVShader@d3d12@nvrhi@@     L                         �                                           �      �      �      �              ����    @                   �      �              ����    @                   �      �                   .?AV?$RefCounter@VIShader@nvrhi@@@nvrhi@@     L                         �                                   �      �      �                                         �      �      �                                         �            �                   .?AVShaderLibrary@d3d12@nvrhi@@     L                                                                          
      �      �              ����    @                   �                    ����    @                   
                         .?AV?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@     L                                                            
      �      �                                                                        .?AVShaderLibraryEntry@d3d12@nvrhi@@     L                                                                    "      �      �      �              ����    @                                                                  
            %                                         +      .      (                   .?AVInputLayout@d3d12@nvrhi@@     L                         1                                           4      7      �      �              ����    @                   +      .              ����    @                   :      =                   .?AV?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@     L                         @                                   7      �      �                                         :      =      C      _   �   (   & 
縠        nvrhi::IShader::`vftable'    Y      Y  
    �   /   - 
縠        nvrhi::IShaderLibrary::`vftable'     \      \  
    �   @   > 
縠        nvrhi::RefCounter<nvrhi::IInputLayout>::`vftable'    n      n  
    �   3   1 
縠        nvrhi::d3d12::InputLayout::`vftable'     q      q  
    �   (   & 
 +        std::exception::`vftable'    '      '  
    �   (   & 
 +        std::bad_alloc::`vftable'    -      -  
    �   3   1 
 +        std::bad_array_new_length::`vftable'     0      0  
    �   ;   9 
縠        nvrhi::RefCounter<nvrhi::IShader>::`vftable'     _      _  
    �   *   ( 
攅        nvrhi::IResource::`vftable'      $      $  
    �   .   , 
縠        nvrhi::d3d12::Shader::`vftable'      b      b  
    �   :   8 
縠        nvrhi::d3d12::ShaderLibraryEntry::`vftable'      e      e  
    �   B   @ 
縠        nvrhi::RefCounter<nvrhi::IShaderLibrary>::`vftable'      h      h  
    �   5   3 
縠        nvrhi::d3d12::ShaderLibrary::`vftable'       k      k  
    �   -   + 
縠        nvrhi::IInputLayout::`vftable'       U      U  
 
<V贷�檤/i騿壳鋘i﹤%:玹′uh�K蜌�(�巒'IJ肆峖=f瓵q┝鮷蔲嶢"R�禥c�
&�sA"R�稜i `圙EA"R��&o栕幏�:A"R��'h搐佪鐘A"R�墩:Ao驐弛�姿S创}A恥Ho幉趕彛�T�*_裛s鲚溏qⅫ俕~碉0U綤(o`s鲚溏qWg櫩4b県埈pN嵝哵囄嶸藊�0^k�>Dt暦靿n 仢/撏珬,3郧Jvz肝隺3纺婺�:K[Z
d戮翁r垯︳蔽FI橴歒鮩驑蚂浚u譪瘠噁oD5倻R	┎8
d戮翁r5D98臝9q�-D�訧)#hv瓯訧)#hv瓯~o睝砳=o〃銰煎#�嚻址Z�[鰱腵|�嚻址Z父簔抖綄襜綩藋TY喘�6Zu��(！
Z�簱|
顕臂ep禭�}掱'晣臂ep禭P援� 猓&6萪O��:稦麷霵婬(�8~'髼�'項j犮� &���'項jX&H雁�'項j蒠蓩磷Ⅰ�<�蠺缮N�浂鳢-汎埜戄"凂漒夂嫑�凹纂軥!H�<�蠺蒛隘�7鑌[�:b8�4n下衊(摭礘�;C盾65幆Zb侌驚鬅p�?AH�(X}�;S� �9柒泪瑛5d��灅�
 B垱嫈端祆癜~t;豉鷲槑軱珁Q@嶲K�蠠 嶀預棊膬�咥9N彏嶀預棊膬赽9筴pP>嶿JI怣*�T椏�嶿JI怣炏 驕嶿JI怣b�\
�:D饫�*伋W騱h瓷K肉磊*伋丙e<p絋饫�*伋鸰�+jt饫�*伋g驫�毪E覜'笅嶍蚧倸)U�K泲�&=��!e麆=�$=a蘯
淑筇��0�G+案怛悧D箩邆5>矦`峯�g箩邆5>1闲夘'w箩邆5>TiV彌/c� �蹰kQ
som^鴆�,�
鐱uZ-[UM�慶�,�
鐱u�b3
c�,�
鐱u]� 4廡*錹虫試G�/
;蛦嘥*錹虫試揘��?o衮T*錹虫試枷i侠跾溊欤狔ohnx�=帎�''0餪�0药S�=[愜�呉�?6rd8佾霒%I栶賑?T�=w羯d]{謑pL鶜諮4Rf]{謑pMR槨5<鲤f]{謑p ?�2^vf]{謑p諤胮躎f]{謑pi瀛笲V遞]{謑p�
猏沔噇f]{謑p籓5紕]丽Υ賸p#舀"?淞	= C哴�0_庞J�1惾^蔦j�#哥秄惌+Y`@,�嘻���7����/噜V�*v葎滃�*揇}騨旁<��O�
�屼軠no魼A宿摛�=jK竟袸暫鹂[*��K槄�ふ塰+浠�W呺战�yzｄ島葹�X墺鉪d�?鬐
攠襱V �銏憴t崇�Z
 砌&reE鵛fb3慲I .毯
围窝灜c忪謠�擖hQ鷸�(鋛p屌�,<郯`x儜s鑻 烮C憎Е殬赘4V�**脍lM塙�-虭哰E&滔aAf2^鴷8凐従
朷�5xb!旊蒢(�
朷�5x�.莉訷~惵$r妿旤�袩c岨掝Y裃覛p树bJ 锘?�检狎�&}晟v蛲沂\j�#哥都�	徙Ds�,�嘻畋#覛-遨p齓�6)�:痉裯N鵘J釫鲉譨駀F{'yZ祼垩寯啦烹嘕-WV8oc8曀黩6雵J-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�-坓�(鬄��1�8]Z嘕-WV8o��腫62V^橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 夒嘕-WV8oc8曀黩6雵J-WV8o斀�&g卷雵J-WV8oc8曀黩6雵J-WV8oc8曀黩6了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G�-坓�(鬄酲;[純o嘶嫫﹥�,舂}q菩�-坓�(鬄�汬'这栫{殮殂
雵J-WV8o额	hQ�)缦F爔嘕-WV8oc8曀黩6氈\6	柪瀛屗�8-坓�(鬄�汬'这�-坓�(鬄酲;[純o�-坓�(鬄�汬'这�-坓�(鬄鮳�>i,夿雵J-WV8o'Q鶘2m46-坓�(鬄�汬'这朿闲�
墸g�伣tou了5YJq覜垒�咞taR�,F_棢杻#Q`�G埻﨏帲晗D�Tラ~�&咞taR�,F_棢杻#Q吀qv蕞	ョ{殮殂
雵J-WV8o额	hQ�)缦F爔嘕-WV8oc8曀黩6椼駍鉌枪TdWh侢dd�a�:；�駧�
>S�"a忼樢閣yQ E<礼\樢閣yQ}�!罱4=9E\$L釉迒鰡畆殅W鐊殮殂
雵J-WV8o额	hQ�)缦F爔嘕-WV8oc8曀黩6-坓�(鬄鮐搜n2竌V雵J-WV8o塣d\蜩kU-坓�(鬄�汬'这�-坓�(鬄鮳�>i,夿雵J-WV8om�M%>mb-坓�(鬄�汬'这枾骺弨bx旊%-<$澊蘁	!+k不挷孨U
M!维猉�5疃沤鱂w壱3,�4q胭o�!骬6�/s.�.:6俙貱霯*�2�谥腲�)裯 j|Uu絓e豷,;窇藿垨休t疿�G�6'j�#yX]绪/ｎ	蜍R僁舴�:kn嶜�:5齵h周洊{= 栰宧霅;�d_f湲z竘雵J-WV8om�M%>mb9E\$L釉�|m�/.L9E\$L釉抟�.鳐g彿闁�0i#^?�2dd�a�:炒v54J�:VG辯諍樢閣yQ5R犵�喪樢閣yQ}�!罱4=d繅鬮�摮Dk.,1[眲�9鸗鍊/绷dd�a�:_棢杻#Ql[�4@|樢閣yQ}�!罱4=4e能3欅嬟∩篡dd�a�:铫|l6稌w饸諔A樢閣yQ E<礼\樢閣yQ)傂螨]箅我F詍汰偞荵�dd�a�:ZmQ1&o:%i'雕
аs1
蒴W�$峾$J-h苪{ 祗叫驴瓎�灧NUX銴�)+^�!炟缏)唲<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦�)�8蛾爨�"eN�&y*�杜`颀l+�鞯.r擣�0G#盱谑3蕢y奶�(��苳乮5絚_}4n4�硓樝0;a�l珗.z; 儁*�杜`颀l+�鞯.r擣�0G#盱谑o+�G馒籹;嗐8�1�8]Z齨4�硓橂嘕-WV8o>%'剌iz�:喍'歆哑暬饄*�杜`颀l+�鞯.r擣�0G#盱谑}B?�'@擐(��苳乮5絚_}4n4�硓�)�8蛾爨��8畔矬y*�杜`颀l+�鞯.r擣�0G#盱谑铁Ri%毃(��苳乮5絚_}4n4�硓榲暕妝�#(ew`(琈sy*�杜`颀l+�鞯.r擣�0G#盱谑J諶�'(��苳乮5絚_}4n4�硓榲暕妝�#(埶騯�yy*�杜`颀l+�鞯.r擣�0G#盱谑籃婖Gv婻(��苳乮5絚_}4n4�硓�-坓�(鬄�/ｎ	蜍R雵J-WV8o�%-<$濍嘕-WV8o�%-<$�9E\$L釉��E光9E\$L釉��E光9E\$L釉��E光9E\$L釉��E光9E\$L釉��E光,4��;儗潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H詠zv(缭亃v(缭亃v(缭亃v(缭亃v(缭亃v(缭亃v(缭亃v(缭亃v(缭亃v(缈%G>禡h樬鍏穧 5]叨蝝�\&2渘渗-%�?"�:邍A愦靮鸬2�>料C5��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2� 纲櫌DM%ZZ�$为赞G刹~赣 "^惋砤��\&2�	镤絹溯�%ZZ�$为赞G刹~赣 "^惋砤��\&2溍粜�-鷋%ZZ�$为赞G刹~赣 "^惋砤��\&2溬l"yv�<黋lL�^鴐禵諢覸鰛B	挿;褨鏌�� n�2P鳻
B�#qM�5<A:蓰咨��\&2��\&2�#鳬f鹟穃黋lL�^鴐禵諢覸鰛B	挿;褨鏌�� F�+��#qM�5<A:蓰咨��\&2淢�彈{髩黋lL�^鴐禵諢覸鰛B	挿;�\&2��\&2�,礟@�R黋lL�^鴐禵諢覸鰛B	挿;褨鏌�� �)業{
_聡#qM�5<A:蓰咨��\&2湼擡@wX+]\礛�劯塎;鷶駪龌�7�$犟N&&堮�ajO�(N<S`�#)�*f诣k恒X彗`タ撣Za蘈峈剡駥c馹�5T2GdkR犿坕x兎H槨&>琺溨wOx鋪[l![@�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       j                .debug$S       %              .debug$T       t                 .rdata         8       )�                         -          .text$mn       :      眡�     .debug$S                    .text$mn              恶Lc     .debug$S                    .text$mn    	   0      燥"V     .debug$S    
   �         	    .text$mn       0      燥"V     .debug$S       �             .text$mn    
   0      燥"V     .debug$S       �         
    .text$mn       0      燥"V     .debug$S       �             .text$mn       0      燥"V     .debug$S       �             .text$mn       �      P囷Y     .debug$S                     .text$mn       �     鳔s�     .debug$S       �  f           .text$x        (      镽=    .text$mn       �     �3R     .debug$S       �  f           .text$x        (      镽=    .text$mn       7       Zo萅     .debug$S       t             .text$mn       D       磚
:     .debug$S       �             .text$mn       C     ��     .debug$S        �
  \           .text$x     !   (      弳1�    .text$mn    "        簧E�     .debug$S    #   0	  H       "    .text$x     $   (      纥吨"    .text$mn    %   1  	   赨.>     .debug$S    &   �  R       %    .text$x     '   =      W乤�%    .text$mn    (   �     幓S�     .debug$S    )   �
  X       (    .text$x     *   (      纥吨(    .text$mn    +   �     稼愝     .debug$S    ,   P  \       +    .text$x     -         S�+    .text$x     .         S�+    .text$mn    /   b      伓g     .debug$S    0             /    .text$mn    1         憟⑸     .debug$S    2   4  
       1    .text$mn    3         憟⑸     .debug$S    4   <  
       3    .text$mn    5   �       `螏�     .debug$S    6   �         5    .text$mn    7           _葓�     .debug$S    8   �         7    .text$mn    9   <      .ズ     .debug$S    :   0  
       9    .text$mn    ;   <      .ズ     .debug$S    <   L  
       ;    .text$mn    =   !      :著�     .debug$S    >   <         =    .text$mn    ?   2      X于     .debug$S    @   <         ?    .text$mn    A   "       坼	     .debug$S    B   �         A    .text$mn    C   "       坼	     .debug$S    D   �         C    .text$mn    E   "       坼	     .debug$S    F   �         E    .text$mn    G         5玓     .debug$S    H            G    .text$mn    I   �      �Mf     .debug$S    J   �         I    .text$mn    K   [       荘�     .debug$S    L   �         K    .text$mn    M         5玓     .debug$S    N   (         M    .text$mn    O   ^      wP�     .debug$S    P   T         O    .text$mn    Q   \      L
�     .debug$S    R   �         Q    .text$mn    S   [      J败     .debug$S    T            S    .text$mn    U   �      ��     .debug$S    V   $         U    .text$mn    W   y      V�鋤     .debug$S    X            W    .text$mn    Y          .B+�     .debug$S    Z   �          Y    .text$mn    [   �      cu]     .debug$S    \   |  $       [    .text$mn    ]         ��#     .debug$S    ^   �          ]    .text$mn    _         ��#     .debug$S    `   �          _    .text$mn    a   !      -嵎     .debug$S    b   �          a    .text$mn    c   !      -嵎     .debug$S    d   �          c    .text$mn    e   !      -嵎     .debug$S    f   �          e    .text$mn    g   !       ��     .debug$S    h   �          g    .text$mn    i   !       ��     .debug$S    j   �          i    .text$mn    k   !       ��     .debug$S    l   �          k    .text$mn    m   !       ��     .debug$S    n   �          m    .text$mn    o   �      w~�     .debug$S    p   �         o    .text$mn    q   Z     毧$�     .debug$S    r   �  6       q    .text$mn    s   z      灥     .debug$S    t   �         s    .text$mn    u   W      m��     .debug$S    v   h         u    .text$mn    w   B      贘S     .debug$S    x             w    .text$mn    y   B      贘S     .debug$S    z            y    .text$mn    {   B      贘S     .debug$S    |   �          {    .text$mn    }   H       襶.      .debug$S    ~   �         }    .text$mn       
       7�
b     .debug$S    �                .text$mn    �   
       7�
b     .debug$S    �            �    .text$mn    �   
       7�
b     .debug$S    �            �    .text$mn    �   +       �<Hu     .debug$S    �   l         �    .text$mn    �   +       �<Hu     .debug$S    �   d         �    .text$mn    �   +       �<Hu     .debug$S    �   l         �    .text$mn    �   :     愽鉻     .debug$S    �   x  <       �    .text$mn    �   �      6矨{     .debug$S    �   �  "       �    .text$mn    �   �     \8�     .debug$S    �   `  P       �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �            �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �             �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �   `     匮�5     .debug$S    �   �  B       �    .text$mn    �   �     <��     .debug$S    �   �  l       �    .text$x     �         �$�'�    .text$x     �         =偂仯    .text$x     �         �坕�    .text$x     �         |w ��    .text$x     �         鐄鰞�    .text$x     �   )      z�0    .text$mn    �   x     n��     .debug$S    �   �  �       �    .text$x     �          赝鑁�    .text$x     �         D�3'�    .text$x     �   &      y詣W�    .text$mn    �   �      �(I$     .debug$S    �   p         �    .text$x     �   &      鳟銺�    .text$mn    �         �Ztr     .debug$S    �   �         �    .text$mn    �   =      菮�8     .debug$S    �   �         �    .text$mn    �   B      鸮     .debug$S    �   �         �    .text$mn    �   B      mr{V     .debug$S    �   �         �    .text$mn    �   A      o漮     .debug$S    �   �         �    .text$mn    �   >      0�-�     .debug$S    �   �         �    .text$mn    �   A      �^     .debug$S    �   �         �    .text$mn    �   9       *�C     .debug$S    �   �  
       �    .text$mn    �   &       鵵�      .debug$S    �   �  
       �    .text$mn    �          B �-     .debug$S    �   �  
       �    .text$mn    �          x陻?     .debug$S    �   t         �    .text$mn    �          �GN     .debug$S    �   �          �    .text$mn    �          �GN     .debug$S    �   �          �    .text$mn    �          乬�     .debug$S    �   D         �    .text$mn    �   '       殣3�     .debug$S    �             �    .text$mn    �   "     紦�     .debug$S    �   X         �    .text$x     �         :9j.�    .text$x     �         =偂佈    .text$x     �         �u    .text$x     �         |w ��    .text$x     �   &      u誌�    .text$mn    �   �      珐     .debug$S    �   (         �    .text$mn    �         崪覩     .debug$S    �   �          �        R       }        n                x                �                �                �                �                �                �       Y              �        E      i        f          i�                   �               �               �      ?        �      _        �      �              {        .          i�                   M      9        n      w        �          i�                   �      =        �      ]        �      ;        "      y        L          i�                   v      �        �      �        �      �        �      O        ,      �        �               �      g        �          i�                         [        !      k        @          i�                   _      m        �          i�                   �      A        �      C                      <               ]               }      �        �      �        �      �                        e      �        �      �        �      c                  i�                   :      �        p      �        �      �        	      �        �	      �        �	      W        M
      �        �
      �        �
      �              q        9          i                   ]      E        �      �        �      �              u        G          i                   w      �        �      �        �      e        ,
          i                   h
      �        �
      �              s        3          i                   ^              �      �        �      a        
          i                   G      �        �      U              �        y      �        �      S        =      �        �      Q        �      �        �      �        �      K        .      I        �      �        �      �        ?      o        h          i&                   �      �        �      �        z      �        �      �        [      +        B              �              <      7        7      5        $              O      G        �      M                      �      %        �      /        :      (        �              �              :       "        �                !      1        '!      3        N!              �!              "      
        �"      	        �"              �"              �#              �$      !        D%      $        �%      *        p&      '        ''      -        (      �        �(      �        )      �        �)      �        #*      �        �*      .        �+      �         ,      �        �,      �        -      �        �-      �        .      �        t.      �        �.      �        v/      �        0      �        x0               �0           ceilf            memcpy           memmove          memset           $LN13       }    $LN8        i    $LN5        ?    $LN10       {    $LN7        9    $LN13       w    $LN10       ;    $LN16       y    $LN3        �    $LN4        �    $LN3       �    $LN4        �    $LN18   =   �    $LN21       �    $LN33   ^   O    $LN36       O    $LN93   `  �    $LN100      �    $LN8        g    $LN70   �   [    $LN73       [    $LN8        k    $LN8        m    $LN10       A    $LN10       C    $LN18   B   �    $LN21       �    $LN25   �   �    $LN28       �    $LN3       �    $LN4        �    $LN14       �    $LN8        c    $LN29       �    $LN3       �    $LN4        �    $LN18   B   �    $LN21       �    $LN3       �    $LN4        �    $LN18   A   �    $LN21       �    $LN28   y   W    $LN31       W    $LN3       �    $LN4        �    $LN91   Z  q    $LN94       q    $LN10       E    $LN16       u    $LN14       �    $LN8        e    $LN132      �    $LN37   z   s    $LN40       s    $LN14       �    $LN8        a    $LN18   >   �    $LN21       �    $LN28   �   U    $LN31       U    $LN3       �    $LN4        �    $LN18   A   �    $LN21       �    $LN28   [   S    $LN31       S    $LN3       �    $LN4        �    $LN63       Q    $LN185  �  �    $LN189      �    $LN82   :  �    $LN85       �    $LN30   [   K    $LN33       K    $LN93   �   I    $LN96       I    $LN40   �   o    $LN43       o    $LN356  �  �    $LN357  �  �    $LN8    {  �    $LN10   ,  �    $LN14   e  �    $LN50   W  �    $LN17   e  �    $LN367      �    $LN9        �    $LN45       �    $LN242      �    $LN186  �  +    $LN195      +    $LN58   �       $LN62           $LN20       5    $LN118  �          �0  
       $LN123          $LN116  1  %        g1     '    $LN120      %    $LN40       /    $LN101  �  (        2  
   *    $LN105      (    $LN110  �          �2  
       $LN115          $LN92   C          �3  
   !    $LN97           $LN76     "        64  
   $    $LN80       "    $LN14   :       $LN17           $LN4        1    $LN4        3    $LN4            $LN4            $LN4        
    $LN4        	    $LN4            .xdata      �          F┑@}        �4      �    .pdata      �         X賦鷠        �4      �    .xdata      �          （亵i        �4      �    .pdata      �         萣�5i        !5      �    .xdata      �          （亵?        I5      �    .pdata      �          T枨?        r5      �    .xdata      �          %蚘%{        �5      �    .pdata      �         惻竗{        �5      �    .xdata      �          （亵9        �5      �    .pdata      �         2Fb�9        6      �    .xdata      �          %蚘%w        86      �    .pdata      �         惻竗w        _6      �    .xdata      �          （亵;        �6      �    .pdata      �         2Fb�;        �6      �    .xdata      �          %蚘%y        �6      �    .pdata      �         惻竗y        7      �    .xdata      �          懐j瀾        O7      �    .pdata      �         Vbv鶓        7      �    .xdata      �          �9��        �7      �    .pdata      �         �1皳        �7      �    .xdata      �          �9��        �7      �    .pdata      �         現��        $8      �    .xdata      �          （亵O        X8      �    .pdata      �         翎珸O        �8      �    .xdata      �          蔜-濉        �8      �    .pdata      �         愶L�        X9      �    .xdata      �         �qL儭        �9      �    .pdata      �         ~蕉健        :      �    .xdata      �         |薄        |:      �    .pdata      �         瞚挨�        �:      �    .xdata      �         S!熐�        @;      �    .pdata      �         �o垺        �;      �    .xdata      �          （亵g        <      �    .pdata      �         萣�5g        0<      �    .xdata      �          （亵[        [<      �    .pdata      �         邴'鱗        �<      �    .xdata                （亵k        �<          .pdata              萣�5k        �<         .xdata               （亵m        �<         .pdata              萣�5m        =         .xdata              /
        L=         .pdata              +eS籄        �=         .xdata        	      �#荤A        �=         .xdata              jA        >         .xdata               3狷 A        M>         .xdata      	        /
        �>      	   .pdata      
        +eS籆        �>      
   .xdata        	      �#荤C        �>         .xdata              jC        9?         .xdata      
         3狷 C        z?      
   .xdata               �9��        �?         .pdata              惻竗�        �?         .xdata               嘋c魨        @         .pdata              脤�        h@         .xdata               �9��        盄         .pdata              �1皸        鞞         .voltbl              忯jp�    _volmd         .xdata               （亵�        (A         .pdata               ~        fA         .voltbl              堎覘�    _volmd         .xdata               （亵c                 .pdata              萣�5c        郃         .xdata               �	棕        B         .pdata              �        ZB         .xdata               �9��        桞         .pdata              �1皶        覤         .xdata               �9��        C         .pdata              惻竗�        廋         .xdata                �9��        D          .pdata      !        �1皺        桪      !   .xdata      "         �9��        E      "   .pdata      #        s�7蹇        }E      #   .xdata      $         （亵W        跡      $   .pdata      %        粖砏        <F      %   .xdata      &         �9��        淔      &   .pdata      '        �1盁        G      '   .xdata      (         O韖        gG      (   .pdata      )        轰慴q        揋      )   .xdata      *        /
        綠      *   .pdata      +        +eS籈        礼      +   .xdata      ,  	      �#荤E        =H      ,   .xdata      -        jE        H      -   .xdata      .         3狷 E        荋      .   .xdata      /        �酑u        	I      /   .pdata      0        啁鉥u        AI      0   .xdata      1  	      �#荤u        xI      1   .xdata      2        ju        睮      2   .xdata      3         攰eu        騃      3   .voltbl     4         忯jp�    _volmd      4   .xdata      5         （亵�        ,J      5   .pdata      6         ~        qJ      6   .voltbl     7         堎覘�    _volmd      7   .xdata      8         （亵e        礘      8   .pdata      9        萣�5e        鵍      9   .xdata      :  $      �<蝾�        <K      :   .pdata      ;        焠$�        ↘      ;   .xdata      <  	      � )9�        L      <   .xdata      =        颂~屟        丩      =   .xdata      >         �佳        鮈      >   .xdata      ?         k寡        cM      ?   .pdata      @        �$剧�        轒      @   .xdata      A         k寡        XN      A   .pdata      B        裬?�        訬      B   .xdata      C         %蚘%s        OO      C   .pdata      D        X崘=s        侽      D   .voltbl     E         忯jp    _volmd      E   .xdata      F         （亵�        碠      F   .pdata      G         ~        鱋      G   .voltbl     H         堎覘�    _volmd      H   .xdata      I         （亵a        9P      I   .pdata      J        萣�5a        {P      J   .xdata      K         �9��        糚      K   .pdata      L        OAG惤        'Q      L   .xdata      M         （亵U        慟      M   .pdata      N        緥�U              N   .xdata      O         �9��        jR      O   .pdata      P        �1皾        躌      P   .xdata      Q         �9��        MS      Q   .pdata      R        s�7寤        禨      R   .xdata      S         （亵S        T      S   .pdata      T        愶LS        塗      T   .xdata      U         �9��        骉      U   .pdata      V        �1皼        cU      V   .xdata      W         3�俀        襏      W   .pdata      X        �#洢Q        *V      X   .xdata      Y        狳%Q        乂      Y   .pdata      Z        ì哑Q        赩      Z   .xdata      [        k商Q        3W      [   .pdata      \        馆豎        學      \   .xdata      ]         G栚鲝        錡      ]   .pdata      ^         T枨�        峏      ^   .xdata      _        0W圫�        4Y      _   .pdata      `        ]%(	�        軾      `   .xdata      a        甞淰�        哯      a   .pdata      b        $钡蹚        /[      b   .xdata      c        毕皬        豙      c   .pdata      d        鹆�        乗      d   .xdata      e        �(崚�        *]      e   .pdata      f        %t�        覿      f   .xdata      g        炀縹�        |^      g   .pdata      h        �<蚂�        %_      h   .xdata      i         ii@�        蝊      i   .pdata      j        礝
�        赻      j   .xdata      k        塯4穻        錫      k   .pdata      l        囥鱢�        騜      l   .xdata      m        Y瓔        �c      m   .pdata      n        s�&k�        e      n   .xdata      o        n奧w�        f      o   .pdata      p        '擊倠        &g      p   .xdata      q         （亵K        3h      q   .pdata      r        愶LK        衕      r   .xdata      s         %蚘%I        li      s   .pdata      t        o嗦$I        j      t   .xdata      u         %蚘%o        漥      u   .pdata      v        翊�/o        蝚      v   .xdata      w  $      @[绔              w   .pdata      x        韩        jk      x   .xdata      y  	      � )9�        誯      y   .xdata      z        X3Uh�        Cl      z   .xdata      {         楝3 �        穕      {   .xdata      |         k公        %m      |   .pdata      }        Vbv        爉      }   .xdata      ~         k公        n      ~   .pdata              裬?�        杗         .xdata      �         僣汲        o      �   .pdata      �        #1i�        瀘      �   .xdata      �        }��        *p      �   .pdata      �        o�*惆        損      �   .xdata      �  	      � )9�        鹥      �   .xdata      �        j�        fq      �   .xdata      �         邅�#�        譹      �   .xdata      �         k拱        Br      �   .pdata      �        裬?�        簉      �   .xdata      �  $      �C�        1s      �   .pdata      �        兰H牵        箂      �   .xdata      �  	      � )9�        @t      �   .xdata      �        -匇;�        蕋      �   .xdata      �  
       bGル�        Zu      �   .xdata      �         k梗        鋟      �   .pdata      �        �$剧�        {v      �   .xdata      �         k梗        w      �   .pdata      �        }y9妫        ﹚      �   .xdata      �         �"膧+        @x      �   .pdata      �        �^�+        /y      �   .xdata      �  	      � )9+        z      �   .xdata      �  
      諕附+        {      �   .xdata      �  
       �8:+        |      �   .xdata      �         �2耈        鰘      �   .pdata      �        � �        爙      �   .xdata      �        �)<�        I~      �   .pdata      �        0罞        魚      �   .xdata      �        @鴚`        �      �   .pdata      �        �?        J�      �   .xdata      �        Ty飺        鮻      �   .pdata      �        寿
        爜      �   .xdata      �         確5        K�      �   .pdata      �        OAG�5        @�      �   .xdata      �        +縬[5        4�      �   .pdata      �        蹷謔5        *�      �   .xdata      �        ＋)5         �      �   .pdata      �        穣5        �      �   .xdata      �        萦[�        �      �   .pdata      �        痧鷿        訄      �   .xdata      �  
      B>z]        檳      �   .xdata      �         �2g�        b�      �   .xdata      �        T�8        1�      �   .xdata      �        r%�        鴭      �   .xdata      �  	       �
&/        脤      �   .xdata      �         3賟P        實      �   .pdata      �        銀�*        c�      �   .voltbl     �                 _volmd      �   .xdata      �        C驎�%        9�      �   .pdata      �        蔅%        鑿      �   .xdata      �  
      B>z]%        枑      �   .xdata      �         �2g�%        G�      �   .xdata      �        T�8%              �   .xdata      �        r%�%        瓛      �   .xdata      �  	       $�%        `�      �   .xdata      �         M[�%        �      �   .pdata      �        現�%        袛      �   .voltbl     �             '    _volmd      �   .xdata      �         （亵/        帟      �   .pdata      �        僻螔/        K�      �   .xdata      �        屐�:(        �      �   .pdata      �        氯勲(        獥      �   .xdata      �  
      B>z](        L�      �   .xdata      �         �2g�(        駱      �   .xdata      �        T�8(        湙      �   .xdata      �        r%�(        ?�      �   .xdata      �  	       \兛](        鏆      �   .xdata      �         3賟P(        嫑      �   .pdata      �        銀�*(        >�      �   .voltbl     �             *    _volmd      �   .xdata      �        萦[�        饻      �   .pdata      �        榄譖        鞚      �   .xdata      �  
      B>z]        鐬      �   .xdata      �         �2g�        鍩      �   .xdata      �        T�8        闋      �   .xdata      �        r%�        濉      �   .xdata      �  	       �5|        澧      �   .xdata      �         3賟P        悖      �   .pdata      �        銀�*        铯      �   .voltbl     �                 _volmd      �   .xdata      �        啄qJ              �   .pdata      �        何e        s�      �   .xdata      �  
      B>z]        毽      �   .xdata      �         �2g�        f�      �   .xdata      �        T�8        绉      �   .xdata      �        r%�        `�      �   .xdata      �         弯	        莰      �   .xdata      �         3賟P        X�      �   .pdata      �        銀�*        岍      �   .voltbl     �             !    _volmd      �   .xdata      �        啄qJ"        i�      �   .pdata      �        W&K"        猹      �   .xdata      �  
      B>z]"        Z�      �   .xdata      �         �2g�"        斋      �   .xdata      �        T�8"        V�      �   .xdata      �        r%�"        犀      �   .xdata      �         _>�"        L�      �   .xdata      �         3賟P"        黔      �   .pdata      �        銀�*"        P�      �   .voltbl     �             $    _volmd      �   .xdata      �         �9�        禺      �   .pdata      �        礝
        5�      �   .xdata      �         （亵1        懐      �   .pdata      �        �#洢1        蜡      �   .xdata      �         （亵3        畀      �   .pdata      �        �#洢3        �      �   .xdata      �         %蚘%        K�      �   .pdata      �        }S蛥        拱      �   .xdata      �         %蚘%        &�      �   .pdata      �        }S蛥        姳      �   .xdata      �         %蚘%
        肀      �   .pdata      �        }S蛥
        y�      �   .xdata      �         %蚘%	        �      �   .pdata      �        }S蛥	        :�      �   .xdata      �         %蚘%        o�      �   .pdata      �        }S蛥        コ      �   .rdata      �  (                   诔     �   .rdata      �                     蟪     �   .rdata      �         �;�         
�      �   .rdata      �                     1�     �   .rdata      �                     H�     �   .rdata      �         �)         j�      �   .xdata$x    �                     柎      �   .xdata$x    �        虼�)         复      �   .data$r     �  /      嶼�         鄞      �   .xdata$x    �  $      4��          �      �   .data$r     �  $      鎊=         U�      �   .xdata$x    �  $      銸E�         o�      �   .data$r     �  $      騏糡               �   .xdata$x       $      4��         鹊              �           .rdata               燺渾         �         .data                 烀�          @�         .rdata        8                   t�            惗        .rdata        8                   范        .rdata        8                   味        .rdata        8                   於        .rdata        8                   �        .rdata        8                   5�        .rdata      	  8                   ]�     	   .rdata      
  8                   懛     
   .rdata        8                   捶        .rdata        8                   娣        .rdata      
         IM         �      
   .rdata               ��         -�         .rdata               藾味         _�         .rdata$r      $      'e%�         惛         .data$rs      &      煷>                  .rdata$r            �          聘         .rdata$r                         薷         .rdata$r      $      Gv�:         龈         .rdata$r      $      'e%�         �         .rdata$r            �          /�         .rdata$r                         E�         .rdata$r      $      Gv�:         [�         .rdata$r      $      'e%�         z�         .rdata$r            }%B         捁         .rdata$r                         ü         .rdata$r      $      `         竟         .rdata$r      $      'e%�         莨         .rdata$r            �弾          �         .rdata$r                         !�         .rdata$r       $      H衡�         B�          .rdata$r    !  $      'e%�         l�      !   .data$rs    "  )      惕|          壓      "   .rdata$r    #        }%B         ê      #   .rdata$r    $                     煤      $   .rdata$r    %  $      `         藓      %   .rdata$r    &  $      'e%�         �      &   .data$rs    '  $      */鋱         �      '   .rdata$r    (        }%B         4�      (   .rdata$r    )                     J�      )   .rdata$r    *  $      `         `�      *   .rdata$r    +  $      'e%�         �      +   .data$rs    ,  +      !,椞         灮      ,   .rdata$r    -        }%B         炕      -   .rdata$r    .                     芑      .   .rdata$r    /  $      `               /   .rdata$r    0  $      'e%�         �      0   .data$rs    1  )      B         <�      1   .rdata$r    2        �J�         [�      2   .rdata$r    3  $                   v�      3   .rdata$r    4  $      o咔b         懠      4   .rdata$r    5  $      H衡�         导      5   .data$rs    6  :      獣JJ         昙      6   .rdata$r    7        �弾         �      7   .rdata$r    8                     F�      8   .rdata$r    9  $      'e%�         r�      9   .rdata$r    :  $      'e%�         牻      :   .data$rs    ;  0      �3�         慕      ;   .rdata$r    <        �J�         杲      <   .rdata$r    =  $                   �      =   .rdata$r    >  $      o咔b         .�      >   .rdata$r    ?  $      H衡�         Y�      ?   .data$rs    @  A      .p麛         暰      @   .rdata$r    A        �弾         叹      A   .rdata$r    B                     ��      B   .rdata$r    C  $      'e%�         2�      C   .data$rs    D  5      �蟋         [�      D   .rdata$r    E        �J�         喛      E   .rdata$r    F  $                         F   .rdata$r    G  $      o咔b         钥      G   .rdata$r    H  $      'e%�         �      H   .rdata$r    I  $      'e%�         9�      I   .data$rs    J  .      諬蠑         [�      J   .rdata$r    K        �J�         �      K   .rdata$r    L  $                   熇      L   .rdata$r    M  $      o咔b         坷      M   .rdata$r    N  $      H衡�         枥      N   .data$rs    O  ?      C*S         "�      O   .rdata$r    P        �弾         W�      P   .rdata$r    Q                     埩      Q   .rdata$r    R  $      'e%�         沽      R       炝           .rdata      S         eL喳               S   _fltused         .debug$S    T  4             .debug$S    U  <             .debug$S    V  L             .debug$S    W  @             .debug$S    X  4          �   .debug$S    Y  4          �   .debug$S    Z  @          �   .debug$S    [  H             .debug$S    \  8          �   .debug$S    ]  <             .debug$S    ^  H             .debug$S    _  P          	   .debug$S    `  D          
   .debug$S    a  <             .chks64     b                  �  ?c_ResourceStateUnknown@d3d12@nvrhi@@3IB ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn strncpy_s ?_Xlength_error@std@@YAXPEBD@Z ??1IResource@nvrhi@@MEAA@XZ ?getNativeObject@IResource@nvrhi@@UEAA?AUObject@2@I@Z ??_GIResource@nvrhi@@MEAAPEAXI@Z ??_EIResource@nvrhi@@MEAAPEAXI@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ?deallocate@?$allocator@D@std@@QEAAXQEAD_K@Z ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ?getFormatInfo@nvrhi@@YAAEBUFormatInfo@1@W4Format@1@@Z ??_GIInputLayout@nvrhi@@UEAAPEAXI@Z ??_EIInputLayout@nvrhi@@UEAAPEAXI@Z ??1ShaderDesc@nvrhi@@QEAA@XZ ??_GIShader@nvrhi@@UEAAPEAXI@Z ??_EIShader@nvrhi@@UEAAPEAXI@Z ??_GIShaderLibrary@nvrhi@@UEAAPEAXI@Z ??_EIShaderLibrary@nvrhi@@UEAAPEAXI@Z ??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??$_Hash_representation@I@std@@YA_KAEBI@Z ?NotSupported@utils@nvrhi@@YAXXZ ?InvalidEnum@utils@nvrhi@@YAXXZ ?deallocate@?$allocator@I@std@@QEAAXQEAI_K@Z ?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AEAAXQEAI_K1@Z ?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ ?getDxgiFormatMapping@nvrhi@@YAAEBUDxgiFormatMapping@1@W4Format@1@@Z ?AddRef@?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAKXZ ?Release@?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAKXZ ??_G?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAPEAXI@Z ??_E?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAPEAXI@Z ?resize@?$vector@DV?$allocator@D@std@@@std@@QEAAX_K@Z ?_Xlength@?$vector@DV?$allocator@D@std@@@std@@CAXXZ ?deallocate@?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@QEAAXQEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@_K@Z ?_Xlength@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@CAXXZ ?deallocate@?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@QEAAXQEAU_NV_CUSTOM_SEMANTIC@@_K@Z ??1?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@CAXXZ ?getDesc@Shader@d3d12@nvrhi@@UEBAAEBUShaderDesc@3@XZ ?getBytecode@Shader@d3d12@nvrhi@@UEBAXPEAPEBXPEA_K@Z ??_GShader@d3d12@nvrhi@@UEAAPEAXI@Z ??_EShader@d3d12@nvrhi@@UEAAPEAXI@Z ??1?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@QEAA@XZ ?getDesc@ShaderLibraryEntry@d3d12@nvrhi@@UEBAAEBUShaderDesc@3@XZ ?getBytecode@ShaderLibraryEntry@d3d12@nvrhi@@UEBAXPEAPEBXPEA_K@Z ??_GShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z ??_EShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z ?AddRef@?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAKXZ ?Release@?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAKXZ ??_G?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAPEAXI@Z ??_E?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAPEAXI@Z ?getBytecode@ShaderLibrary@d3d12@nvrhi@@UEBAXPEAPEBXPEA_K@Z ?getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z ??_GShaderLibrary@d3d12@nvrhi@@UEAAPEAXI@Z ??_EShaderLibrary@d3d12@nvrhi@@UEAAPEAXI@Z ?AddRef@?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAKXZ ?Release@?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAKXZ ??_G?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAPEAXI@Z ??_E?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAPEAXI@Z ?deallocate@?$allocator@UVertexAttributeDesc@nvrhi@@@std@@QEAAXQEAUVertexAttributeDesc@nvrhi@@_K@Z ??1?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@CAXXZ ?deallocate@?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@QEAAXQEAUD3D12_INPUT_ELEMENT_DESC@@_K@Z ??1?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@CAXXZ ??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ ?getNumAttributes@InputLayout@d3d12@nvrhi@@UEBAIXZ ?getAttributeDesc@InputLayout@d3d12@nvrhi@@UEBAPEBUVertexAttributeDesc@3@I@Z ??_GInputLayout@d3d12@nvrhi@@UEAAPEAXI@Z ??_EInputLayout@d3d12@nvrhi@@UEAAPEAXI@Z ?createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z ?createShaderSpecialization@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEAVIShader@3@PEBUShaderSpecialization@3@I@Z ?createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z ?createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z ??$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z ??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z ??$_Atomic_address_as@JU?$_Atomic_padded@K@std@@@std@@YAPECJAEAU?$_Atomic_padded@K@0@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z ??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Uninitialized_value_construct_n@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAPEAUVertexAttributeDesc@nvrhi@@PEAU12@_KAEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Zero_range@PEAD@std@@YAPEADQEAD0@Z ??$_Zero_range@PEAI@std@@YAPEAIQEAI0@Z ??$_Copy_memmove@PEAUD3D12_INPUT_ELEMENT_DESC@@PEAU1@@std@@YAPEAUD3D12_INPUT_ELEMENT_DESC@@PEAU1@00@Z ??$_Copy_memmove@PEAU_NV_CUSTOM_SEMANTIC@@PEAU1@@std@@YAPEAU_NV_CUSTOM_SEMANTIC@@PEAU1@00@Z ??$_Copy_memmove@PEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@PEAPEAU1@@std@@YAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@PEAPEAU1@00@Z ??$_Copy_memmove@PEADPEAD@std@@YAPEADPEAD00@Z ??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z ?catch$0@?0???$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z@4HA ?catch$0@?0???$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?catch$4@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0???$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z@4HA ?dtor$0@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA ?dtor$0@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA ?dtor$10@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA ?dtor$14@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA ?dtor$16@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA ?dtor$1@?0???$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z@4HA ?dtor$1@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA ?dtor$2@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA ?dtor$2@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA ?dtor$3@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA ?dtor$3@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA ?dtor$4@?0??createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z@4HA ?dtor$6@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA ?dtor$8@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA ?dtor$9@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA ?dtor$9@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA _CxxThrowException __CxxFrameHandler4 __catch$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??_GIResource@nvrhi@@MEAAPEAXI@Z $pdata$??_GIResource@nvrhi@@MEAAPEAXI@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?deallocate@?$allocator@D@std@@QEAAXQEAD_K@Z $pdata$?deallocate@?$allocator@D@std@@QEAAXQEAD_K@Z $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??_GIInputLayout@nvrhi@@UEAAPEAXI@Z $pdata$??_GIInputLayout@nvrhi@@UEAAPEAXI@Z $unwind$??1ShaderDesc@nvrhi@@QEAA@XZ $pdata$??1ShaderDesc@nvrhi@@QEAA@XZ $unwind$??_GIShader@nvrhi@@UEAAPEAXI@Z $pdata$??_GIShader@nvrhi@@UEAAPEAXI@Z $unwind$??_GIShaderLibrary@nvrhi@@UEAAPEAXI@Z $pdata$??_GIShaderLibrary@nvrhi@@UEAAPEAXI@Z $unwind$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$?deallocate@?$allocator@I@std@@QEAAXQEAI_K@Z $pdata$?deallocate@?$allocator@I@std@@QEAAXQEAI_K@Z $unwind$?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AEAAXQEAI_K1@Z $pdata$?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AEAAXQEAI_K1@Z $unwind$?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ $unwind$?Release@?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAKXZ $pdata$?Release@?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAKXZ $unwind$??_G?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAPEAXI@Z $pdata$??_G?$RefCounter@VIShader@nvrhi@@@nvrhi@@UEAAPEAXI@Z $unwind$?resize@?$vector@DV?$allocator@D@std@@@std@@QEAAX_K@Z $pdata$?resize@?$vector@DV?$allocator@D@std@@@std@@QEAAX_K@Z $unwind$?_Xlength@?$vector@DV?$allocator@D@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@DV?$allocator@D@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@QEAAXQEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@_K@Z $pdata$?deallocate@?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@QEAAXQEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@_K@Z $unwind$?_Xlength@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@QEAAXQEAU_NV_CUSTOM_SEMANTIC@@_K@Z $pdata$?deallocate@?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@QEAAXQEAU_NV_CUSTOM_SEMANTIC@@_K@Z $unwind$??1?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@CAXXZ $unwind$??_GShader@d3d12@nvrhi@@UEAAPEAXI@Z $pdata$??_GShader@d3d12@nvrhi@@UEAAPEAXI@Z $unwind$??1?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??_GShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z $pdata$??_GShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z $cppxdata$??_GShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z $stateUnwindMap$??_GShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z $ip2state$??_GShaderLibraryEntry@d3d12@nvrhi@@UEAAPEAXI@Z $unwind$?Release@?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAKXZ $pdata$?Release@?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAKXZ $unwind$??_G?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAPEAXI@Z $pdata$??_G?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@UEAAPEAXI@Z $unwind$?getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z $pdata$?getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z $cppxdata$?getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z $stateUnwindMap$?getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z $ip2state$?getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z $unwind$?dtor$0@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA $pdata$?dtor$0@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA $unwind$?dtor$14@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA $pdata$?dtor$14@?0??getShader@ShaderLibrary@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEBDW4ShaderType@3@@Z@4HA $unwind$??_GShaderLibrary@d3d12@nvrhi@@UEAAPEAXI@Z $pdata$??_GShaderLibrary@d3d12@nvrhi@@UEAAPEAXI@Z $unwind$?Release@?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAKXZ $pdata$?Release@?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAKXZ $unwind$??_G?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAPEAXI@Z $pdata$??_G?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@UEAAPEAXI@Z $unwind$?deallocate@?$allocator@UVertexAttributeDesc@nvrhi@@@std@@QEAAXQEAUVertexAttributeDesc@nvrhi@@_K@Z $pdata$?deallocate@?$allocator@UVertexAttributeDesc@nvrhi@@@std@@QEAAXQEAUVertexAttributeDesc@nvrhi@@_K@Z $unwind$??1?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@QEAAXQEAUD3D12_INPUT_ELEMENT_DESC@@_K@Z $pdata$?deallocate@?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@QEAAXQEAUD3D12_INPUT_ELEMENT_DESC@@_K@Z $unwind$??1?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@CAXXZ $unwind$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ $chain$0$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ $pdata$0$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ $chain$1$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ $pdata$1$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $chain$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $pdata$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $chain$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $pdata$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $chain$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $pdata$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $chain$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $pdata$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $chain$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $pdata$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAX_K@Z $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $chain$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $pdata$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $chain$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $pdata$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $chain$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $pdata$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ $unwind$??_GInputLayout@d3d12@nvrhi@@UEAAPEAXI@Z $pdata$??_GInputLayout@d3d12@nvrhi@@UEAAPEAXI@Z $unwind$?createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z $pdata$?createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z $cppxdata$?createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z $stateUnwindMap$?createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z $ip2state$?createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z $unwind$?dtor$1@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA $pdata$?dtor$1@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA $unwind$?dtor$16@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA $pdata$?dtor$16@?0??createShader@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@AEBUShaderDesc@3@PEBX_K@Z@4HA $unwind$?createShaderSpecialization@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEAVIShader@3@PEBUShaderSpecialization@3@I@Z $pdata$?createShaderSpecialization@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShader@nvrhi@@@3@PEAVIShader@3@PEBUShaderSpecialization@3@I@Z $unwind$?createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z $pdata$?createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z $cppxdata$?createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z $stateUnwindMap$?createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z $ip2state$?createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z $unwind$?dtor$4@?0??createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z@4HA $pdata$?dtor$4@?0??createShaderLibrary@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@3@PEBX_K@Z@4HA $unwind$?createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z $pdata$?createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z $cppxdata$?createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z $stateUnwindMap$?createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z $ip2state$?createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z $unwind$?dtor$0@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA $pdata$?dtor$0@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA $unwind$?dtor$10@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA $pdata$?dtor$10@?0??createInputLayout@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@3@PEBUVertexAttributeDesc@3@IPEAVIShader@3@@Z@4HA $unwind$??$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z $pdata$??$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z $cppxdata$??$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z $stateUnwindMap$??$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z $ip2state$??$_Try_emplace@AEBI$$V@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@_N@1@AEBI@Z $unwind$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $chain$0$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $pdata$0$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $chain$1$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $pdata$1$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $chain$2$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $pdata$2$??$_Destroy_range@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAXPEAUVertexAttributeDesc@nvrhi@@QEAU12@AEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUD3D12_INPUT_ELEMENT_DESC@@@?$vector@UD3D12_INPUT_ELEMENT_DESC@@V?$allocator@UD3D12_INPUT_ELEMENT_DESC@@@std@@@std@@AEAAPEAUD3D12_INPUT_ELEMENT_DESC@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$4@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$4@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UVertexAttributeDesc@nvrhi@@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Uninitialized_value_construct_n@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAPEAUVertexAttributeDesc@nvrhi@@PEAU12@_KAEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $pdata$??$_Uninitialized_value_construct_n@V?$allocator@UVertexAttributeDesc@nvrhi@@@std@@@std@@YAPEAUVertexAttributeDesc@nvrhi@@PEAU12@_KAEAV?$allocator@UVertexAttributeDesc@nvrhi@@@0@@Z $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@U_NV_CUSTOM_SEMANTIC@@V?$allocator@U_NV_CUSTOM_SEMANTIC@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $pdata$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $cppxdata$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $stateUnwindMap$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $tryMap$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $handlerMap$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $ip2state$??$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@?$vector@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@V?$allocator@PEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@@std@@@std@@AEAAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@QEAPEAU2@$$QEAPEAU2@@Z@4HA $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@DV?$allocator@D@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Zero_range@PEAD@std@@YAPEADQEAD0@Z $pdata$??$_Zero_range@PEAD@std@@YAPEADQEAD0@Z $unwind$??$_Zero_range@PEAI@std@@YAPEAIQEAI0@Z $pdata$??$_Zero_range@PEAI@std@@YAPEAIQEAI0@Z $unwind$??$_Copy_memmove@PEAUD3D12_INPUT_ELEMENT_DESC@@PEAU1@@std@@YAPEAUD3D12_INPUT_ELEMENT_DESC@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUD3D12_INPUT_ELEMENT_DESC@@PEAU1@@std@@YAPEAUD3D12_INPUT_ELEMENT_DESC@@PEAU1@00@Z $unwind$??$_Copy_memmove@PEAU_NV_CUSTOM_SEMANTIC@@PEAU1@@std@@YAPEAU_NV_CUSTOM_SEMANTIC@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAU_NV_CUSTOM_SEMANTIC@@PEAU1@@std@@YAPEAU_NV_CUSTOM_SEMANTIC@@PEAU1@00@Z $unwind$??$_Copy_memmove@PEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@PEAPEAU1@@std@@YAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@PEAPEAU1@00@Z $pdata$??$_Copy_memmove@PEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@PEAPEAU1@@std@@YAPEAPEAUNVAPI_D3D12_PSO_EXTENSION_DESC_V1@@PEAPEAU1@00@Z $unwind$??$_Copy_memmove@PEADPEAD@std@@YAPEADPEAD00@Z $pdata$??$_Copy_memmove@PEADPEAD@std@@YAPEADPEAD00@Z $unwind$??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z $pdata$??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z ??_7IResource@nvrhi@@6B@ ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ??_7IInputLayout@nvrhi@@6B@ ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7IShader@nvrhi@@6B@ ??_7IShaderLibrary@nvrhi@@6B@ ??_7?$RefCounter@VIShader@nvrhi@@@nvrhi@@6B@ ??_7Shader@d3d12@nvrhi@@6B@ ??_7ShaderLibraryEntry@d3d12@nvrhi@@6B@ ??_7?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@6B@ ??_7ShaderLibrary@d3d12@nvrhi@@6B@ ??_7?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@6B@ ??_7InputLayout@d3d12@nvrhi@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_R4IResource@nvrhi@@6B@ ??_R0?AVIResource@nvrhi@@@8 ??_R3IResource@nvrhi@@8 ??_R2IResource@nvrhi@@8 ??_R1A@?0A@EA@IResource@nvrhi@@8 ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R4IInputLayout@nvrhi@@6B@ ??_R0?AVIInputLayout@nvrhi@@@8 ??_R3IInputLayout@nvrhi@@8 ??_R2IInputLayout@nvrhi@@8 ??_R1A@?0A@EA@IInputLayout@nvrhi@@8 ??_R4IShader@nvrhi@@6B@ ??_R0?AVIShader@nvrhi@@@8 ??_R3IShader@nvrhi@@8 ??_R2IShader@nvrhi@@8 ??_R1A@?0A@EA@IShader@nvrhi@@8 ??_R4IShaderLibrary@nvrhi@@6B@ ??_R0?AVIShaderLibrary@nvrhi@@@8 ??_R3IShaderLibrary@nvrhi@@8 ??_R2IShaderLibrary@nvrhi@@8 ??_R1A@?0A@EA@IShaderLibrary@nvrhi@@8 ??_R4Shader@d3d12@nvrhi@@6B@ ??_R0?AVShader@d3d12@nvrhi@@@8 ??_R3Shader@d3d12@nvrhi@@8 ??_R2Shader@d3d12@nvrhi@@8 ??_R1A@?0A@EA@Shader@d3d12@nvrhi@@8 ??_R1A@?0A@EA@?$RefCounter@VIShader@nvrhi@@@nvrhi@@8 ??_R0?AV?$RefCounter@VIShader@nvrhi@@@nvrhi@@@8 ??_R3?$RefCounter@VIShader@nvrhi@@@nvrhi@@8 ??_R2?$RefCounter@VIShader@nvrhi@@@nvrhi@@8 ??_R4?$RefCounter@VIShader@nvrhi@@@nvrhi@@6B@ ??_R4ShaderLibrary@d3d12@nvrhi@@6B@ ??_R0?AVShaderLibrary@d3d12@nvrhi@@@8 ??_R3ShaderLibrary@d3d12@nvrhi@@8 ??_R2ShaderLibrary@d3d12@nvrhi@@8 ??_R1A@?0A@EA@ShaderLibrary@d3d12@nvrhi@@8 ??_R1A@?0A@EA@?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@8 ??_R0?AV?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@@8 ??_R3?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@8 ??_R2?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@8 ??_R4ShaderLibraryEntry@d3d12@nvrhi@@6B@ ??_R0?AVShaderLibraryEntry@d3d12@nvrhi@@@8 ??_R3ShaderLibraryEntry@d3d12@nvrhi@@8 ??_R2ShaderLibraryEntry@d3d12@nvrhi@@8 ??_R1A@?0A@EA@ShaderLibraryEntry@d3d12@nvrhi@@8 ??_R4?$RefCounter@VIShaderLibrary@nvrhi@@@nvrhi@@6B@ ??_R4InputLayout@d3d12@nvrhi@@6B@ ??_R0?AVInputLayout@d3d12@nvrhi@@@8 ??_R3InputLayout@d3d12@nvrhi@@8 ??_R2InputLayout@d3d12@nvrhi@@8 ??_R1A@?0A@EA@InputLayout@d3d12@nvrhi@@8 ??_R1A@?0A@EA@?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@8 ??_R0?AV?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@@8 ??_R3?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@8 ??_R2?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@8 ??_R4?$RefCounter@VIInputLayout@nvrhi@@@nvrhi@@6B@ __ImageBase __real@5f000000 