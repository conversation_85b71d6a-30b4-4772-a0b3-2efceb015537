d�8t馟h�9 �      .drectve        j  �0               
 .debug$S        � >2  
;        @ B.debug$T        t   Z;             @ B.rdata          8   �;             @ @@.text$mn           <              P`.debug$S        �   
< �<        @B.text$mn        :   *= d=         P`.debug$S          �= �?        @B.text$mn           @              P`.debug$S          @ &A        @B.text$mn        K   bA              P`.debug$S        �  瑼 ID        @B.text$mn        r  ME 縂         P`.debug$S        \  鸊 WY     �   @B.text$x         F   W^ 漗         P`.text$mn        P   籢              P`.debug$S        H  _ Sa        @B.text$mn        Z   Wb              P`.debug$S          眀 礵        @B.text$mn        �   Ae              P`.debug$S        �  舉 羐        @B.text$mn           9h              P`.debug$S           =h =i        @B.text$mn           yi              P`.debug$S          }i 卝        @B.text$mn           羓              P`.debug$S          舑 裬        @B.text$mn           
l              P`.debug$S          l (m        @B.text$mn        �   dm              P`.debug$S        �   &n 騨        @B.text$mn           o              P`.debug$S        �   +o 鉶        @B.text$mn        <   p Gp         P`.debug$S        0  ep 晀     
   @B.text$mn        <   鵴 5r         P`.debug$S        L  Sr 焥     
   @B.text$mn        !   t $t         P`.debug$S        <  8t tu        @B.text$mn        2   皍 鈛         P`.debug$S        <  鰑 2w        @B.text$mn        "   獁              P`.debug$S        �  蘷 `y        @B.text$mn        "    z              P`.debug$S        �  "z 簕        @B.text$mn        "   Z|              P`.debug$S        �  || ~        @B.text$mn        "   竳              P`.debug$S        �  趡 j�        @B.text$mn        "   
�              P`.debug$S        �  ,� 競        @B.text$mn        "   X�              P`.debug$S        �  z� �        @B.text$mn        ^   緟 �         P`.debug$S        T  0� 剦        @B.text$mn           L�              P`.debug$S        �   O� #�        @B.text$mn        t   _� 計         P`.debug$S        L  鐙 3�        @B.text$mn           #� 6�         P`.debug$S        �   J� .�        @B.text$mn           V� i�         P`.debug$S        �   }� ]�        @B.text$mn        ~  檻              P`.debug$S        �  � 邩     2   @B.text$mn        !   託 魵         P`.debug$S        �    隂        @B.text$mn        !   &� G�         P`.debug$S        �   Q� -�        @B.text$mn        !   i� 姖         P`.debug$S        �   敐 h�        @B.text$mn        u    �         P`.debug$S        �  -� �        @B.text$mn        B   拧 �         P`.debug$S           %� %�        @B.text$mn        B   a� ＃         P`.debug$S          粒 绚        @B.text$mn        B   
� O�         P`.debug$S        �   m� i�        @B.text$mn        H   ウ              P`.debug$S        �  恙 报        @B.text$mn        
   嫂              P`.debug$S          蜘 颡        @B.text$mn        +   .�              P`.debug$S        p  Y� 涩        @B.text$mn            A� a�         P`.debug$S        �   � C�        @B.text$mn           � 惍         P`.debug$S          ぎ 腐        @B.text$mn        �   舣          P`.debug$S        |  蛋 1�        @B.text$mn          I� K�         P`.debug$S        �
  苟 嵙     F   @B.text$x             I� i�         P`.text$x            s� 兡         P`.text$x            嵞 澞         P`.text$x             纺         P`.text$x         )   聊 昴         P`.text$mn        �   裟          P`.debug$S        ,  团      &   @B.text$x            u� 佀         P`.text$x            嬎 椝         P`.text$mn        x  ∷ �         P`.debug$S        x  � 曗     p   @B.text$x            蹑 �         P`.text$x            � �         P`.text$x            !� -�         P`.text$mn        B   7� y�         P`.debug$S        �  嶇 E�        @B.text$mn        U   I� 炿         P`.debug$S           错        @B.text$mn           愵              P`.debug$S        �   曨 m�        @B.text$mn           ╋              P`.debug$S        �   憋 曫        @B.text$mn           佯              P`.debug$S        D  莛  �        @B.text$mn        W   p�              P`.debug$S        �  球 o�     
   @B.text$mn          郁 睇         P`.debug$S        �  [� 
     �   @B.text$x             �         P`.text$x            � �         P`.text$mn           � �         P`.debug$S        �   � �        @B.xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata              *        @0@.xdata             H             @0@.pdata             P \        @0@.xdata             z             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata               ,        @0@.xdata             J             @0@.pdata             V b        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata          	   2 ;        @@.xdata             O U        @@.xdata             _             @@.xdata             b r        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata                      @0@.xdata          	   . 7        @@.xdata             K Q        @@.xdata             [             @@.xdata              ` �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata                      @0@.xdata             / ?        @0@.pdata             S _        @0@.xdata          	   } �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	   �         @@.xdata                      @@.xdata             (             @@.voltbl            +               .xdata             ,             @0@.pdata             4 @        @0@.voltbl            ^               .xdata             _             @0@.pdata             g s        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             2 8        @@.xdata             B             @@.xdata             G             @0@.pdata             O [        @0@.xdata             y             @0@.pdata             � �        @0@.xdata          (   � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             . @        @@.xdata             ^             @@.xdata             s             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �         @0@.pdata              #        @0@.xdata          	   A J        @@.xdata             ^ u        @@.xdata          
   �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	    
        @@.xdata          $   ! E        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata          $    %        @0@.pdata             9 E        @0@.xdata          	   c l        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	     $         @@.xdata             8  >         @@.xdata             H              @@.xdata             K  [         @0@.pdata             o  {         @0@.xdata          	   �  �         @@.xdata             �  �         @@.xdata             �              @@.xdata             �  �         @0@.pdata             �  !        @0@.xdata          	   ! (!        @@.xdata             <! B!        @@.xdata             L!             @@.xdata             O! k!        @0@.pdata             ! �!        @0@.xdata          
   �! �!        @@.xdata             �! �!        @@.xdata             �! �!        @@.xdata             �! "        @@.xdata             "             @@.xdata             "             @0@.pdata             #" /"        @0@.voltbl            M"               .xdata             N"             @0@.pdata             V" b"        @0@.rdata          (   �" �"        @@@.rdata             �" �"        @@@.rdata             #             @@@.rdata             "# :#        @@@.rdata             X# p#        @@@.rdata             �#             @@@.xdata$x           �# �#        @@@.xdata$x           �# �#        @@@.data$r         /   
$ <$        @@�.xdata$x        $   F$ j$        @@@.data$r         $   ~$ �$        @@�.xdata$x        $   �$ �$        @@@.data$r         $   �$ %        @@�.xdata$x        $   % 6%        @@@.rdata             J%             @@@.data               Z%             @ @�.rdata          8   z% �%        @@@.rdata          8   �% 0&        @@@.rdata          8   v& �&        @@@.rdata             �&             @@@.rdata          ,   '             @@@.rdata          1   0'             @@@.rdata             a'             @@@.rdata$r        $   q' �'        @@@.data$rs        &   �' �'        @@�.rdata$r           �' �'        @@@.rdata$r           ( 
(        @@@.rdata$r        $   ( ;(        @@@.rdata$r        $   O( s(        @@@.rdata$r           �( �(        @@@.rdata$r           �( �(        @@@.rdata$r        $   �( �(        @@@.rdata$r        $   �( !)        @@@.rdata$r           ?) S)        @@@.rdata$r           ]) q)        @@@.rdata$r        $   �) �)        @@@.rdata$r        $   �) �)        @@@.rdata$r           �) *        @@@.rdata$r           * 9*        @@@.rdata$r        $   W* {*        @@@.rdata$r        $   �* �*        @@@.data$rs        -   �* �*        @@�.rdata$r           + +        @@@.rdata$r           &+ :+        @@@.rdata$r        $   N+ r+        @@@.rdata$r        $   �+ �+        @@@.data$rs        2   �+ �+        @@�.rdata$r           , ,        @@@.rdata$r        $   ", F,        @@@.rdata$r        $   n, �,        @@@.rdata$r        $   �, �,        @@@.data$rs        C   �, !-        @P�.rdata$r           +- ?-        @@@.rdata$r           I- e-        @@@.rdata$r        $   �- �-        @@@.debug$S        4   �- �-        @B.debug$S        4   
. A.        @B.debug$S        @   U. �.        @B.debug$S        @   �. �.        @B.debug$S        8   �. 5/        @B.debug$S        P   I/ �/        @B.debug$S        D   �/ �/        @B.chks64         �	  0              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   
  q     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\nvrhi_d3d12.dir\Release\d3d12-meshlets.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $d3d12  $rt 
 $cluster  $ObjectTypes 	 $stdext    �       �        nvrhi::EntireBuffer  鵨    D3D_INCLUDE_LOCAL  鵨   D3D_INCLUDE_SYSTEM 1 鵪    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES - �   std::chrono::steady_clock::is_steady  癳    D3D_SVC_SCALAR  癳   D3D_SVC_VECTOR  癳   D3D_SVC_MATRIX_ROWS  癳   D3D_SVC_MATRIX_COLUMNS  癳   D3D_SVC_OBJECT  癳   D3D_SVC_STRUCT   癳   D3D_SVC_INTERFACE_CLASS " 癳   D3D_SVC_INTERFACE_POINTER & E   std::ratio<1,1000000000>::num  榝   D3D_SVF_USERPACKED * E  � 蕷;std::ratio<1,1000000000>::den  榝   D3D_SVF_USED " 榝   D3D_SVF_INTERFACE_POINTER $ 榝   D3D_SVF_INTERFACE_PARAMETER  杄    D3D_SVT_VOID  杄   D3D_SVT_BOOL  杄   D3D_SVT_INT  杄   D3D_SVT_FLOAT  杄   D3D_SVT_STRING  杄   D3D_SVT_TEXTURE  杄   D3D_SVT_TEXTURE1D  杄   D3D_SVT_TEXTURE2D  杄   D3D_SVT_TEXTURE3D  杄  	 D3D_SVT_TEXTURECUBE  杄  
 D3D_SVT_SAMPLER  杄   D3D_SVT_SAMPLER1D  杄   D3D_SVT_SAMPLER2D  杄  
 D3D_SVT_SAMPLER3D  杄   D3D_SVT_SAMPLERCUBE  杄   D3D_SVT_PIXELSHADER  杄   D3D_SVT_VERTEXSHADER  杄   D3D_SVT_PIXELFRAGMENT  杄   D3D_SVT_VERTEXFRAGMENT  杄   D3D_SVT_UINT  杄   D3D_SVT_UINT8  杄   D3D_SVT_GEOMETRYSHADER  杄   D3D_SVT_RASTERIZER  杄   D3D_SVT_DEPTHSTENCIL  杄   D3D_SVT_BLEND  杄   D3D_SVT_BUFFER  杄   D3D_SVT_CBUFFER  杄   D3D_SVT_TBUFFER  杄   D3D_SVT_TEXTURE1DARRAY  杄   D3D_SVT_TEXTURE2DARRAY ! 杄   D3D_SVT_RENDERTARGETVIEW ! 杄   D3D_SVT_DEPTHSTENCILVIEW  杄    D3D_SVT_TEXTURE2DMS ! 杄  ! D3D_SVT_TEXTURE2DMSARRAY ! 杄  " D3D_SVT_TEXTURECUBEARRAY  杄  # D3D_SVT_HULLSHADER  杄  $ D3D_SVT_DOMAINSHADER " 杄  % D3D_SVT_INTERFACE_POINTER  杄  & D3D_SVT_COMPUTESHADER  杄  ' D3D_SVT_DOUBLE  杄  ( D3D_SVT_RWTEXTURE1D ! 杄  ) D3D_SVT_RWTEXTURE1DARRAY  杄  * D3D_SVT_RWTEXTURE2D ! 杄  + D3D_SVT_RWTEXTURE2DARRAY  杄  , D3D_SVT_RWTEXTURE3D  杄  - D3D_SVT_RWBUFFER # 杄  . D3D_SVT_BYTEADDRESS_BUFFER % 杄  / D3D_SVT_RWBYTEADDRESS_BUFFER " 杄  0 D3D_SVT_STRUCTURED_BUFFER $ 杄  1 D3D_SVT_RWSTRUCTURED_BUFFER ) 杄  2 D3D_SVT_APPEND_STRUCTURED_BUFFER * 杄  3 D3D_SVT_CONSUME_STRUCTURED_BUFFER L    ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos  +   PARSE_CANONICALIZE  +   PARSE_FRIENDLY  +   PARSE_SECURITY_URL  +   PARSE_ROOTDOCUMENT  +   PARSE_DOCUMENT  +   PARSE_ANCHOR ! +   PARSE_ENCODE_IS_UNESCAPE  +   PARSE_DECODE_IS_ESCAPE  +  	 PARSE_PATH_FROM_URL  +  
 PARSE_URL_FROM_PATH  +   PARSE_MIME  +   PARSE_SERVER  +  
 PARSE_SCHEMA  +   PARSE_SITE  +   PARSE_DOMAIN  +   PARSE_LOCATION  +   PARSE_SECURITY_DOMAIN  +   PARSE_ESCAPE  漞   D3D_SIF_USERPACKED # 漞   D3D_SIF_COMPARISON_SAMPLER  �,   PSU_DEFAULT $ 漞   D3D_SIF_TEXTURE_COMPONENT_0 $ 漞   D3D_SIF_TEXTURE_COMPONENT_1 # 漞   D3D_SIF_TEXTURE_COMPONENTS  '+   QUERY_EXPIRATION_DATE " '+   QUERY_TIME_OF_LAST_CHANGE  '+   QUERY_CONTENT_ENCODING  '+   QUERY_CONTENT_TYPE   d   std::_Iosb<int>::skipws  '+   QUERY_REFRESH  '+   QUERY_RECOMBINE ! d   std::_Iosb<int>::unitbuf  '+   QUERY_CAN_NAVIGATE  '+   QUERY_USES_NETWORK # d   std::_Iosb<int>::uppercase  '+  	 QUERY_IS_CACHED  噀    D3D_SIT_CBUFFER  噀   D3D_SIT_TBUFFER   '+  
 QUERY_IS_INSTALLEDENTRY " d   std::_Iosb<int>::showbase " '+   QUERY_IS_CACHED_OR_MAPPED  噀   D3D_SIT_TEXTURE  '+   QUERY_USES_CACHE  噀   D3D_SIT_SAMPLER  '+  
 QUERY_IS_SECURE # d   std::_Iosb<int>::showpoint  噀   D3D_SIT_UAV_RWTYPED  噀   D3D_SIT_STRUCTURED  '+   QUERY_IS_SAFE ! 噀   D3D_SIT_UAV_RWSTRUCTURED ! d    std::_Iosb<int>::showpos ! '+   QUERY_USES_HISTORYFOLDER  �+    ServerApplication  噀   D3D_SIT_BYTEADDRESS  d  @ std::_Iosb<int>::left " 噀   D3D_SIT_UAV_RWBYTEADDRESS & 噀  	 D3D_SIT_UAV_APPEND_STRUCTURED  d  � std::_Iosb<int>::right ' 噀  
 D3D_SIT_UAV_CONSUME_STRUCTURED  n.    IdleShutdown . 噀   D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER ( 噀   D3D_SIT_RTACCELERATIONSTRUCTURE " d   std::_Iosb<int>::internal  d   std::_Iosb<int>::dec  d   std::_Iosb<int>::oct  d   std::_Iosb<int>::hex $ d   std::_Iosb<int>::scientific  d    std::_Iosb<int>::fixed P    std::allocator<ID3D12CommandList *>::_Minimum_asan_allocation_alignment " d   0std::_Iosb<int>::hexfloat # d   @std::_Iosb<int>::boolalpha " d  � �std::_Iosb<int>::_Stdio  媑   D3D_CBF_USERPACKED % d  �std::_Iosb<int>::adjustfield # d   std::_Iosb<int>::basefield  癴    D3D_CT_CBUFFER $ d   0std::_Iosb<int>::floatfield  癴   D3D_CT_TBUFFER " 癴   D3D_CT_INTERFACE_POINTERS " 癴   D3D_CT_RESOURCE_BIND_INFO ! d    std::_Iosb<int>::goodbit   d   std::_Iosb<int>::eofbit   E  std::ratio<3600,1>::num ! d   std::_Iosb<int>::failbit   E   std::ratio<3600,1>::den   d   std::_Iosb<int>::badbit  〆    D3D_NAME_UNDEFINED  〆   D3D_NAME_POSITION  〆   D3D_NAME_CLIP_DISTANCE  d   std::_Iosb<int>::in  〆   D3D_NAME_CULL_DISTANCE + 〆   D3D_NAME_RENDER_TARGET_ARRAY_INDEX  d   std::_Iosb<int>::out & 〆   D3D_NAME_VIEWPORT_ARRAY_INDEX  〆   D3D_NAME_VERTEX_ID  〆   D3D_NAME_PRIMITIVE_ID  d   std::_Iosb<int>::ate  〆   D3D_NAME_INSTANCE_ID  d   std::_Iosb<int>::app  〆  	 D3D_NAME_IS_FRONT_FACE  〆  
 D3D_NAME_SAMPLE_INDEX , 〆   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR  d   std::_Iosb<int>::trunc Q    std::allocator<nvrhi::BufferBarrier>::_Minimum_asan_allocation_alignment . 〆   D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR # d  @ std::_Iosb<int>::_Nocreate + 〆  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR - 〆   D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR $ d  � std::_Iosb<int>::_Noreplace . 〆   D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR 3    nvrhi::d3d12::BufferChunk::c_sizeAlignment / 〆   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR   d    std::_Iosb<int>::binary  〆   D3D_NAME_BARYCENTRICS  〆   D3D_NAME_SHADINGRATE  〆   D3D_NAME_CULLPRIMITIVE  〆  @ D3D_NAME_TARGET  〆  A D3D_NAME_DEPTH  〆  B D3D_NAME_COVERAGE % 〆  C D3D_NAME_DEPTH_GREATER_EQUAL  d    std::_Iosb<int>::beg " 〆  D D3D_NAME_DEPTH_LESS_EQUAL  d   std::_Iosb<int>::cur  〆  E D3D_NAME_STENCIL_REF   〆  F D3D_NAME_INNER_COVERAGE  d   std::_Iosb<int>::end , d  @ std::_Iosb<int>::_Default_open_prot < 鏶   NV_GPU_WORKSTATION_FEATURE_TYPE_NVIDIA_RTX_VR_READY  峞   D3D_RETURN_TYPE_UNORM  峞   D3D_RETURN_TYPE_SNORM  峞   D3D_RETURN_TYPE_SINT  峞   D3D_RETURN_TYPE_UINT  峞   D3D_RETURN_TYPE_FLOAT  峞   D3D_RETURN_TYPE_MIXED  峞   D3D_RETURN_TYPE_DOUBLE " 峞   D3D_RETURN_TYPE_CONTINUED ' 焑    D3D_REGISTER_COMPONENT_UNKNOWN & 焑   D3D_REGISTER_COMPONENT_UINT32 & 焑   D3D_REGISTER_COMPONENT_SINT32 ' 焑   D3D_REGISTER_COMPONENT_FLOAT32  %+    FEATURE_OBJECT_CACHING & 焑   D3D_REGISTER_COMPONENT_UINT16  %+   FEATURE_ZONE_ELEVATION & 焑   D3D_REGISTER_COMPONENT_SINT16  %+   FEATURE_MIME_HANDLING ' 焑   D3D_REGISTER_COMPONENT_FLOAT16  %+   FEATURE_MIME_SNIFFING & 焑   D3D_REGISTER_COMPONENT_UINT64 & 焑   D3D_REGISTER_COMPONENT_SINT64 $ %+   FEATURE_WINDOW_RESTRICTIONS ' 焑  	 D3D_REGISTER_COMPONENT_FLOAT64 & %+   FEATURE_WEBOC_POPUPMANAGEMENT  %+   FEATURE_BEHAVIORS $ %+   FEATURE_DISABLE_MK_PROTOCOL & %+   FEATURE_LOCALMACHINE_LOCKDOWN  %+  	 FEATURE_SECURITYBAND ( %+  
 FEATURE_RESTRICT_ACTIVEXINSTALL & %+   FEATURE_VALIDATE_NAVIGATE_URL & %+   FEATURE_RESTRICT_FILEDOWNLOAD ! %+  
 FEATURE_ADDON_MANAGEMENT " %+   FEATURE_PROTOCOL_LOCKDOWN / %+   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE " %+   FEATURE_SAFE_BINDTOOBJECT # %+   FEATURE_UNC_SAVEDFILECHECK ) 璭    D3D_TESSELLATOR_DOMAIN_UNDEFINED / %+   FEATURE_GET_URL_DOM_FILEPATH_UNENCODED ' 璭   D3D_TESSELLATOR_DOMAIN_ISOLINE   %+   FEATURE_TABBED_BROWSING # 璭   D3D_TESSELLATOR_DOMAIN_TRI  %+   FEATURE_SSLUX $ 璭   D3D_TESSELLATOR_DOMAIN_QUAD * %+   FEATURE_DISABLE_NAVIGATION_SOUNDS + %+   FEATURE_DISABLE_LEGACY_COMPRESSION & %+   FEATURE_FORCE_ADDR_AND_STATUS  %+   FEATURE_XMLHTTP ( %+   FEATURE_DISABLE_TELNET_PROTOCOL  %+   FEATURE_FEEDS / 秄    D3D_TESSELLATOR_PARTITIONING_UNDEFINED $ %+   FEATURE_BLOCK_INPUT_PROMPTS - 秄   D3D_TESSELLATOR_PARTITIONING_INTEGER * 秄   D3D_TESSELLATOR_PARTITIONING_POW2 4 秄   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 5 秄   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN ) 萬    D3D_TESSELLATOR_OUTPUT_UNDEFINED % 萬   D3D_TESSELLATOR_OUTPUT_POINT $ 萬   D3D_TESSELLATOR_OUTPUT_LINE + 萬   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW , 萬   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW h    std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> >::_Minimum_asan_allocation_alignment ' 圦    D3D12_COMMAND_LIST_TYPE_DIRECT ' 圦   D3D12_COMMAND_LIST_TYPE_BUNDLE ( 圦   D3D12_COMMAND_LIST_TYPE_COMPUTE % 圦   D3D12_COMMAND_LIST_TYPE_COPY - 圦   D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE . 圦   D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS - 圦   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE Z    std::allocator<nvrhi::RefCountPtr<IUnknown> >::_Minimum_asan_allocation_alignment & 緀   NV_LICENSE_FEATURE_NVIDIA_RTX , 醙   D3D12_PRIMITIVE_TOPOLOGY_TYPE_POINT + 醙   D3D12_PRIMITIVE_TOPOLOGY_TYPE_LINE / 醙   D3D12_PRIMITIVE_TOPOLOGY_TYPE_TRIANGLE �    std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> >::_Minimum_asan_allocation_alignment M    std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment  �+    URLZONE_LOCAL_MACHINE  �+   URLZONE_INTRANET  �+   URLZONE_TRUSTED  �+   URLZONE_INTERNET  E  < std::ratio<60,1>::num  E   std::ratio<60,1>::den  �,    URLZONEREG_DEFAULT  �,   URLZONEREG_HKLM .   	�       �nvrhi::c_VersionSubmittedFlag # �  < nvrhi::c_VersionQueueShift " �   nvrhi::c_VersionQueueMask '   
��������nvrhi::c_VersionIDMask % 糵   D3D12_COLOR_WRITE_ENABLE_RED ' 糵   D3D12_COLOR_WRITE_ENABLE_GREEN & 糵   D3D12_COLOR_WRITE_ENABLE_BLUE ' 糵   D3D12_COLOR_WRITE_ENABLE_ALPHA  塭    D3D12_LOGIC_OP_CLEAR  塭   D3D12_LOGIC_OP_SET  塭   D3D12_LOGIC_OP_COPY % 塭   D3D12_LOGIC_OP_COPY_INVERTED  塭   D3D12_LOGIC_OP_NOOP  塭   D3D12_LOGIC_OP_INVERT 3 �  �����nvrhi::d3d12::c_InvalidDescriptorIndex  塭   D3D12_LOGIC_OP_AND 1 �        nvrhi::d3d12::c_ResourceStateUnknown  塭   D3D12_LOGIC_OP_NAND  塭   D3D12_LOGIC_OP_OR  塭  	 D3D12_LOGIC_OP_NOR  塭  
 D3D12_LOGIC_OP_XOR  塭   D3D12_LOGIC_OP_EQUIV # 塭   D3D12_LOGIC_OP_AND_REVERSE $ 塭  
 D3D12_LOGIC_OP_AND_INVERTED " 塭   D3D12_LOGIC_OP_OR_REVERSE . 峠    D3D12_LINE_RASTERIZATION_MODE_ALIASED 8 峠   D3D12_LINE_RASTERIZATION_MODE_ALPHA_ANTIALIASED 9 峠   D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_WIDE n    std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> >::_Minimum_asan_allocation_alignment   E   std::ratio<1,1000>::num   E  �std::ratio<1,1000>::den + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits # �+   BINDHANDLETYPES_DEPENDENCY ; 檈    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS : 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT 2 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND 8 檈  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 7 檈  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER : 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL 9 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT ? 檈  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE ? 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY B 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS A 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 8 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 6 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK 7 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 2 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS ; 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_AS / 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS 8 檈   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER2 1 篺    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED F 篺   D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS A 篺   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK + 蝒    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 1 蝒   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS ? 蝒   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY # E   std::ratio<1,1000000>::num ' E  �@B std::ratio<1,1000000>::den J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2  �+    TKIND_ENUM J E   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2  �+   TKIND_RECORD  �+   TKIND_MODULE  �+   TKIND_INTERFACE  �+   TKIND_DISPATCH  �+   TKIND_COCLASS  �+   TKIND_ALIAS  �+   TKIND_UNION W    std::allocator<nvrhi::VertexAttributeDesc>::_Minimum_asan_allocation_alignment L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P E  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1  �*    PIDMSI_STATUS_NORMAL  �*   PIDMSI_STATUS_NEW L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2  �*   PIDMSI_STATUS_PRELIM  �*   PIDMSI_STATUS_DRAFT ! �*   PIDMSI_STATUS_INPROGRESS L E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2  �*   PIDMSI_STATUS_EDIT K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx  �*   PIDMSI_STATUS_REVIEW  �*   PIDMSI_STATUS_PROOF K E   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy 8 磃    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD 9 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE 6 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR : 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_NO_ACCESS F 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER C 磃   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_SRV i    std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> >::_Minimum_asan_allocation_alignment f    std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> >::_Minimum_asan_allocation_alignment A    std::allocator<char>::_Minimum_asan_allocation_alignment 5 慹    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD 6 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE 5 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE 7 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_NO_ACCESS C 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER @ 慹   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_SRV ~    std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >::_Minimum_asan_allocation_alignment  <+   CC_CDECL  <+   CC_MSCPASCAL  <+   CC_PASCAL  <+   CC_MACPASCAL  <+   CC_STDCALL  <+   CC_FPFASTCALL  <+   CC_SYSCALL  <+   CC_MPWCDECL  <+   CC_MPWPASCAL  #+    FUNC_VIRTUAL  #+   FUNC_PUREVIRTUAL  #+   FUNC_NONVIRTUAL  #+   FUNC_STATIC  +    VAR_PERINSTANCE  +   VAR_STATIC  +   VAR_CONST 	�    std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0>::_Multi �   std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0>::_Standard ?    std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A    std::_String_val<std::_Simple_types<char> >::_Alloc_mask L    std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _     std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size  莋  i D3D_SHADER_MODEL_6_9 a    std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> >::_Minimum_asan_allocation_alignment A    std::allocator<bool>::_Minimum_asan_allocation_alignment U    std::allocator<D3D12_INPUT_ELEMENT_DESC>::_Minimum_asan_allocation_alignment T    ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos � �   std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::IResource>,nvrhi::RefCountPtr<nvrhi::IResource>,nvrhi::RefCountPtr<nvrhi::IResource> &&,nvrhi::RefCountPtr<nvrhi::IResource> &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::IResource>,nvrhi::RefCountPtr<nvrhi::IResource>,nvrhi::RefCountPtr<nvrhi::IResource> &&,nvrhi::RefCountPtr<nvrhi::IResource> &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::IResource>,nvrhi::RefCountPtr<nvrhi::IResource>,nvrhi::RefCountPtr<nvrhi::IResource> &&,nvrhi::RefCountPtr<nvrhi::IResource> &>::_Bitcopy_assignable [    std::allocator<D3D12_RAYTRACING_INSTANCE_DESC>::_Minimum_asan_allocation_alignment I    std::allocator<unsigned int>::_Minimum_asan_allocation_alignment j    std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> >::_Minimum_asan_allocation_alignment '  g    D3D12_SHADER_CACHE_MODE_MEMORY 3 蔲    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 3 蔲   D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1  +    DESCKIND_NONE  +   DESCKIND_FUNCDESC  +   DESCKIND_VARDESC  +   DESCKIND_TYPECOMP   +   DESCKIND_IMPLICITAPPOBJ o �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Same_size_and_compatible l �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Bitcopy_constructible i �   std::_Trivial_cat<unsigned int,unsigned int,unsigned int &&,unsigned int &>::_Bitcopy_assignable % 媏    D3D12_BARRIER_LAYOUT_PRESENT * 媏   D3D12_BARRIER_LAYOUT_GENERIC_READ + 媏   D3D12_BARRIER_LAYOUT_RENDER_TARGET . 媏   D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS 1 媏   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE 0 媏   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ - 媏   D3D12_BARRIER_LAYOUT_SHADER_RESOURCE ) 媏   D3D12_BARRIER_LAYOUT_COPY_SOURCE ' 媏   D3D12_BARRIER_LAYOUT_COPY_DEST , 媏  	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE * 媏  
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST 1 媏   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE / 媏   D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ 0 媏  
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE 0 媏   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ 1 媏   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE / 媏   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ 0 媏   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE  �,   COR_VERSION_MAJOR_V2 1 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON 7 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ ; 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS : 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE 6 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE 4 媏   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST 2 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON 8 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ < 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS ; 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 7 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 5 媏   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST + <        nvrhi::rt::c_IdentityTransform � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> >::_Bucket_size " 雈    D3D12_BARRIER_TYPE_GLOBAL    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> >::_Min_buckets # 雈   D3D12_BARRIER_TYPE_TEXTURE �    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> >::_Multi  �+    SYS_WIN16  �+   SYS_WIN32  �+   SYS_MAC � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable D    std::allocator<wchar_t>::_Minimum_asan_allocation_alignment i    std::allocator<std::pair<unsigned int const ,unsigned int> >::_Minimum_asan_allocation_alignment  +    CHANGEKIND_ADDMEMBER   +   CHANGEKIND_DELETEMEMBER  +   CHANGEKIND_SETNAMES $ +   CHANGEKIND_SETDOCUMENTATION  +   CHANGEKIND_GENERAL  +   CHANGEKIND_INVALIDATE   +   CHANGEKIND_CHANGEFAILED �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > >::_Minimum_asan_allocation_alignment � �    std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0>::_Multi � �   std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0>::_Standard / g   NV_COLOR_SELECTION_POLICY_BEST_QUALITY /    NV_DESKTOP_COLOR_DEPTH_16BPC_FLOAT_HDR B    std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D    std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O    std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable a    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n   	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset �    std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >::_Minimum_asan_allocation_alignment h     std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size �    std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment 7   � nvrhi::AftermathMarkerTracker::MaxEventStrings 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable R�    std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0>::_Multi U�   std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0>::_Standard W    std::allocator<enum nvrhi::ResourceStates>::_Minimum_asan_allocation_alignment T    std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ]    ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable p    std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> >::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> >::_Minimum_asan_allocation_alignment R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified / 羍    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION - 羍   D3D12_RESOURCE_BARRIER_TYPE_ALIASING �    std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> >::_Minimum_asan_allocation_alignment 3 歠    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS C 歠   D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS E 歠   D3D12_DEBUG_DEVICE_PARAMETER_GPU_SLOWDOWN_PERFORMANCE_FACTOR �    std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Minimum_asan_allocation_alignment :     D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE I    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY J    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION H    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION X�    std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0>::_Multi [�   std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0>::_Standard  �)    std::denorm_absent  �)   std::denorm_present  �)    std::round_toward_zero  �)   std::round_to_nearest # �)    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value - d    std::integral_constant<int,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �)    std::_Num_base::round_style  d    std::_Num_base::digits �    std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Bucket_size ! d    std::_Num_base::digits10 % d    std::_Num_base::max_digits10 �    std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Min_buckets � �    std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Multi % d    std::_Num_base::max_exponent ' d    std::_Num_base::max_exponent10 % d    std::_Num_base::min_exponent ' d    std::_Num_base::min_exponent10  d    std::_Num_base::radix 3 Q  \ std::filesystem::path::preferred_separator n    std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >::_Minimum_asan_allocation_alignment ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact 8 辠    D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_DISABLED B 辠   D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_WHEN_HASH_BYPASSED ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " d   std::_Num_int_base::radix ) �)   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �)   std::_Num_float_base::round_style $ d   std::_Num_float_base::radix �    std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >::_Minimum_asan_allocation_alignment * d   std::numeric_limits<bool>::digits � �    std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0>::_Multi � �   std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0>::_Standard - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * d   std::numeric_limits<char>::digits , d   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 d   std::numeric_limits<signed char>::digits 3 d   std::numeric_limits<signed char>::digits10 e   std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> >::_Bucket_size e   std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> >::_Min_buckets _�    std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> >::_Multi . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 d   std::numeric_limits<unsigned char>::digits 5 d   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . d   std::numeric_limits<char16_t>::digits 0 d   std::numeric_limits<char16_t>::digits10 �    std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> >::_Minimum_asan_allocation_alignment E    std::allocator<char16_t>::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . d    std::numeric_limits<char32_t>::digits 0 d  	 std::numeric_limits<char32_t>::digits10 3 g    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED - g   D3D12_MESSAGE_CATEGORY_MISCELLANEOUS . g   D3D12_MESSAGE_CATEGORY_INITIALIZATION ' g   D3D12_MESSAGE_CATEGORY_CLEANUP + g   D3D12_MESSAGE_CATEGORY_COMPILATION . g   D3D12_MESSAGE_CATEGORY_STATE_CREATION - g   D3D12_MESSAGE_CATEGORY_STATE_SETTING - g   D3D12_MESSAGE_CATEGORY_STATE_GETTING 5 g   D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION ) g  	 D3D12_MESSAGE_CATEGORY_EXECUTION 0 �   std::numeric_limits<wchar_t>::is_modulo * 揺    D3D12_MESSAGE_SEVERITY_CORRUPTION - d   std::numeric_limits<wchar_t>::digits % 揺   D3D12_MESSAGE_SEVERITY_ERROR ' 揺   D3D12_MESSAGE_SEVERITY_WARNING $ 揺   D3D12_MESSAGE_SEVERITY_INFO / d   std::numeric_limits<wchar_t>::digits10 �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment . �   std::numeric_limits<short>::is_signed + d   std::numeric_limits<short>::digits �    std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >::_Minimum_asan_allocation_alignment - d   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) d   std::numeric_limits<int>::digits + d  	 std::numeric_limits<int>::digits10 C    std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * d   std::numeric_limits<long>::digits E    std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , d  	 std::numeric_limits<long>::digits10 P    std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity ��    std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0>::_Multi ��   std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0>::_Standard 5 �    std::filesystem::_File_time_clock::is_steady 0 �   std::numeric_limits<__int64>::is_signed - d  ? std::numeric_limits<__int64>::digits / d   std::numeric_limits<__int64>::digits10 d    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q   	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k     std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size # 鱡   NV_MOSAIC_TOPO_BEGIN_BASIC 7 �   std::numeric_limits<unsigned short>::is_modulo 4 d   std::numeric_limits<unsigned short>::digits 6 d   std::numeric_limits<unsigned short>::digits10 ! 鱡   NV_MOSAIC_TOPO_7x1_BASIC , 鱡   NV_MOSAIC_TOPO_BEGIN_PASSIVE_STEREO * 鱡   NV_MOSAIC_TOPO_2x2_PASSIVE_STEREO  鱡  # NV_MOSAIC_TOPO_MAX �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > >::_Minimum_asan_allocation_alignment 5 �   std::numeric_limits<unsigned int>::is_modulo 2 d    std::numeric_limits<unsigned int>::digits 4 d  	 std::numeric_limits<unsigned int>::digits10 �    std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> >::_Minimum_asan_allocation_alignment '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible 6 �   std::numeric_limits<unsigned long>::is_modulo 3 d    std::numeric_limits<unsigned long>::digits $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible 5 d  	 std::numeric_limits<unsigned long>::digits10 !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable p    std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >::_Minimum_asan_allocation_alignment 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 d  @ std::numeric_limits<unsigned __int64>::digits 8 d   std::numeric_limits<unsigned __int64>::digits10 k   std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Bucket_size k   std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Min_buckets e�    std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Multi `    ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + d   std::numeric_limits<float>::digits - d   std::numeric_limits<float>::digits10 1 d  	 std::numeric_limits<float>::max_digits10 1 d  � std::numeric_limits<float>::max_exponent 3 d  & std::numeric_limits<float>::max_exponent10 2 d   �僺td::numeric_limits<float>::min_exponent 4 d   �踫td::numeric_limits<float>::min_exponent10 $ �$   TP_CALLBACK_PRIORITY_NORMAL % �$   TP_CALLBACK_PRIORITY_INVALID � �    std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0>::_Multi � �   std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0>::_Standard , d  5 std::numeric_limits<double>::digits . d   std::numeric_limits<double>::digits10 2 d   std::numeric_limits<double>::max_digits10 2 d   std::numeric_limits<double>::max_exponent 4 d  4std::numeric_limits<double>::max_exponent10 4 d  �黶td::numeric_limits<double>::min_exponent 6 d  �威std::numeric_limits<double>::min_exponent10 H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified 1 d  5 std::numeric_limits<long double>::digits 3 d   std::numeric_limits<long double>::digits10 7 d   std::numeric_limits<long double>::max_digits10 7 d   std::numeric_limits<long double>::max_exponent 9 d  4std::numeric_limits<long double>::max_exponent10 9 d  �黶td::numeric_limits<long double>::min_exponent ; d  �威std::numeric_limits<long double>::min_exponent10 �    std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> >::_Bucket_size �    std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> >::_Min_buckets � �    std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> >::_Multi �    std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> >::_Minimum_asan_allocation_alignment " �    std::memory_order_relaxed " �   std::memory_order_consume " �   std::memory_order_acquire " �   std::memory_order_release " �   std::memory_order_acq_rel " �   std::memory_order_seq_cst � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible   �   �  � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable ! )+    COINITBASE_MULTITHREADED �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >::_Minimum_asan_allocation_alignment �   "  T    std::allocator<D3D12_DESCRIPTOR_RANGE1>::_Minimum_asan_allocation_alignment �   std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> >::_Bucket_size �   std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> >::_Min_buckets �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > >::_Minimum_asan_allocation_alignment ��    std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> >::_Multi 4   @ _Mtx_internal_imp_t::_Critical_section_size 5    _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E    std::allocator<char32_t>::_Minimum_asan_allocation_alignment / f    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV + f   D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER ' f   D3D12_DESCRIPTOR_HEAP_TYPE_RTV ' f   D3D12_DESCRIPTOR_HEAP_TYPE_DSV 3 沢  �D3D12_MESSAGE_ID_BYTECODE_VALIDATION_ERROR    std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> >::_Bucket_size    std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> >::_Min_buckets � �    std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> >::_Multi  骻   _Mtx_try  骻   _Mtx_recursive �    std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >::_Minimum_asan_allocation_alignment ( ╣    D3D12_DESCRIPTOR_RANGE_TYPE_SRV ( ╣   D3D12_DESCRIPTOR_RANGE_TYPE_UAV ( ╣   D3D12_DESCRIPTOR_RANGE_TYPE_CBV  蒶   std::_INVALID_ARGUMENT  蒶   std::_NO_SUCH_PROCESS & 蒶   std::_OPERATION_NOT_PERMITTED , 蒶   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蒶   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > >::_Minimum_asan_allocation_alignment % +    _Atomic_memory_order_relaxed % +   _Atomic_memory_order_consume % +   _Atomic_memory_order_acquire % +   _Atomic_memory_order_release % +   _Atomic_memory_order_acq_rel % +   _Atomic_memory_order_seq_cst C    std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE 3 玡    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE E    std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask 2 玡   D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS P    std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity & 玡   D3D12_ROOT_PARAMETER_TYPE_CBV 9�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0>::_Multi & 玡   D3D12_ROOT_PARAMETER_TYPE_SRV <�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0>::_Standard d    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size \    std::allocator<nvrhi::AftermathMarkerTracker *>::_Minimum_asan_allocation_alignment U    std::allocator<nvrhi::BindingLayoutItem>::_Minimum_asan_allocation_alignment j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k     std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size 4     D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK /    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK /    D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE 4    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK_UINT �    std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> >::_Minimum_asan_allocation_alignment � �    std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0>::_Multi x    std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > >::_Minimum_asan_allocation_alignment `    ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos u    std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> >::_Minimum_asan_allocation_alignment ' ++  �   CLSCTX_ACTIVATE_X86_SERVER   �   �$  � �    std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Multi � �   std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Is_set + �$   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 �$   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - �$   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 �$   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS :     std::integral_constant<unsigned __int64,0>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified * �$   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �$   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �$   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �$   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP `    std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>::_Minimum_asan_allocation_alignment 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) �-    std::_Invoker_functor::_Strategy , :+   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL 3 �  � nvrhi::ObjectTypes::Nvrhi_D3D12_Device 8 �  � nvrhi::ObjectTypes::Nvrhi_D3D12_CommandList , �-   std::_Invoker_pmf_object::_Strategy - �-   std::_Invoker_pmf_refwrap::_Strategy - �-   std::_Invoker_pmf_pointer::_Strategy L   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> >::_Bucket_size L   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> >::_Min_buckets F�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> >::_Multi , �-   std::_Invoker_pmd_object::_Strategy �    std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >::_Minimum_asan_allocation_alignment - �-   std::_Invoker_pmd_refwrap::_Strategy - �-   std::_Invoker_pmd_pointer::_Strategy S    std::allocator<D3D12_RESOURCE_BARRIER>::_Minimum_asan_allocation_alignment * 苀    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW 2 苀   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED ��    std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0>::_Multi . 苀   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH 8 苀   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW ��   std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0>::_Standard 7 苀   D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW . 苀   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT : 苀   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW : 苀   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW ; 苀   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW 3 苀  	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS 3 苀  
 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_MESH �    std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> >::_Minimum_asan_allocation_alignment  +    NODE_INVALID  +   NODE_ELEMENT  +   NODE_ATTRIBUTE  +   NODE_TEXT  +   NODE_CDATA_SECTION  +   NODE_ENTITY_REFERENCE  +   NODE_ENTITY $ +   NODE_PROCESSING_INSTRUCTION  +   NODE_COMMENT  +  	 NODE_DOCUMENT  +  
 NODE_DOCUMENT_TYPE  +   NODE_DOCUMENT_FRAGMENT Z    std::allocator<nvrhi::AftermathMarkerTracker>::_Minimum_asan_allocation_alignment  +    XMLELEMTYPE_ELEMENT  +   XMLELEMTYPE_TEXT    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > >::_Minimum_asan_allocation_alignment  +   XMLELEMTYPE_COMMENT  +   XMLELEMTYPE_DOCUMENT  +   XMLELEMTYPE_DTD  +   XMLELEMTYPE_PI  2+   VT_I2  2+   VT_I4  2+   VT_BSTR  2+  	 VT_DISPATCH P    std::allocator<_NV_CUSTOM_SEMANTIC>::_Minimum_asan_allocation_alignment  2+  
 VT_ERROR  2+   VT_VARIANT  2+  
 VT_UNKNOWN 3 �+   DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED  2+   VT_I1  2+   VT_I8  2+  $ VT_RECORD  2+  � �VT_RESERVED t d   std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Minimum_map_size  �+    TYSPEC_CLSID  �+   TYSPEC_FILEEXT  �+   TYSPEC_MIMETYPE  �+   TYSPEC_FILENAME  �+   TYSPEC_PROGID  �+   TYSPEC_PACKAGENAME Z    std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment Z   hstd::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> >::_Bytes _ d   std::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> >::_Block_size / �   std::atomic<long>::is_always_lock_free n d   std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Block_size � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo    �   t  $ d   std::_Locbase<int>::collate " d   std::_Locbase<int>::ctype % d   std::_Locbase<int>::monetary $ d   std::_Locbase<int>::numeric ! d   std::_Locbase<int>::time % d    std::_Locbase<int>::messages   d  ? std::_Locbase<int>::all ! d    std::_Locbase<int>::none �   std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> >::_Bucket_size �   std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> >::_Multi �    std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >::_Minimum_asan_allocation_alignment \    std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ��    std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0>::_Multi ��   std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0>::_Standard 8 �   std::atomic<unsigned long>::is_always_lock_free : d   std::_Floating_type_traits<float>::_Mantissa_bits : d   std::_Floating_type_traits<float>::_Exponent_bits D d   std::_Floating_type_traits<float>::_Maximum_binary_exponent E d   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : d   std::_Floating_type_traits<float>::_Exponent_bias 7 d   std::_Floating_type_traits<float>::_Sign_shift ; d   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; d  5 std::_Floating_type_traits<double>::_Mantissa_bits ; d   std::_Floating_type_traits<double>::_Exponent_bits E d  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G d  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; d  �std::_Floating_type_traits<double>::_Exponent_bias 8 d  ? std::_Floating_type_traits<double>::_Sign_shift < d  4 std::_Floating_type_traits<double>::_Exponent_shift    std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >::_Minimum_asan_allocation_alignment ;   �std::_Floating_type_traits<double>::_Exponent_mask J   
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L   
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O   
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask  g    NVDX_SWAPCHAIN_NONE    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > >::_Minimum_asan_allocation_alignment G   	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K   
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ]    std::allocator<nvrhi::d3d12::ShaderTable::Entry>::_Minimum_asan_allocation_alignment �    std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> >::_Minimum_asan_allocation_alignment / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex �    std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0>::_Multi �   std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0>::_Standard *   std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> >::_Minimum_asan_allocation_alignment  �5   std::_Consume_header  �5   std::_Generate_header  漟    NVDX_OBJECT_NONE  �+   PowerUserMaximum :    std::integral_constant<unsigned __int64,2>::value �   �    �+    DVEXTENT_CONTENT u    std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >::_Minimum_asan_allocation_alignment   �   蒮 �    std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0>::_Multi �   std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0>::_Standard �    std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> >::_Minimum_asan_allocation_alignment �   std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> >::_Bucket_size �   std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> >::_Multi  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment $ 瞗    D3D12_LIFETIME_STATE_IN_USE + 竐  �   NV_DISPLAY_TV_FORMAT_UHD_4Kp30 + 竐  �   NV_DISPLAY_TV_FORMAT_UHD_4Kp25 + 竐  �    NV_DISPLAY_TV_FORMAT_UHD_4Kp24 # �*   BINDSTATUS_FINDINGRESOURCE  �*   BINDSTATUS_CONNECTING  �*   BINDSTATUS_REDIRECTING % �*   BINDSTATUS_BEGINDOWNLOADDATA # �*   BINDSTATUS_DOWNLOADINGDATA # �*   BINDSTATUS_ENDDOWNLOADDATA + �*   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �*   BINDSTATUS_INSTALLINGCOMPONENTS ) �*  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �*  
 BINDSTATUS_USINGCACHEDCOPY " �*   BINDSTATUS_SENDINGREQUEST $ �*   BINDSTATUS_CLASSIDAVAILABLE % �*  
 BINDSTATUS_MIMETYPEAVAILABLE * �*   BINDSTATUS_CACHEFILENAMEAVAILABLE & �*   BINDSTATUS_BEGINSYNCOPERATION $ �*   BINDSTATUS_ENDSYNCOPERATION # �*   BINDSTATUS_BEGINUPLOADDATA ! �*   BINDSTATUS_UPLOADINGDATA ! �*   BINDSTATUS_ENDUPLOADDATA # �*   BINDSTATUS_PROTOCOLCLASSID  �*   BINDSTATUS_ENCODING - �*   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �*   BINDSTATUS_CLASSINSTALLLOCATION  �*   BINDSTATUS_DECODING & �*   BINDSTATUS_LOADINGMIMEHANDLER , �*   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �*   BINDSTATUS_FILTERREPORTMIMETYPE ' �*   BINDSTATUS_CLSIDCANINSTANTIATE �    std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment % �*   BINDSTATUS_IUNKNOWNAVAILABLE  �*   BINDSTATUS_DIRECTBIND * 蔱   NV_SCALING_GPU_SCALING_TO_CLOSEST  �*   BINDSTATUS_RAWMIMETYPE ) 蔱   NV_SCALING_GPU_SCALING_TO_NATIVE " �*    BINDSTATUS_PROXYDETECTING ) 蔱   NV_SCALING_GPU_SCANOUT_TO_NATIVE   �*  ! BINDSTATUS_ACCEPTRANGES ; 蔱   NV_SCALING_GPU_SCALING_TO_ASPECT_SCANOUT_TO_NATIVE  �*  " BINDSTATUS_COOKIE_SENT + �*  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �*  $ BINDSTATUS_COOKIE_SUPPRESSED ( �*  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �*  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �*  ' BINDSTATUS_COOKIE_STATE_REJECT ' �*  ( BINDSTATUS_COOKIE_STATE_PROMPT & �*  ) BINDSTATUS_COOKIE_STATE_LEASH * �*  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �*  + BINDSTATUS_POLICY_HREF  �*  , BINDSTATUS_P3P_HEADER + �*  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �*  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + �*  / BINDSTATUS_SESSION_COOKIES_ALLOWED   �*  0 BINDSTATUS_CACHECONTROL . �*  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) �*  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & �*  3 BINDSTATUS_PUBLISHERAVAILABLE ( �*  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �*  5 BINDSTATUS_SSLUX_NAVBLOCKED , �*  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �*  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �*  8 BINDSTATUS_64BIT_PROGRESS  �*  8 BINDSTATUS_LAST  �*  9 BINDSTATUS_RESERVED_0  �*  : BINDSTATUS_RESERVED_1  �*  ; BINDSTATUS_RESERVED_2  �*  < BINDSTATUS_RESERVED_3  �*  = BINDSTATUS_RESERVED_4  �*  > BINDSTATUS_RESERVED_5  �*  ? BINDSTATUS_RESERVED_6  �*  @ BINDSTATUS_RESERVED_7  �*  A BINDSTATUS_RESERVED_8  �*  B BINDSTATUS_RESERVED_9  �*  C BINDSTATUS_RESERVED_A  �*  D BINDSTATUS_RESERVED_B  �*  E BINDSTATUS_RESERVED_C  �*  F BINDSTATUS_RESERVED_D  �*  G BINDSTATUS_RESERVED_E  �*  H BINDSTATUS_RESERVED_F  �*  I BINDSTATUS_RESERVED_10  �*  J BINDSTATUS_RESERVED_11  �*  K BINDSTATUS_RESERVED_12  �*  L BINDSTATUS_RESERVED_13  �*  M BINDSTATUS_RESERVED_14 ) 瞘   NV_TIMING_OVERRIDE_NV_PREDEFINED &   std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> >::_Bucket_size &   std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> >::_Min_buckets  �    std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> >::_Multi ; �   std::atomic<unsigned __int64>::is_always_lock_free    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > >::_Minimum_asan_allocation_alignment p    std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> >::_Minimum_asan_allocation_alignment b    std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >::_Minimum_asan_allocation_alignment  8+    CIP_DISK_FULL  8+   CIP_ACCESS_DENIED ! 8+   CIP_NEWER_VERSION_EXISTS ! 8+   CIP_OLDER_VERSION_EXISTS  8+   CIP_NAME_CONFLICT 1 8+   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + 8+   CIP_EXE_SELF_REGISTERATION_TIMEOUT  8+   CIP_UNSAFE_TO_ABORT  8+   CIP_NEED_REBOOT 2 g   D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2 �    std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> >::_Minimum_asan_allocation_alignment " +    Uri_PROPERTY_ABSOLUTE_URI ]   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > >::_Minimum_asan_allocation_alignment  +   Uri_PROPERTY_USER_NAME  +   Uri_PROPERTY_HOST_TYPE  +   Uri_PROPERTY_ZONE  �+    Uri_HOST_UNKNOWN  �+   Uri_HOST_DNS  �+   Uri_HOST_IPV4  �+   Uri_HOST_IPV6 W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified q    std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *>::_Minimum_asan_allocation_alignment "   std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> >::_Bucket_size - �    std::chrono::system_clock::is_steady "   std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> >::_Min_buckets �    std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> >::_Multi R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified $ E   std::ratio<1,10000000>::num ( E  ��枠 std::ratio<1,10000000>::den � d   std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Minimum_map_size 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D    ��std::basic_string_view<char,std::char_traits<char> >::npos %    std::ctype<char>::table_size # �        nvrhi::AllSubresources _    std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment K    std::allocator<unsigned short>::_Minimum_asan_allocation_alignment J    ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos p    std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Bytes u d   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Block_size   沞    D3D_DRIVER_TYPE_UNKNOWN ! 沞   D3D_DRIVER_TYPE_HARDWARE " 沞   D3D_DRIVER_TYPE_REFERENCE  沞   D3D_DRIVER_TYPE_NULL ! 沞   D3D_DRIVER_TYPE_SOFTWARE � d   std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Block_size ) 罳    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED ) 罳   D3D_PRIMITIVE_TOPOLOGY_POINTLIST ( 罳   D3D_PRIMITIVE_TOPOLOGY_LINELIST  E   std::ratio<1,1>::num ) 罳   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP , 罳   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST - 罳   D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP  E   std::ratio<1,1>::den , 罳  
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ - 罳   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 0 罳   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 1 罳  
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 9 罳  ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST �    std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment 9 罳  " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 9 罳  # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 9 罳  $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 9 罳  % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 9 罳  & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST 9 罳  ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 9 罳  ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 9 罳  ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST : 罳  * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST : 罳  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST : 罳  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST : 罳  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST : 罳  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST : 罳  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST : 罳  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST : 罳  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST : 罳  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST : 罳  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST : 罳  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST : 罳  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST : 罳  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST : 罳  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST : 罳  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST : 罳  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST : 罳  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST : 罳  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST : 罳  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST R    std::allocator<nvrhi::TextureBarrier>::_Minimum_asan_allocation_alignment : 罳  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST : 罳  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST : 罳  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST : 罳  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST  �*   BINDSTRING_HEADERS   廵    D3D_PRIMITIVE_UNDEFINED   �*   BINDSTRING_ACCEPT_MIMES  廵   D3D_PRIMITIVE_POINT  廵   D3D_PRIMITIVE_LINE  �*   BINDSTRING_EXTRA_URL  �*   BINDSTRING_LANGUAGE  廵   D3D_PRIMITIVE_TRIANGLE  �*   BINDSTRING_USERNAME  廵   D3D_PRIMITIVE_LINE_ADJ # 廵   D3D_PRIMITIVE_TRIANGLE_ADJ  �*   BINDSTRING_PASSWORD , 廵   D3D_PRIMITIVE_1_CONTROL_POINT_PATCH  �*   BINDSTRING_UA_PIXELS , 廵  	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH  �*   BINDSTRING_UA_COLOR , 廵  
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH  �*  	 BINDSTRING_OS  �*  
 BINDSTRING_USER_AGENT , 廵   D3D_PRIMITIVE_4_CONTROL_POINT_PATCH $ �*   BINDSTRING_ACCEPT_ENCODINGS , 廵   D3D_PRIMITIVE_5_CONTROL_POINT_PATCH  �*   BINDSTRING_POST_COOKIE , 廵  
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH , 廵   D3D_PRIMITIVE_7_CONTROL_POINT_PATCH " �*  
 BINDSTRING_POST_DATA_MIME , 廵   D3D_PRIMITIVE_8_CONTROL_POINT_PATCH  �*   BINDSTRING_URL  �*   BINDSTRING_IID , 廵   D3D_PRIMITIVE_9_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_10_CONTROL_POINT_PATCH ' �*   BINDSTRING_FLAG_BIND_TO_OBJECT $ �*   BINDSTRING_PTR_BIND_CONTEXT - 廵   D3D_PRIMITIVE_11_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_12_CONTROL_POINT_PATCH  �*   BINDSTRING_XDR_ORIGIN - 廵   D3D_PRIMITIVE_13_CONTROL_POINT_PATCH   �*   BINDSTRING_DOWNLOADPATH - 廵   D3D_PRIMITIVE_14_CONTROL_POINT_PATCH  �*   BINDSTRING_ROOTDOC_URL $ �*   BINDSTRING_INITIAL_FILENAME - 廵   D3D_PRIMITIVE_15_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_16_CONTROL_POINT_PATCH " �*   BINDSTRING_PROXY_USERNAME " �*   BINDSTRING_PROXY_PASSWORD - 廵   D3D_PRIMITIVE_17_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_18_CONTROL_POINT_PATCH ! �*   BINDSTRING_ENTERPRISE_ID  �*   BINDSTRING_DOC_URL - 廵   D3D_PRIMITIVE_19_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_20_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_21_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_22_CONTROL_POINT_PATCH - 廵   D3D_PRIMITIVE_23_CONTROL_POINT_PATCH J E   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 - 廵   D3D_PRIMITIVE_24_CONTROL_POINT_PATCH - 廵    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH N E  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 - 廵  ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH - 廵  " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH - 廵  # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH - 廵  $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH - 廵  % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH - 廵  & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH - 廵  ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( E  ��枠 std::ratio<10000000,1>::num $ E   std::ratio<10000000,1>::den P E  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx " 鄁    D3D_SRV_DIMENSION_UNKNOWN K E   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy ! 鄁   D3D_SRV_DIMENSION_BUFFER $ 鄁   D3D_SRV_DIMENSION_TEXTURE1D ) 鄁   D3D_SRV_DIMENSION_TEXTURE1DARRAY $ 鄁   D3D_SRV_DIMENSION_TEXTURE2D ) 鄁   D3D_SRV_DIMENSION_TEXTURE2DARRAY & 鄁   D3D_SRV_DIMENSION_TEXTURE2DMS L    ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos + 鄁   D3D_SRV_DIMENSION_TEXTURE2DMSARRAY $ 鄁   D3D_SRV_DIMENSION_TEXTURE3D & 鄁  	 D3D_SRV_DIMENSION_TEXTURECUBE + 鄁  
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY # 鄁   D3D_SRV_DIMENSION_BUFFEREX < E  ��枠 std::integral_constant<__int64,10000000>::value 1 E   std::integral_constant<__int64,1>::value  �.  LPPARAMDESCEX  #+  FUNCKIND + �  D3D12_RAYTRACING_GEOMETRY_AABBS_DESC  �.  tagPARAMDESCEX + �  D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE  �.  PARAMDESC  �.  tagPARAMDESC / 饐  D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC  �.  tagARRAYDESC  韱  D3D12_TEXCUBE_ARRAY_SRV 2 雴  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV  閱  D3D12_TEXCUBE_SRV  <+  CALLCONV  鑶  D3D12_TEX1D_ARRAY_RTV  鐔  D3D12_TEX1D_ARRAY_SRV  鍐  D3D12_TEX2D_ARRAY_RTV  釂  D3D12_TEX3D_SRV  鄦  D3D12_TEX2D_ARRAY_SRV  迒  D3D12_INPUT_ELEMENT_DESC  貑  D3D12_TEX2D_RTV  諉  D3D12_TEX3D_RTV 7 詥  D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER  覇  D3D12_TEX2DMS_ARRAY_RTV  +  DESCKIND  �.  ELEMDESC  艈  D3D12_TEX1D_SRV 
 _  LPCSTR  脙  NV_PSO_EXTENSION % 羻  D3D12_RAYTRACING_GEOMETRY_DESC  �.  BINDPTR  �.  tagFUNCDESC & 絾  D3D12_GPU_VIRTUAL_ADDRESS_RANGE  p,  INVOKEKIND  x.  TLIBATTR  箚  D3D12_TEX2D_ARRAY_DSV C �  D3D12_RENDER_PASS_BEGINNING_ACCESS_PRESERVE_LOCAL_PARAMETERS  竼  D3D12_BUFFER_SRV  談  NvAPI_LongString  秵  D3D12_BUFFER_RTV  拕  ID3D12Device7  �.  tagBINDPTR  �.  tagSTATSTG  =�  __s_GUID    D3D12_META_COMMAND_DESC  爢  D3D12_TEX1D_DSV E �  D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS  �.  tagTYPEDESC  �  D3D12_RANGE_UINT64  潌  D3D12_TEX2DMS_ARRAY_SRV  �.  FUNCDESC  "   HREFTYPE  �+  SYSKIND ; 枂  D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS , 媶  D3D12_PROTECTED_RESOURCE_SESSION_DESC  �,  tagVARDESC  墕  D3D12_TEX1D_ARRAY_UAV  昰  RECT  儐  ID3D12LifetimeOwner  q�  D3D12_TEX2DMS_RTV " p�  D3D12_CACHED_PIPELINE_STATE : �  D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS  �+  TYPEKIND  n�  D3D12_DESCRIPTOR_RANGE1  �.  IEnumSTATSTG  l�  D3D12_MIP_REGION  �.  STATSTG  B�  ID3D12ProtectedSession  �.  ITypeComp  �.  TYPEDESC  h�  D3D12_TEX1D_RTV  h�  ID3D12Device5  {�  ID3D12Device6 ! g�  D3D12_SO_DECLARATION_ENTRY ( e�  D3D12_META_COMMAND_PARAMETER_DESC  .  IDLDESC  �.  tagELEMDESC     INT8  a�  D3D12_BUFFER_UAV 1 _�  D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE  ]�  D3D12_TEX2D_SRV  [�  D3D12_RESOURCE_DESC1  X�  D3D12_STATE_SUBOBJECT  .  tagIDLDESC  V�  D3D12_TEX2DMS_SRV % 艃  NVAPI_D3D12_PSO_EXTENSION_DESC  v%  VARIANTARG  }.  EXCEPINFO  g  NVDX_SwapChainHandle__  }.  tagEXCEPINFO 
    DISPID  畢  D3D12_ROOT_CONSTANTS     MEMBERID  �,  _CatchableType  u   UINT  竐  _NV_DISPLAY_TV_FORMAT  ▍  D3D12_PACKED_MIP_INFO &   D3D12_SHADER_RESOURCE_VIEW_DESC ' 篺  D3D12_BACKGROUND_PROCESSING_MODE  媏  D3D12_BARRIER_LAYOUT " 晠  D3D12_GPU_DESCRIPTOR_HANDLE  H,  tagCAUL  x.  tagTLIBATTR  �$  _TP_CALLBACK_PRIORITY  攨  D3D12_TEX1D_ARRAY_DSV " +  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> % 颵  D3D12_RAYTRACING_INSTANCE_DESC & �+  $_TypeDescriptor$_extraBytes_24  拝  D3D12_TEX2DMS_DSV & 憛  D3D12_CONSTANT_BUFFER_VIEW_DESC  弲  D3D12_INPUT_LAYOUT_DESC 6 r.  __vcrt_va_list_is_reference<char const * const>  妳  ID3D12DescriptorHeap  麯  D3D12_RESOURCE_DESC  罳  D3D_PRIMITIVE_TOPOLOGY  "   NvU32  n.  tagShutdownType  q   OLECHAR G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  x�  D3D12_TILE_REGION_SIZE  v�  D3D12_TEX2DMS_ARRAY_DSV   u�  D3D12_DEPTH_STENCIL_VALUE  �4  _Ctypevec " 4T  ID3D12StateObjectProperties  �+  tagCABSTR  雈  D3D12_BARRIER_TYPE     off_t  <+  tagCALLCONV  �+  tagTYPEKIND $ q�  D3D12_RENDER_TARGET_VIEW_DESC     D3D12_STATIC_BORDER_COLOR & 痝  $_TypeDescriptor$_extraBytes_28  v%  VARIANT  e�  D3D12_TEX2DMS_ARRAY_UAV  #   uintmax_t  c�  ID3D12Heap  �-  ISequentialStream     int64_t % S�  ID3D12ProtectedResourceSession  -�  D3D12_TEX2D_DSV &   $_TypeDescriptor$_extraBytes_58  �,  BSTRBLOB    _Smtx_t  �=  _Thrd_result  ,�  ID3D12QueryHeap  �  D3D12_DISPATCH_RAYS_DESC  #   rsize_t  鵨  _D3D_INCLUDE_TYPE " �  D3D12_SUBRESOURCE_FOOTPRINT  #   DWORD_PTR  �,  TYPEATTR     VARIANT_BOOL - h.  __vc_attributes::event_sourceAttribute 9 a.  __vc_attributes::event_sourceAttribute::optimize_e 5 _.  __vc_attributes::event_sourceAttribute::type_e > ].  __vc_attributes::helper_attributes::v1_alttypeAttribute F X.  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 U.  __vc_attributes::helper_attributes::usageAttribute B Q.  __vc_attributes::helper_attributes::usageAttribute::usage_e * N.  __vc_attributes::threadingAttribute 7 G.  __vc_attributes::threadingAttribute::threading_e - D.  __vc_attributes::aggregatableAttribute 5 =.  __vc_attributes::aggregatableAttribute::type_e / :.  __vc_attributes::event_receiverAttribute 7 1.  __vc_attributes::event_receiverAttribute::type_e ' ..  __vc_attributes::moduleAttribute / %.  __vc_attributes::moduleAttribute::type_e < �  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO  �7  __std_fs_find_data & 蝔  $_TypeDescriptor$_extraBytes_23  ET  ID3D12Pageable  �  ID3D12Device3  �  D3D12_TEX1D_UAV 
 z%  PUWSTR -   $_s__CatchableTypeArray$_extraBytes_32 # =  __std_fs_reparse_data_buffer Z �  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ �  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` �  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �7  __std_fs_dir_handle ( �$  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  	%  AR_STATE  .  tagCADBL  %  _DEVICE_DATA_SET_RANGE  �3  __std_access_rights # 齽  D3D12_INDIRECT_ARGUMENT_DESC W 	�  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-IncrementingConstant> V �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-UnorderedAccessView> U �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-ShaderResourceView> U �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-ConstantBufferView> K �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-Constant> O �  D3D12_INDIRECT_ARGUMENT_DESC::<unnamed-tag>::<unnamed-type-VertexBuffer>  +  VARKIND 3   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE  鮿  D3D12_QUERY_HEAP_DESC  鍍  ID3D12Device1  ".  _TypeDescriptor & g  $_TypeDescriptor$_extraBytes_34 & 黤  $_TypeDescriptor$_extraBytes_43 ( 駝  D3D12_COMPUTE_PIPELINE_STATE_DESC ! f  D3D12_DESCRIPTOR_HEAP_TYPE  �,  _tagPSUACTION 
 .  tagDEC  .  CALPSTR     LONG_PTR & 飫  D3D12_RESOURCE_ALIASING_BARRIER  �*  tagBINDSTRING  韯  _Stl_critical_section 	 �  tm   杄  _D3D_SHADER_VARIABLE_TYPE ! 癳  _D3D_SHADER_VARIABLE_CLASS  r-  tagCACLIPDATA  #   ULONG_PTR " 羍  D3D12_RESOURCE_BARRIER_TYPE % !+  _s__RTTICompleteObjectLocator2 " ╣  D3D12_DESCRIPTOR_RANGE_TYPE  �+  tagURLZONE  %  PUWSTR_C $ 鐒  D3D12_DEPTH_STENCIL_VIEW_DESC  �$  PTP_CLEANUP_GROUP % 鋯  D3D12_SUBRESOURCE_RANGE_UINT64 % 輨  D3D12_RESOURCE_ALLOCATION_INFO  8+  __MIDL_ICodeInstall_0001  p  PCHAR  �*  tagBINDSTATUS  w$  _GUID  �,  _URLZONEREG  �+  _LARGE_INTEGER ' .  _LARGE_INTEGER::<unnamed-type-u>  蹌  D3D12_ROOT_DESCRIPTOR1 & 単  $_TypeDescriptor$_extraBytes_30 - 賱  D3D12_PROTECTED_RESOURCE_SESSION_DESC1  糵  D3D12_COLOR_WRITE_ENABLE  �+  CLIPDATA  讋  _NV_CUSTOM_SEMANTIC  �+  CAFILETIME  .  tagCALPSTR  =,  CALPWSTR 
 �*  CAL  �,  tagCABSTRBLOB  蝿  DXGI_SAMPLE_DESC  �+  tagSAFEARRAYBOUND & l.  $_TypeDescriptor$_extraBytes_29  4�  ID3D12Device4  虅  D3D12_COMMAND_QUEUE_DESC  sg  ID3DBlob  .  tagCAFLT A .  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 糴  $_TypeDescriptor$_extraBytes_46 
 �+  tagCAH  .  DECIMAL  �+  tagCAUI  蕜  D3D12_STATE_OBJECT_DESC & 襢  $_TypeDescriptor$_extraBytes_51  !   WORD  �,  _s__CatchableType ! 趩  D3D12_DEPTH_STENCILOP_DESC  縿  D3D12_HEAP_DESC  �,  CAUH  〆  D3D_NAME  +  tagCADATE  莋  D3D_SHADER_MODEL  .  CADBL  R  LPCOLESTR  %  PCUWSTR  �+  CAPROPVARIANT & 箘  D3D12_TILED_RESOURCE_COORDINATE  .  CAFLT & 纅  $_TypeDescriptor$_extraBytes_19  瞘  _NV_TIMING_OVERRIDE & 穭  D3D12_RESOURCE_ALLOCATION_INFO1 & g  $_TypeDescriptor$_extraBytes_21  #   uint64_t ' �$  JOB_OBJECT_IO_RATE_CONTROL_FLAGS 9 	.  __vcrt_va_list_is_reference<wchar_t const * const>  �+  _USER_ACTIVITY_PRESENCE  �:  __std_fs_filetime E w  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> ( 祫  D3D12_FEATURE_DATA_D3D12_OPTIONS5    PLONG 9 �  D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS & 6+  $_TypeDescriptor$_extraBytes_20  瘎  ID3D12Device8  �+  DISPPARAMS  mU  ID3D12Fence  �+  _FILETIME ' 讍  D3D12_PIPELINE_STATE_STREAM_DESC $ 峠  D3D12_LINE_RASTERIZATION_MODE ( 艃  NVAPI_D3D12_PSO_EXTENSION_DESC_V1  p  va_list  '%  FS_BPIO_INFLAGS - �,  $_s__CatchableTypeArray$_extraBytes_16   5=  __std_fs_copy_file_result  羶  D3D12_STREAM_OUTPUT_DESC  �7  __std_code_page  %  PDEVICE_DSM_DEFINITION      BYTE . 磃  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE  簝  D3D12_CLEAR_VALUE % 鵪  D3D12_RAYTRACING_GEOMETRY_TYPE 
 R  PCWSTR  .  IStream � 攦  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > �穬  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > >,1> P瘍  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > >,1> U 殎  std::_Default_allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker> > A   std::_Compressed_pair<std::equal_to<unsigned int>,float,1> \ 厓  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > � x�  std::_Simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > � 瀮  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > >,1> � 杻  std::allocator_traits<std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > W |  std::_Default_allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker *> > 
  std::_Default_allocator_traits<std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > l 褈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *> > � a�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > a 噧  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > �  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > O H�  std::_Default_allocator_traits<std::allocator<D3D12_DESCRIPTOR_RANGE1> > 們  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > >,1> � z�  std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > i }  std::_Default_allocator_traits<std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > > ;   std::_Conditionally_enabled_hash<nvrhi::IBuffer *,1> � 齸  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> > > E u�  std::_Compressed_pair<std::equal_to<nvrhi::IBuffer *>,float,1> k 穫  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > E l�  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> K y~  std::_Default_allocator_traits<std::allocator<_NV_CUSTOM_SEMANTIC> > �   std::_Simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > � c�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > [ �  std::_Default_allocator_traits<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > � 9�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > [G{  std::_Hash<std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> > � H�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > � R�  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > >,1> � 莵  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> > > F J�  std::allocator_traits<std::allocator<D3D12_DESCRIPTOR_RANGE1> > � 魝  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > H 蟳  std::_Default_allocator_traits<std::allocator<unsigned __int64> > � <�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > N !�  std::_Simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > 2�  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > >,1> e 饙  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > > � *�  std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> S #�  std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > ` �  std::_Compressed_pair<std::allocator<char>,std::_Vector_val<std::_Simple_types<char> >,1> � �  std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > b 
�  std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > �  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > >,1> � 辺  std::_Hash<std::_Umap_traits<enum DXGI_FORMAT,unsigned char,std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> >,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> >,0> > � 
�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> > > c 秚  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > � 鰝  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > � 鐐  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > y 瀞  std::_Uhash_choose_transparency<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT>,void> E 輦  std::_Compressed_pair<std::equal_to<enum DXGI_FORMAT>,float,1> " 詡  std::equal_to<unsigned int> � *z  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> > > � 蟼  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > � 剚  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > > 艂  std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > b 箓  std::allocator_traits<std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > �獋  std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0>   std::allocator_traits<std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > � 攤  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > > � ��  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> >,std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > >,1> � �|  std::_Simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > Ex�  std::list<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > z �  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> > > N 齺  std::_Compressed_pair<std::equal_to<nvrhi::rt::IShaderTable *>,float,1> � L~  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > E 魜  std::_Vector_val<std::_Simple_types<D3D12_DESCRIPTOR_RANGE1> > Q 陙  std::_Vector_val<std::_Simple_types<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > H 鄟  std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > x 褋  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Vector_val<std::_Simple_types<unsigned __int64> >,1> � 蓙  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> > > y ﹣  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > | }  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > > K 邁  std::_Default_allocator_traits<std::allocator<ID3D12CommandList *> > N Z  std::_Default_allocator_traits<std::allocator<D3D12_RESOURCE_BARRIER> > & 簛  std::equal_to<nvrhi::IBuffer *> U 趰  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<IUnknown> > > r 祦  std::_Compressed_pair<std::hash<unsigned int>,std::_Compressed_pair<std::equal_to<unsigned int>,float,1>,1> � 
r  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > > k 趒  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > p 珌  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > L 渷  std::allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker> > F 顈  std::_Simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > � 搎  std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> > � &q  std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Strategy � q  std::_Tree<std::_Tset_traits<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *>,0> >::_Redbl p巵  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > >,1> � 唩  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > > � w�  std::_Compressed_pair<std::allocator<nvrhi::AftermathMarkerTracker>,std::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> >,1> } x  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > %榾  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> > > 7o�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > X聙  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > m 獄  std::_Uhash_choose_transparency<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,void> { ]�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> > > r _�  std::allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> > > 1 鮭  std::less<nvrhi::AftermathMarkerTracker *> NP�  std::_Umap_traits<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> � J�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > O 踳  std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> > � ;�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > . 攟  std::allocator<D3D12_DESCRIPTOR_RANGE1> J ,�  std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > r �  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > d �  std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > z �  std::_Compressed_pair<std::hash<nvrhi::IBuffer *>,std::_Compressed_pair<std::equal_to<nvrhi::IBuffer *>,float,1>,1> 
鷢  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > � 饊  std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > a r�  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > > 鄝  std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> > 2 蝷  std::_Vector_val<std::_Simple_types<char> > D  ~  std::_Conditionally_enabled_hash<nvrhi::rt::IShaderTable *,1> � r  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > O膧  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > p <x  std::_Default_allocator_traits<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> > > ~ 眬  std::_Compressed_pair<std::allocator<ID3D12CommandList *>,std::_Vector_val<std::_Simple_types<ID3D12CommandList *> >,1> * ﹢  std::allocator<ID3D12CommandList *> 殌  std::allocator_traits<std::allocator<std::_List_node<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,void *> > > � 墍  std::list<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > �  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > � �  std::_Compressed_pair<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>,std::_Vector_val<std::_Simple_types<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >,1> � �  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > � �  std::list<std::pair<nvrhi::IBuffer * const,unsigned __int64>,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > y 辯  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void> L o}  std::_Simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > � F  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > R �  std::allocator_traits<std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > N ~  std::allocator_traits<std::allocator<nvrhi::AftermathMarkerTracker *> > � o  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > ; e  std::_Compressed_pair<std::equal_to<void *>,float,1> ; Pv  std::_Conditionally_enabled_hash<enum DXGI_FORMAT,1> 減  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0> > E \  std::allocator_traits<std::allocator<D3D12_RESOURCE_BARRIER> > EN  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,1> � jv  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � 銃  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > � 2  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > �   std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > "   std::hash<nvrhi::IBuffer *> \ 驆  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > > �   std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > x �~  std::allocator_traits<std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> > >  饉  std::equal_to<void *> p Tn  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > A 雫  std::_Vector_val<std::_Simple_types<ID3D12CommandList *> > B 醻  std::allocator_traits<std::allocator<ID3D12CommandList *> > c 觺  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *> > " Sv  std::hash<enum DXGI_FORMAT> 4 聗  std::allocator<nvrhi::AftermathMarkerTracker> ` 硚  std::allocator<std::_List_node<std::pair<enum DXGI_FORMAT const ,unsigned char>,void *> > b 焴  std::allocator<std::_List_node<std::pair<nvrhi::IBuffer * const,unsigned __int64>,void *> > * 0}  std::allocator<_NV_CUSTOM_SEMANTIC> - Rw  std::allocator<D3D12_RESOURCE_BARRIER> P 媬  std::_Deque_val<std::_Deque_simple_types<nvrhi::AftermathMarkerTracker> > + #~  std::hash<nvrhi::rt::IShaderTable *> z 厏  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1> � 皉  std::_Simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > B {~  std::allocator_traits<std::allocator<_NV_CUSTOM_SEMANTIC> > 1 
n  std::_Conditionally_enabled_hash<void *,1> � 鋶  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > >,1> � m~  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > > > > �V~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >,1> > 7~  std::_Vector_val<std::_Simple_types<unsigned __int64> > � -~  std::_Compressed_pair<std::hash<nvrhi::rt::IShaderTable *>,std::_Compressed_pair<std::equal_to<nvrhi::rt::IShaderTable *>,float,1>,1> �~  std::list<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > � At  std::_Hash<std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> > A 畗  std::_Vector_val<std::_Simple_types<_NV_CUSTOM_SEMANTIC> > N  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > � 梷  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > J N}  std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > ` 亇  std::allocator_traits<std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > > Q q}  std::_List_simple_types<std::pair<enum DXGI_FORMAT const ,unsigned char> > l}  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > > > > L 軑  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<IUnknown> > > � V}  std::_Compressed_pair<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> >,std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >,1> � @}  std::_Umap_traits<unsigned __int64,nvrhi::d3d12::RootSignature *,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> >,0>  9}  std::_Default_sentinel ~ 8}  std::_Compressed_pair<std::allocator<_NV_CUSTOM_SEMANTIC>,std::_Vector_val<std::_Simple_types<_NV_CUSTOM_SEMANTIC> >,1> [ e]  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 	W  std::default_delete<wchar_t [0]> � n  std::_Uhash_choose_transparency<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *>,void> . &3  std::_Conditionally_enabled_hash<int,1> A �;  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> i 榶  std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> > � xY  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >,std::_Iterator_base0> ? �*  std::_Default_allocator_traits<std::allocator<wchar_t> > S U  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IResource> > > � }_  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > *> � !}  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > ) 躖  std::hash<nvrhi::IBindingLayout *>  .  std::_Lockit d }  std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > s }  std::allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > >  �>  std::timed_mutex X ee  std::_Default_allocator_traits<std::allocator<nvrhi::d3d12::ShaderTable::Entry> > � 鮸  std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > * �'  std::hash<enum nvrhi::ResourceType> � 縏  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > >,1> - c8  std::reverse_iterator<wchar_t const *> � 鍇  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > " A*  std::_Char_traits<char,int>  4  std::_Fs_file Z 鱳  std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> > & 褆  std::equal_to<enum DXGI_FORMAT> � 2e  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> > > a 隸  std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >  "   std::_Atomic_counter_t  �)  std::_Num_base & 23  std::hash<std::error_condition> K \(  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > � Aw  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >   std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > >,1> = YJ  std::array<std::pair<unsigned int,nvrhi::IBuffer *>,6> X 蘾  std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > � cY  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > >  x0  std::_Big_uint128  V>  std::condition_variable b 鱈  std::vector<D3D12_RAYTRACING_INSTANCE_DESC,std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> > x 臠  std::vector<D3D12_RAYTRACING_INSTANCE_DESC,std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> >::_Reallocation_policy f 紎  std::_Compressed_pair<std::hash<void *>,std::_Compressed_pair<std::equal_to<void *>,float,1>,1> � 瞸  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > > > > ) O*  std::_Narrow_char_traits<char,int> 橺  std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> � [  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > � 渱  std::_Compressed_pair<std::allocator<D3D12_DESCRIPTOR_RANGE1>,std::_Vector_val<std::_Simple_types<D3D12_DESCRIPTOR_RANGE1> >,1>    std::hash<float> 6 (  std::allocator<nvrhi::rt::PipelineHitGroupDesc> � w[  std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> > L 榹  std::_Default_allocator_traits<std::allocator<nvrhi::BufferBarrier> > " 厊  std::_Align_type<double,64>  *3  std::hash<int>  *  std::_Num_int_base � 倈  std::_List_simple_types<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  �6  std::ctype<wchar_t> " �3  std::_System_error_category 9 XR  std::shared_ptr<nvrhi::d3d12::InternalCommandList> F  std::_Hash<std::_Umap_traits<nvrhi::TextureBindingKey,unsigned int,std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> >,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >,0> > � 繸  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > >,1> / �'  std::_Conditionally_enabled_hash<bool,1> 8 am  std::_Ptr_base<nvrhi::d3d12::CommandListInstance> N 	o  std::_Simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > � 螎  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<IUnknown> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<IUnknown> > >,1> � Y  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > � }|  std::list<std::pair<enum DXGI_FORMAT const ,unsigned char>,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > >  �)  std::float_denorm_style v 巑  std::_Simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > H 蠈  std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > � )\  std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> > � 橵  std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> > � |  std::initializer_list<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > � \  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > � |  std::list<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > T  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> C 歿  std::allocator_traits<std::allocator<nvrhi::BufferBarrier> > f 寋  std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > u 
c  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 �-  std::allocator_traits<std::allocator<wchar_t> > & 唟  std::equal_to<unsigned __int64> C V  std::_Vector_val<std::_Simple_types<nvrhi::TextureBarrier> > '倇  std::unordered_map<unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >  $0  std::bad_cast ] 聑  std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >  j^  std::equal_to<void> T   std::vector<D3D12_DESCRIPTOR_RANGE1,std::allocator<D3D12_DESCRIPTOR_RANGE1> > j uz  std::vector<D3D12_DESCRIPTOR_RANGE1,std::allocator<D3D12_DESCRIPTOR_RANGE1> >::_Reallocation_policy � �9  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > \ [V  std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > t 鎀  std::_Compressed_pair<std::allocator<unsigned short>,std::_Vector_val<std::_Simple_types<unsigned short> >,1> o $Y  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> y 唝  std::_Uhash_choose_transparency<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *>,void> P ;z  std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> > X /z  std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> >::_Redbl  +r  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > > " &*  std::numeric_limits<double> � ,z  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> > > $ 轕  std::atomic<unsigned __int64>  T0  std::__non_rtti_object 6 z  std::allocator<nvrhi::AftermathMarkerTracker *> Tz  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > >,1> ( n  std::_Basic_container_proxy_ptr12 
辻  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > 騰  std::_Hash<std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> > � z  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > > � cV  std::_Compressed_pair<std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> >,std::_Vector_val<std::_Simple_types<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > >,1> > 紽  std::vector<unsigned int,std::allocator<unsigned int> > T 孎  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy D 茘  std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > 1 �  std::array<nvrhi::FramebufferAttachment,8> � 鈁  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > >  "*  std::_Num_float_base K 饄  std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > � 難  std::initializer_list<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >  �1  std::logic_error � 騜  std::_Compressed_pair<std::allocator<enum nvrhi::ResourceStates>,std::_Vector_val<std::_Simple_types<enum nvrhi::ResourceStates> >,1> s5\  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > >,1> k m  std::_Default_allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > 5 怲  std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> 鄖  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > 7 �'  std::_Conditionally_enabled_hash<unsigned int,1> 4 穽  std::allocator<nvrhi::RefCountPtr<IUnknown> > G �'  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  Y4  std::pointer_safety ! �-  std::char_traits<char32_t>  O5  std::locale  �5  std::locale::_Locimp  `5  std::locale::facet   h5  std::locale::_Facet_guard  5  std::locale::id ? 褃  std::allocator_traits<std::allocator<unsigned __int64> > s e  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > : 脃  std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *>   *  std::numeric_limits<bool> �JO  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > J 磞  std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > # �*  std::_WChar_traits<char16_t> P =W  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > � E[  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > >,std::_Iterator_base0> T   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   ]  std::_Fake_proxy_ptr_impl 	  std::_Umap_traits<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > >,0> F dn  std::_Default_allocator_traits<std::allocator<unsigned short> > � 礯  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > *> * *  std::numeric_limits<unsigned short> ' $  std::hash<nvrhi::BindingSetDesc> Z za  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > � 焬  std::_Umap_traits<nvrhi::IBuffer *,unsigned __int64,std::_Uhash_compare<nvrhi::IBuffer *,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *> >,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> >,0> M M8  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > 玠  std::list<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > � %(  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  d2  std::overflow_error B 鑅  std::_Vector_val<std::_Simple_types<nvrhi::BufferBarrier> > . 剏  std::vector<char,std::allocator<char> > D Ty  std::vector<char,std::allocator<char> >::_Reallocation_policy � 鏢  std::_Compressed_pair<std::allocator<nvrhi::d3d12::ShaderTable::Entry>,std::_Vector_val<std::_Simple_types<nvrhi::d3d12::ShaderTable::Entry> >,1> @ 實  std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > � y  std::unordered_map<enum DXGI_FORMAT,unsigned char,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT>,std::allocator<std::pair<enum DXGI_FORMAT const ,unsigned char> > > % V&  std::_One_then_variadic_args_t K ◢  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<IUnknown> > > D 頬  std::_Constexpr_immortalize_impl<std::_System_error_category> W �)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * E?  std::_Vb_val<std::allocator<bool> > �9s  std::_Hash<std::_Umap_traits<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > >,0> > J 騔  std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> E �<  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j Vx  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �-  std::char_traits<wchar_t>  #>  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> - Mh  std::array<D3D12_DESCRIPTOR_RANGE1,32> 4 H(  std::allocator<nvrhi::rt::PipelineShaderDesc> U 歋  std::unique_ptr<nvrhi::d3d12::Queue,std::default_delete<nvrhi::d3d12::Queue> > HTx  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > > > > � O\  std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> g >x  std::allocator_traits<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> > > n �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � �  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy 甔  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > ' /x  std::allocator<unsigned __int64> C 稵  std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> >  �-  std::false_type t !x  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > � x  std::allocator<std::_List_node<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > >,void *> > , V  std::allocator<nvrhi::TextureBarrier>  �)  std::float_round_style � �w  std::_Compressed_pair<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::BufferChunk>,void *> >,std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > >,1> T   std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j �  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � 鉾  std::_Compressed_pair<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> >,std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> >,1> ! �=  std::hash<std::thread::id> F 苭  std::vector<unsigned __int64,std::allocator<unsigned __int64> > \ 晈  std::vector<unsigned __int64,std::allocator<unsigned __int64> >::_Reallocation_policy  X  std::string i d�  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > > B �-  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> � Pe  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> > > T �8  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , d  std::array<nvrhi::BindingSetItem,128> ! ZD  std::atomic<unsigned long> � P(  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> � Zw  std::_Compressed_pair<std::allocator<D3D12_RESOURCE_BARRIER>,std::_Vector_val<std::_Simple_types<D3D12_RESOURCE_BARRIER> >,1> > �  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> D ╜  std::_Uninitialized_backout_al<std::allocator<unsigned int> > � 僘  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > >  4>  std::adopt_lock_t � Cw  std::allocator_traits<std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > � {(  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , *  std::numeric_limits<unsigned __int64> �4w  std::unordered_map<void *,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)>,std::hash<void *>,std::equal_to<void *>,std::allocator<std::pair<void * const,std::function<std::pair<void const *,unsigned __int64> __cdecl(unsigned __int64,std::function<unsigned __int64 __cdecl(std::pair<void const *,unsigned __int64>,enum nvrhi::GraphicsAPI)>)> > > > � lv  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � m  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > >  �4  std::_Locinfo 6 ;  std::_Ptr_base<std::filesystem::_Dir_enum_impl> z ]v  std::_Compressed_pair<std::hash<enum DXGI_FORMAT>,std::_Compressed_pair<std::equal_to<enum DXGI_FORMAT>,float,1>,1> \ �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s 芵  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > �WY  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > >,1> [ 觤  std::_Uhash_choose_transparency<void *,std::hash<void *>,std::equal_to<void *>,void> � 猋  std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> > � Hv  std::_Compressed_pair<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> >,std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > >,1> � <V  std::_Compressed_pair<std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> >,std::_Vector_val<std::_Simple_types<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > >,1> $ *  std::numeric_limits<char16_t> 0 !  std::array<nvrhi::VertexBufferBinding,16> /赬  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0>  �  std::string_view    std::wstring_view � 腨  std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > % �-  std::integral_constant<bool,1> L   std::_Vector_val<std::_Simple_types<D3D12_RAYTRACING_INSTANCE_DESC> >   _  std::_Leave_proxy_unbound  >  std::_Mutex_base � Fe  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> > >  �7  std::money_base  @v  std::money_base::pattern � 楾  std::_Compressed_pair<std::allocator<D3D12_RAYTRACING_INSTANCE_DESC>,std::_Vector_val<std::_Simple_types<D3D12_RAYTRACING_INSTANCE_DESC> >,1>  �4  std::_Timevec N 骃  std::_Vector_val<std::_Simple_types<nvrhi::d3d12::ShaderTable::Entry> >  >v  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> > > d pe  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > >  <v  std::defer_lock_t H 縃  std::pair<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,unsigned int> l ;v  std::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> > � 	v  std::vector<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *,std::allocator<NVAPI_D3D12_PSO_EXTENSION_DESC_V1 *> >::_Reallocation_policy   �2  std::_Init_once_completer � 艭  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > > � 昒  std::_Uhash_compare<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey> > K 胾  std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> *> L 痷  std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> > b }u  std::vector<_NV_CUSTOM_SEMANTIC,std::allocator<_NV_CUSTOM_SEMANTIC> >::_Reallocation_policy j f<  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � 5<  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy L 8u  std::vector<ID3D12CommandList *,std::allocator<ID3D12CommandList *> > b u  std::vector<ID3D12CommandList *,std::allocator<ID3D12CommandList *> >::_Reallocation_policy  2>  std::scoped_lock<> + 6  std::codecvt<wchar_t,char,_Mbstatet> h �%  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> ^ 鮝  std::_Compressed_pair<std::default_delete<nvrhi::TextureState>,nvrhi::TextureState *,1> Q 膖  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % �  std::array<nvrhi::Viewport,16>  >  std::_Iterator_base12 0 [\  std::hash<nvrhi::TextureStateExtension *> � V  std::_Compressed_pair<std::allocator<nvrhi::TextureBarrier>,std::_Vector_val<std::_Simple_types<nvrhi::TextureBarrier> >,1>  JX  std::_Pocma_values 7 L$  std::_Array_const_iterator<enum nvrhi::Format,8> D 聇  std::_Vector_val<std::_Simple_types<D3D12_RESOURCE_BARRIER> > !  3  std::hash<std::error_code> Z 竧  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > N 9  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > � q  std::_Simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > @ �*  std::_Default_allocator_traits<std::allocator<char32_t> >  �%  std::allocator<char32_t> ? =  std::unique_ptr<char [0],std::default_delete<char [0]> > ) qX  std::_Atomic_padded<unsigned long> $ �  std::_Atomic_integral<long,4> #e  std::list<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > P  q  std::_Default_allocator_traits<std::allocator<nvrhi::BindingLayoutItem> >  �'  std::hash<bool>     std::streamsize _ 瀺  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > > 6 �&  std::_String_val<std::_Simple_types<char32_t> > = �&  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` v&  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ ﹖  std::_List_val<std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > � 漷  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > > > � 唗  std::unordered_map<nvrhi::IBuffer *,unsigned __int64,std::hash<nvrhi::IBuffer *>,std::equal_to<nvrhi::IBuffer *>,std::allocator<std::pair<nvrhi::IBuffer * const,unsigned __int64> > > i 穝  std::_Uhash_compare<enum DXGI_FORMAT,std::hash<enum DXGI_FORMAT>,std::equal_to<enum DXGI_FORMAT> > " �>  std::lock_guard<std::mutex> p 泂  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *>,void *> >  (  std::hash<long double> � �9  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � _9  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l H  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k D  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � 錤  std::_Compressed_pair<std::hash<nvrhi::TextureBindingKey>,std::_Compressed_pair<std::equal_to<nvrhi::TextureBindingKey>,float,1>,1> � E_  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > *>  噑  std::try_to_lock_t r HV  std::_Vector_val<std::_Simple_types<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > > U �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > � 俍  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > >,std::_Iterator_base0> # *  std::numeric_limits<wchar_t>  �  std::_Container_base0 - D  std::_Atomic_integral<unsigned long,4>    std::hash<double> O �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �-  std::bidirectional_iterator_tag . �#  std::hash<nvrhi::TextureSubresourceSet> �  std::_Hash<std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> > � 籱  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > F 唖  std::_Vector_val<std::_Simple_types<nvrhi::BindingLayoutItem> > r霡  std::unordered_map<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *>,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > m僛  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > >,1> �|s  std::unordered_map<nvrhi::rt::IShaderTable *,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> >,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *>,std::allocator<std::pair<nvrhi::rt::IShaderTable * const,std::unique_ptr<nvrhi::d3d12::ShaderTableState,std::default_delete<nvrhi::d3d12::ShaderTableState> > > > > " [>  std::_Align_type<double,72> g 碯  std::allocator<std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *> > � 逨  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � PF  std::unordered_map<nvrhi::TextureBindingKey,unsigned int,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey>,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > + }k  std::array<D3D12_ROOT_PARAMETER1,32> < 騎  std::_Vector_val<std::_Simple_types<unsigned short> > � 腣  std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> > / �*  std::_Char_traits<char32_t,unsigned int>  J3  std::_System_error � 塵  std::_Default_allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> > > . 嶿  std::equal_to<nvrhi::TextureBindingKey> ( 1$  std::hash<nvrhi::FramebufferInfo> 9 )W  std::allocator<std::filesystem::_Find_file_handle>  3  std::error_condition % �-  std::integral_constant<bool,0>  .  std::bad_exception & �%  std::_Zero_then_variadic_args_t  �  std::u32string � 蝀  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > � 瞨  std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >  �  std::_Fake_allocator _ 璻  std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> > i 巖  std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Is_bidi o 峳  std::deque<nvrhi::AftermathMarkerTracker,std::allocator<nvrhi::AftermathMarkerTracker> >::_Pop_direction / f  std::array<nvrhi::BindingLayoutItem,128>  2  std::invalid_argument / 3r  std::equal_to<nvrhi::rt::IShaderTable *> N �-  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U �)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > ] {e  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >  O>  std::cv_status S �-  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > K  [  std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> � 峑  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > R �%  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > /   std::hash<nvrhi::BufferStateExtension *> + S=  std::pair<enum __std_win_error,bool> � -r  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > > � r  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > >  �=  std::thread  �=  std::thread::id S =8  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  2  std::length_error � <e  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> > > � r  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > > � jT  std::_Uhash_choose_transparency<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *>,void> * ?  std::hash<nvrhi::TextureBindingKey> F y`  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 1W  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! $*  std::numeric_limits<float> ^aZ  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > >,1>  �7  std::time_base   �7  std::time_base::dateorder � 攳  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > >,1> �   std::_Compressed_pair<std::less<nvrhi::AftermathMarkerTracker *>,std::_Compressed_pair<std::allocator<std::_Tree_node<nvrhi::AftermathMarkerTracker *,void *> >,std::_Tree_val<std::_Tree_simple_types<nvrhi::AftermathMarkerTracker *> >,1>,1> j翧  std::unordered_map<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *>,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > ) �  std::_Atomic_integral_facade<long> i 餼  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >  m>  std::mutex � 鮔  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> > % �'  std::hash<enum nvrhi::BlendOp> b 躴  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > � mZ  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > > � 0[  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > >  S4  std::_Ref_count_base " �'  std::hash<unsigned __int64>  蝢  std::ratio<60,1> 7 逽  std::allocator<nvrhi::d3d12::ShaderTable::Entry>  t  std::exception_ptr B 蘱  std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> >  緌  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > � 紂  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> > > : 鞻  std::_Vector_val<std::_Simple_types<unsigned int> > ) �'  std::hash<enum nvrhi::BlendFactor> � a_  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > *> $ 
*  std::numeric_limits<char32_t>  �2  std::once_flag  �2  std::error_code �$[  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > >,1>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 簈  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  !7  std::_Iosb<int>   7  std::_Iosb<int>::_Seekdir ! 7  std::_Iosb<int>::_Openmode   7  std::_Iosb<int>::_Iostate ! 7  std::_Iosb<int>::_Fmtflags # 7  std::_Iosb<int>::_Dummy_enum c �S  std::array<std::unique_ptr<nvrhi::d3d12::Queue,std::default_delete<nvrhi::d3d12::Queue> >,3> 7 �-  std::allocator_traits<std::allocator<char32_t> > � 竡  std::set<nvrhi::AftermathMarkerTracker *,std::less<nvrhi::AftermathMarkerTracker *>,std::allocator<nvrhi::AftermathMarkerTracker *> >  n  std::nano  �  std::_Iterator_base0 � P[  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > �\[  std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> M 1(  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > � q  std::initializer_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > � 慬  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > 1 �*  std::_Char_traits<char16_t,unsigned short> � q  std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > G q  std::allocator_traits<std::allocator<nvrhi::BindingLayoutItem> > $ �#  std::hash<nvrhi::BufferRange> T 	9  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > > � 魀  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> > >   5  std::_Locbase<int> T 騪  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > > ! �-  std::char_traits<char16_t> � 鎅  std::_Compressed_pair<std::hash<nvrhi::IBindingLayout *>,std::_Compressed_pair<std::equal_to<nvrhi::IBindingLayout *>,float,1>,1> � 俷  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > 2 ,D  std::_Atomic_integral_facade<unsigned long>  |  std::tuple<> � 餻  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> > > U 鎍  std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >    std::_Container_base12 W 'Y  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  3  std::io_errc  X7  std::ios_base  i7  std::ios_base::_Fnarray  c7  std::ios_base::_Iosarray  7  std::ios_base::Init  7  std::ios_base::failure  #7  std::ios_base::event E 蝅  std::_Constexpr_immortalize_impl<std::_Generic_error_category> ) 	*  std::numeric_limits<unsigned char> % 轙  std::allocator<unsigned short> � �%  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  �-  std::true_type   *  std::numeric_limits<long> " �-  std::initializer_list<char>  �-  std::_Invoker_strategy  �<  std::nothrow_t y 靝  std::initializer_list<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > � 醦  std::unordered_map<unsigned __int64,nvrhi::d3d12::RootSignature *,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::d3d12::RootSignature *> > > T  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ +*  std::_Default_allocate_traits H   std::_Vector_val<std::_Simple_types<enum nvrhi::ResourceStates> > y 衁  std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> N �8  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > R p  std::vector<D3D12_RESOURCE_BARRIER,std::allocator<D3D12_RESOURCE_BARRIER> > h 鈕  std::vector<D3D12_RESOURCE_BARRIER,std::allocator<D3D12_RESOURCE_BARRIER> >::_Reallocation_policy 3 �-  std::allocator_traits<std::allocator<char> > <O  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> >,0> > ! *  std::numeric_limits<short>  u   std::_Vbase . s(  std::allocator<nvrhi::rt::GeometryDesc> # �>  std::unique_lock<std::mutex> ; �  std::basic_string_view<char,std::char_traits<char> > W ~�  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > > ! �6  std::ctype<unsigned short> C T  std::basic_string_view<char16_t,std::char_traits<char16_t> > � <B  std::vector<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > � 
B  std::vector<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > >::_Reallocation_policy V 爋  std::initializer_list<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > y 昽  std::list<std::shared_ptr<nvrhi::d3d12::BufferChunk>,std::allocator<std::shared_ptr<nvrhi::d3d12::BufferChunk> > > X t�  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > > � 榅  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > < ($  std::_Array_const_iterator<nvrhi::BindingSetItem,128> [ 
o  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > > 6 �&  std::_String_val<std::_Simple_types<char16_t> > = �&  std::_String_val<std::_Simple_types<char16_t> >::_Bxty C 錳  std::array<std::pair<unsigned int,D3D12_ROOT_DESCRIPTOR1>,6> , D  std::_Atomic_storage<unsigned long,4> / -U  std::pair<unsigned int,nvrhi::IBuffer *> O ^  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P �8  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . �'  std::hash<enum nvrhi::TextureDimension> ! `4  std::_Shared_ptr_spin_lock S o  std::_List_simple_types<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > 0 遧  std::_Ptr_base<nvrhi::d3d12::BufferChunk> r eT  std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >  D  std::bad_alloc  {2  std::underflow_error B �'  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> . b  std::default_delete<nvrhi::BufferState> � mO  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > > J W  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> � o  std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > > � 鏽  std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Is_bidi � 錸  std::deque<std::shared_ptr<nvrhi::d3d12::CommandListInstance>,std::allocator<std::shared_ptr<nvrhi::d3d12::CommandListInstance> > >::_Pop_direction � WZ  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > > > D �V  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  �7  std::messages_base  62  std::out_of_range # *  std::numeric_limits<__int64> � 刵  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > i 俉  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  z6  std::ctype<char> � n  std::_Compressed_pair<std::allocator<nvrhi::BindingLayoutItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingLayoutItem> >,1>  �  std::memory_order ! �>  std::recursive_timed_mutex > 鷃  std::pair<nvrhi::TextureBindingKey const ,unsigned int>  wn  std::ratio<3600,1> R 蝝  std::_Default_allocator_traits<std::allocator<enum nvrhi::ResourceStates> > # �  std::_Atomic_storage<long,4> # j$  std::hash<nvrhi::BlendState>  �  std::atomic_flag f 7&  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> ` f�  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > > 3   std::equal_to<nvrhi::BufferStateExtension *> L 僝  std::_Compressed_pair<std::equal_to<nvrhi::IBindingLayout *>,float,1> / un  std::allocator<nvrhi::BindingLayoutItem> f 丳  std::vector<nvrhi::d3d12::ShaderTable::Entry,std::allocator<nvrhi::d3d12::ShaderTable::Entry> > | PP  std::vector<nvrhi::d3d12::ShaderTable::Entry,std::allocator<nvrhi::d3d12::ShaderTable::Entry> >::_Reallocation_policy  k3  std::system_error = fn  std::allocator_traits<std::allocator<unsigned short> > < �*  std::_Default_allocator_traits<std::allocator<char> > W �*  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > 8 @R  std::_Ptr_base<nvrhi::d3d12::InternalCommandList>  Xn  std::ratio<1,1> Z 豣  std::vector<enum nvrhi::ResourceStates,std::allocator<enum nvrhi::ResourceStates> > p   std::vector<enum nvrhi::ResourceStates,std::allocator<enum nvrhi::ResourceStates> >::_Reallocation_policy [ X�  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > >   �-  std::forward_iterator_tag  M2  std::runtime_error  N�  std::vector<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery>,std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > > � �  std::vector<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery>,std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::TimerQuery> > >::_Reallocation_policy � 鉩  std::list<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >   Z  std::bad_array_new_length � 塐  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > > > > E �(  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 7c  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > > / 昉  std::_Atomic_storage<unsigned __int64,8> \ b  std::_Compressed_pair<std::default_delete<nvrhi::BufferState>,nvrhi::BufferState *,1> 1 鏱  std::allocator<enum nvrhi::ResourceStates> g Vn  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > � 俈  std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > p 酼  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>  �4  std::_Yarn<char>    std::_Container_proxy ( Gn  std::_Facetptr<std::ctype<char> > Z (e  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > M 梋  std::_Compressed_pair<std::equal_to<nvrhi::TextureBindingKey>,float,1>  Dn  std::allocator<bool> H廦  std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0>  �  std::u16string  �  std::nested_exception  r  std::_Distance_unknown ( *  std::numeric_limits<unsigned int> < \`  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> B 資  std::_Conditionally_enabled_hash<nvrhi::IBindingLayout *,1> , �5  std::codecvt<char32_t,char,_Mbstatet> V n  std::_Default_allocator_traits<std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> > 8 I  std::array<nvrhi::RefCountPtr<nvrhi::ITexture>,9> � 睼  std::_Uhash_choose_transparency<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *>,void> � 肵  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > >,std::_Iterator_base0> @ (  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) �   std::array<nvrhi::IBindingSet *,5> K X  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy � 讓  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > >,1>     std::streamoff / 維  std::default_delete<nvrhi::d3d12::Queue> � 3n  std::_Uhash_compare<nvrhi::rt::IShaderTable *,std::hash<nvrhi::rt::IShaderTable *>,std::equal_to<nvrhi::rt::IShaderTable *> >    std::atomic<long> � 漑  std::_List_node<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > >,void *> & �-  std::initializer_list<char32_t> d �"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z s"  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & �-  std::initializer_list<char16_t> % �-  std::initializer_list<wchar_t> C �'  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   /  std::hash<std::nullptr_t> ' (*  std::numeric_limits<long double>  3  std::errc H   std::_Conditionally_enabled_hash<nvrhi::BufferStateExtension *,1> � 嘨  std::_Uhash_choose_transparency<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *>,void> R 玗  std::_Compressed_pair<std::equal_to<nvrhi::BufferStateExtension *>,float,1> , 踎  std::default_delete<std::_Facet_base>  �2  std::range_error + 訳  std::allocator<nvrhi::BufferBarrier> �Y  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > >,1>  <0  std::bad_typeid > �'  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �>  std::_UInt_is_zero N 峧  std::array<nvrhi::d3d12::CommandList::VolatileConstantBufferBinding,32> s oV  std::_Vector_val<std::_Simple_types<std::pair<nvrhi::TextureStateExtension *,enum nvrhi::ResourceStates> > > 驧  std::unordered_map<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *>,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >  n  std::ratio<1,1000000000>  &  std::allocator<char16_t> $ 鳹  std::default_delete<char [0]> � 豟  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> . �?  std::vector<bool,std::allocator<bool> > O ~U  std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > ` 4"  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v "  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J �8  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  n  std::ratio<1,1000> < U  std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >  n  std::ratio<1,10000000> M \l  std::_Default_allocator_traits<std::allocator<nvrhi::TextureBarrier> > )禮  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > >,1> ; �%  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  "5  std::_Crt_new_delete � MY  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > > > > % �3  std::_Iostream_error_category2 S 續  std::_Simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > � 
a  std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > � 躑  std::_Compressed_pair<std::allocator<nvrhi::BufferBarrier>,std::_Vector_val<std::_Simple_types<nvrhi::BufferBarrier> >,1> * �-  std::_String_constructor_concat_tag  m&  std::allocator<char> ) 	?  std::hash<nvrhi::BufferBindingKey> O n  std::allocator_traits<std::allocator<nvrhi::d3d12::ShaderTable::Entry> > G 轢  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>  
n  std::hash<void *>    std::nullptr_t & �-  std::random_access_iterator_tag w 缹  std::vector<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > > � 弻  std::vector<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::Buffer> > >::_Reallocation_policy ; �'  std::_Conditionally_enabled_hash<unsigned __int64,1> Z 薚  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > > ^ 臩  std::_Compressed_pair<std::default_delete<nvrhi::d3d12::Queue>,nvrhi::d3d12::Queue *,1> R nW  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > V 豘  std::_List_node<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,void *>  =4  std::bad_weak_ptr ) *  std::numeric_limits<unsigned long>   �&  std::_Atomic_padded<long> %m  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > > @ �;  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> N   std::vector<nvrhi::BufferBarrier,std::allocator<nvrhi::BufferBarrier> > d qC  std::vector<nvrhi::BufferBarrier,std::allocator<nvrhi::BufferBarrier> >::_Reallocation_policy # 賄  std::allocator<unsigned int> ~ |T  std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >  �4  std::_Yarn<wchar_t> = �'  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> � e\  std::_Compressed_pair<std::hash<nvrhi::TextureStateExtension *>,std::_Compressed_pair<std::equal_to<nvrhi::TextureStateExtension *>,float,1>,1> M n  std::allocator_traits<std::allocator<D3D12_RAYTRACING_INSTANCE_DESC> > 5 鰉  std::pair<unsigned int,D3D12_ROOT_DESCRIPTOR1>    std::wstring K 靘  std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> > 5 疨  std::_Atomic_integral_facade<unsigned __int64> ' *  std::numeric_limits<signed char> � �9  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �1  std::domain_error  �  std::u32string_view _ R�  std::vector<nvrhi::RefCountPtr<IUnknown>,std::allocator<nvrhi::RefCountPtr<IUnknown> > > u  �  std::vector<nvrhi::RefCountPtr<IUnknown>,std::allocator<nvrhi::RefCountPtr<IUnknown> > >::_Reallocation_policy  �  std::_Container_base I 衜  std::allocator_traits<std::allocator<enum nvrhi::ResourceStates> > X 耺  std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> >  .&  std::allocator<wchar_t> L z-  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > � 絤  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > i 蝂  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > � 釩  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > > > � Ze  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> > > $ �'  std::hash<nvrhi::IResource *> b 甿  std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> > �yA  std::_Hash<std::_Umap_traits<nvrhi::BufferStateExtension *,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> >,std::_Uhash_compare<nvrhi::BufferStateExtension *,std::hash<nvrhi::BufferStateExtension *>,std::equal_to<nvrhi::BufferStateExtension *> >,std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > >,0> > � 鱗  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > >,std::_Iterator_base0> �  a  std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > � 1Y  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 T$  std::hash<nvrhi::BlendState::RenderTarget>   *  std::numeric_limits<char> D 卐  std::_Default_allocator_traits<std::allocator<unsigned int> > - z]  std::equal_to<nvrhi::IBindingLayout *> 9 m1  std::chrono::duration<__int64,std::ratio<1,1000> >  �0  std::chrono::nanoseconds y '4  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �0  std::chrono::duration<__int64,std::ratio<1,1000000000> > , e  std::chrono::duration_values<__int64>  �0  std::chrono::seconds 3 +1  std::chrono::duration<int,std::ratio<60,1> > 6 �0  std::chrono::duration<__int64,std::ratio<1,1> > s �0  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   渕  std::chrono::steady_clock   檓  std::chrono::system_clock 6 @1  std::chrono::duration<double,std::ratio<60,1> > ; �1  std::chrono::duration<double,std::ratio<1,1000000> > > �1  std::chrono::duration<double,std::ratio<1,1000000000> > = �0  std::chrono::duration<__int64,std::ratio<1,10000000> > q �0  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5  1  std::chrono::duration<int,std::ratio<3600,1> > 8 �1  std::chrono::duration<double,std::ratio<1,1000> > < �1  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 W1  std::chrono::duration<double,std::ratio<1,1> > 8 1  std::chrono::duration<double,std::ratio<3600,1> >  O6  std::ctype_base  N:  std::filesystem::perms ' �:  std::filesystem::directory_entry $ S:  std::filesystem::copy_options ( >:  std::filesystem::filesystem_error 7 峖  std::filesystem::_Path_iterator<wchar_t const *> ) �7  std::filesystem::_Find_file_handle & �7  std::filesystem::_Is_slash_oper . �;  std::filesystem::_Should_recurse_result $ �=  std::filesystem::perm_options 4 �<  std::filesystem::recursive_directory_iterator . �:  std::filesystem::_File_status_and_error & 5;  std::filesystem::_Dir_enum_impl 0 G;  std::filesystem::_Dir_enum_impl::_Creator @ M;  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! ]:  std::filesystem::file_type . k;  std::filesystem::_Directory_entry_proxy " q=  std::filesystem::space_info * �;  std::filesystem::directory_iterator & '4  std::filesystem::file_time_type 0 �;  std::filesystem::_Recursive_dir_enum_impl ) �:  std::filesystem::directory_options # p:  std::filesystem::file_status u �9  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 搈  std::filesystem::_File_time_clock  �8  std::filesystem::path $ 8  std::filesystem::path::format * a^  std::filesystem::_Normal_conversion � 礏  std::vector<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > > � 傿  std::vector<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates>,std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > >::_Reallocation_policy < 宍  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �5  std::codecvt<char16_t,char,_Mbstatet>  x-  std::char_traits<char> � vW  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �2  std::error_category ) �2  std::error_category::_Addr_storage ! �3  std::_System_error_message  k  std::_Unused_parameter h &  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> S 縘  std::_Compressed_pair<std::equal_to<nvrhi::TextureStateExtension *>,float,1> A   std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 ;  std::shared_ptr<std::filesystem::_Dir_enum_impl> � C\  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > �諿  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > >,1> { 恗  std::_List_simple_types<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > > � T  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry> > = �'  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> � 僓  std::_Uhash_choose_transparency<nvrhi::TextureBindingKey,std::hash<nvrhi::TextureBindingKey>,std::equal_to<nvrhi::TextureBindingKey>,void> z 媘  std::allocator_traits<std::allocator<std::_List_node<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,void *> > >  �5  std::_Codecvt_mode @ �*  std::_Default_allocator_traits<std::allocator<char16_t> > I X\  std::_Conditionally_enabled_hash<nvrhi::TextureStateExtension *,1> U狹  std::_Hash<std::_Umap_traits<nvrhi::IBindingLayout *,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>,std::_Uhash_compare<nvrhi::IBindingLayout *,std::hash<nvrhi::IBindingLayout *>,std::equal_to<nvrhi::IBindingLayout *> >,std::allocator<std::pair<nvrhi::IBindingLayout * const,nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature> > >,0> > � :  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > 9 zm  std::shared_ptr<nvrhi::d3d12::CommandListInstance> 0 d*  std::_Char_traits<wchar_t,unsigned short> '   std::array<enum nvrhi::Format,8> ' Lm  std::array<unsigned __int64,128> � )m  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > >,void *> > > �\  std::_Umap_traits<nvrhi::TextureStateExtension *,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> >,std::_Uhash_compare<nvrhi::TextureStateExtension *,std::hash<nvrhi::TextureStateExtension *>,std::equal_to<nvrhi::TextureStateExtension *> >,std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > >,0> \ C)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 I&  std::_String_val<std::_Simple_types<wchar_t> > < �&  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  q4  std::_Facet_base ' �#  std::hash<nvrhi::BindingSetItem> [ 4V  std::allocator<std::pair<nvrhi::BufferStateExtension *,enum nvrhi::ResourceStates> > " r*  std::_WChar_traits<wchar_t> 2 96  std::codecvt<unsigned short,char,_Mbstatet> z n]  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> o 麶  std::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > > � 蔎  std::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >::_Reallocation_policy � 'm  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > > # �3  std::_Generic_error_category � 蹕  std::vector<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > > �   std::vector<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::d3d12::StagingTexture> > >::_Reallocation_policy  -*  std::streampos  ~-  std::input_iterator_tag � 砙  std::_Compressed_pair<std::hash<nvrhi::BufferStateExtension *>,std::_Compressed_pair<std::equal_to<nvrhi::BufferStateExtension *>,float,1>,1> 2 q`  std::_Wrap<std::filesystem::_Dir_enum_impl> ! eI  std::array<unsigned int,8> X 鉥  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> B pK  std::vector<unsigned short,std::allocator<unsigned short> > X ?K  std::vector<unsigned short,std::allocator<unsigned short> >::_Reallocation_policy � 檁  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > > *> � 璙  std::allocator<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > � m  std::allocator_traits<std::allocator<std::pair<nvrhi::BufferStateExtension * const,std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > > > > ] 鎶  std::_Uninitialized_backout_al<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > > 0 汸  std::_Atomic_integral<unsigned __int64,8> , 蜸  std::_Atomic_padded<unsigned __int64> ' (  std::hash<enum nvrhi::ColorMask> 4 禲  std::equal_to<nvrhi::TextureStateExtension *>  �5  std::codecvt_base S 萢  std::unique_ptr<nvrhi::BufferState,std::default_delete<nvrhi::BufferState> > / 韆  std::default_delete<nvrhi::TextureState>  l0  std::bad_function_call O �)  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > ; 	m  std::allocator_traits<std::allocator<unsigned int> > b m  std::allocator_traits<std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > ' �=  std::hash<std::filesystem::path> 1 鴏  std::shared_ptr<nvrhi::d3d12::BufferChunk> V RH  std::array<std::pair<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,unsigned int>,5> P ,C  std::vector<nvrhi::TextureBarrier,std::allocator<nvrhi::TextureBarrier> > f 鶥  std::vector<nvrhi::TextureBarrier,std::allocator<nvrhi::TextureBarrier> >::_Reallocation_policy  �'  std::hash<unsigned int> 7 v-  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers � Y  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,nvrhi::d3d12::RayTracingPipeline::ExportTableEntry>,void *> � Gd  std::list<std::pair<nvrhi::TextureBindingKey const ,unsigned int>,std::allocator<std::pair<nvrhi::TextureBindingKey const ,unsigned int> > > V 蔿  std::vector<nvrhi::BindingLayoutItem,std::allocator<nvrhi::BindingLayoutItem> > l 榣  std::vector<nvrhi::BindingLayoutItem,std::allocator<nvrhi::BindingLayoutItem> >::_Reallocation_policy F t-  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > D ^l  std::allocator_traits<std::allocator<nvrhi::TextureBarrier> > � 蘙  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureStateExtension * const,std::unique_ptr<nvrhi::TextureState,std::default_delete<nvrhi::TextureState> > > > > > > . �  std::array<nvrhi::BindingLayoutItem,16> � 
U  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IResource> > >,1> $ �'  std::hash<enum nvrhi::Format>  *  std::numeric_limits<int> � Pl  std::list<std::shared_ptr<nvrhi::d3d12::InternalCommandList>,std::allocator<std::shared_ptr<nvrhi::d3d12::InternalCommandList> > > 2 �&  std::_String_val<std::_Simple_types<char> > 9 �&  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access } ~L  std::vector<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > > � LL  std::vector<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> > >::_Reallocation_policy 
 !   wint_t  r-  CACLIPDATA & >S  nvrhi::AftermathCrashDumpHelper # �"  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet ; 覱  nvrhi::RefCountPtr<nvrhi::d3d12::RayTracingPipeline>  ;  nvrhi::BindingSetDesc  p-  nvrhi::SubresourceTiling $ /#  nvrhi::GraphicsPipelineHandle    nvrhi::ResourceType  u   nvrhi::ObjectType 1 莐  nvrhi::RefCountPtr<ID3D12CommandAllocator> ) X  nvrhi::RefCountPtr<nvrhi::IShader>  #  nvrhi::InputLayoutHandle   J   nvrhi::IndexBufferBinding   l-  nvrhi::MemoryRequirements 5 瀔  nvrhi::static_vector<D3D12_ROOT_PARAMETER1,32> 4 鶳  nvrhi::RefCounter<nvrhi::d3d12::ICommandList>  #   nvrhi::GpuVirtualAddress % Uk  nvrhi::RefCountPtr<ID3D12Heap> 8 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> 6 H  nvrhi::RefCounter<nvrhi::d3d12::IRootSignature> " -  nvrhi::VulkanBindingOffsets  :  nvrhi::ResourceStates . M  nvrhi::RefCounter<nvrhi::rt::IPipeline> 0 滵  nvrhi::RefCountPtr<nvrhi::IShaderLibrary> ) 盙  nvrhi::RefCounter<nvrhi::ISampler> , NU  nvrhi::RefCounter<nvrhi::IEventQuery> / 耈  nvrhi::RefCounter<nvrhi::IShaderLibrary>  j   nvrhi::GraphicsState 1 鬕  nvrhi::RefCounter<nvrhi::rt::IAccelStruct> / �  nvrhi::static_vector<nvrhi::Viewport,16> ` }H  nvrhi::static_vector<std::pair<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,unsigned int>,5>    nvrhi::ShaderDesc - XU  nvrhi::RefCounter<nvrhi::IInputLayout>  S!  nvrhi::IComputePipeline : "!  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc 7 猘  nvrhi::RefCountPtr<nvrhi::d3d12::IRootSignature>    nvrhi::Rect ( +k  nvrhi::RefCountPtr<ID3D12Device8> 1 �j  nvrhi::RefCountPtr<ID3D12CommandSignature>  �  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray  u   nvrhi::MipLevel  v   nvrhi::IGraphicsPipeline 4 橰  nvrhi::RefCountPtr<ID3D12GraphicsCommandList> ! 滵  nvrhi::ShaderLibraryHandle  <  nvrhi::FramebufferInfoEx ' 謏  nvrhi::RefCountPtr<ID3D12Device> ( +S  nvrhi::ShaderBinaryLookupCallback B >I  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::ITexture>,9>  b  nvrhi::IShader  `  nvrhi::TextureDesc    nvrhi::ISampler ! 9   nvrhi::VertexBufferBinding !    nvrhi::ComputePipelineDesc G 侸  nvrhi::static_vector<std::pair<unsigned int,nvrhi::IBuffer *>,6>  Q  nvrhi::SamplerDesc  r  nvrhi::TextureSlice X 甹  nvrhi::static_vector<nvrhi::d3d12::CommandList::VolatileConstantBufferBinding,32> * cj  nvrhi::RefCountPtr<ID3D12QueryHeap> # 牂  nvrhi::MeshletPipelineHandle  @  nvrhi::Format  *!  nvrhi::DrawArguments  漇  nvrhi::DxgiFormatMapping  m!  nvrhi::MeshletState  �  nvrhi::IBuffer  �  nvrhi::Color + @  nvrhi::static_vector<nvrhi::Rect,16>  �  nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  =  nvrhi::BindingLayoutDesc   D  nvrhi::SamplerAddressMode  z#  nvrhi::IDevice ! �  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray ) +E  nvrhi::RefCountPtr<ID3D12Resource>  b  nvrhi::BufferState 6 #J  nvrhi::RefCountPtr<nvrhi::d3d12::BindingLayout> . �?  nvrhi::RefCountPtr<nvrhi::ICommandList>   f-  nvrhi::TiledTextureRegion  y!  nvrhi::IMeshletPipeline  }  nvrhi::RasterState & �  nvrhi::VariableRateShadingState  b-  nvrhi::IStagingTexture 5 9j  nvrhi::RefCountPtr<ID3D12GraphicsCommandList6> ) 2S  nvrhi::ShaderHashGeneratorFunction ! �?  nvrhi::utils::ScopedMarker $ ?  nvrhi::utils::BitSetAllocator M 
j  nvrhi::static_vector<std::pair<unsigned int,D3D12_ROOT_DESCRIPTOR1>,6> . #  nvrhi::RefCountPtr<nvrhi::IInputLayout>  �  nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " '  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  V  nvrhi::TextureBarrier / 瞚  nvrhi::RefCountPtr<ID3D12DescriptorHeap>  B  nvrhi::TextureDimension , 釯  nvrhi::RefCounter<nvrhi::IBindingSet> 0 �  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' \!  nvrhi::DispatchIndirectArguments 6 N  nvrhi::RefCountPtr<nvrhi::d3d12::RootSignature> / 轌  nvrhi::RefCounter<nvrhi::IBindingLayout>  #  nvrhi::SamplerHandle 1 矷  nvrhi::RefCounter<nvrhi::IComputePipeline> * ;!  nvrhi::DrawIndexedIndirectArguments - 菻  nvrhi::RefCounter<nvrhi::IFramebuffer> # B#  nvrhi::DescriptorTableHandle $ 奿  nvrhi::AftermathMarkerTracker  �  nvrhi::ShaderType - si  nvrhi::RefCountPtr<ID3D12CommandQueue>  "#  nvrhi::TimerQueryHandle + =Z  nvrhi::RefCountPtr<nvrhi::IResource> , 荊  nvrhi::RefCounter<nvrhi::ITimerQuery> & 禗  nvrhi::RefCounter<nvrhi::IHeap>   �  nvrhi::BindlessLayoutDesc  �  nvrhi::CustomSemantic " �  nvrhi::CustomSemantic::Type ! *   nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128> 0 BG  nvrhi::RefCounter<nvrhi::IStagingTexture> ( Ji  nvrhi::RefCountPtr<ID3D12Device2>  VE  nvrhi::HeapHandle # 2#  nvrhi::ComputePipelineHandle  /S  nvrhi::ResolvedMarker  X-  nvrhi::PackedMipDesc  j  nvrhi::RasterFillMode - @  nvrhi::CommandListResourceStateTracker  u   nvrhi::ArraySlice  8  nvrhi::IResource , d�  nvrhi::RefCountPtr<ID3D12CommandList>  �   nvrhi::IBindingSet 1 扥  nvrhi::RefCounter<nvrhi::rt::IShaderTable>  T-  nvrhi::TileShape # �?  nvrhi::TextureStateExtension ; �  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> 1 ~K  nvrhi::RefCounter<nvrhi::IDescriptorTable> - P  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �"  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % -  nvrhi::ISamplerFeedbackTexture / 鍷  nvrhi::RefCountPtr<nvrhi::d3d12::Buffer>  �"  nvrhi::CommandQueue  2  nvrhi::BlendFactor  #  nvrhi::EventQueryHandle  "  nvrhi::BindingLayoutItem . 琀  nvrhi::RefCountPtr<ID3D12RootSignature>  -#  nvrhi::FramebufferHandle  鞺  nvrhi::BufferBarrier 1 2  nvrhi::static_vector<enum nvrhi::Format,8> * u   nvrhi::d3d12::OptionalResourceState  pE  nvrhi::d3d12::Context " H  nvrhi::d3d12::RootSignature ' M  nvrhi::d3d12::RayTracingPipeline 9 i  nvrhi::d3d12::RayTracingPipeline::ExportTableEntry  鮅  nvrhi::d3d12::BindingSet  螱  nvrhi::d3d12::TimerQuery + lG  nvrhi::d3d12::SamplerFeedbackTexture  籊  nvrhi::d3d12::Sampler ) i  nvrhi::d3d12::StaticDescriptorHeap   L  nvrhi::d3d12::AccelStruct  镈  nvrhi::d3d12::Texture  鸆  nvrhi::d3d12::Shader   跦  nvrhi::d3d12::Framebuffer  砇  nvrhi::d3d12::IDevice % 	i  nvrhi::d3d12::ShaderTableState  LS  nvrhi::d3d12::Queue ( 
�  nvrhi::d3d12::CommandListInstance %   nvrhi::d3d12::GraphicsPipeline $ 糏  nvrhi::d3d12::ComputePipeline # H  nvrhi::d3d12::IRootSignature  﨔  nvrhi::d3d12::Buffer ' mD  nvrhi::d3d12::ShaderLibraryEntry ( oR  nvrhi::d3d12::InternalCommandList $ 奒  nvrhi::d3d12::DescriptorTable $ 丒  nvrhi::d3d12::DeviceResources ( 猘  nvrhi::d3d12::RootSignatureHandle  i  nvrhi::d3d12::DeviceDesc ' u   nvrhi::d3d12::RootParameterIndex $  i  nvrhi::d3d12::IDescriptorHeap " 闓  nvrhi::d3d12::BindingLayout # TG  nvrhi::d3d12::StagingTexture 0 録  nvrhi::d3d12::StagingTexture::SliceRegion  綝  nvrhi::d3d12::Heap $ 諭  nvrhi::d3d12::MeshletPipeline " 醜  nvrhi::d3d12::UploadManager ' 睓  nvrhi::d3d12::DX12_ViewportState  S  nvrhi::d3d12::Device $ u   nvrhi::d3d12::DescriptorIndex ! 騊  nvrhi::d3d12::ICommandList # 鵊  nvrhi::d3d12::BindlessLayout $ 篕  nvrhi::d3d12::OpacityMicromap !   nvrhi::d3d12::TextureState     nvrhi::d3d12::ShaderTable ' 镺  nvrhi::d3d12::ShaderTable::Entry   [Q  nvrhi::d3d12::CommandList ? 舎  nvrhi::d3d12::CommandList::VolatileConstantBufferBinding  #  nvrhi::BufferHandle  �  nvrhi::StencilOp 6 {N  nvrhi::RefCountPtr<ID3D12StateObjectProperties>  �>  nvrhi::BufferBindingKey ) 蔇  nvrhi::RefCounter<nvrhi::ITexture>  �  nvrhi::IBindingLayout  -  nvrhi::ColorMask  �  nvrhi::FramebufferInfo  滸  nvrhi::TextureHandle  P-  nvrhi::IEventQuery 1 薎  nvrhi::RefCounter<nvrhi::IMeshletPipeline>  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  J-  nvrhi::IMessageCallback  �  nvrhi::PrimitiveType ( 顲  nvrhi::RefCounter<nvrhi::IShader> , MN  nvrhi::RefCountPtr<ID3D12StateObject>  b  nvrhi::BlendState & F  nvrhi::BlendState::RenderTarget 3 �   nvrhi::static_vector<nvrhi::IBindingSet *,5> " �  nvrhi::GraphicsPipelineDesc " �?  nvrhi::BufferStateExtension H �  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 4-  nvrhi::TiledTextureCoordinate  0-  nvrhi::IHeap  1S  nvrhi::BinaryBlob # g  nvrhi::FramebufferAttachment + 侷  nvrhi::static_vector<unsigned int,8>  �   nvrhi::BindingSetVector 5 琄  nvrhi::RefCounter<nvrhi::rt::IOpacityMicromap> 2 牂  nvrhi::RefCountPtr<nvrhi::IMeshletPipeline>  P  nvrhi::BindingSetHandle ( )-  nvrhi::SamplerFeedbackTextureDesc ! �  nvrhi::BindingLayoutVector . 胔  nvrhi::RefCountPtr<ID3D12PipelineState> ' VE  nvrhi::RefCountPtr<nvrhi::IHeap> " �"  nvrhi::StagingTextureHandle    nvrhi::Object  -  nvrhi::IInputLayout  l  nvrhi::RasterCullMode '   nvrhi::rt::AccelStructBuildFlags  h  nvrhi::rt::InstanceFlags "   nvrhi::rt::IOpacityMicromap  y  nvrhi::rt::InstanceDesc  �  nvrhi::rt::GeometryFlags !   nvrhi::rt::GeometrySpheres # �,  nvrhi::rt::ShaderTableHandle + 	  nvrhi::rt::OpacityMicromapUsageCount $ �!  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   7#  nvrhi::rt::PipelineHandle ! 7  nvrhi::rt::AffineTransform & �!  nvrhi::rt::PipelineHitGroupDesc  0  nvrhi::rt::GeometryLss 3 -  nvrhi::rt::cluster::OperationBlasBuildParams . -  nvrhi::rt::cluster::OperationMoveParams ( -  nvrhi::rt::cluster::OperationDesc 3 -  nvrhi::rt::cluster::OperationClasBuildParams , 
-  nvrhi::rt::cluster::OperationSizeInfo * 	-  nvrhi::rt::cluster::OperationParams  9  nvrhi::rt::GeometryType ' J#  nvrhi::rt::OpacityMicromapHandle  S  nvrhi::rt::GeometryDesc - X  nvrhi::rt::GeometryDesc::GeomTypeUnion % `  nvrhi::rt::OpacityMicromapDesc # �  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # Z  nvrhi::rt::AccelStructHandle  �"  nvrhi::rt::IShaderTable ' �"  nvrhi::rt::DispatchRaysArguments  �"  nvrhi::rt::State    nvrhi::rt::GeometryAABBs  �!  nvrhi::rt::PipelineDesc  �,  nvrhi::rt::IPipeline 2 桰  nvrhi::RefCounter<nvrhi::IGraphicsPipeline>  �?  nvrhi::CommandListHandle # 2!  nvrhi::DrawIndirectArguments ! �,  nvrhi::TextureTilesMapping / 籖  nvrhi::RefCounter<nvrhi::d3d12::IDevice>  5  nvrhi::HeapDesc  �#  nvrhi::ICommandList  �  nvrhi::BufferDesc  �,  nvrhi::IDescriptorTable & 4G  nvrhi::RefCountPtr<ID3D12Fence>  〥  nvrhi::IShaderLibrary * 滸  nvrhi::RefCountPtr<nvrhi::ITexture>  4  nvrhi::BlendOp  H!  nvrhi::ComputeState 2 Z  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct> 8 ^G  nvrhi::RefCounter<nvrhi::ISamplerFeedbackTexture>  �>  nvrhi::TextureBindingKey  �   nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc  �  nvrhi::Viewport ( 鐵  nvrhi::RefCounter<nvrhi::IBuffer>  �  nvrhi::RenderState ( 沨  nvrhi::RefCountPtr<ID3D12Device5> 7 oh  nvrhi::static_vector<D3D12_DESCRIPTOR_RANGE1,32>  b  nvrhi::TextureState  X  nvrhi::ShaderHandle  u  nvrhi::ITexture  �,  nvrhi::ITimerQuery 5 %h  nvrhi::RefCountPtr<ID3D12GraphicsCommandList4>  �,  VARDESC     LONG  �,  ITypeLib  F,  tagCACY  �,  tagBSTRBLOB  �,  tagCAUH  �$  _TP_CALLBACK_ENVIRON_V3 0 �$  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> B �$  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s>  D,  _ULARGE_INTEGER ( �,  _ULARGE_INTEGER::<unnamed-type-u>  �3  __std_win_error  p%  LPVARIANT  �+  SAFEARRAY  �4  lconv  鄁  D3D_SRV_DIMENSION  �,  tagCABOOL   +  __RTTIBaseClassDescriptor   g  D3D12_SHADER_CACHE_MODE  @U  ID3D12RootSignature  <,  tagBLOB 
 �,  CABOOL  駁  NVDX_SwapChainHandle   玡  D3D12_ROOT_PARAMETER_TYPE 
    _off_t  雊  D3D12_BOX  #   ULONG64 
 U  SNB ! 間  D3D12_RESOURCE_UAV_BARRIER  %+  _tagINTERNETFEATURELIST ' 鏶  _NV_GPU_WORKSTATION_FEATURE_TYPE  �,  CABSTRBLOB 
 #   SIZE_T  �,  tagTYPEATTR  �  stat ) 錱  D3D12_GRAPHICS_PIPELINE_STATE_DESC  舋  LUID  t   int32_t B 褑  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC  �  timespec  譯  D3D12_RANGE & 塯  $_TypeDescriptor$_extraBytes_37  D=  __std_fs_file_id 
 !   _ino_t  焒  NVDX_ObjectHandle__ 
 L>  _Cnd_t  誫  D3D12_HEAP_PROPERTIES !   ID3D12GraphicsCommandList3  A   DATE # �,  ReplacesCorHdrNumericDefines  蟝  D3D12_TEX3D_UAV  +%  FS_BPIO_OUTFLAGS 
 #   UINT64  "   DWORD 
 !   UINT16  蚲  D3D12_TEX2DMS_UAV  薵  D3D12_TEX2D_UAV  蛦  D3D12_RASTERIZER_DESC  �$  PTP_CALLBACK_INSTANCE 
   PSHORT ' -=  __std_fs_create_directory_result  蒰  D3D12_TEX2D_ARRAY_UAV  沢  D3D12_MESSAGE_ID  "   TP_VERSION      UINT8  q  BSTR  沞  D3D_DRIVER_TYPE  !   uint16_t  �3  __std_fs_stats $ 醙  D3D12_PRIMITIVE_TOPOLOGY_TYPE  �+  CAUB  �,  ITypeInfo  n%  tagPROPVARIANT  r�  D3D12_RT_FORMAT_ARRAY  H,  CAUL M s  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  TT  ID3D12StateObject   #   D3D12_GPU_VIRTUAL_ADDRESS  F,  CACY  舋  _LUID ' 膅  D3D12_FEATURE_DATA_D3D12_OPTIONS  昰  tagRECT  �  _Mbstatet  D,  ULARGE_INTEGER  �$  TP_CALLBACK_PRIORITY  a  _locale_t  竒  _SECURITY_ATTRIBUTES B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; A,  __vcrt_va_list_is_reference<__crt_locale_pointers *>  2+  VARENUM     intmax_t  �+  tagCASCODE # 秄  D3D_TESSELLATOR_PARTITIONING ) �  D3D12_RENDER_PASS_BEGINNING_ACCESS  癵  NVDX_ObjectHandle  ]  terminate_handler  �+  _s__RTTIBaseClassArray  粏  D3D12_SHADER_BYTECODE  
+  tagCACLSID  緀  _NV_LICENSE_FEATURE_TYPE  �$  MACHINE_ATTRIBUTES  峞  D3D_RESOURCE_RETURN_TYPE 
 H  ldiv_t  =,  tagCALPWSTR  �3  __std_fs_file_flags  �4  _Cvtvec ) 玤  D3D12_PLACED_SUBRESOURCE_FOOTPRINT  <,  BLOB ! g  _NV_COLOR_SELECTION_POLICY  #   DWORD64  u   _Thrd_id_t ! 覅  ID3D12GraphicsCommandList1 (   D3D12_FEATURE_DATA_D3D12_OPTIONS7  !   PROPVAR_PAD1 - 0+  $_s__RTTIBaseClassArray$_extraBytes_24  �$  PTP_SIMPLE_CALLBACK  g  D3D12_MESSAGE_CATEGORY 
 t   INT  昰  D3D12_RECT  �+  _CatchableTypeArray  :,  IStorage ! .�  ID3D12GraphicsCommandList4  v%  tagVARIANT 
 �+  tagCAI 
 A   DOUBLE      UCHAR " 廹  D3D12_CPU_DESCRIPTOR_HANDLE   媑  _D3D_SHADER_CBUFFER_FLAGS  "   LCID      BOOLEAN  �$  PTP_CALLBACK_ENVIRON  uQ  ID3D12Object  �3  __std_fs_copy_options     ptrdiff_t  �+  tagTYSPEC  N%  LPVERSIONEDSTREAM  �  _stat64i32  塭  D3D12_LOGIC_OP  �+  tagDISPPARAMS  磫  ID3D12MetaCommand 
 !   USHORT  �+  _PMD      uint8_t  z%  LPUWSTR  +  tagVARKIND  �/  type_info    PVOID ' g  D3D12_UNORDERED_ACCESS_VIEW_DESC  �+  SAFEARRAYBOUND  sg  ID3D10Blob ' �+  _s__RTTIClassHierarchyDescriptor  Q+  IUnknown  t   errno_t  q   WCHAR     PBYTE  ag  D3D12_VERTEX_BUFFER_VIEW  窾  ID3D12Resource 9 焼  D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC  璭  D3D_TESSELLATOR_DOMAIN  �3  __std_fs_reparse_tag  %  _DEVICE_DSM_DEFINITION 
 �+  tagCAC  �+  tagCAUB  K  _lldiv_t  渾  D3D12_BLEND_DESC 
 w$  IID ! 榝  _D3D_SHADER_VARIABLE_FLAGS # _g  D3D12_ROOT_DESCRIPTOR_TABLE1  '+  _tagQUERYOPTION  q  LPOLESTR  廵  D3D_PRIMITIVE  Zg  D3D12_TILE_SHAPE  �+  tagExtentMode  �+  __MIDL_IUri_0002 & g  $_TypeDescriptor$_extraBytes_50     HRESULT  噀  _D3D_SHADER_INPUT_TYPE  罳  D3D12_PRIMITIVE_TOPOLOGY  Tg  ID3D12PipelineState 
 �+  CAI  0  __std_type_info_data  %  PDEVICE_DSM_INPUT  ?g  ID3D12CommandSignature & �+  $_TypeDescriptor$_extraBytes_27  �+  CASCODE  �  _s__ThrowInfo  4  __std_fs_convert_result / 蔲  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER ! �+  __MIDL_IGetBindHandle_0001  +g  ID3D12CommandAllocator  哘  ID3D12DeviceChild  �3  __std_fs_stats_flags  �+  tagCY 
    LONG64 " g  D3D12_TEXTURE_COPY_LOCATION  )+  tagCOINITBASE @ �  D3D12_RENDER_PASS_ENDING_ACCESS_PRESERVE_LOCAL_PARAMETERS & 鴉  $_TypeDescriptor$_extraBytes_47  %  LPCUWSTR  "   ULONG  �+  __RTTIBaseClassArray ! g  D3D12_STATE_SUBOBJECT_TYPE  !   VARTYPE  t   BOOL  垎  D3D12_DEPTH_STENCIL_DESC 
 �+  CAC  �  __crt_locale_data_public  �+  tagApplicationType 0 #%  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES_OUTPUT  R  LPCWSTR & �+  DISPLAYCONFIG_SCANLINE_ORDERING - �+  $_s__CatchableTypeArray$_extraBytes_24  @   FLOAT  !   PROPVAR_PAD3  +  tagDOMNodeType  �+  CAUI  �+  tagCLIPDATA  鱡  NV_MOSAIC_TOPO  
>  _Mtx_internal_imp_t  �+  tagSAFEARRAY & 齟  $_TypeDescriptor$_extraBytes_25  "   DEVICE_DSM_ACTION % �+  __RTTIClassHierarchyDescriptor  �4  _Collvec   �<  __std_fs_volume_name_kind  歈  ID3D12CommandList  �+  tagVersionedStream 
 �+  CABSTR ( 駀  D3D12_FEATURE_DATA_D3D12_OPTIONS6     __time64_t  +  tagCHANGEKIND 
 u   UINT32 ( 韋  D3D12_RESOURCE_TRANSITION_BARRIER  m  FILE ! 閒  D3D12_DESCRIPTOR_HEAP_DESC  �+  tagSYSKIND  錰  D3D12_ROOT_PARAMETER1 & 趂  $_TypeDescriptor$_extraBytes_26 2 辠  D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_MODE    _NV_DESKTOP_COLOR_DEPTH 
 >  _Mtx_t 3 �+  __vcrt_va_list_is_reference<wchar_t const *>  躥  D3D12_VIEWPORT  �+  IDispatch  鷥  ID3D12Device2  w$  CLSID  �  mbstate_t  j�  D3D12_SAMPLE_POSITION  �  _PMFN  #   uintptr_t 
 q  LPWSTR  n%  PROPVARIANT  P%  LPSAFEARRAY  #   UINT_PTR  �$  PTP_POOL  �+  _s__CatchableTypeArray 
   LPVOID  )=  __std_fs_remove_result  w$  GUID * �$  _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG ' 萬  D3D_TESSELLATOR_OUTPUT_PRIMITIVE # 苀  D3D12_INDIRECT_ARGUMENT_TYPE  圦  D3D12_COMMAND_LIST_TYPE  �$  TP_CALLBACK_ENVIRON_V3  #+  tagFUNCKIND  �+  LARGE_INTEGER 
 �+  CAH  竑  D3D12_INDEX_BUFFER_VIEW  t   INT32  �+  tagCAFILETIME 
   HANDLE  瞗  D3D12_LIFETIME_STATE  �*  PIDMSI_STATUS_VALUE  癴  _D3D_CBUFFER_TYPE  #   ULONGLONG  �+  tagCAPROPVARIANT ( �$  PTP_CLEANUP_GROUP_CANCEL_CALLBACK # 甪  D3D12_COMMAND_SIGNATURE_DESC ( ゝ  D3D12_FEATURE_DATA_D3D12_OPTIONS1 	 �+  CY  �=  _Thrd_t  �+  FILETIME  %  PDEVICE_DSM_RANGE ( 歠  D3D12_DEBUG_DEVICE_PARAMETER_TYPE  杅  D3D12_SUBRESOURCE_TILING - �+  $_s__RTTIBaseClassArray$_extraBytes_16  +  __MIDL_IUri_0001 & 攆  D3D12_STREAM_OUTPUT_BUFFER_VIEW &   D3D12_RENDER_PASS_ENDING_ACCESS 
 /%  REGCLS ! 靺  ID3D12GraphicsCommandList2 - 磂  $_s__RTTIBaseClassArray$_extraBytes_32  抐  ID3D12Device  |+  IRecordInfo 
 #   size_t  !%  PDEVICE_DSM_OUTPUT ! U�  ID3D12GraphicsCommandList6 
    time_t  �3  __std_fs_file_attr     LONGLONG  鮡  ID3D12CommandQueue   蝒  D3D12_MEASUREMENTS_ACTION  蔱  _NV_SCALING % 鷨  D3D12_RENDER_TARGET_BLEND_DESC + 鴨  D3D12_RENDER_PASS_RENDER_TARGET_DESC  
  __std_exception_data * 檈  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE 
 u   _dev_t  芿  D3D12_RESOURCE_BARRIER  �<  __std_ulong_and_error ) %  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES  :+  tagGLOBALOPT_EH_VALUES * �$  PCUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG  K  lldiv_t     SHORT    PLONG64  秂  D3D12_DISCARD_REGION  H  _ldiv_t  3%  COWAIT_FLAGS     SCODE  ++  tagCLSCTX  �  _timespec64 + 鰡  D3D12_WRITEBUFFERIMMEDIATE_PARAMETER     intptr_t  竒  SECURITY_ATTRIBUTES     INT_PTR  漞  _D3D_SHADER_INPUT_FLAGS  u   uint32_t    D3D12_SAMPLER_DESC  +  tagXMLEMEM_TYPE " 焑  D3D_REGISTER_COMPONENT_TYPE 
 m  _iobuf 
 +  CADATE  p   CHAR  
+  CACLSID  !   PROPVAR_PAD2  +  _tagPARSEACTION  揺  D3D12_MESSAGE_SEVERITY + 慹  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE  p  LPSTR  +  tagDESCKIND ! D�  ID3D12GraphicsCommandList5  j  __crt_locale_pointers + 魡  D3D12_RENDER_PASS_DEPTH_STENCIL_DESC 
 �*  tagCAL  #   DWORDLONG   'R  ID3D12GraphicsCommandList    �   p0      靋!揕�H|}��婡欏B箜围紑^@�銵  A    窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  �    Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �    �!栁闙h闸@}Sx汧=�亏B謉午�     鳘�禽橉g米KD巟陪D D'oe�  `   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   襋钡�/翤㏄^觜�藔膛vЮ7��37嵇  �   択赜Oy圹;铥 N�<鸰蜬,Q雮JR�,�7  6   t�j噾捴忊��
敟秊�
渷lH�#  u   馒{蓱蕩L蘯e�LW�疗譄幗IR:;a�  �   _%1糠7硘籺蚻q5饶昈v纪嗈�     ��8/�
0躚/﨤h盙裉餠G怤爛��]�  Y   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�     澏泼寊H"鱄LUHFy]厽I媢h�J赫Cp   b   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  �   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  2   �:2K] �
j�苊赁e�
湿�3k椨�  |   樸7 忁�珨��3]"Fキ�:�,郩�  �   猯�諽!~�:gn菾�]騈购����'      犢x琿儳莻k鬿锘HC閷�!(/捱&eM殙�  H    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   掴'圭,@H4sS裬�!泉:莠й�"fE)  �   <瑓�傻a鱼张隥T$ vJ黠I鯝�0  .   �茬� 霚FmH暃蜽.駢宫誖�6?靵oP  v   U恂{榸冾�fⅢ��Hb釃"�6e`a  �   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:     �fE液}髢V壥~�?"浬�^PEΡ4L�  V   嶹栢ABZC凂U久Gk�!貟~龡单癉Q  �   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  ,	   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  u	   葦鮆培XDW駐鰄�W{�+ノ2$<F$帰B  �	   t$�>喰k柸窣鄾埉笺%撙>2 pg�,  
   _O縋[HU-銌�鼪根�鲋薺篮�j��  O
   8�'预P�憖�0R�(3銖� pN*�  �
   sL&%�znOdz垗�M,�:吶1B滖  �
   l籴靈LN~噾2u�< 嵓9z0iv&jザ  :   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  }   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS     )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  D    狾闘�	C縟�&9N�┲蘻c蟝2  �   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �   �'稌� 变邯D)\欅)	@'1:A:熾/�  
   	{Z�范�F�m猉	痹缠!囃ZtK�T�  Z
   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �
   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �
   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲     毌-��
(襔  橥轃\|�!�!p牶F  e   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   j蛹�#翎笶另Tu汔
W眡%y徚;Z鬖5  '   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  s   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  �   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�     繃S,;fi@`騂廩k叉c.2狇x佚�  Q   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  �   悯R痱v 瓩愿碀"禰J5�>xF痧  +   渐袿.@=4L笴速婒m瑜;_琲M %q�  }   矨�陘�2{WV�y紥*f�u龘��  �   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶     .�-髳�o2o~翵4D�8鷗a殔氰3籃G  Z   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  �   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  >   6觏v畿S倂9紵"�%��;_%z︹  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  !   郖�Χ葦'S詍7,U若眤�M进`  r   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   �n儹`
舔�	Y氀�:b
#p:  
   "�挨	b�'+舒�5<O�呱_歲+/�P�?  V   �>2
^�﨟2W酟傲X{b?荼猲�;  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   L錁鞾啂�JB媥潉�Z傶oKW榮忷霜�  +   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  u   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   "   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  d   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   � `�6_H蝺�W覶G沁�"Я?=!�     J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  \   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   S烌纎}啤�礴餔馞:E: �y琖�0槑&  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱     9艁嗜匠鋙-驣鈡壠�
}'禸�耣鰉蛾糡  R   5ビ�)昶��d腺V� �4禥Z嵫悪�	,%qq,  ~   衠琪槡铟钭}_XO>�蛭X�7Mp处d  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�     �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  ]   �~鴧傳.P怬WsP-"焫#N�:�&場璁  �   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �   �	玮媔=zY沚�c簐P`尚足,\�>:O  /   伙a蕑堐v!@曓�+梭�'驂蒀飫鴐  {   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  �   ,醕奂鸭uo'暬L�搟q嫾銊燠B馟�  �   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  D   ��#�#苹�1覀鉵6焉BK�1骓傎�  r   �猥��6%s� t鋁r{`玈}畫噺l鑫�  �   駒og暼硚~鮼_5屏�8BL.9妖"�r/�-R  �   チ畴�
�&u?�#寷K�資 +限^塌>�j  "   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  b   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   穫農�.伆l'h��37x,��
fO��  �   5�\營	6}朖晧�-w氌rJ籠騳榈      }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  c   頒牛/�	� G犨韈圂J�.山o楾鐴  �   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  �   喲8s7痺撆�&!林qA>ブ�Y~a璳`N3�  G   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �   ��(`.巑QEo"焷�"娧汝l毮89fб�      �*o驑瓂a�(施眗9歐湬

�  b    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �    �8��/X昋旒�.胱#h=J"髈篒go#  �    ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  ;!    I嘛襨签.濟;剕��7啧�)煇9触�.  {!   [/?KL�5�!k浘猼*�洤	�齍/嬹軍  �!   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  �!   �儔14褥緅�3]饃鹷�hK3g搋bA竑  <"   綔)\�谑U⒊磒'�!W磼B0锶!;  �"   5睔`&N_鏃|�<�$�獖�!銸]}"  �"   E縄�7�g虩狱呂�/y蛨惏l斋�笵  ##   ┫緞A$窄�0� NG�%+�*�
!7�=b  r#   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �#   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  $    栀��綔&@�.�)�C�磍萘k  X$   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �$   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �$    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  F%   d2軇L沼vK凔J!女計j儨杹3膦���  �%   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  �%   �呾��+h7晃O枖��*谵|羓嗡捬  ,&   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  }&   蜅�萷l�/费�	廵崹
T,W�&連芿  �&   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  '   v�%啧4壽/�.A腔$矜!洎\,Jr敎  Y'   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �'   D���0�郋鬔G5啚髡J竆)俻w��  �'   2W瓓�<X	綧]�龐IE?'笼t唰��  C(   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  {(   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  �(   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  )   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  S)   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �)   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �)   �X�& 嗗�鹄-53腱mN�<杴媽1魫  /*   �F9�6K�v�/亅S诵]t婻F廤2惶I  }*   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  �*   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  +   c�#�'�縌殹龇D兺f�$x�;]糺z�  n+   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �+   �7穲碶⒖鍉鸻�:怉婤莞b=竱�  ,   ﹪冓�嗈�1蓌�)eF�;珉5� �橌5偧  I,   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �,   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �,   錵s铿勃砓b棬偡遯鮓尛�9泂惻  -   `k�"�1�^�`�d�.	*貎e挖芺
脑�  \-   $G\|R_熖泤煡4勄颧绖�?(�~�:  �-   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �-   嫎V濺蕠&kZ峠l甩u8蝬QW蹚z`k
+  @.   �2梉�7伆鼂:E囂!2Wｔ孵>羑�輡  �.   擐�0阅累~-�X澐媆P 舋gD�  �.   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  /   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  k/   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �/   f扥�,攇(�
}2�祛浧&Y�6橵�  �/   曀"�H枩U传嫘�"繹q�>窃�8  0   窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  r0   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �0   [届T藎秏1潴�藠?鄧j穊亘^a  �0   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  H1   蓏翰bsF讍E熑j]�0萛b*xs*q翻惲�  �1   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  �1   A縏 �;面褡8歸�-構�壋馵�2�-R癕  2   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  h2   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  �2   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  3   dhl12� 蒑�3L� q酺試\垉R^{i�  K3   bRè1�5捘:.z錨{娯啹}坬麺P  �3   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  �3   6��7@L�.�梗�4�檕�!Q戸�$�  04   豊+�丟uJo6粑'@棚荶v�g毩笨C  s4   (鄁盯J錭澥A��/�!c� ;b卹  �4   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �4   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  K5   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �5   o藾錚\F鄦泭|嚎醖b&惰�_槮  �5   匐衏�$=�"�3�a旬SY�
乢�骣�  6   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  _6   G髼*悭�2睆�侻皣軁舃裄樘珱)  �6   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �6   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  B7   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �7   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �7   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  18   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  x8   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �8   禿辎31�;添谞擎�.H闄(岃黜��  
9   戹�j-�99檽=�8熈讠鳖铮�  S9   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  �9   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �9   �"睱建Bi圀対隤v��cB�'窘�n  A:   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  �:   覽s鴧罪}�'v,�*!�
9E汲褑g;  �:   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  (;   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  l;   ,�<鈬獿鍢憁�g$��8`�"�  �;   � 罟)M�:J榊?纸i�6R�CS�7膧俇  <   �-考X韵歎K�fUi2�'跎Zq@锍�2aJ�  U<   8蟴B或绢溵9"C dD揭鞧Vm5TB�  �<   鹰杩@坓!)IE搒�;puY�'i憷n!  �<   Eム聂�
C�?潗'{胿D'x劵;釱�  ==   聤�苮g8鄞<aZ�%4)闪�|袉uh�  �=   0T砞獃钎藰�0逪喌I窐G(崹�  �=   �(M↙溋�
q�2,緀!蝺屦碄F觡  >   馩圣纸lMO]P桋tA荚�'羮肠曖K  X>   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �>   G�膢刉^O郀�/耦��萁n!鮋W VS  �>   存*?\��-矪q7o責覃:},p穿奵�  ?   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  b?   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �?   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �?   齛|)3h�2%籨糜/N_燿C虺r_�9仌  4@   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  q@   �0�*е彗9釗獳+U叅[4椪 P"��  珸   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�   A   +YE擋%1r+套捑@鸋MT61' p廝 飨�  AA   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �A   L�9[皫zS�6;厝�楿绷]!��t  続   �=蔑藏鄌�
艼�(YWg懀猊	*)  �A   交�,�;+愱`�3p炛秓ee td�	^,  @B   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  夿   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  訠   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  C   _臒~I��歌�0蘏嘺QU5<蝪祰S  ZC   櫕襩!)G昞�&鞿b凉�豻$峜LTQ�7�  楥   謷蘋愦	豢]麸Md�觙 G澿f1e
T奞~  鍯   D,y邥鞃黎v)�8%遾1�*8赩�婯�  -D   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  wD   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  虳   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  E   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  UE   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  滶   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  蹺   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�  (F   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  rF   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  綟   +4[(広
倬禼�溞K^洞齹誇*f�5  G   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  [G    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  橤   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  谿   V� c鯐鄥杕me綻呥EG磷扂浝W)  $H   a�傌�抣?�g]}拃洘銌刬H-髛&╟  bH   �7頔碠<晔@岙�撁k4統N絠熙鶳 �     潝(綊r�*9�6}颞7V竅\剫�8値�#  颒   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  8I   副謐�斦=犻媨铩0
龉�3曃譹5D   zI   憒峦锴摦懣苍劇o刦澬z�/s▄![�  笽   k�8.s��鉁�-[粽I*1O鲠-8H� U  鸌   �l{`G2
`0m襭皗犿鷕g貋⒌joゥ  EJ   ^憖�眜蘓�y冊日/缁ta铁6殔  揓   魯f�u覬n\��zx騖笹笾骊q*砎�,�  跩   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  -K   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  mK   v-�+鑟臻U裦@驍�0屽锯
砝簠@  ↘    萾箒�$.潆�j閖i转pf-�稃陞��  鳮   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  PL   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  峀   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  螸   �嵪=�2}Qコk捑8噣酻:JY?�`  	M   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  HM   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  怣   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  豈   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  N   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  WN   跻]�	隗[鼀hF鰘廰)鋥Le6�攬R7  燦   iu�	��&? 蔬釥�#簤Ηu-塘b@遍迭  鍺   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  3O   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  凮   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  蠴   K�:荳)a懃J�拌� ,寨吙u⑺�  P   哳訵a醴泉撬4wO�)鹁疜�)誊)�=跹�  gP   b4鵣r龋遙账y圗D轛磧﹜�$瀬Y+�7�     �3掍S\絧飦戀轝
5鍄0d
妺XE��
  關   閯�価=�<酛皾u漑O�髦jx`-�4睲�  6Q   邃2硂摜_< )D��７"�(咆e泺�  bQ   �="V�A�D熈fó 喦坭7b曉叼o1  ≦   �#i匒U0/��%鷛1,爆簡n)瞰#謺狌�  齉   r�L剟FsS鏴醼+E千I呯贄0鬬/�  HR   
罬}�(囫Ldh]僘l9-6牜I�.拾R欐佬  濺   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏  颮   鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  .S   �=A�%K鹹圛19振╯鵽C殾錦`蔣  kS   猟涔紳0箆|琋唞o{雏.临mX|珈  桽   �
bH<j峪w�/&d[荨?躹耯=�  諷   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  T   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  ZT   S\颂a毡荊v髟>P鬂{O�0I粪冪艝{  僒   �-�雧n�5L屯�:I硾�鮎访~(梱  萒   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  U   +椬恡�
	#G許�/G候Mc�蜀煟-  ZU   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  橴   妇舠幸佦郒]泙茸餈u)	�位剎  赨   鹴y�	宯N卮洗袾uG6E灊搠d�  �         �  �"  `   �    B   �    H   �    Y   �    �   
  X  U     X  �   "  h  _   $  h  r   &  h  �   z  �"  �   �  �  N   �  �&  B  �  �"    �  �"  �   �  �"    �  �"  �   �  �    �  �&  �  �  �"  �   �  �&  D
  �  �&  �    �&  0     �"  K     X  �  L  X  �  N  �&  L
  `  X  �   k  �"  �   l  �"  �   {  �&  �  }  �&  �  �  �&  s  �  �&  �  �  �&  )
  5  X  �  B  X  �   P  �"  �   Q  �"  �   W  X  �  h  X  �   u  h      �"  �     �"  �   �  �  5   �  �  5   �  �&  �	  �  P#  �    �"  �   h  �&  �  l  P#    m  P#    �  X  �  �  X  >     X  @   �  X  �     �"  �   "   �"  �   �$  �'  j  �$  �"  �   �$  �"  �   �$  �"  �   �$  �"  �   �$  �"  �   �$  �"  �   �$  �"  �   %  �"    	%  �"  �   
%  �"    %  �"  �   %  �"  �   !%  �*  2   (%  �'    5%  �"  �   H%  �    ~%  �'    �%  �'  �  �%  �'  �  �%  �'  ]  �%  �  �   �%  X  �  �%  X  �  �%  X  �  �%  X  �  �%  �"  �   �%  X  �  �%  �"    �%  X    �*  h  `   �*  h  z   �*  h  �   �*    �   +  �"  \  +  �"  �   +  �"  �   +  �"  �   +  �"  L  +  �"  �   ++  �"    -+  �"  �   .+  �"  �   0+  �*  $   1+  �*  $   2+  �"  �   3+  �"  �   D+  �    E+  �    �   "V   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\RTXPT\External\Donut\nvrhi\src\common\dxgi-format.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resourcebindingmap.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgitype.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\aftermath.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winioctl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ncrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mciapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\lzexpand.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ole2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winspool.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\prsht.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\RTXPT\External\Donut\nvrhi\src\common\versioning.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\playsoundapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12sdklayers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\RTXPT\External\nvapi\nvapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\RTXPT\External\nvapi\nvapi_lite_salstart.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\nvrhi\src\d3d12\d3d12-meshlets.cpp D:\RTXPT\External\nvapi\nvapi_lite_common.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\bcrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h D:\RTXPT\External\Donut\nvrhi\src\d3d12\d3d12-backend.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h D:\RTXPT\External\nvapi\nvapi_lite_surround.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmeapi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winscard.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsystem.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsyscom.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\nvapi\nvapi_lite_stereo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleauto.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dpapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\commdlg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\cderr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dde.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ddeml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\inaddr.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\set D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtree D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bitset C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winsmcrd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\shellapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\misc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsock.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\nb30.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\joystickapi.h D:\RTXPT\External\Donut\nvrhi\src\common\state-tracking.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winperf.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h D:\RTXPT\External\nvapi\nvapi_lite_d3dext.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dlgs.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3dcommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\queue D:\RTXPT\External\nvapi\nvapi_lite_salend.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\RTXPT\External\nvapi\nvapi_lite_sli.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h   �       Lg+  ,  ?   0  ?  
 �+      �+     
 4T      8T     
 覢 >   譆 >  
    n 摄j�#翅M弫v齴菉   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\Release\nvrhi_d3d12.pdb ����      �?                  �?                  �?    H嬃�   �   �   N G                      %        �IID_PPV_ARGS_Helper<ID3D12PipelineState>  >穐   pp  AJ                                 H     穐  Opp  O�   0              �     $       b �    e �   f �,   P   0   P  
 q   P   u   P  
 �   P   �   P  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �      /      5         �   �  k G            :      :   h        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >   _Bytes  AJ        9  $  >    _Block_size  AH       1 
   >    _Ptr_container  AJ        
 >0    _Ptr  AH  %     	  M        
  
 Z   �   N Z   �     (                      H 
 h   
         $LN14  0     O_Bytes  O   �   h           :   X  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   \   0   \  
 �   \   �   \  
 �   \   �   \  
 �   \   �   \  
   \     \  
 s  �   w  �  
 �  \   �  \  
 H嬃�   �   �   g G                      �        �std::_Atomic_address_as<long,std::_Atomic_padded<unsigned long> >  >鯸   _Source  AJ                                 H�     鯸  O_Source  O �   0              @$     $       j  �    m  �   n  �,   Z   0   Z  
 �   Z   �   Z  
 �   Z   �   Z  
 H;蕋EH塡$H塼$WH冹 H孃H嬞3�@ H�H吷t
H�3H��P怘兠H;遳錒媆$0H媡$8H兡 _�   �   =  q G            K      K   �        �std::_Destroy_range<std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >  >塉   _First  AI       &  AJ          AJ J       >隞   _Last  AK          AM       3  AK J       >驤   _Al  AP           AP       +    D@    M        �    M             M        "    CE
 >�    temp  AJ  #       AJ       +    N N N                      0H�  h   �  �  �     "    0   塉  O_First  8   隞  O_Last  @   驤  O_Al  9.       /   O   �   H           K   X     <       > �    B �   > �    C �2   B �;   F �,   Y   0   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y   �   Y  
   Y     Y  
 &  Y   *  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 9  Y   =  Y  
 T  Y   X  Y  
 H塋$SVWATAUAVAWH冹PM嬭H嬟L孂H�L嬨L+釯咙H婣H+翲柳I�������I;��  L岪L塂$(H婭H+蔋六H嬔H殃I嬃H+翲;�囷  H�
M嬸I;繪C餗;�囏  J��    H塂$0L壌$�   H=   r3H岺'H;�啴  �    H吚劕  H峱'H冩郒塅鳫壌$�   3��0H吚tH嬋�    H嬸H墑$�   3��3�嬿H壖$�   L壌$�   N�$�    I�4H塋$8H岮H塂$@H�9I;蛅I婨 H�I墋 H塋$ M婫I�H嬛I;豼)I;�剕   H�:H;裻	H�H�H�9H兟H兞I;萿怆\H;藅H�:H;裻	H�H�H�9H兟H兞H;藆釮塼$ I媁H;趖+L+鉓岲$L苀怞�H�9H;藅	H�H�H�;H兠H;趗釯�H呟tYM媤I;辴怘�H吷t
H�;H��P怘兠I;辵錓�I媁H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐wEI嬋�    I�7H婦$(H�艻塐H婰$0H蜪塐H婦$8H兡PA_A^A]A\_^[描    惕    惕    坛      �      +     a     g  A   m        �   S
  � G            r     r  %        �std::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >::_Emplace_reallocate<nvrhi::RefCountPtr<nvrhi::IResource> > 
 >桱   this  AJ          AW       U9
  D�    >隞   _Whereptr  AK           >矹   <_Val_0>  AP          AU       [C  D�    >#     _Newcapacity  AV  x     b AV /    C )  7   B�   �     ��  >    _Newsize  AH  7      AP  L     k ! �  � ` AP      >  D(    >    _Whereoff  AT  #     I  � P AT l      >隞    _Constructed_last  AJ        D@    >    _Oldsize  AH  .     =  7  >塉    _Constructed_first  D     >隞    _Newvec  AL  �          AL     jV  B�   �     �     �  M        �%  �媢侂 M        �%  �媢侂& M        B  ��(
3%乴( M        h  ��$	%)
仒
 Z      >    _Block_size  AJ  �       AJ `      >    _Ptr_container  AH  �     !  AH     i O
 >0    _Ptr  AL  �       AL     jV  M        
  ��
 Z   �   N N M        
  ��
 Z   �   N N M           
u
 N N N M        �%  Qk >    _Oldcapacity  AJ  U     �   T / �   AJ      e L >    _Geometric  AH  u       AH `      M        �%  Q N N M        �%  �" M        �%  �"C M        �%  �* N N N M        �%  $両)	 >隞    _ULast  AP  >    m  AP �    � #  L   >塉    _UFirst  AJ  k      AJ R    �  c �  � 1  >鎶   _Backout  CK     D    3 " CK    w    �  	 ' 0 z  � -  M        �%  丷 M        �%  丷 M        �%  丷C	 M        �%  乑 N N N N N M        �%  $仦	 >隞   _Last  AK  �    0  AK �    � #  4 -  >塉    _UFirst  AI       R��  AI l      M        �%  伆 M        �%  伆 M        �%  伆G	 M        �%  伡 N N N N N M        �%  $乺%	 >塉    _UFirst  AJ  A    o     AJ R    �  E b  �  � 1  >鎶   _Backout  CK     �    	  CK    w    '  	  M        �%  亀 M        �%  亀 M        �%  亀C	 M        �%  � N N N N N/ M        �%  佄	
I4#
- M        �  *�g M        `  �	)B
 Z   G  
 >   _Ptr  AJ *      >#    _Bytes  AK      -    AK l     % M          �d#
E
 Z   �   >    _Ptr_container  AP        AP *    G  =  >    _Back_shift  AJ  �    ,  AJ *    G  =  N N N M        �  佢	 >塉   _First  AI  �    �  AI l      >隞   _Last  AV  �    U  AV /    C )   M        �  佮 M           佮 M        "   佮CE
 >�    temp  AJ  �      AJ �        N N N N N
 Z   �%   P           8         0@ � h!   e  
      `  B  h  �  �  �     �  �  �     "   '%  }%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%         $LN237  �   桱  Othis  �   隞  O_Whereptr  �   矹  O<_Val_0>  @   隞  O_Constructed_last      塉  O_Constructed_first  9�      /   O �   �           r  �'     �       * �   3 �*   4 �5   6 �H   : �Q   ; �u   = �   > �"  B �5  C �:  E �I  G �p  K �r  L ��  M ��  N ��  M ��  N ��  V �K  W �P  X �`  = �f  7 �l  V ��   �  � F            F      F             �`std::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >::_Emplace_reallocate<nvrhi::RefCountPtr<nvrhi::IResource> >'::`1'::catch$11 
 >桱   this  EN  �         F  Z   �  �   (                    �        __catch$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z$0        $LN237  �   桱  Nthis  �   隞  N_Whereptr  �   矹  N<_Val_0>  @   隞  N_Constructed_last      塉  N_Constructed_first  O�   8           F   �'     ,       P �   Q �"   R �<   S �,   [   0   [  
 �   [   �   [  
 �   [     [  
 /  [   3  [  
 R  [   V  [  
 b  [   f  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
   [     [  
 <  [   @  [  
 T  [   X  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
   [     [  
 "  [   &  [  
 �  [   �  [  
 �  [   �  [  
   [   #  [  
 /  [   3  [  
 R  [   V  [  
 b  [   f  [  
 !  [   %  [  
 =  [   A  [  
 f  [   j  [  
 v  [   z  [  
 (  [   ,  [  
 8  [   <  [  
 b  [   f  [  
 r  [   v  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �  [   �  [  
 �	  [   �	  [  
 
  [   
  [  
 ,
  [   0
  [  
 �
  [   �
  [  
 �
  [   �
  [  
 �
  [   �
  [  
 �
  [   �
  [  
 #  [   '  [  
 3  [   7  [  
 S  [   W  [  
 c  [   g  [  
 �  [   �  [  
 �  [   �  [  
 �  �   �  �  
 O
  [   S
  [  
 h
  [   l
  [  
 h  ]   l  ]  
 @  ]   D  ]  
 z  �   ~  �  
 �  �   �  �  
 $  ]   (  ]  
 H塗$SUH冹(H嬯L媴�   H婾@H婱 �    L媴�   H嫊�   H媿�   �    3�3设    �   Y   8   @   A   j   L嬔L嬄H婭(H;J(t�����3绤蓆3A�   D嬞L+��     K�M岪A嬔蠭;H�D蠥蚜嬄I冸u崦   �   �  � G            P       O   !%        �nvrhi::arrayDifferenceMask<nvrhi::static_vector<nvrhi::IBindingSet *,5>,nvrhi::static_vector<nvrhi::IBindingSet *,5> > 
 >:Q   a  AJ          AR       %  AR O      
 >:Q   b  AK          AP       *  AP 0         
 >u     mask  A          A  0         
 >u     i  A   I       A   =       A  0       
                          H  h   <%  =%  H%      :Q  Oa     :Q  Ob  O �   P           P   �*     D       2  �   6  �   7  �   A  �   9  �   :  �O   A  �,   W   0   W  
 �   W   �   W  
 �   W   �   W  
 �   W   �   W  
 �   W   �   W  
 
  W     W  
   W     W  
 =  W   A  W  
 M  W   Q  W  
 m  W   q  W  
 }  W   �  W  
 �  W   �  W  
 �  W   �  W  
 L嫅   L嬌L;�   uDE3繣呉t9H岯L+蕥H鳤9L鴘+�B9u#婬麬9L黸婬A9LuA�繦兝E;聄�2烂��   �   �  � G            Z       Y   1+        �nvrhi::arraysAreDifferent<nvrhi::static_vector<nvrhi::Rect,16>,nvrhi::static_vector<nvrhi::Rect,16> > 
 >脵   a  AJ        
  AQ  
       AQ T      
 >脵   b  AK        Z 
 >u     i  Ah       D  M        �*  &" M        &  &" N N                        H  h   &  �*  =+  >+  D+      脵  Oa     脵  Ob  O �   `           Z   �*  	   T       $  �    %  �   (  �"   *  �H   (  �T   .  �V   /  �W   +  �Y   /  �,   V   0   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
   V   	  V  
 �  V   �  V  
 L媺�  L;妧  uqE3繣吷tfH岯H+蕫�D�.@鴝UuS�. zIuG�D.@z;u9�D�.@鼁-u+�D.@zu�D.@zuA�繦兝E;羠�2烂��   �   �  � G            �       �   0+        �nvrhi::arraysAreDifferent<nvrhi::static_vector<nvrhi::Viewport,16>,nvrhi::static_vector<nvrhi::Viewport,16> > 
 >綑   a  AJ          AJ ~      
 >綑   b  AK        � 
 >u     i  Ah       q  M        �*  R  M        $  R  N N                        H  h   $  �*  ?+  @+  E+      綑  Oa     綑  Ob  O �   `           �   �*  	   T       $  �    %  �   (  �    *  �r   (  �~   .  ��   /  ��   +  ��   /  �,   U   0   U  
 �   U   �   U  
 �   U   �   U  
 �   U   �   U  
 �   U     U  
 �  U   �  U  
 H嬃�   �   �   l G                      /+        �nvrhi::checked_cast<nvrhi::d3d12::Framebuffer *,nvrhi::IFramebuffer *> 
 >R    u  AJ                                 H     R   Ou  O�   0              �*     $       L  �    T  �   V  �,   T   0   T  
 �   T   �   T  
 �   T   �   T  
 H嬃�   �   �   t G                      #'        �nvrhi::checked_cast<nvrhi::d3d12::MeshletPipeline *,nvrhi::IMeshletPipeline *> 
 >a!   u  AJ                                 H     a!  Ou  O�   0              �*     $       L  �    T  �   V  �,   S   0   S  
 �   S   �   S  
 �   S   �   S  
 H嬃�   �   �   w G                      ,+        �nvrhi::checked_cast<nvrhi::d3d12::RootSignature *,nvrhi::d3d12::IRootSignature *> 
 >H   u  AJ                                 H     H  Ou  O �   0              �*     $       L  �    T  �   V  �,   R   0   R  
 �   R   �   R  
 �   R   �   R  
 H�    H嬃�   �   �   s G                   
   H        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >�   this  AJ                                 @�     �  Othis  O   �   0              �"     $       �  �    �  �   �  �,   %   0   %  
 �   %   �   %  
 �   %   �   %  
 � 3狼AW狼A W汕A茿 茿茿 茿茿  茿$茿( 茿,茿0 茿4茿8 茿<茿@ H堿\H堿d茿D �� 茿H 茿L艫P茿T    f茿X  艫Z AlI|墎�   H嬃�   �   �   E G            �       �   �*        �nvrhi::RenderState::RenderState 
 >�   this  AJ        �                         @ " h   �  I&  �*  �*  �*  �*  �*      �  Othis  O ,   #   0   #  
 j   #   n   #  
 � H嬃茿�   �   �   S G                      �*        �nvrhi::BlendState::RenderTarget::RenderTarget 
 >0   this  AJ                                 H�     0  Othis  O   ,   !   0   !  
 x   !   |   !  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�      %   
   ,         �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >2   this  AI  	     2  AJ        	  >7   __that  AH         AK          M        �  :$
 Z   `   N                       H� 
 h   �   0   2  Othis  8   7  O__that  O ,      0     
 d      h     
 t      x     
 �      �     
 �      �     
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�      %   
   ,         �   =  U G            <      6           �std::bad_array_new_length::bad_array_new_length 
 >I   this  AI  	     2  AJ        	  >N   __that  AH         AK          M        �  :$
 Z   `   N                       @�  h   �     0   I  Othis  8   N  O__that  O   ,      0     
 z      ~     
 �      �     
 �      �     
 �      �     
 H�    H茿    H堿H�    H�H嬃�               �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >I   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      I  Othis  O   �   8           !        ,       �  �    �  �   �  �   �  �,      0     
 z      ~     
          
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�      %   
      �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   `                         H�  0   �  Othis  8   �  O_Other  O �   0           2        $       H  �   I  �)   J  �,      0     
 d      h     
 t      x     
 �      �     
 �      �     
          
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �$        �nvrhi::RefCountPtr<ID3D12PipelineState>::~RefCountPtr<ID3D12PipelineState> 
 >焗   this  AH         AJ          AH        M        	%  GCE
 >    temp  AJ  
       AJ        N (                     0H� 
 h   	%   0   焗  Othis  9       B+   O�   0           "   �"     $       �  �   �  �   �  �,   4   0   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 �   4     4  
 L  4   P  4  
 d  4   h  4  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         z        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >�   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �  Othis  9       /   O�   0           "   �"     $       �  �   �  �   �  �,   &   0   &  
 �   &   �   &  
 �   &   �   &  
 �   &   �   &  
 �   &   �   &  
   &     &  
 P  &   T  &  
 h  &   l  &  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   X  x G            "         +        �nvrhi::RefCountPtr<nvrhi::IMeshletPipeline>::~RefCountPtr<nvrhi::IMeshletPipeline> 
 >}�   this  AH         AJ          AH        M        +  GCE
 >a!    temp  AJ  
       AJ        N (                     0@� 
 h   +   0   }�  Othis  9       /   O�   0           "   �"     $       �  �   �  �   �  �,   Q   0   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 �   Q   �   Q  
   Q     Q  
 T  Q   X  Q  
 l  Q   p  Q  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   J  j G            "                  �nvrhi::RefCountPtr<nvrhi::IResource>::~RefCountPtr<nvrhi::IResource> 
 >隞   this  AH         AJ          AH        M        "   GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   "    0   隞  Othis  9       /   O  �   0           "   �"     $       �  �   �  �   �  �,   X   0   X  
 �   X   �   X  
 �   X   �   X  
 �   X   �   X  
 �   X   �   X  
 �   X   �   X  
 F  X   J  X  
 `  X   d  X  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >3   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   3  Othis  9       /   O  �   0           "   �"     $       �  �   �  �   �  �,   $   0   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 B  $   F  $  
 \  $   `  $  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   `  � G            "                 �nvrhi::RefCountPtr<nvrhi::d3d12::RootSignature>::~RefCountPtr<nvrhi::d3d12::RootSignature> 
 >鶰   this  AH         AJ          AH        M          GCE
 >璈    temp  AJ  
       AJ        N (                     0H� 
 h      0   鶰  Othis  9       H   O�   0           "   �"     $       �  �   �  �   �  �,   3   0   3  
 �   3   �   3  
 �   3   �   3  
 �   3   �   3  
 �   3     3  
   3     3  
 \  3   `  3  
 t  3   x  3  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;      Y         �   �  � G            ^      ^   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  
     Q J   AJ        
 ) M        �  ,(
	 M        �   N M        �  ,E M          &? M        `  )
 Z   G  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M          
"#
!
 Z   �   >    _Ptr_container  AP  &     7    AP :       >    _Back_shift  AJ  -     0 
   N N N N N                       H� 6 h       �  �  �  �  �  �      X  `         $LN33  0   �  Othis  O�   H           ^   �&     <       B �   C �
   B �
   C �R   J �X   C �,       0      
 �       �      
 �       �      
 �      �     
 �      �     
 ,      0     
 @      D     
 f      j     
 �  z   �  z  
            
 �     �   �   B G                       =        �nvrhi::IResource::~IResource 
 >"   this  AJ          D                           H�     "  Othis  O�                  �"            i  �,      0     
 g      k     
 �      �     
 @SH冹 H嬞H伭�   L�
    �   D岯    怘婯H吷tH荂    H��P怘婯H吷tH荂    H��P怘婯H吷tH荂    H��P怘兡 [�   &   !         �   ?  V G            t      n    +        �nvrhi::MeshletPipelineDesc::~MeshletPipelineDesc 
 >    this  AI  	     j  AJ        	  M        �  V M        �  VDE
 >�    temp  AJ  Z       AJ n       N N M        �  > M        �  >DE
 >�    temp  AJ  B       AJ V       N N M        �  & M        �  &DE
 >�    temp  AJ  *       AJ >       N N                      0@�  h   �  �  �  �   0      Othis  9:       /   9R       /   9j       /   O ,   '   0   '  
 {   '      '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 E  '   I  '  
 U  '   Y  '  
 �  '   �  '  
 �  '   �  '  
   '     '  
 +  '   /  '  
 ;  '   ?  '  
 H�    H�H兞�                   �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >I   this  AJ          M        �   	
 N                        H�  h   �  �      I  Othis  O ,      0     
 {           
 H�    H�H兞�                   �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   2                          H�     �  Othis  O  �   (                          Y  �
   Z  �,      0     
 e      i     
 �      �     
 H塡$H塴$H塼$WAVAWH冹 L嬺H嬹��H媄H9Yt#H呟t
H�H嬎�P怘婲H塣H吷tH��P怚媈H9^t#H呟t
H�H嬎�P怘婲H塣H吷tH��P怚媈H9^t#H呟t
H�H嬎�P怘婲H塣H吷tH��P怉F F AN0N0AF@F@ANPNPAF`F`ANpNpA唨   唨   A帎   帎   A啝   啝   H嵕�   M孇L+   怞�?H9t!H呟t
H�H嬎�P怘�H�H吷tH��P怘兦H冺u蘄媶�   H墕�   H嬈H媆$@H媗$HH媡$PH兡 A_A^_�   �   �  K G            ~     e  �*        �nvrhi::MeshletPipelineDesc::operator= 
 >    this  AJ          AL       V >    __that  AK          AV       a M        �$  � 	 M        z  �> M        �  �>
 >�    temp  AI  $    F  AI        N N M        �  �8 N M        P  �) M        k  �)#	 N N N M        �$  }
 M        �  �� M        �  ��
 >�    temp  AJ  �       AJ �     � �  �   N N M        �  �� >�    tmp  AI  �     �  AI        N M        Q  �� M        l  ��#	 N N N M        �$  P
 M        �  q M        �  q
 >�    temp  AJ  m       AJ }         �  �   N N M        �  i >�    tmp  AI  T     -  N M        Q  Z M        l  Z#	 N N N M        �$  #
 M        �  D M        �  D
 >�    temp  AJ  @      & AJ P     .    B  J  �  �   N N M        �  < >�    tmp  AI  '     -  N M        Q  - M        l  -#	 N N N                      0@� > h   z  �  �  �  �  �  k  l  P  Q  �$  �$  �$  �$   @      Othis  H      O__that  98       /   9L       /   9e       /   9y       /   9�       /   9�       /   94      /   9F      /   O,   (   0   (  
 p   (   t   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
   (   !  (  
 -  (   1  (  
 �  (   �  (  
 
  (     (  
 R  (   V  (  
 b  (   f  (  
   (     (  
   (     (  
 j  (   n  (  
   (     (  
 !  (   %  (  
 x  (   |  (  
 H  (   L  (  
 X  (   \  (  
 h  (   l  (  
 x  (   |  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�         �   �   n G            !         �!        �nvrhi::RefCounter<nvrhi::IMeshletPipeline>::`scalar deleting destructor' 
 >腎   this  AI  	       AJ        	                        @� 
 h   �*   0   腎  Othis  O,   7   0   7  
 �   7   �   7  
 �   7   �   7  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�         �   �   [ G            !         �*        �nvrhi::IMeshletPipeline::`scalar deleting destructor' 
 >t!   this  AI  	       AJ        	                        @� 
 h   �*   0   t!  Othis  O   ,   )   0   )  
 �   )   �   )  
 �   )   �   )  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�         �   �   T G            !         :        �nvrhi::IResource::`scalar deleting destructor' 
 >"   this  AI  	       AJ        	                        @� 
 h   =   0   "  Othis  O  ,   
   0   
  
 y   
   }   
  
 �   
   �   
  
 H塡$WH冹 孃H嬞H媺  H吷tH莾      H��P怘媼  H吷tH莾      H��P怘岾�    @銮t
喊  H嬎�    H嬅H媆$0H兡 _肞   '   c         �   �  a G            u   
   j   �*        �nvrhi::d3d12::MeshletPipeline::`scalar deleting destructor' 
 >螴   this  AI       `  AJ          M          - M          -GE
 >璈    temp  AJ  4       AJ K       N N M        �$   M        	%  OGE
 >    temp  AJ         AJ -       N N                      0@�  h       �$  	%  �*  �*   0   螴  Othis  9)       B+   9G       H   O ,   =   0   =  
 �   =   �   =  
 �   =   �   =  
 �   =   �   =  
 �   =   �   =  
 N  =   R  =  
 ^  =   b  =  
 �  =   �  =  
 �  =   �  =  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
            0         �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >2   this  AJ          AM       -  M        �  

	
 Z   2   N                       @�  h   �  �   0   2  Othis  O ,      0     
 w      {     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
            0         �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >I   this  AJ          AM       -  M        �  

	
 Z   2   N                       @�  h   �  �  �   0   I  Othis  O  ,      0     
 �      �     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
            0         �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   2   N                       @� 
 h   �   0   �  Othis  O ,      0     
 w      {     
 �      �     
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >U   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   U  O__f  9(       U   O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 �   �罙�烂   �   �   X G            
          +        �nvrhi::RefCounter<nvrhi::IMeshletPipeline>::AddRef 
 >腎   this  AJ        
  M        m    N                        @  h   m  �      腎  Othis  O �   0           
   �"     $       x �    y �   z �,   5   0   5  
 }   5   �   5  
 �   5   �   5  
 @SH冹 �����罽冸uH吷t	L�峉A�嬅H兡 [�   �     Y G            +      %   +        �nvrhi::RefCounter<nvrhi::IMeshletPipeline>::Release 
 >腎   this  AJ        #  AJ #       >"     result  A          M        l  
 N                       @  h   l  �   0   腎  Othis  9        蒊   O   �   @           +   �"     4       } �   ~ �    �   � �#   � �,   6   0   6  
 ~   6   �   6  
 �   6   �   6  
 �   6   �   6  
   6     6  
 0  6   4  6  
 H冹HH峀$ �    H�    H峀$ �    �
         "      j      �   �   F G                               坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �$            J �   K �,      0     
 �   x   �   x  
 �      �     
 H冹(H�
    �    �   T            �   �   � G                     �%        坰td::vector<nvrhi::RefCountPtr<nvrhi::IResource>,std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> > >::_Xlength 
 Z   �   (                      @        $LN3  O�   (              �'            a �   b �,   A   0   A  
 �   �   �   �  
 �   A   �   A  
 H塡$H塼$WH冹 H媮�  H嬺H媥E劺tH嫆  H嬒H�H婻p�愷   H�H嬒H嫋  �惾   H�3�禢H嫎�   �    嬓H嬒�計�   呉tH�L崋$  H嬒�惃   兙�   tH�L崋�  嫋   H嬒�惏   H媆$0H媡$8H兡 _胅   .      �   �  T G            �      �   �*        �nvrhi::d3d12::CommandList::bindMeshletPipeline 
 >銹   this  AJ        ,  AJ 9       >QQ   pso  AK          AL       �  >0    updateRootSignature  AX        9  AX 9       >諵    commandList  AM       � 
 Z   �                         H  h   �  z  �$  �$  �$   0   銹  Othis  8   QQ  Opso   @   0   OupdateRootSignature  93       軶   9F          9f       肣   9       萉   9�       蚎   O�   p           �        d       �  �   �  �   �  �"   �  �9   �  �L   �  �h   �  �r   �  ��   �  ��   �  ��   �  �,   I   0   I  
 y   I   }   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
   I     I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
   I     I  
 H塡$UVWAVAWH冹PM嬹M孁H嬺3韷l$0M吚劺  M吷劮  拱  �    H孁H墑$�   H吚勸   3褹赴  H嬋�    荊   H�    H�H峗H塡$8�H塳H塳H塳H岾 �    H伱�   H塡$@W�3�CH塁 H�    H塂$ L�
    峌D岴H嬎�    怘塳(3繦墖�   H壇�   垏   H菄     H壇  H壇  壇   H崗$  3褹竴  �    壇�  H崗�  3褹�   �    @埊�  �H孆H峅H嫓$�   H嬘�    H媱$�    囸   H�   L9�  t$I�I嬒�P怘嫃  L壙  H吷tH��P怢9�  t$I�I嬑�P怘嫃  L壏  H吷tH��P怘岾 嫍�   �    垏�  H�>�H�*H嬈H嫓$�   H兡PA_A^_^]�7      [   o   i   H   �   #   �   &   �   %   �         o   3  o   S  (   �  "      �   �  ` G                 �  �*        �nvrhi::d3d12::Device::createHandleForNativeMeshletPipeline 
 >漅   this  AJ        �6 � AJ �      D�    >H   rootSignature  AP          AW       � >   pipelineState  AQ          AV       �
 >    desc  AI  O    �  EO  (           D�    >�   framebufferInfo  EO  0           D�    >QQ    pso  AM  >     � AM �      B�   F     ��  M        +  併 M        +  併 N N M        +  仭		
 M        �$  伮 M        	%  伮
 >    temp  AJ  �      AJ �      N N M        %  伌 N M        .+  	仾 M        %  	仾 N N N M        +  乼		
 M          仌 M          仌
 >璈    temp  AJ  �      AJ �    1    N N M        
%  亣 N M        -+  	亇 M        %  	亇 N N N M        �$  �� N M          �� N M        u  ��	 M        �  ��	 N N M        �  
��0
 >�   this  AI  �       B@   �     e�  K  N M        �$  �� N M        �$  �� N M        �$  | N M        �  _ N M        +  佽 N Z   �  �*  \+   P           (         @ � h#   �  1  2  3  4  u      �  �  �  �    �$  �$  �$  %  	%  
%  %  %  �*  �*  �*  �*  �*  +  +  +  +  +  +  ,+  -+  .+   �   漅  Othis  �   H  OrootSignature  �     OpipelineState  �      Odesc  �   �  OframebufferInfo  ^6      繧   9�      H   9�      H   9�      B+   9�      B+   O�   x                  l       �  �   �  �(   �  �1   �  �C  �  �W  �  �t  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��   �   o F                                �`nvrhi::d3d12::Device::createHandleForNativeMeshletPipeline'::`1'::dtor$1 
 >漅   this  EN  �                                 �  O  �   �   o F                                �`nvrhi::d3d12::Device::createHandleForNativeMeshletPipeline'::`1'::dtor$7 
 >漅   this  EN  �                                  �  O  �   �   o F                                �`nvrhi::d3d12::Device::createHandleForNativeMeshletPipeline'::`1'::dtor$8 
 >漅   this  EN  �                                  �  O  �   �   o F                                �`nvrhi::d3d12::Device::createHandleForNativeMeshletPipeline'::`1'::dtor$9 
 >漅   this  EN  �                                  �  O  �   �   p F            )      #             �`nvrhi::d3d12::Device::createHandleForNativeMeshletPipeline'::`1'::dtor$19 
 >漅   this  EN  �         #                        �  O ,   M   0   M  
 �   M   �   M  
 �   M   �   M  
 �   M   �   M  
 �   M   �   M  
   M     M  
   M     M  
 0  M   4  M  
 D  M   H  M  
 z  M   ~  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 d  M   h  M  
 t  M   x  M  
 >  M   B  M  
 N  M   R  M  
 \  M   `  M  
 l  M   p  M  
 D  M   H  M  
 T  M   X  M  
 d  M   h  M  
 t  M   x  M  
 �  M   �  M  
 �  M   �  M  
 <  b   @  b  
 �  b   �  b  
 �  g      g  
 h  g   l  g  
 �  h   �  h  
 (	  h   ,	  h  
 |	  i   �	  i  
 �	  i   �	  i  
 <
  a   @
  a  
 �
  a   �
  a  
 @UH冹 H嬯喊  H媿�   �    H兡 ]�      H媻8   H兞�       $   H媻8   H兞�       $   H媻8   H兞�       $   @UH冹 H嬯婨0冟吚t僥0鼿媿�   �    H兡 ]�   Q   H塡$H塗$UVWH冹@I嬞I孁H嬯H嬹I伬�   E3蒆峊$h�    怘�H嬎�P(H塂$ L婰$hL嬊H峊$pH嬑�    怘�H嬎�P(H塂$(H墊$ H媆$pL嬎L婦$hH嬚H嬑�    怘呟t
H�H嬎�P怘婰$hH吷tH荄$h    H��P怘嬇H媆$`H兡@_^]�-   N   Q   O   }   M      �   �  Q G            �      �   �*        �nvrhi::d3d12::Device::createMeshletPipeline 
 >漅   this  AJ          AL       � 
 >    desc  AM       �  AP          >R    fb  AI       Z  AQ         
 >胔    pPSO  AI  n     E  Bp   U     f  >N    pRS  Bh   1     �  M          �� M          ��HB	
 >璈    temp  AJ  �       AJ �       N N M        �$  �� M        	%  ��	 N N Z   �%  �*  �*   @                    @  h       �$  �$  �$  	%   `   漅  Othis  p      Odesc  x   R   Ofb  p   胔  OpPSO  h   N  OpRS  98       |    9\       |    9�       B+   9�       H   O�   @           �        4       �  �   �  �2   �  �V   �  ��   �  ��   �   ` F                                �`nvrhi::d3d12::Device::createMeshletPipeline'::`1'::dtor$0                         �  O�   �   ` F                                �`nvrhi::d3d12::Device::createMeshletPipeline'::`1'::dtor$1                         �  O,   L   0   L  
 v   L   z   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
   L     L  
   L     L  
 /  L   3  L  
 �  L   �  L  
 �  L   �  L  
 �  L   �  L  
 �  L   �  L  
 �  L   �  L  
 �  L   �  L  
 �  L   �  L  
 <  ^   @  ^  
 �  c   �  c  
 H崐h   �       3   H崐p   �       4   @USVWATAUAVAWH崿$(��H侅�  H�    H3腍墔�  I嬞M孁L嬺L嬮H塗$0H嫷@  E3銵塪$ 3褹竊  H峀$@�    荄$P   荄$X   荄$p   荅�   荅�
   荅�   荅   菂X     菂h  	   菂p     菂�     H婥pH塂$HI峅 H峌�    H峌訧峅a�    E8gauE8gdt�~ uD塭訢塭郔峅tH峌よ    A�吷凬  冮�;  冮�(  冮�  凒t&�    M�&H婰$ H吷�  L塪$ H��P轱  W�厾  L墺�  H菂�     �0   �    H嬓H墔�  H菂�  +   H菂�  /        
   H�    �@ �(   f塀(�*   圔*艬+ H崟�  I峂�    怘嫊�  H凓v4H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘙  �    M�&H婰$ H吷�  L塪$ H��P辂  荄$T   �荄$T   �荄$T   婩墔\  婩墔`  菂l  ����A孅婩吚t! 嬤�3�    婸墧漷  �菋F;鴕鈮厰  禢�    婬墠�  I婳H吷tH�L岲$hH峊$`�P(I婳H吷tH�L岴�H峊$x�P(I婳H吷tH�L岴楬峌�P(H岲$@H塂$8H荄$0`  I婱H�L峀$ L�    H峊$0�恱  吚壪   W�厾  L墺�  H菂�     笯   �    H墔�  H菂�  0   H菂�  ?        
   H    @ 艪0 H崟�  I峂�    怘嫊�  H凓v0H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐wu�    M�&H婰$ H吷t=L塪$ H��P�0M�&H岲$ L;餿
H婦$ I�I嬏�H婰$ H吷tL塪$ H��R怚嬈H媿�  H3惕    H伳�  A_A^A]A\_^[]描    愯    �   �   [   o   �   /   �   0     1   2  ,   y     �  N   �  N   �  N   �  N   �  N   �  2   &     �  -   �  -   0  K   i     �  Q   �  Q   �  Q   �  2   �     T  m   m     s        �   �  O G            x  -   x  �*        �nvrhi::d3d12::Device::createPipelineState 
 >S   this  AJ        9  AU  9     ?,  >    state  AP        3  AW  3     E.  >璈   pRS  AI  0     H`� AQ        0  AI �    �  �  >�   fbinfo  AL  E     3$  EO  (           D@   >讍    streamDesc  B0   �     � >胔    pipelineState  D     >     hr  A   ?    � . �  >�    rasterState  AJ  �     	  >y�    psoDesc  D@   
 >u     i  A   �    ��&  A  F    "  M        �$  H N M        �$  �9 M        	%  �9HF
 >    temp  AJ  >      AJ F    
  N N M        �$  �6 N M        �$  �- M        	%  �-HF
 >    temp  AJ  2      AJ F    
  N N M        �$  �* N M        �  A侀倝 M        �  侀4
倈 M        �  4侖倈 M          1侚倅  M        `  �)侻
 Z   G  
 >   _Ptr  AH        AJ         AH %      >#    _Bytes  AK  �    ~1 H M          �d俒
 Z   �   >    _Ptr_container  AH        AJ        N N N N N N M        �  乑p$ M        N  P乭K-G+
7 M        �  
乻 >p    _Fancy_ptr  AK  �    _  M        5  
乻 M        W  
乻 M        B  
乻 M        
  
乻
 Z   �   N N N N N M           7仢 N N M        L  乑 M        }  乑�� M        �  乑 N N N N M        �  倫 M        �  
倫 N N M        �$  �5 M        	%  �5CB
 >    temp  AJ  .        AJ F    
  N N M        �$  �CJ M        %  �# N N M        �$  � M        	%  �HB
 >    temp  AJ        AJ F    
  N N M        �$  凕 N M        �  =兛�� M        �  兛0
�� M        �  0兲�� M          -兿�� M        `  冑)u
 Z   G  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    � - p  M          冣d

 Z   �   >    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  僇V$ M        N  P僗K*G+
  M        �  
僣 >p    _Fancy_ptr  AH  m    Q  M        5  
僣 M        W  
僣 M        B  
僣 M        
  
僣
 Z   �   N N N N N M            儕 N N M        L  僇 M        }  僇�� M        �  僇 N N N N& Z   a+  d+  c+  �!  �%  m  m  �%   �          @         A � h4   
          �  �  �  �  �  �  �  �  �  �          L  N  X  [  `  {  |  }  ~  �  �  �  �    5  B  W  h  o  �  h  �$  �$  �$  �$  �$  �$  %  	%  %  	+  
+  +  
 :�  O        $LN250     S  Othis  0     Ostate  8  璈  OpRS  @  �  Ofbinfo B y�  nvrhi::d3d12::Device::createPipelineState::__l2::PSO_STREAM  0   讍  OstreamDesc      胔  OpipelineState  @   y�  OpsoDesc  9O      B+   9C      B+   9�      ]   9�      ]   9      ]   99      陜   9      B+   9B      B+   O   �   �          x    8   �      ,  �H   -  �M   A  �_   E  �g   F  �o   G  �w   H  �~   I  ��   J  ��   K  ��   L  ��   M  ��   N  ��   O  ��   Q  ��   S  ��   V  ��   X  ��   Z  ��   [  ��   ^  ��   _  �  a  �1  q  �6  r  �Z  n  �*  o  �K  k  �S  l  �U  g  �]  h  �_  d  �g  u  �p  v  �y  w  ��  y  ��  {  ��  y  ��  }  ��    ��  �  ��  �  ��  �  ��  �  ��  �  �   �  �  �  �  �  �!  �  �?  �  �J  �  ��  �  �  �  �I  �  �l  �  �r  n  ��   �   ^ F                                �`nvrhi::d3d12::Device::createPipelineState'::`1'::dtor$0  >胔    pipelineState  EN              >y�    psoDesc  EN  @                                  �  O�   �   ^ F                                �`nvrhi::d3d12::Device::createPipelineState'::`1'::dtor$1  >胔    pipelineState  EN              >y�    psoDesc  EN  @                                  �  O�   �   ^ F                                �`nvrhi::d3d12::Device::createPipelineState'::`1'::dtor$3  >胔    pipelineState  EN              >y�    psoDesc  EN  @                                  �  O,   O   0   O  
 t   O   x   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O     O  
 '  O   +  O  
 ?  O   C  O  
 l  O   p  O  
 �  O   �  O  
 �  O   �  O  
 	  O   
  O  
   O   !  O  
 �  O   �  O  
 �  O   �  O  
   O     O  
   O   "  O  
 �  O   �  O  
 	  O   
  O  
   O     O  
 :  O   >  O  
 �  O   �  O  
 �  O   �  O  
 4  O   8  O  
 �  O   �  O  
 �  O   �  O  
 Q  O   U  O  
 a  O   e  O  
 ;  O   ?  O  
 K  O   O  O  
 [  O   _  O  
 |  O   �  O  
 �  O   �  O  
 �  O   �  O  
 u	  O   y	  O  
 �  �   �  �  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 
  O   
  O  
   _     _  
 p  _   t  _  
 �  _   �  _  
 �  d   �  d  
 L  d   P  d  
 r  d   v  d  
 �  f   �  f  
 (  f   ,  f  
 N  f   R  f  
 H崐    �       4   H崐�  �           H崐�  �           H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8      =         �   k  g G            B      B   �        �std::allocator<nvrhi::RefCountPtr<nvrhi::IResource> >::deallocate 
 >黅   this  AJ          AJ 0       D0   
 >隞   _Ptr  AK          >   _Count  AP        A   M        `  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M          
#

 Z   �   >    _Ptr_container  AJ       %    AJ 0       >    _Back_shift  AH          AH 0       N N (                      H  h     `         $LN18  0   黅  Othis  8   隞  O_Ptr  @     O_Count  O �   8           B   X     ,       � �   � �3   � �7   � �,   @   0   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 %  @   )  @  
 F  @   J  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 �  @   �  @  
 +  �   /  �  
 �  @   �  @  
 H塡$H塴$H塼$WH冹 A孂A嬸嬯H嬞�    H媰�  D嬒D嬈嬚H婬H�H媆$0H媗$8H媡$@H兡 _H�爔      G      �   �  M G            U      :   �(        �nvrhi::d3d12::CommandList::dispatchMesh 
 >   this  AI          AJ          >u    groupsX  A           A        (  >u    groupsY  A        /  Ah          >u    groupsZ  A        7  Ai         
 Z   )                         @  h   z  +   0     Othis  8   u   OgroupsX  @   u   OgroupsY  H   u   OgroupsZ  9N       E�   O �   @           U        4       * �   + �$   - �:   . �N   - �,   D   0   D  
 r   D   v   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
   D     D  
   D     D  
 �  D   �  D  
 �  D   �  D  
 H岮�   �   �   L G                      �*        �nvrhi::d3d12::MeshletPipeline::getDesc 
 >翴   this  AJ                                 @     翴  Othis  O  �                  �            W �,   :   0   :  
 q   :   u   :  
 �   :   �   :  
 H崄�   �   �   �   W G                      �*        �nvrhi::d3d12::MeshletPipeline::getFramebufferInfo 
 >翴   this  AJ                                 @     翴  Othis  O   �                  �            X �,   ;   0   ;  
 |   ;   �   ;  
 �   ;   �   ;  
 H�    H嬄�   �     G G                   
   �        �nvrhi::IResource::getNativeObject 
 >"   this  AJ          D    >u    objectType  Ah          D    M        �    N                        @ 
 h   �      "  Othis     u   OobjectType  O  �                  �"            q  �,   	   0   	  
 l   	   p   	  
 �   	   �   	  
 $  	   (  	  
 @SH冹 H嬟A佽	  t)A凐tH�    H嬄H兡 [肏媮  H�H嬅H兡 [肏媺  A�	  H��PH嬅H兡 [�   �   :  T G            W      Q   �*        �nvrhi::d3d12::MeshletPipeline::getNativeObject 
 >螴   this  AJ        B  >u    objectType  Ah          M        �   N M        �  
( N                       @  h   �  �$  �$   0   螴  Othis  @   u   OobjectType  9K       H   O  �   X           W        L         �	      �   '  �   )  �(   %  �2   )  �;   #  �N   )  �,   <   0   <  
 y   <   }   <  
 �   <   �   <  
 6  <   :  <  
 P  <   T  <  
 H塡$UVWATAUAVAWH侅�  H�    H3腍墑$p  H孃H嬹H�H塡$8H婤H塂$P�    D毒
  E�tH婫H9嗮  uE2潆A�E�t#H崕�  H�H呉tH媰  H9�  u2垭H崕�  �E�t
H�H9uE2鲭A�E�tH媷�  H9喰	  uE2黼A�E�凜  H峎H崕�  H嫨�  H;獉  �$  E3绤�劌   H岯H+�@ �     �D�.@�婗   咅   �. 娾   呠   �D.@娞   吰   �D�.@�姸   叞   �D.@姞   厷   �D.@妸   厔   A�繦兝D;�俷���H崌�  H崠�  L嫼   L;�   uNE3繣�t6H岺H+袗婣�9D
鴘5�9
u.婣�9D
黸%婣9D
uA�繦兞E;莚�2缊D$2D毒
  �D毒
  �圖$2E�tW�唸	  .嚑  zFuD�唽	  .嚖  z3u1�啇	  .嚚  z u�啍	  .嚞  z
u艱$0 圖$2�艱$0H婦$8�xx t	秶�  �禓w圖$1E�t8啒	  u	艱$3 3黼3砥D$3E�t勠t����H嬑�    劺t����險呿uQL崌�  L崕�	  I婣(H;囙  t�����/3韰纓)�   M+菵嬓f怟�嬍虸; D蛬檠翸岪I冴u銭匂�  H媶�  L媥勠H媆$8tI�L媭�   H媰  H婸pI嬒A�蠭�H嫇  I嬒�惾   I�H嫎�   3襀婦$8禜�    嬓I嬒�親婰$8嫅   呉tI�L崄$  I嬒�惃   H婰$8児�   tI�L崄�  嫅   I嬒�惏   H婰$8L嬀  H嬞H塋$@H��P怚媁0I;W8t"H�    H岲$@H;衪
H婦$8H�3跧僄0�L岲$@I峅(�    H媆$@H呟t
H�H嬎�P怘媆$8�{t t+E匂uD8t$3tH媶�  H婬H�D秥$1A嬜�惱   �D秥$1�花   t"�|$0 tH媶�  H婬H�H崡�  �惛   E勪L媎$PtxI嬙H嬑�    L嫸  I嬡H塡$HM呬tI�$I嬏�P怚媀0I;V8tH�    H岲$HH;衪L�"3跧僃0�L岲$HI峃(�    H媆$HH呟t
H�H嬎�P怘媆$8H崡�  H媰  H塂$(D坙$ L嫃�  D嬇H嬑�    H嬑�    �   �|$2 勔   L峅M崉$X  H婽$8H伮�   H峀$X�    H崒$�  嬘 HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�嫈$�  呉tH媶�  H婬H�L崉$�  �惃   嫈$d  呉tH媶�  H婬H�L崉$h  �惏   菃
     H崋�  �      OHG @ O0H0G@@@OPHPG`@`H崁�   OpH餒嵖�   H冸u� OHG @ O0H0G@@@OPHPG`@`D埦�	  H媽$p  H3惕    H嫓$�  H伳�  A_A^A]A\_^]�   �   A   K   �  F   �  .   2  [   �  J     [   Y  H   a  E   �  B   �  m      �   �
  P G              )   �  �(        �nvrhi::d3d12::CommandList::setMeshletState 
 >   this  AJ        /  AL  /     � >�#   state  AK        ,  AM  ,     � >�    updateBlendFactor  B0   r    �  ! >	    effectiveStencilRefValue  A   �    =    Ao  u    �   E6u 1   �    � >�    updatePipeline  A^  �     %   A^ 3    � >QQ    pso  AH  �    �
   � �  AI  2     8[  f �O� �c  AJ  �    ^   >   AK  �      AH !    .   "   B8   7     � >�    updateFramebuffer  A\  b     W   >�    updateRootSignature  A   �       A   �     � 	  A  J      >�    updateViewports  A        �  
  B2       
   >処    framebuffer  AH  ;     
  AT  �    ] BP   @     � >�    updateIndirectParams  A]  �     O   >�    updateStencilRef  AF  �      AF �    ^  @  D -  B3   �    o   >u     bindingUpdateMask  A   �    L 
  & $  A  �    ^  @  D -  >睓   vpState  C               C   �  '       C             C  �  G    �  D�  ! M        1+  伆"
 >脵   a  AK  �      AK �    � 
 >脵   b  AH  �    '  AH �    H  + 0  
 >u     i  Ah  �    K  Ah     �  M        �*  "佇 M        &  "佇 N N N M        �*  L�! M        "  L�! N N" M        0+  ����
 >綑   a  AJ  �     &  AJ �    %* 2 
 >綑   b  AK  �     �  AK     � 
 >u     i  Ah  �     �  Ah     �  M        �*  �倎 M        $  �倎 N N N M        !%  傞-G
 >:Q   a  AQ  �    "  AQ ,    &K � R �m 
 >:Q   b  AP  �    . " AP     E  C  g � n �m 
 >u     mask  A   �      A      	  
 >u     i  A         A          A      	   N M           �; M        "   �;	
 >�   temp  AI  �    Z F   AI J      C      !    .    Bp  6    � D@    N N M        �$  ?凕 M        (%  
凕*"
 Z   %   M        ~%  � M        �%  � M        �%  �G

 M        �%  � N N N N N N M        2+  冺 M        5%  凊 N N1 M        �*  �5KI!	
 Z   �   >諵    commandList  AW  @    �  N M           � M        "   �	
 >�   temp  AI  �    c J   C          .    B�        DH    N N M        �$  :勫 M        (%  
勫*
 Z   %   M        ~%  勶 M        �%  勶 M        �%  勶G
 M        �%  �  N N N N N N M        3+  勍 M        5%  務#
 N N Z   �%  �%  _+  ^+  �%  b+   �          8         A � h-   e  "  $  &  �  z     "   �$  �$  �$  �$  �$  �$  !%  "%  '%  (%  5%  <%  =%  H%  }%  ~%  �%  �%  �%  �%  �%  #'  �*  �*  �*  �*  /+  0+  1+  2+  3+  =+  >+  ?+  @+  D+  E+  
 :p  O  �    Othis  �  �#  Ostate  0   �  OupdatePipeline  0   �  OupdateFramebuffer   0   �  OupdateRootSignature  0   �  OupdateViewports ! 0   �  OupdateIndirectParams  3   �  OupdateStencilRef  �  睓  OvpState  9a      軶   9q         9�      肣   9�      萉   9�      蚎   9�      /   9F      /   9x      蠶   9�      螿   9�      /   9*      /   9      萉   9A      蚎   O �   h              *   \      �  �/   �  �7   �  �@   �  �E   �  �g   �  ��   �  ��   �  ��   �  �  �  �}  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �,  �  �5  �  ��  �  �J  �  �a  �  �~  �  ��   ��   ��   ��   ��  	 �3   �]   �e   �u   ��   �   �    �+   �G  ! �Q  % ��  & ��  ' ��   �   _ F                                �`nvrhi::d3d12::CommandList::setMeshletState'::`1'::dtor$0  >睓    vpState  EN  �                                 �  O   �   �   _ F                                �`nvrhi::d3d12::CommandList::setMeshletState'::`1'::dtor$1  >睓    vpState  EN  �                                 �  O   ,   C   0   C  
 u   C   y   C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
   C     C  
 ,  C   0  C  
 D  C   H  C  
 m  C   q  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
   C     C  
   C     C  
 G  C   K  C  
 y  C   }  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
   C   	  C  
   C     C  
 %  C   )  C  
 T  C   X  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 )  C   -  C  
 =  C   A  C  
 Q  C   U  C  
 e  C   i  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
   C     C  
   C      C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
   C     C  
   C   "  C  
 .  C   2  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
   C   "  C  
 .  C   2  C  
 N  C   R  C  
 ^  C   b  C  
 n  C   r  C  
 �  C   �  C  
 �  C   �  C  
 �  C   �  C  
   C     C  
 j	  C   n	  C  
 �	  C   �	  C  
 �	  C   �	  C  
 �	  C   �	  C  
 �  C   
  C  
 
  C   
  C  
 
  C   #
  C  
 /
  C   3
  C  
 ?
  C   C
  C  
 O
  C   S
  C  
 _
  C   c
  C  
 o
  C   s
  C  
 
  C   �
  C  
 �
  C   �
  C  
 �
  C   �
  C  
 �
  C   �
  C  
 �
  C   �
  C  
 �
  C   �
  C  
 h  `   l  `  
 �  `   �  `  
   e      e  
 {  e     e  
 H崐@   �       X   H崐H   �       X   H婹H�    H呉HE旅         �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                   $       ^  �    _  �   `  �,      0     
 _      c     
 �      �     
  d T 4 2p    H           p      p      �    20    !           q      q      �    20    2           r      r      �   
 
4 
2p    B           s      s      �    20    <           t      t      �   
 
4 
2p    B           u      u      �    20    <           v      v      �   
 
4 
2p    B           w      w      �    �                  y      y      �    20    ^           {      {      �    B             k      �       "           |      |      �   h           �      �             2 B             k      �       "           }      }      �   h           �      �             2 20           k      �       t           ~      ~      �   h           �                   @� 
 d
 T	 4 2��p           k             ~                         h                              p0 *0 *0 ),  20    !           �      �          B             k              "           �      �         h           #      &             2 B             k      /       "           �      �      )   h           2      5             2 20    +           �      �      ;    20    !           �      �      D    20    W           �      �      J   
 
4 
2p           k      V       u           �      �      P   h           Y      \             RD  B      B           �      �      _    B                 �      �      e   ) 4� � ���
�p`P          r     l       q                 �      �      k   (           t      w       2    �b    �         X   
   X   � �~ - t  d T 4 2p    U           �      �      z    d 4 2p    �           �      �      �    4 r
p`P           k      �       �           �      �      �   (           �      �   
    �2    鄁    �       3      4               X <X4  4 ��
�p`P           k      �                 �      �      �   (           �      �       .    .    .    .    �    �       b      g      h      i                   a   l �|
�< < * 2P                b      b      �    2P    )           a      a      �   -
 [ 
��	��p`0P        �     l      �       x          �      �      �   (           �      �   
    @2    �
v       4                1�<"��a"�j L B             k      �       "           �      �      �   h           �      �             2 B             k      �       "           �      �      �   h           �      �             2 d 4 2p           k      �       K           �      �      �   h           �      �             \ ����
�p`0           k      �       r          �      �      �   8               �      �   	   �   (6                  �   �       ]   � �1�  BP0      F           ]      ]           B      :           �      �      
                                               W                         	                               f               Unknown exception                             r                                           ~               bad array new length                                      %                                 +      1      7                   .?AVbad_array_new_length@std@@     8               ����                      (                         .?AVbad_alloc@std@@     8              ����                      .                         .?AVexception@std@@     8               ����                      4         string too long     ����    ����        ��������                                                            �      *                   	   (      0                                                                  �      8      5      6       	   (      0                                                                  �      >      5      6       <   (   :   0   ;   �0Zv$鰋L�(Hb$EUnsupported primitive topology for meshlets Failed to create a meshlet pipeline state object vector too long                                       Z      ]      W                   .?AVIResource@nvrhi@@     8                         `                   c               ����    @                   Z      ]                                         4      i      f                         l                   o               ����    @                   4      i                                         .      u      r                         x                           {      o              ����    @                   .      u                                         (      �      ~                         �                                   �      {      o              ����    @                   (      �                                         �      �      �                   .?AVIMeshletPipeline@nvrhi@@     8                         �                           �      c              ����    @                   �      �                                         �      �      �                   .?AVMeshletPipeline@d3d12@nvrhi@@     8                         �                                           �      �      �      c              ����    @                   �      �              ����    @                   �      �                   .?AV?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@     8                         �                                   �      �      c                                         �      �      �      �   (   & 
 +        std::exception::`vftable'            
    �   (   & 
 +        std::bad_alloc::`vftable'            
    �   3   1 
 +        std::bad_array_new_length::`vftable'             
    �   1   / 
縠        nvrhi::IMeshletPipeline::`vftable'       B      B  
    �   *   ( 
攅        nvrhi::IResource::`vftable'              
    �   D   B 
縠        nvrhi::RefCounter<nvrhi::IMeshletPipeline>::`vftable'    E      E  
    �   7   5 
縠        nvrhi::d3d12::MeshletPipeline::`vftable'     H      H  
 
<V贷��1撦m涌�鋘i﹤%:玹′u肆峖=f瓵蹜蘚z	yYh�K蜌�(�巒'IJ肆峖=f瓵q┝鮷蔲幇*毛%"罌!ovQ炸鴖仿�G,谋椟绎劲ge粦`K�5廏�.�
鯉�.���惴硁秞廈9QVL;揁l豰肆峖=f瓵y端翇^=f瓵燡偼992魉翇^=f瓵h扒rr0伈3>飖9屓读棫錨盹嚡�V[8�踹!瘢未s�(�-
�-觝W�▏臂ep禭�}掱'晣臂ep禭P援� 猓&6萪O��:稦麷霵婬(�8~'髼�'項jNlSH\磯�'項j鸬F澁枏�'項j獶8�艒�'項j(囀/蜋RJ�'項j擔_湇壂'項j囹0p蹩:b8�4n下衊(摭礘端祆癜~t;豉鷲槑桌�2*C ;c厠�鋶犷A棊膬�咥9N彏嶀預棊膬赽9筴pP>见5FB�f錵B�嶿JI怣ZR/纍6掴磊*伋mEρ(糕磊*伋丙e<p絋W�K瓦(�.嗤<痖侣徇�5>矦`峯�g箩邆5>1闲夘'w箩邆5>TiV彌/c� �蹰kn:Fu*c�,�
鐱u藚\�"�0朤*錹虫試醋j=镄壆%I栶賑?T�=w羯d]{謑p�+珹<榛;�=忾:盘紎+苯P/鞍5]j��/*/�6aD髌擗�姽荛Oz笛賩yh铷赹u苟^C弩	袐稪铩�ol雭 Lo厩纷�5q�,俻爱T+倧A糲��雄&邚��偬訧)#hv瓯�ob斟�ob斟tV �鉨�o}i傓澠�#暜娄.�/钶�
朷�5xG�	�>寧4q嚚賲r艍&O饨~惵$r妿旤�袩c
钤K薓�"<�:�a�,f煷	T[�(髷鈃伹7�;�(� �3�$;4�B鹡N鵘J釫鲉譨駀F{'yZ祼垩寯啦烹嘕-WV8oc8曀黩6雵J-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o��腫62V了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D��<楁 佂咞taR�,F_棢杻#Qr瑺穄�R酁46Q蝎j@W╃砍鐓鹴aR�,F_棢杻#Q�.壙嬇雵J-WV8oc8曀黩6了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堢{殮殂
雵J-WV8o额	hQ�)缦F爔嘕-WV8oc8曀黩6雵J-WV8o�Tラ~�&威C帲晗D朎-;}k�;咞taR�,F_棢杻#Q魝o�!-坓�(鬄酲;[純o�-坓�(鬄�汬'这枱豤蛲㎏�4d�,>� dd�a�:筫�/1Щ笢}�8�R峄嫫﹥�,烶雟禑c闲�
墸g覢JstTK|瀧>�p`@衔dd�a�:で揩TAUa鑺觬豇�愘�+砋G堌x始wdd�a�:��>?)伭1鶩�?c樢閣yQ5R犵�喪樢閣yQ)傂螨顣GW�4滕P獠|$�dd�a�:刃璌1蘕代')&撀盍�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G圡血�<)�nk�#氷咞taR�,F_棢杻#Q�蟛�9h�q鸧&�5a膾%y*�杜`�6�>h�2擟~畖��0G#盱谑%郎耎灴8籹;嗐8�-b(纍4�硓�-坓�(鬄�/ｎ	蜍R,4��;儗潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H詠zv(缭亃v(缭亃v(鏰艕撬鼊鬧`匝G&N翙�#蒜�%G>禡h��\&2渘渗-%�?"�:邍A愦靮鸬2�>料C5��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2渘kjA�7m%ZZ�$为赞G刹~赣 "^惋砤��\&2湁搆q搄N茳YlL�^鴐禵諢覸鰛B	挿;褨鏌�� �府3*�#qM�5<A:蓰咨��\&2溿(N<S`�#)�*f诣k恒X彗`l鳳蔳蚅峈剡駥雒mQ�7#c垶�o�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       j                .debug$S       �              .debug$T       t                 .rdata         8       )�                         -          .text$mn              恶Lc     .debug$S       �              .text$mn       :      眡�     .debug$S                    .text$mn    	          恶Lc     .debug$S    
            	    .text$mn       K       誌摤     .debug$S       �             .text$mn    
   r     鷂yw     .debug$S       \  �       
    .text$x        F      絤�:
    .text$mn       P       EW�5     .debug$S       H             .text$mn       Z       狈 E     .debug$S                    .text$mn       �       垗褚     .debug$S       �             .text$mn              恶Lc     .debug$S                     .text$mn              恶Lc     .debug$S                    .text$mn              恶Lc     .debug$S                    .text$mn              �邆     .debug$S                    .text$mn       �       (回     .debug$S       �              .text$mn               袁z\     .debug$S    !   �               .text$mn    "   <      .ズ     .debug$S    #   0  
       "    .text$mn    $   <      .ズ     .debug$S    %   L  
       $    .text$mn    &   !      :著�     .debug$S    '   <         &    .text$mn    (   2      X于     .debug$S    )   <         (    .text$mn    *   "       坼	     .debug$S    +   �         *    .text$mn    ,   "       坼	     .debug$S    -   �         ,    .text$mn    .   "       坼	     .debug$S    /   �         .    .text$mn    0   "       坼	     .debug$S    1   �         0    .text$mn    2   "       坼	     .debug$S    3   �         2    .text$mn    4   "       坼	     .debug$S    5   �         4    .text$mn    6   ^      wP�     .debug$S    7   T         6    .text$mn    8          .B+�     .debug$S    9   �          8    .text$mn    :   t           .debug$S    ;   L         :    .text$mn    <         ��#     .debug$S    =   �          <    .text$mn    >         ��#     .debug$S    ?   �          >    .text$mn    @   ~      �0}�     .debug$S    A   �  2       @    .text$mn    B   !      -嵎     .debug$S    C   �          B    .text$mn    D   !       ��     .debug$S    E   �          D    .text$mn    F   !       ��     .debug$S    G   �          F    .text$mn    H   u      淿`�     .debug$S    I   �         H    .text$mn    J   B      贘S     .debug$S    K             J    .text$mn    L   B      贘S     .debug$S    M            L    .text$mn    N   B      贘S     .debug$S    O   �          N    .text$mn    P   H       襶.      .debug$S    Q   �         P    .text$mn    R   
       7�
b     .debug$S    S            R    .text$mn    T   +       �<Hu     .debug$S    U   p         T    .text$mn    V          aJ鄔     .debug$S    W   �          V    .text$mn    X         �ッ     .debug$S    Y            X    .text$mn    Z   �      I朖�     .debug$S    [   |         Z    .text$mn    \        鄔,�     .debug$S    ]   �
  F       \    .text$x     ^          蚬-
\    .text$x     _         �#赲    .text$x     `         �$�5\    .text$x     a         姦踬\    .text$x     b   )      k�腬    .text$mn    c   �      鮄嬴     .debug$S    d   ,  &       c    .text$x     e         L筩    .text$x     f         %FZ甤    .text$mn    g   x     歰�     .debug$S    h   x  p       g    .text$x     i         S躦    .text$x     j         a x鴊    .text$x     k         a x鴊    .text$mn    l   B      mr{V     .debug$S    m   �         l    .text$mn    n   U      fz     .debug$S    o            n    .text$mn    p          �GN     .debug$S    q   �          p    .text$mn    r          @V�     .debug$S    s   �          r    .text$mn    t          乬�     .debug$S    u   D         t    .text$mn    v   W       7a]m     .debug$S    w   �  
       v    .text$mn    x        檖�     .debug$S    y   �  �       x    .text$x     z         鲉k�x    .text$x     {         �$�;x    .text$mn    |         崪覩     .debug$S    }   �          |        R       P        n                �                �                �                �                �                �                �                      8        8      t        n      F        �          i
                   �               �               �      (        �      >              |        8      N        W          i                   v      "        �      J        �          i                   �      &        �      <              $        K      L        u          i                   �      V        �      6                       9               h              �      2        �              �      ,        '      :        M      @        �      D        �          i)                   �               �               5               �               �               @               �               �      4        <      *        s      R        �      T        �      B        .	          i7                   l	      p        �	      r        
      v        C
      H        p
          i=                   �
      l              X        �                     x        b      n        �               �               �               >
               �
      Z                       _               �      c        $      \        �               �      g        7              �      .        �              J              �              J              �              N              �      0                      �      	        0      
        (              }              �      e        !      i        �      z        #      b               ^        �      f        w      j        %      {        y      k        '      _              `        �      a        �                �                �                �            memcpy           memset           $LN13       P    $LN8        F    $LN5        (    $LN10       N    $LN7        "    $LN13       J    $LN10       $    $LN16       L    $LN3        V    $LN4        V    $LN33   ^   6    $LN36       6    $LN10       2    $LN10       ,    $LN30       :    $LN88       @    $LN8        D    $LN10       4    $LN10       *    $LN14       T    $LN8        B    $LN16       v    $LN22       H    $LN18   B   l    $LN21       l    $LN3       X    $LN4        X    $LN193      x    $LN4        n    $LN10       Z    $LN22       c    $LN113      \    $LN250  x  g    $LN255      g    $LN10       .    $LN10       0    $LN24           $LN237  r  
        !         $LN241      
    $LN14   :       $LN17           .xdata      ~          F┑@P        "      ~    .pdata               X賦鶳        4"          .xdata      �          （亵F        W"      �    .pdata      �         萣�5F        �"      �    .xdata      �          （亵(        �"      �    .pdata      �          T枨(        �"      �    .xdata      �          %蚘%N        �"      �    .pdata      �         惻竗N         #      �    .xdata      �          （亵"        F#      �    .pdata      �         2Fb�"        o#      �    .xdata      �          %蚘%J        �#      �    .pdata      �         惻竗J        �#      �    .xdata      �          （亵$        �#      �    .pdata      �         2Fb�$        $      �    .xdata      �          %蚘%L        K$      �    .pdata      �         惻竗L        }$      �    .xdata      �          懐j濾        �$      �    .pdata      �         Vbv鵙        �$      �    .xdata      �          （亵6        
%      �    .pdata      �         翎珸6        ]%      �    .xdata      �         /
�2        �%      �    .pdata      �         +eS�2        �%      �    .xdata      �   	      �#荤2        &      �    .xdata      �         j2        X&      �    .xdata      �          3狷 2        �&      �    .xdata      �         /
�,        �&      �    .pdata      �         +eS�,        '      �    .xdata      �   	      �#荤,        S'      �    .xdata      �         j,        �'      �    .xdata      �          3狷 ,        �'      �    .xdata      �         蚲7M:        (      �    .pdata      �         j�(:        M(      �    .xdata      �   	      �#荤:        z(      �    .xdata      �         j:        �(      �    .xdata      �          咃浒:        �(      �    .xdata      �          揉|蔃        )      �    .pdata      �         埈@        K)      �    .xdata      �   	      �#荤@        �)      �    .xdata      �         j@        �)      �    .xdata      �          旐@        *      �    .xdata      �          （亵D        B*      �    .pdata      �         萣�5D        r*      �    .xdata      �         /
�4        �*      �    .pdata      �         +eS�4        �*      �    .xdata      �   	      �#荤4        *+      �    .xdata      �         j4        q+      �    .xdata      �          3狷 4        �+      �    .xdata      �         /
�*        ,      �    .pdata      �         +eS�*        D,      �    .xdata      �   	      �#荤*        �,      �    .xdata      �         j*        �,      �    .xdata      �          3狷 *        
-      �    .voltbl     �          忯jpR    _volmd      �    .xdata      �          （亵T        K-      �    .pdata      �          ~        �-      �    .voltbl     �          堎覘T    _volmd      �    .xdata      �          （亵B        �-      �    .pdata      �         萣�5B        .      �    .xdata      �          （亵v        c.      �    .pdata      �         啁鉥v        �.      �    .xdata      �         �酑H        �.      �    .pdata      �         魺颁H        +/      �    .xdata      �   	      �#荤H        _/      �    .xdata      �         jH        �/      �    .xdata      �          �?H        �/      �    .xdata      �          �9�l        
0      �    .pdata      �         惻竗l        �0      �    .xdata      �          �9�X        1      �    .pdata      �         �1癤        �1      �    .xdata      �   (      �8'獂        *2      �    .pdata      �         m�(x        w2      �    .xdata      �   	      � )9x        �2      �    .xdata      �         o;o妜        3      �    .xdata      �          祆ex        g3      �    .xdata      �          嘋c鬾        �3      �    .pdata      �         �n        �3      �    .xdata      �          O鞿        )4      �    .pdata      �         具3躗        �4      �    .xdata      �         B,縢c        �4      �    .pdata      �         袷湅c        j5      �    .xdata      �   	      � )9c        �5      �    .xdata      �         g�碿        �6      �    .xdata      �   
       k�
鯿        /7      �    .xdata      �         @ェ1\        �7      �    .pdata      �         T赫\        �8      �    .xdata      �   	      � )9\        n9      �    .xdata      �   $      �,Y踈        E:      �    .xdata      �          寀
\        ";      �    .xdata      �          k筡        �;      �    .pdata      �         Vbv鵟        �<      �    .xdata      �          k筡        �=      �    .pdata      �         }y9鎈        �>      �    .xdata      �   $      �"奇g        �?      �    .pdata      �         6媑        0@      �    .xdata      �   	      � )9g        諤      �    .xdata      �         � �3g        A      �    .xdata      �          蔃敭g        .B      �    .xdata      �         /
�.        譈      �    .pdata      �         +eS�.        C      �    .xdata      �   	      �#荤.        ZC      �    .xdata      �         j.        濩      �    .xdata      �          3狷 .        鐲      �    .xdata      �         /
�0        ,D      �    .pdata      �         +eS�0        gD      �    .xdata      �   	      �#荤0              �    .xdata      �         j0        轉      �    .xdata      �          3狷 0        !E      �    .xdata      �         Cg�        ^E      �    .pdata      �         晦鱰        /F      �    .xdata      �   	      �#荤        �F      �    .xdata      �         j        褿      �    .xdata      �          �        獺      �    .xdata      �         偎�
        ~I      �    .pdata      �         膰陏
        ~J      �    .xdata      �   
      B>z]
        }K      �    .xdata      �         訷O
        L      �    .xdata      �         ’�
        嘙      �    .xdata      �         r%�
        嘚      �    .xdata      �          ��
        婳      �    .xdata      �          M[�
        峆      �    .pdata      �         j蓑�
        濹      �    .voltbl     �                  _volmd      �    .xdata      �          �9�        甊      �    .pdata      �         礝
        S      �    .rdata      �   (                   gS     �    .rdata      �                      �S     �    .rdata      �          �;�         桽      �    .rdata      �                      維     �    .rdata      �                      誗     �    .rdata      �          �)         鱏      �    .xdata$x                          #T          .xdata$x            虼�)         ET         .data$r       /      嶼�         hT         .xdata$x      $      4��         峊         .data$r       $      鎊=         釺         .xdata$x      $      銸E�         黅         .data$r       $      騏糡         ;U         .xdata$x      $      4��         UU             擴           .rdata               燺渾                  .data       	          烀�          蚒      	       V     	   .rdata      
  8                   (V     
   .rdata        8                   HV        .rdata        8                   ~V        .rdata      
         邿�               
   .rdata        ,       戻�         蜼         .rdata        1       伎磒         W         .rdata               IM         >W         .rdata$r      $      'e%�         dW         .data$rs      &      煷>         ~W         .rdata$r            �          歐         .rdata$r                         瞁         .rdata$r      $      Gv�:         蔠         .rdata$r      $      'e%�         隬         .rdata$r            �          X         .rdata$r                         X         .rdata$r      $      Gv�:         /X         .rdata$r      $      'e%�         NX         .rdata$r            }%B         fX         .rdata$r                         |X         .rdata$r      $      `         扻         .rdata$r      $      'e%�         盭         .rdata$r            �弾         訶         .rdata$r                          鮔          .rdata$r    !  $      H衡�         Y      !   .rdata$r    "  $      'e%�         @Y      "   .data$rs    #  -      &俪         aY      #   .rdata$r    $        }%B         刌      $   .rdata$r    %                           %   .rdata$r    &  $      `         耏      &   .rdata$r    '  $      'e%�         闥      '   .data$rs    (  2      
�.�         Z      (   .rdata$r    )        �J�         8Z      )   .rdata$r    *  $                   \Z      *   .rdata$r    +  $      o咔b         �Z      +   .rdata$r    ,  $      H衡�         璟      ,   .data$rs    -  C      #$         隯      -   .rdata$r    .        �弾         $[      .   .rdata$r    /                     Y[      /   .rdata$r    0  $      'e%�         嶽      0       臶           _fltused         .debug$S    1  4          �    .debug$S    2  4          �    .debug$S    3  @          �    .debug$S    4  @          
   .debug$S    5  8          �    .debug$S    6  P             .debug$S    7  D             .chks64     8  �	                譡  ?c_ResourceStateUnknown@d3d12@nvrhi@@3IB ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z ??1IResource@nvrhi@@MEAA@XZ ?getNativeObject@IResource@nvrhi@@UEAA?AUObject@2@I@Z ??_GIResource@nvrhi@@MEAAPEAXI@Z ??_EIResource@nvrhi@@MEAAPEAXI@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ?usesConstantColor@BlendState@nvrhi@@QEBA_NI@Z ??0RenderState@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1MeshletPipelineDesc@nvrhi@@QEAA@XZ ??4MeshletPipelineDesc@nvrhi@@QEAAAEAU01@AEBU01@@Z ??_GIMeshletPipeline@nvrhi@@UEAAPEAXI@Z ??_EIMeshletPipeline@nvrhi@@UEAAPEAXI@Z ?InvalidEnum@utils@nvrhi@@YAXXZ ?getDxgiFormatMapping@nvrhi@@YAAEBUDxgiFormatMapping@1@W4Format@1@@Z ?convertPrimitiveType@d3d12@nvrhi@@YA?AW4D3D_PRIMITIVE_TOPOLOGY@@W4PrimitiveType@2@I@Z ?TranslateBlendState@d3d12@nvrhi@@YAXAEBUBlendState@2@AEAUD3D12_BLEND_DESC@@@Z ?TranslateDepthStencilState@d3d12@nvrhi@@YAXAEBUDepthStencilState@2@AEAUD3D12_DEPTH_STENCIL_DESC@@@Z ?TranslateRasterizerState@d3d12@nvrhi@@YAXAEBURasterState@2@AEAUD3D12_RASTERIZER_DESC@@@Z ?error@Context@d3d12@nvrhi@@QEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ??1?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@UID3D12PipelineState@@@nvrhi@@QEAA@XZ ?AddRef@?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAKXZ ?Release@?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAKXZ ??_G?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAPEAXI@Z ??_E?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAPEAXI@Z ?getDesc@MeshletPipeline@d3d12@nvrhi@@UEBAAEBUMeshletPipelineDesc@3@XZ ?getFramebufferInfo@MeshletPipeline@d3d12@nvrhi@@UEBAAEBUFramebufferInfo@3@XZ ?getNativeObject@MeshletPipeline@d3d12@nvrhi@@UEAA?AUObject@3@I@Z ??_GMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z ??_EMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z ?deallocate@?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@QEAAXQEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@_K@Z ?_Xlength@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ ?convertViewportState@d3d12@nvrhi@@YA?AUDX12_ViewportState@12@AEBURasterState@2@AEBUFramebufferInfoEx@2@AEBUViewportState@2@@Z ?setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z ?dispatchMesh@CommandList@d3d12@nvrhi@@UEAAXIII@Z ?commitBarriers@CommandList@d3d12@nvrhi@@UEAAXXZ ?commitDescriptorHeaps@CommandList@d3d12@nvrhi@@UEAA_NXZ ?updateGraphicsVolatileBuffers@CommandList@d3d12@nvrhi@@UEAAXXZ ?setGraphicsBindings@CommandList@d3d12@nvrhi@@QEAAXAEBU?$static_vector@PEAVIBindingSet@nvrhi@@$04@3@IPEAVIBuffer@3@_NPEBVRootSignature@23@@Z ?bindMeshletPipeline@CommandList@d3d12@nvrhi@@AEBAXPEAVMeshletPipeline@23@_N@Z ?bindFramebuffer@CommandList@d3d12@nvrhi@@AEAAXPEAVFramebuffer@23@@Z ?unbindShadingRateState@CommandList@d3d12@nvrhi@@AEAAXXZ ?createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z ?createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z ?getRootSignature@Device@d3d12@nvrhi@@AEAA?AV?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@3@AEBU?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@3@_N@Z ?createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z ??$IID_PPV_ARGS_Helper@UID3D12PipelineState@@@@YAPEAPEAXPEAPEAUID3D12PipelineState@@@Z ??1?$RefCountPtr@VIMeshletPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??$checked_cast@PEAVRootSignature@d3d12@nvrhi@@PEAVIRootSignature@23@@nvrhi@@YAPEAVRootSignature@d3d12@0@PEAVIRootSignature@20@@Z ??$checked_cast@PEAVMeshletPipeline@d3d12@nvrhi@@PEAVIMeshletPipeline@3@@nvrhi@@YAPEAVMeshletPipeline@d3d12@0@PEAVIMeshletPipeline@0@@Z ??$checked_cast@PEAVFramebuffer@d3d12@nvrhi@@PEAVIFramebuffer@3@@nvrhi@@YAPEAVFramebuffer@d3d12@0@PEAVIFramebuffer@0@@Z ??$arraysAreDifferent@U?$static_vector@UViewport@nvrhi@@$0BA@@nvrhi@@U12@@nvrhi@@YA_NAEBU?$static_vector@UViewport@nvrhi@@$0BA@@0@0@Z ??$arraysAreDifferent@U?$static_vector@URect@nvrhi@@$0BA@@nvrhi@@U12@@nvrhi@@YA_NAEBU?$static_vector@URect@nvrhi@@$0BA@@0@0@Z ??$arrayDifferenceMask@U?$static_vector@PEAVIBindingSet@nvrhi@@$04@nvrhi@@U12@@nvrhi@@YAIAEBU?$static_vector@PEAVIBindingSet@nvrhi@@$04@0@0@Z ??1?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAA@XZ ??$_Destroy_range@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@YAXPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV12@AEAV?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@0@@Z ??$_Atomic_address_as@JU?$_Atomic_padded@K@std@@@std@@YAPECJAEAU?$_Atomic_padded@K@0@@Z ??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$11@?0???$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z@4HA ?dtor$0@?0??createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z@4HA ?dtor$0@?0??createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z@4HA ?dtor$0@?0??setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z@4HA ?dtor$19@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA ?dtor$1@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA ?dtor$1@?0??createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z@4HA ?dtor$1@?0??createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z@4HA ?dtor$1@?0??setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z@4HA ?dtor$3@?0??createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z@4HA ?dtor$7@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA ?dtor$8@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA ?dtor$9@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??_GIResource@nvrhi@@MEAAPEAXI@Z $pdata$??_GIResource@nvrhi@@MEAAPEAXI@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1MeshletPipelineDesc@nvrhi@@QEAA@XZ $pdata$??1MeshletPipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1MeshletPipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1MeshletPipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1MeshletPipelineDesc@nvrhi@@QEAA@XZ $unwind$??4MeshletPipelineDesc@nvrhi@@QEAAAEAU01@AEBU01@@Z $pdata$??4MeshletPipelineDesc@nvrhi@@QEAAAEAU01@AEBU01@@Z $cppxdata$??4MeshletPipelineDesc@nvrhi@@QEAAAEAU01@AEBU01@@Z $stateUnwindMap$??4MeshletPipelineDesc@nvrhi@@QEAAAEAU01@AEBU01@@Z $ip2state$??4MeshletPipelineDesc@nvrhi@@QEAAAEAU01@AEBU01@@Z $unwind$??_GIMeshletPipeline@nvrhi@@UEAAPEAXI@Z $pdata$??_GIMeshletPipeline@nvrhi@@UEAAPEAXI@Z $unwind$??1?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VRootSignature@d3d12@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@UID3D12PipelineState@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@UID3D12PipelineState@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@UID3D12PipelineState@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@UID3D12PipelineState@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@UID3D12PipelineState@@@nvrhi@@QEAA@XZ $unwind$?Release@?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAKXZ $pdata$?Release@?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAKXZ $unwind$??_G?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAPEAXI@Z $pdata$??_G?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@UEAAPEAXI@Z $unwind$?getNativeObject@MeshletPipeline@d3d12@nvrhi@@UEAA?AUObject@3@I@Z $pdata$?getNativeObject@MeshletPipeline@d3d12@nvrhi@@UEAA?AUObject@3@I@Z $unwind$??_GMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z $pdata$??_GMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z $cppxdata$??_GMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z $stateUnwindMap$??_GMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z $ip2state$??_GMeshletPipeline@d3d12@nvrhi@@UEAAPEAXI@Z $unwind$?deallocate@?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@QEAAXQEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@_K@Z $pdata$?deallocate@?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@QEAAXQEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@_K@Z $unwind$?_Xlength@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ $unwind$?setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z $pdata$?setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z $cppxdata$?setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z $stateUnwindMap$?setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z $ip2state$?setMeshletState@CommandList@d3d12@nvrhi@@UEAAXAEBUMeshletState@3@@Z $unwind$?dispatchMesh@CommandList@d3d12@nvrhi@@UEAAXIII@Z $pdata$?dispatchMesh@CommandList@d3d12@nvrhi@@UEAAXIII@Z $unwind$?bindMeshletPipeline@CommandList@d3d12@nvrhi@@AEBAXPEAVMeshletPipeline@23@_N@Z $pdata$?bindMeshletPipeline@CommandList@d3d12@nvrhi@@AEBAXPEAVMeshletPipeline@23@_N@Z $unwind$?createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z $pdata$?createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z $cppxdata$?createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z $stateUnwindMap$?createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z $ip2state$?createMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@AEBUMeshletPipelineDesc@3@PEAVIFramebuffer@3@@Z $unwind$?createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z $pdata$?createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z $cppxdata$?createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z $stateUnwindMap$?createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z $ip2state$?createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z $unwind$?dtor$1@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA $pdata$?dtor$1@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA $unwind$?dtor$19@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA $pdata$?dtor$19@?0??createHandleForNativeMeshletPipeline@Device@d3d12@nvrhi@@UEAA?AV?$RefCountPtr@VIMeshletPipeline@nvrhi@@@3@PEAVIRootSignature@23@PEAUID3D12PipelineState@@AEBUMeshletPipelineDesc@3@AEBUFramebufferInfo@3@@Z@4HA $unwind$?createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z $pdata$?createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z $cppxdata$?createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z $stateUnwindMap$?createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z $ip2state$?createPipelineState@Device@d3d12@nvrhi@@AEBA?AV?$RefCountPtr@UID3D12PipelineState@@@3@AEBUMeshletPipelineDesc@3@PEAVRootSignature@23@AEBUFramebufferInfo@3@@Z $unwind$??1?$RefCountPtr@VIMeshletPipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIMeshletPipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIMeshletPipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIMeshletPipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIMeshletPipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??$_Destroy_range@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@YAXPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV12@AEAV?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@YAXPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV12@AEAV?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@0@@Z $cppxdata$??$_Destroy_range@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@YAXPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV12@AEAV?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@0@@Z $stateUnwindMap$??$_Destroy_range@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@YAXPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV12@AEAV?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@0@@Z $ip2state$??$_Destroy_range@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@YAXPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV12@AEAV?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@0@@Z $unwind$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $pdata$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $cppxdata$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $stateUnwindMap$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $tryMap$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $handlerMap$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $ip2state$??$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z $unwind$?catch$11@?0???$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z@4HA $pdata$?catch$11@?0???$_Emplace_reallocate@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@@std@@@std@@AEAAPEAV?$RefCountPtr@VIResource@nvrhi@@@nvrhi@@QEAV23@$$QEAV23@@Z@4HA $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7IResource@nvrhi@@6B@ ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7IMeshletPipeline@nvrhi@@6B@ ??_7?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@6B@ ??_7MeshletPipeline@d3d12@nvrhi@@6B@ _GUID_765a30f3_f624_4c6f_a828_ace948622445 ??_C@_0CM@OMMFGBHC@Unsupported?5primitive?5topology?5@ ??_C@_0DB@NNNOEALG@Failed?5to?5create?5a?5meshlet?5pipe@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4IResource@nvrhi@@6B@ ??_R0?AVIResource@nvrhi@@@8 ??_R3IResource@nvrhi@@8 ??_R2IResource@nvrhi@@8 ??_R1A@?0A@EA@IResource@nvrhi@@8 ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R4IMeshletPipeline@nvrhi@@6B@ ??_R0?AVIMeshletPipeline@nvrhi@@@8 ??_R3IMeshletPipeline@nvrhi@@8 ??_R2IMeshletPipeline@nvrhi@@8 ??_R1A@?0A@EA@IMeshletPipeline@nvrhi@@8 ??_R4MeshletPipeline@d3d12@nvrhi@@6B@ ??_R0?AVMeshletPipeline@d3d12@nvrhi@@@8 ??_R3MeshletPipeline@d3d12@nvrhi@@8 ??_R2MeshletPipeline@d3d12@nvrhi@@8 ??_R1A@?0A@EA@MeshletPipeline@d3d12@nvrhi@@8 ??_R1A@?0A@EA@?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@8 ??_R0?AV?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@@8 ??_R3?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@8 ??_R2?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@8 ??_R4?$RefCounter@VIMeshletPipeline@nvrhi@@@nvrhi@@6B@ __security_cookie 