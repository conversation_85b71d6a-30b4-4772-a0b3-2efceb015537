d唝F馟hr� �      .drectve        j  �:               
 .debug$S        処 f<  陞     n   @ B.debug$T        �   6�             @ B.rdata          �&  蕣 罕     :  @ P@.rdata                          @@@.rdata             �             @@@.rdata             �             @@@.rdata             /�             @@@.rdata             A�             @@@.rdata             S�             @@@.rdata             f�             @@@.rdata             v�             @@@.rdata             吘             @@@.rdata             斁             @@@.rdata                          @@@.rdata             骄             @@@.rdata             芯             @@@.rdata             饩             @@@.rdata             蹙             @@@.rdata             �             @@@.rdata          
   �             @@@.rdata             $�             @@@.rdata             0�             @@@.rdata             <�             @@@.rdata             N�             @@@.rdata             c�             @@@.rdata             |�             @@@.rdata             斂             @@@.rdata             ┛             @@@.rdata             豢             @@@.rdata             炭             @@@.rdata             芸             @@@.rdata             羁             @@@.rdata                          @@@.rdata             �             @@@.rdata             �             @@@.rdata             .�             @@@.rdata             <�             @@@.rdata          
   L�             @@@.rdata          
   Y�             @@@.rdata             f�             @@@.rdata          
   r�             @@@.rdata             �             @@@.rdata          
   嬂             @@@.rdata          
   樌             @@@.rdata          
   ⒗             @@@.rdata          	                @@@.rdata          	   道             @@@.rdata             纠             @@@.rdata             屠             @@@.rdata             呃             @@@.rdata             趵             @@@.rdata             
�             @@@.rdata             �             @@@.rdata          
   #�             @@@.rdata             -�             @@@.rdata          
   8�             @@@.rdata          
   B�             @@@.rdata          
   O�             @@@.rdata          
   Y�             @@@.rdata          
   c�             @@@.rdata          	   m�             @@@.rdata          
   v�             @@@.rdata          	   ��             @@@.rdata             壛             @@@.rdata          	   暳             @@@.rdata             灹             @@@.rdata          	   α             @@@.rdata                          @@@.rdata          	   妨             @@@.rdata          	   懒             @@@.rdata             闪             @@@.rdata             芰             @@@.rdata             炝             @@@.rdata          
                @@@.rdata          
   	�             @@@.rdata             �             @@@.rdata          
   "�             @@@.rdata          
   /�             @@@.rdata             9�             @@@.rdata          
   H�             @@@.rdata          
   U�             @@@.rdata             _�             @@@.rdata          
   n�             @@@.rdata          
   {�             @@@.rdata          
   吢             @@@.rdata          
   徛             @@@.rdata          
   溌             @@@.rdata          
   β             @@@.rdata          
   奥             @@@.rdata             铰             @@@.rdata             搪             @@@.rdata             勐             @@@.rdata             曷             @@@.rdata             �             @@@.rdata             �             @@@.rdata             +�             @@@.rdata             =�             @@@.rdata             Q�             @@@.rdata          
   _�             @@@.rdata          
   i�             @@@.rdata          
   s�             @@@.rdata          
   ��             @@@.rdata             娒             @@@.rdata             櫭             @0@.rdata             灻             @0@.rdata             Ｃ             @0@.rdata                          @0@.rdata                          @0@.rdata             裁             @0@.rdata             访             @@@.rdata             旅             @0@.rdata             敲             @0@.rdata             堂             @0@.rdata             衙             @0@.rdata             置             @0@.rdata             勖             @0@.rdata             嗝             @0@.rdata             忝             @0@.rdata             杳             @@@.rdata             髅             @0@.rdata                          @0@.rdata             �             @0@.rdata              �             @@@.rdata          (   &�             @@@.rdata             N�             @@@.text$mn        2   ]� 從         P`.debug$S        <  Ｄ 吲        @B.text$mn        <   W� 撈         P`.debug$S        D  逼 跚     
   @B.text$mn        G   Y� 犎         P`.debug$S        �  救 v�        @B.text$mn        <   钍 *�         P`.debug$S        4  H� |�     
   @B.text$mn           嗵 筇         P`.debug$S        �   � 缤        @B.text$mn           #� 6�         P`.debug$S        �   J� &�        @B.text$mn        B   N� 愊         P`.debug$S        �            @B.text$mn        B   嫘 (�         P`.debug$S          F� R�        @B.text$mn        B   幰 幸         P`.debug$S          钜 蛴        @B.text$mn        6  .� d�         P`.debug$S        �  艺 嘿     (   @B.text$mn        �   J� 褊         P`.debug$S        �   走        @B.text$mn          镟 �         P`.debug$S        8  � O�     (   @B.text$mn        S  哞 2�         P`.debug$S        �	  F� >�     h   @B.text$mn        �   N� 纟         P`.debug$S        �  "� 嘻        @B.text$mn        R   傸 渣         P`.debug$S        P  � V�        @B.text$mn        P    鳋         P`.debug$S        X  (� �         @B.text$mn           �               P`.debug$S        $  �  	        @B.text$mn           E              P`.debug$S          T \        @B.text$mn            � �         P`.debug$S        4  � �        @B.text$mn        �   2 �         P`.debug$S          � �        @B.text$mn        S   
 o
         P`.debug$S        X  �
 �        @B.text$mn        +   I t         P`.debug$S        \  ~ �
        @B.text$mn        3   * ]         P`.debug$S        �  g �        @B.text$mn        �  K �         P`.debug$S        D  �         @B.text$mn        �   � j         P`.debug$S        �  t T        @B.text$mn        T   � H         P`.debug$S        �  z      
   @B.text$mn        �   v ;          P`.debug$S          m  �"        @B.text$mn        �   
# �#         P`.debug$S          $ &        @B.text$mn           �& �&         P`.debug$S          �& �'        @B.text$mn        M   ( ^(         P`.debug$S        T  �( �)        @B.text$mn        _   4* �*         P`.debug$S        �  �* �,        @B.text$mn        �   - �-         P`.debug$S          C. O0        @B.text$mn        #   �0 �0         P`.debug$S          �0  2        @B.text$mn           <2              P`.debug$S        �   K2 G3        @B.text$mn           �3              P`.debug$S          �3 �4        @B.text$mn        @   �4  5         P`.debug$S        8  H5 �6        @B.text$mn           �6 �6         P`.debug$S          �6 8        @B.text$mn           L8 _8         P`.debug$S        �   i8 a9        @B.text$mn        �   �9 :         P`.debug$S          �: �<        @B.text$mn        %   c= �=         P`.debug$S          �= �>        @B.text$mn        �   �> j?         P`.debug$S        �  �? \A     
   @B.text$mn           繟 華         P`.debug$S        �   褹 払        @B.text$mn        #   築 軧         P`.debug$S          鏐 驝        @B.text$mn           /D              P`.debug$S        �   5D E        @B.text$mn        #   -E PE         P`.debug$S          ZE ^F        @B.text$mn        *   欶 腇         P`.debug$S        �  谾 淗        @B.text$mn        }   (I              P`.debug$S        �   9L        @B.text$mn        N   M cM         P`.debug$S        �  昅 O        @B.text$mn        �   eO HP         P`.debug$S          zP 係     "   @B.text$mn           諸              P`.debug$S        �   躎 淯        @B.text$mn           腢 諹         P`.debug$S        �   郩 豓        @B.text$mn        �   W 蜽         P`.debug$S        �  X ╕        @B.text$mn        �   HZ 0[     	    P`.debug$S        @  奫 蔧        @B.text$mn          篰 蘟     
    P`.debug$S          Nb jh     2   @B.text$mn        �  ^j m         P`.debug$S        d   m 剅        @B.text$mn        #   Ls os         P`.debug$S          ys }t        @B.text$mn            箃 賢         P`.debug$S          鉻 飖        @B.text$mn        �   +v 詖     	    P`.debug$S        p  .w 瀥        @B.text$mn           巣              P`.debug$S           磠        @B.text$mn        '   饆 |         P`.debug$S          !| 9}        @B.text$mn        7   u}              P`.debug$S        �  瑌 <     
   @B.text$mn           �              P`.debug$S          � 硛        @B.text$mn        /   飥 �         P`.debug$S        T  (� |�        @B.text$mn        >   競 鰝         P`.debug$S        �   � ��        @B.text$mn        	   紕              P`.debug$S        �   艅 祬        @B.text$mn        $   駞 �         P`.debug$S           � �        @B.text$mn        �   [� 雵         P`.debug$S          � �        @B.text$mn        �   K� 鴮         P`.debug$S        �  *� �        @B.text$mn        $   蕫 類         P`.debug$S        $  鴲 �        @B.text$mn           X�              P`.debug$S          k� 儞        @B.text$mn        �   繐 彅         P`.debug$S          翑 艝        @B.text$mn        4   Q�              P`.debug$S        T  厳 贅     
   @B.text$mn        $   =� a�         P`.debug$S        �   k� g�        @B.text$mn            稓         P`.debug$S        �   罋 敍        @B.xdata             袥             @0@.pdata             貨 錄        @0@.xdata             �             @0@.pdata             
� �        @0@.xdata             4�             @0@.pdata             <� H�        @0@.xdata             f�             @0@.pdata             n� z�        @0@.xdata             槣 瑴        @0@.pdata             蕼 譁        @0@.xdata             魷 �        @0@.pdata             &� 2�        @0@.xdata             P� `�        @0@.pdata             ~� 姖        @0@.xdata                          @0@.pdata             礉 罎        @0@.xdata             逎             @0@.pdata             鏉 驖        @0@.xdata             �             @0@.pdata             � $�        @0@.xdata             B�             @0@.pdata             J� V�        @0@.xdata             t�             @0@.pdata             |� 垶        @0@.xdata                          @0@.pdata             疄 簽        @0@.xdata             貫             @0@.pdata             馂 鼮        @0@.xdata             �             @0@.pdata             2� >�        @0@.xdata             \�             @0@.pdata             d� p�        @0@.xdata             師             @0@.pdata             枱         @0@.xdata             罒             @0@.pdata             葻 詿        @0@.xdata             驘             @0@.pdata             鸁 �        @0@.xdata             $� 8�        @0@.pdata             V� b�        @0@.xdata             �� 敔        @0@.pdata             矤 緺        @0@.xdata             軤             @0@.pdata             鞝 鵂        @0@.xdata          (   �             @0@.pdata             >� J�        @0@.xdata             h�             @0@.pdata             p� |�        @0@.xdata             殹             @0@.pdata             ⅰ         @0@.xdata             獭             @0@.pdata             亍 洹        @0@.xdata             �             @0@.pdata             
� �        @0@.xdata             4�             @0@.pdata             <� H�        @0@.xdata             f�             @0@.pdata             n� z�        @0@.xdata             槩             @0@.pdata             牏         @0@.xdata             盛             @0@.pdata             症 猗        @0@.xdata              �             @0@.pdata             � �        @0@.xdata             2�             @0@.pdata             >� J�        @0@.xdata             h�             @0@.pdata             p� |�        @0@.xdata             殻             @0@.pdata             ⅲ         @0@.xdata             蹋             @0@.pdata             兀 洌        @0@.rdata             � �        @@@.rdata             8�             @@@.data$r         $   J� n�        @@�.xdata$x        $   x� 湦        @@@.rdata             挨 趣        @@@.rdata             妞         @@@.rdata          
   �             @@@.xdata$x           )� E�        @@@.xdata$x           Y� u�        @@@.data$r         +   摜 茎        @@�.xdata$x        $   去 欹        @@@.data$r         &    � &�        @@�.xdata$x        $   0� T�        @@@.rdata             h�             @@@.rdata             �             @0@.rdata$r        $   啨         @@@.rdata$r           圈 堞        @@@.rdata$r           姒 颚        @@@.rdata$r        $     �        @@@.rdata$r        $   4� X�        @@@.rdata$r           v� 姧        @@@.rdata$r           敡 ě        @@@.rdata$r        $   姬 唰        @@@.rdata$r        $   臾 �        @@@.rdata$r           6� J�        @@@.rdata$r           T� p�        @@@.rdata$r        $   帹 波        @@@.debug$S        4   屁         @B.debug$S        8   � F�        @B.debug$S        <   Z� 柀        @B.chks64         �                
     /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   d  �     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\DirectX-Headers.dir\Release\d3dx12_property_format_table.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $_Has_ADL_swap_detail 
 $rel_ops  $tr1  $literals  $string_literals �   �  ! �  � 翫3D_FEATURE_LEVEL_12_2 $ �   TP_CALLBACK_PRIORITY_NORMAL ) �    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED % �   TP_CALLBACK_PRIORITY_INVALID ) �   D3D_PRIMITIVE_TOPOLOGY_POINTLIST ( �   D3D_PRIMITIVE_TOPOLOGY_LINELIST ) �   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP , �   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST - �   D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP , �  
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ - �   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 0 �   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 1 �  
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 9 �  ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST 9 �  " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 9 �  # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 9 �  $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 9 �  % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 9 �  & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST 9 �  ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 9 �  ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 9 �  ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST : �  * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST : �  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST : �  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST : �  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST : �  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST : �  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST : �  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST : �  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST : �  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST : �  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST : �  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST : �  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST : �  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST : �  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST : �  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST : �  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST : �  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST : �  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST : �  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST : �  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST : �  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST : �  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST : �  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST   �    D3D_PRIMITIVE_UNDEFINED  �   D3D_PRIMITIVE_POINT  �   D3D_PRIMITIVE_LINE  �   D3D_PRIMITIVE_TRIANGLE  �   D3D_PRIMITIVE_LINE_ADJ # �   D3D_PRIMITIVE_TRIANGLE_ADJ , �   D3D_PRIMITIVE_1_CONTROL_POINT_PATCH , �  	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH , �  
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH , �   D3D_PRIMITIVE_4_CONTROL_POINT_PATCH , �   D3D_PRIMITIVE_5_CONTROL_POINT_PATCH , �  
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH , �   D3D_PRIMITIVE_7_CONTROL_POINT_PATCH , �   D3D_PRIMITIVE_8_CONTROL_POINT_PATCH , �   D3D_PRIMITIVE_9_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_10_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_11_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_12_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_13_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_14_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_15_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_16_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_17_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_18_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_19_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_20_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_21_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_22_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_23_CONTROL_POINT_PATCH - �   D3D_PRIMITIVE_24_CONTROL_POINT_PATCH - �    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH - �  ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH - �  " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH - �  # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH - �  $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH - �  % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH - �  & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH - �  ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH " �    D3D_SRV_DIMENSION_UNKNOWN ! �   D3D_SRV_DIMENSION_BUFFER $ �   D3D_SRV_DIMENSION_TEXTURE1D ) �   D3D_SRV_DIMENSION_TEXTURE1DARRAY $ �   D3D_SRV_DIMENSION_TEXTURE2D ) �   D3D_SRV_DIMENSION_TEXTURE2DARRAY & �   D3D_SRV_DIMENSION_TEXTURE2DMS + �   D3D_SRV_DIMENSION_TEXTURE2DMSARRAY $ �   D3D_SRV_DIMENSION_TEXTURE3D & �  	 D3D_SRV_DIMENSION_TEXTURECUBE + �  
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY # �   D3D_SRV_DIMENSION_BUFFEREX  �    NODE_INVALID  �   NODE_ELEMENT  �   NODE_ATTRIBUTE  �   NODE_TEXT  �   NODE_CDATA_SECTION  �   NODE_ENTITY_REFERENCE  �   NODE_ENTITY $ �   NODE_PROCESSING_INSTRUCTION  �   NODE_COMMENT  �  	 NODE_DOCUMENT  �  
 NODE_DOCUMENT_TYPE  �   NODE_DOCUMENT_FRAGMENT  �    XMLELEMTYPE_ELEMENT  �   XMLELEMTYPE_TEXT  �   XMLELEMTYPE_COMMENT  �   XMLELEMTYPE_DOCUMENT  �   XMLELEMTYPE_DTD  �   XMLELEMTYPE_PI  �    D3D_INCLUDE_LOCAL  �   D3D_INCLUDE_SYSTEM 1 �    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES  �    D3D_SVC_SCALAR  �   D3D_SVC_VECTOR  �   D3D_SVC_MATRIX_ROWS  �   D3D_SVC_MATRIX_COLUMNS  �   D3D_SVC_OBJECT  �   D3D_SVC_STRUCT   �   D3D_SVC_INTERFACE_CLASS " �   D3D_SVC_INTERFACE_POINTER  H   D3D_SVF_USERPACKED  H   D3D_SVF_USED " H   D3D_SVF_INTERFACE_POINTER $ H   D3D_SVF_INTERFACE_PARAMETER  �    D3D_SVT_VOID  �   D3D_SVT_BOOL  �   D3D_SVT_INT  �   D3D_SVT_FLOAT  �   D3D_SVT_STRING  �   D3D_SVT_TEXTURE  �   D3D_SVT_TEXTURE1D  �   D3D_SVT_TEXTURE2D  �   D3D_SVT_TEXTURE3D  �  	 D3D_SVT_TEXTURECUBE  �  
 D3D_SVT_SAMPLER  �   D3D_SVT_SAMPLER1D  �   D3D_SVT_SAMPLER2D  �  
 D3D_SVT_SAMPLER3D  �   D3D_SVT_SAMPLERCUBE  '   std::_Asan_granularity  �   D3D_SVT_PIXELSHADER $ '   std::_Asan_granularity_mask  �   D3D_SVT_VERTEXSHADER  �   D3D_SVT_PIXELFRAGMENT  �   D3D_SVT_VERTEXFRAGMENT  �   D3D_SVT_UINT  �   D3D_SVT_UINT8  �   D3D_SVT_GEOMETRYSHADER  �   D3D_SVT_RASTERIZER  �   D3D_SVT_DEPTHSTENCIL  �   D3D_SVT_BLEND  �   D3D_SVT_BUFFER  �   D3D_SVT_CBUFFER  �   D3D_SVT_TBUFFER  �   D3D_SVT_TEXTURE1DARRAY  �   D3D_SVT_TEXTURE2DARRAY ! �   D3D_SVT_RENDERTARGETVIEW ! �   D3D_SVT_DEPTHSTENCILVIEW  �    D3D_SVT_TEXTURE2DMS ! �  ! D3D_SVT_TEXTURE2DMSARRAY ! �  " D3D_SVT_TEXTURECUBEARRAY  �  # D3D_SVT_HULLSHADER  �  $ D3D_SVT_DOMAINSHADER " �  % D3D_SVT_INTERFACE_POINTER  �  & D3D_SVT_COMPUTESHADER  �  ' D3D_SVT_DOUBLE  �  ( D3D_SVT_RWTEXTURE1D ! �  ) D3D_SVT_RWTEXTURE1DARRAY  �  * D3D_SVT_RWTEXTURE2D ! �  + D3D_SVT_RWTEXTURE2DARRAY  �  , D3D_SVT_RWTEXTURE3D  �  - D3D_SVT_RWBUFFER # �  . D3D_SVT_BYTEADDRESS_BUFFER % �  / D3D_SVT_RWBYTEADDRESS_BUFFER " �  0 D3D_SVT_STRUCTURED_BUFFER $ �  1 D3D_SVT_RWSTRUCTURED_BUFFER ) �  2 D3D_SVT_APPEND_STRUCTURED_BUFFER * �  3 D3D_SVT_CONSUME_STRUCTURED_BUFFER  �   D3D_SIF_USERPACKED # �   D3D_SIF_COMPARISON_SAMPLER $ �   D3D_SIF_TEXTURE_COMPONENT_0 $ �   D3D_SIF_TEXTURE_COMPONENT_1 # �   D3D_SIF_TEXTURE_COMPONENTS  �    D3D_SIT_CBUFFER  �   D3D_SIT_TBUFFER  �   D3D_SIT_TEXTURE  �   D3D_SIT_SAMPLER  �   D3D_SIT_UAV_RWTYPED  �   D3D_SIT_STRUCTURED ! �   D3D_SIT_UAV_RWSTRUCTURED  �   D3D_SIT_BYTEADDRESS " �   D3D_SIT_UAV_RWBYTEADDRESS & �  	 D3D_SIT_UAV_APPEND_STRUCTURED ' �  
 D3D_SIT_UAV_CONSUME_STRUCTURED . �   D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER ( �   D3D_SIT_RTACCELERATIONSTRUCTURE  �   D3D_CBF_USERPACKED  V    D3D_CT_CBUFFER  V   D3D_CT_TBUFFER " V   D3D_CT_INTERFACE_POINTERS " V   D3D_CT_RESOURCE_BIND_INFO  �    D3D_NAME_UNDEFINED  �   D3D_NAME_POSITION  �   D3D_NAME_CLIP_DISTANCE  �   D3D_NAME_CULL_DISTANCE + �   D3D_NAME_RENDER_TARGET_ARRAY_INDEX & �   D3D_NAME_VIEWPORT_ARRAY_INDEX  �   D3D_NAME_VERTEX_ID  �   D3D_NAME_PRIMITIVE_ID  �   D3D_NAME_INSTANCE_ID  �  	 D3D_NAME_IS_FRONT_FACE  �  
 D3D_NAME_SAMPLE_INDEX , �   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR . �   D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR + �  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR - �   D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR . �   D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR / �   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR  �   D3D_NAME_BARYCENTRICS  �   D3D_NAME_SHADINGRATE  �   D3D_NAME_CULLPRIMITIVE  �  @ D3D_NAME_TARGET  �  A D3D_NAME_DEPTH  �  B D3D_NAME_COVERAGE % �  C D3D_NAME_DEPTH_GREATER_EQUAL " �  D D3D_NAME_DEPTH_LESS_EQUAL  �  E D3D_NAME_STENCIL_REF   �  F D3D_NAME_INNER_COVERAGE 6    std::_Iterator_base0::_Unwrap_when_unverified  �   D3D_RETURN_TYPE_UNORM  �   D3D_RETURN_TYPE_SNORM  �   D3D_RETURN_TYPE_SINT  �   D3D_RETURN_TYPE_UINT  �   D3D_RETURN_TYPE_FLOAT  �   D3D_RETURN_TYPE_MIXED  �   D3D_RETURN_TYPE_DOUBLE " �   D3D_RETURN_TYPE_CONTINUED ' �    D3D_REGISTER_COMPONENT_UNKNOWN & �   D3D_REGISTER_COMPONENT_UINT32 & �   D3D_REGISTER_COMPONENT_SINT32 ' �   D3D_REGISTER_COMPONENT_FLOAT32 & �   D3D_REGISTER_COMPONENT_UINT16 & �   D3D_REGISTER_COMPONENT_SINT16 ' �   D3D_REGISTER_COMPONENT_FLOAT16 & �   D3D_REGISTER_COMPONENT_UINT64 & �   D3D_REGISTER_COMPONENT_SINT64 ' �  	 D3D_REGISTER_COMPONENT_FLOAT64 ) �    D3D_TESSELLATOR_DOMAIN_UNDEFINED ' �   D3D_TESSELLATOR_DOMAIN_ISOLINE # �   D3D_TESSELLATOR_DOMAIN_TRI $ �   D3D_TESSELLATOR_DOMAIN_QUAD / `    D3D_TESSELLATOR_PARTITIONING_UNDEFINED - `   D3D_TESSELLATOR_PARTITIONING_INTEGER * `   D3D_TESSELLATOR_PARTITIONING_POW2 4 `   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 5 `   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN ) }    D3D_TESSELLATOR_OUTPUT_UNDEFINED 7    std::_Iterator_base12::_Unwrap_when_unverified % }   D3D_TESSELLATOR_OUTPUT_POINT $ }   D3D_TESSELLATOR_OUTPUT_LINE + }   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW , }   D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW  �    D3DFL_STANDARD  �   ��D3DFL_CUSTOM  �    D3DFTL_NO_TYPE  �   �﨑3DFTL_PARTIAL_TYPE  �   ��D3DFTL_FULL_TYPE  �   �麯3DFCN_R  �   �鼶3DFCN_G  �   �﨑3DFCN_B  �   ��D3DFCN_A  �    D3DFCN_D  �   D3DFCN_S  �   D3DFCN_X  �    D3DFCI_TYPELESS  �   �麯3DFCI_FLOAT  �   �鼶3DFCI_SNORM  �   �﨑3DFCI_UNORM  �   ��D3DFCI_SINT  �   D3DFCI_UINT  �   D3DFCI_UNORM_SRGB   �   D3DFCI_BIASED_FIXED_2_8 ' =    D3D12_COMMAND_LIST_TYPE_DIRECT ' =   D3D12_COMMAND_LIST_TYPE_BUNDLE ( =   D3D12_COMMAND_LIST_TYPE_COMPUTE % =   D3D12_COMMAND_LIST_TYPE_COPY - =   D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE . =   D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS - =   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE          std::_Fake_alloc % s   D3D12_COLOR_WRITE_ENABLE_RED ' s   D3D12_COLOR_WRITE_ENABLE_GREEN & s   D3D12_COLOR_WRITE_ENABLE_BLUE ' s   D3D12_COLOR_WRITE_ENABLE_ALPHA  �    D3D12_LOGIC_OP_CLEAR  �   D3D12_LOGIC_OP_SET  �   D3D12_LOGIC_OP_COPY % �   D3D12_LOGIC_OP_COPY_INVERTED  �   D3D12_LOGIC_OP_NOOP  �   D3D12_LOGIC_OP_INVERT  �   D3D12_LOGIC_OP_AND  �   D3D12_LOGIC_OP_NAND  �   D3D12_LOGIC_OP_OR  �  	 D3D12_LOGIC_OP_NOR  �  
 D3D12_LOGIC_OP_XOR  �   D3D12_LOGIC_OP_EQUIV # �   D3D12_LOGIC_OP_AND_REVERSE $ �  
 D3D12_LOGIC_OP_AND_INVERTED " �   D3D12_LOGIC_OP_OR_REVERSE  �   VT_I2  �   VT_I4  �   VT_BSTR  �  	 VT_DISPATCH  �  
 VT_ERROR  �   VT_VARIANT  �  
 VT_UNKNOWN  �   VT_I1  �   VT_I8  �  $ VT_RECORD  �  � �VT_RESERVED       std::_ISORT_MAX . �    D3D12_LINE_RASTERIZATION_MODE_ALIASED 8 �   D3D12_LINE_RASTERIZATION_MODE_ALPHA_ANTIALIASED 9 �   D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_WIDE  �    TYSPEC_CLSID  �   TYSPEC_FILEEXT  �   TYSPEC_MIMETYPE  �   TYSPEC_FILENAME  �   TYSPEC_PROGID  �   TYSPEC_PACKAGENAME + �   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 �   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - �   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 �   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS * �   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP ; �    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE / �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS / �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS / �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS / �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS / �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS / �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS : �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT 2 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND 8 �  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 7 �  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER : �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL 9 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT ? �  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE ? �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY B �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS A �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 8 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 6 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK 7 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 2 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS ; �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 8 �   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER2 1 q    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED F q   D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS A q   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK + �    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 1 �   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS ? �   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY # �   BINDSTATUS_FINDINGRESOURCE  �   BINDSTATUS_CONNECTING  �   BINDSTATUS_REDIRECTING % �   BINDSTATUS_BEGINDOWNLOADDATA # �   BINDSTATUS_DOWNLOADINGDATA # �   BINDSTATUS_ENDDOWNLOADDATA + �   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �   BINDSTATUS_INSTALLINGCOMPONENTS ) �  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �  
 BINDSTATUS_USINGCACHEDCOPY " �   BINDSTATUS_SENDINGREQUEST $ �   BINDSTATUS_CLASSIDAVAILABLE % �  
 BINDSTATUS_MIMETYPEAVAILABLE * �   BINDSTATUS_CACHEFILENAMEAVAILABLE & �   BINDSTATUS_BEGINSYNCOPERATION $ �   BINDSTATUS_ENDSYNCOPERATION # �   BINDSTATUS_BEGINUPLOADDATA ! �   BINDSTATUS_UPLOADINGDATA ! �   BINDSTATUS_ENDUPLOADDATA # �   BINDSTATUS_PROTOCOLCLASSID  �   BINDSTATUS_ENCODING - �   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �   BINDSTATUS_CLASSINSTALLLOCATION  �   BINDSTATUS_DECODING & �   BINDSTATUS_LOADINGMIMEHANDLER , �   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �   BINDSTATUS_FILTERREPORTMIMETYPE ' �   BINDSTATUS_CLSIDCANINSTANTIATE % �   BINDSTATUS_IUNKNOWNAVAILABLE  �   BINDSTATUS_DIRECTBIND  �   BINDSTATUS_RAWMIMETYPE " �    BINDSTATUS_PROXYDETECTING   �  ! BINDSTATUS_ACCEPTRANGES  �  " BINDSTATUS_COOKIE_SENT + �  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �  $ BINDSTATUS_COOKIE_SUPPRESSED ( �  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �  ' BINDSTATUS_COOKIE_STATE_REJECT ' �  ( BINDSTATUS_COOKIE_STATE_PROMPT & �  ) BINDSTATUS_COOKIE_STATE_LEASH * �  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �  + BINDSTATUS_POLICY_HREF  �  , BINDSTATUS_P3P_HEADER + �  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED 8 ]    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD + �  / BINDSTATUS_SESSION_COOKIES_ALLOWED 9 ]   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE   �  0 BINDSTATUS_CACHECONTROL . �  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME 6 ]   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR ) �  2 BINDSTATUS_MIMETEXTPLAINMISMATCH : ]   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_NO_ACCESS & �  3 BINDSTATUS_PUBLISHERAVAILABLE F ]   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER C ]   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_SRV ( �  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �  5 BINDSTATUS_SSLUX_NAVBLOCKED , �  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �  8 BINDSTATUS_64BIT_PROGRESS  �  8 BINDSTATUS_LAST  �  9 BINDSTATUS_RESERVED_0  �  : BINDSTATUS_RESERVED_1  �  ; BINDSTATUS_RESERVED_2  �  < BINDSTATUS_RESERVED_3  �  = BINDSTATUS_RESERVED_4  �  > BINDSTATUS_RESERVED_5  �  ? BINDSTATUS_RESERVED_6  �  @ BINDSTATUS_RESERVED_7  �  A BINDSTATUS_RESERVED_8  �  B BINDSTATUS_RESERVED_9  �  C BINDSTATUS_RESERVED_A  �  D BINDSTATUS_RESERVED_B  �  E BINDSTATUS_RESERVED_C  �  F BINDSTATUS_RESERVED_D  �  G BINDSTATUS_RESERVED_E  �  H BINDSTATUS_RESERVED_F  �  I BINDSTATUS_RESERVED_10  �  J BINDSTATUS_RESERVED_11  �  K BINDSTATUS_RESERVED_12  �  L BINDSTATUS_RESERVED_13  �  M BINDSTATUS_RESERVED_14 5 �    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD 6 �   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE 5 �   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE 7 �   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_NO_ACCESS C �   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER @ �   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_SRV 3 �   DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED .     std::integral_constant<bool,0>::value  �  i D3D_SHADER_MODEL_6_9  �    CIP_DISK_FULL  �   CIP_ACCESS_DENIED ! �   CIP_NEWER_VERSION_EXISTS ! �   CIP_OLDER_VERSION_EXISTS  �   CIP_NAME_CONFLICT 1 �   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + �   CIP_EXE_SELF_REGISTERATION_TIMEOUT  �   CIP_UNSAFE_TO_ABORT  �   CIP_NEED_REBOOT " F    Uri_PROPERTY_ABSOLUTE_URI  F   Uri_PROPERTY_USER_NAME  F   Uri_PROPERTY_HOST_TYPE  F   Uri_PROPERTY_ZONE  �    Uri_HOST_UNKNOWN  �   Uri_HOST_DNS  �   Uri_HOST_IPV4  �   Uri_HOST_IPV6 ' �    D3D12_SHADER_CACHE_MODE_MEMORY  �    D3D10_SB_4_COMPONENT_R  �   D3D10_SB_4_COMPONENT_G  �   D3D10_SB_4_COMPONENT_B  �   D3D10_SB_4_COMPONENT_A 3     D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 3    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1  �    std::denorm_absent  �   std::denorm_present  �    std::round_toward_zero  �   std::round_to_nearest # �    std::_Num_base::has_denorm (     std::_Num_base::has_denorm_loss %     std::_Num_base::has_infinity &     std::_Num_base::has_quiet_NaN *     std::_Num_base::has_signaling_NaN #     std::_Num_base::is_bounded !     std::_Num_base::is_exact "     std::_Num_base::is_iec559 #     std::_Num_base::is_integer "     std::_Num_base::is_modulo "     std::_Num_base::is_signed '     std::_Num_base::is_specialized (     std::_Num_base::tinyness_before      std::_Num_base::traps $ �    std::_Num_base::round_style       std::_Num_base::digits !      std::_Num_base::digits10 %      std::_Num_base::max_digits10 %      std::_Num_base::max_exponent '      std::_Num_base::max_exponent10 %      std::_Num_base::min_exponent '      std::_Num_base::min_exponent10       std::_Num_base::radix % �    D3D12_BARRIER_LAYOUT_PRESENT * �   D3D12_BARRIER_LAYOUT_GENERIC_READ + �   D3D12_BARRIER_LAYOUT_RENDER_TARGET . �   D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS 1 �   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE 0 �   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ - �   D3D12_BARRIER_LAYOUT_SHADER_RESOURCE ) �   D3D12_BARRIER_LAYOUT_COPY_SOURCE ' �   D3D12_BARRIER_LAYOUT_COPY_DEST , �  	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE * �  
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST 1 �   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE / �   D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ 0 �  
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE 0 �   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ '    std::_Num_int_base::is_bounded 1 �   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE / �   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ 0 �   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE %    std::_Num_int_base::is_exact 1 �   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON '    std::_Num_int_base::is_integer 7 �   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ ; �   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS +    std::_Num_int_base::is_specialized : �   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE 6 �   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE 4 �   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST "     std::_Num_int_base::radix 2 �   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON 8 �   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ < �   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS ; �   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 7 �   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 5 �   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST ) �   std::_Num_float_base::has_denorm +    std::_Num_float_base::has_infinity ,    std::_Num_float_base::has_quiet_NaN 0    std::_Num_float_base::has_signaling_NaN )    std::_Num_float_base::is_bounded (    std::_Num_float_base::is_iec559 (    std::_Num_float_base::is_signed -    std::_Num_float_base::is_specialized * �   std::_Num_float_base::round_style $     std::_Num_float_base::radix *     std::numeric_limits<bool>::digits -    std::numeric_limits<char>::is_signed -     std::numeric_limits<char>::is_modulo *     std::numeric_limits<char>::digits ,     std::numeric_limits<char>::digits10 4    std::numeric_limits<signed char>::is_signed 1     std::numeric_limits<signed char>::digits 3     std::numeric_limits<signed char>::digits10 6    std::numeric_limits<unsigned char>::is_modulo 3     std::numeric_limits<unsigned char>::digits 5     std::numeric_limits<unsigned char>::digits10 " �    D3D12_BARRIER_TYPE_GLOBAL # �   D3D12_BARRIER_TYPE_TEXTURE 1    std::numeric_limits<char16_t>::is_modulo .     std::numeric_limits<char16_t>::digits 0     std::numeric_limits<char16_t>::digits10 1    std::numeric_limits<char32_t>::is_modulo .      std::numeric_limits<char32_t>::digits 0    	 std::numeric_limits<char32_t>::digits10  �   BINDSTRING_HEADERS   �   BINDSTRING_ACCEPT_MIMES  �   BINDSTRING_EXTRA_URL  �   BINDSTRING_LANGUAGE  �   BINDSTRING_USERNAME  �   BINDSTRING_PASSWORD  �   BINDSTRING_UA_PIXELS  �   BINDSTRING_UA_COLOR  �  	 BINDSTRING_OS  �  
 BINDSTRING_USER_AGENT $ �   BINDSTRING_ACCEPT_ENCODINGS 0    std::numeric_limits<wchar_t>::is_modulo  �   BINDSTRING_POST_COOKIE -     std::numeric_limits<wchar_t>::digits " �  
 BINDSTRING_POST_DATA_MIME  �   BINDSTRING_URL  �   BINDSTRING_IID /     std::numeric_limits<wchar_t>::digits10 ' �   BINDSTRING_FLAG_BIND_TO_OBJECT $ �   BINDSTRING_PTR_BIND_CONTEXT  �   BINDSTRING_XDR_ORIGIN   �   BINDSTRING_DOWNLOADPATH  �   BINDSTRING_ROOTDOC_URL $ �   BINDSTRING_INITIAL_FILENAME " �   BINDSTRING_PROXY_USERNAME " �   BINDSTRING_PROXY_PASSWORD ! �   BINDSTRING_ENTERPRISE_ID  �   BINDSTRING_DOC_URL .    std::numeric_limits<short>::is_signed +     std::numeric_limits<short>::digits -     std::numeric_limits<short>::digits10 ,    std::numeric_limits<int>::is_signed )     std::numeric_limits<int>::digits +    	 std::numeric_limits<int>::digits10 -    std::numeric_limits<long>::is_signed *     std::numeric_limits<long>::digits ,    	 std::numeric_limits<long>::digits10 0    std::numeric_limits<__int64>::is_signed -    ? std::numeric_limits<__int64>::digits /     std::numeric_limits<__int64>::digits10 7    std::numeric_limits<unsigned short>::is_modulo 4     std::numeric_limits<unsigned short>::digits 6     std::numeric_limits<unsigned short>::digits10 ) �    D3D12_RESOURCE_DIMENSION_UNKNOWN ( �   D3D12_RESOURCE_DIMENSION_BUFFER + �   D3D12_RESOURCE_DIMENSION_TEXTURE1D + �   D3D12_RESOURCE_DIMENSION_TEXTURE2D + �   D3D12_RESOURCE_DIMENSION_TEXTURE3D 5    std::numeric_limits<unsigned int>::is_modulo 2      std::numeric_limits<unsigned int>::digits 4    	 std::numeric_limits<unsigned int>::digits10 6    std::numeric_limits<unsigned long>::is_modulo 3      std::numeric_limits<unsigned long>::digits 5    	 std::numeric_limits<unsigned long>::digits10 9    std::numeric_limits<unsigned __int64>::is_modulo 6    @ std::numeric_limits<unsigned __int64>::digits 8     std::numeric_limits<unsigned __int64>::digits10  �   PowerUserMaximum +     std::numeric_limits<float>::digits -     std::numeric_limits<float>::digits10 1    	 std::numeric_limits<float>::max_digits10 1    � std::numeric_limits<float>::max_exponent 3    & std::numeric_limits<float>::max_exponent10 2     �僺td::numeric_limits<float>::min_exponent 4     �踫td::numeric_limits<float>::min_exponent10  �   PARSE_CANONICALIZE  �   PARSE_FRIENDLY  �   PARSE_SECURITY_URL  �   PARSE_ROOTDOCUMENT  �   PARSE_DOCUMENT  �   PARSE_ANCHOR ! �   PARSE_ENCODE_IS_UNESCAPE  �   PARSE_DECODE_IS_ESCAPE  �  	 PARSE_PATH_FROM_URL  �  
 PARSE_URL_FROM_PATH  �   PARSE_MIME  �   PARSE_SERVER  �  
 PARSE_SCHEMA  �   PARSE_SITE  �   PARSE_DOMAIN  �   PARSE_LOCATION  �   PARSE_SECURITY_DOMAIN  �   PARSE_ESCAPE  �   PSU_DEFAULT ,    5 std::numeric_limits<double>::digits  �   QUERY_EXPIRATION_DATE .     std::numeric_limits<double>::digits10 " �   QUERY_TIME_OF_LAST_CHANGE  �   QUERY_CONTENT_ENCODING  �   QUERY_CONTENT_TYPE 2     std::numeric_limits<double>::max_digits10  �   QUERY_REFRESH 2     std::numeric_limits<double>::max_exponent  �   QUERY_RECOMBINE  �   QUERY_CAN_NAVIGATE  �   QUERY_USES_NETWORK 4    4std::numeric_limits<double>::max_exponent10  �  	 QUERY_IS_CACHED   �  
 QUERY_IS_INSTALLEDENTRY 4    �黶td::numeric_limits<double>::min_exponent " �   QUERY_IS_CACHED_OR_MAPPED  �   QUERY_USES_CACHE 6    �威std::numeric_limits<double>::min_exponent10  �  
 QUERY_IS_SECURE  �   QUERY_IS_SAFE ! �   QUERY_USES_HISTORYFOLDER  �    ServerApplication  �    IdleShutdown 1    5 std::numeric_limits<long double>::digits 3     std::numeric_limits<long double>::digits10 7     std::numeric_limits<long double>::max_digits10 7     std::numeric_limits<long double>::max_exponent 9    4std::numeric_limits<long double>::max_exponent10 9    �黶td::numeric_limits<long double>::min_exponent ;    �威std::numeric_limits<long double>::min_exponent10  �    FEATURE_OBJECT_CACHING  �   FEATURE_ZONE_ELEVATION  �   FEATURE_MIME_HANDLING  �   FEATURE_MIME_SNIFFING $ �   FEATURE_WINDOW_RESTRICTIONS & �   FEATURE_WEBOC_POPUPMANAGEMENT  �   FEATURE_BEHAVIORS $ �   FEATURE_DISABLE_MK_PROTOCOL & �   FEATURE_LOCALMACHINE_LOCKDOWN  �  	 FEATURE_SECURITYBAND ( �  
 FEATURE_RESTRICT_ACTIVEXINSTALL & �   FEATURE_VALIDATE_NAVIGATE_URL & �   FEATURE_RESTRICT_FILEDOWNLOAD ! �  
 FEATURE_ADDON_MANAGEMENT " �   FEATURE_PROTOCOL_LOCKDOWN / �   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE " �   FEATURE_SAFE_BINDTOOBJECT # �   FEATURE_UNC_SAVEDFILECHECK / �   FEATURE_GET_URL_DOM_FILEPATH_UNENCODED   �   FEATURE_TABBED_BROWSING  �   FEATURE_SSLUX * �   FEATURE_DISABLE_NAVIGATION_SOUNDS + �   FEATURE_DISABLE_LEGACY_COMPRESSION & �   FEATURE_FORCE_ADDR_AND_STATUS  �   FEATURE_XMLHTTP ( �   FEATURE_DISABLE_TELNET_PROTOCOL  �   FEATURE_FEEDS $ �   FEATURE_BLOCK_INPUT_PROMPTS .    std::integral_constant<bool,1>::value / �    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION - �   D3D12_RESOURCE_BARRIER_TYPE_ALIASING 3 J    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS C J   D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS E J   D3D12_DEBUG_DEVICE_PARAMETER_GPU_SLOWDOWN_PERFORMANCE_FACTOR  �    URLZONE_LOCAL_MACHINE  �   URLZONE_INTRANET  �   URLZONE_TRUSTED  �   URLZONE_INTERNET : L    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE I L   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY J L   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION H L   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION  �    URLZONEREG_DEFAULT  �   URLZONEREG_HKLM 8 �    D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_DISABLED B �   D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_WHEN_HASH_BYPASSED 3 �    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED - �   D3D12_MESSAGE_CATEGORY_MISCELLANEOUS . �   D3D12_MESSAGE_CATEGORY_INITIALIZATION ' �   D3D12_MESSAGE_CATEGORY_CLEANUP + �   D3D12_MESSAGE_CATEGORY_COMPILATION . �   D3D12_MESSAGE_CATEGORY_STATE_CREATION - �   D3D12_MESSAGE_CATEGORY_STATE_SETTING - �   D3D12_MESSAGE_CATEGORY_STATE_GETTING 5 �   D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION ) �  	 D3D12_MESSAGE_CATEGORY_EXECUTION * �    D3D12_MESSAGE_SEVERITY_CORRUPTION % �   D3D12_MESSAGE_SEVERITY_ERROR ' �   D3D12_MESSAGE_SEVERITY_WARNING $ �   D3D12_MESSAGE_SEVERITY_INFO # �   BINDHANDLETYPES_DEPENDENCY  �    TKIND_ENUM  �   TKIND_RECORD  �   TKIND_MODULE  �   TKIND_INTERFACE  �   TKIND_DISPATCH  �   TKIND_COCLASS  �   TKIND_ALIAS  �   TKIND_UNION  �    PIDMSI_STATUS_NORMAL  �   PIDMSI_STATUS_NEW  �   PIDMSI_STATUS_PRELIM  �   PIDMSI_STATUS_DRAFT ! �   PIDMSI_STATUS_INPROGRESS  �   PIDMSI_STATUS_EDIT  �   PIDMSI_STATUS_REVIEW  �   PIDMSI_STATUS_PROOF  �   CC_CDECL  �   CC_MSCPASCAL  �   CC_PASCAL  �   CC_MACPASCAL  �   CC_STDCALL  �   CC_FPFASTCALL  �   CC_SYSCALL  �   CC_MPWCDECL  �   CC_MPWPASCAL  �    FUNC_VIRTUAL  �   FUNC_PUREVIRTUAL  �   FUNC_NONVIRTUAL  �   FUNC_STATIC A '   std::allocator<char>::_Minimum_asan_allocation_alignment  �    VAR_PERINSTANCE  �   VAR_STATIC  �   VAR_CONST : '    std::integral_constant<unsigned __int64,0>::value ? '   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A '   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L '   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X '   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z '   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e '   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e '   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a '    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ '    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy T '   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos - �   std::_Invoker_pmd_pointer::_Strategy  �    DESCKIND_NONE  �   DESCKIND_FUNCDESC  �   DESCKIND_VARDESC  �   DESCKIND_TYPECOMP   �   DESCKIND_IMPLICITAPPOBJ / u    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV + u   D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER ' u   D3D12_DESCRIPTOR_HEAP_TYPE_RTV ' u   D3D12_DESCRIPTOR_HEAP_TYPE_DSV 3 8  �D3D12_MESSAGE_ID_BYTECODE_VALIDATION_ERROR ( <    D3D12_DESCRIPTOR_RANGE_TYPE_SRV ( <   D3D12_DESCRIPTOR_RANGE_TYPE_UAV ( <   D3D12_DESCRIPTOR_RANGE_TYPE_CBV 3 �    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE 2 �   D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS & �   D3D12_ROOT_PARAMETER_TYPE_CBV & �   D3D12_ROOT_PARAMETER_TYPE_SRV 4 �    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK / �   D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK / �   D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE 4 �   D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK_UINT  R    SYS_WIN16  R   SYS_WIN32  R   SYS_MAC D '   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment ' '  	�%#"勪滘藄td::_FNV_offset_basis   '  
��     std::_FNV_prime  �    CHANGEKIND_ADDMEMBER   �   CHANGEKIND_DELETEMEMBER  �   CHANGEKIND_SETNAMES $ �   CHANGEKIND_SETDOCUMENTATION  �   CHANGEKIND_GENERAL  �   CHANGEKIND_INVALIDATE   �   CHANGEKIND_CHANGEFAILED B '   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D '   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O '   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity :     std::_Floating_type_traits<float>::_Mantissa_bits :     std::_Floating_type_traits<float>::_Exponent_bits D     std::_Floating_type_traits<float>::_Maximum_binary_exponent E     �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent :     std::_Floating_type_traits<float>::_Exponent_bias 7     std::_Floating_type_traits<float>::_Sign_shift ;     std::_Floating_type_traits<float>::_Exponent_shift : %  � std::_Floating_type_traits<float>::_Exponent_mask E %  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G %  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask a '   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE J %  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask c '   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask B %  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask n '   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity F %  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask n '  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j '    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h '    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size ;    5 std::_Floating_type_traits<double>::_Mantissa_bits ;     std::_Floating_type_traits<double>::_Exponent_bits E    �std::_Floating_type_traits<double>::_Maximum_binary_exponent G    �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ;    �std::_Floating_type_traits<double>::_Exponent_bias 8    ? std::_Floating_type_traits<double>::_Sign_shift <    4 std::_Floating_type_traits<double>::_Exponent_shift ; '  �std::_Floating_type_traits<double>::_Exponent_mask J '  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L '  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O '  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G '  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K '  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] '   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos   �   �
  ? 
�        D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::s_FormatDetail = 
%        D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::s_NumFormats > 
s        D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::s_FormatNames * {    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW 2 {   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED . {   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH 8 {   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW 7 {   D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW . {   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT : {   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW : {   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW ; {   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW 3 {  	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS 3 {  
 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_MESH ! �    COINITBASE_MULTITHREADED E '   std::allocator<char16_t>::_Minimum_asan_allocation_alignment ' �  �   CLSCTX_ACTIVATE_X86_SERVER C '   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E '   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P '   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d '   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f '   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q '   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q '  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m '    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k '    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size  �   COR_VERSION_MAJOR_V2 ` '   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos : '   std::integral_constant<unsigned __int64,2>::value E '   std::allocator<char32_t>::_Minimum_asan_allocation_alignment C '   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E '   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P '   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d '   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f '   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q '   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q '   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m '    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k '    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size  �    DVEXTENT_CONTENT ` '   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos $ X    D3D12_LIFETIME_STATE_IN_USE , �   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL 8     std::_False_trivial_cat::_Bitcopy_constructible 5     std::_False_trivial_cat::_Bitcopy_assignable  �   4   2 �   D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2 �      �    DXGI_FORMAT_UNKNOWN * �   DXGI_FORMAT_R32G32B32A32_TYPELESS ' �   DXGI_FORMAT_R32G32B32A32_FLOAT & �   DXGI_FORMAT_R32G32B32A32_UINT & �   DXGI_FORMAT_R32G32B32A32_SINT ' �   DXGI_FORMAT_R32G32B32_TYPELESS $ �   DXGI_FORMAT_R32G32B32_FLOAT # �   DXGI_FORMAT_R32G32B32_UINT # �   DXGI_FORMAT_R32G32B32_SINT * �  	 DXGI_FORMAT_R16G16B16A16_TYPELESS ' �  
 DXGI_FORMAT_R16G16B16A16_FLOAT ' �   DXGI_FORMAT_R16G16B16A16_UNORM & �   DXGI_FORMAT_R16G16B16A16_UINT ' �  
 DXGI_FORMAT_R16G16B16A16_SNORM & �   DXGI_FORMAT_R16G16B16A16_SINT $ �   DXGI_FORMAT_R32G32_TYPELESS ! �   DXGI_FORMAT_R32G32_FLOAT   �   DXGI_FORMAT_R32G32_UINT   �   DXGI_FORMAT_R32G32_SINT & �   DXGI_FORMAT_R32G8X24_TYPELESS ) �   DXGI_FORMAT_D32_FLOAT_S8X24_UINT - �   DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS , �   DXGI_FORMAT_X32_TYPELESS_G8X24_UINT ) �   DXGI_FORMAT_R10G10B10A2_TYPELESS & �   DXGI_FORMAT_R10G10B10A2_UNORM % �   DXGI_FORMAT_R10G10B10A2_UINT $ �   DXGI_FORMAT_R11G11B10_FLOAT & �   DXGI_FORMAT_R8G8B8A8_TYPELESS # �   DXGI_FORMAT_R8G8B8A8_UNORM ( �   DXGI_FORMAT_R8G8B8A8_UNORM_SRGB " �   DXGI_FORMAT_R8G8B8A8_UINT # �   DXGI_FORMAT_R8G8B8A8_SNORM " �    DXGI_FORMAT_R8G8B8A8_SINT $ �  ! DXGI_FORMAT_R16G16_TYPELESS ! �  " DXGI_FORMAT_R16G16_FLOAT ! �  # DXGI_FORMAT_R16G16_UNORM   �  $ DXGI_FORMAT_R16G16_UINT ! �  % DXGI_FORMAT_R16G16_SNORM   �  & DXGI_FORMAT_R16G16_SINT ! �  ' DXGI_FORMAT_R32_TYPELESS  �  ( DXGI_FORMAT_D32_FLOAT  �  ) DXGI_FORMAT_R32_FLOAT  �  * DXGI_FORMAT_R32_UINT  �  + DXGI_FORMAT_R32_SINT # �  , DXGI_FORMAT_R24G8_TYPELESS & �  - DXGI_FORMAT_D24_UNORM_S8_UINT * �  . DXGI_FORMAT_R24_UNORM_X8_TYPELESS ) �  / DXGI_FORMAT_X24_TYPELESS_G8_UINT " �  0 DXGI_FORMAT_R8G8_TYPELESS  �  1 DXGI_FORMAT_R8G8_UNORM  �  2 DXGI_FORMAT_R8G8_UINT  �  3 DXGI_FORMAT_R8G8_SNORM  �  4 DXGI_FORMAT_R8G8_SINT ! �  5 DXGI_FORMAT_R16_TYPELESS  �  6 DXGI_FORMAT_R16_FLOAT  �  7 DXGI_FORMAT_D16_UNORM  �  8 DXGI_FORMAT_R16_UNORM  �  9 DXGI_FORMAT_R16_UINT  �  : DXGI_FORMAT_R16_SNORM  �  ; DXGI_FORMAT_R16_SINT   �  < DXGI_FORMAT_R8_TYPELESS  �  = DXGI_FORMAT_R8_UNORM  �  > DXGI_FORMAT_R8_UINT  �  ? DXGI_FORMAT_R8_SNORM  �  @ DXGI_FORMAT_R8_SINT  �  A DXGI_FORMAT_A8_UNORM  �  B DXGI_FORMAT_R1_UNORM ' �  C DXGI_FORMAT_R9G9B9E5_SHAREDEXP $ �  D DXGI_FORMAT_R8G8_B8G8_UNORM $ �  E DXGI_FORMAT_G8R8_G8B8_UNORM ! �  F DXGI_FORMAT_BC1_TYPELESS  �  G DXGI_FORMAT_BC1_UNORM  `        D3DFCS_UNKNOWN # �  H DXGI_FORMAT_BC1_UNORM_SRGB ! �  I DXGI_FORMAT_BC2_TYPELESS   �        D3DFCS_R32G32B32A32  �        D3DFCS_R32G32B32  �  J DXGI_FORMAT_BC2_UNORM # �  K DXGI_FORMAT_BC2_UNORM_SRGB   �        D3DFCS_R16G16B16A16  �        D3DFCS_R32G32 ! �  L DXGI_FORMAT_BC3_TYPELESS  �        D3DFCS_R32G8X24  �  M DXGI_FORMAT_BC3_UNORM  �        D3DFCS_R11G11B10 # �  N DXGI_FORMAT_BC3_UNORM_SRGB ! �  O DXGI_FORMAT_BC4_TYPELESS  �        D3DFCS_R8G8B8A8  �  P DXGI_FORMAT_BC4_UNORM  �        D3DFCS_R16G16  �  Q DXGI_FORMAT_BC4_SNORM  ^        D3DFCS_R32 ! �  R DXGI_FORMAT_BC5_TYPELESS  �        D3DFCS_R24G8  �  S DXGI_FORMAT_BC5_UNORM  ^        D3DFCS_R8G8  �        D3DFCS_R16  �  T DXGI_FORMAT_BC5_SNORM ! �  U DXGI_FORMAT_B5G6R5_UNORM  ^        D3DFCS_R8 # �  V DXGI_FORMAT_B5G5R5A1_UNORM  �        D3DFCS_A8  �        D3DFCS_R1 # �  W DXGI_FORMAT_B8G8R8A8_UNORM  �        D3DFCS_R9G9B9E5 # �  X DXGI_FORMAT_B8G8R8X8_UNORM / �  Y DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM  �        D3DFCS_R8G8_B8G8 & �  Z DXGI_FORMAT_B8G8R8A8_TYPELESS  �        D3DFCS_G8R8_G8B8  �        D3DFCS_BC1 ( �  [ DXGI_FORMAT_B8G8R8A8_UNORM_SRGB  �        D3DFCS_BC2 & �  \ DXGI_FORMAT_B8G8R8X8_TYPELESS ( �  ] DXGI_FORMAT_B8G8R8X8_UNORM_SRGB  �        D3DFCS_BC3 " �  ^ DXGI_FORMAT_BC6H_TYPELESS  �        D3DFCS_BC4  �        D3DFCS_BC5  �  _ DXGI_FORMAT_BC6H_UF16  �        D3DFCS_B5G6R5  �  ` DXGI_FORMAT_BC6H_SF16  �        D3DFCS_B5G5R5A1 ! �  a DXGI_FORMAT_BC7_TYPELESS  �  b DXGI_FORMAT_BC7_UNORM  �        D3DFCS_B8G8R8A8  �        D3DFCS_B8G8R8X8 # �  c DXGI_FORMAT_BC7_UNORM_SRGB  �        D3DFCS_R10G10B10A2  �  d DXGI_FORMAT_AYUV  �        D3DFCS_BC6H  �  e DXGI_FORMAT_Y410  �        D3DFCS_BC7  �  f DXGI_FORMAT_Y416  �  g DXGI_FORMAT_NV12  �        D3DFCS_AYUV  �        D3DFCS_NV12  �  h DXGI_FORMAT_P010  �  i DXGI_FORMAT_P016  �        D3DFCS_YUY2  �  j DXGI_FORMAT_420_OPAQUE  �        D3DFCS_P010  �        D3DFCS_P016  �  k DXGI_FORMAT_YUY2  �        D3DFCS_NV11  �  l DXGI_FORMAT_Y210 ' '   std::_Big_allocation_threshold  �        D3DFCS_420_OPAQUE  �  m DXGI_FORMAT_Y216  �  n DXGI_FORMAT_NV11  �        D3DFCS_Y410 ' '    std::_Big_allocation_alignment  '  ' std::_Non_user_size  �  o DXGI_FORMAT_AI44  �        D3DFCS_Y416  �        D3DFCS_Y210  �  p DXGI_FORMAT_IA44  �        D3DFCS_Y216 . '  	�std::_Big_allocation_sentinel  �  q DXGI_FORMAT_P8  �  r DXGI_FORMAT_A8P8  �        D3DFCS_AI44 # �  s DXGI_FORMAT_B4G4R4A4_UNORM  �        D3DFCS_IA44  �  � DXGI_FORMAT_P208  �        D3DFCS_P8  �        D3DFCS_A8P8  �  � DXGI_FORMAT_V208  �        D3DFCS_B4G4R4A4  �  � DXGI_FORMAT_V408 4 �  � DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE  �        D3DFCS_P208  �        D3DFCS_V208 < �  � DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE # �  � DXGI_FORMAT_A4B4G4R4_UNORM  �        D3DFCS_V408  �        D3DFCS_A4B4G4R4   �    D3D_DRIVER_TYPE_UNKNOWN ! �   D3D_DRIVER_TYPE_HARDWARE " �   D3D_DRIVER_TYPE_REFERENCE  �   D3D_DRIVER_TYPE_NULL ! �   D3D_DRIVER_TYPE_SOFTWARE & �   D3D_FEATURE_LEVEL_1_0_GENERIC # �   D3D_FEATURE_LEVEL_1_0_CORE ! �  � 燚3D_FEATURE_LEVEL_10_0  !  LPPARAMDESCEX  �  FUNCKIND  %  tagPARAMDESCEX  #  PARAMDESC  #  tagPARAMDESC    tagARRAYDESC  �  CALLCONV  �  DESCKIND  t   int32_t  �  ELEMDESC    BINDPTR    tagFUNCDESC  t  INVOKEKIND  �  TLIBATTR    tagBINDPTR  �  tagSTATSTG  �  tagTYPEDESC    FUNCDESC  "   HREFTYPE  R  SYSKIND  �  tagVARDESC  �  TYPEKIND    IEnumSTATSTG  �  STATSTG  �  ITypeComp  �  TYPEDESC  �  IDLDESC  �  tagELEMDESC  �  tagIDLDESC  L  VARIANTARG  �  EXCEPINFO  �  tagEXCEPINFO 
    DISPID     MEMBERID  u   uint32_t  �  _CatchableType  u   UINT ' q  D3D12_BACKGROUND_PROCESSING_MODE  �  D3D12_BARRIER_LAYOUT  L  tagCAUL  �  tagTLIBATTR  �  _TP_CALLBACK_PRIORITY " �  _s__RTTIBaseClassDescriptor ? k  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 �  __vcrt_va_list_is_reference<char const * const>  �  D3D_PRIMITIVE_TOPOLOGY  �  tagShutdownType  q   OLECHAR G t  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �  D3D_FEATURE_LEVEL  �  tagCABSTR  �  D3D12_BARRIER_TYPE  �  tagCALLCONV  �  tagTYPEKIND   �  D3D12_STATIC_BORDER_COLOR  L  VARIANT  =  ISequentialStream  �  BSTRBLOB  #   rsize_t  �  _D3D_INCLUDE_TYPE  �  TYPEATTR     VARIANT_BOOL - �  __vc_attributes::event_sourceAttribute 9 �  __vc_attributes::event_sourceAttribute::optimize_e 5 �  __vc_attributes::event_sourceAttribute::type_e > �  __vc_attributes::helper_attributes::v1_alttypeAttribute F �  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 �  __vc_attributes::helper_attributes::usageAttribute B �  __vc_attributes::helper_attributes::usageAttribute::usage_e * �  __vc_attributes::threadingAttribute 7 �  __vc_attributes::threadingAttribute::threading_e - �  __vc_attributes::aggregatableAttribute 5 �  __vc_attributes::aggregatableAttribute::type_e / �  __vc_attributes::event_receiverAttribute 7 �  __vc_attributes::event_receiverAttribute::type_e ' �  __vc_attributes::moduleAttribute / �  __vc_attributes::moduleAttribute::type_e & �  $_TypeDescriptor$_extraBytes_23 
 �  PUWSTR ( �  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  �  AR_STATE  g  tagCADBL  �  VARKIND 3 L  D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE  ~  _TypeDescriptor ! u  D3D12_DESCRIPTOR_HEAP_TYPE  �  _tagPSUACTION 
 i  tagDEC  o  CALPSTR     LONG_PTR  �  tagBINDSTRING   �  _D3D_SHADER_VARIABLE_TYPE ! �  _D3D_SHADER_VARIABLE_CLASS  �  tagCACLIPDATA  #   ULONG_PTR " �  D3D12_RESOURCE_BARRIER_TYPE % �  _s__RTTICompleteObjectLocator2 " <  D3D12_DESCRIPTOR_RANGE_TYPE  �  tagURLZONE  �  PUWSTR_C  �  PTP_CLEANUP_GROUP  �  __MIDL_ICodeInstall_0001  p  PCHAR  �  tagBINDSTATUS  H  _GUID  �  _URLZONEREG  o  _LARGE_INTEGER ' r  _LARGE_INTEGER::<unnamed-type-u>  s  D3D12_COLOR_WRITE_ENABLE  �  CLIPDATA  [  CAFILETIME  o  tagCALPSTR  A  CALPWSTR 
 �  CAL  �  tagCABSTRBLOB  �  tagSAFEARRAYBOUND  f  tagCAFLT A m  __vcrt_va_list_is_reference<__crt_locale_pointers * const> 
 c  tagCAH  i  DECIMAL  �  tagCAUI  !   WORD  �  _s__CatchableType  �  CAUH  �  D3D_NAME  �  tagCADATE  �  D3D_SHADER_MODEL  g  CADBL  -  LPCOLESTR  �  PCUWSTR  T  CAPROPVARIANT  f  CAFLT  �  DXGI_FORMAT  #   uint64_t ' �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS 9 d  __vcrt_va_list_is_reference<wchar_t const * const>  �  _USER_ACTIVITY_PRESENCE E \  __vcrt_assert_va_start_is_not_reference<wchar_t const * const>    PLONG & �  $_TypeDescriptor$_extraBytes_20  �  DISPPARAMS  N  _FILETIME $ �  D3D12_LINE_RASTERIZATION_MODE  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16      BYTE . ]  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE % �  D3D12_RAYTRACING_GEOMETRY_TYPE 
 -  PCWSTR  [  IStream ? �  std::_Default_allocator_traits<std::allocator<wchar_t> >  N  std::_Lockit " �  std::_Char_traits<char,int>  �  std::_Num_base )   std::_Narrow_char_traits<char,int>  �  std::hash<float>  �  std::_Num_int_base  �  std::float_denorm_style 6 '  std::allocator_traits<std::allocator<wchar_t> >   �  std::_Rand_urng_from_func " �  std::numeric_limits<double> ( �  std::_Basic_container_proxy_ptr12  �  std::_Num_float_base  �  std::logic_error ! %  std::char_traits<char32_t>   �  std::numeric_limits<bool> # e  std::_WChar_traits<char16_t> T &  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   }  std::_Fake_proxy_ptr_impl * �  std::numeric_limits<unsigned short>  V  std::overflow_error   #  std::char_traits<wchar_t>  !  std::false_type  �  std::float_round_style  b  std::string , �  std::numeric_limits<unsigned __int64> $ �  std::numeric_limits<char16_t> %   std::integral_constant<bool,1>     std::_Leave_proxy_unbound h   std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1>  ^  std::_Iterator_base12 @ o  std::_Default_allocator_traits<std::allocator<char32_t> >    std::allocator<char32_t> 6 �  std::_String_val<std::_Simple_types<char32_t> > = �  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` {  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>  �  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l f  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy # �  std::numeric_limits<wchar_t>    std::_Container_base0  �  std::hash<double> / A  std::_Char_traits<char32_t,unsigned int> % !  std::integral_constant<bool,0>  C  std::bad_exception & "  std::_Zero_then_variadic_args_t  �  std::u32string    std::_Fake_allocator  �  std::invalid_argument    std::length_error ! �  std::numeric_limits<float>  �  std::exception_ptr $ �  std::numeric_limits<char32_t>    std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l .  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7   std::allocator_traits<std::allocator<char32_t> >    std::_Iterator_base0 1 V  std::_Char_traits<char16_t,unsigned short> !   std::char_traits<char16_t>  6  std::_Container_base12 ) �  std::numeric_limits<unsigned char>    std::true_type   �  std::numeric_limits<long> "   std::initializer_list<char>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits 3 
  std::allocator_traits<std::allocator<char> > ! �  std::numeric_limits<short> 6 �  std::_String_val<std::_Simple_types<char16_t> > = �  std::_String_val<std::_Simple_types<char16_t> >::_Bxty  Y  std::bad_alloc  m  std::underflow_error  (  std::out_of_range # �  std::numeric_limits<__int64> f ]  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> < �  std::_Default_allocator_traits<std::allocator<char> >  ?  std::runtime_error   o  std::bad_array_new_length  %  std::_Container_proxy  �  std::u16string  �  std::nested_exception  �  std::_Distance_unknown ( �  std::numeric_limits<unsigned int> K b  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff &   std::initializer_list<char32_t> & �  std::initializer_list<char16_t> % �  std::initializer_list<wchar_t>   �  std::hash<std::nullptr_t> ' �  std::numeric_limits<long double>  �  std::range_error  5  std::allocator<char16_t> * �  std::_String_constructor_concat_tag  r  std::allocator<char>    std::nullptr_t ) �  std::numeric_limits<unsigned long>  &  std::wstring ' �  std::numeric_limits<signed char>  �  std::domain_error  S  std::allocator<wchar_t>   �  std::numeric_limits<char>  �  std::char_traits<char>  �  std::_Unused_parameter h ?  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> @ x  std::_Default_allocator_traits<std::allocator<char16_t> > 0   std::_Char_traits<wchar_t,unsigned short> 5 �  std::_String_val<std::_Simple_types<wchar_t> > < �  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty " +  std::_WChar_traits<wchar_t>  �  std::streampos 7 �  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers  �  std::numeric_limits<int> 2 �  std::_String_val<std::_Simple_types<char> > 9 �  std::_String_val<std::_Simple_types<char> >::_Bxty  �  std::bad_variant_access  �  CACLIPDATA  �  VARDESC     LONG  �  ITypeLib  J  tagCACY  �  tagBSTRBLOB  �  tagCAUH  �  _TP_CALLBACK_ENVIRON_V3 0 �  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> B �  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s>  H  _ULARGE_INTEGER ( �  _ULARGE_INTEGER::<unnamed-type-u>  F  LPVARIANT  �  SAFEARRAY  �  D3D_SRV_DIMENSION  �  tagCABOOL   �  __RTTIBaseClassDescriptor  �  D3D12_SHADER_CACHE_MODE  :  tagBLOB 
 �  CABOOL   �  D3D12_ROOT_PARAMETER_TYPE 
    _off_t  #   ULONG64 
 0  SNB  �  _tagINTERNETFEATURELIST  �  CABSTRBLOB 
 #   SIZE_T  �  tagTYPEATTR  �  stat 
 !   _ino_t  A   DATE # �  ReplacesCorHdrNumericDefines 
 #   UINT64  "   DWORD 
 :  LPCSTR  �  PTP_CALLBACK_INSTANCE 
   PSHORT  �  D3D12_MEMCPY_DEST  8  D3D12_MESSAGE_ID  "   TP_VERSION      UINT8  q  BSTR  �  D3D_DRIVER_TYPE  �  CAUB  �  ITypeInfo  D  tagPROPVARIANT  L  CAUL M X  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  J  CACY  �  _Mbstatet  H  ULARGE_INTEGER  �  TP_CALLBACK_PRIORITY    _locale_t B }  __vcrt_assert_va_start_is_not_reference<char const * const> ; E  __vcrt_va_list_is_reference<__crt_locale_pointers *>  �  VARENUM  �  tagCASCODE # `  D3D_TESSELLATOR_PARTITIONING  r  terminate_handler  �  _s__RTTIBaseClassArray  �  tagCACLSID  �  MACHINE_ATTRIBUTES  �  D3D_RESOURCE_RETURN_TYPE 
    ldiv_t  A  tagCALPWSTR  :  BLOB  #   DWORD64  !   PROPVAR_PAD1 - �  $_s__RTTIBaseClassArray$_extraBytes_24  �  PTP_SIMPLE_CALLBACK  �  D3D12_MESSAGE_CATEGORY 
 t   INT  �  _CatchableTypeArray  6  IStorage  L  tagVARIANT 
 �  tagCAI 
 A   DOUBLE      UCHAR   �  _D3D_SHADER_CBUFFER_FLAGS  "   LCID      BOOLEAN  �  PTP_CALLBACK_ENVIRON     ptrdiff_t  �  tagTYSPEC  $  LPVERSIONEDSTREAM  �  _stat64i32  �  D3D12_LOGIC_OP  �  tagDISPPARAMS 
 !   USHORT ) �  D3D12_PROPERTY_LAYOUT_FORMAT_TABLE 8 �  D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FORMAT_DETAIL   �  D3D10_SB_4_COMPONENT_NAME  �  _PMD  �  LPUWSTR  �  tagVARKIND    PVOID  �  SAFEARRAYBOUND ' h  _s__RTTIClassHierarchyDescriptor    IUnknown  t   errno_t  q   WCHAR     PBYTE  �  D3D_TESSELLATOR_DOMAIN 
 �  tagCAC  �  tagCAUB    _lldiv_t 
 H  IID ! H  _D3D_SHADER_VARIABLE_FLAGS  �  _tagQUERYOPTION  q  LPOLESTR  �  D3D12_RESOURCE_DIMENSION  �  D3D_PRIMITIVE  �  D3D12_TILE_SHAPE  �  tagExtentMode  �  __MIDL_IUri_0002     HRESULT  �  _D3D_SHADER_INPUT_TYPE 
 �  CAI & �  $_TypeDescriptor$_extraBytes_27  �  CASCODE  �  _s__ThrowInfo /   D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER ! �  __MIDL_IGetBindHandle_0001  P  tagCY 
    LONG64  �  tagCOINITBASE  �  LPCUWSTR  "   ULONG  �  __RTTIBaseClassArray ! �  D3D12_STATE_SUBOBJECT_TYPE  !   VARTYPE  t   BOOL 
 �  CAC    __crt_locale_data_public  �  tagApplicationType  -  LPCWSTR & �  DISPLAYCONFIG_SCANLINE_ORDERING - D  $_s__CatchableTypeArray$_extraBytes_24  @   FLOAT  !   PROPVAR_PAD3  �  tagDOMNodeType  �  CAUI  �  tagCLIPDATA  �  tagSAFEARRAY % h  __RTTIClassHierarchyDescriptor  �  tagVersionedStream 
 �  CABSTR     __time64_t  �  tagCHANGEKIND 
 u   UINT32  P  FILE  R  tagSYSKIND 2 �  D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_MODE 3 �  __vcrt_va_list_is_reference<wchar_t const *>  �  IDispatch  H  CLSID  �  mbstate_t  �  _PMFN  #   uintptr_t 
 q  LPWSTR  D  PROPVARIANT  &  LPSAFEARRAY  #   UINT_PTR  �  PTP_POOL  �  _s__CatchableTypeArray  H  GUID * �  _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG ' }  D3D_TESSELLATOR_OUTPUT_PRIMITIVE # {  D3D12_INDIRECT_ARGUMENT_TYPE  =  D3D12_COMMAND_LIST_TYPE  �  TP_CALLBACK_ENVIRON_V3  �  tagFUNCKIND  o  LARGE_INTEGER 
 c  CAH  t   INT32  [  tagCAFILETIME 
   HANDLE  X  D3D12_LIFETIME_STATE  �  PIDMSI_STATUS_VALUE  V  _D3D_CBUFFER_TYPE  #   ULONGLONG  T  tagCAPROPVARIANT ( �  PTP_CLEANUP_GROUP_CANCEL_CALLBACK 	 P  CY  N  FILETIME   �  D3D_FORMAT_COMPONENT_NAME ( J  D3D12_DEBUG_DEVICE_PARAMETER_TYPE  �  D3D_FORMAT_LAYOUT  F  __MIDL_IUri_0001 
   REGCLS  �  D3D_FORMAT_TYPE_LEVEL  :  IRecordInfo 
 #   size_t 
    time_t     LONGLONG   �  D3D12_MEASUREMENTS_ACTION    __std_exception_data * �  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE 
 u   _dev_t  �  tagGLOBALOPT_EH_VALUES * �  PCUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG  r  unexpected_handler    lldiv_t     SHORT    PLONG64     _ldiv_t  	  COWAIT_FLAGS     SCODE  �  tagCLSCTX     INT_PTR  �  _D3D_SHADER_INPUT_FLAGS  �  tagXMLEMEM_TYPE " �  D3D_REGISTER_COMPONENT_TYPE 
 P  _iobuf * �  D3D_FORMAT_COMPONENT_INTERPRETATION 
 �  CADATE  p   CHAR  �  CACLSID  !   PROPVAR_PAD2  �  _tagPARSEACTION  �  D3D12_MESSAGE_SEVERITY + �  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE  p  LPSTR  �  tagDESCKIND    __crt_locale_pointers 
 �  tagCAL �   P      �0�*е彗9釗獳+U叅[4椪 P"��  <    仐嚫+'連Gwa錬貎襅.侕*岋N纆肏  �    �=蔑藏鄌�
艼�(YWg懀猊	*)  �    谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS     o藾錚\F鄦泭|嚎醖b&惰�_槮  U   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   qAp�6敁p銋�,c .諵輕底髫L灇	9�  !   匐衏�$=�"�3�a旬SY�
乢�骣�  k   悯R痱v 瓩愿碀"禰J5�>xF痧  �   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  :   矨�陘�2{WV�y紥*f�u龘��  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  U   +4[(広
倬禼�溞K^洞齹誇*f�5  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  <   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �   �7穲碶⒖鍉鸻�:怉婤莞b=竱�  �   +椬恡�
	#G許�/G候Mc�蜀煟-     �:2K] �
j�苊赁e�
湿�3k椨�  `   樸7 忁�珨��3]"Fキ�:�,郩�  �   覽s鴧罪}�'v,�*!�
9E汲褑g;  �   6觏v畿S倂9紵"�%��;_%z︹  <    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   掴'圭,@H4sS裬�!泉:莠й�"fE)  �   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  #   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  s   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  	   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  ]	   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �	   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �	   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  :
   L錁鞾啂�JB媥潉�Z傶oKW榮忷霜�  �
   sL&%�znOdz垗�M,�:吶1B滖  �
   �(M↙溋�
q�2,緀!蝺屦碄F觡  -   G�膢刉^O郀�/耦��萁n!鮋W VS  l   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   �-�雧n�5L屯�:I硾�鮎访~(梱  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  7   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  u   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �   繃S,;fi@`騂廩k叉c.2狇x佚�  
   �"睱建Bi圀対隤v��cB�'窘�n  `
   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �
   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �
   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  K   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  �   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   �n儹`
舔�	Y氀�:b
#p:  4   "�挨	b�'+舒�5<O�呱_歲+/�P�?  }   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠     嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   i   .'�逫S��-6鸋��_晝罗橯獗嶔�#�+  �   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�     d潣7熈[$袎o�懠I殑Iy厵唫嬎�  W   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   衠琪槡铟钭}_XO>�蛭X�7Mp处d  �   �~鴧傳.P怬WsP-"焫#N�:�&場璁  .   �fwv鋽砻毆�經�⒂k秼芴襚扉w  w   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  
   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  J   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  �   5睔`&N_鏃|�<�$�獖�!銸]}"  �   ┫緞A$窄�0� NG�%+�*�
!7�=b  1   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  y   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  �   �儔14褥緅�3]饃鹷�hK3g搋bA竑     I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  n   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  H   5�\營	6}朖晧�-w氌rJ籠騳榈  �   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  �   豊+�丟uJo6粑'@棚荶v�g毩笨C      欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  u   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7     ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  F   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   綔)\�谑U⒊磒'�!W磼B0锶!;  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  &   E縄�7�g虩狱呂�/y蛨惏l斋�笵  s   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/     �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  R   �呾��+h7晃O枖��*谵|羓嗡捬  �   2W瓓�<X	綧]�龐IE?'笼t唰��  �   窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  >   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  �   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  (   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  y   �F9�6K�v�/亅S诵]t婻F廤2惶I  �   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A     錵s铿勃砓b棬偡遯鮓尛�9泂惻  ]   �
bH<j峪w�/&d[荨?躹耯=�  �   $G\|R_熖泤煡4勄颧绖�?(�~�:  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  (   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  v   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  E   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  <    �5�-�塎To>我c撙侹�迫�蛷铕.~�!  �    _%1糠7硘籺蚻q5饶昈v纪嗈�  �    聤�苮g8鄞<aZ�%4)闪�|袉uh�  !   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  m!   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  �!   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  "   j轲P[塵5m榤g摏癭 鋍1O骺�*�  P"   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �"   bRè1�5捘:.z錨{娯啹}坬麺P  �"   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  *#   6��7@L�.�梗�4�檕�!Q戸�$�  t#   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �#   鹴y�	宯N卮洗袾uG6E灊搠d�  $   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  W$   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  �$   G髼*悭�2睆�侻皣軁舃裄樘珱)  �$   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  6%   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �%   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  �%   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  &   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  e&   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�  �&   戹�j-�99檽=�8熈讠鳖铮�  �&   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  E'   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  �'   D,y邥鞃黎v)�8%遾1�*8赩�婯�  �'   ,�<鈬獿鍢憁�g$��8`�"�  )(   傊P棼r铞
w爉筫y;H+(皈LL��7縮  v(   8蟴B或绢溵9"C dD揭鞧Vm5TB�  �(    d蜯�:＠T邱�"猊`�?d�B�#G騋  �(   鹰杩@坓!)IE搒�;puY�'i憷n!  F)   Eム聂�
C�?潗'{胿D'x劵;釱�  �)   溶�$椉�
悇� 騐`菚y�0O腖悘T  �)   0T砞獃钎藰�0逪喌I窐G(崹�  7*   齛|)3h�2%籨糜/N_燿C虺r_�9仌  �*   �fE液}髢V壥~�?"浬�^PEΡ4L�  �*   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  +   潝(綊r�*9�6}颞7V竅\剫�8値�#  `+   8�'预P�憖�0R�(3銖� pN*�  �+   �8��/X昋旒�.胱#h=J"髈篒go#  �+   ^憖�眜蘓�y冊日/缁ta铁6殔  F,   魯f�u覬n\��zx騖笹笾骊q*砎�,�  �,   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  �,   交�,�;+愱`�3p炛秓ee td�	^,  %-   _O縋[HU-銌�鼪根�鲋薺篮�j��  n-   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  �-   _臒~I��歌�0蘏嘺QU5<蝪祰S  �-   l籴靈LN~噾2u�< 嵓9z0iv&jザ  N.   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �.   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  �.   �>竎,~;庨麶}9皗.�"貂
荺hA圔  L/   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �/   毌-��
(襔  橥轃\|�!�!p牶F  �/   D���0�郋鬔G5啚髡J竆)俻w��  90   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  �0   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  �0   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  1    萾箒�$.潆�j閖i转pf-�稃陞��  c1   渐袿.@=4L笴速婒m瑜;_琲M %q�  �1   �#i匒U0/��%鷛1,爆簡n)瞰#謺狌�  
2   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  `2   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �2   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �2   RX鰷稐蒋駏U	�>�5妆癫�
8A/  H3   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �3   
罬}�(囫Ldh]僘l9-6牜I�.拾R欐佬  �3   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  74   �>2
^�﨟2W酟傲X{b?荼猲�;  v4   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �4   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �4   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  B5   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �5   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �5   n挕瀗麇�#q<営QVS蓃\轣雂6yp旈  56   �*o驑瓂a�(施眗9歐湬

�  }6    I嘛襨签.濟;剕��7啧�)煇9触�.  �6   ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  
7   閯�価=�<酛皾u漑O�髦jx`-�4睲�  V7    栀��綔&@�.�)�C�磍萘k  �7   c�#�'�縌殹龇D兺f�$x�;]糺z�  �7    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  98   d2軇L沼vK凔J!女計j儨杹3膦���  �8   r�L剟FsS鏴醼+E千I呯贄0鬬/�  �   0      K  �  ;   M  �  H   N  �  Y   x  �     �  �  <   �  �  J   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �     �  �  &  �  �  +  �  �  0  �  �  6  �  �  ]  �  �  j  �  �  �  �  �    �  �  :  �  �  q  �  �  x  �  �  �  �  �  �  �   �8   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleauto.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12sdklayers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\src\d3dx12_property_format_table.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ole2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3dx12_property_format_table.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3dcommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\D3D12TokenizedProgramFormat.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h  �       L�  -      -     
 悙      攼     
 褠  
    諓  
   
 �      �     
 龚  }   建  }  
 �  ~   "�  ~  
 @�     D�    
 ぉ  �   ī  �  
 譬  �   施  �  
 �  �   	�  �  
 C�  �   G�  �  
   �     �  
 瑾  �   飒  �  
 $�  �   (�  �  
 `�  �   d�  �  
 洬  �   煫  �  
 但  �   公  �  
 �  �   �  �  
 N�  �   R�  �  
 f�  �   j�  �  
 ，  �   К  �  
 �  �   �  �  
 ^�  �   b�  �  
 }�  �   伃  �  
 拉  �   沫  �  
 +�  �   /�  �  
 h�  �   l�  �  
 伄  �   叜  �  
 寒  �   井  �  
 霎  �     �  
 W�  �   [�  �  
 u�  �   y�  �  
 腐  �   集  �  
 舣  �     �  
 )�  �   -�  �  
 x�  �   |�  �  
 挵  �   柊  �  
 獍  �   姘  �  
 �  �   !�  �  
 7�  �   ;�  �  
 l�  �   p�  �  
 时  �   伪  �  
  �  �   $�  �  
 湶  �   牪  �  
 恫  �   翰  �  
 氩  �   锊  �  
 i�  �   m�  �  
 ǔ  �     �  
 莩  �   岢  �  
 醭  �     �  
 *�  �   .�  �  
 櫞  �   澊  �  
 炒  �   反  �  
 0�  �   4�  �  
 J�  �   N�  �  
    � M2蘱鰼臚哱騞箮�   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\Release\DirectX-Headers.pdb �                                        #I                              �   生                               �   旺$$                            �   旺		                             �   旺??                             `   蒢                               `   蚙$                            `   蚙	                             `   蚙?           	   	           @   生             
   	           @   旺$$             	           @   旺66             	           @   旺		           
   	           @   旺--             	           @   旺??                             @   蒍                               @   蚃$                             @   蚃	                              @   蚃?                            @   蒍                            @   岺                           @   MI                           @   璊                         


    生                           


    旺66   &                     


    旺		                         
     蚙$                            生                               旺66                           旺                            旺		                             旺--                             旺??           !   !                 蒍             "   !                 蚃$           #   !                 蚃6           $   !                 蚃	            %   !                 蚃-           &   !                 蚃?            '   '                   II             (   '                   
I           )   '                   MI           *   '                   MI            +   '                   MI            ,   ,                 蒍            -   ,                 岺    
       .   ,                 MI           /   ,                 璊           0   0                蒍             1   0                蚃6           2   0                蚃	            3   0                蚃-           4   0                蚃?            5   5                 II             6   5                 MI           7   5                 
I           8   5                 MI           9   5                 MI            :   5                 MI           ;   5                 MI            <   <                 II             =   <                 MI           >   <                 MI            ?   <                 MI           @   <                 MI            A   A                 -� 0          B   B                 MI           C   C                   蟌$$          D   D                  蟌6          E   E                  蟌6          F   F               @   D塌            G   F               @   D销66          H   F               @  D销2          I   I               �   D塌            J   I               �   D销66          K   I               �  D销2          L   L               �   D塌             M   L               �   D销66          N   L               �  D销2          O   O               @   DKI             P   O               @   DOI           Q   O               @   DOI           R   R               �   D薐             S   R               �   D螶6           T   R               �   D螶-           U   U               鞷6          V   V              眚66          W   Z               眚66          X   \               鞷6          Y              


    旺3          Z   Z               轵            [   Z              眚          \   \               镽            ]   \              鞷          ^   ^               �   D薢             _   ^               �   D蟌$          `   ^               �   D蟌$          a   a               �   D塌             b   a               �   D销66          c   a               �  D销2          d   d               眚66          e   e           


    眚66          f   f           @   眚66          g   g                  "OI           h   h                  "OI           i   i                  "OI           j   j                  "OI           k   k                  蟌6          l   l                   蟌6          m   m                   蟌6          n   n                  OI           o   o                  OI           p   p                  OI           q   q                  OI           r   r                  OI           s   s              眚66          t                       #I     @       u                       #I     @       v                       #I     @       w                       #I     @       x                       #I     @       y                       #I     @       z                       #I     @       {                       #I     @       |                       #I     @       }                       #I     @       ~                       #I     @                              #I     @       �                       #I     @       �                       #I     @       �   �                  OI           �   �                  !OI           �   �                  OI           �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                       #I     @       �                      OI            �                      OI            �   �              }�66           �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               	   
         
                                                                                        !   "   #   $   %   &           '   (   )   *   +       ,   -   .   /           0   1   2   3   4       5   6   7   8   9   :   ;       <   =   >   ?   @       A       B       C       D       E       F   G   H       I   J   K       L   M   N       O   P   Q       R   S   T       U       V       Z   W   [       \   X   ]                Y           ^   _   `       a   b   c       d       g       k       h       i       n       j       e       f       l       m       o       p       q       r       s       �       �       �       �          }   @   ~   h   ~   �   ~   �   ~   �           0     X     �  �   �  �   �  �   �  �      �   H  �   p  �   �  �   �  �   �  �     �   8  �   `  �   �  �   �  �   �  �      �   (  �   P  �   x  �   �  �   �  �   �  �     �   @  �   h  �   �  �   �  �   �  �     �   0  �   X  �   �  �   �  �   �  �   �  �      �   H  �   p  �   �  �   �  �   �  �     �   8  �   `  �   �  �   �  �   �  �    	  �   (	  �   P	  �   x	  �   �	  �   �	  �   �	  �   
  �   @
  �   h
  �   �
  �   �
  �   �
  �     �   0  �   X  �   �  �   �  �   �  �   �  �      �   H  �   p  �   �  �   �  �   �  �   
  �   8
  �   `
  �   �
  �   �
  �   �
  �      �   (  �   P  �   x  �   �  �   �  �   �  �     �   @  �   h  �   �  �   �  �   �  �     �   0  �   X  �   �  �   �  �   �  �   �  �      �   H  �   p  �   �  �   �  �   �  �     �   8  }   `  }   �  }   �  }   �  }      }   (  }   P  }   x  }   �  }   �  }   �  }     }   @  }   h  �   �  �   �  �   �  }     }   0  }   X  }   �  }   �  }   �  }   �  }      }   H  }   p  }   �  }   �  }   �  }     }   8  }   `  }   �  }   �  }   �  }      }   (  }   P  }   x  }   �  }   �  }   �  }     }   @  }   h  }   �  }   �  }   �  }     }   0  }   X  }   �  }   �  }   �  }   �  }      }   H  }   p  }   �  }   �  }   �  }     }   8  }   `  }   �  }   �  }   �  }      }   (  }   P  }   x  }   �  }   �  }   �  �          (      0      8      @      H       P  #    X  &    `  )    h  ,    p  /    x  2    �  5    �  8    �  ;    �  >    �  A    �  D    �  G    �  J    �  M    �  P    �  S    �  V    �  Y    �  \    �  _    �  b       e      h      k      n       q    (  t    0  w    8  z    @  }    H  �    P  �    X  �    `  �    h  �    p  �    x  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �        �       �       �       �        �    (   �    0   �    8   �    @   �    H   �    P   �    X   �    `   �    h   �    p   �    x   �    �   �    �   �    �   �    �   �    �      �      �      �   
   �   
   �      �      �      �      �      �      �   "    !  %   !  (   !  +   !  .    !  1   (!  4   0!  7   8!  :   @!  =   H!  @   P!  C   X!  F   `!  I   h!  L   p!  O   x!  R   �!  U   �!  X   �!  [   �!  ^   �!  a   �!  d   �!  g   �!  j   0"  m   8"  p   @"  s   $  v   $  y   $  |   UNKNOWN R32G32B32A32_TYPELESS R32G32B32A32_FLOAT R32G32B32A32_UINT R32G32B32A32_SINT R32G32B32_TYPELESS R32G32B32_FLOAT R32G32B32_UINT R32G32B32_SINT R16G16B16A16_TYPELESS R16G16B16A16_FLOAT R16G16B16A16_UNORM R16G16B16A16_UINT R16G16B16A16_SNORM R16G16B16A16_SINT R32G32_TYPELESS R32G32_FLOAT R32G32_UINT R32G32_SINT R32G8X24_TYPELESS D32_FLOAT_S8X24_UINT R32_FLOAT_X8X24_TYPELESS X32_TYPELESS_G8X24_UINT R10G10B10A2_TYPELESS R10G10B10A2_UNORM R10G10B10A2_UINT R11G11B10_FLOAT R8G8B8A8_TYPELESS R8G8B8A8_UNORM R8G8B8A8_UNORM_SRGB R8G8B8A8_UINT R8G8B8A8_SNORM R8G8B8A8_SINT R16G16_TYPELESS R16G16_FLOAT R16G16_UNORM R16G16_UINT R16G16_SNORM R16G16_SINT R32_TYPELESS D32_FLOAT R32_FLOAT R32_UINT R32_SINT R24G8_TYPELESS D24_UNORM_S8_UINT R24_UNORM_X8_TYPELESS X24_TYPELESS_G8_UINT R8G8_TYPELESS R8G8_UNORM R8G8_UINT R8G8_SNORM R8G8_SINT R16_TYPELESS R16_FLOAT D16_UNORM R16_UNORM R16_UINT R16_SNORM R16_SINT R8_TYPELESS R8_UNORM R8_UINT R8_SNORM R8_SINT A8_UNORM R1_UNORM R9G9B9E5_SHAREDEXP R8G8_B8G8_UNORM G8R8_G8B8_UNORM BC1_TYPELESS BC1_UNORM BC1_UNORM_SRGB BC2_TYPELESS BC2_UNORM BC2_UNORM_SRGB BC3_TYPELESS BC3_UNORM BC3_UNORM_SRGB BC4_TYPELESS BC4_UNORM BC4_SNORM BC5_TYPELESS BC5_UNORM BC5_SNORM B5G6R5_UNORM B5G5R5A1_UNORM B8G8R8A8_UNORM B8G8R8X8_UNORM R10G10B10_XR_BIAS_A2_UNORM B8G8R8A8_TYPELESS B8G8R8A8_UNORM_SRGB B8G8R8X8_TYPELESS B8G8R8X8_UNORM_SRGB BC6H_TYPELESS BC6H_UF16 BC6H_SF16 BC7_TYPELESS BC7_UNORM BC7_UNORM_SRGB AYUV Y410 Y416 NV12 P010 P016 420_OPAQUE YUY2 Y210 Y216 NV11 AI44 IA44 P8 A8P8 B4G4R4A4_UNORM P208 V208 V408 SAMPLER_FEEDBACK_MIN_MIP_OPAQUE SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE A4B4G4R4_UNORM @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   ]   %         �   �   ? G            2      ,   M        �std::exception::exception 
 >   this  AI  	     (  AJ        	  >   _Other  AH         AK         
 Z   J                         H�  0     Othis  8     O_Other  O �   0           2   �     $       H  �   I  �)   J  �,      0     
 d      h     
 t      x     
 �      �     
 �      �     
          
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   ]   %      ,   m      �   5  M G            <      6   �        �std::invalid_argument::invalid_argument 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        M  :$
 Z   J   N                       @�  h   M  �   0   �  Othis  8   �  O__that  O   ,   '   0   '  
 r   '   v   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 @SH冹0H嬞艱$(H嬄H�
    H峉H塂$ W繦�H峀$ �    H�    H�H嬅H兡0[�   ]   0      7   m      �   �  M G            G      A   }        �std::invalid_argument::invalid_argument 
 >�   this  AI  	     =  AJ        	  D@    >:   _Message  AH       #  AK          M        x  	! M        K  	88

 Z   J   >    _InitData  D     N N 0                     @  h   K  x   @   �  Othis  H   :  O_Message  O   �               G   �            :  �,   %   0   %  
 r   %   v   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �  %   �  %  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   ]   %      ,   j      �   '  C G            <      6   �        �std::logic_error::logic_error 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        M  :$
 Z   J   N                       H� 
 h   M   0   �  Othis  8   �  O__that  O ,   !   0   !  
 h   !   l   !  
 x   !   |   !  
 �   !   �   !  
 �   !   �   !  
 H�    H�H兞�       ]            �   �   @ G                      N        �std::exception::~exception 
 >   this  AJ         
 Z   �                          H�       Othis  O  �   (              �            Y  �
   Z  �,      0     
 e      i     
 �      �     
 H�    H�H兞�       ]            �   �   N G                              �std::invalid_argument::~invalid_argument 
 >�   this  AJ          M        N   	
 N                        H�  h   N  z      �  Othis  O ,   &   0   &  
 s   &   w   &  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   ]         0   �      �   �   R G            B   
   4   P        �std::exception::`scalar deleting destructor' 
 >   this  AJ          AM       -  M        N  

	
 Z   �   N                       @� 
 h   N   0     Othis  O ,      0     
 w      {     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   ]         0   �      �   �   Y G            B   
   4   ~        �std::invalid_argument::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        N  

	
 Z   �   N                       @�  h   N  z     0   �  Othis  O  ,   (   0   (  
 ~   (   �   (  
 �   (   �   (  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   ]         0   �      �   �   T G            B   
   4   y        �std::logic_error::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        N  

	
 Z   �   N                       @�  h   N  z   0   �  Othis  O   ,   "   0   "  
 y   "   }   "  
 �   "   �   "  
 侚�   D嬕嬃H�    A����AC肔��B鰟�    tzHc罤��媱�   兝韮鴔wgH�秳    媽�    H�岣   嬋��   ��   岺��   3繧嬕HI;觲�;蕆验B�A;聄A� 3烂E���肊�3烂�                             �   $       5       G   X   N   Y   �   ^   �   Z   �   [   �   \   �   ]   �   _      �   "  b G            6      6  �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::CalculateExtraPlanarRows  >�   format  A         R  A  �       >u    plane0Height  A         	  Aj  	     - >�   totalHeight  AP        6 >u     round  A   ^         A  x     ,    A  x     -    M        �   	 N M        �  + N M        �  ��	 >u    uAddend  A   �       A  �       N M        �  �� N M        �  v	 >#     ull64Result  AK       &  N                        @  h   �  �  �  �  �  �  
            
                    $LN13         $LN12         $LN11         $LN9         $LN5     �  Oformat     u   Oplane0Height     �  OtotalHeight  O  �   �           6  �     �       � �    � �   � �	   � �+    �W   
 �^    �`    �e    �g    �o    �q    �v   , ��   2 ��   , ��   . ��   2 ��   ! ��   2 �,   
   0   
  
 �   
   �   
  
 �   
   �   
  
 �   
   �   
  
 �   
   �   
  
 �   
   �   
  
   
     
  
 *  
   .  
  
 >  
   B  
  
 �  
   �  
  
 �  
   �  
  
 %  
   )  
  
 w  X   {  X  
 �  Y   �  Y  
 �  ^   �  ^  
 �  ]   �  ]  
 �  \   �  \  
 �  [   �  [  
 �  Z   �  Z  
 8  
   <  
  
 H冹D嬌吷u
A�3繦兡肁侚�   H�$A嬃H�    A����AC肏��L��    A婰A岮簝�凐vA岮�v岯��;聄6髻#岭
岯��;聄'3吟馎禠HI;藈灵A�3繦�$H兡肊�H�$��H兡�&          �     j G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::CalculateMinimumRowMajorRowPitch  >�   Format  A           Ai       �  >u    Width  A         t  A  v     1  >�   RowPitch  AP        �  >u     NumUnits  A   ]     <    
 0 	  A  �     	  >u     WidthAlignment  A   E     1     A  v     1    M        �  %) N M        �  E N M        �  n >u     adjustedDividend  A   l       M        �  n N N M        �  _ N M        �  v
 >#     ull64Result  AJ  �         AJ �       N                       @ & h   �  �  �  �  �  �  �  �      �  OFormat     u   OWidth      �  ORowPitch  O  �   �           �   �     �       � �   � �   � �   � �   � �   � �E   � �I   � �L   � �_   � �c   � �i   � �n   � �v   � ��   � ��   � ��   � ��   � ��   � ��   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 3     7    
 \     `    
 p     t    
 �     �    
 p     t    
 �     �    
 $     (    
 H塡$H塼$WH冹 嬃孃侚�   ����I嬞E嬋C芁��N��    H�    A鯠 tJL岲$0A嬔�    吚埁   婰$0嬈HA�@ �H;�F罞3繦;螇EG罙嬂H媆$8H媡$@H兡 _脜蓇4H嬜嬑IA�@ �H;�F蔈3繦;謮EG罙嬂H媆$8H媡$@H兡 _肁婰A岮�灵冡罙;羠%3吟駤菻H;蝫�3繦媆$8H媡$@H兡 _脡3H媆$8��H媡$@H兡 _�6       K   
      �   d  l G                 �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::CalculateMinimumRowMajorSlicePitch  >�   Format  A         � O = � /  >u    TightRowPitch  A           A         x  �  �   >u    Height  Ah        $  Ai  $     � + = { %  Ai �       >�   SlicePitch  AI  !     � `  �  �   AQ        !  >u     HeightAlignment  A   �         A  �       >u     HeightOfPacked  A   �     !    A  �     
  >u     PlanarHeight  B0   O     � = s  M        �   N M        �  W >#     ull64Result  AJ  a     +  N M        �  ��	 N M        �  �� >#     ull64Result  AK  �     +  N M        �  ��
 >u     adjustedDividend  A   �       M        �  ��
 N N M        �  ��F >#     ull64Result  AJ  �       AJ �       N
 Z   �                         @ " h   �  �  �  �  �  �  �   0   �  OFormat  8   u   OTightRowPitch  @   u   OHeight  H   �  OSlicePitch  0   u   OPlanarHeight  O�   �             �     �       � �   � �   � �   � �   � �$   � �B   � �W   � �|    ��    ��    ��    ��    ��   
 ��    ��   
 ��    ��    ��    ��    ��    �,      0     
 �      �     
 �      �     
 �      �     
           
          
 (     ,    
 M     Q    
 i     m    
 �     �    
 �     �    
 �     �    
 �     �    
          
 p     t    
 �     �    
       $    
 �     �    
 �     �    
 x     |    
 D塋$ D塂$塗$塋$H冹xA侚�   D嬞����A嬃C罞嬔;羥
竁 �H兡x肔媽$�   H��H塡$pH�    H塴$hH墊$XL塪$PL塴$HL�,華岯篖塼$@L墊$8I�    凐v
A岯�v2垭�A嬰圽$ D嫓$�   E3銭3鯤塼$`孃E孁E呟刧  H嫶$�   H兤�    A婱D嬃A冟A岪��;��4  3褹黟塂$(勠t 灵冡岮��;��  A禲3吟駤入A禲嬒AA儅  �   塋$$D亓�A凟 t3嬔L岲$$A嬍�    吚埶   婰$$L媽$�   D嫈$�   D嫓$�   婦$(嬘����HH;�嚄   D嬄嬃IH;�噮   嬓A嬒HH兗$�    tI�H塅餖塅鳫�H岮A�艸冟餓D;�$�   r嫭$�   E3鰦�$�   D嫾$�   �凖暳禹�暳语A�暳A语A�腍兤E;鉺禱$ 槌�����3繦媡$`L媗$HL媎$PH媩$XL媡$@H媗$hH媆$pL媩$8H兡x肞       F  
      �   |  _ G            S     N  �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::CalculateResourceSize  >u    width  A            Ak        �  D�    >u    height  A         �  A   �    
  A  �     � 2 D�    >u    depth  Ah        �  Ah �     � M D�    >�   format  Ai        .  Aj  .     � �  Aj     6    D�    >u    mipLevels  EO  (           D�    >u    subresources  Ak  �     �� $ y  EO  0           D�    >�   totalByteSize  AQ  D     � �  EO  8           D�   
 >�   pDst  AL  �       EO  @           D�    >u     subDepth  Ao  �     � >u     tableIndex  A   (     ,   ! >0     fIsBlockCompressedFormat  A   �     �   }   A   �     M>  K �  A      ,  A      ,    E6u     �     � >u     subHeight  A   �     V - A  �     j
 -
  >�    formatDetail  AU  l     � >u     subWidth  A   �     �
 >u     s  Al  �     � >u     iM  An  �     � >u     blockSize  A         A       k    A      ,    >#     subresourceByteSize  AJ  �    R  AJ �     � 3M  >u     blockWidth  B(   �     �# >#     subresourceByteSizeAligned  AH  �    _  AH �     V E >u     blockHeight  A       Z  @  A  !      D$    M        �  l N M        �  ��	 >u    divisor  Ah  �     k  Ah n    �  � �   >u     adjustedDividend  A   �       M        �  �� N N M        �  
�� >u    divisor  A   �     I %  @   A      6    >u     adjustedDividend  A   �       M        �  
�� N N M        �  亞		 >#     ull64Result  AH  �       AH �    h  _  N M        �  乶 >#     ull64Result  AK  }      AK     6    N
 Z   �   x                      @  h   �  �  �  �  �  �   �   u   Owidth  �   u   Oheight  �   u   Odepth  �   �  Oformat  �   u   OmipLevels  �   u   Osubresources  �   �  OtotalByteSize  �   �  OpDst  $   u   OblockHeight  O�   h          S  �  *   \      ? �   @ �.   A �2   B �7   � �<   D �D   F �l   H �z   D ��   H ��   M ��   P ��   S ��   Y ��   [ �	  a �  [ �  b �  m �!  p �2  w �5  y �;  { �n  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  P �  � �$  � �N  � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 
         
          
 G     K    
 W     [    
 o     s    
 �     �    
 �     �    
 �     �    
      "    
 :     >    
 a     e    
 u     y    
 �     �    
 �     �    
 �          
          
 ,     0    
 <     @    
 T     X    
 x     |    
 �     �    
 �     �    
 �     �    
 �     �    
          
 ;     ?    
 K     O    
 _     c    
 �     �    
 �     �    
 �     �    
          
          
 =     A    
 Q     U    
 �     �    
 �     �    
 �     �    
 O     S    
 g     k    
 �     �    
 �     �    
          
 L     P    
 \     `    
 �     �    
 侜   t<侜   t4岮鑳鳦w,L�    H楢秳     A媽�    I�岚脕� �  澙�2烂�                 �   &   7   .   8   H   :   L   9   P   ;      �   8  a G            �       �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::CanBeCastEvenFullyTyped  >�   Format  A         2  A  D       >�   fl  A         �                         @ 
            
                    $LN13         $LN9     �  OFormat     �  Ofl  O�   `           �   �  	   T       A �    C �   K �7   Q �9   X �:   T �C   X �D   G �F   X �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   7   �   7  
 �   8   �   8  
 �   :     :  
   9     9  
 L  �   P  �  
 H冹H侚�   ����C�;萾H�塇�    �妊鑳�H兡H肏�    H峀$ �    H�    H峀$ �    �       2   �   <   %   C   s   M   +      �     Z G            R      R   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::DX9TextureFormat  >�   Format  A         ;    M        �  "+
 Z   }   N H                      @  h   �  �         $LN10  P   �  OFormat  O �   8           R   �     ,       {	 �   |	 �*   }	 �/   |	 �,   �   0   �  
 �   �   �   �  
 �   @   �   @  
   �     �  
 H冹H侚�   ����C�;萾H�塇�    �葍�H兡H肏�    H峀$ �    H�    H峀$ �    �       0   �   :   %   A   s   K   +      �   	  ` G            P      P   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::DX9VertexOrIndexFormat  >�   Format  A         9    M        �  ")
 Z   }   N H                      @  h   �  �         $LN10  P   �  OFormat  O   �   8           P   �     ,       t	 �   u	 �(   v	 �-   u	 �,   �   0   �  
 �   �   �   �  
 �   >   �   >  
    �   $  �  
 凒jt
凒c~凒n}��2烂   �   �   v G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::DecodeHistogramAllowedForOutputFormatSupport  >�   Format  A                                  @     �  OFormat  O�   @              �     4       �	 �    �	 �   �	 �   �	 �   �	 �,      0     
 �      �     
 �      �     
 凒(t凒7斃冒�   �   �   Y G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::DepthOnlyFormat  >�   Format  A                                  @     �  OFormat  O �   @              �     4        �     �    �    �    �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 Hc罤��H�   �葍�t凐,斃冒�
          �   �   _ G                       �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FamilySupportsStencil  >�   Format  A           M        �    N                        @ 
 h   �      �  OFormat  O  �   @               �     4       � �    � �   � �   � �   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塴$H塼$H墊$ AVH冹 嬯D嬹�    嬐嬝�    ;貗�B�3蹍鰐/fD  嬘A嬑�    嬘嬐孁�    ;鴗
�黷(凐黷#��;農�2繦媆$0H媗$8H媡$@H媩$HH兡 A^冒脶    �   )   �   F      Q         �   �  a G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FloatAndNotFloatFormats  >�   FormatA  A           An       k f   >�   FormatB  A           A        n Y   >u     NumComponents  A   4     V F  
 >u     c  A   6     T :  
 >�    fciB  A   U     3    A  @     + 
  
 >�    fciA  A   P     :    A  @     ?    M        �  %	 Z   �  �   >u     NumComponentsFormatA  A   (       >u     NumComponentsFormatB  A   -       A   1       N Z   �  �                         @ 
 h   �   0   �  OFormatA  8   �  OFormatB  O�   h           �   �  
   \       �	 �   �	 �4   �	 �@   �	 �J   �	 �U   �	 �c   �	 �i   �	 �k   �	 ��   �	 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 >     B    
 R     V    
 u     y    
 �     �    
 �     �    
          
 &     *    
 �     �    
 H冹H侚�   ����C�;萾H�塇�    �攘�冟H兡H肏�    H峀$ �    H�    H峀$ �    �       3   �   =   %   D   s   N   +      �   	  ` G            S      S   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FloatNormTextureFormat  >�   Format  A         <    M        �  ",
 Z   }   N H                      @  h   �  �         $LN10  P   �  OFormat  O   �   8           S   �     ,       �	 �   �	 �+   �	 �0   �	 �,   �   0   �  
 �   �   �   �  
 �   B   �   B  
    �   $  �  
 Hc罤=�   sH��H�
    �羶�暲霉����;�暲�          �     V G            +       *   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FormatExists  >�   Format  A         %  
  M        �  # >#    Index  AH       '    N                        @  h   �  �      �  OFormat  O�   @           +   �     4       � �    � �   � �    � �*   � �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
   �      �  
 侚�   ����C�;萾勔tH�塇�    �壤�鲂$冒�2烂          �   -  ^ G            3       2   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FormatExistsInHeader  >�   Format  A         3    >0    bExternalHeader  A         3  M        �   N                        @  h   �  �      �  OFormat     0   ObExternalHeader  O   �   P           3   �     D       � �    � �,   � �-   � �/   � �0   � �2   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 H塡$E嬝H�    侜�   A����嬄AC繪��F禗�E呟�"  A冸�  A冸�  A冸�  A凔�   岯簝�喅   岯�啩   A凐w�   茿   茿   H媆$肁凐w�   茿   茿   H媆$肁凐 w�   茿   茿   H媆$肁凐@w�   茿   茿   H媆$肁侙�   噞  �   茿   茿   H媆$肂婦�冟拎�B婦�凌冟茿   拎堿A凐@�2  茿   H媆$脥B呵A   凐喗   岯�啽   A凐w�@   茿@   隢A凐w�@   茿    �9A凐 w�    茿    �$A凐@w�    �A侙�   w
�   茿   A凒啓   A凒w�)H媆$肁凒w�)裪H媆$肁凒w�)裪H媆$肁凒wb�)羒H媆$肂婽�冣菱�B婦�凌冟拎堿A凐@u1��H媆$肊吚u�   �
3腋 �  A黟茿   茿   �H媆$�          �   �  X G            �     |  �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::Get4KTileShape  >�   pTileShape  AJ        � >�   Format  A         d,*  A  l      >�   Dimension  Ah          Ak       0  Ak V    ,  >u    SampleCount  Ai        � >u     BPU  Ah  +     W M        �  # N M        �  乄
 N M        �  �4 N M        �  �' N M        �  \ N M        �  �( N M        �  � N                        @  h   �  �  �  �  �  �      �  OpTileShape     �  OFormat     �  ODimension      u   OSampleCount  O   �   �          �  �  P   �      � �   � �+   � �\    �t   " �z   $ ��   % ��   & ��   L ��   ( ��   * ��   + ��   , ��   L ��   . ��   0 ��   1 ��   2 ��   L ��   4 ��   6 ��   7 ��   8 ��   L ��   : �  < �  = �  > �  L �   �(   �3   �@   �J   �Q  L �W  � �Z  � �a  � �v  � �|  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  L ��  � ��  � ��  � ��  L �  � �  � �  � �  L �  � �  � �  � �!  L �'  � �4  � �E  � �K  � �N  L �V  � �l  � �s  � �z  L �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �           
 "     &    
 @     D    
 �     �    
 侚�   L�
    嬃����C翲��E禗�岮簝�v*岮�v"A婽�嬍灵嬄冡凌冟冣DA凌A冭t'A冭tA冭tA冭tA凐t�冒冒冒冒�2烂	          �   <  i G            �       �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetAddressingBitsPerAlignedSize  >�   Format  A         8  A  S     4  >      addressBitsPerElement  A   z     
       
   >u     byteAlignment  Ah  W       M        �   !"
 >u     bits  A   L       Ah  !     6 +   M        �  !  N M        �  ! N M        �  C N M        �  = N M        �  1 N N                        @ & h   �  �  �  �  �  �  �  �      �  OFormat  O�   �           �   �     �       � �    � �W   � �u   � �w   � �x   � �z   � �{   � �}   � �~   � ��   � ��   � ��   � ��   � ��   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 F  �   J  �  
 V  �   Z  �  
 P  �   T  �  
 H冹H凓w(侚�   ����C葖翲�塇�菻�   �H兡H肏�    H峀$ �    H�    H峀$ �    �$       4   �   >   %   E   s   O   +      �   A  ] G            T      T   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetBitsPerComponent  >�   Format  A         =    >u    AbsoluteComponentIndex  A         8 !  
 Z   }   H                      @  h   �  �         $LN9  P   �  OFormat # X   u   OAbsoluteComponentIndex  O   �   @           T   �     4       M �   N �	   V �,   W �1   Q �,      0     
 �      �     
 �      �     
   T     T  
 X     \    
 H冹H侚�   ����C�;�剤   H�塇��    L�    B婦拎柳兝凐wK3蓩羺蓆-冭t冭t凐u;B婦拎�B婦拎�B婦拎�B婦拎柳吚t�羶�r�3繦兡H脣罤翨禗 H兡H肏�    H峀$ �    H�    H峀$ �    �)       �   �   �   %   �   s   �   +      �   e  Y G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetBitsPerDepth  >�   Format  A         �  � 
 >u     comp  A   B     ` 
 >�    name  A   \     1   
    !   A  B     R  N  M        �  "��
 Z   }   N H                      @  h   �  �         $LN30  P   �  OFormat  O   �   �           �   �     �       � �   � �   � �@   � �B   � �W   � �a   � �k   � �u   � �}   � ��   � ��   � ��   � ��   � ��   � ��   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 K  .   O  .  
 |  �   �  �  
 H冹H侚�   ����C�;�剦   H�塇��    L�    B婦拎柳兝凐wL3蓩羺蓆-冭t冭t凐u.B婦拎�B婦拎�B婦拎�B婦拎柳凐t�羶�r�3繦兡H脣罤翨禗 H兡H肏�    H峀$ �    H�    H峀$ �    �)       �   �   �   %   �   s   �   +      �   g  [ G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetBitsPerStencil  >�   Format  A         �  � 
 >u     comp  A   B     a 
 >�    name " A   \     9   
    !  2   A  B       M        �  "��
 Z   }   N H                      H  h   �  �         $LN29  P   �  OFormat  O �   �           �   �     �       j �   k �   l �@   q �B   t �W   y �a   x �k   w �u   v �}   { ��   q ��   � ��   � ��   } ��   � ��   k �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 M  ,   Q  ,  
 |  �   �  �  
 侚�   ����C菻�   H���让          �   �   X G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetBitsPerUnit  >�   Format  A                                  H  h   �  �      �  OFormat  O  �   0              �     $         �    ! �   " �,   �   0   �  
    �   �   �  
 �   �   �   �  
 H冹H侚�   ����C�;萾H�塇�   �菻兡H肏�    H峀$ �    H�    H峀$ �    �       -   �   7   %   >   s   H   +      �     ] G            M      M   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetBitsPerUnitThrow  >�   Format  A         6    M        �  "&
 Z   }   N H                      @  h   �  �         $LN10  P   �  OFormat  O  �   8           M   �     ,       � �   � �%   � �*   � �,   �   0   �  
 �   �   �   �  
 �   <   �   <  
   �      �  
 侚�   L�    嬃����C翲��E禠�岮簝�v.岮�v&E婦�A嬓陵A嬋冣灵冡A冟ADA灵A嬃�	          �   �  Z G            _       ^   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetByteAlignment  >�   Format  A         ?  A  W      
 >u     bits  A   O       Ai  !     : .   M        �  !  N M        �  ! N M        �  E N M        �  ? N M        �  1 N                        H " h   �  �  �  �  �  �  �      �  OFormat  O  �   H           _   �     <       � �    � �!   � �1   � �W   � �^   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H冹H呉剫   冴t^冴t1凓厾   侚�   ����C菻�    H�墜D�拎柳H兡H脕   ����C菻�    H�墜D�拎柳H兡H脕   ����C菻�    H�墜D�拎柳H兡H脕   ����C菻�    H�墜D�拎柳H兡H肏�    H峀$ �    H�    H峀$ �    �0       X       �       �       �   �   �   %   �   s   �   +      �   �  Z G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetComponentName  >�   Format  A         � 8  `  �  �   >u    AbsoluteComponentIndex  A         �  � 
 >�    name * A   <     �     +  3  S  [  {  
 Z   }   H                      H  h   �  �         $LN30  P   �  OFormat # X   u   OAbsoluteComponentIndex  O  �   p           �   �     d       : �   < �   A �?   I �G   @ �g   I �o   ? ��   I ��   > ��   I ��   C �,   	   0   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 G  V   K  V  
 �  	   �  	  
 侚�   ����C菻�   H�墜攘�冟�          �   �   [ G            #       "   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetDepthAlignment  >�   Format  A                                  H  h   �  �      �  OFormat  O   �   0           #   �     $       0 �    1 �"   2 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 侚�   ����B撩   �   �   ] G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetDetailTableIndex  >�   Format  A                                  H     �  OFormat  O �   0              �     $       � �    � �   � �,      0     
 �      �     
 �      �     
 ����侚�   C葖撩   �   �   d G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetDetailTableIndexNoThrow  >�   Format  A                                  H 
 h   �      �  OFormat  O  �   0              �     $       � �    � �   � �,      0     
 �      �     
 �      �     
 H冹H侚�   ����C�;萾嬃H兡H肏�    H峀$ �    H�    H峀$ �    �    �   *   %   1   s   ;   +      �   �   b G            @      @   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetDetailTableIndexThrow  >�   Format  A         ) 
 Z   }   H                      H 
 h   �         $LN7  P   �  OFormat  O�   @           @   �     4       � �   � �   � �   � �   � �,      0     
 �      �     
 �   x   �   x  
 �      �     
 H侚�   sH�塇�
    �撩�����          �   �   S G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetFormat  >#    Index  AJ                                   H 
 h   �      #   OIndex  O �   H              �     <       6 �    7 �	   9 �   < �   ; �   < �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 Hc罤�
   H��H�撩          �   �   Z G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetFormatCastSet  >�   Format  A         
                         @     �  OFormat  O�   0              �     $       % �    & �   ' �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H冹H呉剫   冴t^冴t1凓厾   侚�   ����C菻�    H�墜D�拎柳H兡H脕   ����C菻�    H�墜D�拎柳H兡H脕   ����C菻�    H�墜D�拎柳H兡H脕   ����C菻�    H�墜D�拎柳H兡H肏�    H峀$ �    H�    H峀$ �    �0       X       �       �       �   �   �   %   �   s   �   +      �   �  j G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetFormatComponentInterpretation  >�   Format  A         � 8  `  �  �   >u    AbsoluteComponentIndex  A         �  �  >�    interp * A   <     �     +  3  S  [  {  
 Z   }   H                      @  h   �  �         $LN30  P   �  OFormat # X   u   OAbsoluteComponentIndex  O�   p           �   �     d       \ �   _ �   d �?   l �G   c �g   l �o   b ��   l ��   a ��   l ��   f �,      0     
 �      �     
 �      �     
 �      �     
 Y  R   ]  R  
 �     �    
 侚�   ����C�;萿3烂H�塇�    H�让          �   �   Y G            %       $   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetFormatDetail  >�   Format  A                                  H 
 h   �      �  OFormat  O �   H           %   �     <       ] �    _ �   a �   e �   d �$   e �,      0     
 �      �     
 �      �     
 H冹H侚�   ����C�;萾GH�塋�    A婦�拎柳�A婦�拎柳塀A婦�拎柳塀A婦�拎柳塀H兡H肏�    H峀$ �    H�    H峀$ �    �       `   �   j   %   q   s   {   +      �   Q  ^ G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetFormatReturnTypes  >�   Format  A         i  C  >�   pInterpretations  AK        d  M        �  "Y
 Z   }   N H                      @  h   �  �         $LN10  P   �  OFormat  X   �  OpInterpretations  O   �   X           �   �     L       � �   � �   � �.   � �<   � �J   � �X   � �]   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   0     0  
 h  �   l  �  
 H�    �          �   |   X G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetFormatTable                         @  O�   0              �     $       { �    | �   } �,   �   0   �  
 �   �   �   �  
 侚�   ����C菻�   H�墜攘�冟�          �   �   \ G            #       "   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetHeightAlignment  >�   Format  A                                  H  h   �  �      �  OFormat  O  �   0           #   �     $       + �    , �"   - �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 � �  �   �   �   g G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetHighestDefinedFeatureLevel                         @  O �   0              �     $       m �    n �   o �,   �   0   �  
 �   �   �   �  
 侚�   ����C菻�   H�墜攘�柳�          �   �   S G            #       "   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetLayout  >�   Format  A                                  @  h   �  �      �  OFormat  O   �   0           #   �     $       3 �    4 �"   5 �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 H塡$WH冹 嬟�    嬎孁�    H媆$0;�B荋兡 _�
   �      �      �   x  f G            *   
      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetMinNumComponentsInFormats  >�   FormatA  A           >�   FormatB  A           A          >u     NumComponentsFormatA  A          >u     NumComponentsFormatB  A          Z   �  �                         H  0   �  OFormatA  8   �  OFormatB  O�   8           *   �     ,       � �   � �   � �   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   "  �  
 �  �   �  �  
 H塡$H墊$H嬟�   D嬜3褹逾H�I黩L嬝M吚t
I� 3襂黩H嬋�H嬒M吷t
I�3襂黩�H嬊H吷HD螹呟LD逪吚L�HD荕吚tI�M吷tI�H媆$H媩$�   �     Z G            }   
   r   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetMipDimensions  >     mipSlice  A         8 3   >#   pWidth  AI  
     j  AK        
  >#   pHeight  AP        }  >#   pDepth  AQ        }  >#     mipWidth  AS  #     Z  >#     mipHeight  AJ  3     J    >u     denominator  Aj       c  >#     mipDepth  AH  E     8                           @         OmipSlice     #  OpWidth     #  OpHeight      #  OpDepth  O�   x           }   �     l       ^	 �
   _	 �   `	 �#   a	 �8   b	 �J   e	 �Q   g	 �X   i	 �[   l	 �b   m	 �j   n	 �r   o	 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
      "    
 B     F    
 l     p    
 �     �    
           
 侚�   ����C�;萾4L�    勔tH�堿鰟�    @uI媱�    H�
    H吚HE菻嬃肏�    �   �   %       0       7   p   I   p      �   *  Q G            N       M   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetName  >�   Format  A         N ;   >0    bHideInternalFormats  A         N  M        �   N                        @  h   �  �      �  OFormat !    0   ObHideInternalFormats  O  �   @           N   �     4       � �    � �E   � �F   � �M   � �,   �   0   �  
 x   �   |   �  
 �   �   �   �  
 @  �   D  �  
 H冹H3篮����侚�   C�;�劋   H�塇塡$@H�
   D嬂L�褘袐谼嬝D嬓�繟嬋E吚tL冮t2冮t凒tQA�繟凐r訦媆$@H兡H肁�	玲六A�纼�AD秒礎�	玲六A�纼�AD码燗�	玲六A�纼�D码孉�	玲六凒D肏媆$@H兡H肏�    H峀$ �    H�    H峀$ �    �(       �   �   �   %   �   s   �   +      �   Z  b G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetNumComponentsInFormat  >�   Format  A         � , � 
 >u     n  A        � - I x  �  �   A  3      
 >u     comp  Ah  /     :  &  Ah 3     � &  B 	 W 	 l  
 >�    name  A   ?     k -  B  W  * A   l     T   	      -  3  A   A   5       A   7       Ah  u     2 	     Aj  =       Ak  :       A  3       Ah 3       M        �  "��
 Z   }   N H                      @  h   �  �         $LN34  P   �  OFormat  O  �   �           �   �     �       � �   � �   � �   � �3   � �?   � �V   � �_   � �d   � �i   � �~   � ��   � ��   � ��   � ��   � ��   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �      �  
 +  �   /  �  
 G  �   K  �  
 s  �   w  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @  2   D  2  
 p  �   t  �  
 咐   �   �   {   W G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetNumFormats                         H  O �   0              �     $       t �    u �   v �,   �   0   �  
 �   �   �   �  
 Hc罤�
   H���撩          �   �   Y G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetParentFormat  >�   Format  A         
                         H     �  OFormat  O �   0              �     $        �     �     �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 Hc罤�    H��媱�   兝韮鴔wH�秳    媽�    H�岚冒冒�                      �          #   D   *   E   <   F   @   G   D   H      �   %  W G            �       �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetPlaneCount  >�   Format  A         .  A  9       M        �    N                        @ 
 h   �  
            
                    $LN11         $LN4     �  OFormat  O   �   X           �   �     L       I	 �    J	 �3   S	 �5   Z	 �6   V	 �8   Z	 �9   X	 �;   Z	 �,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
 �   D   �   D  
 �   E   �   E  
 �   G   �   G  
   F     F  
 <  �   @  �  
 Hc罫�    H��A媱�   兝韮鴒w"H楢秳     A媽�    I�醿�.t凓t!2烂冴腭冴1t冴腱冴#t
冴t冴u甙�                        �          %   I   -   J   d   L   h   K   l   M   p   N   t   O      �   �  e G            �       �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetPlaneSliceFromViewFormat  >�   ResourceFormat  A         1  A  @       >�   ViewFormat  A         � 9 
 F  K  U   A  @       M        �    N                        @ 
 h   �  
            
                    $LN28         $LN20         $LN16         $LN12     �  OResourceFormat     �  OViewFormat  O�   h           �   �  
   \       W �    X �6   [ �9   � �B   � �C   f �H   s �R   � �a   � �c   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 <  I   @  I  
 K  J   O  J  
 V  N   Z  N  
 g  M   k  M  
 x  L   |  L  
 �  K   �  K  
 �     �    
 H塡$Hc翲�    D嬞L������F嫈�   A侜�   AB翲��鰟�    �  A岯韮鴔�+  H�秳    媽�    H�酘婽$8E呟t5A凔uEH婦$(A岻� 0   A岪谚�H婦$@验�H婦$0�
�H媆$肏婦$(� <   H婦$@D�D�H婦$0�
�H媆$肏婽$8E呟t5A凔uEH婦$(A岻� !   A岪谚�H婦$@验�H婦$0�
�H媆$肏婦$(� 5   H婦$@D�D�H婦$0�
�H媆$肏婰$8E呟t"A凔�0  H婦$(� 0   A岪谚��  H婦$(� <   辇   H婦$(H婽$8� <   E呟�-���A冸t
A凔�(���H婦$@A岻D�验�H婦$0�
�H媆$肊呟tA冸t
A凔叴   H婦$(� <   閶   E呟tIA凔厱   H婦$(A岺灵� 0   H婦$8�A岺H婦$@验D�H婦$0�H媆$肊呟tA凔uVH婦$(� <   �H婦$(� '   H婦$8D� H婦$@D�H婦$0D� H媆$肏婦$(�H婰$8D�H婦$@D�H婦$0�	�H媆$胒�                                     �          5       S   m   Z   n   �  u   �  o   �  p   �  t   �  q   �  r   �  s   �  v      �   �  z G                   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetPlaneSubsampledSizeAndFormatForCopyableLayout  >u    PlaneSlice  A           Ak       Rr+ ��  Ak �    N�   >�   Format  A         h _ � _ q>  >u    Width  Ah         >u    Height  Ai         >�   PlaneFormat  D(    EO  (           >�   MinPlanePitchWidth  D0    EO  0           >�   PlaneWidth  AJ  0    F7 �  AK  h     G_  � F  D8    EO  8           >�   PlaneHeight  D@    EO  @           >�    ParentFormat  Aj  #     � M        �  

	 N M        �  
 M        �  
 >u     Index  A        B)  M        �  
 N N N                        @  h   �  �  �  �  
            
                    $LN47         $LN43         $LN38         $LN33         $LN29         $LN24         $LN20     u   OPlaneSlice     �  OFormat     u   OWidth      u   OHeight  (   �  OPlaneFormat  0   �  OMinPlanePitchWidth  8   �  OPlaneWidth  @   �  OPlaneHeight  O �   �            �  -   t      � �   � �   � �   � �   � �   � �#   � �@   � �c   � �s   � �x   � ��   D	 ��   � ��   D	 ��   � ��   � ��   � ��   D	 �  � �  � �  D	 �+  � �?  � �J  � �R  � �W  � �b  � �g  � �l  � ��  � ��  D	 ��  � ��  	 ��  		 ��  
	 ��  	 ��  	 ��  	 �
  D	 �  %	 �"  D	 �/  (	 �:  D	 �X  ?	 �_  D	 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
      "    
 ?     C    
 q     u    
 �     �    
 �     �    
 �     �    
          
 9     =    
 `     d    
 �     �    
 =  m   A  m  
 L  n   P  n  
 W  u   [  u  
 h  t   l  t  
 y  s   }  s  
 �  r   �  r  
 �  q   �  q  
 �  p   �  p  
 �  o   �  o  
 �     �    
 H塡$E嬝H�    侜�   A����嬄AC繪��F禗�E呟刋  A冸凬  A冸凞  A冸�4  A凔匳  岯簝�嗂   岯�單   岯糀;�啫   A冭剟   A冭tdA冭tDA冭 t$A凐@�  �   茿   茿   H媆$们    茿   茿   H媆$们    茿    茿   H媆$们    茿    茿    H媆$们@   茿    茿    H媆$们@   茿    茿   H媆$肂婽�冣菱�B婦�凌冟拎堿B婦�凌冟拎堿A凐@�7  ��H媆$脥B簝�喣   岯�喐   茿   A凐w�   茿   隢A凐w�   茿�   �9A凐 w��   茿�   �$A凐@w��   �A侙�   w
�@   茿@   A凒啝   A凒w�)H媆$肁凒w�)裪H媆$肁凒w�)裪H媆$肁凒wi�)羒H媆$肂婽�冣菱�B婦�凌冟茿   拎堿A凐@u1��H媆$肊吚u�   �
3腋   A黟茿   茿   �H媆$�          �   �  V G            �     �  �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetTileShape  >�   pTileShape  AJ        � >�   Format  A         �G? [1  A  �      >�   Dimension  Ah          Ak       0  Ak �    ,  >u    SampleCount  Ai        � >u     BPU  Ah  +     �Y �  M        �  # N M        �  亞 N M        �  俢 N M        �  俈 N M        �  \ N M        �  乣 N M        �  丱 N M        �  丅 N                        @ " h   �  �  �  �  �  �  �      �  OpTileShape     �  OFormat     �  ODimension      u   OSampleCount  O  �   �          �  �  P   �      � �     �+    �\   a �t   r ��   } ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �  � �  � �  � �  � �"  � �(  u �.  v �5  w �<  � �B  f �O  g �`  h �q  i �{  o �~  � ��   ��  $ ��  & ��  ' ��  ( ��  ) ��  + ��  , ��  - ��  . ��  0 ��  1 ��  2 ��  3 ��  5 ��  7 ��  8 ��  : �  B �  D �  F �  � �   I �&  K �(  L �+  � �1  N �7  P �:  Q �=  � �C  S �I  U �L  V �P  � �V   �c   �n   �{   ��   ��  � ��  	 ��  
 ��   ��  � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
 $     (    
 B     F    
 �     �    
 侚�   ����C菻�   H�墜攘�柳�          �   �   V G            #       "   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetTypeLevel  >�   Format  A                                  @  h   �  �      �  OFormat  O�   0           #   �     $       , �    - �"   . �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 侚�   ����C菻�   H�墜葍��          �   �   [ G                       �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetWidthAlignment  >�   Format  A                                  H  h   �  �      �  OFormat  O   �   0               �     $       & �    ' �   ( �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 兞渻� wXHc罫�
    A秳    A媽�    I�崆   A�    们   A�    们   A�    们   A�    们   A�    胒�                                   �      b      c   p   h   t   d   x   e   |   g   �   f   �   h      �   �  c G            �       �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::GetYCbCrChromaSubsampling  >�   Format  A           >�   HorizontalSubsampling  AK        �  >�   VerticalSubsampling  AP        �                         @ 
            
                    $LN21         $LN20         $LN11         $LN8         $LN4     �  OFormat "    �  OHorizontalSubsampling      �  OVerticalSubsampling  O  �   �           �   �     |       � �    � �(   � �.   � �5   � �6   � �<   � �D   � �J   � �Q   � �R   � �X   � �`   � �f   � �,   
   0   
  
 �   
   �   
  
 �   
   �   
  
 �   
   �   
  
   b     b  
 %  c   )  c  
 0  h   4  h  
 A  g   E  g  
 R  f   V  f  
 c  e   g  e  
 s  d   w  d  
 �  
   �  
  
 岮簝�v岮�v2烂��   �   �   _ G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::IsBlockCompressFormat  >�   Format  A                                  H     �  OFormat  O   �   @              �     4       � �    � �   � �   � �   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 侚�   ����C�;萿2烂H�塇�   ��$�          �   �   V G            '       &   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::IsSRGBFormat  >�   Format  A                                  @ 
 h   �      �  OFormat  O�   H           '   �     <       � �    � �   � �   � �   � �&   � �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 勔t岮櫓���斃脥A﹥�w
�  Ｂr凒w
�  Ｈs��2烂   �   !  m G            7       6   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::IsSupportedTextureDisplayableFormat  >�   Format  A         7  >0    bMediaFormatOnly  A           A  "                              @     �  OFormat     0   ObMediaFormatOnly  O   �   X           7   �     L       �	 �    �	 �   �	 �   �	 �   �	 �3   �	 �4   �	 �6   �	 �,      0     
 �      �     
 �      �     
 �      �     
 8     <    
 凒g斃�   �   �   k G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::MotionEstimatorAllowedInputFormat  >�   Format  A                                  @     �  OFormat  O   �   0              �     $       
 �    
 �   
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 嬃侚�   ����C翲��H�    ��t凒jt�   �3烂          �   �   Y G            /       .   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::NonOpaquePlanar  >�   Format  A         /  M        �  ! N M        �    N                        H  h   �  �  �  �      �  OFormat  O   �   @           /   �     4       x �    y �+   z �,   y �.   z �,   �   0   �  
 �   �   �   �  
   �     �  
 嬃侚�   ����C翲��H�    ��t凒jt3纴羮���凒柪兝酶   �          �   "  ] G            >       =   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::NonOpaquePlaneCount  >�   Format  A         > . 
  M        �  &  M        �  ! N M        �    N N                        @  h   �  �  �  �  �      �  OFormat  O  �   H           >   �     <       � �    � �&   � �7   � �8   � �=   � �,   �   0   �  
 �   �   �   �  
 8  �   <  �  
 3纼鵭斃�   �   �   P G            	          �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::Opaque  >�   Format  A         	                         H     �  OFormat  O  �   0           	   �     $       � �    � �   � �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 侚�   ����C菻�    H���攘�冟�          �   �   P G            $       #   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::Planar  >�   Format  A                                  H  h   �  �      �  OFormat  O  �   0           $   �     $       q �    r �#   s �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$H塴$H塼$H墊$ AVH冹 嬯D嬹�    嬐嬝�    ;貗�B�3蹍鰐5fD  嬘A嬑�    嬘嬐孁�    �齯凐-凐齯�#��;農�2繦媆$0H媗$8H媡$@H媩$HH兡 A^冒脶    �   )   �   F      Q         �   �  ^ G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::SNORMAndUNORMFormats  >�   FormatA  A           An       q l   >�   FormatB  A           A        t _   >u     NumComponents  A   4     \ L  
 >u     c  A   6     Z @  
 >�    fciB  A   U     9    A  @     1 
 % 
 >�    fciA  A   P     @    A  @     E    M        �  %	 Z   �  �   >u     NumComponentsFormatA  A   (       >u     NumComponentsFormatB  A   -       A   1       N Z   �  �                         @ 
 h   �   0   �  OFormatA  8   �  OFormatB  O   �   h           �   �  
   \       �	 �   �	 �4   �	 �@   �	 �J   �	 �U   �	 �i   �	 �o   �	 �q   �	 ��   �	 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 ;     ?    
 O     S    
 r     v    
 �     �    
 �     �    
          
 #     '    
 �     �    
 H冹HE3繟����侚�   D嬕AC薃;藅kH�堿嬓H�   L�葖聟襱'冭t冭t
凐u&A�拎�A�拎�A�拎�A�拎柳凐tE;衪A��聝�r禔嬅H兡H脣翲兡H肏�    H峀$ �    H�    H峀$ �    �)       �   �   �   %   �   s   �   +      �     k G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::Sequential2AbsoluteComponentIndex  >�   Format  A         � # g ! >u    SequentialComponentIndex  A           Aj       � 
 >u     n  Ah       � 
 >u     comp  A   &     d 
 >�    name  A   I     +          A  1     T  A M   M        �  	"t
 Z   }   N H                      @  h   �  �         $LN28  P   �  OFormat % X   u   OSequentialComponentIndex  O �   �           �   �     �       � �   � �   � �   � �   � �   � �&   � �1   � �F   � �N   � �V   � �^   � �d   � �l   � �q   � �t   � �{   � �~   � ��   � ��   � ��   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 3  �   7  �  
 S  �   W  �  
 �  5   �  5  
   �     �  
 侚�   ����C菻�   H�夨� p  斃�          �   �   W G            $       #   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::SupportsDepth  >�   Format  A           M        �    N                        @  h   �  �  �      �  OFormat  O  �   0           $   �     $       

 �    
 �#   
 �,      0     
 ~      �     
 �      �     
 侀�   t凒t2烂��   �   �   a G                      �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::SupportsSamplerFeedback  >�   Format  A                                  @     �  OFormat  O �   H              �     <        �     �
    �    �    �    �,       0      
 �       �      
 �       �      
 H冹H侚�   ����C�;�創   H��3襆��    L�
    C婦拎柳兝凐wa嬍嬃吷t-冭t冭t凐u.C婦拎�C婦拎�C婦拎�C婦拎柳凐t�羶�r秴�暲H兡H脣罥繠禩呉暲H兡H肏�    H峀$ �    H�    H峀$ �    �+       �   �   �   %   �   s   �   +      �   �  Y G            �      �   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::SupportsStencil  >�   Format  A         �  � 7 M        �  $
(b
:::
%"
 >u     comp  A   D     i 
 >�    name " A   ^     <   
    !  5   A  D       M        �  "��
 Z   }   N N H                      @  h   �  �  �         $LN31  P   �  OFormat  O  �   H           �   �     <       
 �   
 ��   
 ��   
 ��   
 ��   
 �,      0     
 �      �     
 �      �     
 �      �     
      #    
 �  z   �  z  
 �     �    
 岯變�w)凒!wH�  �   HＪr凒Zt凒\t凒'斃冒�2烂   �   �   [ G            4       3   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::ValidCastToR32UAV 
 >�   from  A         4  >�   to  A           A                                @     �  Ofrom     �  Oto  O �   P           4   �     D       �	 �    �	 �-   �	 �.   �	 �0   �	 �1   �	 �3   �	 �,      0     
 �      �     
 �      �     
 �      �     
          
 侚�   ����C菻�    H���攘�冟�          �   �   M G            $       #   �        �D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::YUV  >�   Format  A                                  @  h   �  �      �  OFormat  O �   0           $   �     $        �    � �#   � �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 H婹H�    H呉HE旅   `      �   �   : G                      O        �std::exception::what 
 >   this  AJ                                 @       Othis  O�   0              �     $       ^  �    _  �   `  �,      0     
 _      c     
 �      �     
  �      �           -      -      �    �      �           /      /      �    �      �           1      1      �    �                  3      3      �   ! 4                3      3      �       i           3      3      �   !   4                3      3      �   i   �           3      3      �   !                  3      3      �   �   �           3      3      �   
 
4 
2p    *           4      4      �    �      �           6      6      �    �      M           =      =      �    �      P           ?      ?      �    �      R           A      A      �    �      S           C      C      �   
 t	 d T 4 2�    �           P      P      �   
 t	 d T 4 2�    �           Q      Q      �    �      �           S      S      �    �      T           U      U      �    �      �           W      W      �                     `      `      �   ! 4                `      `      �      �           `      `      �   !   4                `      `      �   �   �           `      `      �    d 4 2p              a      a         � 痙 z� u� h�	 c�
 ^t YT
 M4 �      S          i      i      	    4     �          j      j          4     �          k      k         
 
t 4     }           l      l          4               w      w      !    �      @           y      y      '    �      �           {      {      -    20    2           |      |      3   
 
4 
2p    B           }      }      9    20    <           ~      ~      ?   
 
4 
2p    B                       E    R0    G           �      �      K    20    <           �      �      Q   
 
4 
2p    B           �      �      W                               �               Unknown exception                 .?AVexception@std@@     g               ����                      c                                     �      #                                     �      )         Unrecognized                                &      v                                 |      �      f                   .?AVinvalid_argument@std@@     g               ����                      y      '                   .?AVlogic_error@std@@     g               ����                            !   AbsoluteComponentIndex Format                                       c      �      �                         �                   �               ����    @                   c      �                                               �      �                         �                           �      �              ����    @                         �                                         y      �      �                         �                                   �      �      �              ����    @                   y      �      �   (   & 
�        std::exception::`vftable'    ]      ]  
    �   *   ( 
�        std::logic_error::`vftable'      j      j  
    �   /   - 
�        std::invalid_argument::`vftable'     m      m  
 �済藄�/�.帾枉庒痕z�%++驅%劤gV2D骒拶PIiT�%�;鎎斺兢?F君匦E}駾mk膆d蜌戀e襵焋=W杯喚秨c)>�/停舂艔w�靲�1懙罭v]<RF蒨97妨傁葅I*1�.�(幱忤瑤臗壎銙j蕕噎8�5孽Y镶�4�
D{;]U鯣椻��曾(违
汧��<�剀阊呿�@do騼N挰�漕C螔抖
磧累汆\{釨A椟)盎綗T垐蜎蚓F拽HLLd谆乤匢佧-sb⑨奟肥Fy'i扭怕4薈� d錵�6^m饷軠穉 籈�57*_昘辞uA� [(�3m6�屇載黏bf�倊㈠鶾#夰�罊鶺m緅>Y��闊z)Z桱=�?叫�1壉4徸>]~脼鮤�葅镐蛞`訠�/�璛零翍.]4.�9彮袵E錊鍉,���6硄陮齺f碭(CE�-\旍>?,a�
髿4�&�=讆(堃w�4Ν6躳嵮茇%u4麣r�<薬錍�楋g辁w办~M鈅�棠挤�:抜`�)旕慹A&Omｓm�>餿菶l滇貖Q嫾灐牵╓,c]C�%$<萙'D溉阜峤嚑 4唷缕9粕醏i�m75玳�9+蒧釅~<饟烴R逭6塶汒猗�謔x醖Ж/!'糍囍褲斠~C�7孉槭9℃�-�+|�0#鳩A辚A→]��>�(婼魕報1Τ��暄桊觞坥72塳�Rs�Tùq潼俑曅z蓣W�"�9�阿胋羕譢U(嶻线樗敯1Q衢��迩�1/`t玾梭谍+`�劣A笮縦z] 矮恬be勞"�(慟#y夊戈ボ濘q堝鷏澺�5(�嵐'|!爜Hй%臢嘆济}P哻慃f斿+藌┽{\媱�1⒄r-翉A婇訮弬轤冐鰀� 1Ef恾3c
 c蟜鯞�
Gs棼务$姈j�籏 Sn ;塇�/帹�	宮堙�
�8p鉑霵婬(Χ�秝Rc嚤踖p禭壚;|嵞))L滽7d墢�嚤踖p禭�0�lr棈犷A棊膬]�*	`嶀預棊膬穏�櫳B箩邆5>熦�).�箩邆5>�2q\綩箩邆5>7{�(��Ｋnb篫/W9啪鯑袞�*桒�}-.oVc弙�読鼕蝫辇e秹辋r頱
铱舝0j鎲±�6屗�鴱L�饈'譐j颂獘<��.�.-艈忓:W鯈nJo鐿榔g槕7
C嚤�Pe�袘X╚o頞��Ga�騸(橒譤Jl箞`=K鶗殘Hb醑蛦~膌渦!y�]╈藔蘷唂鞡\�.�谗c潺湐,eN楃ej坮4"婑煐蓿鎻W�HgvD佬蜅�-�,重}�^�y堦b扯鶸“G.瓪m螉萲遐F;%火械�B�
l��埶0�%鞦判d*n躓_鯢讅鞌&�
鋆曲 K�)w獄�+c菜0� &十朁�$�眒	WX縫X�(嵤纑猩OV7\厓礬@锫@�4Q�鬨RS璥啮璫蔲G,0攖71�喋�,<■嚳0謄�?壺攱�#
��2孀d��>sr蔠 d仇澇�:dxx
D繙�>#捜宒�1H
(�	|発X&B�/铏B3�襜來�棨J燂�
S蕈,j
>$�3�>炗緉M薎簥�;6 3Wh腍輡銑Q�.rB蚆�&�&轼c搾壩r蓁�蓖9鏜杖p釭l儔覷鄓�9貊.庳桋�,樊偋1薅WO髿僨;訰H�>軫�?鮵T\by8�PUq\f鵜s俄﹛�(D�6諡?頙�藻/J3�緌8愂璜耸澄祇�Xf�PZ,戓e�雸�岫笥縂勤冋
�? 荬藎e@#婅Te鞢竗��2朚�
擸喫7弧i惑梈窂=[髞�*?"鸄垊�p�4騯,D(�叏w長�2醷佻璖'ez阬摆槜0F0夑囊惏嵢0@檩欷喙Qm囜癒4g3廼E�寗�|綟崚鯣/(趱�)旮SD痍;w藲J0y嫷V� �/崪鎁污;旽^鰾�=Y逐`6O腪b<羮2擇鷐�y腿~偹蕺诵�T淃醉a�'貨xR豒�.�5Z+PAAW
煦%笩f裯N鵘J夥!�*j踪l窢蠯跻^�SQ�:賚窢蠯跻=��(_l窢蠯跻D惥繢技l窢蠯跻5R犵�喪g紒G熳�抪M嬾屓�:阾\Si�&觧_胩腄�
8|�>j=嘍筪9E\$L釉辥;想=藤l窢蠯跻綂硦贳3K賚窢蠯跻L垣"�/噘l窢蠯跻 +N癄i�:賚窢蠯跻蹃灬腤el窢蠯跻媟_蚴ノjx滑嶕賚1h狐鬀瞲滑嶕僖�.鳐賚窢蠯跻?D糒鋎r葙l窢蠯跻<礻.B賚窢蠯跻?D糒鋎r荸�<欠9h蹜T街R三痌Q
桖j�&s绛�塥7*G.�	FD��?6墯謀6	朖怤y懄蓑f熸*jCpQ如2蓸瘳�5*窆H利`�*‖�5*窆H�7M4懞�Tq��憵凡K劯郜�5*窆HN蕵淑*=官l窢蠯跻+$�"钞d唾l窢蠯跻籩项扭雵J-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o她餯繅鬮z邘c膋c雵J-WV8o��&{-�9E\$L釉蕤;[純o藵悗隙睼�l洯T铞m��輢A+|潗幭恫V逎悗隙睼揲M`穬�:蓰咨氖M�6摯(�[L�#輢A+|牅vc釗峕輢A+|�%�!庀E蛭�唔�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� ;綍銔!\[~Q薣`
PQ}扐珀�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       j                .debug$S       処 n             .debug$T       �                 .rdata         �&  :  条\�                         ,              v             �          .rdata                札蠴         �           .rdata                6飶{                   .rdata                挐�         ,          .rdata                v�'�         S          .rdata      	          �gP         y      	    .rdata      
          -p鸎         �      
    .rdata                M�         �          .rdata                S苿         �          .rdata      
          喀�               
    .rdata                蓺睯         .          .rdata                �+5�         X          .rdata                霞酌                   .rdata                卅         �          .rdata                襙�         �          .rdata                xSP�         �          .rdata                o簌�                   .rdata         
       v�2+         <          .rdata                鄃�         \          .rdata                �'C         {          .rdata                嘫Z�         �          .rdata                gM�         �          .rdata                芔盢         �          .rdata                 y鋖                   .rdata                u訁�         B          .rdata                VG'j         k          .rdata                4lEU         �          .rdata                b镋q         �          .rdata                 郒^�         �           .rdata      !          墍�                !    .rdata      "          =魹,         "      "    .rdata      #          駥磗         J      #    .rdata      $          攃VW         k      $    .rdata      %          Qy酎         �      %    .rdata      &          � 1y         �      &    .rdata      '   
       l�         �      '    .rdata      (   
       s帋�         �      (    .rdata      )          邻�+               )    .rdata      *   
       nm譹         1      *    .rdata      +          a/胜         Q      +    .rdata      ,   
       @J�         p      ,    .rdata      -   
       f齜�         �      -    .rdata      .   
       �覑         �      .    .rdata      /   	       �'         �      /    .rdata      0   	       ┚�         �      0    .rdata      1          &r�         �      1    .rdata      2          q[�                2    .rdata      3          鋞�         F      3    .rdata      4          {f         p      4    .rdata      5          W`�         �      5    .rdata      6          噱�0         �      6    .rdata      7   
       柿8�         �      7    .rdata      8          �矜         �      8    .rdata      9   
       j4xP               9    .rdata      :   
       
�         .      :    .rdata      ;   
       �*n:         N      ;    .rdata      <   
       ~L<j         j      <    .rdata      =   
       呀孈         �      =    .rdata      >   	       �攽         �      >    .rdata      ?   
       蘜諙         �      ?    .rdata      @   	       o娫         �      @    .rdata      A          珊妮         �      A    .rdata      B   	       鱀	�         	      B    .rdata      C          :蔾�         -	      C    .rdata      D   	       戋P[         G	      D    .rdata      E          �?+L         b	      E    .rdata      F   	       C:D         |	      F    .rdata      G   	       躝F�         �	      G    .rdata      H          ~
�         �	      H    .rdata      I          th�         �	      I    .rdata      J          餩菭         �	      J    .rdata      K   
       ?屐          
      K    .rdata      L   
       s薏�         @
      L    .rdata      M          b�>%         \
      M    .rdata      N   
       >gu         }
      N    .rdata      O   
       愘==         �
      O    .rdata      P          瑧魳         �
      P    .rdata      Q   
       壌         �
      Q    .rdata      R   
       贄�         �
      R    .rdata      S          )HbE               S    .rdata      T   
       }�         9      T    .rdata      U   
       蠷�         Y      U    .rdata      V   
       
3-         u      V    .rdata      W   
       絩<\         �      W    .rdata      X   
       壭�7         �      X    .rdata      Y   
       �3♂         �      Y    .rdata      Z   
       潦奢         �      Z    .rdata      [          #:�         	      [    .rdata      \          
蹧k         +      \    .rdata      ]          '/鴎         M      ]    .rdata      ^          蠨闡         o      ^    .rdata      _          �(O�         �      _    .rdata      `          哸*         �      `    .rdata      a          ��         �      a    .rdata      b          涒         
      b    .rdata      c          tq"�         :
      c    .rdata      d   
       j鱒         [
      d    .rdata      e   
       睙焚         w
      e    .rdata      f   
       |薖         �
      f    .rdata      g   
       糇輚         �
      g    .rdata      h          �"蹍         �
      h    .rdata      i          X' U         �
      i    .rdata      j          {グ               j    .rdata      k          炣��               k    .rdata      l          �m|�         6      l    .rdata      m          >幾2         M      m    .rdata      n          �)峝         c      n    .rdata      o          a         z      o    .rdata      p          d虶         �      p    .rdata      q          �$螘         �      q    .rdata      r          B償�         �      r    .rdata      s          C>Q�         �      s    .rdata      t          踟�         �      t    .rdata      u          刍�               u    .rdata      v          K�#{         "      v    .rdata      w          �3/+         7      w    .rdata      x          `蝼	         N      x    .rdata      y          姦臦         p      y    .rdata      z          *S呣         �      z    .rdata      {          �铥         �      {    .rdata      |           遯g�         �      |    .rdata      }   (       ��9         �      }    .rdata      ~          7=IO               ~        =   $          b  ($          �  @$          �  X$          �  x$            �$          '  �$          N  �$          t  �$          �  �$          �  %          �   %          �  8%            X%          ?  p%          _  x%            �%          �  �%          �  �%          �  �%            �%          5  �%          V  �%          w  �%          �  �%          �  �%          �  �%            &          .  &          W  0&          y  @&          �  P&          �  X&          �  `&             h&          "  p&          D  x&          f  �&          �  �&          �  �&          �  �&          �  �&            �&          8  �&          Z  �&          z  �&          �  �&          �  �&          �  �&            �&          (  �&      .text$mn       2      X于     .debug$S    �   <             .text$mn    �   <      .ズ     .debug$S    �   D  
       �    .text$mn    �   G      �:ra     .debug$S    �   �         �    .text$mn    �   <      .ズ     .debug$S    �   4  
       �    .text$mn    �         ��#     .debug$S    �   �          �    .text$mn    �         ��#     .debug$S    �   �          �    .text$mn    �   B      贘S     .debug$S    �   �          �    .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   6     L]Q     .debug$S    �   �  (       �    .text$mn    �   �      狑麻     .debug$S    �   �         �    .text$mn    �        �u�     .debug$S    �   8  (       �    .text$mn    �   S     �	&R     .debug$S    �   �	  h       �    .text$mn    �   �      浹NF     .debug$S    �   �         �    .text$mn    �   R      p�     .debug$S    �   P         �    .text$mn    �   P      �:07     .debug$S    �   X         �    .text$mn    �          B~�     .debug$S    �   $         �    .text$mn    �          艬     .debug$S    �            �    .text$mn    �          墫�     .debug$S    �   4         �    .text$mn    �   �      庵v     .debug$S    �            �    .text$mn    �   S      �橐     .debug$S    �   X         �    .text$mn    �   +      :&寜     .debug$S    �   \         �    .text$mn    �   3      荽c�     .debug$S    �   �         �    .text$mn    �   �     � v�     .debug$S    �   D         �    .text$mn    �   �      R彄c     .debug$S    �   �         �    .text$mn    �   T      ~隃4     .debug$S    �   �  
       �    .text$mn    �   �      I     .debug$S    �            �    .text$mn    �   �      C't�     .debug$S    �            �    .text$mn    �         裒仆     .debug$S    �            �    .text$mn    �   M      .�     .debug$S    �   T         �    .text$mn    �   _      .b@     .debug$S    �   �         �    .text$mn    �   �      F蛕U     .debug$S    �            �    .text$mn    �   #      i黹�     .debug$S    �            �    .text$mn    �          鍮A�     .debug$S    �   �          �    .text$mn    �          ]8ig     .debug$S    �            �    .text$mn    �   @      ?a�     .debug$S    �   8         �    .text$mn    �         柶浱     .debug$S    �            �    .text$mn    �         @7@�     .debug$S    �   �          �    .text$mn    �   �       C賹     .debug$S    �            �    .text$mn    �   %      25G�     .debug$S    �            �    .text$mn    �   �      IP�(     .debug$S    �   �  
       �    .text$mn    �         覲A     .debug$S    �   �          �    .text$mn    �   #      �-毆     .debug$S    �            �    .text$mn    �          �$�(     .debug$S    �   �          �    .text$mn    �   #      F5     .debug$S    �            �    .text$mn    �   *      k毢�     .debug$S    �   �         �    .text$mn    �   }       ｉ�     .debug$S    �   �         �    .text$mn    �   N      �(妅     .debug$S    �   �         �    .text$mn    �   �      #ヾ�     .debug$S    �     "       �    .text$mn    �          申�     .debug$S    �   �          �    .text$mn    �         6g婐     .debug$S    �   �          �    .text$mn    �   �      N竦z     .debug$S    �   �         �    .text$mn    �   �   	   .8�/     .debug$S    �   @         �    .text$mn    �     
   �6k     .debug$S    �     2       �    .text$mn    �   �     =U缭     .debug$S    �   d         �    .text$mn    �   #      gy豓     .debug$S    �            �    .text$mn    �          X渼�     .debug$S    �            �    .text$mn    �   �   	   凫�     .debug$S    �   p         �    .text$mn    �          � l     .debug$S    �            �    .text$mn    �   '      鮰r�     .debug$S    �            �    .text$mn    �   7       �
     .debug$S    �   �  
       �    .text$mn    �          軼熕     .debug$S    �            �    .text$mn    �   /      贼;)     .debug$S    �   T         �    .text$mn    �   >      %�"     .debug$S    �   �         �    .text$mn    �   	       L(^     .debug$S       �          �    .text$mn      $      呹�     .debug$S                   .text$mn      �      Z‰     .debug$S                  .text$mn      �      ⑽�     .debug$S      �            .text$mn      $      D,�     .debug$S      $            .text$mn    	         鹵     .debug$S    
           	   .text$mn      �      Rz歿     .debug$S                  .text$mn    
  4       Л�*     .debug$S      T  
       
   .text$mn      $      Y�1     .debug$S      �             .text$mn            崪覩     .debug$S      �                 N               ^      �        �      �        �      �        C      �        �      �        �      �        $      �        o      �        �      �              �        O      �        �      �        �      �        ^      �        �      �        	             f      �        �      �        (      �        w      �        �      �        "      �        �      �        �      �              �        f      �        �      �        �             @       �        �              �       �        	!      �        Z!      �        �!      �        �!      �        D"      �        �"      �        �"      �        (#      �        �#      	       �#      �        @$      �        �$      �        �$             <%      
       �%      �        �%      �        o&      �        �&      �        ('      �        �'      �        �'      �        C(      �        �(      �        
)      �        �)      �        *      �        G*      �        �*      �        +      �        e+      �        �+             ,             L,      �        �,               �,               �,              �,      �        -             1-      �        P-          i                   o-      �        �-      �        �-          i"                   �-      �        �-      �        .      �        B.      �        h.          i(                   �.           $LN29   �   �    $LN33       �    $LN30   �   �    $LN34       �    $LN10   �   �    $LN13       �    $LN34   �   �    $LN37       �    $LN4        �    $LN28   �      $LN32          $LN17   T   �    $LN18   H   �    $LN9    7   �    $LN13   :   �    $LN6    D   �    $LN10   M   �    $LN13       �    $LN10   P   �    $LN13       �    $LN10   R   �    $LN13       �    $LN10   S   �    $LN13       �    $LN17   H   �    $LN18   <   �    $LN4    3   �    $LN11   6   �    $LN13   9   �    $LN40   x   �    $LN41   d   �    $LN12   6   �    $LN16   C   �    $LN20   H   �    $LN28   R   �    $LN36   @   �    $LN19       �    $LN20          $LN30   �   �    $LN34       �    $LN9    T   �    $LN12       �    $LN30   �   �    $LN34       �    $LN41   �   �    $LN42   �   �    $LN5    W   �    $LN9    `   �    $LN11   g   �    $LN12   q   �    $LN13   �   �    $LN15   �   �    $LN56       �    $LN49       �    $LN23   �   �    $LN24   p   �    $LN4    (   �    $LN8    6   �    $LN11   D   �    $LN20   R   �    $LN21   `   �    $LN90       �    $LN138      �    $LN129      �    $LN17       �    $LN63   �  �    $LN64   �  �    $LN20   c   �    $LN24   �   �    $LN29   +  �    $LN33   g  �    $LN38   �  �    $LN43   �  �    $LN47     �    $LN19   x  �    $LN87       �    $LN7    @   �    $LN10       �    $LN31   �      $LN35          $LN5            $LN10       �    $LN7        �    $LN13       �    $LN10       �    $LN10       �    $LN16       �    .xdata               懐j灥        �.         .pdata              癗杉�        �.         .xdata               懐j灣        H/         .pdata              SIF2�        �/         .xdata               懐j炏        �/         .pdata              趨;S�        l0         .xdata               懐j炦        �0         .pdata              Vbv        G1         .xdata              拌h策        �1         .pdata              h`曌�        �1         .xdata              富鲞        Y2         .pdata              �]线        �2         .xdata              簣!@�        3         .pdata               掹鸧�        m3          .xdata      !         %蚘%�        �3      !   .pdata      "        瀪秇�        )4      "   .xdata      #         懐j�       �4      #   .pdata      $        ]騂1       �4      $   .xdata      %         懐j灩        Q5      %   .pdata      &        <讟补        �5      &   .xdata      '         懐j灊        �5      '   .pdata      (        企&U�        U6      (   .xdata      )         懐j灈        �6      )   .pdata      *        霍�         7      *   .xdata      +         懐j灖        R7      +   .pdata      ,        %舂郄        �7      ,   .xdata      -         U费楼        8      -   .pdata      .        欫楼        _8      .   .xdata      /         U费�       �8      /   .pdata      0        翊�/       9      0   .xdata      1         懐j炈        k9      1   .pdata      2        "趿�        �9      2   .xdata      3         懐j灡        �:      3   .pdata      4        <齦驯        �:      4   .xdata      5         懐j灲        /;      5   .pdata      6        "趿�        �;      6   .xdata      7         確�        <      7   .pdata      8        d$+�        z<      8   .xdata      9        }≠*�        �<      9   .pdata      :        �        J=      :   .xdata      ;        鄝�        �=      ;   .pdata      <        i蒵#�        >      <   .xdata      =         �	讜        �>      =   .pdata      >        e4哵�        �>      >   .xdata      ?  (       QD逆�        Z?      ?   .pdata      @        �6L�        �?      @   .xdata      A         
�        K@      A   .pdata      B        ;軐掚        虭      B   .xdata      C         
�        NA      C   .pdata      D        帗亞�        褹      D   .xdata      E         Uqi观        UB      E   .pdata      F        A刄7�              F   .xdata      G         
�        霣      G   .pdata      H        �,v�        oC      H   .xdata      I         懐j炁        馛      I   .pdata      J        砺�)�        LD      J   .xdata      K         懐j�             K   .pdata      L        v       鵇      L   .xdata      M         （亵        KE      M   .pdata      N         T枨        tE      N   .xdata      O         %蚘%�        淓      O   .pdata      P        惻竗�        肊      P   .xdata      Q         （亵�        镋      Q   .pdata      R        2Fb覅        F      R   .xdata      S         %蚘%�        >F      S   .pdata      T        惻竗�        gF      T   .xdata      U         僣純        廎      U   .pdata      V        羲X#�        糉      V   .xdata      W         （亵�        鐵      W   .pdata      X        2Fb襾        G      X   .xdata      Y         %蚘%�        GG      Y   .pdata      Z        惻竗�        uG      Z   .rdata      [                          [   .rdata      \         �;�         笹      \   .data$r     ]  $      騏糡         郍      ]   .xdata$x    ^  $      4��         鶪      ^       9H           .rdata      _                     LH     _   .rdata      `                     eH     `   .rdata      a  
       辋         僅      a   .xdata$x    b                           b   .xdata$x    c        虼�)         罤      c   .data$r     d  +      � e         郒      d   .xdata$x    e  $      4��         I      e   .data$r     f  &      �8X         NI      f   .xdata$x    g  $      4��         jI      g   .rdata      h         "��         璉      h   .rdata      i         X|a�         豂      i   .rdata$r    j  $      'e%�         馡      j   .rdata$r    k        �          	J      k   .rdata$r    l                     J      l   .rdata$r    m  $      Gv�:         5J      m   .rdata$r    n  $      'e%�         TJ      n   .rdata$r    o        }%B         nJ      o   .rdata$r    p                     咼      p   .rdata$r    q  $      `         濲      q   .rdata$r    r  $      'e%�         縅      r   .rdata$r    s        �弾         轏      s   .rdata$r    t                     鸍      t   .rdata$r    u  $      H衡�         K      u       >K           .debug$S    v  4          [   .debug$S    w  8          _   .debug$S    x  <          `   .chks64     y  �                JK  ?_Fake_alloc@std@@3U_Fake_allocator@1@B ?s_FormatDetail@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@0QBUFORMAT_DETAIL@1@B ?s_NumFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@0IB ?s_FormatNames@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@0QBQEBDB ??_C@_07NFANNNEC@UNKNOWN@ ??_C@_0BG@CMFJKBCJ@R32G32B32A32_TYPELESS@ ??_C@_0BD@KNGKKEJM@R32G32B32A32_FLOAT@ ??_C@_0BC@EHMDNOME@R32G32B32A32_UINT@ ??_C@_0BC@MIIDCLGE@R32G32B32A32_SINT@ ??_C@_0BD@GOAMEGLN@R32G32B32_TYPELESS@ ??_C@_0BA@KPOMBOOH@R32G32B32_FLOAT@ ??_C@_0P@KMOKKPAH@R32G32B32_UINT@ ??_C@_0P@CDKKFKKH@R32G32B32_SINT@ ??_C@_0BG@BNGENENG@R16G16B16A16_TYPELESS@ ??_C@_0BD@JMMCBNDL@R16G16B16A16_FLOAT@ ??_C@_0BD@OGCAIKFP@R16G16B16A16_UNORM@ ??_C@_0BC@IBPEJGGK@R16G16B16A16_UINT@ ??_C@_0BD@DAHJGJEC@R16G16B16A16_SNORM@ ??_C@_0BC@OLEGDMK@R16G16B16A16_SINT@ ??_C@_0BA@IHLNEHMF@R32G32_TYPELESS@ ??_C@_0N@NLLJGLAL@R32G32_FLOAT@ ??_C@_0M@EIENNJIE@R32G32_UINT@ ??_C@_0M@MHANCMCE@R32G32_SINT@ ??_C@_0BC@HALOGJDF@R32G8X24_TYPELESS@ ??_C@_0BF@BPLGFNGE@D32_FLOAT_S8X24_UINT@ ??_C@_0BJ@FIIBKELG@R32_FLOAT_X8X24_TYPELESS@ ??_C@_0BI@DANKEMNP@X32_TYPELESS_G8X24_UINT@ ??_C@_0BF@EMHOOJAB@R10G10B10A2_TYPELESS@ ??_C@_0BC@PCMDHHOE@R10G10B10A2_UNORM@ ??_C@_0BB@GDFFGCHG@R10G10B10A2_UINT@ ??_C@_0BA@GCABFNMI@R11G11B10_FLOAT@ ??_C@_0BC@FNLKHIFC@R8G8B8A8_TYPELESS@ ??_C@_0P@KJCDHMJB@R8G8B8A8_UNORM@ ??_C@_0BE@NMLEJAEP@R8G8B8A8_UNORM_SRGB@ ??_C@_0O@FNPAAKMJ@R8G8B8A8_UINT@ ??_C@_0P@HPHKJPIM@R8G8B8A8_SNORM@ ??_C@_0O@NCLAPPGJ@R8G8B8A8_SINT@ ??_C@_0BA@GKHFJEFC@R16G16_TYPELESS@ ??_C@_0N@CNOHKAGK@R16G16_FLOAT@ ??_C@_0N@FHAFDHAO@R16G16_UNORM@ ??_C@_0M@KPKAODFB@R16G16_UINT@ ??_C@_0N@IBFMNEBD@R16G16_SNORM@ ??_C@_0M@CAOABGPB@R16G16_SINT@ ??_C@_0N@BFMBPJGN@R32_TYPELESS@ ??_C@_09KGBHGKOP@D32_FLOAT@ ??_C@_09IMKHJLEA@R32_FLOAT@ ??_C@_08DOAILHPA@R32_UINT@ ??_C@_08LBEIECFA@R32_SINT@ ??_C@_0P@MKDEIODO@R24G8_TYPELESS@ ??_C@_0BC@HELPDOMD@D24_UNORM_S8_UINT@ ??_C@_0BG@IOKCKKBD@R24_UNORM_X8_TYPELESS@ ??_C@_0BF@KLOKFMAP@X24_TYPELESS_G8_UINT@ ??_C@_0O@PMCEJHGP@R8G8_TYPELESS@ ??_C@_0L@KENAKLPD@R8G8_UNORM@ ??_C@_09MDENFGED@R8G8_UINT@ ??_C@_0L@HCIJEIOO@R8G8_SNORM@ ??_C@_09EMANKDOD@R8G8_SINT@ ??_C@_0N@EJCDBEHA@R16_TYPELESS@ ??_C@_09CGBLLNDM@R16_FLOAT@ ??_C@_09HGEJNLPH@D16_UNORM@ ??_C@_09FMPJCKFI@R16_UNORM@ ??_C@_08IIGCJEJO@R16_UINT@ ??_C@_09IKKAMJEF@R16_SNORM@ ??_C@_08HCCGBDO@R16_SINT@ ??_C@_0M@FJOOIDFJ@R8_TYPELESS@ ??_C@_08JEPPKPKG@R8_UNORM@ ??_C@_07FJLGOKKM@R8_UINT@ ??_C@_08ECKGEMLL@R8_SNORM@ ??_C@_07NGPGBPAM@R8_SINT@ ??_C@_08BLCNBBC@A8_UNORM@ ??_C@_08ILLALEIN@R1_UNORM@ ??_C@_0BD@JFOHDLOO@R9G9B9E5_SHAREDEXP@ ??_C@_0BA@PGCMLDNO@R8G8_B8G8_UNORM@ ??_C@_0BA@LDIDOLFK@G8R8_G8B8_UNORM@ ??_C@_0N@BMGOLIEC@BC1_TYPELESS@ ??_C@_09KPMHEJPK@BC1_UNORM@ ??_C@_0P@NBCABHK@BC1_UNORM_SRGB@ ??_C@_0N@IFIMNOED@BC2_TYPELESS@ ??_C@_09CBEIEOBJ@BC2_UNORM@ ??_C@_0P@LANIGNLE@BC2_UNORM_SRGB@ ??_C@_0N@EEACABID@BC3_TYPELESS@ ??_C@_09ONOCEOIH@BC3_UNORM@ ??_C@_0P@GNEOLEDB@BC3_UNORM_SRGB@ ??_C@_0N@GNDJBEAA@BC4_TYPELESS@ ??_C@_09OHCHEHJO@BC4_UNORM@ ??_C@_09DBHOKEID@BC4_SNORM@ ??_C@_0N@KMLHMLMA@BC5_TYPELESS@ ??_C@_09CLINEHAA@BC5_UNORM@ ??_C@_09PNNEKEBN@BC5_SNORM@ ??_C@_0N@CNECHDLM@B5G6R5_UNORM@ ??_C@_0P@IAFDMGDL@B5G5R5A1_UNORM@ ??_C@_0P@EDLHCHBF@B8G8R8A8_UNORM@ ??_C@_0P@EDNENDDP@B8G8R8X8_UNORM@ ??_C@_0BL@LAGPOJJM@R10G10B10_XR_BIAS_A2_UNORM@ ??_C@_0BC@BFKLBICG@B8G8R8A8_TYPELESS@ ??_C@_0BE@NKFFAFPE@B8G8R8A8_UNORM_SRGB@ ??_C@_0BC@IEBKMPEL@B8G8R8X8_TYPELESS@ ??_C@_0BE@BCLBMGNI@B8G8R8X8_UNORM_SRGB@ ??_C@_0O@IGGGPHEM@BC6H_TYPELESS@ ??_C@_09EKICPNJI@BC6H_UF16@ ??_C@_09MFMCAIDI@BC6H_SF16@ ??_C@_0N@PENLHCAB@BC7_TYPELESS@ ??_C@_09GJKIEAHN@BC7_UNORM@ ??_C@_0P@KNPHNOKH@BC7_UNORM_SRGB@ ??_C@_04GMNNCPLK@AYUV@ ??_C@_04IJHIHDPK@Y410@ ??_C@_04NPCCNEHM@Y416@ ??_C@_04PIKBGFGC@NV12@ ??_C@_04LAKIGNM@P010@ ??_C@_04FNFACBFK@P016@ ??_C@_0L@INGMEGHC@420_OPAQUE@ ??_C@_04HOBABLIG@YUY2@ ??_C@_04KMBDCMCG@Y210@ ??_C@_04PKEJILKA@Y216@ ??_C@_04NDIMDGKB@NV11@ ??_C@_04CCBKNABH@AI44@ ??_C@_04NHNOLDDJ@IA44@ ??_C@_02HLJNLEKG@P8@ ??_C@_04BCPCDLBO@A8P8@ ??_C@_0P@CBNLAOHI@B4G4R4A4_UNORM@ ??_C@_04GIBIKOGI@P208@ ??_C@_04OHFIFLMI@V208@ ??_C@_04MCDDAEBE@V408@ ??_C@_0CA@JCNLIN@SAMPLER_FEEDBACK_MIN_MIP_OPAQUE@ ??_C@_0CI@CPLLMKLL@SAMPLER_FEEDBACK_MIP_REGION_USE@ ??_C@_0P@GHGFMBCP@A4B4G4R4_UNORM@ ?D3DFCS_UNKNOWN@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R32G32B32A32@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R32G32B32@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R16G16B16A16@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R32G32@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R32G8X24@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R11G11B10@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R8G8B8A8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R16G16@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R32@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R24G8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R8G8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R16@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_A8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R1@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R9G9B9E5@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R8G8_B8G8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_G8R8_G8B8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC1@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC2@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC3@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC4@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC5@@3QBW4DXGI_FORMAT@@B ?D3DFCS_B5G6R5@@3QBW4DXGI_FORMAT@@B ?D3DFCS_B5G5R5A1@@3QBW4DXGI_FORMAT@@B ?D3DFCS_B8G8R8A8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_B8G8R8X8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_R10G10B10A2@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC6H@@3QBW4DXGI_FORMAT@@B ?D3DFCS_BC7@@3QBW4DXGI_FORMAT@@B ?D3DFCS_AYUV@@3QBW4DXGI_FORMAT@@B ?D3DFCS_NV12@@3QBW4DXGI_FORMAT@@B ?D3DFCS_YUY2@@3QBW4DXGI_FORMAT@@B ?D3DFCS_P010@@3QBW4DXGI_FORMAT@@B ?D3DFCS_P016@@3QBW4DXGI_FORMAT@@B ?D3DFCS_NV11@@3QBW4DXGI_FORMAT@@B ?D3DFCS_420_OPAQUE@@3QBW4DXGI_FORMAT@@B ?D3DFCS_Y410@@3QBW4DXGI_FORMAT@@B ?D3DFCS_Y416@@3QBW4DXGI_FORMAT@@B ?D3DFCS_Y210@@3QBW4DXGI_FORMAT@@B ?D3DFCS_Y216@@3QBW4DXGI_FORMAT@@B ?D3DFCS_AI44@@3QBW4DXGI_FORMAT@@B ?D3DFCS_IA44@@3QBW4DXGI_FORMAT@@B ?D3DFCS_P8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_A8P8@@3QBW4DXGI_FORMAT@@B ?D3DFCS_B4G4R4A4@@3QBW4DXGI_FORMAT@@B ?D3DFCS_P208@@3QBW4DXGI_FORMAT@@B ?D3DFCS_V208@@3QBW4DXGI_FORMAT@@B ?D3DFCS_V408@@3QBW4DXGI_FORMAT@@B ?D3DFCS_A4B4G4R4@@3QBW4DXGI_FORMAT@@B ??3@YAXPEAX_K@Z ?GetNumFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIXZ ?GetFormatTable@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAPEBUFORMAT_DETAIL@1@XZ ?GetHighestDefinedFeatureLevel@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FEATURE_LEVEL@@XZ ?GetFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4DXGI_FORMAT@@_K@Z ?FormatExists@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?FormatExistsInHeader@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@_N@Z ?GetByteAlignment@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?IsBlockCompressFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?GetName@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAPEBDW4DXGI_FORMAT@@_N@Z ?IsSRGBFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?GetBitsPerStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetBitsPerDepth@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetFormatReturnTypes@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXW4DXGI_FORMAT@@PEAW4D3D_FORMAT_COMPONENT_INTERPRETATION@@@Z ?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetMinNumComponentsInFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@0@Z ?Sequential2AbsoluteComponentIndex@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@I@Z ?CanBeCastEvenFullyTyped@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@W4D3D_FEATURE_LEVEL@@@Z ?GetAddressingBitsPerAlignedSize@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAEW4DXGI_FORMAT@@@Z ?GetParentFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4DXGI_FORMAT@@W42@@Z ?GetFormatCastSet@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAPEBW4DXGI_FORMAT@@W42@@Z ?GetLayout@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_LAYOUT@@W4DXGI_FORMAT@@@Z ?GetTypeLevel@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_TYPE_LEVEL@@W4DXGI_FORMAT@@@Z ?GetBitsPerUnit@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetBitsPerUnitThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetWidthAlignment@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetHeightAlignment@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetDepthAlignment@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?Planar@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?NonOpaquePlanar@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?YUV@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?Opaque@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?FamilySupportsStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?NonOpaquePlaneCount@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?DX9VertexOrIndexFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?DX9TextureFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?FloatNormTextureFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z ?DepthOnlyFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?GetPlaneCount@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAEW4DXGI_FORMAT@@@Z ?MotionEstimatorAllowedInputFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?SupportsSamplerFeedback@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?DecodeHistogramAllowedForOutputFormatSupport@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?GetPlaneSliceFromViewFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAEW4DXGI_FORMAT@@0@Z ?FloatAndNotFloatFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z ?SNORMAndUNORMFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z ?ValidCastToR32UAV@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z ?IsSupportedTextureDisplayableFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@_N@Z ?GetFormatComponentInterpretation@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_COMPONENT_INTERPRETATION@@W4DXGI_FORMAT@@I@Z ?GetBitsPerComponent@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@I@Z ?GetComponentName@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_COMPONENT_NAME@@W4DXGI_FORMAT@@I@Z ?CalculateExtraPlanarRows@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z ?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z ?CalculateMinimumRowMajorSlicePitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IIAEAI@Z ?GetYCbCrChromaSubsampling@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXW4DXGI_FORMAT@@AEAI1@Z ?CalculateResourceSize@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJIIIW4DXGI_FORMAT@@IIAEA_KPEAUD3D12_MEMCPY_DEST@@@Z ?GetTileShape@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXPEAUD3D12_TILE_SHAPE@@W4DXGI_FORMAT@@W4D3D12_RESOURCE_DIMENSION@@I@Z ?Get4KTileShape@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXPEAUD3D12_TILE_SHAPE@@W4DXGI_FORMAT@@W4D3D12_RESOURCE_DIMENSION@@I@Z ?GetMipDimensions@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXEPEA_K00@Z ?GetPlaneSubsampledSizeAndFormatForCopyableLayout@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXIW4DXGI_FORMAT@@IIAEAW42@AEAI22@Z ?GetDetailTableIndex@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetDetailTableIndexNoThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?GetDetailTableIndexThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z ?SupportsDepth@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?SupportsStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z ?GetFormatDetail@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@CAPEBUFORMAT_DETAIL@1@W4DXGI_FORMAT@@@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0logic_error@std@@QEAA@AEBV01@@Z ??_Glogic_error@std@@UEAAPEAXI@Z ??_Elogic_error@std@@UEAAPEAXI@Z ??0invalid_argument@std@@QEAA@PEBD@Z ??1invalid_argument@std@@UEAA@XZ ??0invalid_argument@std@@QEAA@AEBV01@@Z ??_Ginvalid_argument@std@@UEAAPEAXI@Z ??_Einvalid_argument@std@@UEAAPEAXI@Z _CxxThrowException $unwind$?GetBitsPerStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$?GetBitsPerStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $unwind$?GetBitsPerDepth@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$?GetBitsPerDepth@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $unwind$?GetFormatReturnTypes@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXW4DXGI_FORMAT@@PEAW4D3D_FORMAT_COMPONENT_INTERPRETATION@@@Z $pdata$?GetFormatReturnTypes@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXW4DXGI_FORMAT@@PEAW4D3D_FORMAT_COMPONENT_INTERPRETATION@@@Z $unwind$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $chain$0$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$0$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $chain$2$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$2$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $chain$3$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$3$?GetNumComponentsInFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $unwind$?GetMinNumComponentsInFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@0@Z $pdata$?GetMinNumComponentsInFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@0@Z $unwind$?Sequential2AbsoluteComponentIndex@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@I@Z $pdata$?Sequential2AbsoluteComponentIndex@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@I@Z $unwind$?GetBitsPerUnitThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$?GetBitsPerUnitThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $unwind$?DX9VertexOrIndexFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z $pdata$?DX9VertexOrIndexFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z $unwind$?DX9TextureFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z $pdata$?DX9TextureFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z $unwind$?FloatNormTextureFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z $pdata$?FloatNormTextureFormat@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAHW4DXGI_FORMAT@@@Z $unwind$?FloatAndNotFloatFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z $pdata$?FloatAndNotFloatFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z $unwind$?SNORMAndUNORMFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z $pdata$?SNORMAndUNORMFormats@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@0@Z $unwind$?GetFormatComponentInterpretation@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_COMPONENT_INTERPRETATION@@W4DXGI_FORMAT@@I@Z $pdata$?GetFormatComponentInterpretation@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_COMPONENT_INTERPRETATION@@W4DXGI_FORMAT@@I@Z $unwind$?GetBitsPerComponent@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@I@Z $pdata$?GetBitsPerComponent@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@I@Z $unwind$?GetComponentName@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_COMPONENT_NAME@@W4DXGI_FORMAT@@I@Z $pdata$?GetComponentName@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA?AW4D3D_FORMAT_COMPONENT_NAME@@W4DXGI_FORMAT@@I@Z $unwind$?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z $pdata$?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z $chain$0$?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z $pdata$0$?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z $chain$2$?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z $pdata$2$?CalculateMinimumRowMajorRowPitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IAEAI@Z $unwind$?CalculateMinimumRowMajorSlicePitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IIAEAI@Z $pdata$?CalculateMinimumRowMajorSlicePitch@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJW4DXGI_FORMAT@@IIAEAI@Z $unwind$?CalculateResourceSize@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJIIIW4DXGI_FORMAT@@IIAEA_KPEAUD3D12_MEMCPY_DEST@@@Z $pdata$?CalculateResourceSize@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAJIIIW4DXGI_FORMAT@@IIAEA_KPEAUD3D12_MEMCPY_DEST@@@Z $unwind$?GetTileShape@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXPEAUD3D12_TILE_SHAPE@@W4DXGI_FORMAT@@W4D3D12_RESOURCE_DIMENSION@@I@Z $pdata$?GetTileShape@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXPEAUD3D12_TILE_SHAPE@@W4DXGI_FORMAT@@W4D3D12_RESOURCE_DIMENSION@@I@Z $unwind$?Get4KTileShape@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXPEAUD3D12_TILE_SHAPE@@W4DXGI_FORMAT@@W4D3D12_RESOURCE_DIMENSION@@I@Z $pdata$?Get4KTileShape@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXPEAUD3D12_TILE_SHAPE@@W4DXGI_FORMAT@@W4D3D12_RESOURCE_DIMENSION@@I@Z $unwind$?GetMipDimensions@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXEPEA_K00@Z $pdata$?GetMipDimensions@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXEPEA_K00@Z $unwind$?GetPlaneSubsampledSizeAndFormatForCopyableLayout@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXIW4DXGI_FORMAT@@IIAEAW42@AEAI22@Z $pdata$?GetPlaneSubsampledSizeAndFormatForCopyableLayout@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAXIW4DXGI_FORMAT@@IIAEAW42@AEAI22@Z $unwind$?GetDetailTableIndexThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $pdata$?GetDetailTableIndexThrow@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SAIW4DXGI_FORMAT@@@Z $unwind$?SupportsStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z $pdata$?SupportsStencil@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@SA_NW4DXGI_FORMAT@@@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0logic_error@std@@QEAA@AEBV01@@Z $pdata$??0logic_error@std@@QEAA@AEBV01@@Z $unwind$??_Glogic_error@std@@UEAAPEAXI@Z $pdata$??_Glogic_error@std@@UEAAPEAXI@Z $unwind$??0invalid_argument@std@@QEAA@PEBD@Z $pdata$??0invalid_argument@std@@QEAA@PEBD@Z $unwind$??0invalid_argument@std@@QEAA@AEBV01@@Z $pdata$??0invalid_argument@std@@QEAA@AEBV01@@Z $unwind$??_Ginvalid_argument@std@@UEAAPEAXI@Z $pdata$??_Ginvalid_argument@std@@UEAAPEAXI@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_7logic_error@std@@6B@ ??_7invalid_argument@std@@6B@ ??_C@_0N@HLCKEHJE@Unrecognized@ _TI3?AVinvalid_argument@std@@ _CTA3?AVinvalid_argument@std@@ ??_R0?AVinvalid_argument@std@@@8 _CT??_R0?AVinvalid_argument@std@@@8??0invalid_argument@std@@QEAA@AEBV01@@Z24 ??_R0?AVlogic_error@std@@@8 _CT??_R0?AVlogic_error@std@@@8??0logic_error@std@@QEAA@AEBV01@@Z24 ??_C@_0BH@DFFNCDJJ@AbsoluteComponentIndex@ ??_C@_06PEPCFMNJ@Format@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4logic_error@std@@6B@ ??_R3logic_error@std@@8 ??_R2logic_error@std@@8 ??_R1A@?0A@EA@logic_error@std@@8 ??_R4invalid_argument@std@@6B@ ??_R3invalid_argument@std@@8 ??_R2invalid_argument@std@@8 ??_R1A@?0A@EA@invalid_argument@std@@8 __ImageBase 