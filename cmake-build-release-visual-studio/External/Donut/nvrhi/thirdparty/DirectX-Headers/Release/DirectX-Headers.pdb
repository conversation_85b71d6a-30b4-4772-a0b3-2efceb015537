Microsoft C/C++ MSF 7.00
DS         c   �      a                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           �           ������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������                     ��     ����    ����    ����
 t    蝰
 !    蝰
    
        t        
     蝰
    F   �              __crt_locale_pointers .?AU__crt_locale_pointers@@ 
     >   �              __crt_locale_data .?AU__crt_locale_data@@ 
 	    F   �              __crt_multibyte_data .?AU__crt_multibyte_data@@ 蝰
     * 
 
    locinfo 蝰
    mbcinfo 蝰F   
           __crt_locale_pointers .?AU__crt_locale_pointers@@ 
    
 
    N   �              __crt_locale_data_public .?AU__crt_locale_data_public@@ 蝰
     
     ^ 
     _locale_pctype 篁�
 t    _locale_mb_cur_max 篁�
 u    _locale_lc_codepage 蝰N              __crt_locale_data_public .?AU__crt_locale_data_public@@ 蝰             t                      
              
                  t            t   t      t        
     蝰
 !    
 "    蝰
     蝰
 u    蝰
 !  
�  
 #    蝰
      t                  
 !   
 q    蝰
 ,        -  #    #      .  
 q        q  -  0   q     1  
 ,   
 q        -  q    -     5      -  -   -     7  
 p    蝰
 9        :  #    #      ;      :  t    :     =  
 p        :  :   :     @  
 9   &   �              _GUID .?AU_GUID@@ 
 C   蝰
 D         #     馚 
 "     Data1 
 !    Data2 
 !    Data3 
 F   Data4 &   G           _GUID .?AU_GUID@@ 
 D  ,      I  I   t      J   0      J  
     蝰
 M    
     蝰
 O    
 p    蝰
 Q    
 p    蝰
 S    
     蝰
 U    
     蝰
 W    
     蝰
 Y    
     蝰
 [    
    R   p      ]  
      蝰
 _        T  p          a  
      蝰
 c    
    V         e  
 !    蝰
 g        X            i  
 !    蝰
 k    
    Z         m  
 "    蝰
 o        N            q  
 "    蝰
 s    
 t    蝰
 u    
 t    蝰
 w    
 u    蝰
 y    
 u    蝰
 {    
    \         }  
 #    蝰
         P            �  
 #    蝰
 �    
     
 �    
     
 �    �   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 篁�  JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG �  JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS 蝰Z   t   �  JOB_OBJECT_NET_RATE_CONTROL_FLAGS .?AW4JOB_OBJECT_NET_RATE_CONTROL_FLAGS@@ �
 �    
 �  ,    JOB_OBJECT_IO_RATE_CONTROL_ENABLE   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME �  JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL �  JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP �  JOB_OBJECT_IO_RATE_CONTROL_VALID_FLAGS 篁馴   t   �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS .?AW4JOB_OBJECT_IO_RATE_CONTROL_FLAGS@@ 篁�
 �    
 �  ,  b   �              _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG .?AU_CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG@@ 
 �    & 
 "     Size �
 -   TriggerId b   �           _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG .?AU_CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG@@ 
 -    J   �              _TP_CALLBACK_ENVIRON_V3 .?AU_TP_CALLBACK_ENVIRON_V3@@ 
 �    .   �              _TP_POOL .?AU_TP_POOL@@ 蝰
 �    >   �              _TP_CLEANUP_GROUP .?AU_TP_CLEANUP_GROUP@@ 
 �                   �  
 �    B   �              _ACTIVATION_CONTEXT .?AU_ACTIVATION_CONTEXT@@ 
 �    F   �              _TP_CALLBACK_INSTANCE .?AU_TP_CALLBACK_INSTANCE@@ 
 �        �           �  
 �    f   �      _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> .?AT<unnamed-type-u>@_TP_CALLBACK_ENVIRON_V3@@ 癞    TP_CALLBACK_PRIORITY_HIGH   TP_CALLBACK_PRIORITY_NORMAL 蝰  TP_CALLBACK_PRIORI�.1E馟h   M2蘱鰼臚哱騞箮�   /names                            躋3Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h �      i        b  V     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h 篁� H       N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h � �    �2   �    3   �    猈   �    瀆   �    筤   �    羄   �    胇  Z     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h � �  
  �  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h 篁� �    �   R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h 篁� �    �   �    �   �    �=   �    >  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h 蝰               R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h      -   	    �   
      V     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h 篁� D    .  N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h � L    �  Z     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12.h 篁� N  !  �   R  !  �   V  !  8	   Z  !  \	   ^  !  �	   b  !  �	   f  !  ?
   j  !  Y
   n  !  �   r  !  �   v  !  n   z  !  v   ~  !  �   �  !  �   �  !  
   �  !  3
   �  !  A   �  !  ]   �  !  -   �  !  R   �  !  Z   �  !  n   �  !  �   �  !     �  !  +   �  !  w'   �  !  2,   �  !  �.   �  !  �.   �  !  �.   �  !  p/   �  !  ~/   �  !  �4   �  !  �4   �  !  �7   �  !  �7   �  !  /8   �  !  �8   �  !  �8   �  !  �8   �  !  
9   �  !  �9   �  !  o=   �  !  �=   �  !  RQ     !  鱍     !  砋   
  !  鞺     !  V     !  3V     !  ?W     !  IW  b     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12sdklayers.h 蝰   V  -   "  V  �   &  V  �   *  V     .  V     2  !  zr   6  !  +s  Z     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h 蝰 P  ^      X  ^  �   \  ^  �   k  ^  |   t  ^  �  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h 篁� }  d  H  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h  �  f  W    �  f  6    �     q  J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits � �  j  W	   �  j  `	   �  j  i	   �  j  r	  V     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h 篁�   o  0      o      C  o  g    Y  o  r    o  o  �   J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception 篁� �  u  �   �     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\predefined C++ types (compiler internal)  �  w  �    �  u  N   �  u  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility  �  {  C   �  {  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory � �  ~        ~  �     ~  �     ~  �   %  ~  �   6  ~  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h � N  �  [   ^  ~  �   }  ~  �     ~  �   �  ~  �  J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm 篁� �  �  D  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring � �  �  &
   �  �  I
   b  �     �  �  &
   �  �  I
   &  �     a  �  �.1E馟h   M2蘱鰼臚哱騞箮�                          躋3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          <                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    TY_LOW �  TP_CALLBACK_PRIORITY_INVALID �  TP_CALLBACK_PRIORITY_COUNT 篁馚   t   �  _TP_CALLBACK_PRIORITY .?AW4_TP_CALLBACK_PRIORITY@@ �

 "     Version 蝰
 �   Pool �
 �   CleanupGroup �
 �   CleanupGroupCancelCallback 篁�
     RaceDll 蝰
 �  ( ActivationContext 
 �  0 FinalizationCallback �  �  <unnamed-type-u> 篁�
 �  8 u 
 �  < CallbackPriority �
 "   @ Size 馢  �          H _TP_CALLBACK_ENVIRON_V3 .?AU_TP_CALLBACK_ENVIRON_V3@@ 
 �    
 �    
 �    
     
 �    
 �    
 �    
 �    �   �              _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s> .?AU<unnamed-type-s>@<unnamed-type-u>@_TP_CALLBACK_ENVIRON_V3@@ 蝰: 
 "     Flags   �  <unnamed-type-s> 篁�
 �    s f  �   _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> .?AT<unnamed-type-u>@_TP_CALLBACK_ENVIRON_V3@@ �
 �    
 �    
 �    
 "    蝰
 "   蝰
 "   蝰F 
 �    LongFunction �
 �    Persistent 篁�
 �    Private 蝰�  �           _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s> .?AU<unnamed-type-s>@<unnamed-type-u>@_TP_CALLBACK_ENVIRON_V3@@ 蝰&   �              _TEB .?AU_TEB@@ 蝰
 �             B   UserEnabled 蝰  KernelEnabled   Wow64Container 篁�>   t   �  _MACHINE_ATTRIBUTES .?AW4_MACHINE_ATTRIBUTES@@ �
 �    
 �  ,  &        #     #   "   "   "         �  F    FILE_WRITE_FLAGS_NONE   FILE_WRITE_FLAGS_WRITE_THROUGH 篁�:   t   �  FILE_WRITE_FLAGS .?AW4FILE_WRITE_FLAGS@@ 篁�
 �    
 �  ,  
    �         �      �  �         �      �  �  �         �      �           �      �  �         �  
    "          �  R    DCDC_DEFAULT �  DCDC_DISABLE_FONT_UPDATE �  DCDC_DISABLE_RELAYOUT ^   t   �  DIALOG_CONTROL_DPI_CHANGE_BEHAVIORS .?AW4DIALOG_CONTROL_DPI_CHANGE_BEHAVIORS@@ �
 �    
 �  ,  n    DDC_DEFAULT 蝰  DDC_DISABLE_ALL 蝰  DDC_DISABLE_RESIZE 篁�  DDC_DISABLE_CONTROL_RELAYOUT 馧   t   �  DIALOG_DPI_CHANGE_BEHAVIORS .?AW4DIALOG_DPI_CHANGE_BEHAVIORS@@ �
 �    
 �  ,  �    AR_ENABLED 篁�  AR_DISABLED 蝰  AR_SUPPRESSED   AR_REMOTESESSION �  AR_MULTIMON 蝰  AR_NOSENSOR 蝰   AR_NOT_SUPPORTED � @ AR_DOCKED  � AR_LAPTOP . 	  t   �  tagAR_STATE .?AW4tagAR_STATE@@ �
 �    
 �  ,  �    ORIENTATION_PREFERENCE_NONE 蝰  ORIENTATION_PREFERENCE_LANDSCAPE �  ORIENTATION_PREFERENCE_PORTRAIT 蝰  ORIENTATION_PREFERENCE_LANDSCAPE_FLIPPED �  ORIENTATION_PREFERENCE_PORTRAIT_FLIPPED 蝰F   t   �  ORIENTATION_PREFERENCE .?AW4ORIENTATION_PREFERENCE@@ 篁�
 �    
 �  ,  
    q   q     �  
 q    蝰
 �    
    �   �     �   t      7  
 q    蝰
 �        �  �   t      �  
    -   t      �  
    �   t      �      �  q    �     �      �  �   �     �      q  #   -   t      �   #      �  
     蝰*   �              _ldiv_t .?AU_ldiv_t@@ " 
      quot �
     rem 蝰*   �           _ldiv_t .?AU_ldiv_t@@ .   �              _lldiv_t .?AU_lldiv_t@@ 蝰" 
      quot �
     rem 蝰.              _lldiv_t .?AU_lldiv_t@@ 蝰�    REGCLS_SINGLEUSE �  REGCLS_MULTIPLEUSE 篁�  REGCLS_MULTI_SEPARATE   REGCLS_SUSPENDED �  REGCLS_SURROGATE �  REGCLS_AGILE �*   t     tagREGCLS .?AW4tagREGCLS@@ �
     
   ,  �    COWAIT_DEFAULT 篁�  COWAIT_WAITALL 篁�  COWAIT_ALERTABLE �  COWAIT_INPUTAVAILABLE   COWAIT_DISPATCH_CALLS   COWAIT_DISPATCH_WINDOW_MESSAGES 蝰6   t     tagCOWAIT_FLAGS .?AW4tagCOWAIT_FLAGS@@ �
 	    
 	  ,  V    CWMO_DEFAULT �  CWMO_DISPATCH_CALLS 蝰  CWMO_DISPATCH_WINDOW_MESSAGES .   t     CWMO_FLAGS .?AW4CWMO_FLAGS@@ 篁�
 
    
 
  ,  :   �              tagPROPVARIANT .?AUtagPROPVARIANT@@ 蝰
     2   �      _LARGE_INTEGER .?AT_LARGE_INTEGER@@ 蝰2   �      _ULARGE_INTEGER .?AT_ULARGE_INTEGER@@    �      tagCY .?ATtagCY@@ .   �              _FILETIME .?AU_FILETIME@@ 
 C    2   �              tagCLIPDATA .?AUtagCLIPDATA@@ 
     2   �              tagBSTRBLOB .?AUtagBSTRBLOB@@ *   �              tagBLOB .?AUtagBLOB@@ .   �              IUnknown .?AUIUnknown@@ 蝰
     .   �              IDispatch .?AUIDispatch@@ 
     *   �              IStream .?AUIStream@@ 
     .   �              IStorage .?AUIStorage@@ 蝰
 !    B   �              tagVersionedStream .?AUtagVersionedStream@@ 蝰
 #    6   �              tagSAFEARRAY .?AUtagSAFEARRAY@@ 蝰
 %    *   �              tagCAC .?AUtagCAC@@ 蝰*   �              tagCAUB .?AUtagCAUB@@ *   �              tagCAI .?AUtagCAI@@ 蝰*   �              tagCAUI .?AUtagCAUI@@ *   �              tagCAL .?AUtagCAL@@ 蝰*   �              tagCAUL .?AUtagCAUL@@ *   �              tagCAH .?AUtagCAH@@ 蝰*   �              tagCAUH .?AUtagCAUH@@ .   �              tagCAFLT .?AUtagCAFLT@@ 蝰.   �              tagCADBL .?AUtagCADBL@@ 蝰.   �              tagCABOOL .?AUtagCABOOL@@ 2   �              tagCASCODE .?AUtagCASCODE@@ 蝰*   �              tagCACY .?AUtagCACY@@ .   �              tagCADATE .?AUtagCADATE@@ 6   �              tagCAFILETIME .?AUtagCAFILETIME@@ 2   �              tagCACLSID .?AUtagCACLSID@@ 蝰6   �              tagCACLIPDATA .?AUtagCACLIPDATA@@ .   �              tagCABSTR .?AUtagCABSTR@@ 6   �              tagCABSTRBLOB .?AUtagCABSTRBLOB@@ 2   �              tagCALPSTR .?AUtagCALPSTR@@ 蝰2   �              tagCALPWSTR .?AUtagCALPWSTR@@ >   �              tagCAPROPVARIANT .?AUtagCAPROPVARIANT@@ 蝰*   �              tagDEC .?AUtagDEC@@ 蝰
 =    
     
     
     
 &    �
 !     vt 篁�
 !    wReserved1 篁�
 !    wReserved2 篁�
 !    wReserved3 篁�
 p    cVal �
      bVal �
     iVal �
 !    uiVal 
     lVal �
 "    ulVal 
 t    intVal 篁�
 u    uintVal 蝰
    hVal �
    uhVal 
 @    fltVal 篁�
 A    dblVal 篁�
     boolVal 蝰
     __OBSOLETE__VARIANT_BOOL �
     scode 
    cyVal 
 A    date �
    filetime �
    puuid 
    pclipdata 
 q   bstrVal 蝰
    bstrblobVal 蝰
    blob �
 p   pszVal 篁�
 q   pwszVal 蝰
    punkVal 蝰
    pdispVal �
     pStream 蝰
 "   pStorage �
 $   pVersionedStream �
 &   parray 篁�
 '   cac 蝰
 (   caub �
 )   cai 蝰
 *   caui �
 +   cal 蝰
 ,   caul �
 -   cah 蝰
 .   cauh �
 /   caflt 
 0   cadbl 
 1   cabool 篁�
 2   cascode 蝰
 3   cacy �
 4   cadate 篁�
 5   cafiletime 篁�
 6   cauuid 篁�
 7   caclipdata 篁�
 8   cabstr 篁�
 9   cabstrblob 篁�
 :   calpstr 蝰
 ;   calpwstr �
 <   capropvar 
 p   pcVal 
     pbVal 
    piVal 
 !   puiVal 篁�
    plVal 
 "   pulVal 篁�
 t   pintVal 蝰
 u   puintVal �
 @   pfltVal 蝰
 A   pdblVal 蝰
    pboolVal �
 >   pdecVal 蝰
    pscode 篁�
 ?   pcyVal 篁�
 A   pdate 
 0   pbstrVal �
 @   ppunkVal �
 A   ppdispVal 
 B   pparray 蝰
    pvarVal 蝰
 =    decVal 篁�: N  C           tagPROPVARIANT .?AUtagPROPVARIANT@@ 蝰2   �              tagVARIANT .?AUtagVARIANT@@ 蝰
 E        F  F  "   "          G  2   �              IRecordInfo .?AUIRecordInfo@@ 
 I    �
 !     vt 篁�
 !    wReserved1 篁�
 !    wReserved2 篁�
 !    wReserved3 篁�
     llVal 
     lVal �
      bVal �
     iVal �
 @    fltVal 篁�
 A    dblVal 篁�
     boolVal 蝰
     __OBSOLETE__VARIANT_BOOL �
     scode 
    cyVal 
 A    date �
 q   bstrVal 蝰
    punkVal 蝰
    pdispVal �
 &   parray 篁�
     pbVal 
    piVal 
    plVal 
    pllVal 篁�
 @   pfltVal 蝰
 A   pdblVal 蝰
    pboolVal �
    __OBSOLETE__VARIANT_PBOOL 
    pscode 篁�
 ?   pcyVal 篁�
 A   pdate 
 0   pbstrVal �
 @   ppunkVal �
 A   ppdispVal 
 B   pparray 蝰
 F   pvarVal 蝰
    byref 
 p    cVal �
 !    uiVal 
 "    ulVal 
 #    ullVal 篁�
 t    intVal 篁�
 u    uintVal 蝰
 >   pdecVal 蝰
 p   pcVal 
 !   puiVal 篁�
 "   pulVal 篁�
 #   pullVal 蝰
 t   pintVal 蝰
 u   puintVal �
    pvRecord �
 J   pRecInfo �
 =    decVal 篁�2 4  K           tagVARIANT .?AUtagVARIANT@@ 蝰Z    D3D12_COMMAND_QUEUE_FLAG_NONE   D3D12_COMMAND_QUEUE_FLAG_DISABLE_GPU_TIMEOUT 馢   t   M  D3D12_COMMAND_QUEUE_FLAGS .?AW4D3D12_COMMAND_QUEUE_FLAGS@@ �
 N    
 N  ,  �    D3D12_PIPELINE_STATE_FLAG_NONE 篁�  D3D12_PIPELINE_STATE_FLAG_TOOL_DEBUG �  D3D12_PIPELINE_STATE_FLAG_DYNAMIC_DEPTH_BIAS �  D3D12_PIPELINE_STATE_FLAG_DYNAMIC_INDEX_BUFFER_STRIP_CUT 馧   t   Q  D3D12_PIPELINE_STATE_FLAGS .?AW4D3D12_PIPELINE_STATE_FLAGS@@ 篁�
 R    
 R  ,  �    D3D12_SHADER_MIN_PRECISION_SUPPORT_NONE 蝰  D3D12_SHADER_MIN_PRECISION_SUPPORT_10_BIT   D3D12_SHADER_MIN_PRECISION_SUPPORT_16_BIT ^   t   U  D3D12_SHADER_MIN_PRECISION_SUPPORT .?AW4D3D12_SHADER_MIN_PRECISION_SUPPORT@@ 篁�
 V    
 V  ,  �   D3D12_FORMAT_SUPPORT1_NONE 篁�  D3D12_FORMAT_SUPPORT1_BUFFER �  D3D12_FORMAT_SUPPORT1_IA_VERTEX_BUFFER 篁�  D3D12_FORMAT_SUPPORT1_IA_INDEX_BUFFER   D3D12_FORMAT_SUPPORT1_SO_BUFFER 蝰  D3D12_FORMAT_SUPPORT1_TEXTURE1D 蝰   D3D12_FORMAT_SUPPORT1_TEXTURE2D 蝰 @ D3D12_FORMAT_SUPPORT1_TEXTURE3D 蝰 � D3D12_FORMAT_SUPPORT1_TEXTURECUBE   D3D12_FORMAT_SUPPORT1_SHADER_LOAD   D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE 蝰  D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_COMPARISON 篁�  D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_MONO_TEXT   D3D12_FORMAT_SUPPORT1_MIP   @D3D12_FORMAT_SUPPORT1_RENDER_TARGET 蝰 � �D3D12_FORMAT_SUPPORT1_BLENDABLE  �   D3D12_FORMAT_SUPPORT1_DEPTH_STENCIL 蝰 �   D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RESOLVE  �   D3D12_FORMAT_SUPPORT1_DISPLAY  �   D3D12_FORMAT_SUPPORT1_CAST_WITHIN_BIT_LAYOUT � �    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RENDERTARGET 篁� �  @ D3D12_FORMAT_SUPPORT1_MULTISAMPLE_LOAD 篁� �  � D3D12_FORMAT_SUPPORT1_SHADER_GATHER 蝰 �   D3D12_FORMAT_SUPPORT1_BACK_BUFFER_CAST 篁� �   D3D12_FORMAT_SUPPORT1_TYPED_UNORDERED_ACCESS_VIEW  �   D3D12_FORMAT_SUPPORT1_SHADER_GATHER_COMPARISON 篁� �   D3D12_FORMAT_SUPPORT1_DECODER_OUTPUT � �   D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_OUTPUT � �    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_INPUT 蝰 �   @D3D12_FORMAT_SUPPORT1_VIDEO_ENCODER 蝰B   t   Y  D3D12_FORMAT_SUPPORT1 .?AW4D3D12_FORMAT_SUPPORT1@@ �
 Z    
 Z  ,  �   D3D12_FORMAT_SUPPORT2_NONE 篁�  D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_ADD �  D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS �  D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE 篁�  D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE   D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX 篁�   D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX � @ D3D12_FORMAT_SUPPORT2_UAV_TYPED_LOAD � � D3D12_FORMAT_SUPPORT2_UAV_TYPED_STORE   D3D12_FORMAT_SUPPORT2_OUTPUT_MERGER_LOGIC_OP �  D3D12_FORMAT_SUPPORT2_TILED 蝰  @D3D12_FORMAT_SUPPORT2_MULTIPLANE_OVERLAY � � �D3D12_FORMAT_SUPPORT2_SAMPLER_FEEDBACK 馚 
  t   ]  D3D12_FORMAT_SUPPORT2 .?AW4D3D12_FORMAT_SUPPORT2@@ �
 ^    
 ^  ,  r    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_NONE 篁�  D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_TILED_RESOURCE 馼   t   a  D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS .?AW4D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS@@ �
 b    
 b  ,  �   D3D12_SHADER_CACHE_SUPPORT_NONE 蝰  D3D12_SHADER_CACHE_SUPPORT_SINGLE_PSO   D3D12_SHADER_CACHE_SUPPORT_LIBRARY 篁�  D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_INPROC_CACHE   D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_DISK_CACHE 蝰  D3D12_SHADER_CACHE_SUPPORT_DRIVER_MANAGED_CACHE 蝰   D3D12_SHADER_CACHE_SUPPORT_SHADER_CONTROL_CLEAR 蝰 @ D3D12_SHADER_CACHE_SUPPORT_SHADER_SESSION_DELETE 馴   t   e  D3D12_SHADER_CACHE_SUPPORT_FLAGS .?AW4D3D12_SHADER_CACHE_SUPPORT_FLAGS@@ 篁�
 f    
 f  ,  �   D3D12_COMMAND_LIST_SUPPORT_FLAG_NONE �  D3D12_COMMAND_LIST_SUPPORT_FLAG_DIRECT 篁�  D3D12_COMMAND_LIST_SUPPORT_FLAG_BUNDLE 篁�  D3D12_COMMAND_LIST_SUPPORT_FLAG_COMPUTE 蝰  D3D12_COMMAND_LIST_SUPPORT_FLAG_COPY �  D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_DECODE �   D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_PROCESS  @ D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_ENCODE 馴   t   i  D3D12_COMMAND_LIST_SUPPORT_FLAGS .?AW4D3D12_COMMAND_LIST_SUPPORT_FLAGS@@ 篁�
 j    
 j  ,  �   D3D12_HEAP_FLAG_NONE �  D3D12_HEAP_FLAG_SHARED 篁�  D3D12_HEAP_FLAG_DENY_BUFFERS �  D3D12_HEAP_FLAG_ALLOW_DISPLAY    D3D12_HEAP_FLAG_SHARED_CROSS_ADAPTER � @ D3D12_HEAP_FLAG_DENY_RT_DS_TEXTURES 蝰 � D3D12_HEAP_FLAG_DENY_NON_RT_DS_TEXTURES 蝰  D3D12_HEAP_FLAG_HARDWARE_PROTECTED 篁�  D3D12_HEAP_FLAG_ALLOW_WRITE_WATCH   D3D12_HEAP_FLAG_ALLOW_SHADER_ATOMICS �  D3D12_HEAP_FLAG_CREATE_NOT_RESIDENT 蝰  D3D12_HEAP_FLAG_CREATE_NOT_ZEROED    D3D12_HEAP_FLAG_TOOLS_USE_MANUAL_WRITE_TRACKING 蝰   D3D12_HEAP_FLAG_ALLOW_ALL_BUFFERS_AND_TEXTURES 篁� � D3D12_HEAP_FLAG_ALLOW_ONLY_BUFFERS 篁� D D3D12_HEAP_FLAG_ALLOW_ONLY_NON_RT_DS_TEXTURES  � D3D12_HEAP_FLAG_ALLOW_ONLY_RT_DS_TEXTURES :   t   m  D3D12_HEAP_FLAGS .?AW4D3D12_HEAP_FLAGS@@ 篁�
 n    
 n  ,  �   D3D12_RESOURCE_FLAG_NONE �  D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET 蝰  D3D12_RESOURCE_FLAG_ALLOW_DEPTH_STENCIL 蝰  D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS 篁�  D3D12_RESOURCE_FLAG_DENY_SHADER_RESOURCE �  D3D12_RESOURCE_FLAG_ALLOW_CROSS_ADAPTER 蝰   D3D12_RESOURCE_FLAG_ALLOW_SIMULTANEOUS_ACCESS  @ D3D12_RESOURCE_FLAG_VIDEO_DECODE_REFERENCE_ONLY 蝰 � D3D12_RESOURCE_FLAG_VIDEO_ENCODE_REFERENCE_ONLY 蝰  D3D12_RESOURCE_FLAG_RAYTRACING_ACCELERATION_STRUCTURE B 
  t   q  D3D12_RESOURCE_FLAGS .?AW4D3D12_RESOURCE_FLAGS@@ 篁�
 r    
 r  ,  N    D3D12_TILE_MAPPING_FLAG_NONE �  D3D12_TILE_MAPPING_FLAG_NO_HAZARD J   t   u  D3D12_TILE_MAPPING_FLAGS .?AW4D3D12_TILE_MAPPING_FLAGS@@ 篁�
 v    
 v  ,  �    D3D12_TILE_COPY_FLAG_NONE   D3D12_TILE_COPY_FLAG_NO_HAZARD 篁�  D3D12_TILE_COPY_FLAG_LINEAR_BUFFER_TO_SWIZZLED_TILED_RESOURCE   D3D12_TILE_COPY_FLAG_SWIZZLED_TILED_RESOURCE_TO_LINEAR_BUFFER B   t   y  D3D12_TILE_COPY_FLAGS .?AW4D3D12_TILE_COPY_FLAGS@@ �
 z    
 z  ,     D3D12_RESOURCE_STATE_COMMON 蝰  D3D12_RESOURCE_STATE_VERTEX_AND_CONSTANT_BUFFER 蝰  D3D12_RESOURCE_STATE_INDEX_BUFFER   D3D12_RESOURCE_STATE_RENDER_TARGET 篁�  D3D12_RESOURCE_STATE_UNORDERED_ACCESS   D3D12_RESOURCE_STATE_DEPTH_WRITE �   D3D12_RESOURCE_STATE_DEPTH_READ 蝰 @ D3D12_RESOURCE_STATE_NON_PIXEL_SHADER_RESOURCE 篁� � D3D12_RESOURCE_STATE_PIXEL_SHADER_RESOURCE 篁�  D3D12_RESOURCE_STATE_STREAM_OUT 蝰  D3D12_RESOURCE_STATE_INDIRECT_ARGUMENT 篁�  D3D12_RESOURCE_STATE_COPY_DEST 篁�  D3D12_RESOURCE_STATE_COPY_SOURCE �  D3D12_RESOURCE_STATE_RESOLVE_DEST    D3D12_RESOURCE_STATE_RESOLVE_SOURCE 蝰 �  @ D3D12_RESOURCE_STATE_RAYTRACING_ACCELERATION_STRUCTURE 篁� �   D3D12_RESOURCE_STATE_SHADING_RATE_SOURCE � � �D3D12_RESOURCE_STATE_RESERVED_INTERNAL_8000   @D3D12_RESOURCE_STATE_RESERVED_INTERNAL_4000 蝰 �   D3D12_RESOURCE_STATE_RESERVED_INTERNAL_100000  �   @D3D12_RESOURCE_STATE_RESERVED_INTERNAL_40000000 蝰 �   �D3D12_RESOURCE_STATE_RESERVED_INTERNAL_80000000 蝰 �
D3D12_RESOURCE_STATE_GENERIC_READ  � D3D12_RESOURCE_STATE_ALL_SHADER_RESOURCE �   D3D12_RESOURCE_STATE_PRESENT �  D3D12_RESOURCE_STATE_PREDICATION � �   D3D12_RESOURCE_STATE_VIDEO_DECODE_READ 篁� �   D3D12_RESOURCE_STATE_VIDEO_DECODE_WRITE 蝰 �   D3D12_RESOURCE_STATE_VIDEO_PROCESS_READ 蝰 �   D3D12_RESOURCE_STATE_VIDEO_PROCESS_WRITE � �    D3D12_RESOURCE_STATE_VIDEO_ENCODE_READ 篁� �  � D3D12_RESOURCE_STATE_VIDEO_ENCODE_WRITE 蝰B    t   }  D3D12_RESOURCE_STATES .?AW4D3D12_RESOURCE_STATES@@ �
 ~    
 ~  ,  �    D3D12_RESOURCE_BARRIER_FLAG_NONE �  D3D12_RESOURCE_BARRIER_FLAG_BEGIN_ONLY 篁�  D3D12_RESOURCE_BARRIER_FLAG_END_ONLY 馬   t   �  D3D12_RESOURCE_BARRIER_FLAGS .?AW4D3D12_RESOURCE_BARRIER_FLAGS@@ 篁�
 �    
 �  ,  j    D3D12_VIEW_INSTANCING_FLAG_NONE 蝰  D3D12_VIEW_INSTANCING_FLAG_ENABLE_VIEW_INSTANCE_MASKING 蝰N   t   �  D3D12_VIEW_INSTANCING_FLAGS .?AW4D3D12_VIEW_INSTANCING_FLAGS@@ �
 �    
 �  ,  F    D3D12_BUFFER_SRV_FLAG_NONE 篁�  D3D12_BUFFER_SRV_FLAG_RAW F   t   �  D3D12_BUFFER_SRV_FLAGS .?AW4D3D12_BUFFER_SRV_FLAGS@@ 篁�
 �    
 �  ,  �    D3D12_SAMPLER_FLAG_NONE 蝰  D3D12_SAMPLER_FLAG_UINT_BORDER_COLOR �  D3D12_SAMPLER_FLAG_NON_NORMALIZED_COORDINATES >   t   �  D3D12_SAMPLER_FLAGS .?AW4D3D12_SAMPLER_FLAGS@@ �
 �    
 �  ,  F    D3D12_BUFFER_UAV_FLAG_NONE 篁�  D3D12_BUFFER_UAV_FLAG_RAW F   t   �  D3D12_BUFFER_UAV_FLAGS .?AW4D3D12_BUFFER_UAV_FLAGS@@ 篁�
 �    
 �  ,  n    D3D12_DSV_FLAG_NONE 蝰  D3D12_DSV_FLAG_READ_ONLY_DEPTH 篁�  D3D12_DSV_FLAG_READ_ONLY_STENCIL �6   t   �  D3D12_DSV_FLAGS .?AW4D3D12_DSV_FLAGS@@ �
 �    
 �  ,  B   D3D12_CLEAR_FLAG_DEPTH 篁�  D3D12_CLEAR_FLAG_STENCIL �:   t   �  D3D12_CLEAR_FLAGS .?AW4D3D12_CLEAR_FLAGS@@ �
 �    
 �  ,  �    D3D12_FENCE_FLAG_NONE   D3D12_FENCE_FLAG_SHARED 蝰  D3D12_FENCE_FLAG_SHARED_CROSS_ADAPTER   D3D12_FENCE_FLAG_NON_MONITORED 篁�:   t   �  D3D12_FENCE_FLAGS .?AW4D3D12_FENCE_FLAGS@@ �
 �    
 �  ,  Z    D3D12_DESCRIPTOR_HEAP_FLAG_NONE 蝰  D3D12_DESCRIPTOR_HEAP_FLAG_SHADER_VISIBLE N   t   �  D3D12_DESCRIPTOR_HEAP_FLAGS .?AW4D3D12_DESCRIPTOR_HEAP_FLAGS@@ �
 �    
 �  ,  *   D3D12_ROOT_SIGNATURE_FLAG_NONE 篁�  D3D12_ROOT_SIGNATURE_FLAG_ALLOW_INPUT_ASSEMBLER_INPUT_LAYOUT �  D3D12_ROOT_SIGNATURE_FLAG_DENY_VERTEX_SHADER_ROOT_ACCESS �  D3D12_ROOT_SIGNATURE_FLAG_DENY_HULL_SHADER_ROOT_ACCESS 篁�  D3D12_ROOT_SIGNATURE_FLAG_DENY_DOMAIN_SHADER_ROOT_ACCESS �  D3D12_ROOT_SIGNATURE_FLAG_DENY_GEOMETRY_SHADER_ROOT_ACCESS 篁�   D3D12_ROOT_SIGNATURE_FLAG_DENY_PIXEL_SHADER_ROOT_ACCESS 蝰 @ D3D12_ROOT_SIGNATURE_FLAG_ALLOW_STREAM_OUTPUT  � D3D12_ROOT_SIGNATURE_FLAG_LOCAL_ROOT_SIGNATURE 篁�  D3D12_ROOT_SIGNATURE_FLAG_DENY_AMPLIFICATION_SHADER_ROOT_ACCESS 蝰  D3D12_ROOT_SIGNATURE_FLAG_DENY_MESH_SHADER_ROOT_ACCESS 篁�  D3D12_ROOT_SIGNATURE_FLAG_CBV_SRV_UAV_HEAP_DIRECTLY_INDEXED 蝰  D3D12_ROOT_SIGNATURE_FLAG_SAMPLER_HEAP_DIRECTLY_INDEXED 蝰N 
  t   �  D3D12_ROOT_SIGNATURE_FLAGS .?AW4D3D12_ROOT_SIGNATURE_FLAGS@@ 篁�
 �    
 �  ,  ^   D3D12_DESCRIPTOR_RANGE_FLAG_NONE �  D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_VOLATILE �  D3D12_DESCRIPTOR_RANGE_FLAG_DATA_VOLATILE   D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE �  D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC 蝰 �   D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_STATIC_KEEPING_BUFFER_BOUNDS_CHECKS 蝰R   t   �  D3D12_DESCRIPTOR_RANGE_FLAGS .?AW4D3D12_DESCRIPTOR_RANGE_FLAGS@@ 篁�
 �    
 �  ,  �    D3D12_ROOT_DESCRIPTOR_FLAG_NONE 蝰  D3D12_ROOT_DESCRIPTOR_FLAG_DATA_VOLATILE �  D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE 蝰  D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC 篁馧   t   �  D3D12_ROOT_DESCRIPTOR_FLAGS .?AW4D3D12_ROOT_DESCRIPTOR_FLAGS@@ �
 �    
 �  ,  �    D3D12_MULTIPLE_FENCE_WAIT_FLAG_NONE 蝰  D3D12_MULTIPLE_FENCE_WAIT_FLAG_ANY 篁�   D3D12_MULTIPLE_FENCE_WAIT_FLAG_ALL 篁馰   t   �  D3D12_MULTIPLE_FENCE_WAIT_FLAGS .?AW4D3D12_MULTIPLE_FENCE_WAIT_FLAGS@@ �
 �    
 �  ,  N    D3D12_RESIDENCY_FLAG_NONE   D3D12_RESIDENCY_FLAG_DENY_OVERBUDGET 馚   t   �  D3D12_RESIDENCY_FLAGS .?AW4D3D12_RESIDENCY_FLAGS@@ �
 �    
 �  ,  &    D3D12_COMMAND_LIST_FLAG_NONE 馢   t   �  D3D12_COMMAND_LIST_FLAGS .?AW4D3D12_COMMAND_LIST_FLAGS@@ 篁�
 �    
 �  ,  &    D3D12_COMMAND_POOL_FLAG_NONE 馢   t   �  D3D12_COMMAND_POOL_FLAGS .?AW4D3D12_COMMAND_POOL_FLAGS@@ 篁�
 �    
 �  ,  *    D3D12_COMMAND_RECORDER_FLAG_NONE 馬   t   �  D3D12_COMMAND_RECORDER_FLAGS .?AW4D3D12_COMMAND_RECORDER_FLAGS@@ 篁�
 �    
 �  ,  ~    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAG_NONE 篁�  D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAG_SUPPORTED 蝰v   t   �  D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS .?AW4D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS@@ 篁�
 �    
 �  ,  6    D3D12_PROTECTED_RESOURCE_SESSION_FLAG_NONE 篁駀   t   �  D3D12_PROTECTED_RESOURCE_SESSION_FLAGS .?AW4D3D12_PROTECTED_RESOURCE_SESSION_FLAGS@@ 篁�
 �    
 �  ,  b   D3D12_META_COMMAND_PARAMETER_FLAG_INPUT 蝰  D3D12_META_COMMAND_PARAMETER_FLAG_OUTPUT 馸   t   �  D3D12_META_COMMAND_PARAMETER_FLAGS .?AW4D3D12_META_COMMAND_PARAMETER_FLAGS@@ 篁�
 �    
 �  ,  .   D3D12_GRAPHICS_STATE_NONE   D3D12_GRAPHICS_STATE_IA_VERTEX_BUFFERS 篁�  D3D12_GRAPHICS_STATE_IA_INDEX_BUFFER �  D3D12_GRAPHICS_STATE_IA_PRIMITIVE_TOPOLOGY 篁�  D3D12_GRAPHICS_STATE_DESCRIPTOR_HEAP �  D3D12_GRAPHICS_STATE_GRAPHICS_ROOT_SIGNATURE �   D3D12_GRAPHICS_STATE_COMPUTE_ROOT_SIGNATURE 蝰 @ D3D12_GRAPHICS_STATE_RS_VIEWPORTS  � D3D12_GRAPHICS_STATE_RS_SCISSOR_RECTS   D3D12_GRAPHICS_STATE_PREDICATION �  D3D12_GRAPHICS_STATE_OM_RENDER_TARGETS 篁�  D3D12_GRAPHICS_STATE_OM_STENCIL_REF 蝰  D3D12_GRAPHICS_STATE_OM_BLEND_FACTOR �  D3D12_GRAPHICS_STATE_PIPELINE_STATE 蝰   D3D12_GRAPHICS_STATE_SO_TARGETS 蝰  @D3D12_GRAPHICS_STATE_OM_DEPTH_BOUNDS � � �D3D12_GRAPHICS_STATE_SAMPLE_POSITIONS 蝰 �   D3D12_GRAPHICS_STATE_VIEW_INSTANCE_MASK 蝰B   t   �  D3D12_GRAPHICS_STATES .?AW4D3D12_GRAPHICS_STATES@@ �
 �    
 �  ,     D3D12_STATE_OBJECT_FLAG_NONE �  D3D12_STATE_OBJECT_FLAG_ALLOW_LOCAL_DEPENDENCIES_ON_EXTERNAL_DEFINITIONS �  D3D12_STATE_OBJECT_FLAG_ALLOW_EXTERNAL_DEPENDENCIES_ON_LOCAL_DEFINITIONS �  D3D12_STATE_OBJECT_FLAG_ALLOW_STATE_OBJECT_ADDITIONS 馢   t   �  D3D12_STATE_OBJECT_FLAGS .?AW4D3D12_STATE_OBJECT_FLAGS@@ 篁�
 �    
 �  ,  "    D3D12_EXPORT_FLAG_NONE 篁�>   t   �  D3D12_EXPORT_FLAGS .?AW4D3D12_EXPORT_FLAGS@@ 篁�
 �    
 �  ,  �    D3D12_RAYTRACING_PIPELINE_FLAG_NONE 蝰  D3D12_RAYTRACING_PIPELINE_FLAG_SKIP_TRIANGLES   D3D12_RAYTRACING_PIPELINE_FLAG_SKIP_PROCEDURAL_PRIMITIVES V   t   �  D3D12_RAYTRACING_PIPELINE_FLAGS .?AW4D3D12_RAYTRACING_PIPELINE_FLAGS@@ �
 �    
 �  ,  ^    D3D12_WORK_GRAPH_FLAG_NONE 篁�  D3D12_WORK_GRAPH_FLAG_INCLUDE_ALL_AVAILABLE_NODES F   t   �  D3D12_WORK_GRAPH_FLAGS .?AW4D3D12_WORK_GRAPH_FLAGS@@ 篁�
 �    
 �  ,  �    D3D12_RAYTRACING_GEOMETRY_FLAG_NONE 蝰  D3D12_RAYTRACING_GEOMETRY_FLAG_OPAQUE   D3D12_RAYTRACING_GEOMETRY_FLAG_NO_DUPLICATE_ANYHIT_INVOCATION V   t   �  D3D12_RAYTRACING_GEOMETRY_FLAGS .?AW4D3D12_RAYTRACING_GEOMETRY_FLAGS@@ �
 �    
 �  ,     D3D12_RAYTRACING_INSTANCE_FLAG_NONE 蝰  D3D12_RAYTRACING_INSTANCE_FLAG_TRIANGLE_CULL_DISABLE �  D3D12_RAYTRACING_INSTANCE_FLAG_TRIANGLE_FRONT_COUNTERCLOCKWISE 篁�  D3D12_RAYTRACING_INSTANCE_FLAG_FORCE_OPAQUE 蝰  D3D12_RAYTRACING_INSTANCE_FLAG_FORCE_NON_OPAQUE 蝰V   t   �  D3D12_RAYTRACING_INSTANCE_FLAGS .?AW4D3D12_RAYTRACING_INSTANCE_FLAGS@@ �
 �    
 �  ,     D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_NONE 蝰  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_ALLOW_UPDATE 蝰  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_ALLOW_COMPACTION 蝰  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PREFER_FAST_TRACE �  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PREFER_FAST_BUILD �  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_MINIMIZE_MEMORY 篁�   D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PERFORM_UPDATE ~   t   �  D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS .?AW4D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS@@ �
 �    
 �  ,  �   D3D12_RAY_FLAG_NONE 蝰  D3D12_RAY_FLAG_FORCE_OPAQUE 蝰  D3D12_RAY_FLAG_FORCE_NON_OPAQUE 蝰  D3D12_RAY_FLAG_ACCEPT_FIRST_HIT_AND_END_SEARCH 篁�  D3D12_RAY_FLAG_SKIP_CLOSEST_HIT_SHADER 篁�  D3D12_RAY_FLAG_CULL_BACK_FACING_TRIANGLES    D3D12_RAY_FLAG_CULL_FRONT_FACING_TRIANGLES 篁� @ D3D12_RAY_FLAG_CULL_OPAQUE 篁� � D3D12_RAY_FLAG_CULL_NON_OPAQUE 篁�  D3D12_RAY_FLAG_SKIP_TRIANGLES   D3D12_RAY_FLAG_SKIP_PROCEDURAL_PRIMITIVES 6   t   �  D3D12_RAY_FLAGS .?AW4D3D12_RAY_FLAGS@@ �
 �    
 �  ,  r    D3D12_DRED_FLAG_NONE �  D3D12_DRED_FLAG_FORCE_ENABLE �  D3D12_DRED_FLAG_DISABLE_AUTOBREADCRUMBS 蝰:   t   �  D3D12_DRED_FLAGS .?AW4D3D12_DRED_FLAGS@@ 篁�
 �    
 �  ,  *    D3D12_DRED_PAGE_FAULT_FLAGS_NONE 馧   t   �  D3D12_DRED_PAGE_FAULT_FLAGS .?AW4D3D12_DRED_PAGE_FAULT_FLAGS@@ �
 �    
 �  ,     D3D12_RENDER_PASS_FLAG_NONE 蝰  D3D12_RENDER_PASS_FLAG_ALLOW_UAV_WRITES 蝰  D3D12_RENDER_PASS_FLAG_SUSPENDING_PASS 篁�  D3D12_RENDER_PASS_FLAG_RESUMING_PASS �  D3D12_RENDER_PASS_FLAG_BIND_READ_ONLY_DEPTH 蝰  D3D12_RENDER_PASS_FLAG_BIND_READ_ONLY_STENCIL F   t   �  D3D12_RENDER_PASS_FLAGS .?AW4D3D12_RENDER_PASS_FLAGS@@ �
 �    
 �  ,  V    D3D12_SET_WORK_GRAPH_FLAG_NONE 篁�  D3D12_SET_WORK_GRAPH_FLAG_INITIALIZE 馧   t     D3D12_SET_WORK_GRAPH_FLAGS .?AW4D3D12_SET_WORK_GRAPH_FLAGS@@ 篁�
     
   ,  �    D3D12_SHADER_CACHE_FLAG_NONE �  D3D12_SHADER_CACHE_FLAG_DRIVER_VERSIONED �  D3D12_SHADER_CACHE_FLAG_USE_WORKING_DIR 蝰J   t     D3D12_SHADER_CACHE_FLAGS .?AW4D3D12_SHADER_CACHE_FLAGS@@ 篁�
     
   ,  "   D3D12_BARRIER_SYNC_NONE 蝰  D3D12_BARRIER_SYNC_ALL 篁�  D3D12_BARRIER_SYNC_DRAW 蝰  D3D12_BARRIER_SYNC_INDEX_INPUT 篁�  D3D12_BARRIER_SYNC_VERTEX_SHADING   D3D12_BARRIER_SYNC_PIXEL_SHADING �   D3D12_BARRIER_SYNC_DEPTH_STENCIL � @ D3D12_BARRIER_SYNC_RENDER_TARGET � � D3D12_BARRIER_SYNC_COMPUTE_SHADING 篁�  D3D12_BARRIER_SYNC_RAYTRACING   D3D12_BARRIER_SYNC_COPY 蝰  D3D12_BARRIER_SYNC_RESOLVE 篁�  D3D12_BARRIER_SYNC_EXECUTE_INDIRECT 蝰  D3D12_BARRIER_SYNC_PREDICATION 篁�  D3D12_BARRIER_SYNC_ALL_SHADING 篁�   D3D12_BARRIER_SYNC_NON_PIXEL_SHADING �  @D3D12_BARRIER_SYNC_EMIT_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO � � �D3D12_BARRIER_SYNC_CLEAR_UNORDERED_ACCESS_VIEW � �   D3D12_BARRIER_SYNC_VIDEO_DECODE 蝰 �    D3D12_BARRIER_SYNC_VIDEO_PROCESS � �  @ D3D12_BARRIER_SYNC_VIDEO_ENCODE 蝰 �  � D3D12_BARRIER_SYNC_BUILD_RAYTRACING_ACCELERATION_STRUCTURE 篁� �   D3D12_BARRIER_SYNC_COPY_RAYTRACING_ACCELERATION_STRUCTURE  �   �D3D12_BARRIER_SYNC_SPLIT �>   t   	  D3D12_BARRIER_SYNC .?AW4D3D12_BARRIER_SYNC@@ 篁�
 
    
 
  ,  �   D3D12_BARRIER_ACCESS_COMMON 蝰  D3D12_BARRIER_ACCESS_VERTEX_BUFFER 篁�  D3D12_BARRIER_ACCESS_CONSTANT_BUFFER �  D3D12_BARRIER_ACCESS_INDEX_BUFFER   D3D12_BARRIER_ACCESS_RENDER_TARGET 篁�  D3D12_BARRIER_ACCESS_UNORDERED_ACCESS    D3D12_BARRIER_ACCESS_DEPTH_STENCIL_WRITE � @ D3D12_BARRIER_ACCESS_DEPTH_STENCIL_READ 蝰 � D3D12_BARRIER_ACCESS_SHADER_RESOURCE �  D3D12_BARRIER_ACCESS_STREAM_OUTPUT 篁�  D3D12_BARRIER_ACCESS_INDIRECT_ARGUMENT 篁�  D3D12_BARRIER_ACCESS_PREDICATION �  D3D12_BARRIER_ACCESS_COPY_DEST 篁�  D3D12_BARRIER_ACCESS_COPY_SOURCE �  D3D12_BARRIER_ACCESS_RESOLVE_DEST    D3D12_BARRIER_ACCESS_RESOLVE_SOURCE 蝰  @D3D12_BARRIER_ACCESS_RAYTRACING_ACCELERATION_STRUCTURE_READ 蝰 � �D3D12_BARRIER_ACCESS_RAYTRACING_ACCELERATION_STRUCTURE_WRITE 篁� �   D3D12_BARRIER_ACCESS_SHADING_RATE_SOURCE � �   D3D12_BARRIER_ACCESS_VIDEO_DECODE_READ 篁� �   D3D12_BARRIER_ACCESS_VIDEO_DECODE_WRITE 蝰 �   D3D12_BARRIER_ACCESS_VIDEO_PROCESS_READ 蝰 �   D3D12_BARRIER_ACCESS_VIDEO_PROCESS_WRITE � �    D3D12_BARRIER_ACCESS_VIDEO_ENCODE_READ 篁� �  @ D3D12_BARRIER_ACCESS_VIDEO_ENCODE_WRITE 蝰 �   �D3D12_BARRIER_ACCESS_NO_ACCESS 篁馚   t   
  D3D12_BARRIER_ACCESS .?AW4D3D12_BARRIER_ACCESS@@ 篁�
     
   ,  V    D3D12_TEXTURE_BARRIER_FLAG_NONE 蝰  D3D12_TEXTURE_BARRIER_FLAG_DISCARD 篁馧   t     D3D12_TEXTURE_BARRIER_FLAGS .?AW4D3D12_TEXTURE_BARRIER_FLAGS@@ �
     
   ,  �   D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_D3D_CACHE_FOR_DRIVER 篁�  D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_D3D_CONVERSIONS   D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_DRIVER_MANAGED �  D3D12_SHADER_CACHE_KIND_FLAG_APPLICATION_MANAGED 馬   t     D3D12_SHADER_CACHE_KIND_FLAGS .?AW4D3D12_SHADER_CACHE_KIND_FLAGS@@ �
     
   ,  �   D3D12_SHADER_CACHE_CONTROL_FLAG_DISABLE 蝰  D3D12_SHADER_CACHE_CONTROL_FLAG_ENABLE 篁�  D3D12_SHADER_CACHE_CONTROL_FLAG_CLEAR Z   t     D3D12_SHADER_CACHE_CONTROL_FLAGS .?AW4D3D12_SHADER_CACHE_CONTROL_FLAGS@@ 篁�
     
   ,  n    D3D12_GPU_BASED_VALIDATION_FLAGS_NONE   D3D12_GPU_BASED_VALIDATION_FLAGS_DISABLE_STATE_TRACKING 蝰Z   t     D3D12_GPU_BASED_VALIDATION_FLAGS .?AW4D3D12_GPU_BASED_VALIDATION_FLAGS@@ 篁�
     
   ,  r    D3D12_RLDO_NONE 蝰  D3D12_RLDO_SUMMARY 篁�  D3D12_RLDO_DETAIL   D3D12_RLDO_IGNORE_INTERNAL 篁�:   t   !  D3D12_RLDO_FLAGS .?AW4D3D12_RLDO_FLAGS@@ 篁�
 "    
 "  ,     D3D12_DEBUG_FEATURE_NONE �  D3D12_DEBUG_FEATURE_ALLOW_BEHAVIOR_CHANGING_DEBUG_AIDS 篁�  D3D12_DEBUG_FEATURE_CONSERVATIVE_RESOURCE_STATE_TRACKING �  D3D12_DEBUG_FEATURE_DISABLE_VIRTUALIZED_BUNDLES_VALIDATION 篁�  D3D12_DEBUG_FEATURE_EMULATE_WINDOWS7 �>   t   %  D3D12_DEBUG_FEATURE .?AW4D3D12_DEBUG_FEATURE@@ �
 &    
 &  ,  �   D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_NONE 篁�  D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_TRACKING_ONLY_SHADERS   D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_UNGUARDED_VALIDATION_SHADERS �  D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_GUARDED_VALIDATION_SHADERS 篁�  D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS_VALID_MASK �   t   )  D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS .?AW4D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS@@ 篁�
 *    
 *  ,  V    D3D12_MESSAGE_CALLBACK_FLAG_NONE �  D3D12_MESSAGE_CALLBACK_IGNORE_FILTERS R   t   -  D3D12_MESSAGE_CALLBACK_FLAGS .?AW4D3D12_MESSAGE_CALLBACK_FLAGS@@ 篁�
 .    
 .  ,     D3D12_DEVICE_FACTORY_FLAG_NONE 篁�  D3D12_DEVICE_FACTORY_FLAG_ALLOW_RETURNING_EXISTING_DEVICE   D3D12_DEVICE_FACTORY_FLAG_ALLOW_RETURNING_INCOMPATIBLE_EXISTING_DEVICE 篁�  D3D12_DEVICE_FACTORY_FLAG_DISALLOW_STORING_NEW_DEVICE_AS_SINGLETON 篁馧   t   1  D3D12_DEVICE_FACTORY_FLAGS .?AW4D3D12_DEVICE_FACTORY_FLAGS@@ 篁�
 2    
 2  ,  �   D3D12_DEVICE_FLAG_NONE 篁�  D3D12_DEVICE_FLAG_DEBUG_LAYER_ENABLED   D3D12_DEVICE_FLAG_GPU_BASED_VALIDATION_ENABLED 篁�  D3D12_DEVICE_FLAG_SYNCHRONIZED_COMMAND_QUEUE_VALIDATION_DISABLED �  D3D12_DEVICE_FLAG_DRED_AUTO_BREADCRUMBS_ENABLED 蝰  D3D12_DEVICE_FLAG_DRED_PAGE_FAULT_REPORTING_ENABLED 蝰   D3D12_DEVICE_FLAG_DRED_WATSON_REPORTING_ENABLED 蝰 @ D3D12_DEVICE_FLAG_DRED_BREADCRUMB_CONTEXT_ENABLED  � D3D12_DEVICE_FLAG_DRED_USE_MARKERS_ONLY_BREADCRUMBS 蝰  D3D12_DEVICE_FLAG_SHADER_INSTRUMENTATION_ENABLED �  D3D12_DEVICE_FLAG_AUTO_DEBUG_NAME_ENABLED   D3D12_DEVICE_FLAG_FORCE_LEGACY_STATE_VALIDATION 蝰>   t   5  D3D12_DEVICE_FLAGS .?AW4D3D12_DEVICE_FLAGS@@ 篁�
 6    
 6  ,  
    @    t      9  
    A    t      ;      @   @    t      =      A   A    t      ?      A   t   A      A      A   t    A      C   A      ;   A      ?      A   A   A      G   @      9  *   �              _iobuf .?AU_iobuf@@ 蝰
 J        #   K  -    p   t      L  
 J    
     _Placeholder �*   O           _iobuf .?AU_iobuf@@ 蝰 #            N  3    p   t      R  
    u    K     T  �   �              __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEAU__crt_locale_pointers@@@@ 篁� �    W           __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEAU__crt_locale_pointers@@@@ 篁�
 V    
 p    �   �              __vcrt_assert_va_start_is_not_reference<wchar_t const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEB_W@@ 駣    W           __vcrt_assert_va_start_is_not_reference<wchar_t const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEB_W@@ �
 [        #   q  #   -    p   t      ^  "    #   q  #   #   -    p   t      `      4  '  '  3    p   t      b      4  '  3    p   t      d      3    p   t      f      4  3    p   t      h  �   �              __vcrt_assert_va_start_is_not_reference<wchar_t const *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEB_W@@ 篁駟    W           __vcrt_assert_va_start_is_not_reference<wchar_t const *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEB_W@@ 篁�
 j        4  '  3  p   t      m      #   -  #   -    p   t      o      3  3    p   t      q  �   �              __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEAU__crt_locale_pointers@@@@ 瘼    W           __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEAU__crt_locale_pointers@@@@ �
 s        3  '  3    p   t      v      #   K  :    p   t      x      N  B    p   t      z  �   �              __vcrt_assert_va_start_is_not_reference<char const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEBD@@ 駟    W           __vcrt_assert_va_start_is_not_reference<char const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEBD@@ �
 |        #   p  #   :    p   t            ?  '  B    p   t      �  "    #   p  #   #   :    p   t      �      ?  '  '  B    p   t      �      B    p   t      �      ?  B    p   t      �      ?  '  B  p   t      �      B  p   t      �      #   :  #   :    p   t      �      B  B    p   t      �      B  B  p   t      �      #   -    p   t      �      -  t   t   t   t  t    t      �  
 �    
    �   q     �  
 �       q  #   �   t      �  2   �              _stat64i32 .?AU_stat64i32@@ 蝰
 �        t   �   t      �  &   �              stat .?AUstat@@ 蝰
 �   � 
 u     st_dev 篁�
 !    st_ino 篁�
 !    st_mode 蝰
     st_nlink �
    
 st_uid 篁�
     st_gid 篁�
 u    st_rdev 蝰
     st_size 蝰
     st_atime �
      st_mtime �
    ( st_ctime �&   �          0 stat .?AUstat@@ 蝰2   �          0 _stat64i32 .?AU_stat64i32@@ 蝰    :  �   t      �  .   �              _Mbstatet .?AU_Mbstatet@@ 
 �   蝰
 �    : 
 "     _Wchar 篁�
 !    _Byte 
 !    _State 篁�.   �           _Mbstatet .?AU_Mbstatet@@ 
 #        -  q   #    -     �  N   �              std::numeric_limits<bool> .?AV?$numeric_limits@_N@std@@ 蝰 	0   �                N   �              std::numeric_limits<char> .?AV?$numeric_limits@D@std@@ 篁� 	p   �                R   �              std::numeric_limits<signed char> .?AV?$numeric_limits@C@std@@  	   �                V   �              std::numeric_limits<unsigned char> .?AV?$numeric_limits@E@std@@ 蝰 	    �                R   �              std::numeric_limits<char16_t> .?AV?$numeric_limits@_S@std@@ 蝰 	z   �                R   �              std::numeric_limits<char32_t> .?AV?$numeric_limits@_U@std@@ 蝰 	{   �                R   �              std::numeric_limits<wchar_t> .?AV?$numeric_limits@_W@std@@ 篁� 	q   �                N   �              std::numeric_limits<short> .?AV?$numeric_limits@F@std@@ 蝰 	   �                J   �              std::numeric_limits<int> .?AV?$numeric_limits@H@std@@  	t   �                N   �              std::numeric_limits<long> .?AV?$numeric_limits@J@std@@ 篁� 	   �                R   �              std::numeric_limits<__int64> .?AV?$numeric_limits@_J@std@@ 篁� 	   �                V   �              std::numeric_limits<unsigned short> .?AV?$numeric_limits@G@std@@ � 	!   �                V   �              std::numeric_limits<unsigned int> .?AV?$numeric_limits@I@std@@ 篁� 	u   �                V   �              std::numeric_limits<unsigned long> .?AV?$numeric_limits@K@std@@ 蝰 	"   �                Z   �              std::numeric_limits<unsigned __int64> .?AV?$numeric_limits@_K@std@@ 蝰 	#   �                N   �              std::numeric_limits<float> .?AV?$numeric_limits@M@std@@ 蝰 	@   �                N   �              std::numeric_limits<double> .?AV?$numeric_limits@N@std@@ � 	A   �                R   �              std::numeric_limits<long double> .?AV?$numeric_limits@O@std@@  	A   �                
      蝰
 �   :   �              std::hash<float> .?AU?$hash@M@std@@ 蝰
 �   蝰
 �   
 @    蝰
    �   	#   �  �     �      B   @   argument_type 蝰  #   result_type  �  operator() �:  �           std::hash<float> .?AU?$hash@M@std@@ 蝰
 �  ,  
    �   #      �  
 �    :   �              std::hash<double> .?AU?$hash@N@std@@ �
 �   蝰
 �   
 A    蝰
    �   	#   �  �     �      B   A   argument_type 蝰  #   result_type  �  operator() �:  �           std::hash<double> .?AU?$hash@N@std@@ �
 �  ,  
    �   #      �  
 �    >   �              std::hash<long double> .?AU?$hash@O@std@@ 
 �   蝰
 �    	#   �  �     �      B   A   argument_type 蝰  #   result_type  �  operator() �>  �           std::hash<long double> .?AU?$hash@O@std@@ F   �              std::hash<std::nullptr_t> .?AU?$hash@$$T@std@@ 篁�
 �   蝰
 �   
       	#   �  �     �      B     argument_type 蝰  #   result_type  �  operator() 馞  �           std::hash<std::nullptr_t> .?AU?$hash@$$T@std@@ 篁�
 (  ,  
    �   #      �  
                :   �              std::exception .?AVexception@std@@ 篁�
     
  U�
     
    蝰
   ,  
       	                   B  t    	         
      
    B   	                	                "    	          
       
   ,   	                	                 
     	:                F   �              __std_exception_data .?AU__std_exception_data@@ 蝰 	         T      � 	       exception 蝰   operator= 蝰       ~exception �      what 篁�
    _Data   __local_vftable_ctor_closure 篁�      __vecDelDtor 篁�:  &         std::exception .?AVexception@std@@ 篁�
 *   
     
     
     
     & 
 :    _What 
 0    _DoFree 蝰F              __std_exception_data .?AU__std_exception_data@@ 蝰 	                
 :    
    蝰
 "        #           $  
      	          
       	                
     
 #    
             +  
 B     9  #     �      #          /  B   �              std::bad_exception .?AVbad_exception@std@@ 篁�
 1   
 1  �  
    3   	   1  2    4      
 1   蝰
 6  ,  
    7   	   1  2    8       	   1  2              5    9     :   	   1  2            
 1  ,   	=  1  2     4       	=  1  2     8         >    ?   	  1  2     T      �       蝰 ;  bad_exception 蝰<  ~bad_exception � @  operator= 蝰<  __local_vftable_ctor_closure 篁�A      __vecDelDtor 篁馚 	 &B         std::bad_exception .?AVbad_exception@std@@ 篁� 9  #     �
 1    :   �              std::bad_alloc .?AVbad_alloc@std@@ 篁�
 F   
 F  �  
    H   	   F  G    I      
 F   蝰
 K  ,  
    L   	   F  G    M       	   F  G           	   F  G           "   J    N     O     P   	   F  G            
 F  ,   	S  F  G     I       	S  F  G     M         T    U   	  F  G     T      �       蝰 Q  bad_alloc 蝰R  ~bad_alloc � V  operator= 蝰R  __local_vftable_ctor_closure 篁�W      __vecDelDtor 篁�: 
 &X         std::bad_alloc .?AVbad_alloc@std@@ 篁� 9  #     � 	   F  G           
 F    N   �              std::bad_array_new_length .?AVbad_array_new_length@std@@ �
 ]   
 ]  �  
    _   	   ]  ^    `      
 ]   蝰
 b  ,  
    c   	   ]  ^    d       	   ]  ^              a    e     f   	   ]  ^            
 ]  ,   	i  ]  ^     `       	i  ]  ^     d         j    k   	  ]  ^     T      �   F    蝰 g  bad_array_new_length 篁�h  ~bad_array_new_length 蝰 l  operator= 蝰h  __local_vftable_ctor_closure 篁�m      __vecDelDtor 篁馧 	 &n         std::bad_array_new_length .?AVbad_array_new_length@std@@ � 9  #     �
 ]    
 *     r        B   �              std::exception_ptr .?AVexception_ptr@std@@ 篁�
 t   
 t   蝰
 v  ,  
    w   	   t  u    x       	   t  u    �       	   t  u               y     z     {   	   t  u            
 t  ,   	~  t  u     �       	~  t  u     x               �  
 v    	0   t  �             	t  t                     "   	t  t        �       	  t  u     T      �  |  exception_ptr 蝰 }  ~exception_ptr � �  operator= 蝰 �  operator bool 蝰 �  _Current_exception � �  _Copy_exception 
     _Data1 篁�
    _Data2 篁��  __vecDelDtor 篁馚  f�           std::exception_ptr .?AVexception_ptr@std@@ 篁�
 t     	   t  u     �       	   t  u     x             �  
 v    
    "   0      �        "  "         �      "  "   0      �      w  w   0      �      w     0      �        w   0      �         �  6   �              _s__ThrowInfo .?AU_s__ThrowInfo@@ 
 �   蝰
 �    
     
         t      �  
 �    J   �              _s__CatchableTypeArray .?AU_s__CatchableTypeArray@@ 蝰
 �   蝰
 �    n 
 u     attributes 篁�
 �   pmfnUnwind 篁�
 �   pForwardCompat 篁�
 �   pCatchableTypeArray 蝰6   �           _s__ThrowInfo .?AU_s__ThrowInfo@@  	   F  G     I      
   �   	   ]  ^     `       	   F  G     M      
 K     	   ]  ^     d      
 b    F   �              std::nested_exception .?AVnested_exception@std@@ �
 �    
  P�
 �    
 �   蝰
 �  ,  
    �   	   �  �    �       	   �  �               �     �  
 �  ,   	�  �  �     �       	   �  �            
 �    	   �  �             	t  �  �            	  �  �     T      � 	  �   �  nested_exception 篁� �  operator= 蝰 �      ~nested_exception 蝰 �  rethrow_nested � �  nested_ptr �
 t   _Exc ��  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁馞 
 &�      �   std::nested_exception .?AVnested_exception@std@@ � t       
 �    
 �    
 �    
 �    
    t         �  J   �              std::bad_variant_access .?AVbad_variant_access@std@@ �
 �   蝰
 �   
 �  �  
    �  
 �    	   �  �    �      
 �  ,  
    �   	   �  �    �       	   �  �              �    �     �   	:  �  �             	   �  �            
 �  ,   	�  �  �     �       	�  �  �     �         �    �   	  �  �     T      �       蝰 �  bad_variant_access � �  what 篁��  ~bad_variant_access  �  operator= 蝰�  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁馢 
 &�         std::bad_variant_access .?AVbad_variant_access@std@@ � 9  #     �
 �     	   �  �     �       	   �  �     �      
 �    J   �              std::_Unused_parameter .?AU_Unused_parameter@std@@ 篁�
 �   J    W           std::_Unused_parameter .?AU_Unused_parameter@std@@ 篁� 	   �  �            J   �              std::_Distance_unknown .?AU_Distance_unknown@std@@ 篁�
 �   蝰
 �    	�  �  �             �  operator- 蝰J  �           std::_Distance_unknown .?AU_Distance_unknown@std@@ 篁�
 �    
    #         �  
 #   ,  
   ,  
 '   R   �              std::_Asan_aligned_pointers .?AU_Asan_aligned_pointers@std@@ �
 �   蝰
 �    	"  �  �     �      > 
 "    _First 篁�
 "   _End � �  _Clamp_to_end 蝰R   �           std::_Asan_aligned_pointers .?AU_Asan_aligned_pointers@std@@ �
 �    
 +    
 �    
 "    F   �              std::_Container_base0 .?AU_Container_base0@std@@ �
 �    	   �  �            
 �  ,  
        	   �  �           F   �              std::_Fake_allocator .?AU_Fake_allocator@std@@ 篁�
    蝰
   ,  
       	   �  �                    	   �  �           j  �  _Orphan_all    _Swap_proxy_and_iterators 蝰   _Alloc_proxy 篁� 	  _Reload_proxy 蝰F   
           std::_Container_base0 .?AU_Container_base0@std@@ 馞    W           std::_Fake_allocator .?AU_Fake_allocator@std@@ 篁馞   �              std::_Iterator_base0 .?AU_Iterator_base0@std@@ 篁�
 
    	   
       �      
 �   蝰
     
 
   蝰
     	  
              
 0    蝰F    _Adopt �   _Getcont 篁�   _Unwrap_when_unverified F              std::_Iterator_base0 .?AU_Iterator_base0@std@@ 篁馞   �              std::_Container_proxy .?AU_Container_proxy@std@@ �
    J   �              std::_Container_base12 .?AU_Container_base12@std@@ 篁�
     
       	                	                           
    蝰
      F   �              std::_Iterator_base12 .?AU_Iterator_base12@std@@ �
 "    J    _Container_proxy 篁�
 !    _Mycont 蝰
 #   _Myfirstiter 馞  $           std::_Container_proxy .?AU_Container_proxy@std@@ � 	                
     
 !    
 #    
    
    ,  
    +   	     *    ,       	     *               -     .  
   ,   	0    *     ,       	     *            
    0   	     *     3       /  _Container_base12 蝰 1  operator= 蝰 2  _Orphan_all  4  _Swap_proxy_and_iterators 蝰
 '    _Myproxy � 2  _Orphan_all_unlocked_v3  4  _Swap_proxy_and_iterators_unlocked � 2  _Orphan_all_locked_v3 蝰 4  _Swap_proxy_and_iterators_locked 篁馢 
 &5           std::_Container_base12 .?AU_Container_base12@std@@ 篁�6   �              std::_Lockit .?AV_Lockit@std@@ 篁�
 7   
 7   蝰
 9  ,  
    :   	   7  8    ;      
    t    	   7  8    =       	   7  8               <     >     ?   	   7  8            
 7        B  t    	   7         C      
    B   	   7         E       	   7         =       	   D  	   F     G   	   F     G  
 7  ,   	J  7  8     ;       	  7  8     T      �  @  _Lockit  A  ~_Lockit 篁� H  _Lockit_ctor 篁� I  _Lockit_dtor 篁� K  operator= 蝰
 t     _Locktype L  __vecDelDtor 篁�6  &M           std::_Lockit .?AV_Lockit@std@@ 篁� 	   7  8     =      
 "   
 "   蝰
 Q  ,  
    R   	   "  P    S       	   "  P               T     U  
 "  ,   	W  "  P     S      
    !   	   "  P     Y      
 Q    	!  "  [            �  V  _Iterator_base12 篁� X  operator= 蝰 Z  _Adopt � \  _Getcont 篁�   _Unwrap_when_unverified 
 '    _Myproxy �
 #   _Mynextiter 蝰F  &]           std::_Iterator_base12 .?AU_Iterator_base12@std@@ � 	   "  P     S      
 '    
 Q    
     
 #  ,  
   �      c  d   #     e  
     
 )    N   �              std::_Fake_proxy_ptr_impl .?AU_Fake_proxy_ptr_impl@std@@ �
 i   
   ,        k   	   i  j    l      N   �              std::_Leave_proxy_unbound .?AU_Leave_proxy_unbound@std@@ �      n   	   i  j    o      
 i   蝰
 q  ,  
    r   	   i  j    s          m     p     t  
 i  ,   	v  i  j     s      
 �          x   	   i  j     y       	   i  j            Z  u  _Fake_proxy_ptr_impl 篁� w  operator= 蝰 z  _Bind 蝰 {  _Release 篁馧  &|           std::_Fake_proxy_ptr_impl .?AU_Fake_proxy_ptr_impl@std@@ � 	   i  j     o      N    W           std::_Leave_proxy_unbound .?AU_Leave_proxy_unbound@std@@ � 	   i  j     l      ^   �              std::_Basic_container_proxy_ptr12 .?AU_Basic_container_proxy_ptr12@std@@ �
 �    	   �  �            
 �  �  
    �   	   �  �    �      
 �   蝰
 �  ,  
    �   	   �  �    �       	   �  �               �     �     �  N 
 '    _Ptr � �  _Release 篁� �  _Basic_container_proxy_ptr12 篁馸  �           std::_Basic_container_proxy_ptr12 .?AU_Basic_container_proxy_ptr12@std@@ �
 �    N   �              std::_Rand_urng_from_func .?AU_Rand_urng_from_func@std@@ �
 �    	u   �                 	u   �  �            B   u   result_type  �  min  �  max  �  operator() 馧  �           std::_Rand_urng_from_func .?AU_Rand_urng_from_func@std@@ � t         
    :         �   9  #     駷   �              std::basic_string<char,std::char_traits<char>,std::allocator<char> > .?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ 篁�
 �    B   �              std::allocator<char> .?AV?$allocator@D@std@@ 駐   �              std::allocator_traits<std::allocator<char> > .?AU?$allocator_traits@V?$allocator@D@std@@@std@@ 篁駌   �              std::_String_val<std::_Simple_types<char> > .?AV?$_String_val@U?$_Simple_types@D@std@@@std@@ 馞   �              std::char_traits<char> .?AU?$char_traits@D@std@@ �
 p   ,  
 9  ,  �   �              std::_String_iterator<std::_String_val<std::_Simple_types<char> > > .?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@std@@ 篁癫   �              std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > .?AV?$_String_const_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@std@@ 篁裰   �              std::reverse_iterator<std::_String_iterator<std::_String_val<std::_Simple_types<char> > > > .?AV?$reverse_iterator@V?$_String_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@std@@@std@@ 疋   �              std::reverse_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > > .?AV?$reverse_iterator@V?$_String_const_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@std@@@std@@ 馬   �              std::initializer_list<char> .?AV?$initializer_list@D@std@@ 篁�
 �   蝰
 �  ,      �  �  
 �    	   �  �    �      b   �              std::_String_constructor_concat_tag .?AU_String_constructor_concat_tag@std@@ �
 �  ,      �  �  �   	   �  �    �      
 �   蝰
 �  ,      �  �  B  '  B  '   	   �  �    �      
 �  �      �  �   	   �  �    �      
    �   	   �  �    �          '  9  �   	   �  �    �          '  9   	   �  �    �          B  �   	   �  �    �       	   �  �              B  '  �   	   �  �    �          B  '   	   �  �    �          �  '  '  �   	   �  �    �          �  '  �   	   �  �    �          �  �   	   �  �    �      
    �   	   �  �    �      
    �   	   �  �    �       	   �  �           �    �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �  "    _At_least   _Exactly 窬  t   �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy .?AW4_Allocation_policy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ �
 �  ,      �  ?  '   	   �         �      :    _From_char 篁�  _From_ptr   _From_string 衤      �  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy .?AW4_Construct_strategy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ 篁�
    9   	�  �  �     �       	�  �  �            	�  �  �     �      
    �   	�  �  �     �       	�  �  �     �      *    �     �     �     �     �   	�  �  �     �       	�  �  �     �          �  '  #    	�  �  �     �      :    �     �     �     �     �     �     �   	   �  �     �      
    �   	   �  �     �      
    p    	�  �  �     �      "    �     �     �     �  2    �     �     �     �     �     �  
 �   蝰    �  '  9   	�  �  �    �          �  9   	�  �  �    �          '  '  9   	�  �  �     �          '  B   	�  �  �     �          '  B  '   	�  �  �     �          '  �  '  #    	�  �  �     �          '  �   	�  �  �     �      
 �   蝰    �  �   	�  �  �    �      B    �     �     �     �     �     �     �     �      �  �  '  9   	�  �  �     �          �  �  B   	�  �  �                �  �  B  '   	�  �  �               �  �  �   	�  �  �               '  #   '  9   	�  �  �               '  '  B   	�  �  �               '  #   B  '   	�  �  �     
          '  #   �  '  #    	�  �  �               '  '  �   	�  �  �               �  �  �   	�  �  �           R    �                         	          
             	   �  �                �  �   	�  �  �          
    �   	�  �  �              '  '   	�  �  �           
    '   	�  �  �           "                         '  #    	�  �  �           
 �    	�  �              	�  �  �                     !   	:  �               	p  �  �                #     $   	�  �              	�  �  �               &     '   	�  �              	�  �  �               )     *   	   �  �     �       	�  �               	�  �  �                -     .   	#   �               	   �  �     �       	   �  �            	0   �                  ?  #   '   	#   �       4          ?  '  #   '   	#   �       6      
 �  ,      8  8   	   �         9          9  '   	#   �       ;       	#   �       �          B  '  '   	#   �       >          �  '   	#   �       @      "    <     =     ?     A      �  #    	#   �       C      "    <     =     ?     D      B  #    	#   �       F      "    <     G     ?     A   	�  �             	0   �              	0   �       �          J     K      '  '  B  '   	t   �       M       	t   �              	t   �                 '  '  �  '  '   	t   �       Q          #   #   �   	t   �       S       	t   �       �      2    N     O     P     R     T     U   	�  �              	#   �                 '  '  '   	#   �         Y          X  	   Z   	�  �               	�  �  �                \     ]  �   �              std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> .?AV?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@ � 	  �  �     T      �  �  _Alty 蝰  �  _Alty_traits 篁�  �  _Scary_val �  �  traits_type   �  allocator_type �  p   value_type �  #   size_type 蝰     difference_type   p  pointer   :  const_pointer 蝰  �  reference 蝰  �  const_reference   �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�  �  const_reverse_iterator � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � '  _Least_allocation_size �   _Can_memcpy_val  '  _Memcpy_val_offset � '  _Memcpy_val_size 篁� �  basic_string<char,std::char_traits<char>,std::allocator<char> >   �  _Allocation_policy �	 �  _Deallocate_for_capacity 篁�  �  _Construct_strategy  �  operator= 蝰 �  assign � �  _Memcpy_val_from 篁� �  _Take_contents � �  operator+= � �  append � �  insert �
   replace    ~basic_string<char,std::char_traits<char>,std::allocator<char> > 篁� '  npos 篁�   erase 蝰   _Erase_noexcept    clear 蝰 "  begin 蝰 "  end  %  _Unchecked_begin 篁� %  _Unchecked_end � (  rbegin � (  rend 篁�    cbegin �    cend 篁� &  crbegin  &  crend 蝰   shrink_to_fit 蝰 +  at � +  operator[] � ,  push_back 蝰   pop_back 篁� /  front 蝰 /  back 篁� #  c_str 蝰 #  data 篁� 0  length � 0  size 篁� 0  max_size 篁� 1  resize � 0  capacity 篁� 2  reserve  3  empty 蝰 5  copy 篁� 7  _Copy_s  :  _Swap_bx_large_with_small 蝰 �  _Swap_data � �  swap 篁� B  find 篁� B  rfind 蝰 B  find_first_of 蝰 E  find_last_of 篁� H  find_first_not_of 蝰 B  find_last_not_of 篁� I  substr � L  _Equal � V  compare  W  get_allocator 蝰 [  _Calculate_growth 蝰   _Become_small 蝰 2  _Eos 篁�   _Tidy_init �   _Tidy_deallocate 篁�   _Orphan_all  �  _Swap_proxy_and_iterators 蝰 ^  _Getal �
 _    _Mypair 蝰`  __vecDelDtor 篁駷 � 6a            std::basic_string<char,std::char_traits<char>,std::allocator<char> > .?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ 篁� 	   �  �     �      �   �              std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > .?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ 篁�
 d    F   �              std::allocator<wchar_t> .?AV?$allocator@_W@std@@ 駔   �              std::allocator_traits<std::allocator<wchar_t> > .?AU?$allocator_traits@V?$allocator@_W@std@@@std@@ 篁駐   �              std::_String_val<std::_Simple_types<wchar_t> > .?AV?$_String_val@U?$_Simple_types@_W@std@@@std@@ 馢   �              std::char_traits<wchar_t> .?AU?$char_traits@_W@std@@ �
 q   ,  
 ,  ,  �   �              std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > .?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@_W@std@@@std@@@std@@ 篁穸   �              std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > .?AV?$_String_const_iterator@V?$_String_val@U?$_Simple_types@_W@std@@@std@@@std@@ 篁褛   �              std::reverse_iterator<std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > .?AV?$reverse_iterator@V?$_String_iterator@V?$_String_val@U?$_Simple_types@_W@std@@@std@@@std@@@std@@ 矜   �              std::reverse_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > .?AV?$reverse_iterator@V?$_String_const_iterator@V?$_String_val@U?$_Simple_types@_W@std@@@std@@@std@@@std@@ 馰   �              std::initializer_list<wchar_t> .?AV?$initializer_list@_W@std@@ 篁�
 f   蝰
 q  ,      p  r  
 d    	   d  t    s      
 d  ,      �  v  v   	   d  t    w      
 d   蝰
 y  ,      �  z  3  '  3  '   	   d  t    {      
 d  �      }  r   	   d  t    ~      
    }   	   d  t    �          '  ,  r   	   d  t    �          '  ,   	   d  t    �          3  r   	   d  t    �      
    3   	   d  t    �          3  '  r   	   d  t    �          3  '   	   d  t    �          z  '  '  r   	   d  t    �          z  '  r   	   d  t    �          z  r   	   d  t    �      
    z   	   d  t    �      
    r   	   d  t    �       	   d  t           �    u     x     |          �     �     �     �     �     �     �     �     �     �     �     �     �  �  t   �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy .?AW4_Allocation_policy@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ �
 f  ,      �  4  '   	   d         �      �      �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy .?AW4_Construct_strategy@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ 篁�
    ,   	v  d  t     �       	v  d  t     �       	v  d  t     �      
    p   	v  d  t     �       	v  d  t     �      *    �     �     �     �     �   	v  d  t     �       	v  d  t     �          z  '  #    	v  d  t     �      :    �     �     �     �     �     �     �   	   d  t     �      
    v   	   d  t     �      
    q    	v  d  t     �      "    �     �     �     �  2    �     �     �     �     �     �  
 m   蝰    �  '  ,   	l  d  t    �          �  ,   	l  d  t    �          '  '  ,   	v  d  t     �          '  3   	v  d  t     �          '  3  '   	v  d  t     �          '  z  '  #    	v  d  t     �          '  z   	v  d  t     �      
 p   蝰    �  �   	l  d  t    �      B    �     �     �     �     �     �     �     �      �  �  '  ,   	v  d  t     �          �  �  3   	v  d  t     �          �  �  3  '   	v  d  t     �          �  �  z   	v  d  t     �          '  #   '  ,   	v  d  t     �          '  '  3   	v  d  t     �          '  #   3  '   	v  d  t     �          '  #   z  '  #    	v  d  t     �          '  '  z   	v  d  t     �          �  �  �   	v  d  t     �      R    �     �     �     �     �     �     �     �     �     �   	   d  t                �  �   	l  d  t    �      
    �   	l  d  t    �       	v  d  t            	v  d  t           "    �     �     �     �   	v  d  t           
 y    	m  d  �            	l  d  t               �     �   	-  d  �             	q  d  t                �     �   	o  d  �            	n  d  t               �     �   	k  d  �            	j  d  t               �     �   	   d  t     �       	k  d  �             	j  d  t                �     �   	#   d  �             	   d  t     �       	   d  t            	0   d  �                4  #   '   	#   d  �     �          4  '  #   '   	#   d  �     �      
 h  ,      �  �   	   d         �          ,  '   	#   d  �             	#   d  �     �          3  '  '   	#   d  �               z  '   	#   d  �           "                         z  #    	#   d  �           "                   	      3  #    	#   d  �           "                      	d  d  �           	0   d  �     �       	0   d  �     �                     '  '  3  '   	t   d  �            	t   d  �     �       	t   d  �     �          '  '  z  '  '   	t   d  �               #   #   z   	t   d  �            	t   d  �     �      2                                	f  d  �            	#   d  �            	#   d         Y            	      	r  d  �             	�  d  t                      !  �   �              std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> .?AV?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@ � 	  d  t     T      �  f  _Alty 蝰  g  _Alty_traits 篁�  h  _Scary_val �  i  traits_type   f  allocator_type �  q   value_type �  #   size_type 蝰     difference_type   q  pointer   -  const_pointer 蝰  j  reference 蝰  k  const_reference   l  iterator 篁�  m  const_iterator �  n  reverse_iterator 篁�  o  const_reverse_iterator � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � '  _Least_allocation_size �   _Can_memcpy_val  '  _Memcpy_val_offset � '  _Memcpy_val_size 篁� �  basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > 篁�  �  _Allocation_policy �	 �  _Deallocate_for_capacity 篁�  �  _Construct_strategy  �  operator= 蝰 �  assign � �  _Memcpy_val_from 篁� �  _Take_contents � �  operator+= � �  append � �  insert �
 �  replace  �  ~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > 蝰 '  npos 篁� �  erase 蝰 �  _Erase_noexcept  �  clear 蝰 �  begin 蝰 �  end  �  _Unchecked_begin 篁� �  _Unchecked_end � �  rbegin � �  rend 篁� �  cbegin � �  cend 篁� �  crbegin  �  crend 蝰 �  shrink_to_fit 蝰 �  at � �  operator[] � �  push_back 蝰 �  pop_back 篁� �  front 蝰 �  back 篁� �  c_str 蝰 �  data 篁� �  length � �  size 篁� �  max_size 篁� �  resize � �  capacity 篁� �  reserve  �  empty 蝰 �  copy 篁� �  _Copy_s  �  _Swap_bx_large_with_small 蝰 �  _Swap_data � �  swap 篁�   find 篁�   rfind 蝰   find_first_of 蝰 
  find_last_of 篁� 
  find_first_not_of 蝰   find_last_not_of 篁�   substr �   _Equal �   compare    get_allocator 蝰   _Calculate_growth 蝰 �  _Become_small 蝰 �  _Eos 篁� �  _Tidy_init � �  _Tidy_deallocate 篁� �  _Orphan_all  �  _Swap_proxy_and_iterators 蝰 "  _Getal �
 #    _Mypair 蝰$  __vecDelDtor 篁癃 � 6%            std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > .?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ 篁� 	   d  t     �      �   �              std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > .?AV?$basic_string@_SU?$char_traits@_S@std@@V?$allocator@_S@2@@std@@ 
 (    F   �              std::allocator<char16_t> .?AV?$allocator@_S@std@@ z   �              std::allocator_traits<std::allocator<char16_t> > .?AU?$allocator_traits@V?$allocator@_S@std@@@std@@ 蝰v   �              std::_String_val<std::_Simple_types<char16_t> > .?AV?$_String_val@U?$_Simple_types@_S@std@@@std@@ J   �              std::char_traits<char16_t> .?AU?$char_traits@_S@std@@ 
 z    蝰
 .    
 z   ,  
 .  ,  �   �              std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > .?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@_S@std@@@std@@@std@@ 蝰�   �              std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > .?AV?$_String_const_iterator@V?$_String_val@U?$_Simple_types@_S@std@@@std@@@std@@ 蝰�   �              std::reverse_iterator<std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > > .?AV?$reverse_iterator@V?$_String_iterator@V?$_String_val@U?$_Simple_types@_S@std@@@std@@@std@@@std@@ �   �              std::reverse_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > > .?AV?$reverse_iterator@V?$_String_const_iterator@V?$_String_val@U?$_Simple_types@_S@std@@@std@@@std@@@std@@ V   �              std::initializer_list<char16_t> .?AV?$initializer_list@_S@std@@ 蝰
 *   蝰
 7  ,      6  8  
 (    	   (  :    9      
 (  ,      �  <  <   	   (  :    =      
 (   蝰
 ?  ,  
 .       �  @  A  '  A  '   	   (  :    B      
 (  �      D  8   	   (  :    E      
    D   	   (  :    G          '  .  8   	   (  :    I          '  .   	   (  :    K          A  8   	   (  :    M      
    A   	   (  :    O          A  '  8   	   (  :    Q          A  '   	   (  :    S          @  '  '  8   	   (  :    U          @  '  8   	   (  :    W          @  8   	   (  :    Y      
    @   	   (  :    [      
    8   	   (  :    ]       	   (  :           �    ;     >     C     F     H     J     L     N     P     R     T     V     X     Z     \     ^     _  �  t   �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy .?AW4_Allocation_policy@?$basic_string@_SU?$char_traits@_S@std@@V?$allocator@_S@2@@std@@ 蝰
 *  ,  
 z        b  c  '   	   (         d      �      �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy .?AW4_Construct_strategy@?$basic_string@_SU?$char_traits@_S@std@@V?$allocator@_S@2@@std@@ 
    .   	<  (  :     g       	<  (  :     O       	<  (  :     [      
    6   	<  (  :     k       	<  (  :     G      *    h     i     j     l     m   	<  (  :     K       	<  (  :     S          @  '  #    	<  (  :     q      :    o     i     p     r     j     l     m   	   (  :     [      
    <   	   (  :     u      
    z    	<  (  :     w      "    x     i     j     l  2    o     i     p     r     j     l  
 3   蝰    {  '  .   	2  (  :    |          {  .   	2  (  :    ~          '  '  .   	<  (  :     �          '  A   	<  (  :     �          '  A  '   	<  (  :     �          '  @  '  #    	<  (  :     �          '  @   	<  (  :     �      
 6   蝰    {  �   	2  (  :    �      B    }          �     �     �     �     �     �      {  {  '  .   	<  (  :     �          {  {  A   	<  (  :     �          {  {  A  '   	<  (  :     �          {  {  @   	<  (  :     �          '  #   '  .   	<  (  :     �          '  '  A   	<  (  :     �          '  #   A  '   	<  (  :     �          '  #   @  '  #    	<  (  :     �          '  '  @   	<  (  :     �          {  {  �   	<  (  :     �      R    �     �     �     �     �     �     �     �     �     �   	   (  :                {  {   	2  (  :    �      
    {   	2  (  :    �       	<  (  :            	<  (  :           "    �     �     �     �   	<  (  :           
 ?    	3  (  �            	2  (  :               �     �   	/  (  �             	z  (  :                �     �   	5  (  �            	4  (  :               �     �   	1  (  �            	0  (  :               �     �   	   (  :     g       	1  (  �             	0  (  :                �     �   	#   (  �             	   (  :     K       	   (  :            	0   (  �                c  #   '   	#   (  �     �          c  '  #   '   	#   (  �     �      
 ,  ,      �  �   	   (         �          .  '   	#   (  �     �       	#   (  �     S          A  '  '   	#   (  �     �          @  '   	#   (  �     �      "    �     �     �     �      @  #    	#   (  �     �      "    �     �     �     �      A  #    	#   (  �     �      "    �     �     �     �   	(  (  �           	0   (  �     O       	0   (  �     [          �     �      '  '  A  '   	t   (  �     �       	t   (  �     �       	t   (  �     O          '  '  @  '  '   	t   (  �     �          #   #   @   	t   (  �     �       	t   (  �     [      2    �     �     �     �     �     �   	*  (  �            	#   (  �            	#   (         Y          �  	   �   	8  (  �             	b  (  :                �     �  �   �              std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> .?AV?$_Compressed_pair@V?$allocator@_S@std@@V?$_String_val@U?$_Simple_types@_S@std@@@2@$00@std@@ 篁� 	  (  :     T      �  *  _Alty 蝰  +  _Alty_traits 篁�  ,  _Scary_val �  -  traits_type   *  allocator_type �  z   value_type �  #   size_type 蝰     difference_type   z  pointer   /  const_pointer 蝰  0  reference 蝰  1  const_reference   2  iterator 篁�  3  const_iterator �  4  reverse_iterator 篁�  5  const_reverse_iterator � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � '  _Least_allocation_size �   _Can_memcpy_val  '  _Memcpy_val_offset � '  _Memcpy_val_size 篁� `  basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >   a  _Allocation_policy �	 e  _Deallocate_for_capacity 篁�  f  _Construct_strategy  n  operator= 蝰 s  assign � t  _Memcpy_val_from 篁� v  _Take_contents � y  operator+= � z  append � �  insert �
 �  replace  �  ~basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > 篁� '  npos 篁� �  erase 蝰 �  _Erase_noexcept  �  clear 蝰 �  begin 蝰 �  end  �  _Unchecked_begin 篁� �  _Unchecked_end � �  rbegin � �  rend 篁� �  cbegin � �  cend 篁� �  crbegin  �  crend 蝰 �  shrink_to_fit 蝰 �  at � �  operator[] � �  push_back 蝰 �  pop_back 篁� �  front 蝰 �  back 篁� �  c_str 蝰 �  data 篁� �  length � �  size 篁� �  max_size 篁� �  resize � �  capacity 篁� �  reserve  �  empty 蝰 �  copy 篁� �  _Copy_s  �  _Swap_bx_large_with_small 蝰 v  _Swap_data � v  swap 篁� �  find 篁� �  rfind 蝰 �  find_first_of 蝰 �  find_last_of 篁� �  find_first_not_of 蝰 �  find_last_not_of 篁� �  substr � �  _Equal � �  compare  �  get_allocator 蝰 �  _Calculate_growth 蝰 �  _Become_small 蝰 �  _Eos 篁� �  _Tidy_init � �  _Tidy_deallocate 篁� �  _Orphan_all  v  _Swap_proxy_and_iterators 蝰 �  _Getal �
 �    _Mypair 蝰�  __vecDelDtor 篁癃 � 6�            std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > .?AV?$basic_string@_SU?$char_traits@_S@std@@V?$allocator@_S@2@@std@@  	   (  :     S      �   �              std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > .?AV?$basic_string@_UU?$char_traits@_U@std@@V?$allocator@_U@2@@std@@ 
 �    F   �              std::allocator<char32_t> .?AV?$allocator@_U@std@@ z   �              std::allocator_traits<std::allocator<char32_t> > .?AU?$allocator_traits@V?$allocator@_U@std@@@std@@ 蝰v   �              std::_String_val<std::_Simple_types<char32_t> > .?AV?$_String_val@U?$_Simple_types@_U@std@@@std@@ J   �              std::char_traits<char32_t> .?AU?$char_traits@_U@std@@ 
 {    蝰
 �    
 {   ,  
 �  ,  �   �              std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > .?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@_U@std@@@std@@@std@@ 蝰�   �              std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > > .?AV?$_String_const_iterator@V?$_String_val@U?$_Simple_types@_U@std@@@std@@@std@@ 蝰�   �              std::reverse_iterator<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > > .?AV?$reverse_iterator@V?$_String_iterator@V?$_String_val@U?$_Simple_types@_U@std@@@std@@@std@@@std@@ �   �              std::reverse_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > > > .?AV?$reverse_iterator@V?$_String_const_iterator@V?$_String_val@U?$_Simple_types@_U@std@@@std@@@std@@@std@@ V   �              std::initializer_list<char32_t> .?AV?$initializer_list@_U@std@@ 蝰
 �   蝰
 �  ,      �     
 �    	   �            
 �  ,      �       	   �            
 �   蝰
   ,  
 �       �    	  '  	  '   	   �      
      
 �  �            	   �      
      
       	   �                '  �      	   �                '  �   	   �                	      	   �            
    	   	   �                	  '      	   �                	  '   	   �                  '  '      	   �                  '      	   �                      	   �      !      
       	   �      #      
        	   �      %       	   �             �                                                                      "     $     &     '  �  t   �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy .?AW4_Allocation_policy@?$basic_string@_UU?$char_traits@_U@std@@V?$allocator@_U@2@@std@@ 蝰
 �  ,  
 {        *  +  '   	   �         ,      �      �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy .?AW4_Construct_strategy@?$basic_string@_UU?$char_traits@_U@std@@V?$allocator@_U@2@@std@@ 
    �   	  �       /       	  �              	  �       #      
    �   	  �       3       	  �             *    0     1     2     4     5   	  �              	  �                   '  #    	  �       9      :    7     1     8     :     2     4     5   	   �       #      
       	   �       =      
    {    	  �       ?      "    @     1     2     4  2    7     1     8     :     2     4  
 �   蝰    C  '  �   	�  �      D          C  �   	�  �      F          '  '  �   	  �       H          '  	   	  �       J          '  	  '   	  �       L          '    '  #    	  �       N          '     	  �       P      
 �   蝰    C  R   	�  �      S      B    E     G     I     K     M     O     Q     T      C  C  '  �   	  �       V          C  C  	   	  �       X          C  C  	  '   	  �       Z          C  C     	  �       \          '  #   '  �   	  �       ^          '  '  	   	  �       `          '  #   	  '   	  �       b          '  #     '  #    	  �       d          '  '     	  �       f          C  C  R   	  �       h      R    W     Y     [     ]     _     a     c     e     g     i   	   �                  C  C   	�  �      l      
    C   	�  �      n       	  �              	  �             "    m     o     p     q   	  �             
     	�  �  t            	�  �                 u     v   	�  �  t             	{  �                  x     y   	�  �  t            	�  �                 {     |   	�  �  t            	�  �                 ~        	   �       /       	�  �  t             	�  �                  �     �   	#   �  t             	   �              	   �              	0   �  t                +  #   '   	#   �  t     �          +  '  #   '   	#   �  t     �      
 �  ,      �  �   	   �         �          �  '   	#   �  t     �       	#   �  t               	  '  '   	#   �  t     �            '   	#   �  t     �      "    �     �     �     �        #    	#   �  t     �      "    �     �     �     �      	  #    	#   �  t     �      "    �     �     �     �   	�  �  t           	0   �  t            	0   �  t     #          �     �      '  '  	  '   	t   �  t     �       	t   �  t     `       	t   �  t               '  '    '  '   	t   �  t     �          #   #      	t   �  t     �       	t   �  t     #      2    �     �     �     �     �     �   	�  �  t            	#   �  t            	#   �         Y          �  	   �   	   �  t             	*  �                  �     �  �   �              std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> .?AV?$_Compressed_pair@V?$allocator@_U@std@@V?$_String_val@U?$_Simple_types@_U@std@@@2@$00@std@@ 篁� 	  �       T      �  �  _Alty 蝰  �  _Alty_traits 篁�  �  _Scary_val �  �  traits_type   �  allocator_type �  {   value_type �  #   size_type 蝰     difference_type   {  pointer   �  const_pointer 蝰  �  reference 蝰  �  const_reference   �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�  �  const_reverse_iterator � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � '  _Least_allocation_size �   _Can_memcpy_val  '  _Memcpy_val_offset � '  _Memcpy_val_size 篁� (  basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >   )  _Allocation_policy �	 -  _Deallocate_for_capacity 篁�  .  _Construct_strategy  6  operator= 蝰 ;  assign � <  _Memcpy_val_from 篁� >  _Take_contents � A  operator+= � B  append � U  insert �
 j  replace  k  ~basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > 篁� '  npos 篁� r  erase 蝰 s  _Erase_noexcept  k  clear 蝰 w  begin 蝰 w  end  z  _Unchecked_begin 篁� z  _Unchecked_end � }  rbegin � }  rend 篁� u  cbegin � u  cend 篁� {  crbegin  {  crend 蝰 k  shrink_to_fit 蝰 �  at � �  operator[] � �  push_back 蝰 k  pop_back 篁� �  front 蝰 �  back 篁� x  c_str 蝰 x  data 篁� �  length � �  size 篁� �  max_size 篁� �  resize � �  capacity 篁� �  reserve  �  empty 蝰 �  copy 篁� �  _Copy_s  �  _Swap_bx_large_with_small 蝰 >  _Swap_data � >  swap 篁� �  find 篁� �  rfind 蝰 �  find_first_of 蝰 �  find_last_of 篁� �  find_first_not_of 蝰 �  find_last_not_of 篁� �  substr � �  _Equal � �  compare  �  get_allocator 蝰 �  _Calculate_growth 蝰 k  _Become_small 蝰 �  _Eos 篁� k  _Tidy_init � k  _Tidy_deallocate 篁� k  _Orphan_all  >  _Swap_proxy_and_iterators 蝰 �  _Getal �
 �    _Mypair 蝰�  __vecDelDtor 篁癃 � 6�            std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > .?AV?$basic_string@_UU?$char_traits@_U@std@@V?$allocator@_U@2@@std@@  	   �             >   �              std::logic_error .?AVlogic_error@std@@ 篁�
 �   
 �  �  
    �   	   �  �    �      
 �   蝰
 �  ,  
    �   	   �  �    �       	   �  �    �       	   �  �    �      "   �    �     �     �   	   �  �            
 �  ,   	�  �  �     �       	�  �  �     �         �    �   	  �  �     T      z       蝰    _Mybase  �  logic_error �  ~logic_error 篁� �  operator= 蝰�      __vecDelDtor 篁�> 
 6�         std::logic_error .?AVlogic_error@std@@ 篁� 	   �  �     �      
 �     	   �  �     �      
 �    >   �              std::domain_error .?AVdomain_error@std@@ �
 �   
 �  �  
    �   	   �  �    �      
 �   蝰
 �  ,  
    �   	   �  �    �       	   �  �    �       	   �  �    �      "   �    �     �     �   	   �  �            
 �  ,   	�  �  �     �       	�  �  �     �         �    �   	  �  �     T      ~   �    蝰  �  _Mybase  �  domain_error 篁��  ~domain_error 蝰 �  operator= 蝰�      __vecDelDtor 篁�> 
 6�         std::domain_error .?AVdomain_error@std@@ � 	   �  �     �       	   �  �     �      
 �    F   �              std::invalid_argument .?AVinvalid_argument@std@@ �
 �   
 �  �  
    �   	   �  �    �      
 �   蝰
 �  ,  
    �   	   �  �    �       	   �  �    �       	   �  �    �      "   �    �     �     �   	   �  �            
 �  ,   	�  �  �     �       	�  �  �     �         �    �   	  �  �     T      �   �    蝰  �  _Mybase  �  invalid_argument 篁��  ~invalid_argument 蝰 �  operator= 蝰�      __vecDelDtor 篁馞 
 6�         std::invalid_argument .?AVinvalid_argument@std@@ � 	   �  �     �       	   �  �     �      
 �    >   �              std::length_error .?AVlength_error@std@@ �
 �   
 �  �  
        	   �  �          
 �   蝰
   ,  
       	   �  �           	   �  �    �       	   �  �    �      "                    	   �  �            
 �  ,   	  �  �            	  �  �                  
   	  �  �     T      ~   �    蝰  �  _Mybase  	  length_error 篁�
  ~length_error 蝰   operator= 蝰      __vecDelDtor 篁�> 
 6         std::length_error .?AVlength_error@std@@ � 	   �  �     �       	   �  �     �      
 �    >   �              std::out_of_range .?AVout_of_range@std@@ �
    
   �  
       	               
    蝰
   ,  
       	                	         �       	         �      "                    	                 
   ,   	"                	"                  #    $   	         T      ~   �    蝰  �  _Mybase     out_of_range 篁�!  ~out_of_range 蝰 %  operator= 蝰&      __vecDelDtor 篁�> 
 6'         std::out_of_range .?AVout_of_range@std@@ � 	          �       	          �      
     B   �              std::runtime_error .?AVruntime_error@std@@ 篁�
 ,   
 ,  �  
    .   	   ,  -    /      
 ,   蝰
 1  ,  
    2   	   ,  -    3       	   ,  -    �       	   ,  -    �      "   0    4     5     6   	   ,  -            
 ,  ,   	9  ,  -     /       	9  ,  -     3         :    ;   	  ,  -     T      ~       蝰    _Mybase  7  runtime_error 蝰8  ~runtime_error � <  operator= 蝰=      __vecDelDtor 篁馚 
 6>         std::runtime_error .?AVruntime_error@std@@ 篁� 	   ,  -     �       	   ,  -     �      
 ,    B   �              std::overflow_error .?AVoverflow_error@std@@ �
 C   
 C  �  
    E   	   C  D    F      
 C   蝰
 H  ,  
    I   	   C  D    J       	   C  D    �       	   C  D    �      "   G    K     L     M   	   C  D            
 C  ,   	P  C  D     F       	P  C  D     J         Q    R   	  C  D     T      ~   ,    蝰  ,  _Mybase  N  overflow_error �O  ~overflow_error  S  operator= 蝰T      __vecDelDtor 篁馚 
 6U         std::overflow_error .?AVoverflow_error@std@@ � 	   C  D     �       	   C  D     �      
 C    F   �              std::underflow_error .?AVunderflow_error@std@@ 篁�
 Z   
 Z  �  
    \   	   Z  [    ]      
 Z   蝰
 _  ,  
    `   	   Z  [    a       	   Z  [    �       	   Z  [    �      "   ^    b     c     d   	   Z  [            
 Z  ,   	g  Z  [     ]       	g  Z  [     a         h    i   	  Z  [     T      �   ,    蝰  ,  _Mybase  e  underflow_error f  ~underflow_error 篁� j  operator= 蝰k      __vecDelDtor 篁馞 
 6l         std::underflow_error .?AVunderflow_error@std@@ 篁� 	   Z  [     �       	   Z  [     �      
 Z    >   �              std::range_error .?AVrange_error@std@@ 篁�
 q   
 q  �  
    s   	   q  r    t      
 q   蝰
 v  ,  
    w   	   q  r    x       	   q  r    �       	   q  r    �      "   u    y     z     {   	   q  r            
 q  ,   	~  q  r     t       	~  q  r     x             �   	  q  r     T      z   ,    蝰  ,  _Mybase  |  range_error }  ~range_error 篁� �  operator= 蝰�      __vecDelDtor 篁�> 
 6�         std::range_error .?AVrange_error@std@@ 篁� 	   q  r     �       	   q  r     �      
 q     	   ,  -     /       	   q  r     t       	   ,  -     3      
 1     	   q  r     x      
 v     9  #     � 9  #     � 9  #   
  � 9  #     � 9  #     � 9  #     � 9  #     � 9  #     � 9  #   
  � 9  #   	  � 9  #     � 9  #     � 9  #     � 9  #     � 9  #      � 9  #   (  駣  D3D_FEATURE_LEVEL_1_0_GENERIC   D3D_FEATURE_LEVEL_1_0_CORE 篁� � 慏3D_FEATURE_LEVEL_9_1 蝰 � 扗3D_FEATURE_LEVEL_9_2 蝰 � 揇3D_FEATURE_LEVEL_9_3 蝰 � 燚3D_FEATURE_LEVEL_10_0 � � 3D_FEATURE_LEVEL_10_1 � � 癉3D_FEATURE_LEVEL_11_0 � � 盌3D_FEATURE_LEVEL_11_1 � � 繢3D_FEATURE_LEVEL_12_0 � � 罝3D_FEATURE_LEVEL_12_1 � � 翫3D_FEATURE_LEVEL_12_2 �:   t   �  D3D_FEATURE_LEVEL .?AW4D3D_FEATURE_LEVEL@@ 馼   �              D3D12_PROPERTY_LAYOUT_FORMAT_TABLE .?AUD3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@ 蝰~   �              D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FORMAT_DETAIL .?AUFORMAT_DETAIL@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@ �
 �   蝰
 �    �   DXGI_FORMAT_UNKNOWN 蝰  DXGI_FORMAT_R32G32B32A32_TYPELESS   DXGI_FORMAT_R32G32B32A32_FLOAT 篁�  DXGI_FORMAT_R32G32B32A32_UINT   DXGI_FORMAT_R32G32B32A32_SINT   DXGI_FORMAT_R32G32B32_TYPELESS 篁�  DXGI_FORMAT_R32G32B32_FLOAT 蝰  DXGI_FORMAT_R32G32B32_UINT 篁�  DXGI_FORMAT_R32G32B32_SINT 篁� 	 DXGI_FORMAT_R16G16B16A16_TYPELESS  
 DXGI_FORMAT_R16G16B16A16_FLOAT 篁�  DXGI_FORMAT_R16G16B16A16_UNORM 篁�  DXGI_FORMAT_R16G16B16A16_UINT  
 DXGI_FORMAT_R16G16B16A16_SNORM 篁�  DXGI_FORMAT_R16G16B16A16_SINT   DXGI_FORMAT_R32G32_TYPELESS 蝰  DXGI_FORMAT_R32G32_FLOAT �  DXGI_FORMAT_R32G32_UINT 蝰  DXGI_FORMAT_R32G32_SINT 蝰  DXGI_FORMAT_R32G8X24_TYPELESS   DXGI_FORMAT_D32_FLOAT_S8X24_UINT �  DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS �  DXGI_FORMAT_X32_TYPELESS_G8X24_UINT 蝰  DXGI_FORMAT_R10G10B10A2_TYPELESS �  DXGI_FORMAT_R10G10B10A2_UNORM   DXGI_FORMAT_R10G10B10A2_UINT �  DXGI_FORMAT_R11G11B10_FLOAT 蝰  DXGI_FORMAT_R8G8B8A8_TYPELESS   DXGI_FORMAT_R8G8B8A8_UNORM 篁�  DXGI_FORMAT_R8G8B8A8_UNORM_SRGB 蝰  DXGI_FORMAT_R8G8B8A8_UINT   DXGI_FORMAT_R8G8B8A8_SNORM 篁�   DXGI_FORMAT_R8G8B8A8_SINT  ! DXGI_FORMAT_R16G16_TYPELESS 蝰 " DXGI_FORMAT_R16G16_FLOAT � # DXGI_FORMAT_R16G16_UNORM � $ DXGI_FORMAT_R16G16_UINT 蝰 % DXGI_FORMAT_R16G16_SNORM � & DXGI_FORMAT_R16G16_SINT 蝰 ' DXGI_FORMAT_R32_TYPELESS � ( DXGI_FORMAT_D32_FLOAT  ) DXGI_FORMAT_R32_FLOAT  * DXGI_FORMAT_R32_UINT � + DXGI_FORMAT_R32_SINT � , DXGI_FORMAT_R24G8_TYPELESS 篁� - DXGI_FORMAT_D24_UNORM_S8_UINT  . DXGI_FORMAT_R24_UNORM_X8_TYPELESS  / DXGI_FORMAT_X24_TYPELESS_G8_UINT � 0 DXGI_FORMAT_R8G8_TYPELESS  1 DXGI_FORMAT_R8G8_UNORM 篁� 2 DXGI_FORMAT_R8G8_UINT  3 DXGI_FORMAT_R8G8_SNORM 篁� 4 DXGI_FORMAT_R8G8_SINT  5 DXGI_FORMAT_R16_TYPELESS � 6 DXGI_FORMAT_R16_FLOAT  7 DXGI_FORMAT_D16_UNORM  8 DXGI_FORMAT_R16_UNORM  9 DXGI_FORMAT_R16_UINT � : DXGI_FORMAT_R16_SNORM  ; DXGI_FORMAT_R16_SINT � < DXGI_FORMAT_R8_TYPELESS 蝰 = DXGI_FORMAT_R8_UNORM � > DXGI_FORMAT_R8_UINT 蝰 ? DXGI_FORMAT_R8_SNORM � @ DXGI_FORMAT_R8_SINT 蝰 A DXGI_FORMAT_A8_UNORM � B DXGI_FORMAT_R1_UNORM � C DXGI_FORMAT_R9G9B9E5_SHAREDEXP 篁� D DXGI_FORMAT_R8G8_B8G8_UNORM 蝰 E DXGI_FORMAT_G8R8_G8B8_UNORM 蝰 F DXGI_FORMAT_BC1_TYPELESS � G DXGI_FORMAT_BC1_UNORM  H DXGI_FORMAT_BC1_UNORM_SRGB 篁� I DXGI_FORMAT_BC2_TYPELESS � J DXGI_FORMAT_BC2_UNORM  K DXGI_FORMAT_BC2_UNORM_SRGB 篁� L DXGI_FORMAT_BC3_TYPELESS � M DXGI_FORMAT_BC3_UNORM  N DXGI_FORMAT_BC3_UNORM_SRGB 篁� O DXGI_FORMAT_BC4_TYPELESS � P DXGI_FORMAT_BC4_UNORM  Q DXGI_FORMAT_BC4_SNORM  R DXGI_FORMAT_BC5_TYPELESS � S DXGI_FORMAT_BC5_UNORM  T DXGI_FORMAT_BC5_SNORM  U DXGI_FORMAT_B5G6R5_UNORM � V DXGI_FORMAT_B5G5R5A1_UNORM 篁� W DXGI_FORMAT_B8G8R8A8_UNORM 篁� X DXGI_FORMAT_B8G8R8X8_UNORM 篁� Y DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM 篁� Z DXGI_FORMAT_B8G8R8A8_TYPELESS  [ DXGI_FORMAT_B8G8R8A8_UNORM_SRGB 蝰 \ DXGI_FORMAT_B8G8R8X8_TYPELESS  ] DXGI_FORMAT_B8G8R8X8_UNORM_SRGB 蝰 ^ DXGI_FORMAT_BC6H_TYPELESS  _ DXGI_FORMAT_BC6H_UF16  ` DXGI_FORMAT_BC6H_SF16  a DXGI_FORMAT_BC7_TYPELESS � b DXGI_FORMAT_BC7_UNORM  c DXGI_FORMAT_BC7_UNORM_SRGB 篁� d DXGI_FORMAT_AYUV � e DXGI_FORMAT_Y410 � f DXGI_FORMAT_Y416 � g DXGI_FORMAT_NV12 � h DXGI_FORMAT_P010 � i DXGI_FORMAT_P016 � j DXGI_FORMAT_420_OPAQUE 篁� k DXGI_FORMAT_YUY2 � l DXGI_FORMAT_Y210 � m DXGI_FORMAT_Y216 � n DXGI_FORMAT_NV11 � o DXGI_FORMAT_AI44 � p DXGI_FORMAT_IA44 � q DXGI_FORMAT_P8 篁� r DXGI_FORMAT_A8P8 � s DXGI_FORMAT_B4G4R4A4_UNORM 篁� � DXGI_FORMAT_P208 � � DXGI_FORMAT_V208 � � DXGI_FORMAT_V408 � � DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE 蝰 � DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE 蝰 � DXGI_FORMAT_A4B4G4R4_UNORM 篁�  ��DXGI_FORMAT_FORCE_UINT 蝰. {  t   �  DXGI_FORMAT .?AW4DXGI_FORMAT@@ �
 �   蝰
 �         #     �
      蝰
 u    蝰
 u   蝰
 u   蝰.    D3DFL_STANDARD 篁�  ��D3DFL_CUSTOM :   t   �  D3D_FORMAT_LAYOUT .?AW4D3D_FORMAT_LAYOUT@@ �
 �  	蝰N    D3DFTL_NO_TYPE 篁�  �﨑3DFTL_PARTIAL_TYPE �  ��D3DFTL_FULL_TYPE B   t   �  D3D_FORMAT_TYPE_LEVEL .?AW4D3D_FORMAT_TYPE_LEVEL@@ �
 �  
蝰r   �麯3DFCN_R   �鼶3DFCN_G   �﨑3DFCN_B   ��D3DFCN_A    D3DFCN_D �  D3DFCN_S �  D3DFCN_X 馢   t   �  D3D_FORMAT_COMPONENT_NAME .?AW4D3D_FORMAT_COMPONENT_NAME@@ �
 �  蝰
 �  蝰
 �  蝰
 �  蝰�    D3DFCI_TYPELESS 蝰  �麯3DFCI_FLOAT   �鼶3DFCI_SNORM   �﨑3DFCI_UNORM   ��D3DFCI_SINT �  D3DFCI_UINT 蝰  D3DFCI_UNORM_SRGB   D3DFCI_BIASED_FIXED_2_8 蝰^   t   �  D3D_FORMAT_COMPONENT_INTERPRETATION .?AW4D3D_FORMAT_COMPONENT_INTERPRETATION@@ �
 �  蝰
 �  蝰
 �   蝰
 �  蝰
 0    蝰
 0   蝰
 0   蝰
 0   蝰
 0   蝰
 0   蝰
 0   蝰�
 �    DXGIFormat 篁�
 �   ParentFormat �
 �   pDefaultFormatCastSet 
 �   BitsPerComponent �
      BitsPerUnit 蝰
 �   SRGBFormat 篁�
 �   WidthAlignment 篁�
 �   HeightAlignment 蝰
 �   DepthAlignment 篁�
 �   Layout 篁�
 �   TypeLevel 
 �   ComponentName0 篁�
 �   ComponentName1 篁�
 �   ComponentName2 篁�
 �   ComponentName3 篁�
 �   ComponentInterpretation0 �
 �   ComponentInterpretation1 �
 �   ComponentInterpretation2 �
 �   ComponentInterpretation3 �
 �    bDX9VertexOrIndexFormat 蝰
 �    bDX9TextureFormat 
 �    bFloatNormFormat �
 �    bPlanar 蝰
 �    bYUV �
 �    bDependantFormatCastSet 蝰
 �    bInternal ~  �          ( D3D12_PROPERTY_LAYOUT_FORMAT_TABLE::FORMAT_DETAIL .?AUFORMAT_DETAIL@D3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@ � 	�  �         �      
    �   	u   �         �       	0   �         �      
 �     	�  �         �      
         u   u   u         �  
 u   ,   	t   �         �       	�  �         �      
 �  ,      u   u   �         �  
 %        �  u   �   	   �         �      >   �              D3D12_MEMCPY_DEST .?AUD3D12_MEMCPY_DEST@@ 
 �    > 
     pData 
 #    RowPitch �
 #    SlicePitch 篁�>   �           D3D12_MEMCPY_DEST .?AUD3D12_MEMCPY_DEST@@ 
 �  ,   	u   �                
 �   蝰
 �    
 �   蝰
 �    
 �    
 �    
 �   蝰
 �    
 �   
 �    
 �   蝰
 �     9  #     � 	   �  �     �       	   �  �     �       	   �  �     �      
 �     	   �  �     �      
 �     9  #     裰    D3D12_RESOURCE_DIMENSION_UNKNOWN �  D3D12_RESOURCE_DIMENSION_BUFFER 蝰  D3D12_RESOURCE_DIMENSION_TEXTURE1D 篁�  D3D12_RESOURCE_DIMENSION_TEXTURE2D 篁�  D3D12_RESOURCE_DIMENSION_TEXTURE3D 篁馢   t   �  D3D12_RESOURCE_DIMENSION .?AW4D3D12_RESOURCE_DIMENSION@@ 篁�>   �              D3D12_TILE_SHAPE .?AUD3D12_TILE_SHAPE@@ 蝰
 �    N 
 u     WidthInTexels 
 u    HeightInTexels 篁�
 u    DepthInTexels >   �           D3D12_TILE_SHAPE .?AUD3D12_TILE_SHAPE@@ 蝰
 �  ,      �  �   	u   �         �          �  u    	�  �         �       	�  �         �      
 �    
      Z   �              std::integral_constant<bool,1> .?AU?$integral_constant@_N$00@std@@ 篁�
    �  
 �    	�  �             
    �   	{  �                        
 �    	   �  	               +  '   	   �  	               '  "   	{  �  	     
       	{  �  	                       	#   �              �  �  _From_primary 蝰  {   value_type �  {  pointer   �  const_pointer 蝰  �  reference 蝰  �  const_reference   #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal    address  
  allocator<char32_t>    deallocate �   allocate 篁�   max_size 篁� '  _Minimum_asan_allocation_alignment 馞             std::allocator<char32_t> .?AV?$allocator@_U@std@@ 
 �   蝰
     	   �              
 �    	*  �                          	   �               	  �       T      �   �    蝰
 �    _Myval2 蝰  �  _Mybase    _Get_first �  ~_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> 蝰  __vecDelDtor 篁褛              std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> .?AV?$_Compressed_pair@V?$allocator@_U@std@@V?$_String_val@U?$_Simple_types@_U@std@@@2@$00@std@@ 篁馴   �              std::_Zero_then_variadic_args_t .?AU_Zero_then_variadic_args_t@std@@ �
       	   �              	   �            Z    W           std::_Zero_then_variadic_args_t .?AU_Zero_then_variadic_args_t@std@@ �
     
 �    
 $    
    1  
 7    	/  *  '     &      
    0   	z  *  '     )          (     *  
 *    	   *  ,               c  '   	   *  ,     .       	z  *  ,     
       	z  *  ,               0     1   	#   *  '            �  *  _From_primary 蝰  z   value_type �  z  pointer   /  const_pointer 蝰  0  reference 蝰  1  const_reference   #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal  +  address  -  allocator<char16_t>  /  deallocate � 2  allocate 篁� 3  max_size 篁� '  _Minimum_asan_allocation_alignment 馞  4           std::allocator<char16_t> .?AV?$allocator@_S@std@@ 
 �   蝰
 6    	8  �  7            
 �    	b  �  9                8     :   	   �  9             	  �  9     T      �   *    蝰
 ,    _Myval2 蝰  *  _Mybase  ;  _Get_first �<  ~_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> 蝰=  __vecDelDtor 篁褛  >            std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> .?AV?$_Compressed_pair@V?$allocator@_S@std@@V?$_String_val@U?$_Simple_types@_S@std@@@2@$00@std@@ 篁� 	   �  9            	   �  9          
 #    
 B    
    k  
 q    	-  f  E     D      
    j   	q  f  E     G          F     H  
 f    	   f  J               4  '   	   f  J     L       	q  f  J     
       	q  f  J               N     O   	#   f  E            �  f  _From_primary 蝰  q   value_type �  q  pointer   -  const_pointer 蝰  j  reference 蝰  k  const_reference   #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal  I  address  K  allocator<wchar_t> � M  deallocate � P  allocate 篁� Q  max_size 篁� '  _Minimum_asan_allocation_alignment 馞  R           std::allocator<wchar_t> .?AV?$allocator@_W@std@@ �
 #   蝰
 T    	r  #  U            
 #    	�  #  W                V     X   	   #  W             	  #  W     T      �   f    蝰
 h    _Myval2 蝰  f  _Mybase  Y  _Get_first �Z  ~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> [  __vecDelDtor 篁裰  \            std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> .?AV?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@ � 	   #  W            	   #  W          
 _   蝰
 `    
 a    
    �  
 �    	:  �  d     c      
    �   	p  �  d     f          e     g  
 �    	   �  i               ?  '   	   �  i     k       	p  �  i     
       	p  �  i               m     n   	#   �  d            �  �  _From_primary 蝰  p   value_type �  p  pointer   :  const_pointer 蝰  �  reference 蝰  �  const_reference   #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal  h  address  j  allocator<char>  l  deallocate � o  allocate 篁� p  max_size 篁� '  _Minimum_asan_allocation_alignment 馚  q           std::allocator<char> .?AV?$allocator@D@std@@ �
 `    	�  _  s            
 _    	�  _  u                t     v   	   _  u             	  _  u     T      �   �    蝰
 �    _Myval2 蝰  �  _Mybase  w  _Get_first �x  ~_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> 蝰y  __vecDelDtor 篁裎  z            std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> .?AV?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@ �
 �   蝰
 |    
 }    
 �    	   �             
 |    	:  �  �             	p  �                  �     �   	0   �  �             	   �               	   �  �            	   �                 	#   �  �           v   �      std::_String_val<std::_Simple_types<char> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@D@std@@@std@@  	  �       T      Z  �    蝰  p   value_type �  #   size_type 蝰     difference_type   p  pointer   :  const_pointer 蝰  �  reference 蝰  �  const_reference  �  _String_val<std::_Simple_types<char> > � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � �  _Myptr � �  _Large_mode_engaged  �  _Activate_SSO_buffer 篁� �  _Check_offset 蝰 �  _Check_offset_exclusive  �  _Xran 蝰 �  _Clamp_suffix_size �  �  _Bxty 蝰
 �    _Bx 蝰
 #    _Mysize 蝰
 #    _Myres 篁��  ~_String_val<std::_Simple_types<char> > �  __vecDelDtor 篁駌  �            std::_String_val<std::_Simple_types<char> > .?AV?$_String_val@U?$_Simple_types@D@std@@@std@@ �
 _    
 �     	   _  u            	   _  u          
 �    
 �    
 �    	   �  �           
 �   蝰
 �    	�  �  �             	{  �  �                �     �   	0   �  �             	   �  �             	   �  �            	   �                 	#   �  �           ~   �      std::_String_val<std::_Simple_types<char32_t> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@_U@std@@@std@@ 篁� 	  �  �     T      b  �    蝰  {   value_type �  #   size_type 蝰     difference_type   {  pointer   �  const_pointer 蝰  �  reference 蝰  �  const_reference  �  _String_val<std::_Simple_types<char32_t> > � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � �  _Myptr � �  _Large_mode_engaged  �  _Activate_SSO_buffer 篁� �  _Check_offset 蝰 �  _Check_offset_exclusive  �  _Xran 蝰 �  _Clamp_suffix_size �  �  _Bxty 蝰
 �    _Bx 蝰
 #    _Mysize 蝰
 #    _Myres 篁��  ~_String_val<std::_Simple_types<char32_t> > �  __vecDelDtor 篁駐  �            std::_String_val<std::_Simple_types<char32_t> > .?AV?$_String_val@U?$_Simple_types@_U@std@@@std@@ 
 ,    
 �    
 ,    	   ,  �           
 ,   蝰
 �    	/  ,  �             	z  ,  �                �     �   	0   ,  �             	   ,  �             	   ,  �            	   ,                 	#   ,  �           ~   �      std::_String_val<std::_Simple_types<char16_t> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@_S@std@@@std@@ 篁� 	  ,  �     T      b  �    蝰  z   value_type �  #   size_type 蝰     difference_type   z  pointer   /  const_pointer 蝰  0  reference 蝰  1  const_reference  �  _String_val<std::_Simple_types<char16_t> > � '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � �  _Myptr � �  _Large_mode_engaged  �  _Activate_SSO_buffer 篁� �  _Check_offset 蝰 �  _Check_offset_exclusive  �  _Xran 蝰 �  _Clamp_suffix_size �  �  _Bxty 蝰
 �    _Bx 蝰
 #    _Mysize 蝰
 #    _Myres 篁��  ~_String_val<std::_Simple_types<char16_t> > �  __vecDelDtor 篁駐  �            std::_String_val<std::_Simple_types<char16_t> > .?AV?$_String_val@U?$_Simple_types@_S@std@@@std@@ 
 h    
 �    
 h    	   h  �           
 h   蝰
 �    	-  h  �             	q  h  �                �     �   	0   h  �             	   h  �             	   h  �            	   h                 	#   h  �           z   �      std::_String_val<std::_Simple_types<wchar_t> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@_W@std@@@std@@  	  h  �     T      b  �    蝰  q   value_type �  #   size_type 蝰     difference_type   q  pointer   -  const_pointer 蝰  j  reference 蝰  k  const_reference  �  _String_val<std::_Simple_types<wchar_t> > 蝰 '  _BUF_SIZE 蝰 '  _Alloc_mask  '  _Small_string_capacity � �  _Myptr � �  _Large_mode_engaged  �  _Activate_SSO_buffer 篁� �  _Check_offset 蝰 �  _Check_offset_exclusive  �  _Xran 蝰 �  _Clamp_suffix_size �  �  _Bxty 蝰
 �    _Bx 蝰
 #    _Mysize 蝰
 #    _Myres 篁��  ~_String_val<std::_Simple_types<wchar_t> > ��  __vecDelDtor 篁駐  �            std::_String_val<std::_Simple_types<wchar_t> > .?AV?$_String_val@U?$_Simple_types@_W@std@@@std@@ �
 �    
 �    
 �    
 �    
 �    	   �  �            	   �  �             {   #     � p   #     � 	  �  �     T      n  �  _Bxty 蝰 �  ~_Bxty �
 �    _Buf �
 {    _Ptr �
 �    _Alias 篁��  __vecDelDtor 篁駘  
�   std::_String_val<std::_Simple_types<char32_t> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@_U@std@@@std@@ 篁�
 �    
 �    
 �    	   �  �            	   �  �             z   #     � p   #     � 	  �  �     T      n  �  _Bxty 蝰 �  ~_Bxty �
 �    _Buf �
 z    _Ptr �
 �    _Alias 篁��  __vecDelDtor 篁駘  
�   std::_String_val<std::_Simple_types<char16_t> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@_S@std@@@std@@ 篁�
 �    
 �    
 �    	   �  �            	   �  �             q   #     � 	  �  �     T      n  �  _Bxty 蝰 �  ~_Bxty �
 �    _Buf �
 q    _Ptr �
 �    _Alias 篁��  __vecDelDtor 篁駔  
�   std::_String_val<std::_Simple_types<wchar_t> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@_W@std@@@std@@ 
 �    
 �    
 �    	   �  �            	   �  �             p   #     � 	  �  �     T      n  �  _Bxty 蝰 �  ~_Bxty �
 �    _Buf �
 p    _Ptr �
 �    _Alias 篁��  __vecDelDtor 篁駐  
�   std::_String_val<std::_Simple_types<char> >::_Bxty .?AT_Bxty@?$_String_val@U?$_Simple_types@D@std@@@std@@ 
 {    
 �    
 {  ,  
    �         �  
 �    Z   �              std::_Char_traits<char32_t,unsigned int> .?AU?$_Char_traits@_UI@std@@     �  �   	   �         �      
 z    
 *    
 z  ,  
    �         �  
 �    N   �              std::_WChar_traits<char16_t> .?AU?$_WChar_traits@_S@std@@     0  1   	   �                
 f    
 q  ,  
               
 0    N   �              std::_WChar_traits<wchar_t> .?AU?$_WChar_traits@_W@std@@ �    j  k   	                  
 �    
 p  ,  
               
 Z    Z   �              std::_Narrow_char_traits<char,int> .?AU?$_Narrow_char_traits@DH@std@@     �  �   	                  
 �   蝰
     
     
 �    
    p   p       
 ?    
 �    
 '    
 �    
 �    
 '  ,      '  �   #            '  �   #             '  �   #      "  
 (     	   �  	            
 i    
         +  	  '   	{  �         (      
 �        *  �   	{  �         +      
 +  ,      �  -         .  
    {   {     0   	   *  ,            ^   �              std::_Char_traits<char16_t,unsigned short> .?AU?$_Char_traits@_SG@std@@ 蝰    c  A  '   	z  3         4      
 �        b  �   	z  (         7      
 c  ,      �  9         :  
    z   z     <   	   f  J            ^   �              std::_Char_traits<wchar_t,unsigned short> .?AU?$_Char_traits@_WG@std@@ 篁�    4  3  '   	q  ?         @      
 �        �  �   	q  d         C      
 4  ,        E         F   	   �  i            N   �              std::_Char_traits<char,int> .?AU?$_Char_traits@DH@std@@ 蝰    ?  B  '   	p  I         J      
 �        �  �   	p  �         M      
 ?  ,        O         P      �  �         R  
     
     
 U    
     �   �              std::_Default_allocator_traits<std::allocator<char32_t> > .?AU?$_Default_allocator_traits@V?$allocator@_U@std@@@std@@  	#   X         %      
 �                  [  
 ?    
 6    
 ^    
     �   �              std::_Default_allocator_traits<std::allocator<char16_t> > .?AU?$_Default_allocator_traits@V?$allocator@_S@std@@@std@@  	#   a         ]      
 7    
 y    
 T    
 e    
     �   �              std::_Default_allocator_traits<std::allocator<wchar_t> > .?AU?$_Default_allocator_traits@V?$allocator@_W@std@@@std@@ � 	#   h         �      
 q    �   �              std::_Default_allocator_traits<std::allocator<char> > .?AU?$_Default_allocator_traits@V?$allocator@D@std@@@std@@ � 	#   k         �      
 �    
 *    
 6    
 B    
 L        #   �  '   #      r   {     +      #           u   �     �  
    -   -     x  
 +     z     7   �     �  
    9   9     }  
 c     q     C   0       
    E   E     �  
 4     p     M   Z       
    O   O     �           #        V   �              std::_Default_allocate_traits .?AU_Default_allocate_traits@std@@ � 	  �               B   �              std::_Num_int_base .?AU_Num_int_base@std@@ 篁�:   �              std::_Num_base .?AU_Num_base@std@@ 篁馢   ��denorm_indeterminate    denorm_absent   denorm_present 篁馞   t   �  std::float_denorm_style .?AW4float_denorm_style@std@@ 蝰
 �   蝰�   ��round_indeterminate �   round_toward_zero   round_to_nearest �  round_toward_infinity   round_toward_neg_infinity B   t   �  std::float_round_style .?AW4float_round_style@std@@ 
 �   蝰� �  has_denorm �   has_denorm_loss    has_infinity 篁�   has_quiet_NaN 蝰   has_signaling_NaN 蝰   is_bounded �   is_exact 篁�   is_iec559 蝰   is_integer �   is_modulo 蝰   is_signed 蝰   is_specialized �   tinyness_before    traps 蝰 �  round_style     digits �    digits10 篁�    max_digits10 篁�    max_exponent 篁�    max_exponent10 �    min_exponent 篁�    min_exponent10 �    radix 蝰:   �           std::_Num_base .?AU_Num_base@std@@ 篁駌   �    蝰   is_bounded �   is_exact 篁�   is_integer �   is_specialized �    radix 蝰B   �           std::_Num_int_base .?AU_Num_int_base@std@@ 篁窬   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰    digits 馧   �           std::numeric_limits<bool> .?AV?$numeric_limits@_N@std@@ 蝰�   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_signed 蝰   is_modulo 蝰    digits �    digits10 篁馧   �           std::numeric_limits<char> .?AV?$numeric_limits@D@std@@ 篁矜   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_signed 蝰    digits �    digits10 篁馬 
  �           std::numeric_limits<signed char> .?AV?$numeric_limits@C@std@@ �   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馰 
  �           std::numeric_limits<unsigned char> .?AV?$numeric_limits@E@std@@ 蝰�   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馬 
  �           std::numeric_limits<char16_t> .?AV?$numeric_limits@_S@std@@ 蝰�   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馬 
  �           std::numeric_limits<char32_t> .?AV?$numeric_limits@_U@std@@ 蝰�   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馬 
  �           std::numeric_limits<wchar_t> .?AV?$numeric_limits@_W@std@@ 篁矜   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_signed 蝰    digits �    digits10 篁馧 
  �           std::numeric_limits<short> .?AV?$numeric_limits@F@std@@ 蝰�   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_signed 蝰    digits �    digits10 篁馢 
  �           std::numeric_limits<int> .?AV?$numeric_limits@H@std@@ �   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_signed 蝰    digits �    digits10 篁馧 
  �           std::numeric_limits<long> .?AV?$numeric_limits@J@std@@ 篁矜   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_signed 蝰    digits �    digits10 篁馬 
  �           std::numeric_limits<__int64> .?AV?$numeric_limits@_J@std@@ 篁矜   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馰 
  �           std::numeric_limits<unsigned short> .?AV?$numeric_limits@G@std@@ 矜   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馰 
  �           std::numeric_limits<unsigned int> .?AV?$numeric_limits@I@std@@ 篁矜   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馰 
  �           std::numeric_limits<unsigned long> .?AV?$numeric_limits@K@std@@ 蝰�   �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰   is_modulo 蝰    digits �    digits10 篁馴 
  �           std::numeric_limits<unsigned __int64> .?AV?$numeric_limits@_K@std@@ 蝰F   �              std::_Num_float_base .?AU_Num_float_base@std@@ 篁矜   �    蝰 �  has_denorm �   has_infinity 篁�   has_quiet_NaN 蝰   has_signaling_NaN 蝰   is_bounded �   is_iec559 蝰   is_signed 蝰   is_specialized � �  round_style     radix 蝰F   �           std::_Num_float_base .?AU_Num_float_base@std@@ 篁馢  �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰    digits �    digits10 篁�    max_digits10 篁�    max_exponent 篁�    max_exponent10 �    min_exponent 篁�    min_exponent10 馧   �           std::numeric_limits<float> .?AV?$numeric_limits@M@std@@ 蝰J  �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰    digits �    digits10 篁�    max_digits10 篁�    max_exponent 篁�    max_exponent10 �    min_exponent 篁�    min_exponent10 馧   �           std::numeric_limits<double> .?AV?$numeric_limits@N@std@@ 馢  �    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰    digits �    digits10 篁�    max_digits10 篁�    max_exponent 篁�    max_exponent10 �    min_exponent 篁�    min_exponent10 馬   �           std::numeric_limits<long double> .?AV?$numeric_limits@O@std@@   �  _Allocate 蝰V   �           std::_Default_allocate_traits .?AU_Default_allocate_traits@std@@ � �  #      � B  #      � 	�  �                 	�  �                    �  0    	0   �         �       	:  �         �          �  �   	   �         �       	u   �         �          �  �   	0   �         �       	    �         �       	�  �         �       	�  �         �       	�  �         �       	    �         �       	0   �         �          �  u   u   �   	   �         �          �  �  �   	   �         �      &    u   u   u   �  u   u   �  �   	   �         �          �  �  �  u    	   �         �              #  #  #   	   �         �      &    u   �  u   u   �  �  �  �   	   �         �        �  FORMAT_DETAIL 蝰 �  s_FormatDetail � %  s_NumFormats 篁� �  s_FormatNames 蝰 �  GetNumFormats 蝰 �  GetFormatTable � �  GetHighestDefinedFeatureLevel 蝰 �  GetFormat 蝰 �  FormatExists 篁� �  FormatExistsInHeader 篁� �  GetByteAlignment 篁� �  IsBlockCompressFormat 蝰 �  GetName  �  IsSRGBFormat 篁� �  GetBitsPerStencil 蝰 �  GetBitsPerDepth  �  GetFormatReturnTypes 篁� �  GetNumComponentsInFormat 篁� �  GetMinNumComponentsInFormats 篁� �  Sequential2AbsoluteComponentIndex 蝰 �  CanBeCastEvenFullyTyped  �  GetAddressingBitsPerAlignedSize  �  GetParentFormat  �  GetFormatCastSet 篁� �  GetLayout 蝰 �  GetTypeLevel 篁� �  GetBitsPerUnit � �  GetBitsPerUnitThrow  �  GetBitsPerElement 蝰 �  GetWidthAlignment 蝰 �  GetHeightAlignment � �  GetDepthAlignment 蝰 �  Planar � �  NonOpaquePlanar  �  YUV  �  Opaque � �  FamilySupportsStencil 蝰 �  NonOpaquePlaneCount  �  DX9VertexOrIndexFormat � �  DX9TextureFormat 篁� �  FloatNormTextureFormat � �  DepthOnlyFormat  �  GetPlaneCount 蝰 �  MotionEstimatorAllowedInputFormat 蝰 �  SupportsSamplerFeedback  �  DecodeHistogramAllowedForOutputFormatSupport 篁� �  GetPlaneSliceFromViewFormat  �  FloatAndNotFloatFormats  �  SNORMAndUNORMFormats 篁� �  ValidCastToR32UAV 蝰 �  IsSupportedTextureDisplayableFormat  �  GetFormatComponentInterpretation 篁� �  GetBitsPerComponent  �  GetComponentName 篁� �  CalculateExtraPlanarRows 篁� �  CalculateMinimumRowMajorRowPitch 篁� �  CalculateMinimumRowMajorSlicePitch � �  GetYCbCrChromaSubsampling 蝰 �  CalculateResourceSize 蝰 �  GetTileShape 篁� �  Get4KTileShape � �  GetMipDimensions 篁� �  GetPlaneSubsampledSizeAndFormatForCopyableLayout 篁� �  GetDetailTableIndex  �  GetDetailTableIndexNoThrow � �  GetDetailTableIndexThrow 篁� �  SupportsDepth 蝰 �  SupportsStencil 	 �  GetFormatDetail b E �           D3D12_PROPERTY_LAYOUT_FORMAT_TABLE .?AUD3D12_PROPERTY_LAYOUT_FORMAT_TABLE@@ 蝰J   �              std::fpos<_Mbstatet> .?AV?$fpos@U_Mbstatet@@@std@@ 篁�    ?  '  B  '   	p  I         �          :  :  #    	t   I         �       	#   I         �          :  #   �   	:  I         �       	   I                   ?  #   9   	p  I         �          �     �      9  9   	0   I         �       	p   I                	t   I         �                 	0   I         �       	t   I                	t   I                J  p   char_type 蝰  t   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type � K  copy 篁� �  _Copy_s  K  move 篁� �  compare  �  length � �  find 篁� �  assign � �  eq � �  lt � �  to_char_type 篁� �  to_int_type  �  eq_int_type  �  not_eof  �  eof N  �           std::_Char_traits<char,int> .?AU?$_Char_traits@DH@std@@ 蝰    B  B  '   	t            �       	#                      B  '  �   	:           �       	p           �               �   	0            �       	p                   	t            �       	0            �       	t                   	t                   F  I    蝰  I  _Primary_char_traits 篁�  p   char_type 蝰  t   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type � �  compare  �  length � �  find 篁� �  assign � �  eq � �  lt �    to_char_type 篁�   to_int_type    eq_int_type    not_eof    eof Z             std::_Narrow_char_traits<char,int> .?AU?$_Narrow_char_traits@DH@std@@     4  '  3  '   	q  ?                   -  -  #    	t   ?         	       	#   ?         �          -  #   k   	-  ?                	   ?                   4  #   ,   	q  ?                              ,  ,   	0   ?               
       	q   ?                	!   ?         �               	0   ?                	!   ?                	!   ?                J  q   char_type 蝰  !   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type � A  copy 篁�   _Copy_s  A  move 篁� 
  compare    length � 
  find 篁�   assign �   eq �   lt �   to_char_type 篁�   to_int_type    eq_int_type    not_eof    eof ^             std::_Char_traits<wchar_t,unsigned short> .?AU?$_Char_traits@_WG@std@@ 篁�    3  3  '   	t                   	#            �          -  '  k   	-                   	q                     	     "   	0                   	q                   	!            �       	0                   	!                   	!                   F  ?    蝰  ?  _Primary_char_traits 篁�  q   char_type 蝰  !   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type �   compare    length � !  find 篁� #  assign � $  eq � $  lt � %  to_char_type 篁� &  to_int_type  '  eq_int_type  (  not_eof  )  eof N  *           std::_WChar_traits<wchar_t> .?AU?$_WChar_traits@_W@std@@ �    +  '  	  '   	{  �         ,          �  �  #    	t   �         .      
    �   	#   �         0          �  #   �   	�  �         2          +  #   �   	{  �         4          �     5      �  �   	0   �         7      
    %   	{   �         9       	u   �         /          %  %   	0   �         <       	u   �         9       	u   �                J  {   char_type 蝰  u   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type � )  copy 篁� -  _Copy_s  )  move 篁� /  compare  1  length � 3  find 篁� 6  assign � 8  eq � 8  lt � :  to_char_type 篁� ;  to_int_type  =  eq_int_type  >  not_eof  ?  eof Z  @           std::_Char_traits<char32_t,unsigned int> .?AU?$_Char_traits@_UI@std@@     c  '  A  '   	z  3         B          /  /  #    	t   3         D      
    /   	#   3         F          /  #   1   	/  3         H       	   3                    c  #   .   	z  3         K          J     L      .  .   	0   3         N       	z   3                	!   3         g       	0   3                	!   3                	!   3                J  z   char_type 蝰  !   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type � 5  copy 篁� C  _Copy_s  5  move 篁� E  compare  G  length � I  find 篁� M  assign � O  eq � O  lt � P  to_char_type 篁� Q  to_int_type  R  eq_int_type  S  not_eof  T  eof ^  U           std::_Char_traits<char16_t,unsigned short> .?AU?$_Char_traits@_SG@std@@ 蝰    A  A  '   	t   �         W       	#   �         F          /  '  1   	/  �         Z       	z  �         K               \   	0   �         N       	z   �                	!   �         g       	0   �                	!   �                	!   �                F  3    蝰  3  _Primary_char_traits 篁�  z   char_type 蝰  !   int_type 篁�  �  pos_type 篁�     off_type 篁�  �  state_type � X  compare  Y  length � [  find 篁� ]  assign � ^  eq � ^  lt � _  to_char_type 篁� `  to_int_type  a  eq_int_type  b  not_eof  c  eof N  d           std::_WChar_traits<char16_t> .?AU?$_WChar_traits@_S@std@@ Z   �              std::integral_constant<bool,0> .?AU?$integral_constant@_N$0A@@std@@ 蝰    *  '  "   	{  X         g          *  '   	{  X         i          h     j   	   X         ,       	�  X        %      �  �  allocator_type �  {   value_type �  {  pointer   �  const_pointer 蝰    void_pointer 篁�  "  const_void_pointer �  #   size_type 蝰     difference_type   f  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  f  propagate_on_container_swap     is_always_equal  k  allocate 篁� l  deallocate � Y  max_size 篁� m  select_on_container_copy_construction 蝰�  n           std::_Default_allocator_traits<std::allocator<char32_t> > .?AU?$_Default_allocator_traits@V?$allocator@_U@std@@@std@@     b  '  "   	z  a         p          b  '   	z  a         r          q     s   	   a         d       	*  a        ]      �  *  allocator_type �  z   value_type �  z  pointer   /  const_pointer 蝰    void_pointer 篁�  "  const_void_pointer �  #   size_type 蝰     difference_type   f  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  f  propagate_on_container_swap     is_always_equal  t  allocate 篁� u  deallocate � b  max_size 篁� v  select_on_container_copy_construction 蝰�  w           std::_Default_allocator_traits<std::allocator<char16_t> > .?AU?$_Default_allocator_traits@V?$allocator@_S@std@@@std@@     �  '  "   	q  h         y          �  '   	q  h         {          z     |   	   h         �       	f  h        �      �  f  allocator_type �  q   value_type �  q  pointer   -  const_pointer 蝰    void_pointer 篁�  "  const_void_pointer �  #   size_type 蝰     difference_type   f  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  f  propagate_on_container_swap     is_always_equal  }  allocate 篁� ~  deallocate � i  max_size 篁�   select_on_container_copy_construction 蝰�  �           std::_Default_allocator_traits<std::allocator<wchar_t> > .?AU?$_Default_allocator_traits@V?$allocator@_W@std@@@std@@ �    �  '  "   	p  k         �          �  '   	p  k         �          �     �   	   k         �       	�  k        �      �  �  allocator_type �  p   value_type �  p  pointer   :  const_pointer 蝰    void_pointer 篁�  "  const_void_pointer �  #   size_type 蝰     difference_type   f  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  f  propagate_on_container_swap     is_always_equal  �  allocate 篁� �  deallocate � l  max_size 篁� �  select_on_container_copy_construction 蝰�  �           std::_Default_allocator_traits<std::allocator<char> > .?AU?$_Default_allocator_traits@V?$allocator@D@std@@@std@@ � �  #     �6   D3D_SIT_CBUFFER 蝰  D3D_SIT_TBUFFER 蝰  D3D_SIT_TEXTURE 蝰  D3D_SIT_SAMPLER 蝰  D3D_SIT_UAV_RWTYPED 蝰  D3D_SIT_STRUCTURED 篁�  D3D_SIT_UAV_RWSTRUCTURED �  D3D_SIT_BYTEADDRESS 蝰  D3D_SIT_UAV_RWBYTEADDRESS  	 D3D_SIT_UAV_APPEND_STRUCTURED  
 D3D_SIT_UAV_CONSUME_STRUCTURED 篁�  D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER   D3D_SIT_RTACCELERATIONSTRUCTURE 蝰 
 D3D_SIT_UAV_FEEDBACKTEXTURE 蝰   D3D10_SIT_CBUFFER   D3D10_SIT_TBUFFER   D3D10_SIT_TEXTURE   D3D10_SIT_SAMPLER   D3D11_SIT_UAV_RWTYPED   D3D11_SIT_STRUCTURED �  D3D11_SIT_UAV_RWSTRUCTURED 篁�  D3D11_SIT_BYTEADDRESS   D3D11_SIT_UAV_RWBYTEADDRESS 蝰 	 D3D11_SIT_UAV_APPEND_STRUCTURED 蝰 
 D3D11_SIT_UAV_CONSUME_STRUCTURED �  D3D11_SIT_UAV_RWSTRUCTURED_WITH_COUNTER 蝰F   t   �  _D3D_SHADER_INPUT_TYPE .?AW4_D3D_SHADER_INPUT_TYPE@@ 篁疋   D3D12_LOGIC_OP_CLEAR �  D3D12_LOGIC_OP_SET 篁�  D3D12_LOGIC_OP_COPY 蝰  D3D12_LOGIC_OP_COPY_INVERTED �  D3D12_LOGIC_OP_NOOP 蝰  D3D12_LOGIC_OP_INVERT   D3D12_LOGIC_OP_AND 篁�  D3D12_LOGIC_OP_NAND 蝰  D3D12_LOGIC_OP_OR  	 D3D12_LOGIC_OP_NOR 篁� 
 D3D12_LOGIC_OP_XOR 篁�  D3D12_LOGIC_OP_EQUIV �  D3D12_LOGIC_OP_AND_REVERSE 篁� 
 D3D12_LOGIC_OP_AND_INVERTED 蝰  D3D12_LOGIC_OP_OR_REVERSE   D3D12_LOGIC_OP_OR_INVERTED 篁�6   t   �  D3D12_LOGIC_OP .?AW4D3D12_LOGIC_OP@@ 篁�  BINDSTRING_HEADERS 篁�  BINDSTRING_ACCEPT_MIMES 蝰  BINDSTRING_EXTRA_URL �  BINDSTRING_LANGUAGE 蝰  BINDSTRING_USERNAME 蝰  BINDSTRING_PASSWORD 蝰  BINDSTRING_UA_PIXELS �  BINDSTRING_UA_COLOR 蝰 	 BINDSTRING_OS  
 BINDSTRING_USER_AGENT   BINDSTRING_ACCEPT_ENCODINGS 蝰  BINDSTRING_POST_COOKIE 篁� 
 BINDSTRING_POST_DATA_MIME   BINDSTRING_URL 篁�  BINDSTRING_IID 篁�  BINDSTRING_FLAG_BIND_TO_OBJECT 篁�  BINDSTRING_PTR_BIND_CONTEXT 蝰  BINDSTRING_XDR_ORIGIN   BINDSTRING_DOWNLOADPATH 蝰  BINDSTRING_ROOTDOC_URL 篁�  BINDSTRING_INITIAL_FILENAME 蝰  BINDSTRING_PROXY_USERNAME   BINDSTRING_PROXY_PASSWORD   BINDSTRING_ENTERPRISE_ID �  BINDSTRING_DOC_URL 篁�  BINDSTRING_SAMESITE_COOKIE_LEVEL �2   t   �  tagBINDSTRING .?AW4tagBINDSTRING@@ �* 
 "     cElems 篁�
    pElems 篁�*   �           tagCAL .?AUtagCAL@@ 蝰R   �              _s__RTTIBaseClassDescriptor .?AU_s__RTTIBaseClassDescriptor@@ 
 �   蝰:   �              _TypeDescriptor .?AU_TypeDescriptor@@ 
 �    &   �              _PMD .?AU_PMD@@ 蝰^   �              _s__RTTIClassHierarchyDescriptor .?AU_s__RTTIClassHierarchyDescriptor@@ 蝰
 �   蝰
 �    ~ 
 �    pTypeDescriptor 蝰
 "    numContainedBases 
 �   where 
 "    attributes 篁�
 �   pClassDescriptor 馬   �          $ _s__RTTIBaseClassDescriptor .?AU_s__RTTIBaseClassDescriptor@@ 2  ��D3D12_BARRIER_LAYOUT_UNDEFINED 蝰   D3D12_BARRIER_LAYOUT_COMMON 蝰   D3D12_BARRIER_LAYOUT_PRESENT �  D3D12_BARRIER_LAYOUT_GENERIC_READ   D3D12_BARRIER_LAYOUT_RENDER_TARGET 篁�  D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE �  D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ 蝰  D3D12_BARRIER_LAYOUT_SHADER_RESOURCE �  D3D12_BARRIER_LAYOUT_COPY_SOURCE �  D3D12_BARRIER_LAYOUT_COPY_DEST 篁� 	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE 蝰 
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE �  D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ 篁� 
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE 蝰  D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ 蝰  D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE �  D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ 篁�  D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE 蝰  D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON �  D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ 篁�  D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS 篁�  D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST 蝰  D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ 蝰  D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS 蝰  D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 篁�  D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 篁�  D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST �  D3D12_BARRIER_LAYOUT_VIDEO_QUEUE_COMMON 蝰B !  t   �  D3D12_BARRIER_LAYOUT .?AW4D3D12_BARRIER_LAYOUT@@ 篁�   PIDMSI_STATUS_NORMAL �  PIDMSI_STATUS_NEW   PIDMSI_STATUS_PRELIM �  PIDMSI_STATUS_DRAFT 蝰  PIDMSI_STATUS_INPROGRESS �  PIDMSI_STATUS_EDIT 篁�  PIDMSI_STATUS_REVIEW �  PIDMSI_STATUS_PROOF 蝰  PIDMSI_STATUS_FINAL 蝰 �PIDMSI_STATUS_OTHER 蝰> 
  t   �  PIDMSI_STATUS_VALUE .?AW4PIDMSI_STATUS_VALUE@@ 癃
  BINDSTATUS_FINDINGRESOURCE 篁�  BINDSTATUS_CONNECTING   BINDSTATUS_REDIRECTING 篁�  BINDSTATUS_BEGINDOWNLOADDATA �  BINDSTATUS_DOWNLOADINGDATA 篁�  BINDSTATUS_ENDDOWNLOADDATA 篁�  BINDSTATUS_BEGINDOWNLOADCOMPONENTS 篁�  BINDSTATUS_INSTALLINGCOMPONENTS 蝰 	 BINDSTATUS_ENDDOWNLOADCOMPONENTS � 
 BINDSTATUS_USINGCACHEDCOPY 篁�  BINDSTATUS_SENDINGREQUEST   BINDSTATUS_CLASSIDAVAILABLE 蝰 
 BINDSTATUS_MIMETYPEAVAILABLE �  BINDSTATUS_CACHEFILENAMEAVAILABLE   BINDSTATUS_BEGINSYNCOPERATION   BINDSTATUS_ENDSYNCOPERATION 蝰  BINDSTATUS_BEGINUPLOADDATA 篁�  BINDSTATUS_UPLOADINGDATA �  BINDSTATUS_ENDUPLOADDATA �  BINDSTATUS_PROTOCOLCLASSID 篁�  BINDSTATUS_ENCODING 蝰  BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE �  BINDSTATUS_CLASSINSTALLLOCATION 蝰  BINDSTATUS_DECODING 蝰  BINDSTATUS_LOADINGMIMEHANDLER   BINDSTATUS_CONTENTDISPOSITIONATTACH 蝰  BINDSTATUS_FILTERREPORTMIMETYPE 蝰  BINDSTATUS_CLSIDCANINSTANTIATE 篁�  BINDSTATUS_IUNKNOWNAVAILABLE �  BINDSTATUS_DIRECTBIND   BINDSTATUS_RAWMIMETYPE 篁�   BINDSTATUS_PROXYDETECTING  ! BINDSTATUS_ACCEPTRANGES 蝰 " BINDSTATUS_COOKIE_SENT 篁� # BINDSTATUS_COMPACT_POLICY_RECEIVED 篁� $ BINDSTATUS_COOKIE_SUPPRESSED � % BINDSTATUS_COOKIE_STATE_UNKNOWN 蝰 & BINDSTATUS_COOKIE_STATE_ACCEPT 篁� ' BINDSTATUS_COOKIE_STATE_REJECT 篁� ( BINDSTATUS_COOKIE_STATE_PROMPT 篁� ) BINDSTATUS_COOKIE_STATE_LEASH  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  + BINDSTATUS_POLICY_HREF 篁� , BINDSTATUS_P3P_HEADER  - BINDSTATUS_SESSION_COOKIE_RECEIVED 篁� . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED  / BINDSTATUS_SESSION_COOKIES_ALLOWED 篁� 0 BINDSTATUS_CACHECONTROL 蝰 1 BINDSTATUS_CONTENTDISPOSITIONFILENAME  2 BINDSTATUS_MIMETEXTPLAINMISMATCH � 3 BINDSTATUS_PUBLISHERAVAILABLE  4 BINDSTATUS_DISPLAYNAMEAVAILABLE 蝰 5 BINDSTATUS_SSLUX_NAVBLOCKED 蝰 6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE 蝰 7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE 蝰 8 BINDSTATUS_64BIT_PROGRESS  8 BINDSTATUS_LAST 蝰 9 BINDSTATUS_RESERVED_0  : BINDSTATUS_RESERVED_1  ; BINDSTATUS_RESERVED_2  < BINDSTATUS_RESERVED_3  = BINDSTATUS_RESERVED_4  > BINDSTATUS_RESERVED_5  ? BINDSTATUS_RESERVED_6  @ BINDSTATUS_RESERVED_7  A BINDSTATUS_RESERVED_8  B BINDSTATUS_RESERVED_9  C BINDSTATUS_RESERVED_A  D BINDSTATUS_RESERVED_B  E BINDSTATUS_RESERVED_C  F BINDSTATUS_RESERVED_D  G BINDSTATUS_RESERVED_E  H BINDSTATUS_RESERVED_F  I BINDSTATUS_RESERVED_10 篁� J BINDSTATUS_RESERVED_11 篁� K BINDSTATUS_RESERVED_12 篁� L BINDSTATUS_RESERVED_13 篁� M BINDSTATUS_RESERVED_14 篁� M BINDSTATUS_LAST_PRIVATE 蝰2 O  t   �  tagBINDSTATUS .?AW4tagBINDSTATUS@@ �   #     癞  D3D_RETURN_TYPE_UNORM   D3D_RETURN_TYPE_SNORM   D3D_RETURN_TYPE_SINT �  D3D_RETURN_TYPE_UINT �  D3D_RETURN_TYPE_FLOAT   D3D_RETURN_TYPE_MIXED   D3D_RETURN_TYPE_DOUBLE 篁�  D3D_RETURN_TYPE_CONTINUED   D3D10_RETURN_TYPE_UNORM 蝰  D3D10_RETURN_TYPE_SNORM 蝰  D3D10_RETURN_TYPE_SINT 篁�  D3D10_RETURN_TYPE_UINT 篁�  D3D10_RETURN_TYPE_FLOAT 蝰  D3D10_RETURN_TYPE_MIXED 蝰  D3D11_RETURN_TYPE_UNORM 蝰  D3D11_RETURN_TYPE_SNORM 蝰  D3D11_RETURN_TYPE_SINT 篁�  D3D11_RETURN_TYPE_UINT 篁�  D3D11_RETURN_TYPE_FLOAT 蝰  D3D11_RETURN_TYPE_MIXED 蝰  D3D11_RETURN_TYPE_DOUBLE �  D3D11_RETURN_TYPE_CONTINUED 蝰J   t   �  D3D_RESOURCE_RETURN_TYPE .?AW4D3D_RESOURCE_RETURN_TYPE@@ 篁駫
   D3D_PRIMITIVE_UNDEFINED 蝰  D3D_PRIMITIVE_POINT 蝰  D3D_PRIMITIVE_LINE 篁�  D3D_PRIMITIVE_TRIANGLE 篁�  D3D_PRIMITIVE_LINE_ADJ 篁�  D3D_PRIMITIVE_TRIANGLE_ADJ 篁�  D3D_PRIMITIVE_1_CONTROL_POINT_PATCH 蝰 	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH 蝰 
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH 蝰  D3D_PRIMITIVE_4_CONTROL_POINT_PATCH 蝰  D3D_PRIMITIVE_5_CONTROL_POINT_PATCH 蝰 
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH 蝰  D3D_PRIMITIVE_7_CONTROL_POINT_PATCH 蝰  D3D_PRIMITIVE_8_CONTROL_POINT_PATCH 蝰  D3D_PRIMITIVE_9_CONTROL_POINT_PATCH 蝰  D3D_PRIMITIVE_10_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_11_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_12_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_13_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_14_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_15_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_16_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_17_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_18_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_19_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_20_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_21_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_22_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_23_CONTROL_POINT_PATCH �  D3D_PRIMITIVE_24_CONTROL_POINT_PATCH �   D3D_PRIMITIVE_25_CONTROL_POINT_PATCH � ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH � " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH � # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH � $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH � % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH � & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH � ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH �   D3D10_PRIMITIVE_UNDEFINED   D3D10_PRIMITIVE_POINT   D3D10_PRIMITIVE_LINE �  D3D10_PRIMITIVE_TRIANGLE �  D3D10_PRIMITIVE_LINE_ADJ �  D3D10_PRIMITIVE_TRIANGLE_ADJ �   D3D11_PRIMITIVE_UNDEFINED   D3D11_PRIMITIVE_POINT   D3D11_PRIMITIVE_LINE �  D3D11_PRIMITIVE_TRIANGLE �  D3D11_PRIMITIVE_LINE_ADJ �  D3D11_PRIMITIVE_TRIANGLE_ADJ �  D3D11_PRIMITIVE_1_CONTROL_POINT_PATCH  	 D3D11_PRIMITIVE_2_CONTROL_POINT_PATCH  
 D3D11_PRIMITIVE_3_CONTROL_POINT_PATCH   D3D11_PRIMITIVE_4_CONTROL_POINT_PATCH   D3D11_PRIMITIVE_5_CONTROL_POINT_PATCH  
 D3D11_PRIMITIVE_6_CONTROL_POINT_PATCH   D3D11_PRIMITIVE_7_CONTROL_POINT_PATCH   D3D11_PRIMITIVE_8_CONTROL_POINT_PATCH   D3D11_PRIMITIVE_9_CONTROL_POINT_PATCH   D3D11_PRIMITIVE_10_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_11_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_12_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_13_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_14_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_15_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_16_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_17_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_18_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_19_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_20_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_21_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_22_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_23_CONTROL_POINT_PATCH 篁�  D3D11_PRIMITIVE_24_CONTROL_POINT_PATCH 篁�   D3D11_PRIMITIVE_25_CONTROL_POINT_PATCH 篁� ! D3D11_PRIMITIVE_26_CONTROL_POINT_PATCH 篁� " D3D11_PRIMITIVE_27_CONTROL_POINT_PATCH 篁� # D3D11_PRIMITIVE_28_CONTROL_POINT_PATCH 篁� $ D3D11_PRIMITIVE_29_CONTROL_POINT_PATCH 篁� % D3D11_PRIMITIVE_30_CONTROL_POINT_PATCH 篁� & D3D11_PRIMITIVE_31_CONTROL_POINT_PATCH 篁� ' D3D11_PRIMITIVE_32_CONTROL_POINT_PATCH 篁�2 R  t   �  D3D_PRIMITIVE .?AW4D3D_PRIMITIVE@@ �:   NODE_INVALID �  NODE_ELEMENT �  NODE_ATTRIBUTE 篁�  NODE_TEXT   NODE_CDATA_SECTION 篁�  NODE_ENTITY_REFERENCE   NODE_ENTITY 蝰  NODE_PROCESSING_INSTRUCTION 蝰  NODE_COMMENT � 	 NODE_DOCUMENT  
 NODE_DOCUMENT_TYPE 篁�  NODE_DOCUMENT_FRAGMENT 篁�  NODE_NOTATION 6 
  t   �  tagDOMNodeType .?AW4tagDOMNodeType@@ 篁駫    DESCKIND_NONE   DESCKIND_FUNCDESC   DESCKIND_VARDESC �  DESCKIND_TYPECOMP   DESCKIND_IMPLICITAPPOBJ 蝰  DESCKIND_MAX �.   t   �  tagDESCKIND .?AW4tagDESCKIND@@ 駳   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD �  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE �  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_NO_ACCESS 篁�  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER 篁�  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_SRV 蝰  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_UAV 蝰b   t   �  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE .?AW4D3D12_RENDER_PASS_ENDING_ACCESS_TYPE@@ 篁衤    D3D12_MESSAGE_SEVERITY_CORRUPTION   D3D12_MESSAGE_SEVERITY_ERROR �  D3D12_MESSAGE_SEVERITY_WARNING 篁�  D3D12_MESSAGE_SEVERITY_INFO 蝰  D3D12_MESSAGE_SEVERITY_MESSAGE 篁馞   t   �  D3D12_MESSAGE_SEVERITY .?AW4D3D12_MESSAGE_SEVERITY@@ 篁裰  PARSE_CANONICALIZE 篁�  PARSE_FRIENDLY 篁�  PARSE_SECURITY_URL 篁�  PARSE_ROOTDOCUMENT 篁�  PARSE_DOCUMENT 篁�  PARSE_ANCHOR �  PARSE_ENCODE_IS_UNESCAPE �  PARSE_DECODE_IS_ESCAPE 篁� 	 PARSE_PATH_FROM_URL 蝰 
 PARSE_URL_FROM_PATH 蝰  PARSE_MIME 篁�  PARSE_SERVER � 
 PARSE_SCHEMA �  PARSE_SITE 篁�  PARSE_DOMAIN �  PARSE_LOCATION 篁�  PARSE_SECURITY_DOMAIN   PARSE_ESCAPE �  PARSE_UNESCAPE 篁�6   t   �  _tagPARSEACTION .?AW4_tagPARSEACTION@@ 褶   D3D_SVT_VOID �  D3D_SVT_BOOL �  D3D_SVT_INT 蝰  D3D_SVT_FLOAT   D3D_SVT_STRING 篁�  D3D_SVT_TEXTURE 蝰  D3D_SVT_TEXTURE1D   D3D_SVT_TEXTURE2D   D3D_SVT_TEXTURE3D  	 D3D_SVT_TEXTURECUBE 蝰 
 D3D_SVT_SAMPLER 蝰  D3D_SVT_SAMPLER1D   D3D_SVT_SAMPLER2D  
 D3D_SVT_SAMPLER3D   D3D_SVT_SAMPLERCUBE 蝰  D3D_SVT_PIXELSHADER 蝰  D3D_SVT_VERTEXSHADER �  D3D_SVT_PIXELFRAGMENT   D3D_SVT_VERTEXFRAGMENT 篁�  D3D_SVT_UINT �  D3D_SVT_UINT8   D3D_SVT_GEOMETRYSHADER 篁�  D3D_SVT_RASTERIZER 篁�  D3D_SVT_DEPTHSTENCIL �  D3D_SVT_BLEND   D3D_SVT_BUFFER 篁�  D3D_SVT_CBUFFER 蝰  D3D_SVT_TBUFFER 蝰  D3D_SVT_TEXTURE1DARRAY 篁�  D3D_SVT_TEXTURE2DARRAY 篁�  D3D_SVT_RENDERTARGETVIEW �  D3D_SVT_DEPTHSTENCILVIEW �   D3D_SVT_TEXTURE2DMS 蝰 ! D3D_SVT_TEXTURE2DMSARRAY � " D3D_SVT_TEXTURECUBEARRAY � # D3D_SVT_HULLSHADER 篁� $ D3D_SVT_DOMAINSHADER � % D3D_SVT_INTERFACE_POINTER  & D3D_SVT_COMPUTESHADER  ' D3D_SVT_DOUBLE 篁� ( D3D_SVT_RWTEXTURE1D 蝰 ) D3D_SVT_RWTEXTURE1DARRAY � * D3D_SVT_RWTEXTURE2D 蝰 + D3D_SVT_RWTEXTURE2DARRAY � , D3D_SVT_RWTEXTURE3D 蝰 - D3D_SVT_RWBUFFER � . D3D_SVT_BYTEADDRESS_BUFFER 篁� / D3D_SVT_RWBYTEADDRESS_BUFFER � 0 D3D_SVT_STRUCTURED_BUFFER  1 D3D_SVT_RWSTRUCTURED_BUFFER 蝰 2 D3D_SVT_APPEND_STRUCTURED_BUFFER � 3 D3D_SVT_CONSUME_STRUCTURED_BUFFER  4 D3D_SVT_MIN8FLOAT  5 D3D_SVT_MIN10FLOAT 篁� 6 D3D_SVT_MIN16FLOAT 篁� 7 D3D_SVT_MIN12INT � 8 D3D_SVT_MIN16INT � 9 D3D_SVT_MIN16UINT  : D3D_SVT_INT16  ; D3D_SVT_UINT16 篁� < D3D_SVT_FLOAT16 蝰 = D3D_SVT_INT64  > D3D_SVT_UINT64 篁�   D3D10_SVT_VOID 篁�  D3D10_SVT_BOOL 篁�  D3D10_SVT_INT   D3D10_SVT_FLOAT 蝰  D3D10_SVT_STRING �  D3D10_SVT_TEXTURE   D3D10_SVT_TEXTURE1D 蝰  D3D10_SVT_TEXTURE2D 蝰  D3D10_SVT_TEXTURE3D 蝰 	 D3D10_SVT_TEXTURECUBE  
 D3D10_SVT_SAMPLER   D3D10_SVT_SAMPLER1D 蝰  D3D10_SVT_SAMPLER2D 蝰 
 D3D10_SVT_SAMPLER3D 蝰  D3D10_SVT_SAMPLERCUBE   D3D10_SVT_PIXELSHADER   D3D10_SVT_VERTEXSHADER 篁�  D3D10_SVT_PIXELFRAGMENT 蝰  D3D10_SVT_VERTEXFRAGMENT �  D3D10_SVT_UINT 篁�  D3D10_SVT_UINT8 蝰  D3D10_SVT_GEOMETRYSHADER �  D3D10_SVT_RASTERIZER �  D3D10_SVT_DEPTHSTENCIL 篁�  D3D10_SVT_BLEND 蝰  D3D10_SVT_BUFFER �  D3D10_SVT_CBUFFER   D3D10_SVT_TBUFFER   D3D10_SVT_TEXTURE1DARRAY �  D3D10_SVT_TEXTURE2DARRAY �  D3D10_SVT_RENDERTARGETVIEW 篁�  D3D10_SVT_DEPTHSTENCILVIEW 篁�   D3D10_SVT_TEXTURE2DMS  ! D3D10_SVT_TEXTURE2DMSARRAY 篁� " D3D10_SVT_TEXTURECUBEARRAY 篁� # D3D11_SVT_HULLSHADER � $ D3D11_SVT_DOMAINSHADER 篁� % D3D11_SVT_INTERFACE_POINTER 蝰 & D3D11_SVT_COMPUTESHADER 蝰 ' D3D11_SVT_DOUBLE � ( D3D11_SVT_RWTEXTURE1D  ) D3D11_SVT_RWTEXTURE1DARRAY 篁� * D3D11_SVT_RWTEXTURE2D  + D3D11_SVT_RWTEXTURE2DARRAY 篁� , D3D11_SVT_RWTEXTURE3D  - D3D11_SVT_RWBUFFER 篁� . D3D11_SVT_BYTEADDRESS_BUFFER � / D3D11_SVT_RWBYTEADDRESS_BUFFER 篁� 0 D3D11_SVT_STRUCTURED_BUFFER 蝰 1 D3D11_SVT_RWSTRUCTURED_BUFFER  2 D3D11_SVT_APPEND_STRUCTURED_BUFFER 篁� 3 D3D11_SVT_CONSUME_STRUCTURED_BUFFER 蝰 ����D3D_SVT_FORCE_DWORD 蝰J t  t   �  _D3D_SHADER_VARIABLE_TYPE .?AW4_D3D_SHADER_VARIABLE_TYPE@@ �* 
 "     cElems 篁�
    pElems 篁�2   �           tagCACLSID .?AUtagCACLSID@@ 蝰2   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 蝰 
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT � 
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT �  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 蝰  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VIEW_INSTANCING 蝰  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_AS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2 篁�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER1 蝰  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER2 蝰  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MAX_VALID ^   t   �  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE .?AW4D3D12_PIPELINE_STATE_SUBOBJECT_TYPE@@ �* 
 "     cElems 篁�
 A   pElems 篁�.   �           tagCADATE .?AUtagCADATE@@ �    D3D_DRIVER_TYPE_UNKNOWN 蝰  D3D_DRIVER_TYPE_HARDWARE �  D3D_DRIVER_TYPE_REFERENCE   D3D_DRIVER_TYPE_NULL �  D3D_DRIVER_TYPE_SOFTWARE �  D3D_DRIVER_TYPE_WARP �6   t   �  D3D_DRIVER_TYPE .?AW4D3D_DRIVER_TYPE@@ 馬    VAR_PERINSTANCE 蝰  VAR_STATIC 篁�  VAR_CONST   VAR_DISPATCH �.   t   �  tagVARKIND .?AW4tagVARKIND@@ 篁駫  D3D_SIF_USERPACKED 篁�  D3D_SIF_COMPARISON_SAMPLER 篁�  D3D_SIF_TEXTURE_COMPONENT_0 蝰  D3D_SIF_TEXTURE_COMPONENT_1 蝰  D3D_SIF_TEXTURE_COMPONENTS 篁�  D3D_SIF_UNUSED 篁�  D3D10_SIF_USERPACKED �  D3D10_SIF_COMPARISON_SAMPLER �  D3D10_SIF_TEXTURE_COMPONENT_0   D3D10_SIF_TEXTURE_COMPONENT_1   D3D10_SIF_TEXTURE_COMPONENTS � ����D3D_SIF_FORCE_DWORD 蝰F   t   �  _D3D_SHADER_INPUT_FLAGS .?AW4_D3D_SHADER_INPUT_FLAGS@@ 耦    CHANGEKIND_ADDMEMBER �  CHANGEKIND_DELETEMEMBER 蝰  CHANGEKIND_SETNAMES 蝰  CHANGEKIND_SETDOCUMENTATION 蝰  CHANGEKIND_GENERAL 篁�  CHANGEKIND_INVALIDATE   CHANGEKIND_CHANGEFAILED 蝰  CHANGEKIND_MAX 篁�2   t   �  tagCHANGEKIND .?AW4tagCHANGEKIND@@ �
   D3D_REGISTER_COMPONENT_UNKNOWN 篁�  D3D_REGISTER_COMPONENT_UINT32   D3D_REGISTER_COMPONENT_SINT32   D3D_REGISTER_COMPONENT_FLOAT32 篁�  D3D_REGISTER_COMPONENT_UINT16   D3D_REGISTER_COMPONENT_SINT16   D3D_REGISTER_COMPONENT_FLOAT16 篁�  D3D_REGISTER_COMPONENT_UINT64   D3D_REGISTER_COMPONENT_SINT64  	 D3D_REGISTER_COMPONENT_FLOAT64 篁�   D3D10_REGISTER_COMPONENT_UNKNOWN �  D3D10_REGISTER_COMPONENT_UINT32 蝰  D3D10_REGISTER_COMPONENT_SINT32 蝰  D3D10_REGISTER_COMPONENT_FLOAT32 �  D3D10_REGISTER_COMPONENT_UINT16 蝰  D3D10_REGISTER_COMPONENT_SINT16 蝰  D3D10_REGISTER_COMPONENT_FLOAT16 �  D3D10_REGISTER_COMPONENT_UINT64 蝰  D3D10_REGISTER_COMPONENT_SINT64 蝰 	 D3D10_REGISTER_COMPONENT_FLOAT64 馧   t   �  D3D_REGISTER_COMPONENT_TYPE .?AW4D3D_REGISTER_COMPONENT_TYPE@@ 穸    XMLELEMTYPE_ELEMENT 蝰  XMLELEMTYPE_TEXT �  XMLELEMTYPE_COMMENT 蝰  XMLELEMTYPE_DOCUMENT �  XMLELEMTYPE_DTD 蝰  XMLELEMTYPE_PI 篁�  XMLELEMTYPE_OTHER 6   t   �  tagXMLEMEM_TYPE .?AW4tagXMLEMEM_TYPE@@ �:   D3D_NAME_UNDEFINED 篁�  D3D_NAME_POSITION   D3D_NAME_CLIP_DISTANCE 篁�  D3D_NAME_CULL_DISTANCE 篁�  D3D_NAME_RENDER_TARGET_ARRAY_INDEX 篁�  D3D_NAME_VIEWPORT_ARRAY_INDEX   D3D_NAME_VERTEX_ID 篁�  D3D_NAME_PRIMITIVE_ID   D3D_NAME_INSTANCE_ID � 	 D3D_NAME_IS_FRONT_FACE 篁� 
 D3D_NAME_SAMPLE_INDEX   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR 蝰  D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR 篁�  D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR �  D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR 篁�  D3D_NAME_BARYCENTRICS   D3D_NAME_SHADINGRATE �  D3D_NAME_CULLPRIMITIVE 篁� @ D3D_NAME_TARGET 蝰 A D3D_NAME_DEPTH 篁� B D3D_NAME_COVERAGE  C D3D_NAME_DEPTH_GREATER_EQUAL � D D3D_NAME_DEPTH_LESS_EQUAL  E D3D_NAME_STENCIL_REF � F D3D_NAME_INNER_COVERAGE 蝰   D3D10_NAME_UNDEFINED �  D3D10_NAME_POSITION 蝰  D3D10_NAME_CLIP_DISTANCE �  D3D10_NAME_CULL_DISTANCE �  D3D10_NAME_RENDER_TARGET_ARRAY_INDEX �  D3D10_NAME_VIEWPORT_ARRAY_INDEX 蝰  D3D10_NAME_VERTEX_ID �  D3D10_NAME_PRIMITIVE_ID 蝰  D3D10_NAME_INSTANCE_ID 篁� 	 D3D10_NAME_IS_FRONT_FACE � 
 D3D10_NAME_SAMPLE_INDEX 蝰 @ D3D10_NAME_TARGET  A D3D10_NAME_DEPTH � B D3D10_NAME_COVERAGE 蝰  D3D11_NAME_FINAL_QUAD_EDGE_TESSFACTOR   D3D11_NAME_FINAL_QUAD_INSIDE_TESSFACTOR 蝰 
 D3D11_NAME_FINAL_TRI_EDGE_TESSFACTOR �  D3D11_NAME_FINAL_TRI_INSIDE_TESSFACTOR 篁�  D3D11_NAME_FINAL_LINE_DETAIL_TESSFACTOR 蝰  D3D11_NAME_FINAL_LINE_DENSITY_TESSFACTOR � C D3D11_NAME_DEPTH_GREATER_EQUAL 篁� D D3D11_NAME_DEPTH_LESS_EQUAL 蝰 E D3D11_NAME_STENCIL_REF 篁� F D3D11_NAME_INNER_COVERAGE   D3D12_NAME_BARYCENTRICS 蝰  D3D12_NAME_SHADINGRATE 篁�  D3D12_NAME_CULLPRIMITIVE �* 6  t   �  D3D_NAME .?AW4D3D_NAME@@ 篁褚    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE 篁�  D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS   D3D12_ROOT_PARAMETER_TYPE_CBV   D3D12_ROOT_PARAMETER_TYPE_SRV   D3D12_ROOT_PARAMETER_TYPE_UAV J   t   �  D3D12_ROOT_PARAMETER_TYPE .?AW4D3D12_ROOT_PARAMETER_TYPE@@ �6   D3D_TESSELLATOR_DOMAIN_UNDEFINED �  D3D_TESSELLATOR_DOMAIN_ISOLINE 篁�  D3D_TESSELLATOR_DOMAIN_TRI 篁�  D3D_TESSELLATOR_DOMAIN_QUAD 蝰   D3D11_TESSELLATOR_DOMAIN_UNDEFINED 篁�  D3D11_TESSELLATOR_DOMAIN_ISOLINE �  D3D11_TESSELLATOR_DOMAIN_TRI �  D3D11_TESSELLATOR_DOMAIN_QUAD F   t   �  D3D_TESSELLATOR_DOMAIN .?AW4D3D_TESSELLATOR_DOMAIN@@ 篁馴   �              _s__RTTICompleteObjectLocator2 .?AU_s__RTTICompleteObjectLocator2@@ 蝰
 �   蝰
 �    � 
 "     signature 
 "    offset 篁�
 "    cdOffset �
 �   pTypeDescriptor 蝰
 �   pClassDescriptor �
 �   pSelf Z   �          $ _s__RTTICompleteObjectLocator2 .?AU_s__RTTICompleteObjectLocator2@@ 蝰Z   �              $_TypeDescriptor$_extraBytes_24 .?AU$_TypeDescriptor$_extraBytes_24@@  p   #     �6 
 "    pVFTable �
    spare 
 �   name 馴   �          ( $_TypeDescriptor$_extraBytes_24 .?AU$_TypeDescriptor$_extraBytes_24@@ n    FUNC_VIRTUAL �  FUNC_PUREVIRTUAL �  FUNC_NONVIRTUAL 蝰  FUNC_STATIC 蝰  FUNC_DISPATCH .   t   �  tagFUNCKIND .?AW4tagFUNCKIND@@ 褚   FEATURE_OBJECT_CACHING 篁�  FEATURE_ZONE_ELEVATION 篁�  FEATURE_MIME_HANDLING   FEATURE_MIME_SNIFFING   FEATURE_WINDOW_RESTRICTIONS 蝰  FEATURE_WEBOC_POPUPMANAGEMENT   FEATURE_BEHAVIORS   FEATURE_DISABLE_MK_PROTOCOL 蝰  FEATURE_LOCALMACHINE_LOCKDOWN  	 FEATURE_SECURITYBAND � 
 FEATURE_RESTRICT_ACTIVEXINSTALL 蝰  FEATURE_VALIDATE_NAVIGATE_URL   FEATURE_RESTRICT_FILEDOWNLOAD  
 FEATURE_ADDON_MANAGEMENT �  FEATURE_PROTOCOL_LOCKDOWN   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE 篁�  FEATURE_SAFE_BINDTOOBJECT   FEATURE_UNC_SAVEDFILECHECK 篁�  FEATURE_GET_URL_DOM_FILEPATH_UNENCODED 篁�  FEATURE_TABBED_BROWSING 蝰  FEATURE_SSLUX   FEATURE_DISABLE_NAVIGATION_SOUNDS   FEATURE_DISABLE_LEGACY_COMPRESSION 篁�  FEATURE_FORCE_ADDR_AND_STATUS   FEATURE_XMLHTTP 蝰  FEATURE_DISABLE_TELNET_PROTOCOL 蝰  FEATURE_FEEDS   FEATURE_BLOCK_INPUT_PROMPTS 蝰  FEATURE_ENTRY_COUNT 蝰F   t   �  _tagINTERNETFEATURELIST .?AW4_tagINTERNETFEATURELIST@@ 衿  QUERY_EXPIRATION_DATE   QUERY_TIME_OF_LAST_CHANGE   QUERY_CONTENT_ENCODING 篁�  QUERY_CONTENT_TYPE 篁�  QUERY_REFRESH   QUERY_RECOMBINE 蝰  QUERY_CAN_NAVIGATE 篁�  QUERY_USES_NETWORK 篁� 	 QUERY_IS_CACHED 蝰 
 QUERY_IS_INSTALLEDENTRY 蝰  QUERY_IS_CACHED_OR_MAPPED   QUERY_USES_CACHE � 
 QUERY_IS_SECURE 蝰  QUERY_IS_SAFE   QUERY_USES_HISTORYFOLDER �  QUERY_IS_CACHED_AND_USABLE_OFFLINE 篁�6   t   �  _tagQUERYOPTION .?AW4_tagQUERYOPTION@@ �"    COINITBASE_MULTITHREADED �2   t   �  tagCOINITBASE .?AW4tagCOINITBASE@@ 駄   �              $_s__RTTIBaseClassArray$_extraBytes_24 .?AU$_s__RTTIBaseClassArray$_extraBytes_24@@ 蝰
 �     �  #     �* 
 �    arrayOfBaseClassDescriptors 蝰j   �           $_s__RTTIBaseClassArray$_extraBytes_24 .?AU$_s__RTTIBaseClassArray$_extraBytes_24@@ 蝰v  CLSCTX_INPROC_SERVER �  CLSCTX_INPROC_HANDLER   CLSCTX_LOCAL_SERVER 蝰  CLSCTX_INPROC_SERVER16 篁�  CLSCTX_REMOTE_SERVER �   CLSCTX_INPROC_HANDLER16 蝰 @ CLSCTX_RESERVED1 � � CLSCTX_RESERVED2 �  CLSCTX_RESERVED3 �  CLSCTX_RESERVED4 �  CLSCTX_NO_CODE_DOWNLOAD 蝰  CLSCTX_RESERVED5 �  CLSCTX_NO_CUSTOM_MARSHAL �   CLSCTX_ENABLE_CODE_DOWNLOAD 蝰  @CLSCTX_NO_FAILURE_LOG  � �CLSCTX_DISABLE_AAA � �   CLSCTX_ENABLE_AAA  �   CLSCTX_FROM_DEFAULT_CONTEXT 蝰 �   CLSCTX_ACTIVATE_X86_SERVER 篁� �   CLSCTX_ACTIVATE_32_BIT_SERVER  �   CLSCTX_ACTIVATE_64_BIT_SERVER  �   CLSCTX_ENABLE_CLOAKING 篁� �  @ CLSCTX_APPCONTAINER 蝰 �  � CLSCTX_ACTIVATE_AAA_AS_IU  �   CLSCTX_RESERVED6 � �   CLSCTX_ACTIVATE_ARM32_SERVER � �   CLSCTX_ALLOW_LOWER_TRUST_REGISTRATION  �   �CLSCTX_PS_DLL *   t   �  tagCLSCTX .?AW4tagCLSCTX@@ 駫   VT_EMPTY �  VT_NULL 蝰  VT_I2   VT_I4   VT_R4   VT_R8   VT_CY   VT_DATE 蝰  VT_BSTR 蝰 	 VT_DISPATCH 蝰 
 VT_ERROR �  VT_BOOL 蝰  VT_VARIANT 篁� 
 VT_UNKNOWN 篁�  VT_DECIMAL 篁�  VT_I1   VT_UI1 篁�  VT_UI2 篁�  VT_UI4 篁�  VT_I8   VT_UI8 篁�  VT_INT 篁�  VT_UINT 蝰  VT_VOID 蝰  VT_HRESULT 篁�  VT_PTR 篁�  VT_SAFEARRAY �  VT_CARRAY   VT_USERDEFINED 篁�  VT_LPSTR �  VT_LPWSTR  $ VT_RECORD  % VT_INT_PTR 篁� & VT_UINT_PTR 蝰 @ VT_FILETIME 蝰 A VT_BLOB 蝰 B VT_STREAM  C VT_STORAGE 篁� D VT_STREAMED_OBJECT 篁� E VT_STORED_OBJECT � F VT_BLOB_OBJECT 篁� G VT_CF  H VT_CLSID � I VT_VERSIONED_STREAM 蝰 �VT_BSTR_BLOB �  VT_VECTOR    VT_ARRAY �  @VT_BYREF � � �VT_RESERVED  ���VT_ILLEGAL � �VT_ILLEGALMASKED � �VT_TYPEMASK 蝰& 4  t   �  VARENUM .?AW4VARENUM@@ 耦   D3D_PRIMITIVE_TOPOLOGY_UNDEFINED �  D3D_PRIMITIVE_TOPOLOGY_POINTLIST �  D3D_PRIMITIVE_TOPOLOGY_LINELIST 蝰  D3D_PRIMITIVE_TOPOLOGY_LINESTRIP �  D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST 蝰  D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP �  D3D_PRIMITIVE_TOPOLOGY_TRIANGLEFAN 篁� 
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ 蝰  D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ �  D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 蝰 
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ � ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST � " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST � # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST � $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST � % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST � & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST � ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST � ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST � ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST � * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST    D3D10_PRIMITIVE_TOPOLOGY_UNDEFINED 篁�  D3D10_PRIMITIVE_TOPOLOGY_POINTLIST 篁�  D3D10_PRIMITIVE_TOPOLOGY_LINELIST   D3D10_PRIMITIVE_TOPOLOGY_LINESTRIP 篁�  D3D10_PRIMITIVE_TOPOLOGY_TRIANGLELIST   D3D10_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP 篁� 
 D3D10_PRIMITIVE_TOPOLOGY_LINELIST_ADJ   D3D10_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 篁�  D3D10_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ  
 D3D10_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 篁�   D3D11_PRIMITIVE_TOPOLOGY_UNDEFINED 篁�  D3D11_PRIMITIVE_TOPOLOGY_POINTLIST 篁�  D3D11_PRIMITIVE_TOPOLOGY_LINELIST   D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP 篁�  D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST   D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP 篁� 
 D3D11_PRIMITIVE_TOPOLOGY_LINELIST_ADJ   D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 篁�  D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ  
 D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 篁� ! D3D11_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST 篁� " D3D11_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 篁� # D3D11_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 篁� $ D3D11_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 篁� % D3D11_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 篁� & D3D11_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST 篁� ' D3D11_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 篁� ( D3D11_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 篁� ) D3D11_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST 篁� * D3D11_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST 蝰 + D3D11_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST 蝰 , D3D11_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST 蝰 - D3D11_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST 蝰 . D3D11_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST 蝰 / D3D11_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST 蝰 0 D3D11_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST 蝰 1 D3D11_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST 蝰 2 D3D11_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST 蝰 3 D3D11_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST 蝰 4 D3D11_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST 蝰 5 D3D11_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST 蝰 6 D3D11_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST 蝰 7 D3D11_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST 蝰 8 D3D11_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST 蝰 9 D3D11_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST 蝰 : D3D11_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST 蝰 ; D3D11_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST 蝰 < D3D11_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST 蝰 = D3D11_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST 蝰 > D3D11_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST 蝰 ? D3D11_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST 蝰 @ D3D11_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST 蝰F _  t   �  D3D_PRIMITIVE_TOPOLOGY .?AW4D3D_PRIMITIVE_TOPOLOGY@@ 篁褶   D3D_SVC_SCALAR 篁�  D3D_SVC_VECTOR 篁�  D3D_SVC_MATRIX_ROWS 蝰  D3D_SVC_MATRIX_COLUMNS 篁�  D3D_SVC_OBJECT 篁�  D3D_SVC_STRUCT 篁�  D3D_SVC_INTERFACE_CLASS 蝰  D3D_SVC_INTERFACE_POINTER    D3D10_SVC_SCALAR �  D3D10_SVC_VECTOR �  D3D10_SVC_MATRIX_ROWS   D3D10_SVC_MATRIX_COLUMNS �  D3D10_SVC_OBJECT �  D3D10_SVC_STRUCT �  D3D11_SVC_INTERFACE_CLASS   D3D11_SVC_INTERFACE_POINTER 蝰 ����D3D_SVC_FORCE_DWORD 蝰N   t   �  _D3D_SHADER_VARIABLE_CLASS .?AW4_D3D_SHADER_VARIABLE_CLASS@@ 篁馴   �              $_TypeDescriptor$_extraBytes_20 .?AU$_TypeDescriptor$_extraBytes_20@@  p   #     �6 
 "    pVFTable �
    spare 
 �   name 馴   �          $ $_TypeDescriptor$_extraBytes_20 .?AU$_TypeDescriptor$_extraBytes_20@@  �  #     � �  #     �:   CIP_DISK_FULL   CIP_ACCESS_DENIED   CIP_NEWER_VERSION_EXISTS �  CIP_OLDER_VERSION_EXISTS �  CIP_NAME_CONFLICT   CIP_TRUST_VERIFICATION_COMPONENT_MISSING �  CIP_EXE_SELF_REGISTERATION_TIMEOUT 篁�  CIP_UNSAFE_TO_ABORT 蝰  CIP_NEED_REBOOT 蝰 	 CIP_NEED_REBOOT_UI_PERMISSION J 
  t   �  __MIDL_ICodeInstall_0001 .?AW4__MIDL_ICodeInstall_0001@@ 篁駳    COMGLB_EXCEPTION_HANDLE 蝰  COMGLB_EXCEPTION_DONOT_HANDLE_FATAL 蝰  COMGLB_EXCEPTION_DONOT_HANDLE   COMGLB_EXCEPTION_DONOT_HANDLE_ANY F   t   �  tagGLOBALOPT_EH_VALUES .?AW4tagGLOBALOPT_EH_VALUES@@ 篁褚    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 篁�  D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS �  D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY 篁�  D3D12_MEASUREMENTS_ACTION_DISCARD_PREVIOUS 篁馢   t   �  D3D12_MEASUREMENTS_ACTION .?AW4D3D12_MEASUREMENTS_ACTION@@ 褚    CC_FASTCALL 蝰  CC_CDECL �  CC_MSCPASCAL �  CC_PASCAL   CC_MACPASCAL �  CC_STDCALL 篁�  CC_FPFASTCALL   CC_SYSCALL 篁�  CC_MPWCDECL 蝰  CC_MPWPASCAL � 	 CC_MAX 篁�.   t   �  tagCALLCONV .?AW4tagCALLCONV@@ 駫    D3D_INCLUDE_LOCAL   D3D_INCLUDE_SYSTEM 篁�   D3D10_INCLUDE_LOCAL 蝰  D3D10_INCLUDE_SYSTEM � ����D3D_INCLUDE_FORCE_DWORD 蝰:   t   �  _D3D_INCLUDE_TYPE .?AW4_D3D_INCLUDE_TYPE@@ � 
  UP
 �        I  �  
     	     �     �       	"     �            
   �  
       	     �          
    蝰
   ,  
       	     �           	     �                        
   ,   	
    �            	
    �                    v 	  �   �      QueryInterface �       AddRef �       Release  	  IUnknown 篁� 
  operator= 蝰. 	 &      �   IUnknown .?AUIUnknown@@ 蝰
 I    	   I               	   I       �      
       	   I             
    0   	   I             
    "   	   I             .   �              ITypeInfo .?AUITypeInfo@@ 
     
     
       	   I                   -  F   	   I                   -  F  �   	   I                  "     -  F   	   I       "          "  0   	   I       $      
    J   	t   I       &       	  I                    �   	   I       )      
 I  �  
    +   	   I      ,      
 I   蝰
 .  ,  
    /   	   I      0       	   I                -    1    2  
 I  ,   	4  I       ,       	4  I       0         5    6  �      蝰      RecordInit �       RecordClear    (   RecordCopy �   0   GetGuid    8   GetName    @   GetSize    H   GetTypeInfo    P   GetField 篁� !  X   GetFieldNoCopy � #  `   PutField 篁� #  h   PutFieldNoCopy � %  p   GetFieldNames 蝰 '  x   IsMatchingType � (  �   RecordCreate 篁� *  �   RecordCreateCopy 篁�   �   RecordDestroy 蝰 3  IRecordInfo  7  operator= 蝰 
  UUUUUUUUUP2  &8      9   IRecordInfo .?AUIRecordInfo@@    #     馞   D3D12_COMMAND_LIST_TYPE_DIRECT 篁�  D3D12_COMMAND_LIST_TYPE_BUNDLE 篁�  D3D12_COMMAND_LIST_TYPE_COMPUTE 蝰  D3D12_COMMAND_LIST_TYPE_COPY �  D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE �  D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE �  ��D3D12_COMMAND_LIST_TYPE_NONE F   t   <  D3D12_COMMAND_LIST_TYPE .?AW4D3D12_COMMAND_LIST_TYPE@@ 駄   �              $_s__CatchableTypeArray$_extraBytes_24 .?AU$_s__CatchableTypeArray$_extraBytes_24@@ 蝰>   �              _s__CatchableType .?AU_s__CatchableType@@ 
 ?   蝰
 @     A  #     �> 
 t     nCatchableTypes 蝰
 B   arrayOfCatchableTypes j   C           $_s__CatchableTypeArray$_extraBytes_24 .?AU$_s__CatchableTypeArray$_extraBytes_24@@ 蝰�   Uri_PROPERTY_ABSOLUTE_URI    Uri_PROPERTY_STRING_START   Uri_PROPERTY_AUTHORITY 篁�  Uri_PROPERTY_DISPLAY_URI �  Uri_PROPERTY_DOMAIN 蝰  Uri_PROPERTY_EXTENSION 篁�  Uri_PROPERTY_FRAGMENT   Uri_PROPERTY_HOST   Uri_PROPERTY_PASSWORD   Uri_PROPERTY_PATH  	 Uri_PROPERTY_PATH_AND_QUERY 蝰 
 Uri_PROPERTY_QUERY 篁�  Uri_PROPERTY_RAW_URI �  Uri_PROPERTY_SCHEME_NAME � 
 Uri_PROPERTY_USER_INFO 篁�  Uri_PROPERTY_USER_NAME 篁�  Uri_PROPERTY_STRING_LAST �  Uri_PROPERTY_HOST_TYPE 篁�  Uri_PROPERTY_DWORD_START �  Uri_PROPERTY_PORT   Uri_PROPERTY_SCHEME 蝰  Uri_PROPERTY_ZONE   Uri_PROPERTY_DWORD_LAST 蝰:   t   E  __MIDL_IUri_0001 .?AW4__MIDL_IUri_0001@@ 篁�  D3D_SVF_USERPACKED 篁�  D3D_SVF_USED �  D3D_SVF_INTERFACE_POINTER   D3D_SVF_INTERFACE_PARAMETER 蝰  D3D10_SVF_USERPACKED �  D3D10_SVF_USED 篁�  D3D11_SVF_INTERFACE_POINTER 蝰  D3D11_SVF_INTERFACE_PARAMETER  ����D3D_SVF_FORCE_DWORD 蝰N 	  t   G  _D3D_SHADER_VARIABLE_FLAGS .?AW4_D3D_SHADER_VARIABLE_FLAGS@@ 篁聱    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS 篁�  D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS 篁�  D3D12_DEBUG_DEVICE_PARAMETER_GPU_SLOWDOWN_PERFORMANCE_FACTOR �  D3D12_DEBUG_DEVICE_PARAMETER_BYTECODE_VALIDATION_MODE Z   t   I  D3D12_DEBUG_DEVICE_PARAMETER_TYPE .?AW4D3D12_DEBUG_DEVICE_PARAMETER_TYPE@@ 馢   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY �  D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION 蝰  NUM_D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODES r   t   K  D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE .?AW4D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE@@ 篁�6 
 "     dwLowDateTime 
 "    dwHighDateTime 篁�.   M           _FILETIME .?AU_FILETIME@@ 2 
 "     Lo 篁�
     Hi 篁�
      int64    O   tagCY .?ATtagCY@@ B    SYS_WIN16   SYS_WIN32   SYS_MAC 蝰  SYS_WIN64 .   t   Q  tagSYSKIND .?AW4tagSYSKIND@@ 篁�* 
 "     cElems 篁�
    pElems 篁�>   S           tagCAPROPVARIANT .?AUtagCAPROPVARIANT@@ 蝰   D3D_CT_CBUFFER 篁�  D3D_CT_TBUFFER 篁�  D3D_CT_INTERFACE_POINTERS   D3D_CT_RESOURCE_BIND_INFO    D3D10_CT_CBUFFER �  D3D10_CT_TBUFFER �   D3D11_CT_CBUFFER �  D3D11_CT_TBUFFER �  D3D11_CT_INTERFACE_POINTERS 蝰  D3D11_CT_RESOURCE_BIND_INFO 蝰: 
  t   U  _D3D_CBUFFER_TYPE .?AW4_D3D_CBUFFER_TYPE@@ 馧    D3D12_LIFETIME_STATE_IN_USE 蝰  D3D12_LIFETIME_STATE_NOT_IN_USE 蝰B   t   W  D3D12_LIFETIME_STATE .?AW4D3D12_LIFETIME_STATE@@ 篁�
     * 
 "     cElems 篁�
 Y   pElems 篁�6   Z           tagCAFILETIME .?AUtagCAFILETIME@@ �   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD 蝰  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE �  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_NO_ACCESS   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_SRV 篁�  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_UAV 篁駀   t   \  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE .?AW4D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE@@ � �  #     矜   D3D_TESSELLATOR_PARTITIONING_UNDEFINED 篁�  D3D_TESSELLATOR_PARTITIONING_INTEGER �  D3D_TESSELLATOR_PARTITIONING_POW2   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 蝰  D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN �   D3D11_TESSELLATOR_PARTITIONING_UNDEFINED �  D3D11_TESSELLATOR_PARTITIONING_INTEGER 篁�  D3D11_TESSELLATOR_PARTITIONING_POW2 蝰  D3D11_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD   D3D11_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN 篁馬 
  t   _  D3D_TESSELLATOR_PARTITIONING .?AW4D3D_TESSELLATOR_PARTITIONING@@ 篁�
     * 
 "     cElems 篁�
 a   pElems 篁�*   b           tagCAH .?AUtagCAH@@ 蝰J   �              _s__RTTIBaseClassArray .?AU_s__RTTIBaseClassArray@@ 蝰
 d   蝰
 e    f 
 "     signature 
 "    attributes 篁�
 "    numBaseClasses 篁�
 f   pBaseClassArray 蝰^   g           _s__RTTIClassHierarchyDescriptor .?AU_s__RTTIClassHierarchyDescriptor@@ 蝰j   �              $_s__RTTIBaseClassArray$_extraBytes_16 .?AU$_s__RTTIBaseClassArray$_extraBytes_16@@ 蝰 �  #     �* 
 j    arrayOfBaseClassDescriptors 蝰j   k           $_s__RTTIBaseClassArray$_extraBytes_16 .?AU$_s__RTTIBaseClassArray$_extraBytes_16@@ 蝰^   �              _LARGE_INTEGER::<unnamed-type-u> .?AU<unnamed-type-u>@_LARGE_INTEGER@@ 篁駀 
 "     LowPart 蝰
     HighPart �  m  <unnamed-type-u> 篁�
 m    u 
      QuadPart �2  n   _LARGE_INTEGER .?AT_LARGE_INTEGER@@ 蝰�    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED �  D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK �  D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_PROFILING_BY_SYSTEM 馴   t   p  D3D12_BACKGROUND_PROCESSING_MODE .?AW4D3D12_BACKGROUND_PROCESSING_MODE@@ 篁窬   D3D12_COLOR_WRITE_ENABLE_RED �  D3D12_COLOR_WRITE_ENABLE_GREEN 篁�  D3D12_COLOR_WRITE_ENABLE_BLUE   D3D12_COLOR_WRITE_ENABLE_ALPHA 篁�  D3D12_COLOR_WRITE_ENABLE_ALL 馢   t   r  D3D12_COLOR_WRITE_ENABLE .?AW4D3D12_COLOR_WRITE_ENABLE@@ 篁褛    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV 篁�  D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER 篁�  D3D12_DESCRIPTOR_HEAP_TYPE_RTV 篁�  D3D12_DESCRIPTOR_HEAP_TYPE_DSV 篁�  D3D12_DESCRIPTOR_HEAP_TYPE_NUM_TYPES 馧   t   t  D3D12_DESCRIPTOR_HEAP_TYPE .?AW4D3D12_DESCRIPTOR_HEAP_TYPE@@ 篁馴   �              $_TypeDescriptor$_extraBytes_22 .?AU$_TypeDescriptor$_extraBytes_22@@  p   #     �6 
 "    pVFTable �
    spare 
 w   name 馴   x          & $_TypeDescriptor$_extraBytes_22 .?AU$_TypeDescriptor$_extraBytes_22@@ r   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW 蝰  D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW 篁�  D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW 篁� 	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS 篁� 
 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_MESH 篁�  D3D12_INDIRECT_ARGUMENT_TYPE_INCREMENTING_CONSTANT 篁馬   t   z  D3D12_INDIRECT_ARGUMENT_TYPE .?AW4D3D12_INDIRECT_ARGUMENT_TYPE@@ 篁駳   D3D_TESSELLATOR_OUTPUT_UNDEFINED �  D3D_TESSELLATOR_OUTPUT_POINT �  D3D_TESSELLATOR_OUTPUT_LINE 蝰  D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW 篁�  D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW 蝰   D3D11_TESSELLATOR_OUTPUT_UNDEFINED 篁�  D3D11_TESSELLATOR_OUTPUT_POINT 篁�  D3D11_TESSELLATOR_OUTPUT_LINE   D3D11_TESSELLATOR_OUTPUT_TRIANGLE_CW �  D3D11_TESSELLATOR_OUTPUT_TRIANGLE_CCW Z 
  t   |  D3D_TESSELLATOR_OUTPUT_PRIMITIVE .?AW4D3D_TESSELLATOR_OUTPUT_PRIMITIVE@@ 篁駷    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 篁�  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1 篁�  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_2 篁駄   t   ~  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER .?AW4D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER@@ 篁� A  #      �> 
 t     nCatchableTypes 蝰
 �   arrayOfCatchableTypes J   �           _s__CatchableTypeArray .?AU_s__CatchableTypeArray@@ 蝰Z   �              $_TypeDescriptor$_extraBytes_23 .?AU$_TypeDescriptor$_extraBytes_23@@  p   #     �6 
 "    pVFTable �
    spare 
 �   name 馴   �          ' $_TypeDescriptor$_extraBytes_23 .?AU$_TypeDescriptor$_extraBytes_23@@ >   �              tagSAFEARRAYBOUND .?AUtagSAFEARRAYBOUND@@  �  #     駔 
 !     cDims 
 !    fFeatures 
 "    cbElements 篁�
 "    cLocks 篁�
    pvData 篁�
 �   rgsabound 6   �            tagSAFEARRAY .?AUtagSAFEARRAY@@ 蝰
    u  
     	     �     �          u   "      	     �     �          I  0  u   "      	     �     �      6   �              tagDISPPARAMS .?AUtagDISPPARAMS@@ 
 �    6   �              tagEXCEPINFO .?AUtagEXCEPINFO@@ 蝰
 �    &       I  "   !   �  F  �  u   	     �     �      
   �  
    �   	     �    �      
    蝰
 �  ,  
    �   	     �    �       	     �              �    �    �  
   ,   	�    �     �       	�    �     �         �    �  �       蝰 �     GetTypeInfoCount 篁� �      GetTypeInfo  �  (   GetIDsOfNames 蝰 �  0   Invoke � �  IDispatch 蝰 �  operator= 蝰
 
  UUUP蝰. 
 &�      �   IDispatch .?AUIDispatch@@  �  #     駈   �              __vcrt_va_list_is_reference<wchar_t const *> .?AU?$__vcrt_va_list_is_reference@PEB_W@@ 篁�    __the_value 蝰�  0   �  __vcrt_va_list_is_reference<wchar_t const *>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@PEB_W@@ �&   �  <unnamed-enum-__the_value> 駈  �           __vcrt_va_list_is_reference<wchar_t const *> .?AU?$__vcrt_va_list_is_reference@PEB_W@@ 篁耱    D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_DISABLED 蝰  D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_WHEN_HASH_BYPASSED   D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_ALL_BYTECODE 蝰  D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_MODE_DEFAULT 蝰n   t   �  D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_MODE .?AW4D3D12_DEBUG_DEVICE_BYTECODE_VALIDATION_MODE@@ 耜   D3D_SRV_DIMENSION_UNKNOWN   D3D_SRV_DIMENSION_BUFFER �  D3D_SRV_DIMENSION_TEXTURE1D 蝰  D3D_SRV_DIMENSION_TEXTURE1DARRAY �  D3D_SRV_DIMENSION_TEXTURE2D 蝰  D3D_SRV_DIMENSION_TEXTURE2DARRAY �  D3D_SRV_DIMENSION_TEXTURE2DMS   D3D_SRV_DIMENSION_TEXTURE2DMSARRAY 篁�  D3D_SRV_DIMENSION_TEXTURE3D 蝰 	 D3D_SRV_DIMENSION_TEXTURECUBE  
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY 篁�  D3D_SRV_DIMENSION_BUFFEREX 篁�   D3D10_SRV_DIMENSION_UNKNOWN 蝰  D3D10_SRV_DIMENSION_BUFFER 篁�  D3D10_SRV_DIMENSION_TEXTURE1D   D3D10_SRV_DIMENSION_TEXTURE1DARRAY 篁�  D3D10_SRV_DIMENSION_TEXTURE2D   D3D10_SRV_DIMENSION_TEXTURE2DARRAY 篁�  D3D10_SRV_DIMENSION_TEXTURE2DMS 蝰  D3D10_SRV_DIMENSION_TEXTURE2DMSARRAY �  D3D10_SRV_DIMENSION_TEXTURE3D  	 D3D10_SRV_DIMENSION_TEXTURECUBE 蝰   D3D10_1_SRV_DIMENSION_UNKNOWN   D3D10_1_SRV_DIMENSION_BUFFER �  D3D10_1_SRV_DIMENSION_TEXTURE1D 蝰  D3D10_1_SRV_DIMENSION_TEXTURE1DARRAY �  D3D10_1_SRV_DIMENSION_TEXTURE2D 蝰  D3D10_1_SRV_DIMENSION_TEXTURE2DARRAY �  D3D10_1_SRV_DIMENSION_TEXTURE2DMS   D3D10_1_SRV_DIMENSION_TEXTURE2DMSARRAY 篁�  D3D10_1_SRV_DIMENSION_TEXTURE3D 蝰 	 D3D10_1_SRV_DIMENSION_TEXTURECUBE  
 D3D10_1_SRV_DIMENSION_TEXTURECUBEARRAY 篁�   D3D11_SRV_DIMENSION_UNKNOWN 蝰  D3D11_SRV_DIMENSION_BUFFER 篁�  D3D11_SRV_DIMENSION_TEXTURE1D   D3D11_SRV_DIMENSION_TEXTURE1DARRAY 篁�  D3D11_SRV_DIMENSION_TEXTURE2D   D3D11_SRV_DIMENSION_TEXTURE2DARRAY 篁�  D3D11_SRV_DIMENSION_TEXTURE2DMS 蝰  D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY �  D3D11_SRV_DIMENSION_TEXTURE3D  	 D3D11_SRV_DIMENSION_TEXTURECUBE 蝰 
 D3D11_SRV_DIMENSION_TEXTURECUBEARRAY �  D3D11_SRV_DIMENSION_BUFFEREX �: -  t   �  D3D_SRV_DIMENSION .?AW4D3D_SRV_DIMENSION@@ 馴   DISPLAYCONFIG_SCANLINE_ORDERING_UNSPECIFIED 蝰  DISPLAYCONFIG_SCANLINE_ORDERING_PROGRESSIVE 蝰  DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED 篁�  DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED_UPPERFIELDFIRST 篁�  DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED_LOWERFIELDFIRST 篁�  ��DISPLAYCONFIG_SCANLINE_ORDERING_FORCE_UINT32 V   t   �  DISPLAYCONFIG_SCANLINE_ORDERING .?AW4DISPLAYCONFIG_SCANLINE_ORDERING@@ 駀    D3D12_BARRIER_TYPE_GLOBAL   D3D12_BARRIER_TYPE_TEXTURE 篁�  D3D12_BARRIER_TYPE_BUFFER >   t   �  D3D12_BARRIER_TYPE .?AW4D3D12_BARRIER_TYPE@@ 篁�* 
 "     cElems 篁�
 0   pElems 篁�.   �           tagCABSTR .?AUtagCABSTR@@ . 
 C    guidVersion 蝰
     pStream 蝰B   �           tagVersionedStream .?AUtagVersionedStream@@ 蝰v 
 u     properties 篁�
 �   pType 
 �   thisDisplacement �
 t    sizeOrOffset �
 �   copyFunction �>   �          $ _s__CatchableType .?AU_s__CatchableType@@  �  #     瘭    TYSPEC_CLSID �  TYSPEC_FILEEXT 篁�  TYSPEC_MIMETYPE 蝰  TYSPEC_FILENAME 蝰  TYSPEC_PROGID   TYSPEC_PACKAGENAME 篁�  TYSPEC_OBJECTID 蝰*   t   �  tagTYSPEC .?AW4tagTYSPEC@@ �> 
 "     cbSize 篁�
     ulClipFmt 
     pClipData 2   �           tagCLIPDATA .?AUtagCLIPDATA@@ * 
 "     cElems 篁�
 !   pElems 篁�*   �           tagCAUI .?AUtagCAUI@@ Z   �              $_TypeDescriptor$_extraBytes_27 .?AU$_TypeDescriptor$_extraBytes_27@@  p   #     �6 
 "    pVFTable �
    spare 
 �   name 馴   �          + $_TypeDescriptor$_extraBytes_27 .?AU$_TypeDescriptor$_extraBytes_27@@ 6    ServerApplication   LibraryApplication 篁�>   t   �  tagApplicationType .?AW4tagApplicationType@@ 篁�2    DVEXTENT_CONTENT �  DVEXTENT_INTEGRAL 2   t   �  tagExtentMode .?AW4tagExtentMode@@ �* 
 "     cElems 篁�
 p   pElems 篁�*   �           tagCAC .?AUtagCAC@@ 蝰�    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK 蝰  D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK 篁�  D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE 篁�  D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK_UINT 蝰  D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE_UINT 蝰J   t   �  D3D12_STATIC_BORDER_COLOR .?AW4D3D12_STATIC_BORDER_COLOR@@ 馧    D3D12_SHADER_CACHE_MODE_MEMORY 篁�  D3D12_SHADER_CACHE_MODE_DISK 馞   t   �  D3D12_SHADER_CACHE_MODE .?AW4D3D12_SHADER_CACHE_MODE@@ 駄    Uri_HOST_UNKNOWN �  Uri_HOST_DNS �  Uri_HOST_IPV4   Uri_HOST_IPV6   Uri_HOST_IDN �:   t   �  __MIDL_IUri_0002 .?AW4__MIDL_IUri_0002@@ 篁�   D3D12_STATE_SUBOBJECT_TYPE_STATE_OBJECT_CONFIG 篁�  D3D12_STATE_SUBOBJECT_TYPE_GLOBAL_ROOT_SIGNATURE �  D3D12_STATE_SUBOBJECT_TYPE_LOCAL_ROOT_SIGNATURE 蝰  D3D12_STATE_SUBOBJECT_TYPE_NODE_MASK �  D3D12_STATE_SUBOBJECT_TYPE_DXIL_LIBRARY 蝰  D3D12_STATE_SUBOBJECT_TYPE_EXISTING_COLLECTION 篁�  D3D12_STATE_SUBOBJECT_TYPE_SUBOBJECT_TO_EXPORTS_ASSOCIATION 蝰  D3D12_STATE_SUBOBJECT_TYPE_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION � 	 D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_SHADER_CONFIG 蝰 
 D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG   D3D12_STATE_SUBOBJECT_TYPE_HIT_GROUP �  D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG1 篁� 
 D3D12_STATE_SUBOBJECT_TYPE_WORK_GRAPH   D3D12_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT �  D3D12_STATE_SUBOBJECT_TYPE_BLEND �  D3D12_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 篁�  D3D12_STATE_SUBOBJECT_TYPE_RASTERIZER   D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL �  D3D12_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT 蝰  D3D12_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE   D3D12_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY   D3D12_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS �  D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 蝰  D3D12_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 篁�  D3D12_STATE_SUBOBJECT_TYPE_FLAGS �  D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1   D3D12_STATE_SUBOBJECT_TYPE_VIEW_INSTANCING 篁�  D3D12_STATE_SUBOBJECT_TYPE_GENERIC_PROGRAM 篁�  D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2   D3D12_STATE_SUBOBJECT_TYPE_MAX_VALID 馧   t   �  D3D12_STATE_SUBOBJECT_TYPE .?AW4D3D12_STATE_SUBOBJECT_TYPE@@ 篁� �  #      �* 
 �    arrayOfBaseClassDescriptors 蝰J   �           _s__RTTIBaseClassArray .?AU_s__RTTIBaseClassArray@@ 蝰" 
 "     Lo 篁�
     Hi 篁馞  �           tagCY::<unnamed-tag> .?AU<unnamed-tag>@tagCY@@ 篁矜   D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED 篁�  D3D12_MESSAGE_CATEGORY_MISCELLANEOUS �  D3D12_MESSAGE_CATEGORY_INITIALIZATION   D3D12_MESSAGE_CATEGORY_CLEANUP 篁�  D3D12_MESSAGE_CATEGORY_COMPILATION 篁�  D3D12_MESSAGE_CATEGORY_STATE_CREATION   D3D12_MESSAGE_CATEGORY_STATE_SETTING �  D3D12_MESSAGE_CATEGORY_STATE_GETTING �  D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION � 	 D3D12_MESSAGE_CATEGORY_EXECUTION � 
 D3D12_MESSAGE_CATEGORY_SHADER F   t   �  D3D12_MESSAGE_CATEGORY .?AW4D3D12_MESSAGE_CATEGORY@@ 篁馼    BINDHANDLETYPES_APPCACHE �  BINDHANDLETYPES_DEPENDENCY 篁�  BINDHANDLETYPES_COUNT N   t   �  __MIDL_IGetBindHandle_0001 .?AW4__MIDL_IGetBindHandle_0001@@ 篁�2   �           tagCASCODE .?AUtagCASCODE@@ 蝰* 
 "     cElems 篁�
    pElems 篁�*   �           tagCAI .?AUtagCAI@@ 蝰  ��URLZONE_INVALID �   URLZONE_PREDEFINED_MIN 篁�   URLZONE_LOCAL_MACHINE   URLZONE_INTRANET �  URLZONE_TRUSTED 蝰  URLZONE_INTERNET �  URLZONE_UNTRUSTED  �URLZONE_PREDEFINED_MAX 篁� �URLZONE_USER_MIN � 'URLZONE_USER_MAX �. 
  t   �  tagURLZONE .?AW4tagURLZONE@@ 篁駘    PowerUserPresent �  PowerUserNotPresent 蝰  PowerUserInactive   PowerUserMaximum �  PowerUserInvalid 馞   t   �  _USER_ACTIVITY_PRESENCE .?AW4_USER_ACTIVITY_PRESENCE@@ � �  #      窈    TKIND_ENUM 篁�  TKIND_RECORD �  TKIND_MODULE �  TKIND_INTERFACE 蝰  TKIND_DISPATCH 篁�  TKIND_COCLASS   TKIND_ALIAS 蝰  TKIND_UNION 蝰  TKIND_MAX . 	  t   �  tagTYPEKIND .?AW4tagTYPEKIND@@ �* 
 "     cElems 篁�
     pElems 篁�*   �           tagCAUB .?AUtagCAUB@@ �    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION 篁�  D3D12_RESOURCE_BARRIER_TYPE_ALIASING �  D3D12_RESOURCE_BARRIER_TYPE_UAV 蝰N   t   �  D3D12_RESOURCE_BARRIER_TYPE .?AW4D3D12_RESOURCE_BARRIER_TYPE@@ 駀   �              $_s__RTTIBaseClassArray$_extraBytes_8 .?AU$_s__RTTIBaseClassArray$_extraBytes_8@@  �  #     �* 
 �    arrayOfBaseClassDescriptors 蝰f   �           $_s__RTTIBaseClassArray$_extraBytes_8 .?AU$_s__RTTIBaseClassArray$_extraBytes_8@@ * 
 "     cElements 
     lLbound 蝰>   �           tagSAFEARRAYBOUND .?AUtagSAFEARRAYBOUND@@    D3D10_SB_4_COMPONENT_X 篁�  D3D10_SB_4_COMPONENT_Y 篁�  D3D10_SB_4_COMPONENT_Z 篁�  D3D10_SB_4_COMPONENT_W 篁�   D3D10_SB_4_COMPONENT_R 篁�  D3D10_SB_4_COMPONENT_G 篁�  D3D10_SB_4_COMPONENT_B 篁�  D3D10_SB_4_COMPONENT_A 篁馢   t   �  D3D10_SB_4_COMPONENT_NAME .?AW4D3D10_SB_4_COMPONENT_NAME@@ �2 
 t     mdisp 
 t    pdisp 
 t    vdisp &   �           _PMD .?AU_PMD@@ 蝰Z 
 F    rgvarg 篁�
    rgdispidNamedArgs 
 u    cArgs 
 u    cNamedArgs 篁�6   �           tagDISPPARAMS .?AUtagDISPPARAMS@@ Z   D3D_CBF_USERPACKED 篁�  D3D10_CBF_USERPACKED � ����D3D_CBF_FORCE_DWORD 蝰J   t   �  _D3D_SHADER_CBUFFER_FLAGS .?AW4_D3D_SHADER_CBUFFER_FLAGS@@ 褛    D3D12_LINE_RASTERIZATION_MODE_ALIASED   D3D12_LINE_RASTERIZATION_MODE_ALPHA_ANTIALIASED 蝰  D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_WIDE �  D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_NARROW 篁馬   t   �  D3D12_LINE_RASTERIZATION_MODE .?AW4D3D12_LINE_RASTERIZATION_MODE@@ 馞 4 K   tagVARIANT::<unnamed-tag> .?AT<unnamed-tag>@tagVARIANT@@ 矜
 !     vt 篁�
 !    wReserved1 篁�
 !    wReserved2 篁�
 !    wReserved3 篁�
     llVal 
     lVal �
      bVal �
     iVal �
 @    fltVal 篁�
 A    dblVal 篁�
     boolVal 蝰
     __OBSOLETE__VARIANT_BOOL �
     scode 
    cyVal 
 A    date �
 q   bstrVal 蝰
    punkVal 蝰
    pdispVal �
 &   parray 篁�
     pbVal 
    piVal 
    plVal 
    pllVal 篁�
 @   pfltVal 蝰
 A   pdblVal 蝰
    pboolVal �
    __OBSOLETE__VARIANT_PBOOL 
    pscode 篁�
 ?   pcyVal 篁�
 A   pdate 
 0   pbstrVal �
 @   ppunkVal �
 A   ppdispVal 
 B   pparray 蝰
 F   pvarVal 蝰
    byref 
 p    cVal �
 !    uiVal 
 "    ulVal 
 #    ullVal 篁�
 t    intVal 篁�
 u    uintVal 蝰
 >   pdecVal 蝰
 p   pcVal 
 !   puiVal 篁�
 "   pulVal 篁�
 #   pullVal 蝰
 t   pintVal 蝰
 u   puintVal �
    pvRecord �
 J   pRecInfo 馸 3 �           tagVARIANT::<unnamed-tag>::<unnamed-tag> .?AU<unnamed-tag>@0tagVARIANT@@ 駧
      llVal 
      lVal �
       bVal �
      iVal �
 @     fltVal 篁�
 A     dblVal 篁�
      boolVal 蝰
      __OBSOLETE__VARIANT_BOOL �
      scode 
     cyVal 
 A     date �
 q    bstrVal 蝰
     punkVal 蝰
     pdispVal �
 &    parray 篁�
      pbVal 
     piVal 
     plVal 
     pllVal 篁�
 @    pfltVal 蝰
 A    pdblVal 蝰
     pboolVal �
     __OBSOLETE__VARIANT_PBOOL 
     pscode 篁�
 ?    pcyVal 篁�
 A    pdate 
 0    pbstrVal �
 @    ppunkVal �
 A    ppdispVal 
 B    pparray 蝰
 F    pvarVal 蝰
     byref 
 p     cVal �
 !     uiVal 
 "     ulVal 
 #     ullVal 篁�
 t     intVal 篁�
 u     uintVal 蝰
 >    pdecVal 蝰
 p    pcVal 
 !    puiVal 篁�
 "    pulVal 篁�
 #    pullVal 蝰
 t    pintVal 蝰
 u    puintVal �
     pvRecord �
 J   pRecInfo 駀 / �   tagVARIANT::<unnamed-tag>::<unnamed-tag>::<unnamed-tag> .?AT<unnamed-tag>@00tagVARIANT@@ �* 
     pvRecord �
 J   pRecInfo 駘             tagVARIANT::<unnamed-tag>::<unnamed-tag>::<unnamed-tag>::<unnamed-tag> .?AU<unnamed-tag>@000tagVARIANT@@ �
          -  "   "   "     
 !    	   !                 -    "   "      	   !             
 "        -  "   "   "   	   	   !       
          -  "  "   0  "   	   	   !                 "   E  0  "   	   !                 -  "  -  "    	   !              	   !       �       	   !              6   �              IEnumSTATSTG .?AUIEnumSTATSTG@@ 蝰
     
         "     "      	   !              	   !       �       	   !       7      
    蝰
         -         	   !             
    I   	   !                 "   "    	   !       !      2   �              tagSTATSTG .?AUtagSTATSTG@@ 蝰
 #        $  "    	   !       %      
 !  �  
    '   	   !      (      
 !   蝰
 *  ,  
    +   	   !      ,       	   !                )    -    .  
 !  ,   	0  !       (       	0  !       ,         1    2  �      蝰      CreateStream 篁�       OpenStream �   (   CreateStorage 蝰 
  0   OpenStorage    8   CopyTo �   @   MoveElementTo 蝰   H   Commit �   P   Revert �   X   EnumElements 篁�   `   DestroyElement �   h   RenameElement 蝰   p   SetElementTimes     x   SetClass 篁� "  �   SetStateBits 篁� &  �   Stat 篁� /  IStorage 篁� 3  operator= 蝰 
  UUUUUUUUU�.  &4      5   IStorage .?AUIStorage@@ 蝰N�   D3D12_MESSAGE_ID_UNKNOWN �  D3D12_MESSAGE_ID_STRING_FROM_APPLICATION �  D3D12_MESSAGE_ID_CORRUPTED_THIS 蝰  D3D12_MESSAGE_ID_CORRUPTED_PARAMETER1   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER2   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER3   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER4   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER5   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER6  	 D3D12_MESSAGE_ID_CORRUPTED_PARAMETER7  
 D3D12_MESSAGE_ID_CORRUPTED_PARAMETER8   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER9   D3D12_MESSAGE_ID_CORRUPTED_PARAMETER10 篁� 
 D3D12_MESSAGE_ID_CORRUPTED_PARAMETER11 篁�  D3D12_MESSAGE_ID_CORRUPTED_PARAMETER12 篁�  D3D12_MESSAGE_ID_CORRUPTED_PARAMETER13 篁�  D3D12_MESSAGE_ID_CORRUPTED_PARAMETER14 篁�  D3D12_MESSAGE_ID_CORRUPTED_PARAMETER15 篁�  D3D12_MESSAGE_ID_CORRUPTED_MULTITHREADING   D3D12_MESSAGE_ID_MESSAGE_REPORTING_OUTOFMEMORY 篁�  D3D12_MESSAGE_ID_GETPRIVATEDATA_MOREDATA �  D3D12_MESSAGE_ID_SETPRIVATEDATA_INVALIDFREEDATA 蝰  D3D12_MESSAGE_ID_SETPRIVATEDATA_CHANGINGPARAMS 篁�  D3D12_MESSAGE_ID_SETPRIVATEDATA_OUTOFMEMORY 蝰  D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_UNRECOGNIZEDFORMAT �  D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDESC   D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFORMAT 蝰  D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDVIDEOPLANESLICE �  D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDPLANESLICE 蝰  D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDIMENSIONS 蝰   D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDRESOURCE  # D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_UNRECOGNIZEDFORMAT 篁� $ D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_UNSUPPORTEDFORMAT  % D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDESC 蝰 & D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDFORMAT  ' D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDVIDEOPLANESLICE 篁� ( D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDPLANESLICE  ) D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDIMENSIONS  * D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDRESOURCE 蝰 - D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_UNRECOGNIZEDFORMAT 篁� . D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDESC 蝰 / D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFORMAT  0 D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDIMENSIONS  1 D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDRESOURCE 蝰 4 D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_OUTOFMEMORY 篁� 5 D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TOOMANYELEMENTS 篁� 6 D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDFORMAT � 7 D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INCOMPATIBLEFORMAT  8 D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOT 篁� 9 D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDINPUTSLOTCLASS � : D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_STEPRATESLOTCLASSMISMATCH � ; D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOTCLASSCHANGE  < D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSTEPRATECHANGE � = D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDALIGNMENT 蝰 > D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_DUPLICATESEMANTIC � ? D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_UNPARSEABLEINPUTSIGNATURE � @ D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_NULLSEMANTIC 蝰 A D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_MISSINGELEMENT  B D3D12_MESSAGE_ID_CREATEVERTEXSHADER_OUTOFMEMORY 蝰 C D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERBYTECODE  D D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERTYPE  E D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_OUTOFMEMORY  F D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERBYTECODE 蝰 G D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERTYPE 蝰 H D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTOFMEMORY  I D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERBYTECODE 蝰 J D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE 蝰 K D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMENTRIES 蝰 L D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSTREAMSTRIDEUNUSED 篁� O D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSLOT0EXPECTED  P D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSLOT 蝰 Q D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_ONLYONEELEMENTPERSLOT 蝰 R D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCOMPONENTCOUNT 蝰 S D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTARTCOMPONENTANDCOMPONENTCOUNT � T D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDGAPDEFINITION 篁� U D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_REPEATEDOUTPUT � V D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSTREAMSTRIDE 蝰 W D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGSEMANTIC  X D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MASKMISMATCH 篁� Y D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_CANTHAVEONLYGAPS 篁� Z D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DECLTOOCOMPLEX � [ D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGOUTPUTSIGNATURE � \ D3D12_MESSAGE_ID_CREATEPIXELSHADER_OUTOFMEMORY 篁� ] D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERBYTECODE � ^ D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERTYPE � _ D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFILLMODE 篁� ` D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDCULLMODE 篁� a D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDDEPTHBIASCLAMP � b D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDSLOPESCALEDDEPTHBIAS 篁� d D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHWRITEMASK 篁� e D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHFUNC  f D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFAILOP 篁� g D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILZFAILOP 蝰 h D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILPASSOP 篁� i D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFUNC � j D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFAILOP  k D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILZFAILOP 篁� l D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILPASSOP  m D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFUNC 蝰 o D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLEND  p D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLEND 篁� q D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOP � r D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLENDALPHA 篁� s D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLENDALPHA 蝰 t D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOPALPHA  u D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDRENDERTARGETWRITEMASK 篁� v D3D12_MESSAGE_ID_GET_PROGRAM_IDENTIFIER_ERROR  w D3D12_MESSAGE_ID_GET_WORK_GRAPH_PROPERTIES_ERROR � x D3D12_MESSAGE_ID_SET_PROGRAM_ERROR 篁� � D3D12_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_INVALID 篁� � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ROOT_SIGNATURE_NOT_SET  � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ROOT_SIGNATURE_MISMATCH 篁� � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_NOT_SET � � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_STRIDE_TOO_SMALL  � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_TOO_SMALL 篁� � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_NOT_SET 蝰 � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_FORMAT_INVALID 篁� � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_TOO_SMALL  � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INVALID_PRIMITIVETOPOLOGY � � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_STRIDE_UNALIGNED 篁� � D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_OFFSET_UNALIGNED  � D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_AT_FAULT � � D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_POSSIBLY_AT_FAULT  � D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_NOT_AT_FAULT � � D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TRAILING_DIGIT_IN_SEMANTIC  � D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_TRAILING_DIGIT_IN_SEMANTIC � � D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TYPE_MISMATCH � � D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_EMPTY_LAYOUT 蝰 � D3D12_MESSAGE_ID_LIVE_OBJECT_SUMMARY � D3D12_MESSAGE_ID_LIVE_DEVICE � D3D12_MESSAGE_ID_LIVE_SWAPCHAIN 蝰 D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFLAGS � D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDCLASSLINKAGE 蝰 D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDCLASSLINKAGE  D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAMTORASTERIZER 蝰 D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDCLASSLINKAGE 篁� D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAM 蝰 D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDENTRIES 蝰 D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTRIDES 蝰 D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTRIDES 蝰 !D3D12_MESSAGE_ID_CREATEHULLSHADER_OUTOFMEMORY  "D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERBYTECODE 蝰 #D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERTYPE 蝰 $D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDCLASSLINKAGE  &D3D12_MESSAGE_ID_CREATEDOMAINSHADER_OUTOFMEMORY 蝰 'D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERBYTECODE  (D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERTYPE  )D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCLASSLINKAGE 蝰 6D3D12_MESSAGE_ID_RESOURCE_UNMAP_NOTMAPPED  >D3D12_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_MISMATCHED_DATA_SIZE � AD3D12_MESSAGE_ID_CREATECOMPUTESHADER_OUTOFMEMORY � BD3D12_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERBYTECODE 篁� CD3D12_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCLASSLINKAGE � KD3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEFLOATOPSNOTSUPPORTED  LD3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEFLOATOPSNOTSUPPORTED 蝰 MD3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEFLOATOPSNOTSUPPORTED  ND3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEFLOATOPSNOTSUPPORTED 蝰 OD3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEFLOATOPSNOTSUPPORTED 蝰 PD3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEFLOATOPSNOTSUPPORTED � QD3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEFLOATOPSNOTSUPPORTED 篁� TD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDRESOURCE 篁� UD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDESC 篁� VD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFORMAT � WD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDVIDEOPLANESLICE  XD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDPLANESLICE � YD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDIMENSIONS � ZD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_UNRECOGNIZEDFORMAT  bD3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFLAGS 蝰 �D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFORCEDSAMPLECOUNT 蝰 �D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDLOGICOPS  �D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEEXTENSIONSNOTSUPPORTED 蝰 �D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEEXTENSIONSNOTSUPPORTED  �D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEEXTENSIONSNOTSUPPORTED 蝰 �D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEEXTENSIONSNOTSUPPORTED  �D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEEXTENSIONSNOTSUPPORTED  �D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEEXTENSIONSNOTSUPPORTED 篁� �D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEEXTENSIONSNOTSUPPORTED � �D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_UAVSNOTSUPPORTED 蝰 �D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_UAVSNOTSUPPORTED  �D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_UAVSNOTSUPPORTED 蝰 �D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_UAVSNOTSUPPORTED  �D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UAVSNOTSUPPORTED  �D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_UAVSNOTSUPPORTED 篁� �D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_UAVSNOTSUPPORTED � �D3D12_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDSOURCERECT 蝰 �D3D12_MESSAGE_ID_DEVICE_CLEARVIEW_EMPTYRECT 蝰 �D3D12_MESSAGE_ID_UPDATETILEMAPPINGS_INVALID_PARAMETER  �D3D12_MESSAGE_ID_COPYTILEMAPPINGS_INVALID_PARAMETER 蝰 �D3D12_MESSAGE_ID_CREATEDEVICE_INVALIDARGS  �D3D12_MESSAGE_ID_CREATEDEVICE_WARNING  D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_TYPE 篁� D3D12_MESSAGE_ID_RESOURCE_BARRIER_NULL_POINTER 篁� 	D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_SUBRESOURCE  
D3D12_MESSAGE_ID_RESOURCE_BARRIER_RESERVED_BITS 蝰 D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISSING_BIND_FLAGS � D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_MISC_FLAGS � 
D3D12_MESSAGE_ID_RESOURCE_BARRIER_MATCHING_STATES  D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMBINATION  D3D12_MESSAGE_ID_RESOURCE_BARRIER_BEFORE_AFTER_MISMATCH 蝰 D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_RESOURCE 篁� D3D12_MESSAGE_ID_RESOURCE_BARRIER_SAMPLE_COUNT 篁� D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAGS 蝰 D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMBINED_FLAGS � D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAGS_FOR_FORMAT 篁� D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_SPLIT_BARRIER 蝰 D3D12_MESSAGE_ID_RESOURCE_BARRIER_UNMATCHED_END 蝰 D3D12_MESSAGE_ID_RESOURCE_BARRIER_UNMATCHED_BEGIN  D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAG 篁� D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMMAND_LIST_TYPE 蝰 D3D12_MESSAGE_ID_INVALID_SUBRESOURCE_STATE 篁� D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_CONTENTION  D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_RESET � D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_RESET_BUNDLE 蝰 D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_CANNOT_RESET 蝰  D3D12_MESSAGE_ID_COMMAND_LIST_OPEN 篁� "D3D12_MESSAGE_ID_INVALID_BUNDLE_API 蝰 #D3D12_MESSAGE_ID_COMMAND_LIST_CLOSED � %D3D12_MESSAGE_ID_WRONG_COMMAND_ALLOCATOR_TYPE  (D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_SYNC 蝰 )D3D12_MESSAGE_ID_COMMAND_LIST_SYNC 篁� *D3D12_MESSAGE_ID_SET_DESCRIPTOR_HEAP_INVALID � -D3D12_MESSAGE_ID_CREATE_COMMANDQUEUE � .D3D12_MESSAGE_ID_CREATE_COMMANDALLOCATOR � /D3D12_MESSAGE_ID_CREATE_PIPELINESTATE  0D3D12_MESSAGE_ID_CREATE_COMMANDLIST12  2D3D12_MESSAGE_ID_CREATE_RESOURCE � 3D3D12_MESSAGE_ID_CREATE_DESCRIPTORHEAP 篁� 4D3D12_MESSAGE_ID_CREATE_ROOTSIGNATURE  5D3D12_MESSAGE_ID_CREATE_LIBRARY 蝰 6D3D12_MESSAGE_ID_CREATE_HEAP � 7D3D12_MESSAGE_ID_CREATE_MONITOREDFENCE 篁� 8D3D12_MESSAGE_ID_CREATE_QUERYHEAP  9D3D12_MESSAGE_ID_CREATE_COMMANDSIGNATURE � :D3D12_MESSAGE_ID_LIVE_COMMANDQUEUE 篁� ;D3D12_MESSAGE_ID_LIVE_COMMANDALLOCATOR 篁� <D3D12_MESSAGE_ID_LIVE_PIPELINESTATE 蝰 =D3D12_MESSAGE_ID_LIVE_COMMANDLIST12 蝰 ?D3D12_MESSAGE_ID_LIVE_RESOURCE 篁� @D3D12_MESSAGE_ID_LIVE_DESCRIPTORHEAP � AD3D12_MESSAGE_ID_LIVE_ROOTSIGNATURE 蝰 BD3D12_MESSAGE_ID_LIVE_LIBRARY  CD3D12_MESSAGE_ID_LIVE_HEAP 篁� DD3D12_MESSAGE_ID_LIVE_MONITOREDFENCE � ED3D12_MESSAGE_ID_LIVE_QUERYHEAP 蝰 FD3D12_MESSAGE_ID_LIVE_COMMANDSIGNATURE 篁� GD3D12_MESSAGE_ID_DESTROY_COMMANDQUEUE  HD3D12_MESSAGE_ID_DESTROY_COMMANDALLOCATOR  ID3D12_MESSAGE_ID_DESTROY_PIPELINESTATE 篁� JD3D12_MESSAGE_ID_DESTROY_COMMANDLIST12 篁� LD3D12_MESSAGE_ID_DESTROY_RESOURCE  MD3D12_MESSAGE_ID_DESTROY_DESCRIPTORHEAP 蝰 ND3D12_MESSAGE_ID_DESTROY_ROOTSIGNATURE 篁� OD3D12_MESSAGE_ID_DESTROY_LIBRARY � PD3D12_MESSAGE_ID_DESTROY_HEAP  QD3D12_MESSAGE_ID_DESTROY_MONITOREDFENCE 蝰 RD3D12_MESSAGE_ID_DESTROY_QUERYHEAP 篁� SD3D12_MESSAGE_ID_DESTROY_COMMANDSIGNATURE  UD3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDIMENSIONS  WD3D12_MESSAGE_ID_CREATERESOURCE_INVALIDMISCFLAGS � ZD3D12_MESSAGE_ID_CREATERESOURCE_INVALIDARG_RETURN  [D3D12_MESSAGE_ID_CREATERESOURCE_OUTOFMEMORY_RETURN 篁� \D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDESC 蝰 _D3D12_MESSAGE_ID_POSSIBLY_INVALID_SUBRESOURCE_STATE 蝰 `D3D12_MESSAGE_ID_INVALID_USE_OF_NON_RESIDENT_RESOURCE  aD3D12_MESSAGE_ID_POSSIBLE_INVALID_USE_OF_NON_RESIDENT_RESOURCE 篁� bD3D12_MESSAGE_ID_BUNDLE_PIPELINE_STATE_MISMATCH 蝰 cD3D12_MESSAGE_ID_PRIMITIVE_TOPOLOGY_MISMATCH_PIPELINE_STATE 蝰 eD3D12_MESSAGE_ID_RENDER_TARGET_FORMAT_MISMATCH_PIPELINE_STATE  fD3D12_MESSAGE_ID_RENDER_TARGET_SAMPLE_DESC_MISMATCH_PIPELINE_STATE 篁� gD3D12_MESSAGE_ID_DEPTH_STENCIL_FORMAT_MISMATCH_PIPELINE_STATE  hD3D12_MESSAGE_ID_DEPTH_STENCIL_SAMPLE_DESC_MISMATCH_PIPELINE_STATE 篁� nD3D12_MESSAGE_ID_CREATESHADER_INVALIDBYTECODE  oD3D12_MESSAGE_ID_CREATEHEAP_NULLDESC � pD3D12_MESSAGE_ID_CREATEHEAP_INVALIDSIZE 蝰 qD3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDHEAPTYPE � rD3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDCPUPAGEPROPERTIES  sD3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDMEMORYPOOL 篁� tD3D12_MESSAGE_ID_CREATEHEAP_INVALIDPROPERTIES  uD3D12_MESSAGE_ID_CREATEHEAP_INVALIDALIGNMENT � vD3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDMISCFLAGS  wD3D12_MESSAGE_ID_CREATEHEAP_INVALIDMISCFLAGS � xD3D12_MESSAGE_ID_CREATEHEAP_INVALIDARG_RETURN  yD3D12_MESSAGE_ID_CREATEHEAP_OUTOFMEMORY_RETURN 篁� zD3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLHEAPPROPERTIES  {D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDHEAPTYPE 蝰 |D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDCPUPAGEPROPERTIES � }D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDMEMORYPOOL  ~D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPPROPERTIES � D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDHEAPMISCFLAGS � �D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPMISCFLAGS 蝰 �D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDARG_RETURN � �D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_OUTOFMEMORY_RETURN  �D3D12_MESSAGE_ID_GETCUSTOMHEAPPROPERTIES_UNRECOGNIZEDHEAPTYPE  �D3D12_MESSAGE_ID_GETCUSTOMHEAPPROPERTIES_INVALIDHEAPTYPE � �D3D12_MESSAGE_ID_CREATE_DESCRIPTOR_HEAP_INVALID_DESC � �D3D12_MESSAGE_ID_INVALID_DESCRIPTOR_HANDLE 篁� �D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALID_CONSERVATIVERASTERMODE  �D3D12_MESSAGE_ID_CREATE_CONSTANT_BUFFER_VIEW_INVALID_RESOURCE  �D3D12_MESSAGE_ID_CREATE_CONSTANT_BUFFER_VIEW_INVALID_DESC  �D3D12_MESSAGE_ID_CREATE_UNORDEREDACCESS_VIEW_INVALID_COUNTER_USAGE 篁� �D3D12_MESSAGE_ID_COPY_DESCRIPTORS_INVALID_RANGES � �D3D12_MESSAGE_ID_COPY_DESCRIPTORS_WRITE_ONLY_DESCRIPTOR 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RTV_FORMAT_NOT_UNKNOWN 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_RENDER_TARGET_COUNT � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VERTEX_SHADER_NOT_SET 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INPUTLAYOUT_NOT_SET � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_HS_DS_SIGNATURE_MISMATCH � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_REGISTERINDEX  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_COMPONENTTYPE  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_REGISTERMASK � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_SYSTEMVALUE 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_NEVERWRITTEN_ALWAYSREADS � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_MINPRECISION � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_SEMANTICNAME_NOT_FOUND 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_XOR_DS_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HULL_SHADER_INPUT_TOPOLOGY_MISMATCH � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_DS_CONTROL_POINT_COUNT_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_DS_TESSELLATOR_DOMAIN_MISMATCH 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_USE_OF_CENTER_MULTISAMPLE_PATTERN 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_USE_OF_FORCED_SAMPLE_COUNT 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_PRIMITIVETOPOLOGY 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_SYSTEMVALUE � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_DUAL_SOURCE_BLENDING_CAN_ONLY_HAVE_RENDER_TARGET_0 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_RENDER_TARGET_DOES_NOT_SUPPORT_BLENDING 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_OUTPUT_TYPE_MISMATCH � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_RENDER_TARGET_DOES_NOT_SUPPORT_LOGIC_OPS � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RENDERTARGETVIEW_NOT_SET  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_DEPTHSTENCILVIEW_NOT_SET  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_GS_INPUT_PRIMITIVE_MISMATCH � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_POSITION_NOT_PRESENT  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MISSING_ROOT_SIGNATURE_FLAGS  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_INDEX_BUFFER_PROPERTIES � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_SAMPLE_DESC � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_DS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_GS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MISSING_ROOT_SIGNATURE 蝰 �D3D12_MESSAGE_ID_EXECUTE_BUNDLE_OPEN_BUNDLE 蝰 �D3D12_MESSAGE_ID_EXECUTE_BUNDLE_DESCRIPTOR_HEAP_MISMATCH � �D3D12_MESSAGE_ID_EXECUTE_BUNDLE_TYPE � �D3D12_MESSAGE_ID_DRAW_EMPTY_SCISSOR_RECTANGLE  �D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_BLOB_NOT_FOUND  �D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_DESERIALIZE_FAILED  �D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_INVALID_CONFIGURATION � �D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_NOT_SUPPORTED_ON_DEVICE 篁� �D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLRESOURCEPROPERTIES  �D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLHEAP 蝰 �D3D12_MESSAGE_ID_GETRESOURCEALLOCATIONINFO_INVALIDRDESCS � �D3D12_MESSAGE_ID_MAKERESIDENT_NULLOBJECTARRAY  �D3D12_MESSAGE_ID_EVICT_NULLOBJECTARRAY 篁� �D3D12_MESSAGE_ID_SET_DESCRIPTOR_TABLE_INVALID  �D3D12_MESSAGE_ID_SET_ROOT_CONSTANT_INVALID 篁� �D3D12_MESSAGE_ID_SET_ROOT_CONSTANT_BUFFER_VIEW_INVALID 篁� �D3D12_MESSAGE_ID_SET_ROOT_SHADER_RESOURCE_VIEW_INVALID 篁� �D3D12_MESSAGE_ID_SET_ROOT_UNORDERED_ACCESS_VIEW_INVALID 蝰 �D3D12_MESSAGE_ID_SET_VERTEX_BUFFERS_INVALID_DESC � �D3D12_MESSAGE_ID_SET_INDEX_BUFFER_INVALID_DESC 篁� �D3D12_MESSAGE_ID_SET_STREAM_OUTPUT_BUFFERS_INVALID_DESC 蝰 �D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDDIMENSIONALITY 篁� �D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDLAYOUT 篁� �D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDIMENSIONALITY  �D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDALIGNMENT � �D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDMIPLEVELS � �D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDSAMPLEDESC  �D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDLAYOUT  �D3D12_MESSAGE_ID_SET_INDEX_BUFFER_INVALID  �D3D12_MESSAGE_ID_SET_VERTEX_BUFFERS_INVALID 蝰 �D3D12_MESSAGE_ID_SET_STREAM_OUTPUT_BUFFERS_INVALID 篁� �D3D12_MESSAGE_ID_SET_RENDER_TARGETS_INVALID 蝰 �D3D12_MESSAGE_ID_CREATEQUERY_HEAP_INVALID_PARAMETERS � �D3D12_MESSAGE_ID_BEGIN_END_QUERY_INVALID_PARAMETERS 蝰 �D3D12_MESSAGE_ID_CLOSE_COMMAND_LIST_OPEN_QUERY 篁� �D3D12_MESSAGE_ID_RESOLVE_QUERY_DATA_INVALID_PARAMETERS 篁� �D3D12_MESSAGE_ID_SET_PREDICATION_INVALID_PARAMETERS 蝰 �D3D12_MESSAGE_ID_TIMESTAMPS_NOT_SUPPORTED  �D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDFORMAT 篁� �D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDFORMAT  �D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_INVALIDSUBRESOURCERANGE 篁� �D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_INVALIDBASEOFFSET � �D3D12_MESSAGE_ID_GETCOPYABLELAYOUT_INVALIDSUBRESOURCERANGE 篁� �D3D12_MESSAGE_ID_GETCOPYABLELAYOUT_INVALIDBASEOFFSET � �D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_HEAP 篁� �D3D12_MESSAGE_ID_CREATE_SAMPLER_INVALID 蝰 �D3D12_MESSAGE_ID_CREATECOMMANDSIGNATURE_INVALID 蝰 �D3D12_MESSAGE_ID_EXECUTE_INDIRECT_INVALID_PARAMETERS � �D3D12_MESSAGE_ID_GETGPUVIRTUALADDRESS_INVALID_RESOURCE_DIMENSION � /D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDCLEARVALUE  0D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDCLEARVALUEFORMAT � 1D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDCLEARVALUEFORMAT 蝰 2D3D12_MESSAGE_ID_CREATERESOURCE_CLEARVALUEDENORMFLUSH  4D3D12_MESSAGE_ID_CLEARRENDERTARGETVIEW_MISMATCHINGCLEARVALUE � 5D3D12_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_MISMATCHINGCLEARVALUE � 6D3D12_MESSAGE_ID_MAP_INVALIDHEAP � 7D3D12_MESSAGE_ID_UNMAP_INVALIDHEAP 篁� 8D3D12_MESSAGE_ID_MAP_INVALIDRESOURCE � 9D3D12_MESSAGE_ID_UNMAP_INVALIDRESOURCE 篁� :D3D12_MESSAGE_ID_MAP_INVALIDSUBRESOURCE 蝰 ;D3D12_MESSAGE_ID_UNMAP_INVALIDSUBRESOURCE  <D3D12_MESSAGE_ID_MAP_INVALIDRANGE  =D3D12_MESSAGE_ID_UNMAP_INVALIDRANGE 蝰 @D3D12_MESSAGE_ID_MAP_INVALIDDATAPOINTER 蝰 AD3D12_MESSAGE_ID_MAP_INVALIDARG_RETURN 篁� BD3D12_MESSAGE_ID_MAP_OUTOFMEMORY_RETURN 蝰 CD3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_BUNDLENOTSUPPORTED 蝰 DD3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_COMMANDLISTMISMATCH � ED3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_OPENCOMMANDLIST � FD3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_FAILEDCOMMANDLIST 篁� GD3D12_MESSAGE_ID_COPYBUFFERREGION_NULLDST  HD3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDDSTRESOURCEDIMENSION  ID3D12_MESSAGE_ID_COPYBUFFERREGION_DSTRANGEOUTOFBOUNDS  JD3D12_MESSAGE_ID_COPYBUFFERREGION_NULLSRC  KD3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDSRCRESOURCEDIMENSION  LD3D12_MESSAGE_ID_COPYBUFFERREGION_SRCRANGEOUTOFBOUNDS  MD3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDCOPYFLAGS 篁� ND3D12_MESSAGE_ID_COPYTEXTUREREGION_NULLDST 篁� OD3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDDSTTYPE 篁� PD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTRESOURCEDIMENSION 篁� QD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTRESOURCE  RD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTSUBRESOURCE � SD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTOFFSET 蝰 TD3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDDSTFORMAT � UD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTFORMAT 蝰 VD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTDIMENSIONS 蝰 WD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTROWPITCH  XD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTPLACEMENT 篁� YD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTDSPLACEDFOOTPRINTFORMAT � ZD3D12_MESSAGE_ID_COPYTEXTUREREGION_DSTREGIONOUTOFBOUNDS 蝰 [D3D12_MESSAGE_ID_COPYTEXTUREREGION_NULLSRC 篁� \D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDSRCTYPE 篁� ]D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCRESOURCEDIMENSION 篁� ^D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCRESOURCE  _D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCSUBRESOURCE � `D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCOFFSET 蝰 aD3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDSRCFORMAT � bD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCFORMAT 蝰 cD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCDIMENSIONS 蝰 dD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCROWPITCH  eD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCPLACEMENT 篁� fD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCDSPLACEDFOOTPRINTFORMAT � gD3D12_MESSAGE_ID_COPYTEXTUREREGION_SRCREGIONOUTOFBOUNDS 蝰 hD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTCOORDINATES � iD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCBOX � jD3D12_MESSAGE_ID_COPYTEXTUREREGION_FORMATMISMATCH  kD3D12_MESSAGE_ID_COPYTEXTUREREGION_EMPTYBOX 蝰 lD3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDCOPYFLAGS 蝰 mD3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_SUBRESOURCE_INDEX  nD3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_FORMAT 篁� oD3D12_MESSAGE_ID_RESOLVESUBRESOURCE_RESOURCE_MISMATCH  pD3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_SAMPLE_COUNT � qD3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_INVALID_SHADER 篁� rD3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_CS_ROOT_SIGNATURE_MISMATCH 篁� sD3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_MISSING_ROOT_SIGNATURE 篁� tD3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALIDCACHEDBLOB 篁� uD3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBADAPTERMISMATCH 篁� vD3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBDRIVERVERSIONMISMATCH � wD3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBDESCMISMATCH 蝰 xD3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBIGNORED 篁� yD3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDHEAP 蝰 zD3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDRESOURCE 蝰 {D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDBOX 篁� |D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDSUBRESOURCE 篁� }D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_EMPTYBOX � ~D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDHEAP � D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDRESOURCE � �D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDBOX 蝰 �D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDSUBRESOURCE 蝰 �D3D12_MESSAGE_ID_READFROMSUBRESOURCE_EMPTYBOX  �D3D12_MESSAGE_ID_TOO_MANY_NODES_SPECIFIED  �D3D12_MESSAGE_ID_INVALID_NODE_INDEX 蝰 �D3D12_MESSAGE_ID_GETHEAPPROPERTIES_INVALIDRESOURCE 篁� �D3D12_MESSAGE_ID_NODE_MASK_MISMATCH 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_OUTOFMEMORY  �D3D12_MESSAGE_ID_COMMAND_LIST_MULTIPLE_SWAPCHAIN_BUFFER_REFERENCES 篁� �D3D12_MESSAGE_ID_COMMAND_LIST_TOO_MANY_SWAPCHAIN_REFERENCES 蝰 �D3D12_MESSAGE_ID_COMMAND_QUEUE_TOO_MANY_SWAPCHAIN_REFERENCES � �D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_WRONGSWAPCHAINBUFFERREFERENCE 篁� �D3D12_MESSAGE_ID_COMMAND_LIST_SETRENDERTARGETS_INVALIDNUMRENDERTARGETS 篁� �D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_TYPE 篁� �D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_FLAGS 蝰 �D3D12_MESSAGE_ID_CREATESHAREDRESOURCE_INVALIDFLAGS 篁� �D3D12_MESSAGE_ID_CREATESHAREDRESOURCE_INVALIDFORMAT 蝰 �D3D12_MESSAGE_ID_CREATESHAREDHEAP_INVALIDFLAGS 篁� �D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_UNRECOGNIZEDPROPERTIES 蝰 �D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_INVALIDSIZE � �D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_INVALIDOBJECT 篁� �D3D12_MESSAGE_ID_KEYEDMUTEX_INVALIDOBJECT  �D3D12_MESSAGE_ID_KEYEDMUTEX_INVALIDKEY 篁� �D3D12_MESSAGE_ID_KEYEDMUTEX_WRONGSTATE 篁� �D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_PRIORITY 篁� �D3D12_MESSAGE_ID_OBJECT_DELETED_WHILE_STILL_IN_USE 篁� �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALID_FLAGS 篁� �D3D12_MESSAGE_ID_HEAP_ADDRESS_RANGE_HAS_NO_RESOURCE 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_RENDER_TARGET_DELETED � �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_ALL_RENDER_TARGETS_HAVE_UNKNOWN_FORMAT 蝰 �D3D12_MESSAGE_ID_HEAP_ADDRESS_RANGE_INTERSECTS_MULTIPLE_BUFFERS 蝰 �D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_GPU_WRITTEN_READBACK_RESOURCE_MAPPED  �D3D12_MESSAGE_ID_UNMAP_RANGE_NOT_EMPTY 篁� �D3D12_MESSAGE_ID_MAP_INVALID_NULLRANGE 篁� �D3D12_MESSAGE_ID_UNMAP_INVALID_NULLRANGE � �D3D12_MESSAGE_ID_NO_GRAPHICS_API_SUPPORT � �D3D12_MESSAGE_ID_NO_COMPUTE_API_SUPPORT 蝰 �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_RESOURCE_FLAGS_NOT_SUPPORTED � �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_ROOT_ARGUMENT_UNINITIALIZED  �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_HEAP_INDEX_OUT_OF_BOUNDS  �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_TABLE_REGISTER_INDEX_OUT_OF_BOUNDS 蝰 �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_UNINITIALIZED 篁� �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_TYPE_MISMATCH 篁� �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_SRV_RESOURCE_DIMENSION_MISMATCH  �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_UAV_RESOURCE_DIMENSION_MISMATCH  �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INCOMPATIBLE_RESOURCE_STATE  �D3D12_MESSAGE_ID_COPYRESOURCE_NULLDST  �D3D12_MESSAGE_ID_COPYRESOURCE_INVALIDDSTRESOURCE � �D3D12_MESSAGE_ID_COPYRESOURCE_NULLSRC  �D3D12_MESSAGE_ID_COPYRESOURCE_INVALIDSRCRESOURCE � �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_NULLDST 蝰 �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALIDDSTRESOURCE 篁� �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_NULLSRC 蝰 �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALIDSRCRESOURCE 篁� �D3D12_MESSAGE_ID_PIPELINE_STATE_TYPE_MISMATCH  �D3D12_MESSAGE_ID_COMMAND_LIST_DISPATCH_ROOT_SIGNATURE_NOT_SET  �D3D12_MESSAGE_ID_COMMAND_LIST_DISPATCH_ROOT_SIGNATURE_MISMATCH 篁� �D3D12_MESSAGE_ID_RESOURCE_BARRIER_ZERO_BARRIERS 蝰 �D3D12_MESSAGE_ID_BEGIN_END_EVENT_MISMATCH  �D3D12_MESSAGE_ID_RESOURCE_BARRIER_POSSIBLE_BEFORE_AFTER_MISMATCH � �D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_BEGIN_END 蝰 �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INVALID_RESOURCE 篁� �D3D12_MESSAGE_ID_USE_OF_ZERO_REFCOUNT_OBJECT � �D3D12_MESSAGE_ID_OBJECT_EVICTED_WHILE_STILL_IN_USE 篁� �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_ROOT_DESCRIPTOR_ACCESS_OUT_OF_BOUNDS 篁� �D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_INVALIDLIBRARYBLOB  �D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_DRIVERVERSIONMISMATCH � �D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_ADAPTERVERSIONMISMATCH  �D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_UNSUPPORTED 篁� �D3D12_MESSAGE_ID_CREATE_PIPELINELIBRARY 蝰 �D3D12_MESSAGE_ID_LIVE_PIPELINELIBRARY  �D3D12_MESSAGE_ID_DESTROY_PIPELINELIBRARY � �D3D12_MESSAGE_ID_STOREPIPELINE_NONAME  �D3D12_MESSAGE_ID_STOREPIPELINE_DUPLICATENAME � �D3D12_MESSAGE_ID_LOADPIPELINE_NAMENOTFOUND 篁� �D3D12_MESSAGE_ID_LOADPIPELINE_INVALIDDESC  �D3D12_MESSAGE_ID_PIPELINELIBRARY_SERIALIZE_NOTENOUGHMEMORY 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_OUTPUT_RT_OUTPUT_MISMATCH  �D3D12_MESSAGE_ID_SETEVENTONMULTIPLEFENCECOMPLETION_INVALIDFLAGS 蝰 �D3D12_MESSAGE_ID_CREATE_QUEUE_VIDEO_NOT_SUPPORTED  �D3D12_MESSAGE_ID_CREATE_COMMAND_ALLOCATOR_VIDEO_NOT_SUPPORTED  �D3D12_MESSAGE_ID_CREATEQUERY_HEAP_VIDEO_DECODE_STATISTICS_NOT_SUPPORTED 蝰 �D3D12_MESSAGE_ID_CREATE_VIDEODECODECOMMANDLIST 篁� �D3D12_MESSAGE_ID_CREATE_VIDEODECODER � �D3D12_MESSAGE_ID_CREATE_VIDEODECODESTREAM  �D3D12_MESSAGE_ID_LIVE_VIDEODECODECOMMANDLIST � �D3D12_MESSAGE_ID_LIVE_VIDEODECODER 篁� �D3D12_MESSAGE_ID_LIVE_VIDEODECODESTREAM 蝰 �D3D12_MESSAGE_ID_DESTROY_VIDEODECODECOMMANDLIST 蝰 �D3D12_MESSAGE_ID_DESTROY_VIDEODECODER  �D3D12_MESSAGE_ID_DESTROY_VIDEODECODESTREAM 篁� �D3D12_MESSAGE_ID_DECODE_FRAME_INVALID_PARAMETERS � �D3D12_MESSAGE_ID_DEPRECATED_API 蝰 �D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_COMMAND_LIST_TYPE 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_DESCRIPTOR_TABLE_NOT_SET 篁� �D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_CONSTANT_BUFFER_VIEW_NOT_SET 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_SHADER_RESOURCE_VIEW_NOT_SET 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_UNORDERED_ACCESS_VIEW_NOT_SET � �D3D12_MESSAGE_ID_DISCARD_INVALID_SUBRESOURCE_RANGE 篁� �D3D12_MESSAGE_ID_DISCARD_ONE_SUBRESOURCE_FOR_MIPS_WITH_RECTS � �D3D12_MESSAGE_ID_DISCARD_NO_RECTS_FOR_NON_TEXTURE2D 蝰 �D3D12_MESSAGE_ID_COPY_ON_SAME_SUBRESOURCE  �D3D12_MESSAGE_ID_SETRESIDENCYPRIORITY_INVALID_PAGEABLE 篁� �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_UNSUPPORTED  �D3D12_MESSAGE_ID_STATIC_DESCRIPTOR_INVALID_DESCRIPTOR_CHANGE � �D3D12_MESSAGE_ID_DATA_STATIC_DESCRIPTOR_INVALID_DATA_CHANGE 蝰 �D3D12_MESSAGE_ID_DATA_STATIC_WHILE_SET_AT_EXECUTE_DESCRIPTOR_INVALID_DATA_CHANGE � �D3D12_MESSAGE_ID_EXECUTE_BUNDLE_STATIC_DESCRIPTOR_DATA_STATIC_NOT_SET  �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_RESOURCE_ACCESS_OUT_OF_BOUNDS 蝰 �D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_SAMPLER_MODE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATE_FENCE_INVALID_FLAGS 蝰 �D3D12_MESSAGE_ID_RESOURCE_BARRIER_DUPLICATE_SUBRESOURCE_TRANSITIONS 蝰 �D3D12_MESSAGE_ID_SETRESIDENCYPRIORITY_INVALID_PRIORITY 篁� �D3D12_MESSAGE_ID_CREATE_DESCRIPTOR_HEAP_LARGE_NUM_DESCRIPTORS  �D3D12_MESSAGE_ID_BEGIN_EVENT � �D3D12_MESSAGE_ID_END_EVENT 篁� �D3D12_MESSAGE_ID_CREATEDEVICE_DEBUG_LAYER_STARTUP_OPTIONS  �D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_DEPTHBOUNDSTEST_UNSUPPORTED � �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_DUPLICATE_SUBOBJECT � �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_UNKNOWN_SUBOBJECT 篁� �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_ZERO_SIZE_STREAM  �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALID_STREAM 蝰 �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CANNOT_DEDUCE_TYPE 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_STATIC_DESCRIPTOR_RESOURCE_DIMENSION_MISMATCH 蝰  D3D12_MESSAGE_ID_CREATE_COMMAND_QUEUE_INSUFFICIENT_PRIVILEGE_FOR_GLOBAL_REALTIME � D3D12_MESSAGE_ID_CREATE_COMMAND_QUEUE_INSUFFICIENT_HARDWARE_SUPPORT_FOR_GLOBAL_REALTIME 蝰 D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_ARCHITECTURE 篁� D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DST 篁� D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DST_RESOURCE_DIMENSION � D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DST_RANGE_OUT_OF_BOUNDS  D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_SRC 篁� D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_SRC_RESOURCE_DIMENSION � D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_SRC_RANGE_OUT_OF_BOUNDS  	D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_OFFSET_ALIGNMENT 篁� 
D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DEPENDENT_RESOURCES 篁� D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DEPENDENT_SUBRESOURCE_RANGES 蝰 D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DEPENDENT_RESOURCE � 
D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DEPENDENT_SUBRESOURCE_RANGE  D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DEPENDENT_SUBRESOURCE_OUT_OF_BOUNDS  D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DEPENDENT_RANGE_OUT_OF_BOUNDS 蝰 D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_ZERO_DEPENDENCIES 蝰 D3D12_MESSAGE_ID_DEVICE_CREATE_SHARED_HANDLE_INVALIDARG 蝰 D3D12_MESSAGE_ID_DESCRIPTOR_HANDLE_WITH_INVALID_RESOURCE � D3D12_MESSAGE_ID_SETDEPTHBOUNDS_INVALIDARGS 蝰 D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_RESOURCE_STATE_IMPRECISE 篁� D3D12_MESSAGE_ID_COMMAND_LIST_PIPELINE_STATE_NOT_SET � D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_MODEL_MISMATCH 篁� D3D12_MESSAGE_ID_OBJECT_ACCESSED_WHILE_STILL_IN_USE 蝰 D3D12_MESSAGE_ID_PROGRAMMABLE_MSAA_UNSUPPORTED 篁� D3D12_MESSAGE_ID_SETSAMPLEPOSITIONS_INVALIDARGS 蝰 D3D12_MESSAGE_ID_RESOLVESUBRESOURCEREGION_INVALID_RECT 篁� D3D12_MESSAGE_ID_CREATE_VIDEODECODECOMMANDQUEUE 蝰 D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSCOMMANDLIST 蝰 D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSCOMMANDQUEUE � D3D12_MESSAGE_ID_LIVE_VIDEODECODECOMMANDQUEUE  D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSCOMMANDLIST   D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSCOMMANDQUEUE 篁� !D3D12_MESSAGE_ID_DESTROY_VIDEODECODECOMMANDQUEUE � "D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSCOMMANDLIST � #D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSCOMMANDQUEUE  $D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSOR 篁� %D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSSTREAM 篁� &D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSOR � 'D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSSTREAM � (D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSOR 蝰 )D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSSTREAM 蝰 *D3D12_MESSAGE_ID_PROCESS_FRAME_INVALID_PARAMETERS  +D3D12_MESSAGE_ID_COPY_INVALIDLAYOUT 蝰 ,D3D12_MESSAGE_ID_CREATE_CRYPTO_SESSION 篁� -D3D12_MESSAGE_ID_CREATE_CRYPTO_SESSION_POLICY  .D3D12_MESSAGE_ID_CREATE_PROTECTED_RESOURCE_SESSION 篁� /D3D12_MESSAGE_ID_LIVE_CRYPTO_SESSION � 0D3D12_MESSAGE_ID_LIVE_CRYPTO_SESSION_POLICY 蝰 1D3D12_MESSAGE_ID_LIVE_PROTECTED_RESOURCE_SESSION � 2D3D12_MESSAGE_ID_DESTROY_CRYPTO_SESSION 蝰 3D3D12_MESSAGE_ID_DESTROY_CRYPTO_SESSION_POLICY 篁� 4D3D12_MESSAGE_ID_DESTROY_PROTECTED_RESOURCE_SESSION 蝰 5D3D12_MESSAGE_ID_PROTECTED_RESOURCE_SESSION_UNSUPPORTED 蝰 6D3D12_MESSAGE_ID_FENCE_INVALIDOPERATION 蝰 7D3D12_MESSAGE_ID_CREATEQUERY_HEAP_COPY_QUEUE_TIMESTAMPS_NOT_SUPPORTED  8D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_DEFERRED 篁� 9D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_RECORDTIME_ASSUMEDFROMFIRSTUSE � :D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_RECORDTIME_ASSUMEDFROMCLEAR  ;D3D12_MESSAGE_ID_CREATE_VIDEODECODERHEAP � <D3D12_MESSAGE_ID_LIVE_VIDEODECODERHEAP 篁� =D3D12_MESSAGE_ID_DESTROY_VIDEODECODERHEAP  >D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDARG_RETURN 蝰 ?D3D12_MESSAGE_ID_OPENEXISTINGHEAP_OUTOFMEMORY_RETURN � @D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDADDRESS � AD3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDHANDLE 蝰 BD3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_DEST 篁� CD3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_MODE 篁� DD3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_ALIGNMENT 蝰 ED3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_NOT_SUPPORTED 蝰 FD3D12_MESSAGE_ID_SETVIEWINSTANCEMASK_INVALIDARGS � GD3D12_MESSAGE_ID_VIEW_INSTANCING_UNSUPPORTED � HD3D12_MESSAGE_ID_VIEW_INSTANCING_INVALIDARGS � ID3D12_MESSAGE_ID_COPYTEXTUREREGION_MISMATCH_DECODE_REFERENCE_ONLY_FLAG 篁� JD3D12_MESSAGE_ID_COPYRESOURCE_MISMATCH_DECODE_REFERENCE_ONLY_FLAG  KD3D12_MESSAGE_ID_CREATE_VIDEO_DECODE_HEAP_CAPS_FAILURE 篁� LD3D12_MESSAGE_ID_CREATE_VIDEO_DECODE_HEAP_CAPS_UNSUPPORTED 篁� MD3D12_MESSAGE_ID_VIDEO_DECODE_SUPPORT_INVALID_INPUT 蝰 ND3D12_MESSAGE_ID_CREATE_VIDEO_DECODER_UNSUPPORTED  OD3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_METADATA_ERROR 蝰 PD3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VIEW_INSTANCING_VERTEX_SIZE_EXCEEDED  QD3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RUNTIME_INTERNAL_ERROR 蝰 RD3D12_MESSAGE_ID_NO_VIDEO_API_SUPPORT  SD3D12_MESSAGE_ID_VIDEO_PROCESS_SUPPORT_INVALID_INPUT � TD3D12_MESSAGE_ID_CREATE_VIDEO_PROCESSOR_CAPS_FAILURE � UD3D12_MESSAGE_ID_VIDEO_PROCESS_SUPPORT_UNSUPPORTED_FORMAT  VD3D12_MESSAGE_ID_VIDEO_DECODE_FRAME_INVALID_ARGUMENT � WD3D12_MESSAGE_ID_ENQUEUE_MAKE_RESIDENT_INVALID_FLAGS � XD3D12_MESSAGE_ID_OPENEXISTINGHEAP_UNSUPPORTED  YD3D12_MESSAGE_ID_VIDEO_PROCESS_FRAMES_INVALID_ARGUMENT 篁� ZD3D12_MESSAGE_ID_VIDEO_DECODE_SUPPORT_UNSUPPORTED  [D3D12_MESSAGE_ID_CREATE_COMMANDRECORDER 蝰 \D3D12_MESSAGE_ID_LIVE_COMMANDRECORDER  ]D3D12_MESSAGE_ID_DESTROY_COMMANDRECORDER � ^D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_VIDEO_NOT_SUPPORTED � _D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_INVALID_SUPPORT_FLAGS 篁� `D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_INVALID_FLAGS 篁� aD3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_MORE_RECORDERS_THAN_LOGICAL_PROCESSORS 蝰 bD3D12_MESSAGE_ID_CREATE_COMMANDPOOL 蝰 cD3D12_MESSAGE_ID_LIVE_COMMANDPOOL  dD3D12_MESSAGE_ID_DESTROY_COMMANDPOOL � eD3D12_MESSAGE_ID_CREATE_COMMAND_POOL_INVALID_FLAGS 篁� fD3D12_MESSAGE_ID_CREATE_COMMAND_LIST_VIDEO_NOT_SUPPORTED � gD3D12_MESSAGE_ID_COMMAND_RECORDER_SUPPORT_FLAGS_MISMATCH � hD3D12_MESSAGE_ID_COMMAND_RECORDER_CONTENTION � iD3D12_MESSAGE_ID_COMMAND_RECORDER_USAGE_WITH_CREATECOMMANDLIST_COMMAND_LIST 蝰 jD3D12_MESSAGE_ID_COMMAND_ALLOCATOR_USAGE_WITH_CREATECOMMANDLIST1_COMMAND_LIST  kD3D12_MESSAGE_ID_CANNOT_EXECUTE_EMPTY_COMMAND_LIST 篁� lD3D12_MESSAGE_ID_CANNOT_RESET_COMMAND_POOL_WITH_OPEN_COMMAND_LISTS 篁� mD3D12_MESSAGE_ID_CANNOT_USE_COMMAND_RECORDER_WITHOUT_CURRENT_TARGET 蝰 nD3D12_MESSAGE_ID_CANNOT_CHANGE_COMMAND_RECORDER_TARGET_WHILE_RECORDING 篁� oD3D12_MESSAGE_ID_COMMAND_POOL_SYNC 篁� pD3D12_MESSAGE_ID_EVICT_UNDERFLOW � qD3D12_MESSAGE_ID_CREATE_META_COMMAND � rD3D12_MESSAGE_ID_LIVE_META_COMMAND 篁� sD3D12_MESSAGE_ID_DESTROY_META_COMMAND  tD3D12_MESSAGE_ID_COPYBUFFERREGION_INVALID_DST_RESOURCE 篁� uD3D12_MESSAGE_ID_COPYBUFFERREGION_INVALID_SRC_RESOURCE 篁� vD3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DST_RESOURCE 篁� wD3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_SRC_RESOURCE 篁� xD3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_NULL_BUFFER  yD3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_NULL_RESOURCE_DESC � zD3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_UNSUPPORTED  {D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_DIMENSION 篁� |D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_FLAGS 篁� }D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_OFFSET 蝰 ~D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_RESOURCE_DIMENSION � D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_RESOURCE_FLAGS � �D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_OUTOFMEMORY_RETURN � �D3D12_MESSAGE_ID_CANNOT_CREATE_GRAPHICS_AND_VIDEO_COMMAND_RECORDER 篁� �D3D12_MESSAGE_ID_UPDATETILEMAPPINGS_POSSIBLY_MISMATCHING_PROPERTIES 蝰 �D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_INVALID_COMMAND_LIST_TYPE 篁� �D3D12_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_INCOMPATIBLE_WITH_STRUCTURED_BUFFERS 篁� �D3D12_MESSAGE_ID_COMPUTE_ONLY_DEVICE_OPERATION_UNSUPPORTED 篁� �D3D12_MESSAGE_ID_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INVALID � �D3D12_MESSAGE_ID_EMIT_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_INVALID 篁� �D3D12_MESSAGE_ID_COPY_RAYTRACING_ACCELERATION_STRUCTURE_INVALID 蝰 �D3D12_MESSAGE_ID_DISPATCH_RAYS_INVALID 篁� �D3D12_MESSAGE_ID_GET_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO_INVALID � �D3D12_MESSAGE_ID_CREATE_LIFETIMETRACKER 蝰 �D3D12_MESSAGE_ID_LIVE_LIFETIMETRACKER  �D3D12_MESSAGE_ID_DESTROY_LIFETIMETRACKER � �D3D12_MESSAGE_ID_DESTROYOWNEDOBJECT_OBJECTNOTOWNED 篁� �D3D12_MESSAGE_ID_CREATE_TRACKEDWORKLOAD 蝰 �D3D12_MESSAGE_ID_LIVE_TRACKEDWORKLOAD  �D3D12_MESSAGE_ID_DESTROY_TRACKEDWORKLOAD � �D3D12_MESSAGE_ID_RENDER_PASS_ERROR 篁� �D3D12_MESSAGE_ID_META_COMMAND_ID_INVALID � �D3D12_MESSAGE_ID_META_COMMAND_UNSUPPORTED_PARAMS � �D3D12_MESSAGE_ID_META_COMMAND_FAILED_ENUMERATION � �D3D12_MESSAGE_ID_META_COMMAND_PARAMETER_SIZE_MISMATCH  �D3D12_MESSAGE_ID_UNINITIALIZED_META_COMMAND 蝰 �D3D12_MESSAGE_ID_META_COMMAND_INVALID_GPU_VIRTUAL_ADDRESS  �D3D12_MESSAGE_ID_CREATE_VIDEOENCODECOMMANDLIST 篁� �D3D12_MESSAGE_ID_LIVE_VIDEOENCODECOMMANDLIST � �D3D12_MESSAGE_ID_DESTROY_VIDEOENCODECOMMANDLIST 蝰 �D3D12_MESSAGE_ID_CREATE_VIDEOENCODECOMMANDQUEUE 蝰 �D3D12_MESSAGE_ID_LIVE_VIDEOENCODECOMMANDQUEUE  �D3D12_MESSAGE_ID_DESTROY_VIDEOENCODECOMMANDQUEUE � �D3D12_MESSAGE_ID_CREATE_VIDEOMOTIONESTIMATOR � �D3D12_MESSAGE_ID_LIVE_VIDEOMOTIONESTIMATOR 篁� �D3D12_MESSAGE_ID_DESTROY_VIDEOMOTIONESTIMATOR  �D3D12_MESSAGE_ID_CREATE_VIDEOMOTIONVECTORHEAP  �D3D12_MESSAGE_ID_LIVE_VIDEOMOTIONVECTORHEAP 蝰 �D3D12_MESSAGE_ID_DESTROY_VIDEOMOTIONVECTORHEAP 篁� �D3D12_MESSAGE_ID_MULTIPLE_TRACKED_WORKLOADS 蝰 �D3D12_MESSAGE_ID_MULTIPLE_TRACKED_WORKLOAD_PAIRS � �D3D12_MESSAGE_ID_OUT_OF_ORDER_TRACKED_WORKLOAD_PAIR 蝰 �D3D12_MESSAGE_ID_CANNOT_ADD_TRACKED_WORKLOAD � �D3D12_MESSAGE_ID_INCOMPLETE_TRACKED_WORKLOAD_PAIR  �D3D12_MESSAGE_ID_CREATE_STATE_OBJECT_ERROR 篁� �D3D12_MESSAGE_ID_GET_SHADER_IDENTIFIER_ERROR � �D3D12_MESSAGE_ID_GET_SHADER_STACK_SIZE_ERROR � �D3D12_MESSAGE_ID_GET_PIPELINE_STACK_SIZE_ERROR 篁� �D3D12_MESSAGE_ID_SET_PIPELINE_STACK_SIZE_ERROR 篁� �D3D12_MESSAGE_ID_GET_SHADER_IDENTIFIER_SIZE_INVALID 蝰 �D3D12_MESSAGE_ID_CHECK_DRIVER_MATCHING_IDENTIFIER_INVALID  �D3D12_MESSAGE_ID_CHECK_DRIVER_MATCHING_IDENTIFIER_DRIVER_REPORTED_ISSUE 蝰 �D3D12_MESSAGE_ID_RENDER_PASS_INVALID_RESOURCE_BARRIER  �D3D12_MESSAGE_ID_RENDER_PASS_DISALLOWED_API_CALLED 篁� �D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_NEST_RENDER_PASSES 篁� �D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_END_WITHOUT_BEGIN  �D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_CLOSE_COMMAND_LIST 篁� �D3D12_MESSAGE_ID_RENDER_PASS_GPU_WORK_WHILE_SUSPENDED  �D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_SUSPEND_RESUME 蝰 �D3D12_MESSAGE_ID_RENDER_PASS_NO_PRIOR_SUSPEND_WITHIN_EXECUTECOMMANDLISTS � �D3D12_MESSAGE_ID_RENDER_PASS_NO_SUBSEQUENT_RESUME_WITHIN_EXECUTECOMMANDLISTS � �D3D12_MESSAGE_ID_TRACKED_WORKLOAD_COMMAND_QUEUE_MISMATCH � �D3D12_MESSAGE_ID_TRACKED_WORKLOAD_NOT_SUPPORTED 蝰 �D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_NO_ACCESS 篁� �D3D12_MESSAGE_ID_RENDER_PASS_UNSUPPORTED_RESOLVE � �D3D12_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_INVALID_RESOURCE_PTR 篁� �D3D12_MESSAGE_ID_WINDOWS7_FENCE_OUTOFORDER_SIGNAL  �D3D12_MESSAGE_ID_WINDOWS7_FENCE_OUTOFORDER_WAIT 蝰 �D3D12_MESSAGE_ID_VIDEO_CREATE_MOTION_ESTIMATOR_INVALID_ARGUMENT 蝰 �D3D12_MESSAGE_ID_VIDEO_CREATE_MOTION_VECTOR_HEAP_INVALID_ARGUMENT  �D3D12_MESSAGE_ID_ESTIMATE_MOTION_INVALID_ARGUMENT  �D3D12_MESSAGE_ID_RESOLVE_MOTION_VECTOR_HEAP_INVALID_ARGUMENT � �D3D12_MESSAGE_ID_GETGPUVIRTUALADDRESS_INVALID_HEAP_TYPE 蝰 �D3D12_MESSAGE_ID_SET_BACKGROUND_PROCESSING_MODE_INVALID_ARGUMENT � �D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_INVALID_COMMAND_LIST_TYPE_FOR_FEATURE_LEVEL � �D3D12_MESSAGE_ID_CREATE_VIDEOEXTENSIONCOMMAND  �D3D12_MESSAGE_ID_LIVE_VIDEOEXTENSIONCOMMAND 蝰 �D3D12_MESSAGE_ID_DESTROY_VIDEOEXTENSIONCOMMAND 篁� �D3D12_MESSAGE_ID_INVALID_VIDEO_EXTENSION_COMMAND_ID 蝰 �D3D12_MESSAGE_ID_VIDEO_EXTENSION_COMMAND_INVALID_ARGUMENT  �D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_NOT_UNIQUE_IN_DXIL_LIBRARY  �D3D12_MESSAGE_ID_VARIABLE_SHADING_RATE_NOT_ALLOWED_WITH_TIR 蝰 �D3D12_MESSAGE_ID_GEOMETRY_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE 篁� �D3D12_MESSAGE_ID_RSSETSHADING_RATE_INVALID_SHADING_RATE 蝰 �D3D12_MESSAGE_ID_RSSETSHADING_RATE_SHADING_RATE_NOT_PERMITTED_BY_CAP � �D3D12_MESSAGE_ID_RSSETSHADING_RATE_INVALID_COMBINER 蝰 �D3D12_MESSAGE_ID_RSSETSHADINGRATEIMAGE_REQUIRES_TIER_2 篁� �D3D12_MESSAGE_ID_RSSETSHADINGRATE_REQUIRES_TIER_1  �D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_FORMAT � �D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_ARRAY_SIZE � �D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_MIP_LEVEL 蝰 �D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_SAMPLE_COUNT 篁� �D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_SAMPLE_QUALITY � �D3D12_MESSAGE_ID_NON_RETAIL_SHADER_MODEL_WONT_VALIDATE 篁� �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_AS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_ROOT_SIGNATURE_MISMATCH 蝰 �D3D12_MESSAGE_ID_ADD_TO_STATE_OBJECT_ERROR 篁� �D3D12_MESSAGE_ID_CREATE_PROTECTED_RESOURCE_SESSION_INVALID_ARGUMENT 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_PSO_DESC_MISMATCH  �D3D12_MESSAGE_ID_CREATEPIPELINESTATE_MS_INCOMPLETE_TYPE 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_AS_NOT_MS_MISMATCH 蝰 �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_NOT_PS_MISMATCH 蝰 �D3D12_MESSAGE_ID_NONZERO_SAMPLER_FEEDBACK_MIP_REGION_WITH_INCOMPATIBLE_FORMAT  �D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INPUTLAYOUT_SHADER_MISMATCH � �D3D12_MESSAGE_ID_EMPTY_DISPATCH 蝰 �D3D12_MESSAGE_ID_RESOURCE_FORMAT_REQUIRES_SAMPLER_FEEDBACK_CAPABILITY  �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_MIP_REGION � �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_DIMENSION 蝰 �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_SAMPLE_COUNT 篁� �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_SAMPLE_QUALITY � �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_LAYOUT � �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_REQUIRES_UNORDERED_ACCESS_FLAG � �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_NULL_ARGUMENTS 蝰 �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_UAV_REQUIRES_SAMPLER_FEEDBACK_CAPABILITY 篁� �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_REQUIRES_FEEDBACK_MAP_FORMAT  �D3D12_MESSAGE_ID_CREATEMESHSHADER_INVALIDSHADERBYTECODE 蝰 �D3D12_MESSAGE_ID_CREATEMESHSHADER_OUTOFMEMORY  �D3D12_MESSAGE_ID_CREATEMESHSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE 蝰 �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_TRANSCODE_INVALID_FORMAT  �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_INVALID_MIP_LEVEL_COUNT � �D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_TRANSCODE_ARRAY_SIZE_MISMATCH 篁� �D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_MISMATCHING_TARGETED_RESOURCE 篁� �D3D12_MESSAGE_ID_CREATEMESHSHADER_OUTPUTEXCEEDSMAXSIZE 篁� �D3D12_MESSAGE_ID_CREATEMESHSHADER_GROUPSHAREDEXCEEDSMAXSIZE 蝰 �D3D12_MESSAGE_ID_VERTEX_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE � �D3D12_MESSAGE_ID_MESH_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE 篁� �D3D12_MESSAGE_ID_CREATEMESHSHADER_MISMATCHEDASMSPAYLOADSIZE 蝰 �D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_UNBOUNDED_STATIC_DESCRIPTORS 蝰 �D3D12_MESSAGE_ID_CREATEAMPLIFICATIONSHADER_INVALIDSHADERBYTECODE � �D3D12_MESSAGE_ID_CREATEAMPLIFICATIONSHADER_OUTOFMEMORY 篁�  D3D12_MESSAGE_ID_CREATE_SHADERCACHESESSION 篁� D3D12_MESSAGE_ID_LIVE_SHADERCACHESESSION � D3D12_MESSAGE_ID_DESTROY_SHADERCACHESESSION 蝰 D3D12_MESSAGE_ID_CREATESHADERCACHESESSION_INVALIDARGS  D3D12_MESSAGE_ID_CREATESHADERCACHESESSION_DISABLED 篁� D3D12_MESSAGE_ID_CREATESHADERCACHESESSION_ALREADYOPEN  D3D12_MESSAGE_ID_SHADERCACHECONTROL_DEVELOPERMODE  D3D12_MESSAGE_ID_SHADERCACHECONTROL_INVALIDFLAGS � D3D12_MESSAGE_ID_SHADERCACHECONTROL_STATEALREADYSET 蝰 	D3D12_MESSAGE_ID_SHADERCACHECONTROL_IGNOREDFLAG 蝰 
D3D12_MESSAGE_ID_SHADERCACHESESSION_STOREVALUE_ALREADYPRESENT  D3D12_MESSAGE_ID_SHADERCACHESESSION_STOREVALUE_HASHCOLLISION � D3D12_MESSAGE_ID_SHADERCACHESESSION_STOREVALUE_CACHEFULL � 
D3D12_MESSAGE_ID_SHADERCACHESESSION_FINDVALUE_NOTFOUND 篁� D3D12_MESSAGE_ID_SHADERCACHESESSION_CORRUPT 蝰 D3D12_MESSAGE_ID_SHADERCACHESESSION_DISABLED � D3D12_MESSAGE_ID_OVERSIZED_DISPATCH 蝰 D3D12_MESSAGE_ID_CREATE_VIDEOENCODER � D3D12_MESSAGE_ID_LIVE_VIDEOENCODER 篁� D3D12_MESSAGE_ID_DESTROY_VIDEOENCODER  D3D12_MESSAGE_ID_CREATE_VIDEOENCODERHEAP � D3D12_MESSAGE_ID_LIVE_VIDEOENCODERHEAP 篁� D3D12_MESSAGE_ID_DESTROY_VIDEOENCODERHEAP  D3D12_MESSAGE_ID_COPYTEXTUREREGION_MISMATCH_ENCODE_REFERENCE_ONLY_FLAG 篁� D3D12_MESSAGE_ID_COPYRESOURCE_MISMATCH_ENCODE_REFERENCE_ONLY_FLAG  D3D12_MESSAGE_ID_ENCODE_FRAME_INVALID_PARAMETERS � D3D12_MESSAGE_ID_ENCODE_FRAME_UNSUPPORTED_PARAMETERS � D3D12_MESSAGE_ID_RESOLVE_ENCODER_OUTPUT_METADATA_INVALID_PARAMETERS 蝰 D3D12_MESSAGE_ID_RESOLVE_ENCODER_OUTPUT_METADATA_UNSUPPORTED_PARAMETERS 蝰 D3D12_MESSAGE_ID_CREATE_VIDEO_ENCODER_INVALID_PARAMETERS � D3D12_MESSAGE_ID_CREATE_VIDEO_ENCODER_UNSUPPORTED_PARAMETERS � D3D12_MESSAGE_ID_CREATE_VIDEO_ENCODER_HEAP_INVALID_PARAMETERS   D3D12_MESSAGE_ID_CREATE_VIDEO_ENCODER_HEAP_UNSUPPORTED_PARAMETERS  !D3D12_MESSAGE_ID_CREATECOMMANDLIST_NULL_COMMANDALLOCATOR � "D3D12_MESSAGE_ID_CLEAR_UNORDERED_ACCESS_VIEW_INVALID_DESCRIPTOR_HANDLE 篁� #D3D12_MESSAGE_ID_DESCRIPTOR_HEAP_NOT_SHADER_VISIBLE 蝰 $D3D12_MESSAGE_ID_CREATEBLENDSTATE_BLENDOP_WARNING  %D3D12_MESSAGE_ID_CREATEBLENDSTATE_BLENDOPALPHA_WARNING 篁� &D3D12_MESSAGE_ID_WRITE_COMBINE_PERFORMANCE_WARNING 篁� 'D3D12_MESSAGE_ID_RESOLVE_QUERY_INVALID_QUERY_STATE 篁� (D3D12_MESSAGE_ID_SETPRIVATEDATA_NO_ACCESS  )D3D12_MESSAGE_ID_COMMAND_LIST_STATIC_DESCRIPTOR_SAMPLER_MODE_MISMATCH  *D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_UNSUPPORTED_BUFFER_WIDTH 蝰 +D3D12_MESSAGE_ID_CREATEMESHSHADER_TOPOLOGY_MISMATCH 蝰 ,D3D12_MESSAGE_ID_VRS_SUM_COMBINER_REQUIRES_CAPABILITY  -D3D12_MESSAGE_ID_SETTING_SHADING_RATE_FROM_MS_REQUIRES_CAPABILITY  .D3D12_MESSAGE_ID_SHADERCACHESESSION_SHADERCACHEDELETE_NOTSUPPORTED 篁� /D3D12_MESSAGE_ID_SHADERCACHECONTROL_SHADERCACHECLEAR_NOTSUPPORTED  0D3D12_MESSAGE_ID_CREATERESOURCE_STATE_IGNORED  1D3D12_MESSAGE_ID_UNUSED_CROSS_EXECUTE_SPLIT_BARRIER 蝰 2D3D12_MESSAGE_ID_DEVICE_OPEN_SHARED_HANDLE_ACCESS_DENIED � 3D3D12_MESSAGE_ID_INCOMPATIBLE_BARRIER_VALUES � 4D3D12_MESSAGE_ID_INCOMPATIBLE_BARRIER_ACCESS � 5D3D12_MESSAGE_ID_INCOMPATIBLE_BARRIER_SYNC 篁� 6D3D12_MESSAGE_ID_INCOMPATIBLE_BARRIER_LAYOUT � 7D3D12_MESSAGE_ID_INCOMPATIBLE_BARRIER_TYPE 篁� 8D3D12_MESSAGE_ID_OUT_OF_BOUNDS_BARRIER_SUBRESOURCE_RANGE � 9D3D12_MESSAGE_ID_INCOMPATIBLE_BARRIER_RESOURCE_DIMENSION � :D3D12_MESSAGE_ID_SET_SCISSOR_RECTS_INVALID_RECT 蝰 ;D3D12_MESSAGE_ID_SHADING_RATE_SOURCE_REQUIRES_DIMENSION_TEXTURE2D  <D3D12_MESSAGE_ID_BUFFER_BARRIER_SUBREGION_OUT_OF_BOUNDS 蝰 =D3D12_MESSAGE_ID_UNSUPPORTED_BARRIER_LAYOUT 蝰 >D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALID_PARAMETERS  ?D3D12_MESSAGE_ID_ENHANCED_BARRIERS_NOT_SUPPORTED � BD3D12_MESSAGE_ID_LEGACY_BARRIER_VALIDATION_FORCED_ON � CD3D12_MESSAGE_ID_EMPTY_ROOT_DESCRIPTOR_TABLE � DD3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ELEMENT_OFFSET_UNALIGNED 蝰 ED3D12_MESSAGE_ID_ALPHA_BLEND_FACTOR_NOT_SUPPORTED  FD3D12_MESSAGE_ID_BARRIER_INTEROP_INVALID_LAYOUT 蝰 GD3D12_MESSAGE_ID_BARRIER_INTEROP_INVALID_STATE 篁� HD3D12_MESSAGE_ID_GRAPHICS_PIPELINE_STATE_DESC_ZERO_SAMPLE_MASK 篁� ID3D12_MESSAGE_ID_INDEPENDENT_STENCIL_REF_NOT_SUPPORTED 篁� JD3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INDEPENDENT_MASKS_UNSUPPORTED 篁� KD3D12_MESSAGE_ID_TEXTURE_BARRIER_SUBRESOURCES_OUT_OF_BOUNDS 蝰 LD3D12_MESSAGE_ID_NON_OPTIMAL_BARRIER_ONLY_EXECUTE_COMMAND_LISTS 蝰 MD3D12_MESSAGE_ID_EXECUTE_INDIRECT_ZERO_COMMAND_COUNT � ND3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INCOMPATIBLE_TEXTURE_LAYOUT  OD3D12_MESSAGE_ID_DYNAMIC_INDEX_BUFFER_STRIP_CUT_NOT_SUPPORTED  PD3D12_MESSAGE_ID_PRIMITIVE_TOPOLOGY_TRIANGLE_FANS_NOT_SUPPORTED 蝰 QD3D12_MESSAGE_ID_CREATE_SAMPLER_COMPARISON_FUNC_IGNORED 蝰 RD3D12_MESSAGE_ID_CREATEHEAP_INVALIDHEAPTYPE 蝰 SD3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPTYPE 篁� TD3D12_MESSAGE_ID_DYNAMIC_DEPTH_BIAS_NOT_SUPPORTED  UD3D12_MESSAGE_ID_CREATERASTERIZERSTATE_NON_WHOLE_DYNAMIC_DEPTH_BIAS 蝰 VD3D12_MESSAGE_ID_DYNAMIC_DEPTH_BIAS_FLAG_MISSING � WD3D12_MESSAGE_ID_DYNAMIC_DEPTH_BIAS_NO_PIPELINE 蝰 XD3D12_MESSAGE_ID_DYNAMIC_INDEX_BUFFER_STRIP_CUT_FLAG_MISSING � YD3D12_MESSAGE_ID_DYNAMIC_INDEX_BUFFER_STRIP_CUT_NO_PIPELINE 蝰 ZD3D12_MESSAGE_ID_NONNORMALIZED_COORDINATE_SAMPLING_NOT_SUPPORTED � [D3D12_MESSAGE_ID_INVALID_CAST_TARGET � \D3D12_MESSAGE_ID_RENDER_PASS_COMMANDLIST_INVALID_END_STATE 篁� ]D3D12_MESSAGE_ID_RENDER_PASS_COMMANDLIST_INVALID_START_STATE � ^D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_ACCESS 蝰 _D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_LOCAL_PRESERVE_PARAMETERS 篁� `D3D12_MESSAGE_ID_RENDER_PASS_LOCAL_PRESERVE_RENDER_PARAMETERS_ERROR 蝰 aD3D12_MESSAGE_ID_RENDER_PASS_LOCAL_DEPTH_STENCIL_ERROR 篁� bD3D12_MESSAGE_ID_DRAW_POTENTIALLY_OUTSIDE_OF_VALID_RENDER_AREA 篁� cD3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALID_LINERASTERIZATIONMODE � dD3D12_MESSAGE_ID_CREATERESOURCE_INVALIDALIGNMENT_SMALLRESOURCE 篁� eD3D12_MESSAGE_ID_GENERIC_DEVICE_OPERATION_UNSUPPORTED  fD3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RENDER_TARGET_WRONG_WRITE_MASK 蝰 gD3D12_MESSAGE_ID_PROBABLE_PIX_EVENT_LEAK � hD3D12_MESSAGE_ID_PIX_EVENT_UNDERFLOW � iD3D12_MESSAGE_ID_RECREATEAT_INVALID_TARGET 篁� jD3D12_MESSAGE_ID_RECREATEAT_INSUFFICIENT_SUPPORT � kD3D12_MESSAGE_ID_GPU_BASED_VALIDATION_STRUCTURED_BUFFER_STRIDE_MISMATCH 蝰 lD3D12_MESSAGE_ID_DISPATCH_GRAPH_INVALID 蝰 mD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_TARGET_FORMAT_INVALID � nD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_TARGET_DIMENSION_INVALID 蝰 oD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_SOURCE_COLOR_FORMAT_INVALID 篁� pD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_SOURCE_DEPTH_FORMAT_INVALID 篁� qD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_EXPOSURE_SCALE_FORMAT_INVALID � rD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_ENGINE_CREATE_FLAGS_INVALID 篁� sD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_EXTENSION_INTERNAL_LOAD_FAILURE 篁� tD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_EXTENSION_INTERNAL_ENGINE_CREATION_ERROR 蝰 uD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_EXTENSION_INTERNAL_UPSCALER_CREATION_ERROR  vD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_EXTENSION_INTERNAL_UPSCALER_EXECUTION_ERROR 篁� wD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_REGION_INVALID 篁� xD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_TIME_DELTA_INVALID 篁� yD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_REQUIRED_TEXTURE_IS_NULL � zD3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_MOTION_VECTORS_FORMAT_INVALID  {D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_FLAGS_INVALID  |D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_FORMAT_INVALID 篁� }D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_EXPOSURE_SCALE_TEXTURE_SIZE_INVALID 蝰 ~D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_VARIANT_INDEX_OUT_OF_BOUNDS 篁� D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_VARIANT_ID_NOT_FOUND 蝰 �D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_DUPLICATE_VARIANT_ID 蝰 �D3D12_MESSAGE_ID_DIRECTSR_OUT_OF_MEMORY 蝰 �D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_UNEXPECTED_TEXTURE_IS_IGNORED  �D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EVICT_UNDERFLOW 蝰 �D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_OPTIONAL_TEXTURE_IS_NULL � �D3D12_MESSAGE_ID_DIRECTSR_SUPERRES_UPSCALER_EXECUTE_INVALID_CAMERA_JITTER  �D3D12_MESSAGE_ID_CREATE_STATE_OBJECT_WARNING � �D3D12_MESSAGE_ID_GUID_TEXTURE_LAYOUT_UNSUPPORTED � �D3D12_MESSAGE_ID_RESOLVE_ENCODER_INPUT_PARAM_LAYOUT_INVALID_PARAMETERS 篁� �D3D12_MESSAGE_ID_INVALID_BARRIER_ACCESS 蝰 �D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INSTANCE_COUNT_ZERO 篁� �D3D12_MESSAGE_ID_DESCRIPTOR_HEAP_NOT_SET_BEFORE_ROOT_SIGNATURE_WITH_DIRECTLY_INDEXED_FLAG  �D3D12_MESSAGE_ID_DIFFERENT_DESCRIPTOR_HEAP_SET_AFTER_ROOT_SIGNATURE_WITH_DIRECTLY_INDEXED_FLAG 篁� �D3D12_MESSAGE_ID_APPLICATION_SPECIFIC_DRIVER_STATE_NOT_SUPPORTED � �D3D12_MESSAGE_ID_RENDER_TARGET_OR_DEPTH_STENCIL_RESOUCE_NOT_INITIALIZED 蝰 �D3D12_MESSAGE_ID_BYTECODE_VALIDATION_ERROR 篁� �D3D12_MESSAGE_ID_D3D12_MESSAGES_END 蝰: � t   7  D3D12_MESSAGE_ID .?AW4D3D12_MESSAGE_ID@@ 篁�* 
 "     cbSize 篁�
     pBlobData *   9           tagBLOB .?AUtagBLOB@@ �    D3D12_DESCRIPTOR_RANGE_TYPE_SRV 蝰  D3D12_DESCRIPTOR_RANGE_TYPE_UAV 蝰  D3D12_DESCRIPTOR_RANGE_TYPE_CBV 蝰  D3D12_DESCRIPTOR_RANGE_TYPE_SAMPLER 蝰N   t   ;  D3D12_DESCRIPTOR_RANGE_TYPE .?AW4D3D12_DESCRIPTOR_RANGE_TYPE@@ 馴   �              $_TypeDescriptor$_extraBytes_26 .?AU$_TypeDescriptor$_extraBytes_26@@  p   #     �6 
 "    pVFTable �
    spare 
 >   name 馴   ?          * $_TypeDescriptor$_extraBytes_26 .?AU$_TypeDescriptor$_extraBytes_26@@ 2   �           tagCALPWSTR .?AUtagCALPWSTR@@ �   �              __vcrt_va_list_is_reference<__crt_locale_pointers *> .?AU?$__vcrt_va_list_is_reference@PEAU__crt_locale_pointers@@@@ 窬  0   �  __vcrt_va_list_is_reference<__crt_locale_pointers *>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@PEAU__crt_locale_pointers@@@@ 篁�&   C  <unnamed-enum-__the_value> 駣  D           __vcrt_va_list_is_reference<__crt_locale_pointers *> .?AU?$__vcrt_va_list_is_reference@PEAU__crt_locale_pointers@@@@ 馸   �              _ULARGE_INTEGER::<unnamed-type-u> .?AU<unnamed-type-u>@_ULARGE_INTEGER@@ 駀 
 "     LowPart 蝰
 "    HighPart �  F  <unnamed-type-u> 篁�
 F    u 
 #     QuadPart �2  G   _ULARGE_INTEGER .?AT_ULARGE_INTEGER@@ * 
 "     cElems 篁�
 ?   pElems 篁�*   I           tagCACY .?AUtagCACY@@ * 
 "     cElems 篁�
 "   pElems 篁�*   K           tagCAUL .?AUtagCAUL@@ N N C   tagPROPVARIANT::<unnamed-tag> .?AT<unnamed-tag>@tagPROPVARIANT@@ 衤
 !     vt 篁�
 !    wReserved1 篁�
 !    wReserved2 篁�
 !    wReserved3 篁�
 p    cVal �
      bVal �
     iVal �
 !    uiVal 
     lVal �
 "    ulVal 
 t    intVal 篁�
 u    uintVal 蝰
    hVal �
    uhVal 
 @    fltVal 篁�
 A    dblVal 篁�
     boolVal 蝰
     __OBSOLETE__VARIANT_BOOL �
     scode 
    cyVal 
 A    date �
    filetime �
    puuid 
    pclipdata 
 q   bstrVal 蝰
    bstrblobVal 蝰
    blob �
 p   pszVal 篁�
 q   pwszVal 蝰
    punkVal 蝰
    pdispVal �
     pStream 蝰
 "   pStorage �
 $   pVersionedStream �
 &   parray 篁�
 '   cac 蝰
 (   caub �
 )   cai 蝰
 *   caui �
 +   cal 蝰
 ,   caul �
 -   cah 蝰
 .   cauh �
 /   caflt 
 0   cadbl 
 1   cabool 篁�
 2   cascode 蝰
 3   cacy �
 4   cadate 篁�
 5   cafiletime 篁�
 6   cauuid 篁�
 7   caclipdata 篁�
 8   cabstr 篁�
 9   cabstrblob 篁�
 :   calpstr 蝰
 ;   calpwstr �
 <   capropvar 
 p   pcVal 
     pbVal 
    piVal 
 !   puiVal 篁�
    plVal 
 "   pulVal 篁�
 t   pintVal 蝰
 u   puintVal �
 @   pfltVal 蝰
 A   pdblVal 蝰
    pboolVal �
 >   pdecVal 蝰
    pscode 篁�
 ?   pcyVal 篁�
 A   pdate 
 0   pbstrVal �
 @   ppunkVal �
 A   ppdispVal 
 B   pparray 蝰
    pvarVal 蝰f M N           tagPROPVARIANT::<unnamed-tag>::<unnamed-tag> .?AU<unnamed-tag>@0tagPROPVARIANT@@ 駄
 p     cVal �
       bVal �
      iVal �
 !     uiVal 
      lVal �
 "     ulVal 
 t     intVal 篁�
 u     uintVal 蝰
     hVal �
     uhVal 
 @     fltVal 篁�
 A     dblVal 篁�
      boolVal 蝰
      __OBSOLETE__VARIANT_BOOL �
      scode 
     cyVal 
 A     date �
     filetime �
     puuid 
     pclipdata 
 q    bstrVal 蝰
     bstrblobVal 蝰
     blob �
 p    pszVal 篁�
 q    pwszVal 蝰
     punkVal 蝰
     pdispVal �
      pStream 蝰
 "    pStorage �
 $    pVersionedStream �
 &    parray 篁�
 '    cac 蝰
 (    caub �
 )    cai 蝰
 *    caui �
 +    cal 蝰
 ,    caul �
 -    cah 蝰
 .    cauh �
 /    caflt 
 0    cadbl 
 1    cabool 篁�
 2    cascode 蝰
 3    cacy �
 4    cadate 篁�
 5    cafiletime 篁�
 6    cauuid 篁�
 7    caclipdata 篁�
 8    cabstr 篁�
 9    cabstrblob 篁�
 :    calpstr 蝰
 ;    calpwstr �
 <    capropvar 
 p    pcVal 
      pbVal 
     piVal 
 !    puiVal 篁�
     plVal 
 "    pulVal 篁�
 t    pintVal 蝰
 u    puintVal �
 @    pfltVal 蝰
 A    pdblVal 蝰
     pboolVal �
 >    pdecVal 蝰
     pscode 篁�
 ?    pcyVal 篁�
 A    pdate 
 0    pbstrVal �
 @    ppunkVal �
 A    ppdispVal 
 B    pparray 蝰
     pvarVal 蝰n I P   tagPROPVARIANT::<unnamed-tag>::<unnamed-tag>::<unnamed-tag> .?AT<unnamed-tag>@00tagPROPVARIANT@@ �2   �              tagTYPEATTR .?AUtagTYPEATTR@@ 
 R    
 S    
    T  
     	     V     U      .   �              ITypeComp .?AUITypeComp@@ 
 X    
 Y    
    Z   	     V     [      2   �              tagFUNCDESC .?AUtagFUNCDESC@@ 
 ]    
 ^        u   _   	     V     `      2   �              tagVARDESC .?AUtagVARDESC@@ 蝰
 b    
 c        u   d   	     V     e             0  u   u   	     V     g          u   "   	     V     i          u   t   	     V     k          0  u      	     V     m      "         !   �  F  �  u   	     V     o             0  0  "  0   	     V     q      j   INVOKE_FUNC 蝰  INVOKE_PROPERTYGET 篁�  INVOKE_PROPERTYPUT 篁�  INVOKE_PROPERTYPUTREF 2   t   s  tagINVOKEKIND .?AW4tagINVOKEKIND@@ �       t  0  0  !   	     V     u          "      	     V     w             t  �   	     V     y            I  �   	     V     {             0   	     V     }      .   �              ITypeLib .?AUITypeLib@@ 蝰
     
 �        �  u   	     V     �      
    S   	     V     �      
    ^   	     V     �      
    c   	     V     �      
   �  
    �   	     V    �      
    蝰
 �  ,  
    �   	     V    �       	     V              �    �    �  
   ,   	�    V     �       	�    V     �         �    �  :      蝰 W     GetTypeAttr  \      GetTypeComp  a  (   GetFuncDesc  f  0   GetVarDesc � h  8   GetNames 篁� j  @   GetRefTypeOfImplType 篁� l  H   GetImplTypeFlags 篁� n  P   GetIDsOfNames 蝰 p  X   Invoke � r  `   GetDocumentation 篁� v  h   GetDllEntry  x  p   GetRefTypeInfo � z  x   AddressOfMember  |  �   CreateInstance � ~  �   GetMops  �  �   GetContainingTypeLib 篁� �  �   ReleaseTypeAttr  �  �   ReleaseFuncDesc  �  �   ReleaseVarDesc � �  ITypeInfo 蝰 �  operator= 蝰 
  UUUUUUUUUUU篁�.  &�      �   ITypeInfo .?AUITypeInfo@@ N    URLZONEREG_DEFAULT 篁�  URLZONEREG_HKLM 蝰  URLZONEREG_HKCU 蝰.   t   �  _URLZONEREG .?AW4_URLZONEREG@@ 駌   D3D_SHADER_MODEL_NONE  Q D3D_SHADER_MODEL_5_1 � ` D3D_SHADER_MODEL_6_0 � a D3D_SHADER_MODEL_6_1 � b D3D_SHADER_MODEL_6_2 � c D3D_SHADER_MODEL_6_3 � d D3D_SHADER_MODEL_6_4 � e D3D_SHADER_MODEL_6_5 � f D3D_SHADER_MODEL_6_6 � g D3D_SHADER_MODEL_6_7 � h D3D_SHADER_MODEL_6_8 � i D3D_SHADER_MODEL_6_9 � i D3D_HIGHEST_SHADER_MODEL �: 
  t   �  D3D_SHADER_MODEL .?AW4D3D_SHADER_MODEL@@ 篁�2   PSU_DEFAULT 蝰  PSU_SECURITY_URL_ONLY 2   t   �  _tagPSUACTION .?AW4_tagPSUACTION@@ �*  COMIMAGE_FLAGS_ILONLY   COMIMAGE_FLAGS_32BITREQUIRED �  COMIMAGE_FLAGS_IL_LIBRARY   COMIMAGE_FLAGS_STRONGNAMESIGNED 蝰  COMIMAGE_FLAGS_NATIVE_ENTRYPOINT � �   COMIMAGE_FLAGS_TRACKDEBUGDATA  �   COMIMAGE_FLAGS_32BITPREFERRED   COR_VERSION_MAJOR_V2 �  COR_VERSION_MAJOR   COR_VERSION_MINOR   COR_DELETED_NAME_LENGTH 蝰  COR_VTABLEGAP_NAME_LENGTH   NATIVE_TYPE_MAX_CB 篁� � COR_ILMETHOD_SECT_SMALL_MAX_DATASIZE �  IMAGE_COR_MIH_METHODRVA 蝰  IMAGE_COR_MIH_EHRVA 蝰  IMAGE_COR_MIH_BASICBLOCK �  COR_VTABLE_32BIT �  COR_VTABLE_64BIT �  COR_VTABLE_FROM_UNMANAGED   COR_VTABLE_FROM_UNMANAGED_RETAIN_APPDOMAIN 篁�  COR_VTABLE_CALL_MOST_DERIVED �   IMAGE_COR_EATJ_THUNK_SIZE   MAX_CLASS_NAME 篁�  MAX_PACKAGE_NAME 馬   t   �  ReplacesCorHdrNumericDefines .?AW4ReplacesCorHdrNumericDefines@@ 篁�2   �              tagTYPEDESC .?AUtagTYPEDESC@@ 2   �              tagIDLDESC .?AUtagIDLDESC@@ 蝰�
 C    guid �
 "    lcid �
 "    dwReserved 篁�
     memidConstructor �
     memidDestructor 蝰
 q    lpstrSchema 蝰
 "   ( cbSizeInstance 篁�
 �  , typekind �
 !   0 cFuncs 篁�
 !   2 cVars 
 !   4 cImplTypes 篁�
 !   6 cbSizeVft 
 !   8 cbAlignment 蝰
 !   : wTypeFlags 篁�
 !   < wMajorVerNum �
 !   > wMinorVerNum �
 �  @ tdescAlias 篁�
 �  P idldescType 蝰2   �          ` tagTYPEATTR .?AUtagTYPEATTR@@ 
     * 
 "     cElems 篁�
 �   pElems 篁�6   �           tagCABSTRBLOB .?AUtagCABSTRBLOB@@ .   �           tagCABOOL .?AUtagCABOOL@@ * 
 "     LowPart 蝰
 "    HighPart 馴  �           _ULARGE_INTEGER::<unnamed-tag> .?AU<unnamed-tag>@_ULARGE_INTEGER@@ 篁馸  �           _ULARGE_INTEGER::<unnamed-type-u> .?AU<unnamed-type-u>@_ULARGE_INTEGER@@ �
     * 
 "     cElems 篁�
 �   pElems 篁�*   �           tagCAUH .?AUtagCAUH@@ & 
 "     cbSize 篁�
     pData 2   �           tagBSTRBLOB .?AUtagBSTRBLOB@@ j   �              $_s__CatchableTypeArray$_extraBytes_16 .?AU$_s__CatchableTypeArray$_extraBytes_16@@ 蝰 A  #     �> 
 t     nCatchableTypes 蝰
 �   arrayOfCatchableTypes j   �           $_s__CatchableTypeArray$_extraBytes_16 .?AU$_s__CatchableTypeArray$_extraBytes_16@@ 蝰
     	u     �                u      	     �     �      
 �        u   �   	     �     �          I     	     �     �      2   �              tagTLIBATTR .?AUtagTLIBATTR@@ 
 �    
 �    
    �   	     �     �       	     �     [          t   0  0  "  0   	     �     �          q  "   t   	     �     �          q  "       !   	     �     �      
    �   	     �     �      
   �  
    �   	     �    �      
    蝰
 �  ,  
    �   	     �    �       	     �              �    �    �  
   ,   	�    �     �       	�    �     �         �    �  B      蝰 �     GetTypeInfoCount 篁� �      GetTypeInfo  �  (   GetTypeInfoType  �  0   GetTypeInfoOfGuid 蝰 �  8   GetLibAttr � �  @   GetTypeComp  �  H   GetDocumentation 篁� �  P   IsName � �  X   FindName 篁� �  `   ReleaseTLibAttr  �  ITypeLib 篁� �  operator= 蝰 
 
 UUUUUUP篁�.  &�      �   ITypeLib .?AUITypeLib@@ 蝰r    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES �  D3D12_RAYTRACING_GEOMETRY_TYPE_PROCEDURAL_PRIMITIVE_AABBS V   t   �  D3D12_RAYTRACING_GEOMETRY_TYPE .?AW4D3D12_RAYTRACING_GEOMETRY_TYPE@@ 篁�2   �              tagELEMDESC .?AUtagELEMDESC@@ � 
      memid 
 q   lpstrSchema 蝰
 "    oInst 
 F   lpvarValue 篁�
 �   elemdescVar 蝰
 !   8 wVarFlags 
 �  < varkind 蝰2   �          @ tagVARDESC .?AUtagVARDESC@@ 蝰* 
 "     cElems 篁�
    pElems 篁�6   �           tagCACLIPDATA .?AUtagCACLIPDATA@@    a    蝰z   �           std::allocator_traits<std::allocator<char16_t> > .?AU?$allocator_traits@V?$allocator@_S@std@@@std@@ 蝰       蝰F   �           std::char_traits<char> .?AU?$char_traits@D@std@@ 馼    W           std::_String_constructor_concat_tag .?AU_String_constructor_concat_tag@std@@ 駣    _Functor �  _Pmf_object 蝰  _Pmf_refwrap �  _Pmf_pointer �  _Pmd_object 蝰  _Pmd_refwrap �  _Pmd_pointer 馚   t   �  std::_Invoker_strategy .?AW4_Invoker_strategy@std@@ 
 �   蝰
 p    	   p  �    7       	   p  �               �     �  
 �    	-  p  �             	#   p  �            �   q   value_type �  k  reference 蝰  k  const_reference   #   size_type 蝰  -  iterator 篁�  -  const_iterator � �  initializer_list<wchar_t> 蝰 �  begin 蝰 �  end  �  size 篁�
 -    _First 篁�
 -   _Last V 
 �           std::initializer_list<wchar_t> .?AV?$initializer_list@_W@std@@ 篁�    /  /  
 6    	   6  �    �       	   6  �               �     �  
 �    	/  6  �             	#   6  �            �   z   value_type �  1  reference 蝰  1  const_reference   #   size_type 蝰  /  iterator 篁�  /  const_iterator � �  initializer_list<char16_t> � �  begin 蝰 �  end  �  size 篁�
 /    _First 篁�
 /   _Last V 
 �           std::initializer_list<char16_t> .?AV?$initializer_list@_S@std@@ 蝰    �  �  
 �    	   �       �       	   �                         
 R    	�  �               	#   �              �   {   value_type �  �  reference 蝰  �  const_reference   #   size_type 蝰  �  iterator 篁�  �  const_iterator �   initializer_list<char32_t> �   begin 蝰   end    size 篁�
 �    _First 篁�
 �   _Last V 
            std::initializer_list<char32_t> .?AV?$initializer_list@_U@std@@ 蝰   k    蝰v   	           std::allocator_traits<std::allocator<char> > .?AU?$allocator_traits@V?$allocator@D@std@@@std@@ 篁�
 �    	   �      @       	   �                      
  
 �    	:  �               	#   �              �   p   value_type �  �  reference 蝰  �  const_reference   #   size_type 蝰  :  iterator 篁�  :  const_iterator �   initializer_list<char> �   begin 蝰   end    size 篁�
 :    _First 篁�
 :   _Last R 
            std::initializer_list<char> .?AV?$initializer_list@D@std@@ 篁�
    蝰
     	0                 b    value 蝰  0   value_type �    type 篁�   operator bool 蝰   operator() 馴  T           std::integral_constant<bool,1> .?AU?$integral_constant@_N$00@std@@ 篁�   �    蝰J              std::char_traits<char16_t> .?AU?$char_traits@_S@std@@    X    蝰z              std::allocator_traits<std::allocator<char32_t> > .?AU?$allocator_traits@V?$allocator@_U@std@@@std@@ 蝰
 f   蝰
     	0   f              b    value 蝰  0   value_type �  f  type 篁�   operator bool 蝰   operator() 馴  T            std::integral_constant<bool,0> .?AU?$integral_constant@_N$0A@@std@@ 蝰       蝰J   "           std::char_traits<wchar_t> .?AU?$char_traits@_W@std@@ �   �    蝰J   $           std::char_traits<char32_t> .?AU?$char_traits@_U@std@@    h    蝰z   &           std::allocator_traits<std::allocator<wchar_t> > .?AU?$allocator_traits@V?$allocator@_W@std@@@std@@ 篁�>   �              ISequentialStream .?AUISequentialStream@@       "   "  
 (    	   (  *     )          "  "   "   	   (  *     ,      
 (  �  
    .   	   (  *    /      
 (   蝰
 1  ,  
    2   	   (  *    3       	   (  *              0    4    5  
 (  ,   	7  (  *     /       	7  (  *     3         8    9  f       蝰 +     Read 篁� -      Write 蝰 6  ISequentialStream 蝰 :  operator= 蝰
 
  UUP篁�>  &;      <   ISequentialStream .?AUISequentialStream@@       "   �  
     	     ?     >      
       	     ?     A               �  �   	     ?     C       	     ?     �       	     ?                    "    	     ?     G       	     ?     %      
       	     ?     J      
   �  
    L   	     ?    M      
    蝰
 O  ,  
    P   	     ?    Q       	     ?              N    R    S  
   ,   	U    ?     M       	U    ?     Q         V    W  �   (    蝰 @  (   Seek 篁� B  0   SetSize  D  8   CopyTo � E  @   Commit � F  H   Revert � H  P   LockRegion � H  X   UnlockRegion 篁� I  `   Stat 篁� K  h   Clone 蝰 T  IStream  X  operator= 蝰 
  UUUUUUU篁�*  &Y      Z   IStream .?AUIStream@@ Z   �              $_TypeDescriptor$_extraBytes_25 .?AU$_TypeDescriptor$_extraBytes_25@@  p   #     �6 
 "    pVFTable �
    spare 
 ]   name 馴   ^          ) $_TypeDescriptor$_extraBytes_25 .?AU$_TypeDescriptor$_extraBytes_25@@  �  #     駌   �              __vcrt_va_list_is_reference<wchar_t const * const> .?AU?$__vcrt_va_list_is_reference@QEB_W@@ 瘭  0   �  __vcrt_va_list_is_reference<wchar_t const * const>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@QEB_W@@ 篁�&   b  <unnamed-enum-__the_value> 駌  c           __vcrt_va_list_is_reference<wchar_t const * const> .?AU?$__vcrt_va_list_is_reference@QEB_W@@ �* 
 "     cElems 篁�
 @   pElems 篁�.   e           tagCAFLT .?AUtagCAFLT@@ 蝰.   �           tagCADBL .?AUtagCADBL@@ 蝰� 
 !     wReserved 
      scale 
      sign �
 !    signscale 
 "    Hi32 �
 "    Lo32 �
 "    Mid32 
 #    Lo64 �*   h           tagDEC .?AUtagDEC@@ 蝰�   �              __vcrt_va_list_is_reference<__crt_locale_pointers * const> .?AU?$__vcrt_va_list_is_reference@QEAU__crt_locale_pointers@@@@ 篁衤  0   �  __vcrt_va_list_is_reference<__crt_locale_pointers * const>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@QEAU__crt_locale_pointers@@@@ �&   k  <unnamed-enum-__the_value> 駫  l           __vcrt_va_list_is_reference<__crt_locale_pointers * const> .?AU?$__vcrt_va_list_is_reference@QEAU__crt_locale_pointers@@@@ 篁�* 
 "     cElems 篁�
 Z   pElems 篁�2   n           tagCALPSTR .?AUtagCALPSTR@@ 蝰* 
 "     LowPart 蝰
     HighPart 馰  p           _LARGE_INTEGER::<unnamed-tag> .?AU<unnamed-tag>@_LARGE_INTEGER@@ 馸  p           _LARGE_INTEGER::<unnamed-type-u> .?AU<unnamed-type-u>@_LARGE_INTEGER@@ 篁� B  #     �6 
       scale 
      sign �
 !     signscale >  t   tagDEC::<unnamed-tag> .?AT<unnamed-tag>@tagDEC@@ �" 
       scale 
      sign 馰  v           tagDEC::<unnamed-tag>::<unnamed-tag> .?AU<unnamed-tag>@0tagDEC@@ �2 
 "     Lo32 �
 "    Mid32 
 #     Lo64 �>  x   tagDEC::<unnamed-tag> .?AT<unnamed-tag>@tagDEC@@ �" 
 "     Lo32 �
 "    Mid32 V  z           tagDEC::<unnamed-tag>::<unnamed-tag> .?AU<unnamed-tag>@0tagDEC@@ � p   #      �6 
 "    pVFTable �
    spare 
 |   name �:   }           _TypeDescriptor .?AU_TypeDescriptor@@ ^   �              __vc_attributes::moduleAttribute .?AUmoduleAttribute@__vc_attributes@@ 篁馴   dll 蝰  exe 蝰  service 蝰  unspecified 蝰  EXE 蝰  SERVICE 蝰f  t   �  __vc_attributes::moduleAttribute::type_e .?AW4type_e@moduleAttribute@__vc_attributes@@ 馚    �  :  :  :  t   0   :  t   :  :  t   0   0   :  :  
     	     �    �      
    �   	     �    �       	     �               �     �     �  b  �  type_e � �  moduleAttribute 
 �    type �
 :   name �
 :   version 蝰
 :   uuid �
 t     lcid �
 0   $ control 蝰
 :  ( helpstring 篁�
 t   0 helpstringcontext 
 :  8 helpstringdll 
 :  @ helpfile �
 t   H helpcontext 蝰
 0   L hidden 篁�
 0   M restricted 篁�
 :  P custom 篁�
 :  X resource_name ^  �          ` __vc_attributes::moduleAttribute .?AUmoduleAttribute@__vc_attributes@@ 篁駈   �              __vc_attributes::event_receiverAttribute .?AUevent_receiverAttribute@__vc_attributes@@ 篁�.    native 篁�  com 蝰  managed 蝰v  t   �  __vc_attributes::event_receiverAttribute::type_e .?AW4type_e@event_receiverAttribute@__vc_attributes@@ �    �  0   
 �    	   �  �    �      
    �   	   �  �    �       	   �  �               �     �     �  ^   �  type_e � �  event_receiverAttribute 
 �    type �
 0    layout_dependent 駈  �           __vc_attributes::event_receiverAttribute .?AUevent_receiverAttribute@__vc_attributes@@ 篁駄   �              __vc_attributes::aggregatableAttribute .?AUaggregatableAttribute@__vc_attributes@@ 篁�.    never   allowed 蝰  always 篁駌  t   �  __vc_attributes::aggregatableAttribute::type_e .?AW4type_e@aggregatableAttribute@__vc_attributes@@ �
    �  
 �    	   �  �    �       	   �  �               �     �  B   �  type_e � �  aggregatableAttribute 蝰
 �    type 駄  �           __vc_attributes::aggregatableAttribute .?AUaggregatableAttribute@__vc_attributes@@ 篁馼   �              __vc_attributes::threadingAttribute .?AUthreadingAttribute@__vc_attributes@@ 馢   apartment   single 篁�  free �  neutral 蝰  both 駐  t   �  __vc_attributes::threadingAttribute::threading_e .?AW4threading_e@threadingAttribute@__vc_attributes@@ �
    �  
 �    	   �  �    �       	   �  �               �     �  B   �  threading_e  �  threadingAttribute �
 �    value b  �           __vc_attributes::threadingAttribute .?AUthreadingAttribute@__vc_attributes@@ 駘   �              __vc_attributes::helper_attributes::usageAttribute .?AUusageAttribute@helper_attributes@__vc_attributes@@ �   eAnyUsage   eCoClassUsage   eCOMInterfaceUsage 篁�  eInterfaceUsage 蝰  eMemberUsage �  eMethodUsage �   eInterfaceMethodUsage  @ eInterfaceMemberUsage  � eCoClassMemberUsage 蝰  eCoClassMethodUsage 蝰  eGlobalMethodUsage 篁�  eGlobalDataUsage �  eClassUsage 蝰  eInterfaceParameterUsage �  0eMethodParameterUsage   @eIDLModuleUsage 蝰 � �eAnonymousUsage  �   eTypedefUsage  �   eUnionUsage 蝰 �   eEnumUsage 篁� �   eDefineTagUsage 蝰 �   eStructUsage � �    eLocalUsage 蝰 �  @ ePropertyUsage 篁� �  � eEventUsage 蝰 �   eTemplateUsage 篁� �   eModuleUsage � �   eIllegalUsage  �   eAsynchronousUsage 篁� ��? eAnyIDLUsage 駣  t   �  __vc_attributes::helper_attributes::usageAttribute::usage_e .?AW4usage_e@usageAttribute@helper_attributes@__vc_attributes@@ 
 �    	   �  �    T      :   �  usage_e  �  usageAttribute �
 u     value ~  �           __vc_attributes::helper_attributes::usageAttribute .?AUusageAttribute@helper_attributes@__vc_attributes@@ �   �              __vc_attributes::helper_attributes::v1_alttypeAttribute .?AUv1_alttypeAttribute@helper_attributes@__vc_attributes@@ 蝰B    eBoolean �  eInteger �  eFloat 篁�  eDouble 蝰�  t   �  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e .?AW4type_e@v1_alttypeAttribute@helper_attributes@__vc_attributes@@ 
    �  
 �    	   �  �    �      >   �  type_e � �  v1_alttypeAttribute 
 �    type 駣  �           __vc_attributes::helper_attributes::v1_alttypeAttribute .?AUv1_alttypeAttribute@helper_attributes@__vc_attributes@@ 蝰j   �              __vc_attributes::event_sourceAttribute .?AUevent_sourceAttribute@__vc_attributes@@ 篁駌  t   �  __vc_attributes::event_sourceAttribute::type_e .?AW4type_e@event_sourceAttribute@__vc_attributes@@ �    speed   size 駔  t   �  __vc_attributes::event_sourceAttribute::optimize_e .?AW4optimize_e@event_sourceAttribute@__vc_attributes@@ �
    �  
 �    	   �  �    �       	   �  �               �     �  ~   �  type_e �  �  optimize_e � �  event_sourceAttribute 蝰
 �    type �
 �   optimize �
 0    decorate 駄  �           __vc_attributes::event_sourceAttribute .?AUevent_sourceAttribute@__vc_attributes@@ 篁馴   �              $_TypeDescriptor$_extraBytes_29 .?AU$_TypeDescriptor$_extraBytes_29@@  p   #     �6 
 "    pVFTable �
    spare 
 �   name 馴   �          - $_TypeDescriptor$_extraBytes_29 .?AU$_TypeDescriptor$_extraBytes_29@@ .    IdleShutdown �  ForcedShutdown 篁�6   t   �  tagShutdownType .?AW4tagShutdownType@@ 駈   �              __vcrt_va_list_is_reference<char const * const> .?AU?$__vcrt_va_list_is_reference@QEBD@@ 瘼  0   �  __vcrt_va_list_is_reference<char const * const>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@QEBD@@ 篁�&   �  <unnamed-enum-__the_value> 駈  �           __vcrt_va_list_is_reference<char const * const> .?AU?$__vcrt_va_list_is_reference@QEBD@@ 馴   �              $_TypeDescriptor$_extraBytes_31 .?AU$_TypeDescriptor$_extraBytes_31@@  p   #     �6 
 "    pVFTable �
    spare 
 �   name 馴   �          / $_TypeDescriptor$_extraBytes_31 .?AU$_TypeDescriptor$_extraBytes_31@@ z 
 C    guid �
 "    lcid �
 R   syskind 蝰
 !    wMajorVerNum �
 !    wMinorVerNum �
 !    wLibFlags 2   �            tagTLIBATTR .?AUtagTLIBATTR@@ 
    �         �  
 �    � 
 !     wCode 
 !    wReserved 
 q   bstrSource 篁�
 q   bstrDescription 蝰
 q   bstrHelpFile �
 "     dwHelpContext 
   ( pvReserved 篁�
 �  0 pfnDeferredFillIn 
    8 scode 6 	  �          @ tagEXCEPINFO .?AUtagEXCEPINFO@@ 蝰. 
 #     dwReserved 篁�
 !    wIDLFlags 2   �           tagIDLDESC .?AUtagIDLDESC@@ 蝰6   �              tagPARAMDESC .?AUtagPARAMDESC@@ 蝰: 
 �    tdesc 
 �   idldesc 蝰
 �   paramdesc 2   �            tagELEMDESC .?AUtagELEMDESC@@ * 
 �    idldesc 蝰
 �    paramdesc J  �   tagELEMDESC::<unnamed-tag> .?AT<unnamed-tag>@tagELEMDESC@@ 篁�
 �    6   �              tagARRAYDESC .?AUtagARRAYDESC@@ 蝰
 �    N 
 �    lptdesc 蝰
 �    lpadesc 蝰
 "     hreftype �
 !    vt 篁�2   �           tagTYPEDESC .?AUtagTYPEDESC@@ 
 �    *   �      tagBINDPTR .?ATtagBINDPTR@@ 蝰
 �        q  "   !     �  �  
 X    	   X  �     �          q  "     Z   	   X  �     �      
 X  �  
    �   	   X  �    �      
 X   蝰
 �  ,  
    �   	   X  �    �       	   X  �              �    �    �  
 X  ,   	�  X  �     �       	�  X  �     �         �    �  b       蝰 �     Bind 篁� �      BindType 篁� �  ITypeComp 蝰 �  operator= 蝰.  &�      <   ITypeComp .?AUITypeComp@@ � 
 q    pwcsName �
 "    type �
    cbSize 篁�
    mtime 
     ctime 
   ( atime 
 "   0 grfMode 蝰
 "   4 grfLocksSupported 
 C  8 clsid 
 "   H grfStateBits �
 "   L reserved �2   �          P tagSTATSTG .?AUtagSTATSTG@@ 蝰    "   $  "  
     	           �       	           �       	                  
       	                 
   �  
       	                
    蝰
 	  ,  
    
   	                 	                            
  
   ,   	                 	                         �       蝰      Next 篁�       Skip 篁�   (   Reset 蝰   0   Clone 蝰   IEnumSTATSTG 篁�   operator= 蝰6 
 &      �   IEnumSTATSTG .?AUIEnumSTATSTG@@ 蝰* 
 "     oInst 
 F    lpvarValue 篁馞     tagVARDESC::<unnamed-tag> .?AT<unnamed-tag>@tagVARDESC@@ �
 �    � 
      memid 
    lprgscode 
    lprgelemdescParam 
 �   funckind �
 t   invkind 蝰
 �    callconv �
    $ cParams 蝰
    & cParamsOpt 篁�
    ( oVft �
    * cScodes 蝰
 �  0 elemdescFunc �
 !   P wFuncFlags 篁�2             X tagFUNCDESC .?AUtagFUNCDESC@@ > 
 �    lptdesc 蝰
 �    lpadesc 蝰
 "     hreftype 馢     tagTYPEDESC::<unnamed-tag> .?AT<unnamed-tag>@tagTYPEDESC@@ 篁馚 
 ^    lpfuncdesc 篁�
 c    lpvardesc 
 Y    lptcomp 蝰*      tagBINDPTR .?ATtagBINDPTR@@ 蝰: 
 �    tdescElem 
 !    cDims 
 �   rgbounds �6               tagARRAYDESC .?AUtagARRAYDESC@@ 蝰:   �              tagPARAMDESCEX .?AUtagPARAMDESCEX@@ 蝰
      2 
 !    pparamdescex �
 !    wParamFlags 蝰6   "           tagPARAMDESC .?AUtagPARAMDESC@@ 蝰2 
 "     cBytes 篁�
 E   varDefaultValue 蝰:   $            tagPARAMDESCEX .?AUtagPARAMDESCEX@@ 蝰�           {   ??_7IUnknown@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ 耦 =  &      �   ??_7ISequentialStream@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?Read@ISequentialStream@@UEAAJPEAXKPEAK@Z ?Write@ISequentialStream@@UEAAJPEBXKPEAK@Z 篁駀[  '      Q  ??_7IStream@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?Read@ISequentialStream@@UEAAJPEAXKPEAK@Z ?Write@ISequentialStream@@UEAAJPEBXKPEAK@Z ?Seek@IStream@@UEAAJT_LARGE_INTEGER@@KPEAT_ULARGE_INTEGER@@@Z ?SetSize@IStream@@UEAAJT_ULARGE_INTEGER@@@Z ?CopyTo@IStream@@UEAAJPEAU1@T_ULARGE_INTEGER@@PEAT2@2@Z ?Commit@IStream@@UEAAJK@Z ?Revert@IStream@@UEAAJXZ ?LockRegion@IStream@@UEAAJT_ULARGE_INTEGER@@0K@Z ?UnlockRegion@IStream@@UEAAJT_ULARGE_INTEGER@@0K@Z ?Stat@IStream@@UEAAJPEAUtagSTATSTG@@K@Z ?Clone@IStream@@UEAAJPEAPEAU1@@Z 篁�"  &        ??_7IEnumSTATSTG@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?Next@IEnumSTATSTG@@UEAAJKPEAUtagSTATSTG@@PEAK@Z ?Skip@IEnumSTATSTG@@UEAAJK@Z ?Reset@IEnumSTATSTG@@UEAAJXZ ?Clone@IEnumSTATSTG@@UEAAJPEAPEAU1@@Z :6  &      &  ??_7IStorage@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?CreateStream@IStorage@@UEAAJPEB_WKKKPEAPEAUIStream@@@Z ?OpenStream@IStorage@@UEAAJPEB_WPEAXKKPEAPEAUIStream@@@Z ?CreateStorage@IStorage@@UEAAJPEB_WKKKPEAPEAU1@@Z ?OpenStorage@IStorage@@UEAAJPEB_WPEAU1@KPEAPEA_WKPEAPEAU1@@Z ?CopyTo@IStorage@@UEAAJKPEBU_GUID@@PEAPEA_WPEAU1@@Z ?MoveElementTo@IStorage@@UEAAJPEB_WPEAU1@0K@Z ?Commit@IStorage@@UEAAJK@Z ?Revert@IStorage@@UEAAJXZ ?EnumElements@IStorage@@UEAAJKPEAXKPEAPEAUIEnumSTATSTG@@@Z ?DestroyElement@IStorage@@UEAAJPEB_W@Z ?RenameElement@IStorage@@UEAAJPEB_W0@Z ?SetElementTimes@IStorage@@UEAAJPEB_WPEBU_FILETIME@@11@Z ?SetClass@IStorage@@UEAAJAEBU_GUID@@@Z ?SetStateBits@IStorage@@UEAAJKK@Z ?Stat@IStorage@@UEAAJPEAUtagSTATSTG@@K@Z 蝰��  &      v  ??_7IDispatch@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?GetTypeInfoCount@IDispatch@@UEAAJPEAI@Z ?GetTypeInfo@IDispatch@@UEAAJIKPEAPEAUITypeInfo@@@Z ?GetIDsOfNames@IDispatch@@UEAAJAEBU_GUID@@PEAPEA_WIKPEAJ@Z ?Invoke@IDispatch@@UEAAJJAEBU_GUID@@KGPEAUtagDISPPARAMS@@PEAUtagVARIANT@@PEAUtagEXCEPINFO@@PEAI@Z 蝰"�  &        ??_7ITypeComp@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?Bind@ITypeComp@@UEAAJPEA_WKGPEAPEAUITypeInfo@@PEAW4tagDESCKIND@@PEATtagBINDPTR@@@Z ?BindType@ITypeComp@@UEAAJPEA_WKPEAPEAUITypeInfo@@PEAPEAU1@@Z 蝰��  &      v  ??_7ITypeInfo@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?GetTypeAttr@ITypeInfo@@UEAAJPEAPEAUtagTYPEATTR@@@Z ?GetTypeComp@ITypeInfo@@UEAAJPEAPEAUITypeComp@@@Z ?GetFuncDesc@ITypeInfo@@UEAAJIPEAPEAUtagFUNCDESC@@@Z ?GetVarDesc@ITypeInfo@@UEAAJIPEAPEAUtagVARDESC@@@Z ?GetNames@ITypeInfo@@UEAAJJPEAPEA_WIPEAI@Z ?GetRefTypeOfImplType@ITypeInfo@@UEAAJIPEAK@Z ?GetImplTypeFlags@ITypeInfo@@UEAAJIPEAH@Z ?GetIDsOfNames@ITypeInfo@@UEAAJPEAPEA_WIPEAJ@Z ?Invoke@ITypeInfo@@UEAAJPEAXJGPEAUtagDISPPARAMS@@PEAUtagVARIANT@@PEAUtagEXCEPINFO@@PEAI@Z ?GetDocumentation@ITypeInfo@@UEAAJJPEAPEA_W0PEAK0@Z ?GetDllEntry@ITypeInfo@@UEAAJJW4tagINVOKEKIND@@PEAPEA_W1PEAG@Z ?GetRefTypeInfo@ITypeInfo@@UEAAJKPEAPEAU1@@Z ?AddressOfMember@ITypeInfo@@UEAAJJW4tagINVOKEKIND@@PEAPEAX@Z ?CreateInstance@ITypeInfo@@UEAAJPEAUIUnknown@@AEBU_GUID@@PEAPEAX@Z ?GetMops@ITypeInfo@@UEAAJJPEAPEA_W@Z ?GetContainingTypeLib@ITypeInfo@@UEAAJPEAPEAUITypeLib@@PEAI@Z ?ReleaseTypeAttr@ITypeInfo@@UEAAXPEAUtagTYPEATTR@@@Z ?ReleaseFuncDesc@ITypeInfo@@UEAAXPEAUtagFUNCDESC@@@Z ?ReleaseVarDesc@ITypeInfo@@UEAAXPEAUtagVARDESC@@@Z 蝰��  &      s  ??_7ITypeLib@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?GetTypeInfoCount@ITypeLib@@UEAAIXZ ?GetTypeInfo@ITypeLib@@UEAAJIPEAPEAUITypeInfo@@@Z ?GetTypeInfoType@ITypeLib@@UEAAJIPEAW4tagTYPEKIND@@@Z ?GetTypeInfoOfGuid@ITypeLib@@UEAAJAEBU_GUID@@PEAPEAUITypeInfo@@@Z ?GetLibAttr@ITypeLib@@UEAAJPEAPEAUtagTLIBATTR@@@Z ?GetTypeComp@ITypeLib@@UEAAJPEAPEAUITypeComp@@@Z ?GetDocumentation@ITypeLib@@UEAAJHPEAPEA_W0PEAK0@Z ?IsName@ITypeLib@@UEAAJPEA_WKPEAH@Z ?FindName@ITypeLib@@UEAAJPEA_WKPEAPEAUITypeInfo@@PEAJPEAG@Z ?ReleaseTLibAttr@ITypeLib@@UEAAXPEAUtagTLIBATTR@@@Z 駐:  &      d  ??_7IRecordInfo@@6B@ ?QueryInterface@IUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z ?AddRef@IUnknown@@UEAAKXZ ?Release@IUnknown@@UEAAKXZ ?RecordInit@IRecordInfo@@UEAAJPEAX@Z ?RecordClear@IRecordInfo@@UEAAJPEAX@Z ?RecordCopy@IRecordInfo@@UEAAJPEAX0@Z ?GetGuid@IRecordInfo@@UEAAJPEAU_GUID@@@Z ?GetName@IRecordInfo@@UEAAJPEAPEA_W@Z ?GetSize@IRecordInfo@@UEAAJPEAK@Z ?GetTypeInfo@IRecordInfo@@UEAAJPEAPEAUITypeInfo@@@Z ?GetField@IRecordInfo@@UEAAJPEAXPEB_WPEAUtagVARIANT@@@Z ?GetFieldNoCopy@IRecordInfo@@UEAAJPEAXPEB_WPEAUtagVARIANT@@PEAPEAX@Z ?PutField@IRecordInfo@@UEAAJKPEAXPEB_WPEAUtagVARIANT@@@Z ?PutFieldNoCopy@IRecordInfo@@UEAAJKPEAXPEB_WPEAUtagVARIANT@@@Z ?GetFieldNames@IRecordInfo@@UEAAJPEAKPEAPEA_W@Z ?IsMatchingType@IRecordInfo@@UEAAHPEAU1@@Z ?RecordCreate@IRecordInfo@@UEAAPEAXXZ ?RecordCreateCopy@IRecordInfo@@UEAAJPEAXPEAPEAX@Z ?RecordDestroy@IRecordInfo@@UEAAJPEAX@Z j           V   ??_7exception@std@@6B@ ??_Eexception@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰r C  0      ^   ??_7bad_exception@std@@6B@ ??_Ebad_exception@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰j Y  0      V   ??_7bad_alloc@std@@6B@ ??_Ebad_alloc@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰~ o  2      l   ??_7bad_array_new_length@std@@6B@ ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ V �          D   ??_7nested_exception@std@@6B@ ??_Enested_exception@std@@UEAAPEAXI@Z � �  0      q   ??_7bad_variant_access@std@@6B@ ??_Ebad_variant_access@std@@UEAAPEAXI@Z ?what@bad_variant_access@std@@UEBAPEBDXZ 篁駈 �  0      Z   ??_7logic_error@std@@6B@ ??_Elogic_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰n �  6      \   ??_7domain_error@std@@6B@ ??_Edomain_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ v �  6      d   ??_7invalid_argument@std@@6B@ ??_Einvalid_argument@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ n   6      \   ??_7length_error@std@@6B@ ??_Elength_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ n (  6      \   ??_7out_of_range@std@@6B@ ??_Eout_of_range@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ r ?  0      ^   ??_7runtime_error@std@@6B@ ??_Eruntime_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰r V  ;      `   ??_7overflow_error@std@@6B@ ??_Eoverflow_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ v m  ;      b   ??_7underflow_error@std@@6B@ ??_Eunderflow_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰n �  ;      Z   ??_7range_error@std@@6B@ ??_Erange_error@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         O� 娘 ` N= 錨  �  翅 8�  s� 垗 搜 * � <� 骤 飘 畣 /�  2� (= 齓  湽 %I  �$ I� #2  6? � 莈 d� 潭 qg  挈 � 彅 r  L~ q�  郅  齺 � )W ?�  @� 5U  鹽 曈  橴 亝 - 巷 曣  S� 輅 権 饪 鑺 p[  Aj 麄 � =� � " v� 扄  浲  苑 �  M;  0*  襳 cX 
�  H� &�  �  蔟  )� W�    ╔  l� A� n3 �  C  !� � {� 詘 跚 誵  7 �/ _� 侶 (� Xf  庆 � %X 宎  � �  枀 � 鉘  續 RB � 	� � ?	 �$ A�  � 于  �  0 |� 襌  K  釹  x� � 奛  责 W� 也  B lc  z� Q�   餿 &  �  D� M� �$ v�  k� 淌   鎕  7� 鴼 ^� 啭  Z0 s� 廔 E� �< 胈 q� 捍 � +�  U� 瑀 w�  哚 1   !�  潓 6� 4D :p   �   ,� �" +x � �  鑨 攮  曩 S�  UA  �; �  	  !�   6 ll ~�  Q�  �  q�  y� F� p� 鼫 H  R> � 诪 �  9� f� �. 儷 "] U 1 [� F  � 電 流 I� +� 鉥 -! 杫 郲  i� 髳 �& L�  蟚 /� [� 屛 瞩  �> y |Q � � _@ 0�   Fq � 砘 � � �  鞇 	� 敐 q  M� 霌 !�   瘽  O� 侒 虓 鼡  W�  鮞 紏 L� 雓 
 蔋 T� 鱦 "�  拝  襴 向 @� �  JU  � ╙ g �: H  0 �- (  妥  宾 遪 u  a  @� gG 驾  -N �� G� �	 鉼 � �	  o 	�  � 塑 K  餕 教 羊  璛   o ^D 醆 z�  m� {� 袍 =8  �   i�  {=  污  嶎 _/ 构 |� 钊  R � �  邯 榦 *  t� 缿  蔖 纪 w� 對 �9  �  	 �   亡 G  J� `  � 5 0 B} ╊ S  h� f� 徱 � �  � 啿 M O* ![ {� 褋 s* 9U    && {�  晛 適 � g 塮 乺 &$  ? 
z  m k c� -� � 9� �  1i  *o � 槅 s�  7� :� T� �( 嗷   � .�  U9 驭   陓 m� +' b� pR  昐 <j  都  �) 甭 蔻 溆 蔎 q� J  m dS �
 ∶ kV 椎 �8 挽 �+ _$ !� 曄  � s  w � �4   �  ^ � �1 � IH G�  蚸 G  ;� 纪  }f s�  [�  *� �7 �  楠  鈁 :   � '�  綮  h &� G 徨  |8  &� 3Y F � !  凯 総 痦 {~ o  �
 �  埶 宖  F5 �. a� 銒  � '� 氧 桓 便  h� y�  邭 阉 �� 5� wt 曬 p� � 8� V� 拰 n�  瑉   莭 欎 � 濑  Wn F� �  L� 埇 狻 )�  #  J%  � uX {- 润  W� 
X  '  陉 pr  Y| 7� �:  �) �2 狁 U� X� 嘕 呡 /  禇 �)  詹 i � � 寡  艰 / �6 ⅲ  (� b TL  ~� *� � 烉 ↙  �
  �6 詘 阼 �3 |  寪 � 懂 热  I- u � 忞  娊 7 s� �' q� D _{ � V  z�  = :l � y-  #� �   Ju � `N 1 坋 璻 勰 X�  � #� o� 漑 zf 碶 |� 跌 i� 铍 �!  �5  %� 嬠  <  a� P�   瘏  �
   o�  > D� K    � Z: 掝 � 砰 sb 5� �  罧  飳 H�  嬠 盷 )�  �   1Y � ? � V` \G u� 熟 媥 狍 釰  Q�   n &U �* 浩 姟 ㄝ  a? 9� q� 刦   b 化 M� l] 餚 �) L� 鸀 搱 訧 �  )  }� �  v� 率 鴌 g� � 櫵 R�    剃 $� 燢 1� 萸 
J � 頩 2 挌 � [� ~� zz 5� t� 5C *  誢  螴  蛭 `E 3� }� k� 篻 h�  蹎 1� ぼ �=  忘 Z  � �  v$ ~K   �  <� 7B 俍 D X  �+ 侊 :� }� 瘆 �, 罀 7�  qo 
 骱 岤 �  �& 挂 v�  �  喲 鯞 B� qZ 粟 簖  僗 頎  綫 歋 �+  圯 � i� :� к  蔺 �( � 斘  勽 N  fO 勹 ? �    凊 � +� 綞 F� # 媚 奱 � 癹 r 鄖 $�  ⑷  6@  讍  諺 ^� *D qw  �  J� e� 2#  )0 棖 舸 G\ g� 刖 	D � P� 5� : k  ]� DB �9  l0  �! 4�  �  栾 � 衣 ╥ \ ?� � % 镊  �< *! 穱 蕁 婊 �'  �4 "[ " .�  � F � a� 嵕 X  �  媐 薟 u� >y 镢 �   ` �; �  謬 %�  罈 � Nf  啡 颺 a (F  澕 琳 孷  � $� �) � � - 2d 諯 k_ � +i  2� A�  J1 櫆 �/ S 莢 Y� "G  � �L 瞛  hq z� Bz qJ  d �3 �	  r� `  鱸 � PE  濫 $ C� 一 �  v( ю ^[ 甅 �$ Tr g  {�   � 頂 � v) 軡 �
 暐 p� +� 隋 � 恺 ~ 螔 �� � �
  �0 b< ,� '� 攀 �< 榑 亳  f �
 翸 f Q� A 袴  �:  骭 罫    效  � 虜  ＋  铃  )�  =�   Jq 壃 r� � 崗 c� q� ]j t 絡 � � g� -� 
� Ｘ  ],  � Yt 齖 7  � �& 烼 GV E  窜 � (� {V 黎 k� n�  �  簰  a( M� 鄌  [� 3� � 灗 呦 ;| 9K  ^m  mw  滼 祅 丂 /  玟 *� �  C� -c  閻 V �,  垠 忯 %v �  O� �=  >�  橒 a� .� 奂 =� u K� 犚  �  �8 � 拓 毿 硙 踘 �?  <Z  敁 )M   e7 N� PK  犙  譹   %g A�  '� 傇 $� 8� D� �  � 剒 VB 觭  ,� L� m� H� �=  溬 S. R� �
 8�   "l 僱 � 祸 u  倞 G�  廇 s  T�   3� 曨 翭  紆 3  �  fo g?  2 j� 烌 = k� � �<  �  � 鐊 }�  釿 幬 罜 E� s� 鐩 觾 �(  爛 �  峃 � r 验 )�    �2 埾  
 迋 楫  �   �  z�  | 冴  � �  圱 昸 � Xa {$ 様  Bh �2 浕 曅 9w � 貛 x� 	Y t� �( U� 鐲 佬 y� 賨 諉 漲 n� ! + 箘 胎 A  � 7� "�  N� 僿 櫆 逖 殺 g� 榓  厉 l5  雬   [.  �0 盵 qc 鋀 r  �* �>  4+ 秗  t� \� 狨  R� 靕 刳 %�  ! *�  j   鼪 �> 
 己 �! $� ,V 牳 F� 拼 e�  < � 蔿  �	 鄵 硓 韐 �  Y 籎 麇 菈 � 4 � �
  逶 T� 枠  yC  �& �  �+     "�  _ 千 縌   �& q  � �( 濘 �% g� 橗  kb 耺 uW } C? � eK 賨 輓  怍 扩 羽 �. 袳 磶 
g � S  荩 H d�  @l 8� (H  漱 "D %� 甼  9' :p 扃  7 � y� R  嗂 �8  �4 礶 憱 k� о � C� =� ZG � � ♀ 奓 唵  o \�  �= O M  �( g� 襻 煠  � f� .�  6
 t� 貋 _ � 炐 B| 
L � A� � N4  鞨 3  @� 8� J� <� �$  勝 w.  �  @� Q /� �=   W rT m 鼭  �  蟘  �  @�  {v ;/ /% � 彷 往 =�  w+ n�  � 橜 栋  �9  �3 h~  \] 泓  I� 寛  棺  tz %� {� 蟜 歖 *� 战 � b5  挪 嗐   _ L0 饅 H?   " 鵲  覲 嶥 ;� 樆 邺   # c � 騿 t 骂 筭 旋 }J 爋 t3  T� 牁 x> ?� t� vE �    �  栞 	� � E� F� 撏  壛  ff 菞  磧  *� 酢   
�  �6   氊 弻 F9  l) &  ]� T  �  峋 	U   ホ � � J�  �  碦  J 巿 瞔  ;�  ~^ [  *; w� _ ,P 娹 
� 炈 <� j�  疕 炛 懟 �  {�  
� 挝 �  樎 逥 铿 � 龚 墄 w� F 应 �&  茂 缌 ~ g�  嵬  i� $ ｈ Q+ ↗ � 閴 2� :. �  a� � p�  崨 � "�  � m� 穧 氟 蜝 ~� 碔 o5 
z 廻 � 鶁  M�  壮 +� Ru a�  � =a  郡 
K  h� +�  D 橿   耳 踐  Pi �  L& j �4  N 1� >� 18 .m q�  煒  � O� CQ {  Z ,a ~� 鄷 �
  � 纴 Sr  � b	 埔 屡 琻 饘 �4 � �:  潢 恺 皻 搆 往 綐 倵 F 镎 q� 玤  訲 8� .� 5� 盤 粘  引 SB 瞝  � %�  �* Y�  z�   m � 穻  �  w� } a P Q� �% 儞 怡 姓 柚  ' í 栂 簽 � L$  謒  Q  虆 溾  W� /� 衙 %� 鉋  谘 *- 捏 $� 【 幙  )� ~� %�  邟 C+  ^ 龇 ;m �, 掯  <� �  冉 咩 f�  J  QW OI �  U� �  T� 鱨 h� !V "� 疊   箆 =  i� uC !r �& 癭 壓 }&  nv �< � �  zq � 坅 �8 ZH �  j	 豔 铊 X�  ^� �3  Q  苌 Xt  褅  3� hM  臕  ++ 蒷 L� # 肚 韗  {� c� .r * 划 杔 ;  	| 篭 *! Z�  � S� 岷 Z�  �: �   ]�  DO   D� 蜓 褘 )� AL 電  牧  V�  p�   `� Yy 伧 逼 �  '  5�  剡  侔 剘 梻  4} Z* 鮗 U� 楯 艻  E Gj �< S4 � 乢 x� N� wd  �+ B  .c 裤 よ �  Q� 餘 饭  � 憺 漓 � � Xm L ]�   鈎  牭 烐 r7 {n � D�   V�   蚒  � 鱗  � $�  歇 \ N  }� D� 伴 朄 郤 ℃ t& g�  篂 兇   q> 原 濣 �  S� ]A  拂  �<  �  蔌  �  W{ Cq  ;a i  #� t,  X� � 墅 邼  憩 禄  莫 ` 綿 �! 螳   � \g �3 �  	1 �  �0 � 4� �   _� 謂  ] q3  \� 靬  �  r 骞 ゴ  u� 9� �	 K� �# � GM {� 2 �' 锕   *# � T� � 亥 B�  铤 � �4  枋 葿 牺  � ~� ks  螋 寽 �	  磗 V�  蚊 [{  � `; 啳  SE  蹽 �& @�  V5  �  柔 � O 柊 � 慯  Qe �  � �% 
� ]� ==  呮 �.  鍻 $  忮 镧 b �  D$ f@  �  ,  � l�  =M 纪  �  �5 *�  ^� �E  炻 � E� `� �4 绒 q P  實 �| S 饌  刡  >k  (�  鞱 �* 敪 x- .~ 筒 鋱 �  B 唎 Hj �  � 檔 臺  糄  p� +�  `� yR U� 儷 � zA *i 坷 ]: 纚 � 嗛 !d 婉 � 炞 丿  D /� SI  铿 he d& J 0� 瘴 F 喵  pC � 兪 � c� ;�  #   �   � 櫠  騇 蛚 氠 � 螑 Kl  {� 傷  SL &� 稶 潈 >� v� �p �  趻 !� 鍅  ]� 跀 攭 �  � � U2  ぞ q p� 8� 寪  3| 樥  � 颫 +> i�  m� mu � �-  v� P	 � Ag  櫹  H�  鼨 哦 �1 挺 � 戂 鞿 x� b' mI  q� [ N� G� � I 健 LO 初 �5 D� �4  _� 鰺  "D �  �, 椀  @h 冕    \  氈  1�  憹  �  `T 垪 y�  璺 -� 彪  (� 5Y V� lF �� X  <  V> 5I 嘻 ﹀  ez  ] r� P�  �5  {� @� 胉 鰷 k� . ?V 3� X� � �  歹 泜 � � 6 x )S M� 梟 J4 � J� 珦 %� % ,� 坏 归  藊 2( _3 � 廨  抵 P5 券  樺 S� 謜    忲 �9 wM  �6 脯 璮  62  盡 .�  漅 蠢  .M  歽  M  膞 纕 Z� 色 >�  j�  �  埪 =   1� 蘄 � ] � 騩  �% �:  �0  c� 讉  毺  Y  !  耨 �7 �/ 堬 硪  e H 6 全 O� 禥  cI :� n� %�  ^� S� 鐂 �	 敏 � #� + 頸 � 9� �  YT 騧 L� q� 华  � 芖  #� J@  2� 名 1> p '  s 菭  fM q� 觶 槏 m  � 产 	6 B� {l )� 1� ZE  �   t  c �4 �+ c% B� 撵  �: �
 _l  |� �5 婏  QU  u2 M( s�  瑎 胪 i _ $�  頩 Ｈ 9� 僢 i� a�  
 �4 w 寐 玣 輝 �  ;   �% �  �
 x � �  鰵 C	 ~� FL g� N�  O=  I� 钖 嗔 5
  =j m   �� 鷢 F� 黽 E� � {�  鉺 夎 �= ~1  鯑 i � �  3� O� 箔 Rq  � 覨  �  稙 饚 s�    誫 &� 偷  � �	 粞  � 釲 胥 攀  w� 閑 F�  禴 #Q  e
 (  e� 屦 � = 镙  芐  戊  
� � � )  兰 ; � � RO   �-  暕  抶  Wh  }� =� � 螝 � J� 尥  ^� � f7 � � 槕 蓜  _3 � 雎 v�  浀  漛 囐 � �2 鲪 �9 粒 �8 蕑  n� 瀏  P  � 3� 哭  韖 � �	 朡 �7 軒 LB 延 >_ ╄ �. 翐 s� �0  9z  庑 o�  �  Hc  辛  �	 c" _u �; 覃 ^� � 8e 嵔 q D� GH � w�  �5 擦 �X  "� 楸 狴 頝 v�  嫗 � 鶠 <+  [ �  �7 丒  帹 ┛  Z�  磁 }  �/ z\ 趋 m" gx �'  熬 OA 酤  峟   , � 槟 s� 僘 a�  � 齼 盾  qx Lv %[ O� 5' 
�  祽 E� 櫧 �  鉷 
| 3� G�   �: 帏 浌 ]k 顓 s�  Y� 3o a% 身 C 詀 7� X  �% 詜  |�  #�  ]% R{  駆 :x 垭 !� 矔 籃 硠 嵬  t 檂 C w 礅 � �# 犤 `5 �5  ,� �,  檙 V  ^x  Q� J� 學 S, � l *� 棢  &  � I� 2�  A< �	 w N> �) �4  X� �$ x� �  系  嫅 侀  o	  陓  硫 阄 ) K�  xD  { 6  U� L�  Ｓ 惖  �#  L� � y� �  c�  Q � 9�  +�   � U� 虠  鰋  汊  � gJ   K L� 苸 屝  嗧 �)  � 卑 � ο  �+ q� G ī C^ �& �!  �  閸 �  唼 笽 �  ╤ 茇 e? </  =� S� W  P   �- 域  %� 峇 潤 q� 珑 瑙 X  Q� x � :!   _@ 琚  � Ee  � 财 摵  蛖 ~. H� 煵  巩 N\ a
 8C ��  #� �  係 F  g� f� 虂 閳 #L 比 D� 炷  啺 '� � 魯 H�  Ta 2�  苮 份   ." 畳 3>  {� 铠 r� 槩  . 慪 _;  Zs ]?   )( M�  }  跲 [B HB Qa  庲  =� S� 犃  a� [� >= 锰  :� � 罣  唍  � 瘜 钪 寂 � #� c�  臎 珷  珰 埗  佄 	� C� g � $� Y 劣 [� Do 鍂    璱 .� � �  邅 
�    e 倻 Z\ ] �( ^  � � 摪  A� �  \� q~ 昇 I� _d \ 5� :� ⑹  飩 p  }�  w� 惾 !� 蜾 脂 |< 筭  x� h[  Jr  壏  喙 诿  A�   9� $�  � :�  嬆  4  失 ,$ 姅  Sc  Z� sO 猍  讟  氜  �  | 合 碾 � |�  � i 1W  p2 � 硡 D� I 僻 鐔 �6 睉  � H  閣 �  K�  [r   �  g�  �
 濚  E�  檓  7H V� r f�  + b  畂 鬇 %� ] 唷  #� 谦  rE 9V `� n� C� �7 鍨 H 歪   酝 Pk 槶 �� 迫 槁  沚 Z }
 f�   釷 姸 豸 �, Em �0 擇 � 摩  }  @  洄  X]  rS t
 暆  伉 g�  憑  # 孓  � E�  �9 8� �=  忕 矌 
� 鋉 jo 憁 B�   D% '� 熏 �  d�  /� �? f� 非 	2  差  �  xK  逘  猀 x� 薑 G� 打 y� �  �  湚 og DI 蟺 |� 6� �- 趤 馃 �  �; 氃   � � 
� 柛 ;� n� � q? 蕞  � Q+ �  PC 蕽 1�  ︵  �2  .� 瘀   �. 绋 Z� 0� 聘 ⑹  � 5m  � a�  H_ 1 8� =e \ 郎   帖 ń a� �9 �9 鴊 � # u � Q � 竟 郋    �> 湜 硃 豲    �  ]� _�  p� 瓡 审 [7 3� 閆  陠  哩 橽 ⒌  R� � Fh  � M 岋 湨 � 糹  彀  
  M� ,� ∕ Ur t� � J) 75    ^ � n� {� zN   甞 c� 乯 �+ �# nR  m #   !7 ie � r] � :� A�  N� 园 葕 芉 獰 屃 #K 朸 jv 11 �% 儡  �= �  RG 靔  �\ 縁 � n> � B� 6�   3� 嘁 k� 祉 崩  � � 敍  �  N v� 臾 颢 �  �5  蛦 耺 U- �& ' �1 �  ~X �  Ks  +R K D � 逷 庢  桤 � 濈  喈 � 崖  Y� x� 讼  	X 禼  � �?  �  剣 F6  P ^B  鎁 ? 媥 d  涱 饡 ?Q Jv 揩  i 6B {� � 稇  % �? 温 ^� � � Y� 讯 �
 蒟 �
  M� ?�  鲩 � 4�  岕 � U� \� 濐  馼 v�  �  H� 紲 � �  S�  �  袔 椃  	k  w� 鞑 m� 搎 B� 肷 见 臞 蔟 瘵 T 孪 � 玷  %! � 鲒 oN  ( � 顛 i� � �  jx  �� L
 d t� (_ 螭 搞 狟 � E� 秚 ]� E0 �  ㄤ 簱 屴 " � � 瞝 侁 �  �   t   冪 Q	  ;� A� 2 掃 扒 嘂 薚 蜡 鈑 � s� �! FT  c� � /� 擣 }�   K� )�  婹 咸   粞  q� � �) 0�  
 �! 茺 � <�  骊 阙 旲 =�  �$  � �) � 彡 � }� 訲 T 祰 磻 +� #G 凕  	� 嗦 <�  z� 蝓 炔 
  鳸 螜  � 皪 ]� �  � 櫞 ,� 峥 眎 槉 <  A f� 7� �9 w� R! L*  嚔 鳨  �- 茽 �  n 溟 裛 �, ) 豵 |? 瑰  >� 頾 J�  軶 ,2 K  v� E� > n� /  a�  ~�  �, 暪 .g 瀬  �  J S�  B� E=  $�  嚎  a� 
� m7 	
  � 鴋 襄  /  膏 � ┘ �
 |� bl � r� 鲐 j� 殌  |I  珈 E�  び �L w� 紽 b 蜹 瞕 % ', 岸 	� t2  掿 1  轍  �  � �  � Ⅷ 渦 ?  F& �  
  C� ^�  昀 蓞 0  M  [�  : S�  羘 �� `| 瓛  捊 %) 褣 � 姵 摲  	�  � 槧 炞 X  � {  器  3
  KI 摚 ?� 嬤 � 錃 R 暰 X y�  � �  ζ n� 蠮 酼 �, 猼 I(  Q\ 恚 眙 1 僊 茱 ! A� R� 詛 嬹 v  燐 XP  撩 �  V� �  � � 抳 R� {4 [� 喙 �  r� 鷀 1� 蔜 &K  胐 Wn x� a� 炲  �; V 嬏 !^ ?<  諠  %6 g > [� 沼  Ej  蟭 �4 � P&  ;� S � > 嚦  堿 耪 豟 �& : |�  r� H8 (U 麨 [� yV  �  伔  �8 抣  ) 猉 %' �# b= 觥 �3 辒 ︳ [  戾  鶁 �: 楺 K� R 羃  廷  
� z� 粖  � 擟 f� W� 赪 �3 `� 缠  @q <l 鸂 R�  秉 $  �6 }� �1 y� 馳 芿 �3   s] 7  钩 c*  6� ;E 欨 D 璅 V' 茪 �  wU � M 亢 �
 逻 � Ld r� ,� o�    � �
  �>  � 臸 悵  俙 鯙 �	 � 渍 氡 e�  巡 筜 崥  惼  � 辣 y#  bY ^d #D y� 金 鶅  豬  x�  �4  +O � 篤  � b� � A   錴 X# 3 �: �4 0 ~� C� 铬 ; 停 d� ey 鄩   
� �:  1 �$  &J  M8  府 膊  �. ;+ 槕  �  擉 q�   踺 nf w�  C8  R G y�  	 劂 亃  �6 L� 瀳 脂  薝 葽 鶓 !	 ;   黳 �) 睿 剑   .    �)  睪 Ⅺ }E M � !5 狭 =� 糓 躺  �  !�  
I  逍 4l  魛 富  稔 � p8 �- �0 b  ~W  e% 0� 﨑  �<   瑞 � NT 邡 鷒 �6  譺 �?  ｉ  o- *� yn  剐 � 概  粢 x m�  @� 羥  ]3  �  皠 6�  K !- q� 緱  脵  O� 這  y 橭 � � 吭 x� 鸗 v� O� 锠  啵 ,  堍 t m� a 擷  �: ^z �  �  槔  � u� 绕  孀 <H s�  � y 包  1� 街 5? ﹞ w� j� ) 鉓 Qk �  Lb  狆  廁 
k = 鴑  �	  E2  �4 ﹙  #l 侚 �  $� 险 "� %�  晠 鋃 楕  糕   	O 谆 敼  <I _E 尕 t�  i�  r � 羳 藔 q  盝 �%   
� e  荸 �'  霈 J" 0J �& 铻 礃  枣 5�   铐 #  m  �   鼝  � 蛤 攤 - 饦 漶 C� zg  壘 �* 1� f  v�  v � 虀  _� 篙 g� � +�  p�  oX  { ┵ 郙 蒟 NW - !� @� U�  �  B�              f  孈  �   `    詟  �  0�  �  樌  �  朽  h  D  &  �  �  腃 �  膄 �  � �  4� �  袅 �   � �    e  (  �  怞 �  擿 �  ,� �  � 9  L� }  � �   7  $  8  t Q  �$ �  8@ �  4`   � :   �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     �18      ?  @�  ��   ��     �<  �<     �=      
 t    蝰
 !    蝰
    
        t        
     蝰
    F   �              __crt_locale_pointers .?AU__crt_locale_pointers@@ 
     >   �              __crt_locale_data .?AU__crt_locale_data@@ 
 	    F   �              __crt_multibyte_data .?AU__crt_multibyte_data@@ 蝰
     * 
 
    locinfo 蝰
    mbcinfo 蝰F   
           __crt_locale_pointers .?AU__crt_locale_pointers@@ 
    
 
    N   �              __crt_locale_data_public .?AU__crt_locale_data_public@@ 蝰
     
     ^ 
     _locale_pctype 篁�
 t    _locale_mb_cur_max 篁�
 u    _locale_lc_codepage 蝰N              __crt_locale_data_public .?AU__crt_locale_data_public@@ 蝰             t                      
              
                  t            t   t      t        
     蝰
 !    
 "    蝰
     蝰
 u    蝰
 !  
�  
 #    蝰
      t                  
 !   
 q    蝰
 ,        -  #    #      .  
 q        q  -  0   q     1  
 ,   
 q        -  q    -     5      -  -   -     7  
 p    蝰
 9        :  #    #      ;      :  t    :     =  
 p        :  :   :     @  
 9   &   �              _GUID .?AU_GUID@@ 
 C   蝰
 D         #     馚 
 "     Data1 
 !    Data2 
 !    Data3 
 F   Data4 &   G           _GUID .?AU_GUID@@ 
 D  ,      I  I   t      J   0      J  
     蝰
 M    
     蝰
 O    
 p    蝰
 Q    
 p    蝰
 S    
     蝰
 U    
     蝰
 W    
     蝰
 Y    
     蝰
 [    
    R   p      ]  
      蝰
 _        T  p          a  
      蝰
 c    
    V         e  
 !    蝰
 g        X            i  
 !    蝰
 k    
    Z         m  
 "    蝰
 o        N            q  
 "    蝰
 s    
 t    蝰
 u    
 t    蝰
 w    
 u    蝰
 y    
 u    蝰
 {    
    \         }  
 #    蝰
         P            �  
 #    蝰
 �    
     
 �    
     
 �    �   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 篁�  JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG �  JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS 蝰Z   t   �  JOB_OBJECT_NET_RATE_CONTROL_FLAGS .?AW4JOB_OBJECT_NET_RATE_CONTROL_FLAGS@@ �
 �    
 �  ,    JOB_OBJECT_IO_RATE_CONTROL_ENABLE   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME �  JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL �  JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP �  JOB_OBJECT_IO_RATE_CONTROL_VALID_FLAGS 篁馴   t   �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS .?AW4JOB_OBJECT_IO_RATE_CONTROL_FLAGS@@ 篁�
 �    
 �  ,  b   �              _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG .?AU_CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG@@ 
 �    & 
 "     Size �
 -   TriggerId b   �           _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG .?AU_CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG@@ 
 -    J   �              _TP_CALLBACK_ENVIRON_V3 .?AU_TP_CALLBACK_ENVIRON_V3@@ 
 �    .   �              _TP_POOL .?AU_TP_POOL@@ 蝰
 �    >   �              _TP_CLEANUP_GROUP .?AU_TP_CLEANUP_GROUP@@ 
 �                   �  
 �    B   �              _ACTIVATION_CONTEXT .?AU_ACTIVATION_CONTEXT@@ 
 �    F   �              _TP_CALLBACK_INSTANCE .?AU_TP_CALLBACK_INSTANCE@@ 
 �        �           �  
 �    f   �      _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> .?AT<unnamed-type-u>@_TP_CALLBACK_ENVIRON_V3@@ 癞    TP_CALLBACK_PRIORITY_HIGH   TP_CALLBACK_PRIORITY_NORMAL 蝰  TP_CALLBACK_PRIORI&
   f  �  I
   �  �     )  �  &
   .  �  I
   �  �  
  J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept 篁� �  �      �  �  $    �  �  4      �  D    (  �  T    ?  �  d    V  �  t    m  �  �    �  �  �   ^     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3dcommon.h 篁� �  �  ^   ^     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\dxgiformat.h 蝰 �  �      �  �  )   �  �  0   �  �  8   �  �  D  n     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3dx12_property_format_table.h  �  �      �  !  %q   �  !  �   �  !  ^     �  
     �  	   "  ~  �   5  �     ?  �  	  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd 蝰 S  �     ]  �  	   r  �  �    {  �  	   �  �  	   �  �  	   �  �  	   �  �  	   �  �  �   �  �  �   �  �  �   �  �  �  6       __acrt_locale_get_ctype_array_value 蠢-叞驴�"       __ascii_tolower 渘蟢"       __ascii_toupper (e�#逝b2       __acrt_get_locale_data_prefix /蹤�!焦蝰"       _chvalidchk_l 遾股�]
蝰     K  IsEqualGUID 覽慩o     L  operator== 馾t
D�"     ^  ReadAcquire8 �1曝�&R篁�"     ^  ReadNoFence8 �H'ZⅢ蝰"     b  WriteRelease8 唍C貲蒝蝰"     b  WriteNoFence8 O熏鵝!苖蝰"     f  ReadAcquire16 蚶}^⌒蝰"     f  ReadNoFence16 嶿9彛蝰"     j  WriteRelease16 ;3G曐�珩"     j  WriteNoFence16 拨癋寮 �     n  ReadAcquire @�
9�     n  ReadNoFence 挓R;�=Y"     r  WriteRelease %$9t�&~篁�"     r  WriteNoFence .+�挋�篁�"     ~  ReadAcquire64 |毿N錄,蝰"     ~  ReadNoFence64 %櫅 w�潋�"     �  WriteRelease64 割�邗0A�"     �  WriteNoFence64 s3�荤默�     ^  ReadRaw8 鹗�(F綌篁�     b  WriteRaw8 襉鄺(苉+蝰     f  ReadRaw16 5岋�-T鶱蝰     j  WriteRaw16 獦I粩H .�     n  ReadRaw 洮6�&駇     r  WriteRaw (>6j(u滙蝰     ~  ReadRaw64 �"{v醈岒�     �  WriteRaw64 m�%钕岶囫.     �  TpInitializeCallbackEnviron 蹰沥
to*     �  TpSetCallbackThreadpool y轛K(>O.     �  TpSetCallbackCleanupGroup 溊0吿�<S蝰.     �  TpSetCallbackLongFunction 祔珞佊(蝰.     �  TpSetCallbackRaceWithDll -箸鄥i仩篁�*     �  TpSetCallbackPriority FP|\ 蝰*     �  TpSetCallbackPersistent !峰噽魭r.     �  TpDestroyCallbackEnviron �获�
f篁�"     �  GetCurrentFiber F�$烏擫&     �  HRESULT_FROM_WIN32 糁9pyb9�     I  fabsf 懕H$醆i蝰     E  fabsl ~�;馗	~#蝰2     Q  __local_stdio_printf_options 帪羻篁�.     Q  __local_stdio_scanf_options #鸅�6棗�"     S  _vfwprintf_l q
'爀�&痼蝰"     S  _vfwprintf_s_l A 耩�H�"     S  _vfwprintf_p_l .臒 瘙I�     S  _vfwscanf_l �姶��"     S  _vfwscanf_s_l 睖脈�姠蝰"     e  _vsnwprintf_l 妈縶嚒7�"     c  _vsnwprintf_s_l 憈sw嶾"     e  _vswprintf_c_l �
Y>,I劀�"     e  _vswprintf_l Yo艾H泽蝰"     i  __vswprintf_l �g鱮^講蝰     n  vswprintf �7F蝰"     e  _vswprintf_s_l #苷�芅岏"     e  _vswprintf_p_l 鼿�3_l�%�"     g  _vscwprintf_l $瓉Y6!C'蝰"     g  _vscwprintf_p_l 9疧kl鉘d     r  _vswscanf_l v鶂:w樼�"     r  _vswscanf_s_l JZ吨託蝰"     w  _vsnwscanf_l Q�9ft蝰"     w  _vsnwscanf_s_l 6轇=5Ｌ婑     {  _vfprintf_l 馁,'塕L�"     {  _vfprintf_s_l �)B枤軉蝰"     {  _vfprintf_p_l _*a�沣蝰     {  _vfscanf_l �5�+聵濜�"     {  _vfscanf_s_l Deri(創篁�"     �  _vsnprintf_l 钡4G椝U
篁�     �  _vsnprintf 0>韝�:�     �  vsnprintf 嫜�5�/鞁蝰     �  _vsprintf_l 跨圳堛〃"     �  _vsprintf_s_l _N葬q�$乞�"     �  _vsprintf_p_l 諕�!5硑蝰"     �  _vsnprintf_s_l 湖�-�"     �  _vscprintf_l 訽Zq1]兡篁�"     �  _vscprintf_p_l 籄K幙谋�"     �  _vscprintf_p 慇Y	4w�'篁�"     �  _vsnprintf_c_l g�ET=>曬     �  _vsscanf_l 菀#�"     �  _vsscanf_s_l �=蒘簌@篁�     �  vsscanf_s �扼E蝰"     g  _vcwprintf_l �膴狜狊蝰"     g  _vcwprintf_s_l 禂蕀蛰��"     g  _vcwprintf_p_l 証躅容駩�     g  _vcwscanf_l %B僮哰�"     g  _vcwscanf_s_l 甃�薈蝰     �  wmemchr 6b
J儔mF     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits 蝰 �        �         �    (    �    v    �    �    �  �  min �1�蓩� �    �    �  �  min 塖姊�8 �    �    �  �  min 埉
1 �       �  �  min 
UUkI篨x �    l   �  �  min 4仹书"勥 �    �   �  �  min 疟[�: �    �   �  �  min �^�蠍 �    �   �  �  min -�>埲军 �       �  �  min 頷@�	 �    H   �  �  min '菵韬�� �    u   �  �  min �,摐�'� �  �  max �5�=E�+ �    �   �  �  min 蔑�x垎� �    �   �  �  min 觮+竰�
 �    �   �  �  min X蹰N��
 �    (   �  �  min q,蹊gK� �    ~    �    T   �  �  max 鰬鮌^2�8 �    �   �  �  max �j'+懙 �    �   �  �  max 捙V艾�"     v  operator new 莜O颠鍈篁�
     std & H  s  _Fnv1a_append_bytes z愳7镅鵧     {ctor} eJ0楍   
  {ctor} 瘓A扙鍽M�     {ctor} 比犍,w�   	  {ctor} /� �J疡     {dtor} Й@珪g蓠     what i岷kw�!l篁�     __delDtor m咄�'忩� 1  A  __delDtor c鲮�?龥打� 1  <  {dtor} 鷙攈y绸 F  O  {ctor} 关�/棉喟� F  W  __delDtor y"吙魔膀� F  R  {dtor} 箦9R廟撷� ]  f  {ctor} \瞾埌#涶 ]  m  __delDtor sV耮向蝰 ]  h  {dtor} 	�#肨絟~� t  {  {ctor} 斩韯�巴� t  }  {dtor} �w.阐曬 t  y  {ctor} S5P�
�
篑" t  �  operator bool $C0,�$S蝰& t  �  _Current_exception 鄲n凋柽� H  �  operator== �yeo`� H  �  operator== 綽m煍v H  �  operator== ;�:噰芕& H  �  current_exception 慞If	Z�蝰& H  �  rethrow_exception L:$�-旙打�. H  *  _Throw_bad_array_new_length 擠諚聖 F  J  {ctor} 鬟婓i禊漶 ]  e  {ctor} ��;嘽� F  N  {ctor} 盕~M垊[捡 �  �  {dtor} Is�8Q偞 �  �  __delDtor �嵾揫骝� �  �  what �>\ k�
烍蝰 �  �  {ctor} J�l4O橊 �  �  {dtor} 悽�"鍋� �  �  {ctor} 倏鄤|羼 �  �  __delDtor 謉Y徜�蝰 �  ~  P    �  �  _Allocate yC羡E抖�2 H  S  _Adjust_manually_vector_aligned %�劻,旰 �  �  _Orphan_all 繅fS遉� "  X  operator= ~8w碯耱�*   2  _Orphan_all_unlocked_v3 �?罰�6   4  _Swap_proxy_and_iterators_unlocked M躯}o i  m  {ctor} 轊允A熍� i  {  _Release ;F撕;�"篁�" H  *  _Xlen_string 萺f�	PK篁� �  �  {ctor} WQQ.C{�锐 �  �  __delDtor 木/u惔侞蝰 �  �  {dtor} �
('℉]躐 �  �  __delDtor `渐驘鑥昨� �  �  {dtor} f�+犜忨 �  �  {ctor} GuuM鵸)堮 �  �  __delDtor 呷V氹gG蝰 �  �  {dtor} ?9Y挊價侎 �    __delDtor M煔﹉n洐蝰 �  
  {dtor} T鰋~{蚒擇   &  __delDtor 堰y湀�6蝰   !  {dtor} 粍鱽�#=柜 ,  5  {ctor} -$}癒c ,  =  __delDtor 崽顜-嵣蝰 ,  8  {dtor} 膨襀鳓凇� C  T  __delDtor k@	二=蝰 C  O  {dtor} ,QY踮�\� Z  k  __delDtor 繁�哸戻汄� Z  f  {dtor} 覶l聨+uF� q  z  {ctor} S缘u8�� q  �  __delDtor X摌U鐲硫� q  }  {dtor} 燘A匍麫}� ,  0  {ctor} =�2,機g� q  y  {ctor} 鳰�=g壐� ,  4  {ctor} う.:沒狇"     �  Safe_UIntAdd �褫Eq泽蝰"     �  Safe_UIntMult No空�B或� �  �     2 �  �  GetHighestDefinedFeatureLevel ��n#蝰" �  �  GetNumFormats 2畇uqY蝰" �  �  GetFormatTable 褵闑"0埋 �  �  Opaque $�<pq/�" �  �  FormatExists q\刧璛'篁�& �  �  GetDetailTableIndex _�雨\@j�* �  �  IsBlockCompressFormat 闋洵~莆蝰& �  �  GetByteAlignment �(嚗軁F
篁�& �  �  GetBitsPerUnitThrow 埪舆玚* �  �  FormatExistsInHeader 咬B悮4殷蝰 �  �  GetName �99冧氄�" �  �  IsSRGBFormat 黢棽�殷蝰&     �  DivideAndRoundUp ｓ�Tp篁�. �  �  CalculateExtraPlanarRows zz慄瘍蝰* �  �  CalculateResourceSize b俘"陛蝰6 �  �  CalculateMinimumRowMajorRowPitch F�尅J嗴蝰6 �  �  CalculateMinimumRowMajorSlicePitch 譚胶Xp⒓�" �  �  GetBitsPerUnit V�決!�& �  �  GetWidthAlignment �*"O|傘G蝰& �  �  GetHeightAlignment 
S鬳r%汃& �  �  GetDepthAlignment 范x鱒毬蝰 �  �  GetFormat �-洽L^唑�* �  �  CanBeCastEvenFullyTyped �9a羨H壒" �  �  GetFormatDetail 榷稲H�& �  �  GetBitsPerStencil �/蟩	蝰" �  �  GetBitsPerDepth pf嚁<!�* �  �  GetFormatReturnTypes n\h赚篁�2 �  �  GetAddressingBitsPerAlignedSize 鏤:(:憅�. �  �  GetNumComponentsInFormat 燊訠懤蜹篁�2 �  �  GetMinNumComponentsInFormats 1rW唧V篁�6 �  �  Sequential2AbsoluteComponentIndex %J�2れ蝰" �  �  DepthOnlyFormat ^`�)h* �  �  SupportsSamplerFeedback ��z嫶" �  �  GetParentFormat R�Yb鐻& �  �  GetFormatCastSet t_�T#篁�" �  �  GetTypeLevel 1皈O
KF后蝰 �  �  GetLayout 詏�7K沈�& �  �  GetComponentName F�Tv篁� �  �  {ctor} f		勄R筢� �  �  {ctor} �钳lO桉 �  �  {ctor} 挛�涯�& �  �  GetBitsPerComponent CM=S�6 �  �  GetFormatComponentInterpretation 鱚��#摅蝰 �  �  Planar W嗫" �  �  NonOpaquePlanar �*廰璀Z �  �  YUV f庳漷鵕l* �  �  FamilySupportsStencil 	�:�晚莉�. �  �  GetDetailTableIndexThrow 瞣Gu\蠷x篁�. �  �  GetDetailTableIndexNoThrow 軗XY
. �  �  GetYCbCrChromaSubsampling )�<挫K茯�& �  �  NonOpaquePlaneCount ,G垦襓Z" �  �  GetTileShape 鏦镴�s篁�" �  �  Get4KTileShape �.蒐�0�. �  �  GetPlaneSliceFromViewFormat Do%P$(鯢 �  �  GetPlaneSubsampledSizeAndFormatForCopyableLayout _I4捪篁�" �  �  GetPlaneCount 忚\��*黩�& �  �  GetMipDimensions 	嚱WE蒹蝰* �  �  DX9VertexOrIndexFormat 鶼.奩��& �  �  DX9TextureFormat 矝眎灀篁�* �  �  FloatNormTextureFormat b>ù�0�& �  �  ValidCastToR32UAV �"=軥萜悟�6 �  �  IsSupportedTextureDisplayableFormat 
�<* �  �  FloatAndNotFloatFormats %锼笆��* �  �  SNORMAndUNORMFormats t�澃爕�/篁馚 �  �  DecodeHistogramAllowedForOutputFormatSupport ma�"cん蝰6 �  �  MotionEstimatorAllowedInputFormat 橼u噇逃=蝰" �  �  SupportsDepth 婳攡妓<v蝰" �  �  SupportsStencil |$� � �  k  {dtor} 5�)黼圈� �    {ctor} (
窖8c (  �  {dtor} )噁縚擇 (  T  {ctor} 靊o7Y瘩 d  �  {dtor} 4轩紳� d  �  {ctor} 伟'3*5� �  #  c_str Q靝俵?:旘� �    {dtor} 騁�翅�� �  �  {ctor} 皾N鏟P麗� �    {dtor} N=�3祚 �  <  {dtor} G*k9� #  Z  {dtor} G>c弽澢G� _  x  {dtor} `�!*偉婉 �  �  {dtor} 玒萯栔曠� ,  �  {dtor} 桄兌~y� h  �  {dtor} %鷩yS笞� �  �  {dtor} @Fq^晁�& �  k  _Tidy_deallocate 8�.K{勫篁� �  �  {dtor} 狔-剿�3�& (  �  _Tidy_deallocate ,無Z'�2篁� �  �  {dtor} �>)3w`�|�& d  �  _Tidy_deallocate $C翨vU篁� �  �  {dtor} �5螘賱沀�& �    _Tidy_deallocate 鞼悢V4篁� �  �  {dtor} 眈袥R'Y� �  �  _Myptr �<埉_馴� �  �  _Getal [�$:#�. �  -  _Deallocate_for_capacity 諞�扗篁�* �  �  _Activate_SSO_buffer h{茊<:7篁�& �  �  _Large_mode_engaged 蒻5�1[� (  �  _Getal Yf�5劫Ve�. (  e  _Deallocate_for_capacity .9关aX篁�* ,  �  _Activate_SSO_buffer 譏]n/O綖篁�& ,  �  _Large_mode_engaged 彆dU� d  !  _Getal Θ x埡珰�. d  �  _Deallocate_for_capacity  *�,医后蝰* h  �  _Activate_SSO_buffer = 4�68`篁�& h  �  _Large_mode_engaged |楆Ji �  ]  _Getal p	稆頥匤�. �  �  _Deallocate_for_capacity 姬2Q�瘦篁�* �  �  _Activate_SSO_buffer 缚$�-两#篁�& �  �  _Large_mode_engaged 靕槴<� �  �  d     �  �       assign W鮖珱W~赳   �  �    +  �  V     	  assign ^浦�+X� A  �  S   �  �  assign |�3�,G靌� V  �  �    e  �  P   �    assign �5泪繕$� �    _Get_first 丫3の鬙� �    deallocate [葛y�b � H  \  max !*湔ǎT! �  :  _Get_first ”鑘轫恬 *  /  deallocate #啊嶤6f� #  X  _Get_first 鈂?涍轿� f  M  deallocate 
I篅fヱ _  v  _Get_first 訅虅
吐V� �  l  deallocate 棣Sw&採* H  �  _Hash_representation �.�n呎祗蝰* H  �  _Hash_representation Ea熪m�1蝰* H  �  _Hash_representation >
疮�*⒁篁�* H  �  _Hash_representation 敷噹�;@惑蝰 H  f  exchange h9eL=篁� �  !  {ctor} 鳅�>趣邱 �  �  _Construct 厔贰廔纛� �  A  {ctor} T �9�� (  �  _Construct l,.倒*振 #  _  {ctor} 拻y镃O4� d  '  _Construct k姗雕 _  �  {ctor} 5�m�燥� �  c  _Construct 馉揞$湧岏& H  �  _Destroy_in_place T坁桃湰L蝰& H  �  _Destroy_in_place �*[盞卟蝰& H    _Destroy_in_place \A蓭N滘�& H  
  _Destroy_in_place �璲糲魯蝰 H    _Unfancy 縈_7�+痓篁� H  0  _Deallocate �テ櫈褒& �  �  _Calculate_growth 稠l剷蟧a蝰 �  �  max_size �t4�W,篁� �  �  {ctor} J昫s脢� �  
  {ctor} 伒_鱪�+�& (  �  _Calculate_growth 殑鴔�j� (  �  max_size o劊N�C篁� ,  �  {ctor} ;�蜨曨犟 *  -  {ctor} 缣毁郗�& d    _Calculate_growth 懆7lx�-Q蝰 d  �  max_size 亁傓氣�篁� h  �  {ctor} 稰弃�-v黢 f  K  {ctor} 愋n礀O�& �  X  _Calculate_growth oh臂j冋� �  0  max_size 齉噱�sR%篁� �  �  {ctor} �
V否 �  j  {ctor} >瘭E咱b� I  K  copy 顎7�.篁� ?  A  copy w婷~,L�%篁� �  )  copy }v愯轫�3篁� 3  5  copy /
\&n'琒篁� �  �  _Getal 给m誔�
�& �  �  _Calculate_growth 洐0+霓蝰 �  �  {ctor} 譩~�g蹏锺 o  ~  �   X  Y  max_size �*�-縰U篁� (  �  _Getal i囙娧O梋�& (  �  _Calculate_growth 08o�7� �  �  {ctor} u7�;N� x  ~  �   a  b  max_size #�%]琚橌蝰 d     _Getal 遙莕鳹a	�& d    _Calculate_growth �-珵�8蝰 �  �  {ctor} 咕1瀾侔� �  ~  �   h  i  max_size Q謚�"�?篁� �  \  _Getal 蔇 V[Us�& �  Z  _Calculate_growth B["n圷蝰 �  �  {ctor} oE銫嫟点� �  ~  �   k  l  max_size  .瓚嶈6篁� �    _Get_first 脧�*7囲赳 �  8  _Get_first 想�5�椕� #  V  _Get_first 鼁Z垼[&� _  t  _Get_first 踠覵淧jh�& H    _Fnv1a_append_value �O9`*∥& H  !  _Fnv1a_append_value �"裊嘂�& H  !  _Fnv1a_append_value  �2zT�& H  #  _Fnv1a_append_value D"ⅶ* �  ,  _Allocate_for_capacity 妜,镜序泷& H  /  _Construct_in_place �焘Og H  1  _Unfancy 翋癭�$`篁�* (  8  _Allocate_for_capacity (~�6�;揆& H  ;  _Construct_in_place 聾眧蠟� H  =  _Unfancy  疬"艏篁�* d  D  _Allocate_for_capacity Vzz[�祓�& H  G  _Construct_in_place (鼱靫G徸 H  �  _Unfancy t▏� 瞘濗蝰* �  N  _Allocate_for_capacity C@tJ栺& H  Q  _Construct_in_place }p崝�'# H  \  min Zv珵�	�. H  t  _Allocate_at_least_helper K"炲a無蝰 H  w  addressof 9w�9� �(蝰 H  y  forward �2符@低. H  {  _Allocate_at_least_helper h�
|薶V蝰 H  |  addressof 稟陜瘿株蝰 H  ~  forward Q=o4G��. H  �  _Allocate_at_least_helper �奖�%矐蝰 H  �  addressof 	i:扎疶m蝰 H  �  forward 拿o �y�. H  �  _Allocate_at_least_helper T厹圬簶*蝰 H  �  addressof 喇fM*蝰 H  �  forward K穠瑘X� �    allocate �.蛂P	)篁� *  1  allocate 牜睘籝A馏蝰 f  O  allocate 鹽�磍L篁� �  n  allocate s○鬐珞蝰" H  �  _Get_size_of_n 腹,_嘠,� H  �  _Allocate ㎜苾�$舔�" H  �  _Get_size_of_n 曻#|~r馄�" H  �  _Get_size_of_n dE笱T"Z�6 H  �  _Allocate_manually_vector_aligned .m?N瓿9.蝰 �  �  6   �  !  �  N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h  �  |  $   �    �    �  w  �    �  !  荱  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h 篁� �  �  �   �  |  �   �  �  �   �  �  �   N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h � �  �  �   �    ;
   �  !  Q   �  V  :	   �  |     �  �  �   �       �  !  �   �    �    �  �  S    �    \   �  �  %   �    m   �  �  �   �  �  �   �  �  l   �  !  �   �  �  �   �  w  �    �  w  �    �    7   �  |  }   �  |  8  6     *  _invalid_parameter_noinfo_noreturn 7毺欛詮�     �  _fstat64i32 5dＩji� �    K   �  w  �   V     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h  �  �  n       �  lstrcmpW 峲銺/堙c篁馬     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h  �  �  A   �  �  ~    �  �  �       �  uaw_wcschr :Y)-dn车�     �  uaw_wcslen #|HDU髾� �  w  �        �  CharUpperW &�濵幧�     >  strrchr 2拋1^睖�     6  wcsrchr [凵�+�.     y  __stdio_common_vfprintf_s �/�箛蝰 �  |  #  *     �  __stdio_common_vsscanf ""健谌:庱R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h  �  �  $       �  lstrcmpiW N嶴堸5~ｒ�.     �  __conio_common_vcwprintf x�嗃G+*篁� �  !  盄   �    '  "     �  uaw_lstrcmpiW 镗卺笴 蝰 �  �  u  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h    �  u    :    5   =  !  �       �  uaw_wcsicmp �*zo�9亐 D  w  �    F  |  �   H  �  �   J  V  �       @  _ldpcomp �<X櫂篁� L  V  �  V     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h � N  �     P  �  �   R    
   T    �    V  �  ]       <  strnlen s�cp6.     �  __conio_common_vcwprintf_p ��J唏 X  !  /3   [           >  strchr �:'�嘫j� ]  !  馪   `  �  �   c    �    h  w  �    l  w  �    o    j       A  strstr エ^.A:�,� q  !  ˊ   s  !  �   u  !  d  "     *  __std_terminate uSw檽\�+ y  w  �    {  !  �   }  �  �     !  u
   �  w  �    �  w  �    �    u       B  frexp �7il�孹滘�&     �  _wsopen_dispatch �=铩v�.
篁�       free 瓡怔e╩篁� �    	  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h  �  �  �    �  �  �    �  V     �  �  9  N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h  �  �         <  _ldsign [r遘鹣N �  !  +V  *     y  __stdio_common_vfscanf 5'LcB趄f� �    �    �    r    �  w  �        D  ldexp n氕薆钇铗� �  �  �   �  �  �   �    �    �  w  �   .     �  __conio_common_vcwprintf_s 嘛g咕d馧     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h  �  �  Z:  "     �  uaw_CharUpperW D硾鸁鞹馧     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h � �  �  �   �    �    �  !  �   �  !  琔   �  |  �   �  !  �7   �  w  �    �  �  �   �  V  *	  .     _  __stdio_common_vswprintf_p 赺庌戸鼐�*     �  __stdio_common_vsprintf 韃CjT詡     8  wcsstr 
挊[v锐 �  |  :)   �    �    �    �    �  |  �"       :  _fdsign 赤)滑擣     6  wcschr 鬡6渟振 �    譈       )  _errno 唒庘E3�.     _  __stdio_common_vswprintf 嬏朹7S篁� �    ~   �    �        F  _copysign 崪㈢4侣简�"       __pctype_func k�咎r擈�     E  _chgsign (,z�%p%篁� �  !  �       �  uaw_wcscpy (臶(牓x� �  w  �    �      r     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\D3D12TokenizedProgramFormat.hpp 篁� �       �  w  {    �    �  "     s  _get_terminate 	dJ猔�     �  _stat64i32 薢5蹀4u�     2  wcstok ;,�7W綊榴*     �  __ExceptionPtrRethrow 株d}[-�*蝰2       __ExceptionPtrCurrentException �*蹝潵t柜        _isctype_l G��-� �  �  U   �  !  �   �    �   �    �        �          6  �  �)       8  wcspbrk 軡猈_     *  terminate R鲌-翉K蝰*     �  __ExceptionPtrCompare +耐M蝰 8  V  D	  .     a  __stdio_common_vsnwprintf_s 椳"捏濟.     �  __stdio_common_vsnprintf_s `4$1�� :  �  
  "     �  uaw_lstrlenW 獇4喡k&M篁� <  !  ~   @  w  �    A       *       __ExceptionPtrDestroy 
�/妦捔欜�*     y  __stdio_common_vfprintf xG踮銕dJ"     0  operator delete ln%�="     s  _get_unexpected 紀1�.     *  _invalid_parameter_noinfo a團阵縜蝰 C  �  �    E  �  �    H    ~  *     �  __ExceptionPtrToBool b噠稵對燇蝰 J    �    L    �   .     �  __stdio_common_vsprintf_s 瓷4?C袮蝰 M    /   O    2   Q    8   t    A   �    �
   �  |  �"       �  rand e栩迻耋蝰*     %  __std_exception_copy ＼?)恉�	篁� �  !  
       �  wcscpy_s 3�讶�	搀蝰 �  |  1   �      .     �  __stdio_common_vsprintf_p 怳u]|S!]蝰 �    �   �    �   .     M  __stdio_common_vfwprintf 犱tw王摡篁� �    �    �       �    �  &     �  __ExceptionPtrSwap Ua鉦丫燽� �    �    �  �  �  *     p  __stdio_common_vswscanf 划�9L钌~ �  w  �    �    /   �  !  �8   �    l   �      *     �  __conio_common_vcwscanf 炞2赕幟�&     �  MapViewOfFileNuma2 eK躢茔eA�"     �  uaw_lstrcmpW 痆怫揫蝰" �  �  __vecDelDtor 血賚W﹗象蝰 �  ~    " H  �  _Xlength_error 蠫Vn泑� �  �  �  " �  �  __vecDelDtor 纾F饄"篁� �  �  �   �  j  U  " q  �  __vecDelDtor 童=�3晆篁� �  �     �  �       �    " �  �  __vecDelDtor .>N耰,掦蝰" ]  m  __vecDelDtor d苂{8�(篁�" ,  =  __vecDelDtor "怎畢帳%篁�"   &  __vecDelDtor a�堆.篁�" Z  k  __vecDelDtor /匏蹧鸓垠蝰" F  W  __vecDelDtor �#�榹+篁� 
  ~       �    J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common 蝰   u  *      �  P     ~    "     __vecDelDtor 9u�^�3蝰" �    __vecDelDtor 瀬�1蝰" �  �  __vecDelDtor S鐴�嬄蝰" 1  A  __vecDelDtor 嫓��1!/篁� !  u  *    #  �  V  " C  T  __vecDelDtor 
`v+�-俖篁� %  �  S  " �  �  __vecDelDtor �?褌^[髴篁� '  ~     7  >  {ctor} 鯸肖H�)M� 7  A  {dtor} 
蜀盬慮G� =  �  ~   [  �  7	   _  w  �   &     �  __ExceptionPtrCopy �&�5铖 b  �  �    d  �  �    f    �    g    �   "     �  operator new 煮g貙I汅蝰     �  _wcsicmp h�9撃l,篁�     �  _wctime64_s 栖b瓷矏     <  _dsign �o,試6I�"     U  __acrt_iob_func ��-曖 i  �  �  *     M  __stdio_common_vfwscanf vVツし�.     �  __ExceptionPtrCopyException +赹�9�3 k  �  �    m  �  �    o    �        A  strpbrk '$[W
堈 q    k   r    o       �  uaw_wcsrchr �+y嘜氡.     y  __stdio_common_vfprintf_p 哜[8蝰     >  _fdpcomp z�7!Ky篁�     �  lstrlenW D奡t^:J篁�     H  VarCmp tg�6坻\� u  �  �   w  �  �   y  �  �   {  �  �       @  _dpcomp ��!4{S ~  w  �   .     _  __stdio_common_vswprintf_s \纣迖N竹�     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\predefined C++ attributes (compiler internal) 篁� �  �  �   �  �  �   �  �  �    �  �  �    �  �  !   �  �     �  �  �   �  �  �   �  �  2    �  �  0    �  �  &    �  �  $    �  �  v   �  �  y   �  �  t   �  w  �   *       __ExceptionPtrCreate 淜蚨t $篁�.     M  __stdio_common_vfwprintf_s =謔S燱耞�.     M  __stdio_common_vfwprintf_p 眏F
�8 狁 �  �  a:       H  modf E晔茄r镢篁� �  �  �    �  �  �    �  w  �   *     �  __ExceptionPtrAssign K44�?篁�*     ,  __std_exception_destroy �-�-穞�2     �  _wctime64 坓殕!uw忩� �           /  wcsnlen 	霩b调^q �       �    �   �    �   �    �   �    �   �    T
   �  �  		     �  .)       q       I       �       E
       �   #    �   %    �  b     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers 馧     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\bin\HostX64\x64\CL.exe 蝰
    -c -ID:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx -Zi -nologo -W4 -WX -diagnostics:column -MP -O2 -Ob2 -D_MBCS -DWIN32 -D_WINDOWS -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DNDEBUG -DRTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\" 
    ) -DCMAKE_INTDIR=\"Release\" -EHs -EHc -MT -GS -fp:precise -Zc:wchar_t -Zc:forScope -Zc:inline -GR -std:c++14 -external:W0 -Gd -TP -errorreport:queue -external:ID:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include -ID:\1softwares\VS2022\VC\Tools\M 
    SVC\14.38.33130\include -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include -ID:\1softwares\VS2022\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include �      -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -I"C:\Program Files 篁�
     (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include 篁耦      -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\include -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -external:I"C:\Program Files 篁耦      (x86)\Windows Kits\10\Include\10.0.22621.0\um" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -external:I"C:\Program 蝰"    �  �  �  �  �  �  �  � �   Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -external:I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -X f     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\src\d3dx12_property_format_table.cpp 蝰~     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\Release\DirectX-Headers.pdb �  �  �  �  �  �  蝰                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    宨      u�  S  � �  �  �  �  �  �  �  �) �  畃  �  篸 �  �  �  �  檺   
  r�        ^� O  {� W  矗 U  Y  ]  a  e  I  M  Q  U  Y  ]  a  e  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  	  
          !  騩 %  	  
          ;m [  c  g  R  _  �! h  嫚 �  �  �  晉 �  �  �  �  AP #  &  J  d  V  籮 �  K= �  �  �  侩 �  �  �.  �            诛 U  e  h  f  �  Y� �   �  �  I  �  �  
  L  M  �      �  *� �  �  �  $  $  %$  ^$  W$  �$  F�  �$  遯 �$  �$  �$  �$  �$  , �$  �$  �$  �$  %  '%  
%  %  %%  � Y%  g%  Z%  a%  �%  �%  �%  �%  �%  �%  �%  �%  僗  �& �5 =� V� �2 #  X3 � �"  膩 ] l8 �5 �	 崼 孹 D� F  %�  � <�  �? 掕 '5 ?s � /� W� ~� 雈 � ?.  蘐 H�  獇 爺 �" 湚 �0 侎  犵 罘 F�  0I 催 襲 徺 �  B� X1 �> っ #.  濨 摮  諶  -� 飢 鼣   綃 � N� �   �. 庙 B . �7  鉕  ﹠ � 罶 �#  �8  =�  � S� 瑤  X{ 侣 忆 Nc 鐟 悕   � b2 �&  �&  �&  �&  �&  +: �&  (W �&  樖 �&  媒 �&  w# �&  �& �&  炏 �&  潫 �&  衹 �&  � �&  � � �&  鳥  �&  �9 �&  均 �&  锩 �&  �&  犜 �&  B  �&  痪 �6 尫 K�  �# �# �, 鴅 � 4� �4 I�  懈  U� 
� }�   懠 �
 C� 箰 B�  馲 襃 $�  �
 �/ 瘺 阰 2� 尪 h 該  zw fs  (� 占 O� <� rl  �&  .W  �- E� l! 比 V� �?  n� 霢  
� $5  堂 |� Y� ~  鐋 x� :� _�  &@ 媮 L � 〩  �,  �* ]) � 嗆  
 淃 �  揋 � 咀  y �&  � ヰ 砼 �  磽 �! 没 �9 嵪 闣  H  �" �) 蕰  ig �/ 貮 喫 �. v�    隈 雷 津  ,  h� 鬬 %/ M� 弈 Z�  韊 ]  �  旙 ň �
 _[ ヨ 跸 � 橈 � 嫓 � e� 塑 �# 横    聒 #�  8� �  堵 E 8� 訮  云 h� � 殏 摫  鄼 �   N 韀  C� =�   颓  侁 } � l
 + 嬒 �  ?�  疺 槽  RH P  g� 黄 X   E�  8 铠   � �8 }�  叩 U� � j�  C  澇 Q!  } H  %� 8. M�  7 � �&  '  ou  ('  '  =� K'  K�  ^'  O'  ^� P� 戏   +^  /� 辖 � *� 謦 )� �+  b  飹 曈 & T  
   '� 賝  � �1 !o 2� 磽  喽  jH +4 8� E,  {� � �&  �  � 嘑 � 蕤  :� � � 褴   :? 甝    2/ )�  }� �3  U'  圏 曨 斀 ��  d'  挋 q� ?� G �'  エ  忝 V� �'  E &  N/ jL ^� 琽 帀  KW   看 me 蒊  芘 ~� B�  � 2� 逅  � 餱  � q�  J� � 朌 婫  ▊ 0 � 剡  KS  戧 |� �; 握 获 � 'c 抱 $  hT �'  �'  �  �'  �'  �'  �'  鬫 �'  �'  �'  �'  蕪 �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  2�  �   �'  �'  慊 �'  魄 麻  �'  �'  �'  �
  �'  A 楏 C  O� �'  }�  �'  � 止 �'  �'  #D �'  fu    "   '   P P   N   T   R   韞  X   :  V   \   Z   `   ^   Yr 辐  d   a   2  g   L   I   T   X   U   為 [   Y   _   虈 c   a   g   e   �   �   �   徆  絡 �  �   '�  �   �   �   �   T� �   �4 �   擰 �   �   �   L& �   �   �   �   ]� 4� �   眪  椵 �   �   �   �   �   �   �   �   �   籾  �< 叩 �   �   �   �   憏 銭 �   墔 ^} �   �   & 餠  �   l� �   �   J� �   �   �   そ 穃 牢 黤 V�  譥  �   �   �   �   !  
!  !  jj ( .�  $!  Q� � "!  e�  (!  L!  K!  4]  劒   �  I!  O!  T!  a* R!  X!  9 W!  U!  [!  `!  �!  �!  � 拄 �!  碑 �!  �!  ~� �!  �!  北  �!  �!  �!  � �!  �!  頇 �!  �!  �!  �!  �!  3} n� 叝  U �!  �� �!  划  �!  �!  fL  �!  �!  "  .k  a 滨  藭  城 驱  "  "  鄠  $"  ""  ("  g�   "� 槸 "  	"  蟂  "  钇 
"  U� z� '"  a"  e"  裤  J"  P"  N"  M"  蒯 6� 
 �, < S"  穬  >� Q"  W"  U"  E ["  Z"  /4  ^� 酁 脙 _"  ]"  c"  a"  单 f"  +R  \- �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  �"  � K4  � �"  
~ �"  �"  �"  " �?  +�  �"  薷 �"  �"  �"  �"  �"  �"  �"   #  #  ##  !#  '#  %#  	#  #  J�  ��  鰥  鞼 悋 d� 鐻  ╬ * w  咀 5� 鐉 /;               @  1  `                                                                                          �18      �  攜   ��   ��     �  �      �      R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h �      i        b  V     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h 篁� H       N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h � �    �2   �    3   �    猈   �    瀆   �    筤   �    羄   �    胇  Z     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h � �  
  �  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h 篁� �    �   R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h 篁� �    �   �    �   �    �=   �    >  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h 蝰               R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h      -   	    �   
      V     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h 篁� D    .  N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h � L    �  Z     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12.h 篁� N  !  �   R  !  �   V  !  8	   Z  !  \	   ^  !  �	   b  !  �	   f  !  ?
   j  !  Y
   n  !  �   r  !  �   v  !  n   z  !  v   ~  !  �   �  !  �   �  !  
   �  !  3
   �  !  A   �  !  ]   �  !  -   �  !  R   �  !  Z   �  !  n   �  !  �   �  !     �  !  +   �  !  w'   �  !  2,   �  !  �.   �  !  �.   �  !  �.   �  !  p/   �  !  ~/   �  !  �4   �  !  �4   �  !  �7   �  !  �7   �  !  /8   �  !  �8   �  !  �8   �  !  �8   �  !  
9   �  !  �9   �  !  o=   �  !  �=   �  !  RQ     !  鱍     !  砋   
  !  鞺     !  V     !  3V     !  ?W     !  IW  b     D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include\directx\d3d12sdklayers.h 蝰   V  -   "  V  �   &  V  �   *  V     .  V     2  !  zr   6  !  +s  Z     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h 蝰 P  ^      X  ^  �   \  ^  �   k  ^  |   t  ^  �  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h 篁� }  d  H  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h  �  f  W    �  f  6    �     q  J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits � �  j  W	   �  j  `	   �  j  i	   �  j  r	  V     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h 篁�   o  0      o      C  o  g    Y  o  r    o  o  �   J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception 篁� �  u  �   �     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\predefined C++ types (compiler internal)  �  w  �    �  u  N   �  u  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility  �  {  C   �  {  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory � �  ~        ~  �     ~  �     ~  �   %  ~  �   6  ~  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h � N  �  [   ^  ~  �   }  ~  �     ~  �   �  ~  �  J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm 篁� �  �  D  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring � �  �  &
   �  �  I
   b  �     �  �  &
   �  �  I
   &  �     a  �         K   x�     蘺     �=  �        V      	   
         
                                                             !   "   #   $   %   &   '   (   )   *   +   ,   -   .   /   0   1   2   3   4   5   6   7   8   9   :   ;   <   =   >   ?   @   A   B   C   D   E   F   G   H   I   J   K   L   M   N   O   P   Q   _   W   X   Y   Z   [   \   ]      R   S   T   U   ^                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   `                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               