d�$ 祚Gh媋 o       .drectve        <  �               
 .debug$S        t' �  d.        @ B.debug$T        p   �.             @ B.rdata          @   $/             @ @@.text$mn        
   d/              P`.debug$S        �   q/ 50        @B.text$mn           q0              P`.debug$S        �   |0 41        @B.text$mn           \1              P`.debug$S        �   s1 ?2        @B.text$mn        �   {2              P`.debug$S        �  63 �5        @B.text$mn        H   �6              P`.debug$S        �  F7 
9        @B.text$mn        q  ": �;         P`.debug$S          �; 籄     J   @B.text$mn        V  烡 鮅         P`.debug$S          1J IX     �   @B.xdata             誡             @0@.pdata             閉 鮙        @0@.xdata              ^ 3^        @0@.pdata             =^ I^        @0@.xdata             g^             @0@.pdata             o^ {^        @0@.xdata          $   橿 絕        @0@.pdata             踍 鏭        @0@.xdata             _ _        @0@.pdata             ;_ G_        @0@.xdata             e_ u_        @0@.pdata             揰 焈        @0@.xdata             絖 蚠        @0@.pdata             隷 鱛        @0@.xdata             `             @0@.pdata             !` -`        @0@.data               K`             @ @�.chks64            k`              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   /  l     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\GeometryPasses.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $math 	 $colors 	 $render  $Json 	 $stdext  �   抒   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align " ;    std::memory_order_relaxed + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_consume " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst R #   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask � #   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size : #   std::integral_constant<unsigned __int64,2>::value a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment j #   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment 3   \ std::filesystem::path::preferred_separator R #   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline \ #   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo i #   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard � #   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free j #   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable _ #   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment q #   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment 5 �    std::filesystem::_File_time_clock::is_steady � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard q #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment h #   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN A #   std::allocator<bool>::_Minimum_asan_allocation_alignment 6 �   std::_Iterator_base0::_Unwrap_when_unverified I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified i #   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment 7 �   std::_Iterator_base12::_Unwrap_when_unverified M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size $ %   std::_Locbase<int>::collate M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets " %   std::_Locbase<int>::ctype G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment l #   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,1>::value #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment c #   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment  %    LightType_None  %   LightType_Directional  %   LightType_Spot  %   LightType_Point  �?   std::_Consume_header  �?   std::_Generate_header W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources + �   donut::math::vector<double,3>::DIM ) �   donut::math::vector<bool,4>::DIM 7 �   std::atomic<unsigned int>::is_always_lock_free J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos + �   donut::math::vector<double,4>::DIM . �   std::integral_constant<bool,1>::value L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos   3        nvrhi::EntireBuffer L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos � #   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment M #   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment Z�    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]�   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard - %    std::integral_constant<int,0>::value O #   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment O #   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment % #   std::ctype<char>::table_size Z %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment M #   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R %   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable Z #   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g�    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible  %   std::_Iosb<int>::in � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot x #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable Z #   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment A #   std::allocator<char>::_Minimum_asan_allocation_alignment c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment : #    std::integral_constant<unsigned __int64,0>::value � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment - �    std::chrono::system_clock::is_steady ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den - 9   std::_Invoker_pmf_refwrap::_Strategy a #   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment - 9   std::_Invoker_pmf_pointer::_Strategy , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos c #   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment . %   donut::math::box<float,3>::numCorners . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard * �        donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < r  ��枠 std::integral_constant<__int64,10000000>::value + �        nvrhi::rt::c_IdentityTransform R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified 1 r   std::integral_constant<__int64,1>::value  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss - �   std::chrono::steady_clock::is_steady % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps & r   std::ratio<1,1000000000>::num $ �5    std::_Num_base::round_style  %    std::_Num_base::digits * r  � 蕷;std::ratio<1,1000000000>::den T #   std::allocator<donut::render::DrawItem>::_Minimum_asan_allocation_alignment ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact Z #   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity 0 �   std::numeric_limits<wchar_t>::is_modulo - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask \ #   std::allocator<donut::render::DrawItem const *>::_Minimum_asan_allocation_alignment O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 B #   std::allocator<float>::_Minimum_asan_allocation_alignment 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10 � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent ; %  �威std::numeric_limits<long double>::min_exponent10  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34 	 !  tm %  7  _s__RTTICompleteObjectLocator2 & 鱥  $_TypeDescriptor$_extraBytes_30 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � +~  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w -~  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 觹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > d 鈣  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c ~  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h ~  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 瞸  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y ~  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > c 寋  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 
~  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 諀  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ] ~  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ [|  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 鴠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 題  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 鋧  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 諁  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > 苶  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 緘  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � ?|  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W祡  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 畗  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 憐  std::_Default_allocator_traits<std::allocator<float> > ; 靭  std::hash<std::shared_ptr<donut::engine::Material> > � l|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ 殅  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � 憓  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ %}  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C }  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � }  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 鱸  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 鰔  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � 珄  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 飢  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 遼  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 瓅  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 讄  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ? 箌  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 z  std::allocator<donut::engine::SkinnedMeshJoint> M M|  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L 磡  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 瘄  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � |  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 爘  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T 巪  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > W 儂  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem const *> > � x|  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � n|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U ]|  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 鐆  std::_Ptr_base<donut::engine::LoadedTexture> :  <  std::_Vector_val<std::_Simple_types<unsigned int> > D O|  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 踫  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � A|  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 =u  std::_Ptr_base<donut::engine::DescriptorHandle> � 2|  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~(|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e bt  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 蕑  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > "坸  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 噞  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > d祔  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > U .y  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w |  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � |  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y 鋥  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 貃  std::allocator<donut::math::vector<float,2> > M 蓒  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = 秡  std::allocator<donut::math::vector<unsigned short,4> > K 縶  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U  u  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 趚  std::_Ptr_base<donut::engine::BufferGroup> F祘  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ >s  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 杮  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 巤  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e 蘳  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N 墈  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > s k:  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � l9  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > { {{  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l   std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > , {  std::allocator<nvrhi::BindingSetItem> K  {  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 鰖  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # �8  std::allocator<unsigned int> � 鑪  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 閟  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 魕  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � w  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � ╬  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > g 搑  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 蘻  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  u  std::allocator<float> � 緕  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1>   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 爖  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t 4 搝  std::allocator_traits<std::allocator<float> > N 厇  std::allocator_traits<std::allocator<donut::render::DrawItem const *> > [ wz  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > l 7k  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > w vq  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > � �:  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > ; �8  std::allocator_traits<std::allocator<unsigned int> > [ cz  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 {u  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Yz  std::hash<std::shared_ptr<donut::engine::MeshInfo> > O 0s  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem> > WUz  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> E Nz  std::_Vector_val<std::_Simple_types<donut::render::DrawItem> > � Dz  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H 蕆  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ mh  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> �  z  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 鎟  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X 	z  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> > . Ni  std::integer_sequence<unsigned __int64>  �  std::_Lockit � 訽  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > �   std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy * C2  std::hash<enum nvrhi::ResourceType> / 豜  std::shared_ptr<donut::engine::Material> - 払  std::reverse_iterator<wchar_t const *> 5 鋂  std::shared_ptr<donut::engine::SceneGraphNode> 9 肵  std::shared_ptr<donut::engine::animation::Sampler> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file � z  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 聎  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >  �(  std::_Big_uint128 / 沑  std::weak_ptr<donut::engine::SceneGraph> 騳  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) U6  std::_Narrow_char_traits<char,int> L 0y  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 觟  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 芢  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > > R �.  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >  f+  std::hash<int>  �5  std::_Num_int_base  錊  std::ctype<wchar_t> " �+  std::_System_error_category � y  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / a2  std::_Conditionally_enabled_hash<bool,1> 2 髕  std::shared_ptr<donut::engine::BufferGroup> � 莤  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::float_denorm_style 4  x  std::shared_ptr<donut::engine::LoadedTexture>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 8g  std::piecewise_construct_t u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 襴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . 'Z  std::_Ptr_base<donut::engine::MeshInfo> 6 2;  std::allocator_traits<std::allocator<wchar_t> > � 蕎  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  &  std::bad_cast B [  std::enable_shared_from_this<donut::engine::SceneGraphNode>  玁  std::equal_to<void> 4 s  std::allocator<donut::math::vector<float,4> > � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } 耟  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 恅  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 硍  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 亀  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o hh  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � je  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > " �5  std::numeric_limits<double>  C&  std::__non_rtti_object < 裋  std::_Ptr_base<donut::engine::DescriptorTableManager> ( 0  std::_Basic_container_proxy_ptr12 � �.  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> 4 <w  std::allocator<donut::math::vector<float,3> > � g  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � *\  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � 鵞  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > 0;  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8> T -w  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �5  std::_Num_float_base � #w  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  *  std::logic_error 3   std::weak_ptr<donut::engine::SceneGraphNode> � Lg  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � 飃  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety P w  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 辷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id ?   std::allocator_traits<std::allocator<unsigned __int64> > : 揨  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 1e  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z   std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> _ 檝  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u hv  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P�0  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl .   std::_Ptr_base<donut::engine::Material> * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> � �.  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � �:  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error d *v  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 鴘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy . 硊  std::initializer_list<unsigned __int64> % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> 7 攗  std::shared_ptr<donut::engine::SceneTypeFactory> � fu  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � ^u  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 纃  std::allocator<unsigned __int64>  h:  std::false_type S �:  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #�0  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ai  std::_Default_allocator_traits<std::allocator<unsigned __int64> >  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � bk  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 Vu  std::shared_ptr<donut::engine::DescriptorHandle> , �5  std::numeric_limits<unsigned __int64> � *u  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L "u  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H 4m  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16> � �.  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void>  ;  std::string_view  w  std::wstring_view � �0  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound � 奼  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > b u  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec �   std::_Compressed_pair<std::allocator<donut::render::DrawItem>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem> >,1> D 鰐  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >   �*  std::_Init_once_completer �'  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � �(  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 j wX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � EX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > � 癵  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ m  std::_Atomic_integral<long,4> � lt  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ dt  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > > 俕  std::enable_shared_from_this<donut::engine::SceneGraph> K Vt  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > dn  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> > � u:  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >  �  std::hash<long double> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy 2 �1  std::equal_to<nvrhi::TextureSubresourceSet> W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � �3  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o �.  std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / =Z  std::shared_ptr<donut::engine::MeshInfo> � 1  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> 5 S\  std::shared_ptr<donut::engine::SceneGraphLeaf> O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> , 甦  std::allocator<std::_Container_proxy> x m:  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception & �.  std::_Zero_then_variadic_args_t � �0  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > d ps  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ t  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � t  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string 鱯  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 鮯  std::_Vector_val<std::_Simple_types<float> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A 雜  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + G  std::pair<enum __std_win_error,bool> � 輘  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error \ 蝧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder ) ~  std::_Atomic_integral_facade<long> 8 琗  std::_Ptr_base<donut::engine::animation::Sampler> % }2  std::hash<enum nvrhi::BlendOp> c   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B 梥  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> [ rs  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > M ds  std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> > ) q2  std::hash<enum nvrhi::BlendFactor> 輌  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 骾  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code J W  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano  �  std::_Iterator_base0 � *6  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > | Hs  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � g  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U @s  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0錰  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �6  std::_Char_traits<char16_t,unsigned short> 6 3q  std::allocator<donut::render::DrawItem const *> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> F 2s  std::allocator_traits<std::allocator<donut::render::DrawItem> > ! �9  std::char_traits<char16_t> 裧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ $s  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > > � �0  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - 糫  std::weak_ptr<donut::engine::Material>  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 Ri  std::integer_sequence<unsigned __int64,0> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char> N Qm  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X s  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  9  std::_Invoker_strategy  	G  std::nothrow_t � n9  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � s  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T �r  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �5  std::_Default_allocate_traits � $e  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � 苧  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > 0 uo  std::_Ptr_base<donut::engine::IShadowMap> . 膔  std::allocator<donut::render::DrawItem> ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> ( 淲  std::array<nvrhi::BufferRange,11> ; ;  std::basic_string_view<char,std::char_traits<char> > c Gq  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � 祌  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 琙  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9羒  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 漴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 時  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock Y 噐  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  }r  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � Kr  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 5g  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D r  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base  r*  std::out_of_range # �5  std::numeric_limits<__int64> _ 鱭  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 苢  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b 坬  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > ~q  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  珸  std::ctype<char> @ bq  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 噈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P Xq  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? Nq  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  ;  std::memory_order Z Iq  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � ;q  std::_Compressed_pair<std::allocator<donut::render::DrawItem const *>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> >,1> � $q  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > q 9a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  縒  std::nullopt_t  罻  std::nullopt_t::_Tag  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K 竝  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 莋  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  [9  std::ratio<1,1>   �8  std::forward_iterator_tag  �*  std::runtime_error � 猵  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ��0  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   	  std::bad_array_new_length T 沺  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> > j ip  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Reallocation_policy E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 'p  std::allocator<donut::engine::animation::Keyframe> K p  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > v W9  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool> � [e  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  5  std::u16string _ p  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 謔  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 韋  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *> ]�'  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >  \  std::nested_exception  �  std::_Distance_unknown ) 榦  std::allocator<nvrhi::BufferRange> ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , %@  std::codecvt<char32_t,char,_Mbstatet> 1 噊  std::shared_ptr<donut::engine::IShadowMap> C 蝑  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F e  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 bo  std::vector<float,std::allocator<float> > F 0o  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 j\  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> � �0  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc } 賒  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J ym  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  f_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 4_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy ; {.  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > � 鬾  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , EO  std::default_delete<std::_Facet_base>  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> C 踤  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . �-  std::vector<bool,std::allocator<bool> > J 蘮  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 沶  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> i ]n  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >  �8  std::ratio<1,10000000> Sn  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d i  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag j 塵  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A {m  std::allocator_traits<std::allocator<unsigned __int64 *> >  �/  std::allocator<char> T jm  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> d 0m  std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> > z   std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Reallocation_policy G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> ) f  std::allocator<unsigned __int64 *>    std::nullptr_t =MY  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > Lh  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K)h  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & �8  std::random_access_iterator_tag ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  x  std::wstring ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � 鷊  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  -*  std::domain_error  �  std::u32string_view � Me  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 俓  std::shared_ptr<donut::engine::SceneGraph>  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 閗  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 穔  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z ]k  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; 鏥  std::weak_ptr<donut::engine::DescriptorTableManager> $ 72  std::hash<nvrhi::IResource *> 4 =\  std::_Ptr_base<donut::engine::SceneGraphLeaf> " 轜  std::_Nontrivial_dummy_type � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet>  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage � K`  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � `  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! �+  std::_System_error_message  �  std::_Unused_parameter = sk  std::allocator<std::shared_ptr<donut::engine::Light> > h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  諻  std::bad_optional_access A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> � �0  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q �1  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > � dk  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  鴋  std::_Exact_args_t � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > Q _k  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � �0  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > � Qk  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base b Ck  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> c 9k  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> � f^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 4^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> �圷  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ^ �4  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 |Z  std::_Ptr_base<donut::engine::SkinnedMeshInstance> v 1  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> ' 訥  std::hash<std::filesystem::path>   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 蘗  std::_Ptr_base<donut::engine::SceneGraphNode> � �6  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m m(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � ;(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy � 蒳  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � 襤  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> �|g  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E @]  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O #]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U !]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t & �i  $_TypeDescriptor$_extraBytes_40 # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling $ �$  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets . �5  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  �!  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16>  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice> !    nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  t$  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # RW  nvrhi::DescriptorTableHandle  �$  nvrhi::TimerQueryHandle 2 RW  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # �$  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice !    nvrhi::VariableShadingRate  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �5  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  ]!  nvrhi::IBindingLayout  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # +k  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline  �$  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �"  nvrhi::ComputeState 2 +k  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �!  nvrhi::IFramebuffer  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats * fZ  donut::engine::SkinnedMeshReference ! 籞  donut::engine::SceneCamera $ H  donut::engine::ICompositeView  ?H  donut::engine::IView $ X[  donut::engine::SceneGraphNode 0 ![  donut::engine::SceneGraphNode::DirtyFlags " Z  donut::engine::MeshInstance   fH  donut::engine::PlanarView ) ZZ  donut::engine::SkinnedMeshInstance ( �&  donut::engine::FramebufferFactory   籢  donut::engine::SceneGraph > 颺  donut::engine::ResourceTracker<donut::engine::MeshInfo> ( h]  donut::engine::AnimationAttribute $ 筜  donut::engine::SceneGraphLeaf ! uW  donut::engine::BufferGroup  榙  donut::engine::Material *  k  donut::engine::Material::HairParams 0 黬  donut::engine::Material::SubsurfaceParams  語  donut::engine::Light ' ℡  donut::engine::SceneContentFlags  礧  donut::engine::MeshInfo & 鎆  donut::engine::DirectionalLight & \]  donut::engine::SceneGraphWalker ( X  donut::engine::animation::Sampler ) 鴍  donut::engine::animation::Keyframe ) 橷  donut::engine::animation::Sequence    donut::engine::MeshType  鯶  donut::engine::SpotLight & 竀  donut::engine::DescriptorHandle , &W  donut::engine::DescriptorTableManager B 鱒  donut::engine::DescriptorTableManager::BindingSetItemsEqual B 餠  donut::engine::DescriptorTableManager::BindingSetItemHasher % _W  donut::engine::VertexAttribute 0 圿  donut::engine::SceneGraphAnimationChannel " 鮦  donut::engine::MeshGeometry % t   donut::engine::DescriptorIndex > 誢  donut::engine::ResourceTracker<donut::engine::Material>   [  donut::engine::PointLight ) 鮙  donut::engine::SceneGraphAnimation  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  /  donut::math::float2  }[  donut::math::dquat # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane  瞇  donut::math::daffine3  燵  donut::math::double3 # �  donut::math::vector<float,4> $ 燵  donut::math::vector<double,3>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes $ }j  donut::math::vector<double,4>  �  donut::math::float4 & 	e  donut::math::matrix<double,3,3> % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> $ 瞇  donut::math::affine<double,3> & }[  donut::math::quaternion<double> # #j  donut::render::IGeometryPass - 宎  donut::render::PassthroughDrawStrategy ) Fa  donut::render::GeometryPassContext 1   donut::render::InstancedOpaqueDrawStrategy # va  donut::render::IDrawStrategy  
j  donut::render::DrawItem M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24 & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers   �         隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽     M]S噴=泥G)w��!&鍌S硚YQD铢g�/  N   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�      [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  ^   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  )   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  k   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�      d蜯�:＠T邱�"猊`�?d�B�#G騋  H   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�      !m�#~6蠗4璟飜陷]�絨案翈T3骮�  b   f扥�,攇(�
}2�祛浧&Y�6橵�  �   曀"�H枩U传嫘�"繹q�>窃�8  �   天e�1濎夑Y%� 褡\�Tā�%&閜�      [届T藎秏1潴�藠?鄧j穊亘^a  _   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   揾配饬`vM|�%
犕�哝煹懿鏈椸  �   縀A�鳏闭�!淠^�楗l鷡犬�)�W	�     藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  e   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   �0�*е彗9釗獳+U叅[4椪 P"��  �   dhl12� 蒑�3L� q酺試\垉R^{i�     Z�=g!9﹞�+庐隀鶋鶾��0L-�誩Q  \   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   �暊M茀嚆{�嬦0亊2�;i[C�/a\  	   j轲P[塵5m榤g摏癭 鋍1O骺�*�  X	   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �	   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �	   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  
   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  J
   繃S,;fi@`騂廩k叉c.2狇x佚�  �
   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �
   +YE擋%1r+套捑@鸋MT61' p廝 飨�     }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  b   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�  )    I嘛襨签.濟;剕��7啧�)煇9触�.  i   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  I
   *u\{┞稦�3壅阱\繺ěk�6U�  �
   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �
   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     桅棙�萑�3�<)-~浰-�?>撎�6=Y}  ]   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  8   鹴y�	宯N卮洗袾uG6E灊搠d�  �   k&�2箍�#た↗�U嬗醇芧'l�-G恇|:  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G     L�9[皫zS�6;厝�楿绷]!��t  J   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  �   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  
   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  I   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   G�膢刉^O郀�/耦��萁n!鮋W VS     l籴靈LN~噾2u�< 嵓9z0iv&jザ  e   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  '   $^IXV嫓進OI蔁
�;T6T@佮m琦�  ^   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  "   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  _   5�\營	6}朖晧�-w氌rJ籠騳榈  �   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  #   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  ]   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �   o藾錚\F鄦泭|嚎醖b&惰�_槮     �
bH<j峪w�/&d[荨?躹耯=�  S   v峞M� {�:稚�闙蛂龣 �]<��  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  #   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  h   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  5   a�傌�抣?�g]}拃洘銌刬H-髛&╟  s   猯�諽!~�:gn菾�]騈购����'  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  J   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  "   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  a   +4[(広
倬禼�溞K^洞齹誇*f�5  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  6   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  o   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   妇舠幸佦郒]泙茸餈u)	�位剎     c�#�'�縌殹龇D兺f�$x�;]糺z�  V   靋!揕�H|}��婡欏B箜围紑^@�銵  �   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   =J�(o�'k螓4o奇缃�
黓睆=呄k_     t�j噾捴忊��
敟秊�
渷lH�#  Z   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   交�,�;+愱`�3p炛秓ee td�	^,  �   zY{���睃R焤�0聃
扨-瘜}     _臒~I��歌�0蘏嘺QU5<蝪祰S  W   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   副謐�斦=犻媨铩0
龉�3曃譹5D      娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  _   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  (    ;o屮G蕞鍐剑辺a岿;q琂謇:謇  p    吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �    評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �     狾闘�	C縟�&9N�┲蘻c蟝2  =!   _O縋[HU-銌�鼪根�鲋薺篮�j��  �!   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �!   �'稌� 变邯D)\欅)	@'1:A:熾/�  "   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  b"   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �"   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �"   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  ;#   蜅�萷l�/费�	廵崹
T,W�&連芿  x#   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �#   D���0�郋鬔G5啚髡J竆)俻w��  $   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  S$   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �$   匐衏�$=�"�3�a旬SY�
乢�骣�  �$   チ畴�
�&u?�#寷K�資 +限^塌>�j  %   悯R痱v 瓩愿碀"禰J5�>xF痧  b%   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �%   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �%   矨�陘�2{WV�y紥*f�u龘��  )&   郖�Χ葦'S詍7,U若眤�M进`  z&   穫農�.伆l'h��37x,��
fO��  �&   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �&   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  )'   鏀q�N�&}
;霂�#�0ncP抝  b'   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �'   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �'   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  ;(   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   �       �  �  [   �  �  �  �  �      �  �  D  �  �  V  �  i   X  �  �   �  X  5   �  X  5   �  X  5   �  X  5   )    V   )    V   �   �(   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\RTXPT\External\Donut\src\render\GeometryPasses.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\RTXPT\External\Donut\include\donut\render\GeometryPasses.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\engine\View.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\RTXPT\External\Donut\include\donut\render\DrawStrategy.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h �       LF)  塇  i    岺  i   
 6J  j    :J  j   
 攕      榮     
 媤      弚     
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    3繦�H堿H嬃�   �   �   7 G            
          X        �nvrhi::Rect::Rect 
 >f   this  AJ        
                         H     f  Othis  O   �               
   �            �  �,   +    0   +   
 \   +    `   +   
 �   +    �   +   
 H�    H嬃�   �   �   U G                   
   �        �nvrhi::VertexBufferBinding::VertexBufferBinding 
 >�!   this  AJ                                 H�     �!  Othis  O ,   ,    0   ,   
 z   ,    ~   ,   
 3狼A  �?H�H堿堿H嬃�   �   �   ? G                      V        �nvrhi::Viewport::Viewport 
 >R   this  AJ                                 H     R  Othis  O   �                  �            i  �,   *    0   *   
 d   *    h   *   
 �   *    �   *   
 H塡$WH冹0H婣H孃H嬞儀 剴   �y tRH婣 H�H;裻FH吷tH�H��怷  H� u
H婥 H�     �!H� H峎H�H�vH��怭  H婥 H�8H婯(H婥L婯L�L�H婼0H塂$ A�R H�H婼H��惱   H婥茾    H媆$@H兡0_�   �   &  [ F            �   
   �   )        �<lambda_6d16b71fffc1163f52255a91a3aad392>::operator() 
 >�   this  AI       �  AJ          >j   material  AK          AM       �  M        �  A N M        �  	U M          U
 >�/   this  AK  ^       AK i       >@    _Result  AK i       M        D  U N N N 0                     H  h   �  �    D  �   @   �  Othis  H   j  Omaterial  9;       �$   9i       &%   9�       j   9�       %   O  �   �           �        |       V  �
   ?  �   B  �0   D  �5   E  �A   G  �H   I  �S   J  �U   M  �o   N  �v   R  ��   T  ��   U  ��   V  �,   0    0   0   
 �   0    �   0   
 �   0    �   0   
 �   0    �   0   
 �   0    �   0   
 /  0    3  0   
 ?  0    C  0   
 a  0    e  0   
 �  0    �  0   
   0      0   
   0      0   
 "  0    &  0   
 <  0    @  0   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   )    0   )   
 g   )    k   )   
 w   )    {   )   
 �   )    �   )   
 �   )    �   )   
 �   )    �   )   
 �   )    �   )   
 �   )    �   )   
 �   )    �   )   
   )      )   
 !  )    %  )   
 1  )    5  )   
 A  )    E  )   
 �  )    �  )   
 H嬆L塇 AWH冹`H塜L孂H塰H嬟H塸H墄餓孁L塰郘嫭$�   M呿tH�I嬚�怭  H媽$�   H��H�3鰦袐鐻�H嬎A�袇�勌   L嫭$�   L塪$PL塼$@@ H�D嬈嬚H嬎�PL嬥H�tH�D嬈嬚L婭H嬒A�袻嬸�E3鯥婨 M嬆H嫈$�   I嬐�H媽$�   I嬙�    秾$�   L嬋圠$8M嬈H媽$�   I嬙H塋$0H媽$�   H塋$(I嬒L塴$ �    H�嬚H嬎��;�俓���L媡$@L媎$PL嫭$�   H媩$XM呿L媗$HH嫶$�   H媗$xH媆$ptI�I嬒H兡`A_H�燲  H兡`A_眯   /      -       �   g  H G            q  
   j  )        �donut::render::RenderCompositeView  >�$   commandList  AJ          AW       \O  >買   compositeView  AI       : AK          >買   compositeViewPrev  AM  &      AP        &  >�&   framebufferFactory  AQ        C  AQ C     
  D�    >襓   rootNode  EO  (           D�    >ra   drawStrategy  AU  r     �  EO  0           D�   
 >j   pass  AJ  K     �  �  EO  8           D�    >j   passContext  AJ  �       EO  @           D�    >@   passEvent  AU  2     @ �  EO  H           D�    >0    materialEvents  A   �       EO  P           D�    >
H    supportedViewTypes  A   P     	  A   Y     �  >u     viewIndex  A   U     �  >H    viewPrev  AV  �     ~    AV �     0 +  
 >H    view  AT  �     �  AT �       >�!    framebuffer  AH  �     <  Z   �  )   `                     @  p   �$  OcommandList  x   買  OcompositeView  �   買  OcompositeViewPrev  �   �&  OframebufferFactory  �   襓  OrootNode  �   ra  OdrawStrategy  �   j  Opass  �   j  OpassContext  �   @  OpassEvent  �   0   OmaterialEvents  9=       &%   9N       j   9_       H   9�       H   9�       H   9�       fa   9      H   9c      �$   O �   �           q       �       �  �*   �  �7   �  �C   �  �P   �  ��   �  ��   �  ��   �  ��   �  ��   �  �$  �  �6  �  �W  �  �]  �  �c  �  �j  �  �,   .    0   .   
 t   .    x   .   
 �   .    �   .   
 �   .    �   .   
 �   .    �   .   
 �   .    �   .   
 �   .       .   
 )  .    -  .   
 9  .    =  .   
 h  .    l  .   
 �  .    �  .   
 �  .    �  .   
 �  .    �  .   
 �  .    �  .   
   .      .   
 ,  .    0  .   
 X  .    \  .   
 p  .    t  .   
 �  .    �  .   
 �  .    �  .   
 �  .    �  .   
 �  .    �  .   
   .    "  .   
 A  .    E  .   
 U  .    Y  .   
 x  .    |  .   
 �  .    �  .   
 �  .    �  .   
 �  .    �  .   
   .      .   
   .      .   
 #  .    '  .   
 3  .    7  .   
 C  .    G  .   
 S  .    W  .   
 c  .    g  .   
 |  .    �  .   
 @USVWATAUAVAWH崿$��H侅  H�    H3腍墔�  L嫢x  H嬞H媿�  M孂H媴p  H嬺H塂$hL嬍I�$H嬔H塋$XI嬏L塂$ L嬅�P3善D$1D嬹H塋$`圠$03襀塋$8A竴  H塎蠩2鞨峂噼    �   H岴鑻�3襢D  H塒鳫�塒茾  �?H岪H冮u錋�   H墪`  H崓h  �    H嬒H崊p  3襀塒鳫�H岪H冮u�W繦墪h  3缐昿  A竫  H墔�  H崓�  H墪t  厛  H墪|  厴  垥�  H墪�  �    H崊�  3蒮怘�H岪H冿u驢�H崟`  H嬑H壗8  H壗@  H壗P  L墋�P H峂鄭W H崏�    H崁�   A�H�I�@�A�H�I�@�A�H�I�@�A�H�I餒冴u� H�H峊$pH嬑�P(兜�  H塢怈坲構H岴蠬塃燞岲$@H塃℉岲$8H塃癏婦$X墠p  H婰$hH塃繦墊$@H墊$HH�墊$PL塭�PH孁H吚劋  fD  H婫H吚剆  L� I;苪禗$08G,u@2鲭@�H婦$`L;鴘T@匂uOD婦$DL媩$X@匂�  I�$H峂蠨禣,I嬜L婫H塋$ I嬏�P禣,E2鞮媤D婦$D圠$0圖$1檩   D婦$DE吚剳   �綀   tJH婦$8L;餿@H吚tH�H嬎�怷  I儈 u	3蒆塋$8�I儈 I峍H�vH�H嬎�怭  L塼$8I�$H峀$@H婽$XL峂蠬塋$ L嬅I嬏�P H�H峊$@H嬎�惱   H婦$`E3繢塂$DL;�����I�$L峂蠰媩$XI嬏L婫 I嬜�PH婫 E2鞤婦$DH塂$`殂��禗$1劺�  E勴uL�H峌蠬嬎A�惏   D婦$DA�H媁H婳荄$|   婤8塂$x媮�   B4婻0憖   塃凥�塙�媝E吚t9T$Hu婰$PA�;蝩
A�繢塂$D闊   H�E吚剟   �綀   tLH婦$8H;鴗BH吚tH�H嬎�怷  H� uH荄$8    �H� H峎H�vH�H嬎�怭  H墊$8I�$H峀$@H塋$ L峂蠭嬏L嬅I嬜�P H�H峊$@H嬎�惱   D$x塼$PD$@H婰$hH��PH孁H吚卛��兜�  I嬛H峂愯    @匂tH億$8 tH�H嬎�怷  H媿�  H3惕    H伳  A_A^A]A\_^[]�   k    �   3    �   3    Q  3      0    >  2       �   $  ? G            V  -   3  )        �donut::render::RenderView  >�$   commandList  AI  7      AJ        7 
 >H   view  AK        K  AL  K     � >H   viewPrev  AP        j  >�!   framebuffer  AQ        A  AW  A     / AW p    � o >ra   drawStrategy  Bh   P      AH  H       AJ  B    � � EO  (           Dp  
 >j   pass  EO  0           Dx   >j   passContext  BX   _     � AH  7      AW  �    ?E �  AW p    � o EO  8           D�   >0    materialEvents  A       Eb � A  p    �#  ( X EO  @           D�   >�!    graphicsState  D�    >0     stateValid  A]  �     � >vW    lastBuffers  AH  �    6! 8 u {   AH W      B`   |     � >0     drawMaterial  A  �    %  9 �  E6u 1   t     � >j    eventMaterial  AH      g
 M D8    >j    lastMaterial  AV  w     � >�   flushDraw  CT  (   4     ' CJ  0   >     $  D�    >�    lastCullMode  A   �    
  A   �      A  �      A  �    "  . �  E6u 0   �     � >�"   currentDraw & Ch     �    c
  ? h � / � ( N 2 Ch    �    O" Z �  � $  7 �
    D@   
 >ia    item  AM  a    ���  AM �      >0     newMaterial  AD  �    �   AD p    �#  ( X
 >�"   args  C      .    �  C     p    �#  ( X Dx    M        )  �* N M        �  �
0* N" M        �  �
 N M        �  �' N% M        �  ��
	O M        X  �� N N% M        �  ��	
! M        V  �� N N5 M        )  /傱n%LG'b  M        �  �( N M        �  	�8 M          �8
 >�/   this  AK  A      AK I    	  >@    _Result  AK I    	  M        D  �8 N N N N0 M        )  /刄i%LG)b >j   material  AM  U    �  AM �      M        �  剛 N M        �  	剶 M          剶
 >�/   this  AK  �      AK �    	  >@    _Result  AK �    	  M        D  剶 N N N N
 Z   )             @          A j h   �  �  �  �    D  �  V  X     +  -  .  /  �  �  �  �  �  �  �  �  )  )  )  
 :�  O  P  �$  OcommandList  X  H  Oview  `  H  OviewPrev  h  �!  Oframebuffer  p  ra  OdrawStrategy  x  j  Opass  �  j  OpassContext  �  0   OmaterialEvents  �   �!  OgraphicsState  8   j  OeventMaterial  �   �  OflushDraw  @   �"  OcurrentDraw Q �  donut::render::RenderView::__l2::<lambda_6d16b71fffc1163f52255a91a3aad392>  x   �"  Oargs  9j       j   9�      #H   9      $H   9[      ja   9�      j   9"      �$   9L      &%   9t      j   9�      %   9�      j   9�      �$   9|      �$   9�      &%   9�      j   9�      %   9�      ja   9-      �$   O�   �          V    9   �      )  �P   *  �m   ,  �o   0  ��   5  ��   3  ��   5  ��   1  ��   5  ��   )  ��   5  �m  7  �z  5  ��  6  ��  7  ��  8  �  V  �  8  �  V  �7  8  �=  X  �B  V  �P  X  �W  V  �[  X  �p  Z  �}  ^  ��  _  ��  a  ��  n  ��  p  ��  s  ��  t  ��  c  ��  f  ��  h  ��  j  ��  k  ��  n  ��  w  ��  y  ��  {  ��  |  ��  �  ��  �  �  �  �%  �  �.  �  �D  �  �L  �  �Q  �  ��  �  ��  X  �  �  �  �  �'  �  �3  �  �,   -    0   -   
 k   -    o   -   
 {   -       -   
 �   -    �   -   
 �   -    �   -   
 �   -    �   -   
 �   -    �   -   
   -      -   
   -      -   
 >  -    B  -   
 N  -    R  -   
 ^  -    b  -   
 v  -    z  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
   -      -   
   -      -   
 L  -    P  -   
 `  -    d  -   
 |  -    �  -   
 �  -    �  -   
 �  -    �  -   
   -      -   
   -      -   
 B  -    F  -   
 ^  -    b  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 $  -    (  -   
 4  -    8  -   
 D  -    H  -   
 T  -    X  -   
 p  -    t  -   
 �  -    �  -   
 �  -    �  -   
 	  -    
  -   
   -    !  -   
 C  -    G  -   
 W  -    [  -   
 �  -    �  -   
 �  -    �  -   
 [  -    _  -   
 k  -    o  -   
 �  -    �  -   
   -      -   
   -      -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
    -    $  -   
 0  -    4  -   
 @  -    D  -   
 P  -    T  -   
 `  -    d  -   
 p  -    t  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
 �  -    �  -   
    -      -   
   -      -   
    -    $  -   
 8  -    <  -   
  d T 4 2p    H           4       4       9    -
 
��	��p`0P    �     1        V          5       5       ?    
 
�	�    
           6       6       E    !
 �	 t d T 4     
          6       6        E    
   r           6       6       K    !
 
� �
 
   r          6       6       K    r   6          6       6       Q    !   
   r          6       6       K    6  W          6       6       W    !       
          6       6       E    W  q          6       6       ]    
 
4 
Rp    �           0       0       c        ����    ����        ��������噾姏@|#�8B訠=*�(.瞗輩耾"0� T(倴
骞循/久
3>飖9屓D6b�搿E閈原曫A俧�.舕燥琣�!\芤ほ痡n� �蹰k�.�D�3�撰\#"㈩�娳掺P"9猰x�翭F{'yZ祼垩寯啦臿q�3巹YL蚵}wg�纊:;+%M凪延�.Dホp颴
稣单垱諒�&蒭庋WfJM躹0鲎!2逧ev檵�2膭:�%啉Q8覘:Xd鰰�m-u"`@衔K��H        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       t'              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       
       �9�     .debug$S       �              .text$mn              �邆     .debug$S       �              .text$mn    	          痖I     .debug$S    
   �          	    .text$mn       �       厨剽     .debug$S       �             .text$mn    
   H       襶.      .debug$S       �         
    .text$mn       q     �7	G     .debug$S         J           .text$mn       V     坎:V     .debug$S         �               \       
        x       	        �               �               �               v              n               �                             -           memset           $LN13       
    $LN170          $LN21           .xdata                F┑@
        E          .pdata               X賦�
        i          .xdata                鈘�+        �          .pdata               嬲q�        :          .xdata                z�        �          .pdata               ��        �          .xdata         $      z餲        �          .pdata               椱p        �          .xdata               @        �          .pdata               %N�-        �	          .xdata               飣嵣        �
          .pdata               O��        �          .xdata               =�R"        �          .pdata                郞        �
           .xdata      !          ug刉        �      !    .pdata      "         袷湅        G      "    .data       #           烀�          �      #        �     #        �           _fltused         .chks64     $                      ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??0Viewport@nvrhi@@QEAA@XZ ??0Rect@nvrhi@@QEAA@XZ ??0VertexBufferBinding@nvrhi@@QEAA@XZ ?RenderView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVIView@engine@2@1PEAVIFramebuffer@4@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@_N@Z ?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z ?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z ??R<lambda_6d16b71fffc1163f52255a91a3aad392>@@QEBA@PEBUMaterial@engine@donut@@@Z __GSHandlerCheck __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$?RenderView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVIView@engine@2@1PEAVIFramebuffer@4@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@_N@Z $pdata$?RenderView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVIView@engine@2@1PEAVIFramebuffer@4@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@_N@Z $unwind$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $pdata$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $chain$4$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $pdata$4$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $chain$6$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $pdata$6$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $chain$7$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $pdata$7$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $chain$8$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $pdata$8$?RenderCompositeView@render@donut@@YAXPEAVICommandList@nvrhi@@PEBVICompositeView@engine@2@1AEAVFramebufferFactory@62@AEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEAVIDrawStrategy@12@AEAVIGeometryPass@12@AEAVGeometryPassContext@12@PEBD_N@Z $unwind$??R<lambda_6d16b71fffc1163f52255a91a3aad392>@@QEBA@PEBUMaterial@engine@donut@@@Z $pdata$??R<lambda_6d16b71fffc1163f52255a91a3aad392>@@QEBA@PEBUMaterial@engine@donut@@@Z ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B __security_cookie 