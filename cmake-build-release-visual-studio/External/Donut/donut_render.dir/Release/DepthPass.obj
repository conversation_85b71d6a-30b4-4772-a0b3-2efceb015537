d嗋眈Gh疳 �      .drectve        <  K               
 .debug$S        复 PL          @ B.debug$T        p   X             @ B.rdata          @   �             @ @@.text$mn        :    B         P`.debug$S          ` l        @B.text$mn        0   � (         P`.debug$S        �  2         @B.text$mn        c   �              P`.debug$S        �  	 �	        @B.text$mn        p   
              P`.debug$S        �  }
 !        @B.text$mn        +  � �         P`.debug$S        �    �      Z   @B.text$x            ($ 4$         P`.text$x            >$ J$         P`.text$mn        �   T$              P`.debug$S           �$ �'        @B.text$mn            x(              P`.debug$S        \  �( �+        @B.text$mn           �,              P`.debug$S          �, �-        @B.text$mn           �-              P`.debug$S          �- 
/        @B.text$mn           F/              P`.debug$S        �   J/ "0        @B.text$mn        �  ^0 2         P`.debug$S        �  W2 G     �   @B.text$x            7L CL         P`.text$x            ML ]L         P`.text$x            gL wL         P`.text$x            丩 慙         P`.text$x            汱 獿         P`.text$x            礚 臠         P`.text$x            螸 週         P`.text$x            長 鵏         P`.text$x            M M         P`.text$x         .   M KM         P`.text$x            _M rM         P`.text$x            |M 廙         P`.text$x            橫 琈         P`.text$x            禡 蒑         P`.text$x            覯 鉓         P`.text$x            鞰 齅         P`.text$mn        �   N              P`.debug$S        �   蒒 昈        @B.text$mn           絆              P`.debug$S        �   蜲 哖        @B.text$mn           甈              P`.debug$S        �   筆 qQ        @B.text$mn        <   橯 誕         P`.debug$S        0  驫 #S     
   @B.text$mn        <   嘢 肧         P`.debug$S        L  酳 -U     
   @B.text$mn        !   慤 睻         P`.debug$S        <  芔 W        @B.text$mn        2   >W pW         P`.debug$S        <  刉 繶        @B.text$mn        "   8Y              P`.debug$S        �  ZY 騔        @B.text$mn        "   抂              P`.debug$S        �  碵 H]        @B.text$mn        "   鑍              P`.debug$S        �  
^ 朹        @B.text$mn        "   6`              P`.debug$S        �  X` 鋋        @B.text$mn        "   刡              P`.debug$S        �   Fd        @B.text$mn        "   鎑              P`.debug$S        �  e 渇        @B.text$mn        "   <g              P`.debug$S        �  ^g 阧        @B.text$mn           奿 瀒         P`.debug$S        l  ╥ l        @B.text$mn        e   dl 蒷         P`.debug$S        X  鏻 ?r        @B.text$mn        [   s bs         P`.debug$S        $  vs 歸        @B.text$mn        J   vx 纗         P`.debug$S        �  蕏 V|        @B.text$mn           } )}         P`.debug$S        4  3} g~        @B.text$mn        }              P`.debug$S        �  4  �        @B.text$mn           鼊 �         P`.debug$S          � &�        @B.text$mn        K   b�              P`.debug$S        �  瓎 崍        @B.text$mn        K   �              P`.debug$S        �  d� H�        @B.text$mn           詪 賸         P`.debug$S        �  銒 繋        @B.text$mn        [   鐜 B�         P`.debug$S        T  V� 獟        @B.text$mn        `   啌 鎿         P`.debug$S        �  鷵 簴        @B.text$mn        c  n� 褬         P`.debug$S        �  鶚 y�     B   @B.text$mn        �   
� 饥         P`.debug$S        �  孝 敠     *   @B.text$mn           8�              P`.debug$S        �   ;� �        @B.text$mn        ~  [� 侏         P`.debug$S        \	  � q�     P   @B.text$mn        ^   懛 锓         P`.debug$S        �  � 缀        @B.text$mn           嫽              P`.debug$S        �   幓 n�        @B.text$mn            郊         P`.debug$S        �   鸭 到        @B.text$mn           萁 鸾         P`.debug$S        �   � 渚        @B.text$mn            � %�         P`.debug$S        �   /� 罂        @B.text$mn        +   � F�         P`.debug$S        �   Z� V�        @B.text$mn        4   捔 屏         P`.debug$S        �   诹         @B.text$mn        !   媛 �         P`.debug$S        �   � 衩        @B.text$mn        B   -� o�         P`.debug$S           嵞 嵟        @B.text$mn        B   膳 �         P`.debug$S          )� 9�        @B.text$mn        B   u� 非         P`.debug$S        �   涨 讶        @B.text$mn        H   
�              P`.debug$S        �  U� �        @B.text$mn        $  1� U�         P`.debug$S        �  胄 o�     �   @B.text$x             混         P`.text$x            澎 侦         P`.text$x            哽 镬         P`.text$x             	�         P`.text$x            � #�         P`.text$x            -� =�         P`.text$x            G� W�         P`.text$mn        �  a� <�         P`.debug$S        �  d� ,�        @B.text$mn        �  囿 �         P`.debug$S        `  货 �        @B.text$mn        �    掹     
    P`.debug$S        p  鳅 f�        @B.text$x            ~  �          P`.text$x            �  �          P`.text$x         *   �  �          P`.text$mn        �  �  �     	    P`.debug$S          � �
     ,   @B.text$x            � �         P`.text$x            � �         P`.text$mn        s   � ^
         P`.debug$S        �  |
         @B.text$mn        �   � 3         P`.debug$S        �  [         @B.text$mn          � �         P`.debug$S        �  � �     H   @B.text$mn        �  �! 6#         P`.debug$S        (  @# h+     @   @B.text$x            �- �-         P`.text$mn           �-              P`.debug$S        �   . �.        @B.text$mn        W  8/ �2         P`.debug$S        <  �2 E     �   @B.text$x            縆 薑         P`.text$mn        &   誎 鸎         P`.debug$S        (  L 7M        @B.text$mn        r   嘙 鵐         P`.debug$S        �  
N 筆        @B.text$mn        �  絈 揝         P`.debug$S        �  盨 9X     (   @B.text$mn        L  蒠 \         P`.debug$S        �
  Q\ 鵩     `   @B.text$x            筳 舑         P`.text$mn        �   蟡              P`.debug$S        �  媖 Wn        @B.text$mn        :  僶 絧         P`.debug$S        �  鵳      <   @B.text$mn           鵽              P`.debug$S          { #|        @B.text$mn        	   s| ||         P`.debug$S          唡 瀩        @B.text$mn        �  趠 �         P`.debug$S        �  � 瑡     P   @B.text$mn           處              P`.debug$S        ,  蠋 麖        @B.text$mn            K� k�         P`.debug$S        �   墣 M�        @B.text$mn        {  墤 �         P`.debug$S        �
  �  �     H   @B.text$mn        �   小 牏         P`.debug$S        X  劲 �        @B.text$mn           .� A�         P`.debug$S        �   K� �        @B.xdata             [�             @0@.pdata             o� {�        @0@.xdata             櫔             @0@.pdata             —         @0@.xdata             霜             @0@.pdata             转 悛        @0@.xdata             �             @0@.pdata             	� �        @0@.xdata             3�             @0@.pdata             ?� K�        @0@.xdata             i�             @0@.pdata             q� }�        @0@.xdata             洬             @0@.pdata             Й 倡        @0@.xdata             勋             @0@.pdata             佾 瀚        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             5�             @0@.pdata             =� I�        @0@.xdata             g� w�        @0@.pdata             嫭 棳        @0@.xdata          	   惮 粳        @@.xdata             椰 噩        @@.xdata             猬             @@.xdata             瀣 醅        @0@.pdata             	� �        @0@.xdata          	   3� <�        @@.xdata             P� V�        @@.xdata             `�             @@.xdata             c� s�        @0@.pdata             嚟 摥        @0@.xdata          	   杯 涵        @@.xdata             苇 原        @@.xdata             蕲             @@.xdata             岘 醐        @0@.pdata             	� �        @0@.xdata          	   3� <�        @@.xdata             P� V�        @@.xdata             `�             @@.xdata             f� v�        @0@.pdata             姰 柈        @0@.xdata          	   串 疆        @@.xdata             旬 桩        @@.xdata             岙             @@.xdata             洚 舢        @0@.pdata             � �        @0@.xdata          	   2� ;�        @@.xdata             O� U�        @@.xdata             _�             @@.xdata             b�             @0@.pdata             j� v�        @0@.xdata             敮 く        @0@.pdata             腐 寞        @0@.xdata          	   獐 氙        @@.xdata             �� �        @@.xdata             �             @@.xdata             �             @0@.pdata             � &�        @0@.xdata             D� X�        @0@.pdata             v� 偘        @0@.xdata             牥 鞍        @0@.pdata             伟 诎        @0@.voltbl                           .xdata              
�        @0@.pdata             � *�        @0@.xdata          	   H� Q�        @@.xdata             e� k�        @@.xdata             u�             @@.xdata             x� 敱        @0@.pdata             ū 幢        @0@.xdata          	   冶 郾        @@.xdata             锉 醣        @@.xdata             ��             @@.xdata              � "�        @0@.pdata             6� B�        @0@.xdata          	   `� i�        @@.xdata             }� 儾        @@.xdata             嵅             @@.xdata             敳 安        @0@.pdata             牟 胁        @0@.xdata          	   畈 鞑        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             *� 6�        @0@.xdata             T� l�        @0@.pdata             姵 柍        @0@.xdata             闯 瘸        @0@.pdata             娉 虺        @0@.xdata             �  �        @0@.pdata             >� J�        @0@.xdata             h� 劥        @0@.pdata             ⒋         @0@.xdata             檀 艽        @0@.pdata              �        @0@.xdata             $�             @0@.pdata             0� <�        @0@.xdata             Z� r�        @0@.pdata             惖 湹        @0@.xdata             旱 实        @0@.pdata             璧 舻        @0@.xdata             � *�        @0@.pdata             H� T�        @0@.xdata             r�             @0@.pdata             z� 喍        @0@.xdata             ざ             @0@.pdata              付        @0@.xdata             侄             @0@.pdata             薅 甓        @0@.xdata             � �        @0@.pdata             :� F�        @0@.xdata             d� t�        @0@.pdata             挿 灧        @0@.voltbl            挤               .xdata             痉             @0@.pdata             史 址        @0@.xdata             舴             @0@.pdata              � �        @0@.xdata             *� F�        @0@.pdata             Z� f�        @0@.xdata          	   劯 嵏        @@.xdata             「 掣        @@.xdata             迅             @@.xdata             芨             @0@.pdata             涓 鸶        @0@.xdata             � *�        @0@.pdata             4� @�        @0@.xdata             ^� z�        @0@.pdata             劰 惞        @0@.xdata          $    夜        @0@.pdata             婀 蚬        @0@.xdata          	   � �        @@.xdata             -� 3�        @@.xdata          
   =�             @@.xdata          $   J� n�        @0@.pdata             偤 幒        @0@.xdata          	    岛        @@.xdata             珊 蘸        @@.xdata          	   楹             @@.xdata             蚝             @0@.pdata              �        @0@.xdata          $   $� H�        @0@.pdata             \� h�        @0@.xdata          	   喕 徎        @@.xdata          6   ； 倩     
   @@.xdata          2   =�             @@.xdata             o� 嚰        @0@.pdata             浖 Ъ        @0@.xdata          	   偶 渭        @@.xdata             饧 蠹        @@.xdata             �             @@.xdata             � 7�        @0@.pdata             K� W�        @0@.xdata          	   u� ~�        @@.xdata          W   捊 榻        @@.xdata             摼             @@.xdata             灳             @0@.pdata              簿        @0@.voltbl            芯                .xdata              跃 艟        @0@.pdata             � �        @0@.xdata          	   2� ;�        @@.xdata             O� a�        @@.xdata             �             @@.voltbl         
   櫩                .xdata             ？             @0@.pdata              房        @0@.xdata             湛             @0@.pdata             砜         @0@.xdata             � /�        @0@.pdata             C� O�        @0@.xdata          	   m� v�        @@.xdata          
   娎 椑        @@.xdata          
                @@.xdata             咐 岳        @0@.pdata             枥 衾        @0@.xdata          	   � �        @@.xdata             /� 5�        @@.xdata             ?�             @@.xdata             G� W�        @0@.pdata             a� m�        @0@.xdata             嬃 Ａ        @0@.pdata             妨 昧        @0@.xdata          	   崃 炅        @@.xdata              �        @@.xdata          	   �             @@.voltbl            �               .xdata             �             @0@.pdata             '� 3�        @0@.xdata             Q�             @0@.pdata             Y� e�        @0@.xdata             兟 熉        @0@.pdata             陈 柯        @0@.xdata          	   萋 媛        @@.xdata               �        @@.xdata          
   
�             @@.xdata              � 4�        @0@.pdata             H� T�        @0@.xdata          	   r� {�        @@.xdata             徝 澝        @@.xdata          
   泵             @@.xdata             幻             @0@.pdata             妹 厦        @0@.xdata             砻 �        @0@.pdata             � +�        @0@.xdata             I� Y�        @0@.pdata             w� 兡        @0@.xdata             ∧             @0@.pdata             ┠ 的        @0@.xdata             幽 隳        @0@.pdata             髂 �        @0@.xdata          	   !� *�        @@.xdata             >� D�        @@.xdata             N�             @@.xdata             Q�             @0@.pdata             Y� e�        @0@.xdata             兣             @0@.pdata             徟 浥        @0@.rdata             古 雅        @@@.rdata             锱             @@@.rdata             � �        @@@.rdata             7� O�        @@@.rdata             m�             @@@.xdata$x           偲 炂        @@@.xdata$x           财 纹        @@@.data$r         /   炱 �        @@�.xdata$x        $   %� I�        @@@.data$r         $   ]� 伹        @@�.xdata$x        $   嬊         @@@.data$r         $   们 缜        @@�.xdata$x        $   袂 �        @@@.data               )�             @ @�.rdata          8   I� 伻        @@@.rdata          �   侨 G�        @@@.rdata             缟             @@@.rdata                          @@@.rdata             �             @@@.rdata          
   %�             @@@.rdata             2�             @0@.rdata             7�             @@@.rdata          	   R�             @@@.rdata          	   [�             @@@.rdata          
   d�             @@@.rdata             n�             @@@.rdata          (   ~� κ        @@@.rdata             厥             @@@.rdata             笫             @@@.rdata$r        $   
� 1�        @@@.rdata$r           O� c�        @@@.rdata$r           m� y�        @@@.rdata$r        $   兯         @@@.rdata$r        $   凰 咚        @@@.rdata$r            �        @@@.rdata$r           � /�        @@@.rdata$r        $   C� g�        @@@.rdata$r        $   {� 熖        @@@.rdata$r           教 烟        @@@.rdata$r           厶 魈        @@@.rdata$r        $   � 9�        @@@.data$rs        *   M� w�        @@�.rdata$r           佂 曂        @@@.rdata$r           熗         @@@.rdata$r        $   低 偻        @@@.rdata$r        $   硗 �        @@@.data$rs        1   /� `�        @@�.rdata$r           j� ~�        @@@.rdata$r           埼 斘        @@@.rdata$r        $   炍 挛        @@@.rdata$r        $   治         @@@.data$rs        -   � E�        @@�.rdata$r           O� c�        @@@.rdata$r           m� 佅        @@@.rdata$r        $   曄 瓜        @@@.rdata$r        $   拖 裣        @@@.data$rs        Q   � `�        @P�.rdata$r           j� ~�        @@@.rdata$r           埿 溞        @@@.rdata$r        $   靶 孕        @@@.rdata             栊             @0@.debug$S        `   煨 L�        @B.debug$S        @   `� 犙        @B.debug$S        D   囱         @B.debug$S        4   � @�        @B.debug$S        4   T� 堃        @B.debug$S        @   溡 芤        @B.chks64            鹨              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   L  g     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\DepthPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $vfs  $math 	 $colors 	 $render  $Json 	 $stdext �   �  R #   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align " ;    std::memory_order_relaxed " ;   std::memory_order_consume + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment + �   donut::math::vector<double,4>::DIM   8   _Mtx_try   8   _Mtx_recursive E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment  �8   std::_INVALID_ARGUMENT  �8   std::_NO_SUCH_PROCESS & �8   std::_OPERATION_NOT_PERMITTED , �8   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - �8   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN �   �  % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst T #   std::allocator<donut::render::DrawItem>::_Minimum_asan_allocation_alignment C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable M #   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,2>::value ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable i #   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment R #   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment O #   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable Z %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size % #   std::ctype<char>::table_size M #   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes � #   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment �   7  R %   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment T %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size \ #   std::allocator<donut::render::DrawItem const *>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable  �   幄  E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE # %   std::_Iosb<int>::showpoint f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask ! %    std::_Iosb<int>::showpos q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val  %   std::_Iosb<int>::hex m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio / �   std::atomic<long>::is_always_lock_free % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield x #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment , %  @ std::_Iosb<int>::_Default_open_prot �    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi �   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment _ #   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment - �    std::chrono::system_clock::is_steady $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den 8 �   std::atomic<unsigned long>::is_always_lock_free / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den A #   std::allocator<bool>::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 '�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard < r  ��枠 std::integral_constant<__int64,10000000>::value I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment 1 r   std::integral_constant<__int64,1>::value - �   std::chrono::steady_clock::is_steady � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment & r   std::ratio<1,1000000000>::num * r  � 蕷;std::ratio<1,1000000000>::den Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment  %    LightType_None  %   LightType_Directional  %   LightType_Spot  %   LightType_Point  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment = �   donut::engine::c_MaxRenderPassConstantBufferVersions . %   donut::math::box<float,2>::numCorners  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN 7 �   std::atomic<unsigned int>::is_always_lock_free   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den � #   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment ��    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi ��   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard � #   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard O #   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources ) �   donut::math::vector<bool,4>::DIM 3   \ std::filesystem::path::preferred_separator J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos Z #   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi . �   std::integral_constant<bool,1>::value L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos - %    std::integral_constant<int,0>::value   3        nvrhi::EntireBuffer L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment Z #   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified \ #   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding const &,donut::engine::MaterialResourceBinding &>::_Same_size_and_compatible � �   std::_Trivial_cat<donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding const &,donut::engine::MaterialResourceBinding &>::_Bitcopy_constructible � �   std::_Trivial_cat<donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding,donut::engine::MaterialResourceBinding const &,donut::engine::MaterialResourceBinding &>::_Bitcopy_assignable � #   std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment j #   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment c #   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment f�    std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi i�   std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard � #   std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,1>::value x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment q #   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 Z #   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits 5 �    std::filesystem::_File_time_clock::is_steady y#   std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size y#   std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets s�    std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi A #   std::allocator<char>::_Minimum_asan_allocation_alignment : #    std::integral_constant<unsigned __int64,0>::value ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment 5 #    donut::render::DepthPass::PipelineKey::Count ) 9    std::_Invoker_functor::_Strategy q #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment , 9   std::_Invoker_pmf_object::_Strategy � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment - 9   std::_Invoker_pmf_refwrap::_Strategy B #   std::allocator<float>::_Minimum_asan_allocation_alignment - 9   std::_Invoker_pmf_pointer::_Strategy h #   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM + �        nvrhi::rt::c_IdentityTransform i #   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified c #   std::allocator<donut::engine::MaterialResourceBinding>::_Minimum_asan_allocation_alignment  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix � #   std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix . %   donut::math::box<float,3>::numCorners ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix $ %   std::_Locbase<int>::collate " %   std::_Locbase<int>::ctype % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all * %   std::numeric_limits<bool>::digits ! %    std::_Locbase<int>::none u�    std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi x�   std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment l #   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 � #   std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask 0 �   std::numeric_limits<wchar_t>::is_modulo O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size c #   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 j #   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10  �?   std::_Consume_header  �?   std::_Generate_header 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10    �   �  , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent   �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �    �#   std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi + �   donut::math::vector<double,3>::DIM  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  5,  _Thrd_result  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34  D<  _Stl_critical_section 	 !  tm %  7  _s__RTTICompleteObjectLocator2 & 鱥  $_TypeDescriptor$_extraBytes_30 & �<  $_TypeDescriptor$_extraBytes_29 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  仏  DepthPushConstants  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � +~  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w -~  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 觹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 2<  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > d 鈣  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c ~  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h ~  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 瞸  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y ~  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � #<  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � <  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鸖  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > c 寋  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 
~  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 諀  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > � 
T  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > ] ~  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ [|  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 鴠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 題  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 鋧  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 諁  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 4<  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > 苶  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 緘  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � ?|  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W祡  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 畗  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 憐  std::_Default_allocator_traits<std::allocator<float> > ; 靭  std::hash<std::shared_ptr<donut::engine::Material> > � l|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > � %<  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ 殅  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � 憓  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ %}  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C }  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > iT  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � }  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 鱸  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 鰔  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � 珄  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 飢  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 遼  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 瓅  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 讄  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ? 箌  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 z  std::allocator<donut::engine::SkinnedMeshJoint> M M|  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > � T  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > T j�  std::_Compressed_pair<std::equal_to<donut::engine::Material const *>,float,1> � 腟  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > L 磡  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 瘄  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � |  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 爘  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T 巪  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > W 儂  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem const *> > � x|  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � n|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U ]|  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > :  <  std::_Vector_val<std::_Simple_types<unsigned int> > D O|  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 踫  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � A|  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 =u  std::_Ptr_base<donut::engine::DescriptorHandle> � 2|  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~(|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e bt  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 蕑  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > � <  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �:  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > "坸  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 噞  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > � 齋  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > d祔  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > �<  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> c �;  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> U .y  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w |  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � |  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y 鋥  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 貃  std::allocator<donut::math::vector<float,2> > M 蓒  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = 秡  std::allocator<donut::math::vector<unsigned short,4> > K 縶  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U  u  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 趚  std::_Ptr_base<donut::engine::BufferGroup> � �;  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > F祘  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ >s  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 杮  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 巤  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e 蘳  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N 墈  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > { {{  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l   std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � _:  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > , {  std::allocator<nvrhi::BindingSetItem> K  {  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 鳶  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > � 鰖  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> . 籕  std::_Ptr_base<donut::vfs::IFileSystem> �霺  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � �;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鑪  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 閟  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 魕  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � w  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � ╬  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 釹  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > ��;  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> � 錿  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > g 搑  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 蘻  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  u  std::allocator<float> � 緕  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> t U�  std::_Simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 爖  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t � �  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 4 搝  std::allocator_traits<std::allocator<float> > N 厇  std::allocator_traits<std::allocator<donut::render::DrawItem const *> > [ wz  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > Q�;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � 芐  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > l 7k  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > 稴  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> �;  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > w vq  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > \R  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > � �  std::_Uhash_choose_transparency<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *>,void> [ cz  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 {u  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Yz  std::hash<std::shared_ptr<donut::engine::MeshInfo> > O 0s  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem> > WUz  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> E Nz  std::_Vector_val<std::_Simple_types<donut::render::DrawItem> > � Dz  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H 蕆  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ mh  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> �  z  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 鎟  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X 	z  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage � a�  std::_Compressed_pair<std::hash<donut::engine::Material const *>,std::_Compressed_pair<std::equal_to<donut::engine::Material const *>,float,1>,1> ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> > . Ni  std::integer_sequence<unsigned __int64>  �  std::_Lockit  
-  std::timed_mutex � 訽  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > �   std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy � [;  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * C2  std::hash<enum nvrhi::ResourceType> / 豜  std::shared_ptr<donut::engine::Material> - 払  std::reverse_iterator<wchar_t const *> 5 鋂  std::shared_ptr<donut::engine::SceneGraphNode> 9 肵  std::shared_ptr<donut::engine::animation::Sampler> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file � 罋  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > � 琒  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � z  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 聎  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > � 淪  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >  �(  std::_Big_uint128  �,  std::condition_variable � I;  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > / 沑  std::weak_ptr<donut::engine::SceneGraph> 騳  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) U6  std::_Narrow_char_traits<char,int> L 0y  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 觟  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> y W�  std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � s�  std::_Tidy_guard<std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> > > c 芢  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > > 嘢  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > " 5;  std::_Align_type<double,64>  f+  std::hash<int> � 訓  std::pair<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *,bool>  �5  std::_Num_int_base � "y  std::_Compressed_pair<std::hash<donut::engine::BufferGroup const *>,std::_Compressed_pair<std::equal_to<donut::engine::BufferGroup const *>,float,1>,1>  錊  std::ctype<wchar_t> " �+  std::_System_error_category w 杬  std::_Simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � y  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / a2  std::_Conditionally_enabled_hash<bool,1> 2 髕  std::shared_ptr<donut::engine::BufferGroup> � |�  std::_Hash_find_last_result<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> *> � 莤  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > D 2�  std::initializer_list<donut::engine::MaterialResourceBinding> � )�  std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >  �5  std::float_denorm_style � 牃  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 4  x  std::shared_ptr<donut::engine::LoadedTexture>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 8g  std::piecewise_construct_t u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 襴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> ^ o�  std::_Uninitialized_backout_al<std::allocator<donut::engine::MaterialResourceBinding> > . 'Z  std::_Ptr_base<donut::engine::MeshInfo> 6 2;  std::allocator_traits<std::allocator<wchar_t> > � 蕎  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  &  std::bad_cast B [  std::enable_shared_from_this<donut::engine::SceneGraphNode>  玁  std::equal_to<void> 4 s  std::allocator<donut::math::vector<float,4> > � 緰  std::_Compressed_pair<std::allocator<donut::engine::MaterialResourceBinding>,std::_Vector_val<std::_Simple_types<donut::engine::MaterialResourceBinding> >,1> � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } 耟  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 恅  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 硍  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 亀  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o hh  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � je  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > " �5  std::numeric_limits<double>  C&  std::__non_rtti_object < 裋  std::_Ptr_base<donut::engine::DescriptorTableManager> ( 0  std::_Basic_container_proxy_ptr12 4 <w  std::allocator<donut::math::vector<float,3> > � g  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � *\  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � 鵞  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > 0;  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8> T -w  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �5  std::_Num_float_base � #w  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  *  std::logic_error 3   std::weak_ptr<donut::engine::SceneGraphNode> � Lg  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � �:  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > � 飃  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety P w  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 辷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id � 逈  std::list<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ?   std::allocator_traits<std::allocator<unsigned __int64> > : 揨  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 1e  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z   std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > > M 艞  std::_Conditionally_enabled_hash<donut::engine::BufferGroup const *,1>   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> _ 檝  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u hv  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl .   std::_Ptr_base<donut::engine::Material> * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > T 憲  std::_Vector_val<std::_Simple_types<donut::engine::MaterialResourceBinding> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error d *v  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 鴘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy . 硊  std::initializer_list<unsigned __int64> � ╱  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > &U�  std::unordered_map<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *>,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >   �:  std::char_traits<wchar_t> B "�  std::_Tuple_val<donut::engine::BufferGroup const * const &> _ g�  std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >  ~,  std::recursive_mutex J �  std::_Conditionally_enabled_hash<donut::engine::Material const *,1>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> � 
S  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � �:  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � �:  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> 7 攗  std::shared_ptr<donut::engine::SceneTypeFactory> � fu  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � ^u  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 纃  std::allocator<unsigned __int64>  h:  std::false_type  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ai  std::_Default_allocator_traits<std::allocator<unsigned __int64> > ! K,  std::hash<std::thread::id>  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � bk  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  �,  std::adopt_lock_t � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 Vu  std::shared_ptr<donut::engine::DescriptorHandle> , �5  std::numeric_limits<unsigned __int64> � *u  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L "u  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H 4m  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> � 鯮  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16>  ;  std::string_view  w  std::wstring_view % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound � 奼  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >  `,  std::_Mutex_base r �  std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> > � 輹  std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> >::_Reallocation_policy b u  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec �   std::_Compressed_pair<std::allocator<donut::render::DrawItem>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem> >,1> D 鰐  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  v:  std::defer_lock_t � 鐃  std::allocator_traits<std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >   �*  std::_Init_once_completer j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �,  std::scoped_lock<> + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 � 蜶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > j wX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � EX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> � r�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > � 豻  std::list<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � 葇  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> > � 鏂  std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > � 癵  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ m  std::_Atomic_integral<long,4> � lt  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ dt  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > 2 iJ  std::initializer_list<nvrhi::IBindingSet *> > 俕  std::enable_shared_from_this<donut::engine::SceneGraph> " -  std::lock_guard<std::mutex> K Vt  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > dn  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >  �  std::hash<long double> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � 阯  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > / =Z  std::shared_ptr<donut::engine::MeshInfo>  s:  std::try_to_lock_t H 鬒  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0 �   std::_Uhash_choose_transparency<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *>,void>  �  std::hash<double> 5 S\  std::shared_ptr<donut::engine::SceneGraphLeaf> O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> , 甦  std::allocator<std::_Container_proxy> D r:  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> " �,  std::_Align_type<double,72> � 禋  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0> / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception & �.  std::_Zero_then_variadic_args_t z 輺  std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> ; Lt  std::shared_ptr<donut::engine::MaterialBindingCache> d ps  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ t  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � t  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string 鱯  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � a:  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >  �,  std::cv_status S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 鮯  std::_Vector_val<std::_Simple_types<float> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A 雜  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + G  std::pair<enum __std_win_error,bool> � 9  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void> � 剕  std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � 輘  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > >  !,  std::thread  ?,  std::thread::id S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error \ 蝧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder � 驓  std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � O�  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0> k礂  std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> ) ~  std::_Atomic_integral_facade<long> b }�  std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >  �,  std::mutex 8 琗  std::_Ptr_base<donut::engine::animation::Sampler> % }2  std::hash<enum nvrhi::BlendOp> c   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B 梥  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1> 3 鐆  std::_Ptr_base<donut::engine::LoadedTexture>  #  std::exception_ptr  L:  std::ratio<1,1000000> � 塻  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > [ rs  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > M ds  std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> > � Zs  std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > ) q2  std::hash<enum nvrhi::BlendFactor> 輌  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 骾  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code J W  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano  �  std::_Iterator_base0 | Hs  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � g  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U @s  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0錰  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > jH:  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � 蠷  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 �6  std::_Char_traits<char16_t,unsigned short> 6 3q  std::allocator<donut::render::DrawItem const *> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> F 2s  std::allocator_traits<std::allocator<donut::render::DrawItem> > 9 紿  std::shared_ptr<donut::engine::CommonRenderPasses> � p�  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > ! �9  std::char_traits<char16_t> xul  std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > �  std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Range_eraser �/�  std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Clear_guard 裧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ $s  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - 糫  std::weak_ptr<donut::engine::Material>  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event � R�  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 Ri  std::integer_sequence<unsigned __int64,0> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1> �|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char> N Qm  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X s  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  9  std::_Invoker_strategy  	G  std::nothrow_t w   std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> � s  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> � 铇  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *> 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T �r  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �5  std::_Default_allocate_traits � $e  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � 苧  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > 0 uo  std::_Ptr_base<donut::engine::IShadowMap> . 膔  std::allocator<donut::render::DrawItem> ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> # �,  std::unique_lock<std::mutex> ( 淲  std::array<nvrhi::BufferRange,11> ; ;  std::basic_string_view<char,std::char_traits<char> > c Gq  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > �  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > z P�  std::initializer_list<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � 祌  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 5 E�  std::equal_to<donut::engine::Material const *> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 琙  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9羒  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > s 璻  std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � 漴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 時  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock Y 噐  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > > p @�  std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  }r  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � Kr  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 5g  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D r  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base  r*  std::out_of_range � 姎  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > # �5  std::numeric_limits<__int64> _ 鱭  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 苢  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b 坬  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > ~q  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  珸  std::ctype<char> @ bq  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 噈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � a�  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > P Xq  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? Nq  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  ;  std::memory_order Z Iq  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � ;q  std::_Compressed_pair<std::allocator<donut::render::DrawItem const *>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> >,1> � $q  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > �敋  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> : 3t  std::_Ptr_base<donut::engine::MaterialBindingCache> ! /-  std::recursive_timed_mutex q 9a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  縒  std::nullopt_t  罻  std::nullopt_t::_Tag � 灅  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >  ]9  std::ratio<3600,1> @ S�  std::_Ref_count_obj2<donut::engine::MaterialBindingCache> = �  std::allocator<donut::engine::MaterialResourceBinding> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState> / 訯  std::shared_ptr<donut::vfs::IFileSystem>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K 竝  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 莋  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  [9  std::ratio<1,1> � 顊  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >   �8  std::forward_iterator_tag  �*  std::runtime_error � 猵  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >   	  std::bad_array_new_length ; �2  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> T 沺  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> > j ip  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Reallocation_policy E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 'p  std::allocator<donut::engine::animation::Keyframe> K p  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool> � [e  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  5  std::u16string _ p  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 謔  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 韋  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *>  \  std::nested_exception  �  std::_Distance_unknown ) 榦  std::allocator<nvrhi::BufferRange> ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> %錃  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> , %@  std::codecvt<char32_t,char,_Mbstatet> 1 噊  std::shared_ptr<donut::engine::IShadowMap> C 蝑  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ^ w�  std::_Default_allocator_traits<std::allocator<donut::engine::MaterialResourceBinding> > ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F e  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 bo  std::vector<float,std::allocator<float> > F 0o  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 j\  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> 8 珫  std::initializer_list<nvrhi::VertexBufferBinding> & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc \0�  std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> } 賒  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J ym  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  f_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 4_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy � 鬾  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , EO  std::default_delete<std::_Facet_base> � 靚  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >  �*  std::range_error i�  std::_Hash<std::_Umap_traits<donut::engine::Material const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::Material const *,std::hash<donut::engine::Material const *>,std::equal_to<donut::engine::Material const *> >,std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::_UInt_is_zero  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> C 踤  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . �-  std::vector<bool,std::allocator<bool> > J 蘮  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 沶  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> i ]n  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 9  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  �8  std::ratio<1,10000000> Sn  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d i  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag j 塵  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A {m  std::allocator_traits<std::allocator<unsigned __int64 *> >  �/  std::allocator<char> T jm  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> d 0m  std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> > z   std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Reallocation_policy G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> ) f  std::allocator<unsigned __int64 *>    std::nullptr_t =MY  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > Lh  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K)h  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & �8  std::random_access_iterator_tag ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> � �  std::allocator_traits<std::allocator<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> # �8  std::allocator<unsigned int>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> 2簂  std::unordered_map<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *>,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  x  std::wstring 1 �  std::hash<donut::engine::Material const *> ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  -*  std::domain_error � 鷊  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  �  std::u32string_view � Me  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 俓  std::shared_ptr<donut::engine::SceneGraph>  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 閗  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 穔  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z ]k  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; 鏥  std::weak_ptr<donut::engine::DescriptorTableManager> $ 72  std::hash<nvrhi::IResource *> 4 =\  std::_Ptr_base<donut::engine::SceneGraphLeaf> 8 %z  std::equal_to<donut::engine::BufferGroup const *> " 轜  std::_Nontrivial_dummy_type � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet>  �8  std::char_traits<char> +詚  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage � K`  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � `  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! �+  std::_System_error_message  �  std::_Unused_parameter " �2  std::hash<nvrhi::IShader *> = sk  std::allocator<std::shared_ptr<donut::engine::Light> > h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  諻  std::bad_optional_access A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> 8   std::_Ptr_base<donut::engine::CommonRenderPasses> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > 4 y  std::hash<donut::engine::BufferGroup const *> | 榸  std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � dk  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  鴋  std::_Exact_args_t � 铔  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > � 苵  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > Q _k  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � Qk  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base U 龥  std::allocator_traits<std::allocator<donut::engine::MaterialResourceBinding> > b Ck  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> c 9k  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> = 粯  std::tuple<donut::engine::BufferGroup const * const &> � 鶝  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> � 貧  std::allocator<std::_List_node<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > � f^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 4^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> y m�  std::pointer_traits<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > *> �圷  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 |Z  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ; �8  std::allocator_traits<std::allocator<unsigned int> > ' 訥  std::hash<std::filesystem::path> � 麞  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > � 摌  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 蘗  std::_Ptr_base<donut::engine::SceneGraphNode> � 蒳  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > 6 h�  std::_Wrap<donut::engine::MaterialBindingCache> F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � 襤  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> � 麤  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::Material const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> �|g  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E @]  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O #]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U !]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction W .z  std::_Compressed_pair<std::equal_to<donut::engine::BufferGroup const *>,float,1> 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t & �i  $_TypeDescriptor$_extraBytes_40 # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling $ J  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress  �$  nvrhi::GraphicsAPI 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �!  nvrhi::GraphicsState * �8  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16>  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice> !    nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray . �-  nvrhi::RefCountPtr<nvrhi::ICommandList>  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture ! �-  nvrhi::utils::ScopedMarker $ C-  nvrhi::utils::BitSetAllocator . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  �8  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # RW  nvrhi::DescriptorTableHandle  :  nvrhi::ShaderType  �$  nvrhi::TimerQueryHandle 2 RW  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # �$  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice !    nvrhi::VariableShadingRate  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �$  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  �  nvrhi::StencilOp  ]!  nvrhi::IBindingLayout  �  nvrhi::ColorMask  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  .   nvrhi::PrimitiveType  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 J  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # +k  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline  �-  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �  nvrhi::BlendOp  �"  nvrhi::ComputeState 2 +k  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �!  nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t 
 �,  _Cnd_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats * fZ  donut::engine::SkinnedMeshReference ! 籞  donut::engine::SceneCamera $ H  donut::engine::ICompositeView  ?H  donut::engine::IView ( �7  donut::engine::CommonRenderPasses 5 �&  donut::engine::CommonRenderPasses::PsoCacheKey ; �&  donut::engine::CommonRenderPasses::PsoCacheKey::Hash $ �7  donut::engine::BlitParameters $ X[  donut::engine::SceneGraphNode 0 ![  donut::engine::SceneGraphNode::DirtyFlags " Z  donut::engine::MeshInstance   fH  donut::engine::PlanarView ) ZZ  donut::engine::SkinnedMeshInstance   籢  donut::engine::SceneGraph - %�  donut::engine::MaterialResourceBinding > 颺  donut::engine::ResourceTracker<donut::engine::MeshInfo>  哘  donut::engine::ViewType $ 
H  donut::engine::ViewType::Enum ( h]  donut::engine::AnimationAttribute $ 筜  donut::engine::SceneGraphLeaf ! uW  donut::engine::BufferGroup  榙  donut::engine::Material *  k  donut::engine::Material::HairParams 0 黬  donut::engine::Material::SubsurfaceParams ! H  donut::engine::ShaderMacro # 荌  donut::engine::ShaderFactory  語  donut::engine::Light ' ℡  donut::engine::SceneContentFlags  礧  donut::engine::MeshInfo & 鎆  donut::engine::DirectionalLight & \]  donut::engine::SceneGraphWalker ( X  donut::engine::animation::Sampler ) 鴍  donut::engine::animation::Keyframe ) 橷  donut::engine::animation::Sequence    donut::engine::MeshType * 厱  donut::engine::MaterialBindingCache  鯶  donut::engine::SpotLight & #�  donut::engine::MaterialResource # 殩  donut::engine::LoadedTexture & 竀  donut::engine::DescriptorHandle , &W  donut::engine::DescriptorTableManager B 鱒  donut::engine::DescriptorTableManager::BindingSetItemsEqual B 餠  donut::engine::DescriptorTableManager::BindingSetItemHasher % _W  donut::engine::VertexAttribute 0 圿  donut::engine::SceneGraphAnimationChannel " 鮦  donut::engine::MeshGeometry % t   donut::engine::DescriptorIndex > 誢  donut::engine::ResourceTracker<donut::engine::Material>   [  donut::engine::PointLight ) 鮙  donut::engine::SceneGraphAnimation " nH  donut::engine::StaticShader $ 乨  donut::engine::MaterialDomain  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  /  donut::math::float2  }[  donut::math::dquat # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane  瞇  donut::math::daffine3  燵  donut::math::double3 # �  donut::math::vector<float,4> $ 燵  donut::math::vector<double,3>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes $ }j  donut::math::vector<double,4>  �  donut::math::float4 & 	e  donut::math::matrix<double,3,3> % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3>   �.  donut::math::box<float,2>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> $ 瞇  donut::math::affine<double,3> & }[  donut::math::quaternion<double> # #j  donut::render::IGeometryPass - 宎  donut::render::PassthroughDrawStrategy  Fj  donut::render::DepthPass 1 Yj  donut::render::DepthPass::CreateParameters ( Xa  donut::render::DepthPass::Context , _a  donut::render::DepthPass::PipelineKey A Ij  donut::render::DepthPass::PipelineKey::<unnamed-type-bits> ) Fa  donut::render::GeometryPassContext 1   donut::render::InstancedOpaqueDrawStrategy # va  donut::render::IDrawStrategy  
j  donut::render::DrawItem M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec  u   _Thrd_id_t - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24 & 鞗  $_TypeDescriptor$_extraBytes_65  h,  _Mtx_internal_imp_t & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE  問  DepthPassConstants 
 \,  _Mtx_t 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result  %,  _Thrd_t - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers    �   �      [届T藎秏1潴�藠?鄧j穊亘^a  @    噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �    跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �    隄�:LwV�=G'V裲稩绛-}}辌t[吁Z     U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  E   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  ~   dhl12� 蒑�3L� q酺試\垉R^{i�  �   Z�=g!9﹞�+庐隀鶋鶾��0L-�誩Q  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  K   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n  �   �暊M茀嚆{�嬦0亊2�;i[C�/a\     M]S噴=泥G)w��!&鍌S硚YQD铢g�/  O   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎     )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  K   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �    狾闘�	C縟�&9N�┲蘻c蟝2     傊P棼r铞
w爉筫y;H+(皈LL��7縮  d   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   o忍x:筞e飴刌ed'�g%X鶩赴5�n�      d蜯�:＠T邱�"猊`�?d�B�#G騋  =   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     `k�"�1�^�`�d�.	*貎e挖芺
脑�  W   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �     憒峦锴摦懣苍劇o刦澬z�/s▄![�  V   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  �   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   �'稌� 变邯D)\欅)	@'1:A:熾/�  	   d乔淝d>{[逑Gwo+牙3蹀
 籬};旟�  H	   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �	   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �	   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  
   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  A
   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  ~
   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �
   �0�*е彗9釗獳+U叅[4椪 P"��  �
   +Gtd歾$皅\�6妮`胧絶+狎6s示v�  6   �=蔑藏鄌�
艼�(YWg懀猊	*)  w   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  2   j轲P[塵5m榤g摏癭 鋍1O骺�*�  {   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  *
   繃S,;fi@`騂廩k叉c.2狇x佚�  s
   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �
   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��     +YE擋%1r+套捑@鸋MT61' p廝 飨�  E   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�  
    I嘛襨签.濟;剕��7啧�)煇9触�.  M   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   妇舠幸佦郒]泙茸餈u)	�位剎  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺     靋!揕�H|}��婡欏B箜围紑^@�銵  P   猯�諽!~�:gn菾�]騈购����'  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   �颠喲津,嗆y�%\峤'找_廔�Z+�     瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  a   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   �1沩炙酙罐擆鲱�9i踥鷣Cv��<~�  �   t�j噾捴忊��
敟秊�
渷lH�#  +   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  j   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   鹴y�	宯N卮洗袾uG6E灊搠d�  :   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   �(M↙溋�
q�2,緀!蝺屦碄F觡     G�膢刉^O郀�/耦��萁n!鮋W VS  U   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  &   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  d   5�\營	6}朖晧�-w氌rJ籠騳榈  �   k&�2箍�#た↗�U嬗醇芧'l�-G恇|:  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  '   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  a   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   天e�1濎夑Y%� 褡\�Tā�%&閜�  �   o藾錚\F鄦泭|嚎醖b&惰�_槮     �
bH<j峪w�/&d[荨?躹耯=�  ^   揾配饬`vM|�%
犕�哝煹懿鏈椸  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  (   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  n   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  <   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  /   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  n   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  *   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  v   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5      �"鈖@M�骑潆譢aMy1绾鎕瑞lg  R   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁     o]Eh堖�--�暝 }DvJ爇羯�郫�  Z   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   郖�Χ葦'S詍7,U若眤�M进`  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  >   A縏 �;面褡8歸�-構�壋馵�2�-R癕  }   交�,�;+愱`�3p炛秓ee td�	^,  �   zY{���睃R焤�0聃
扨-瘜}  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  <   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  |   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  B    副謐�斦=犻媨铩0
龉�3曃譹5D   �    寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �    J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  !   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  M!   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �!   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �!   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹   "   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  d"   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �"   � 罟)M�:J榊?纸i�6R�CS�7膧俇  #   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  U#   険L韱#�簀O闚样�4莿Y丳堟3捜狰  �#   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �#   =J�(o�'k螓4o奇缃�
黓睆=呄k_  $   _O縋[HU-銌�鼪根�鲋薺篮�j��  b$   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �$   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  	%   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  R%   蜅�萷l�/费�	廵崹
T,W�&連芿  �%   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �%   D���0�郋鬔G5啚髡J竆)俻w��  +&   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  j&   L�9[皫zS�6;厝�楿绷]!��t  �&   匐衏�$=�"�3�a旬SY�
乢�骣�  �&   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  3'   v峞M� {�:稚�闙蛂龣 �]<��  y'   悯R痱v 瓩愿碀"禰J5�>xF痧  �'   チ畴�
�&u?�#寷K�資 +限^塌>�j  �'   矨�陘�2{WV�y紥*f�u龘��  A(   �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  �(   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �(   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �(   穫農�.伆l'h��37x,��
fO��  8)   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  r)   鏀q�N�&}
;霂�#�0ncP抝  �)   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �)   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  D*   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �*   f扥�,攇(�
}2�祛浧&Y�6橵�  �*   曀"�H枩U传嫘�"繹q�>窃�8  �   �      c  X  	  f     B   g     H   h     Y   m     �   �    U   �    �   �  �  �  �  �  �  �  �  x  �  �     M     +   N     0   O     7   Q     J   R     Q   a     �  b     �  u  �  �   |  �  �   �    q   �    q   �    x   �  �  �   �    q   �    q   �  �  B  �  �  �   �  �  �   �  �    �  �    �  �  �   �  �    �  �  �   �  �    �  �  �     �  �    0  K   (  �  �   )  �  �   *  �  �   B  �  D
  D  �  �  `    �  �  �  �   �  �  �   �    �   �  0  �   �    @   �    �     �  �   
  �  �     �  �     �  �   �    �   8  �  �   9  �  �   g  �  �   a  �  w  b  �  x  d  �  L  e  �  [  y  �    z  �  �   {  �  �   �  �    �  �  �   �  �    �  �  �   �  �  �  �  �  d  �    @   �    @   �    5   �  �  �   �  �    �  �  �   �  �  �   �  �  �   �    5   �    5   �  �  5  &  �    '  �  �   (  �  �   *  �    +  �  �   ,  �  �   /  �    0  �  �   1  �  �   2  �  t  :    q   u  �  �   
  �  :  =    @   -     h   \  �  �   >  �  �  ~   �  2   �   `    6&  �  �   M&  �  �   '  �  �   C'  �  �   �)  �  r  �)  �  t  �)  �  }  �)  �  �  �)  �  �  �)  �  �  �)  �  w  �)  �  �  �)  �  �  �)  �  �  �)  �  �  �)  �     �)  �  5  �)  X  �  �)  X  �  �)  `	  �  �)  `	  j   �)  �	  >  �)  �  �  �)  �  �  �)  �  �  �)  �  �   �)  �  �  �)    @   �)    x   �)  �  �   �)  �  �   �)  �    �)  �  �   �)  �  �   �)   
  �   �)  �  ,   �)  X  �  �)  �	  4  �)  �	  '  �)  �	  u  �)   
  �  �)   
  �  �)   
  H  �)  �  �  �)  �  d  �)  �  Z  �)  �  t  �)  �  �   �)  �  �   *  �	  �  *  �	  �  *  �	  �  	*   
  �   
*   
  %   *    �  *  �	    *    �  *  �	  S  *   
  X  *   
  "  *  `	  1   *  �  z  *  �	  
  *    �  *   
  '   *    �  #*  �  �
  $*  �	  �  &*    �  '*  X    )*  �	  �   7*  �	    8*   
  C  9*   
  3  ;*    �  <*  �  :  =*  �  D  B*   
  <  C*    �  D*    �  E*  0  �   I*    F  P*  X  �  Q*  X  �  R*  �	  w  S*  �	  q  T*  �	  j  U*  �	  K  V*   
  a  W*   
  `  X*    �  Z*  X  ?	  [*  �  n  `*    �  b*  X  �  e*  �	  �  f*  �	  �  h*  X  Q	  n*   
  j   p*   
  L   q*   
  G   r*   
  <   s*   
  1   t*   
  )   z*  �	  �  {*  �	    |*   
  �  *    �  �*   
  P  �*    �  �*  �  &  �*  �  9  �*  �	  �  �*      �*  �	    �*  0  �   �*   
  G  �*  0  �   �*    �  �*    �  �*    R  �*  0  �  �*    �  �*  �	  �   �*  X  *	  �*    �  �*    |  �*    �  �*  �	  >  �*   
    �*  X  �  �*  �  �  �*  �	  4  �*   
  �  �*  �	    �*    �  �*  �  �   �*  �  �  �*  �  a  �*    �  �*  X  $	  �*   
  C  �*   
  3  �*  �  l  �*    �  �*  �  ;  �*  0  9  �*   
  <  �*  �  �   �*  0  5  �*    �  �   +   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\RTXPT\External\Donut\include\donut\render\GeometryPasses.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\RTXPT\External\Donut\src\render\DepthPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\RTXPT\External\Donut\include\donut\render\DepthPass.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\RTXPT\External\Donut\include\donut\shaders\depth_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\RTXPT\External\Donut\include\donut\render\DrawStrategy.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\include\donut\engine\MaterialBindingCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale   �       L+  醳  !   鍄  !  
 �  "   �  "  
 褱      諟     
 )�      -�     
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 s  c   w  c  
 �  �   �  �  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�          �   �  � G            0   
   %   �*        �std::_Copy_memmove<donut::engine::MaterialResourceBinding const *,donut::engine::MaterialResourceBinding *>  >   _First  AJ          >   _Last  AK          >   _Dest  AM         AP          >#    _Count  AI  
                             H  h   �*  �*   0     O_First  8     O_Last  @     O_Dest  O�   @           0   �     4       � �   � �   � �!   � �%   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 )  �   -  �  
 �  �   �  �  
 禕I钩     D�L3�禞ML3�禕ML3�禕ML3�禕ML3�禕MI3繧H3�禞IH3罥�   �   F  b G            c       b   �*        �std::_Fnv1a_append_value<donut::engine::BufferGroup const *> 
 >#   _Val  AJ          >'l   _Keyval  AK        c  M        c  @^
 >#    _Val  AH  L       AP       7  N                        H� 
 h   c      #  O_Val     'l  O_Keyval  O  �   0           c   X     $       $	 �    &	 �b   '	 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 \  �   `  �  
 D�I钩     H�%#"勪滘薍嬔L3�禔ML3�禔ML3�禔ML3�禔ML3�禔禝MI3繧H3�禞IH3罥�   �   N  c G            p       o   �*        �std::_Hash_representation<donut::engine::BufferGroup const *>  >'l   _Keyval  AJ          AK       U  M        �*   T M        c  @M
 >#    _Val  AH  Y       AP       ;  N N                        H�  h   c  �*      'l  O_Keyval  O  �   @           p   X     4       *	 �    +	 �   *	 �   +	 �o   ,	 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 d  �   h  �  
 H塡$UVWATAUAVAWH冹0I嬸L嬺H嬮A�H�%#"勪滘薍3菻撼     HA禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻A禓H3菻H墝$�   L岴0I� H#罤繦婱H媆�H峌L�"I;躸
I嬡L塂$pM孅隟H�罤�H;CtH;賢*H媅H;Cu馡�A艶 I嬈H媆$xH兡0A_A^A]A\_^]肔嬨H岴0H塂$pL孄L峬H�������H9E勽  H塗$ H荄$(    �    �    H孁H塂$(H�H塇H茾    H婱�] H兞W纗驢*岭H嬃H谚冡H馏H*荔X繦媢8W襀咑x驢*蛛H嬑H验H嬈冟H润H*洋X�(润^�/�椑劺勼   �^描    3审
    /羠�\�/羢
H�       �H嬋驢,繦凉   H;罤G菻;駍H侢   s
H�4�    H;駍H嬹H嬛H嬐�    H婱0L媱$�   I#菻蒆婾H婦�H媇H;胾H荄$(    �$H�蔋婳H;Ht怘嬝H;聇"H婡H;Hu頗�H塡$ L孄L峬H岴0L嬨�-H塂$ H荄$(    L孄L峬H岴0L媎$ �
H婦$pL媱$�   H婼H�EL�'H塛H�:H墈I婱 H� I#繦繪�罫;EuH�<岭M;莡H�<岭H9T�uH墊�I�>A艶橥��H�
    �    �>  �   �     �  �   .  �   !  L   &  �      �   �  �G            +     +  (*        �std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<donut::engine::BufferGroup const * const &> 
 >l   this  AJ          AN       �   >'l   _Keyval_arg  AL       �  t� AP          AL �       >|�   _Target  CH      
      CI      �       CT          � ��  CU          {	 � �W  CW      �     Q  K  CT     �     3 � CU     �     3 � >灅   _Newnode  CM     E      B    �     3 , t � � 1 M        �*  ��*,'%md	& >駅    _Where  AI  �     d  
 =   AI �     @  fi 
 >Dl    _End  AT  �     Q 6   AT �     @  � �  >Dl    _Bucket_lo  AJ  �     G     AJ �     :  R � >#    _Bucket  AH  �       M        �*  �� M        �*  �� N N N M        )*  { M        Z*  { M        h*  { M        �*  { M        �*  {5 M        c  >(4(4(4(4(4(4
 >#    _Val  B�   �     � AJ  .     {  AP  >    � | 
  N N N N N N M        �*  �*% M        �*  丣 M        �*  丣 M        �*  丣 M        \  丵 N N N N M        �*  �/	 M         *  
�8 M        �  
�8 M        �  
�8
 Z   �   N N N N M        �*  �* N N M        T*  ��
 Z   j   N M        �*  �� N M        S*  乊D5Y >#    _Newsize  AJ  f      AJ �    Z  I �  >#    _Oldsize  AJ  ]    	  M        f*  乚 N N8 M        �*  �2/,$%k
 >駅    _Where  AH  M    f H  
 >Dl    _End  AI  Q      AI �     3 }� �-  >Dl    _Bucket_lo  AK  e    U     AK �    F  -  >#    _Bucket  AJ  6      M        �*  俥 M        �*  俥 N N N M        R*  k伹
 Z   d*    M        e*  伹B
 >#   _Req_buckets  AJ  	    $  C             M        z*  6伹 N N N M        �*  
� N2 M        U*  偳$$#$#d$&CJ$"E >乻    _Bucket_array  AJ  �    =  AJ �       >Dl    _Insert_after  AK  �    S  AK �       >#    _Bucket  AH  �      N 0           8         0@ hF   B  c  �  �    �  �  �  �  ,  =  �  \  }   "  R"  T"  y"  �)  *   *  )*  3*  6*  J*  K*  R*  S*  T*  U*  Y*  Z*  `*  e*  f*  g*  h*  y*  z*  *  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*         $LN206  p   l  Othis  �   'l  O_Keyval_arg      灅  O_Newnode  O  �   �           +  �	     �       � �   � ��   � ��   � ��   � �  � �  � �*  � �Y  � ��  � �2  � ��  � ��  � ��  � ��  � �  � ��     �F                                �`std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<donut::engine::BufferGroup const * const &>'::`1'::dtor$1                         �  O�     �F                                �`std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Try_emplace<donut::engine::BufferGroup const * const &>'::`1'::dtor$0                         �  O,   �   0   �  
 �  �   �  �  
   �   
  �  
 0  �   4  �  
 H  �   L  �  
 X  �   \  �  
 ~  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 =  �   A  �  
 M  �   Q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 5  �   9  �  
 I  �   M  �  
 s  �   w  �  
 j  �   n  �  
 z  �   ~  �  
 �  �   �  �  
 =  �   A  �  
 M  �   Q  �  
 x  �   |  �  
 �  �   �  �  
   �     �  
 $  �   (  �  
 U  �   Y  �  
 i  �   m  �  
 �  �   �  �  
 2	  �   6	  �  
 F	  �   J	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 
  �   
  �  
 $
  �   (
  �  
 F
  �   J
  �  
 �  ^   �  ^  
 �  �   �  �  
 �     �    
 �  �   �  �  
 H崐    �       �   H崐    �       �   H冹L嬕3繦+袻嬌H兟H陵I;蔋G蠬呉t?H凓r9I� H兞鳫�袽;葁I;萻$H�<$H冣蘒孂H��    H嬍H灵驢獿蔋�<$M;蕋f�     I� I�I兞M;蕌馠兡�   �   �  �G            �         **        �std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >乻   _First  AJ        0  AJ b     "  >乻   _Last  AK          AR       } 
 >ws   _Val  AP        �  >ss    _UFirst  AQ       u                        @  h   +*  �*      乻  O_First     乻  O_Last      ws  O_Val  O �   X           �   �     L       � �   � �   � �)   � �9   � �b   � �p   � �   � �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 )  �   -  �  
 �  �   �  �  
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  �G                       C*        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >乻   _First  AJ          AJ       
   >乻   _Last  AK          
 >ws   _Val  AP           >铇   _Backout  CJ            CJ          
   M        I*    N M        �*   N                        H & h   +*  G*  H*  I*  �*  �*  �*  �*      乻  O_First     乻  O_Last     ws  O_Val  O  �   H                    <       � �    � �   � �   � �   � �   � �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 	  �   
  �  
 0  �   4  �  
 D  �   H  �  
   �     �  
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AJ                                 H�     .!  Othis  O   �   0              �     $       �  �    �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H嬃�   �   �   y G                   
   ^        �nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>::RefCountPtr<nvrhi::IGraphicsPipeline> 
 >鸌   this  AJ                                 @�     鸌  Othis  O �   0              �     $       �  �    �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >C   this  AJ                                 H     C  Othis  O   �                  �             �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 H塡$ L塂$H塋$UVWH冹0I嬸H孂H�    H�H塓H呉t
H�H嬍�P�3鞨塷H塷H塷 H塷(H塷0H塷8H塷@H峅HH�    H塂$ L�
    峌D岴 �    怘崗H  �)H塱H塱W�AA(A8H塱H峌�    怘壇�  壇�  f壇�  茋�  H崯�  H塡$X�+H塳H塳峂 �    H� H堾H塁H岾H�)H塱H塱H荂0   H荂8   �  �?L婥峌�    怘壇�  H壇�  H�H墖�  H婩H墖�  H�.H塶H壇�  H壇   H婳H���   劺斃垏�  H媈H呟t,����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH嬊H媆$hH兡0_^]�   (   ^   �   j   �   v   �   �   �   �   �     �      �   �  I G            �     �  �)        �donut::render::DepthPass::DepthPass 
 >+j   this  AJ          AM       � DP    >�$   device  AK        8  AK 9     8  >綡   commonPasses  AL       c AP          AL �      D`    M        �  5乶 M        2  乶,	 M        �  亀
 >a&   this  AI  r    9  M        �  亹	
 N N N N M        �)  丠 M        �)  �丠 N N M        �  � M        
  �,*K N M        �  �� N N M        �)  N��
 >}l   this  AI  �       BX   �     �  M        �)  ��,H
 Z   *   M        7*  �� M        �*  �� M        �*  �� N N N M        *  �� M        *  ��(# >駅    _Newhead  AH  �     <  M         *  �� M        �  �� M        �  ��
 Z   �   N N N N M        D*  �� M        V*  �� N N N M        *  �� N N N M        -  
��

 >�,   this  AJ  �     %  M        M  
��

 Z   �   N N M        \  S N M        {  O N M        �  K N M        �  G N M        �  C N M        �  ? N M        �  9 N M        �  & M          *	 N N
 Z   i   0                    @ � h8   �  �  �  �  �  �  �  M  S  �  �    �  �  �  {  �  �  �  �  �  �  �  2  �  
  =  -  \  �)  �)  �)  �)  �)  �)  �)  
*  *  *  *  *  *  *  *   *  7*  ;*  D*  F*  V*  �*  �*  �*  �*  �*  �*   P   +j  Othis  X   �$  Odevice  `   綡  OcommonPasses  95       �   9]      �$   9�      b&   9�      b&   O�   �           �  x     $       ?  �&   =  �9   ?  ��     ,   �   f  ��   h  ��   i  ��   k  �x     4   �   ?  �  >  �H  ?  �V  @  �n  A  ��   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$0 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$1 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$2 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$3 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$4 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$5 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$6 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$7 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$8 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O  �   �   X F            .      (             �`donut::render::DepthPass::DepthPass'::`1'::dtor$9 
 >+j   this  EN  P         (  >綡   commonPasses  EN  `         (                        �  O  �   �   Y F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$10 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O �   �   Y F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$17 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O �   �   Y F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$18 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O �   �   Y F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$11 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O �   �   Y F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$12 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O �   �   Y F                                �`donut::render::DepthPass::DepthPass'::`1'::dtor$13 
 >+j   this  EN  P           >綡   commonPasses  EN  `                                  �  O ,   �   0   �  
 n   �   r   �  
 ~   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 s  �   w  �  
 d  �   h  �  
 t  �   x  �  
 ?  �   C  �  
 '  �   +  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 t     x    
 �     �    
 �     �    
 H	  	   L	  	  
 �	  	   �	  	  
 �	  	   �	  	  
 
  
    
  
  
 q
  
   u
  
  
 �
  
   �
  
  
 �
  
   �
  
  
 E  
   I  
  
 p  
   t  
  
 �     �    
          
 D     H    
 �     �    
 �     �    
 
     
    
 l
     p
    
 �
     �
    
 �
     �
    
 @     D    
 �     �    
 �     �    
          
 i     m    
 �     �    
 �  �   �  �  
 >  �   B  �  
 i  �   m  �  
 �     �    
          
 =     A    
 �     �    
 �     �    
          
 d      h     
 �      �     
 �      �     
 8     <    
 �     �    
 �     �    
          
 b     f    
 �     �    
 H媻`   �       �   H媻P   H兞�       �   H媻P   H兞�       �   H媻P   H兞�       �   H媻P   H兞 �       �   H媻P   H兞(�       �   H媻P   H兞0�       �   H媻P   H兞8�       �   H媻P   H兞@�       �   @UH冹 H嬯H婱PH兞HL�
    A�    �   �    H兡 ]�   �   $   �   H媻P   H伭H  �       �   H媻P   H伭�  �       �   H媻P   H伭�  �       �   H媻P   H伭�  �       �   H媻X   H兞�       �   H媻X   H兞�       �   � 3狼AW狼A W汕A茿 茿茿 茿茿  茿$茿( 茿,茿0 茿4茿8 茿<茿@ H堿\H堿d茿D �� 茿H 茿L艫P茿T    f茿X  艫Z AlI|墎�   H嬃�   �   �   E G            �       �           �nvrhi::RenderState::RenderState 
 >�   this  AJ        �                         @ " h   �                  �  Othis  O ,   �   0   �  
 j   �   n   �  
 � H嬃茿�   �   �   S G                              �nvrhi::BlendState::RenderTarget::RenderTarget 
 >�   this  AJ                                 H�     �  Othis  O   ,   �   0   �  
 x   �   |   �  
 H�    H嬃�   �   �   U G                   
   �        �nvrhi::VertexBufferBinding::VertexBufferBinding 
 >�!   this  AJ                                 H�     �!  Othis  O ,   �   0   �  
 z   �   ~   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,         �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�               �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !         ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �      �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2         $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AH         AJ          AH        M        �  GCE
 >D     temp  AJ  
       AJ        N (                     0H� 
 h   �   0   .!  Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 P  �   T  �  
 h  �   l  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >3J   this  AH         AJ          AH        M        ,  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   ,   0   3J  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 J  �   N  �  
 d  �   h  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         z        �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >?I   this  AH         AJ          AH        M        (  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   (   0   ?I  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         u        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >�&   this  AH         AJ          AH        M        �  GCE
 >�$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �&  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   Z  z G            "         �        �nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>::~RefCountPtr<nvrhi::IGraphicsPipeline> 
 >鸌   this  AH         AJ          AH        M        1  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   1   0   鸌  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 	  �   
  �  
 V  �   Z  �  
 p  �   t  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �        �nvrhi::RefCountPtr<nvrhi::IInputLayout>::~RefCountPtr<nvrhi::IInputLayout> 
 >^    this  AH         AJ          AH        M        *  GCE
 >7     temp  AJ  
       AJ        N (                     0H� 
 h   *   0   ^   Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 L  �   P  �  
 d  �   h  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�    this  AH         AJ          AH        M        )  GCE
 >;     temp  AJ  
       AJ        N (                     0H� 
 h   )   0   �   Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H婭H吷t
�    �    �   �      �     dG                      `*        �std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >嚇   this  AJ          M        *  
	 M        �  
	
 >   _Ptr  AJ         N N                        H�  h   �  �  *      嚇  Othis  O �   8                   ,       � �    � �	   � �   � �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 4  �   8  �  
 @SH冹 H嬞H婭H吷t?H婼(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁H塁 H塁(H岾H兡 [�    �    �?   �   [   �   `   �      �   I  G            e      e   �)        �std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::~_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 
 >l   this  AI  	     \ Q   AJ        	  M        �)  H	V" M        �)  )I1& M        *  *F M        �  )!
 Z     
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        �  
&#
$
 Z   q   >#    _Ptr_container  AP  *     :  !  AP >       >#    _Back_shift  AJ  
     W 1 !  AJ >         N N N M        *   N N N                       @� & h   �  �  �)  �)  
*  *  *  5*         $LN33  0   l  Othis  O   ,   �   0   �  
 +  �   /  �  
 ?  �   C  �  
 �  �   �  �  
 �  �     �  
 `  �   d  �  
 t  �   x  �  
 �  �   �  �  
 �  �   �  �  
 1  B   5  B  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �   V   �      �   �  �G            [      [   �)        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 
 >歶   this  AI  	     R K   AJ        	 " M        �)  )H1%
 M        *  *= M        �  )
 Z     
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z   q   >#    _Ptr_container  AP  )     1    AP =       >#    _Back_shift  AJ       N 1   AJ =       
  N N N M        *   N N                       H� " h   �  �  �)  
*  *  *  5*         $LN30  0   歶  Othis  O �   8           [   �	     ,       > �	   ? �O   D �U   ? �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 t  �   x  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 0  �   4  �  
 D  �   H  �  
 �  @   �  @  
 �  �   �  �  
 @SH冹 H嬞H婭H吷t2H婹H呉tH茿    H�H嬍�PH婯H吷t�    H兡 [�    H兡 [聾   �      �   =  hG            J      D   J*        �std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > 
 >倶   this  AI  	     @ 6   AJ        	  M        `*  0
 M        *  5
 M        �  5

 >   _Ptr  AJ  
       AJ 0       N N N M        �*   M        �   M        ,  DE

 >�!    temp  AK         AK 0      
 
  >"     ref  A  0       N N N                      0H� . h
   �  �  �  ,  3*  `*  *  �*  �*  �*   0   倶  Othis  9)       �   O   �   8           J    
     ,       L �	   M �   N �0   P �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �      �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 9  �   =  �  
 T  �   X  �  
 H�    H��   I      �   �   � G                   
   O*        �std::_Ref_count_obj2<donut::engine::MaterialBindingCache>::~_Ref_count_obj2<donut::engine::MaterialBindingCache> 
 >M�   this  AJ                                 H� 
 h   �      M�  Othis  O�   (              �            2 �
   8 �,   �   0   �  
 �   �   �   �  
   �     �  
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯H吷tH塳H��P惡    H嬎�    H嬤H�u院    H�H媆$0H媗$8H媡$@H兡 _�    P   �   y   �      �   �  �G            }      d   �)        �std::list<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~list<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 >rt   this  AJ          AL       \   M        �)   M        9*  
\ M        X*  \ M        �  \ N N N' M        8*  I*
 >駅   _Head  AI         >駅    _Pnode  AI  &     C  >駅    _Pnext  AM  3     )  AM 0     H  )  M        B*  3
 M        9*  

G M        X*  
G M        �  
G
 Z      N N N M        �*  3 M        �  3 M        ,  3DE
 >�!    temp  AJ  7       AJ G       N N N N N N                      0@� F h   �  �  �  �  ,  �)  *  3*  8*  9*  B*  X*  �*  �*  �*  �*   0   rt  Othis  9C       �   O  �   8           }    
     ,        �    �d    �x    �,   �   0   �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 �  �   �  �  
 �  �     �  
 �  �   �  �  
 �  �   �  �  
 H�	�       �      �   �   Z G                      a        �std::lock_guard<std::mutex>::~lock_guard<std::mutex> 
 >-   this  AJ          M        Q    N                        H�  h   Q  S      -  Othis  O   �   (                           � �    � �,   �   0   �  
    �   �   �  
 �   �   �   �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �        �std::shared_ptr<donut::engine::CommonRenderPasses>::~shared_ptr<donut::engine::CommonRenderPasses> 
 >慔   this  AJ        +  AJ @       M        2  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  2   0   慔  Othis  9+       b&   9=       b&   O  �   0           K   �     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �)        �std::shared_ptr<donut::engine::MaterialBindingCache>::~shared_ptr<donut::engine::MaterialBindingCache> 
 >7t   this  AJ        +  AJ @       M        �)  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �)   0   7t  Othis  9+       b&   9=       b&   O  �   0           K   �     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �       �      �   �  zG                       �)        �std::unordered_map<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *>,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~unordered_map<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *>,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 >}l   this  AJ                                 H�     }l  Othis  O,   �   0   �  
 �  �   �  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �   V   �      �     � G            [      [   �)        �std::vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> >::~vector<donut::engine::MaterialResourceBinding,std::allocator<donut::engine::MaterialResourceBinding> > 
 >灂   this  AI  	     R K   AJ        	 $ M        �)  	h1%	
 M        *  *= M        �  )
 Z     
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z   q   >#    _Ptr_container  AP  )     1    AP =       >#    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� " h   �  �  �  �)  *  *  4*         $LN28  0   灂  Othis  O  �   8           [   X     ,       � �	   � �O    �U   � �,   �   0   �  
   �   #  �  
 3  �   7  �  
 �  �   �  �  
 �  �   �  �  
 >  �   B  �  
 R  �   V  �  
 x  �   |  �  
 �  �   �  �  
 �  Z   �  Z  
   �      �  
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �   [   �      �   �  D G            `      `   #        �nvrhi::BufferDesc::~BufferDesc 
 >   this  AI  
     S L   AJ        
  M        �  GM) M          -(

 M        D   N M        B  -G M        `  &@ M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   q   >#    _Ptr_container  AP  '     8    AP ;       >#    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0     Othis  O ,   �   0   �  
 i   �   m   �  
 }   �   �   �  
 ]  �   a  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  -   �  -  
 H塡$H塼$WH冹 H嬞H嫻   ����H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH嫽�  H�t'嬈�罣凐uH�H嬒��羨凗u	H�H嬒�PH崑�  �    H崑H  �    H岾HL�
    �   D岯�    怘婯@3�H吷tH墈@H��P怘婯8H吷tH墈8H��P怘婯0H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘媆$0H媡$8H兡 _脟   �   �   �   �   �   �   �      �   t  J G            c     S  �)        �donut::render::DepthPass::~DepthPass 
 >+j   this  AI       F AJ          M        u  �? M        �  �?DE
 >�$    temp  AJ  C      AJ S      N N M        �  �+ M        *  �+DE
 >7     temp  AJ  /      AJ ?      N N M        �  � M        )  �DE
 >;     temp  AJ        AJ +      N N M        �  � M        )  �DE
 >;     temp  AJ        AJ       N N M        �  �� M        �  ��DE
 >D     temp  AJ  �       AJ       N N M        �  �� M        �  ��DE
 >D     temp  AJ  �       AJ �       N N M        z  �� M        (  ��DE
 >�    temp  AJ  �       AJ �       N N M        �  �� M        ,  ��DG
 >�!    temp  AJ  �       AJ �       N N M        N  ��
 Z   	   N M        �  3L M        2  L' M        �  X,
 >a&   this  AM  S     d  M        �  l	
 N N N N M        �)  : M        �)  ) M        �  #,
 >a&   this  AM       :  M        �  7	 N N N N                      0@� b h   �  �  N  S  u  �  �  �  �  �  )  *  z  �  �  (  ,  2  �  �)  �)  �)  �)   0   +j  Othis  95       b&   9I       b&   9j       b&   9|       b&   9�       �   9�       �   9�       �   9�       �   9      �   9'      �   9;      �   9O      �   O,   �   0   �  
 o   �   s   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 =  �   A  �  
 M  �   Q  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 #  �   '  �  
 ~  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 T  �   X  �  
 d  �   h  �  
 �  �   �  �  
 �  �   �  �  
 e  �   i  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �     �  
   �     �  
    �   $  �  
 0  �   4  �  
 @  �   D  �  
 P  �   T  �  
 `  �   d  �  
 p  �   t  �  
 H塡$WH冹 H嬞H伭�   L�
    �   D岯    怘婯03�H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘媆$0H兡 _�   �   %   �      �   �  X G            �   
   �   &        �nvrhi::GraphicsPipelineDesc::~GraphicsPipelineDesc 
 >,    this  AI  
     �  AJ        
  M        �  �� M        *  ��DE
 >7     temp  AJ  �       AJ �       N N M        �  | M        )  |DE
 >;     temp  AJ  �       AJ �       N N M        �  h M        )  hDE
 >;     temp  AJ  l       AJ |       N N M        �  T M        )  TDE
 >;     temp  AJ  X       AJ h       N N M        �  @ M        )  @DE
 >;     temp  AJ  D       AJ T       N N M        �  * M        )  *DG
 >;     temp  AJ  .       AJ @       N N                      0H�  h   �  �  )  *  %  )   0   ,   Othis  9<       �   9P       �   9d       �   9x       �   9�       �   9�       �   O  ,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 I  �   M  �  
 Y  �   ]  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 +  �   /  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 b  �   f  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �     �   �   R G                       �)        �donut::render::IGeometryPass::~IGeometryPass 
 >j   this  AJ          D                           H�     j  Othis  O�                              B  �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$H塴$H塼$WH冹 H嬞H兞x�    怘婯p3鞨吷tH塳pH��P怘婯hH吷tH塳hH��P怘婯PH吷tIH婥`H+罤柳H��    H侜   rH兟'L婣鳬+菻岮鳫凐囷   I嬋�    H塳PH塳XH塳`H婯(H吷tIH婥8H+罤柳H��    H侜   rH兟'L婣鳬+菻岮鳫凐嚌   I嬋�    H塳(H塳0H塳8H婯H婣H�(H�9H�t,H�7H婳H吷tH塷H��P惡    H嬒�    H孇H咑u院    H婯�    怘婯H吷tH塳H��P怘�H吷t
H�+H��P怘媆$0H媗$8H媡$@H兡 _描    �   �   �   �   �   �   "  �   8  �   y  �      �   N	  ` G            ~     ~  �*        �donut::engine::MaterialBindingCache::~MaterialBindingCache 
 >u�   this  AI       gQ  AJ          M        u  丵 M        �  丵CE
 >�$    temp  AJ  T      AJ c      N N M        �  �= M        �  �=DE
 >D     temp  AJ  A      AJ Q      N N M        �*  )��#* M        �*  )��* M        �*  
�. M        �*  �. M        �  �.
 Z      N N N% M        �*  ��G#%
 >寵   _Head  AJ  �       AJ     5  %  >寵    _Pnode  AM  �     z  >寵    _Pnext  AL      )  AL     p  )  M        �*  �
 M        �*  

� M        �*  
� M        �  
�
 Z      N N N M        �*  � M        �  � M        ,  �DE
 >�!    temp  AJ  	      AJ       N N N N N N N M        �*  R����& M        �*  ��I=$�� M        �*  2����  M        �  ��)��
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  �     *  AK x     & M        �  ��d#��
 Z   q   >#    _Ptr_container  AP  �       AP �     �  �  >#    _Back_shift  AJ  �     =  AJ �     �    �  N N N M        �*  �� N N N M        �)  RK M        �)  Ki=$ M        *  2_ >#   _Count  AH  X     '    AH �       M        �  g)
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  g     *  AK x      M        �  pd# >#    _Ptr_container  AP  x       AP �     �  �  >#    _Back_shift  AJ  O     =  AJ �     �    �  N N N N N M        |  7 M        �  7DE
 >�    temp  AJ  ;       AJ K       N N M        6&  ! M        M&  !DG
 >M    temp  AJ  %       AJ 7       N N M        N  
 Z   	   N                      0@� � h)   �  �  N  S  u  |  �  �  �  �  �  �  ,  �  6&  M&  �)  �)  *  *  4*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*         $LN165  0   u�  Othis  93       �   9G       �   9      �   9M      �   9_      �   O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �      �  
 W  �   [  �  
 g  �   k  �  
 F  �   J  �  
 V  �   Z  �  
 {  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 c  �   g  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 +  �   /  �  
 ;  �   ?  �  
 �  �   �  �  
 �  �   �  �  
 7  �   ;  �  
 X  �   \  �  
 h  �   l  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
 i  �   m  �  
 y  �   }  �  
 �  �   �  �  
 �  �   �  �  
 �  \   �  \  
 
	  �   	  �  
 	  �   	  �  
 *	  �   .	  �  
 :	  �   >	  �  
 J	  �   N	  �  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �   Y   �      �   �  V G            ^      ^   �)        �nvrhi::VertexAttributeDesc::~VertexAttributeDesc 
 >�   this  AI  
     Q J   AJ        
  M        �  EK) M          ,(
	 M        D   N M        B  ,E M        `  &? M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        �  
"#
!
 Z   q   >#    _Ptr_container  AP  &     7    AP :       >#    _Back_shift  AJ  -     0 
   N N N N N N                       @� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0   �  Othis  O   ,   �   0   �  
 {   �      �  
 �   �   �   �  
 o  �   s  �  
 �  �   �  �  
 �  �   �  �  
   �   	  �  
 +  �   /  �  
 �  +   �  +  
 �     �   �   L G                       �        �std::_Ref_count_base::~_Ref_count_base 
 >a&   this  AJ          D                           H�     a&  Othis  O  �                  �            ~ �,   �   0   �  
 q   �   u   �  
 �   �   �   �  
 H�    H�H兞�       �      �      �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,   �   0   �  
 {   �      �  
 H�    H�H兞�       �      �      �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (                           Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 �       �      �   �   8 G                       �        �std::mutex::~mutex 
 >�,   this  AJ          M        N    N                        H�  h   N  S      �,  Othis  O ,   �   0   �  
 ]   �   a   �  
 @SH冹 H�    H嬞H�雎t
亨   �    H嬅H兡 [�	   I      �      �   �   } G            +      %   _*        �std::_Ref_count_obj2<donut::engine::MaterialBindingCache>::`scalar deleting destructor' 
 >M�   this  AI         AJ                                @� 
 h   O*   0   M�  Othis  O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 嬟H孂�    雒t
�  H嬒�    H媆$0H嬊H兡 _�   �   "   �      �   �   \ G            4   
   &   �)        �donut::render::DepthPass::`scalar deleting destructor' 
 >+j   this  AJ          AM       $                        @�  0   +j  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   ` G            !         �)        �donut::render::IGeometryPass::`scalar deleting destructor' 
 >j   this  AI  	       AJ        	                        @� 
 h   �)   0   j  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 @USVWATAUAVAWH崿$h���H侅�  H�    H3腍墔�   M嬦A孁L孃L嬹H塗$0E3砥D$PD塴$TL塴$XL塴$`L塴$hL塴$pL塴$xL塵�H峂堣    D塵W�3�E E0H塃@H�5    H塼$ L�
    峆E岴H峂 �    怢塵HI媈H婰$XH;藅$H呟tH�H嬎�PH婰$XH塡$XH吷tH��P怚媈H婰$`H;藅$H呟tH�H嬎�PH婰$`H塡$`H吷tH��P怘婱�H吷tL塵�H��P怉媶�  塃潴A啘  �E梵A帬  �M霡肚黎$圗轅肚$圗軥銮�   �   E翀E薎婲0H塋$0H吷tH��P怘塼$ L�
    �   D岯鼿峂P�    怚嬽L塵xH媆$0H呟t
H�H嬎�PH媢xH9\鮌t)H呟t
H�H嬎�P怘婰鮌H塡鮌H吷tH��P怘媢xH�艸塽xH呟t
H�H嬎�P怚嬢I嬚H岴PH肏峀$@H;萾H�L�(H婰 H塗 H吷tH��P怘兠H凔(r艸婨xH塃HL�
    �   D岯鼿峂P�    怢�
    �   D岯鵋峀$0�    @銮ttI媈 H婱�H;藅"H呟t
H�H嬎�PH婱�H塢�H吷tH��P怚嫀�  �    H嬝H吚t
H� H嬎�P怘婨HH婰� H塡� H吷tH��P怘媇HH�肏塢H�H媇HA�茎   u;I媬(H9|� t)H�t
H�H嬒�P怘婰� H墊� H吷tH��P怘媇HH�肏塢HI婲H�M嬏L岲$PI嬜��0  怢�
    �   D岯鼿峂 �    怘婱�H吷tL塵�H��P怘婰$xH吷tL塴$xH��P怘婰$pH吷tL塴$pH��P怘婰$hH吷tL塴$hH��P怘婰$`H吷tL塴$`H��P怘婰$XH吷tL塴$XH��R怚嬊H媿�   H3惕    H伳�  A_A^A]A\_^[]�   �   m   �   �   �   �   �   �   �   �  �   �  �   U  �   g  �   o  �   �  �   �  �   e  �   w  �           �   �  V G            $  -     �)        �donut::render::DepthPass::CreateGraphicsPipeline 
 >+j   this  AJ        9  AV  9     � >_a   key  A   3     � Ah        3  A  I    �  >�!   framebuffer  AQ        0  AT  0     � >V    pipelineDesc  CI  �   �    R  G  CI �       A   DP    M        �  /� M        �  �4 M        �  �4 N N M        �  �* N M        (  �C M        �  �$ N N N# M        �  仼G
 >N!    i  AI  �    [  M        �  侢 M        �  侢	
 N N M        :  伹 M        u  伹 M        �  佺 M        �  佺 N N M        �  佪 N M        8  佄 M        
  佄#	 N N N N M        8  伆 M        
  伒#
 N N N M        8  乽	 M        
  亊# N N M        �)  � M        )  �GB
 >;     temp  AJ        AJ &    I  N N M        �  ��# M        �  � M        )  �
 >;     temp  AJ  �     (    AJ       N N M        �  � >;     tmp  AI  �     �  N M        9  �� M          ��# N N N M        �)  ��# M        �  �� M        *  ��
 >7     temp  AJ  �     (    AJ �     	  N N M        �  �� >7     tmp  AI  �     2  N M        �)  �� M          ��# N N N M        �  x2 N M        �  d
 N M        �  _ N M        �  Z N M        �  U N M        �  P N M        �  K N M        �  傎 M        �  傒 M        �  傜 M        �  傜
 >D     temp  AJ  �      AJ �    Z 
  1  :   N N M        �  傒 N N N M        �  偸 >D    other  AI  �    -  M        
  偸#	 N N M        �  倢!
 M        �  偗 M        )  偗
 >;     temp  AJ  �    &    AJ �      N N M        �  偒 >;     tmp  AI  �    :  N M        9  倷 M          倷#
 N N N M        :  � M        u  � M        �  �2 M        �  �2
 >D     temp  AJ  -      AJ >      N N M        �  �( >D     tmp  AM      7  AM I    �  N M        8  � M        
  �#	 N N N N M        �  冭 M        *  冭HB
 >7     temp  AJ  �      AJ �    
  N N M        �  円 M        )  円HB
 >;     temp  AJ  �      AJ �      N N M        �  兗 M        )  兗HB
 >;     temp  AJ  �      AJ �      N N M        �  儲 M        )  儲HB
 >;     temp  AJ  �      AJ �      N N M        �  儛 M        )  儛HB
 >;     temp  AJ  �      AJ �      N N M        �  億 M        )  億GB
 >;     temp  AJ  �      AJ �      N N Z   "  �*   �          @         A � h+   �  �  �  �  �  �  �  �  �  �  (  )  *  �  �  
      8  9       %  &  '  (  )  2  �  �  �  �  �  �  �  �  �  :  u  �)  �)  �)  "*  
 :�  O  �  +j  Othis  �  _a  Okey  �  �!  Oframebuffer  P   V   OpipelineDesc  9�       �   9�       �   9�       �   9      �   9"      �   9�      �   9�      �   9�      �   9�      �   9	      �   9<      �   9�      �   9�      �   9�      �   9�      �   9$      �   9:      �   9[      �$   9�      �   9�      �   9�      �   9�      �   9�      �   9�      �   O �   �           $  x     �       �  �K   �  ��   �  ��   �  �  �  �&  �  �0  �  �>  �  �L  �  �X  �  �a  �  �u  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �I  �  ��  �  ��   �   e F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$6  >V     pipelineDesc  EN  P                                  �  O�   �   e F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$7  >V     pipelineDesc  EN  P                                  �  O�   �   e F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$8  >V     pipelineDesc  EN  P                                  �  O�   �   e F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$9  >V     pipelineDesc  EN  P                                  �  O�   �   f F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$10  >V     pipelineDesc  EN  P                                  �  O   �   �   f F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$11  >V     pipelineDesc  EN  P                                  �  O   �   �   e F                                �`donut::render::DepthPass::CreateGraphicsPipeline'::`1'::dtor$0  >V     pipelineDesc  EN  P                                  �  O,   �   0   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 *  �   .  �  
 B  �   F  �  
 =  �   A  �  
 *  �   .  �  
 :  �   >  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 k  �   o  �  
 {  �     �  
 �  �   �  �  
 v  �   z  �  
 �  �   �  �  
 �  �   �  �  
 �	  �   �	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 �	  �   
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �
  �     �  
   �     �  
 j  �   n  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 @  �   D  �  
 P  �   T  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 '  �   +  �  
 7  �   ;  �  
 G  �   K  �  
 W  �   [  �  
 g  �   k  �  
 w  �   {  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 '  �   +  �  
 7  �   ;  �  
 G  �   K  �  
 W  �   [  �  
 g  �   k  �  
 w  �   {  �  
 �  �   �  �  
 �  �   �  �  
 �     �    
 �     �    
 <     @    
 �     �    
 �     �    
 b     f    
 �     �    
      "    
 p  �   t  �  
 �  �   �  �  
 0     4    
 �     �    
 �  �   �  �  
 Z  �   ^  �  
 H崐P   �       �   H崐P   H兞�       �   H崐P   H兞�       �   H崐P   H兞�       �   H崐P   H兞 �       �   H崐P   H兞(�       �   H崐P   H兞0�       �   H塡$H塼$ WH侅�  H�    H3腍墑$�  3鯤塗$ H嬟H孂@8饱  tH�2閝  3襀峀$0A�(  �    3襀峀$@A�   �    稄�  嬈勆H塼$ �   壌$H  斃莿$L  �   勆莿$P     莿$T  �  擠$8f塗$0塂$4荄$ 
   艱$$勆u艱$$H婦$ H崒$`  H塼$ H塂$@荄$    艱$$H婦$ H塼$ H塂$H�   f塂$&塗$ �   艱$$
H婦$ H塂$PH岲$0H莿$@     H崏�    HH崁�   A�@�I�H�A�@�I�H�A�@�I�H�A�I餒冴u� L崉$`  H嬘HH婡 IH堿 H婳H��怭  H嬅H媽$�  H3惕    L崪$�  I媅 I媠(I嬨_�   �   P   !   b   !   �        �   j  X G            �  $   �  �)        �donut::render::DepthPass::CreateInputBindingLayout 
 >+j   this  AJ        1  AM  1     � >�    bindingLayoutDesc  D`   M        �)  
� M        �  
� N N M        �)  ��
 >�    result  B    �     �  N M        �)  ��
 M        �  ��
 N N M        �)  ��
 >�    result  B    �       N M        �)  �� M        �  �� N N M        �)  �� >�    result  B    �       N M        �)  �� >�    result  B    v     h  N M        �)  �� N M        �)  v7 N M        �  T'

 N M        �)  : N �                    A > h   �  �      �  �  �)  �)  �)  �)  �)  �)  �)  �)  
 :�  O  �  +j  Othis  `  �  ObindingLayoutDesc  9�      �$   O  �   H           �  x     <       �  �1   �  �:   �  �B   �  ��  �  ��  �  �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 ,  �   0  �  
 �  �   �  �  
   �     �  
 J  �   N  �  
 f  �   j  �  
 �  �   �  �  
 H塡$UVWH崿$`��笭   �    H+郒�    H3腍墔�  3繦塗$hW缐厃  I嬸f墔}  H嬟垍  H孂3褹笭  H峂�D$pE��    �郡   H婩    茀x  t!H塂$ H岲$ 荄$(
   荄$,   T$0�H塂$@H岲$@荄$H
   荄$L   T$P A�  H峊$pHH婩H崓�  )D$pH塂$@)M�荄$H   荄$L   D$@荄$(   荄$,
   )E怘荄$     D$ H荄$0    H荄$8   L$0H菂p     )E�)U�)M黎    H婳L崊�  L婳(H嬘L�A�抈  H嬅H媿�  H3惕    H嫓$�   H伳�   _^]�          �   j   !   |   "   Z     �        �     U G            �  .   }  �)        �donut::render::DepthPass::CreateInputBindingSet 
 >+j   this  AJ        T  AM  T     H >vW   bufferGroup  AL  A     \ AP        A  >�    bindingSetDesc  D�   M        �)  �&  M        �  �& N N$ M        �)  �		n		 N M        �)  �8 M        �  �8 N N M        �)  ��Y N M        �)  ��
 M        �  ��
 N N M        �)  ��Z( N M        �)  ��Z( N M        �  T N �                     A : h
   �  �  �  !  x  �  �  �  �  �)  �)  �)  �)  
 :�   O  �   +j  Othis  �   vW  ObufferGroup  �  �  ObindingSetDesc  9s      �$   O�   8           �  x     ,       �  �.   �  �^  �  �}  �  �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 (  �   ,  �  
 @SVWH侅   H�    H3腍墑$�   I嬸H嬟H孂H塗$83繟8A創   E3蒐�    3襀峀$@�    怉�   L�    A峇H峀$x�    怉�   L�    A峇H崒$�   �    怘婳H�L嫆�   H塼$ A�   L岲$@H嬘A�覑L�
    �8   D岯薍峀$@�    �H�H嬅H媽$�   H3惕    H伳   _^[�   �   =   =   I   �   W   @   e   �   s   C   �   �   �   �   �   �   �         �   �  Q G            �      �   �)        �donut::render::DepthPass::CreateInputLayout 
 >+j   this  AJ        &  AM  &     �  >;    vertexShader  AL        �  AP           >)j   params  AQ        � : �  >枙    aInputDescs  D@    M        �)  �� N Z   �*  �*  �*  "                       A  h   �  �)  
 :�   O     +j  Othis  0  ;   OvertexShader  8  )j  Oparams  @   枙  OaInputDescs  9�       x$   O   �   X           �   x     L       w  �-   x  �7   |  �N   }  �j   ~  ��   �  ��   �  ��   �  ��   �   ` F                                �`donut::render::DepthPass::CreateInputLayout'::`1'::dtor$0  >枙    aInputDescs  EN  @                                  �  O  �   �   ` F                                �`donut::render::DepthPass::CreateInputLayout'::`1'::dtor$1  >枙    aInputDescs  EN  @                                  �  O  �   �   ` F            *      $             �`donut::render::DepthPass::CreateInputLayout'::`1'::dtor$3  >枙    aInputDescs  EN  @         $                        �  O  ,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 p  �   t  �  
 �  �   �  �  
 (     ,    
 �     �    
 �     �    
 D     H    
 H崐@   �       �   H崐x   �       �   @UH冹 H嬯L�
    A�   �8   H峂@�    H兡 ]�   �       �   L嬡I塠 UVWAVAWH侅�   H�    H3腍墑$�   I嬸H孃H嬮H塗$pE3�I荂�   A荂�   I荂�   E墈�W荔D$PM墈楢峅�    H塂$PH塂$XH峏H塡$`E岹H峊$xH嬋�    H塡$XD8饯  A斊灌   �    H嬝H塂$pH吚tiW� 茾   茾   H�    H�H岾L9線   暲L嫀�   L嫋�   E岹圖$@L塋$8L塗$0H岲$PH塂$(D坱$ E3蒆婾�    �I嬤H岰H�H塤H婰$PH吷t2H婽$`H+袶冣鳫嬃H侜   rH兟'H婭鳫+罤兝鳫凐w/�    H嬊H媽$�   H3惕    H嫓$�   H伳�   A_A^_^]描    �   �   f   �   �       �   �   �   I     �   d  �   w     �  �      �   ?  Z G            �  '   �  �)        �donut::render::DepthPass::CreateMaterialBindingCache 
 >+j   this  AJ        0  AN  0     ha  >�7   commonPasses  AL  *     nf  AP        *  >�    materialBindings  DP    M        �)  <�,f M        �)  �,2
\ M        *  *�>T M        �  丒)/
 Z     
 >   _Ptr  AH  E      AJ  1      AH c      AJ h      >#    _Bytes  AK  ;    \   - *  M        �  丯d
9
 Z   q   >#    _Ptr_container  AH  Y      AJ  V      N N N N N M        #*  ��i
 Z   �   >M�    _Rx  AI  �     � �   Bp   �     �  M        �*  �% N M        �*  ��I M        �  	�� N M        �*  E��
 Z   �*   N N N M        �)  W2
 M        '*  a M        Q*  a M        b*  a)%	 >    _Newvec  AH  j     $  M        �*  	a M        �*  	a M        �  	a M        �  	a
 Z   �   N N N N N N M        �*  0} M        �*  } N N N M        &*  
W M        P*  
W N N N �           (         A h@   B  �  �  �  �  �  �  y  �  �  �  �  �  4  �  �!  �!  �)  �)  �)  �)  �)  �)  �)  �)  �)  �)  *  *  #*  &*  '*  4*  L*  P*  Q*  b*  c*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  
 :�   O        $LN119  �   +j  Othis  �   �7  OcommonPasses  P   �  OmaterialBindings  ^�      L�   O �   @           �  x     4       �  �8   �  ��   �  �k  �  ��  �  ��   �   i F                                �`donut::render::DepthPass::CreateMaterialBindingCache'::`1'::dtor$0  >�    materialBindings  EN  P                                  �  O�   �   i F                               �`donut::render::DepthPass::CreateMaterialBindingCache'::`1'::dtor$3  >�    materialBindings  EN  P                                 �  O,   �   0   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 9  �   =  �  
 I  �   M  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  K   �  K  
 ;  �   ?  �  
 T  �   X  �  
 �  �   �  �  
 .  �   2  �  
 �     �    
 �     �    
 H崐P   �       �   @UH冹 H嬯亨   H婱p�    H兡 ]�   �   L嬡SH侅�   f荄$@ I岾菻嬟L�
    3襂嬂I塖燣�    I塊業岾豂塊怚岾鐸塊圚嬋I塖菼塖蠭塖豂塖郔塖鐸塖餒嬘�    H嬅H伳�   [�   7   ,   :   c   �      �   Y  Q G            s      j   �)        �donut::render::DepthPass::CreatePixelShader 
 >+j   this  AJ          D�    >肐   shaderFactory  AH  %     B  AP        %  >)j   params  AQ           D�   
 Z   �   �                     @ 
 h      �   +j  Othis  �   肐  OshaderFactory  �   )j  Oparams  O   �   0           s   x     $       r  �   s  �j   t  �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 p  �   t  �  
 L嬡SH侅�   H嬟f荄$@ 3襂嬂A8QL�    I塖菼塖蠭塖豂塖郔塖鐸塖餓塖燞嬘tI岾菼塊楲�
    I岾豂塊怚岾桦#H崒$�   H塋$0L�
    H峀$pH塋$(H峀$`H塋$ H嬋�    H嬅H伳�   [�!   .   Q   1   s   4   �   �      �   Z  R G            �      �   �)        �donut::render::DepthPass::CreateVertexShader 
 >+j   this  AJ        k J   D�    >肐   shaderFactory  AH       y  AP          >)j   params  AQ        w U  
 Z   �   �                     @ 
 h      �   +j  Othis  �   肐  OshaderFactory  �   )j  Oparams  O  �   @           �   x     4       b  �   e  �F   g  �c   l  ��   o  �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 p  �   t  �  
 H塡$ UVWAVAWH崿$0��感$  �    H+郒�    H3腍墔�#  I嬞I孁H嬺L嬹3褹�(  H峀$p�    3褹�   H峂��    E3�D壗�  菂�  �   菂�     菂�  �  �   f塂$pA稁�  �   勆AE菈D$t擠$xL墊$ 荄$    艱$$
H婦$ H塃�L墊$ 艱$$H婦$ H塃圚菂�     H崟�  H岲$pA峅�      HJ@ B H0J0@@B@HPJP@`B`H崚�   HpJ餒崁�   H冮u� HJH婬 H塉 I婲H�L崊�  H峊$ �怭  I嬜H峀$`H;萾H�L�8H�H�H吷tH��P怘婰$ H吷tL墊$ H��P�3缐厃  f墔}  垍  3褹咐  H峂拌    禖垍x  I媈8    D$ H呟tH�H嬎�P �x; �
u�	荄$H   圖$LH塡$@f荄$M  艱$O D$@)D$pL$ )M�I媶�  H媹�   D墊$(荄$,   H塋$ L墊$0L墊$8D$ )E�L$0)M燞菂p     A�  H峊$pH崓�  �    I婲H�L�L崊�  H峊$ �恅  I嬜H峀$hH;萾H�L�8H�H�H吷tH��P怘婰$ H吷tL墊$ H��P怘媿�#  H3惕    H嫓$%  H伳�$  A_A^_^]�      $   �   L   !   ]   !   �  !   �  "   �             �   �  R G              2   �  �)        �donut::render::DepthPass::CreateViewBindings 
 >+j   this  AJ        >  AV  >     � >�    layout  AK        ;  AL  ;     � >�=   set  AM  8     � AP        8  >)j   params  AI  5     � AQ        5  >�    bindingLayoutDesc  D�   >�    bindingSetDesc  D�   M        �  傘 M        ,  傘HB
 >�!    temp  AJ  �      AJ �      B    �    -  B(+  �    a  N N M        �  $偩 M        �  傋 M        ,  傋
 >�!    temp  AJ  �      AJ �      N N M        *  傃 >�!    tmp  AK  �    !  AK �    <    N M        +  偩C
 M        *  偹 N N N M        �)  俷 M        �  俷 N N M        e  俁%(
 >M   sampler  AJ  R    K  N M        �)  �1 M        �  �1 N N" M        d  侟($E N M        �)  
佲 N M        �  佈 N M        �  仸 M        �  仸HB
 >D     temp  AJ  �      AJ �    !  B    �    G  B�)  �    � N N M        �  $亖 M        �  仛 M        �  仛
 >D     temp  AJ  �      AJ �      N N M        �  仈 >D     tmp  AK  �    !  AK �    -    N M        (  亖C
 M        �  亷 N N N M        �)  �� M        �  ��	 N N M        b  
�� >�    result  B    �     -�   N M        �)  	�� M        �  	�� N N M        a  �� >�    result  B    �       N M        �)  �� N M        �)  
�� N M        �  P( N �$          (         A � h%   �  �  �  �  �  �  �  �  �  (  a  b  d  e      !  4  x  �  �  �  �  �  �  �  *  +  ,  �  �)  �)  �)  �)  �)  �)  �)  
 :�$  O   %  +j  Othis  %  �   Olayout  %  �=  Oset  %  )j  Oparams  �  �  ObindingLayoutDesc  �  �  ObindingSetDesc  9{      �$   9�      �   9�      �   9      =   9�      �$   9�      �   9�      �   O �   H             x     <       �  �>   �  �h  �  ��  �  ��  �  ��  �  �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 L  �   P  �  
 \  �   `  �  
 �  �   �  �  
 �  �   �  �  
 l  �   p  �  
 O  �   S  �  
 _  �   c  �  
 o  �   s  �  
   �   �  �  
 �  �   �  �  
 �  �      �  
 <  �   @  �  
 L  �   P  �  
   �     �  
 p  �   t  �  
   �   #  �  
 /  �   3  �  
 ?  �   C  �  
 O  �   S  �  
 _  �   c  �  
 o  �   s  �  
   �   �  �  
 �  �   �  �  
 L嬡I塠I塻M塁WH冹@H孃H嬞E度H�%#"勪滘薒3菻钩     LA禖L3萀A禖L3萀A禖L3萀A禖L3萀A禖L3萀A禖L3萀A禖L3萀H媼�  I#蒆玲H嬂  H婣H嫇�  H;聇H�	L;@tD  H;羣H婡L;@u耠3繦吚t H;聇H婬H�H吷剶   H��P閴   H�H峊$(H嬎�PP怘崑�  L岲$`H峊$0�    H�0H媆$(H9^t(H呟t
H�H嬎�P怘婲H塣H吷tH��P怘媆$(H�    H岲$(H;鴗H�3跦呟tH荄$(    H�H嬎�P怘嬊H媆$PH媡$XH兡@_�  �      �   �  Z G            �     �  �)        �donut::render::DepthPass::GetOrCreateInputBindingSet 
 >+j   this  AI        AJ          AI �      >vW   bufferGroup  AP        �   AP �      D`    >p�   it  C      �     �   ( �  >SJ    bindingSet  D(    M        $*  ���� M        �*  
.��3/ M        �*  ��*/' >駅    _Where  AH  �     -  AH �     �   ( � 
 >Dl    _End  AK  �     S F   AK �      >Dl    _Bucket_lo  AJ  �       AJ �     2    >#    _Bucket  AJ  �       N N M        )*  s M        Z*  s M        h*  s M        �*  s M        �*  s5 M        c  >(4(4(4(4(4(4
 >#    _Val  AQ  +     � �   AQ �      N N N N N N N M        '  �� M        C'  ��	 N N M        �  乮 M        ,  乮CB		
 >�!   temp  AI  %    \  AI �      C      i       N N M        +  丼G
 M        *  乨 N N M        �)  � 
 >3J   this  AL       a  AL �    
  M        �  丅 M        ,  丅
 >�!    temp  AJ  >      AJ N    F /   N N M        *  �: N M        '  �+ M        C'  �+#	 N N N M        �)  �
 Z   (*   N @                    @ � h$   c  �  �  *  +  ,  '  C'  �)  �)  �)  �)  �)  �)  �)  �)  *  *  	*  
*  $*  )*  6*  :*  W*  Y*  Z*  h*  �*  �*  �*  �*  �*  �*  �*  �*   P   +j  Othis  `   vW  ObufferGroup  (   SJ  ObindingSet  9�       �   9      1j   96      �   9J      �   9}      �   O�   X           �  x     L       �  �   �  ��   �  ��   �  ��   �  �  �  �S  �  ��  �  ��   �   i F                                �`donut::render::DepthPass::GetOrCreateInputBindingSet'::`1'::dtor$0  >vW   bufferGroup  EN  `           >SJ    bindingSet  EN  (                                  �  O,   �   0   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 0  �   4  �  
 V  �   Z  �  
   �   !  �  
 1  �   5  �  
 �  �   �  �  
 �  �   �  �  
    �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 h  �   l  �  
 �  �   �  �  
 �  �     �  
 H崐(   �       �   �   �   �   �   U G                      �)        �donut::render::DepthPass::GetSupportedViewTypes 
 >=j   this  AJ          D                           @     =j  Othis  O �   0              x     $        �     �    �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 @SUVWAVH侅�   H�    H3腍墑$�   M嬸H嬟H嬹A禓垇�  H�M嬋L嬄H峊$(�P03韹誋峀$@H;萾H�H�(H婲H塚H吷tH��P怘婰$(H吷tH塴$(H��P怘�M嬑L嬅H峊$0H嬑�P8H嬚H峀$HH;萾H�H�(H婲 H塚 H吷tH��P怘婰$0H吷tH塴$0H��P怘�M嬑L婩H峊$8H嬑�P@H嬚H峀$PH;萾H�H�(H婲H塚H吷tH��P怘婰$8H吷tH塴$8H��P怘�H峊$ H嬑�PHH嬚H峀$XH;萾H�H�(H婲(H塚(H吷tH��P怘婰$ H吷tH塴$ H��P怚�> t9I婩H吚t�@I婲I�H墕�  H嬀   H墡   H�劊   ����雞H�L媶�  H峊$hH嬑�P`H�H婸H�(H塰H墡�  H嬀   H墫   ����H�t)嬅�罣凐uH�H嬒�嬅�罣凐u	H�H嬒�PH媩$pH�t'嬅�罣凐uH�H嬒��羅凔u	H�H嬒�PH媬H�H嫎�   E婲 L�    篅   H崒$�   �    怢嬂H峊$ H嬒�親嬚H峀$`H;萾H�H�(H婲8H塚8H吷tH��P怘婰$ H吷tH塴$ H��P怘嫈$�   H凓v1H�翲媽$�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐wU�    H�L岶@H峍0M嬑H嬑�PXA婩墕�  A婩墕�  A婩墕�  H媽$�   H3惕    H伳�   A^_^][描    �   �   a  +   s  �   �  �   ?     R  �      �     D G            W      W  �)        �donut::render::DepthPass::Init 
 >+j   this  AJ        )  AL  )     .%  >肐   shaderFactory  AI  &     ��  AK        &  AI L      >)j   params  AP        #  AV  #     4)  M        �  乕 M        �  乕HB
 >D     temp  AJ  `      AJ q    Q  (  B    j    �Q  B  4    # N N M        �  &�4 M        �  丱 M        �  丱
 >D     temp  AJ  K      AJ [      B�  �    �, I  N N M        �  丟 >D     tmp  AK  7    #  AK [      d f �   N M        (  �4C
 M        �  丄 N N N M        �  � M        *  �HB
 >7     temp  AJ        AJ &      B�  �     n D8    N N M        �)  &�� M        �  � M        *  �
 >7     temp  AJ         AJ       B�  �    �, I  N N M        �  �� >7     tmp  AK  �     #  AK         N M        �)  ��C
 M        �  �� N N N M        �  �� M        )  ��HB
 >;     temp  AJ  �       AJ �       B`  �     � D0    N N M        �  &�� M        �  �� M        )  ��
 >;     temp  AJ  �       AJ �       B@  �    �, I  N N M        �  �� >;     tmp  AK  �     #  AK �     %    N M        g  ��C
 M        �  �� N N N M        �  m M        )  mHB
 >;     temp  AJ  r       AJ �       B  E      D(    N N M        �  'E M        �  a M        )  a
 >;     temp  AJ  ]       AJ m       B�   �    �, I  N N M        �  Y >;    tmp  AK  V       AK m     $    C       I     
  C      Y     8   )   N M        g  ED
 M        �  S N N N M        �)  7亀 M        �)  仯 M        �)  仯 M        �  仼 N N N M        �)  亪 M        *  亪
 M        �  亽

 >r&    _Tmp  AJ  �    (  AJ %    M    N M        E*  
亪 N N N M        �)  亀 M        =*  亀
 M        [*  亀	 M        >  亐 N N N N N M        �)  
� M        �)  
� N N M        �)  V伵 M        �)  +侌 M        �)  侌) M        �  侐, M        �  �	 N N N N M        �)  佊 M        *  佊 M        �  佢
 >r&    _Tmp  AK  �    :  AK     O    N M        E*  佊 N N N M        �)  伵 M        <*  伵#D N N N M        �  ?偮�� M          偮1�� M        D  偮 N M        B  1傂�� M        `  .傆~ M        �  傓)U
 Z     
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    � . P  M        �  傜d
_
 Z   q   >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        z  偓 M        (  偓HB
 >�    temp  AJ  �      AJ �    P  &  B    �    �  Bp  �    �, I  N N M        y  &倕 M        z  偁 M        (  偁
 >�    temp  AJ  �      AJ �      BP  �    �, I  N N M        &  倶 >�    tmp  AK  �    #  AK �        N M        '  倕C
 M        &  倰 N N N
 Z   �*   �           (         A � h;   �  �  �  �  �  �  �  �  �  �  �  �  �  �      (  )  *  B  C  D  E  `  �  �  �  �  g  #  y  z  �  �  &  '  (  >  �)  �)  �)  �)  �)  �)  �)  �)  �)  �)  �)  �)  �)  *  !*  ,*  <*  =*  E*  [*  �*  
 :�   O        $LN231    +j  Othis    肐  OshaderFactory     )j  Oparams  9B       ,j   9i       �   9       �   9�       ,j   9�       �   9�       �   9�       .j   9      �   9"      �   91      /j   9W      �   9m      �   9�      5j   9      b&   9      b&   97      b&   9I      b&   9�      b$   9�      �   9�      �   9      3j   O�   �           W  x     �       D  �)   E  �4   G  ��   H  ��   I  �&  J  �q  L  �w  M  ��  O  �%  Q  �  T  �  V  �  W  �)  X  �3  Y  �Q  Q  ��   w   S F                                �`donut::render::DepthPass::Init'::`1'::dtor$5                         �  O ,   �   0   �  
 i   �   m   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 Q  �   U  �  
 a  �   e  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
   �     �  
 Z  �   ^  �  
 j  �   n  �  
   �     �  
   �   #  �  
 /  �   3  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 J  �   N  �  
 Z  �   ^  �  
 j  �   n  �  
 �  �   �  �  
 �  �   �  �  
 Y  �   ]  �  
 i  �   m  �  
 y  �   }  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 N  �   R  �  
 ^  �   b  �  
 v  �   z  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ~
  �   �
  �  
 �
  �   �
  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 b  �   f  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 
  �   
  �  
 
  �   
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 v  P   z  P  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
   �     �  
   �      �  
 ,  �   0  �  
 <  �   @  �  
 L  �   P  �  
 \  �   `  �  
 l  �   p  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
   �     �  
 $  �   (  �  
 �     �    
 H崐�   �       �   @SH冹 H嬞H媺�  �    H崑�  H兡 [�       �   "   �      �   �   Q G            &         �)        �donut::render::DepthPass::ResetBindingCache 
 >+j   this  AI  	       AJ        	  Z   �*  �)                         @ 
 h   "*   0   +j  Othis  O �   @           &   x     4       \  �	   ]  �   ^  �   _  �!   ^  �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹@H�    H3腍塂$0�攻   M嬋H媆$pu9婥I嬌塂$ 婥塂$$婤塂$(婤H峊$ 塂$,I� A�   �惃   3繦塁H婰$0H3惕    H兡@[�	   �   h         �   H  P G            r      _   �)        �donut::render::DepthPass::SetPushConstants 
 >+j   this  AJ        ,  AJ _       >j   abstractContext  AK        F  AK _       >�$   commandList  AP          AQ       :  AQ _       >�!   state  AQ          Dh   
 >�"   args  AI  $     M  EO  (           Dp    >仏    constants  D     @                     A 
 :0   O  P   +j  Othis  X   j  OabstractContext  `   �$  OcommandList  h   �!  Ostate  p   �"  Oargs      仏  Oconstants  9S       �$   O�   P           r   x     D       �  �   �  �&    �)    �Y   	 �[   
 �_    �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   	  �  
 %  �   )  �  
 L  �   P  �  
 `  �   d  �  
 D  �   H  �  
 \  �   `  �  
 @USVWH崿$叁��H侅8  H�    H3腍墔   I孁H嬺I� H塂$ 艱$(3蹓\$,D$ A乸  8櫏  �  I婬H塋$@塡$HI婡HH塂$PH塋$X荄$`   I婡hH塂$hI婡H塂$p荄$x   H塢�H岴惞   H�H岪H冮u驢墲  H峀$@fD  	�AH�[L艕�D艩H嫕  H�肏墲  H兞H岴圚;萿薎崙�  H岴惞   fff�      HJ@ B H0J0@@B@HPJP@`B`H崚�   HpJ餒崁�   H冮u瓾�H�
隥H峊$ �    H嬘H峀$0H;萾H�H�H�H�H吷tH��P怘婰$ H吷tH塡$ H��P悑GH塅婫h塅H媿   H3惕    H伳8  _^[]�   �   p  �   �        �     Q G            �  %   �  �)        �donut::render::DepthPass::SetupInputBuffers 
 >+j   this  AJ        t[  >j   abstractContext  AK        +  AL  +     � >vW   buffers  AM  (     � AP        (  >�!   state  AQ        t AQ �      M        �)  ��
( >�!    <begin>$L0  AJ  �     K  M        �  �� N N M        �  仚 M        ,  仚HB
 >�!    temp  AJ  �      AJ �      B    �    .  B   t    b  N N M        �  $乼 M        �  亶 M        ,  亶
 >�!    temp  AJ  �      AJ �      N N M        *  亣 >�!    tmp  AK  w    !  AK �    =    N M        +  乼C
 M        *  亖 N N N
 Z   �)   8                    A J h   �  �  �  x  �  �  �  �  *  +  ,  �   �)  �)  �)  �)  �)  
 :   O  `  +j  Othis  h  j  OabstractContext  p  vW  Obuffers  x  �!  Ostate  9�      �   9�      �   O �   `           �  x  	   T       X �+   [ �K   ] �W   _ �h  d �j  g ��  h ��  i ��  k �,   �   0   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   
  �  
   �     �  
 Z  �   ^  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 l  �   p  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
   �     �  
 (  �   ,  �  
 @SVWAVAWH冹pH孃L嬹婤墑$�   敦A2賭�2豂婡PH吚tH�8 tA�竝   t��2襂媭�   H吚tH�8 tA�竩   t��2繟禜L��uf勔u劺剤  I嬓I嫀�  �    H吚刾  I婲@H塋$(H塂$03蒆塋$`H峊$(H�H塂�8H婰$`H�罤塋$`H兟H岲$8H;衭輤��勆�&  I婩@H塂$8H荄$`   �沱L嫾$�   I崡�  D$8L$HJD$X垳$�   B A�茎   uH�H婤(H�翲�B(嫾$�   I�4﨟儈H 吶   I崬H  H墱$�   H嬎�    吚呂   婥L=���劙   H婩HH吚厗   I�M婳D嬊H崝$�   I嬑�PhH嬋3繦峊$ H;裻
H�H�    H婲HH塅HH吷t
H��PH婩HH媽$�   H吷tH莿$�       H��PH婩HH吚uH嬎�    2繦兡pA_A^_^[肏嬎�    H婩HI��H兡pA_A^_^[�葔CL�   �    坦   �    虊   �   g  �     �     �   <  �   G  �      �   		  M G            L     L  �)        �donut::render::DepthPass::SetupMaterial 
 >+j   this  AJ          AV       :�   >j   abstractContext  AK          AM       6 AM       >j   material  AP        � � K  AP �     <v �   >�   cullMode  AY        � � K  AY �     <v �   >�!   state  AW  �     O	 .  EO  (           D�    >�    hasOpacityTexture  A   d     �   ) K  A        >_a   key  A          A   E    �  �   C       %     �  �  C      D     ��  � �	  B�        0 >6:    pipeline  AL  I    �  �   AL        >�    hasBaseOrDiffuseTexture  A   B     �   ? W  A  �       >�!    materialBindingSet  AH  �     #  AH �     X S >-    lockGuard  AI  [      B�   c    �  M        �)  F N M        �)  ' N M        �  ��G( >�!    <begin>$L0  AK  �     (  AK �      
 >�!    i  AH  �       M        �  ��
 N N M        �  �� M        �  ��	 N N M        �)  �2 N M        b  乧��
% M        O  乧
��

 Z   �  �  �   M        R  乻�� N N N M        �  #佒 M        1  佒KB

 >�!    temp  AJ  �      AJ �        B�   �    B  B�  �    �  >"     ref  A  �    # 
   N N M        �  -仼 M        �  伹 M        1  伹

 >�!    temp  AJ  �      AJ �      >"     ref  A  �    F   0   N N M        /  伩 >�!   tmp  AH  �    J  '  AH �    F   0   C       �    
  C      �    ]   3  G   N M        0  仼B

 M        /  伒 N N N M        a  � M        Q  �
 Z   u   N N M        a  侢 M        Q  侢
 Z   u   N N
 Z   �*   p           (         @ n h   O  Q  R  S  a  b  y  �  �  0  �  �  �  �  �  �  �  /  0  1  �)  �)  �)  "*  %*  -*         $LN94  �   +j  Othis  �   j  OabstractContext  �   j  Omaterial  �   �  OcullMode  �   �!  Ostate  �   _a  Okey  �   -  OlockGuard  9�      7j   9�      �   9�      �   O   �              L  x     �        �   " �   # �'   % �F   ) �h   - �~   / ��   1 ��   4 ��   5 ��   6 ��   7 ��   9 ��   : ��   A �/  B �>  D �I  F �T  H ��  J ��  K ��  M ��  N �  U �  O �  S �#  T �%  U �1  H ��   �   \ F                                �`donut::render::DepthPass::SetupMaterial'::`1'::dtor$0                         �  O,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 6  �   :  �  
 J  �   N  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 $  �   (  �  
 <  �   @  �  
 X  �   \  �  
 {  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �   "  �  
 F  �   J  �  
 V  �   Z  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 B  �   F  �  
 R  �   V  �  
 f  �   j  �  
 v  �   z  �  
 �  �   �  �  
   �   	  �  
   �     �  
 3  �   7  �  
 {  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 7  T   ;  T  
 �  �   �  �  
 �  �   �  �  
 	  �   		  �  
  	  �   $	  �  
 H
  �   L
  �  
 H崐�   �       �   H塡$H塴$H塼$WH侅�   I�I孁H嬯H嬞A�H峊$pI嬌I嬹�惃   H婼8L岲$0A笯   H荄$     H嬒 )D$0H)L$@@ )D$PH0H�)L$`�PxH�H嬑�P`类H嬑2E$0EH��P8类L崪$�   2EI媅$0EI媖I媠 I嬨_�   �   m  I G            �      �   �)        �donut::render::DepthPass::SetupView 
 >+j   this  AI  #     �  AJ        #  >j   abstractContext  AK           AN        �  >�$   commandList  AM       �  AP         
 >H   view  AL  1     �  AQ        1  >H   viewPrev  EO  (           D�    >問    depthConstants  D0    �                     @  h   x  H   �   +j  Othis  �   j  OabstractContext  �   �$  OcommandList  �   H  Oview  �   H  OviewPrev  0   問  OdepthConstants  91       +H   9x       �$   9�       &H   9�       &H   O   �   H           �   x     <        �    �7    �{    ��    ��    �,   �   0   �  
 n   �   r   �  
 ~   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 
  �     �  
   �   !  �  
 D  �   H  �  
 9  �   =  �  
 I  �   M  �  
 Y  �   ]  �  
 i  �   m  �  
 �  �   �  �  
 H塡$ L塂$AVH冹 L嬹I嬝H婭H嬃M�I+罤柳H;�冣   H�������H塼$0H墊$8H;�囜   H�4�    H侢   r)H峃'H;�喢   �    H吚劵   H峹'H冪郒塆H咑t
H嬑�    H孁�3�I�I婩H+罤柳H吚t.H��    H侜   rL婣鳫兟'I+菻岮鳫凐w`I嬋�    H�>I�>I塅I塅H;鴗H�H兦H;鴘鬑媡$0H媩$8H媆$HH兡 A^肏嬔L岲$@I嬌�    H媆$HH兡 A^描    惕    蘬   �   �   �   �   �     �   /  �   5  �      �   �  � G            :     :  *        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::_Assign_grow 
 >歶   this  AJ          AV       '�    >#   _Cells  AK        3p  �  � w   AK �     w  & 
 >麞   _Val  AI       $�    AP          D@    >#    _Oldsize  AH  '     �  �  >乻    _Newend  AH  �     2  >#    _Oldcapacity  AH  �     ,    AH �     	  >乻    _Newvec  AM  �       AM �     � \  k .  M        *   N M        *  �� N M        *  
0W��% M        �  U)
)%
��' M        �  ^$	%)
��
 Z   }   >#    _Block_size  AJ  b       AJ .      >#    _Ptr_container  AH  p       AH �     �  � 
 >`    _Ptr  AM  �       AM �     � \  k .  M        �  k
 Z   �   N N M        �  ��
 Z   �   N N M        �  

0
	 N N M        C*  ��#" >铇   _Backout  CM     �       CM    �         M        I*  �� N M        �*  �� N N M        *  .���� M        �  ��)]
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  �     &  AK 4     # M        �  
��#
`
 Z   q   >#    _Ptr_container  AP  �       AP �     b  X  >#    _Back_shift  AJ  �     9  AJ �     b ; "  N N N
 Z   **                         @ Z h   �  �  �  �  �  �  
*  *  *  *  *  +*  5*  C*  G*  H*  I*  �*  �*  �*  �*         $LN82  0   歶  Othis  8   #  O_Cells  @   麞  O_Val  O �   �           :  �	     �        �    �'   ! �0   " ��   $ ��   % ��   ' ��   + ��   , ��   - ��   . �  2 �  0 �"  2 �.  " �4  ' �,   �   0   �  
 !  �   %  �  
 1  �   5  �  
 Z  �   ^  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
 -  �   1  �  
 A  �   E  �  
 c  �   g  �  
 s  �   w  �  
 L  �   P  �  
 \  �   `  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 D  �   H  �  
 e  �   i  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
 	  �   
  �  
   �     �  
 �  >   �  >  
   �     �  
 H吷tH��   H�`�   �   �   m G                      M*        �std::_Ref_count_obj2<donut::engine::MaterialBindingCache>::_Delete_this 
 >M�   this  AJ                                 @�     M�  Othis  9
       Q�   O �   0              �     $       C �    D �   E �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H兞�       �      �   �   i G            	          N*        �std::_Ref_count_obj2<donut::engine::MaterialBindingCache>::_Destroy 
 >M�   this  AJ         
 Z   �*                          @�  h   �*  �*      M�  Othis  O �   (           	   �            ? �    @ �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塼$ WH冹 H�������H嬹H饺�   嬊H余H;�嚤  H岯�H塡$0H媈H荋饺L嬅L塼$@�罤隅H峃H�?�    H岹�H墌8H塅0H婩H� H嬋H;��)  H塴$8H砍     H�%#"勪滘�@ �     禤D禭H�	L3軱L3�禤LL3�禤LL3�禤LL3�禤LL3�禤LL3�禤LL3贚L#^0I零L^M�L;藆	I�I塁雤I婼L婡L;Bu/L�L;衪!L婬I�	L婣M�I婻H�M塀L塈H塒I塁�=L;蕋D  H婻L;BtOL;蕌馤婸I�
L婭I�L婤I� L塉L塓L堾I�H嬃H;�����H媗$8H媆$0L媡$@H媡$HH兡 _肔�L婬I�	L婣M�I婻H�M塀L塈H塒氪H�
    �    蘕   �   �  O   �  �      �   \
  �G            �  
   �  d*        �std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Forced_rehash 
 >l   this  AJ          AL       ��  >#   	 _Buckets  AK        �O � AM  O     ;  AM �      C             /  C      �    
  >#    _Max_storage_buckets  AH  %     �
 �
 >麞    _End  AI  ;     �n  >麞    _Inserted  AH  �      AH �     ?�   >麞    _Next_inserted  AJ  r     m >vs    _Bucket_lo  AS      � �   AS �     	 �  >#    _Bucket  AS         M        �  "


 N M        {*  h M        �*  h M        
*  l N N N M        �)  7 M        *  7 M        
*  7 N N N M        ~   .
 M        �  ;  >#    _Value  AH  2     *  N N M        p*  r�$ M        q*  r�$ N N M        *  	��T M        )*  	��P M        Z*  	��P M        h*  	��P M        �*  	��P M        �*  	��P M        c  	��M
 >#    _Val  AS  �     Q  N N N N N N N M        n*  �� M        s*  �� N N M        q*  � N M        �*  � M        �*  � N N M        s*  �' N& M        |*  �/$#$#$c$ >Dl    _Before_prev  AK  A      AK �      �  >Dl    _Last_prev  AP  :      AP �     � r  >Dl    _First_prev  AQ  3    #  AQ �     k �  N M        q*  乂 N& M        |*  乷$#$#$c$ >Dl    _Before_prev  AP  �      AP �     � r  >Dl    _Last_prev  AQ  z      AQ �     k �  >Dl    _First_prev  AR  s       AR �     � , �    N M        �*  乨 M        �*  乨 N N M        r*  乣 N& M        |*  伡$#$#$c$ >Dl   _First  AR  �    #  AR �     � , �    >Dl    _Before_prev  AK  �      AK �      �  >Dl    _Last_prev  AP  �      AP �     � r  >Dl    _First_prev  AQ  �      AQ �     k �  N Z   *  j                         @ � h    c  �  �  ~   �)  
*  *  *  *  )*  6*  Y*  Z*  h*  l*  m*  n*  o*  p*  q*  r*  s*  t*  x*  {*  |*  �*  �*  �*  �*  �*  �*         $LN138  0   l  Othis  8   #   O_Buckets  O�   X          �  �	  (   L      � �
   � �   � �   � �   � �%   � �.   � �7   � �;   � �B   � �J   � �O   � �\   � �`   � �h   � �o   � ��   � ��   � ��   � �   � �  � �  � �  � �  � �  � �  � �'  � �-  � �/  � �P  � �T  � �V  � �`  � �j  � �o  � ��  � ��  � ��   ��  � ��  � �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 '  �   +  �  
 ;  �   ?  �  
 O  �   S  �  
 ~  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
 +  �   /  �  
 ?  �   C  �  
 e  �   i  �  
 h  �   l  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 	  �   	  �  
 (	  �   ,	  �  
 R	  �   V	  �  
 b	  �   f	  �  
 ,
  <   0
  <  
 p
  �   t
  �  
 3烂   �   �   H G                      �        �std::_Ref_count_base::_Get_deleter 
 >l&   this  AJ          D    >�%   __formal  AK          D                           @�     l&  Othis     �%  O__formal  O�   0              �     $       � �    � �   � �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 �   �      �  
 H冹HH峀$ �    H�    H峀$ �    �
   �                  �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               �            J �   K �,   �   0   �  
 �   )   �   )  
 �   �   �   �  
 H塡$UVWATAUAVAWH冹0M嬸L孃H嬹I;��=  L媔H婹H塗$(M媑L墹$�   I嬤A禣H�%#"勪滘薍3菼赋     IA禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼A禛H3菼H婩0H#罤拎H翲塂$xH�H墝$�   H婡H塂$  H嬰H孄H�H婳H吷tH荊    H��P惡    H嬒�    H�NH;l$ t I;辵翷9�$�   �/  H婰$xH��"  H婰$xL9�$�   uL�)I嬇�I嬆H堿I;�匊   禟H�%#"勪滘薍3菻撼     H禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻禖H3菻L媐0L#酙龄Ld$(M媩$@ fff�     H嬰H孄H�H婳H吷tH荊    H��P惡    H嬒�    H�NI;飔I;辵腎�$H媱$�   H�H塁�"M�,$M塴$I;��
���L嫟$�   I�$L塩I嬈H媆$pH兡0A_A^A]A\_^]�
  �     �      �   �	  �G            {     f  �)        �std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Unchecked_erase 
 >l   this  AJ          AL       \ >駅   _First  AK          AW       F AW `    � z  >Dl   _Last  AP          AV       \ >乻    _Bucket_bounds  AK  .     �  AK �     &     B(   3     H >   _Eraser  CI     B     ! CI    c     
 >Dl    _End  AU  *     9 AU c      >瓧    _Bucket_lo  AH  �       AJ  1    /    AJ `     �  Bx   �     � >#    _Bucket  AH  �       >瓧    _Bucket_lo  AT  �      AT `    p �  >#    _Bucket  AT  �      M        *  3 N M        *  {B M        )*  tB M        Z*  tB M        h*  tB M        �*  tB M        �*  tB5 M        c  B>(4(4(4(4(4(4
 >#    _Val  AJ  T     x  N N N N N N N! M        *  ��#
 >Dl    _Oldnext  AM  �     z  AM �     � z e  M        B*  ��
 M        9*  

� M        X*  
� M        �  
�
 Z      N N N M        �*  �� M        �  �� M        ,  ��DE
 >�!    temp  AJ  �       AJ       N N N N N M        *  s乣 M        )*  l乣 M        Z*  l乣 M        h*  l乣 M        �*  l乣 M        �*  l乣6 M        c  乣>'4'4'4'4'4'4
 >#    _Val  AJ  q    �  N N N N N N N! M        *  侒#
 >Dl    _Oldnext  AM  �    e  AM `    � e  M        B*  侚
 M        9*  

� M        X*  
� M        �  
�
 Z      N N N M        �*  侚 M        �  侚 M        ,  侚DE
 >�!    temp  AJ  �      AJ       N N N N N M        *  �0 N 0           8         0@� j h   c  �  �  �  ,  *  *  *  *  *  )*  3*  6*  9*  B*  X*  Y*  Z*  h*  �*  �*  �*  �*  �*  �*   p   l  Othis  x   駅  O_First  �   Dl  O_Last  9�       �   9
      �   O   �   0          {  �	  #   $      � �     �&    �*    �3    �B   
 ��    ��    ��    ��    ��    �   �   �   �,   �4   �9  ! �H  # �K  $ �N  % �P  & �S  + �`  , ��  . ��  0 ��  2 ��  3 �"  4 �'  8 �,  : �0  ; �A  @ �E  A �J  + �[  E �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
   �     �  
   �   #  �  
 C  �   G  �  
 S  �   W  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 	  �   
  �  
 .  �   2  �  
 >  �   B  �  
 R  �   V  �  
 f  �   j  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 0  �   4  �  
 @  �   D  �  
 &  �   *  �  
 6  �   :  �  
 %  �   )  �  
 �  �   �  �  
 �  �   �  �  
 y  �   }  �  
 �  �   �  �  
 �	  �   �	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 H塡$H塴$H塼$ WH冹 H孂H婭H吷剹   H婫8H凌H;羦H媁L嬄H�H嬒�    雟H婳H婣3鞨�(H�H呟t2fD  H�3H婯H吷tH塳H��P惡    H嬎�    H嬣H咑u訦婫H� H婫H堾H塷H婫H塂$0L岲$0H媁 H婳�    怘媆$8H媗$@H媡$HH兡 _�?   �   �   �   �   �      �   �  �G            �      �   �)        �std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::clear 
 >l   this  AJ          AM       �  >#    _Oldsize  AJ       . #   AJ �       >Dl    _Head  AK  5       M        �)  	�� M        *  	�� M        
*  	�� N N N# M        �)  E'' M        8*  II+
 >駅    _Pnode  AI  U     f  AI �       >駅    _Pnext  AL  c     )  AL `     j  )  M        B*  c
 M        9*  

w M        X*  
w M        �  
w
 Z      N N N M        �*  c M        �  c M        ,  cDE
 >�!    temp  AJ  g       AJ w       N N N N N N Z   �)  **                        0@� ^ h   �  �  �  �  ,  �)  �)  �)  
*  *  *  *  *  3*  8*  9*  B*  X*  �*  �*  �*  �*   0   l  Othis  9s       �   O  �   h           �   �	  
   \       { �   � �   � �$   � �1   � �5   � �C   � �E   � ��   � ��   � �,   �   0   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 *  �   .  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 &  �   *  �  
   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 H婹H�    H呉HE旅   �      �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                    $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H           "      "      h    20    2           #      #      n   
 
4 
2p    B           $      $      t    20    <           %      %      z   
 
4 
2p    B           &      &      �    20    <           '      '      �   
 
4 
2p    B           (      (      �    �                  *      *      �    20    ^           ,      ,      �    20    `           .      .      �    B                   �       "           /      /      �   h           �      �          �   2 B                   �       "           0      0      �   h           �      �          �   2 B                   �       "           1      1      �   h           �      �          �   2
 
4 
2p                 �       �           2      2      �   h           �      �          �   H  B                   �       "           3      3      �   h           �      �          �   2 B                   �       "           4      4      �   h           �      �          �   2 20    !           5      5      �    B                   
       "           6      6         h           
                �   2 20               7      7         ! t               7      7            E           7      7         !                 7      7         E   K           7      7         - B                   .       "           8      8      (   h           1      4          �   2 d T 4 2p                 =       }           9      9      7   h           @      C          �   �
 4 R���
�p`P                 L       {          :      :      F   h           O      R          �   �Q  d	 T 4 2p                 [       �           ;      ;      U   h           ^      a          �   �
 
d	 
2p    2           =      =      d   ! � 4     2          =      =      d   2   {           =      =      j   ! T 2   {          =      =      j   {   �          =      =      p   !   2   {          =      =      j   �  �          =      =      v   !   �  T  4     2          =      =      d   �  �          =      =      |   !       2          =      =      d   �  �          =      =      �    4	 2�    :           ?      ?      �   !
 
t d     :          ?      ?      �   :             ?      ?      �   !       :          ?      ?      �     .          ?      ?      �   !   t  d     :          ?      ?      �   .  :          ?      ?      �    20    [           A      A      �    20    e           C      C      �    20               D      D      �   ! t               D      D      �      E           D      D      �   !                 D      D      �   E   K           D      D      �   -  0      �           E      E      �     0      s           F      F      �      p`0          �            �       �           G      G      �   (           �      �   
    �2    餹       �      �         
� 8>N2  2P    *                       �   $ d4p      �            �          H      H      �   . 4p`P      �             �          I      I      �   2	 !4�!��
�p`P          �$           �                 J      J      �   (           �      �          �   
2 D mD '	 4  ��
p	`P          �                   �          L      L      �   (                 	   
    �6       �         � |��  2P                              -
 3 
��	��p`0P        �                  $          M      M         (                        .    .    .    .    .    .    E    �>    
                               �             �   &   �   ,   �   2   �   0�pF22Z�F.:$<^`Z$&<b4Hq  d 4
 rp                 '       �          N      N      !   (           *      -   
    P6    f       �      �   
   �   �& *<0^ 4
 Rp`P                 6       �          O      O      0   (           9      <   "    �6    ^    .    .    .    .    .    .    .    .    .    .    .    ~    .    .       �      �   
         	      
      
   !      &      +      0      5      :   �   ?      D      I       N      S      
j��x�" 2P    .                       ?   ~�   �p`P0          �            N       W          Q      Q      H   (           Q      T       2    >       �      �      �   � N4 ^D `D RD PD ��'9 20    &           R      R      Z   	 d T 4  p      �           S      S      `    ���p`0                 l       L          U      U      f   (           o      r   
    �>       �   	   �   
! mXT� % G p`0P        "           {       �          V      V      u   (           ~      �          �   � L4  r0    0             r           W      W      �    d 4 2p                 �       c          X      X      �   h           �      �          �   � 0A %9Zl
 
4 
2p    4           Y      Y      �    20    [           [      [      �    d T 4 2p                 �       ~          ]      ]      �   h           �      �          �   f0 )� 
 4 R���
�p`P                 �       +          _      _      �   (           �      �   
    @:    @h   �      �   ���        >           `      `      �   ! t      >          `      `      �   >   b           `      `      �   !       >          `      `      �   b   �           `      `      �    20    +           a      a      �    20                 �       J           b      b      �   h           �      �          �   R B      :           d      d      �   
 
4 
2p    0           e      e      �                               R      �      �   Unknown exception                             ^      �      �                               j      �      �   bad array new length                                �                                                                      .?AVbad_array_new_length@std@@                    ����                            �                   .?AVbad_alloc@std@@                   ����                            �                   .?AVexception@std@@                    ����                            �       ����    ����        ��������                                                            �      �      �      �       �   (   �   0   �                                                                                                                                       �      �      �      �       �   (   �   0   �   8   �   @   �   H   �   P   �   X   �   `   �   h   �   p   �   x   �   DepthPassConstants donut/passes/depth_vs.hlsl input_assembler buffer_loads main donut/passes/depth_ps.hlsl POSITION TEXCOORD TRANSFORM vector too long                                             �      �      �      �       �   unordered_map/set too long invalid hash bucket count                                             U      R                         X                   [               ����    @                         U                                               a      ^                         d                           g      [              ����    @                         a                                               m      j                         p                                   s      g      [              ����    @                         m                   .?AV_Ref_count_base@std@@                              |                                  ����    @                   v      y                                         �      �      �                   .?AVIGeometryPass@render@donut@@                              �                   �               ����    @                   �      �                                         �      �      �                   .?AVDepthPass@render@donut@@                              �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@                              �                           �                    ����    @                   �      �      _   �   S   Q 
{7        std::_Ref_count_obj2<donut::engine::MaterialBindingCache>::`vftable'     I      I  
    �   2   0 
嘠        donut::render::DepthPass::`vftable'      (      (  
    �   6   4 
27        donut::render::IGeometryPass::`vftable'      %      %  
    �   (   & 
7        std::exception::`vftable'    �      �  
    �   (   & 
7        std::bad_alloc::`vftable'    �      �  
    �   3   1 
7        std::bad_array_new_length::`vftable'             
 噾姏@|#~y埠斠�=*�(.瞗輩耾"0h�K蜌�([柏q�廁A"R�洞 80!A勽洶NE齱重�)遂哯峲0~閡黤j�蘴�&y�,jb,儁瓈訧)#hv瓯訧)#hv瓯屢b綩藋T籼x ���(！
Z暱s�7緷M3>飖9屓鰎蘯]忐e3>飖9屓(鎁甩沋犓翇^=f瓵��9淉X嬐�峧8�藆墾濐�$phxlA鸇畟豼�/��$B�h
t
�6圅�8v�+鸸雀nB0斤�3掁抅{l獕褳L%盒JV �G堠/鈸kp鑕\B�wmP�,�#顠"
^_滶欆柑僢靏"$呣-盹嚡�V[篠缲丟9未s�(�-
<鰏mk'3>飖9屓D6b�雵臂ep禭诵鰐麌臂ep禭�6IB��6萪O��'��$x稫霵婬(︶辋
爌桗�'項jT炧琔电'項j怽濸酾5�'項j`悟幎5坺�'項j球[U鱹r�'項j懭P豂QR�'項j:�c�>rj�'項jLmJ棶s刃d裞儌頂"d/#q�识鋲+^{3�1]訪^旦>漒夂嫑�v
`�0�a~.`�獉藝氧矘貴k{儃唴瑢演臉
孷�U� M�7%�&皵觅0~T�$愜w獛啯_"割棍B$愜w獛啯愹笐>倣涜胴'洋m|桬�3,=鞚\夂嫑�jyr撯╱p泸翬蟱_?+伽v�礏糋�!漹%)mZ繡)�M�1�4萉端祆癜~tj�> �!垸Ｒ啹�R※侺=�:b8�4n�	V3端祆癜~t孬艡QDs嶀預棊膬$R驯�-n嶀預棊膬B涵 =6]桦�'洋m|n�u具(�&搣S﹕绌崞佋甔殲衐ｖ螢雵�#佲磊*伋Yn瑐?f�,箩邆5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k�.�D�3擳^5�畯刺啇8麌�&茤3鱿�.嵘腴tu�孨d梅痧p﨔跣噉^u,�U(￥-腇樱�<�6W�畖�	I:愡+蛟鲷c{�">螶�,�u远(d�碏綁;�(� �3覠�/4<U�/IN�=y貆,癳Mt鸪Y0z蟗�$圑&茤3�n�厏莕0i|惱腛,|揯7綀f侯鞉泆�*�)亮浒脵"擴�7;�	属�6阳2P弐阧進♁恗昷-�N	YyFq奾7�1+;	=~Z)xm�1�祽蘝
W\鯩�=~盦韐;��宪鯵1\螸�#G狦尃m鹝�s劼�(vh�偄l7姤6�6l%觥呝贬Ce琈�佗� 譸P4曫.:(�>溊欤狔oX鳽Dn�躔7颦硣K鬦涗�:U蛜A霌郠p9熺�臮O,鍹觍�3!�_KK�$蟊惺鬮Щ"4咿%I栶賑?T#K駄$u卤<:斫FyV憑gki7d�
骘Y豾ImA糇J爊N鵘J廒%e�%C2F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o��腫62V雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埻﨏帲晗Di＄燤�'鹴aR�,F_棢杻#Q噤釺�
鮱了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8oc8曀黩6了5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛了5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊瓕汥0�#*O魕囱Q� 粎鹴aR�,F_棢杻#Q邽dk+p琢犇Li籩项扭咞taR�,F_棢杻#Q蹚�:%UFw壱3,�4q胭o�!骬6�/s.�.:6俙貱霯蝭﹛_韃�)裯 j|�8厢M\筹s,;窇藿啓��=緱�G�6'j职DgCJ8剩杫X]绪/ｎ	蜍R僁舴�:kn嶜�:5齵h周洊{= 栰宧霅;�d_f湲z竘雵J-WV8om�M%>mb雵J-WV8o.*~襠[
B雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛&ЖS椣�鍉巚K朩&ЖS椣�AF�辛�婿�@�=� -dd�a�:瀏7U旚g氯(邝u}樢閣yQj;想=�k(e嘘�
広xI$椦瞴j蓘焼��8畔矬�(�.�e=扊-)卪 dd�a�:_棢杻#QX P到恋�&"炡,檯�:a?M�"�dd�a�:"颰攘涹%a#弯杨$t樢閣yQ E<礼\dAWq�9�#nmdd�a�:W�輈λC氁舍劏誾C逅/�7牝�dd�a�:�
KY�)醬	�<覘妵
鶵R暚縆h韜舂�dd�a�:tk}i饀z2�&S�/�)樢閣yQ�
,騤离廕D�&嵢髽椩緖6�	E-嘚%dd�a�:r�I.�`9p>�%F
4雵J-WV8o}�!罱4=沟�a３p3a駽}�懵X泚哔�(�8��dd�a�:〉`阆揕�  Se�2b牳唑�軴�"S�dd�a�:_棢杻#Q骯丛g鶹~黄�(�D丢遧�6觛楟迺�%戆;}鰠鹴aR�,F_棢杻#QiI烰3怢蘠�:Rz�9E\$L釉轎4u�=雵J-WV8om�M%>mb.�?覡j@W╃砍鐓鹴aR�,F_棢杻#Q�-鶁嫓Wb]箅我F詍CD� K^�dd�a�:r�7)}玖焋tjxⅲ<欠9h跿搜n2竌V8CO芦'_��漲`A黫�3#\2帟巄G硦请嘕-WV8o额	hQ�)蒘�8萀D脸v傘]-屾咞taR�,F_棢杻#Q吀qv蕞	�-坓�(鬄�/ｎ	蜍R9E\$L釉��E光潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H詠zv(�8r:.^�础�Il�:3壊&
;柒鋦S辝杉槫墑~$樐蜆{脚+${�8hr�o7 璐誩W�匝瞁1焃吙%G>禡h�,4��;儗阱叿} 5]叨蝝�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2滃���$"�:邍A愦靮鸬2�>料C5��\&2�'O"佬L/g%ZZ�$为赞G刹~赣 "^惋砤��\&2溊齱蘱�%ZZ�$为赞G刹~赣 "^惋砤雀擡@wX+]T�LNE厧鐲乆,���1忆P越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       复              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn       0      燥"V     .debug$S       �             .text$mn    	   c       謠锖     .debug$S    
   �         	    .text$mn       p       '      .debug$S       �             .text$mn    
   +     L啎     .debug$S       �  Z       
    .text$x              S�
    .text$x              S�
    .text$mn       �       `螏�     .debug$S                     .text$mn               _葓�     .debug$S       \             .text$mn              �邆     .debug$S                    .text$mn              �邆     .debug$S                    .text$mn              恶Lc     .debug$S       �              .text$mn       �     樴輸     .debug$S       �  �           .text$x              �/�    .text$x              "�    .text$x              �-Va    .text$x               汞�    .text$x     !         y*,e    .text$x     "         ��    .text$x     #         �(鷉    .text$x     $         彧    .text$x     %         #%豰    .text$x     &   .      S�     .text$x     '         v虻�    .text$x     (         漵凍    .text$x     )         歱(    .text$x     *         �2褎    .text$x     +         O~性    .text$x     ,         詜�    .text$mn    -   �       (回     .debug$S    .   �          -    .text$mn    /          袁z\     .debug$S    0   �          /    .text$mn    1          �邆     .debug$S    2   �          1    .text$mn    3   <      .ズ     .debug$S    4   0  
       3    .text$mn    5   <      .ズ     .debug$S    6   L  
       5    .text$mn    7   !      :著�     .debug$S    8   <         7    .text$mn    9   2      X于     .debug$S    :   <         9    .text$mn    ;   "       坼	     .debug$S    <   �         ;    .text$mn    =   "       坼	     .debug$S    >   �         =    .text$mn    ?   "       坼	     .debug$S    @   �         ?    .text$mn    A   "       坼	     .debug$S    B   �         A    .text$mn    C   "       坼	     .debug$S    D   �         C    .text$mn    E   "       坼	     .debug$S    F   �         E    .text$mn    G   "       坼	     .debug$S    H   �         G    .text$mn    I         �!     .debug$S    J   l         I    .text$mn    K   e      D远     .debug$S    L   X         K    .text$mn    M   [       荘�     .debug$S    N   $         M    .text$mn    O   J      O�0�     .debug$S    P   �         O    .text$mn    Q         峦諡     .debug$S    R   4         Q    .text$mn    S   }      1�-�     .debug$S    T   �         S    .text$mn    U         6摙r     .debug$S    V            U    .text$mn    W   K       }'     .debug$S    X   �         W    .text$mn    Y   K       }'     .debug$S    Z   �         Y    .text$mn    [         �%     .debug$S    \   �         [    .text$mn    ]   [       荘�     .debug$S    ^   T         ]    .text$mn    _   `      板@�     .debug$S    `   �         _    .text$mn    a   c     氅1     .debug$S    b   �  B       a    .text$mn    c   �      4;�     .debug$S    d   �  *       c    .text$mn    e          .B+�     .debug$S    f   �          e    .text$mn    g   ~     替嵭     .debug$S    h   \	  P       g    .text$mn    i   ^      wP�     .debug$S    j   �         i    .text$mn    k          .B+�     .debug$S    l   �          k    .text$mn    m         ��#     .debug$S    n   �          m    .text$mn    o         ��#     .debug$S    p   �          o    .text$mn    q         �%     .debug$S    r   �          q    .text$mn    s   +      兾�4     .debug$S    t   �          s    .text$mn    u   4      +�L     .debug$S    v   �          u    .text$mn    w   !       ��     .debug$S    x   �          w    .text$mn    y   B      贘S     .debug$S    z             y    .text$mn    {   B      贘S     .debug$S    |            {    .text$mn    }   B      贘S     .debug$S    ~   �          }    .text$mn       H       襶.      .debug$S    �   �             .text$mn    �   $     �     .debug$S    �   �  �       �    .text$x     �         喣�,�    .text$x     �         衴)瑏    .text$x     �         &C�    .text$x     �         K{�瘉    .text$x     �         孇諫�    .text$x     �         鎩叓�    .text$x     �         � D�    .text$mn    �   �     r潟     .debug$S    �   �         �    .text$mn    �   �     鯡|�     .debug$S    �   `         �    .text$mn    �   �   
   鏷�     .debug$S    �   p         �    .text$x     �         鲉k��    .text$x     �         =鋫�    .text$x     �   *      .�
+�    .text$mn    �   �  	   �%�     .debug$S    �     ,       �    .text$x     �         喣�,�    .text$x     �         q�0v�    .text$mn    �   s      B&頢     .debug$S    �   �         �    .text$mn    �   �      Vj�     .debug$S    �   �         �    .text$mn    �        濨^�     .debug$S    �   �  H       �    .text$mn    �   �     雵磵     .debug$S    �   (  @       �    .text$x     �         Kバg�    .text$mn    �          �+斏     .debug$S    �   �          �    .text$mn    �   W     箱K�     .debug$S    �   <  �       �    .text$x     �         繀�8�    .text$mn    �   &      ��#     .debug$S    �   (         �    .text$mn    �   r      a�5F     .debug$S    �   �         �    .text$mn    �   �     麘翇     .debug$S    �   �  (       �    .text$mn    �   L     h�>F     .debug$S    �   �
  `       �    .text$x     �         mE��    .text$mn    �   �       ;�     .debug$S    �   �         �    .text$mn    �   :     愽鉻     .debug$S    �   �  <       �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �   	      ,嗶     .debug$S    �            �    .text$mn    �   �     濷I     .debug$S    �   �  P       �    .text$mn    �          �猴     .debug$S    �   ,         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �   {     -絽     .debug$S    �   �
  H       �    .text$mn    �   �      X瀏x     .debug$S    �   X         �    .text$mn    �         崪覩     .debug$S    �   �          �        \               x                �                �                �                �                �                �                                              4      9        U      o        o      �        �      }        �          i�                   �      3        �      y        
          i�                   ,      7        Q      m        v      5        �      {        �          i�                   �      �                       =               O               d               n               z               �      i        �      _        �      /                      &      -        D      E        z      G        �              �      ;              c        B      1        h      k        �      �        �      A        �               Y      ?        �      e        �      w        �          i�                   	      q              U        I      =        ~      Y        �              �      C        6      S        	      �        	      �              �               �        �      M        b      K        �      [        �      W        >      �        �      �        ]      �        �      �        M      �        �      �        p      �              �        �      �                      �      �        �      �        
      �        ^      �        �      �        ]      �        �      �        j      a        �      u        �          i�                   �               �      ]        9                              n               �                     g        6               �      
        �!              8#              �$      Q         %      �        N%      �        �%      s        �%          i�                   8&      I        �&      O        �'              �'              G(              �(      	        5)              �+              ,      �        �,      �        7-      �        �-      �        k.      �        /      '        �/      �        !0      (        �0      �        ;1      )        �1      *        32      +        �2      ,        +3              5              �5      �        �6              7               �7      �        8      �        �8      !        ;9      "        �9      �        :      #        �:      �        7;      $        �;      �        O<      %        �<      �        g=      &        �=      �        >               �>               �>               �>           __chkstk             �>           ceilf            memcpy           memmove          memset           $LN13           $LN5        9    $LN10       }    $LN7        3    $LN13       y    $LN10       5    $LN16       {    $LN3        �    $LN4        �    $LN37   ^   i    $LN40       i    $LN37   `   _    $LN40       _    $LN10       E    $LN10       G    $LN10       ;    $LN48       c    $LN10       A    $LN10       ?    $LN8        w    $LN10       =    $LN18       Y    $LN10       C    $LN77       S    $LN186      �    $LN67       �    $LN138  �  �    $LN142      �    $LN82   :  �    $LN85       �    $LN30   [   M    $LN33       M    $LN33   e   K    $LN36       K    $LN18       W    $LN23       �    $LN13       �    $LN15       �    $LN50       �    $LN34       �    $LN107      �    $LN119  �  �    $LN123      �    $LN287      �    $LN127      �    $LN137          $LN231  W  �    $LN238      �    $LN4        �    $LN4        �    $LN94   L  �    $LN101      �    $LN76       �    $LN6        �    $LN84       a    $LN8        u    $LN28   [   ]    $LN31       ]    $LN165  ~  g    $LN168      g    $LN206  +  
    $LN215      
    $LN20           $LN8        s    $LN43       O    $LN14   :       $LN17           $LN4            .xdata      �          F┑@        �>      �    .pdata      �         X賦�        ?      �    .xdata      �          （亵9        *?      �    .pdata      �          T枨9        S?      �    .xdata      �          %蚘%}        {?      �    .pdata      �         惻竗}        �?      �    .xdata      �          （亵3        �?      �    .pdata      �         2Fb�3        �?      �    .xdata      �          %蚘%y        @      �    .pdata      �         惻竗y        @@      �    .xdata      �          （亵5        f@      �    .pdata      �         2Fb�5        欯      �    .xdata      �          %蚘%{        虭      �    .pdata      �         惻竗{        �@      �    .xdata      �          懐j灪        0A      �    .pdata      �         Vbv        `A      �    .xdata      �          （亵i        廇      �    .pdata      �         翎珸i        紸      �    .xdata      �          （亵_        闍      �    .pdata      �         粻胄_        B      �    .xdata      �         /
        3B      �    .pdata      �         +eS籈        qB      �    .xdata      �   	      �#荤E        瓸      �    .xdata      �         jE        頑      �    .xdata      �          3狷 E        4C      �    .xdata      �         /
        tC      �    .pdata      �         +eS籊        瑿      �    .xdata      �   	      �#荤G        錍      �    .xdata      �         jG         D      �    .xdata      �          3狷 G        aD      �    .xdata      �         /
�;        淒      �    .pdata      �         +eS�;        蹹      �    .xdata      �   	      �#荤;        E      �    .xdata      �         j;        ]E      �    .xdata      �          3狷 ;              �    .xdata      �         �酑c        鏓      �    .pdata      �          鮩sc        F      �    .xdata      �   	      �#荤c        DF      �    .xdata      �         jc        uF      �    .xdata      �          爲飆c        現      �    .xdata      �         /
        軫      �    .pdata      �         +eS籄        G      �    .xdata      �   	      �#荤A        NG      �    .xdata      �         jA        塆      �    .xdata      �          3狷 A        蔊      �    .xdata      �         /
�?        H      �    .pdata      �         +eS�?        >H      �    .xdata      �   	      �#荤?        vH      �    .xdata      �         j?        盚      �    .xdata      �          3狷 ?        騂      �    .xdata      �          （亵w        -I      �    .pdata      �         萣�5w        aI      �    .xdata      �         /
�=        擨      �    .pdata      �         +eS�=        袸      �    .xdata      �   	      �#荤=        
J      �    .xdata      �         j=        LJ      �    .xdata      �          3狷 =        慗      �    .xdata      �          （亵Y        蠮      �    .pdata      �         � 資        K      �    .xdata      �         范^揧        cK      �    .pdata      �         鳶�Y        甂      �    .xdata      �         @鴚`Y        鵎      �    .pdata               [7躖        DL          .voltbl              飾殪Y    _volmd         .xdata              /
        廘         .pdata              +eS籆        襆         .xdata        	      �#荤C        M         .xdata              jC        YM         .xdata               3狷 C                 .xdata              vQ9	S        镸         .pdata              A刄7S        荖         .xdata      	  	      �#荤S              	   .xdata      
        jS        凱      
   .xdata               強S�S        jQ         .xdata               /巴劶        JR         .pdata      
        餃�        OT      
   .xdata        	      �#荤�        SV         .xdata              j�        ZX         .xdata               a煫U�        gZ         .xdata              ╬au�        n\         .pdata              v�        靅         .xdata        	      �#荤�        i_         .xdata              j�        閌         .xdata               (岙寰        ob         .xdata               G栚鞫        颿         .pdata               T枨�        xe         .xdata              0W圫�         g         .pdata              ]%(	�        奾         .xdata              甞淰�        j         .pdata              8迁荻        瀔         .xdata              毕岸        (m         .pdata              煐罢�        瞡         .xdata              �(崚�        <p         .pdata              庇僎�        苢         .xdata               炀縹�        Ps          .pdata      !        荖疂�        趖      !   .xdata      "         ii@�        dv      "   .pdata      #        礝
�        鰓      #   .xdata      $        塯4钒        噛      $   .pdata      %        囥鱢�        {      %   .xdata      &        Y        瓅      &   .pdata      '        s�&k�        @~      '   .xdata      (        n奧w�        �      (   .pdata      )        '擊偘        f�      )   .xdata      *         （亵M        鶄      *   .pdata      +        愶LM        賰      +   .xdata      ,         （亵K        竸      ,   .pdata      -        弋楰        2�      -   .xdata      .         （亵W        珖      .   .pdata      /        � 賅        髧      /   .xdata      0        范^揥        :�      0   .pdata      1        鳶�W        儓      1   .xdata      2        @鴚`W        虉      2   .pdata      3        [7躓        �      3   .voltbl     4         飾殪W    _volmd      4   .xdata      5         澏筂�        ^�      5   .pdata      6        ]鰴        鰤      6   .xdata      7         澏筂�        崐      7   .pdata      8        s栠"�        $�      8   .xdata      9        珟 �        簨      9   .pdata      :        79恨�        I�      :   .xdata      ;  	      � )9�        讓      ;   .xdata      <        �.�        h�      <   .xdata      =         \Nu粠        ��      =   .xdata      >         k箮        悗      >   .pdata      ?        瀪秇�        .�      ?   .xdata      @        﨔�%�        藦      @   .pdata      A         龏        <�      A   .xdata      B        �;�        瑦      B   .pdata      C        榄譖�        0�      C   .xdata      D  $      s夝矝        硲      D   .pdata      E        9s敍        b�      E   .xdata      F  	      � )9�        �      F   .xdata      G        j�        翐      G   .xdata      H  
       燩N軟        x�      H   .xdata      I  $      }顡        )�      I   .pdata      J        黏Z�        茣      J   .xdata      K  	      � )9�        b�      K   .xdata      L        3?灾�        �      L   .xdata      M  	        %8�              M   .xdata      N         k箵        E�      N   .pdata      O        �$剧�        駱      O   .xdata      P  $      荰�        湙      P   .pdata      Q        縃s墎        2�      Q   .xdata      R  	      � )9�        菤      R   .xdata      S  6   
   頮扊�        _�      S   .xdata      T  2       	O�        龥      T   .xdata      U        5�.劃        暅      U   .pdata      V        勘�
�        �      V   .xdata      W  	      � )9�              W   .xdata      X        挔�        1�      X   .xdata      Y         盽	p�        聻      Y   .xdata      Z        鈢�        M�      Z   .pdata      [        疒�        翢      [   .xdata      \  	      � )9        4�      \   .xdata      ]  W      e�        獱      ]   .xdata      ^         筻錷        &�      ^   .xdata      _         k�        湣      _   .pdata      `        dp        �      `   .voltbl     a         d�    _volmd      a   .xdata      b         '��        、      b   .pdata      c        B�n�        �      c   .xdata      d  	      � )9�        d�      d   .xdata      e        蠤銪�        龋      e   .xdata      f         奒�
�        2�      f   .voltbl     g  
       4rPd�    _volmd      g   .xdata      h         （亵�        枻      h   .pdata      i        裬?�        绚      i   .xdata      j         R�>瀹        �      j   .pdata      k        杳Y伄        叆      k   .xdata      l        訤撰              l   .pdata      m        嬈�        摝      m   .xdata      n  	      � )9�        '�      n   .xdata      o  
      $�=�        晶      o   .xdata      p  
       �R�        [�      p   .xdata      q        �柀        颞      q   .pdata      r        /tf�        z�      r   .xdata      s  	      � )9�        �      s   .xdata      t        j�        嫪      t   .xdata      u         Hvl京        �      u   .xdata      v        祍K>�        カ      v   .pdata      w        頄u瞌        :�      w   .xdata      x        圇�
a        维      x   .pdata      y        飕鴄              y   .xdata      z  	      �#荤a        #�      z   .xdata      {        ja        P�      {   .xdata      |  	       唎嫕a        儹      |   .voltbl     }         �竌    _volmd      }   .xdata      ~         %蚘%u        碍      ~   .pdata              嘳�u        喹         .xdata      �         （亵]        �      �   .pdata      �        愶L]        挳      �   .xdata      �        vQ9	g        �      �   .pdata      �        埈g        J�      �   .xdata      �  	      �#荤g        �      �   .xdata      �        jg        矾      �   .xdata      �  
       辄謌        醑      �   .xdata      �         �"膧
        -�      �   .pdata      �        漝魰
        z�      �   .xdata      �  	      � )9
        拼      �   .xdata      �        �T
        �      �   .xdata      �  
       � 裇
        j�      �   .xdata      �         確        够      �   .pdata      �        OAG�        4�      �   .xdata      �        +縬[              �   .pdata      �        蹷謔        *�      �   .xdata      �        ＋)        α      �   .pdata      �        穣        "�      �   .xdata      �         （亵s        災      �   .pdata      �         ~        蚰      �   .xdata      �        蚲7MO        E�      �   .pdata      �        %轢窸              �   .xdata      �  	      �#荤O              �   .xdata      �        jO        b�      �   .xdata      �         攰eO        �      �   .xdata      �         �9�        匀      �   .pdata      �        礝
        1�      �   .xdata      �         %蚘%        嵣      �   .pdata      �        }S蛥        �      �   .rdata      �                          �   .rdata      �         �;�         攀      �   .rdata      �                     焓     �   .rdata      �                     �     �   .rdata      �         �)         %�      �   .xdata$x    �                     Q�      �   .xdata$x    �        虼�)         s�      �   .data$r     �  /      嶼�         査      �   .xdata$x    �  $      4��         凰      �   .data$r     �  $      鎊=         �      �   .xdata$x    �  $      銸E�         *�      �   .data$r     �  $      騏糡         i�      �   .xdata$x    �  $      4��         兲      �       绿           .data       �          烀�          仗      �       	�     �   .rdata      �  8                   0�     �   .rdata      �  �                   T�     �   .rdata      �         �;�         t�      �   .rdata      �         R(槕         浲      �   .rdata      �         �6畛         屯      �   .rdata      �  
       鲗�7         裢      �   .rdata      �         旲^         �      �   .rdata      �         �!鱓         (�      �   .rdata      �  	       s�6�         Z�      �   .rdata      �  	       汪�5         u�      �   .rdata      �  
       刈i�         愇      �   .rdata      �         IM               �   .rdata      �  (                   椅     �   .rdata      �         ��         �      �   .rdata      �         藾味         H�      �   .rdata$r    �  $      'e%�         y�      �   .rdata$r    �        �          懴      �   .rdata$r    �                           �   .rdata$r    �  $      Gv�:         较      �   .rdata$r    �  $      'e%�         芟      �   .rdata$r    �        }%B         粝      �   .rdata$r    �                     
�      �   .rdata$r    �  $      `          �      �   .rdata$r    �  $      'e%�         ?�      �   .rdata$r    �        �弾         b�      �   .rdata$r    �                     冃      �   .rdata$r    �  $      H衡�         ば      �   .data$rs    �  *      8V綊         涡      �   .rdata$r    �        �          钚      �   .rdata$r    �                     
�      �   .rdata$r    �  $      Gv�:         &�      �   .rdata$r    �  $      'e%�         K�      �   .data$rs    �  1      d         p�      �   .rdata$r    �        �          椦      �   .rdata$r    �                     貉      �   .rdata$r    �  $      Gv�:         菅      �   .rdata$r    �  $      'e%�         	�      �   .data$rs    �  -      锌Q�         *�      �   .rdata$r    �        }%B         M�      �   .rdata$r    �                     l�      �   .rdata$r    �  $      `         嬕      �   .rdata$r    �  $      'e%�         骋      �   .data$rs    �  Q      �鶴               �   .rdata$r    �        }%B         ?�      �   .rdata$r    �                     傆      �   .rdata$r    �  $      `         庞      �   .rdata      �         eL喳         �      �       !�           _fltused         .debug$S    �  `          �   .debug$S    �  @          �   .debug$S    �  D          �   .debug$S    �  4          �   .debug$S    �  4          �   .debug$S    �  @          �   .chks64     �                   3�  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z _Mtx_init_in_situ _Mtx_destroy_in_situ _Mtx_lock _Mtx_unlock ?_Throw_Cpp_error@std@@YAXH@Z ??1VertexAttributeDesc@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??0RenderState@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ ??0VertexBufferBinding@nvrhi@@QEAA@XZ ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ?GetVertexAttributeDesc@engine@donut@@YA?AUVertexAttributeDesc@nvrhi@@W4VertexAttribute@12@PEBDI@Z ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1IGeometryPass@render@donut@@UEAA@XZ ??_GIGeometryPass@render@donut@@UEAAPEAXI@Z ??_EIGeometryPass@render@donut@@UEAAPEAXI@Z ??1mutex@std@@QEAA@XZ ??1?$lock_guard@Vmutex@std@@@std@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ ??0?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$list@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ ?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z ?clear@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ ?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAA@XZ ??1?$unordered_map@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@7@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@@std@@QEAA@XZ ??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ ?CreateVertexShader@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z ?CreatePixelShader@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z ?CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z ?CreateInputBindingLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@XZ ?CreateInputBindingSet@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z ?CreateViewBindings@DepthPass@render@donut@@MEAAXAEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@5@AEBUCreateParameters@123@@Z ?CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z ?CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z ?GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z ??0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z ?Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z ?ResetBindingCache@DepthPass@render@donut@@QEAAXXZ ?GetSupportedViewTypes@DepthPass@render@donut@@UEBA?AW4Enum@ViewType@engine@3@XZ ?SetupView@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEAVICommandList@nvrhi@@PEBVIView@engine@3@2@Z ?SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z ?SetupInputBuffers@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEBUBufferGroup@engine@3@AEAUGraphicsState@nvrhi@@@Z ?SetPushConstants@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEAVICommandList@nvrhi@@AEAUGraphicsState@6@AEAUDrawArguments@6@@Z ??1DepthPass@render@donut@@UEAA@XZ ??_GDepthPass@render@donut@@UEAAPEAXI@Z ??_EDepthPass@render@donut@@UEAAPEAXI@Z ?CreateAutoShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0UStaticShader@23@11PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ??1?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@QEAA@XZ ??0MaterialBindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@W4ShaderType@4@I_NAEBV?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@PEAVISampler@4@PEAVITexture@4@2@Z ?GetLayout@MaterialBindingCache@engine@donut@@QEBAPEAVIBindingLayout@nvrhi@@XZ ?GetMaterialBindingSet@MaterialBindingCache@engine@donut@@QEAAPEAVIBindingSet@nvrhi@@PEBUMaterial@23@@Z ?Clear@MaterialBindingCache@engine@donut@@QEAAXXZ ??1MaterialBindingCache@engine@donut@@QEAA@XZ ?CreateVolatileConstantBufferDesc@utils@nvrhi@@YA?AUBufferDesc@2@IPEBDI@Z ??$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z ??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??1?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@UEAAPEAXI@Z ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Hash_representation@PEBUBufferGroup@engine@donut@@@std@@YA_KAEBQEBUBufferGroup@engine@donut@@@Z ??$_Copy_memmove@PEBUMaterialResourceBinding@engine@donut@@PEAU123@@std@@YAPEAUMaterialResourceBinding@engine@donut@@PEBU123@0PEAU123@@Z ??$_Fnv1a_append_value@PEBUBufferGroup@engine@donut@@@std@@YA_K_KAEBQEBUBufferGroup@engine@donut@@@Z ?dtor$0@?0???$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z@4HA ?dtor$0@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$0@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA ?dtor$0@?0??CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z@4HA ?dtor$0@?0??CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z@4HA ?dtor$0@?0??GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z@4HA ?dtor$0@?0??SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z@4HA ?dtor$10@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$10@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA ?dtor$11@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$11@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA ?dtor$12@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$13@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$17@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$18@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$1@?0???$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z@4HA ?dtor$1@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$1@?0??CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z@4HA ?dtor$2@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$3@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$3@?0??CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z@4HA ?dtor$3@?0??CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z@4HA ?dtor$4@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$5@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$5@?0??Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z@4HA ?dtor$6@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$6@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA ?dtor$7@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$7@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA ?dtor$8@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$8@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA ?dtor$9@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$9@?0??CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1VertexAttributeDesc@nvrhi@@QEAA@XZ $pdata$??1VertexAttributeDesc@nvrhi@@QEAA@XZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $pdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??_GIGeometryPass@render@donut@@UEAAPEAXI@Z $pdata$??_GIGeometryPass@render@donut@@UEAAPEAXI@Z $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@QEAA@XZ $unwind$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $unwind$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $pdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $cppxdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $stateUnwindMap$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $ip2state$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@2@PEAU32@QEAU32@@Z $unwind$?clear@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $pdata$?clear@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $cppxdata$?clear@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $stateUnwindMap$?clear@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $ip2state$?clear@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAAXXZ $unwind$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$1$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$2$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$3$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$5$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $chain$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $pdata$6$?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAAX_K@Z $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$1$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$2$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $chain$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $pdata$3$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@2@@Z $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $unwind$?CreateVertexShader@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $pdata$?CreateVertexShader@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $unwind$?CreatePixelShader@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $pdata$?CreatePixelShader@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $unwind$?CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z $pdata$?CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z $cppxdata$?CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z $stateUnwindMap$?CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z $ip2state$?CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z $unwind$?dtor$3@?0??CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z@4HA $pdata$?dtor$3@?0??CreateInputLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@PEAVIShader@5@AEBUCreateParameters@123@@Z@4HA $unwind$?CreateInputBindingLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@XZ $pdata$?CreateInputBindingLayout@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@XZ $unwind$?CreateInputBindingSet@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $pdata$?CreateInputBindingSet@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $unwind$?CreateViewBindings@DepthPass@render@donut@@MEAAXAEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@5@AEBUCreateParameters@123@@Z $pdata$?CreateViewBindings@DepthPass@render@donut@@MEAAXAEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@5@AEBUCreateParameters@123@@Z $cppxdata$?CreateViewBindings@DepthPass@render@donut@@MEAAXAEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@5@AEBUCreateParameters@123@@Z $stateUnwindMap$?CreateViewBindings@DepthPass@render@donut@@MEAAXAEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@5@AEBUCreateParameters@123@@Z $ip2state$?CreateViewBindings@DepthPass@render@donut@@MEAAXAEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@5@AEBUCreateParameters@123@@Z $unwind$?CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z $pdata$?CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z $cppxdata$?CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z $stateUnwindMap$?CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z $ip2state$?CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z $unwind$?dtor$3@?0??CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z@4HA $pdata$?dtor$3@?0??CreateMaterialBindingCache@DepthPass@render@donut@@MEAA?AV?$shared_ptr@VMaterialBindingCache@engine@donut@@@std@@AEAVCommonRenderPasses@engine@3@@Z@4HA $unwind$?CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z $pdata$?CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z $cppxdata$?CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z $stateUnwindMap$?CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z $ip2state$?CreateGraphicsPipeline@DepthPass@render@donut@@MEAA?AV?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@TPipelineKey@123@PEAVIFramebuffer@5@@Z $unwind$?GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $pdata$?GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $cppxdata$?GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $stateUnwindMap$?GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $ip2state$?GetOrCreateInputBindingSet@DepthPass@render@donut@@IEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@PEBUBufferGroup@engine@3@@Z $unwind$??0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $pdata$??0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $cppxdata$??0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $stateUnwindMap$??0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $ip2state$??0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $unwind$?dtor$9@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA $pdata$?dtor$9@?0???0DepthPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA $unwind$?Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $pdata$?Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $cppxdata$?Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $stateUnwindMap$?Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $ip2state$?Init@DepthPass@render@donut@@UEAAXAEAVShaderFactory@engine@3@AEBUCreateParameters@123@@Z $unwind$?ResetBindingCache@DepthPass@render@donut@@QEAAXXZ $pdata$?ResetBindingCache@DepthPass@render@donut@@QEAAXXZ $unwind$?SetupView@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEAVICommandList@nvrhi@@PEBVIView@engine@3@2@Z $pdata$?SetupView@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEAVICommandList@nvrhi@@PEBVIView@engine@3@2@Z $unwind$?SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z $pdata$?SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z $cppxdata$?SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z $stateUnwindMap$?SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z $ip2state$?SetupMaterial@DepthPass@render@donut@@UEAA_NAEAVGeometryPassContext@23@PEBUMaterial@engine@3@W4RasterCullMode@nvrhi@@AEAUGraphicsState@8@@Z $unwind$?SetupInputBuffers@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEBUBufferGroup@engine@3@AEAUGraphicsState@nvrhi@@@Z $pdata$?SetupInputBuffers@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEBUBufferGroup@engine@3@AEAUGraphicsState@nvrhi@@@Z $cppxdata$?SetupInputBuffers@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEBUBufferGroup@engine@3@AEAUGraphicsState@nvrhi@@@Z $stateUnwindMap$?SetupInputBuffers@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEBUBufferGroup@engine@3@AEAUGraphicsState@nvrhi@@@Z $ip2state$?SetupInputBuffers@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEBUBufferGroup@engine@3@AEAUGraphicsState@nvrhi@@@Z $unwind$?SetPushConstants@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEAVICommandList@nvrhi@@AEAUGraphicsState@6@AEAUDrawArguments@6@@Z $pdata$?SetPushConstants@DepthPass@render@donut@@UEAAXAEAVGeometryPassContext@23@PEAVICommandList@nvrhi@@AEAUGraphicsState@6@AEAUDrawArguments@6@@Z $unwind$??1DepthPass@render@donut@@UEAA@XZ $pdata$??1DepthPass@render@donut@@UEAA@XZ $cppxdata$??1DepthPass@render@donut@@UEAA@XZ $stateUnwindMap$??1DepthPass@render@donut@@UEAA@XZ $ip2state$??1DepthPass@render@donut@@UEAA@XZ $unwind$??_GDepthPass@render@donut@@UEAAPEAXI@Z $pdata$??_GDepthPass@render@donut@@UEAAPEAXI@Z $unwind$??1?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UMaterialResourceBinding@engine@donut@@V?$allocator@UMaterialResourceBinding@engine@donut@@@std@@@std@@QEAA@XZ $unwind$??1MaterialBindingCache@engine@donut@@QEAA@XZ $pdata$??1MaterialBindingCache@engine@donut@@QEAA@XZ $cppxdata$??1MaterialBindingCache@engine@donut@@QEAA@XZ $stateUnwindMap$??1MaterialBindingCache@engine@donut@@QEAA@XZ $ip2state$??1MaterialBindingCache@engine@donut@@QEAA@XZ $unwind$??$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z $pdata$??$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z $cppxdata$??$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z $stateUnwindMap$??$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z $ip2state$??$_Try_emplace@AEBQEBUBufferGroup@engine@donut@@$$V@?$_Hash@V?$_Umap_traits@PEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@PEBUBufferGroup@engine@donut@@U?$hash@PEBUBufferGroup@engine@donut@@@std@@U?$equal_to@PEBUBufferGroup@engine@donut@@@5@@std@@V?$allocator@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@7@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@_N@1@AEBQEBUBufferGroup@engine@donut@@@Z $unwind$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$0$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $chain$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $pdata$1$??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z $unwind$??_G?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@UEAAPEAXI@Z $unwind$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $cppxdata$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $ip2state$??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@QEBUBufferGroup@engine@donut@@V?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Copy_memmove@PEBUMaterialResourceBinding@engine@donut@@PEAU123@@std@@YAPEAUMaterialResourceBinding@engine@donut@@PEBU123@0PEAU123@@Z $pdata$??$_Copy_memmove@PEBUMaterialResourceBinding@engine@donut@@PEAU123@@std@@YAPEAUMaterialResourceBinding@engine@donut@@PEBU123@0PEAU123@@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7IGeometryPass@render@donut@@6B@ ??_7DepthPass@render@donut@@6B@ ??_C@_0BD@PGMMJIJO@DepthPassConstants@ ??_C@_0BL@HEBNIFBO@donut?1passes?1depth_vs?4hlsl@ ??_C@_0BA@KAKKICBM@input_assembler@ ??_C@_0N@MHELDFIK@buffer_loads@ ??_C@_04GHJNJNPO@main@ ??_C@_0BL@LCHCIMJJ@donut?1passes?1depth_ps?4hlsl@ ??_C@_08JPMAAECC@POSITION@ ??_C@_08CMDKBPJM@TEXCOORD@ ??_C@_09LFBMEAFB@TRANSFORM@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_7?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@6B@ ??_C@_0BL@GOIGLPKN@unordered_map?1set?5too?5long@ ??_C@_0BK@OGNNAFAB@invalid?5hash?5bucket?5count@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4IGeometryPass@render@donut@@6B@ ??_R0?AVIGeometryPass@render@donut@@@8 ??_R3IGeometryPass@render@donut@@8 ??_R2IGeometryPass@render@donut@@8 ??_R1A@?0A@EA@IGeometryPass@render@donut@@8 ??_R4DepthPass@render@donut@@6B@ ??_R0?AVDepthPass@render@donut@@@8 ??_R3DepthPass@render@donut@@8 ??_R2DepthPass@render@donut@@8 ??_R1A@?0A@EA@DepthPass@render@donut@@8 ??_R4?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VMaterialBindingCache@engine@donut@@@std@@8 __real@5f000000 __security_cookie 