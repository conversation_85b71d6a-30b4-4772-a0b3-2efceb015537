d� 祚Gh錊       .drectve        s  (               
 .debug$S        $
 �)  �6        @ B.debug$T        p   �6             @ B.rdata          @   k7             @ @@.text$mn        .   �7              P`.debug$S        �   �7 �8        @B.text$mn        :   9 W9         P`.debug$S          u9 �;        @B.text$mn           
< $<         P`.debug$S        �  .< �=        @B.text$mn        0   �> �>         P`.debug$S        �  �> 怈        @B.text$mn        0   A LA         P`.debug$S        �  VA C        @B.text$mn        �   WE         P`.debug$S        x
   P     P   @B.text$x         (   ?S gS         P`.text$mn        z   {S              P`.debug$S        D  鮏 9W         @B.text$mn        <  yX 礪         P`.debug$S        �  軿 峜     X   @B.text$mn        �   齠              P`.debug$S        h  鬵 \l     .   @B.text$mn        ^  (n 唎         P`.debug$S        D  謔 {     V   @B.text$x         (   v~ 瀪         P`.text$mn        X  瞺 
�         P`.debug$S        x	  Z� 覊     J   @B.text$x         (   秾 迣         P`.text$mn        �  驅 窂         P`.debug$S        �  閺 睒     d   @B.text$mn           櫆 笢         P`.debug$S        P  聹 �     
   @B.text$mn        :   v� 盀         P`.debug$S        L  簽 �        @B.text$mn        #   V�              P`.debug$S        �   y� m�        @B.text$mn        <    濉         P`.debug$S        0  � 3�     
   @B.text$mn        <   棧 樱         P`.debug$S        L  瘢 =�     
   @B.text$mn        !   ˉ 楼         P`.debug$S        <  芝 �        @B.text$mn        2   N� ��         P`.debug$S        <  敡 楔        @B.text$mn        +   H� s�         P`.debug$S        �  嚛 o�        @B.text$mn           揩 耀         P`.debug$S        �   娅 尸        @B.text$mn           颥 �         P`.debug$S        �   �         @B.text$mn        �  5�              P`.debug$S        �  桨 叾        @B.text$mn        B   狈 蠓         P`.debug$S           � �        @B.text$mn        B   M� 徆         P`.debug$S           胶        @B.text$mn        B    ;�         P`.debug$S        �   Y� U�        @B.text$mn        H   懠              P`.debug$S        �  偌 澗        @B.text$mn        ?   悼              P`.debug$S        ,  艨  �     
   @B.text$mn        +   劻              P`.debug$S           寺        @B.text$mn        �  � 羝         P`.debug$S        �  斍 屜     4   @B.text$mn        ]   斞 裱         P`.debug$S        �   撚        @B.text$mn        (   阌              P`.debug$S          � '�        @B.text$mn        C   c�              P`.debug$S        �  φ f�        @B.text$mn          蹲 举         P`.debug$S        �  � 骡     .   @B.text$mn        �  庝 ?�         P`.debug$S        �   丙     t   @B.text$x            9  E          P`.text$mn        	   O               P`.debug$S        `  X  �     
   @B.text$mn        �    �         P`.debug$S        �  � �     "   @B.text$mn            �          P`.debug$S        �   , �        @B.text$mn        �   ,	 �	         P`.debug$S        �  
 
        @B.text$mn           9 J         P`.debug$S          ^ j        @B.text$mn           � �         P`.debug$S        �   � �        @B.text$mn        B    E         P`.debug$S        �  Y 
        @B.text$mn        B    S         P`.debug$S        �  g         @B.text$mn        �    �         P`.debug$S          � �        @B.text$mn        �   � �         P`.debug$S        �  � J"        @B.text$mn           �" #         P`.debug$S        �   # �#        @B.xdata             +$             @0@.pdata             ?$ K$        @0@.xdata             i$             @0@.pdata             q$ }$        @0@.xdata             �$             @0@.pdata             �$ �$        @0@.xdata             �$             @0@.pdata             �$ �$        @0@.xdata             %             @0@.pdata             % %        @0@.xdata             9%             @0@.pdata             A% M%        @0@.xdata             k%             @0@.pdata             w% �%        @0@.xdata             �%             @0@.pdata             �% �%        @0@.xdata          0   �%             @0@.pdata             & &        @0@.xdata             -&             @0@.pdata             =& I&        @0@.xdata             g& {&        @0@.pdata             �& �&        @0@.xdata             �& �&        @0@.pdata             �& �&        @0@.xdata             '             @0@.pdata             #' /'        @0@.xdata             M'             @0@.pdata             U' a'        @0@.xdata             '             @0@.pdata             �' �'        @0@.xdata             �' �'        @0@.pdata             �' �'        @0@.xdata             ( !(        @0@.pdata             ?( K(        @0@.xdata             i(             @0@.pdata             }( �(        @0@.xdata             �(             @0@.pdata             �( �(        @0@.xdata             �(             @0@.pdata             �( �(        @0@.xdata             )             @0@.pdata             ) #)        @0@.xdata             A) U)        @0@.pdata             s) )        @0@.xdata             �) �)        @0@.pdata             �) �)        @0@.xdata             �)             @0@.pdata             �) 	*        @0@.xdata             '*             @0@.pdata             7* C*        @0@.xdata             a* u*        @0@.pdata             �* �*        @0@.xdata             �* �*        @0@.pdata             �* +        @0@.xdata             !+ 1+        @0@.pdata             O+ [+        @0@.xdata             y+ �+        @0@.pdata             �+ �+        @0@.xdata              �+ �+        @0@.pdata             �+ ,        @0@.xdata             %,             @0@.pdata             -, 9,        @0@.xdata          ,   W, �,        @0@.pdata             �, �,        @0@.xdata          	   �, �,        @@.xdata             �, �,        @@.xdata             �,             @@.xdata             �,             @0@.pdata             - -        @0@.xdata             :- R-        @0@.pdata             f- r-        @0@.xdata          
   �- �-        @@.xdata             �-             @@.xdata             �- �-        @@.xdata             �- �-        @@.xdata          	   �-             @@.xdata             �-             @0@.pdata             �- �-        @0@.voltbl            .               .xdata             . 5.        @0@.pdata             I. U.        @0@.xdata          
   s. �.        @@.xdata             �.             @@.xdata             �. �.        @@.xdata             �. �.        @@.xdata             �.             @@.xdata             �.             @0@.pdata             �. �.        @0@.voltbl            �.               .xdata             �. /        @0@.pdata             // ;/        @0@.xdata          
   Y/ f/        @@.xdata             �/             @@.xdata             �/ �/        @@.xdata             �/ �/        @@.xdata             �/             @@.xdata             �/             @0@.pdata             �/ �/        @0@.voltbl            �/               .xdata             �/             @0@.pdata             �/ 	0        @0@.xdata             '0             @0@.pdata             /0 ;0        @0@.xdata             Y0             @0@.pdata             a0 m0        @0@.xdata             �0             @0@.pdata             �0 �0        @0@.xdata             �0 �0        @0@.pdata             �0 1        @0@.xdata             )1 =1        @0@.pdata             [1 g1        @0@.xdata             �1 �1        @0@.pdata             �1 �1        @0@.xdata             �1             @0@.pdata             �1 �1        @0@.xdata             2             @0@.pdata             2 +2        @0@.xdata             I2             @0@.pdata             ]2 i2        @0@.rdata             �2 �2        @@@.rdata             �2             @@@.rdata             �2 �2        @@@.rdata             3 3        @@@.rdata             ;3             @@@.xdata$x           P3 l3        @@@.xdata$x           �3 �3        @@@.data$r         /   �3 �3        @@�.xdata$x        $   �3 4        @@@.data$r         $   +4 O4        @@�.xdata$x        $   Y4 }4        @@@.data$r         $   �4 �4        @@�.xdata$x        $   �4 �4        @@@.data               �4             @ @�.data$r         2   5 I5        @@�.data$r         0   S5 �5        @@�.rdata             �5             @@@.rdata$r        $   �5 �5        @@@.rdata$r           �5 �5        @@@.rdata$r           �5 	6        @@@.rdata$r        $   6 76        @@@.rdata$r        $   K6 o6        @@@.rdata$r           �6 �6        @@@.rdata$r           �6 �6        @@@.rdata$r        $   �6 �6        @@@.rdata$r        $   7 /7        @@@.rdata$r           M7 a7        @@@.rdata$r           k7 �7        @@@.rdata$r        $   �7 �7        @@@.bss                               �0�.rdata             �7             @0@.rdata             �7             @P@.rdata             �7             @P@.debug$S        4   8 58        @B.debug$S        4   I8 }8        @B.debug$S        @   �8 �8        @B.chks64            �8              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /alternatename:__isa_available=__isa_available_default    �   -  j     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\DrawStrategy.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $math 	 $colors 	 $render  $Json 	 $stdext    �   鷷  R #   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align " ;    std::memory_order_relaxed " ;   std::memory_order_consume + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size : #   std::integral_constant<unsigned __int64,2>::value H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos i #   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard \ #   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment j #   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment _ #   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer q #   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout 5 �    std::filesystem::_File_time_clock::is_steady , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,1>::value � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard 8 �   std::atomic<unsigned long>::is_always_lock_free / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment q #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment h #   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment A #   std::allocator<bool>::_Minimum_asan_allocation_alignment I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment i #   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size $ %   std::_Locbase<int>::collate M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets " %   std::_Locbase<int>::ctype G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none l #   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy  %    LightType_None  %   LightType_Directional  %   LightType_Spot  %   LightType_Point c #   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment  �?   std::_Consume_header  �?   std::_Generate_header  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN 7 �   std::atomic<unsigned int>::is_always_lock_free 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified + �   donut::math::vector<double,3>::DIM  �   nvrhi::c_HeaderVersion � �   std::_Trivial_cat<donut::render::DrawItem const *,donut::render::DrawItem const *,donut::render::DrawItem const * &&,donut::render::DrawItem const * &>::_Bitcopy_constructible " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts � �   std::_Trivial_cat<donut::render::DrawItem const *,donut::render::DrawItem const *,donut::render::DrawItem const * &&,donut::render::DrawItem const * &>::_Bitcopy_assignable & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment + �   donut::math::vector<double,4>::DIM � �   std::_Trivial_cat<donut::render::DrawItem,donut::render::DrawItem,donut::render::DrawItem &&,donut::render::DrawItem &>::_Same_size_and_compatible � �   std::_Trivial_cat<donut::render::DrawItem,donut::render::DrawItem,donut::render::DrawItem &&,donut::render::DrawItem &>::_Bitcopy_constructible � �   std::_Trivial_cat<donut::render::DrawItem,donut::render::DrawItem,donut::render::DrawItem &&,donut::render::DrawItem &>::_Bitcopy_assignable T #   std::allocator<donut::render::DrawItem>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable O #   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment M #   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment - %    std::integral_constant<int,0>::value W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified % #   std::ctype<char>::table_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified O #   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible ) �   donut::math::vector<bool,3>::DIM � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable # �        nvrhi::AllSubresources Z %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size ) �   donut::math::vector<bool,4>::DIM M #   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R %   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size \ #   std::allocator<donut::render::DrawItem const *>::_Minimum_asan_allocation_alignment J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified Z #   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible   3        nvrhi::EntireBuffer � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos Z #   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment x #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment - �    std::chrono::system_clock::is_steady '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable c #   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den � #   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < r  ��枠 std::integral_constant<__int64,10000000>::value 1 r   std::integral_constant<__int64,1>::value � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment Z #   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment - �   std::chrono::steady_clock::is_steady & r   std::ratio<1,1000000000>::num * r  � 蕷;std::ratio<1,1000000000>::den A #   std::allocator<char>::_Minimum_asan_allocation_alignment   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den : #    std::integral_constant<unsigned __int64,0>::value � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable R #   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy - 9   std::_Invoker_pmf_refwrap::_Strategy B #   std::allocator<float>::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size - 9   std::_Invoker_pmf_pointer::_Strategy t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment * �   donut::math::vector<float,2>::DIM + �        nvrhi::rt::c_IdentityTransform   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix . %   donut::math::box<float,3>::numCorners ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask 0 �   std::numeric_limits<wchar_t>::is_modulo O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask � #   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 j #   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10   �   Y  � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent    �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �   �+ 3   \ std::filesystem::path::preferred_separator  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34 	 !  tm %  7  _s__RTTICompleteObjectLocator2 & 鱥  $_TypeDescriptor$_extraBytes_30 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � +~  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w -~  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 觹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > d 鈣  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c ~  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h ~  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 瞸  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y ~  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > c 寋  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 
~  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 諀  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ] ~  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ [|  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 鴠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 題  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 鋧  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 諁  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > 苶  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 緘  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � ?|  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W祡  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 畗  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 憐  std::_Default_allocator_traits<std::allocator<float> > ; 靭  std::hash<std::shared_ptr<donut::engine::Material> > � l|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ 殅  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � 憓  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ %}  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C }  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � }  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 鱸  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 鰔  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � 珄  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 飢  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 遼  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 瓅  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 讄  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ? 箌  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 z  std::allocator<donut::engine::SkinnedMeshJoint> M M|  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L 磡  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 瘄  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � |  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 爘  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T 巪  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > � x|  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � n|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U ]|  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 鐆  std::_Ptr_base<donut::engine::LoadedTexture> :  <  std::_Vector_val<std::_Simple_types<unsigned int> > D O|  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 踫  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � A|  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 =u  std::_Ptr_base<donut::engine::DescriptorHandle> � 2|  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~(|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e bt  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 蕑  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > "坸  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 噞  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > d祔  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > U .y  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w |  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � |  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y 鋥  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 貃  std::allocator<donut::math::vector<float,2> > M 蓒  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = 秡  std::allocator<donut::math::vector<unsigned short,4> > K 縶  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U  u  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > F祘  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ >s  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 杮  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 巤  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e 蘳  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N 墈  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > { {{  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l   std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > , {  std::allocator<nvrhi::BindingSetItem> K  {  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 鰖  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # �8  std::allocator<unsigned int> � 鑪  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 閟  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 魕  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � w  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � ╬  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > g 搑  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 蘻  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  u  std::allocator<float> � 緕  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1>   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 爖  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t 4 搝  std::allocator_traits<std::allocator<float> > [ wz  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > l 7k  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > w vq  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > ; �8  std::allocator_traits<std::allocator<unsigned int> > 6 {u  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Yz  std::hash<std::shared_ptr<donut::engine::MeshInfo> > WUz  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> � Dz  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H 蕆  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ mh  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> �  z  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 鎟  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X 	z  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> > . Ni  std::integer_sequence<unsigned __int64>  �  std::_Lockit � 訽  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > �   std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy * C2  std::hash<enum nvrhi::ResourceType> / 豜  std::shared_ptr<donut::engine::Material> - 払  std::reverse_iterator<wchar_t const *> 5 鋂  std::shared_ptr<donut::engine::SceneGraphNode> 9 肵  std::shared_ptr<donut::engine::animation::Sampler> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file  欿  std::_Value_init_tag � z  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 聎  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >  �(  std::_Big_uint128 / 沑  std::weak_ptr<donut::engine::SceneGraph> 騳  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) U6  std::_Narrow_char_traits<char,int> L 0y  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 觟  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 芢  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > >  f+  std::hash<int>  �5  std::_Num_int_base  錊  std::ctype<wchar_t> " �+  std::_System_error_category � y  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / a2  std::_Conditionally_enabled_hash<bool,1> 2 髕  std::shared_ptr<donut::engine::BufferGroup> 2 z�  std::_Ptr_base<donut::engine::MeshGeometry> � 莤  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::float_denorm_style 4  x  std::shared_ptr<donut::engine::LoadedTexture>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 8g  std::piecewise_construct_t u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 襴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . 'Z  std::_Ptr_base<donut::engine::MeshInfo> 6 2;  std::allocator_traits<std::allocator<wchar_t> > � 蕎  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  &  std::bad_cast B [  std::enable_shared_from_this<donut::engine::SceneGraphNode>  玁  std::equal_to<void> 4 s  std::allocator<donut::math::vector<float,4> > � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } 耟  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 恅  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 硍  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 亀  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o hh  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � je  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > " �5  std::numeric_limits<double>  C&  std::__non_rtti_object < 裋  std::_Ptr_base<donut::engine::DescriptorTableManager> ( 0  std::_Basic_container_proxy_ptr12 4 <w  std::allocator<donut::math::vector<float,3> > � g  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � *\  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � 鵞  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > 0;  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8> T -w  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �5  std::_Num_float_base � #w  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  *  std::logic_error 3   std::weak_ptr<donut::engine::SceneGraphNode> � Lg  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � 飃  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety P w  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 辷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id ?   std::allocator_traits<std::allocator<unsigned __int64> > : 揨  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 1e  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z   std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> _ 檝  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u hv  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl .   std::_Ptr_base<donut::engine::Material> * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error d *v  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 鴘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy . 硊  std::initializer_list<unsigned __int64> % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > O 簡  std::_Uninitialized_backout_al<std::allocator<donut::render::DrawItem> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> 7 攗  std::shared_ptr<donut::engine::SceneTypeFactory> � fu  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � ^u  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 纃  std::allocator<unsigned __int64>  h:  std::false_type  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ai  std::_Default_allocator_traits<std::allocator<unsigned __int64> >  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � bk  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 Vu  std::shared_ptr<donut::engine::DescriptorHandle> , �5  std::numeric_limits<unsigned __int64> � *u  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L "u  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H 4m  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16>  ;  std::string_view  w  std::wstring_view % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound � 奼  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > b u  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec �   std::_Compressed_pair<std::allocator<donut::render::DrawItem>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem> >,1> D 鰐  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >   �*  std::_Init_once_completer j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 j wX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � EX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > � 癵  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ m  std::_Atomic_integral<long,4> � lt  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ dt  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > > 俕  std::enable_shared_from_this<donut::engine::SceneGraph> K Vt  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > dn  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >  �  std::hash<long double> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy / =Z  std::shared_ptr<donut::engine::MeshInfo> U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> 5 S\  std::shared_ptr<donut::engine::SceneGraphLeaf> O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> , 甦  std::allocator<std::_Container_proxy> / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception & �.  std::_Zero_then_variadic_args_t d ps  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ t  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � t  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string 鱯  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 鮯  std::_Vector_val<std::_Simple_types<float> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A 雜  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + G  std::pair<enum __std_win_error,bool> W 儂  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem const *> > � 輘  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error \ 蝧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder ) ~  std::_Atomic_integral_facade<long> 8 琗  std::_Ptr_base<donut::engine::animation::Sampler> % }2  std::hash<enum nvrhi::BlendOp> c   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B 梥  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> [ rs  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > M ds  std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> > ) q2  std::hash<enum nvrhi::BlendFactor> U 妴  std::pair<donut::render::DrawItem const * *,donut::render::DrawItem const * *> 輌  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 骾  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code J W  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano  �  std::_Iterator_base0 | Hs  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � g  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U @s  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0錰  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �6  std::_Char_traits<char16_t,unsigned short> 6 3q  std::allocator<donut::render::DrawItem const *> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> F 2s  std::allocator_traits<std::allocator<donut::render::DrawItem> > ! �9  std::char_traits<char16_t> 裧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ $s  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - 糫  std::weak_ptr<donut::engine::Material>  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 Ri  std::integer_sequence<unsigned __int64,0> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char> N Qm  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X s  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  9  std::_Invoker_strategy  	G  std::nothrow_t � s  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T �r  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �5  std::_Default_allocate_traits � $e  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � 苧  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > 0 uo  std::_Ptr_base<donut::engine::IShadowMap> . 膔  std::allocator<donut::render::DrawItem> ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> ( 淲  std::array<nvrhi::BufferRange,11> ; ;  std::basic_string_view<char,std::char_traits<char> > c Gq  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � 祌  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 琙  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9羒  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 漴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 時  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock Y 噐  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  }r  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � Kr  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 5g  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D r  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base  r*  std::out_of_range # �5  std::numeric_limits<__int64> _ 鱭  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 苢  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b 坬  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > ~q  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  珸  std::ctype<char> @ bq  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 噈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P Xq  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? Nq  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  ;  std::memory_order Z Iq  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � ;q  std::_Compressed_pair<std::allocator<donut::render::DrawItem const *>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> >,1> � $q  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > q 9a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  縒  std::nullopt_t  罻  std::nullopt_t::_Tag  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K 竝  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > 3 媰  std::shared_ptr<donut::engine::MeshGeometry>  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 莋  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  [9  std::ratio<1,1>   �8  std::forward_iterator_tag  �*  std::runtime_error � 猵  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > W   std::_Uninitialized_backout_al<std::allocator<donut::render::DrawItem const *> >   	  std::bad_array_new_length T 沺  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> > j ip  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Reallocation_policy E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 'p  std::allocator<donut::engine::animation::Keyframe> K p  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool> � [e  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  5  std::u16string _ p  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 謔  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 韋  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *>  \  std::nested_exception  �  std::_Distance_unknown ) 榦  std::allocator<nvrhi::BufferRange> ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , %@  std::codecvt<char32_t,char,_Mbstatet> 1 趚  std::_Ptr_base<donut::engine::BufferGroup> 1 噊  std::shared_ptr<donut::engine::IShadowMap> C 蝑  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F e  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 bo  std::vector<float,std::allocator<float> > F 0o  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 j\  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc } 賒  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J ym  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  f_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 4_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy � 鬾  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , EO  std::default_delete<std::_Facet_base>  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> C 踤  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . �-  std::vector<bool,std::allocator<bool> > J 蘮  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 沶  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> i ]n  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >  �8  std::ratio<1,10000000> Sn  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d i  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag j 塵  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A {m  std::allocator_traits<std::allocator<unsigned __int64 *> >  �/  std::allocator<char> T jm  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> d 0m  std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> > z   std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Reallocation_policy G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> ) f  std::allocator<unsigned __int64 *>    std::nullptr_t =MY  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > Lh  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K)h  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & �8  std::random_access_iterator_tag ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  x  std::wstring ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � 鷊  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  -*  std::domain_error  �  std::u32string_view � Me  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 俓  std::shared_ptr<donut::engine::SceneGraph>  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 閗  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 穔  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z ]k  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; 鏥  std::weak_ptr<donut::engine::DescriptorTableManager> $ 72  std::hash<nvrhi::IResource *> 4 =\  std::_Ptr_base<donut::engine::SceneGraphLeaf> " 轜  std::_Nontrivial_dummy_type � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet>  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage � K`  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � `  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! �+  std::_System_error_message  �  std::_Unused_parameter = sk  std::allocator<std::shared_ptr<donut::engine::Light> > h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  諻  std::bad_optional_access A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > � dk  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  鴋  std::_Exact_args_t � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > N 厇  std::allocator_traits<std::allocator<donut::render::DrawItem const *> > Q _k  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � Qk  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base b Ck  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> c 9k  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> � f^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 4^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> �圷  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 |Z  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ' 訥  std::hash<std::filesystem::path> [ cz  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 蘗  std::_Ptr_base<donut::engine::SceneGraphNode> � 蒳  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > O 0s  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem> > � 襤  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> .   std::array<nvrhi::BindingLayoutItem,16> E Nz  std::_Vector_val<std::_Simple_types<donut::render::DrawItem> > $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> �|g  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E @]  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O #]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U !]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t & �i  $_TypeDescriptor$_extraBytes_40 # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling $ �$  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �!  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16>  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  t$  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # RW  nvrhi::DescriptorTableHandle  �$  nvrhi::TimerQueryHandle 2 RW  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # �$  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �$  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  ]!  nvrhi::IBindingLayout  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # +k  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline  �$  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �"  nvrhi::ComputeState 2 +k  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �!  nvrhi::IFramebuffer  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats * fZ  donut::engine::SkinnedMeshReference ! 籞  donut::engine::SceneCamera $ H  donut::engine::ICompositeView  ?H  donut::engine::IView $ X[  donut::engine::SceneGraphNode 0 ![  donut::engine::SceneGraphNode::DirtyFlags " Z  donut::engine::MeshInstance   fH  donut::engine::PlanarView ) ZZ  donut::engine::SkinnedMeshInstance   籢  donut::engine::SceneGraph > 颺  donut::engine::ResourceTracker<donut::engine::MeshInfo> ( h]  donut::engine::AnimationAttribute $ 筜  donut::engine::SceneGraphLeaf ! uW  donut::engine::BufferGroup  榙  donut::engine::Material *  k  donut::engine::Material::HairParams 0 黬  donut::engine::Material::SubsurfaceParams  語  donut::engine::Light ' ℡  donut::engine::SceneContentFlags  礧  donut::engine::MeshInfo & 鎆  donut::engine::DirectionalLight & \]  donut::engine::SceneGraphWalker ( X  donut::engine::animation::Sampler ) 鴍  donut::engine::animation::Keyframe ) 橷  donut::engine::animation::Sequence    donut::engine::MeshType  鯶  donut::engine::SpotLight & 竀  donut::engine::DescriptorHandle , &W  donut::engine::DescriptorTableManager B 鱒  donut::engine::DescriptorTableManager::BindingSetItemsEqual B 餠  donut::engine::DescriptorTableManager::BindingSetItemHasher % _W  donut::engine::VertexAttribute 0 圿  donut::engine::SceneGraphAnimationChannel " 鮦  donut::engine::MeshGeometry % t   donut::engine::DescriptorIndex > 誢  donut::engine::ResourceTracker<donut::engine::Material>   [  donut::engine::PointLight ) 鮙  donut::engine::SceneGraphAnimation $ 乨  donut::engine::MaterialDomain  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  /  donut::math::float2  }[  donut::math::dquat # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane  瞇  donut::math::daffine3  燵  donut::math::double3 # �  donut::math::vector<float,4> $ 燵  donut::math::vector<double,3>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes $ }j  donut::math::vector<double,4>  �  donut::math::float4 & 	e  donut::math::matrix<double,3,3> % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> $ 瞇  donut::math::affine<double,3> & }[  donut::math::quaternion<double> - 簝  donut::render::TransparentDrawStrategy - 宎  donut::render::PassthroughDrawStrategy 1   donut::render::InstancedOpaqueDrawStrategy # va  donut::render::IDrawStrategy  
j  donut::render::DrawItem M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24 & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers   �   �      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �    f扥�,攇(�
}2�祛浧&Y�6橵�  �    曀"�H枩U传嫘�"繹q�>窃�8  >   天e�1濎夑Y%� 褡\�Tā�%&閜�     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   �	玮媔=zY沚�c簐P`尚足,\�>:O     �"睱建Bi圀対隤v��cB�'窘�n  b   [届T藎秏1潴�藠?鄧j穊亘^a  �   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   揾配饬`vM|�%
犕�哝煹懿鏈椸  +   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  h   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  ,   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  j   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  5   dhl12� 蒑�3L� q酺試\垉R^{i�  t   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�      d蜯�:＠T邱�"猊`�?d�B�#G騋  Q   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  3   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  	   `k�"�1�^�`�d�.	*貎e挖芺
脑�  K   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   r>�柨�54hC臵3o橧89瀐Jhㄧ�)滆  �   L�9[皫zS�6;厝�楿绷]!��t  �   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  >	   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  	   k&�2箍�#た↗�U嬗醇芧'l�-G恇|:  �	   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  �	   �0�*е彗9釗獳+U叅[4椪 P"��  5
   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  o
   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �
   �=蔑藏鄌�
艼�(YWg懀猊	*)  �
   v-�+鑟臻U裦@驍�0屽锯
砝簠@  *   煋�	y鋵@$5х葑愔*濋>�( 懪銳  d   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  $   j轲P[塵5m榤g摏癭 鋍1O骺�*�  m   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  #
   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  `
   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �
   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �
   繃S,;fi@`騂廩k叉c.2狇x佚�     荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  j   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  4   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  q   �*o驑瓂a�(施眗9歐湬

�  �   Z�=g!9﹞�+庐隀鶋鶾��0L-�誩Q  �    I嘛襨签.濟;剕��7啧�)煇9触�.  7   �暊M茀嚆{�嬦0亊2�;i[C�/a\  k   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   猯�諽!~�:gn菾�]騈购����'  )   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  s   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�     ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  N   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   妇舠幸佦郒]泙茸餈u)	�位剎  '   靋!揕�H|}��婡欏B箜围紑^@�銵  g   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   t�j噾捴忊��
敟秊�
渷lH�#  /   �(M↙溋�
q�2,緀!蝺屦碄F觡  {   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  J   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲     5�\營	6}朖晧�-w氌rJ籠騳榈  K   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   o藾錚\F鄦泭|嚎醖b&惰�_槮     �
bH<j峪w�/&d[荨?躹耯=�  J   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  &   齝D屜u�偫[篔聤>橷�6酀嘧0稈  d   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  =   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�     觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  Q   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5  ;   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  }    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  ,   樁*披B憱祯敛鍭�7� T癀n烬
雚臁      狾闘�	C縟�&9N�┲蘻c蟝2  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   �'稌� 变邯D)\欅)	@'1:A:熾/�  E   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠     交�,�;+愱`�3p炛秓ee td�	^,  Z   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   zY{���睃R焤�0聃
扨-瘜}  �   _臒~I��歌�0蘏嘺QU5<蝪祰S     郖�Χ葦'S詍7,U若眤�M进`  i   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  /    副謐�斦=犻媨铩0
龉�3曃譹5D   q    寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �    J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �    鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  =!   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  z!   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �!   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  "   =J�(o�'k螓4o奇缃�
黓睆=呄k_  K"   _O縋[HU-銌�鼪根�鲋薺篮�j��  �"   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �"   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  ;#   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �#   蜅�萷l�/费�	廵崹
T,W�&連芿  �#   v�%啧4壽/�.A腔$矜!洎\,Jr敎  $   D���0�郋鬔G5啚髡J竆)俻w��  ]$   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �$   匐衏�$=�"�3�a旬SY�
乢�骣�  �$   v峞M� {�:稚�闙蛂龣 �]<��  ,%   悯R痱v 瓩愿碀"禰J5�>xF痧  y%   チ畴�
�&u?�#寷K�資 +限^塌>�j  �%   矨�陘�2{WV�y紥*f�u龘��  �%   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  4&   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  l&   穫農�.伆l'h��37x,��
fO��  �&   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  �&   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  %'   鏀q�N�&}
;霂�#�0ncP抝  ^'   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �'   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �'   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   �      f  H  B   g  H  H   h  H  Y   m  H  �   �    U   �    �   �    q  �  �  g   h  �    i  �  �   j  �  �   m  �  �   �    �   �    @   �    �   b  �  �   �    �   T    @   ,    @   w       y     �  �   �  %   �      �  �      e  �   (   F   !     2  @!     �  A!       B!    �  C!    �  O!     0  d!  �  �   e!  �  +  m!  �  �  n!    o  o!    �  p!    �  w!  �  �  x!  �  �  �!    �  �!     .  �!    �  �!  �  '  �!    c  �!  �  �   &"    �  @%      I%    [  J%    a  K%    r  L%    z  [%  �  b  \%  �  S  ]%  �  N  _%  �  �  a%  �  b  b%  �  S  c%  �  N  e%  �  �  g%  �  f  h%     �  i%  P  1  j%  �  S  m%  P  �  n%  P  $  �%       �%    �  �%  �  
  �%  �  
  �%  �    �%    �  �%    �  �%  �    �%  �  �  �%  �  �  �%    �  �%  �  �  �%    �  �%  �  ]  �%  �  ]  �%    �  �%    �  �%    �  �%    �  �%    �  �%    �  �%    �  �%    �  �%  �  �   �%    �  �%    �  �%    �  �%  �  3  �%    ,  �%    T  �%      �%  �  �   �%  �  �   �%  �  �  �%    �  �%  �  �  �%    �  &  �  �  &    �  &    I  &    �  �   H(   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\src\render\DrawStrategy.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\include\donut\render\DrawStrategy.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\include\donut\render\GeometryPasses.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h �       L&  jM  �   nM  �  
 4T  �   8T  �  
 Cv      Gv     
 mw      qw     
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    �H嬃�X��J�XI�I�B�XA�A�   �   �   D G            .       -   d!        �donut::math::operator+=<float> 
 >o   a  AJ        . 
 ><   b  AK        .                         H     o  Oa     <  Ob  O�               .   �            �  �,   �    0   �   
 f   �    j   �   
 �   �    �   �   
 �   �    �   �   
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  @   w  @  
 �     �    
 H+袸嬂H+翷嬄H嬔H嬋�             �   �  � G                      &        �std::_Copy_backward_memmove<donut::render::DrawItem const * *,donut::render::DrawItem const * *>  >絣   _First  AJ          >絣   _Last  AK          >絣   _Dest  AH         AP          >#    _Count  AK                                H 
 h   �%      絣  O_First     絣  O_Last     絣  O_Dest  O   �   0              �     $       � �    � �   � �,      0     
 �      �     
 �      �     
 �      �     
 �          
      "    
 �     �    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   |  } G            0   
   %   �%        �std::_Copy_memmove<donut::render::DrawItem const * *,donut::render::DrawItem const * *>  >絣   _First  AJ          >絣   _Last  AK          >絣   _Dest  AM         AP          >#    _Count  AI  
                             H 
 h   �%   0   絣  O_First  8   絣  O_Last  @   絣  O_Dest  O�   @           0   �     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 �     �    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   l  m G            0   
   %   �%        �std::_Copy_memmove<donut::render::DrawItem *,donut::render::DrawItem *>  >*p   _First  AJ          >*p   _Last  AK          >*p   _Dest  AM         AP          >#    _Count  AI  
                             H 
 h   �%   0   *p  O_First  8   *p  O_Last  @   *p  O_Dest  O�   @           0   �     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
      	    
 �     �    
 H塋$SVWATAUAVAWH冹 M嬭L孃L嬹L�L嬍M+菼韩*I嬄I鏖H嬺H窿H嬈H凌?H餒婭I+菼嬄H鏖H龙H嬄H凌?H蠭筓UUUUUUI;��4  L峛I婲I+菼嬄H鏖H龙H嬄H凌?H蠬嬍H验I嬃H+罤;�圍   H�I孅I;腍C鳬;�囥   H�H玲H墊$hH侚   r,H岮'H;�喞   H嬋�    H吚劵   H峏'H冦郒塁 H吷t�    H嬝H塂$xH墊$h�3跦墊$hH塡$xH�4vH伶H驛E AMNAE F M婩I�H嬎M;鴘L+码M嬊L+妈    H峃0M婩M+荌嬜�    怢嬒M嬆H嬘I嬑�    H嬈H兡 A_A^A]A\_^[描    惕    惕    嚏   �      �    g     z     �  �    �  �    �  �    �  �       �   >  � G            �     �  �%        �std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Emplace_reallocate<donut::render::DrawItem const &> 
 >6p   this  AJ          AV       �~  D`    >妏   _Whereptr  AK          AW       �
  >,p   <_Val_0>  AP          AU       ��  Dp    >#     _Newcapacity  AM  �     �   AM �      Bh   �     � �   >#    _Newsize  AT  y     <& 0  >#    _Whereoff  AL  9       >妏    _Newvec  AI          AI "    �  Bx       �    M        �%  u���� M        �%  u����) M        �  ��)
,%��- M        �  ��$	()
�� Z   }  q   >#    _Block_size  AH  �       AH �      >#    _Ptr_container  AH  �     �  �  AH "    \ I  
 >`    _Ptr  AI  �       AI "    �  M        �  ��
 Z   �   N N M        �  
�
 Z   �   N N M        T  
��

 N N N M        �%  y >#    _Geometric  AH  �     u - ( _   AH "    � I  \ %  M        �%  y N N M        �%  �2 N M        �%  乕 M        �%  乕 >#    _Count  AP  P      AP y      N N M        �%  乻 >妏   _Last  AP  s      >*p   _Dest  AJ  o    
  AJ y      M        �%  乻 >#    _Count  AP  v      AP y      N N M        �%  乣 M        �%  乣 >C    _First_ch  AK  S      AK y      >#    _Count  AP  c      N N Z   �%  �%               8         0@ j h   B  �  �  �  T  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  
&  &         $LN97  `   6p  Othis  h   妏  O_Whereptr  p   ,p  O<_Val_0>  O  �   �           �  �     �       * �   3 �G   4 �b   6 �u   : �y   ; ��   = �"  A �'  B �L  E �[  G �^  K �`  L �k  N �  V ��  W ��  X ��  = ��  7 ��  = ��   +  � F            (   
   (             �`std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Emplace_reallocate<donut::render::DrawItem const &>'::`1'::catch$0 
 >6p   this  EN  `         ( 
 Z   �%                        � �        __catch$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z$0        $LN97  `   6p  Nthis  h   妏  N_Whereptr  p   ,p  N<_Val_0>  O �   0           (   �     $       P �
   R �   S �,       0      
 �       �      
 �       �      
            
       "     
 E      I     
 U      Y     
 �      �     
 �      �     
 �      �     
 �      �     
 �           
 !      %     
 5      9     
 E      I     
            
 #      '     
 L      P     
 `      d     
 �      �     
 �      �     
 O      S     
 g      k     
 �      �     
            
 A      E     
 a      e     
 q      u     
 �      �     
 �      �     
            
 (      ,     
 I      M     
 �  ;   �  ;  
 T      X     
 4  	   8  	  
 �  	   �  	  
  	  <   $	  <  
 �	  ;   �	  ;  
 H
  	   L
  	  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #   
   H塡$H塴$H塼$WH冹 H嬟H孂H�I嬮I嬸H��諈纓H�L�H�L�H�H��諈纓!H�H�H�H�H��諈纓H�H�H�H�H媆$0H媗$8H媡$@H兡 _�   �   �  � G            z      e   &        �std::_Med3_unchecked<donut::render::DrawItem const * *,int (__cdecl*)(donut::render::DrawItem const *,donut::render::DrawItem const *)>  >絣   _First  AJ          AM       _ 
 >絣   _Mid  AI       S  AK          >絣   _Last  AL  #     Q  AP        #  >潈   _Pred  AN        O  AQ           M        �%  ,	
 >ia    _Tmp  AR  2       N M        �%  D	
 >ia    _Tmp  AJ  J       N M        �%  Y	
 >ia    _Tmp  AJ  _       N                       @  h   �%  �%   0   絣  O_First  8   絣  O_Mid  @   絣  O_Last  H   潈  O_Pred  9&       潈   9>       潈   9S       潈   O   �   X           z        L        �   	 �,   
 �8   
 �D    �P    �Y    �e    �,      0     
 �      �     
 �      �     
          
          
 3     7    
 C     G    
 c     g    
 s     w    
 �     �    
 �     �    
 #     '    
 �     �    
 �     �    
 �     �    
 �     �    
 H塡$H塋$UVWATAUAVAWH冹 I嬂I嬮H+翸嬭H柳L嬧H嬍H�翴岪鳫+翲柳H凐(~dH�繦柳H嬸H伶H�<�    H�L�H塂$hH嬓�    H嬎L�H+螸嬐H嬘�    M峿鳯嬐L+�I峂鳬嬜M岴鳫+舞    H婰$h�M峹鳯嬐M嬊H嬘�    H峴L;鉺%H婯鳫峽鳫��諈纔H�H��諈纔H嬤L;鐁跧;鮯$ H�H��諈纔H�H��諈纔	H兤I;鮮逪孇L孄I;齭:@ H�H��諈纔!H�H��諈纔H;鱰H�H�H�H�H兤H兦I;齬蔒;黺BM峸鴲H�I��諈纔!I�H��諈纔"H冸I;辴I�H�H�I�I冿I冾M;鐁芃;黸3I;齮wH;鱰H�H�H�H�H�H兤H�H�H兠H�H兦镋���I兦鳬;齯+H冸L;鹴H�I�I�H�H婩鳫冾H�H�H�����H�I�H�H兦I�辄��H婦$`H�H媆$pH塸H兡 A_A^A]A\_^]胟      �      �      �         �     � G            <     #  �%        �std::_Partition_by_median_guess_unchecked<donut::render::DrawItem const * *,int (__cdecl*)(donut::render::DrawItem const *,donut::render::DrawItem const *)>  >絣   _First  AK        ,  AT  ,      >絣   _Last  AP        %  AU  %      >潈   _Pred  AN        AQ          >絣    _Plast  AL  �     |
 >絣    _Mid  AI  3     �  AI �     e   �   '  >絣    _Pfirst  AI  �     # � � 9 � 
  AK  �       AP  �       AQ  �       AI �     e   �   '  >絣    _Glast  AW  �    	  AW     z 	 � E  >絣    _Gfirst  AM      %, M        �%  ,7+&G$ Z   &  &  &   >r    _Count  AH  7     u    a  AH �       >r    _Step  AH  K       N M        �%  �=	
 >ia    _Tmp  AJ  C    
 * AJ       ' F 7 �  �  �  �  �   N M        �%  亖	
 >ia    _Tmp  AJ  �     & AJ `    �  ' O  _  �  �  �   N M        �%  �
 N M        �%  仼	
 >ia    _Tmp  AJ  �      AJ �    
  N  M        �%  伒7
 >ia    _Tmp  AJ  �     * AJ     % 0 O 7 �  �  �  �  �   N M        �%  佷	
 >ia    _Tmp  AJ  �      AJ �      N M        �%  侌8#
 >ia    _Tmp  AJ  �     * AJ     % 0 O 7 �  �  �  �  �   N M        �%  �

 >ia    _Tmp  AJ  	     * AJ     % 0 O 7 �  �  �  �  �   N
 Z   &               8          @ " h   �%  �%  �%  �%  �%  �%  &   h   絣  O_First  p   絣  O_Last  x   潈  O_Pred  9�       潈   9�       潈   9�       潈   9      潈   9&      潈   92      潈   9f      潈   9r      潈   O�   �          <    /   �      ( �   * �,   + �/   * �3   + ��   , ��   - ��   0 ��   1 ��   4 �  5 �  8 �  9 �  < �   = �,  ? �8  A �=  B �I  < �V  I �`  K �l  M �x  O ��  P ��  I ��  T ��  Y ��  Z ��  ^ ��  ] ��  ^ ��  _ ��  ^ ��  ` ��  a ��  b ��  c ��  f �  g �  h �  i �  h �  k �  U �#  l �(  U �,  l �,      0     
 �      �     
 �      �     
          
 )     -    
 I     M    
 Y     ]    
 z     ~    
 �     �    
 �     �    
 �     �    
 �     �    
          
          
 '     +    
 X     \    
 h     l    
 �     �    
 �     �    
 
         
 -     1    
 j     n    
 z     ~    
 �     �    
 �     �    
 S     W    
 c     g    
 �     �    
 �     �    
          
 !     %    
 c     g    
 s     w    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
       $    
 H塡$VWATAVAWH冹 L媎$pH嬹H塴$PH嬍H嬟L塴$XL孃M嬹I峆�M嬭H嬯H妖H;蛚1H�<	H婽�H婰�A�憎豀蒆兞H螲�蜨�轍嬞H;蛗覫峌�H;虷媗$PuA雠uJ婦铠H�轍嬟L媗$XL;鹽F�     I�H峽�H�H�嗀�詤纓)H�﨟�轍嬤L;�|跧�H�﨟媆$`H兡 A_A^A\_^肐�H�轍媆$`H兡 A_A^A\_^�   �   �  � G            �      �   �%        �std::_Pop_heap_hole_by_index<donut::render::DrawItem const * *,donut::render::DrawItem const *,int (__cdecl*)(donut::render::DrawItem const *,donut::render::DrawItem const *)>  >絣   _First  AJ          AL       � �   >    _Hole  AI  i     	  AK        3  AI A     � ( 	    >    _Bottom  AP        6  AU  6     ] 
 >鏻   _Val  AQ        /  AV  /     � �   >潈   _Pred  AT       � �   EO  (           Dp    >r    _Max_sequence_non_leaf  AN  9     A 
 >r    _Top  AW  ,     � �  
 >     _Idx  AJ  !        AJ A     �  # m 0 ! M        &  ��
( >    _Hole  AI  $     �    AI A     � ( 	   
 >     _Idx  AM  �     2  AM �     U  7  N             (          H  h   �%  &  &   P   絣  O_First  X      O_Hole  `      O_Bottom  h   鏻  O_Val  p   潈  O_Pred     _Diff  9O       潈   9�       潈   O�   p           �        d       , �   7 �A   8 �E   9 �R   < �f   = �r   @ ��   A ��   B ��   E ��   F �,      0     
 �           
          
 0     4    
 @     D    
 P     T    
 z     ~    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 .     2    
 M     Q    
 p     t    
 �     �    
 �     �    
 �     �    
      
    
          
 �     �    
 �     �    
 �     �    
 H塋$SVWAVAWH冹 L嬺H孂I�������I;��)  L媦L+9I�H婭H+H六H嬔H殃I嬂H+翲;�國   H�
I;苨
I嬈H塂$h�H塂$hI;�囎   H�4�    H侢   r)H峃'H;�喒   �    H吚劮   H峏'H冦郒塁H咑t
H嬑�    H嬝�3跦塡$XJ�鸐嬈M+荖��    3诣    L婫H�L+翲嬎�    怘�H吷t1H媁H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐w3I嬋�    H�J�驢塆H�H塆H兡 A_A^_^[描    惕    惕    虜   �    �   �    �      �      )  �    M  �    S  �    Y  �       �   4  � G            ^     ^  �%        �std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Resize_reallocate<std::_Value_init_tag> 
 >薼   this  AJ          AM       H3  DP    >#   _Newsize  AK          AV       K5 
 >桲   _Val  AP           D`    >#     _Newcapacity  AH  `       AH u     � #  G  l k  Bh   e     �   �   >#    _Oldsize  AW  -     1   %  >m    _Appended_first  AJ  �       >m    _Newvec  AI  �       AI �     � � 
  BX   �     � �   M        �%  Wl�� M        �%  Wl��% M        �  })
)%
��( M        �  ��$	%)
��
 Z   }   >#    _Block_size  AJ  �       AJ L      >#    _Ptr_container  AH  �       AH �     �  w 
 >`    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        �  
l
	 N N N! M        �%  4kD%
 >#    _Oldcapacity  AJ  8     �   R %  
  AJ �     � 	 �  >#    _Geometric  AH  X         AH u     � #  G  l k  M        �%  4 N N M        �%  �� >#    _Count  AP  �       M        �%  �� N N M        �%  �� >m   _First  AK  �       >m   _Last  AP  �       M        �%  ��c >#    _Count  AP  �       N N% M        �%  ��h1#  M        �%  *�U M        �  �)0
 Z     
 >   _Ptr  AJ (      >#    _Bytes  AK       -    AK X     % M        �  �d#
3
 Z   q   >#    _Ptr_container  AP        AP (    5  +  >#    _Back_shift  AJ  �     1  AJ (    5 $   N N N N
 Z   �%               (         0@ � h!   B  �  �  �  �  �  �  �  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  &  	&         $LN97  P   薼  Othis  X   #  O_Newsize  `   桲  O_Val  O�   �           ^  �  
   t       � �   � �)   � �4   � �l   � ��   � ��   � ��   � ��   	 �@  
 �L  � �R  � �X  	 ��   ;  � F            (   
   (             �`std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >薼   this  EN  P         ( 
 >桲   _Val  EN  `         ( 
 Z   �%                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN97  P   薼  Nthis  X   #  N_Newsize  `   桲  N_Val  O �   0           (   �     $        �
    �    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 D  �    H  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
 "  �    &  �   
 2  �    6  �   
 F  �    J  �   
 �  �      �   
   �      �   
 8  �    <  �   
 H  �    L  �   
 k  �    o  �   
 {  �      �   
 B  �    F  �   
 ^  �    b  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 P  �    T  �   
 p  �    t  �   
 �  �    �  �   
 A  �    E  �   
 b  �    f  �   
 v  �    z  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �       �   
 �  5   �  5  
 H  �    L  �   
 �  
   �  
  
 �	  
   �	  
  
 �	  
   �	  
  
 
  6   
  6  
 �
  5   �
  5  
   
     
  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   �    #   
   H塋$SVWAVAWH冹 H嬺L嬹I篣UUUUUUI;��#  H婭I+I公*I嬃H鏖L孃I�I嬒H灵?L鵌婲I+I嬃H鏖H龙H嬄H凌?H蠬嬍H验I嬄H+罤;�嚺   H�<H;
H孇H塼$h�H墊$hI;�嚖   H�H玲H侚   r,H岮'H;�唵   H嬋�    H吚剚   H峏'H冦郒塁H吷t
�    H嬝�3跦塡$XK�H玲H薍嬈I+莟L�@I拎3诣    M婩I�L+翲嬎�    怢嬒L嬈H嬘I嬑H兡 A_A^_^[�    �    惕    惕    棠   �    �   �         %     B  �    G  �    M  �    S  �       �   j  � G            X     X  �%        �std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Resize_reallocate<std::_Value_init_tag> 
 >6p   this  AJ          AV       B(  DP    >#   _Newsize  AK          AL       E- 
 >桲   _Val  AP        Q�  �  � V K  AP �     +    D`    >#     _Newcapacity  AM  �       AM �     � �  �   Bh   �     �   �   >#    _Oldsize  AW  C       >妏    _Appended_first  AJ         AJ     
  >妏    _Newvec  AI  �       AI �     Q  BX   �     Q  M        �%  W���� M        �%  W����( M        �  ��)
,%
X, M        �  ��$	()
m Z   }  q   >#    _Block_size  AH  �       AH F      >#    _Ptr_container  AH  �     �  s  AH �      
 >`    _Ptr  AI  �       AI �     Q  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        T  
��
	 N N N# M        �%  QD%
 >#    _Geometric  AM  �         AM �     � �  �   M        �%  Q N N M        �%  � >#    _Count  AH        AH       N M        �%  � >妏   _First  AK        >妏   _Last  AP        M        �%  �c >#    _Count  AP  !      N N Z   �%  �%               (         0@ n h   B  �  �  �  T  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  
&  &         $LN80  P   6p  Othis  X   #  O_Newsize  `   桲  O_Val  O  �   �           X  �     �       � �   � �)   � �Q   � ��   � ��   � �   � �  � �  � �*  	 �6  
 �A  	 �F  � �L  � �R  � ��   %  � F            (   
   (             �`std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >6p   this  EN  P         ( 
 >桲   _Val  EN  `         ( 
 Z   �%                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN80  P   6p  Nthis  X   #  N_Newsize  `   桲  N_Val  O   �   0           (   �     $        �
    �    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �      �   
 4  �    8  �   
 T  �    X  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 6  �    :  �   
 F  �    J  �   
 V  �    Z  �   
   �    "  �   
 .  �    2  �   
 W  �    [  �   
 k  �    o  �   
 �  �    �  �   
 �  �    �  �   
 ^  �    b  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 9  �    =  �   
 v  �    z  �   
 (  8   ,  8  
 �  �    �  �   
 8     <    
 �     �    
      	    
 ;  9   ?  9  
 �  8   �  8  
 H	     L	    
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   �    #   
   H塡$H塗$UVWATAUAVAWH冹@H嬄M嬹H+罥孁H冟鳯嬯H嬞H=   帗   D  H�幾   M嬑H峀$0M嬇H嬘�    H媗$0H嬒H媡$8H嬇H六H+肏�H冟鳫鵐嬑I嬐L嬊H+蜨冡鳫;羮H嬚H嬎�    H嬣�I嬚H嬑�    L嬳H壃$�   I嬇H+肏冟鳫=   弐���I;�勚  L峜M孅M;�勂  f�     I�7I嬶H�H嬑A�謪�刜  M嬊H嬘L+肐嬏�    H�3閫  I嬇H+肏柳H嬭H墑$�   H妖H呿幣   H峆�L嬭L孃I�f怢媎滕H�虷嬽H嬐I;飣>@ f�     H�<	H婽�H婰�A�主豀蒆兞H螲�薍�驢嬹I;蟶覫峌�I;鱱A雠uJ婦滕H�驢嬺H;題( H崀�I嬙H�H�鸄�謪纓H�鸋�驢嬿H;飢跮�$驣峌�H呿廧���L嫭$�   H媱$�   H凐対   �   I峿鳫+�@ H�L崒$�   H墑$�   L嬊H�L+肐柳3襀嬎H�L塼$ �    H冿H�>H冟鳫凐}茧GI媁鳬�鳫嬑A�謪纓$�     H�H嬑H塃 H嬶H媁鳫冿A�謪纔銱塽 I兦M;�匔��H嫓$�   H兡@A_A^A]A\_^]肵      �   �    �   �         N        �     � G            �     �  �%        �std::_Sort_unchecked<donut::render::DrawItem const * *,int (__cdecl*)(donut::render::DrawItem const *,donut::render::DrawItem const *)>  >絣   _First  AI  /     � AJ        /  >絣   _Last  AK        
  AU  ,     � p � F AU @     p  �  B�   
     �)3  >    _Ideal  AM  %     +Q  � 0  AP        % " AM �     �0 0 � - � ! )M ~2  >潈   _Pred  AQ          AV       �
 >妴   _Mid  CN      a     m  CL     i     e  CN     @     �! m � * � �" CL    @     �) e � - � ��  D0   > M        �%  ��	F並

 >絣    _Mid  AW  �     �B F AW �      >絣    _Hole  AN  �     �* F AN �     � �
 >ia    _Val  AL  �     �- F AL �     � � >絣    _Prev  AM  �      AM �     �0 F~ �  M        �%  � M        &  � >#    _Count  AP        N N N M        �%  
�2 M        �%  � #8
 >ia    _Val  B�        � F G  M        &  �#
 Z   �%   N N N. M        �%  � 
&-1	
) >     _Bottom  AH  #    �   % �  AU  H    �  AH      �  �  B�   5    �1G  >     _Hole  AN  -    9 AN �     
 >ia    _Val  AT  U    �  AT P    q � G 3 M        �%  乕
$ml+)) >    _Hole  AL  [    e  AL P    s e �  � �  >r    _Max_sequence_non_leaf  AW  K    �  AW     � ` G 
 >     _Idx  AJ  ^      AJ P    u  . # ~  � h # M        &  伕8$( >    _Hole  AL  �      AL P    s e �  � � 
 >     _Idx  AM  �      AM P    r$ - t ! � �  N N N Z   �%  �%  �%   @           8          @ 6 h   �%  �%  �%  �%  �%  �%  �%  �%  &  &  &  &   �   絣  O_First  �   絣  O_Last  �      O_Ideal  �   潈  O_Pred  0   妴  O_Mid  9�       潈   9~      潈   9�      潈   9q      潈   9�      潈   O   �   �           �       �       o �   r �@   w �I   ~ �\   � ��   � ��   � ��   � ��   � ��   � ��   r ��   s �   x �  y �f  s ��  � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �      �   
 ,  �    0  �   
 D  �    H  �   
 i  �    m  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
    �    $  �   
 @  �    D  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 "  �    &  �   
 6  �    :  �   
 Z  �    ^  �   
 j  �    n  �   
 �  �    �  �   
 /  �    3  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 &  �    *  �   
 E  �    I  �   
 U  �    Y  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 2  �    6  �   
 B  �    F  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    	  �   
   �      �   
 0  �    4  �   
 @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�         �     Y G                     �%        �std::_Zero_range<donut::render::DrawItem const * *>  >m   _First  AJ          >m   _Last  AI         AK                                H 
 h   �%   0   m  O_First  8   m  O_Last  O �   8                   ,       � �   � �   � �   � �,      0     
 �      �     
 �      �     
 �      �     
          
 �A��I�Y殷Y荔Y审X�W荔X�.聎W荔Q旅(麻    6         �     B G            :       5   e!        �donut::math::length<float,3> 
 ><   a  AJ        :  M        w   %
 >@    _Xx  A�   %       N M        �!   ! M        h   ! N N                        H  h   h  w   �!      <  Oa  O  �               :   �            + �,   �    0   �   
 d   �    h   �   
 �   �    �   �   
 ,  �    0  �   
 �I��Q�Y荔Y审Y殷X馏X旅   �   �   I G            #       "   �!        �donut::math::lengthSquared<float,3> 
 ><   a  AJ        #  M        h  "  N                        H 
 h   h      <  Oa  O  �               #   �            ' �,   �    0   �   
 k   �    o   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !   H     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �       �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2   H     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 @SH冹 H峐H嬎�    H��   H�    H兡 [�       �    '   �       �   �  X G            +      !   S%        �donut::engine::SceneGraphWalker::~SceneGraphWalker 
 >B]   this  AJ        
  M        �   *
 Z   !  
 >踈   this  AI  
       M        n!   M        �!   M        &"   M        �   N N N N M        m!   >�    _Old_val  AJ         N N                       H� 2 h   �  �  �   �   !  !  l!  m!  n!  �!  &"   0   B]  Othis  O,   �    0   �   
 }   �    �   �   
 �   �    �   �   
 o  �    s  �   
 H�    H�H兞�       �      �       �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       �      �       �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (              H            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 L嬡H侅�   駻@$�A婡,塀駻@$�BA婡,塀�驛驛P驛XA)s鐰){�(鵈)C菶)K窫)S‥)[楨)c圖)l$ D)t$D)<$D(蝮Y鳧(DY痼DY�(珞AA(铙Y润Y蠥(黧Y伢]狍]牦_]篌X"驞_蝮Xj驞_Xr�"�j�r�Xz驞Xr驞Xz�z驞r驞z�A驛H驛PD(袤AXD(怏DY谼(塍DY郋(芋DY梵AE(腆Y润Y蠩(朋Y伢D]洋D]鼠D_袤D]皿DX泽DX腕DX唧DX企D驞J驞B驞ZM�驞_釮嬄驞_塍EX鍰(t$驟X顳(<$驞b驞j�A驛P驛X(牦A` (篌Y�(YY�(误A�Y伢Y畜Y�(朋_篌]麦]梭_牦AX鬍(c報AX翬(SAX蒃(K阁AX隕([橌(求J�_]捏AX鼶(l$ 驛X繣(C润B�j�rA(s梵zA({豂嬨�   �   �  J G            �  y   �  p%        �donut::math::box<float,3>::operator* 
 >~J   this  AJ        � >�   transform  AP        �
 >�   f  C�      
      C�      �       C     W     1 M        d!  "����
_
 N. M        x!  ��
��iD N; M        d!  ��	
��

��

 NI M        w!  ����

��4 N) M        i  ����	��
 >@    a  A�   �     nq 6 0/ # M        �  ����"�� >@    _x  A�   �     Ro 8  A�   �    �  >@    _y  A�   �     *q 6  A�   �    �  >@    _z  A�   �     (s 4  A�       �  N N" M        i  1-(��,'��
 >@    a  A�   1     �f q 
�  N �                      @ " h   �  i  %  d!  w!  x!  q%   �   ~J  Othis  �   �  Otransform  �   }J  Oresult  O �              �  (   =   �      �  �
   �  �   �  �-   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �0  �  �4  �  �9  �  �>  �  �B  �  �J  �  �N  �  �W  �  �p  �  �u  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �	  �  �
  �  �  �  �  �  �  �  �$  �  �)  �  �.  �  �3  �  �8  �  �=  �  �I  �  �M  �  �Q  �  �V  �  �\  �  �a  �  �f  �  �k  �  �u  �  �z  �  �  �  �,   �    0   �   
 o   �    s   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    !  �   
 g  �    k  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 %  �    )  �   
 �  �    �  �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 L婮L嬃H婭I;蓆	3繧;�捓肐婬 L婮 I;蓇闕婬3繪婮I;蓇H�
I9捓�   �   �   < F            ?       >   P%        �CompareDrawItemsOpaque 
 >ia   a  AJ          AP       8 
 >ia   b  AK        ?                         @     ia  Oa     ia  Ob  O�   P           ?         D       3  �    4  �   >  �   7  �&   :  �5   =  �;   >  �,   �    0   �   
 ^   �    b   �   
 n   �    r   �   
 �   �    �   �   
 �   �    �   �   
 H�E3繦9u禕,8A,A椑A嬂皿A(/B(A椑A嬂�   �   �   A F            +       *   W%        �CompareDrawItemsTransparent 
 >ia   a  AJ        + 
 >ia   b  AK        +                         @     ia  Oa     ia  Ob  O   �   H           +         <       �  �    �  �   �  �   �  �   �  �#   �  �,   �    0   �   
 c   �    g   �   
    �    �   �   
 �   �    �   �   
 H塋$SVATAVH冹xH嫅�   L崱�   H嬹L墊$XI嬏L墹$�   �    H婩hL崀hI�$E3鯤吚匄  H塴$pH将*H墊$hL塴$`ff�     L;敦   児  嫺�  2蓛�垖$�   鰛�  剘  H峃H崘�  H墝$�   �    垊$�   劺刉  �凮  I�L�
    L�    荄$     3襀婭@�    L嬭H吚�  H媝 H嬇I婰$I+$H鏖L婩XL+FPH龙H嬄I柳H凌?M艸蠭;衧I嬓I嬏�    K�vH零I$H媙XH媬PH;�劙   L嫟$�   H�H婣�xL噦   H婩XH+FPH柳H凐v0H儈@ u)M�H峊$0I伬0  H兞�    H峊$0I嬏�    劺t@L�+H塻H�H塁H�H婬H塊H婩0H塁 秮w  鲐荂(    蓘�圞,H兠0I�艸兦H;�卄���L嫟$�   H嫶$�   H将*稊$�   I嬒�    I�H吚�:��H媩$hH媗$pL媗$`I嬛I嬏�    H崬�   I嬛H嬎�    L媩$X3襇咑�*  I凗傼   �=    岏   H�I岶�驛~$fo%    fl襀�罥;蘷	I;�兤   H;藈	H;�兏   M嬑I冡鴉o    A�   E岯D  I岯Hn耭l繫岪@f阅H兟b螨@薴允fHn荔BL營岯fl纅阅b螨@薴允fIn麦BL癐兟fl纅阅b螨@薴允fHn纅l纅阅驜L纀螨@薴允驜L蠭;�俴���I;謘(L�RI拎I�$H�I菼兝0H�蠬�翴;謗鍵凗v!H�L�
    L媶�   L+罥柳J�凌    H菃�       H兡xA^A\^[�1   �    �   �    �   �   �   �   �      7  �    �  �    �  �    &  �    L  �    ^  �    ~     �     �     �  �    �  �       �   �  [ G            �     �  Q%        �donut::render::InstancedOpaqueDrawStrategy::FillChunk 
 >抋   this  AJ           AL        ��  D�    >#     itemCount  AV  D     � >*p    writePtr  AI  A     �   >0     nodeVisible  A   �       A        B�   p     i >FZ    meshInstance  AU  �     ( AU p     �� ( >#     requiredChunkSize  AP      )    AP G    � F & 
 >禬    mesh  AL  �      >r    <begin>$L0  AM  O    �  AM p     � � >r    <end>$L0  AN  K    � " >濲    geometryGlobalBoundingBox  D0   
 >#    i  AK  �    �  AK �      C       i    w  C      �    �  � �  �   M        L%  5佹 N M        I%  } N M        I%  �� N M        J%  ��
 >℡   a  A   �     �  A  p     � � N M        i%  �� N M        j%  � N M        b%  �� >邇    _My_data  AT p     f� �  B�   0     � N M        m%  亙 N M        j%  乹 N) M        a%  
傕&
&' N M        [%  倝 N M        �%  儮
 Z   �%   >m   _Last  AK  �      N M        \%  儸 N& Z   f%  &  f%  p%  &  
&  f%  `%   x                      @ � h    �!  B%  C%  D%  E%  F%  G%  H%  I%  J%  L%  M%  Z%  [%  \%  ^%  a%  b%  d%  i%  j%  k%  l%  m%  o%  r%  �%  �%  �%  �%  �%  �%   �   抋  Othis & 0   濲  OgeometryGlobalBoundingBox  O   �             �     @         A  �   B  �5   G  �=   D  �A   E  �D   G  �}   K  ��   M  ��   K  ��   M  ��   J  ��   N  ��   P  ��   R  ��   K  ��   R  ��   T  ��   U  ��   W  ��   Z  �  Y  �  Z  �  Y  �!  Z  �%  Y  �(  Z  �0  \  �;  ]  �G  `  �`  b  �g  c  �q  f  ��  h  ��  i  ��  n  ��  o  ��  p  ��  q  ��  r  ��  s  ��  t  ��  v  ��  w  ��  `  �  }  �*  G  �E  �  �P  �  �i  �  �|  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �u  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 
  �      �   
   �    !  �   
 -  �    1  �   
 T  �    X  �   
 d  �    h  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
 '  �    +  �   
 o  �    s  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 3  �    7  �   
 C  �    G  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 @SH冹 H媮�   H嬞H+伕   H柳H9佇   r�    H嫇�   H;摾   u3繦兡 [肏媰�   H岺H墜�   H�翲兡 [�%   �       �     ] G            ]      W   V%        �donut::render::InstancedOpaqueDrawStrategy::GetNextItem 
 >抋   this  AI       L 0   AJ          M        \%  
 N M        ]%  ) N
 Z   Q%                         @  h   [%  \%  ]%   0   抋  Othis  O�   h           ]      
   \       �  �   �  �
   �  �   �  �$   �  �)   �  �9   �  �;   �  �A   �  �W   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 0  �    4  �   
 H婣H吚tH�菻堿H婣H峆0H塓肏茿    3烂   �   �   Y G            (       '   N%        �donut::render::PassthroughDrawStrategy::GetNextItem 
 >ba   this  AJ        (                         @     ba  Othis  O �   X           (         L       !  �    "  �	   $  �   %  �   *  �   (  �%   )  �'   *  �,   �    0   �   
 ~   �    �   �   
 �   �    �   �   
 L婭 H嬔H婣(L婣8I+罤柳L;纑H婣H;AtH堿L;I(tL塈(3烂I岺H塉8K�撩   �   D  Y G            C       B   Y%        �donut::render::TransparentDrawStrategy::GetNextItem 
 >   this  AJ          AK       <  M        \%    N M        _%  
) N M        e%  

 N                        @ * h	   �  [%  \%  _%  e%  �%  �%  �%  �%        Othis  O�   h           C      
   \        �     �    �    �    �)    �3    �5    �6    �B    �,   �    0   �   
 ~   �    �   �   
 �   �    �   �   
 X  �    \  �   
 H塡$H塼$ UWAVH峫$笻侅�   H�    H3腍塃7H�E3鯤孂H塃�W繦塃烮嬸L塽稟峃L塽矿E塽氰    H峂峗xL塸H塃�H婨桯塆hH婨烪塆pH岴;豻cH嬎�    H婱�H�H塃吷tH�H婨吚tH峂�H婨�W繦塁H婨稨塁H婨縃塁H婨荋塁 I嬈H塃求E疞塽侩H婨荋吚tH冭u鶫塃荓塽縃婱疕吷tbH媇稨呟t'H婦嬴H�薍吚t�   H嬋�    H婱疕呟u軭媇稨��    H嬃H侜   rH婭鳫兟'H+罤兝鳫凐嚌   �    H婱Ш   L塽塽稬塽    H�H峌譎嬑�Px GHO@ G(H0O8@@GHHPOXH媷�   H;嚚   tH墖�   L壏�   H婱7H3惕    L崪$�   I媅(I媠8I嬨A^_]描    �      U   �    �   �    '  �    f  �    �  �    �       �       �   0  ` G              (     R%        �donut::render::InstancedOpaqueDrawStrategy::PrepareForView 
 >抋   this  AJ        1  AM  1     ��  >襓   rootNode  AK        Y 
 >�&   view  AL  ?     ��  AP        ?  M        e%  
伬 N' M        �   H��L2]�� M        n!  乶 M        �!  乶 M        &"  乶 M        �  乶
 Z      N N N N M        m!  乯	 >�    _Old_val  AJ  n      N6 M        !  ��%n))-2	?	�� >#     _Block  AI      � ^ �  AI j    �  M        A!  �� N M        @!  $��
 N M        C!  � M        �  �
 Z     
 >   _Ptr  AH        AH     2    N N M        B!  2�8��  M        �  丆)��
 Z     
 >   _Ptr  AH  C      AJ      @ # 	  AH e      AJ j      >#    _Bytes  AK  @    � * �  M        �  
丩��
 Z   q   >#    _Ptr_container  AH  W      AJ  P      N N N N N" M        h%  |4	*-9,
 Z   !  % M        �%  
��&$((
 M        @%  &��  M        �  ��*D%c	 N N N N# M        K%  +" >   scope  AH  +     .  M        �   ��+" M        �   +$4	

# M        p!  CX$ >�    _New_proxy  AH  Y       M        �!  a M        y   a N N M        �!  C M        �  C M        �  C
 Z   �   N N N N M        o!  +
 M        �!  +
 M        �!  2K N N N N N N �                     A � h>   B  �  �  �  �  �  �  �  ,  y   �   �   �   �   �   !  !  !  =!  >!  ?!  @!  A!  B!  C!  I!  J!  K!  L!  N!  O!  l!  m!  n!  o!  p!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  &"  N"  O"  @%  K%  S%  T%  U%  e%  h%  �%  �%  �%  �%  �%  
 :�   O        $LN184  �   抋  Othis  �   襓  OrootNode     �&  Oview  9�      )H   O�   p                   d       �  �(   �  �.   �  �1   �  �<   �  �?   �  ��  �  ��  �  ��  �  ��  �  �  �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
 Q  �    U  �   
 e  �    i  �   
 �  �    �  �   
   �      �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 #  �    '  �   
 3  �    7  �   
 +  �    /  �   
 �  �    �  �   
 �  0   �  0  
 ,  �    0  �   
 D  �    H  �   
 H塡$UVWATAUAVAWH峫$怘侅p  )�$`  H�    H3腍塃PI孁H嬺L嬮E3�L墆8H婣H;AtH堿L峲 L塽圛�I;FtI塅I� H峌怘嬒�PhH�H峌餒嬒�PxH�H塃燞塃�W荔E癓墋繪墋萀墋泄   �    L墄H塃癏峂癏�H婨燞吚務  �5    @ 嫺�  冪E2漩��  剴  H崘�  H峂痂    D多圖$0劺剅  �刯  D墊$ L�
    L�    3襀婱燞婭@�    H吚�?  L媝 M媬XI媬PI;��'  L嬥H�7H婩�xL�   I婩XI+FPH柳H凐vRI婩HH吚tI儀 tCH峃L婨營伬0  H峌罔     D$h�H�L$xH峊$hH峂痂    劺劄  �H婨���  D$h�埌  �L$x3纅塂$e圖$gL塪$8L塼$@H�H塂$HH婡H塂$PI婩0H塂$X�L$|�\$p�\梭D$x�T$l�\麦Y误Y企X袤X畜\]橌\U旙L$t�\L$h�Y误XL$h�\M愺Y审Y殷X鼠Y垠X�W�.羨	W荔Q岭(凌    �D$`H婩I婾�竪   剎   A�}@ tj艱$dI;Ut!L$8
T$HRD$XB I僂0�L岲$8I峂�    T$HL$8艱$d I婾I;UtJ
RD$XB I僂0階艱$d�艱$d I;Ut!D$8L$HJD$XB I僂0�L岲$8I峂�    H兦I;�呪��D禿$0E3�A对H峂犺    H婨燞吚�;��L媢圛婨I婱H;�劷   H+菻揩*H嬊H鏖H龙H嬄H凌?H蠭嬑�    M嬊M婾I婱I+蔋嬊H鏖H龙H嬄H凌?H衪?M嬒fD  K�I�J�繧�繫岻0M婾I婱I+蔋嬊H鏖H龙H嬄H凌?H蠰;聄蔍�M婩L+罥柳I凐vN��    I�I柳L�
    �    怘峂拌    H婱癓墋昂   �    H婱PH3惕    H嫓$�  (�$`  H伳p  A_A^A]A\_^]�'      �   �    �   
   �   �      �      �   /     �  �    �  �    �     �      a      �  �    �  �    ]  �    b  �    l  �    ~  �    �        �   c  \ G            �  2   �  X%        �donut::render::TransparentDrawStrategy::PrepareForView 
 >   this  AJ        ;  AU  ;     p >襓   rootNode  AK        8  AL  8     �  AL �     �� !
 >�&   view  AM  5     �  AP        5  AM �     � ���  >    viewFrustum  D�    >\]   walker  CH      �     �> � CH     {    
  D�    >�    viewOrigin  D�    >0     nodeVisible  A   �       A\  �     �  x $ Al  x    !  A\ �     � � Al �     � � E6u 0   �     � >FZ    meshInstance  AH  3    !  AT  T    $ AH x     
 >禬    mesh  AV  @    ; AV �     �p ; >r    <begin>$L0  AM  H    3 AM �     � ���  >r    <end>$L0  AW  D    7" >濲    geometryGlobalBoundingBox  Dh   
 >
j   item  C�       �    Z    C�      �    R    C�      T    4J + � v C�     T    4J + � % D8   
 >#     i  AP  �    g  M        e%  
B
 N M        K%  ��* >   scope  AH  �     &  M        �   ��*�� M        �   �� M        p!  ��*$ >�    _New_proxy  AH  �       M        �!  �� M        y   �� N N M        �!  
�� M        �  
�� M        �  
��
 Z   �   N N N N M        o!  �� M        �!  �� M        �!  2�� N N N N N N M        _%  
X	
 >薼   this  AV  T     |  AV �     �p ; N M        L%  ��傃 N M        I%  �� N M        I%  	�� N M        n%  	亀 N M        j%  乪 N M        e!  俴 M        w   倐 >@    _Xx  A�         A�  �    � 2  \ 0 �   N M        �!  俴 M        h  俴 N N N M        b  侹	 M        �  俹 >@    _y  A�   k      >@    _z  A�   P    #  N N M        �   5�: M        j  	侭 M        �  侳 >@    _y  A�   f      >@    _z  A�   F    
  N N M        m  �:" M        �  �> >@    _y  A�   `      >@    _z  A�   >    D  N N M        b  $�: M        �  �6 >@    _x  A�   \      >@    _y  A�   6      >@    _z  A�   &      N N N M        g%  � M        �%  
�
 M        �%  �
 M        �%  �
 N N N N M        g%  ?偪 M        �%  
偪&!
 Z   �%   M        �%  偱 M        �%  偱 N N N N M        c%  儥 N M        b%  !儶 N M        b%  冎< N M        a%  �  N M        [%  凎
 N M        \%  �6 >蕛    _My_data  AV �     B�   X     Y N M        �%  刅
 Z   �%   >m   _Last  AK  V      N M        �   刧	
 Z   !   M        n!  
剎 M        �!  
剎 M        &"  
剎 M        �  
剎
 Z      N N N N M        m!  刾 >�    _Old_val  AJ  t      N N Z   &  p%  &  �%  
&  `%   p          8         A fhX   B  �  �  �  �  h  j  m  �  �  b  �  ,  w   y   �   �   �   �   �   �   �   !  !  e!  l!  m!  n!  o!  p!  �!  �!  �!  �!  �!  �!  �!  �!  �!  &"  N"  O"  B%  C%  D%  E%  F%  G%  I%  J%  K%  L%  M%  S%  Z%  [%  \%  ^%  _%  a%  b%  c%  e%  g%  i%  j%  k%  l%  n%  o%  q%  r%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%   &  
 :P  O  �    Othis  �  襓  OrootNode  �  �&  Oview  �     OviewFrustum  �   \]  Owalker  �   �  OviewOrigin & h   濲  OgeometryGlobalBoundingBox  8   
j  Oitem  9o       (H   9|       )H   O �   �          �     7   �      �  �;   �  �B   �  �P   �  �e   �  �r   �  �   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �  �  �3  �  �<  �  �@  �  �D  �  �H  �  �T  �  �W  �  �e  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �
  �  �  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �$  �  �)  �  �+  �  �0  �  �x  �  ��  �  ��  �  ��    ��   ��   �   �6   �J  	 �g   ��   b  k F                                �`donut::render::TransparentDrawStrategy::PrepareForView'::`1'::dtor$0  >    viewFrustum  EN  �           >\]    walker  EN  �           >�    viewOrigin  EN  �          " >濲    geometryGlobalBoundingBox  EN  h          
 >
j    item  EN  8                                  �  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �      �   
 b  �    f  �   
 z  �    ~  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 )  �    -  �   
 P  �    T  �   
 `  �    d  �   
 p  �    t  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 R  �    V  �   
 j  �    n  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 &  �    *  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 E  �    I  �   
 b  �    f  �   
 �  �    �  �   
 �  �    �  �   
 8  �    <  �   
 U  �    Y  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �  �    �  �   
 O  �    S  �   
 _  �    c  �   
 x  �    |  �   
 p     t    
 �     �    
          
 -     1    
 e     i    
 �     �    
 H崐�   �       �    H塓L堿�   �     U G            	          O%        �donut::render::PassthroughDrawStrategy::SetData 
 >ba   this  AJ        	 
 >ia   data  AK        	  >#    count  AP        	                         @     ba  Othis     ia  Odata     #   Ocount  O   �   8           	         ,       -  �    .  �   /  �   0  �,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 (  �    ,  �   
 H塡$H塼$H墊$AVH冹 H嬞I嬹H�	M嬸H孃H吷tPL婼H斧*L+袸麝H龙H嬄H凌?H蠬�RH菱H侜   rL婣鳫兟'I+菻岮鳫凐w?I嬋�    H�;K�vH拎H荋塁H�vH媡$8H拎H荋媩$@H塁H媆$0H兡 A^描    蘵   �    �   �       �   H  � G            �      �   �%        �std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Change_array 
 >6p   this  AI       � �   AJ          >妏   _Newvec  AK        $  AM  $     � |   >#   _Newsize  AP        !  AV  !     � �   >#   _Newcapacity  AL       � y   AQ          M        �%  .Ke M        �  S)<
 Z     
 >   _Ptr  AJ t       >#    _Bytes  AK  S     b & 7 " M        �  
\#
?
 Z   q   >#    _Ptr_container  AP  `     U  <  AP t       >#    _Back_shift  AJ       � V <  AJ t     <  N N N                       @  h   �  �  �  �%  �%  �%         $LN25  0   6p  Othis  8   妏  O_Newvec  @   #  O_Newsize  H   #  O_Newcapacity  O�   X           �   �     L       � �   � �)   � �y   � �|   � ��   � ��   � ��   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 $  �    (  �   
 O  �    S  �   
 c  �    g  �   
 �  �    �  �   
 �  �    �  �   
 D  �    H  �   
 X  �    \  �   
 ~  �    �  �   
 �  �    �  �   
 �  &   �  &  
 \  �    `  �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       �      
      �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               h            J �   K �,   �    0   �   
 �      �     
 �   �    �   �   
 H塼$WH冹 H婣 H孂H吚tH冭H堿 u鯤茿    H儁 L塼$8L峲trH塡$0H媃H呟t-ff�     H婫H�薍�豀吷t
�   �    H呟u酟峸H媁H婳H媆$0H菱H侜   rL婣鳫兟'I+菻岮鳫凐w'I嬋�    H媡$@I�    L媡$8H荊    H兡 _描    蘤   �    �   �    �   �       �   ^  k G            �   
   �   !        �std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Tidy 
 >踈   this  AJ          AM       � �   >#     _Block  AI  A     ?  AV  6       AV �       M        A!  
 N M        @!  $
 N M        C!  
` M        �  
`
 Z     
 >   _Ptr  AJ  [       AJ P     +    N N M        B!  ;oZ# M        �  o	)$
 Z     
 >   _Ptr  AJ  {     /  
  >#    _Bytes  AK  w     W 3  # M        �  
��#
'
 Z   q   >#    _Ptr_container  AP  �     =  $  AP �       >#    _Back_shift  AJ  �     6 
 $  N N N                       H� V h   �  �  !  =!  >!  ?!  @!  A!  B!  C!  I!  J!  K!  L!  N!  O!  �!  �!  �!  �!         $LN71  0   踈  Othis  O  �   �           �      
   t       2 �
   6 �   2 �   6 �   7 �(   : �=   ; �P   < �`   = �j   ; �o   B ��   G ��   B �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
   �       �   
 z  �    ~  �   
 �  �    �  �   
 �  �    �  �   
 F      J     
 t  �    x  �   
 H冹(H�
    �    �   �      �       �   �   � G                     �%        坰td::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Xlength 
 Z   j   (                      @        $LN3  O   �   (              �            a �   b �,   �    0   �   
 �   -   �   -  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   } G                     �%        坰td::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Xlength 
 Z   j   (                      @        $LN3  O   �   (              �            a �   b �,   �    0   �   
 �   (   �   (  
 �   �    �   �   
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   e  a G            B      B   �%        �std::allocator<donut::render::DrawItem const *>::deallocate 
 >*q   this  AJ          AJ 0       D0   
 >m   _Ptr  AK          >#   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        �  
#

 Z   q   >#    _Ptr_container  AJ       %    AJ 0       >#    _Back_shift  AH          AH 0       N N (                      H  h   �  �         $LN18  0   *q  Othis  8   m  O_Ptr  @   #  O_Count  O   �   8           B        ,       � �   � �3   � �7   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    #  �   
 @  �    D  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 %  *   )  *  
 |  �    �  �   
 H冹(H嬄K�@H菱H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   ]  Y G            B      B   �%        �std::allocator<donut::render::DrawItem>::deallocate 
 >籸   this  AJ          AJ 0       D0   
 >妏   _Ptr  AK          >#   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        �  
#

 Z   q   >#    _Ptr_container  AJ       %    AJ 0       >#    _Back_shift  AH          AH 0       N N (                      H  h   �  �         $LN18  0   籸  Othis  8   妏  O_Ptr  @   #  O_Count  O   �   8           B        ,       � �   � �3   � �7   � �,   �    0   �   
 ~   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 8  �    <  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   #   !  #  
 t  �    x  �   
 H塼$WH冹 H媞H孂L�H嬑I+菻六H;裺I�蠬塆H媡$@H兡 _胿QH婫I+繦柳H;衯L岲$0H嬒H媡$@H兡 _�    H+袶塡$8H嬑H��    3襆嬅�    H�3H媆$8H塆H媡$@H兡 _肹   �    x         �   �  � G            �   
   �   `%        �std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::resize 
 >薼   this  AJ          AM       � $  I   >#   _Newsize  AK        b  AK �      3 M        �%  
-"+ >#    _Oldsize  AJ       S   9   AJ �       >m    _Newlast  AH  '       >m    _Oldlast  AL       � "  G 
  >#    _Oldcapacity  AH  C     9  M        �%  _ M        �%  g
 N N N                       @ > h   B  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%   0   薼  Othis  8   #  O_Newsize  O�   `           �   �  	   T       4 �
   6 �   4 �   6 �'   7 �6   6 �P   7 �Z   6 ��   7 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 S  �    W  �   
 k  �    o  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 H塼$WH冹 H媞L嬍L�H嬛I+襂猾*I嬅H孂H麝L嬄I柳I嬂H凌?L繫;萻K�IH拎I翲堿H媡$@H兡 _胿eH婭I嬅I+蔋鏖H龙H嬄H凌?H蠰;蕍L岲$0I嬔H嬒H媡$@H兡 _�    M+萾"H塡$83襅�IH嬑H零L嬅�    H驢媆$8H墂H媡$@H兡 _脮   �    �         �   +  { G            �   
   �   f%        �std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::resize 
 >6p   this  AJ        *  AM  *     � 2  j   >#   _Newsize  AK         9 M        �%  
%"+ >#    _Oldsize  AP  0       >妏    _Newlast  AH  N       M        �%  �� >#    _Count  AQ       �  AQ �       N N                       @ 6 h   B  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%  �%   0   6p  Othis  8   #  O_Newsize  O �   p           �   �     d       4 �
   6 �   4 �   6 �'   4 �*   6 �R   7 �]   6 ��   7 ��   6 ��   7 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 9  �    =  �   
 \  �    `  �   
 �  �    �  �   
 �  �    �  �   
 @  �    D  �   
 H婹H�    H呉HE旅   �      �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              H     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H                       H    20    2                       N   
 
4 
2p    B                       T    20    <                       Z   
 
4 
2p    B                       `    20    <                       f   
 
4 
2p    B                       l    �                              r   y y�  t� n� h� c� ^� Y� T� Lx Gh	 
     �                      x   2 2� 
d 
2p    8           !      !      ~   ! 4     8          !      !      ~   8   �           !      !      �   !       8          !      !      ~   �   �           !      !      �    20    +           "      "      �    B      B           $      $      �   
 
d 
2p    �           %      %      �   ! 4     �          %      %      �   �   �           %      %      �   !       �          %      %      �   �   �           %      %      �    t d 4 2�    �           '      '      �    B                 )      )      �    B      B           +      +      �   
 
d 
2p    b           ,      ,      �   ! 4     b          ,      ,      �   b   �           ,      ,      �   !       b          ,      ,      �   �   �           ,      ,      �    B                 .      .      �    ��	�`0                  /      /      �   ! �                /      /      �       M           /      /      �   ! � t
 T     M          /      /      �   M   E          /      /      �   !       M          /      /      �   E  r          /      /      �   !                  /      /      �   r  �          /      /      �   (	 d! 4  �pP      �                       1      1      �    20    ]           2      2      �   2
 $h 47 . ���
�p`P          R         $          �          3      3         (                    
    �   �    � �� 
 4 r����
p`P    �          4      4          2�
�p`0                        ^          7      7         8                      #   	   )            &   �       
   M r 
 
2P    (           
      
      ,     2�
�p`0                 ;       X          :      :      5   8               >      A   	   G            D   �          
 z� 
 
2P    (                       J     2���
�p`0                 Y       �          =      =      S   8               \      _   	   e            b   �       	   � �� 
 
2P    (           	      	      h    
 4 2����
p`P    <          >      >      q    20               ?      ?      w    B      :           A      A      }    4 2
��	�p`               B      B      �   ! � T
               B      B      �      |           B      B      �   !   �               B      B      �   |   �           B      B      �   !                 B      B      �   �   �           B      B      �   
 
4 
2p    0           C      C      �   
 
4 
2p    0           D      D      �    d T 4 2p    z           E      E      �                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                    .?AVbad_alloc@std@@     �              ����                      �      �                    .?AVexception@std@@     �               ����                      �      �        ����    ����        ��������                .?AVSceneGraphLeaf@engine@donut@@     �                   .?AVMeshInstance@engine@donut@@     �   vector too long                                       �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                         �                           �      �              ����    @                   �      �                                         �      �      �                                                                   �      �              ����    @                   �      �      ?               0       0          �   (   & 
7        std::exception::`vftable'    �      �  
    �   (   & 
7        std::bad_alloc::`vftable'    �      �  
    �   3   1 
7        std::bad_array_new_length::`vftable'     �      �  
 "吢叅频鐦枾迊琅=*�(.瞗輩耾"0誒耦珗V1=J擽h�K蜌�([柏q�廁�"嘇�%@e搟P誹A"R��.�<\腐癆"R��<\伡�茚枲9嶔* 垻逖`s鲚溏q茪n?礘CL�+n0%倓檇蓏Nq��"@訓y蠋鞺Or铌v<f賾��o褪�\�
d戮翁rK�3葮麽d;顃F
d戮翁rx譞惎收斱�,�瞬�嚻址Zd谖蟲@俌樯湱�}�
q靅>菳
泅n黢$hsS醖�0嚤踖p禭诵鰐麌臂ep禭�6IB��6萪O��夲栮O塊霵婬(�1粦�^>禴y猦豬榢档�D嶀預棊膬$R驯�-n嶀預棊膬燆失ms毚eDA忝^omI-箩邆5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k祗��萩�%猻骧6Y=倧]旾8�%毴鳏毡�7剑?桩C+�7E=iunw詈E鎠�<E菓橰妯凊j�m�訅M�7圃=遑艰Z9矚S铖琪�/yc鱃�鈑u:膔i�蔻5荇QN嚜鉪R�縌��9墜揺�%/莐,哫6"
p#8Z%I栶賑?T`緗顔弎H廓]虚�檶
�錰]{謑p[〤孺F髫f]{謑p戓kO寎tV �鉵�z荮R鱒酕嵛奕.3奥0)铒�澾惋b������7朽kh趰廭nN鵘J鈦罈X �F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪籬X吶飖w夷"�%趆�#�9�Ｓ[目湸軃焛猛P<$]�0o�G�<〣.{_�蠑�;_畴嘕-WV8o额	hQ�)-坓�(鬄酲;[純o薢�9.楞ч咏A遐OA�詸i黶uk6DUo9M 羣3�}l鴿�5�蘫� 葆怺�9x-坓�(鬄�汬'这�-坓�(鬄酲;[純o薢�9.楞ч>%'剌i貏�#X�ni1UBE�W颹�8掗疆Y>�-坓�(鬄�汬'这杻�+鏲E�5R犵�喪p�蹅K詢; ��W�2嗃��84L<�O嘀礵!熿�RcJ奄腄�
8|�*屝LN
�3楫W堎{姌�	酼璺雵J-WV8o緬F=屸趾廈�(弒S释鼫 dd�a�:歺竖b焏膦w1�呸�LV租&d纽�馚暕妝�#(戼{1�4焄y*�杜`颀l+�鞯.r擣�0G#盱谑喪仜丩⒚(��苳乮5絚_}4n4�硓榲暕妝�#(UF诒3y*�杜`颀l+�鞯.r擣�0G#盱谑〃涠?忯(��苳乮5絚_}4n4�硓橆J�:驁P欗 {灧絉y*�杜`颀l+�鞯.r擣�0G#盱谑d笱)�(��苳乮5絚_}4n4�硓樻��&續斈咋A浚雵J-WV8o�%-<$�-坓�(鬄�/ｎ	蜍R�周貥�?鷃兟+d+饼鞓閃纝2�D赽}�X翄_蹽窣笈砾$u@呚�!斈頔� "iF�9E\$L釉��E光9E\$L釉��E光�嫫﹥�,嗹啴r殅W潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|K��H$l_	-~5&T_{�6挚%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸�         贋XCRC冼�4熃鐆v紃
^B糚越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       s                .debug$S       $
              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       .       ^�     .debug$S       �              .text$mn       :      眡�     .debug$S                    .text$mn    	         補胑     .debug$S    
   �         	    .text$mn       0      燥"V     .debug$S       �             .text$mn    
   0      燥"V     .debug$S       �         
    .text$mn       �     �&�     .debug$S       x
  P           .text$x        (      镽=    .text$mn       z       FW�     .debug$S       D              .text$mn       <     蒜�     .debug$S       �  X           .text$mn       �       溦     .debug$S       h  .           .text$mn       ^     �岷     .debug$S       D  V           .text$x        (      纥吨    .text$mn       X     �$�     .debug$S       x	  J           .text$x        (      纥吨    .text$mn       �     蜞I     .debug$S       �  d           .text$mn              憟⑸     .debug$S    !   P  
            .text$mn    "   :      s懅n     .debug$S    #   L         "    .text$mn    $   #       v^
�     .debug$S    %   �          $    .text$mn    &   <      .ズ     .debug$S    '   0  
       &    .text$mn    (   <      .ズ     .debug$S    )   L  
       (    .text$mn    *   !      :著�     .debug$S    +   <         *    .text$mn    ,   2      X于     .debug$S    -   <         ,    .text$mn    .   +      rx�/     .debug$S    /   �         .    .text$mn    0         ��#     .debug$S    1   �          0    .text$mn    2         ��#     .debug$S    3   �          2    .text$mn    4   �      摭     .debug$S    5   �         4    .text$mn    6   B      贘S     .debug$S    7             6    .text$mn    8   B      贘S     .debug$S    9            8    .text$mn    :   B      贘S     .debug$S    ;   �          :    .text$mn    <   H       襶.      .debug$S    =   �         <    .text$mn    >   ?       X柗T     .debug$S    ?   ,  
       >    .text$mn    @   +       �/Y�     .debug$S    A            @    .text$mn    B   �     �眏     .debug$S    C   �  4       B    .text$mn    D   ]      锃2     .debug$S    E   �         D    .text$mn    F   (       �,O     .debug$S    G            F    .text$mn    H   C       j.l     .debug$S    I   �         H    .text$mn    J        )戉o     .debug$S    K   �  .       J    .text$mn    L   �     雃     .debug$S    M   �  t       L    .text$x     N         ]篖    .text$mn    O   	       誹jt     .debug$S    P   `  
       O    .text$mn    Q   �      a�)�     .debug$S    R   �  "       Q    .text$mn    S          aJ鄔     .debug$S    T   �          S    .text$mn    U   �      �6�      .debug$S    V   �         U    .text$mn    W         �ッ     .debug$S    X            W    .text$mn    Y         �ッ     .debug$S    Z   �          Y    .text$mn    [   B      mr{V     .debug$S    \   �         [    .text$mn    ]   B      臣6     .debug$S    ^   �         ]    .text$mn    _   �      碙焢     .debug$S    `            _    .text$mn    a   �      鰾荵     .debug$S    b   �         a    .text$mn    c         崪覩     .debug$S    d   �          c        \       <        x                �                �                �                �                �       ,              2        "      c        B      :        a          i�                    �      &        �      6        �          i�                    �      *              0        )      (        U      8                  i�                    �      S        �               �               /      4        l      U        �               �      .        �      F        C      O        �      ]        �      a        K      Q        �      Y        4      [        �      _        �      W        g      B        �      J        .      D        |      L        	      H        L	      >        �	      @        �	      "        �	              :
              �
      $        �
              �              7              �              �
                             i                            r      
        �      	        O              �              �              F              �      N        �               �               �               �               �               �           memmove          memset           sqrtf            $LN13       <    $LN5        ,    $LN10       :    $LN7        &    $LN13       6    $LN10       (    $LN16       8    $LN3        S    $LN4        S    $LN41       4    $LN71   �   U    $LN74       U    $LN38       .    $LN18   B   ]    $LN21       ]    $LN31       a    $LN25   �   Q    $LN28       Q    $LN3       Y    $LN4        Y    $LN18   B   [    $LN21       [    $LN29       _    $LN3       W    $LN4        W    $LN101      B    $LN184    J    $LN188      J    $LN15       D    $LN241      L    $LN103          $LN97   ^          �  
       $LN101          $LN80   X          �  
       $LN84           $LN97   �          V  
       $LN102          $LN111          $LN4             $LN14   :       $LN17           $LN30           $LN4            $LN4        
    $LN19           .xdata      e          F┑@<              e    .pdata      f         X賦�<        ;      f    .xdata      g          （亵,        ^      g    .pdata      h          T枨,        �      h    .xdata      i          %蚘%:        �      i    .pdata      j         惻竗:        �      j    .xdata      k          （亵&        �      k    .pdata      l         2Fb�&        %      l    .xdata      m          %蚘%6        M      m    .pdata      n         惻竗6        t      n    .xdata      o          （亵(        �      o    .pdata      p         2Fb�(        �      p    .xdata      q          %蚘%8              q    .pdata      r         惻竗8        3      r    .xdata      s          懐j濻        d      s    .pdata      t         Vbv鵖        �      t    .xdata      u   0       ��4        �      u    .pdata      v         F廇4              v    .xdata      w          覺误U        L      w    .pdata      x         菻(VU        �      x    .xdata      y         畻U        �      y    .pdata      z         裥赨               z    .xdata      {         $�颱        =      {    .pdata      |         �;頬U        z      |    .xdata      }          （亵.        �      }    .pdata      ~          ~�.        �      ~    .xdata                �9�]                  .pdata      �         惻竗]        }      �    .xdata      �          釫�<a        �      �    .pdata      �         忙
:a        K      �    .xdata      �         訕�'a        �      �    .pdata      �         5H�a        #      �    .xdata      �         /L]僡        �      �    .pdata      �         媿"骯        �      �    .xdata      �          �F鏠        j      �    .pdata      �          邫Q        �      �    .xdata      �          �9�Y        �      �    .pdata      �         �1癥        �      �    .xdata      �          �9�[        Z       �    .pdata      �         惻竗[        �       �    .xdata      �          釫�<_        +!      �    .pdata      �         僻螔_        �!      �    .xdata      �         謾D廮        "      �    .pdata      �         fl坃        �"      �    .xdata      �         *M�+_        �"      �    .pdata      �         z铯V_        g#      �    .xdata      �          �9�W        �#      �    .pdata      �         �1癢        J$      �    .xdata      �          s�=鏐        �$      �    .pdata      �         Vbv鵅        �$      �    .xdata      �         璅uJB        B%      �    .pdata      �         �┆B        �%      �    .xdata      �         鵟罪B        �%      �    .pdata      �         緆.XB        &      �    .xdata      �         (纩B        Z&      �    .pdata      �         妣F塀        �&      �    .xdata      �         簣!@B        �&      �    .pdata      �          学窧        ,'      �    .xdata      �          Af蒌J        r'      �    .pdata      �         �
zDJ        (      �    .xdata      �          （亵D        �(      �    .pdata      �         �#蜠        �(      �    .xdata      �   ,      lM裯L        @)      �    .pdata      �         *�頛        �)      �    .xdata      �   	      � )9L        [*      �    .xdata      �         惷*L        �*      �    .xdata      �          齇rHL        �+      �    .xdata      �          晀T        ,      �    .pdata      �         zX佶        �,      �    .xdata      �         啄qJ        -      �    .pdata      �         @贳�        �-      �    .xdata      �   
      B>z]        q.      �    .xdata      �          �2g�         /      �    .xdata      �         T�8        �/      �    .xdata      �         r%�        �0      �    .xdata      �   	       舔L        31      �    .xdata      �          3賟P        �1      �    .pdata      �         銀�*        �2      �    .voltbl     �                  _volmd      �    .xdata      �         啄qJ        [3      �    .pdata      �         倾�         4      �    .xdata      �   
      B>z]        �4      �    .xdata      �          �2g�        Q5      �    .xdata      �         T�8         6      �    .xdata      �         r%�        �6      �    .xdata      �          8Y�        R7      �    .xdata      �          3賟P        �7      �    .pdata      �         銀�*        �8      �    .voltbl     �                  _volmd      �    .xdata      �         腌禾        h9      �    .pdata      �         w觓8        ':      �    .xdata      �   
      B>z]        �:      �    .xdata      �          �2g�        �;      �    .xdata      �         T�8        m<      �    .xdata      �         r%�        ,=      �    .xdata      �          瓑�        �=      �    .xdata      �          3賟P        �>      �    .pdata      �         銀�*        ?      �    .voltbl     �                  _volmd      �    .xdata      �          �/铂        M@      �    .pdata      �         W鼸        A      �    .xdata      �          （亵         銩      �    .pdata      �         �#洢         NB      �    .xdata      �          �9�        稡      �    .pdata      �         礝
        C      �    .xdata      �          塟ZT        pC      �    .pdata      �          *鬰        D      �    .xdata      �         鴠�        盌      �    .pdata      �         睸Nj        SE      �    .xdata      �         蒨�        鮁      �    .pdata      �         �+
�        桭      �    .xdata      �         炖Ｚ        9G      �    .pdata      �         K�        跥      �    .xdata      �          %蚘%        }H      �    .pdata      �         }S蛥        鮄      �    .xdata      �          %蚘%
        lI      �    .pdata      �         }S蛥
        豂      �    .xdata      �          嘋c�        CJ      �    .pdata      �         X崘=        艼      �    .rdata      �                      HK     �    .rdata      �          �;�         _K      �    .rdata      �                      咾     �    .rdata      �                      滽     �    .rdata      �          �)         縆      �    .xdata$x    �                      隟      �    .xdata$x    �         虼�)         
L      �    .data$r     �   /      嶼�         0L      �    .xdata$x    �   $      4��         UL      �    .data$r     �   $      鎊=         狶      �    .xdata$x    �   $      銸E�         腖      �    .data$r     �   $      騏糡         M      �    .xdata$x    �   $      4��         M      �        \M           .data       �           烀�          oM      �             �    .data$r     �   2      詂刑         蔒      �    .data$r     �   0      舃         騇      �    .rdata      �          IM         N      �    .rdata$r    �   $      'e%�         >N      �    .rdata$r    �         �          VN      �    .rdata$r    �                      lN      �    .rdata$r    �   $      Gv�:         侼      �    .rdata$r    �   $      'e%�               �    .rdata$r    �         }%B         筃      �    .rdata$r    �                      螻      �    .rdata$r    �   $      `         錘      �    .rdata$r    �   $      'e%�         O      �    .rdata$r    �         �弾         'O      �    .rdata$r    �                      HO      �    .rdata$r    �   $      H衡�         iO      �        揙           .bss        �                             �    .rdata      �          =-f�         籓      �        薕           .rdata      �          �          軴      �    .rdata      �          �誸         P      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64                         +P  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?intersectsWith@frustum@math@donut@@QEBA_NAEBU?$box@M$02@23@@Z ??D?$box@M$02@math@donut@@QEBA?AU012@AEBU?$affine@M$02@12@@Z ?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ ?Next@SceneGraphWalker@engine@donut@@QEAAH_N@Z ??1SceneGraphWalker@engine@donut@@QEAA@XZ ?GetNextItem@PassthroughDrawStrategy@render@donut@@UEAAPEBUDrawItem@23@XZ ?SetData@PassthroughDrawStrategy@render@donut@@QEAAXPEBUDrawItem@23@_K@Z ?deallocate@?$allocator@UDrawItem@render@donut@@@std@@QEAAXQEAUDrawItem@render@donut@@_K@Z ?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z ?_Change_array@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAXQEAUDrawItem@render@donut@@_K1@Z ?_Xlength@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@CAXXZ ?deallocate@?$allocator@PEBUDrawItem@render@donut@@@std@@QEAAXQEAPEBUDrawItem@render@donut@@_K@Z ?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z ?_Xlength@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@CAXXZ ?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ ?PrepareForView@InstancedOpaqueDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z ?GetNextItem@InstancedOpaqueDrawStrategy@render@donut@@UEAAPEBUDrawItem@23@XZ ?PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z ?GetNextItem@TransparentDrawStrategy@render@donut@@UEAAPEBUDrawItem@23@XZ ?CompareDrawItemsOpaque@@YAHPEBUDrawItem@render@donut@@0@Z ?CompareDrawItemsTransparent@@YAHPEBUDrawItem@render@donut@@0@Z ??$length@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$?YM@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@AEBU201@@Z ??$_Sort_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@0_JP6AHPEBU123@2@Z@Z ??$lengthSquared@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z ??$_Partition_by_median_guess_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YA?AU?$pair@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@0@PEAPEBUDrawItem@render@donut@@0P6AHPEBU234@1@Z@Z ??$_Zero_range@PEAPEBUDrawItem@render@donut@@@std@@YAPEAPEBUDrawItem@render@donut@@QEAPEBU123@0@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z ??$_Copy_memmove@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@std@@YAPEAPEBUDrawItem@render@donut@@PEAPEBU123@00@Z ??$_Copy_memmove@PEAUDrawItem@render@donut@@PEAU123@@std@@YAPEAUDrawItem@render@donut@@PEAU123@00@Z ??$_Copy_backward_memmove@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@std@@YAPEAPEBUDrawItem@render@donut@@PEAPEBU123@00@Z ??$_Med3_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@00P6AHPEBU123@1@Z@Z ?catch$0@?0???$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0??PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __RTDynamicCast __security_check_cookie __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 __catch$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??D?$box@M$02@math@donut@@QEBA?AU012@AEBU?$affine@M$02@12@@Z $pdata$??D?$box@M$02@math@donut@@QEBA?AU012@AEBU?$affine@M$02@12@@Z $unwind$?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ $pdata$?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ $chain$1$?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ $pdata$1$?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ $chain$2$?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ $pdata$2$?_Tidy@?$deque@_KV?$allocator@_K@std@@@std@@AEAAXXZ $unwind$??1SceneGraphWalker@engine@donut@@QEAA@XZ $pdata$??1SceneGraphWalker@engine@donut@@QEAA@XZ $unwind$?deallocate@?$allocator@UDrawItem@render@donut@@@std@@QEAAXQEAUDrawItem@render@donut@@_K@Z $pdata$?deallocate@?$allocator@UDrawItem@render@donut@@@std@@QEAAXQEAUDrawItem@render@donut@@_K@Z $unwind$?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $pdata$?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $chain$0$?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $pdata$0$?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $chain$1$?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $pdata$1$?resize@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $unwind$?_Change_array@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAXQEAUDrawItem@render@donut@@_K1@Z $pdata$?_Change_array@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAXQEAUDrawItem@render@donut@@_K1@Z $unwind$?_Xlength@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@PEBUDrawItem@render@donut@@@std@@QEAAXQEAPEBUDrawItem@render@donut@@_K@Z $pdata$?deallocate@?$allocator@PEBUDrawItem@render@donut@@@std@@QEAAXQEAPEBUDrawItem@render@donut@@_K@Z $unwind$?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $pdata$?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $chain$0$?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $pdata$0$?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $chain$1$?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $pdata$1$?resize@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@QEAAX_K@Z $unwind$?_Xlength@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@CAXXZ $unwind$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $pdata$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $chain$0$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $pdata$0$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $chain$3$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $pdata$3$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $chain$4$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $pdata$4$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $chain$5$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $pdata$5$?FillChunk@InstancedOpaqueDrawStrategy@render@donut@@AEAAXXZ $unwind$?PrepareForView@InstancedOpaqueDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $pdata$?PrepareForView@InstancedOpaqueDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $unwind$?GetNextItem@InstancedOpaqueDrawStrategy@render@donut@@UEAAPEBUDrawItem@23@XZ $pdata$?GetNextItem@InstancedOpaqueDrawStrategy@render@donut@@UEAAPEBUDrawItem@23@XZ $unwind$?PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $pdata$?PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $cppxdata$?PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $stateUnwindMap$?PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $ip2state$?PrepareForView@TransparentDrawStrategy@render@donut@@UEAAXAEBV?$shared_ptr@VSceneGraphNode@engine@donut@@@std@@AEBVIView@engine@3@@Z $unwind$??$_Sort_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@0_JP6AHPEBU123@2@Z@Z $pdata$??$_Sort_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@0_JP6AHPEBU123@2@Z@Z $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBUDrawItem@render@donut@@V?$allocator@PEBUDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $pdata$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $cppxdata$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $tryMap$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $handlerMap$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $ip2state$??$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUDrawItem@render@donut@@@?$vector@UDrawItem@render@donut@@V?$allocator@UDrawItem@render@donut@@@std@@@std@@AEAAPEAUDrawItem@render@donut@@QEAU234@AEBU234@@Z@4HA $unwind$??$_Partition_by_median_guess_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YA?AU?$pair@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@0@PEAPEBUDrawItem@render@donut@@0P6AHPEBU234@1@Z@Z $pdata$??$_Partition_by_median_guess_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YA?AU?$pair@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@0@PEAPEBUDrawItem@render@donut@@0P6AHPEBU234@1@Z@Z $unwind$??$_Zero_range@PEAPEBUDrawItem@render@donut@@@std@@YAPEAPEBUDrawItem@render@donut@@QEAPEBU123@0@Z $pdata$??$_Zero_range@PEAPEBUDrawItem@render@donut@@@std@@YAPEAPEBUDrawItem@render@donut@@QEAPEBU123@0@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $pdata$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $chain$1$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $pdata$1$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $chain$2$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $pdata$2$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $chain$3$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $pdata$3$??$_Pop_heap_hole_by_index@PEAPEBUDrawItem@render@donut@@PEBU123@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@_J1$$QEAPEBU123@P6AHPEBU123@3@Z@Z $unwind$??$_Copy_memmove@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@std@@YAPEAPEBUDrawItem@render@donut@@PEAPEBU123@00@Z $pdata$??$_Copy_memmove@PEAPEBUDrawItem@render@donut@@PEAPEBU123@@std@@YAPEAPEBUDrawItem@render@donut@@PEAPEBU123@00@Z $unwind$??$_Copy_memmove@PEAUDrawItem@render@donut@@PEAU123@@std@@YAPEAUDrawItem@render@donut@@PEAU123@00@Z $pdata$??$_Copy_memmove@PEAUDrawItem@render@donut@@PEAU123@@std@@YAPEAUDrawItem@render@donut@@PEAU123@00@Z $unwind$??$_Med3_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@00P6AHPEBU123@1@Z@Z $pdata$??$_Med3_unchecked@PEAPEBUDrawItem@render@donut@@P6AHPEBU123@0@Z@std@@YAXPEAPEBUDrawItem@render@donut@@00P6AHPEBU123@1@Z@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_R0?AVSceneGraphLeaf@engine@donut@@@8 ??_R0?AVMeshInstance@engine@donut@@@8 ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __isa_available __isa_available_default __real@3f000000 __security_cookie __xmm@00000000000000010000000000000000 __xmm@00000000000000300000000000000030 