d嗴 祚Gh� �      .drectve        <  &               
 .debug$S        鸡 H'  �        @ B.debug$T        p   T�             @ B.rdata          @   奈             @ @@.text$mn        :   � >�         P`.debug$S          \� h�        @B.text$mn            粞              P`.debug$S        8  � L�        @B.text$mn           煺              P`.debug$S          髡 �        @B.text$mn           C�              P`.debug$S        �   G� '�        @B.text$mn        3  c� 栙         P`.debug$S        ,
  举 赉     D   @B.text$x            掓 炴         P`.text$x            ㄦ 告         P`.text$x            骆 益         P`.text$mn           苕              P`.debug$S        �   噫 哥        @B.text$mn        �   翮 愯         P`.debug$S        �
  よ 8�     B   @B.text$x            条 仵         P`.text$x            怩 蝓         P`.text$x             �         P`.text$x            � &�         P`.text$x            0� @�         P`.text$x            J� Z�         P`.text$x            d� t�         P`.text$x            ~� 庼         P`.text$mn           橏              P`.debug$S        �   滝 T�        @B.text$mn           |�              P`.debug$S        �   �� D�        @B.text$mn           l�              P`.debug$S        �   p� (�        @B.text$mn        <   P� 岡         P`.debug$S        0   邡     
   @B.text$mn        <   >� z�         P`.debug$S        L  橕 潼     
   @B.text$mn        !   H� i�         P`.debug$S        <  }� 哈        @B.text$mn        2   觜 '�         P`.debug$S        <  ;� w         @B.text$mn        "   �               P`.debug$S        �   �        @B.text$mn        "   I              P`.debug$S        �  k �        @B.text$mn        "   �              P`.debug$S        �  � M        @B.text$mn        "   �              P`.debug$S        �   �	        @B.text$mn        "   K
              P`.debug$S        �  m
 �        @B.text$mn        "   �              P`.debug$S        �  � G        @B.text$mn        "   �              P`.debug$S        �  	 �        @B.text$mn        [   5 �         P`.debug$S          � �        @B.text$mn        }   �          P`.debug$S        �   �        @B.text$mn        K   �              P`.debug$S        �  � �        @B.text$mn        `   P �         P`.debug$S        �  � �"        @B.text$mn        ?   8# w#         P`.debug$S        \  �# �$        @B.text$mn           _% r%         P`.debug$S        �   �% j&        @B.text$mn           �& �&         P`.debug$S        �   �& �'        @B.text$mn        B   �' (         P`.debug$S           5( 5)        @B.text$mn        B   q) �)         P`.debug$S          �) �*        @B.text$mn        B   + _+         P`.debug$S        �   }+ y,        @B.text$mn        H   �,              P`.debug$S        �  �, �.        @B.text$mn        s   �/ L0         P`.debug$S        l  j0 �1     
   @B.text$mn        �  :2 :         P`.debug$S        |  �: 6Y     �   @B.text$x            鎎 騜         P`.text$x            黚 c         P`.text$mn        �	  c 辧         P`.debug$S        D  鈓 &�     �   @B.text$x            Z� f�         P`.text$mn        	   p� y�         P`.debug$S        �   儖 �        @B.text$mn        (   粚              P`.debug$S        \  銓 ?�        @B.text$mn            弾 瘞         P`.debug$S        �   蛶 憦        @B.text$mn           蛷 鄰         P`.debug$S        �   陱 緪        @B.xdata             鷲             @0@.pdata             � �        @0@.xdata             8�             @0@.pdata             @� L�        @0@.xdata             j�             @0@.pdata             v� 倯        @0@.xdata             爲             @0@.pdata             ☉ 磻        @0@.xdata             覒             @0@.pdata             迲 陸        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             :�             @0@.pdata             F� R�        @0@.xdata             p�             @0@.pdata             x� 剴        @0@.xdata                          @0@.pdata             獟 稈        @0@.xdata             話 鋻        @0@.pdata             鴴 �        @0@.xdata          	   "� +�        @@.xdata             ?� E�        @@.xdata             O�             @@.xdata             R� b�        @0@.pdata             v� 倱        @0@.xdata          	   爴         @@.xdata             綋 脫        @@.xdata             蛽             @@.xdata             袚 鄵        @0@.pdata             魮  �        @0@.xdata          	   � '�        @@.xdata             ;� A�        @@.xdata             K�             @@.xdata             P� `�        @0@.pdata             t� ��        @0@.xdata          	   灁         @@.xdata             粩 翑        @@.xdata             藬             @@.xdata             螖 陻        @0@.pdata              
�        @0@.xdata          	   (� 1�        @@.xdata             E� K�        @@.xdata             U�             @@.xdata             X�             @0@.pdata             `� l�        @0@.xdata             姇         @0@.pdata             簳 茣        @0@.xdata          	   鋾 頃        @@.xdata             � �        @@.xdata             ?�             @@.xdata             F� V�        @0@.pdata             j� v�        @0@.xdata          	   敄 潠        @@.xdata             睎 窎        @@.xdata             翓             @@.xdata             臇 詵        @0@.pdata             钖 魱        @0@.xdata          	   � �        @@.xdata             /� 5�        @@.xdata             ?�             @@.xdata             B� R�        @0@.pdata             f� r�        @0@.xdata          	   悧 櫁        @@.xdata             瓧 硹        @@.xdata             綏             @@.xdata             罈             @0@.pdata             葪 詶        @0@.xdata             驐 �        @0@.pdata             $� 0�        @0@.xdata             N� ^�        @0@.pdata             |� 垬        @0@.voltbl                           .xdata                          @0@.pdata             礃 罉        @0@.xdata             迾 鰳        @0@.pdata             
� �        @0@.xdata          	   4� =�        @@.xdata          /   Q� ��     	   @@.xdata             跈             @@.xdata          $   邫 �        @0@.pdata             � #�        @0@.xdata          	   A� J�        @@.xdata             ^� |�        @@.xdata          4   畾             @@.xdata          (   鈿 
�        @0@.pdata             � *�        @0@.xdata          	   H� Q�        @@.xdata             e� q�        @@.xdata             厸             @@.xdata             悰 牄        @0@.pdata             礇 罌        @0@.xdata          	   逈 鐩        @@.xdata             麤 �        @@.xdata             �             @@.xdata             �             @0@.pdata             � "�        @0@.rdata             @� X�        @@@.rdata             v�             @@@.rdata             垳 牅        @@@.rdata             緶 譁        @@@.rdata             魷             @@@.xdata$x           	� %�        @@@.xdata$x           9� U�        @@@.data$r         /   s�         @@�.xdata$x        $   瑵 袧        @@@.data$r         $   錆 �        @@�.xdata$x        $   � 6�        @@@.data$r         $   J� n�        @@�.xdata$x        $   x� 湠        @@@.rdata             盀             @@@.data               罏             @ @�.rdata             酁 鵀        @@@.rdata             �             @@@.rdata             0�             @0@.rdata          '   5�             @@@.rdata             \�             @@@.rdata          `   m�             @P@.rdata          F   蜔             @P@.rdata          L   �             @P@.rdata          b   _�             @P@.rdata$r        $   翣 鍫        @@@.rdata$r           � �        @@@.rdata$r           !� -�        @@@.rdata$r        $   7� [�        @@@.rdata$r        $   o� 摗        @@@.rdata$r           薄 拧        @@@.rdata$r           稀 恪        @@@.rdata$r        $   鳌 �        @@@.rdata$r        $   /� S�        @@@.rdata$r           q� 參        @@@.rdata$r           彚         @@@.rdata$r        $   散 恝        @@@.rdata$r        $   � %�        @@@.data$rs        8   C� {�        @@�.rdata$r           叄 櫍        @@@.rdata$r           ＃         @@@.rdata$r        $   梗 荩        @@@.rdata             瘢             @P@.rdata             �             @P@.rdata             �             @P@.rdata             !�             @P@.rdata             1�             @P@.debug$S        L   A� 崵        @B.debug$S        4   · 栅        @B.debug$S        4   椁 �        @B.debug$S        @   1� q�        @B.chks64         �  叆              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   U  r     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\DeferredLightingPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $vfs  $math 	 $colors  $log 	 $render  $Json 	 $stdext    �   ,�  ( �   donut::math::vector<int,4>::DIM % #   std::ctype<char>::table_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment O #   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align " ;    std::memory_order_relaxed + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_consume " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits Z %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size M #   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R %   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size - �    std::chrono::system_clock::is_steady   8   _Mtx_try   8   _Mtx_recursive E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment \ #   std::allocator<donut::render::DrawItem const *>::_Minimum_asan_allocation_alignment $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den  �8   std::_INVALID_ARGUMENT  �8   std::_NO_SUCH_PROCESS & �8   std::_OPERATION_NOT_PERMITTED , �8   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - �8   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size i #   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,2>::value  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 " %   std::_Iosb<int>::internal N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit ( r  ��枠 std::ratio<10000000,1>::num   %   std::_Iosb<int>::badbit $ r   std::ratio<10000000,1>::den  %   std::_Iosb<int>::in ( �   donut::math::vector<int,2>::DIM  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2   %    std::_Iosb<int>::binary L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot � #   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment < r  ��枠 std::integral_constant<__int64,10000000>::value 1 r   std::integral_constant<__int64,1>::value 1 �   donut::math::vector<unsigned int,2>::DIM h #   std::allocator<std::shared_ptr<donut::engine::LightProbe> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable - �   std::chrono::steady_clock::is_steady & r   std::ratio<1,1000000000>::num x #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment * r  � 蕷;std::ratio<1,1000000000>::den _ #   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment Z�    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]�   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity � #   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo 1 �   donut::math::vector<unsigned int,4>::DIM a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard 1 �   donut::math::vector<unsigned int,3>::DIM � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard R #   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g�    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi 8 �   std::atomic<unsigned long>::is_always_lock_free # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den A #   std::allocator<bool>::_Minimum_asan_allocation_alignment / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment a #   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable  %    LightType_None  %   LightType_Directional  %   LightType_Spot  %   LightType_Point \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment 7 �   std::atomic<unsigned int>::is_always_lock_free  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN � #   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 6 �   std::_Iterator_base0::_Unwrap_when_unverified �    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi �   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard 7 �   std::_Iterator_base12::_Unwrap_when_unverified � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment 3   \ std::filesystem::path::preferred_separator  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment O #   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment - %    std::integral_constant<int,0>::value t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets '�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment # �        nvrhi::AllSubresources #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment ) �   donut::math::vector<bool,4>::DIM Z #   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment \ #   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits . �   std::integral_constant<bool,1>::value L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos   3        nvrhi::EntireBuffer Z #   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment j #   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos = �   donut::engine::c_MaxRenderPassConstantBufferVersions . %   donut::math::box<float,2>::numCorners x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment 2 �  �����std::shared_timed_mutex::_Max_readers c #   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment q #   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment 5 �    std::filesystem::_File_time_clock::is_steady � #   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment ��    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi ��   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard � #   std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi �   std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard � #   std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment Z #   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment q #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment h #   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi A #   std::allocator<char>::_Minimum_asan_allocation_alignment : #    std::integral_constant<unsigned __int64,0>::value B #   std::allocator<float>::_Minimum_asan_allocation_alignment i #   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment /#   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size /#   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets )�    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi $ %   std::_Locbase<int>::collate " %   std::_Locbase<int>::ctype % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE ! %    std::_Locbase<int>::none A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask : #   std::integral_constant<unsigned __int64,1>::value e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment - 9   std::_Invoker_pmf_refwrap::_Strategy - 9   std::_Invoker_pmf_pointer::_Strategy , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos l #   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients c #   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment * �   donut::math::vector<float,2>::DIM  �?   std::_Consume_header  �?   std::_Generate_header . %   donut::math::box<float,3>::numCorners + �        nvrhi::rt::c_IdentityTransform  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment + �   donut::math::vector<double,3>::DIM 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment 3 %   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity 0 �   std::numeric_limits<wchar_t>::is_modulo - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 j #   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask + �   donut::math::vector<double,4>::DIM F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits �   �  6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10   �     , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 T #   std::allocator<donut::render::DrawItem>::_Minimum_asan_allocation_alignment 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �   S� M #   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment R #   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  5,  _Thrd_result  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34  D<  _Stl_critical_section 	 !  tm %  7  _s__RTTICompleteObjectLocator2  鰟  LightConstants & 鱥  $_TypeDescriptor$_extraBytes_30 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  鼊  ShadowConstants  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � +~  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w -~  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 觹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 2<  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > d 鈣  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c ~  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h ~  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 瞸  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y ~  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � #<  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � <  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鸖  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > c 寋  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 
~  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 諀  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > � 
T  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > ] ~  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ [|  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 鴠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 題  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 鋧  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 諁  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 4<  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > 苶  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 緘  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � ?|  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W祡  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 畗  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 憐  std::_Default_allocator_traits<std::allocator<float> > ; 靭  std::hash<std::shared_ptr<donut::engine::Material> > � l|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > � %<  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ 殅  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � 憓  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ %}  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C }  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > iT  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � }  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 鱸  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 鰔  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � S  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 8 .  std::_Ptr_base<donut::engine::FramebufferFactory> � 珄  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 飢  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 遼  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 瓅  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 讄  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ? 箌  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 z  std::allocator<donut::engine::SkinnedMeshJoint> M M|  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > � T  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 腟  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > c ^�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::LightProbe> > > L 磡  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 瘄  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � |  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 爘  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T 巪  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > W 儂  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem const *> > � x|  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � n|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U ]|  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 鐆  std::_Ptr_base<donut::engine::LoadedTexture> :  <  std::_Vector_val<std::_Simple_types<unsigned int> > D O|  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 踫  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � A|  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 =u  std::_Ptr_base<donut::engine::DescriptorHandle> � 2|  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~(|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e bt  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 蕑  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > � <  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �:  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > "坸  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 噞  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > � 齋  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > d祔  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > �<  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> c �;  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> U .y  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w |  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � |  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y 鋥  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 貃  std::allocator<donut::math::vector<float,2> > M 蓒  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = 秡  std::allocator<donut::math::vector<unsigned short,4> > K 縶  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U  u  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 趚  std::_Ptr_base<donut::engine::BufferGroup> � �;  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > F祘  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ >s  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 杮  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 巤  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e 蘳  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N 墈  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > s k:  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � l9  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > { {{  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l   std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � _:  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > , {  std::allocator<nvrhi::BindingSetItem> K  {  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 鳶  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > � 鰖  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # �8  std::allocator<unsigned int> . 籕  std::_Ptr_base<donut::vfs::IFileSystem> �霺  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � �;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鑪  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 閟  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 魕  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � w  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � ╬  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 釹  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > ��;  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> g 搑  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 蘻  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  u  std::allocator<float> � 緕  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1>   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 爖  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t 4 搝  std::allocator_traits<std::allocator<float> > N 厇  std::allocator_traits<std::allocator<donut::render::DrawItem const *> > [ wz  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > Q�;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � 芐  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > l 7k  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > 稴  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> �;  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > w vq  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > \R  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > � �:  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > ; �8  std::allocator_traits<std::allocator<unsigned int> > [ cz  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 {u  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Yz  std::hash<std::shared_ptr<donut::engine::MeshInfo> > O 0s  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem> > WUz  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> E Nz  std::_Vector_val<std::_Simple_types<donut::render::DrawItem> > � Dz  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H 蕆  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ mh  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> �  z  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 鎟  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X 	z  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> > . Ni  std::integer_sequence<unsigned __int64>  �  std::_Lockit  
-  std::timed_mutex � 訽  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > �   std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy � [;  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * C2  std::hash<enum nvrhi::ResourceType> / 豜  std::shared_ptr<donut::engine::Material> - 払  std::reverse_iterator<wchar_t const *> 5 鋂  std::shared_ptr<donut::engine::SceneGraphNode> 9 肵  std::shared_ptr<donut::engine::animation::Sampler> " H6  std::_Char_traits<char,int> Z `�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::LightProbe> > >  p>  std::_Fs_file � 琒  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � z  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 聎  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > � 淪  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >  �(  std::_Big_uint128  �,  std::condition_variable � I;  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > / 沑  std::weak_ptr<donut::engine::SceneGraph> 騳  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) U6  std::_Narrow_char_traits<char,int> i 扴  std::initializer_list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > L 0y  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 觟  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  z  std::hash<float> E N  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 芢  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > > R �.  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > 嘢  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > " 5;  std::_Align_type<double,64>  f+  std::hash<int>  �5  std::_Num_int_base  錊  std::ctype<wchar_t> " �+  std::_System_error_category � y  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / a2  std::_Conditionally_enabled_hash<bool,1> 2 髕  std::shared_ptr<donut::engine::BufferGroup> � 莤  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::float_denorm_style 4  x  std::shared_ptr<donut::engine::LoadedTexture>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 8g  std::piecewise_construct_t ! �<  std::_Ptr_base<std::mutex> �銵  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 襴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . 'Z  std::_Ptr_base<donut::engine::MeshInfo> 6 2;  std::allocator_traits<std::allocator<wchar_t> >  4=  std::shared_timed_mutex & N  std::equal_to<unsigned __int64> � 蕎  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  &  std::bad_cast B [  std::enable_shared_from_this<donut::engine::SceneGraphNode>  玁  std::equal_to<void> 4 s  std::allocator<donut::math::vector<float,4> > 3 yI  std::_Ptr_base<donut::engine::ShaderFactory> � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } 耟  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 恅  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 硍  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 亀  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o hh  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � je  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > 6 諭  std::initializer_list<nvrhi::BindingLayoutItem> " �5  std::numeric_limits<double>  C&  std::__non_rtti_object < 裋  std::_Ptr_base<donut::engine::DescriptorTableManager> ( 0  std::_Basic_container_proxy_ptr12 � �.  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> � 漄  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 4 <w  std::allocator<donut::math::vector<float,3> > � g  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � *\  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � 鵞  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > 0;  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8> T -w  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �5  std::_Num_float_base � #w  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  *  std::logic_error 3   std::weak_ptr<donut::engine::SceneGraphNode> � Lg  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � �:  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > � 飃  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety P w  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 辷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �:  std::char_traits<char32_t> � S  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id ?   std::allocator_traits<std::allocator<unsigned __int64> > : 揨  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 1e  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z   std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> _ 檝  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u hv  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P�0  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl .   std::_Ptr_base<donut::engine::Material> * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> � �.  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � �:  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error d *v  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 鴘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy . 硊  std::initializer_list<unsigned __int64> % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t> Y A�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::LightProbe> > >  ~,  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> � 
S  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � �:  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � >  std::unordered_map<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � �:  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> 7 攗  std::shared_ptr<donut::engine::SceneTypeFactory> � fu  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � ^u  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 纃  std::allocator<unsigned __int64>  h:  std::false_type S �:  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #�0  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ai  std::_Default_allocator_traits<std::allocator<unsigned __int64> > ! K,  std::hash<std::thread::id>  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � bk  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  �,  std::adopt_lock_t � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 Vu  std::shared_ptr<donut::engine::DescriptorHandle> , �5  std::numeric_limits<unsigned __int64> � *u  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L "u  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > 9 3.  std::shared_ptr<donut::engine::FramebufferFactory> \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H 4m  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> � 鯮  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16> � �.  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void> f 識  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  ;  std::string_view  w  std::wstring_view � �0  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound � 奼  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >  `,  std::_Mutex_base b u  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec �   std::_Compressed_pair<std::allocator<donut::render::DrawItem>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem> >,1> D 鰐  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  v:  std::defer_lock_t   �*  std::_Init_once_completer �'  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � �(  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �,  std::scoped_lock<> + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 � 蜶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > j wX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � EX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > � 贚  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > � 癵  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ m  std::_Atomic_integral<long,4> � CQ  std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � lt  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  e2  std::hash<bool> � 跼  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ dt  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > 2 iJ  std::initializer_list<nvrhi::IBindingSet *> > 俕  std::enable_shared_from_this<donut::engine::SceneGraph> " -  std::lock_guard<std::mutex> K Vt  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > dn  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> > � u:  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > k 賀  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �  std::hash<long double> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy 2 �1  std::equal_to<nvrhi::TextureSubresourceSet> W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � �3  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o �.  std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / =Z  std::shared_ptr<donut::engine::MeshInfo> M  std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>  s:  std::try_to_lock_t � 1  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> H 鬒  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> 5 S\  std::shared_ptr<donut::engine::SceneGraphLeaf> O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> , 甦  std::allocator<std::_Container_proxy> D r:  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> " �,  std::_Align_type<double,72> x m:  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle> b 礙  std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception & �.  std::_Zero_then_variadic_args_t � �0  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > d ps  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ t  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � t  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string 鱯  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � a:  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >  �,  std::cv_status S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 鮯  std::_Vector_val<std::_Simple_types<float> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A 雜  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + G  std::pair<enum __std_win_error,bool> � 9  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void> � 輘  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > >  !,  std::thread  ?,  std::thread::id S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error \ 蝧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder ) ~  std::_Atomic_integral_facade<long> i 蘇  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >  �,  std::mutex 8 琗  std::_Ptr_base<donut::engine::animation::Sampler> Q @P  std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > % }2  std::hash<enum nvrhi::BlendOp> c   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B 梥  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> [ rs  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > M ds  std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> > ) q2  std::hash<enum nvrhi::BlendFactor> 輌  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 骾  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code J W  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano  �  std::_Iterator_base0 � *6  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > | Hs  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � g  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U @s  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0錰  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > jH:  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � 蠷  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 �6  std::_Char_traits<char16_t,unsigned short> 6 3q  std::allocator<donut::render::DrawItem const *> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> F 2s  std::allocator_traits<std::allocator<donut::render::DrawItem> > " =  std::shared_ptr<std::mutex> i ]M  std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> 9 紿  std::shared_ptr<donut::engine::CommonRenderPasses> ! �9  std::char_traits<char16_t> 裧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> � dQ  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > [ $s  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > > � �0  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  "=  std::shared_mutex - 糫  std::weak_ptr<donut::engine::Material>  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 Ri  std::integer_sequence<unsigned __int64,0> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char> N Qm  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X s  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  9  std::_Invoker_strategy  	G  std::nothrow_t � n9  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � s  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T �r  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �5  std::_Default_allocate_traits � $e  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � 苧  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > 0 uo  std::_Ptr_base<donut::engine::IShadowMap> . 膔  std::allocator<donut::render::DrawItem> ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> # �,  std::unique_lock<std::mutex> ( 淲  std::array<nvrhi::BufferRange,11> ; ;  std::basic_string_view<char,std::char_traits<char> > c Gq  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > �  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � 祌  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 琙  std::weak_ptr<donut::engine::SkinnedMeshInstance> , ,�  std::shared_ptr<donut::engine::Light> 9羒  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 漴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 時  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > 0 貐  std::_Ptr_base<donut::engine::LightProbe> . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock Y 噐  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  �  std::bad_alloc � 4�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::LightProbe> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::LightProbe> > >,1>  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  }r  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � Kr  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 5g  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D r  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1> � M  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0>  軦  std::messages_base  r*  std::out_of_range # �5  std::numeric_limits<__int64> _ 鱭  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 苢  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b 坬  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > ~q  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  珸  std::ctype<char> @ bq  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 噈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P Xq  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? Nq  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  ;  std::memory_order Z Iq  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � ;q  std::_Compressed_pair<std::allocator<donut::render::DrawItem const *>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> >,1> � $q  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ! /-  std::recursive_timed_mutex q 9a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy " �<  std::condition_variable_any  縒  std::nullopt_t  罻  std::nullopt_t::_Tag  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState> / 訯  std::shared_ptr<donut::vfs::IFileSystem>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K 竝  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > z 7M  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 莋  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  [9  std::ratio<1,1> 3 ,J  std::initializer_list<nvrhi::BindingSetItem>   �8  std::forward_iterator_tag  �*  std::runtime_error � 猵  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ��0  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   	  std::bad_array_new_length ; �2  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> T 沺  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> > j ip  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Reallocation_policy 1 陞  std::shared_ptr<donut::engine::LightProbe> E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 'p  std::allocator<donut::engine::animation::Keyframe> K p  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > v W9  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool> � [e  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  5  std::u16string _ p  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 謔  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 韋  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *> ]�'  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >  \  std::nested_exception  �  std::_Distance_unknown ) 榦  std::allocator<nvrhi::BufferRange> ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> � 餖  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > , %@  std::codecvt<char32_t,char,_Mbstatet> | QM  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 1 噊  std::shared_ptr<donut::engine::IShadowMap> C 蝑  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F e  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 bo  std::vector<float,std::allocator<float> > F 0o  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 j\  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> � �0  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc } 賒  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J ym  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  f_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 4_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy ; {.  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > � 鬾  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , EO  std::default_delete<std::_Facet_base>  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::_UInt_is_zero y 篕  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void>  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> C 踤  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . �-  std::vector<bool,std::allocator<bool> > J 蘮  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 沶  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> i ]n  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 9  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  �8  std::ratio<1,10000000> Sn  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d i  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag j 塵  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A {m  std::allocator_traits<std::allocator<unsigned __int64 *> >  �/  std::allocator<char> �=  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > T jm  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> d 0m  std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> > z   std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Reallocation_policy G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> { 脜  std::vector<std::shared_ptr<donut::engine::LightProbe>,std::allocator<std::shared_ptr<donut::engine::LightProbe> > > � 拝  std::vector<std::shared_ptr<donut::engine::LightProbe>,std::allocator<std::shared_ptr<donut::engine::LightProbe> > >::_Reallocation_policy ) f  std::allocator<unsigned __int64 *>    std::nullptr_t =MY  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > Lh  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K)h  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & �8  std::random_access_iterator_tag 4 怚  std::shared_ptr<donut::engine::ShaderFactory> ; cM  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  x  std::wstring z 烸  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  -*  std::domain_error � 鷊  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  �  std::u32string_view � Me  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 俓  std::shared_ptr<donut::engine::SceneGraph>  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 閗  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 穔  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z ]k  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; 鏥  std::weak_ptr<donut::engine::DescriptorTableManager> $ 72  std::hash<nvrhi::IResource *> 4 =\  std::_Ptr_base<donut::engine::SceneGraphLeaf> " 轜  std::_Nontrivial_dummy_type � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet> � />  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage  /N  std::_Wrap<std::mutex> � K`  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � `  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! �+  std::_System_error_message  �  std::_Unused_parameter " �2  std::hash<nvrhi::IShader *> = sk  std::allocator<std::shared_ptr<donut::engine::Light> > h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  諻  std::bad_optional_access A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> � �0  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> 8   std::_Ptr_base<donut::engine::CommonRenderPasses> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q �1  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > z mM  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1> � dk  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  鴋  std::_Exact_args_t � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > Q _k  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � �0  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > � Qk  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base b Ck  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' Y%  std::hash<nvrhi::BindingSetItem> + �  std::_Ptr_base<donut::engine::Light> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> c 9k  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> � f^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 4^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> �圷  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ^ �4  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base � 0O  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *>  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 |Z  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ' #N  std::_Ref_count_obj2<std::mutex> v 1  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> ' 訥  std::hash<std::filesystem::path> 	CM  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> B -�  std::allocator<std::shared_ptr<donut::engine::LightProbe> > � M  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > >   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 蘗  std::_Ptr_base<donut::engine::SceneGraphNode> � �6  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m m(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � ;(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy � 蒳  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � 襤  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> �|g  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E @]  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O #]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U !]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t & �i  $_TypeDescriptor$_extraBytes_40 # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling " �  nvrhi::SamplerReductionType $ �$  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �  nvrhi::ResourceStates . �5  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  �!  nvrhi::GraphicsState * �8  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16> ! �  nvrhi::SharedResourceFlags  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc 2 攡  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  �8  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # RW  nvrhi::DescriptorTableHandle  :  nvrhi::ShaderType  �$  nvrhi::TimerQueryHandle 2 RW  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # 攡  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �5  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  ]!  nvrhi::IBindingLayout  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # +k  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline    nvrhi::CpuAccessMode  �$  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �"  nvrhi::ComputeState 2 +k  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �!  nvrhi::IFramebuffer  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t 
 �,  _Cnd_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats * fZ  donut::engine::SkinnedMeshReference   �  donut::engine::LightProbe ! 籞  donut::engine::SceneCamera   _b  donut::engine::IShadowMap $ H  donut::engine::ICompositeView  ?H  donut::engine::IView ( �7  donut::engine::CommonRenderPasses 5 �&  donut::engine::CommonRenderPasses::PsoCacheKey ; �&  donut::engine::CommonRenderPasses::PsoCacheKey::Hash $ �7  donut::engine::BlitParameters $ X[  donut::engine::SceneGraphNode 0 ![  donut::engine::SceneGraphNode::DirtyFlags " Z  donut::engine::MeshInstance   fH  donut::engine::PlanarView ) ZZ  donut::engine::SkinnedMeshInstance ( �&  donut::engine::FramebufferFactory   籢  donut::engine::SceneGraph > 颺  donut::engine::ResourceTracker<donut::engine::MeshInfo>  哘  donut::engine::ViewType $ 
H  donut::engine::ViewType::Enum ( h]  donut::engine::AnimationAttribute $ 筜  donut::engine::SceneGraphLeaf ! uW  donut::engine::BufferGroup  榙  donut::engine::Material *  k  donut::engine::Material::HairParams 0 黬  donut::engine::Material::SubsurfaceParams ! H  donut::engine::ShaderMacro # 荌  donut::engine::ShaderFactory  語  donut::engine::Light ' ℡  donut::engine::SceneContentFlags  礧  donut::engine::MeshInfo & 鎆  donut::engine::DirectionalLight & \]  donut::engine::SceneGraphWalker ( X  donut::engine::animation::Sampler ) 鴍  donut::engine::animation::Keyframe ) 橷  donut::engine::animation::Sequence    donut::engine::MeshType  鯶  donut::engine::SpotLight " >=  donut::engine::BindingCache & 竀  donut::engine::DescriptorHandle , &W  donut::engine::DescriptorTableManager B 鱒  donut::engine::DescriptorTableManager::BindingSetItemsEqual B 餠  donut::engine::DescriptorTableManager::BindingSetItemHasher % _W  donut::engine::VertexAttribute 0 圿  donut::engine::SceneGraphAnimationChannel % t   donut::engine::DescriptorIndex > 誢  donut::engine::ResourceTracker<donut::engine::Material>   [  donut::engine::PointLight ) 鮙  donut::engine::SceneGraphAnimation " nH  donut::engine::StaticShader  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  �.  donut::math::box2  /  donut::math::float2  }[  donut::math::dquat # �  donut::math::vector<float,3> * �7  donut::math::vector<unsigned int,3>  蕕  donut::math::int4  u   donut::math::uint  �  donut::math::plane ! 蕕  donut::math::vector<int,4>  瞇  donut::math::daffine3  燵  donut::math::double3 # �  donut::math::vector<float,4> $ 燵  donut::math::vector<double,3>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes  �&  donut::math::uint2 $ }j  donut::math::vector<double,4>  �  donut::math::float4 & 	e  donut::math::matrix<double,3,3>   d  donut::math::int2 % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3> !  d  donut::math::vector<int,2>   �.  donut::math::box<float,2>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> * �&  donut::math::vector<unsigned int,2> * �7  donut::math::vector<unsigned int,4> $ 瞇  donut::math::affine<double,3> & }[  donut::math::quaternion<double> - 宎  donut::render::PassthroughDrawStrategy 1   donut::render::InstancedOpaqueDrawStrategy * 爠  donut::render::DeferredLightingPass 2 �  donut::render::DeferredLightingPass::Inputs # va  donut::render::IDrawStrategy * �&  donut::render::GBufferRenderTargets M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray  �  PlanarViewConstants 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec  u   _Thrd_id_t - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t & xQ  $_TypeDescriptor$_extraBytes_41  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24  h,  _Mtx_internal_imp_t & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 
 \,  _Mtx_t 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result  �  LightProbeConstants  %,  _Thrd_t - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t   �  DeferredLightingConstants  1  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers  �   �      �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  C    天e�1濎夑Y%� 褡\�Tā�%&閜�  �    隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �    揾配饬`vM|�%
犕�哝煹懿鏈椸     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  e   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴     M]S噴=泥G)w��!&鍌S硚YQD铢g�/  W   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  )   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  g   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  2   iqF�T詂瑠���>�顉磱咬ws  w   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   eSO僌rM騮纚坵*L犁�L宵�)魐�  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  /   傊P棼r铞
w爉筫y;H+(皈LL��7縮  |   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   郖�Χ葦'S詍7,U若眤�M进`  !    d蜯�:＠T邱�"猊`�?d�B�#G騋  ]   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  3   `k�"�1�^�`�d�.	*貎e挖芺
脑�  u   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   �暊M茀嚆{�嬦0亊2�;i[C�/a\  �   26fk飓q�<h哶+俇�陮
惹夌容S�  '   �0�*е彗9釗獳+U叅[4椪 P"��  b   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   5/�+�-� �/�<f.幃鳐杨p0=麔�  %	   j轲P[塵5m榤g摏癭 鋍1O骺�*�  n	   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �	   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �	   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  '
   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  `
   繃S,;fi@`騂廩k叉c.2狇x佚�  �
   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �
   +YE擋%1r+套捑@鸋MT61' p廝 飨�  5   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  x   =J�(o�'k螓4o奇缃�
黓睆=呄k_  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  0   �*o驑瓂a�(施眗9歐湬

�  x    I嘛襨签.濟;剕��7啧�)煇9触�.  �   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  6
   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �
   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �
   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�     攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  ]   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  ?   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  |   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  >   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   f扥�,攇(�
}2�祛浧&Y�6橵�  �   曀"�H枩U传嫘�"繹q�>窃�8     �X�& 嗗�鹄-53腱mN�<杴媽1魫  R   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  6   [届T藎秏1潴�藠?鄧j穊亘^a  u   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�     o�椨�4梠"愜��
}z�$ )鰭荅珽X  Z   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   k�8.s��鉁�-[粽I*1O鲠-8H� U     N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  \   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   dhl12� 蒑�3L� q酺試\垉R^{i�  "   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  `   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  (   +椬恡�
	#G許�/G候Mc�蜀煟-  h   L�9[皫zS�6;厝�楿绷]!��t  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  6   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  w   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  P   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  %   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  g   +4[(広
倬禼�溞K^洞齹誇*f�5  �    狾闘�	C縟�&9N�┲蘻c蟝2      �"鈖@M�骑潆譢aMy1绾鎕瑞lg  B   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  z   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  O   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �   交�,�;+愱`�3p炛秓ee td�	^,  )   zY{���睃R焤�0聃
扨-瘜}  b   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   険L韱#�簀O闚样�4莿Y丳堟3捜狰  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  )   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  h   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  "   猯�諽!~�:gn菾�]騈购����'  ^   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   副謐�斦=犻媨铩0
龉�3曃譹5D      娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  g   �&�$禤會k呟u#�碟`Gy癥襲櫏  �   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;      
訍癿褎9P巵┠蝫虵艽"漒蘕聋  X    唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �    J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �    E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  !!   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  i!   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �!   +FK茂c�G1灈�7ほ��F�鳺彷餃�  �!   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  $"   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  a"   咡68[�沘謎7
瑫,j蟫堢>�`~乐�#  �"   _O縋[HU-銌�鼪根�鲋薺篮�j��  �"   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  7#   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �#   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �#   蜅�萷l�/费�	廵崹
T,W�&連芿  $   v�%啧4壽/�.A腔$矜!洎\,Jr敎  Z$   D���0�郋鬔G5啚髡J竆)俻w��  �$   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �$   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  -%   匐衏�$=�"�3�a旬SY�
乢�骣�  w%   チ畴�
�&u?�#寷K�資 +限^塌>�j  �%   悯R痱v 瓩愿碀"禰J5�>xF痧  �%   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  8&   矨�陘�2{WV�y紥*f�u龘��  &   妇舠幸佦郒]泙茸餈u)	�位剎  �&   穫農�.伆l'h��37x,��
fO��  �&   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  5'   靋!揕�H|}��婡欏B箜围紑^@�銵  u'   彲 ㄋ牐媝e哟�<N7%�Yn�52�峟�  �'   k&�2箍�#た↗�U嬗醇芧'l�-G恇|:  �'   �颠喲津,嗆y�%\峤'找_廔�Z+�  6(   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  x(   ii(祍P%舌+;�'縿E╪嫏脈3L籾
�  �(   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  �(   t�j噾捴忊��
敟秊�
渷lH�#  5)   W簅r�hc鷟;9攫�.�6Y柦阁��}.J╗  )   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �)   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �)   �'稌� 变邯D)\欅)	@'1:A:熾/�  @*   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  z*   v峞M� {�:稚�闙蛂龣 �]<��  �*   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �*   鏀q�N�&}
;霂�#�0ncP抝  6+   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �+   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �+   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  ,   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  d,   ,┭0甗�+天没2骟Bw蛁�%"艠E�  �   �      f     B   g     H   h     Y   m     �   �  �  U   �  �  �   �  0  \   �  �  �  �  �  �  u  �  �   �  �  q   �  �  �   �  �  q   �  �  q   �  �  �  �  �  B  �  �  �	  �  �  �   �  �    �  �    �  �  �   �  �    �  �  �     �  �    �  �    �  @
    �  +
    �  �    �  �    x
  K   (  �  �   )  �  �   B  �  D
  D  �  �  F  �  O   G  �  0   `  �  �  �  �  �   �  �  �  �  �  �   �  �  @   �  �  �     �  �   
  �  �     �  �    �  �  L  �  s  M  �  �  �  �  )
  �  �  �     �  �  8  �  �   >  �  �  g  �  �   `  0  n  a  0  w  b  0  x  c  0    d  0  L  e  0  [  s       y  �    z  �  �   {  �  �   �  �  �   �  �  d  �    j   �  X  >  �  �  @   �  �  5   �  �  @   �  �  5   �  �  �   �  �    �  �  �   �  �  @   �  �  5   �  �  @   �  �  5   �  �  5  &  �    '  �  �   (  �  �   ,  �  �   2  �  t  3  X  4  4  X  u  5  �  �  :  �  q   m  X    n  X    o  �  �  s  �  "  t    1   u  �  �   �  �  %   �  X  
  �  �  �  �  �  '  �  �  �  
  �  :  (  X    )  �  C  *  �  3  +  �  �  6  �  �  8  8	  �  9  �  �  =  �  @   E  �  F  d  �  a  f  �  �  z  �  �  �  �  �  �  �  <  �  �  R  �  �  �  '  �  �  �#  �    �#  �  �   �#  �  �   �#  �    �#  �  �    $  �  �   X$  0  o  Y$  0  %  &    m   &  0  �  &  0  �   &  0  �  /&  �  �   3&  �  �  5&  �    6&  �  �   7&  �  �   K&  �    L&  �  �   M&  �  �   T&  �  <
  ]&  �  S   f&  �  �  �   -   D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\External\Donut\include\donut\shaders\deferred_lighting_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\RTXPT\External\Donut\include\donut\shaders\light_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\include\donut\engine\View.h D:\RTXPT\External\Donut\src\render\DeferredLightingPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\render\DeferredLightingPass.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\RTXPT\External\Donut\include\donut\render\GBuffer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\RTXPT\External\Donut\include\donut\engine\ShadowMap.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\RTXPT\External\Donut\include\donut\engine\BindingCache.h D:\RTXPT\External\Donut\include\donut\render\DrawStrategy.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\shared_mutex D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\condition_variable D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\DeferredLightingPass.obj �       L�&  瞸  �   秥  �  
 <�  �   @�  �  
 ~�      偂     
            
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  6   w  6  
 �     �    
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  mG                       6        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >襆   _First  AJ          AJ       
   >襆   _Last  AK          
 >萀   _Val  AP           >0O   _Backout  CJ            CJ          
   M        E    N M        �   N                        H & h   7  C  D  E  �  �  (  )      襆  O_First     襆  O_Last     萀  O_Val  O�   H               �     <       � �    � �   � �   � �   � �   � �,       0      
 �      �     
 �      �     
 �      �     
 �      �     
            
 "      &     
 �      �     
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AJ                                 H�     .!  Othis  O   �   0              �     $       �  �    �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H嬃�   �   �   S G                      G&        �donut::math::vector<float,4>::vector<float,4> 
 >�   this  AJ                                 H     �  Othis  O   �                  �            �  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 H塡$H塴$ H塋$VWAVH冹 L嬹H�H呉t
H�H嬍�P怚峷H塼$H3韷.H塶H塶峂 �    H� H堾H塅H塶H塶 H塶(H荈0   H荈8   �  �?H媈嬐嬇H柳H凐sv箑   �    H孁H婲H婩(H+罤柳H吚t.H��    H侜   rH兟'L婣鳬+菻岮鳫凐w^I嬋�    H墌H崌�   H塅 H塅(H;鴗$怘�H兦H;鴘綦H兞H灵H吷t3�H嬅驢獻塶HI嬈H媆$PH媗$XH兡 A^_^描    怑   �    �   �    �   �    .  �       �   �  O G            3     3  r        �donut::engine::BindingCache::BindingCache 
 >6=   this  AJ          AV         D@    >�$   device  AK        +  AK ,       M        s  B� N M        �  ��5#��=
 >�=   this  AL  0       BH   5     � ! M        4  5+Hy#z=9 M        m  zh&M/E.$'$,/ >#   _Oldsize  AH  �     �  l  AH       C       ~       >襆    _Newend  AH  �       AH       >#    _Oldcapacity  AH  �     ,    AH �       >襆    _Newvec  AM  �     � [ =  AM �     ;    M        �  z N M        n  �� N M        �  
�� M        �  
�� M        �  
��
 Z   �   N N N M        6  ��# >0O   _Backout  CM     �       CM    �     ;    M        E  �� N M        �  �� N N M        o  .���� M        �  ��)[
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  �     � & V % M        �  ��d#
^
 Z   q   >#    _Ptr_container  AP  �     p  [  AP �       >#    _Back_shift  AJ  �     � 9 [  AJ �     [ ,   N N N M        8  .� N N M        (  T M        �  T M        �  T N N N M        s  9 M        �  A(# >G=    _Newhead  AH  I     5  M        �  A M        �  A M        �  A
 Z   �   N N N N M        9  9 M        d  9 N N N M        t  5 N N N M        �   M          	 N N                      0@ � h9   �  �  �  �  �  �  �  �  �  �    �  �  �  s  �  4  6  7  l  m  n  o  p  q  r  s  t    �  �  �  �  �  '  (  +  6  7  8  9  <  =  C  D  E  d  z  {  �  �  �  �  �    (  )         $LN158  @   6=  Othis  H   �$  Odevice  9(       �   O   �   0           3  p     $       2  �   1  �,   2  ��   �   ^ F                                �`donut::engine::BindingCache::BindingCache'::`1'::dtor$0 
 >6=   this  EN  @                                  �  O   �   �   ^ F                                �`donut::engine::BindingCache::BindingCache'::`1'::dtor$4 
 >6=   this  EN  @                                  �  O   �   �   ^ F                                �`donut::engine::BindingCache::BindingCache'::`1'::dtor$5 
 >6=   this  EN  @                                  �  O   ,   �    0   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 %  �    )  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
 '  �    +  �   
 ;  �    ?  �   
 ]  �    a  �   
 q  �    u  �   
 J  �    N  �   
 ^  �    b  �   
   �      �   
 #  �    '  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  *   �  *  
 �  �    �  �   
 �  �    �  �   
 D     H    
 �     �    
 �  	   �  	  
 O	  	   S	  	  
 �	     �	    
 �	     
    
 H媻@   �       �    H媻H   H兞�       �    H媻H   H兞�       �    H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >C   this  AJ                                 H     C  Othis  O   �                  0             �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 H塡$L塂$H塋$UVWH冹 I嬸H孃H嬞H�    H�H塓H呉t
H�H嬍�P�3鞨塳H塳H塳 H塳(H塳0H塳8H岾@H嬜�    H壂�   H壂�   H�H墐�   H婩H墐�   H�.H塶H嬅H媆$HH兡 _^]�"   �   ^   �       �   �  _ G            �      �   #&        �donut::render::DeferredLightingPass::DeferredLightingPass 
 >墑   this  AI       u  AJ          D@    >�$   device  AK          AM       }  >綡   commonPasses  AL       �  AP          DP    M        �  b M        
  p*K N M        �  �b N N M        �  R N M        �#  N N M        {  J N M        7&  F N M        7&  B N M        �  < N M        �  ) M          -	 N N
 Z   r                        @ B h   �  �  �    {  �  �  �  �  �  2  �  
  �#  7&   @   墑  Othis  H   �$  Odevice  P   綡  OcommonPasses  98       �   O �   H           �   �     <       D  �)   A  �<   D  �V   B  �b   C  ��   E  ��   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$0 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$1 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$2 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$3 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$4 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$5 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$6 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O�   �   n F                                �`donut::render::DeferredLightingPass::DeferredLightingPass'::`1'::dtor$7 
 >墑   this  EN  @           >綡   commonPasses  EN  P                                  �  O,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 �  �    �  �   
   �      �   
 |     �    
 �     �    
          
 d     h    
 �     �    
 �     �    
 L     P    
 �     �    
 �     �    
 4     8    
 �     �    
 �     �    
   
      
  
 �  
   �  
  
 �  
   �  
  
          
 o     s    
 �     �    
 �     �    
 W	     [	    
 �	     �	    
 �	     �	    
 ?
     C
    
 j
     n
    
 H媻P   �       �    H媻@   H兞�       �    H媻@   H兞�       �    H媻@   H兞�       �    H媻@   H兞 �       �    H媻@   H兞(�       �    H媻@   H兞0�       �    H媻@   H兞8�       �    H嬃�   �   �   D G                      (&        �LightConstants::LightConstants 
 >髣   this  AJ                                 H  h   %  .&      髣  Othis  O  ,   �    0   �   
 i   �    m   �   
 H嬃�   �   �   N G                      *&        �LightProbeConstants::LightProbeConstants 
 >�   this  AJ                                 H  h   �  0&  G&      �  Othis  O,   �    0   �   
 s   �    w   �   
 H嬃�   �   �   F G                      )&        �ShadowConstants::ShadowConstants 
 >鶆   this  AJ                                 H  h   6  H      鶆  Othis  O,   �    0   �   
 k   �    o   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   Z   %   �    ,   `      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   Z   %   �    ,   c      �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   f      c      �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !         ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   Z   %   �       �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2         $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AH         AJ          AH        M        �  GCE
 >D     temp  AJ  
       AJ        N (                     0H� 
 h   �   0   .!  Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 P  �    T  �   
 h  �    l  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >3J   this  AH         AJ          AH        M        ,  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   ,   0   3J  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
 J  �    N  �   
 d  �    h  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         z        �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >?I   this  AH         AJ          AH        M        (  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   (   0   ?I  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   X  x G            "         �#        �nvrhi::RefCountPtr<nvrhi::IComputePipeline>::~RefCountPtr<nvrhi::IComputePipeline> 
 >q~   this  AH         AJ          AH        M         $  GCE
 >�"    temp  AJ  
       AJ        N (                     0H� 
 h    $   0   q~  Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 T  �    X  �   
 l  �    p  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         u        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >�&   this  AH         AJ          AH        M        �  GCE
 >�$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �&  Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         6&        �nvrhi::RefCountPtr<nvrhi::ISampler>::~RefCountPtr<nvrhi::ISampler> 
 >�8   this  AH         AJ          AH        M        M&  GCE
 >M    temp  AJ  
       AJ        N (                     0H� 
 h   M&   0   �8  Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�    this  AH         AJ          AH        M        )  GCE
 >;     temp  AJ  
       AJ        N (                     0H� 
 h   )   0   �   Othis  9       �   O  �   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �   �  �G            [      [   �        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 
 > >   this  AI  	     R K   AJ        	 " M        3  )H1%
 M        o  *= M        �  )
 Z     
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z   q   >#    _Ptr_container  AP  )     1    AP =       >#    _Back_shift  AJ       N 1   AJ =       
  N N N M        n   N N                       H� " h   �  �  3  l  n  o  '         $LN30  0    >  Othis  O   �   8           [   X     ,       > �	   ? �O   D �U   ? �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 R  �    V  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 "  �    &  �   
 �  (   �  (  
 �  �    �  �   
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯H吷tH塳H��P惡    H嬎�    H嬤H�u院    H�H媆$0H媗$8H媡$@H兡 _�    P   �    y   �       �   Z  �G            }      d   �        �std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 >>   this  AJ          AL       \   M        5   M        *  
\ M        f  \ M        �  \ N N N' M        )  I*
 >G=   _Head  AI         >G=    _Pnode  AI  &     C  >G=    _Pnext  AM  3     )  AM 0     H  )  M        �  3
 M        *  

G M        f  
G M        �  
G
 Z      N N N M        '  3 M        �  3 M        ,  3DE
 >�!    temp  AJ  7       AJ G       N N N N N N                      0@� F h   �  �  �  �  ,  5  q  )  *  f  �  �  &  '  .  1   0   >  Othis  9C       �   O  �   8           }   �     ,        �    �d    �x    �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 V  �    Z  �   
 p  �    t  �   
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �        �std::shared_ptr<donut::engine::CommonRenderPasses>::~shared_ptr<donut::engine::CommonRenderPasses> 
 >慔   this  AJ        +  AJ @       M        2  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  2   0   慔  Othis  9+       b&   9=       b&   O  �   0           K   �     $       � �   � �E   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �    [   �       �   �  D G            `      `   #        �nvrhi::BufferDesc::~BufferDesc 
 >   this  AI  
     S L   AJ        
  M        �  GM) M          -(

 M        D   N M        B  -G M        `  &@ M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   q   >#    _Ptr_container  AP  '     8    AP ;       >#    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0     Othis  O ,   �    0   �   
 i   �    m   �   
 }   �    �   �   
 ]  �    a  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  !   �  !  
 @SH冹 H嬞H兞L�
    �   D岯    怘�H吷tH�    H��P怘兡 [�   �       �       �   M  V G            ?      9   �#        �nvrhi::ComputePipelineDesc::~ComputePipelineDesc 
 >_!   this  AI  	     5  AJ        	  M        �  # M        )  #CE
 >;     temp  AJ  &       AJ 9       N N                      0H�  h   �  )  %  )   0   _!  Othis  95       �   O   ,   �    0   �   
 {   �       �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 I  �    M  �   
 H�    H�H兞�       Z      �       �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       Z      �       �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (                           Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   Z      �    0   �       �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   Z      �    0   �       �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   Z      �    0   �       �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 L嬡SH侅�   f荄$@  I岾菻嬟L�
    3襂嬂I塖燣�    I塊業岾豂塊怚岾鐸塊圚嬋I塖菼塖蠭塖豂塖郔塖鐸塖餒嬘�    H嬅H伳�   [�   �   ,   �   c   �       �   (  ^ G            s      j   &&        �donut::render::DeferredLightingPass::CreateComputeShader 
 >墑   this  AJ          D�    >肐   shaderFactory  AH  %     B  AP        % 
 Z   �   �                     @ 
 h      �   墑  Othis  �   肐  OshaderFactory  O�   0           s   �     $       |  �   }  �j   ~  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 <  �    @  �   
 H塡$UVWAVAWH崿$丽��H侅@
  H�    H3腍墔0	  H嬟H孂荅�  H荅�  �?荅�(    D$8E�D$HH婭H�L岲$8H峊$0�惾   E3鯝嬛H峀$XH;萾H�L�0H婳H塛H吷tH��P怘婰$0H吷tL塼$0H��P惼D$VH婳H�L岲$8H峊$0�惾   I嬛H峀$`H;萾H�L�0H婳 H塛 H吷tH��P怘婰$0H吷tL塼$0H��P怢塽�W�E蠰塽郒荅�   艵� H荅�    荅�    艵� 荅    f荅  D塽H荅繾  �    嬑�    H荅�   H荅�        �   �@�
   圚艪 H塃衅E�艵�荅�   H婳H�L岴繦峊$0�悁   I嬛H峀$hH;萾H�L�0H婳(H塛(H吷tH��P怘婰$0H吷tL塼$0H��P怐壍�   茀�    3褹�   H崓   �    D壍  菂  �   菂     菂  �  f壍�   L塼$0艱$4
H婦$0H塃PL塼$0艱$4H婦$0H塃XL塼$0荄$0   艱$4H婦$0H塃`L塼$0荄$0   艱$4H婦$0H塃hL塼$0荄$0   艱$4H婦$0H塃pL塼$0荄$0   艱$4H婦$0H塃xL塼$0荄$0	   艱$4H婦$0H墔�   L塼$0荄$0
   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0艱$4H婦$0H墔�   L塼$0艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   L塼$0荄$0   艱$4H婦$0H墔�   I嬑H墠 	  H峌P怘�H墑�   H媿 	  H�罤墠 	  H兟H崊�   H;衭訦崓   H崊   �   � HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�H婳H�L崊�   H峊$0�怭  I嬛H峀$pH;萾H�L�0H婳8H塛8H吷tH��P怘婰$0H吷tL塼$0H��P怢塽W�3�EE(H塃8L�=    L墊$ L�
    峆D岪H峂�    怢塽@H�L�H峊$0H嬒�I嬛H峀$xH;萾H�L�0H婱H塙H吷tH��P怘婰$0H吷tL塼$0H��P怘婳8H塋$0H吷tH��P怢墊$ L�
    �   D岯鼿峂愯    怚嬾L塽窰媆$0H呟t
H�H嬎�PH媢窰9\鯋t)H呟t
H�H嬎�P怘婰鯋H塡鯋H吷tH��P怘媢窰�艸塽窰呟t
H�H嬎�P怚嬣@ f�     I嬛H岴怘肏峂�H;萾H�L�0H婰H塗H吷tH��P怘兠H凔(r荋婨窰塃@M嬒�   D岯鼿峂愯    怣嬒�   D岯鵋峀$0�    H婳H�L岴H峊$0��8  I嬛H峂圚;萾H�L�0H婳0H塛0H吷tH��P怘婰$0H吷tL塼$0H��P怣嬒�   D岯鼿峂�    怘婱H吷tL塽H��P怘婾鐷凓v-H�翲婱蠬嬃H侜   rH兟'H婭鳫+罤兝鳫凐w+�    H媿0	  H3惕    H嫓$�
  H伳@
  A_A^_^]描    �   �   K   �   \  �    s  �   ~  �   �  �        f  �    r  �    �  �    �  �      �    �  �    �  �    P  �    �  �    �     �  �       �   %  O G            �  ,   �  $&        �donut::render::DeferredLightingPass::Init 
 >墑   this  AJ        2  AM  2     ��  >嘓   shaderFactory  AI  /     � AK        /  >�    samplerDesc  D8    >!   constantBufferDesc  CK  (   m    	  CK (   �    &  D�    >�    layoutDesc  D�   >r!    pipelineDesc  D   M        z  佸 M        (  佸HB
 >�    temp  AJ  �      AJ �      B0   �    Z  B@
  �     N N M        y  &伨 M        z  佡 M        (  佡
 >�    temp  AJ  �      AJ �      N N M        &  佈 >�    tmp  AK  �    #  AK �    &    N M        '  伨C
 M        &  佀 N N N M        �  E乀 M          E乀 M          E乀  M        f&  乀((
% M        T&  乸! M        G   !乸 N N M        �  乀 >p    _Fancy_ptr  AH  `    O  M          乀 M        >  乀 M        �  乀 M        �  乀
 Z   �   N N N N N N N N N M        �  � M          �$ N M        �  � M          � M        M  � N N N N M        6&  �� M        M&  ��HB
 >M    temp  AJ  �       AJ 
    N  B0       H�   Bp  �     � N N M        5&  &�� M        6&  �� M        M&  ��
 >M    temp  AJ  �       AJ �       N N M        K&  �� >M    tmp  AK  �     #  AK �     i    N M        L&  ��C
 M        K&  �� N N N M         &  �� N M        6&  �� M        M&  ��HB
 >M    temp  AJ  �       AJ �     	  B0   �     �Y  G  B  t     X N N M        5&  )t M        6&  �� M        M&  ��
 >M    temp  AJ  �       AJ �       N N M        K&  �� >M   tmp  AK  �       AK �     ,    C       z     
  C      �     @   )   N M        L&  tF
 M        K&  �� N N N M        &  H N M        &  2 N M        �  7噄] M          噄-
S M        D  噄 N M        B  -噑S M        `  *噕P M        �  噠)+
 Z     
 >   _Ptr  AH  }      AJ  z      AH �      >#    _Bytes  AK  v    U * &  M        �  噯d
5
 Z   q   >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  嘦 M        )  嘦GB
 >;     temp  AJ  Y      AJ i    >  &  N N M        �#  �) M         $  �)HB
 >�"    temp  AJ  .      AJ ?      B0   8    �  B�      �  N N M        �#  %� M        �#  � M         $  �
 >�"    temp  AJ        AJ )      N N M        �#  � >�"    tmp  AK      "  AK )        N M        �#  �C	 M        �#  � N N N M        �  .唨 M        �  啠 M        �  啠 N N M        �  啓 N M        (  唨C M        �  啌 N N N# M        �  �G
 >N!    i  AI      [  M        �  哸 M        �  哸	
 N N M        :  �* M        u  �* M        �  咼 M        �  咼 N N M        �  咢 N M        8  �1 M        
  �1#	 N N N N M        8  � M        
  �#
 N N N M        8  呚	 M        
  呩# N N M        �  吢 M        )  吢HB
 >;     temp  AJ  �      AJ �      B0   �      B�  �    1 N N M        �  &厸 M        �  叾 M        )  叾
 >;     temp  AJ  �      AJ �      N N M        �  叜 >;     tmp  AK  �    #  AK �    <   *   N M        g  厸C
 M        �  叏 N N N M        �  匲2 N M        �  匩 N M        �  �8 M        �  �8HB
 >D     temp  AJ  =      AJ N    3  B0   G    � �   Bh      � N N M        �  &� M        �  �, M        �  �,
 >D     temp  AJ  (      AJ 8      N N M        �  �$ >D     tmp  AK      #  AK 8    A    N M        (  �C
 M        �  � N N N M        �  凲
% >�    <begin>$L0  AK  _    @  M        �  刞 N N M        b  �3 >�    result  B0   8    � �  N M        b  � >�    result  B0         N M        b  凎 >�    result  B0   �      N M        b  
冡 >�    result  B0   �      N M        X$  
兯 >�    result  B0   �      N M        `  儹 >�    result  B0   �      N M        `  儚 >�    result  B0   �      N M        `  僸 >�    result  B0   v      N M        `  僑 >�    result  B0   X      N M        `  �5 >�    result  B0   :      N M        `  � >�    result  B0         N M        `  債 >�    result  B0   �      N M        `  傐 >�    result  B0   �      N M        `  偫 >�    result  B0   �      N M        `  偉 >�    result  B0   �      N M        `  倞 >�    result  B0   �      N M        `  俹 >�    result  B0   t      N M        `  
俓 >�    result  B0   a      N M        a  
侷 >�    result  B0   N      N M        �  �	% N
 Z   "   @
          (         A �hl   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �                  (  )  B  C  D  E  F  G  `  �  �  �  �  �  �  
           L  M  �  �  �    8  >  X  g  `  a  b        #  %  '  (  )  2  3  y  z  �  �  �  �  �  �  �  �  �  �  �  �  &  '  (  :  u  �#  �#  �#  �#  �#  �#   $  X$  &  &   &  %&  5&  6&  K&  L&  M&  T&  Z&  f&  
 :0
  O        $LN467  p
  墑  Othis  x
  嘓  OshaderFactory  8   �  OsamplerDesc  �   !  OconstantBufferDesc  �  �  OlayoutDesc    r!  OpipelineDesc  9n       v$   9�       �   9�       �   9�       v$   9�       �   9	      �   9�      b$   9�      �   9�      �   9      �$   94      �   9J      �   9�      巹   9�      �   9�      �   9�      �   9#      �   9<      �   9R      �   9l      �   9�      �   9�      �$   9%      �   9;      �   9e      �   O   �   �           �  �     �       H  �2   I  �]   L  ��   N  ��   O  �  Q  �L  R  �T  S  ��  T  ��  U  ��  V  ��  W  �	  Z  �B  [  �I  \  ��  q  �N  s  ��  t  ��  u  ��  w  �U  x  �i  y  ��   1  ^ F                                �`donut::render::DeferredLightingPass::Init'::`1'::dtor$2  >�    samplerDesc  EN  8           >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  �          >r!    pipelineDesc  EN                                   �  O   �   1  ^ F                                �`donut::render::DeferredLightingPass::Init'::`1'::dtor$5  >�    samplerDesc  EN  8           >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  �          >r!    pipelineDesc  EN                                   �  O   ,   �    0   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 #  �    '  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 W  �    [  �   
 g  �    k  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 7  �    ;  �   
 G  �    K  �   
 W  �    [  �   
 k  �    o  �   
 �  �    �  �   
 �  �    �  �   
 (  �    ,  �   
 8  �    <  �   
 �  �    �  �   
 �  �      �   
 
  �      �   
 %  �    )  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 	  �    
  �   
   �    !  �   
 f	  �    j	  �   
 v	  �    z	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    
  �   
 
  �    
  �   
 z
  �    ~
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 	  �    
  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 
  �    #
  �   
   �      �   
   �       �   
 ,  �    0  �   
 <  �    @  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 	  �    
  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 t  �    x  �   
 �  �    �  �   
   �      �   
 E  �    I  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 )  �    -  �   
 b  �    f  �   
 �  �    �  �   
 �  �    �  �   
 
  �      �   
 F  �    J  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 *  �    .  �   
 c  �    g  �   
 �  �    �  �   
 �  �    �  �   
 �  2   �  2  
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 Q  �    U  �   
 a  �    e  �   
 q  �    u  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 !  �    %  �   
 <  �    @  �   
 ,     0    
 �     �    
 �     �    
 �     �    
          
 h  
   l  
  
 �  
   �  
  
 �  
   �  
  
 $  
   (  
  
 O  
   S  
  
 H崐�   �       �    H崐  �       �    @USVWATAUAVAWH崿$��给<  �    H+�)�$�<  H�    H3腍墔�;  M孂L塂$HL嬄H塗$@H塋$8H�H�    I嬋�怭  3繦墔�  W�咅  3褹�   H崓P  �    3褹�   H崓P
  �    3褹�   H崓P  �    �卄<  ��   (
    )�  (    )�   (
    )�0  (    )匑  驛O`�嵭  驛Gd�呍  驛Oh�嵷  菂�      驛Ol�嵿  驛Gp�呬  驛Ot�嶈  菂�      3繧9G@暲墔�  菂�    �?菂�    �?E3�3跧婫PH吚劊  H�8L媊I;�創  f�     H�H婬H吷tQH�H婸�襇呿u:L嬭H�H婭H�L婤@H峊$PA�衒n [纅nH[审吚  �嵞  �	L;��  媴�  凐�
  Hk萷L嵉P  L馠�H�I嬛�P8H�H婬H吷劷   3鯤��P 吚tO凔}7H�H婬H�嬛�P(H�L婣`Hc薍k裵H崓P
  H袶嬋A�蠬c艫塡咢��艸�H婬H��P ;餽�3鯤�H婬H��P0吚tO凔}7H�H婬H�嬛�P8H�L婣`Hc薍k裵H崓P
  H袶嬋A�蠬c艫塡哖��艸�H婬H��P0;餽�咅  H兦I;�厷���"H�
    �    閤  �   H�
    �    3�3鯡3鯩媑XM呬劚   I�$M媎$I;�劅   H��    劺ts媴�  H拎H崟P  H蠬��    媴�  �缐咊  凐sSH�t0H咑t+M咑t&H�H;x uH;p(uL;p0t H�
    �    檠  H�H媥 H媝(L媝0H兠I;�卼�����   H�
    �    3蹓\$0H婰$HH�峉�吚剏  L媎$8怘婰$HH�D嬅�   �PH塂$PH�L婣0H峌癏嬋A��3褹�   H崓�+  �    H菂�;      茀�;  I媆$(5    H呟tH�H嬎�P �x; �
u�	3覊昘  垍\  H墲P  f墪]  )礰  垥_  I媽$�   M呿I嬇uH媮�   墪x  菂|     H墔p  
    )崁  H�H嬊uH媮�   菂�     菂�     H墔�  )崰  H咑H嬈uH媮�   菂�     菂�     H墔�  )嵗  M咑I嬈uH媮�   菂�     菂�     H墔�  )嵿  I�菂�     菂�     H墔�  E�)�   I婫菂  	   菂     H墔  )�   I婫菂8  
   菂<     H墔0  )匑  I婫菂X     菂\     H墔P  )卄  I婫 菂x     菂|     H墔p  )厐  I婫(H吚uH媮�   菂�     菂�     H墔�  )厾  I婫0H吚uH媮�   菂�     菂�     H墔�  )吚  I婫8H吚uH媮�   菂�     菂�     H墔�  )嵿  I婫@H吚uH媮�   菂�     菂�     H墔�  )�   I婫H墪  菂     H墔  )�   I婦$墪8  菂<     H墔0  H墪@  H墪H  I婦$ 菂X     菂\     H墔P  H墪`  H墪h  I媽$�   H媮�   菂x     菂|     H墔p  H墪�  H墪�  H媮�   菂�     菂�     H墔�  H墪�  H墪�  H嬄H墪�+  H崓P  f�     H拎��  I��  H媴�+  H�繦墔�+  H兞 H崟�  H;蕌腁�  H崟�  H崓�+  �    M婰$8L崊�+  H峊$8I峀$@�    怘媆$PH�H峌餒嬎�PH婰$@H�H荄$     A筦  L岴餓婽$(�Px3繦塃↖婦$0H塂$pH婦$8H塃繦荅�   E�D$xM�M�E�E楬婰$@H�H峊$p�愗   H�H峊$XH嬎�惛   H婰$@H�L嫄�   婦$d+D$`兝檭�D�A柳婦$\+D$X兝檭�辛�A�   A�覑H婰$8H吷tH荄$8    H��P悑\$0�脡\$0H婰$HH��   �;�倣��H婰$@H��怷  H媿�;  H3惕    (�$�<  H伳�<  A_A^A]A\_^[]�      -   �   V   �   �      �      �      �   �   �   �   �   �   �   �     �      �    1  �   6  �    d  �    �  �    �  �   �  �    �  �   �  �    Y     w  �   �  �   a     |  �    �	        �   '  Q G            �	  ;   �	  '&        �donut::render::DeferredLightingPass::Render 
 >墑   this  B8   P     |	�` AJ        ]  AT      t AT �	    4  >�$   commandList  B@   K     �	 AJ  �    	 ? k  � A  AK        F  AP  F       >H   compositeView  BH   C     �	 AJ  
    �  ) N AP        F  >梽   inputs  AQ        >  AW  >     �	 >/   randomOffset  EO  (           D`=  " >�    lightProbeEnvironmentBrdf  AV  A    ` AV �	    "  >t     numShadows  A   �    � A  �    � � >�    lightProbeDiffuse  AM  <    e AM �	    '  >�    lightProbeSpecular  AL  >    c AL �	    (  >�   deferredConstants  C          ' �  C     �      D�    >�    shadowMapTexture  AH  �    _ 7 H  AU  �    | (   AH 
      AU �     H h  >    <range>$L0  AH  �    "  AH �    ] ���  >蔪    <begin>$L0  AM  �    � AM :    � e >蔪    <end>$L0  AT  �    � AT :    � \ >肸    lightConstants  AV  '    �  AV �    w � �` >u     cascade  A   H    [  >u     perObjectShadow  A   �    `  A  �    � � �c >L�    <end>$L1  AT  W    �  AT �    �  t >L�    <begin>$L1  AI  R    �  AI �    � � >d�    <range>$L1  AT  E      AT �    �  t >飬    lightProbeConstants  AK  }      >u     viewIndex  A       �s  A  �	    )  B0       � >u    viewExtent  DX    >�    viewSubresources  A�   �    c A�        D�    >�"    state  Dp   
 >H    view  AI  �    �  BP        � >�    bindingSetDesc  D�,   >SJ    bindingSet  B8        � M        3&  伆 N M        ]&  佺
 >獈   v  AH  �    !  AH 
      N M        3&  �9
 >俹   this  AH  9      AH �    ] Ph" ��  N M        /&  倓 N M        /&  傛 N M        �  処 N M        c  動&* N$ M        d  剓(&G' N M        c  �** N M        c  �8** N M        c  卛** N M        c  厧** >�   texture  AH  �    *  N M        c  呣** >�   texture  AH  �    &  N M        �  塜 M        ,  塜HB	
 >�!    temp  AJ  ]	      AJ r	      N N M        c  �** >�   texture  AH      &  N M        c  �*** >�   texture  AH  *    &  N M        &  塀 N M        c  哱** N M        c  吀** >�   texture  AH  �    &  N M        c  啂** N M        �  
圅, >_    <begin>$L0  AJ      Y  M        �  � N N M        &  �+ N M        c  喞** N M        c  嗱** N M        Y$  �&* >�   texture  AH      #  N M        e  �;&* >M   sampler  AH  ;    *  N M        e  噀** >M   sampler  AH  e    8  N M        e  嚌** >M   sampler  AH  �    0  N! M        e  囃** >M   sampler  AH  �    ,  N M        �  埗 N M        �  埰 M        �  埰	 N N" Z   {&  |&  �&  �&  {&  |&  }&  # S�  �,   nvrhi::AllSubresources  A�   �     A�        �<          @         A � h8   �  �  y  �  �  �  �  �  c  d  e  !  0  1  4  6  x  �  �  �  �  �  �  �  �  �  �  �  �  ,  �  H  �#  �#  Y$  &  &  &  (&  )&  *&  ,&  -&  /&  1&  2&  3&  4&  G&  H&  I&  J&  [&  \&  ]&  ^&  
 :�<  O  @=  墑  Othis  H=  �$  OcommandList  P=  H  OcompositeView  X=  梽  Oinputs  `=  /  OrandomOffset  �   �  OdeferredConstants  X   u  OviewExtent  �   �  OviewSubresources  p   �"  Ostate  �,  �  ObindingSetDesc  8   SJ  ObindingSet  9]       &%   9�      Ib   9�      Nb   93      臵   9K      Jb   9c      Lb   9�      Ub   9�      Jb   9�      Jb   9�      Lb   9�      Ub   9�      Jb   9      H   90      H   9F      %H   9�      =   9�      "H   9�      �$   9�      %   9	      ,H   9T	      %   9n	      �   9�	      H   9�	      �$   O �   �          �	  �  Q   �      �  �P   �  �c   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �.  �  �b  �  �q  �  �{  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �
  �  �  �  �*  �  �6  �  �F  �  �R  �  �W  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �	  �  �  �  �$  �  �)  �  �:  �  �<  �  �>  �  �A  �  �N  �  �`  �  �l  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��   �    �8   �I   �o   �e   ��    ��  ! ��  # ��  $ ��  % ��  & �	  ( �	  ) �X	  , �r	   ��	  . ��	  / ��   ]  ` F                                �`donut::render::DeferredLightingPass::Render'::`1'::dtor$0  >�    deferredConstants  EN  �           >u    viewExtent  EN  X           >�    viewSubresources  EN  �           >�"    state  EN  p           >�    bindingSetDesc  EN  �,                                 �  O   ,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �       �   
   �      �   
 4  �    8  �   
 D  �    H  �   
 \  �    `  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 )  �    -  �   
 9  �    =  �   
 i  �    m  �   
 y  �    }  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 1  �    5  �   
 I  �    M  �   
 ]  �    a  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �    "  �   
 K  �    O  �   
 [  �    _  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 #  �    '  �   
 3  �    7  �   
 \  �    `  �   
 l  �    p  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 .  �    2  �   
 >  �    B  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �    $  �   
 0  �    4  �   
 i  �    m  �   
 y  �    }  �   
 �  �    �  �   
 
	  �    	  �   
 d	  �    h	  �   
 t	  �    x	  �   
 �	  �    �	  �   
  
  �    
  �   
 ~
  �    �
  �   
 �
  �    �
  �   
 �  �    �  �   
 �  �    �  �   
 .  �    2  �   
 r  �    v  �   
 �  �    �  �   
 j
  �    n
  �   
 z
  �    ~
  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 #  �    '  �   
 3  �    7  �   
 C  �    G  �   
 S  �    W  �   
 c  �    g  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 #  �    '  �   
 <  �    @  �   
          
 n     r    
 �     �    
 �     �    
 �     �    
          
 H崐8   �       �    H兞@�       �       �   �   \ G            	          +&        �donut::render::DeferredLightingPass::ResetBindingCache 
 >墑   this  AJ         
 Z   ~&                          @     墑  Othis  O  �   (           	   �            2 �    3 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H婤H�H婤0H堿H婤 H堿H婤(H堿H婤8H堿 �   �   �   ] G            (       '   "&        �donut::render::DeferredLightingPass::Inputs::SetGBuffer 
 >r�   this  AJ        (  >�&   targets  AK        (                         @ 
 h   y      r�  Othis     �&  Otargets  O �   P           (   �     D       6  �    7  �   8  �   9  �   :  �   ;  �'   <  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       i            �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               (            J �   K �,   �    0   �   
 �      �     
 �   �    �   �   
 H婹H�    H呉HE旅   ]      �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                    $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H                       :    20    2                       @   
 
4 
2p    B                       F    20    <                       L   
 
4 
2p    B                       R    20    <                       X   
 
4 
2p    B                       ^    �                                d    20    `           "      "      j    B                   v       "           #      #      p   h           y      |          �    2 B                   �       "           $      $         h           �      �          �    2 20                 �       ?           %      %      �   h           �      �          �    :8  B                   �       "           &      &      �   h           �      �          �    2 d T 4 2p                 �       }           '      '      �   h           �      �          �    � 20    [           )      )      �    T 4
 2�p`                 �       3          +      +      �   (           �      �       4    �6    .       �       �    
   	         P8� B                   �       "           ,      ,      �   h           �      �          �    2 B                   �       "           -      -      �   h           �      �          �    2 B                   �       "           .      .      �   h           �      �          �    2 20               /      /      �   ! t               /      /      �      E           /      /         !                 /      /      �   E   K           /      /      	   -  0      s           0      0          4	 2p`P                        �           1      1         (           !      $       �6    ^    .    .    .    .    .    .       �       �    
                     
   !      &      +      pJ,	 4PH�
�p`P          2
           -       �          3      3      '   (           0      3   
    2    >    b    A>       �       �       �       �       �    2� X4 nD ��D�Df0J
4"
F.
:$
<v
X�
DT ; *h�"�
��	��p`0P        �<            <       �	          4      4      6   (           ?      B   
    p>       �       �    4 �!]4  B                   K       "           5      5      E   h           N      Q          �    2 B      :           7      7      T                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       l                                 r      x      ~                   .?AVbad_array_new_length@std@@                    ����                      o      �                    .?AVbad_alloc@std@@                   ����                      u      �                    .?AVexception@std@@                    ����                      {      �    string too long     ����    ����        ��������                            �      �       �    DeferredLightingConstants main donut/passes/deferred_lighting_cs.hlsl DeferredLighting All lights submitted to DeferredLightingPass::Render(...) must use the same shadow map textures Maximum number of active lights (%d) exceeded in DeferredLightingPass Maximum number of active light probes (%d) exceeded in DeferredLightingPass All light probes submitted to DeferredLightingPass::Render(...) must use the same set of textures                                       {      �      �                         �                   �               ����    @                   {      �                                         u      �      �                         �                           �      �              ����    @                   u      �                                         o      �      �                         �                                   �      �      �              ����    @                   o      �                                         �      �      �                   .?AVDeferredLightingPass@render@donut@@                              �                   �               ����    @                   �      �   `錺?�&�>R?j即>
證?+嚃>潦a?�>祝p>j�4?�=+�?q=媗?X94>恕%?  �?  �?  �?  �?   �   =   ; 
7        donut::render::DeferredLightingPass::`vftable'       �      �  
    �   (   & 
7        std::exception::`vftable'    Z      Z  
    �   (   & 
7        std::bad_alloc::`vftable'    `      `  
    �   3   1 
7        std::bad_array_new_length::`vftable'     c      c  
 噾姏@|#皲~T諷�=*�(.瞗輩耾"0h�K蜌�(`U嶛璓�(！
Z晩y潟轡 3>飖9屓|領}*�o肆峖=f瓵Kg�-).o垶R|D�絓鞂盍�$�&開m.o9蛁7F蠍I�諜L乘翇^=f瓵鋂曑C诱嚙M砎谺笻哋�⒓1M�;镩垓浄岢R妱隂S-郐嫂胊� 篿'オ70煸舚S4H�)!灎鴷K~KB\4b濐じ多Du澦翇^=f瓵舳镧(t⑺翇^=f瓵礆庿肆峖=f瓵獘踒輸W羾臂ep禭诵鰐麌臂ep禭�6IB��6萪O��'��$x稫霵婬(︶辋
爌桗�'項j娿V欁暇剁'項j鞄蕐袬�'項jq!梮屯嬄�'項j簭g	3佚'項j躡vDZq�'項j鷵/uc��'項j瑹 {U欜珴\夂嫑�J斑u!�臉
孷厃刑2
q�$愜w獛啯�.^+?沅B猕up泸翬蟱_?+鲢�6�<藺瘧櫷y`嶀預棊膬$R驯�-n嶀預棊膬B涵 =6]箩邆5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k�.�D�3s罚绡=N"秎��@袃�<躒�%<2锬8��/E�
:奅猚琈韡&/髗溛d#F[h'y+ 埚巌;�m鏳r問怟@拥鈀1曙峊�|]坐柮n�%I栶賑?T$��4分緉N鵘J廒%e�%C2F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D罵��$m翲咞taR�,F_棢杻#Q惕屖碋箎了5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊嘕-WV8om�M%>mbG嶊4K鋆GhT�!蛂�dd�a�:湌h恩悟�8([T�=了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛&ЖS椣�AF�辛卢+�Vh顛Ь]8[dd�a�:+OCd搗�+тX┒
a渮?樣湨G巫�dd�a�:E綇UZ堕袁]豧柏as涏Ys^C呑y衰�dd�a�:蓌?.=卙ょ}頄a了5YJq覜垒�咞taR�,F_棢杻#Q`�G�-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H潗幭恫V揄h�"⑹&�$樐蜆{絏1�轡v鳱r暦�&禌f⒎鷻蝕3闋>ⅵH�舡�;萕賗<�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2溎辽Ut葈"�:邍A愦靮鸬2�>料C5�v)鷇_慙�7OEvq蘟空�Ж�/噵)龌斱/x�>uu汉�糌P越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       鸡              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn               _葓�     .debug$S       8             .text$mn    	          �邆     .debug$S    
            	    .text$mn              恶Lc     .debug$S       �              .text$mn    
   3     �0^�     .debug$S       ,
  D       
    .text$x              碙辢
    .text$x              曍譧
    .text$x              �c
    .text$mn              恶Lc     .debug$S       �              .text$mn       �      怊     .debug$S       �
  B           .text$x              ��    .text$x              ��:    .text$x              婹�    .text$x              c	9    .text$x              +�    .text$x              �x=    .text$x              8廄�    .text$x              U�>    .text$mn              恶Lc     .debug$S       �              .text$mn               恶Lc     .debug$S    !   �               .text$mn    "          恶Lc     .debug$S    #   �          "    .text$mn    $   <      .ズ     .debug$S    %   0  
       $    .text$mn    &   <      .ズ     .debug$S    '   L  
       &    .text$mn    (   !      :著�     .debug$S    )   <         (    .text$mn    *   2      X于     .debug$S    +   <         *    .text$mn    ,   "       坼	     .debug$S    -   �         ,    .text$mn    .   "       坼	     .debug$S    /   �         .    .text$mn    0   "       坼	     .debug$S    1   �         0    .text$mn    2   "       坼	     .debug$S    3   �         2    .text$mn    4   "       坼	     .debug$S    5   �         4    .text$mn    6   "       坼	     .debug$S    7   �         6    .text$mn    8   "       坼	     .debug$S    9   �         8    .text$mn    :   [       荘�     .debug$S    ;            :    .text$mn    <   }      1�-�     .debug$S    =   �         <    .text$mn    >   K       }'     .debug$S    ?   �         >    .text$mn    @   `      板@�     .debug$S    A   �         @    .text$mn    B   ?      劸惂     .debug$S    C   \         B    .text$mn    D         ��#     .debug$S    E   �          D    .text$mn    F         ��#     .debug$S    G   �          F    .text$mn    H   B      贘S     .debug$S    I             H    .text$mn    J   B      贘S     .debug$S    K            J    .text$mn    L   B      贘S     .debug$S    M   �          L    .text$mn    N   H       襶.      .debug$S    O   �         N    .text$mn    P   s      鵡�     .debug$S    Q   l  
       P    .text$mn    R   �     粘     .debug$S    S   |  �       R    .text$x     T         竼>鍾    .text$x     U         菗>R    .text$mn    V   �	     95懲     .debug$S    W   D  �       V    .text$x     X         :�薞    .text$mn    Y   	      ��     .debug$S    Z   �          Y    .text$mn    [   (       h��     .debug$S    \   \         [    .text$mn    ]          aJ鄔     .debug$S    ^   �          ]    .text$mn    _         崪覩     .debug$S    `   �          _        \       N        x                �                �                �                �                �                �                               *      *        K      F        e      _        �      L        �          i�                    �      $        �      H                  i�                    "      (        G      D        l      &        �      J        �          i�                    �      ]                       3              X      @        u              �      8        �      	        �      ,        7      B        ]      4        �      <        4      :        �      
        -               �               �      6              0        J      2        �      >        �      P        G      [        �              	      R        }	      V        	
      Y        G
               s
               �
      .        �
               �               �                     "        2              M               m              �
                            [              �      X        |                            �      T        �              �              �              P              �                    U        �                            �               �               �           __chkstk             �           memcpy           memmove          memset           $LN13       N    $LN5        *    $LN10       L    $LN7        $    $LN13       H    $LN10       &    $LN16       J    $LN3        ]    $LN4        ]    $LN37   `   @    $LN40       @    $LN10       8    $LN10       ,    $LN18       B    $LN10       4    $LN77       <    $LN30   [   :    $LN33       :    $LN158  3  
    $LN161      
    $LN10       6    $LN10       0    $LN10       2    $LN18       >    $LN13       P    $LN50           $LN467  �  R    $LN471      R    $LN285      V    $LN10       .    $LN14   :       $LN17           .xdata      a          F┑@N        �      a    .pdata      b         X賦鶱              b    .xdata      c          （亵*        8      c    .pdata      d          T枨*        a      d    .xdata      e          %蚘%L        �      e    .pdata      f         惻竗L        �      f    .xdata      g          （亵$        �      g    .pdata      h         2Fb�$        �      h    .xdata      i          %蚘%H        '      i    .pdata      j         惻竗H        N      j    .xdata      k          （亵&        t      k    .pdata      l         2Fb�&        �      l    .xdata      m          %蚘%J        �      m    .pdata      n         惻竗J        
      n    .xdata      o          懐j瀅        >      o    .pdata      p         Vbv鵠        n      p    .xdata      q          （亵@        �      q    .pdata      r         粻胄@        �      r    .xdata      s         /
�8        �      s    .pdata      t         +eS�8              t    .xdata      u   	      �#荤8        W      u    .xdata      v         j8        �      v    .xdata      w          3狷 8        �      w    .xdata      x         /
�,              x    .pdata      y         +eS�,        N      y    .xdata      z   	      �#荤,        �      z    .xdata      {         j,        �      {    .xdata      |          3狷 ,              |    .xdata      }         蚲7MB        Y      }    .pdata      ~         袮韁B        �      ~    .xdata         	      �#荤B        �          .xdata      �         jB        �      �    .xdata      �          愔
~B              �    .xdata      �         /
�4        J      �    .pdata      �         +eS�4        �      �    .xdata      �   	      �#荤4        �      �    .xdata      �         j4        �      �    .xdata      �          3狷 4        7      �    .xdata      �         vQ9	<        r      �    .pdata      �         A刄7<               �    .xdata      �   	      �#荤<        �      �    .xdata      �         j<        }      �    .xdata      �          強S�<        3      �    .xdata      �          （亵:        �      �    .pdata      �         愶L:        �       �    .xdata      �         鸝�
        r!      �    .pdata      �         *_5�
        �!      �    .xdata      �   	      � )9
        �!      �    .xdata      �         QuX#
        6"      �    .xdata      �          晇
        "      �    .xdata      �         /
�6        �"      �    .pdata      �         +eS�6        �"      �    .xdata      �   	      �#荤6        5#      �    .xdata      �         j6        q#      �    .xdata      �          3狷 6        �#      �    .xdata      �         /
�0        �#      �    .pdata      �         +eS�0        ($      �    .xdata      �   	      �#荤0        `$      �    .xdata      �         j0        �$      �    .xdata      �          3狷 0        �$      �    .xdata      �         /
�2        %      �    .pdata      �         +eS�2        Y%      �    .xdata      �   	      �#荤2        �%      �    .xdata      �         j2        �%      �    .xdata      �          3狷 2        (&      �    .xdata      �          （亵>        l&      �    .pdata      �         � �>        �&      �    .xdata      �         范^�>        �&      �    .pdata      �         鳶�>        D'      �    .xdata      �         @鴚`>        �'      �    .pdata      �         [7�>        �'      �    .voltbl     �          飾殪>    _volmd      �    .xdata      �          澏筂P        (      �    .pdata      �         s栠"P        �(      �    .xdata      �         頴^        4)      �    .pdata      �         尽/x        �)      �    .xdata      �   	      � )9        1*      �    .xdata      �   /   	   乀翤        �*      �    .xdata      �          Gx檂        9+      �    .xdata      �   $      -$�/R        �+      �    .pdata      �         鰈R        ',      �    .xdata      �   	      � )9R        �,      �    .xdata      �         釬R        -      �    .xdata      �   4       w孳釸        w-      �    .xdata      �   (      i蟦鶹        �-      �    .pdata      �         �懜V        z.      �    .xdata      �   	      � )9V        
/      �    .xdata      �         輍汈V        �/      �    .xdata      �          �I%V        ?0      �    .xdata      �         /
�.        �0      �    .pdata      �         +eS�.        1      �    .xdata      �   	      �#荤.        N1      �    .xdata      �         j.        �1      �    .xdata      �          3狷 .        �1      �    .xdata      �          �9�        2      �    .pdata      �         礝
        n2      �    .rdata      �                      �2     �    .rdata      �          �;�         �2      �    .rdata      �                      3     �    .rdata      �                      3     �    .rdata      �          �)         A3      �    .xdata$x    �                      m3      �    .xdata$x    �         虼�)         �3      �    .data$r     �   /      嶼�         �3      �    .xdata$x    �   $      4��         �3      �    .data$r     �   $      鎊=         ,4      �    .xdata$x    �   $      銸E�         F4      �    .data$r     �   $      騏糡         �4      �    .xdata$x    �   $      4��         �4      �        �4           .rdata      �          燺渾         �4      �    .data       �           烀�          5      �        K5     �    .rdata      �                      r5     �    .rdata      �          ��         �5      �    .rdata      �          旲^         �5      �    .rdata      �   '       c&�         �5      �    .rdata      �          8{噾         6      �    .rdata      �   `       u莨�         =6      �    .rdata      �   F       嶴7         u6      �    .rdata      �   L       }眠�         �6      �    .rdata      �   b       3�         �6      �    .rdata$r    �   $      'e%�         7      �    .rdata$r    �         �          77      �    .rdata$r    �                      M7      �    .rdata$r    �   $      Gv�:         c7      �    .rdata$r    �   $      'e%�         �7      �    .rdata$r    �         }%B         �7      �    .rdata$r    �                      �7      �    .rdata$r    �   $      `         �7      �    .rdata$r    �   $      'e%�         �7      �    .rdata$r    �         �弾         8      �    .rdata$r    �                      )8      �    .rdata$r    �   $      H衡�         J8      �    .rdata$r    �   $      'e%�         t8      �    .data$rs    �   8      踘*>         �8      �    .rdata$r    �         �          �8      �    .rdata$r    �                      �8      �    .rdata$r    �   $      Gv�:         "9      �        U9           .rdata      �          嚙�,         g9      �    .rdata      �          �貳u         �9      �    .rdata      �          祢蒵         �9      �    .rdata      �          B敢�         �9      �    .rdata      �          _�         :      �    _fltused         .debug$S    �   L          �    .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �   �                *:  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ??0?$vector@M$03@math@donut@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1ComputePipelineDesc@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z ?GetOrCreateBindingSet@BindingCache@engine@donut@@QEAA?AV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@AEBUBindingSetDesc@5@PEAVIBindingLayout@5@@Z ?Clear@BindingCache@engine@donut@@QEAAXXZ ??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ ?CreateComputeShader@DeferredLightingPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@@Z ?SetGBuffer@Inputs@DeferredLightingPass@render@donut@@QEAAXAEBVGBufferRenderTargets@34@@Z ??0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z ?Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z ?Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z ?ResetBindingCache@DeferredLightingPass@render@donut@@QEAAXXZ ?IsActive@LightProbe@engine@donut@@QEBA_NXZ ?FillLightProbeConstants@LightProbe@engine@donut@@QEBAXAEAULightProbeConstants@@@Z ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ?CreateAutoShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0UStaticShader@23@11PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ?warning@log@donut@@YAXPEBDZZ ?error@log@donut@@YAXPEBDZZ ??0ShadowConstants@@QEAA@XZ ??0LightConstants@@QEAA@XZ ??0LightProbeConstants@@QEAA@XZ ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?dtor$0@?0???0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$0@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$0@?0??Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z@4HA ?dtor$1@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$2@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$2@?0??Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$3@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$4@?0???0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$4@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$5@?0???0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$5@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$5@?0??Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$6@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA ?dtor$7@?0???0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $pdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $pdata$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $cppxdata$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $stateUnwindMap$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $ip2state$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $unwind$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $unwind$?CreateComputeShader@DeferredLightingPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@@Z $pdata$?CreateComputeShader@DeferredLightingPass@render@donut@@MEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@AEAVShaderFactory@engine@3@@Z $unwind$??0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $pdata$??0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $cppxdata$??0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $stateUnwindMap$??0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $ip2state$??0DeferredLightingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@@Z $unwind$?Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $pdata$?Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $cppxdata$?Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $stateUnwindMap$?Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $ip2state$?Init@DeferredLightingPass@render@donut@@UEAAXAEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $unwind$?Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z $pdata$?Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z $cppxdata$?Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z $stateUnwindMap$?Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z $ip2state$?Render@DeferredLightingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBUInputs@123@U?$vector@M$01@math@3@@Z $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7DeferredLightingPass@render@donut@@6B@ ??_C@_0BK@PJAPEOBB@DeferredLightingConstants@ ??_C@_04GHJNJNPO@main@ ??_C@_0CH@IMCLGIMO@donut?1passes?1deferred_lighting_@ ??_C@_0BB@KHJHHFHK@DeferredLighting@ ??_C@_0GA@MALCEHCE@All?5lights?5submitted?5to?5Deferre@ ??_C@_0EG@DIIOCAKC@Maximum?5number?5of?5active?5lights@ ??_C@_0EM@PPBIJNBL@Maximum?5number?5of?5active?5light?5@ ??_C@_0GC@DGKGKIHH@All?5light?5probes?5submitted?5to?5D@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R4DeferredLightingPass@render@donut@@6B@ ??_R0?AVDeferredLightingPass@render@donut@@@8 ??_R3DeferredLightingPass@render@donut@@8 ??_R2DeferredLightingPass@render@donut@@8 ??_R1A@?0A@EA@DeferredLightingPass@render@donut@@8 __security_cookie __xmm@3eb4bc6a3f52f1aa3ef126e93f70e560 __xmm@3ed2f1aa3f61cac13e96872b3f43d70a __xmm@3f16872b3df1a9fc3f34bc6a3e70a3d7 __xmm@3f25a1cb3e3439583f076c8b3d71a9fc __xmm@3f8000003f8000003f8000003f800000 