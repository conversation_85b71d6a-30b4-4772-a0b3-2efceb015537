d�(祚Gh�1 �      .drectve        <  T.               
 .debug$S        鴿 �/  埻        @ B.debug$T        p   赝             @ B.rdata          <   H�             @ @@.text$mn        �   勎              P`.debug$S        �  D� 粜        @B.text$mn        :   D� ~�         P`.debug$S          溠 ㄓ        @B.text$mn          4� B�         P`.debug$S        D  堈 疼     2   @B.text$mn        1   垒 褫         P`.debug$S        �   镞        @B.text$mn        s  粪 *�     	    P`.debug$S        �  勩 4�     b   @B.text$x         C   � K�         P`.text$mn        �   i�              P`.debug$S        �  � 睇        @B.text$mn        p   牲              P`.debug$S        ,  9� e�        @B.text$mn        3   〓              P`.debug$S        ,  轧           @B.text$mn           <               P`.debug$S          G  W        @B.text$mn          � �         P`.debug$S        �  � �	     2   @B.text$mn           �              P`.debug$S        �   � �        @B.text$mn        �  � �     .    P`.debug$S        i  P h�     >  @B.text$x            援 喈         P`.text$x            戤 霎         P`.text$x             � �         P`.text$x            � "�         P`.text$x            ,� <�         P`.text$x            F� V�         P`.text$x            `� p�         P`.text$x            z� 姱         P`.text$x            敮 く         P`.text$x             警         P`.text$x            券 辕         P`.text$x            薤 戬         P`.text$x            舣  �         P`.text$x            
� �         P`.text$x             � ,�         P`.text$x            6� B�         P`.text$x            L� X�         P`.text$x            b� r�         P`.text$x            |� 尠         P`.text$x            柊 Π         P`.text$x            鞍 腊         P`.text$x            拾 诎         P`.text$x            浒 舭         P`.text$mn        
                 P`.debug$S        �   � 媳        @B.text$mn        �   �              P`.debug$S        �   筒 櫝        @B.text$mn           脸              P`.debug$S        �   页 姶        @B.text$mn           泊              P`.debug$S        �   酱 u�        @B.text$mn           澋              P`.debug$S        �   吹 ��        @B.text$mn        <   级          P`.debug$S        0  � F�     
   @B.text$mn        <    娓         P`.debug$S        L  � P�     
   @B.text$mn        !   春 蘸         P`.debug$S        <  楹 %�        @B.text$mn        2   a� 摷         P`.debug$S        <  Ъ 憬        @B.text$mn        "   [�              P`.debug$S        �  }� �        @B.text$mn        "   道              P`.debug$S        �  桌 k�        @B.text$mn        "   �              P`.debug$S        �  -� 鼓        @B.text$mn        "   Y�              P`.debug$S        �  {� �        @B.text$mn        "   磺              P`.debug$S        �  萸 q�        @B.text$mn        "   �              P`.debug$S        �  3� 克        @B.text$mn        ^   _� 教         P`.debug$S        T  烟 %�        @B.text$mn        K   硇              P`.debug$S        �  8� �        @B.text$mn        K   び              P`.debug$S        �  镉 险        @B.text$mn        K   [�              P`.debug$S        �  χ z�        @B.text$mn        �   � 戀         P`.debug$S        �   W�        @B.text$mn        `   [� 晦         P`.debug$S        �  限 忈        @B.text$mn        �   C� 蜮         P`.debug$S        �  � 舒     *   @B.text$mn        �   n� �         P`.debug$S        �  5� 鬼     $   @B.text$mn           !� 4�         P`.debug$S        �   H� ,�        @B.text$mn           T� g�         P`.debug$S        �   {� [�        @B.text$mn        B   楍 亳         P`.debug$S           黢 黩        @B.text$mn        B   3� u�         P`.debug$S          擉 ｔ        @B.text$mn        B   唪 !�         P`.debug$S        �   ?� ;�        @B.text$mn        H   w�              P`.debug$S        �  况 凐        @B.text$mn        �  淁 z�     	    P`.debug$S        4  轧      .   @B.text$mn            � �         P`.debug$S        �    �        @B.text$mn           	 #	         P`.debug$S        �   7	 �	        @B.text$mn           '
 8
         P`.debug$S           L
 L        @B.text$mn        A   � �         P`.debug$S        �  � �        @B.text$mn           � �         P`.debug$S        �   � �        @B.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata                      @0@.xdata             .             @0@.pdata             : F        @0@.xdata             d             @0@.pdata             l x        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             
         @0@.xdata             4             @0@.pdata             < H        @0@.xdata             f             @0@.pdata             n z        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata                      @0@.xdata             : J        @0@.pdata             ^ j        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             # )        @@.xdata             3             @@.xdata             6 F        @0@.pdata             Z f        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             # )        @@.xdata             3             @@.xdata             9 I        @0@.pdata             ] i        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             " (        @@.xdata             2             @@.xdata             5 E        @0@.pdata             Y e        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata              #        @0@.xdata             A Q        @0@.pdata             o {        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             �         @0@.xdata             ) 9        @0@.pdata             W c        @0@.voltbl            �               .xdata          (   � �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata          �    �        @@.xdata          O   �             @@.voltbl                            .xdata              /        @0@.pdata             9 E        @0@.xdata              c �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             �         @0@.xdata             #             @0@.pdata             + 7        @0@.xdata             U             @0@.pdata             ] i        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.voltbl            ;               .xdata             =             @0@.pdata             E Q        @0@.xdata             o             @0@.pdata             { �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata                      @0@.pdata             / ;        @0@.xdata             Y             @0@.pdata             a m        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata                      @0@.xdata             - A        @0@.pdata             _ k        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                        @0@.xdata             =  M         @0@.pdata             k  w         @0@.xdata             �              @0@.pdata             �  �         @0@.xdata             �  �         @0@.pdata             �  !        @0@.xdata          
   %! 2!        @@.xdata             P!             @@.xdata             S! [!        @@.xdata             e! l!        @@.xdata          	   v!             @@.xdata             !             @0@.pdata             �! �!        @0@.voltbl            �!               .xdata             �!             @0@.pdata             �! �!        @0@.rdata             �!  "        @@@.rdata             "             @@@.rdata             0" H"        @@@.rdata             f" ~"        @@@.rdata             �"             @@@.xdata$x           �" �"        @@@.xdata$x           �" �"        @@@.data$r         /   # J#        @@�.xdata$x        $   T# x#        @@@.data$r         $   �# �#        @@�.xdata$x        $   �# �#        @@@.data$r         $   �# $        @@�.xdata$x        $    $ D$        @@@.rdata             X$             @@@.data               h$             @ @�.rdata             �$             @0@.rdata             �$             @0@.rdata             �$             @@@.rdata             �$             @0@.rdata          %   �$             @@@.rdata          
   �$             @@@.rdata             �$             @@@.rdata             �$             @@@.rdata$r        $   �$ %        @@@.rdata$r           5% I%        @@@.rdata$r           S% _%        @@@.rdata$r        $   i% �%        @@@.rdata$r        $   �% �%        @@@.rdata$r           �% �%        @@@.rdata$r           & &        @@@.rdata$r        $   )& M&        @@@.rdata$r        $   a& �&        @@@.rdata$r           �& �&        @@@.rdata$r           �& �&        @@@.rdata$r        $   �& '        @@@.rdata             3'             @P@.rdata             C'             @P@.rdata             S'             @P@.debug$S        4   c' �'        @B.debug$S        4   �' �'        @B.debug$S        @   �' 3(        @B.chks64         @	  G(              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   $  p     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\EnvironmentMapPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $vfs  $math 	 $colors 	 $render 	 $stdext �   US    3        nvrhi::EntireBuffer  r   std::ratio<1,1>::num L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos  r   std::ratio<1,1>::den � �   std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Same_size_and_compatible � �    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Bitcopy_constructible � �    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Bitcopy_assignable J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < r  ��枠 std::integral_constant<__int64,10000000>::value 1 r   std::integral_constant<__int64,1>::value ) �   donut::math::frustum::numCorners - �   std::chrono::steady_clock::is_steady � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible & r   std::ratio<1,1000000000>::num � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable * r  � 蕷;std::ratio<1,1000000000>::den = �   donut::engine::c_MaxRenderPassConstantBufferVersions . %   donut::math::box<float,2>::numCorners   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable � #   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable ��    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi ��   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard � #   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den A #   std::allocator<char>::_Minimum_asan_allocation_alignment �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment + �        nvrhi::rt::c_IdentityTransform 3   \ std::filesystem::path::preferred_separator - %    std::integral_constant<int,0>::value D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo . �    std::integral_constant<bool,0>::value " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits . %   donut::math::box<float,3>::numCorners - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 . �   std::integral_constant<bool,1>::value 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 �    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi �   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask , %  	 std::numeric_limits<long>::digits10 P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity 0 �   std::numeric_limits<__int64>::is_signed - %  ? std::numeric_limits<__int64>::digits / %   std::numeric_limits<__int64>::digits10 d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 7 �   std::numeric_limits<unsigned short>::is_modulo 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible 6 �   std::numeric_limits<unsigned long>::is_modulo !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable 3 %    std::numeric_limits<unsigned long>::digits � #   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment 5 %  	 std::numeric_limits<unsigned long>::digits10 W #   std::allocator<donut::engine::ShaderMacro>::_Minimum_asan_allocation_alignment x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 Z�    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi 2 %   �僺td::numeric_limits<float>::min_exponent ]�   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard 4 %   �踫td::numeric_limits<float>::min_exponent10 , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent ; %  �威std::numeric_limits<long double>::min_exponent10 � #   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment " ;    std::memory_order_relaxed " ;   std::memory_order_consume " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst 5 �    std::filesystem::_File_time_clock::is_steady � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable    �   �  -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets '�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi  �   x�  4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment ) �   std::_Aligned<64,8,int,0>::_Fits E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g�    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos $ %   std::_Locbase<int>::collate " %   std::_Locbase<int>::ctype % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none : #    std::integral_constant<unsigned __int64,0>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN 7 �   std::_Iterator_base12::_Unwrap_when_unverified ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy - 9   std::_Invoker_pmf_refwrap::_Strategy - 9   std::_Invoker_pmf_pointer::_Strategy , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy a #   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment  �?   std::_Consume_header  �?   std::_Generate_header Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment / �   std::atomic<long>::is_always_lock_free ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo ) �   donut::math::vector<bool,2>::DIM ) �   donut::math::vector<bool,3>::DIM \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ) �   donut::math::vector<bool,4>::DIM 8 �   std::atomic<unsigned long>::is_always_lock_free : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift : �  � std::_Floating_type_traits<float>::_Exponent_mask E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex % #   std::ctype<char>::table_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,2>::value  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified * �   donut::math::vector<float,3>::DIM R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos # �        nvrhi::AllSubresources J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients - �    std::chrono::system_clock::is_steady $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den * �   donut::math::vector<float,2>::DIM L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34 	 !  tm %  7  _s__RTTICompleteObjectLocator2 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � 2<  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � #<  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � <  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鸖  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � 
T  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> � 4<  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � %<  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > iT  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � T  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 腟  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > � <  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �:  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > � 齋  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > �<  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> c �;  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> � �;  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > s k:  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � l9  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � _:  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > � 鳶  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 籕  std::_Ptr_base<donut::vfs::IFileSystem> �霺  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � �;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 釹  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> ��;  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> Q�;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � 芐  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > 稴  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> �;  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > \R  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > � �:  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > [ 癝  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit � [;  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * C2  std::hash<enum nvrhi::ResourceType> - 払  std::reverse_iterator<wchar_t const *> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file � 琒  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > � 淪  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >  �(  std::_Big_uint128 � I;  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > ) U6  std::_Narrow_char_traits<char,int>  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> R �.  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > 嘢  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >  f+  std::hash<int>  �5  std::_Num_int_base  錊  std::ctype<wchar_t> " �+  std::_System_error_category / a2  std::_Conditionally_enabled_hash<bool,1>  �5  std::float_denorm_style   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 2;  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast  玁  std::equal_to<void> 3 yI  std::_Ptr_base<donut::engine::ShaderFactory> � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > o 糝  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> 6 諭  std::initializer_list<nvrhi::BindingLayoutItem> " �5  std::numeric_limits<double>  C&  std::__non_rtti_object ( 0  std::_Basic_container_proxy_ptr12 � �.  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> 1   std::array<nvrhi::FramebufferAttachment,8>  �5  std::_Num_float_base  *  std::logic_error � �:  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> P�0  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> � �.  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � �:  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> � 
S  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � �:  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � �:  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  h:  std::false_type S �:  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #�0  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , �5  std::numeric_limits<unsigned __int64>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> 9 3.  std::shared_ptr<donut::engine::FramebufferFactory> \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 8 .  std::_Ptr_base<donut::engine::FramebufferFactory> s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 鯮  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16> � �.  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void>  ;  std::string_view  w  std::wstring_view � �0  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec   �*  std::_Init_once_completer �'  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � �(  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 � 蜶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > $ m  std::_Atomic_integral<long,4>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> 2 iJ  std::initializer_list<nvrhi::IBindingSet *> � u:  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >  �  std::hash<long double> 2 �1  std::equal_to<nvrhi::TextureSubresourceSet> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � �3  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o �.  std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � 1  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> H 鬒  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0 R 揤  std::_Default_allocator_traits<std::allocator<donut::engine::ShaderMacro> >  �  std::hash<double> H U  std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> > O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> D r:  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> x m:  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception 1 齌  std::allocator<donut::engine::ShaderMacro> & �.  std::_Zero_then_variadic_args_t � �0  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > >  �  std::u32string  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � a:  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > + G  std::pair<enum __std_win_error,bool> � 9  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void> S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder ) ~  std::_Atomic_integral_facade<long> % }2  std::hash<enum nvrhi::BlendOp>  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > ) q2  std::hash<enum nvrhi::BlendFactor> $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano I 橵  std::allocator_traits<std::allocator<donut::engine::ShaderMacro> >  �  std::_Iterator_base0 � *6  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > jH:  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � 蠷  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 �6  std::_Char_traits<char16_t,unsigned short> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> 9 紿  std::shared_ptr<donut::engine::CommonRenderPasses> ! �9  std::char_traits<char16_t> � �0  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char>  9  std::_Invoker_strategy  	G  std::nothrow_t � n9  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �5  std::_Default_allocate_traits N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > ! �5  std::numeric_limits<short> . d3  std::allocator<nvrhi::rt::GeometryDesc> ; ;  std::basic_string_view<char,std::char_traits<char> > �  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base � U  std::_Compressed_pair<std::allocator<donut::engine::ShaderMacro>,std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> >,1>  r*  std::out_of_range # �5  std::numeric_limits<__int64> i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  珸  std::ctype<char> R WV  std::_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> >  ;  std::memory_order  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState> / 訯  std::shared_ptr<donut::vfs::IFileSystem>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  [9  std::ratio<1,1> 3 ,J  std::initializer_list<nvrhi::BindingSetItem>   �8  std::forward_iterator_tag  �*  std::runtime_error ��0  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   	  std::bad_array_new_length ; �2  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > v W9  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  5  std::u16string ]�'  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >  \  std::nested_exception  �  std::_Distance_unknown ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , %@  std::codecvt<char32_t,char,_Mbstatet> @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> � �0  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc ; {.  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > , EO  std::default_delete<std::_Facet_base>  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> � 9  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  �8  std::ratio<1,10000000> ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag  �/  std::allocator<char> G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t & �8  std::random_access_iterator_tag 4 怚  std::shared_ptr<donut::engine::ShaderFactory> ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  x  std::wstring ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  -*  std::domain_error  �  std::u32string_view  �  std::_Container_base  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ 72  std::hash<nvrhi::IResource *> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet>  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage ! �+  std::_System_error_message  �  std::_Unused_parameter " �2  std::hash<nvrhi::IShader *> h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> � �0  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> 8   std::_Ptr_base<donut::engine::CommonRenderPasses> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q �1  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � �0  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> ^ �4  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > v 1  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> ' 訥  std::hash<std::filesystem::path>   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers � �6  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m m(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � ;(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> Z 賂  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > p ═  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Reallocation_policy 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling $ J  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �  nvrhi::ResourceStates . �5  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  �!  nvrhi::GraphicsState * �8  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16> ! �  nvrhi::SharedResourceFlags  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice> !    nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  �8  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # �$  nvrhi::DescriptorTableHandle  :  nvrhi::ShaderType  �$  nvrhi::TimerQueryHandle   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # �$  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice !    nvrhi::VariableShadingRate  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �5  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  �  nvrhi::StencilOp  ]!  nvrhi::IBindingLayout  �  nvrhi::ColorMask  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  .   nvrhi::PrimitiveType  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 J  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # �$  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline    nvrhi::CpuAccessMode  �$  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �  nvrhi::BlendOp  �"  nvrhi::ComputeState  �!  nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats $ H  donut::engine::ICompositeView  ?H  donut::engine::IView ( �7  donut::engine::CommonRenderPasses 5 �&  donut::engine::CommonRenderPasses::PsoCacheKey ; �&  donut::engine::CommonRenderPasses::PsoCacheKey::Hash $ �7  donut::engine::BlitParameters   fH  donut::engine::PlanarView ( �&  donut::engine::FramebufferFactory  哘  donut::engine::ViewType $ 
H  donut::engine::ViewType::Enum ! H  donut::engine::ShaderMacro # 荌  donut::engine::ShaderFactory " nH  donut::engine::StaticShader  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  /  donut::math::float2 # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane # �  donut::math::vector<float,4>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes  �  donut::math::float4 % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3>   �.  donut::math::box<float,2>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> ( hT  donut::render::EnvironmentMapPass M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24 & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  闠  SkyConstants  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t $ 梀  ProceduralSkyShaderParameters  u   uint32_t 
   _iobuf    __crt_locale_pointers �   �      q捉v�z�1乱#嗋�搇闚Q缨外
毛�  ?    譫鰿3鳪v鐇�6瘻x侃�h�3&�  }    j轲P[塵5m榤g摏癭 鋍1O骺�*�  �    k�8.s��鉁�-[粽I*1O鲠-8H� U     齝D屜u�偫[篔聤>橷�6酀嘧0稈  F   _O縋[HU-銌�鼪根�鲋薺篮�j��  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  $   �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  f   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     斝�/5:澫酡Z瞮<箼�漻M洛OIl~  U   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   *u\{┞稦�3壅阱\繺ěk�6U�     a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  N   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   繃S,;fi@`騂廩k叉c.2狇x佚�  �    狾闘�	C縟�&9N�┲蘻c蟝2     黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  a   悯R痱v 瓩愿碀"禰J5�>xF痧  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  $   �'稌� 变邯D)\欅)	@'1:A:熾/�  m   矨�陘�2{WV�y紥*f�u龘��  �   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  >    d蜯�:＠T邱�"猊`�?d�B�#G騋  z   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��     溶�$椉�
悇� 騐`菚y�0O腖悘T  Y   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   郖�Χ葦'S詍7,U若眤�M进`  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  #	   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  e	   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �	   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �	   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  C
   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �
   K4淂�%疟�3媊迍z�<駛餀>H,5 曏�  �
   猯�諽!~�:gn菾�]騈购����'  �
   �暊M茀嚆{�嬦0亊2�;i[C�/a\  *   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  j   険L韱#�簀O闚样�4莿Y丳堟3捜狰  �    Q?)QN忍$R.岕�>徱抴<芵e6聳B  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  +   チ畴�
�&u?�#寷K�資 +限^塌>�j  _   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �   穫農�.伆l'h��37x,��
fO��  
   5�\營	6}朖晧�-w氌rJ籠騳榈  a
   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �
   R冈悚3Y	�1P#��(勁灱�涰n跲
  �
   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦     �*o驑瓂a�(施眗9歐湬

�  `   zY{���睃R焤�0聃
扨-瘜}  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   蜅�萷l�/费�	廵崹
T,W�&連芿     v�%啧4壽/�.A腔$矜!洎\,Jr敎  `   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   D���0�郋鬔G5啚髡J竆)俻w��  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  4   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     `k�"�1�^�`�d�.	*貎e挖芺
脑�  ]   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   f扥�,攇(�
}2�祛浧&Y�6橵�  &   曀"�H枩U传嫘�"繹q�>窃�8  e   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   [届T藎秏1潴�藠?鄧j穊亘^a  �   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  =   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �   豊+�丟uJo6粑'@棚荶v�g毩笨C     A縏 �;面褡8歸�-構�壋馵�2�-R癕  J   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   鏀q�N�&}
;霂�#�0ncP抝  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷     dhl12� 蒑�3L� q酺試\垉R^{i�  A   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   匐衏�$=�"�3�a旬SY�
乢�骣�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�     荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  _   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9     矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  >   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   �"睱建Bi圀対隤v��cB�'窘�n  �   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��     o忍x:筞e飴刌ed'�g%X鶩赴5�n�  X   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  ;   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  y   G�膢刉^O郀�/耦��萁n!鮋W VS  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<     �*M�现.凿萰閱寴诃缶鲍6�#�+�4  F   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   �0�*е彗9釗獳+U叅[4椪 P"��     鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  Z   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   �=蔑藏鄌�
艼�(YWg懀猊	*)     交�,�;+愱`�3p炛秓ee td�	^,  \   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  5   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  s   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   L�9[皫zS�6;厝�楿绷]!��t  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  :   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  y   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   +4[(広
倬禼�溞K^洞齹誇*f�5     �7頔碠<晔@岙�撁k4統N絠熙鶳 �  X   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�      V� c鯐鄥杕me綻呥EG磷扂浝W)  _    a�傌�抣?�g]}拃洘銌刬H-髛&╟  �    v-�+鑟臻U裦@驍�0屽锯
砝簠@  �    双:Pj �>[�.ぷ�<齠cUt5'蠙砥  !!   副謐�斦=犻媨铩0
龉�3曃譹5D   c!   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �!   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �!   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  4"   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  t"   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �"   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  	#   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  K#   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �#   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �#   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  &$   �
bH<j峪w�/&d[荨?躹耯=�  e$   妇舠幸佦郒]泙茸餈u)	�位剎  �$   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �$   +椬恡�
	#G許�/G候Mc�蜀煟-  8%   靋!揕�H|}��婡欏B箜围紑^@�銵  x%   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �%   �颠喲津,嗆y�%\峤'找_廔�Z+�   &   鹴y�	宯N卮洗袾uG6E灊搠d�  H&   t�j噾捴忊��
敟秊�
渷lH�#  �&   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   `      f  8  B   g  8  H   h  8  Y   m  8  �   �    U   �    �   �  P  g   �  X  [   �  X  �  �  X  �  w  X  �  �  �  q   �     �   �  �  q   �  �  q   �  �  �  �  �  B  �  �  �
  �  �  �	  �  �  �	  �  X  t  �       �       �     �   �       �     �   �     �     �  �    �  �    �  @
    �  +
    �  �    �  �    �  �    �  �    X  5    (  K   (     �   )     �   *     �   A  �  �  B  �  D
  D  �  �  F  �  O   G  �  0   `    �  �    �  �    �  �  �  L
  �  �  L
  �    �   �    �   
     �        �     �  �    �  �  L  �  s  M  �  �  �  �  )
  �    �       �  8     �   9     �   >    �  g     �   V  X  i   X  X  �   ^  X  )  `  X  n  a  X  w  b  X  x  c  X    d  X  L  e  X  [  y       z     �   {     �   �       �     �   �       �     �   �  X  �  �  �  5   �  �  @   �  �  5   �  �  @   �  �  5   �     �   �       �     �   �     �   �     �   �  �  @   �  �  5   �  �  @   �  �  5   �  �  5   �  �  5   �  X  5    X  �   &       '     �   (     �   *       +     �   ,     �   /       0     �   1     �   2  X  t  :  �  q   ]  �
  �   u     �   �  �
  �   \     �   ^     �   :  X  ~  <  X  �  =  X  �  >  X  �  @  �  2   D  �  j  E  �  �  F  �  `  G  X  �  I  X  Z  J  X  Z  Z  �  �  [  X  t  b  �  <
  i    �  k  P  �   l  X  T  m  X  	  n  �
  9  p  �    q    �  r  X  D  s  X  D  y    >  |  �  �  ~  �  �  �  �
  �  �  X  n  �  X  n  �  �
  ^  �  �    �  �  �  �  �  �  �  �  ]  �  0  �   �    �  �    �  �    �  �    �  �      �    @   �   �&   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\RTXPT\External\Donut\src\render\EnvironmentMapPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\RTXPT\External\Donut\include\donut\render\EnvironmentMapPass.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h  �       L�  @  D   D  D  
 �$      �$     
 Y�  C   ]�  C  
 鰣      鷪     
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb �  �?                  �?                  �?    谐Y>Y7?樰�=H塡$3繪+罤�L嬞H堿H兟H堿�   H堿H堿 H堿(H堿0H堿8H嬃I岺0@ A�   ff�     驜 �YBX � 驜L �YJX润�T痼Y�X洋��YB�X麦 H兝I冴u獺兟H兞餓冭H冸u塇媆$I嬅�   �   B  I G            �      �   n        �donut::math::operator*<float,4,4,4> 
 >T   a  AK         
 >T   b  AP        
  M        �  , M        �  (- N N                        H & h   �  �  �  �  �  �  �  �      T  Oa     T  Ob     燡  Oresult  O  �   X           �   �
     L       9 �   : �
   9 �   : �@   < �P   > ��   ; ��   @ �,   3   0   3  
 k   3   o   3  
 �   3   �   3  
 X  3   \  3  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5         �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   7   0   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
   7     7  
 s  �   w  �  
 �  7   �  7  
 H塡$H塴$H墊$ AVH冹 H�������I嬝H嬯L嬹L;�囎   H茿   H凔wH塝�    B�3 閿   H嬅H塼$0H內H;莢)H�       �H兝'H嬋�    H吚t~H峱'H冩郒塅8�   H孁H;罤B鵋峅H侚   rH岮'H;羦Q刖H吷t
�    H嬸�3鯨嬅I�6H嬚I塣H嬑I墌�    � H媡$0H媆$8H媗$@H媩$HH兡 A^描    惕    惕    藾   W   u   �    �   �    �   W   �   �         	        �   g  � G                   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *> 
 >	   this  AJ        (  AV  (     � �  
 >C   _Arg  AK        %  AN  %     � �   >#   _Count  AI  "     � �   AP        "  >#     _New_capacity  AH  m       AJ  �     h   % >  AM       � N  � i  AH q       AJ q     c  P  AM �      % M        �  q	*I >p    _Fancy_ptr  AL  �       AL �     "  M          q.I M        >  q.I/ M        �  q.		
%
:. M        �  q(%"
P	 Z   q  }   >#    _Block_size  AH  �     [  O  AH q       >#    _Ptr_container  AH  y     �  p  AH �      
 >`    _Ptr  AL  �       AL �     "  M        �  q
 Z   �   N N M        �  ��
 Z   �   N N N N N M          R2! M        L  R') >#    _Masked  AH  ^     f   N  _   AH �       M          �� N N N M        G   C N M        G   �� N
 Z   �                         H N h   �  �  �      E  G  �  �      L  �  �  �    >  X         $LN56  0   	  Othis  8   C  O_Arg  @   #  O_Count  O �   �             �     �       L
 �   V
 �1   ^
 �?   _
 �C   f
 �H   g
 �M   q
 �R   u
 �q   v
 ��   u
 ��   v
 ��   
 ��   w
 ��   
 ��   y
 ��   
 ��   z
 ��   
 ��   �
 ��   �
 ��   v
 �  W
 �,   4   0   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
   4     4  
 '  4   +  4  
 O  4   S  4  
 _  4   c  4  
 w  4   {  4  
 �  4   �  4  
 �  4   �  4  
 �  4   �  4  
 �  4     4  
   4     4  
 �  4   �  4  
 �  4      4  
 %  4   )  4  
 9  4   =  4  
 X  4   \  4  
 h  4   l  4  
 '  4   +  4  
 C  4   G  4  
 '  �   +  �  
 |  4   �  4  
 H;蕋+H塡$WH冹 H孃H嬞H嬎�    H兠@H;遳颒媆$0H兡 _�   +      �   �  f G            1      1   y        �std::_Destroy_range<std::allocator<donut::engine::ShaderMacro> >  >oT   _First  AI         AJ          AJ 0       >颎   _Last  AK          AM         AK 0       >蠺   _Al  AP          AP          D@   
 Z   B                         H�  h   �  �  �   0   oT  O_First  8   颎  O_Last  @   蠺  O_Al  O�   @           1        4       > �    B �   > �   B �&   F �,   5   0   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
   5     5  
   5     5  
 �  5   �  5  
 L塂$H塗$H塋$SVWATAUAVAWH冹@L嬕H嬹H�L孃L+鳯媋L+郔咙I�������M;��  I�腍婭H+菻六H嬔H殃I嬃H+翲;葀L墝$�   I瞧���I峃'�<H�
I嬡I;腍C豂;�囁  L嬻I伶H墱$�   I侢   r@I峃'I;�啨  �    H吚劋  H峹'H冪郒塆鳫墊$ 3繪嫈$�   L媱$�   �:M咑t$I嬑�    H孁H塂$ 3繪嫈$�   L媱$�   �	3缷鳫塂$ H墱$�   I冪繫�,?I峕@H塡$0W繟E I塃I塃A AE AHAMI堾I茾   A�  W繟E I塃0I塃8A@ AE AH0AM0I堾0I茾8   A艪  L塴$(H媀H�L;襲H嬤�L嬑L嬊I嬕�    H墊$(H媽$�   H媀L嬑L嬅�    怘�H呟tNL媬I;遲H嬎�    H兠@I;遳颒�H媀H+袶冣繦侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�>I龄L鏛塮I�>H塏I嬇H兡@A_A^A]A\_^[描    惕    惕    碳   �    �   �    �  8   �  8   �  +   4  �    b     h  0   n  �       �   �
  � G            s     s  �        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Emplace_reallocate<donut::engine::ShaderMacro> 
 >lT   this  AJ        $  AL  $     O;  D�    >颎   _Whereptr  AK        !  AR  !     K� $ �  �� E  AR �      D�    >餑   <_Val_0>  AP        l� , �  �� f  AP �      D�    >#     _Newcapacity  AI  �     �5 3 � 9 AJ  }       AI �     �3 : y 9 AJ �       B�   r     
 & �  >#    _Newsize  AT  N     %�"   >#    _Whereoff  AW  *       >颎    _Constructed_last  AU  0    	  D0    >#    _Oldsize  AT  1     <    >oT    _Constructed_first  D(    >颎    _Newvec  AM  �     *    AM (    K6  D     M        �  �潃�佫 M        �  �潃�佫& M        �  ��)
@%	$丣( M        �  ��$	%)
亹
 Z   }   >#    _Block_size  AJ  �     	  AJ �     � � >#    _Ptr_container  AH  �       AH m     
 >`    _Ptr  AM  �       AM (    K6  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        �  
��
 N N N M        �  Nk >#    _Oldcapacity  AJ  R     �   +  ` < � !  AJ      F� �  >#    _Geometric  AH  �     �8 3 x  � H AH �       M        �  N N N M        �  0�<23 M        �  乷%
 M          0亅 M          亹$ N M        A  亅 N N M        �  
乷 M          乷�� M        M  乷 N N N N M        �  �<#
 M          0両 M          乗$ N M        A  両 N N M        �  
�< M          �<�� M        M  �< N N N N N, M        �  佲	I4#' M        i  *�_ M        �  �):
 Z     
 >   _Ptr  AJ 3      >#    _Bytes  AK      -    AK m     % M        �  �d#
=
 Z   q   >#    _Ptr_container  AP  #      AP 3    ?  5  >#    _Back_shift  AJ      ,  AJ 3    ?  5  N N N M        y  侇	
 >oT   _First  AI  �    {  AI m      >颎   _Last  AW  �    i  AW m      N N Z   �  �  �   @           8         0@ � h*   B  �  �  �  �  �        @  A  C  D  E  G  �  �  �  �  �  �  �    M  �  �  h  i  y  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN140  �   lT  Othis  �   颎  O_Whereptr  �   餑  O<_Val_0>  0   颎  O_Constructed_last  (   oT  O_Constructed_first  O�   �           s  �     �       * �$   3 �-   4 �8   6 �K   : �N   ; ��   = �   > �<  B ��  C ��  E ��  G ��  K ��  L ��  M ��  N ��  V �N  W �Q  X �a  = �g  7 �m  V ��   �  � F            C      C             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Emplace_reallocate<donut::engine::ShaderMacro>'::`1'::catch$7 
 >lT   this  EN  �         C  >颎   _Whereptr  EN  �         C  >餑   <_Val_0>  EN  �         C  Z   y  i   (                    � �        __catch$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z$0        $LN140  �   lT  Nthis  �   颎  N_Whereptr  �   餑  N<_Val_0>  0   颎  N_Constructed_last  (   oT  N_Constructed_first  O�   8           C   �     ,       P �   Q �"   R �9   S �,   6   0   6  
 �   6   �   6  
 �   6   �   6  
   6     6  
   6   #  6  
 ?  6   C  6  
 j  6   n  6  
 �  6   �  6  
 �  6   �  6  
 �  6   �  6  
 �  6   �  6  
 �  6   �  6  
 	  6   
  6  
 4  6   8  6  
 `  6   d  6  
 �  6   �  6  
 �  6   �  6  
   6   
  6  
   6     6  
 �  6   �  6  
 �  6   �  6  
   6   !  6  
 -  6   1  6  
 L  6   P  6  
 \  6   `  6  
    6   $  6  
 @  6   D  6  
 i  6   m  6  
 �  6   �  6  
 �  6   �  6  
 �  6     6  
   6     6  
 m  6   q  6  
 }  6   �  6  
 �  6   �  6  
 �  6   �  6  
 	  6   	  6  
 	  6   	  6  
 1	  6   5	  6  
 A	  6   E	  6  
 7
  �   ;
  �  
 �
  6   �
  6  
 �  9   �  9  
 {  9     9  
 �  9   �  9  
 �  9   �  9  
 
  �   
  �  
 �
  �   �
  �  
 x  9   |  9  
 H塗$SUH冹(H嬯L媴�   H婾0H婱(�    L媴�   H婾 H媿�   �    3�3设    �   5   5   .   >   Q   I嬂H;�剰   L嬋L岮8L+蒃3��    W繫岪@ O塗業岺萇塗燗@� AH�W�HM塒楬兝@I茾�   E圥圕D∣塗窸塗繟@–DˋH窩L窶塒窱茾�   E圥℉;蕌吤   �   {  � G            �       �   �        �std::_Uninitialized_move<donut::engine::ShaderMacro *,std::allocator<donut::engine::ShaderMacro> >  >颎   _First  AJ           AJ       |  h  >颎   _Last  AK        �  >oT   _Dest  AP          AP �       >蠺   _Al  AQ          AQ �       D     >WV   _Backout  CH     T     G  CH          | 4 G  M        �  
 N# M        �  #(B M        �  'B M        �  `& M          0p M          ��$ N M        A  p N N M        �  ` M          `�� M        M  ` N N N N M        �  '
 M          0
8 M          L( N M        A  
8 N N M        �  ' M          '��	 M        M  ' N N N N N N                        @ � h"   B  �  �  �        @  A  C  D  E  G  �  �  �  �  �    M  �  o  y  �  �  �  �  �  �  �  �  �  �  �      颎  O_First     颎  O_Last     oT  O_Dest      蠺  O_Al  O �   X           �        L       � �   � �   � �#   � �/   � �3   � ��   � ��   � �,   8   0   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
   8     8  
   8     8  
 1  8   5  8  
 A  8   E  8  
 p  8   t  8  
 �  8   �  8  
 �  8   �  8  
 H塡$3跮峇L岮L岼D峓f�     A婣鳰峈A堾鳰岪A婣麺岻A堾霢婣鬉堾餉塟餓冸u覌B$H媆$堿0婤(堿4婤,堿8H嬃茿<  �?�   �   �   O G            p      Q   m        �donut::math::affineToHomogeneous<float,3> 
 >�   a  AK        p                         H " h   &  O  H  �  �  �  �      �  Oa     燡  Oresult  O�   @           p   X     4       	 �     �D    �N    �Q    �,   2   0   2  
 q   2   u   2  
 �   2   �   2  
 H�  �?3繦堿H茿  �?H堿茿   �?婤��A$堿,H嬃�   �   �   G G            3       2   l        �donut::math::translation<float,3> 
 ><   a  AK        3  M          0  N                        H  h       ]  �      <  Oa     翵  Oresult  O �   8           3   X     ,       T �    U �/   V �2   W �,   1   0   1  
 i   1   m   1  
 �   1   �   1  
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AJ                                 H�     .!  Othis  O   �   0                    $       �  �    �  �   �  �,      0     
 �      �     
 �      �     
 H塡$H塼$H墊$AVH冹 3�W�H堿L嬺H堿H嬞H儂H媟vL�2H�������H;�嚻   H茿   H凗wH塹A閮   H嬑H兩H;蟰,H�       �H兝'H嬋�    H嬋H吚trH兝'H冟郒塇1�   H孂H;蔋B鶫峅H侚   rH岮'H;羦E牖H吷t�    L岶H�I嬛H塻H嬋H墈�    H媡$8H嬅H媆$0H媩$@H兡 A^描    惕    惕    虉   �    �   �    �   W     �    
             �   �  � G                   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >	   this  AI  +     � �   AJ        +  >   _Right  AK        �  � k  AK �     �  a U M        �  9.


?
 Z   �   >#     _New_capacity  AH  �       AJ  �     \  9  AM  C     � = " r ]  AH �       AJ �     `  L  AM �      & M        �  ��	-
= >p   _Fancy_ptr  AH �       C            "  C      9     � G " �  �    M          ��1
=  M        >  ��1
=. M        �  ��1		

8/ M        �  ��+%"
D	 Z   q  }   >#    _Block_size  AH  �     O  C  AH �       >#    _Ptr_container  AJ  �     |  d  AJ �      
 >`    _Ptr  AH  �       AH �       M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N N N N M          j8 M        L  j*, >#    _Masked  AJ  q     D    AJ �       M          �� N N N M        G   ^ N M        G   �� N N M          +	 >@    _Result  AV  $     � �   M        D  + N N M        �  
$ M          ������ M        M   N N N                       @ n h   �  �  �            D  G  �  �  �  �        L  M  �  �  �  �    >  X         $LN72  0   	  Othis  8     O_Right  O   �   8             �     ,       �	 �+   �	 ��   �	 �  �	 �,      0     
 �      �     
 �      �     
      	    
      !    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 Y     ]    
 m     q    
 �     �    
 h     l    
 |     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 S     W    
 l  e   p  e  
 �     �    
 H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >C   this  AJ                                 H     C  Othis  O   �                  X             �,      0     
 p      t     
 �      �     
 @USVWATAUAVAWH崿$仍���8,  �    H+�)�$ ,  H�    H3腍墔+  L塎楳孁L塃燣嬯H孂H塎L塃L塎 H媿�+  H塎℉塎(H嫕�+  H嫷�+  E3銵�'L塯L塯L塯L塯 L塯(L塯0I婣H吚t�@I�H塆(I婣H塆0L塯8L塯@H婣H吚t�@H�H塆8H婣H塆@H�H嬑�P 禜����柪W荔D$XL塪$hH�
    H�    劺HE��  W审�(  I瞧����M嬈f�     I�繠�< u鯤崓  �    �W�厐   H菂�      H菂�      �    �厐   �   墔�   �   f墔�   �   垍�   茀�    H崟�   H崓p  �    怘崟  H崓�  �    怘婽$`H;T$hta(卲  (崁  Jfo    f厐  茀p   (厫  B (崰  J0fo    f厾  茀�   H僁$`@�L崊p  H峀$X�    怘崓p  �    怘嫊�   H凓v5H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�
  �    怘嫊0  H凓v4H�翲媿  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐囂	  �    L塭癓塭窵塭繪塭萀塭蠰塭豧荄$@ H岲$XH塂$8H岴癏塂$0H岴繦塂$(H岴蠬塂$ L�
    L�    H峊$pI��    I嬙H峂郒;萾H�L� H�H�H吷tH��P怘婰$pH吷tL塪$pH��P怐塭8W�E@H荅X   艵@ 荅`    f荅d  荅g    f荅k 荅p   f荅t  D塭xH荅0�   H荅P   �    �E@�   塃H艵L 艵f荅<   I婨 L岴0H峊$xI嬐�悁   I嬙H峂鐷;萾H�L� H婳H塛H吷tH��P怘婰$xH吷tL塪$xH��P怘�E3繟峆H嬎�PL孁H嬓H婳8�    L嬥3蹓澊  垵�  3褹�   H崓�  �    墲�  菂�  �   菂�     菂�  �  �   f墔�  H塡$P艱$T
H婦$PH墔   H塡$P艱$TH婦$PH墔  H塡$P艱$TH婦$PH墔  嬎H墲�
  H崟   H�H墑袜  H媿�
  H�罤墠�
  H兟H崊  H;衭訦崓�  H崊�  �   @ f�      HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�I婨 L崊�  H峌�I嬐�怭  H嬘H峂餒;萾H�H�H婳H塛H吷tH��P怘婱�H吷tH塢�H��P惼�+  H媉5    H呟tH�H嬎�P �x; �
u�	菂�       垍�   H墲�   f菂�     )蛋   茀�    3蹓澣   菂�      H壍�       )呅   H婨楬� H媹�   墲�   菂�      H墠�   H墲�   H墲�   嬅H墲�  H崓�   @ H拎��
  I�   H媴�  H�繦墔�  H兞 H崟   H;蕌腁�  H崟�
  H崓   �    I婨 L婳L崊   H峌圛嬐�恅  H嬘H峂鳫;萾H�H�H婳H塛H吷tH��P怘婱圚吷tH塢圚��P惼叞  墲�  H墲�  H墲�  H墲�  H墲�  H墲�  H墲�  H崓�  �    菂x      W�3�厐  厫  H墔�  H�5    H塼$ L�
    峆D岪H崓�  �    怘墲�  茀�  I�I嬒�P8劺H婫(tH兝H�H兝PH�H媿�  H;藅(H呟tH�H嬎�PH媿�  H墲�  H吷tH��P怘�H媿�  H;藅(H呟tH�H嬎�PH媿�  H墲�  H吷tH��P怘婳H塋$PH吷tH��P怘塼$ L�
    �   D岯鼿崓8  �    �3繦墔`  H媆$PH呟tH�H嬎�PH媴`  H嵉8  H�4艸9t(H呟t
H�H嬎�P怘�H�H吷tH��P怘媴`  H�繦墔`  H呟t
H�H嬎�P�3鰦迱H嬛H崊8  H肏峂 H;萾H�H�0H媽�  H墧�  H吷tH��P怘兠H凔(r綡媴`  H墔�  L�
    �   D岯鼿崓8  �    怢�
    �   D岯鵋峀$P�    茀=  f菂)   茀,   I�I嬒�P8�   �   劺E褕�+  I婨 M嬏L崊�  H峌怚嬐��0  H嬛H峂H;萾H�H�0H婳 H塛 H吷tH��P怘婱怘吷tH塽怘��P怢�
    �   D岯鼿崓�  �    怘媿�  H吷tH壍�  H��P怘媿�  H吷tH壍�  H��P怘媿�  H吷tH壍�  H��P怘媿�  H吷tH壍�  H��P怘媿�  H吷tH壍�  H��P怘媿�  H吷tH壍�  H��P怘婾XH凓v1H�翲婱@H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘽  �    H塽PH荅X   艵@ H媆$XH呟taH媡$`H;辴�    H嬎�    H兠@H;辵颒媆$XH婽$hH+親冣繦嬅H侜   rH兟'H媅鳫+肏兝鳫凐囙   H嬎�    怘媇燞媅H呟t,A嬈�罜凐uH�H嬎�A嬈�罜凐u
H�H嬎�P怘婨楬媂H呟t,A嬈�罜凐uH�H嬎�A嬈�罜凐u
H�H嬎�P怘媇℉媅H呟t*A嬈�罜凐uH�H嬎�餌羢A凗u	H�H嬎�RH嬊H媿+  H3惕    (�$ ,  H伳8,  A_A^A]A\_^[]描    愯    愯    �   U   -   �   �   G     J   B  4   k  M   y  M   �  M   �  M   �     �     �  �     �   G  6   T  +   �  �    �  �    #  P   *  S   7  ,   �  V   �  V   Y  *   ~  Y     D   n  C     W   �     �      �     �  �    �     �  �    �	      �	  �    �	      �	  �    T
      i
  �    A  �    t  +   �  �    w  V   �  �    �  �    �  �       �   m,  [ G            �  ;   �  A        �donut::render::EnvironmentMapPass::EnvironmentMapPass 
 >TT   this  D   AJ        L  AM  L     ]G  D�,   >�$   device  AK        I  AU  I     `G  >軹   shaderFactory  D�    D   AI  �      AP        B  AW  B     aL AW �      D�,   >綡   commonPasses  D�    D    AH  }    � } AQ        �  AH 2    :  '  D�,   >.   framebufferFactory  D�    D(   AI  6      AJ  _     z  EO  (           D�,   >H   compositeView  AI  n     5�; EO  0           D�,   >�   environmentMap  AL  u     .d� EO  8           D�,   >�!    sampleFramebuffer  AT  `    =2  AT �      >H    sampleView  AW  Q    L;  AW �      >!   constantBufferDesc  CK  (       	  CK (   E      D0   >0     isCubeMap  A   �     ]  >賂    PSMacros  DX    >�    envMapDimension  A   �       >�    layoutDesc  D�   >V     pipelineDesc  D�   >�    bindingSetDesc  D    M        z  �( M        (  �(HB
 >�    temp  AJ  -      AJ >    
  B�2  �     	 Dx    N N M        y  %� M        z  � M        (  �
 >�    temp  AJ        AJ (      B�2  �     	 N N M        &  � >�    tmp  AK      "  AK (         N M        '  �C	 M        &  � N N N M        �  "兛 M          "兛 M          
兛( M        F  L兦 N N N N M        �  億 M          儉 N M        �  億 M          億 M        M  億 N N N N M        �  僟 M        )  僟HB
 >;     temp  AJ  d      AJ u    �  B 1  �     	 Dp    N N M        �  #�; M        �  僑 M        )  僑
 >;     temp  AJ  P      AJ _      B�0  �     	 N N M        �  僊 >;     tmp  AK  >       AK _    �    N M        g  �;C	 M        �  僄 N N N M        �  A倹 M          倹4
 M        B  4偍 M        `  1偒 M        �  偟)
 Z     
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    1  AK �      M        �  偩d >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  A俌奃 M          俌4
�7 M        B  4俧�7 M        `  1俰�4  M        �  俿)�
 Z     
 >   _Ptr  AH  s      AJ  p      AH �      >#    _Bytes  AK  i    9
1 
 M        �  倈d�
 Z   q   >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        D  ~佂 M        p  
佂,a
 Z   �   M        �  佡Y M        �  Y佡 M        �  -� M          0� M          �
 N M        A  � N N N M        �  ,佡 M          0佡 M          侇
 N M        A  佡 N N N N N N N M        @  仴 Z   �  �   N M        �  丣T M        �  &丵+7 M        G   7乬 N N M        �  丣 M          丣 M        M  丣 N N N N M        �  �


 Z   �  
 >C   _Ptr  AK      A  M          
�
 N M        �  �
 M          ���
 M        M  � N N N N M        F  �� M        q  �� M        ~  �� N N N M        I  �� M        r  ��M M        �  ��	 M        >  �� N N N M          ��� N N M        J  �� M        s  ��M M        �  ��	 M        >  �� N N N M        �  ��� N N M        ^  �� N M        \  �� N M        �   N M        {  { N M        �  u N M        w  7�2 M        �  �2*
 M        �  �?-
 >a&   this  AI  :    [  M        �  孴	 N N N N M        �  8孂 M        2  孂+
 M        �  �-
 >a&   this  AI      5  M        �  �	
 N N N N M        G  8嬂 M        [  嬂+
 M        �  嬐-
 >a&   this  AI  �    9  M        �  嬧	
 N N N N M        E  婾	#9�$ M        Z  婾

	9� M        i  1嫀�	  M        �  嫊)��
 Z     
 >   _Ptr  AH  �      AI  Z    ;    AH �      AI p    T  D  >#    _Bytes  AK  �      4 �  M        �  嫗d��
 Z   q   >#    _Ptr_container  AH  �      AI  �      N N N M        y  媑	 >oT   _First  AI  |    
  AI p      >颎   _Last  AL  d    9\ �  AL �    �  N N N M        �  K�
仚( M          �

1$
丷 M        B  1�亸 M        `  .�亴  M        �  �)乧
 Z     
 >   _Ptr  AH        AJ        AH @      >#    _Bytes  AK      .  AK �      M        �  �'d乹
 Z   q   >#    _Ptr_container  AH  2      AJ  /      N N N N N N M        �  婐 M        *  婐JB
 >7     temp  AJ  �
     * AJ 
    � * i  �  �   H i$  N N M        �  娭 M        )  娭JB
 >;     temp  AJ  �
      AJ �
      N N M        �  娂 M        )  娂JB
 >;     temp  AJ  �
      AJ �
      N N M        �  姠 M        )  姠JB
 >;     temp  AJ  �
      AJ �
      N N M        �  妶 M        )  妶JB
 >;     temp  AJ  �
      AJ �
      N N M        �  妌 M        )  妌JB
 >;     temp  AJ  u
      AJ �
      N N M        �  �= M        1  �=GB
 >�!    temp  AJ  A
      AJ Q
      BX6  �     	 D�    N N M        �  %� M        �  �1 M        1  �1
 >�!    temp  AJ  -
      AJ =
      B86  �     	 N N M        /  �) >�!    tmp  AK  
    "  AK =
         N M        0  �C	 M        /  �# N N N M        <  夦 N M        =  壸 N M        :  	壩 N M        ^  壡 N M        �  7堾 M        �  塴 M        �  塴
 >D     temp  B�5  �     	 N N M        �  塡 N M        (  堾C M        �  塚 N N N# M        �  埱I 
 >N!    i  AI  �    j  M        �  �, M        �  �,	
 N N M        :  堦
 M        u  堳
 >.!   this  AL  �    L  M        �  � M        �  �
 >D     temp  B�5  �     	 N N M        �  �	 N M        8  堹 M        
  堹#	 N N N N M        8  埿 M        
  堈# N N N M        8  垚	 M        
  垯# N N M        �  圷' M        �  垊 M        )  垊
 >;     temp  AJ  c    ,  
  AJ �      B85  �     	 N N M        �  坿 >;     tmp  AI  \    y  N M        9  坔 M          坔# N N N M        �  �"' M        �  圡 M        )  圡
 >;     temp  AJ  ,    ,  
  AJ Y    
  B5  �     	 N N M        �  團 >;     tmp  AI  %    7  N M        9  �1 M          �1# N N N M        �  嚮> N M        �  嚊
 N M        �  嚁 N M        �  噸 N M        �  噯 N M        �  � N M        �  噚 N M        �  嘩 M        ,  嘩GB
 >�!    temp  AJ  [      AJ k    >  B�4  �     	 D�    N N M        �  %�1 M        �  嘖 M        ,  嘖
 >�!    temp  AJ  G      AJ W      B�4  �     	 N N M        *  嘋 >�!    tmp  AK  4    "  AK W    W    N M        +  �1C	 M        *  �= N N N M        �  	啲, >_    <begin>$L0  AJ  �    T  M        �  喞 N N! M        e  唶&* >M   sampler  AJ  �    5  N M        c  哛(* N$ M        d  �*&G) N M        �  呮 M        �  呮GB
 >D     temp  AJ  �      AJ �    �    Bx3  �     	 D�    N N M        �  %吚 M        �  呞 M        �  呞
 >D     temp  AJ  �      AJ �      BX3  �     	 N N M        �  呉 >D     tmp  AK  �    "  AK �      4   N M        (  吚C	 M        �  吿 N N N M        �  勽I' >�    <begin>$L0  AK      ?  M        �  � N N M        b  
勣 >�    result  BP   �    � N M        `  
勅 >�    result  BP   �      N M        a  
劜 >�    result  BP   �      N M        �  刵$ N Z   B  �  �  "   8,          @         A �h�   B  �  �  �  �  �  �  �  �  �  w  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �                      (  )  *  @  A  B  C  D  E  F  G  `  �  �  �  �  �  �  �  �  �  �  �  �  �  
             L  M  �  �  �  �    8  9  >  X  g  ^  `  a  b  c  d  e               !  #  %  &  '  (  )  1  2  3  4  x  y  z  {  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  &  '  (  *  +  ,  /  0  1  2  :  u  �  �  \  ^  :  ;  <  =  >  @  D  E  F  G  I  J  Z  [  b  h  i  o  p  q  r  s  y  |  ~    �  �  �  �  �  �  �  �  �  �  �  
 :,  O        $LN864  �,  TT  Othis  �,  �$  Odevice  �,  軹  OshaderFactory  �,  綡  OcommonPasses  �,  .  OframebufferFactory  �,  H  OcompositeView  �,  �  OenvironmentMap  0  !  OconstantBufferDesc  X   賂  OPSMacros  �  �  OlayoutDesc  �  V   OpipelineDesc     �  ObindingSetDesc  9�       �   9[      �   9q      �   9�      b$   9$      �   9:      �   9K      H   9�      �$   9�      �   9�      �   9      =   9+      �$   9S      �   9g      �   9
      &H   9<      �   9U      �   9s      �   9�      �   9�      �   9�      �   9	      �   9	      �   97	      �   9t	      �   9�	      &H   9
      �$   99
      �   9M
      �   9�
      �   9�
      �   9�
      �   9�
      �   9�
      �   9      �   9�      b&   9�      b&   9      b&   9.      b&   9R      b&   9f      b&   O   �   H          �  @  &   <      9  �L   4  �u   9  ��   7  ��   8  ��   :  ��   ;  ��   >  ��   ?  ��  A  �|  D  ��  H  ��  E  ��  F  ��  G  ��  I  ��  J  �>  L  �Q  M  �`  O  �n  P  ��  Q  ��  R  ��  W  �  Z  �  _  �x  a  �   b  �  c  �Y  d  ��  e  ��	  g  ��	  h  ��	  p  �n
  q  �
  r  ��  ?  ��  r  ��   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$0 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$1 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$2 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$3 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$4 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$5 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$6 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$7 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$8 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  j F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$9 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O  �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$10 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$11 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$12 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$28 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$13 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$15 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$55 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$56 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$57 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$58 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$59 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$60 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O �   �  k F                                �`donut::render::EnvironmentMapPass::EnvironmentMapPass'::`1'::dtor$19 
 >TT   this  EN            EN  �,          >軹   shaderFactory  EN  �           EN            EN  �,          >綡   commonPasses  EN  �           EN             EN  �,          >.   framebufferFactory  EN  �           EN  (          >!    constantBufferDesc  EN  0          >賂    PSMacros  EN  X           >�    layoutDesc  EN  �          >V     pipelineDesc  EN  �          >�    bindingSetDesc  EN                                    �  O ,   (   0   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
   (     (  
 !  (   %  (  
 1  (   5  (  
 E  (   I  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
   (     (  
 E  (   I  (  
 ]  (   a  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
 '  (   +  (  
 X  (   \  (  
 l  (   p  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 "  (   &  (  
 2  (   6  (  
 B  (   F  (  
 �  (   �  (  
 �  (   �  (  
   (   #  (  
 /  (   3  (  
 ?  (   C  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
 $  (   (  (  
 	  (   	  (  
 !	  (   %	  (  
 1	  (   5	  (  
 R	  (   V	  (  
 b	  (   f	  (  
 �	  (   �	  (  
 �	  (   �	  (  
 �
  (   �
  (  
 �
  (   �
  (  
 �
  (   �
  (  
 �
  (   �
  (  
 "  (   &  (  
 2  (   6  (  
 �
  (   �
  (  
 �  (   �  (  
 !  (   %  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 C  (   G  (  
 S  (   W  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 �  (     (  
 T  (   X  (  
 d  (   h  (  
 �  (   �  (  
 �  (   �  (  
 V  (   Z  (  
 f  (   j  (  
 �  (   �  (  
 �  (   �  (  
 ,  (   0  (  
 <  (   @  (  
 �  (   �  (  
 �  (   �  (  
   (     (  
   (     (  
 m  (   q  (  
 }  (   �  (  
 �  (   �  (  
   (     (  
   (     (  
 "  (   &  (  
 b  (   f  (  
 r  (   v  (  
 �  (   �  (  
 *  (   .  (  
 �  (   �  (  
    (   $  (  
 k  (   o  (  
   (   �  (  
 �  (   �  (  
 �  (   �  (  
 t  (   x  (  
 �  (   �  (  
 �  (   �  (  
 �  (   �  (  
 %   (   )   (  
 5   (   9   (  
 E   (   I   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 !  (   !  (  
 *!  (   .!  (  
 �!  (   �!  (  
 "  (   #"  (  
 �"  (   �"  (  
 �"  (   �"  (  
 �"  (   �"  (  
 [#  (   _#  (  
 k#  (   o#  (  
 {#  (   #  (  
 �#  (   �#  (  
 �#  (   �#  (  
 \$  (   `$  (  
 �$  (   �$  (  
 �$  (   �$  (  
 %%  (   )%  (  
 �(  t   �(  t  
 �)  (   �)  (  
 �)  (   �)  (  
 	*  (   
*  (  
 *  (   *  (  
 )*  (   -*  (  
 9*  (   =*  (  
 I*  (   M*  (  
 Y*  (   ]*  (  
 i*  (   m*  (  
 y*  (   }*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 �*  (   �*  (  
 	+  (   
+  (  
 +  (   +  (  
 )+  (   -+  (  
 9+  (   =+  (  
 I+  (   M+  (  
 Y+  (   ]+  (  
 i+  (   m+  (  
 y+  (   }+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 �+  (   �+  (  
 	,  (   
,  (  
 ,  (   ,  (  
 ),  (   -,  (  
 9,  (   =,  (  
 I,  (   M,  (  
 Y,  (   ],  (  
 i,  (   m,  (  
 �,  (   �,  (  
 �-  :   �-  :  
 [.  :   _.  :  
 o.  :   s.  :  
 �.  :   �.  :  
 �.  :   �.  :  
 �.  :   �.  :  
 �.  :   �.  :  
 /  :   /  :  
 /  :   /  :  
 G/  :   K/  :  
 [/  :   _/  :  
 �/  :   �/  :  
 �/  :   �/  :  
 �/  :   �/  :  
 0  :   0  :  
 40  :   80  :  
 �0  A   �0  A  
 �0  A   �0  A  
 1  A   1  A  
 /1  A   31  A  
 C1  A   G1  A  
 W1  A   [1  A  
 �1  A   �1  A  
 �1  A   �1  A  
 �1  A   �1  A  
 �1  A   �1  A  
 �1  A   �1  A  
  2  A   $2  A  
 G2  A   K2  A  
 p2  A   t2  A  
 �2  A   �2  A  
 �2  A   �2  A  
 3  C    3  C  
 �3  C   �3  C  
 �3  C   �3  C  
 �3  C   �3  C  
 �3  C   �3  C  
 �3  C   �3  C  
 4  C   4  C  
 *4  C   .4  C  
 >4  C   B4  C  
 o4  C   s4  C  
 �4  C   �4  C  
 �4  C   �4  C  
 �4  C   �4  C  
 5  C   5  C  
 /5  C   35  C  
 \5  C   `5  C  
 �5  D   �5  D  
 6  D   6  D  
 +6  D   /6  D  
 W6  D   [6  D  
 k6  D   o6  D  
 6  D   �6  D  
 �6  D   �6  D  
 �6  D   �6  D  
 �6  D   �6  D  
 7  D   7  D  
 7  D   7  D  
 H7  D   L7  D  
 o7  D   s7  D  
 �7  D   �7  D  
 �7  D   �7  D  
 �7  D   �7  D  
 D8  E   H8  E  
 �8  E   �8  E  
 �8  E   �8  E  
 �8  E   �8  E  
 �8  E   9  E  
 9  E   9  E  
 >9  E   B9  E  
 R9  E   V9  E  
 f9  E   j9  E  
 �9  E   �9  E  
 �9  E   �9  E  
 �9  E   �9  E  
 :  E   :  E  
 ,:  E   0:  E  
 W:  E   [:  E  
 �:  E   �:  E  
 �:  K   �:  K  
 ?;  K   C;  K  
 S;  K   W;  K  
 ;  K   �;  K  
 �;  K   �;  K  
 �;  K   �;  K  
 �;  K   �;  K  
 �;  K   �;  K  
 �;  K   �;  K  
 +<  K   /<  K  
 ?<  K   C<  K  
 p<  K   t<  K  
 �<  K   �<  K  
 �<  K   �<  K  
 �<  K   �<  K  
 =  K   =  K  
 l=  M   p=  M  
 �=  M   �=  M  
 �=  M   �=  M  
 >  M   >  M  
 '>  M   +>  M  
 ;>  M   ?>  M  
 f>  M   j>  M  
 z>  M   ~>  M  
 �>  M   �>  M  
 �>  M   �>  M  
 �>  M   �>  M  
 ?  M   ?  M  
 +?  M   /?  M  
 T?  M   X?  M  
 ?  M   �?  M  
 �?  M   �?  M  
  @  N   @  N  
 g@  N   k@  N  
 {@  N   @  N  
   N   獲  N  
 籃  N   緻  N  
 螥  N   覢  N  
 鶣  N   兀  N  
 A  N   A  N  
 "A  N   &A  N  
 SA  N   WA  N  
 gA  N   kA  N  
 楢  N   淎  N  
 緼  N   肁  N  
 鐰  N   霢  N  
 B  N   B  N  
 @B  N   DB  N  
 擝  O   楤  O  
 鸅  O   �B  O  
 C  O   C  O  
 ;C  O   ?C  O  
 OC  O   SC  O  
 cC  O   gC  O  
 嶤  O   扖  O  
   O     O  
 禖  O   篊  O  
 鏑  O   隒  O  
 鸆  O   �C  O  
 ,D  O   0D  O  
 SD  O   WD  O  
 |D  O   �D  O  
   O   獶  O  
 訢  O   谼  O  
 (E  P   ,E  P  
 廍  P   揈  P  
   P     P  
 螮  P   覧  P  
 鉋  P   鏓  P  
 鱁  P   鸈  P  
 "F  P   &F  P  
 6F  P   :F  P  
 JF  P   NF  P  
 {F  P   F  P  
 廎  P   揊  P  
 繤  P   腇  P  
 鏔  P   隖  P  
 G  P   G  P  
 ;G  P   ?G  P  
 hG  P   lG  P  
 糋  ;   繥  ;  
 $H  ;   (H  ;  
 8H  ;   <H  ;  
 dH  ;   hH  ;  
 xH  ;   |H  ;  
 孒  ;   怘  ;  
 稨  ;   籋  ;  
 薍  ;   螲  ;  
 逪  ;   鉎  ;  
 I  ;   I  ;  
 $I  ;   (I  ;  
 UI  ;   YI  ;  
 |I  ;   �I  ;  
   ;   ㊣  ;  
 蠭  ;   訧  ;  
 齀  ;   J  ;  
 PJ  <   TJ  <  
 窲  <   糐  <  
 蘆  <   蠮  <  
 鳭  <   麶  <  
 K  <   K  <  
  K  <   $K  <  
 KK  <   OK  <  
 _K  <   cK  <  
 sK  <   wK  <  
   <   ↘  <  
 窴  <   糑  <  
 镵  <   鞬  <  
 L  <   L  <  
 9L  <   =L  <  
 dL  <   hL  <  
 慙  <   昄  <  
 銵  =   鐻  =  
 LM  =   PM  =  
 `M  =   dM  =  
 孧  =   怣  =  
 燤  =     =  
 碝  =   窶  =  
 進  =   鉓  =  
 驧  =   鱉  =  
 N  =   N  =  
 8N  =   <N  =  
 LN  =   PN  =  
 }N  =   丯  =  
   =   ∟  =  
 蚇  =   袾  =  
 鳱  =   麼  =  
 %O  =   )O  =  
 xO  B   |O  B  
 郞  B   銸  B  
 鬙  B   鳲  B  
  P  B   $P  B  
 4P  B   8P  B  
 HP  B   LP  B  
 sP  B   wP  B  
 嘝  B   婸  B  
 汸  B   烶  B  
 蘌  B   蠵  B  
 郟  B   銹  B  
 Q  B   Q  B  
 8Q  B   <Q  B  
 aQ  B   eQ  B  
 孮  B   怮  B  
 筈  B   絈  B  
 R  >   R  >  
 tR  >   xR  >  
 圧  >   孯  >  
 碦  >   窻  >  
 萊  >   蘎  >  
 躌  >   郣  >  
 S  >   S  >  
 S  >   S  >  
 /S  >   3S  >  
 `S  >   dS  >  
 tS  >   xS  >  
   >   ㏒  >  
 蘏  >   蠸  >  
 鮏  >   鵖  >  
  T  >   $T  >  
 MT  >   QT  >  
 燭  ?     ?  
 U  ?   U  ?  
 U  ?    U  ?  
 HU  ?   LU  ?  
 \U  ?   `U  ?  
 pU  ?   tU  ?  
 沀  ?   烾  ?  
 疷  ?   砋  ?  
 肬  ?   荱  ?  
 鬠  ?   鳸  ?  
 V  ?   V  ?  
 9V  ?   =V  ?  
 `V  ?   dV  ?  
 塚  ?   峍  ?  
 碫  ?   竀  ?  
 酼  ?   錠  ?  
 4W  F   8W  F  
 淲  F   燱  F  
 癢  F   碬  F  
 躓  F   郬  F  
 餡  F   鬢  F  
 X  F   X  F  
 /X  F   3X  F  
 CX  F   GX  F  
 WX  F   [X  F  
 圶  F   孹  F  
 淴  F   燲  F  
 蚗  F   裍  F  
 鬤  F   鳻  F  
 Y  F   !Y  F  
 HY  F   LY  F  
 uY  F   yY  F  
 萗  G   蘗  G  
 0Z  G   4Z  G  
 DZ  G   HZ  G  
 pZ  G   tZ  G  
 刏  G   圸  G  
 榋  G   淶  G  
 肸  G   荶  G  
 譠  G   踆  G  
 隯  G   颶  G  
 [  G    [  G  
 0[  G   4[  G  
 a[  G   e[  G  
 圼  G   孾  G  
 盵  G   礫  G  
 躘  G   郲  G  
 	\  G   
\  G  
 \\  H   `\  H  
 腬  H   萛  H  
 豛  H   躙  H  
 ]  H   ]  H  
 ]  H   ]  H  
 ,]  H   0]  H  
 W]  H   []  H  
 k]  H   o]  H  
 ]  H   僝  H  
 癩  H   碷  H  
 腯  H   萞  H  
 鮙  H   鵠  H  
 ^  H    ^  H  
 E^  H   I^  H  
 p^  H   t^  H  
 漗  H     H  
 館  I   鬪  I  
 X_  I   \_  I  
 l_  I   p_  I  
 榑  I   淿  I  
 琠  I   癬  I  
 繽  I   腳  I  
 隷  I   颻  I  
 �_  I   `  I  
 `  I   `  I  
 D`  I   H`  I  
 X`  I   \`  I  
 塦  I   峘  I  
 癭  I   碻  I  
 賎  I   輅  I  
 a  I   a  I  
 1a  I   5a  I  
 刟  J   坅  J  
 靉  J   餫  J  
  b  J   b  J  
 ,b  J   0b  J  
 @b  J   Db  J  
 Tb  J   Xb  J  
 b  J   僢  J  
 揵  J   梑  J  
   J   玝  J  
 豣  J   躡  J  
 靊  J   餬  J  
 c  J   !c  J  
 Dc  J   Hc  J  
 mc  J   qc  J  
 榗  J   渃  J  
 與  J   蒫  J  
 d  L   d  L  
 �d  L   刣  L  
 攄  L   榙  L  
 纃  L   膁  L  
 詃  L   豥  L  
 鑔  L   靌  L  
 e  L   e  L  
 'e  L   +e  L  
 ;e  L   ?e  L  
 le  L   pe  L  
 �e  L   別  L  
 眅  L   礶  L  
 豦  L   躤  L  
 f  L   f  L  
 ,f  L   0f  L  
 Yf  L   ]f  L  
 琭  @   癴  @  
 g  @   g  @  
 (g  @   ,g  @  
 Tg  @   Xg  @  
 hg  @   lg  @  
 |g  @   �g  @  
   @   玤  @  
 籫  @   縢  @  
 蟝  @   觛  @  
  h  @   h  @  
 h  @   h  @  
 Eh  @   Ih  @  
 lh  @   ph  @  
 昲  @   檋  @  
 纇  @   膆  @  
 韍  @   駂  @  
 H媻(  �       '   H媻   �       &   H媻  �       -   H媻  �          H媻  H兞�       #   H媻  H兞�           H媻  H兞�       $   H媻  H兞 �       %   H媻  H兞(�       &   H媻  H兞8�       '   H崐X   �       /   H崐  �          H崐�  �          H崐p  �       +   H崐0  �          H崐�  �       !   H崐p  �          H崐�  H兞�          H崐�  H兞�          H崐�  H兞�          H崐�  H兞 �          H崐�  H兞(�          H崐�  H兞0�          3繦�H堿H嬃�   �   �   7 G            
          X        �nvrhi::Rect::Rect 
 >f   this  AJ        
                         H     f  Othis  O   �               
   X            �  �,      0     
 \      `     
 �      �     
 � 3狼AW狼A W汕A茿 茿茿 茿茿  茿$茿( 茿,茿0 茿4茿8 茿<茿@ H堿\H堿d茿D �� 茿H 茿L艫P茿T    f茿X  艫Z AlI|墎�   H嬃�   �   �   E G            �       �           �nvrhi::RenderState::RenderState 
 >�   this  AJ        �                         @ " h   �                  �  Othis  O ,      0     
 j      n     
 � H嬃茿�   �   �   S G                              �nvrhi::BlendState::RenderTarget::RenderTarget 
 >�   this  AJ                                 H�     �  Othis  O   ,      0     
 x      |     
 H�    H嬃�   �   �   U G                   
   �        �nvrhi::VertexBufferBinding::VertexBufferBinding 
 >�!   this  AJ                                 H�     �!  Othis  O ,   "   0   "  
 z   "   ~   "  
 3狼A  �?H�H堿堿H嬃�   �   �   ? G                      V        �nvrhi::Viewport::Viewport 
 >R   this  AJ                                 H     R  Othis  O   �                  X            i  �,      0     
 d      h     
 �      �     
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�      %      ,         �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   	   0   	  
 d   	   h   	  
 t   	   x   	  
 �   	   �   	  
 �   	   �   	  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�      %      ,   !      �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,      0     
 z      ~     
 �      �     
 �      �     
 �      �     
 H�    H茿    H堿H�    H�H嬃�   $      !      �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !   8     ,       �  �    �  �   �  �   �  �,   
   0   
  
 z   
   ~   
  
   
     
  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�      %         �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2   8     $       H  �   I  �)   J  �,      0     
 d      h     
 t      x     
 �      �     
 �      �     
          
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AH         AJ          AH        M        �  GCE
 >D     temp  AJ  
       AJ        N (                     0H� 
 h   �   0   .!  Othis  9       �   O�   0           "         $       �  �   �  �   �  �,       0      
 �       �      
 �       �      
 �       �      
 �       �      
            
 P      T     
 h      l     
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >3J   this  AH         AJ          AH        M        ,  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   ,   0   3J  Othis  9       �   O  �   0           "         $       �  �   �  �   �  �,   $   0   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
 �   $     $  
 J  $   N  $  
 d  $   h  $  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         z        �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >?I   this  AH         AJ          AH        M        (  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   (   0   ?I  Othis  9       �   O  �   0           "         $       �  �   �  �   �  �,   #   0   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 B  #   F  #  
 \  #   `  #  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   Z  z G            "         �        �nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>::~RefCountPtr<nvrhi::IGraphicsPipeline> 
 >鸌   this  AH         AJ          AH        M        1  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   1   0   鸌  Othis  9       �   O  �   0           "         $       �  �   �  �   �  �,   %   0   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 	  %   
  %  
 V  %   Z  %  
 p  %   t  %  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �        �nvrhi::RefCountPtr<nvrhi::IInputLayout>::~RefCountPtr<nvrhi::IInputLayout> 
 >^    this  AH         AJ          AH        M        *  GCE
 >7     temp  AJ  
       AJ        N (                     0H� 
 h   *   0   ^   Othis  9       �   O�   0           "         $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
 L     P    
 d     h    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�    this  AH         AJ          AH        M        )  GCE
 >;     temp  AJ  
       AJ        N (                     0H� 
 h   )   0   �   Othis  9       �   O  �   0           "         $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 B     F    
 \     `    
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �    Y   �       �   �  � G            ^      ^   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >	   this  AI  
     Q J   AJ        
 ) M          ,(
	 M        D   N M        B  ,E M        `  &? M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        �  
"#
!
 Z   q   >#    _Ptr_container  AP  &     7    AP :       >#    _Back_shift  AJ  -     0 
   N N N N N                       H� 6 h   �  �  �      B  C  D  E  `  �  �         $LN33  0   	  Othis  O�   H           ^   �     <       B �   C �
   B �
   C �R   J �X   C �,      0     
 �      �     
 �      �     
 �     �    
 �     �    
 ,     0    
 @     D    
 f     j    
 �  g   �  g  
          
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �        �std::shared_ptr<donut::engine::CommonRenderPasses>::~shared_ptr<donut::engine::CommonRenderPasses> 
 >慔   this  AJ        +  AJ @       M        2  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  2   0   慔  Othis  9+       b&   9=       b&   O  �   0           K   X     $       � �   � �E   � �,   &   0   &  
 �   &   �   &  
 �   &   �   &  
   &     &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   w        �std::shared_ptr<donut::engine::FramebufferFactory>::~shared_ptr<donut::engine::FramebufferFactory> 
 >.   this  AJ        +  AJ @       M        �  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  �   0   .  Othis  9+       b&   9=       b&   O  �   0           K   X     $       � �   � �E   � �,   '   0   '  
 �   '   �   '  
 �   '   �   '  
   '     '  
 �  '   �  '  
 �  '   �  '  
 �  '   �  '  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   G        �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >}I   this  AJ        +  AJ @       M        [  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  [   0   }I  Othis  9+       b&   9=       b&   O�   0           K   X     $       � �   � �E   � �,   -   0   -  
 �   -   �   -  
 �   -   �   -  
   -   
  -  
 |  -   �  -  
 �  -   �  -  
 �  -   �  -  
 H塡$VH冹 H�H嬹H呟teH墊$0H媦H;遲H嬎�    H兠@H;遳颒�H媀H媩$0H+袶冣繦侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媆$8H兡 ^描    �'   +   i   �    �   �       �   L  � G            �   
   �   E        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::~vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > 
 >lT   this  AJ          AL       { t  . M        Z  
	
I9%	 M        i  ;*B M        �  G)
 Z     
 >   _Ptr  AJ h       >#    _Bytes  AK  ;     O   2  " M        �  
P#
 
 Z   q   >#    _Ptr_container  AP  T     6    AP h       >#    _Back_shift  AJ  7     S 1   AJ h       N N N M        y  	
 >oT   _First  AI  
     ~ r   >颎   _Last  AM       "  N N                       H� . h
   �  �  �  Z  h  i  y  �  �  �         $LN43  0   lT  Othis  O�   H           �   �     <       � �
   � �
   � �   � �z    ��   � �,   /   0   /  
 �   /   �   /  
 �   /     /  
 �  /   �  /  
 �  /   �  /  
   /     /  
 /  /   3  /  
 U  /   Y  /  
 i  /   m  /  
 �  /   �  /  
 �  /   �  /  
 4  |   8  |  
 `  /   d  /  
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �    [   �       �   �  D G            `      `   #        �nvrhi::BufferDesc::~BufferDesc 
 >   this  AI  
     S L   AJ        
  M        �  GM) M          -(

 M        D   N M        B  -G M        `  &@ M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   q   >#    _Ptr_container  AP  '     8    AP ;       >#    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0     Othis  O ,      0     
 i      m     
 }      �     
 ]     a    
 �     �    
 �     �    
 �     �    
          
 �  i   �  i  
 H塡$WH冹 H嬞H伭�   L�
    �   D岯    怘婯03�H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘媆$0H兡 _�       %   �       �   �  X G            �   
   �   &        �nvrhi::GraphicsPipelineDesc::~GraphicsPipelineDesc 
 >,    this  AI  
     �  AJ        
  M        �  �� M        *  ��DE
 >7     temp  AJ  �       AJ �       N N M        �  | M        )  |DE
 >;     temp  AJ  �       AJ �       N N M        �  h M        )  hDE
 >;     temp  AJ  l       AJ |       N N M        �  T M        )  TDE
 >;     temp  AJ  X       AJ h       N N M        �  @ M        )  @DE
 >;     temp  AJ  D       AJ T       N N M        �  * M        )  *DG
 >;     temp  AJ  .       AJ @       N N                      0H�  h   �  �  )  *  %  )   0   ,   Othis  9<       �   9P       �   9d       �   9x       �   9�       �   9�       �   O  ,   !   0   !  
 }   !   �   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
 I  !   M  !  
 Y  !   ]  !  
 �  !   �  !  
 �  !   �  !  
   !     !  
 +  !   /  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  !     !  
 b  !   f  !  
 r  !   v  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐wkI嬋�    H荂0    H荂8   艭  H婼H凓v,H�H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �<   �    �   �    �   �       �   x  N G            �      �   B        �donut::engine::ShaderMacro::~ShaderMacro 
 >颎   this  AI  
     � �   AJ        
  M        �  ITO& M          T
,(
	 M        D  T N M        B  ,^E M        `  ^&? M        �  d)
 Z     
 >   _Ptr  AJ  a     )  
  >#    _Bytes  AK  d     &  AK �      " M        �  
m#
!
 Z   q   >#    _Ptr_container  AP  q       AP �     #    >#    _Back_shift  AJ  x     
  AJ �       N N N N N N M        �  G$ M          -( M        D   N M        B  - M        `  & M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       &  AK �       M        �  
##
 >#    _Ptr_container  AP  '       AP ;     m  c  >#    _Back_shift  AJ  .     
  AJ �       N N N N N N                       @� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN70  0   颎  Othis  O,   +   0   +  
 s   +   w   +  
 �   +   �   +  
 ^  +   b  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +      +  
 &  +   *  +  
 6  +   :  +  
   +     +  
 @  +   D  +  
 P  +   T  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 `  w   d  w  
 H�    H�H兞�                   �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,      0     
 {           
 H�    H�H兞�                   �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (              8            Y  �
   Z  �,      0     
 e      i     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
            0   �       �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   
   0   
  
 w   
   {   
  
 �   
   �   
  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
            0   �       �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,      0     
 �      �     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
            0   �       �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,      0     
 w      {     
 �      �     
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 @UVWAUAVAWH崿$各��H侅H	  H�    H3腍墔  H�H孃L孂H�    H嬒I嬸�怭  H�E3鞨嬑E嬽A峌�吚凷  H墱$�	  H岲$xL墹$@	  L峞�)�$0	  L+�)�$ 	  �=    @ �     H�E嬈�   H嬑�P3襆壄�   A竴  H崓   H嬝�    �   H崓  D  L塱鳯�)D塱茿  �?H岻H冴u銩�   L壄�  H崓�  �    �   H崊�  L塰鳯�(H岪H冮u�W繪壄�  3繢壄�  3襀墔�  A竫  L壄�  H崓�  L壄�  叏  D埈�  吀  L壄�  �    H崊�  �   ff�     L�(H岪H冮u驣婫 H嬘I婳8H墔�   L壄X  L壄`  L壄p  �    L$XH墔�   H崟�  I婫H嬎H塂$HD$HH荄$p   嵏  叏  D$h吶  H��P H崓   �   H崏�    H崁�   A�H�I�@�A�H�I�@�A�H�I�@�A�H�I餒冴u� 3繦峌鴫E\塃lH嬎塃|墔�   墔�   墔�   H��Ph(    L岴�(惹D$h  �?D$HH峂糀�   �HH峊$L�PW�D$X� W�W求L$p�D$l�T$t 婤麺岪堿麳岻�H峈堿饗B鴫A鬍塰餓冮u貶�H崟�   A��E鐷嬎�M祗U鹎E�  �?�惏   W繦峀$xW蒊嬙D$xL岪A�   M�E�M�@ 驛X   驛`A(驛p@ (�(芋YT�(腕Y�X�YL
�X�(企YD
 �X洋X畜H兞H冭u縄兝H兟餓冴u�D$xH�A範   M圛媁L岴)EH嬒E楲塴$ )M M�)E0)M@�PxH�H崟�   H嬒�惏   H�H峊$0H嬒L塴$8D塴$@荄$4   荄$0   �惛   H��   H嬑A��D;��
��(�$ 	  (�$0	  L嫟$@	  H嫓$�	  H�H嬒�怷  H媿  H3惕    H伳H	  A_A^A]_^]�   �   6   Y   �   �   �   Y     Y   �  Y   �  *   �  �   �  V      �     O G            �  *   �  C        �donut::render::EnvironmentMapPass::Render 
 >TT   this  AJ        3  AW  3     � >�$   commandList  AK        0  AM  0     � >H   compositeView  AL  @     � AP        @  >u     viewIndex  An  R     � >闠    skyConstants  D  
 >�"    args  D0    >�!    state  D�  
 >H    view  AI  �     � AI �     * % M        n  僸A M        �  僸 M        �  僸/$ N N N$ M        m  
�

 N M        l  偟, M          偟, N N M        k  傏


 ><   a  AH  �    ^  N M        �  侒	
 M        �  侒	
 N N" M        �  丒	
*: N! M        �  �<" N M        �  乀 N" M        �  ��
\ M        X  �! N N% M        �  ��
	" M        V  �� N N
 Z   �   H	          0          A � h1   �  �  �  �  �  �  %  &  O  V  X     +  -  .  /  0  x  �  �  �  �  �  �  �  �  �  �  �  �  �      ]  �  H  k  l  m  n  �  �  �  �  �  �  �  �  �  
 :	  O  �	  TT  Othis  �	  �$  OcommandList  �	  H  OcompositeView    闠  OskyConstants  0   �"  Oargs  �  �!  Ostate  9@       &%   9V       H   9�       H   9#      #H   9�      (H   9]      +H   99      �$   9I      �$   9t      %   9�      H   9�      �$   O�             �  @           w  �*   x  �F   z  ��   |  ��   ~  ��   |  ��   ~  ��   |  ��   ~  ��    ��  �  ��  ~  ��  �  ��  �  ��  �  ��  �  �   �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �<  �  �O  �  ��  z  ��  �  ��  �  �,   )   0   )  
 t   )   x   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
   )     )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 l  )   p  )  
 |  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
 �  )      )  
   )     )  
 $  )   (  )  
 H冹HH峀$ �    H�    H峀$ �    �
   
      '      Q      �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               �            J �   K �,      0     
 �   a   �   a  
 �      �     
 H冹(H�
    �    �   @             �   w   7 G                     �        坰td::_Xlen_string 
 Z   j   (                      @        $LN3  O �   (              �            		 �   
	 �,      0     
 s   c   w   c  
 �      �     
 H冹(H�
    �    �   \             �   �   � G                     �        坰td::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Xlength 
 Z   j   (                      @        $LN3  O �   (              �            a �   b �,   0   0   0  
 �   ~   �   ~  
 �   0   �   0  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �    <   �       �   d  \ G            A      A   i        �std::allocator<donut::engine::ShaderMacro>::deallocate 
 >鬞   this  AJ          AJ ,       D0   
 >颎   _Ptr  AK        @ /   >#   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        �  
#

 Z   q   >#    _Ptr_container  AJ       (    AJ ,       >#    _Back_shift  AH         AH ,       N N (                      H  h   �  �         $LN18  0   鬞  Othis  8   颎  O_Ptr  @   #  O_Count  O�   8           A        ,       � �   � �2   � �6   � �,   .   0   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
   .   "  .  
 ?  .   C  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 �  .   �  .  
 $  z   (  z  
 x  .   |  .  
 H婹H�    H呉HE旅         �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              8     $       ^  �    _  �   `  �,      0     
 _      c     
 �      �     
  d T 4 2p    H           Z      Z      �    20    2           [      [      �   
 
4 
2p    B           \      \      �    20    <           ]      ]      �   
 
4 
2p    B           ^      ^      �    20    <           _      _      �   
 
4 
2p    B           `      `      �    �                  b      b      �    B                 d      d      �    t d 4 2�              f      f      �    20    ^           h      h      �    20    `           j      j      �    B             R      �       "           k      k      �   h           �      �          �    2 B             R      �       "           l      l      �   h           �      �          �    2 B             R      �       "           m      m      �   h           �      �          �    2
 
4 
2p           R             �           n      n         h           
      
          �    H  B             R             "           o      o         h                           �    2 B             R      %       "           p      p         h           (      +          �    2 B             R      4       "           q      q      .   h           7      :          �    2 20               r      r      =   ! t               r      r      =      E           r      r      C   !                 r      r      =   E   K           r      r      I   - 20               s      s      R   ! t               s      s      R      E           s      s      X   !                 s      s      R   E   K           s      s      ^   -; *h�"�
��	��p`0P        ,     T       m       �          u      u      g   (           p      s   6    �<    �<    a<    A>    .    .    .    .    .    *    �2    a:    :    �	r    �	-    �    �F    n    .    .    .    .    .    .        �
F       '   	   &      -            E   #   K   (   M   -   N   2   O   7   P   <   /   B      I      P      W   +   _   �    e      l   �    q   F   v   G   {   H   �   I   �   J   �   L   �   �    �   !   �   �    F4 ���(��J 4"P$4"q$@"E$@"|0�244^6:446:4"6L426R46H4r6r46@$�� � � ��	BT* )
���p`P    	     S       `           v      v      y   !, ,x� !h� �(43    `          v      v      y   `   �          v      v         !       `          v      v      y   �  �          v      v      �    20    �           x      x      �    20               y      y      �   ! t               y      y      �      E           y      y      �   !                 y      y      �   E   K           y      y      �   - B      A           {      {      �   
 
4 
2`               }      }      �   ! t               }      }      �      P           }      }      �   !                 }      }      �   P   �           }      }      �    B                             �    4     p           �      �      �    4     �           �      �      �    t	 T 4 2�    U           �      �      �   ! d     U          �      �      �   U   �           �      �      �   !       U          �      �      �   �   �           �      �      �   !   d     U          �      �      �   �             �      �      �   !       U          �      �      �               �      �      �    4 2p    1           �      �      �    r����p`0           R      �       s          �      �      �   8               �          	                  �       9   � ��  BP0      C           9      9      	     B      :           �      �                                     _               Unknown exception                             k                                           w               bad array new length                                      *                                 0      6      <                   .?AVbad_array_new_length@std@@     =               ����                      -                         .?AVbad_alloc@std@@     =              ����                      3      	                   .?AVexception@std@@     =               ����                      9         string too long     ����    ����        ��������0 1 LATLONG_TEXTURE main donut/passes/environment_map_ps.hlsl SkyConstants Environment Map vector too long                                       9      b      _                         e                   h               ����    @                   9      b                                         3      n      k                         q                           t      h              ����    @                   3      n                                         -      z      w                         }                                   �      t      h              ����    @                   -      z     �?                              �   �   �   �   �   (   & 
7        std::exception::`vftable'            
    �   (   & 
7        std::bad_alloc::`vftable'            
    �   3   1 
7        std::bad_array_new_length::`vftable'     !      !  
 �+鷯8}`�鴡�<L�=*�(.瞗蔹�k俙:n頁鎴萩yC})S翌<h�K蜌�([柏q�廁滔偩�\ q啮f嘌g1h>箞x1j拞U茘0俶�i栕FX働聖u鳿�\�脴-'X娵[ljk�2骹Y4�(�廧�
蝿败]	嘷&獯屛�3>飖9屓yX儭揃髯P�<黄B�6!�5F肆峖=f瓵87<5'暝o砰2%剻j!X�5�Q/5A"�5哖灥<|"Q{螒抉誒瀵㊣�+�?|荱猢嫨奜咂灗嬍P'�3瑊�):�5�2牬昧謭?w∥�峷�崵aX@G�& o娺q覿jUt��V��54ZB檟飪�徧觍攧搑�54ZB檟�
hー烟7矞篹殐铥侤荝觛_1軱藲堄�<X厴憱�,/08郿~� T(倴
68u%俩身飮瘶V[篠缲丟9未s�(�-
<鰏mk'3>飖9屓D6b�搿E閈原曫旚Z�++嚤踖p禭诵鰐麌臂ep禭�6IB��6萪O�5榬z,�"芀霵婬(�-葥;垧\沌'項j8l�垐�'項j媔郬� 颛�'項jH噳凵-\=�'項j賨B�5Y鳿�'項j�慖羚'項jO槃N穈F�:b8�4n象C驂Sn�$愜w獛啯`鐞�>躇�$愜w獛啯沜W]颇�$愜w獛啯瞋滷h?杺4Y癏^塔埙w鑤| 筲╱p泸翬蟱_?+mZ繡)�M�1�4萉�5亐熫P"櫐O嶀預棊膬$R驯�-n嶀預棊膬毕~lP�&箩邆5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k�.�D�3\c�& 軁Dm攇浅�%I栶賑?TQ壤 墝;jf]{謑pd燍ニJ%f]{謑pv#�抵%�?卢鑂侽��0nN鵘J忄a瓃�虖F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�5�蘫� 菘U睃-雵J-WV8o��腫62V雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埻﨏帲晗Di＄燤�'鹴aR�,F_棢杻#Q噤釺�
鮱了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛�+辵倬毉觨婲 惨dd�a�:蘖縋卒塙2G�F饻櫵&幼
N;き8乿�"労<芭鄷{!闏库;Q椎[�"t���胸雵J-WV8o�.w⒇衞雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛-坓�(鬄鮳�>i,夿坁裗呔屸懚獲r貂筦绬靻3;�F瞁:2&櫇�"`Z_餔旉>R�-坓�(鬄�汬'这柆�5*窆H絁媳躻5*窆H繈饊�/^@�(潮螣P雟禑)媗ㄣ(PI^ub&鷜3!敲:G火s昖廘�汚$S匜*媵-/�謚帊敲:G火�Ｈ屌座_簤�p畚佗勫r|惀拟吤sM7髥榥夅y*�杜`颀l+�鞯.r擣�0G#盱谑f鵱�s觶籹;嗐8儧j� 頿噉4�硓�-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H漩
ψ笥鐬A嶩b5ie猭橂B$樐蜆{�u%9o溕`職)5珉≥頙X慠[�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 摊o5yI~�5]_и�sG﹋P越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       鴿              .debug$T       p                 .rdata         <       擜犞                         )   0       .text$mn       �       膒T     .debug$S       �             .text$mn       :      眡�     .debug$S                    .text$mn    	        �<N�     .debug$S    
   D  2       	    .text$mn       1      瑓w�     .debug$S       �             .text$mn    
   s  	   皳�     .debug$S       �  b       
    .text$x        C      -�
    .text$mn       �       ��     .debug$S       �             .text$mn       p       戢輊     .debug$S       ,             .text$mn       3       盇�     .debug$S       ,             .text$mn              �邆     .debug$S                    .text$mn            0润�     .debug$S       �  2           .text$mn              恶Lc     .debug$S       �              .text$mn       �  .   ^� N     .debug$S       i  >          .text$x              杘螩    .text$x              幫�    .text$x               Dm    .text$x     !         \
&�    .text$x     "         鎝    .text$x     #         饉G    .text$x     $         }r-�    .text$x     %         谨C    .text$x     &         衭W�    .text$x     '         Kw伂    .text$x     (         瀎s�    .text$x     )         鋌�    .text$x     *         !��    .text$x     +         XA�    .text$x     ,         糆b�    .text$x     -         B窽    .text$x     .         XA�    .text$x     /         嬵    .text$x     0         饦    .text$x     1         �]�    .text$x     2         ]焧    .text$x     3         0'�    .text$x     4         茲�    .text$mn    5   
       �9�     .debug$S    6   �          5    .text$mn    7   �       (回     .debug$S    8   �          7    .text$mn    9          袁z\     .debug$S    :   �          9    .text$mn    ;          �邆     .debug$S    <   �          ;    .text$mn    =          痖I     .debug$S    >   �          =    .text$mn    ?   <      .ズ     .debug$S    @   0  
       ?    .text$mn    A   <      .ズ     .debug$S    B   L  
       A    .text$mn    C   !      :著�     .debug$S    D   <         C    .text$mn    E   2      X于     .debug$S    F   <         E    .text$mn    G   "       坼	     .debug$S    H   �         G    .text$mn    I   "       坼	     .debug$S    J   �         I    .text$mn    K   "       坼	     .debug$S    L   �         K    .text$mn    M   "       坼	     .debug$S    N   �         M    .text$mn    O   "       坼	     .debug$S    P   �         O    .text$mn    Q   "       坼	     .debug$S    R   �         Q    .text$mn    S   ^      wP�     .debug$S    T   T         S    .text$mn    U   K       }'     .debug$S    V   �         U    .text$mn    W   K       }'     .debug$S    X   �         W    .text$mn    Y   K       }'     .debug$S    Z   �         Y    .text$mn    [   �      8耾^     .debug$S    \   �         [    .text$mn    ]   `      板@�     .debug$S    ^   �         ]    .text$mn    _   �      4;�     .debug$S    `   �  *       _    .text$mn    a   �      f綛a     .debug$S    b   �  $       a    .text$mn    c         ��#     .debug$S    d   �          c    .text$mn    e         ��#     .debug$S    f   �          e    .text$mn    g   B      贘S     .debug$S    h             g    .text$mn    i   B      贘S     .debug$S    j            i    .text$mn    k   B      贘S     .debug$S    l   �          k    .text$mn    m   H       襶.      .debug$S    n   �         m    .text$mn    o   �  	   mYh     .debug$S    p   4  .       o    .text$mn    q          aJ鄔     .debug$S    r   �          q    .text$mn    s         �ッ     .debug$S    t   �          s    .text$mn    u         �ッ     .debug$S    v             u    .text$mn    w   A      �园     .debug$S    x   �         w    .text$mn    y         崪覩     .debug$S    z   �          y        \       m        x                �                �                �                �                �                �                               1               I      E        j      e        �      y        �      k        �          i                   �      ?              g        "          i
                   A      C        f      c        �      A        �      i        �          i                         q        3      s        L              �      S        �      =        �      5              ]        2      9        \              }      7        �      O        �      Q                      :      G        r      _        �      ;        �      K        �      I        %      M        `      U        �      W        �              �      o        F               �      a        �               �	      Y        �	      w        E
      [        �
      u                      ^              �              �      	        Y              �      
        �
                            �              �              �      (        �      )        �      *        �      +              ,              -        #              3      .        D               T      !        d      "        t      /        �      0        �      1        �      2        �       3        �!      #        �"      4        �#      $        �$      %        
&      &        '      '        *(               =(               P(               a(           __chkstk             v(           memcpy           memmove          memset           $LN13       m    $LN5        E    $LN10       k    $LN7        ?    $LN13       g    $LN10       A    $LN16       i    $LN3        q    $LN4        q    $LN3       s    $LN4        s    $LN72         $LN77           $LN33   ^   S    $LN36       S    $LN37   `   ]    $LN40       ]    $LN10       O    $LN10       Q    $LN10       G    $LN48       _    $LN10       K    $LN10       I    $LN10       M    $LN18       U    $LN18       W    $LN864  �      $LN869          $LN207      o    $LN70   �   a    $LN73       a    $LN18       Y    $LN18   A   w    $LN21       w    $LN43   �   [    $LN46       [    $LN3       u    $LN4        u    $LN52           $LN46           $LN56     	    $LN61       	    $LN19           $LN140  s  
        �(         $LN144      
    $LN14   :       $LN17           .xdata      {          F┑@m        Z)      {    .pdata      |         X賦鷐        ~)      |    .xdata      }          （亵E        �)      }    .pdata      ~          T枨E        �)      ~    .xdata                %蚘%k        �)          .pdata      �         惻竗k        *      �    .xdata      �          （亵?        ?*      �    .pdata      �         2Fb�?        h*      �    .xdata      �          %蚘%g        �*      �    .pdata      �         惻竗g        �*      �    .xdata      �          （亵A        �*      �    .pdata      �         2Fb褹        +      �    .xdata      �          %蚘%i        D+      �    .pdata      �         惻竗i        v+      �    .xdata      �          懐j瀜        �+      �    .pdata      �         Vbv鵴        �+      �    .xdata      �          �9�s        ,      �    .pdata      �         �1皊        ',      �    .xdata      �          �F�        G,      �    .pdata      �         *!)	        �,      �    .xdata      �          （亵S        �,      �    .pdata      �         翎珸S        D-      �    .xdata      �          （亵]        �-      �    .pdata      �         粻胄]        �-      �    .xdata      �         /
        �-      �    .pdata      �         +eS籓        .      �    .xdata      �   	      �#荤O        W.      �    .xdata      �         jO        �.      �    .xdata      �          3狷 O        �.      �    .xdata      �         /
        /      �    .pdata      �         +eS籕        V/      �    .xdata      �   	      �#荤Q        �/      �    .xdata      �         jQ        �/      �    .xdata      �          3狷 Q        
0      �    .xdata      �         /
        E0      �    .pdata      �         +eS籊        �0      �    .xdata      �   	      �#荤G        �0      �    .xdata      �         jG        1      �    .xdata      �          3狷 G        N1      �    .xdata      �         �酑_        �1      �    .pdata      �          鮩s_        �1      �    .xdata      �   	      �#荤_        �1      �    .xdata      �         j_        2      �    .xdata      �          爲飆_        U2      �    .xdata      �         /
        �2      �    .pdata      �         +eS籏        �2      �    .xdata      �   	      �#荤K        �2      �    .xdata      �         jK        23      �    .xdata      �          3狷 K        s3      �    .xdata      �         /
        �3      �    .pdata      �         +eS籌        �3      �    .xdata      �   	      �#荤I        '4      �    .xdata      �         jI        f4      �    .xdata      �          3狷 I        �4      �    .xdata      �         /
        �4      �    .pdata      �         +eS籑        -5      �    .xdata      �   	      �#荤M        o5      �    .xdata      �         jM        �5      �    .xdata      �          3狷 M        �5      �    .xdata      �          （亵U        D6      �    .pdata      �         � 賃        �6      �    .xdata      �         范^揢        �6      �    .pdata      �         鳶�U        7      �    .xdata      �         @鴚`U        e7      �    .pdata      �         [7躑        �7      �    .voltbl     �          飾殪U    _volmd      �    .xdata      �          （亵W        �7      �    .pdata      �         � 賅        ?8      �    .xdata      �         范^揥        �8      �    .pdata      �         鳶�W        �8      �    .xdata      �         @鴚`W        9      �    .pdata      �         [7躓        a9      �    .voltbl     �          飾殪W    _volmd      �    .xdata      �   (      嶪E        �9      �    .pdata      �         訓        �:      �    .xdata      �   	      � )9        �;      �    .xdata      �   �      s鲁        �<      �    .xdata      �   O       Y�6�        �=      �    .voltbl     �          戆�8    _volmd      �    .xdata      �         簅        �>      �    .pdata      �         粻胄o        O?      �    .xdata      �          鰋歰        �?      �    .pdata      �         _檇刼        )@      �    .xdata      �         WJ糹o        桜      �    .pdata      �         5港榦        A      �    .xdata      �          （亵a        sA      �    .pdata      �         礱        燗      �    .xdata      �          （亵Y        藺      �    .pdata      �         � 資        B      �    .xdata      �         范^揧        QB      �    .pdata      �         鳶�Y        旴      �    .xdata      �         @鴚`Y        貰      �    .pdata      �         [7躖        C      �    .voltbl     �          飾殪Y    _volmd      �    .xdata      �          �9�w        aC      �    .pdata      �         s�7鍂        蔆      �    .xdata      �          �搀[        2D      �    .pdata      �         O?[4[        滵      �    .xdata      �         T�%~[        E      �    .pdata      �         *i澚[        sE      �    .xdata      �         Ｕ峓        逧      �    .pdata      �         ��*2[        KF      �    .xdata      �          �9�u        稦      �    .pdata      �         �1皍        'G      �    .xdata      �          
        朑      �    .pdata      �         悜P�        鱃      �    .xdata      �          
        WH      �    .pdata      �         7G        燞      �    .xdata      �          �-th	        鐷      �    .pdata      �         �	        QI      �    .xdata      �         銎�	        笽      �    .pdata      �         �g�	        #J      �    .xdata      �         N懁	        岼      �    .pdata      �         
	        鱆      �    .xdata      �         Z�	W	        aK      �    .pdata      �         敵4	        薑      �    .xdata      �         N懁	        5L      �    .pdata      �         赴t	        烲      �    .xdata      �          |釣�        	M      �    .pdata      �         鉙gI        盡      �    .xdata      �         �9供
        XN      �    .pdata      �         Z嘆�
        "O      �    .xdata      �   
      B>z]
        隣      �    .xdata      �          �2g�
        稰      �    .xdata      �         T�8
        塓      �    .xdata      �         r%�
        SR      �    .xdata      �   	       椷Kg
        !S      �    .xdata      �          M[�
        鞸      �    .pdata      �         ��
        荰      �    .voltbl     �                  _volmd      �    .xdata      �          �9�        燯      �    .pdata      �         礝
        齍      �    .rdata      �                      YV     �    .rdata                �;�         pV          .rdata                           梀        .rdata                           甐        .rdata               �)         蠽         .xdata$x                         黇         .xdata$x            虼�)         W         .data$r       /      嶼�         AW         .xdata$x      $      4��         fW         .data$r       $      鎊=         籛         .xdata$x    	  $      銸E�         誛      	   .data$r     
  $      騏糡         X      
   .xdata$x      $      4��         .X             mX           .rdata               燺渾         �X         .data       
          烀�                
       赬     
   .rdata               �6F�         Y         .rdata               �]�         Y         .rdata               泑          )Y         .rdata               旲^         MY         .rdata        %       G�+�         dY         .rdata        
       ��>         歒         .rdata               /衂         篩         .rdata               IM         轞         .rdata$r      $      'e%�         Z         .rdata$r            �          Z         .rdata$r                         2Z         .rdata$r      $      Gv�:         HZ         .rdata$r      $      'e%�         gZ         .rdata$r            }%B         Z         .rdata$r                         昛         .rdata$r      $      `         玓         .rdata$r      $      'e%�         蔤         .rdata$r            �弾         鞿         .rdata$r                          [          .rdata$r    !  $      H衡�         /[      !       Y[           .rdata      "         z�         k[      "   .rdata      #         � �         抂      #   .rdata      $         �a�         筟      $   _fltused         .debug$S    %  4          �    .debug$S    &  4             .debug$S    '  @             .chks64     (  @	                郲  ?c_IdentityTransform@rt@nvrhi@@3QBMB ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ??0Viewport@nvrhi@@QEAA@XZ ??0Rect@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??0RenderState@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ ??0VertexBufferBinding@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ ??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ ??0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z ?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z ?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z ??1ShaderMacro@engine@donut@@QEAA@XZ ?CreateAutoShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0UStaticShader@23@11PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z ??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ ??$translation@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z ??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z ??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Uninitialized_move@PEAUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@QEAU123@0PEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ?catch$7@?0???$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z@4HA ?dtor$0@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$10@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$11@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$12@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$13@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$15@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$19@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$1@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$28@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$2@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$3@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$4@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$55@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$56@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$57@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$58@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$59@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$5@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$60@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$6@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$7@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$8@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA ?dtor$9@?0???0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $pdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $unwind$??0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z $pdata$??0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z $cppxdata$??0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z $stateUnwindMap$??0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z $ip2state$??0EnvironmentMapPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@V?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@PEAVITexture@4@@Z $unwind$?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z $pdata$?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z $chain$3$?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z $pdata$3$?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z $chain$4$?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z $pdata$4$?Render@EnvironmentMapPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@@Z $unwind$??1ShaderMacro@engine@donut@@QEAA@XZ $pdata$??1ShaderMacro@engine@donut@@QEAA@XZ $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $unwind$?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z $pdata$?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z $unwind$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $chain$0$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$0$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $chain$1$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$1$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ $unwind$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $pdata$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $unwind$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $pdata$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $unwind$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $unwind$??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $unwind$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $pdata$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $cppxdata$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $stateUnwindMap$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $tryMap$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $handlerMap$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $ip2state$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $unwind$?catch$7@?0???$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z@4HA $pdata$?catch$7@?0???$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z@4HA $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_01GBGANLPD@0@ ??_C@_01HIHLOKLC@1@ ??_C@_0BA@BDDICPLA@LATLONG_TEXTURE@ ??_C@_04GHJNJNPO@main@ ??_C@_0CF@BBAFEGNM@donut?1passes?1environment_map_ps@ ??_C@_0N@MOCALHPI@SkyConstants@ ??_C@_0BA@HBOGEIF@Environment?5Map@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __security_cookie __xmm@0000000000000000000000003f800000 __xmm@000000000000000f0000000000000000 __xmm@80000000800000008000000080000000 