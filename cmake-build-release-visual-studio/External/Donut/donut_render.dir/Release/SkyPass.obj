d嗏 眈Ghm �      .drectve        <  d#               
 .debug$S        葂 �$  h�        @ B.debug$T        p   笣             @ B.rdata          @   (�             @ @@.text$mn        �   h�              P`.debug$S        �  (� 貭        @B.text$mn        :   (� b�         P`.debug$S          �� 專        @B.text$mn        p   �              P`.debug$S        ,  垽 触        @B.text$mn        :   馥 *�         P`.debug$S        L  4� ��        @B.text$mn        8   效 �         P`.debug$S          � "�        @B.text$mn        #   ^�              P`.debug$S        �   仼 u�        @B.text$mn        #   豹              P`.debug$S        �   元 全        @B.text$mn        �   � 櫖         P`.debug$S        8  ， 郛        @B.text$mn        �   彲 "�         P`.debug$S        �  ,� �        @B.text$mn           げ              P`.debug$S           砍        @B.text$mn                         P`.debug$S        �   �� 状        @B.text$mn        
   �              P`.debug$S        �    � 涞        @B.text$mn        �    �              P`.debug$S        �   舛         @B.text$mn           址              P`.debug$S        �   绶 煾        @B.text$mn          歉 衫         P`.debug$S        �2  昧 k�     �  @B.text$x            � �         P`.text$x                      P`.text$x             +         P`.text$x            5 E         P`.text$x            O _         P`.text$x            i y         P`.text$x            � �         P`.text$x            � �         P`.text$x            � �         P`.text$x            � �         P`.text$x            � �         P`.text$x            � 
         P`.text$x             '         P`.text$x            1 A         P`.text$mn           K              P`.debug$S        �   V         @B.text$mn           6              P`.debug$S        �   M 	        @B.text$mn        <   U	 �	         P`.debug$S        0  �	 �
     
   @B.text$mn        <   C          P`.debug$S        L  � �     
   @B.text$mn        !   M
 n
         P`.debug$S        <  �
 �        @B.text$mn        2   � ,         P`.debug$S        <  @ |        @B.text$mn        "   �              P`.debug$S        �   �        @B.text$mn        "   N              P`.debug$S        �  p         @B.text$mn        "   �              P`.debug$S        �  � R        @B.text$mn        "   �              P`.debug$S        �   �        @B.text$mn        "   T              P`.debug$S        �  v 
        @B.text$mn        "   �              P`.debug$S        �  � X        @B.text$mn        K   �              P`.debug$S        �  C #!        @B.text$mn        `   �! "         P`.debug$S        �  #" �$        @B.text$mn        �   �% F&         P`.debug$S        �  Z& *     *   @B.text$mn           �+ �+         P`.debug$S        �   �+ �,        @B.text$mn           �, -         P`.debug$S        �   - �-        @B.text$mn        B   8. z.         P`.debug$S           �. �/        @B.text$mn        B   �/ 0         P`.debug$S          40 D1        @B.text$mn        B   �1 �1         P`.debug$S        �   �1 �2        @B.text$mn        H   3              P`.debug$S        �  `3 $5        @B.text$mn        �  <6 89         P`.debug$S        �  �9 XB     2   @B.text$mn        �  LD 軭     	    P`.debug$S        �  7I 肞     4   @B.text$mn            薘 隦         P`.debug$S        �   	S 蚐        @B.text$mn           	T T         P`.debug$S        �   &T 鶷        @B.xdata             6U             @0@.pdata             JU VU        @0@.xdata             tU             @0@.pdata             |U 圲        @0@.xdata                          @0@.pdata             睻 綰        @0@.xdata             躑             @0@.pdata             銾 餟        @0@.xdata             V             @0@.pdata             V &V        @0@.xdata             DV             @0@.pdata             LV XV        @0@.xdata             vV             @0@.pdata             俈 嶸        @0@.xdata             琕             @0@.pdata             碫 繴        @0@.xdata             轛             @0@.pdata             鎂 騐        @0@.xdata             W  W        @0@.pdata             4W @W        @0@.xdata          	   ^W gW        @@.xdata             {W 乄        @@.xdata             媁             @@.xdata             嶹 濿        @0@.pdata             瞁 網        @0@.xdata          	   躓 錡        @@.xdata             鵚 �W        @@.xdata             	X             @@.xdata             X X        @0@.pdata             0X <X        @0@.xdata          	   ZX cX        @@.xdata             wX }X        @@.xdata             嘪             @@.xdata             奨 瀀        @0@.pdata             瞂 綳        @0@.xdata          	   躕 錢        @@.xdata             鵛 �X        @@.xdata             	Y             @@.xdata             Y Y        @0@.pdata             3Y ?Y        @0@.xdata          	   ]Y fY        @@.xdata             zY �Y        @@.xdata             奩             @@.xdata             峐 漎        @0@.pdata             盰 結        @0@.xdata          	   踄 鋂        @@.xdata             鳼         @@.xdata             Z             @@.xdata             Z Z        @0@.pdata             /Z ;Z        @0@.xdata          	   YZ bZ        @@.xdata             vZ |Z        @@.xdata             哯             @@.xdata             塟             @0@.pdata             慫 漐        @0@.xdata             籞 蟌        @0@.pdata             鞿 鵝        @0@.xdata             [ '[        @0@.pdata             E[ Q[        @0@.voltbl            o[               .xdata          (   q[ 橻        @0@.pdata             璠 筟        @0@.xdata          	   譡 郲        @@.xdata          b   鬧 V\        @@.xdata          B   
]             @@.voltbl            L]               .xdata             M] i]        @0@.pdata             s] ]        @0@.xdata             漖 筣        @0@.pdata             譣 鉣        @0@.xdata             ^ ^        @0@.pdata             /^ ;^        @0@.xdata          4   Y^             @0@.pdata             峖 橿        @0@.xdata             穅             @0@.pdata             縙 薧        @0@.xdata             閊             @0@.pdata             馸 齘        @0@.xdata             _             @0@.pdata             /_ ;_        @0@.xdata             Y_             @0@.pdata             m_ y_        @0@.xdata             梍             @0@.pdata             焈 玙        @0@.rdata             蒧 醎        @@@.rdata             �_             @@@.rdata             ` )`        @@@.rdata             G` _`        @@@.rdata             }`             @@@.xdata$x           抈 甡        @@@.xdata$x           耟 轥        @@@.data$r         /   黗 +a        @@�.xdata$x        $   5a Ya        @@@.data$r         $   ma 慳        @@�.xdata$x        $   沘 縜        @@@.data$r         $   觓 鱝        @@�.xdata$x        $   b %b        @@@.rdata             9b             @@@.data               Ib             @ @�.rdata             ib             @0@.rdata             nb             @@@.rdata          
   嘼             @@@.rdata             攂             @0@.rdata$r        $   榖 糱        @@@.rdata$r           赽 頱        @@@.rdata$r           鴅 c        @@@.rdata$r        $   c 2c        @@@.rdata$r        $   Fc jc        @@@.rdata$r           坈 渃        @@@.rdata$r            篶        @@@.rdata$r        $   蝐 騝        @@@.rdata$r        $   d *d        @@@.rdata$r           Hd \d        @@@.rdata$r           fd 俤        @@@.rdata$r        $   燿 膁        @@@.rdata             豥             @0@.rdata             躣             @0@.rdata             郿             @0@.rdata             鋎             @0@.rdata             鑔             @0@.rdata             靌             @0@.rdata             餯             @0@.rdata             鬱             @P@.rdata             e             @P@.debug$S        4   e He        @B.debug$S        4   \e 恊        @B.debug$S        @    鋏        @B.chks64           鴈              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   0  e     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\SkyPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $vfs  $math 	 $colors 	 $render  $Json 	 $stdext �   洗  a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 4 #  @ _Mtx_internal_imp_t::_Critical_section_size � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment 5 #   _Mtx_internal_imp_t::_Critical_section_align j #   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment " ;    std::memory_order_relaxed + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_consume " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment 3   \ std::filesystem::path::preferred_separator % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets '�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size R #   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment : #   std::integral_constant<unsigned __int64,2>::value t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified : #   std::integral_constant<unsigned __int64,1>::value i #   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment \ #   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity = �   donut::engine::c_MaxRenderPassConstantBufferVersions . %   donut::math::box<float,2>::numCorners j #   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free _ #   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi ��    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi ��   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard q #   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free � #   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment 5 �    std::filesystem::_File_time_clock::is_steady / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy q #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment h #   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment ��    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi A #   std::allocator<bool>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment i #   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment $ %   std::_Locbase<int>::collate M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets " %   std::_Locbase<int>::ctype G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable l #   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable c #   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment 6 �   std::_Iterator_base0::_Unwrap_when_unverified  %    LightType_None  %   LightType_Directional  %   LightType_Spot  %   LightType_Point 7 �   std::_Iterator_base12::_Unwrap_when_unverified  �?   std::_Consume_header  �?   std::_Generate_header � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment 7 �   std::atomic<unsigned int>::is_always_lock_free + �   donut::math::vector<double,3>::DIM R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified + �   donut::math::vector<double,4>::DIM W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices T #   std::allocator<donut::render::DrawItem>::_Minimum_asan_allocation_alignment D #   ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources ) �   donut::math::vector<bool,4>::DIM M #   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment O #   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment - %    std::integral_constant<int,0>::value J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos % #   std::ctype<char>::table_size O #   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value Z %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos M #   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R %   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable \ #   std::allocator<donut::render::DrawItem const *>::_Minimum_asan_allocation_alignment   3        nvrhi::EntireBuffer L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos Z #   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment R #   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot x #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment Z #   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment Z�    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]�   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable � #   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment - �    std::chrono::system_clock::is_steady $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den c #   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard A #   std::allocator<char>::_Minimum_asan_allocation_alignment m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g�    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num : #    std::integral_constant<unsigned __int64,0>::value $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment < r  ��枠 std::integral_constant<__int64,10000000>::value 1 r   std::integral_constant<__int64,1>::value ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM Z #   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment - �   std::chrono::steady_clock::is_steady X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val & r   std::ratio<1,1000000000>::num * r  � 蕷;std::ratio<1,1000000000>::den a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy - 9   std::_Invoker_pmf_refwrap::_Strategy - 9   std::_Invoker_pmf_pointer::_Strategy � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi * �   donut::math::vector<float,2>::DIM B #   std::allocator<float>::_Minimum_asan_allocation_alignment + �        nvrhi::rt::c_IdentityTransform  �5    std::denorm_absent  �5   std::denorm_present a #   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized  r  < std::ratio<60,1>::num ( �    std::_Num_base::tinyness_before  r   std::ratio<60,1>::den  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity 0 �   std::numeric_limits<wchar_t>::is_modulo - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 . %   donut::math::box<float,3>::numCorners : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 �    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi + %   std::numeric_limits<float>::digits �   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10  �   �  , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent   �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �   -v � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34 	 !  tm %  7  _s__RTTICompleteObjectLocator2 & 鱥  $_TypeDescriptor$_extraBytes_30 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � +~  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w -~  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 觹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 2<  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > d 鈣  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c ~  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h ~  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 瞸  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y ~  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � #<  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � <  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鸖  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > c 寋  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 
~  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 諀  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > � 
T  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > ] ~  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ [|  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 鴠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 題  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 鋧  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 諁  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 4<  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > 苶  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 緘  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � ?|  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W祡  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 畗  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 憐  std::_Default_allocator_traits<std::allocator<float> > ; 靭  std::hash<std::shared_ptr<donut::engine::Material> > � l|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > � %<  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ 殅  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � 憓  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ %}  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C }  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > iT  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � }  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 鱸  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 鰔  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � 珄  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 飢  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 遼  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 瓅  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 讄  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ? 箌  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 z  std::allocator<donut::engine::SkinnedMeshJoint> M M|  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > � T  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 腟  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > L 磡  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 瘄  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � |  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 爘  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T 巪  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > W 儂  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem const *> > � x|  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � n|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U ]|  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 鐆  std::_Ptr_base<donut::engine::LoadedTexture> :  <  std::_Vector_val<std::_Simple_types<unsigned int> > D O|  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 踫  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � A|  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 =u  std::_Ptr_base<donut::engine::DescriptorHandle> � 2|  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~(|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e bt  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 蕑  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > � <  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �:  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > "坸  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 噞  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > � 齋  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > d祔  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > �<  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> c �;  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> U .y  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w |  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � |  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > y 鋥  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 貃  std::allocator<donut::math::vector<float,2> > M 蓒  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = 秡  std::allocator<donut::math::vector<unsigned short,4> > K 縶  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U  u  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 趚  std::_Ptr_base<donut::engine::BufferGroup> � �;  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > F祘  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ >s  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 杮  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 巤  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e 蘳  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N 墈  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > s k:  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � l9  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > { {{  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l   std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � _:  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > , {  std::allocator<nvrhi::BindingSetItem> K  {  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 鳶  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > � 鰖  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # �8  std::allocator<unsigned int> . 籕  std::_Ptr_base<donut::vfs::IFileSystem> �霺  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � �;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鑪  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 閟  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 魕  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � w  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � ╬  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 釹  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > ��;  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> g 搑  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 蘻  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  u  std::allocator<float> � 緕  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1>   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 爖  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t 4 搝  std::allocator_traits<std::allocator<float> > N 厇  std::allocator_traits<std::allocator<donut::render::DrawItem const *> > [ wz  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > Q�;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � 芐  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > l 7k  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > 稴  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> �;  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > w vq  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > \R  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > � �:  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > ; �8  std::allocator_traits<std::allocator<unsigned int> > [ cz  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 {u  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Yz  std::hash<std::shared_ptr<donut::engine::MeshInfo> > O 0s  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem> > WUz  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> E Nz  std::_Vector_val<std::_Simple_types<donut::render::DrawItem> > � Dz  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H 蕆  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ mh  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> �  z  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 鎟  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X 	z  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> > . Ni  std::integer_sequence<unsigned __int64>  �  std::_Lockit � 訽  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > �   std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy � [;  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * C2  std::hash<enum nvrhi::ResourceType> / 豜  std::shared_ptr<donut::engine::Material> - 払  std::reverse_iterator<wchar_t const *> 5 鋂  std::shared_ptr<donut::engine::SceneGraphNode> 9 肵  std::shared_ptr<donut::engine::animation::Sampler> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file � 琒  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � z  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 聎  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > � 淪  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >  �(  std::_Big_uint128 � I;  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > / 沑  std::weak_ptr<donut::engine::SceneGraph> 騳  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) U6  std::_Narrow_char_traits<char,int> L 0y  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 觟  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 芢  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > > R �.  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > 嘢  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >  f+  std::hash<int>  �5  std::_Num_int_base  錊  std::ctype<wchar_t> " �+  std::_System_error_category � y  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / a2  std::_Conditionally_enabled_hash<bool,1> 2 髕  std::shared_ptr<donut::engine::BufferGroup> � 莤  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::float_denorm_style 4  x  std::shared_ptr<donut::engine::LoadedTexture>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 8g  std::piecewise_construct_t u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 襴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . 'Z  std::_Ptr_base<donut::engine::MeshInfo> 6 2;  std::allocator_traits<std::allocator<wchar_t> > � 蕎  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  &  std::bad_cast B [  std::enable_shared_from_this<donut::engine::SceneGraphNode>  玁  std::equal_to<void> 4 s  std::allocator<donut::math::vector<float,4> > 3 yI  std::_Ptr_base<donut::engine::ShaderFactory> � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } 耟  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 恅  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 硍  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 亀  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o hh  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � je  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > 6 諭  std::initializer_list<nvrhi::BindingLayoutItem> " �5  std::numeric_limits<double>  C&  std::__non_rtti_object < 裋  std::_Ptr_base<donut::engine::DescriptorTableManager> ( 0  std::_Basic_container_proxy_ptr12 � �.  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> 4 <w  std::allocator<donut::math::vector<float,3> > � g  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � *\  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � 鵞  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > 0;  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8> T -w  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �5  std::_Num_float_base � #w  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  *  std::logic_error 3   std::weak_ptr<donut::engine::SceneGraphNode> � Lg  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � �:  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > � 飃  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety P w  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 辷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id ?   std::allocator_traits<std::allocator<unsigned __int64> > : 揨  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 1e  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z   std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> _ 檝  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u hv  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P�0  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl .   std::_Ptr_base<donut::engine::Material> * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> � �.  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � �:  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error d *v  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 鴘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy . 硊  std::initializer_list<unsigned __int64> % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t>   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> � 
S  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � �:  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � �:  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> 7 攗  std::shared_ptr<donut::engine::SceneTypeFactory> � fu  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � ^u  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 纃  std::allocator<unsigned __int64>  h:  std::false_type S �:  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #�0  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ai  std::_Default_allocator_traits<std::allocator<unsigned __int64> >  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � bk  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 Vu  std::shared_ptr<donut::engine::DescriptorHandle> , �5  std::numeric_limits<unsigned __int64> � *u  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L "u  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > 9 3.  std::shared_ptr<donut::engine::FramebufferFactory> \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 8 .  std::_Ptr_base<donut::engine::FramebufferFactory> s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H 4m  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> � 鯮  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16> � �.  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void>  ;  std::string_view  w  std::wstring_view � �0  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound � 奼  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > b u  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec �   std::_Compressed_pair<std::allocator<donut::render::DrawItem>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem> >,1> D 鰐  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >   �*  std::_Init_once_completer �'  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � �(  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 � 蜶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > j wX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � EX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > � 癵  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ m  std::_Atomic_integral<long,4> � lt  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ dt  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > 2 iJ  std::initializer_list<nvrhi::IBindingSet *> > 俕  std::enable_shared_from_this<donut::engine::SceneGraph> K Vt  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > dn  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> > � u:  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >  �  std::hash<long double> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy 2 �1  std::equal_to<nvrhi::TextureSubresourceSet> W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � �3  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o �.  std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / =Z  std::shared_ptr<donut::engine::MeshInfo> � 1  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> H 鬒  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> 5 S\  std::shared_ptr<donut::engine::SceneGraphLeaf> O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> , 甦  std::allocator<std::_Container_proxy> D r:  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> x m:  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception & �.  std::_Zero_then_variadic_args_t � �0  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > d ps  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ t  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � t  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >  �  std::u32string 鱯  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � a:  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 鮯  std::_Vector_val<std::_Simple_types<float> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A 雜  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + G  std::pair<enum __std_win_error,bool> � 9  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void> � 輘  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error \ 蝧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder ) ~  std::_Atomic_integral_facade<long> 8 琗  std::_Ptr_base<donut::engine::animation::Sampler> % }2  std::hash<enum nvrhi::BlendOp> c   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B 梥  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> [ rs  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > M ds  std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> > ) q2  std::hash<enum nvrhi::BlendFactor> 輌  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 骾  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code J W  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano  �  std::_Iterator_base0 � *6  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > | Hs  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � g  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U @s  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0錰  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > jH:  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � 蠷  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 �6  std::_Char_traits<char16_t,unsigned short> 6 3q  std::allocator<donut::render::DrawItem const *> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> F 2s  std::allocator_traits<std::allocator<donut::render::DrawItem> > 9 紿  std::shared_ptr<donut::engine::CommonRenderPasses> ! �9  std::char_traits<char16_t> 裧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ $s  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > > � �0  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - 糫  std::weak_ptr<donut::engine::Material>  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 Ri  std::integer_sequence<unsigned __int64,0> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char> N Qm  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X s  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  9  std::_Invoker_strategy  	G  std::nothrow_t � n9  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � s  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T �r  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �5  std::_Default_allocate_traits � $e  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � 苧  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > 0 uo  std::_Ptr_base<donut::engine::IShadowMap> . 膔  std::allocator<donut::render::DrawItem> ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> ( 淲  std::array<nvrhi::BufferRange,11> ; ;  std::basic_string_view<char,std::char_traits<char> > c Gq  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > �  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � 祌  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 琙  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9羒  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 漴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 時  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock Y 噐  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  }r  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � Kr  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 5g  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D r  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base  r*  std::out_of_range # �5  std::numeric_limits<__int64> _ 鱭  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 苢  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b 坬  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > ~q  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  珸  std::ctype<char> @ bq  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 噈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P Xq  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? Nq  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  ;  std::memory_order Z Iq  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � ;q  std::_Compressed_pair<std::allocator<donut::render::DrawItem const *>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> >,1> � $q  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > q 9a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  縒  std::nullopt_t  罻  std::nullopt_t::_Tag  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState> / 訯  std::shared_ptr<donut::vfs::IFileSystem>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K 竝  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 莋  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  [9  std::ratio<1,1> 3 ,J  std::initializer_list<nvrhi::BindingSetItem>   �8  std::forward_iterator_tag  �*  std::runtime_error � 猵  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ��0  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   	  std::bad_array_new_length ; �2  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> T 沺  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> > j ip  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Reallocation_policy E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 'p  std::allocator<donut::engine::animation::Keyframe> K p  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > v W9  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool> � [e  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  5  std::u16string _ p  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 謔  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 韋  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *> ]�'  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >  \  std::nested_exception  �  std::_Distance_unknown ) 榦  std::allocator<nvrhi::BufferRange> ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , %@  std::codecvt<char32_t,char,_Mbstatet> 1 噊  std::shared_ptr<donut::engine::IShadowMap> C 蝑  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F e  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 bo  std::vector<float,std::allocator<float> > F 0o  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 j\  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> � �0  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc } 賒  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J ym  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  f_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 4_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy ; {.  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > � 鬾  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , EO  std::default_delete<std::_Facet_base>  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> C 踤  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . �-  std::vector<bool,std::allocator<bool> > J 蘮  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 沶  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> i ]n  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 9  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  �8  std::ratio<1,10000000> Sn  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d i  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag j 塵  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A {m  std::allocator_traits<std::allocator<unsigned __int64 *> >  �/  std::allocator<char> T jm  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> d 0m  std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> > z   std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Reallocation_policy G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> ) f  std::allocator<unsigned __int64 *>    std::nullptr_t =MY  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > Lh  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K)h  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & �8  std::random_access_iterator_tag 4 怚  std::shared_ptr<donut::engine::ShaderFactory> ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  x  std::wstring ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � 鷊  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  -*  std::domain_error  �  std::u32string_view � Me  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 俓  std::shared_ptr<donut::engine::SceneGraph>  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 閗  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 穔  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z ]k  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; 鏥  std::weak_ptr<donut::engine::DescriptorTableManager> $ 72  std::hash<nvrhi::IResource *> 4 =\  std::_Ptr_base<donut::engine::SceneGraphLeaf> " 轜  std::_Nontrivial_dummy_type � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet>  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage � K`  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � `  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! �+  std::_System_error_message  �  std::_Unused_parameter " �2  std::hash<nvrhi::IShader *> = sk  std::allocator<std::shared_ptr<donut::engine::Light> > h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  諻  std::bad_optional_access A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> � �0  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> 8   std::_Ptr_base<donut::engine::CommonRenderPasses> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q �1  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > � dk  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  鴋  std::_Exact_args_t � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > Q _k  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � �0  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > � Qk  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base b Ck  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> c 9k  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> � f^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 4^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> �圷  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ^ �4  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 |Z  std::_Ptr_base<donut::engine::SkinnedMeshInstance> v 1  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> ' 訥  std::hash<std::filesystem::path>   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 蘗  std::_Ptr_base<donut::engine::SceneGraphNode> � �6  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m m(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � ;(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy � 蒳  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � 襤  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> �|g  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E @]  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O #]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U !]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t & �i  $_TypeDescriptor$_extraBytes_40 # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling $ J  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �  nvrhi::ResourceStates . �5  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  �!  nvrhi::GraphicsState * �8  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16> ! �  nvrhi::SharedResourceFlags  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice> !    nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  �8  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # RW  nvrhi::DescriptorTableHandle  :  nvrhi::ShaderType  �$  nvrhi::TimerQueryHandle 2 RW  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # �$  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice !    nvrhi::VariableShadingRate  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �5  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  �  nvrhi::StencilOp  ]!  nvrhi::IBindingLayout  �  nvrhi::ColorMask  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  .   nvrhi::PrimitiveType  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 J  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # +k  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline    nvrhi::CpuAccessMode  �$  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �  nvrhi::BlendOp  �"  nvrhi::ComputeState 2 +k  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �!  nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats * fZ  donut::engine::SkinnedMeshReference ! 籞  donut::engine::SceneCamera $ H  donut::engine::ICompositeView  ?H  donut::engine::IView ( �7  donut::engine::CommonRenderPasses 5 �&  donut::engine::CommonRenderPasses::PsoCacheKey ; �&  donut::engine::CommonRenderPasses::PsoCacheKey::Hash $ �7  donut::engine::BlitParameters $ X[  donut::engine::SceneGraphNode 0 ![  donut::engine::SceneGraphNode::DirtyFlags " Z  donut::engine::MeshInstance   fH  donut::engine::PlanarView ) ZZ  donut::engine::SkinnedMeshInstance ( �&  donut::engine::FramebufferFactory   籢  donut::engine::SceneGraph > 颺  donut::engine::ResourceTracker<donut::engine::MeshInfo>  哘  donut::engine::ViewType $ 
H  donut::engine::ViewType::Enum ( h]  donut::engine::AnimationAttribute $ 筜  donut::engine::SceneGraphLeaf ! uW  donut::engine::BufferGroup  榙  donut::engine::Material *  k  donut::engine::Material::HairParams 0 黬  donut::engine::Material::SubsurfaceParams ! H  donut::engine::ShaderMacro # 荌  donut::engine::ShaderFactory  語  donut::engine::Light ' ℡  donut::engine::SceneContentFlags  礧  donut::engine::MeshInfo & 鎆  donut::engine::DirectionalLight & \]  donut::engine::SceneGraphWalker ( X  donut::engine::animation::Sampler ) 鴍  donut::engine::animation::Keyframe ) 橷  donut::engine::animation::Sequence    donut::engine::MeshType  鯶  donut::engine::SpotLight & 竀  donut::engine::DescriptorHandle , &W  donut::engine::DescriptorTableManager B 鱒  donut::engine::DescriptorTableManager::BindingSetItemsEqual B 餠  donut::engine::DescriptorTableManager::BindingSetItemHasher % _W  donut::engine::VertexAttribute 0 圿  donut::engine::SceneGraphAnimationChannel % t   donut::engine::DescriptorIndex > 誢  donut::engine::ResourceTracker<donut::engine::Material>   [  donut::engine::PointLight ) 鮙  donut::engine::SceneGraphAnimation " nH  donut::engine::StaticShader  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  /  donut::math::float2  }[  donut::math::dquat # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane  瞇  donut::math::daffine3  燵  donut::math::double3 # �  donut::math::vector<float,4> $ 燵  donut::math::vector<double,3>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes $ }j  donut::math::vector<double,4>  �  donut::math::float4 & 	e  donut::math::matrix<double,3,3> % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3>   �.  donut::math::box<float,2>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> $ 瞇  donut::math::affine<double,3> & }[  donut::math::quaternion<double> # y�  donut::render::SkyParameters - 宎  donut::render::PassthroughDrawStrategy 1   donut::render::InstancedOpaqueDrawStrategy  m�  donut::render::SkyPass # va  donut::render::IDrawStrategy M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24 & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  闠  SkyConstants  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t $ 梀  ProceduralSkyShaderParameters  u   uint32_t 
   _iobuf    __crt_locale_pointers    �   �      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽     M]S噴=泥G)w��!&鍌S硚YQD铢g�/  N   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n  �   �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  #   f扥�,攇(�
}2�祛浧&Y�6橵�  a   曀"�H枩U传嫘�"繹q�>窃�8  �   天e�1濎夑Y%� 褡\�Tā�%&閜�  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�      [届T藎秏1潴�藠?鄧j穊亘^a  _   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   揾配饬`vM|�%
犕�哝煹懿鏈椸  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  '   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  f   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  :   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  z   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   dhl12� 蒑�3L� q酺試\垉R^{i�  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  E   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  *   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  s   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  
   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  K   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  	   k&�2箍�#た↗�U嬗醇芧'l�-G恇|:  Q	   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  �	   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �	   v峞M� {�:稚�闙蛂龣 �]<��  
   i廗u<釼醢�(q傏冘;涮��	 醂軷�  :
   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  
   L�9[皫zS�6;厝�楿绷]!��t  �
   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �
   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  9   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  z   猯�諽!~�:gn菾�]騈购����'  �   �0�*е彗9釗獳+U叅[4椪 P"��  �   R冈悚3Y	�1P#��(勁灱�涰n跲
  (   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  g   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  %
   LA+鴏 嘩F\訧昻k��Nea,苛3�  \
   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �
   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �
   v-�+鑟臻U裦@驍�0屽锯
砝簠@  #   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  Z   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k     Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  U   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   繃S,;fi@`騂廩k叉c.2狇x佚�     荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  _   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦      �*o驑瓂a�(施眗9歐湬

�  h   =J�(o�'k螓4o奇缃�
黓睆=呄k_  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   *u\{┞稦�3壅阱\繺ěk�6U�  "   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  b   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  J   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   鹴y�	宯N卮洗袾uG6E灊搠d�     Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  k   妇舠幸佦郒]泙茸餈u)	�位剎  �   靋!揕�H|}��婡欏B箜围紑^@�銵  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  ,   �颠喲津,嗆y�%\峤'找_廔�Z+�  u   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   t�j噾捴忊��
敟秊�
渷lH�#      G�膢刉^O郀�/耦��萁n!鮋W VS  ?   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     $^IXV嫓進OI蔁
�;T6T@佮m琦�  N   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C     a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  Q   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�     +椬恡�
	#G許�/G候Mc�蜀煟-  Y   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  '   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  p   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�     觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  X   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  4   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  l   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  :    狾闘�	C縟�&9N�┲蘻c蟝2  x   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   �'稌� 变邯D)\欅)	@'1:A:熾/�      c�#�'�縌殹龇D兺f�$x�;]糺z�  S   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   交�,�;+愱`�3p炛秓ee td�	^,     zY{���睃R焤�0聃
扨-瘜}  N   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   郖�Χ葦'S詍7,U若眤�M进`  $    g,狁}杯-^郯�檼fa蒣岈2V鉈m �  b    �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �    険L韱#�簀O闚样�4莿Y丳堟3捜狰  �    副謐�斦=犻媨铩0
龉�3曃譹5D   !   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  i!   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �!   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �!   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  2"   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  z"   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �"   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  #   �暊M茀嚆{�嬦0亊2�;i[C�/a\  ;#   咡68[�沘謎7
瑫,j蟫堢>�`~乐�#  t#   _O縋[HU-銌�鼪根�鲋薺篮�j��  �#   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  $   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  d$   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �$   蜅�萷l�/费�	廵崹
T,W�&連芿  �$   v�%啧4壽/�.A腔$矜!洎\,Jr敎  4%   D���0�郋鬔G5啚髡J竆)俻w��  �%   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �%   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  &   匐衏�$=�"�3�a旬SY�
乢�骣�  Q&   チ畴�
�&u?�#寷K�資 +限^塌>�j  �&   悯R痱v 瓩愿碀"禰J5�>xF痧  �&   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  '   矨�陘�2{WV�y紥*f�u龘��  Y'   穫農�.伆l'h��37x,��
fO��  �'   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �'   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  (   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  J(   鏀q�N�&}
;霂�#�0ncP抝  �(   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �(   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  )   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   (      f  �  B   g  �  H   h  �  Y   m  �  �   �  �  U   �  �  �   �    g   �  �  [   �  �
  �  �  �
  �  �     q   �  8  �   �     q   �     q   �  h  �  �  h  B  �  h  �	  �  �
  t  �  8    �  8    �  8  �   �  8    �  8  �   �  8  �     h  �    h  �    h  @
    h  +
    h  �    h  �    �
  5    H
  K   (  8  �   )  8  �   *  8  �   B  h  D
  D  h  �  F  h  O   G  h  0   `  �  �  h      i    �   m    �   q    �   �  �  �  �  �  �   �  �  �   
  8  �     8  �     h  �    h  �  L  h  s  M  h  �  �  h  )
  �  �  �     �  �  8  8  �   9  8  �   >  �  �  g  8  �   V  �  i   X  �  �   ^  �  )  a  �  w  d  �  L  y  8    z  8  �   {  8  �   �  8    �  8  �   �  8    �  8  �   �     5   �     @   �     5   �     @   �     5   �  8  �   �  8    �  8  �   �  8  �   �  8  �   �     @   �     5   �     @   �     5   �     5   �     5   &  8    '  8  �   (  8  �   *  8    +  8  �   ,  8  �   /  8    0  8  �   1  8  �   :     q   u  8  �   �  `	  :   �  `	  6   �  `	  4   \  8  �   ^  8  �   :  �  ~  <  �  �  =  �  �  >  �
  �  I  �
  Z  m    	  n  x   9  r  �
  D  �  x   �  �  �
  n  �  x   ^  w   �    �     g   b!    k   e!    +  f!  `	  D   g!    /  �!    �   �!    '  �.  `	  z   �.  h  <
  �.    /  �.  h  �  �.    +  �.    �   �.    '  �.      �   m)   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\include\donut\render\DrawStrategy.h D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\RTXPT\External\Donut\src\render\SkyPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\RTXPT\External\Donut\include\donut\render\SkyPass.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\RTXPT\External\Donut\include\donut\engine\View.h D:\RTXPT\External\Donut\include\donut\engine\ShadowMap.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h    �       L�.  nd  c   rd  c  
 Ul  d   Yl  d  
 湈      爭     
 敁      槗     
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    H塡$3繪+罤�L嬞H堿H兟H堿�   H堿H堿 H堿(H堿0H堿8H嬃I岺0@ A�   ff�     驜 �YBX � 驜L �YJX润�T痼Y�X洋��YB�X麦 H兝I冴u獺兟H兞餓冭H冸u塇媆$I嬅�   �   B  I G            �      �   n        �donut::math::operator*<float,4,4,4> 
 >T   a  AK         
 >T   b  AP        
  M        �  , M        �  (- N N                        H & h   �  �  �  �  �  �  �  �      T  Oa     T  Ob     燡  Oresult  O  �   X           �   x      L       9 �   : �
   9 �   : �@   < �P   > ��   ; ��   @ �,   �    0   �   
 k   �    o   �   
 �   �    �   �   
 X  �    \  �   
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,       0      
 �       �      
 �       �      
 �       �      
            
 s  8   w  8  
 �      �     
 H塡$3跮峇L岮L岼D峓f�     A婣鳰峈A堾鳰岪A婣麺岻A堾霢婣鬉堾餉塟餓冸u覌B$H媆$堿0婤(堿4婤,堿8H嬃茿<  �?�   �   �   O G            p      Q   m        �donut::math::affineToHomogeneous<float,3> 
 >�   a  AK        p                         H " h   &  O  H  �  �  �  �      �  Oa     燡  Oresult  O�   @           p        4       	 �     �D    �N    �Q    �,   �    0   �   
 q   �    u   �   
 �   �    �   �   
 �A��I�Y殷Y荔Y审X�W荔X�.聎W荔Q旅(麻    6         �     B G            :       5   e!        �donut::math::length<float,3> 
 ><   a  AJ        :  M        w   %
 >@    _Xx  A�   %       N M        �!   ! M        h   ! N N                        H  h   h  w   �!      <  Oa  O  �               :               + �,   �    0   �   
 d   �    h   �   
 �   �    �   �   
 ,  �    0  �   
 �A��I�Y因Y莉Y沈X�W莉X裦.聎fQ旅(麻    4         �   �   C G            8       3   �.        �donut::math::length<double,3> 
 >蘘   a  AJ        8  M        �.   ! M        �.   ! N N                        H  h   �.  �.      蘘  Oa  O   �               8               + �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 �I��Q�Y荔Y审Y殷X馏X旅   �   �   I G            #       "   �!        �donut::math::lengthSquared<float,3> 
 ><   a  AJ        #  M        h  "  N                        H 
 h   h      <  Oa  O  �               #               ' �,      0     
 k      o     
 �      �     
 �I��Q�Y莉Y沈Y因X硫X旅   �   �   J G            #       "   �.        �donut::math::lengthSquared<double,3> 
 >蘘   a  AJ        #  M        �.  "  N                        H 
 h   �.      蘘  Oa  O �               #               ' �,      0     
 l      p     
 �      �     
 @SH冹P)t$@H嬞�2)|$0(煮z(求Y煮Y荄)D$ 驞B�X蠥(润AY�W荔X�.聎	W荔Q码(妈    �^餒嬅�^D^荔3(t$@�{(|$0驞CD(D$ H兡P[肻         �     E G            �   0   t   g!        �donut::math::normalize<float,3> 
 ><   a  AK        `  AK `     5  M        m  `

 M        �  p	
 >@    _x  A�   d       >@    _y  A�   k       >@    _z  A  p       N N M        e!  	 M        w   J >@    _Xx  A�   J       A�  `     5  N M        �!  	 M        h  	 N N N P                     H  h   �  h  m  w   e!  �!   h   <  Oa  O  �               �               / �,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 Z  �    ^  �   
 j  �    n  �   
   �      �   
 @SH冹P)t$@H嬞�2)|$0(烛z(球Y烛Y荄)D$ 駾BA(闰X序AY�W莉X裦.聎fQ码(妈    �^餒嬅�^D^莉3(t$@�{(|$0駾CD(D$ H兡P[肸         �   �  F G            �   0   r   �.        �donut::math::normalize<double,3> 
 >蘘   a  AK        ^  AK ^     5  M        �.  ^

 M        �   n	
 >A    _x  A�   b       >A    _y  A�   i       >A    _z  A$  n       N N M        �.  	 M        �.  	 M        �.  	 N N N P                     H  h   �   �.  �.  �.  �.   h   蘘  Oa  O�               �               / �,   �    0   �   
 h   �    l   �   
 x   �    |   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 �  �    �  �   
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AJ                                 H�     .!  Othis  O   �   0              8     $       �  �    �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >C   this  AJ                                 H     C  Othis  O   �                  �             �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 3繦�H堿H嬃�   �   �   7 G            
          X        �nvrhi::Rect::Rect 
 >f   this  AJ        
                         H     f  Othis  O   �               
   �            �  �,   �    0   �   
 \   �    `   �   
 �   �    �   �   
 � 3狼AW狼A W汕A茿 茿茿 茿茿  茿$茿( 茿,茿0 茿4茿8 茿<茿@ H堿\H堿d茿D �� 茿H 茿L艫P茿T    f茿X  艫Z AlI|墎�   H嬃�   �   �   E G            �       �           �nvrhi::RenderState::RenderState 
 >�   this  AJ        �                         @ " h   �                  �  Othis  O ,   �    0   �   
 j   �    n   �   
 � H嬃茿�   �   �   S G                              �nvrhi::BlendState::RenderTarget::RenderTarget 
 >�   this  AJ                                 H�     �  Othis  O   ,   �    0   �   
 x   �    |   �   
 H塡$ UVWATAUAVAWH崿$0��感&  �    H+郒�    H3腍墔�%  I嬹M嬓L嬺H孂H塎癏媿0&  H嫕8&  E3鞮�/L塷L塷L塷L塷 L塷(L塷0H婣H吚t�@H�H塆(H婣H塆0L塵怢塵楲塵燣塵↙塵�L塵圓�   fD墊$@L塴$8H岴怘塂$0H岴燞塂$(H岴�H塂$ L�
    L�    H峊$`I�
�    I嬚H峂窰;萾H�L�(H�H�H吷tH��P怘婰$`H吷tL塴$`H��P怐塵�W�E H荅   艵  荅     f荅$  荅'    f荅+ 荅0   f荅4  D塵8H荅馉   H荅   �    �E �   塃艵 艵&D墋麵�L岴餒峊$XI嬑�悁   I嬚H峂繦;萾H�L�(H婳H塛H吷tH��P怘婰$XH吷tL塴$XH��P怘�E3繟峆H嬎�PL孁H嬓H婳(�    L嬥D壄t  茀x   3褹�   H崓�  �    D壄�  菂�  �   菂�     菂�  �  �   f墔p  L塴$X艱$\
H婦$XH墔�  H菂�	     H崓�  H崊�  �   fff�      HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�I�L崊p  H峊$hI嬑�怭  I嬚H峂菻;萾H�L�(H婳H塛H吷tH��P怘婰$hH吷tL塴$hH��P惼吀%  H媉    E�H呟tH�H嬎�P �x; �
u�	D塵H圗LH塢@f荅M  艵O E@)厾  M�)嵃  H菂�     A�  H崟�  H崓�  �    I�L婳L崊�  H峊$PI嬑�恅  I嬚H峂蠬;萾H�L�(H婳H塛H吷tH��P怘婰$PH吷tL塴$PH��P惼EpD塵tL塵xL壄�   L壄�   L壄�   L壄�   L壄�   H崓�   �    菂8      W�3�匑  匬  H墔`  H�    H塂$ L�
    �   D岯鼿崓@  �    怢壄h  艵pI�I嬒�P8笻   篜   劺D蔋�H�H媿�   H;藅(H呟tH�H嬎�PH媿�   H墲�   H吷tH��P怘�H媿�   H;藅(H呟tH�H嬎�PH媿�   H墲�   H吷tH��P怘婳H塋$PH吷tH��P怘�    H塂$ L�
    �   D岯鼿峂@�    怚嬇H塃hH媆$PH呟t
H�H嬎�PH婨hH島@H�4艸9t%H呟t
H�H嬎�P怘�H�H吷tH��P怘婨hH�繦塃hH呟t
H�H嬎�P怚嬢I嬚H岴@H肏峂豀;萾H�L�(H媽@  H墧@  H吷tH��P怘兠H凔(r罤婨hH墔h  H�    L嬎�   D岯鼿峂@�    怢嬎�   D岯鵋峀$P�    茀�   f菂�    茀�    I�I嬒�P8�   �   劺E褕曤   I�M嬏L岴pH峊$pI嬑��0  I嬚H峂郒;萾H�L�(H婳 H塛 H吷tH��P怘婰$pH吷tL塴$pH��P怢嬎�   D岯鼿崓@  �    怘媿�   H吷tL壄�   H��P怘媿�   H吷tL壄�   H��P怘媿�   H吷tL壄�   H��P怘媿�   H吷tL壄�   H��P怘媿�   H吷tL壄�   H��P怘婱xH吷tL塵xH��P怘婾H凓v.H�翲婱 H嬃H侜   rH兟'H婭鳫+罤兝鳫凐w3�    怘嬊H媿�%  H3惕    H嫓$('  H伳�&  A_A^A]A\_^]描    �      (   �   �   g   �   j   �   �    }  m   �  m     �    ,     Z  d   �     X  �    �  �    �  �    �  �    V  �    b  �    t  �    /  �    D  �    [  �    �  �    �  �    �     �  �       �   �  E G              6     �.        �donut::render::SkyPass::SkyPass 
 >R�   this  D�    AJ        B  AM  B     ��  D'   >�$   device  AK        ?  AV  ?     ��  >嘓   shaderFactory  AP        <  AR  <     �  >獺   commonPasses  AL  9     b AQ        9  > .   framebufferFactory  AJ  M     �  EO  (           D0'   >H   compositeView  AI  T      EO  0           D8'   >�!    sampleFramebuffer  AT      ��  >H    sampleView  AW  �    �
  >!   constantBufferDesc  CK  (   �    	  CK (   �    -  D�    >�    layoutDesc  Dp   >V     pipelineDesc  Dp   >�    bindingSetDesc  D�   M        z  佒 M        (  佒HB
 >�    temp  AJ  �      AJ �    
  BX   �    �  B�.       � N N M        y  %伆 M        z  伿 M        (  伿
 >�    temp  AJ  �      AJ �      Bh.       � N N M        &  伮 >�    tmp  AK  �    "  AK �         N M        '  伆C	 M        &  伡 N N N M        �  "乹 M          "乹 M          
乹( M        F  L亂 N N N N M        �  �. M          �2 N M        �  �. M          �. M        M  �. N N N N M        �  � M        )  �HB
 >;     temp  AJ        AJ '    �  B�,       � D`    N N M        �  #�� M        �  � M        )  �
 >;     temp  AJ        AJ       B�,       � N N M        �  �� >;     tmp  AK  �        AK     �    N M        g  ��C	 M        �  �� N N N M        I  j M        r  rM M        �  r	 M        >  { N N N M          �j N N M        ^  f N M        \  b N M        �  ^ N M        {  Z N M        �  T N M        �  7嚄e M          嚄-
[ M        B  -嚒[ M        `  *嚖X M        �  嚝)3
 Z     
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    ] * .  M        �  嚧d
=
 Z   q   >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  噧 M        *  噧GB
 >7     temp  AJ  �      AJ �    B  '  N N M        �  噄 M        )  噄JB
 >;     temp  AJ  p      AJ �      N N M        �  嘜 M        )  嘜JB
 >;     temp  AJ  V      AJ i      N N M        �  �5 M        )  �5JB
 >;     temp  AJ  <      AJ O      N N M        �  � M        )  �JB
 >;     temp  AJ  "      AJ 5      N N M        �  � M        )  �JB
 >;     temp  AJ        AJ       N N M        �  喴 M        1  喴HB
 >�!    temp  AJ  �      AJ �      B�1       � Dp    N N M        �  %啲 M        �  喥 M        1  喥
 >�!    temp  AJ  �      AJ �      B�1       � N N M        /  喚 >�!    tmp  AK  �    "  AK �        N M        0  啲C	 M        /  喐 N N N M        <  啂 N M        =  唎 N M        :  	唂 N M        ^  哶 N M        �  4呪 M        �  � M        �  �
 >D     temp  B@1       � N N M        �  咞 N M        (  呪C M        �  咍 N N N# M        �  厃G
 >N!    i  AI  �    ]  M        �  呅 M        �  呅	
 N N M        :  厳 M        u  厽
 >.!   this  AL  �    g_  M        �  吂 M        �  吂
 >D     temp  B�0       � N N M        �  叧 N M        8  叅 M        
  叅#	 N N N N M        8  厐 M        
  厖#
 N N N M        8  �>	 M        
  匞# N N M        �  �' M        �  �2 M        )  �2
 >;     temp  AJ      ,  
  AJ >      B�0       � N N M        �  �+ >;     tmp  AI  
    {  N M        9  � M          �# N N N M        �  劷'" M        �  匊 M        )  匊
 >;     temp  AJ  �    ,  
  AJ     
  Bp0       � N N M        �  勽 >;     tmp  AI  �    7  N M        9  勥 M          勥# N N N M        �  刬@ N M        �  処
 N M        �  凚 N M        �  �; N M        �  �4 N M        �  �- N M        �  �) N M        �  � M        ,  �HB
 >�!    temp  AJ        AJ !    6  BP       - B0       � N N M        �  %冨 M        �  � M        ,  �
 >�!    temp  AJ  �      AJ       B�/       � N N M        *  凎 >�!    tmp  AK  �    "  AK     Q    N M        +  冨C	 M        *  凂 N N N M        �  !儚 M        �  儚 N N" M        d  僢$#D N M        �  �6 M        �  �6HB
 >D     temp  AJ  ;      AJ L    x !   B /       � Dh    N N M        �  %� M        �  �* M        �  �*
 >D     temp  AJ  &      AJ 6      B /       � N N M        �  �" >D     tmp  AK      "  AK 6    �   :   N M        (  �C	 M        �  � N N N M        �  俴 M        �  俴 N N M        a  
俛 >�    result  BX   f    � N M        �  �% N Z   �  �  "   �&          8         A &h�   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �                    (  )  *  B  C  D  E  F  G  `  �  �  �  �  �  �  �  
             L  M  �  �  �    8  9  >  X  g  ^  a  d               !  #  %  &  '  (  )  1  2  3  x  y  z  {  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  &  '  (  *  +  ,  /  0  1  :  u  �  �  \  ^  :  ;  <  =  >  I  r  �  �.  �.  
 :�&  O        $LN546  '  R�  Othis  '  �$  Odevice   '  嘓  OshaderFactory  ('  獺  OcommonPasses  0'   .  OframebufferFactory  8'  H  OcompositeView  �   !  OconstantBufferDesc  p  �  OlayoutDesc  p  V   OpipelineDesc  �  �  ObindingSetDesc  9
      �   9#      �   9�      b$   9�      �   9�      �   9�      H   9
      �$   92      �   9H      �   9m      =   9�      �$   9      �   9      �   9�      &H   9�      �   9      �   9!      �   9:      �   9O      �   9�      �   9�      �   9�      �   9�      �   9      �   9|      &H   9�      �$   9�      �   9�      �   9      �   91      �   9K      �   9e      �   9      �   9�      �   O   �   �             �     �       8  �j   7  ��   9  �.  ;  �R  ?  �i  <  �q  =  ��  >  ��  @  ��  A  ��  C  ��  D  �  G  �U  H  �a  I  ��  L  �S  O  ��  R  �)  T  ��  U  ��  V  �  W  �>  X  �_  Z  �f  [  ��  c  �  d  ��  e  ��   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$0 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$1 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$2 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$3 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$4 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$5 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   a  T F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$7 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O   �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$32 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$33 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$34 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$35 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$36 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$37 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  �   b  U F                                �`donut::render::SkyPass::SkyPass'::`1'::dtor$11 
 >R�   this  EN  �           EN  '          >!    constantBufferDesc  EN  �           >�    layoutDesc  EN  p          >V     pipelineDesc  EN  p          >�    bindingSetDesc  EN  �                                 �  O  ,   �    0   �   
 r   �    v   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 "  �    &  �   
 2  �    6  �   
 _  �    c  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 I  �    M  �   
 ]  �    a  �   
   �      �   
 %  �    )  �   
 5  �    9  �   
 E  �    I  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 "  �    &  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 D  �    H  �   
 T  �    X  �   
 d  �    h  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    	  �   
 V	  �    Z	  �   
 f	  �    j	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 @
  �    D
  �   
 P
  �    T
  �   
 �
  �    �
  �   
 �
  �    �
  �   
   �      �   
 &  �    *  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �       �   
 W  �    [  �   
 g  �    k  �   
 w  �    {  �   
 �  �    �  �   
 �  �     
  �   
 
  �    
  �   
 L
  �    P
  �   
 \
  �    `
  �   
 k  �    o  �   
   �      �   
 �  �    �  �   
   �      �   
 a  �    e  �   
 u  �    y  �   
 �  �    �  �   
 �  �    �  �   
 j  �    n  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 +  �    /  �   
 ;  �    ?  �   
 K  �    O  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 %  �    )  �   
 5  �    9  �   
 I  �    M  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    "  �   
 .  �    2  �   
 �  �    �  �   
 }  0   �  0  
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 
  �      �   
   �    !  �   
 -  �    1  �   
 =  �    A  �   
 M  �    Q  �   
 ]  �    a  �   
 m  �    q  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 
  �      �   
   �    !  �   
 -  �    1  �   
 =  �    A  �   
 M  �    Q  �   
 ]  �    a  �   
 m  �    q  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �     �    
 9     =    
 M     Q    
 ~     �    
 �     �    
 �     �    
 �          
 T      X     
 �      �     
 �      �     
 �      �     
 !     !    
 >!     B!    
 k!     o!    
 �!     �!    
 "     "    
 %"     )"    
 V"     Z"    
 "     �"    
 �"     �"    
 �"     �"    
 ,#  
   0#  
  
 }#  
   �#  
  
 �#  
   �#  
  
 �#  
   �#  
  
 �#  
   �#  
  
 $  
   $  
  
 C$  
   G$  
  
 �$     �$    
 �$     �$    
 �$     %    
 .%     2%    
 W%     [%    
 �%     �%    
 �%     �%    
 &     &    
 U&     Y&    
 i&     m&    
 �&     �&    
 �&     �&    
 �&     �&    
 '     '    
 p'     t'    
 �'     �'    
 �'     �'    
 (     
(    
 /(     3(    
 Z(     ^(    
 �(     �(    
 �(     �(    
 .)     2)    
 B)     F)    
 s)     w)    
 �)     �)    
 �)     �)    
 �)     �)    
 H*     L*    
 �*     �*    
 �*     �*    
 �*     �*    
 +     +    
 3+     7+    
 `+     d+    
 �+  	   �+  	  
 ,  	   
,  	  
 ,  	   ,  	  
 K,  	   O,  	  
 t,  	   x,  	  
 �,  	   �,  	  
 �,  	   �,  	  
  -  
   $-  
  
 r-  
   v-  
  
 �-  
   �-  
  
 �-  
   �-  
  
 �-  
   �-  
  
 .  
   .  
  
 8.  
   <.  
  
 �.     �.    
 �.     �.    
 �.     �.    
 #/     '/    
 L/     P/    
 w/     {/    
 �/     �/    
 �/     �/    
 J0     N0    
 ^0     b0    
 �0     �0    
 �0     �0    
 �0     �0    
 1     1    
 d1     h1    
 �1     �1    
 �1     �1    
 �1     �1    
 $2     (2    
 O2     S2    
 |2     �2    
 H媻�   �       �    H媻�   H兞�       �    H媻�   H兞�       �    H媻�   H兞�       �    H媻�   H兞 �       �    H媻�   H兞(�       �    H崐�   �       �    H崐p  �       �    H崐p  H兞�       �    H崐p  H兞�       �    H崐p  H兞�       �    H崐p  H兞 �       �    H崐p  H兞(�       �    H崐p  H兞0�       �    H�    H嬃�   �   �   U G                   
   �        �nvrhi::VertexBufferBinding::VertexBufferBinding 
 >�!   this  AJ                                 H�     �!  Othis  O ,   �    0   �   
 z   �    ~   �   
 3狼A  �?H�H堿堿H嬃�   �   �   ? G                      V        �nvrhi::Viewport::Viewport 
 >R   this  AJ                                 H     R  Othis  O   �                  �            i  �,   �    0   �   
 d   �    h   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   8   %   �    ,   >      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   8   %   �    ,   A      �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   D      A      �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   8   %   �       �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AH         AJ          AH        M        �  GCE
 >D     temp  AJ  
       AJ        N (                     0H� 
 h   �   0   .!  Othis  9       �   O�   0           "   8     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 P  �    T  �   
 h  �    l  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >3J   this  AH         AJ          AH        M        ,  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   ,   0   3J  Othis  9       �   O  �   0           "   8     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
 J  �    N  �   
 d  �    h  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         z        �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >?I   this  AH         AJ          AH        M        (  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   (   0   ?I  Othis  9       �   O  �   0           "   8     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   Z  z G            "         �        �nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>::~RefCountPtr<nvrhi::IGraphicsPipeline> 
 >鸌   this  AH         AJ          AH        M        1  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   1   0   鸌  Othis  9       �   O  �   0           "   8     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 	  �    
  �   
 V  �    Z  �   
 p  �    t  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �        �nvrhi::RefCountPtr<nvrhi::IInputLayout>::~RefCountPtr<nvrhi::IInputLayout> 
 >^    this  AH         AJ          AH        M        *  GCE
 >7     temp  AJ  
       AJ        N (                     0H� 
 h   *   0   ^   Othis  9       �   O�   0           "   8     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
 L  �    P  �   
 d  �    h  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�    this  AH         AJ          AH        M        )  GCE
 >;     temp  AJ  
       AJ        N (                     0H� 
 h   )   0   �   Othis  9       �   O  �   0           "   8     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   w        �std::shared_ptr<donut::engine::FramebufferFactory>::~shared_ptr<donut::engine::FramebufferFactory> 
 >.   this  AJ        +  AJ @       M        �  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       @�  h   �  �  �   0   .  Othis  9+       b&   9=       b&   O  �   0           K   �
     $       � �   � �E   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �    [   �       �   �  D G            `      `   #        �nvrhi::BufferDesc::~BufferDesc 
 >   this  AI  
     S L   AJ        
  M        �  GM) M          -(

 M        D   N M        B  -G M        `  &@ M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   q   >#    _Ptr_container  AP  '     8    AP ;       >#    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0     Othis  O ,   �    0   �   
 i   �    m   �   
 }   �    �   �   
 ]  �    a  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  &   �  &  
 H塡$WH冹 H嬞H伭�   L�
    �   D岯    怘婯03�H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘媆$0H兡 _�   �    %   �       �   �  X G            �   
   �   &        �nvrhi::GraphicsPipelineDesc::~GraphicsPipelineDesc 
 >,    this  AI  
     �  AJ        
  M        �  �� M        *  ��DE
 >7     temp  AJ  �       AJ �       N N M        �  | M        )  |DE
 >;     temp  AJ  �       AJ �       N N M        �  h M        )  hDE
 >;     temp  AJ  l       AJ |       N N M        �  T M        )  TDE
 >;     temp  AJ  X       AJ h       N N M        �  @ M        )  @DE
 >;     temp  AJ  D       AJ T       N N M        �  * M        )  *DG
 >;     temp  AJ  .       AJ @       N N                      0H�  h   �  �  )  *  %  )   0   ,   Othis  9<       �   9P       �   9d       �   9x       �   9�       �   9�       �   O  ,   �    0   �   
 }   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 I  �    M  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 +  �    /  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 b  �    f  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 H�    H�H兞�       8      �       �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       8      �       �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   8      �    0   �       �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   8      �    0   �       �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   8      �    0   �       �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H嬆H塜H塸WH侅�   )p鐸嬝�5    H嬺�_q<)x豀孂D)@菵)H窪)P―)X楧)`報D%    驛]鬌)l$@驞-    驛Y�(企Y    �    �NDEW跘/梭8�Y荔Y    �^鴙�]�(鵋峊$ H嬒�    �    駾 駾HDW莉DPDW菵W蠥(袮(莉AY羊AY繟(黍AY黍X�W莉X裦.聎	W莉Q码(妈    駾^莉D^闰D^�W�W沈AZ买AZ润W沈AZ审K�C(求s�YG,�O0�W4�CW荔Y左Y象K�S�_F8驛]捏AY朋C�^0�N(皿Y�V�Y芋C W荔Y梭K$�S(�_F<�%    �]捏C,�^0�N(皿YF�V�Y芋C0�Y梭K4�S8驞_^4驟]荏EY蒹D[<�^0�N(皿YF�V �Y芋C@�Y梭KD�SH�_f@�]%    �cL�~(�v$(求DF,(煮Y諥(润Y求AY润X�W荔X�.聎	W荔Q码(妈    L崪$�   �^痼^sP�{T驞^荔DCXI媅I媠A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�I嬨_�   �   O   �   c   �   s   �   x      �   �   �   �    �   �        �  �   Q  �   �        �   �  R G            �  ^   �  �.        �donut::render::SkyPass::FillShaderParameters  >kb   light  AJ        1  AM  1     � >_�   input  AK        %  AL  %     � >c�   output  AI       � AP          >@     lightAngularSize  A�   l     � >@     lightRadiance  A�   �     � >@     lightSolidAngle  A�   �       M        f!  ��
 >@    a  A�   |       N M        �.  ^ N M        �  % M        �  J N M        �   N N M        �  �� N M        g!  +俍." M        m  偑 N M        e!  +俍. M        w   倢 >@    _Xx  A�   �      A�  �    Z  N M        �!  +俍. M        h  +俍. N N N N M        �  
侶 M        �  侻 N M        �  侶 N N M        q  �
 >@    b  A�       �  A�  �    Z  N M        �.  � N M        �  � M        �  �
 N M        �  � N N M        q  佦
 >@    b  A�   �    D  N M        �  伬 M        �  伵 N M        �  伬 N N M        q  仌
 >@    b  A�   �    F  N M        �.  亞 N M        �  
亅 M        �  亖 N M        �  亅 N N M        i  並 N M        b!  �% N M        �.  ��	*- M        �.  
�

 >A    b  A$      R M        �   
�
 >A    _x  A%       � N N M        �.  ��	 M        �.  ��	 M        �.  ��	 N N N N M        �!  )��-
 >蘘   a  AH  �     b  AH     � M        �   �� >A    _x  A$  �     P  >A    _y  A%  �     K  >A    _z  A&  �     L  N N
 Z   �"   �                     @ ^ h   �  h  i  m  q  �  �  �  w   �   b!  e!  f!  g!  �!  �!  �.  �.  �.  �.  �.  �.   �   kb  Olight  �   _�  Oinput  �   c�  Ooutput  O�   �           �  �     �       �  �   �  �"   �  �%   �  �.   �  �J   �  �l   �  �|   �  ��   �  ��   �  ��   �  ��   �  ��   �  �K  �  �N  �  �S  �  �|  �  ��  �  ��  �  ��  �  �  �  �  �  �H  �  �Z  �  ��  �  ��  �  ��  �  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 <  �    @  �   
 f  �    j  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 i  �    m  �   
 y  �    }  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 W  �    [  �   
 g  �    k  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 @UVWAUAVAWH崿$洒��H侅8	  H�    H3腍墔   H媴�  H嬺H塂$HL嬹H�H嬑H�    M嬮I孁�怭  H�E3�H嬒A峎�吚匇  H墱$0	  H岲$PL墹$(	  L峞燣+�)�$	  H�E嬊�   H嬒�PE3襀崓�   3襆墪�   A竴  H嬝�    �   H崓�   3繦堿鳫�堿茿  �?H岻H冴u錋�   H墔p  H崓x  �    �   H崊�  3�@ ff�     H塒鳫�H岪H冮u�W繦墪x  3缐晙  A竫  H墔�  H崓�  H墪�  厴  H墪�  叏  垥�  H墪�  �    H崊�  �   3襀�H岪H冮u驣婩 I婲(H墪H  H墪P  H墪`  H嬘H墔�   �    M燞墔�   H崟p  I婩H嬎H塃�E怘荅�   崹  厴  E�吀  H��P H崓�   �   H崏�    H崁�   A�H�I�@�A�H�I�@�A�H�I�@�A�H�I餒冴u� H峌�H�H嬎�悙   L岴淗荅�    H峂斍E�    H峌訟�   E3� 婤麺岪堿麳岻�H峈堿饗B鴫A鬍塒餓冮u�(    H崟�   H�A�H嬎E�悹   W繦峀$PW�)D$P)L$`I嬙L岪)D$p)M�A�   驛X   驛`A(驛p�(�(芋YT�(腕Y�X�YL
�X�(企YD
 �X洋X畜H兞H冭u縄兝H兟餓冴u�(D$PL岴@(L$`I嬐H婽$H)E (D$p)M(M�)E )M0塃L塃\塃l塃|墔�   墔�   �    H�L岴 I媀3跘範   H塡$ H嬑�PxH�H崟�   H嬑�惏   H�H峊$0H嬑H塡$8塡$@荄$4   荄$0   �惛   H�峉H嬒A��D;�侰��(�$	  L嫟$(	  H嫓$0	  H�H嬑�怷  H媿   H3惕    H伳8	  A_A^A]_^]�   �   E   p   �            �     �  �    �  �   �  �    |        �   �  D G            �  *   q  �.        �donut::render::SkyPass::Render 
 >a�   this  AJ        <  AV  <     O >�$   commandList  AK        4  AL  4     [ >H   compositeView  AM  O     ? AP        O  >kb   light  AQ        L  AU  L     A >_�   params  BH   9     X AH  1       EO  (           D�	   >u     viewIndex  Ao  [     . >J    viewToWorld  D�    >繨    clipToTranslatedWorld  DP    >闠    skyConstants  D   
 >�"    args  D0    >�!    state  D�  
 >H    view  AI  �     +% M        n  
�	A M        �  
�	 M        �  �, N N N M        m  
偘
 N M        �  佸 M        �  佸 N N M        �  丆
0( N" M        �  �;
 N M        �  乄 N% M        �  ��
	X M        X  �  N N% M        �  	��	! M        V  �� N N Z   �  �.   8	          0          A � h+   �  �  �  �  �  %  &  O  V  X       +  -  .  /  0  x  �  �  �  �  �  �  �  �  �  �  �  �  �  H  m  n  �  �  �  �  �  �  �  �  �  
 : 	  O  p	  a�  Othis  x	  �$  OcommandList  �	  H  OcompositeView  �	  kb  Olight  �	  _�  Oparams  �   J  OviewToWorld " P   繨  OclipToTranslatedWorld     闠  OskyConstants  0   �"  Oargs  �  �!  Ostate  9O       &%   9b       H   9�       H   9      #H   9�      *H   9�      +H   9�      �$   9      �$   90      %   9B      H   9k      �$   O �   �           �  �     �       l  �<   m  �U   o  ��   q  ��   s  ��   q  ��   s  ��   q  ��   s  ��  t  ��  u  ��  s  ��  u  ��  v  ��  w  ��  v  �  w  �v  y  ��  z  ��  {  ��    ��  �  ��  �  �  �  �M  o  �e  �  �q  �  �,   �    0   �   
 i   �    m   �   
 y   �    }   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �      �   
 8  �    <  �   
 H  �    L  �   
 \  �    `  �   
 �  �    �  �   
 ;  �    ?  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 #  �    '  �   
 3  �    7  �   
 C  �    G  �   
 S  �    W  �   
 c  �    g  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       G            �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �   $   �   $  
 �   �    �   �   
 H婹H�    H呉HE旅   ;      �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H                       <    20    2                       B   
 
4 
2p    B                       H    20    <                         N   
 
4 
2p    B           !      !      T    20    <           "      "      Z   
 
4 
2p    B           #      #      `    �                  %      %      f    20    `           '      '      l    B                   x       "           (      (      r   h           {      ~          �    2 B                   �       "           )      )      �   h           �      �          �    2 B                   �       "           *      *      �   h           �      �          �    2
 
4 
2p                 �       �           +      +      �   h           �      �          �    H  B                   �       "           ,      ,      �   h           �      �          �    2 B                   �       "           -      -      �   h           �      �          �    2 B                   �       "           .      .      �   h           �      �          �    2 20               /      /      �   ! t               /      /      �      E           /      /      �   !                 /      /      �   E   K           /      /      �   -6 %4�%����
�p`P          �&            �                 1      1      �   (           �      �   $    �>    .    .    .    .    .    R    �>    f    .    .    .    .    .    .    �    �F       �    	               
               "   �    '   �    .   �    3      8      =   	   B   
   G      L      Q   �    W   �    ^   �    <: -J4
P4�D�Dl� ."`$:"4$:""$T".$F"$<"h$l"�$D�� {* '
���p`P     	            l           2      2         !$ $h� �%4&    l          2      2         l   e          2      2         !       l          2      2         e  �          2      2         ^ ^� J� E� @� ;� 6�	 .x
 h d 4  p      �          3      3          4     p           4      4          4     �           5      5          0 0� x h �0    �           6      6      &   0 0� x h �0    �           7      7      ,    B      :           9      9      2                               s      �       �    Unknown exception                                   �       �                                �      �       �    bad array new length                                �       J                                 P      V      \                   .?AVbad_array_new_length@std@@     ]               ����                      M      �                    .?AVbad_alloc@std@@     ]              ����                      S      �                    .?AVexception@std@@     ]               ����                      Y      �    string too long     ����    ����        ��������main donut/passes/sky_ps.hlsl SkyConstants Sky                                       Y      v      s                         y                   |               ����    @                   Y      v                                         S      �                               �                           �      |              ����    @                   S      �                                         M      �      �                         �                                   �      �      |              ����    @                   M      �   5鷰<吞�=   ?  �?   A�IA  碆              �?       �       �   �   (   & 
7        std::exception::`vftable'    8      8  
    �   (   & 
7        std::bad_alloc::`vftable'    >      >  
    �   3   1 
7        std::bad_array_new_length::`vftable'     A      A  
 噾姏@|#8_9繼焿
=*�(.瞗輩耾"0n頁鎴萩瓠幈I7Hh�K蜌�(`U嶛璓k�2骹Y4听餅添Ah樯湱猪|戁铋od钍d�+蜏!P槄� 苋B
泅n黢斚熠A圍[辠L�"�>螝蕙[彯諔嫼JMH:�mmp悩帟n[;3,�3>飖9屓炥劲絨肆峖=f瓵�o珓� T(倴
_<队=1盹嚡�V[篠缲丟9未s�(�-
<鰏mk'闠巠畻+�lfs鞚qx鳿m�7�`6杄�
蹋茦v慼�4C歁V^,D攆鈅蓠迋�&�kM9=Q枆缳`�^�I
)媒M9盵k谿e.~�
M［溊宧�I�u7\鐀r愺�3>飖9屓D6b�搿E閈原曫闦��'嚤踖p禭诵鰐麌臂ep禭�6IB��6萪O�喠RlfCaeK霵婬(茘bq堲剁'項j軟镽v隥�'項j)U崠1緱�'項j裓�畏+��'項j鳲细嵧�8�'項j妆[a
m�'項j�3�(緓吽$愜w獛啯 1ベm嘲(猕up泸翬蟱_?+mZ繡)�M�1�4萉嶀預棊膬$R驯�-n嶀預棊膬eiJ忋邍毬徇�5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k�.�D�3+眱?�\�0r�鴙奡 訃椻繠镞�/]峣%I栶賑?T竉蝜闟はnN鵘J鈵夵F�/_mF{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埻﨏帲晗Di＄燤�'鹴aR�,F_棢杻#Q噤釺�
鮱了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛鑗\猴� �:x婕!dd�a�:ㄞ辟3钼B澲T"	^r禆蹃嫘Ure%Z剧�5榁侸e��怑Bq癔rHF埁�+���'侴u蒠讜kE桐�DC偶鳛宫�5*窆H絁媳躻5*窆H繈饊�/琧s�487�hK嗉Дs�487銢TD椣-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H$樐蜆{絳$�,-骉職)5珉L0/�鍯�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 束栘p喳6rA苜瀀CRC冼�^笵A傮�颧套堬C�扯3�7_煓�(Mz个耼O榖奯�={檎P越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       葂              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       �       膒T     .debug$S       �             .text$mn       :      眡�     .debug$S                    .text$mn    	   p       戢輊     .debug$S    
   ,         	    .text$mn       :      s懅n     .debug$S       L             .text$mn    
   8      甡�&     .debug$S                
    .text$mn       #       v^
�     .debug$S       �              .text$mn       #       )A     .debug$S       �              .text$mn       �      媾     .debug$S       8             .text$mn       �      �4 �     .debug$S       �             .text$mn              �邆     .debug$S                    .text$mn              恶Lc     .debug$S       �              .text$mn       
       �9�     .debug$S       �              .text$mn       �       (回     .debug$S       �              .text$mn              袁z\     .debug$S        �              .text$mn    !        �     .debug$S    "   �2  �      !    .text$x     #         .�(�!    .text$x     $         kRJ !    .text$x     %         澮舷!    .text$x     &         餚�#!    .text$x     '         0盏�!    .text$x     (         ]W�'!    .text$x     )         jF�!    .text$x     *         籉餬!    .text$x     +         J~騽!    .text$x     ,         件wk!    .text$x     -         褆$�!    .text$x     .         �
o!    .text$x     /         |{^�!    .text$x     0         婝踠!    .text$mn    1          �邆     .debug$S    2   �          1    .text$mn    3          痖I     .debug$S    4   �          3    .text$mn    5   <      .ズ     .debug$S    6   0  
       5    .text$mn    7   <      .ズ     .debug$S    8   L  
       7    .text$mn    9   !      :著�     .debug$S    :   <         9    .text$mn    ;   2      X于     .debug$S    <   <         ;    .text$mn    =   "       坼	     .debug$S    >   �         =    .text$mn    ?   "       坼	     .debug$S    @   �         ?    .text$mn    A   "       坼	     .debug$S    B   �         A    .text$mn    C   "       坼	     .debug$S    D   �         C    .text$mn    E   "       坼	     .debug$S    F   �         E    .text$mn    G   "       坼	     .debug$S    H   �         G    .text$mn    I   K       }'     .debug$S    J   �         I    .text$mn    K   `      板@�     .debug$S    L   �         K    .text$mn    M   �      4;�     .debug$S    N   �  *       M    .text$mn    O         ��#     .debug$S    P   �          O    .text$mn    Q         ��#     .debug$S    R   �          Q    .text$mn    S   B      贘S     .debug$S    T             S    .text$mn    U   B      贘S     .debug$S    V            U    .text$mn    W   B      贘S     .debug$S    X   �          W    .text$mn    Y   H       襶.      .debug$S    Z   �         Y    .text$mn    [   �     �$軩     .debug$S    \   �  2       [    .text$mn    ]   �  	   糡_     .debug$S    ^   �  4       ]    .text$mn    _          aJ鄔     .debug$S    `   �          _    .text$mn    a         崪覩     .debug$S    b   �          a        \       Y        x                �                �                �                �                �                �                               *      ;        K      Q        e      a        �      W        �          i�                    �      5        �      S                  i�                    "      9        G      O        l      7        �      U        �          i�                    �      _                       3      3        N              e      K        �              �              �              �      E        !      G        R              �      =        �      M        �      1              A        @      ?        u      C        �      I        �      !        �      ]        g      [        �               0               �               n	      	        �	              
              G
              �
      
        �
              �
              G              �              �      #        �      *        �
      $        �      %        �      +        �      ,        �      -        �      .        �      /        �      0        �      &        �      '        �      (        �      )        �               �               �               �           __chkstk                        memcpy           memmove          memset           sinf             sqrt             sqrtf            $LN13       Y    $LN5        ;    $LN10       W    $LN7        5    $LN13       S    $LN10       7    $LN16       U    $LN3        _    $LN4        _    $LN37   `   K    $LN40       K    $LN10       E    $LN10       G    $LN10       =    $LN48       M    $LN10       A    $LN10       ?    $LN10       C    $LN18       I    $LN546    !    $LN550      !    $LN195      ]    $LN123      [    $LN52       	    $LN46           $LN17           $LN20           $LN14   :       $LN17           .xdata      c          F┑@Y              c    .pdata      d         X賦鶼        B      d    .xdata      e          （亵;        e      e    .pdata      f          T枨;        �      f    .xdata      g          %蚘%W        �      g    .pdata      h         惻竗W        �      h    .xdata      i          （亵5              i    .pdata      j         2Fb�5        ,      j    .xdata      k          %蚘%S        T      k    .pdata      l         惻竗S        {      l    .xdata      m          （亵7        �      m    .pdata      n         2Fb�7        �      n    .xdata      o          %蚘%U              o    .pdata      p         惻竗U        :      p    .xdata      q          懐j瀇        k      q    .pdata      r         Vbv鵢        �      r    .xdata      s          （亵K        �      s    .pdata      t         粻胄K        �      t    .xdata      u         /
              u    .pdata      v         +eS籈        Q      v    .xdata      w   	      �#荤E        �      w    .xdata      x         jE        �      x    .xdata      y          3狷 E              y    .xdata      z         /
        T      z    .pdata      {         +eS籊        �      {    .xdata      |   	      �#荤G        �      |    .xdata      }         jG               }    .xdata      ~          3狷 G        A      ~    .xdata               /
�=        |          .pdata      �         +eS�=        �      �    .xdata      �   	      �#荤=        �      �    .xdata      �         j=        =       �    .xdata      �          3狷 =        �       �    .xdata      �         �酑M        �       �    .pdata      �          鮩sM        �       �    .xdata      �   	      �#荤M        $!      �    .xdata      �         jM        U!      �    .xdata      �          爲飆M        �!      �    .xdata      �         /
        �!      �    .pdata      �         +eS籄        �!      �    .xdata      �   	      �#荤A        ."      �    .xdata      �         jA        i"      �    .xdata      �          3狷 A        �"      �    .xdata      �         /
�?        �"      �    .pdata      �         +eS�?        "#      �    .xdata      �   	      �#荤?        ^#      �    .xdata      �         j?        �#      �    .xdata      �          3狷 ?        �#      �    .xdata      �         /
        !$      �    .pdata      �         +eS籆        d$      �    .xdata      �   	      �#荤C        �$      �    .xdata      �         jC        �$      �    .xdata      �          3狷 C        6%      �    .xdata      �          （亵I        {%      �    .pdata      �         � 買        �%      �    .xdata      �         范^揑        
&      �    .pdata      �         鳶�I        S&      �    .xdata      �         @鴚`I        �&      �    .pdata      �         [7躀        �&      �    .voltbl     �          飾殪I    _volmd      �    .xdata      �   (      椕y!        .'      �    .pdata      �         I蕅!        &(      �    .xdata      �   	      � )9!        )      �    .xdata      �   b      g狗+!        *      �    .xdata      �   B       b
擾!        +      �    .voltbl     �          川浊!    _volmd      �    .xdata      �         7�!]        ,      �    .pdata      �         舻D嘳        �,      �    .xdata      �         谐�%]        .-      �    .pdata      �         E�A]        �-      �    .xdata      �         _>]        N.      �    .pdata      �         骧M]        �.      �    .xdata      �   4       Ts爋[        n/      �    .pdata      �         ,[?[        �/      �    .xdata      �          
	        �0      �    .pdata      �         悜P�	        �0      �    .xdata      �          
        N1      �    .pdata      �         7G        �1      �    .xdata      �          NTo�        �1      �    .pdata      �         ��        &2      �    .xdata      �          NTo�        l2      �    .pdata      �         暫`g        �2      �    .xdata      �          �9�        �2      �    .pdata      �         礝
        V3      �    .rdata      �                      �3     �    .rdata      �          �;�         �3      �    .rdata      �                      �3     �    .rdata      �                      4     �    .rdata      �          �)         )4      �    .xdata$x    �                      U4      �    .xdata$x    �         虼�)         w4      �    .data$r     �   /      嶼�         �4      �    .xdata$x    �   $      4��         �4      �    .data$r     �   $      鎊=         5      �    .xdata$x    �   $      銸E�         .5      �    .data$r     �   $      騏糡         m5      �    .xdata$x    �   $      4��         �5      �        �5           .rdata      �          燺渾         �5      �    .data       �           烀�          �5      �        36     �    .rdata      �          旲^         Z6      �    .rdata      �          (兤�         q6      �    .rdata      �   
       ��>         �6      �    .rdata      �          2F莄         �6      �    .rdata$r    �   $      'e%�         �6      �    .rdata$r    �         �          �6      �    .rdata$r    �                      7      �    .rdata$r    �   $      Gv�:         7      �    .rdata$r    �   $      'e%�         :7      �    .rdata$r    �         }%B         R7      �    .rdata$r    �                      h7      �    .rdata$r    �   $      `         ~7      �    .rdata$r    �   $      'e%�         �7      �    .rdata$r    �         �弾         �7      �    .rdata$r    �                      �7      �    .rdata$r    �   $      H衡�         8      �    .rdata      �          �7甚         ,8      �    .rdata      �          瑣�#         <8      �    .rdata      �          =-f�         L8      �    .rdata      �          v靛�         \8      �    .rdata      �          _�         l8      �    .rdata      �          稂i�         |8      �    .rdata      �           K{         �8      �        �8           .rdata      �          v靛�         �8      �    .rdata      �          o冺�         �8      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �                   �8  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ??0Viewport@nvrhi@@QEAA@XZ ??0Rect@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??0RenderState@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ ??0VertexBufferBinding@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ ??0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z ?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z ?FillShaderParameters@SkyPass@render@donut@@SAXAEBVDirectionalLight@engine@3@AEBUSkyParameters@23@AEAUProceduralSkyShaderParameters@@@Z ?GetDirection@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ ?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z ?CreateAutoShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0UStaticShader@23@11PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z ??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z ??$normalize@N$02@math@donut@@YA?AU?$vector@N$02@01@AEBU201@@Z ??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z ??$length@N$02@math@donut@@YANAEBU?$vector@N$02@01@@Z ??$length@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$lengthSquared@N$02@math@donut@@YANAEBU?$vector@N$02@01@@Z ??$lengthSquared@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ?dtor$0@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$11@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$1@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$2@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$32@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$33@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$34@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$35@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$36@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$37@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$3@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$4@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$5@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA ?dtor$7@?0???0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $pdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $unwind$??0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z $pdata$??0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z $cppxdata$??0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z $stateUnwindMap$??0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z $ip2state$??0SkyPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@AEBV?$shared_ptr@VShaderFactory@engine@donut@@@std@@AEBV?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBV?$shared_ptr@VFramebufferFactory@engine@donut@@@6@AEBVICompositeView@engine@2@@Z $unwind$?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z $pdata$?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z $chain$2$?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z $pdata$2$?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z $chain$3$?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z $pdata$3$?Render@SkyPass@render@donut@@QEBAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@AEBVDirectionalLight@73@AEBUSkyParameters@23@@Z $unwind$?FillShaderParameters@SkyPass@render@donut@@SAXAEBVDirectionalLight@engine@3@AEBUSkyParameters@23@AEAUProceduralSkyShaderParameters@@@Z $pdata$?FillShaderParameters@SkyPass@render@donut@@SAXAEBVDirectionalLight@engine@3@AEBUSkyParameters@23@AEAUProceduralSkyShaderParameters@@@Z $unwind$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $pdata$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $unwind$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $pdata$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $unwind$??$normalize@N$02@math@donut@@YA?AU?$vector@N$02@01@AEBU201@@Z $pdata$??$normalize@N$02@math@donut@@YA?AU?$vector@N$02@01@AEBU201@@Z $unwind$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $pdata$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_04GHJNJNPO@main@ ??_C@_0BJ@IFPGHCFI@donut?1passes?1sky_ps?4hlsl@ ??_C@_0N@MOCALHPI@SkyConstants@ ??_C@_03LNHMGGNB@Sky@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@3c8efa35 __real@3dcccccd __real@3f000000 __real@3f800000 __real@41200000 __real@41490fdb __real@42b40000 __security_cookie __xmm@3f800000000000000000000000000000 __xmm@80000000000000008000000000000000 