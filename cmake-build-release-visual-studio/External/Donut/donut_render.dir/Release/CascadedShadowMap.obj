d啠祚Gh�1 �      .drectve        <  孉               
 .debug$S        4B 菳  鼊        @ B.debug$T        p   L�             @ B.rdata          @   紖             @ @@.text$mn        �   鼌              P`.debug$S        �   \�        @B.text$mn        �  瑘              P`.debug$S        �  3� 鐚        @B.text$mn        �   _�              P`.debug$S        0  顛 �        @B.text$mn        .   n�              P`.debug$S        �   湉 悙        @B.text$mn        .   鄲              P`.debug$S        �   � �        @B.text$mn            V�              P`.debug$S        �   v� n�        @B.text$mn        :   緭 鴵         P`.debug$S          � "�        @B.text$mn        q   畺              P`.debug$S        �  � �        @B.text$mn        �  /� �     	    P`.debug$S        d
  r� 知     T   @B.text$x         =   � [�         P`.text$mn        d   y� 莓         P`.debug$S        p  绠 W�        @B.text$mn        @   �              P`.debug$S        4  _� 摯        @B.text$mn        ]  洗 ,�         P`.debug$S        �  @� �        @B.text$mn        �  怨 缚         P`.debug$S        �	  昕 幧     *   @B.text$mn        :   2� l�         P`.debug$S        L  v� 绿        @B.text$mn        #   �              P`.debug$S        �   5� )�        @B.text$mn        �   e�          P`.debug$S        8  � <�        @B.text$mn        o   鹧              P`.debug$S        �  _� �        @B.text$mn           C�              P`.debug$S        �   G� '�        @B.text$mn        �  c� �         P`.debug$S        @#  于 �       @B.text$x            � �         P`.text$x            		 	         P`.text$x            #	 3	         P`.text$x            =	 M	         P`.text$x            W	 c	         P`.text$x            m	 y	         P`.text$x            �	 �	         P`.text$x            �	 �	         P`.text$x             �	 �	         P`.text$x             �	 �	         P`.text$mn        <   
 ?
         P`.debug$S        0  ]
 �     
   @B.text$mn        <   � -         P`.debug$S        L  K �
     
   @B.text$mn        !   �
          P`.debug$S        <  0 l        @B.text$mn        2   � �         P`.debug$S        <  � *        @B.text$mn        "   �              P`.debug$S        �  � P        @B.text$mn           � �         P`.debug$S        ,   1        @B.text$mn        K   m              P`.debug$S        �  � �        @B.text$mn        K                 P`.debug$S        �  g 7        @B.text$mn        �   � �         P`.debug$S        �  � l      "   @B.text$mn        j   �! *"         P`.debug$S        �  H" �%        @B.text$mn        	   �& �&         P`.debug$S        �   �& w'        @B.text$mn           �'              P`.debug$S        �   �' �(        @B.text$mn           �(              P`.debug$S        �   �( �)        @B.text$mn        `   �) E*         P`.debug$S        �  Y* -        @B.text$mn           �-              P`.debug$S        �   �- �.        @B.text$mn           �. /         P`.debug$S        �   / �/        @B.text$mn           #0 60         P`.debug$S        �   J0 *1        @B.text$mn        +   f1 �1         P`.debug$S        �   �1 �2        @B.text$mn        d   �2 =3         P`.debug$S        t  e3 �4        @B.text$mn        8   Q5 �5         P`.debug$S        �   �5 �6        @B.text$mn        !   �6 �6         P`.debug$S        �   �6 �7        @B.text$mn        !   8 %8         P`.debug$S        �   /8 9        @B.text$mn        B   G9 �9         P`.debug$S           �9 �:        @B.text$mn        B   �: %;         P`.debug$S          C; S<        @B.text$mn        B   �< �<         P`.debug$S        �   �< �=        @B.text$mn        H   '>              P`.debug$S        �  o> 3@        @B.text$mn        q   KA 糀         P`.debug$S        �  贏 扖        @B.text$mn           FD              P`.debug$S        <  ID 匛        @B.text$mn           誆              P`.debug$S        h  隕 SG        @B.text$mn        =    郍         P`.debug$S        �  闓         @B.text$mn           鯥              P`.debug$S        @  J UK        @B.text$mn        �                 P`.debug$S        P  ?L 廚        @B.text$mn           /O              P`.debug$S        �   3O +P        @B.text$mn        
   gP              P`.debug$S        $  tP 楺        @B.text$mn        !   訯              P`.debug$S        �  鮍 丼        @B.text$mn        J   裇 T         P`.debug$S          %T 9V        @B.text$mn           塚              P`.debug$S        �   嶸 哤        @B.text$mn        &   耊              P`.debug$S        X  鑇 @Y     
   @B.text$mn                         P`.debug$S        8  肶 鸝        @B.text$mn           K[              P`.debug$S        �   P[ <\        @B.text$mn        9   x\              P`.debug$S        t  盶 %^        @B.text$mn           a^              P`.debug$S        $  o^ 揰        @B.text$mn        �   鉥 �`         P`.debug$S        �  奰 哾        @B.text$x            奺 杄         P`.text$mn        �   爀 >f         P`.debug$S          Hf Lj        @B.text$x            Pk \k         P`.text$mn        (   fk              P`.debug$S        �  巏 m        @B.text$mn        �  bm z     
    P`.debug$S        �  歾 "�     �   @B.text$mn        �
  聶 彜         P`.debug$S        T  %� y�     �   @B.text$mn          i� n�         P`.debug$S        �  �  �     �   @B.text$mn           l� 呯         P`.debug$S        �  忕 _�        @B.text$mn          组 哧         P`.debug$S        `  箨 S�     (   @B.text$x            泸 矧         P`.text$x             �         P`.text$mn           �              P`.debug$S          !� 5�        @B.text$mn        
   咊              P`.debug$S        P  掫 怩     
   @B.text$mn           F�              P`.debug$S        ,  I� u�        @B.text$mn            坯 鬻         P`.debug$S        �   � 区        @B.text$mn           � �         P`.debug$S        ,  (� T�        @B.text$mn        A   慂 漾         P`.debug$S        �  妃         @B.text$mn           炳 宁         P`.debug$S        �   牺 �        @B.xdata             �             @0@.pdata             � �        @0@.xdata                           @0@.pdata             $  0         @0@.xdata             N              @0@.pdata             Z  f         @0@.xdata             �              @0@.pdata             �  �         @0@.xdata             �              @0@.pdata             �  �         @0@.xdata             �              @0@.pdata             �           @0@.xdata                          @0@.pdata             * 6        @0@.xdata             T             @0@.pdata             \ h        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             # )        @@.xdata             3             @@.xdata             6             @0@.pdata             > J        @0@.xdata             h �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             �         @0@.voltbl            $               .xdata             (             @0@.pdata             0 <        @0@.xdata             Z             @0@.pdata             b n        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             �          @0@.xdata              2        @0@.pdata             P \        @0@.xdata             z �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                       @0@.voltbl            *               .xdata             ,             @0@.pdata             8 D        @0@.xdata             b             @0@.pdata             j v        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata          $   �         @0@.pdata             0 <        @0@.xdata          	   Z c        @@.xdata          ?   w �        @@.xdata             $             @@.xdata             B             @0@.pdata             J V        @0@.xdata             t             @0@.pdata             | �        @0@.voltbl            �                .xdata          H   � �        @0@.pdata                      @0@.xdata          (   2 Z        @0@.pdata             d p        @0@.xdata          4   � �        @0@.pdata             � �        @0@.xdata             
	 	        @0@.pdata             8	 D	        @0@.xdata          0   b	 �	        @0@.pdata             �	 �	        @0@.xdata          ,   �	 �	        @0@.pdata             
 
        @0@.xdata             :
 J
        @0@.pdata             h
 t
        @0@.xdata             �
 �
        @0@.pdata             �
 �
        @0@.xdata          	   �
 �
        @@.xdata          
   	         @@.xdata             *             @@.voltbl            /               .xdata             5             @0@.pdata             A M        @0@.xdata             k �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.voltbl            �               .xdata             �         @0@.pdata             ) 5        @0@.xdata          	   S \        @@.xdata             p w        @@.xdata             �             @@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             
             @0@.pdata             %
 1
        @0@.xdata             O
             @0@.pdata             W
 c
        @0@.xdata             �
 �
        @0@.pdata             �
 �
        @0@.xdata          	   �
 �
        @@.xdata             �
 �
        @@.xdata                           @@.xdata                          @0@.pdata                      @0@.xdata             5 I        @0@.pdata             g s        @0@.xdata             � �        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata              1        @0@.pdata             O [        @0@.xdata             y �        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata                          @0@.pdata              !        @0@.xdata          0   ?             @0@.pdata             o {        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             
 !        @0@.pdata             ? K        @0@.xdata             i y        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � 	        @0@.xdata             '             @0@.pdata             3 ?        @0@.xdata          $   ]             @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata          
            @@.xdata             0             @@.xdata             3 ;        @@.xdata             E L        @@.xdata          	   V             @@.xdata             _             @0@.pdata             k w        @0@.voltbl            �                .voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.rdata             �         @@@.rdata             2             @@@.rdata             D \        @@@.rdata             z �        @@@.rdata             �             @@@.xdata$x           � �        @@@.xdata$x           �         @@@.data$r         /   / ^        @@�.xdata$x        $   h �        @@@.data$r         $   � �        @@�.xdata$x        $   � �        @@@.data$r         $    *        @@�.xdata$x        $   4 X        @@@.rdata             l             @@@.data               |             @ @�.rdata          p   �         @@@.rdata              � �        @@@.rdata              �          @@@.rdata          p   ( �        @@@.rdata          
   $             @@@.rdata             .             @@@.rdata          (   > f        @@@.rdata$r        $   � �        @@@.rdata$r           � �        @@@.rdata$r           �         @@@.rdata$r        $    2        @@@.rdata$r        $   F j        @@@.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $    *        @@@.rdata$r           H \        @@@.rdata$r           f �        @@@.rdata$r        $   � �        @@@.data$rs        *   �         @@�.rdata$r                     @@@.rdata$r           * 6        @@@.rdata$r        $   @ d        @@@.rdata$r        $   x �        @@@.data$rs        .   � �        @@�.rdata$r           �         @@@.rdata$r                    @@@.rdata$r        $   & J        @@@.rdata$r        $   ^ �        @@@.data$rs        2   � �        @@�.rdata$r           � �        @@@.rdata$r           �         @@@.rdata$r        $    4        @@@.rdata$r        $   H l        @@@.data$rs        1   � �        @@�.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $    /        @@@.rdata$r        $   C g        @@@.data$rs        5   � �        @@�.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   
  .         @@@.rdata$r        $   B  f         @@@.data$rs        L   �  �         @P�.rdata$r           �  �         @@@.rdata$r           �  !        @@@.rdata$r        $    ! D!        @@@.rdata             X!             @0@.rdata             \!             @@@.rdata             d!             @0@.rdata             h!             @0@.rdata             l!             @@@.rdata             t!             @@@.rdata             |!             @P@.rdata             �!             @P@.rdata             �!             @P@.rdata             �!             @P@.rdata             �!             @P@.rdata             �!             @P@.debug$S        \   �! 8"        @B.debug$S        @   L" �"        @B.debug$S        D   �" �"        @B.debug$S        D   �" <#        @B.debug$S        H   P# �#        @B.debug$S        4   �# �#        @B.debug$S        4   �# ($        @B.debug$S        @   <$ |$        @B.chks64         
  �$              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   B  o     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\CascadedShadowMap.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $math 	 $colors 	 $render  $Json 	 $stdext   �   �=  R #   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align " ;    std::memory_order_relaxed " ;   std::memory_order_consume + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy   8   _Mtx_try   8   _Mtx_recursive E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi  �8   std::_INVALID_ARGUMENT  �8   std::_NO_SUCH_PROCESS & �8   std::_OPERATION_NOT_PERMITTED , �8   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - �8   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size : #   std::integral_constant<unsigned __int64,2>::value H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos i #   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi "�   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard \ #   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment � #   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment T #   std::allocator<donut::render::DrawItem>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<std::shared_ptr<donut::render::PlanarShadowMap>,std::shared_ptr<donut::render::PlanarShadowMap>,std::shared_ptr<donut::render::PlanarShadowMap> &&,std::shared_ptr<donut::render::PlanarShadowMap> &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::shared_ptr<donut::render::PlanarShadowMap>,std::shared_ptr<donut::render::PlanarShadowMap>,std::shared_ptr<donut::render::PlanarShadowMap> &&,std::shared_ptr<donut::render::PlanarShadowMap> &>::_Bitcopy_constructible j #   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment � �    std::_Trivial_cat<std::shared_ptr<donut::render::PlanarShadowMap>,std::shared_ptr<donut::render::PlanarShadowMap>,std::shared_ptr<donut::render::PlanarShadowMap> &&,std::shared_ptr<donut::render::PlanarShadowMap> &>::_Bitcopy_assignable _ #   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2#   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,�    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free \ #   std::allocator<donut::render::DrawItem const *>::_Minimum_asan_allocation_alignment ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer q #   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout 5 �    std::filesystem::_File_time_clock::is_steady , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable :�    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi =�   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard 8 �   std::atomic<unsigned long>::is_always_lock_free / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment q #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable h #   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment A #   std::allocator<bool>::_Minimum_asan_allocation_alignment I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment  �   &  � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment i #   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size $ %   std::_Locbase<int>::collate M#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets " %   std::_Locbase<int>::ctype G�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none l #   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment  %    LightType_None  %   LightType_Directional  %   LightType_Spot  %   LightType_Point c #   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment  �?   std::_Consume_header  �?   std::_Generate_header ( �   donut::math::vector<int,2>::DIM . %   donut::math::box<float,2>::numCorners  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN 7 �   std::atomic<unsigned int>::is_always_lock_free 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified + �   donut::math::vector<double,3>::DIM R #   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable + �   donut::math::vector<double,4>::DIM   �   �  O #   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment M #   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment - %    std::integral_constant<int,0>::value c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified % #   std::ctype<char>::table_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified O #   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources Z %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size ) �   donut::math::vector<bool,4>::DIM M #   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R %   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T %   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos  �   k  Z #   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos m #   std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> >::_Minimum_asan_allocation_alignment   3        nvrhi::EntireBuffer   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos Z #   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment x #   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment  �   *<  - �    std::chrono::system_clock::is_steady $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den c #   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den � #   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 a�    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < r  ��枠 std::integral_constant<__int64,10000000>::value 1 r   std::integral_constant<__int64,1>::value � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment Z #   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment - �   std::chrono::steady_clock::is_steady & r   std::ratio<1,1000000000>::num * r  � 蕷;std::ratio<1,1000000000>::den + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits A #   std::allocator<char>::_Minimum_asan_allocation_alignment + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den : #    std::integral_constant<unsigned __int64,0>::value ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy - 9   std::_Invoker_pmf_refwrap::_Strategy B #   std::allocator<float>::_Minimum_asan_allocation_alignment t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size - 9   std::_Invoker_pmf_pointer::_Strategy t#   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n�    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi , 9   std::_Invoker_pmd_object::_Strategy 5 #    donut::render::DepthPass::PipelineKey::Count - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment * �   donut::math::vector<float,2>::DIM + �        nvrhi::rt::c_IdentityTransform   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix � #   std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment . %   donut::math::box<float,3>::numCorners ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits u�    std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi x�   std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 : #   std::integral_constant<unsigned __int64,1>::value 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 � #   std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask 0 �   std::numeric_limits<wchar_t>::is_modulo O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask � #   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 a�    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d�   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 j #   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10   �   Y  � #   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent    �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �   ^R �#   std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi 3   \ std::filesystem::path::preferred_separator  x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  5,  _Thrd_result  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34  D<  _Stl_critical_section 	 !  tm %  7  _s__RTTICompleteObjectLocator2 & 鱥  $_TypeDescriptor$_extraBytes_30 A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � +~  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > w -~  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 觹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > d 鈣  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > c ~  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > h ~  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > G 瞸  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > y ~  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > c 寋  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 
~  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 諀  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ] ~  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ [|  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � 鴠  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 題  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > � 苵  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > [ 鋧  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 諁  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > 苶  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> ^ 緘  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � ?|  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > w 杬  std::_Simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > W祡  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> � 畗  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 憐  std::_Default_allocator_traits<std::allocator<float> > ; 靭  std::hash<std::shared_ptr<donut::engine::Material> > � l|  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> ^ 殅  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> � 憓  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > _ %}  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > C }  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > M y  std::_Conditionally_enabled_hash<donut::engine::BufferGroup const *,1> � }  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 鱸  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � 鰔  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> � 珄  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 飢  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 遼  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 瓅  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 讄  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > � 葇  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > ? 箌  std::equal_to<std::shared_ptr<donut::engine::Material> > 6 z  std::allocator<donut::engine::SkinnedMeshJoint> M M|  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L 磡  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 瘄  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � |  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > w 爘  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > T 巪  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > � 剕  std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > W 儂  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem const *> > � x|  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> � n|  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > U ]|  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > 3 鐆  std::_Ptr_base<donut::engine::LoadedTexture> :  <  std::_Vector_val<std::_Simple_types<unsigned int> > D O|  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 踫  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � A|  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > 6 =u  std::_Ptr_base<donut::engine::DescriptorHandle> � 2|  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~(|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e bt  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U 蕑  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > �|  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> "坸  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 噞  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > d祔  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > U .y  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w |  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > : 3t  std::_Ptr_base<donut::engine::MaterialBindingCache> � |  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > � 顊  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > y 鋥  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > 4 貃  std::allocator<donut::math::vector<float,2> > M 蓒  std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = 秡  std::allocator<donut::math::vector<unsigned short,4> > K 縶  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U  u  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 趚  std::_Ptr_base<donut::engine::BufferGroup> F祘  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> ^ >s  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > � 杮  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 巤  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > e 蘳  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > N 墈  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > { {{  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l   std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > , {  std::allocator<nvrhi::BindingSetItem> K  {  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 鰖  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> # �8  std::allocator<unsigned int> � 鑪  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J 閟  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � 魕  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � w  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � ╬  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > 8 %z  std::equal_to<donut::engine::BufferGroup const *> D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > � 錿  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > +詚  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> g 搑  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L 蘻  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  u  std::allocator<float> � 緕  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1>   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> 8   std::_Ptr_base<donut::engine::CommonRenderPasses> � 爖  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1>  A   std::max_align_t 4 y  std::hash<donut::engine::BufferGroup const *> | 榸  std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > 4 搝  std::allocator_traits<std::allocator<float> > N 厇  std::allocator_traits<std::allocator<donut::render::DrawItem const *> > [ wz  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > l 7k  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > w vq  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > ; �8  std::allocator_traits<std::allocator<unsigned int> > [ cz  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > 6 {u  std::_Ptr_base<donut::engine::SceneTypeFactory> ; Yz  std::hash<std::shared_ptr<donut::engine::MeshInfo> > O 0s  std::_Default_allocator_traits<std::allocator<donut::render::DrawItem> > WUz  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> E Nz  std::_Vector_val<std::_Simple_types<donut::render::DrawItem> > � Dz  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > W .z  std::_Compressed_pair<std::equal_to<donut::engine::BufferGroup const *>,float,1> H 蕆  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ mh  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> �  z  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N 鎟  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X 	z  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> > . Ni  std::integer_sequence<unsigned __int64>  �  std::_Lockit  
-  std::timed_mutex � 訽  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > �   std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy * C2  std::hash<enum nvrhi::ResourceType> / 豜  std::shared_ptr<donut::engine::Material> - 払  std::reverse_iterator<wchar_t const *> 5 鋂  std::shared_ptr<donut::engine::SceneGraphNode> 9 肵  std::shared_ptr<donut::engine::animation::Sampler> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file � z  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > R 聎  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >  �(  std::_Big_uint128  �,  std::condition_variable / 沑  std::weak_ptr<donut::engine::SceneGraph> 騳  std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > ) U6  std::_Narrow_char_traits<char,int> L 0y  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 觟  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> c 芢  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > > " 5;  std::_Align_type<double,64>  f+  std::hash<int>  �5  std::_Num_int_base � "y  std::_Compressed_pair<std::hash<donut::engine::BufferGroup const *>,std::_Compressed_pair<std::equal_to<donut::engine::BufferGroup const *>,float,1>,1>  錊  std::ctype<wchar_t> " �+  std::_System_error_category � y  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / a2  std::_Conditionally_enabled_hash<bool,1> 2 髕  std::shared_ptr<donut::engine::BufferGroup> � 莤  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::float_denorm_style G :d  std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > 4  x  std::shared_ptr<donut::engine::LoadedTexture>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 8g  std::piecewise_construct_t u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � 襴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . 'Z  std::_Ptr_base<donut::engine::MeshInfo> 6 2;  std::allocator_traits<std::allocator<wchar_t> > � 蕎  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1>  &  std::bad_cast h 輎  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > > B [  std::enable_shared_from_this<donut::engine::SceneGraphNode>  玁  std::equal_to<void> 4 s  std::allocator<donut::math::vector<float,4> > � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } 耟  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 恅  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 硍  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 亀  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o hh  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> � je  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > " �5  std::numeric_limits<double>  C&  std::__non_rtti_object < 裋  std::_Ptr_base<donut::engine::DescriptorTableManager> ( 0  std::_Basic_container_proxy_ptr12 4 <w  std::allocator<donut::math::vector<float,3> > � g  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � *\  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � 鵞  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy > 0;  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8> T -w  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >  �5  std::_Num_float_base � #w  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  *  std::logic_error 3   std::weak_ptr<donut::engine::SceneGraphNode> � Lg  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � 飃  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety P w  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 辷  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id ?   std::allocator_traits<std::allocator<unsigned __int64> > : 揨  std::shared_ptr<donut::engine::SkinnedMeshInstance> ] 1e  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z   std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   �5  std::numeric_limits<bool> # �6  std::_WChar_traits<char16_t> _ 檝  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u hv  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl .   std::_Ptr_base<donut::engine::Material> * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error d *v  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z 鴘  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy . 硊  std::initializer_list<unsigned __int64> � ╱  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > % �/  std::_One_then_variadic_args_t D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t>  ~,  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> 7 攗  std::shared_ptr<donut::engine::SceneTypeFactory> � fu  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � ^u  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' 纃  std::allocator<unsigned __int64>  h:  std::false_type  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy H ai  std::_Default_allocator_traits<std::allocator<unsigned __int64> > ! K,  std::hash<std::thread::id> = hd  std::allocator<std::shared_ptr<donut::engine::IView> >  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> � bk  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  �,  std::adopt_lock_t � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 Vu  std::shared_ptr<donut::engine::DescriptorHandle> , �5  std::numeric_limits<unsigned __int64> � *u  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L "u  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > H 4m  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16>  ;  std::string_view  w  std::wstring_view % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound � 奼  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >  `,  std::_Mutex_base b u  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec �   std::_Compressed_pair<std::allocator<donut::render::DrawItem>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem> >,1> D 鰐  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  v:  std::defer_lock_t � 鐃  std::allocator_traits<std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >   �*  std::_Init_once_completer j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �,  std::scoped_lock<> + D@  std::codecvt<wchar_t,char,_Mbstatet> 1 籪  std::_Wrap<donut::render::PlanarShadowMap> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 j wX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � EX  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > � 豻  std::list<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > @ �6  std::_Default_allocator_traits<std::allocator<char32_t> > � 箂  std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > 1 辌  std::array<donut::math::vector<float,3>,8> � 癵  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ m  std::_Atomic_integral<long,4> � lt  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ dt  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > > 俕  std::enable_shared_from_this<donut::engine::SceneGraph> " -  std::lock_guard<std::mutex> K Vt  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > dn  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >  �  std::hash<long double> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � 阯  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > / =Z  std::shared_ptr<donut::engine::MeshInfo>  s:  std::try_to_lock_t U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0 �   std::_Uhash_choose_transparency<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *>,void>  �  std::hash<double> 5 S\  std::shared_ptr<donut::engine::SceneGraphLeaf> O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> , 甦  std::allocator<std::_Container_proxy> " �,  std::_Align_type<double,72> / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception & �.  std::_Zero_then_variadic_args_t ; Lt  std::shared_ptr<donut::engine::MaterialBindingCache> d ps  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ t  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � t  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > 1 碿  std::shared_ptr<donut::engine::PlanarView>  �  std::u32string 鱯  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >  �,  std::cv_status S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 鮯  std::_Vector_val<std::_Simple_types<float> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > A 雜  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + G  std::pair<enum __std_win_error,bool> � 輘  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > >  !,  std::thread  ?,  std::thread::id S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error \ 蝧  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder k纒  std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> ) ~  std::_Atomic_integral_facade<long>  �,  std::mutex 8 琗  std::_Ptr_base<donut::engine::animation::Sampler> % }2  std::hash<enum nvrhi::BlendOp> c   std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B 梥  std::allocator<std::shared_ptr<donut::engine::IShadowMap> >  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> � 塻  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > [ rs  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > M ds  std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> > � Zs  std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > ) q2  std::hash<enum nvrhi::BlendFactor> 輌  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M 骾  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code q =b  std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > > � b  std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > >::_Reallocation_policy J W  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano  �  std::_Iterator_base0 0 漜  std::_Ptr_base<donut::engine::PlanarView> | Hs  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � g  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U @s  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0錰  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 �6  std::_Char_traits<char16_t,unsigned short> 6 3q  std::allocator<donut::render::DrawItem const *> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> F 2s  std::allocator_traits<std::allocator<donut::render::DrawItem> > 9 紿  std::shared_ptr<donut::engine::CommonRenderPasses> ! �9  std::char_traits<char16_t> xul  std::_Hash<std::_Umap_traits<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<donut::engine::BufferGroup const *,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *> >,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 裧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> [ $s  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > - 糫  std::weak_ptr<donut::engine::Material>  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> 0 Ri  std::integer_sequence<unsigned __int64,0> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char> N Qm  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X s  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  9  std::_Invoker_strategy  	G  std::nothrow_t � s  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1> 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T �r  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �5  std::_Default_allocate_traits � $e  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1> � 苧  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 _9  std::allocator_traits<std::allocator<char> > 0 uo  std::_Ptr_base<donut::engine::IShadowMap> � pd  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IView> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IView> > >,1> . 膔  std::allocator<donut::render::DrawItem> ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> # �,  std::unique_lock<std::mutex> ( 淲  std::array<nvrhi::BufferRange,11> ; ;  std::basic_string_view<char,std::char_traits<char> > c Gq  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � 祌  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 琙  std::weak_ptr<donut::engine::SkinnedMeshInstance> 9羒  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > s 璻  std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � 漴  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 時  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock Y 噐  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>  }r  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � Kr  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy _ 5g  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D r  std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base  r*  std::out_of_range # �5  std::numeric_limits<__int64> _ 鱭  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 苢  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > b 坬  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > ~q  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  珸  std::ctype<char> @ bq  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 噈  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > P Xq  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? Nq  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  ;  std::memory_order Z Iq  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � ;q  std::_Compressed_pair<std::allocator<donut::render::DrawItem const *>,std::_Vector_val<std::_Simple_types<donut::render::DrawItem const *> >,1> � $q  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ! /-  std::recursive_timed_mutex q 9a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � a  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy  縒  std::nullopt_t  罻  std::nullopt_t::_Tag  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> K 竝  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > h 騢  std::_Uninitialized_backout_al<std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > ^ Nd  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::render::PlanarShadowMap> > > � 莋  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >  [9  std::ratio<1,1>   �8  std::forward_iterator_tag  �*  std::runtime_error � 猵  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >   	  std::bad_array_new_length T 沺  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> > j ip  std::vector<donut::render::DrawItem,std::allocator<donut::render::DrawItem> >::_Reallocation_policy E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 'p  std::allocator<donut::engine::animation::Keyframe> K p  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > U 	p  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IView> > >  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool> � [e  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  5  std::u16string _ p  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 謔  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 韋  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *>  \  std::nested_exception  �  std::_Distance_unknown ) 榦  std::allocator<nvrhi::BufferRange> ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> ; 漟  std::_Ref_count_obj2<donut::render::PlanarShadowMap> , %@  std::codecvt<char32_t,char,_Mbstatet> 1 噊  std::shared_ptr<donut::engine::IShadowMap> C 蝑  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> > @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F e  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 bo  std::vector<float,std::allocator<float> > F 0o  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 j\  std::_Ptr_base<donut::engine::SceneGraph>  �  std::atomic<long> & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc } 賒  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J ym  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  f_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 4_  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy � 鬾  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , EO  std::default_delete<std::_Facet_base> � 靚  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::_UInt_is_zero + Hc  std::_Ptr_base<donut::engine::IView>  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> C 踤  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . �-  std::vector<bool,std::allocator<bool> > J 蘮  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 沶  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> i ]n  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >  �8  std::ratio<1,10000000> Sn  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d i  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag T |d  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IView> > > j 塵  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A {m  std::allocator_traits<std::allocator<unsigned __int64 *> >  �/  std::allocator<char> T jm  std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> d 0m  std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> > z   std::vector<donut::render::DrawItem const *,std::allocator<donut::render::DrawItem const *> >::_Reallocation_policy G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> ) f  std::allocator<unsigned __int64 *>    std::nullptr_t =MY  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > Lh  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K)h  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard & �8  std::random_access_iterator_tag ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> 2簂  std::unordered_map<donut::engine::BufferGroup const *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<donut::engine::BufferGroup const *>,std::equal_to<donut::engine::BufferGroup const *>,std::allocator<std::pair<donut::engine::BufferGroup const * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  x  std::wstring _ 雓  std::allocator_traits<std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > > ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � 鷊  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  -*  std::domain_error  �  std::u32string_view � Me  std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 俓  std::shared_ptr<donut::engine::SceneGraph>  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > { 閗  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 穔  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy Z ]k  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> > ; 鏥  std::weak_ptr<donut::engine::DescriptorTableManager> $ 72  std::hash<nvrhi::IResource *> ^ 鑙  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IView> > > 4 =\  std::_Ptr_base<donut::engine::SceneGraphLeaf> " 轜  std::_Nontrivial_dummy_type � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet> � c  std::vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > > � 謆  std::vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >::_Reallocation_policy  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage � K`  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � `  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy ! �+  std::_System_error_message  �  std::_Unused_parameter = sk  std::allocator<std::shared_ptr<donut::engine::Light> > h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  諻  std::bad_optional_access A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> 5  c  std::_Ptr_base<donut::render::PlanarShadowMap>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > � dk  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  鴋  std::_Exact_args_t , Zc  std::shared_ptr<donut::engine::IView> � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > Q _k  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � Qk  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base b Ck  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> c 9k  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �+  std::_Generic_error_category  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> � f^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 4^  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> �圷  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > ' �2  std::hash<enum nvrhi::ColorMask> � Ad  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::render::PlanarShadowMap> > >,1>  �?  std::codecvt_base 6 2c  std::shared_ptr<donut::render::PlanarShadowMap>  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > 9 |Z  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ' 訥  std::hash<std::filesystem::path>   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers 4 蘗  std::_Ptr_base<donut::engine::SceneGraphNode> � 蒳  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > � 襤  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> �|g  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E @]  std::deque<unsigned __int64,std::allocator<unsigned __int64> > O #]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U !]  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t & �i  $_TypeDescriptor$_extraBytes_40 # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling $ J  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �  nvrhi::ResourceStates  �!  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16> ! �  nvrhi::SharedResourceFlags  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  t$  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # RW  nvrhi::DescriptorTableHandle  �$  nvrhi::TimerQueryHandle 2 RW  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  X.  nvrhi::FormatInfo  :$  nvrhi::HeapHandle # �$  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �$  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  ]!  nvrhi::IBindingLayout  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 J  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # +k  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline  �$  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �"  nvrhi::ComputeState 2 +k  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  �!  nvrhi::IFramebuffer  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec & k  $_TypeDescriptor$_extraBytes_37  pG  __std_fs_file_id 
 !   _ino_t 
 �,  _Cnd_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats * fZ  donut::engine::SkinnedMeshReference ! 籞  donut::engine::SceneCamera   _b  donut::engine::IShadowMap $ H  donut::engine::ICompositeView  ?H  donut::engine::IView $ X[  donut::engine::SceneGraphNode 0 ![  donut::engine::SceneGraphNode::DirtyFlags " Z  donut::engine::MeshInstance   fH  donut::engine::PlanarView ) ZZ  donut::engine::SkinnedMeshInstance   籢  donut::engine::SceneGraph > 颺  donut::engine::ResourceTracker<donut::engine::MeshInfo> ( h]  donut::engine::AnimationAttribute $ 筜  donut::engine::SceneGraphLeaf ! uW  donut::engine::BufferGroup  榙  donut::engine::Material *  k  donut::engine::Material::HairParams 0 黬  donut::engine::Material::SubsurfaceParams # 腶  donut::engine::CompositeView  語  donut::engine::Light ' ℡  donut::engine::SceneContentFlags  礧  donut::engine::MeshInfo & 鎆  donut::engine::DirectionalLight & \]  donut::engine::SceneGraphWalker ( X  donut::engine::animation::Sampler ) 鴍  donut::engine::animation::Keyframe ) 橷  donut::engine::animation::Sequence    donut::engine::MeshType  鯶  donut::engine::SpotLight & 竀  donut::engine::DescriptorHandle , &W  donut::engine::DescriptorTableManager B 鱒  donut::engine::DescriptorTableManager::BindingSetItemsEqual B 餠  donut::engine::DescriptorTableManager::BindingSetItemHasher % _W  donut::engine::VertexAttribute 0 圿  donut::engine::SceneGraphAnimationChannel " 鮦  donut::engine::MeshGeometry % t   donut::engine::DescriptorIndex > 誢  donut::engine::ResourceTracker<donut::engine::Material>   [  donut::engine::PointLight ) 鮙  donut::engine::SceneGraphAnimation  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  �.  donut::math::box2  /  donut::math::float2  }[  donut::math::dquat # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane & 鄇  donut::math::matrix<double,4,4>  瞇  donut::math::daffine3  燵  donut::math::double3 # �  donut::math::vector<float,4> $ 燵  donut::math::vector<double,3>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes % 籮  donut::math::matrix<float,3,4> & 沯  donut::math::matrix<double,3,4> $ }j  donut::math::vector<double,4>  �  donut::math::float4 & 	e  donut::math::matrix<double,3,3>   d  donut::math::int2 % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3> !  d  donut::math::vector<int,2>   �.  donut::math::box<float,2>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3  �  donut::math::box3_arg % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3>  @  donut::math::bool3 # /  donut::math::vector<float,2> $ 瞇  donut::math::affine<double,3> & }[  donut::math::quaternion<double> ' 峛  donut::render::CascadedShadowMap # #j  donut::render::IGeometryPass - 宎  donut::render::PassthroughDrawStrategy  Fj  donut::render::DepthPass 1 Yj  donut::render::DepthPass::CreateParameters ( Xa  donut::render::DepthPass::Context , _a  donut::render::DepthPass::PipelineKey A Ij  donut::render::DepthPass::PipelineKey::<unnamed-type-bits> ) Fa  donut::render::GeometryPassContext 1   donut::render::InstancedOpaqueDrawStrategy # va  donut::render::IDrawStrategy  
j  donut::render::DrawItem % 塩  donut::render::PlanarShadowMap M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec  u   _Thrd_id_t - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24  h,  _Mtx_internal_imp_t & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 
 \,  _Mtx_t 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result  %,  _Thrd_t - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers   �   P      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �    f扥�,攇(�
}2�祛浧&Y�6橵�  �    曀"�H枩U传嫘�"繹q�>窃�8  >   天e�1濎夑Y%� 褡\�Tā�%&閜�     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   �	玮媔=zY沚�c簐P`尚足,\�>:O     �"睱建Bi圀対隤v��cB�'窘�n  b   [届T藎秏1潴�藠?鄧j穊亘^a  �   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   揾配饬`vM|�%
犕�哝煹懿鏈椸  +   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  h   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  ,   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  j   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  5   dhl12� 蒑�3L� q酺試\垉R^{i�  t   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�      d蜯�:＠T邱�"猊`�?d�B�#G騋  Q   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  3   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  	   `k�"�1�^�`�d�.	*貎e挖芺
脑�  K   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   B廈孢冱獁&r"慙rj涂彍�9忢鶆迦�绬  �   L�9[皫zS�6;厝�楿绷]!��t  	   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  C	   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �	   8馵�-�l4煗.袠6┬缮営�4萤娂螧  �	   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  
   �0�*е彗9釗獳+U叅[4椪 P"��  ?
   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  y
   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �
   �=蔑藏鄌�
艼�(YWg懀猊	*)  �
   v-�+鑟臻U裦@驍�0屽锯
砝簠@  4   煋�	y鋵@$5х葑愔*濋>�( 懪銳  n   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  .   j轲P[塵5m榤g摏癭 鋍1O骺�*�  w   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  -
   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  j
   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �
   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �
   繃S,;fi@`騂廩k叉c.2狇x佚�  )   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  t   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  >   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  {   �*o驑瓂a�(施眗9歐湬

�  �   咡68[�沘謎7
瑫,j蟫堢>�`~乐�#  �    I嘛襨签.濟;剕��7啧�)煇9触�.  <   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   猯�諽!~�:gn菾�]騈购����'  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  D   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L     鹴y�	宯N卮洗袾uG6E灊搠d�  g   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   妇舠幸佦郒]泙茸餈u)	�位剎  �   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  5   靋!揕�H|}��婡欏B箜围紑^@�銵  u   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  <   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �   t�j噾捴忊��
敟秊�
渷lH�#  �   �(M↙溋�
q�2,緀!蝺屦碄F觡     ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  e   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  4   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz     o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  <   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<     o藾錚\F鄦泭|嚎醖b&惰�_槮  @   �
bH<j峪w�/&d[荨?躹耯=�     yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  	   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  [   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥      �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  r   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   *u\{┞稦�3壅阱\繺ěk�6U�     z�0叐i�%`戉3猂|Ei韍訋�#Q@�  H   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说     +4[(広
倬禼�溞K^洞齹誇*f�5  p   �暊M茀嚆{�嬦0亊2�;i[C�/a\  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  $   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  \   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �    狾闘�	C縟�&9N�┲蘻c蟝2  &   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  e   �'稌� 变邯D)\欅)	@'1:A:熾/�  �   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  >   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   �,eWvKEV匞軀馻驈︻�~(阸呂糀?�~  �   交�,�;+愱`�3p炛秓ee td�	^,      
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  B    zY{���睃R焤�0聃
扨-瘜}  {    _臒~I��歌�0蘏嘺QU5<蝪祰S  �    郖�Χ葦'S詍7,U若眤�M进`  !   k&�2箍�#た↗�U嬗醇芧'l�-G恇|:  M!   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �!   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �!   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  "   副謐�斦=犻媨铩0
龉�3曃譹5D   U"   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �"   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �"   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  !#   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  ^#   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �#   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �#   +Gtd歾$皅\�6妮`胧絶+狎6s示v�  ,$   Z�=g!9﹞�+庐隀鶋鶾��0L-�誩Q  j$   =J�(o�'k螓4o奇缃�
黓睆=呄k_  �$   _O縋[HU-銌�鼪根�鲋薺篮�j��  �$   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  C%   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �%   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �%   蜅�萷l�/费�	廵崹
T,W�&連芿  &   v�%啧4壽/�.A腔$矜!洎\,Jr敎  f&   D���0�郋鬔G5啚髡J竆)俻w��  �&   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �&   匐衏�$=�"�3�a旬SY�
乢�骣�  A'   v峞M� {�:稚�闙蛂龣 �]<��  �'   悯R痱v 瓩愿碀"禰J5�>xF痧  �'   チ畴�
�&u?�#寷K�資 +限^塌>�j  (   矨�陘�2{WV�y紥*f�u龘��  O(   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �(   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �(   穫農�.伆l'h��37x,��
fO��  )   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  F)   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �)   鏀q�N�&}
;霂�#�0ncP抝  �)   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  *   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  R*   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �         f  H  B   g  H  H   h  H  Y   m  H  �   �    U   �    �   �  �  g   �  �  [   �  �  �  �  �  �  �  �  x  z  �    |  �  �   ~  �  �   �  �  �  �  �  B  �  �  �	  �  �  g   �  �    �  �  �   �  �  �     �  �    �  �    �  @
    �  +
    �  �    �  �    �  K   B  �  D
  D  �  �  F  �  O   G  �  0   `    �  h  �    j  �  �   k  H  @   m  �  �   p  �  �  q  �  �   �    �  �    �   �    �     �  �    �  �  L  �  s  M  �  �  b  �  �   �  �  )
  �    �       �  >    �  W  �  k     �  f   �  (   r      P   �  \  P   d  �  H  :   �  H  6   �  H  4   ,    @   >  �  �  k  �  �   w   �    x   �  �     �  �  �   �  Q   �   8    �   �  �  �   �  �  �   �  Z  �   �  �  �   �  "  �   �  5  �   �  l  �   �  b  �   �  S  �   �  f  �   �  `  �   �  `  �   �  �   �   �  g   �   �  f   �   P   �  �   �  �  �   �    �   �  5  �   (   t   �   (   P   �   (   F   	!  �  �   !  �  5  !  �     !  �  t  !  �  t  !  �  t  !  �  �  !  �  �  !  �  �   !  P   �   #!  �  t  2!  �  <
  9!    �  ;!    �  D!  P   �   M!  P   �   X!  �  �
  Z!  �  i  [!  �  �
  \!  �  b   ]!  �  b  `!  �  z   a!  �  k   b!  �  k   d!  �  �   e!  �  +  f!  H  D   g!  �  /  h!  �  D  i!  �    j!    �  k!    �  r!  �  c  w!  �  �  x!  �  �  !    >  �!  �  :  �!  �  �  �!  �     �!  �  5  �!  �  d  �!  �  n  �!  �  �  �!  �  �  �!  �  &  �!  �  9  �!  �  :  �!  �  &  �!  P   F  �!  P   9  �!  P   k  �!  �  �   �!  �  �   �!  P   �   �!  �  '  �!  �    "    �  "    �  "    /  "  �  :  +"  �  �  ,"  �  �  /"  P   �   0"  P   �   1"  P   �   2"  P   �   4"  �  n   :"  �  ]  B"    �   C"    �   D"  H  8   E"  �  �   F"  �  �   G"  �  �   H"  �  �   I"    �   J"    �  K"    �  `"      a"    �  b"    �  m"      x"    �  �   �*   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\src\render\CascadedShadowMap.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\include\donut\render\CascadedShadowMap.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\include\donut\engine\ShadowMap.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\RTXPT\External\Donut\include\donut\render\PlanarShadowMap.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\RTXPT\External\Donut\include\donut\render\DrawStrategy.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\render\DepthPass.h D:\RTXPT\External\Donut\include\donut\render\GeometryPasses.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h  �       L�#  ^Z  4   bZ  4  
   5     5  
 xy      |y     
            
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    3繪+罤�L嬞H堿H兟H堿A�   H堿H堿 H堿(H堿0H堿8H堿@H嬃I岺0@ A�   ff�     駻 �YB痱X � 駼L �YJX闰��Y�X硫 H兝I冮u綡兟H冮I冭I冴u淚嬅�   �   C  J G            �       �   �!        �donut::math::operator*<double,3,3,3> 
 >   a  AK         
 >   b  AP          M        0"   1 M        2"    N N                        H & h   ."  /"  0"  2"  3"  4"  <"  ="        Oa       Ob     e  Oresult  O �   `           �   P   	   T       9 �    : �   9 �   : �@   < �P   > ��   ; ��   ? ��   @ �,   �   0   �  
 l   �   p   �  
 �   �   �   �  
 X  �   \  �  
 H嬆H塜WH冹pW�)p鐷孃�@郒嬞H峊$I嬋W蒆+蔒嬝A�   L峅@�H癓岮0@�H蠬岪狉AY鸷   駻aA)f�     (�(尿Y(万YD駻Y �X�X序X羊H兝H冴u蘄兞I冭H冮I冴u涷wPH嬅�WH(铗AYk(骝AYs((买AY�_X駻Yc (蓑AYK0�X�(买AYS駻YC�X�(蓑AYK8�X蝌AY[@�X�D$駻XkHD$8�X�L$(�X�K駻XcPL$H駻XsXC �D$XK0f�kH�sX(t$`�C@H嫓$�   H兡p_�   �   0  F G            �     y  ^!        �donut::math::operator*<double,3> 
 >8[   a  AK          AM       p
 >8[   b  AP        /  AS  /     X, M        !  �	

 N M        �!  �$ N' M        �!  ��.Pc	 N* M        �!  "(4 M        0"  "$ M        2"   N N N p                     @ > h   �   !  !  �!  �!  �!  ."  /"  0"  2"  3"  4"  <"  ="   �   8[  Oa  �   8[  Ob  �   鈉  Oresult  O�   p           �  �     d       �  �   �  �   �  �   �  �,   �  �/   �  ��   �  ��   �  �o  �  �t  �  �y  �  �,   �   0   �  
 h   �   l   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 H冹�bH嬃�j(则AYP(万AYH0)4$�2(�(掾AY 駻Yp駻YX�X�(尿AY`(駻Y@ �X�(万AYh@�X趄AYH8�X仳�X躜X衮q(4$�YH兡�   �   �   D G            �   '   �   �!        �donut::math::operator*<double> 
 >蘘   a  AK        � 
 >   b  AP        �                        H 
 h   !   (   蘘  Oa  0     Ob      鎐  Oresult  O   �   8           �   P      ,       k �   m �	   p ��   q �,   �   0   �  
 f   �   j   �  
 �   �   �   �  
 �   �   �   �  
 �H嬃�X��J�XI�I�B�XA�A�   �   �   D G            .       -   d!        �donut::math::operator+=<float> 
 >o   a  AJ        . 
 ><   b  AK        .                         H     o  Oa     <  Ob  O�               .   �            �  �,   �   0   �  
 f   �   j   �  
 �   �   �   �  
 �   �   �   �  
 �H嬃�X��J�XI�I�B�XA�A�   �   �   E G            .       -   H"        �donut::math::operator+=<double> 
 >e   a  AJ        . 
 >蘘   b  AK        .                         H     e  Oa     蘘  Ob  O   �               .   �            �  �,   �   0   �  
 g   �   k   �  
 �   �   �   �  
 �   �   �   �  
 H嬃f蒮^��A�^硫A�   �   �   E G                
      F"        �donut::math::operator/=<double> 
 >e   a  AJ          
 >A    b  A�         
                         H     e  Oa     A   Ob  O   �                   �            �  �,   �   0   �  
 g   �   k   �  
 �   �   �   �  
 �   �   �   �  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   ]   /   `   5   s      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 s     w    
 �  �   �  �  
 H;蕋kH塼$WH冹 H嬺H塡$0H孂fD  H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;縃媆$0H媡$8H兡 _�   �   �  | G            q      q   ~!        �std::_Destroy_range<std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >  >昩   _First  AJ          AM       V  AJ p       >鱞   _Last  AK          AL       Y  AK p       >�b   _Al  AP           AP       Q     D@    M        "  8  M        �   8  M        !   /	 M        �  )/
 >a&   this  AI  $     B  AI         M        �  @	 N N N N N                       @� " h   �  �  �   !  "  "  "   0   昩  O_First  8   鱞  O_Last  @   �b  O_Al  9>       b&   9U       b&   O�   P           q        D       > �    B �   > �   B �    C �X   B �f   F �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 L塂$H塋$SVWATAUAVAWH冹0L嬯H孂H�L嬧L+郘媦L+鳬�I�������M;�剹  I�荋婭H+菻六H嬔H殃I嬃H+翲;�噉  H�
M嬿I;荓C餗;�嘩  I嬾H伶L塼$xH侢   r1H峃'H;��5  �    H吚�3  H峏'H冦郒塁鳯媱$�   �!H咑tH嬑�    H嬝L媱$�   �3跮塼$xH墱$�   I冧餗�4M峟L塪$(I�    I荈    I婡H吚t�@I� I�I婡I塅L塼$ H媁H�L;陁L嬨�L嬒L嬅I嬚�    H塡$ I嬐H媁L嬒M嬆�    怘�H吷t@L嬊H媁�    H�H媁H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I羚L鸏�H�H塐I嬈H兡0A_A^A]A\_^[描    惕    惕    蹋   ]   �   ]   J  �   a  �   v  �   �  ^   �  s   �  �   �  `      �   �  � G            �     �  �!        �std::vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >::_Emplace_reallocate<std::shared_ptr<donut::render::PlanarShadowMap> const &> 
 >   this  AJ          AM       ��  Dp    >鱞   _Whereptr  AK          AU       ��  >檅   <_Val_0>  AP        ��  �  F� �  AP Z      D�    >#     _Newcapacity  AV  p     �  AV �        Bx   �     ]Q  >#    _Newsize  AW  I     �l" �  >#    _Whereoff  AT  %       >鱞    _Constructed_last  AV  �     	  D(    >#    _Oldsize  AW  ,     �   � >昩    _Constructed_first  D     >鱞    _Newvec  AI  �      
   AI �     � 
  B�   �     � �   M        J"  um乯 M        b"  um乯& M        �  ��)
1%��( M        �  ��$	%)
�
 Z   }   >#    _Block_size  AJ  �       AJ �      >#    _Ptr_container  AH  �       AH �     , � 
 >`    _Ptr  AI  �       AI �     � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        ,  
m
 N N N M        ,"  Ik >#    _Oldcapacity  AJ  M     �   L - �   AJ �     � T �  >#    _Geometric  AH  m     u :  f 
  AH �     � 1 �  M        :"  I N N M        K"  *�  M        �   �  M        h!  �M M        �!  �	 M        >  � N N N M        !  ��  N N N' M        +"  乫(L4#'
 Z   ~!   M        9!  *亜_ M        �  亪):
 Z     
 >   _Ptr  AJ �      >#    _Bytes  AK  �    -    AK �     % M        �  亼d#
=
 Z   q   >#    _Ptr_container  AP  �      AP �    ?  5  >#    _Back_shift  AJ  }    ,  AJ �    ?  5  N N N N Z   L"  L"  *"   0           8         0@ f h   B  �  �  �  �  �  �  ,  >  �   !  8!  9!  h!  �!  �!  "  +"  ,"  -"  :"  J"  K"  b"         $LN84  p     Othis  x   鱞  O_Whereptr  �   檅  O<_Val_0>  (   鱞  O_Constructed_last      昩  O_Constructed_first  O   �   �           �  �     �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   > �   B �*  C �/  E �;  G �>  K �@  L �N  M �S  N �f  V ��  W ��  X ��  = ��  7 ��  V ��   D  F            =      =             �`std::vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >::_Emplace_reallocate<std::shared_ptr<donut::render::PlanarShadowMap> const &>'::`1'::catch$0 
 >   this  EN  p         =  >檅   <_Val_0>  EN  �         =  Z   ~!  9!   (                    � .       __catch$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z$0        $LN84  p     Nthis  x   鱞  N_Whereptr  �   檅  N<_Val_0>  (   鱞  N_Constructed_last      昩  N_Constructed_first  O�   8           =   �     ,       P �   Q �   R �3   S �,   �   0   �  
   �     �  
 '  �   +  �  
 W  �   [  �  
 g  �   k  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 (  �   ,  �  
 T  �   X  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 "  �   &  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 %  �   )  �  
 H  �   L  �  
 X  �   \  �  
   �     �  
 3  �   7  �  
 \  �   `  �  
 t  �   x  �  
 �  �   �  �  
 �  �     �  
   �     �  
 m  �   q  �  
 }  �   �  �  
 �  �   �  �  
 �  �   �  �  
 q     u    
 	  �   	  �  
  
  �   
  �  
 �
  �     �  
 &  �   *  �  
 `     d    
 �     �    
 ,
  �   0
  �  
 H塗$SUH冹(H嬯L婨pH婾(H婱 �    L婨xH嫊�   H婱p�    3�3设    �   �   /   �   8   �   @SH冹 I嬝H;蕋?H+薊3�@ �     L�L塁H�H�H婦H塁L�L塂H兠H�H;聈覯嬃H嬘H嬎�    H嬅H兡 [肳   �      �     � G            d      ^   L"        �std::_Uninitialized_move<std::shared_ptr<donut::render::PlanarShadowMap> *,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >  >鱞   _First  AJ          AJ M     	  >鱞   _Last  AK        S  >昩   _Dest  AP          AP M       >�b   _Al  AQ        [  >騢   _Backout  CI     D     	  CI          C $ 	  M        a"   N M        `"  M
 Z   ~!   N M        m"     M        x"     M        �!    M        "  ''I N M        !  �  N N N N                       @ : h
   B  !  �!  "  "  "  M"  _"  `"  a"  m"  w"  x"   0   鱞  O_First  8   鱞  O_Last  @   昩  O_Dest  H   �b  O_Al  O�   P           d        D       � �	   � �   � �    � �D   � �M   � �^   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 
  �     �  
 *  �   .  �  
 :  �   >  �  
 X  �   \  �  
   �   �  �  
 �  �   �  �  
    �   $  �  
 H冹(3莉J�f繦堿H堿H堿H堿(H堿0H堿8H嬃�A �I@H兡(�   �   �   E G            @      ;   �!        �donut::math::diagonal<double,3> 
 >蘘   a  AK        @  M        1"   
## M        M!  $; N N (                      H  h   M!  1"   8   蘘  Oa  O   �   0           @   P      $       F �   G �;   H �,   �   0   �  
 g   �   k   �  
   �     �  
 H嬆H塜WH侅�   )p鐷孂)x豀峀$ D)@菻嬟D)H窪)P―)X楧)`圖)l$p�    駾[HL崪$�   駾kPH嬊駾SX�    �L$HDW�(d$ DW�(\$0E(弪DYd$@E(膨T$`DW序D$@E(蓑DYL$(A(篁Yt$0A(Y|$XA(�'_駾Y硫EX狎DY躥�(L$P駻X�G O0駾X珧W@駾Y裦垓DY腧Y牝EX腧X躜EX牝DoH駾gP�wXI媅A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�I嬨_肂   �   g   �      �   _  D G            ]  A   L   _!        �donut::math::inverse<double,3> 
 >8[   a  AI  '     	 AK        '  >	e   mInverted  C�       z     �  C�      �     �  C�   0   �     y  D    * M        !  u	)	
% N@ M        �!  k+

 N# M        �!  F			h M        �   	�  >A    _x  A'  ~     ]  >A    _y  A)  u     �  >A    _z  A&  �     d  N N
 Z   �!   �                     @  h   �   !  !  �!  �!     8[  Oa      	e  OmInverted     鈉  Oresult  O �   X           ]  �     L       �  �   �  �F     �L    �T     �Z    �]     �,   �,   �   0   �  
 f   �   j   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 t  �   x  �  
 H嬆H塇USVWATAUAVAWH峢窰侅  E3晒   J�0   L塎X�%    A孂)p‥峺�5    E嬔)x楨嬞�=    )D$ B H塎`D)@圚岲$ 駾    )L$0J0H塃hH岲$8)D$@H+仳B@�D$`W�)L$P(
    )E�)E狉}�)L$p)M� E峚H嬜L岹A凕�0  A�屶   H�M嬎H峀$8H�罙嬆餍L峫$8M炅�I鬏�繢嬸E�$�D  �A鐺�
�L T�T蘃嬺f/羦N�)�	K�
�D I嬓T�T蘃F謋/葀L岻鐻薎峆�IK�
�D T�T膄/葀I峆L��I0K�
�D T�T膄/葀L岻L薎峆I兝H兞`I冾匴���A凕}B�   A+腖c菾�GI嬂I闰L� H�WH�T舔D� T膄/菻F翴�繦嬓I冮u蒐婱XH�WH黍D� T膄/�嘳  H;譼dB\ H�R駼T0D� BD �L�0D膒駼L0\� B\p�T�0駼T�BDp�L艀駼L�\膒�T艀H婨h�f襢.讂tDBD 駼L0f^买^蔅D BDp駼L0f^翨Dp駼D��^买BD�A�劸   駼l (�T膄/�啙   W葾W�(�(膨BYD((蒡BYT 駼Y\0�D$(D$ �蔲L$(镇BYTpfX硫L$0�X�)D$ (膨BYl�駼YDx�L$0W沈黍D$fL$(D$pfX硫M��X�)D$p�M�A�劵   駼l8(�T膄/�啗   AW�W�(�(镇BYD((蒡BYT 駼Y\0�D$D$8��(說L$駼YTpfX�W�D$8�D$H�黍X抿D$H(膨BYDx駼Yl��D$E坒L$fX�E堯E橋X膨E楨�劜   駼lP(�T膄/�啗   W葾W�(�(膨BYD((蒡BYT 駼Y\0�D$(D$P�蔲L$(镇BYTpfX硫L$`�X�)D$P(膨BYl�駼YDx�L$`W沈黍D$fL$(E爁X硫M膀X�)E狉M癏婱`A�罤兞L塎XH兝 H塎`A�螲塃hH�荋冸I兟I兠A��弔��H婨P(D$p(M� (E�H(M�@ �E�H0�@@�1H婨PH�      �H�H塇H塇H塇H塇 H塇(H塇0H塇8H塇@L崪$  A(s鐰({谽(C菼嬨A_A^A]A\_^[]�:   �   M   �   _   �      �   �   �      �   �  D G            �  u   �  �!        �donut::math::inverse<double,3> 
 >   m  AK        �  AK �      
 >	e    b  Dp   
 >	e    a  D    
 >t     j  Ai  ,    `  Ai �     & � 
; \` 
 >t     i  Al      �  Al �      �  >A     scale  A�       �Z a ,M . M        !  $				. M        D!  $				8 M        M!  $				 N N N3 M        D"  � 
D
 N M        4"  佱 N* M        D"  �)		$			Q N M        4"  �%!-#X N M        /"  侁 N M        /"  佢 N M        D"  	� N M        4"  � N M        /"  � N M        2"  厡 N" M        E"  俈'
 >燵   _Tmp  C�       m    + " C�      �     j^ X� � ��  N% M        E"  �4

 >燵   _Tmp  C�       :    3  N M        /"  �: N M        F"  偳
 N M        F"  偓! N M        D"  
傶
��
�� N+ M        G"  
僈}	'
�� M        �   儐���� >A    _x  A�   U    �Y e d  A�  �     uS �� ��  >A    _y  A�   y    � � � �  >A    _z  A�   r    �< � � �  A�  �     1� �� ��  N N2 M        H"  �<$	��

��$ N M        G"  $�$��$�� M        �   �6���� >A    _x  A�   /    � � � �  >A    _y  A�   %    � � � �  >A    _z  A�   6    �x F 4E  A�  �     j^ X� � ��  N N# M        H"  儌"
�� %�� 
 N           @          @ > h   �   !  D!  M!  /"  2"  4"  ="  D"  E"  F"  G"  H"  k"   X    Om  p   	e  Ob      	e  Oa  O�   �          �  P   P   �      � �   � �!   � �$   � �)   � �-   � �2   � �6   � �A   � �E   � �T   � �X   � �c   � �l   � �p   � �u   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �   � �>  � �B  � �c  � �n  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �/  � �4  � �V  � �[  � �g  � �m  � �s  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �K  � �U  � �h  � �y  � ��  � ��  � ��  � ��  � �  � �  � �  � �  � �%  � �)  � �3  � �j  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �%  � �\  � ��  � ��  � �,   �   0   �  
 f   �   j   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 &  �   *  �  
 Z  �   ^  �  
 n  �   r  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 &  �   *  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 )  �   -  �  
   �     �  
 �A��I�Y殷Y荔Y审X�W荔X�.聎W荔Q旅(麻    6   �      �     B G            :       5   e!        �donut::math::length<float,3> 
 ><   a  AJ        :  M        w   %
 >@    _Xx  A�   %       N M        �!   ! M        h   ! N N                        H  h   h  w   �!      <  Oa  O  �               :   �            + �,   �   0   �  
 d   �   h   �  
 �   �   �   �  
 ,  �   0  �  
 �I��Q�Y荔Y审Y殷X馏X旅   �   �   I G            #       "   �!        �donut::math::lengthSquared<float,3> 
 ><   a  AJ        #  M        h  "  N                        H 
 h   h      <  Oa  O  �               #   �            ' �,   �   0   �  
 k   �   o   �  
 �   �   �   �  
 @SH冹P)t$@H嬞�2)|$0(煮z(求Y煮Y荄)D$ 驞B�X蠥(润AY�W荔X�.聎	W荔Q码(妈    �^餒嬅�^D^荔3(t$@�{(|$0驞CD(D$ H兡P[肻   �      �     E G            �   0   t   g!        �donut::math::normalize<float,3> 
 ><   a  AK        `  AK `     5  M        m  `

 M        �  p	
 >@    _x  A�   d       >@    _y  A�   k       >@    _z  A  p       N N M        e!  	 M        w   J >@    _Xx  A�   J       A�  `     5  N M        �!  	 M        h  	 N N N P                     H  h   �  h  m  w   e!  �!   h   <  Oa  O  �               �   �            / �,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 Z  �   ^  �  
 j  �   n  �  
   �     �  
 H冹x
H嬃�jW�D$(D$HD$ (�W潋羏�L$0D$@f�W�IL$P��A f�I0YH�i@�aXH兡x�   �   R  D G            o      j   ]!        �donut::math::scaling<double,3> 
 >蘘   a  AK        o   M        !   N M        �!  

 M        1"  

 M        M!  

 N N N x                      H  h   �   !  M!  �!  1"   �   蘘  Oa  �   鈉  Oresult  O  �   @           o   �     4       b �   c �   d �
   c �j   e �,   �   0   �  
 f   �   j   �  
 h  �   l  �  
 H嬃�   �   �   S G                      %        �donut::math::vector<float,3>::vector<float,3> 
 >z   this  AJ                                 H     z  Othis  O   �                  �            n  �,   u   0   u  
 x   u   |   u  
 �   u   �   u  
 @USVWATAUAVAWH峫$窰侅H  H�    H3腍塃0E嬮A嬝L嬺H塗$8L嬦H塎�3鯤�    H�H塹H塹H塹H塹 L峺(I�7I墂I墂H�
    I塋$@I塼$HI塼$PI塼$X荅�   荅�   H荅�   W�E郒荅�   @坲鄁荅 f荅 塽f塽塢缐]�秴�   圗蹾荅�	   �    �E��   圗鐯坲槠E (    )E荅$    艵(艵�媴�   A翂E�秴�   圗H�L岴繦峊$0I嬑�P(嬛H峂楬;萾H�H�0I婰$I塗$H吷tH��P怘婰$0H吷tH塼$0H��P惽D$`    fn�[荔D$d荄$h    �D$l荄$p    荄$t  �?E呿巭  3繢嬸筆   �    H嬝H塃燞吚tCW� 茾   茾   H�    H�H岾H岲$`H塂$ E嬑M婦$H婽$8�    E3鲭E3鯝嬣H岰H塂$@H塡$HI婽$I;T$ tL�2L塺H呟t�CH�H塟I僁$�L岲$@I峀$�    H峌℉婰$@�    H嬋W荔D$xH� H塂$xH婣H塃�L�1L塹H峊$xI峀$@�    怘媇癏呟t0�����罜凐u!H�H嬎������罜凐u
H�H嬎�P怘婰$HH吷t6�����罙凐u'H媆$HH�H嬎������罜凐uH婰$HH��P�艱嬾A;�寫��M峾$(L媡$8A荄$`    3�9蛋   �  筆   �    H嬝H塃圚吚t?W� 茾   茾   H�    H�H岾F�.H岲$`H塂$ M婦$I嬛�    �3跮峴L塼$PH孄H塡$XW蒊嬑�    I媁I;Wt%3繦�H塀H呟t	�CH媩$XL�2H塟I僄�L岲$PI嬒�    H媩$XH�t6�����罣凐u'H媆$XH�H嬎������罜凐uH婰$XH��P��;蛋   L媡$8岨��H婾鳫凓v.H�翲婱郒嬃H侜   rH兟'H婭鳫+罤兝鳫凐w)�    怚嬆H婱0H3惕    H伳H  A_A^A]A\_^[]描    �   �   A   A   j   >   �   D   �   D   �   �   �  ]   �  J     �   X  �   f  �   �  �   K  ]   r  J   �  �   �  �   �  �   z  ^   �  �   �  `      �   [  Y G            �  '   �  �         �donut::render::CascadedShadowMap::CascadedShadowMap 
 >?b   this  D�    AJ        8  AT  8     pe  D�   >�$   device  B8   5     s AK        0  AV  0     x�yp� i	  B�  �    � >t    resolution  A   -     � Ah        -  A  �    �
 l�� �  >t    numCascades  Ai        *  Am  *     ~q  >t    numPerObjectShadows  A         EO  (           D�   >�   format  EO  0           D�   >0    isUAV  EO  8           D�  
 >�   desc  CK  8   K    	  CK 8       #  D�    >_    cascadeViewport  D`    >t     cascade  A         A  �    �� & 5, c  B�   �    � >2c   planarShadowMap  CI     �      CJ     �    ;    CI    �    �
 T�� �  CJ    �    � \�� �'  D@    >t     object  A   9    of  >2c   planarShadowMap  CV      �      CI           CJ     .      CM     �      CI    E    c
 � [ " CM    E    cc 1 �  � " Y  DP    M        W  乼$ N M        |  乛 M        �  乛HB
 >�    temp  AJ  c      AJ t    F t�� �'  B0   m    ; B�  7    q N N M        z  &�7 M        |  丷 M        �  丷
 >�    temp  AJ  M      AJ ^      Bp  �    � N N M        �  丠 >�   tmp  AK  E      AK ^    �  a o��  C       9     " C      H      +  w o�  N M        �  �7B	 M        �  丅 N N N M        �  #�� M          #�� M          
��( M        F  L�� N N N N M        �  �� M          �� N M        �  �� M          �� M        M  �� N N N N M        �   s M        k!  s M        �!  s N N N M        �   \
 >   this  AW  \     L;  M        j!  \ M        �!  \ N N N M        �   L M        j!  L M        �!  L N N N M        ~  H N M        �   @傊 M        !  傊6
 M        �  傕/
 M        �  傸 N N N N M        �   8倽 M        !  倽/	 M        �  偊/
 >a&   this  AI  �    S  AI �    �
 T�� �  M        �  偨	 N N N N M        Z!  俻 M        �!  倂(H N M        �!  �俻 N N M        �   :�" M        i!  
�",
 Z   �!   M        �!  �. M        I"  �. M        �   �.
 M        h!  �8F M        �!  �8 M        >  �: N N N M        !  ��. N N N N N N M        X!  伒F
 Z   �   >梖    _Rx  AI  �    �  D�    M        �!  � N M        �!  佄  M        �  	佈 N M        B"  來
 Z   �"   N N N M        �  7凣[ M          凣-
Q M        B  -凲Q M        `  *凾N M        �  刐))
 Z     
 >   _Ptr  AH  [      AJ  X      AH y      >#    _Bytes  AK  T    S * $  M        �  刣d
3
 Z   q   >#    _Ptr_container  AH  o      AJ  l      N N N N N N M        �   ;凒 M        !  凒6 M        �  凗/
 M        �  � N N N N M        �   A兏 M        i!  
兏*%
 Z   �!   M        �!  兟 M        I"  兟 M        �   兟 M        h!  兾K M        �!  兾	 M        >  	冃 N N N M        !  �兟 N N N N N N M        [!  僂>
 Z   �   >梖    _Rx  AI  R    �  AI E    c
 � [  B�   E    c M        �!  儬 N M        �!  僞 M        �  	僡 N M        C"  儅
 Z   �"   N N N Z   �"  �"  �"   H          @         A �hi   B  �  �  �  �  �  �  �  �  �  [  \  y  z  |  ~  �  �  �  �  �  �  �                  B  C  D  E  F  G  `  �  �  �  �  �  �           L  M  p  �  �  �    >  X  W  >  �   �   �   �   �   �   �   �   �   !  !  !  2!  X!  Y!  Z!  [!  h!  i!  j!  k!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  �!  )"  B"  C"  I"  l"  
 :0  O        $LN336  �  ?b  Othis  �  �$  Odevice  �  t   Oresolution  �  t   OnumCascades   �  t   OnumPerObjectShadows  �  �  Oformat  �  0   OisUAV  �   �  Odesc  `   _  OcascadeViewport  @   2c  OplanarShadowMap  P   2c  OplanarShadowMap  94      <$   9Z      �   9p      �   ^�     杅   9�      b&   9�      b&   9�      b&   9      b&   ^J     杅   9      b&   91      b&   O �             �               '  ��   .  ��   +  ��   0  ��   ,  ��   -  ��   1  ��   2  ��   3  ��   4  �   5  �  6  �  7  �  8  �  9  �%  :  �t  <  ��  >  ��  A  ��  @  �"  A  �\  C  ��  D  �  >  �.  F  �7  H  �E  J  ��  K  ��  M  ��  N  �4  H  �G  O  ��   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$0 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$1 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$2 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$3 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$4 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   q  i F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$14 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                 �  O   �   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$6 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$7 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O�   q  i F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$10 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                 �  O   �   p  h F                                �`donut::render::CascadedShadowMap::CascadedShadowMap'::`1'::dtor$9 
 >?b   this  EN  �           EN  �         
 >�    desc  EN  �           >_    cascadeViewport  EN  `           >2c    planarShadowMap  EN  @           >2c    planarShadowMap  EN  P                                  �  O,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 $  �   (  �  
 4  �   8  �  
 D  �   H  �  
 v  �   z  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 %  �   )  �  
 P  �   T  �  
 d  �   h  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
   �   "  �  
 6  �   :  �  
 V  �   Z  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 )  �   -  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 X  �   \  �  
 h  �   l  �  
 x  �   |  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
 �  �   �  �  
 .	  �   2	  �  
 >	  �   B	  �  
 %  �   )  �  
 c  �   g  �  
 s  �   w  �  
 �  �   �  �  
 �  �   �  �  
 �  �    
  �  
 
  �   
  �  
 �  �   �  �  
   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 '  �   +  �  
 7  �   ;  �  
 G  �   K  �  
 W  �   [  �  
 p  �   t  �  
 �  �   �  �  
   �     �  
 )  �   -  �  
 L  �   P  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 (  �   ,  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 N  �   R  �  
 �  �   �  �  
   �   	  �  
   �     �  
 <  �   @  �  
 j  �   n  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 }  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 >  �   B  �  
 �  �   �  �  
 �  �   �  �  
 	  �   
  �  
 ,  �   0  �  
 Z  �   ^  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 n  �   r  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 /  �   3  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
    �   $  �  
 N  �   R  �  
 |  �   �  �  
 �  �   �  �  
 �  �      �  
 a  �   e  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 "   �   &   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 !  �   !  �  
 ?!  �   C!  �  
 m!  �   q!  �  
 �!  �   �!  �  
 �!  �   �!  �  
 U"  �   Y"  �  
 i"  �   m"  �  
 �"  �   �"  �  
 �"  �   �"  �  
 �"  �   �"  �  
 #  �   #  �  
 H媻�   H兞�       {   H媻�   H兞�       �   H媻�   H兞(�       �   H媻�   H兞@�       �   H崐�   �       x   H崐@   �       �   H崐�   �       �   H崐P   �       �   @UH冹 H嬯篜   H媿�   �    H兡 ]�   ^   @UH冹 H嬯篜   H媿�   �    H兡 ]�   ^   @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   	   %   a   ,         �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   i   0   i  
 d   i   h   i  
 t   i   x   i  
 �   i   �   i  
 �   i   �   i  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   	   %   a   ,         �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,   o   0   o  
 z   o   ~   o  
 �   o   �   o  
 �   o   �   o  
 �   o   �   o  
 H�    H茿    H堿H�    H�H嬃�               �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !   H     ,       �  �    �  �   �  �   �  �,   m   0   m  
 z   m   ~   m  
   m     m  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   	   %   a      �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2   H     $       H  �   I  �)   J  �,   c   0   c  
 d   c   h   c  
 t   c   x   c  
 �   c   �   c  
 �   c   �   c  
   c     c  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         |        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >\(   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   \(  Othis  9       �   O�   0           "   �     $       �  �   �  �   �  �,   {   0   {  
 �   {   �   {  
 �   {   �   {  
 �   {   �   {  
 �   {   �   {  
 �   {   �   {  
 D  {   H  {  
 \  {   `  {  
 H�    H��   J      �   �   � G                   
   �!        �std::_Ref_count_obj2<donut::render::PlanarShadowMap>::~_Ref_count_obj2<donut::render::PlanarShadowMap> 
 >梖   this  AJ                                 H� 
 h   �      梖  Othis  O  �   (              �            2 �
   8 �,   �   0   �  
 �   �   �   �  
   �     �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �         �std::shared_ptr<donut::render::PlanarShadowMap>::~shared_ptr<donut::render::PlanarShadowMap> 
 >鱞   this  AJ        +  AJ @       M        !  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  !   0   鱞  Othis  9+       b&   9=       b&   O�   0           K   �     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 
  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  x G            K      E   �         �std::shared_ptr<donut::engine::PlanarView>::~shared_ptr<donut::engine::PlanarView> 
 >焎   this  AJ        +  AJ @       M        !  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  !   0   焎  Othis  9+       b&   9=       b&   O  �   0           K   �     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
    �     �  
 v  �   z  �  
 �  �   �  �  
 �  �   �  �  
 H塼$WH冹 H�9H嬹H�劊   H塴$8H媔H;齮OH塡$0@ H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;齯縃媆$0H�H媀H媗$8H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媡$@H兡 _描    太   ^   �   `      �   l  � G            �   
   �   �         �std::vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > >::~vector<std::shared_ptr<donut::engine::IView>,std::allocator<std::shared_ptr<donut::engine::IView> > > 
 >豠   this  AJ          AL       � �  0 M        !  
F9%	 M        ;!  }*B M        �  ��)
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  }     O   2  # M        �  
��#
 
 Z   q   >#    _Ptr_container  AP  �     6    AP �       >#    _Back_shift  AJ  y     S 1   AJ �       N N N M        !  
".8 >蔭   _First  AM  
     � �   >,b   _Last  AN  "     `  M        "  80 M        �   80 M        !  0/	 M        �  9/
 >a&   this  AI  4     B  AI 0       M        �  P	 N N N N N N N                       @� > h   �  �  �  �  �  �   !  !  :!  ;!  !  	"  "  "         $LN57  0   豠  Othis  9N       b&   9e       b&   O�   H           �   �     <       � �
   � �
   � �   � ��    ��   � �,   �   0   �  
   �   !  �  
 -  �   1  �  
 �  �   �  �  
 �  �   �  �  
 M  �   Q  �  
 a  �   e  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 4  �   8  �  
 X  �   \  �  
 h  �   l  �  
 �  �   �  �  
 @SH冹 H嬞H�	H吷tMH婼L嬅�    H�H婼H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �   �   M   ^   e   `      �   2   G            j      j   �         �std::vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >::~vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > > 
 >   this  AI  	     a Z   AJ        	 & M        !  	(L4%	

 Z   ~!   M        9!  *'= M        �  +)
 Z     
 >   _Ptr  AJ L       >#    _Bytes  AK  $     E   -  " M        �  
4#

 Z   q   >#    _Ptr_container  AP  8     1    AP L       >#    _Back_shift  AJ        I ,   AJ L       N N N N                       @�  h   �  �  �  !  8!  9!         $LN28  0     Othis  O  �   8           j   �     ,       � �	   � �^    �d   � �,   �   0   �  
 E  �   I  �  
 Y  �   ]  �  
 �  �   �  �  
   �     �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 H  �   L  �  
 H兞�       �      �   �   R G            	          �         �donut::engine::CompositeView::~CompositeView 
 >玜   this  AJ                                 H� 
 h   �       玜  Othis  O,   �   0   �  
 w   �   {   �  
 �     �   �   T G                       �         �donut::engine::ICompositeView::~ICompositeView 
 >H   this  AJ          D                           H�     H  Othis  O  �                  �            4  �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �     �   �   L G                       �         �donut::engine::IShadowMap::~IShadowMap 
 >Cb   this  AJ          D                           H�     Cb  Othis  O  �                  �	            #  �,   ~   0   ~  
 q   ~   u   ~  
 �   ~   �   ~  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂0    H荂8   艭  H兡 [描    �<   ^   [   `      �   �  F G            `      `   \        �nvrhi::TextureDesc::~TextureDesc 
 >�   this  AI  
     S L   AJ        
  M        �  GM) M          -(

 M        D   N M        B  -G M        `  &@ M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   q   >#    _Ptr_container  AP  '     8    AP ;       >#    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0   �  Othis  O   ,   x   0   x  
 k   x   o   x  
    x   �   x  
 _  x   c  x  
 �  x   �  x  
 �  x   �  x  
 �  x   �  x  
   x     x  
 �  �   �  �  
 �     �   �   L G                       �        �std::_Ref_count_base::~_Ref_count_base 
 >a&   this  AJ          D                           H�     a&  Othis  O  �                  �            ~ �,   y   0   y  
 q   y   u   y  
 �   y   �   y  
 H�    H�H兞�       	      b      �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,   n   0   n  
 {   n      n  
 H�    H�H兞�       	      b      �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (              H            Y  �
   Z  �,   d   0   d  
 e   d   i   d  
 �   d   �   d  
 @SH冹 H�    H嬞H�雎t
篜   �    H嬅H兡 [�	   J      ^      �   �   x G            +      %   �!        �std::_Ref_count_obj2<donut::render::PlanarShadowMap>::`scalar deleting destructor' 
 >梖   this  AI         AJ                                @� 
 h   �!   0   梖  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 孃H嬞H兞H�    H岾(�    H岾�    怘婯H吷tH荂    H��P怈銮t
篽   H嬎�    H嬅H媆$0H兡 _�   �      �   &   �   R   ^      �   g  d G            d   
   Y   �         �donut::render::CascadedShadowMap::`scalar deleting destructor' 
 >?b   this  AI       O  AJ          M        |  + M        �  +DE
 >�    temp  AJ  /       AJ C     !    N N                      0@�  h   |  �  �   �   �   �    0   ?b  Othis  9?       �   O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 c  �   g  �  
 H塡$WH冹 H孂嬟H兞�    雒t
�    H嬒�    H媆$0H嬊H兡 _�   �   &   ^      �   �   ` G            8   
   *   �         �donut::engine::CompositeView::`scalar deleting destructor' 
 >玜   this  AJ        
  AM  
     *                        @�  h   �   �    0   玜  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   ^      �   �   a G            !         �         �donut::engine::ICompositeView::`scalar deleting destructor' 
 >H   this  AI  	       AJ        	                        @� 
 h   �    0   H  Othis  O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   ^      �   �   ] G            !         �         �donut::engine::IShadowMap::`scalar deleting destructor' 
 >Cb   this  AI  	       AJ        	                        @� 
 h   �    0   Cb  Othis  O ,      0     
 �      �     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   	      b   0   ^      �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   j   0   j  
 w   j   {   j  
 �   j   �   j  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   	      b   0   ^      �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,   p   0   p  
 �   p   �   p  
 �   p   �   p  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   	      b   0   ^      �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,   f   0   f  
 w   f   {   f  
 �   f   �   f  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   [   0   [  
 g   [   k   [  
 w   [   {   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
   [     [  
 !  [   %  [  
 1  [   5  [  
 A  [   E  [  
 �  [   �  [  
 H塡$WH冹PH嬞H孃H婭H��P 禜�        L�L岲$@艱$0 A�禤H嬒狸��)D$@�    圱$(H婼�D$ A�R@H媆$`H兡P_�   w   &   4   P   �      �   i  M G            q   
   f   �         �donut::render::CascadedShadowMap::Clear 
 >?b   this  AI  
     ^  AJ        
  >�$   commandList  AK          AM       `  >Y.    depthFormatInfo  AH  #     C 
 Z   T   P                     @  h   x  y   `   ?b  Othis  h   �$  OcommandList  9       �   9b       �$   O   �   8           q         ,       � �   � �#   � �f   � �,   �   0   �  
 r   �   v   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 U  �   Y  �  
 e  �   i  �  
 �  �   �  �  
 �     �   �   [ G                       �         �donut::render::CascadedShadowMap::FillShadowConstants 
 >{b   this  AJ          D    >Sb   constants  AK          D                           @     {b  Othis     Sb  Oconstants  O   �   (                           } �     �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 ;Q`}H婣嬕H襀�忻3烂   �   	  R G                      �         �donut::render::CascadedShadowMap::GetCascade 
 >{b   this  AJ          >u    index  A             M        �    N                        @  h   �   �       {b  Othis     u   Oindex  O   �   H                    <       R �    S �   T �   W �   V �   W �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
    �   $  �  
 @SH冹03繦嬟D;A`}H婭A嬂H繦�凌    H嬅H兡0[肏�H塀H嬅H兡0[�    �      �   ^  V G            =      7   �         �donut::render::CascadedShadowMap::GetCascadeView 
 >?b   this  AJ        =    >u    cascade  Ah        = $ 	  M        �   - M        �   �- N N M        �    N
 Z   �"   0                     @  h   �   �   �   Y!   @   ?b  Othis  P   u   Ocascade  O  �   H           =         <       � �   � �   � �$   � �-   � �4   � �,   �   0   �  
 {   �      �  
 �   �   �   �  
 t  �   x  �  
 @SH冹 H婣H嬟H�H��PPH嬅H兡 [�   �   �   \ G                     �         �donut::render::CascadedShadowMap::GetFadeRangeInTexels 
 >{b   this  AJ          M        �    N                       @  h   �   Y!   0   {b  Othis  9       c   O �   @                    4       s �   t �
   s �
   t �   u �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
    �     �  
 @SH冹 H婹H嬞H呉t婤吚t�    岺�盝t吚u�3繦兡 [肏墊$8H媨H媅H呟tDH塼$0����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�RH媡$0H嬊H媩$8H兡 [肏嬊H媩$8H兡 [�   �     L G            �      �   �         �donut::engine::SceneGraphLeaf::GetNode 
 >擸   this  AI  
     6 (   AJ        
  M        �   6C M        #!  C,
 M        �  M M        �  f	
 N N N N M        �   " M        r!    M           
+( >     _Count  A          A  .       N N N                       @ . h
   �  �     �   �   �   �   !  #!  r!   0   擸  Othis  9d       b&   9v       b&   O �               �               @  �,   |   0   |  
 q   |   u   |  
 �   |   �   |  
 ~  |   �  |  
 �  |   �  |  
   |     |  
   |     |  
 0  |   4  |  
 婣`�   �   �   [ G                      �         �donut::render::CascadedShadowMap::GetNumberOfCascades 
 >{b   this  AJ                                 @     {b  Othis  O   �   0                    $       M �    N �   O �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H婣0H+A(H柳�   �   �   c G            
          �         �donut::render::CascadedShadowMap::GetNumberOfPerObjectShadows 
 >{b   this  AJ        
  M        �     N                        @ 
 h   �       {b  Othis  O  �   0           
         $       Z �    [ �   \ �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 L婣(H婭0I+葖翲六H;羢H繧�烂3烂   �   -  Z G            !           �         �donut::render::CascadedShadowMap::GetPerObjectShadow 
 >{b   this  AJ          >u    index  A         !  M        �    
 N M        �    N                        @  h   �   �   �       {b  Othis     u   Oindex  O   �   H           !         <       _ �    ` �   a �   d �   c �    d �,   �   0   �  
    �   �   �  
 �   �   �   �  
 D  �   H  �  
 @SH冹0L婹(H嬟H婣03襂+翬嬋H柳L;萻M蒆嬘K�疏    H嬅H兡0[肏�H嬅H塖H兡0[�-   �      �   �  X G            J      D   �         �donut::render::CascadedShadowMap::GetPerObjectView 
 >?b   this  AJ        J ,   >u    object  Ah        J 1 	  M        �    N M        �   : M        �   �: N N M        �   " N
 Z   �"   0                     @  h   �   �   �   �   Y!   @   ?b  Othis  P   u   Oobject  O�   x           J         l       � �   � �
   � �
   � �   � �   � �"   � �1   � �:   � �=   � �@   � �D   � �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H婣�   �   �   R G                      �         �donut::render::CascadedShadowMap::GetTexture 
 >{b   this  AJ                                 @ 
 h   y      {b  Othis  O�   0                    $       H �    I �   J �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 @SH冹 H婭H嬟H��P �婡塁H嬅�H兡 [�   �     V G            &          �         �donut::render::CascadedShadowMap::GetTextureSize 
 >{b   this  AJ        
  >�    textureDesc  AH         M        �    N                       @  h   x  �    0   {b  Othis  9       �   O�   8           &         ,       g �   h �   i �    j �,   �   0   �  
 {   �      �  
 �   �   �   �  
   �     �  
    �   $  �  
 @SH冹 H婣H嬟H�H��PHH嬅H兡 [�   �   �   R G                     �         �donut::render::CascadedShadowMap::GetUVRange 
 >{b   this  AJ          M        �    N                       @  h   �   Y!   0   {b  Othis  9       ~c   O   �   @                    4       m �   o �
   m �
   o �   p �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 H岮@�   �   �   O G                      �         �donut::render::CascadedShadowMap::GetView 
 >{b   this  AJ                                 @     {b  Othis  O   �   0                    $       C �    D �   E �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 3繦�  �?H塀塀H塀塀$H塀0塀8H嬄H荁  �?H荁(  �?荁<  �?�   �   .  \ G            9       8   �         �donut::render::CascadedShadowMap::GetWorldToUvzwMatrix 
 >{b   this  AJ        9  D    M        �    ! M            !" M        \  p-'Z( N N N                        @  h      \  �       {b  Othis  O  �   0           9         $       = �    ? �8   @ �,   �   0   �  
 �   �   �   �  
 D  �   H  �  
 H婣H�H�H�`X   �   �   X G                   
   �         �donut::render::CascadedShadowMap::IsLitOutOfBounds 
 >{b   this  AJ          M        �     N                        @  h   �   Y!      {b  Othis  9
       �c   O �   (                           x �    y �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �      �  
 H塡$H塼$WH冹@)t$0(馠媦H媞H;dH婫H吚t�@H�H塋$ H媉H塡$((舞    怘呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;淗媆$PH媡$X(t$0H兡@_肍   �      �   �  Z G            �      �   �         �donut::render::CascadedShadowMap::SetFalloffDistance 
 >?b   this  AJ        $  AJ $     y  T  >@    distance  A�         $  A�  $     y ! C  >昩    <begin>$L0  AM       �  >昩    <end>$L0  AL       s  >2c   cascade  CJ      4       D     M        �   $ M        h!  $M	 M        �!  $	 M        >  - N N N N M        �   1N M        !  N/ M        �  P/ M        �  g	 N N N N
 Z   �"   @                    @ 6 h   �  �  >  �   �   �   �   !  !  Y!  h!  �!   P   ?b  Othis  X   @   Odistance      2c  Ocascade  9e       b&   9|       b&   O  �   H           �         <       5 �   6 �B   8 �K   9 �   6 ��   : ��   �   i F                                �`donut::render::CascadedShadowMap::SetFalloffDistance'::`1'::dtor$0  >2c    cascade  EN                                     �  O ,   �   0   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 8  �   <  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 h  �   l  �  
 �  �   �  �  
 H崐    �       �   H塡$H塴$H塼$WH冹0蛾H媦H媞H;eH婫H吚t�@H�H塋$ H媉H塡$(@墩�    怘呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;汬媆$@H媗$HH媡$PH兡0_肎   �      �   �  Y G            �      �   �         �donut::render::CascadedShadowMap::SetLitOutOfBounds 
 >?b   this  AJ        $  AJ $     z  U  >0    litOutOfBounds  A           A        |  >昩    <begin>$L0  AM       �  >昩    <end>$L0  AL       y  >2c   cascade  CJ      4       D     M        �   $ M        h!  $M	 M        �!  $	 M        >  - N N N N M        �   1O M        !  O/ M        �  Q/ M        �  h	 N N N N
 Z   �"   0                    @ 6 h   �  �  >  �   �   �   �   !  !  Y!  h!  �!   @   ?b  Othis  H   0   OlitOutOfBounds      2c  Ocascade  9f       b&   9}       b&   O   �   H           �         <       - �   . �B   0 �L   1 ��   . ��   2 ��   �   h F                                �`donut::render::CascadedShadowMap::SetLitOutOfBounds'::`1'::dtor$0  >2c    cascade  EN                                     �  O  ,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 9  �   =  �  
 �  �   �  �  
 �  �   �  �  
    �     �  
 p  �   t  �  
 �  �   �  �  
 H崐    �       �   呉xH婣H+AH柳;衹塓`肏婣H+AH柳堿`�   �   /  a G            (       '   �         �donut::render::CascadedShadowMap::SetNumberOfCascadesUnsafe 
 >?b   this  AJ        (  >t    cascades  A         (  M        �    N M        �    N                        @ 
 h   �       ?b  Othis     t   Ocascades  O �   @           (         4       � �    � �   � �   � �'   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 H嬆H塜 UVWAVAWH崹8��H侅�  )p�)x窪)@―)癏���H�    H3腍墔�  H婣D(驢+AI嬝D媿  L孃H柳H嬹E吷x
A;罝L華嬃堿`L崊8  (    3审    D$`�d$l�l$h�t$d�|$`�    嬔D渡殃嬃��凌A���t(与(談襱(穗(蜤勆t(碾(求A@�馏AHAI兝凒r獻嬒�    �%    L岴�W鲵e`W垓t$p��   H峌 堗   H崓�   )E�W���   )M��   )E��  )M��   )E�(    )M�(�W沈uxE M0E@]Pmh�    H峌燞崓�    H)E�@ )M�H0)E�@@)M�HP)E�)M痂    �@(��h�p�x駾@ fZ纅Z审D$@�@0fZ纅Z眢D$0�@8fZ纅Z鲶D$8�@@fZ纅Z��D$D�@HfZ纅EZ荔D$4�@PfZ荔L$L�D$<�l$P�t$T�|$X驞D$\�@X@2�婩`fZ纼�Lc痼D$H埳	  L墹$�  A�   D)�$`  D)�$P  驞D)�$@  驞[D)�$0  驞cI嬣D)�$   驞-    D)�$   驞�   H零驞T$x驞\$|驞e�f愺�0  D(象�4  ��8  �D$L�|$@驛Y煮AY骟AX芋AY摅AX怏AX荏DY鼠Y腆YDX审Y趔L$0�Y梭DY麦X�<  驞X审L$8�T$X�Y梭DXL$4�Y祗  驛Y鲶DX朋AY骟璂  驛X蝮AY铙DX馏L$D�Y梭AX泱\$\驛X祗Y企DXD$<�Y泽XL$0�Y腕X畜Y荏D$P�X|$H�Y企X洋L$8�Y腕X伢綐  �D$T�XT$4�Y企礖  �X袤L$D�暅  �T$@�Y泽  �X\$<�Y腕X畜璓  驛Y鲶X洋AY骟AY铙AX蝮潬  驛X泱XT$H驛X祗暏  �T$X�D$L�L$0�\$\�Y泽Y企Y腕X畜Y荏D$P�Y企X洋L$8�Y腕X伢D$T�Y企XT$4�礣  �X袤AY鲶L$D�暔  �T$@驛X蝮X\$<�Y泽  �Y腕璡  �X畜D$L�Y企潿  �\$\�X洋L$0驛Y骟AY铙XT$H驛X泱AX祗暟  �T$X�Y泽Y腕X畜Y荏D$P�Y企X洋L$8�Y腕X伢D$T�Y企XT$4�礰  �X袤AY鲶L$D�暣  �T$@驛X蝮X\$<�Y泽  �Y腕環  �X畜D$L驛Y骟AY铙X洋澑  驛X泱AX祗XT$H�暭  �T$X�Y泽L$0�\$\�Y企Y腕X畜Y荏D$P�Y企X洋L$8�Y腕X伢D$T�Y企XT$4�祃  �X袤AY鲶L$D�暲  �T$@驛X蝮X\$<�Y泽  �Y腕璽  �X畜D$L�Y企澞  �\$\�X洋L$0驛Y骟AY铙XT$H驛X泱AX祗暼  �T$X�Y泽Y腕X畜Y荏D$P�Y企X洋L$8�Y腕X伢D$T�Y企XT$4�祙  �X袤AY鲶L$D�曁  �T$@驛X篌X\$<�Y泽  �Y腕瓁  �X畜AY铙AY骟X洋澬  驛X牦AX潴XT$H(朋YD$L(腆曉  (煮YT$X�X畜YL$0H崊�  �   (摅|$h�Yt$@(朋Y\$\�X洋YD$P(腆Yl$T�Yd$D�X伢XT$4�YL$8�X躞瓌  驛Y铙X袤曍  �X趔|$t�  驛X牦AY骟X\$<�Xt$H驛X�(朋YD$L�澻  �掂  �祱  (腆YL$0驛Y鲶AX�(�(煮Y\$\�Yt$@�YT$X�X�(朋YD$P�Yl$T�X谹(馏X袮荔X躜D$`�l$d(腆Yd$D�YL$8�XT$4�|$`�X趔X衮D$l驞d$p�d$l�曚  �Xt$H�X\$<�厫  �奠  �t$h�濊  �\$t�@鳤/羦D(想D(润H�/蛍(轶/謜(�/郃(鵺(郉/醱D(�/趙(贖兝I+蘵D咅  驛\狍D\錏W垠\轊W襀峊$`I嬒驛Y弩EY弩AY蒹DX藺(捏D_潴X�(梭D_皿X误跌  驟Z貳W审_驛(Y=    驞Z润DZ谚    H婲�U�(球M�(�求A\仳E 驞X乞AY袮(腁尿E�E狉AY抿AY鼠EY膨X畜}� fZ莉X洋DE�Y抿XU梵AY舊Z黍U荔X闰AY羊EAY皿M愹M仳AY黍X序@fZ莉X洋Y抿XU痼AY舊Z黍U润X闰AY羊E膀AY皿M旘M囹AY黍X序X羊XU@L峂H�H岴 fZ繪岴怚嬜fZ鼠Y皿AY朋X�W荔D$(H塂$ �M樿    驞T$x劺驞\$|驞e��L$L�l$P�t$T�|$X驞D$\@�AE麳冸M+趔E^�夰��D(�$   D(�$   D(�$0  D(�$@  D(�$P  D(�$`  L嫟$�  @肚H媿�  H3惕    L崪$�  I媅HA(s餉({郋(C蠩(硃���I嬨A_A^_^]�4   �   ~   �   �   �   	  |     �   y  �   �  �   �  �      �   �
  �   �
  }   �  �   �  �      �   �  [ G            �  B   y  �         �donut::render::CascadedShadowMap::SetupForCubemapView 
 >?b   this  AJ        b  AL  b     R >kb   light  AK        [  AW  [     U >�   center  AI  Q     � AP        Q  AI u      >@    maxShadowDistance  A�         �  >@    lightSpaceZUp  EO  (           D�   >@    lightSpaceZDown  EO  0           D�   >@    exponent  EO  8           D    >t    numberOfCascades  Ai  X     X  Ai �       EO  @           D   >瞇    viewToWorld  D�    >J    worldToView  E6u�0   #    �
 E6uA4   ]    Y
 E6u�8   6    �
 E6u�<   r    D
 E6uA@       �
 E6uD   I    m
 E6u�H   �    
 E6u L   l    J
 E6uA P   x    >
 E6u� T   ~    8
 E6u� X   �    2
 E6u\   �    +
 >濲    unitBox  B`   �     �  >@     far  A  1    D  A 0    {		D  >0     viewModified  AE  �    �  AE 0    �	�T  >辌    corners  D0  
 >u     i  A   �     �  >t     cascade  A   �    �  A  0    I	& >@     zDown  A  x
    Z  >�   cascadeCenter  C�       o    "  C�      �    %  >濲    cascadeViewBounds  B`   0    �	 >�   halfShadowBoxSize  C     �
    % C     l
    �  >辌    cascadeViewCorners  D�   >@     fadeRange  A�   �
      A�   �
    y >�   corner  C�      v    �  C�      "    Lj Z " C�          �� 0 y@ K* @ * C�           � � � �� �� h� +�  C     {    
  M        \!  g N M        �  {
	 N M          
q  N; M        `!  �44&$	
 >8[   a  AH  �    � ! M        b!  侼	
*
 N& M        �!  � N N# M        ]!  �
R M        !  �k N M        �   �$ N M        �!  �
i M        1"  �
i M        M!  �
i N N N N M        �   9�� M        p  #�� N M        x   �� N N  M        �   �1 M        j  �;(	 M        �  �;(	 >@    _x  A�   p
    <  N N M        m  奣 M        �  奣
 >@    _x  A  Y
      >@    _z  A�   ^
    N  N N M        b  
�1 M        �  奜 >@    _x  A�   6
      >@    _y  A  ;
      >@    _z  A�   C
      N N NQ M        �   圤_��'.3$	 M        x!  �%
 N M        w!  売
 N N M        q  �Af
 >@    b  A�       
  M        �  �Af >@    _x  A�   a    
  N N}M        	!  僉)$	
8@
 	

",	

	

",	

#) -		!<$
	# Nh M        j  偞;	
+*
AM��

��

��

��
gB	��4\ M        �  �8*
AM��

��

��

��
gB	��4 >@    _x  A�   g    Q  A�       �� ; xG J1 G  A�   �    L� �  N N� M        q  �0

8
(��$
��
.d
��
.d(

��#<- M        �  僱z����������� >@    _x  A�   b      A�       �  �  A�   �    : � � � �� h�  >@    _y  A�   ]    
  A�   �    �& � � � �� ��  A�   �    S 5 >@    _z  A�   l    
  A�       �  �  A�   �    | � � � �� ��  N N M        d!  媡X N M        q  �*<a M        �  �*<a >@    _x  A�   k      N N M        b!  �P_
 >蘘   v  AH  �
     NB M        �   姼,	
]
	
 N M        q  奼 M        �  奼 N N M        b!  媖T N M        a!  奀F
" N Z   �   ^!  _!  �"  �"   �          (          A � h-   =  �  �  j  m  p  q  %  b  Q    8  �  x   �   �   �   �   �   �   �   �   �   �   �   �   �   	!  
!  !  !  M!  Y!  \!  ]!  `!  a!  b!  c!  d!  w!  x!  �!  �!  1"  
 :�  O  �  ?b  Othis  �  kb  Olight  �  �  Ocenter  �  @   OmaxShadowDistance  �  @   OlightSpaceZUp  �  @   OlightSpaceZDown     @   Oexponent    t   OnumberOfCascades  �   瞇  OviewToWorld  `   濲  OunitBox  0  辌  Ocorners  `   濲  OcascadeViewBounds  �  辌  OcascadeViewCorners  O   �   �          �     �   �      �  �B   �  �g   �  �q   �  ��   �  ��   �  ��   �  �  �  �
  �  �*  �  �1  �  �}  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��   ��  �  ��   ��  �  ��   �  �  �
   �L   �X   �{   ��   ��   ��   ��   ��   ��   ��   ��   ��   �   �
   �   �   �   �n   �v   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   �    �   �@   �H   �L   �Q   �e   �j   �t   �|   ��   ��   ��   ��   ��   ��   ��   ��   �   �   �   �#   �7   �<   �F   �N   �R   �Z   �d   �n   �z   ��   ��   ��   ��   ��   ��   ��   �	   �   �   �    �$   �,   �R   �\   �b   �l   �t   �~   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   �   �   �   �"   �<   �E   �O   �[   �^   �d   ��   ��   ��   ��   ��   ��   ��   ��   �	   �	   �	   �	   � 	   �,	   �2	   �I	   �M	   �Q	   �U	   �Y	   �e	   �z	   ��	   ��	   ��	   ��	   ��	   ��	   ��	   ��	   �(
   �1
   �G
   �O
   �g
   �l
   �s
   �x
   �|
  
 ��
   ��
  
 ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   ��
   �   �	   �
   �   �   �!   �*   �.   �=   �B   �U   �Y   �]   �a   �f   �k   �t   �x   ��   ��   ��   ��   ��   ��   ��   ��   �,   �7  �  �u   �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 -  �   1  �  
 Y  �   ]  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 e  �   i  �  
 y  �   }  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   	  �  
   �     �  
 -  �   1  �  
 A  �   E  �  
 c  �   g  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
 (  �   ,  �  
 8  �   <  �  
 \  �   `  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 U  �   Y  �  
 e  �   i  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   	  �  
 P	  �   T	  �  
 m	  �   q	  �  
 �	  �   �	  �  
 h
  �   l
  �  
 �
  �   �
  �  
 
  �   
  �  
 
  �   #
  �  
 ?
  �   C
  �  
 !  �   %  �  
 1  �   5  �  
 E  �   I  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 k  �   o  �  
 �  �   �  �  
 �  �   �  �  
 H嬆USVWAUAVAWH崹更��H侅  )p�)x―)@楧)H圖)恱���D)榟���D)燲���D)℉���D)�8���D)�(���H�    H3腍墔`  L嫮�  L嬹媿�  (鉏嬸L孃I婩I+FH柳吷x;�L葖�W繟塅`/鄓@�
    驛 驛PW馏AXW洋A@W袤AP驛X驛\`驛`3跦嵔�  @ f�     D嬅H峌癏嬑�    �肏�� 婡�G魤G鼉�r左=    I嬒D(珞D^  �    L岴繦崟�   H崓�   �%    W���   堗   )E�W���   )M�W��   )E��  )M��   )E (    )M(�W沈u�厴   崹   吀   澣   �ヘ      �叼   �    H峌繦崓�    H)E�@ )M�H0)E�@@)M�HP)E )M�    �@8駾@駾H 駾P(駾X0�XH駾(駾p駾xfZ纅EZ荔D$4�@@fZ纅EZ审D$<�@PfZ纅EZ襢EZ踗Z垠D$8驞D$`驞L$d驞T$h驞\$l�\$0fEZ韋EZ鰂EZ��@X@2�A婩`fZ纼�Hc伢D$@坮  �E�   �EH�E�E(�E�厐   �E �E@�EE �E痱E`�E栩E0�E囹厛   �E仳EX�E序E8�E闰厫   �E莉EPH呟uEW銭3蒐崊  E3跘隽t(纂A(訟嬃E峇冟兝H�@I嬅�實�  冟嫈崹  (�(�祈UH�@�剭�  �D$P�\餱n怏\l$T�Y蝮Y牦X痼剭�  �\囿Xl$T�Y�(阵AY畜X�(企AY朋X�(腆AY�(企AY企AY黧X�(腆Yd$<�YL$4�X�(蒹AY袤AY牦AP祗X伢X铙X袤X祗X\$8�Xl$@驛X痼Ah鬉雎t(纂A(訟嬄冟兝H�@�實�  I岰嫈崹  (�(閮�祈Ufn釮�@�剭�  A岯�D$P�\痼\l$T�Y蝮Y牦X痼剭�  �\囿Xl$T�Y�(�(蒹AY畜AY袤X囿AY�(企AY�(腆AY梭X�(企AY企AY黧X�(腆YL$4�X伢Yd$<�X铙XT$0�X袤X祗APX\$8�Xl$@驛XA(�t(纂A(訟峇聝�冟兝H�@�實�  H�R�剠�  (耋�  �\痱D$P(�祈U�\l$T�Y蝮Y牦X痼剠�  �\囿Xl$TA岯�Y�(�(蒹AY畜AY袤X囿AY�(企AY�(腆AY梭X�(企AY企AY黧X�(腆YL$4�X伢Yd$<�X铙XT$0�X袤X祗AP�X\$8�Xl$@驛X驛h�t(纂A(訟峇�I兠嬄A兞冟冣兝H�@�實�  H�R�剠�  (耋�  �\痱D$P(�祈U�\l$T�Y蝮Y牦X痼剠�  �\囿Xl$T�Y�(�(阵AY袤X囿AY�(企AY牦AY�(腆AY梭X�(企AY企AY黧X�(腆YL$4�X伢Yd$<�X铙XT$0�X袤X祗AP�X\$8�Xl$@驛X�\$0驛hI兝0A凒傓��H媴  �   ��   �D$p�d$t�\$p塂$x�t$x�D$|驞]��|$|塃凥崊  �m��    �@�/脀(伢H�/蘷(狍/謜(�/鴚(鳧/賥D(�/陊(闔兝H+蝩敷    �\铙\鸈W殷D\蹺W繣W蒆峊$pI嬒�Y梵YDY�(朋X企祒  �X唧AX泱D_唧絧  �_躞DZ润DZ芋DZ捏_    �UPL嬂駻E (掾M`�\逫婲�X5    H嬘A婨H因EpA(肁抿E膀EXH�羊AY缐Ex駻Y因AY审YX序A fZ莉X洋}阁Y抿X晙   �Y苀Z黍晥   �X闰AY序厫   駻Y麦M狉M 駻Y沈X序A@fZ莉X洋Y抿XU(�Y苀Z黍U0�X闰AY序E8駻Y麦MをM@�X序A@H岴p驞Y    L峂癓岴營嬜駻Y蒮Z莉X洋D\$(�Y肏塂$ �XUH�Y苀Z鼠X润Mㄨ    驞D$`劺驞L$dA(D^  @�E﨟+摅DT$h驞\$l�\$0���@肚H媿`  H3惕    L崪$  A(s餉({郋(C蠩(K繣(S癊([燛(c怑(k�E(硃���E(籤���I嬨A_A^A]_^[]胉   �   �   �     v   -  �   B  |   \  �   �  �   �  �   0  �   ]  �   �  }   �  �   �	  �   '
  �   {
  �      �   �  Z G            �
  n   p
  �         �donut::render::CascadedShadowMap::SetupForPlanarView 
 >?b   this  AJ        x  AV  x     N
 >kb   light  AK        �  AW  �     =
 >   viewFrustum  AL  �     � AP        �  AL l
    ^  >@    maxShadowDistance  A�         �  A�  �     %  >@    lightSpaceZUp  EO  (           Dp   >@    lightSpaceZDown  EO  0           Dx   >@    exponent  EO  8           D�   >�   preViewTranslation  AU  u     S
 EO  @           D�   >t    numberOfCascades  A   ~     �  A       
  EO  H           D�   >瞇    viewToWorld  D�    EO�         p
 EO(        p
 EO�0        p
 EO8        p
 EO@        p
 EO�H        p
 EO P        p
 EO�X        p
 EO`        p
 EO��        p
 EO�        p
 EO� �        p

 >@     near  A  A    i	H� A �    � >J    worldToView  E6uA0   �     E6u�4   v    W E6u�8   �    & E6u<   �    C E6u�@   �    � E6u� `   �     E6ud   �     E6uAh   �     E6u�l   �    
 >@     far  A�   ?
    -  A�  �    zr >0     viewModified  AE  �    �  AE �    I�   >辌    corners  D�  
 >u     i  A   �      >t     cascade  A   �    �  A  �    �( � >@     zDown  A�   �    '  >�   cascadeCenter  C�       �	    "  C�      
      >濲    cascadeViewBounds  Bp   �    M >�   halfShadowBoxSize  C�      5	    
 C     �    �  >辌   cascadeViewCorners  CH     �    E  D    >@     fadeRange  A  �	    x 
 >u     i  Ai  �    D >�   corner  C�      &    N. � I� I�  C�      "    . � )� )�  M        \!  �� N M        k  ��% N2 M        `!  俫4*:	
 >8[   a  AH  4    �  M        b!  倧
&!
 N  M        �!  俫
" N N M        ]!  乆O8 M        !  伣$ N M        �   伕 N M        �!  乆O M        1"  乆O M        M!  乆O N N N NL M        �   埿
F


	
	'	 N M        a!  坮F
T	 N M        �   圷)	 M        j  坕& M        �  坕& >@    _x  A�   �    )  N N M        m  圷) M        �  垔 >@    _x  A  �      >@    _z  A�   �    J  N N M        b  坅 M        �  垎 >@    _x  A�   i    !  >@    _y  A  r      >@    _z  A�   e    !  N N N% M        �   嚫JQ&	 M        x!  �6(
 N M        w!  �-
 N Nr M        	!  �&)/��).!4��).!4��#.4 NB M        k  
兲=��	03
��,0

��03	9 M        j  �
��

��
��
	9 M        �  �
��

��
��
	 >@    _x  A�       )U � !� !�  N N* M        q  凔������* M        �  凔������ >@    _x  A�   �      � � �  N N9 M        b  
兲
��	
��
��
 M        �  凎����	 >@    _x  A�   �     � � �  >@    _y  A�   �     � �  >@    _z  A�       
 � � �  N N N  M        �   
冎����	 N  M        �   
儾���	�	 N M        d!  壌i N M        q  塯@n M        �  塯@n >@    _x  A�   �	      N N M        q  塛Gi
 >@    b  A�   O	      M        �  塛Gi >@    _x  A�   �	    	  N N M        b!  �>Xb
 >蘘   v  AP  �     N M        q  埀 M        �  埀 N N M        b!  壂n N M        �   堹 N Z   �"  �   ^!  _!  �"  �"             8          A � h*   =  �  j  k  m  q  %  b  Q    8  k  �   �   �   �   �   �   �   �   �   �   �   �   	!  
!  !  !  M!  Y!  \!  ]!  `!  a!  b!  c!  d!  w!  x!  �!  �!  1"  
 :`  O  P  ?b  Othis  X  kb  Olight  `    OviewFrustum  h  @   OmaxShadowDistance  p  @   OlightSpaceZUp  x  @   OlightSpaceZDown  �  @   Oexponent  �  �  OpreViewTranslation  �  t   OnumberOfCascades  �   瞇  OviewToWorld  �  辌  Ocorners  p   濲  OcascadeViewBounds     辌  OcascadeViewCorners  O  �   P          �
     g   D      R  �x   W  ��   Z  ��   \  ��   `  ��   a  ��   i  �   k  �)  n  �1  q  �F  r  �c  q  ��  r  ��  q  ��  r  ��  s  ��  t  ��  v  ��  s  ��  v  ��  s  ��  v  ��  x  ��  |  ��  ~  �&    ��  ~  �!    �$  ~  �)    �.  ~  �2    ��  ~  �!    �$  ~  �)    �.  ~  �2    ��  ~  �#    �&  ~  �)    �.  ~  �7    ��  �  �Y  �  �z  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �	  �  �	  �  �	  �  �$	  �  �'	  �  �1	  �  �5	  �  �>	  �  �B	  �  �F	  �  �O	  �  �W	  �  �[	  �  �g	  �  �k	  �  �}	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  ��	  �  � 
  �  �
  �  �
  �  �
  �  �
  �  �
  �  �
  �  �!
  �  �;
  �  �?
  �  �l
  �  �,   �   0   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �   	  �  
 1  �   5  �  
 A  �   E  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 N  �   R  �  
 ^  �   b  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
   �     �  
 $  �   (  �  
 8  �   <  �  
 L  �   P  �  
 `  �   d  �  
 t  �   x  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 !  �   %  �  
 5  �   9  �  
 I  �   M  �  
 ]  �   a  �  
 q  �   u  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 (  �   ,  �  
 J  �   N  �  
 Z  �   ^  �  
 ~  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 .  �   2  �  
 _  �   c  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 }  �   �  �  
 r	  �   v	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 ;
  �   ?
  �  
 X
  �   \
  �  
 u
  �   y
  �  
 ;  �   ?  �  
 �  �   �  �  
 P
  �   T
  �  
 y
  �   }
  �  
 �
  �   �
  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 %  �   )  �  
   �     �  
 H塡$ UVWATAUAVAWH崿$0���H侅�  )�$�  H�    H3腍塃 H婣L嬧H+AM嬹嫊X  I嬸L嫮P  L孂H柳呉x;�L袐麦�0  W�/鐗A`v@�
    驛 驛PW馏AXW洋A@W袤AP驛X驛\`驛`3跦峿纅怐嬅H峌怘嬑�    �肏�� 婡�G魤G鼉�r左
    @2�A婫`(馏^匟  冭Hc伢D$0埥  �m鹁   �U荔]捏\牦e�)�$�  �5    D)�$�  D)�$�  D)�$�  D)�$p  驞]鬌)�$`  驞\垠De鳧)�$P  驞\潴Dm驞\m銬)�$@  驞u驞\u鐳)�$0  驞}驞\}祗l$X驞\$\驞d$`驞l$d驞|$l驞t$h H呟u�|$0(莊怐(蠨(菵(荔DY�(EY泽DX鼠DY狍Y鵄(矬Y轶DX泽X}荔DXe润Xm霢(阵EY皿Y蠥(珞DY轶DX皿Y郋(XU潴DXm潴Xe霢(摅DY袤Y伢DX鼳(企T$@驞X]捏X]梵Y罝(�(鼠Dl$D驟X蝮DYXE梵AX审\$8驞X矬d$<驞Y�(芋AX畜l$L�Y误Dt$4D(痼D\|$4�Y煮EX篌D$H驞Y铙T$T驞Y鲶D\轶L$PA(象D\蝮AY螦(朋AY臕(煮AY煮X�W荔X�.聎	W鲶Q螂(妈    �\$8(痼d$<驞\L$@驞\�W荔D\泽EY审EY荔EY殷EX馏EX翧.纖驟Q离
A(黎    D(荔\|$D驞\\$H驞\d$L驞
    W荔EY馏Y�驟Y垠EY潴AXAX�.莣	W垠Q唠(氰    (�(企AY袤X企EY�(误Y垠Y�W殷D^铙D^鲶D^X梭A\润^�W荔_洋]煮\蝮DY牦DY蝮DXl$P�Y鲶DXt$T驞YX篌DX|$4.苭EW荔DQ齐(畦    D(荔礍  H峌狊�8  驛_餓嬏驛_    駻E L峀$p驛VL岴�驛N(鍵婳�\珞A^�X5    H嬘�E怘殷A驛Y臠�袸嬙A婱驛Y謮M業嬍驛Y象X畜AY掾 fZ荔X洋DD$p驛N�Y捏AY象AXV$�Y企DD$t驞Y    �X畜YAF驛Y朋U�驛V�X伢AY烛@fZ荔X袤DD$(驛N �Y捏AY象AX^(�Y企|$x�X伢AF驛Y朋X畜]勻@H岴恌Z繦塂$ �X洋Y捏AXV,�Y企X畜U堣    �D$0劺@�E﨟+摅l$X驞\$\(润^匟  驞d$`W�驞l$d驞t$h驞|$l�U荔]捏e润D$0塶��D(�$0  D(�$@  D(�$P  D(�$`  D(�$p  D(�$�  D(�$�  D(�$�  (�$�  @肚H婱 H3惕    H嫓$(  (�$�  H伳�  A_A^A]A\_^]�*   �   |   �   �   v   �   �   >  �   e  �   �  �   �  �     �   �  �   �  }     �   v  �     �   �  �      �   ]  ` G              5   �  �         �donut::render::CascadedShadowMap::SetupForPlanarViewStable 
 >?b   this  AJ        V  AW  V     � >kb   light  AK        <  AT  <     � >   projectionFrustum  AL  L     �  AP        L  AL �    1  >J   inverseViewMatrix  AQ        C  AV  C     � >@    maxShadowDistance  EO  (           D0   >@    lightSpaceZUp  EO  0           D8   >@    lightSpaceZDown  EO  8           D@   >@    exponent  EO  @           DH   >�   preViewTranslation  AU  S     � EO  H           DP   >t    numberOfCascades  A   I     w  A  �       EO  P           DX  
 >@     near  A�       �� >@     far  A�   @    �  A�  �    � ) >0     viewModified  AE  �     �  AE �    :�  >辌    corners  D�   
 >u     i  A   �     O  >t     cascade  A       � " A  �    �y � " � � >@     zDown  A�   �    5  >@     farDiagonalHalfLength  A�         >�   sphereCenter  C      �    � >@     sphereRadius  A�   �      A�   *      >�   nearDiagonalCenter  C     �      C     �    C  >�   farDiagonalCenter  C      �    D  C     "    ! >@     nearCenterToSphereCenter  A�   P    S  A�  �    &  >�   cascadeCenter  C�       f      C�       >    � ( r  C�      �    } ) J  C�      �      C�            >辌    cascadeCorners  E6u�8   �     E6u�<   �     E6uA@   �     B�   v     � >�   halfShadowBoxSize  C�      �    E  C�      �    �  >@     nearDiagonalHalfLength  A�   �      A  �    B  >@     nearCenterToFarCenter  A�   i      M        \!  ^ N M        k  x% NN M        	!  �
儴
 
'
& N M        f!  刾
 >@    a  A�   _      N M        j  則 M        �  則 >@    _x  A  {    � N N M        q  刣 M        �  刣 >@    _x  A  i      N N M        g!  �4+ M        m  
�9& M        �  
�9& >@    _x  A  >    +  N N M        e!  �4 M        w   �4 N N N M        �  凷 M        �  刉 N M        �  凷 N N M        f!  �! N M        f!  �) N M        f!  �& N M        e!  冧(	 M        w   凕 >@    _Xx  A�   �    �  N M        �!  冧	 M        h  冧	 N N N M        b  兣# M        �  冭 >@    _x  A  �    !  >@    _y  A  �      N N M        e!  儗)
 M        w   儱 >@    _Xx  A  �        N M        �!  儗
 M        h  儗
 N N N M        e!  �(	  M        w   僑 >@    _Xx  A�   S      A�  x    � C  �   N M        �!  �(	 M        h  �(	 N N N M        b  傳5	 M        �  傳5	 >@    _x  A  1    
 N N M        q  偟
c M        �  偟
c >@    _x  A        N N M        j  俢!o M        �  �
 >@    _x  A        >@    _y  A   �    1  N N M        q  偸 M        �  偸 >@    _x  A�       A  N N! M        j  偄: M        �  傝: >@    _x  A�   �    (  >@    _y  A�   �       >@    _z  A  �    %  N N* M        k  c� g" 
%- M        j  �/
!!'
%- M        �  �/
!!'
% >@    _x  A�   �    {  A�   �    !  A  _    :  A  �      N N6 M        q  � 
	

6 M        �  � 
	

 >@    _x  A�   �      A�   �      A  M      A  �      N N! M        b  �!	! M        �  �!	 >@    _x  A�   -    m  A  �    V  A �    j  >@    _y  A  z    5  A  �    A  A �    �  >@    _z  A  �    6  A   �    ,  A  �    w  N N N M        d!  呍< N M        b  儀
 M        �  儜 >@    _x  A        >@    _y  A  �      >@    _z  A  �      N N M        q  厇PB M        �  厇PB >@    _x  A�   �      N N M        q  協UG
 >@    b  A�   [      M        �  協UG >@    _x  A�   �      N N M        b!  匓dO
 >蘘   v  AH  �    ( N M          劎 N M        �   � N Z   �"  �"  �"   �          8          A ~ h   �  h  j  k  m  q  %  b    �  �  �  k  w   {   |   �   �   �   �   	!  Y!  \!  b!  c!  d!  e!  f!  g!  �!  
 :   O    ?b  Othis    kb  Olight       OprojectionFrustum  (  J  OinverseViewMatrix  0  @   OmaxShadowDistance  8  @   OlightSpaceZUp  @  @   OlightSpaceZDown  H  @   Oexponent  P  �  OpreViewTranslation  X  t   OnumberOfCascades  �   辌  Ocorners  O   �   �               n   |      �  �5   �  �^   �  �e   �  �x   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �2  �  �:  �  �B  �  ��  �  ��  �  �   �  �c  �  �g  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �
  �  �  �  �  �  �"  �  �(  �  �x  �  ��  �  ��  �  ��  �  �  �  �4  �  �C  �  �S  �  �[  �  �_  �  �p  �  �t  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �0  �  �B  �  �F  �  �J  �  �Q  �  �W  �  �[  �  �f  �  �j  �  �z  �  �~  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �  �  �  �  �-  �  �=  �  �@  �  ��  �  ��  �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
   �     �  
 =  �   A  �  
 M  �   Q  �  
 }  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 K  �   O  �  
 _  �   c  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
   �     �  
 :  �   >  �  
 J  �   N  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 E  �   I  �  
 l  �   p  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   	  �  
 8  �   <  �  
 H  �   L  �  
 t  �   x  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
 !  �   %  �  
 1  �   5  �  
 a  �   e  �  
 u  �   y  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ?  �   C  �  
 �  �   �  �  
 �	  �   �	  �  
 M
  �   Q
  �  
 j
  �   n
  �  
 �
  �   �
  �  
 X  �   \  �  
 h  �   l  �  
 
  �     �  
 `  �   d  �  
 �  �   �  �  
 �  �   �  �  
 ;
  �   ?
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H  �   L  �  
 X  �   \  �  
 h  �   l  �  
 x  �   |  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
    �   $  �  
 0  �   4  �  
 @  �   D  �  
 ]  �   a  �  
 m  �   q  �  
 }  �   �  �  
 �  �   �  �  
   �     �  
 )  �   -  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 5  �   9  �  
 t  �   x  �  
 H婣(W跡嬂M繨�繫嬃�       �      �   �  \ G                      �         �donut::render::CascadedShadowMap::SetupPerObjectShadow 
 >?b   this  AJ          >kb   light  AK          >u    object  Ah        
  >�   objectBounds  AQ          M        �   
 N
 Z   �"                          @  h   �   Y!      ?b  Othis     kb  Olight     u   Oobject      �  OobjectBounds  O �   (                            �     �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$H塴$H塼$WH冹@H嬮H媦H媞H;aH婫H吚t�@H�H塋$ H媉H塡$(�    怘呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;烪媫(H媢0H;aH婫H吚t�@H�H塋$0H媉H塡$8�    怘呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;烪媆$PH媗$XH媡$`H兡@_肅   �   �   �      �   #  W G                 �   �         �donut::render::CascadedShadowMap::SetupProxyViews 
 >?b   this  AJ          AN       �  >昩    <begin>$L0  AM       n  >昩    <end>$L0  AL       n  >2c   cascade  CJ      4       D     >昩    <end>$L1  AL  �     u  >昩    <begin>$L1  AM  �     ~  >2c   object  CJ      �       D0    M        �   $ M        h!  $M	 M        �!  $	 M        >  - N N N N M        �   1K M        !  K/ M        �  M/ M        �  d	 N N N N M        �   �� M        h!  ��M	 M        �!  ��	 M        >  �� N N N N M        �   1�� M        !  ��/ M        �  ��/ M        �  ��	 N N N N Z   �"  �"   @                    @ 6 h   �  �  >  �   �   �   �   !  !  Y!  h!  �!   P   ?b  Othis      2c  Ocascade  0   2c  Oobject  9b       b&   9y       b&   9�       b&   9�       b&   O �   h                
   \         �   ! �B   # �H   $ �|   ! ��   & ��   ( ��   ) ��   & ��   * ��   �   f F                                �`donut::render::CascadedShadowMap::SetupProxyViews'::`1'::dtor$0  >2c    cascade  EN              >2c    object  EN  0                                  �  O   �   �   f F                                �`donut::render::CascadedShadowMap::SetupProxyViews'::`1'::dtor$1  >2c    cascade  EN              >2c    object  EN  0                                  �  O   ,   �   0   �  
 |   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 %  �   )  �  
 J  �   N  �  
 o  �   s  �  
 �  �   �  �  
 �  �     �  
   �     �  
   �   #  �  
 8  �   <  �  
 �  �   �  �  
 .  �   2  �  
 S  �   W  �  
 �  �   �  �  
   �     �  
 3  �   7  �  
 H崐    �       �   H崐0   �       �   H吷tH��   H�`�   �   �   h G                      �!        �std::_Ref_count_obj2<donut::render::PlanarShadowMap>::_Delete_this 
 >梖   this  AJ                                 @�     梖  Othis  9
       沠   O  �   0              �     $       C �    D �   E �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H婣H兞3襀�    �     d G            
       
   �!        �std::_Ref_count_obj2<donut::render::PlanarShadowMap>::_Destroy 
 >梖   this  AJ          M        "   
 >僣   _Obj  AJ         N                        @� 
 h   "      梖  Othis  9
       嘽   O   �   (           
   �            ? �    @ �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 
  �     �  
 (  �   ,  �  
 3烂   �   �   H G                      �        �std::_Ref_count_base::_Get_deleter 
 >l&   this  AJ          D    >�%   __formal  AK          D                           @�     l&  Othis     �%  O__formal  O�   0              �     $       � �    � �   � �,   z   0   z  
 m   z   q   z  
 �   z   �   z  
 �   z      z  
 H冹HH峀$ �    H�    H峀$ �    �
   m            �      �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               h            J �   K �,   s   0   s  
 �   �   �   �  
 �   s   �   s  
 H冹(H�
    �    �   G      t      �   �   � G                     *"        坰td::vector<std::shared_ptr<donut::render::PlanarShadowMap>,std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> > >::_Xlength 
 Z   j   (                      @        $LN3  O  �   (              �            a �   b �,   �   0   �  
 �   �   �   �  
   �     �  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   ^   <   `      �   z  r G            A      A   9!        �std::allocator<std::shared_ptr<donut::render::PlanarShadowMap> >::deallocate 
 >1d   this  AJ          AJ ,       D0   
 >鱞   _Ptr  AK        @ /   >#   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        �  
#

 Z   q   >#    _Ptr_container  AJ       (    AJ ,       >#    _Back_shift  AH         AH ,       N N (                      H  h   �  �         $LN18  0   1d  Othis  8   鱞  O_Ptr  @   #  O_Count  O  �   8           A        ,       � �   � �2   � �6   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 4  �   8  �  
 U  �   Y  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 :  �   >  �  
 �  �   �  �  
 H婹H�    H呉HE旅         �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              H     $       ^  �    _  �   `  �,   e   0   e  
 _   e   c   e  
 �   e   �   e  
  d T 4 2p    H           �      �          20    2           �      �      #   
 
4 
2p    B           �      �      )    20    <           �      �      /   
 
4 
2p    B           �      �      5    20    <           �      �      ;   
 
4 
2p    B           �      �      A    �                  �      �      G    20    `           �      �      M    B             �      Y       "           �      �      S   h           \      _          _   2 20    6           �      �      b   ! d t     6          �      �      b   6   �           �      �      h   !   t     6          �      �      b   �   �           �      �      n   #Tf 20    !           �      �      w    20    !           �      �      }   
 
d 
2p               �      �      �   ! T               �      �      �      '           �      �      �   ! 4    '          �      �      �   '   v           �      �      �   !      '          �      �      �   v   �           �      �      �   !                 �      �      �   �   �           �      �      �   >U
 
4 
2p    8           �      �      �    B      A           �      �      �    20    j           �      �      �    B                 �      �      �   '
 ) 
��	��p`0P        2     �      �       �          �      �      �   (           �      �       .    .    .    *    >    f    �    �2    ��    5    �   �      �      �      �      x      _   "   �   '   �   -   �   4   �   :   �   �
L4
��
�,`�
�@�
A  2P                �      �      �    2P                �      �      �   :����n ]�7 U�8 M�9 E�: =�; 5�< -�= (�> #x? h@ � 
��	�p`0P      `  @   �       �
          �      �      �   5
 'x 4E : ���
�p`P             �       2          �      �      �   !� 嗻 q� W� C� 4� +� "� � h     2      (   �   ,   �   0   �   2  �          �      �      �   !       2         �      �      �   �            �      �      �   B 1�1 )�7 $x8  h9 4} t ��
p	`P      �  (   �       �          �      �      �   !a a�0 O�2 =�3 .�4  �5 �6 膢     �          �   $   �   (   �   �  u          �      �      �   !       �         �      �      �   u  �          �      �      �    d T 4
 rp           �      
                                  (           
         
    @:    `   �      �   ��-Ri浝�
 
4 
�p    q                           d
 T	 4 Rp           �      "       �                          (           %      (   
    @   �   �-Vm h d 4
 rp           �      4       �                       .   (           7      :   
    @   �   �-Ul R0    =                       @    R0    J                       F    20    &                       L    20                           R    20                           X   
 
4 
2p           �      d       d           	      	      ^   h           g      j          _   ~ 20               
      
      m   ! t               
      
      m      E           
      
      s   !                 
      
      m   E   K           
      
      y   - 20                           �   ! t                           �      E                       �   !                             �   E   K                       �   - �      o                       �    h 4 �p    �          
      
      �   A A� ;� 6�	 1�
 ,� $� x
 h 4   p      ]                      �   0 0� x h �0    �                       �    d 2p                           �   ! 4                           �      p                       �   !                             �   p   q                       �   .E 20    +                       �    B      @                       �   ' 'h  "      �                       �   u u�
 Xx Eh ! ���
�p
`	0P    �                      �    R����
p`0           �      �       �                      �   8               �      �   	   �            �   �       �   � �]  BP0      =           �      �      �     20    d                       �    B      :                                                      M      g      e   Unknown exception                             Y      k      e                               e      q      e   bad array new length                                n                                       !      '      -                   .?AVbad_array_new_length@std@@     .               ����                            o                   .?AVbad_alloc@std@@     .              ����                      $      i                   .?AVexception@std@@     .               ����                      *      c   string too long     ����    ����        ��������                                                                                                                    }      �      \      \       \   (   \   0   \   8   \   @   \   H   \   P   \   X   \   `   \   h   \                                       �      \      \      �                                       �      �      �      �                                                                                                                       �      �      �      �       �   (   �   0   �   8   �   @   �   H   �   P   �   X   �   `   �   h   �   ShadowMap vector too long                                             �      �      �      �       z                                         *      P      M                         S                   V               ����    @                   *      P                                         $      \      Y                         _                           b      V              ����    @                   $      \                                               h      e                         k                                   n      b      V              ����    @                         h                   .?AV_Ref_count_base@std@@     .                         w                   z               ����    @                   q      t                                         �      �      }                   .?AVIShadowMap@engine@donut@@     .                         �                   �               ����    @                   �      �                                         �      �      �                   .?AVICompositeView@engine@donut@@     .                         �                   �               ����    @                   �      �                                         �      �      �                   .?AVCompositeView@engine@donut@@     .                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AVCascadedShadowMap@render@donut@@     .                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@     .                         �                           �      z              ����    @                   �      �   吞�=   狑瓢>   ?  �?      �?      鹂      �?          �?  �?  �?  �?  ��  ��  ��  �?��������������       �       �   �   �   �   �   �   N   L 
{7        std::_Ref_count_obj2<donut::render::PlanarShadowMap>::`vftable'      J      J  
    �   3   1 
 j        donut::engine::IShadowMap::`vftable'     8      8  
    �   7   5 
tQ        donut::engine::ICompositeView::`vftable'     ;      ;  
    �   6   4 
tQ        donut::engine::CompositeView::`vftable'      >      >  
    �   :   8 
 j        donut::render::CascadedShadowMap::`vftable'      A      A  
    �   (   & 
7        std::exception::`vftable'    	      	  
    �   (   & 
7        std::bad_alloc::`vftable'            
    �   3   1 
7        std::bad_array_new_length::`vftable'             
 噾姏@|#��}Ic�=*�(.瞗輩耾"0�
絺撴o�漲G�qㄙT��y弌氉'��葙(%柫�85z耪O耦珗V娏罈癟観I绽4该~(�*[9H�#�%9Q;肗^|坨v+Lh�K蜌�([柏q�廁韪慀費Z┚�%�;jQrkZ顧^�0揅�)?ê�!w筀:
茐甴{苳曹[昖N>|�0�;��聯蒉e� Ps�5g$睤%埧^�
�f樯湱謾掏遈汦鋈B
泅n黢単鬭-[彯諔嫼J*�n6rS3S钁O鋂S砹锂撙肆峖=f瓵菢誘O�6⒇40�渟宙`莙�缁鵂蘮B井O芔}?#�O鰛�祓YR�/E�
:�;�(� �3�9`�0y艊�&茤3�:go>8GUg跌钑鍑臂ep禭诵鰐麌臂ep禭�6IB��6萪O��夲栮O塊霵婬(�1粦�^>剁'項j赒鑓'f劫Fk{�R~蜔�/�$愜w獛啯L漷O勒肂$愜w獛啯[*(獥集馾D預!Z颟�;<�+軀0C�7
贸*�怵�Z懋<啊�H��姸遂祚皛tb=�*鰞�牰遂祚皛t撨�鄩C�:Q蚴0k涽23侷k壎遂祚皛t孬艡QDs嶀預棊膬$R驯�-n嶀預棊膬燆失q葐篚奪漕-6�崰q.恾k閲,�輲塛%恀2暨���饫�*伋曲�*u鈬E饫�*伋趮�o,h:箩邆5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k�.�D�3瑓x谙�<�柛�/怛端祆癜~t媤d簉璣N�=BwLH靬�)`厽迨h驼*f�6銛�栄可
>々延桞e镘g劏Z齐u{纳�5╓戽 啄=5��xf�9p���j舠嵣栙o
4-g_A&�Om夹?<;&-岺窋d 	`K馶F陫婉f詯愨5斮謬B憡�貊0宱憓[伭牪C鯈�1)殱h}忠3F喔�2cq辵�.|H�謊~�(锿簳Ac�搗 t諁�"-浗V鑳蚯嫝$+��$刞訧)#hv瓯蔴圴鞰� 濏�1�:熢I)#hv瓯爜eow縿A漻u帩�;玣輌
�
嘦:氘忝Wa
Q隉�6W$�}蟻�臮'L豉�4艂0>�*(~揅峋X=矠fJ楌f吰防5籀嵲I)#hv瓯�昸鳐3桴�7颦硣KH唌貱I
�瘊衚癙%�飈�K�$蟊惺鬮Щ"4咿%I栶賑?T`緗顔弎f]{謑p�k脺凛鮧�E�鬂閣nN鵘J鈦罈X �F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o覶4�"
�")职F瘔�銖騹侍VHD緔HЪ蔏0mV$Q翜赿e%汶嘕-WV8oc8曀黩6雵J-WV8oc8曀黩6Z�9.楞ч鷃兟+d+眔Aom�昈d婸潤昬*楏W�1嚲\僎&|篇4胎�1� {楚�旲呚�!斈wvu顈騼7RN砛9E\$L釉抻[目湸-坓�(鬄鮳�>i,夿雵J-WV8o譑iv褍| -坓�(鬄�汬'这�.��J:7Qdd�a�:MA6R擛�!溥_唠RS樢閣yQ5R犵�喪樢閣yQ5R犵�喪\s锲螲胢G堈�.聋$ydo簤佩項�剧�ＢsO頖π虔樸榽晎牆c幓,z�
#3"哼玨癦�瀄4ubB`虋Qh麡燛<埽>ey喭蚶�����$惷
貪�L埰M嘡勺DvDK飁黯�+z~dd�a�:啽冺��扰7�(姂瘡]fm総礵曬樿隁�= ��H壢�9�,\�*咏A�dd�a�:画k湻J處怯!偵疙疜\Y7^2Z罃ZО�k^織F眷麔dd�a�:画k湻J處�2薢AB焄?4冽餯繅鬮�1�8]Z餯繅鬮硋傘]-屾雵J-WV8o}�!罱4=雵J-WV8o�%-<$濍嘕-WV8o�%-<$澩﨏帲晗D�&9�=NB咞taR�,F_棢杻#Q�"鰖Y坒雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛尋?郂鞿x�L扟�9坥綷�+炝
y/)Vk6鬐R
歃础Y詼p綾s�487銢TD椣?M钚^再�(i9x�奝k6G磌缘:逻戇�9桸瘟妬
啶鳺菈ouK�=雵J-WV8o额	hQ�)-坓�(鬄�+$�"钞d�2倃齼_*窶圹涩�6┥9�3瑞翌炪丹]B獗G黋p脢�&!y*�杜`颀l+�鞯.r擣�0G#盱谑G
.'穤s;嗐8�1�8]Z��梾�n4�硓橂嘕-WV8o�&9�=NB-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H谍玿財~琭hzb絯fhzb絯谍玿財~�謳�%G>禡h�,4��;儗�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2�Vs�,蒜t"�:邍A愦靮鸬2�>料C5��\&2淟Z�7轊
k"�:邍A愦靮鸬2�>料C5��\&2溫狈冸C[	%ZZ�$为赞G刹~赣 "^惋砤��\&2湭�
蠾葉%ZZ�$为赞G刹~赣 "^惋砤��\&2湵j?m%ZZ�$为赞G刹~赣 "^惋砤塞6rA�!t鎫祻R贋XCRC冼�^笵A傮l<9n值胢鐇禝�c29癨8�%膂�斱/x�鲶偩躛坢磩^Q噴_�={檎�sG﹋SoB6纫遬e@魉+
�/N!舋婅界pO�1"眆C籞p敓鸓越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       4B              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       �       钽R     .debug$S       �             .text$mn       �      q�      .debug$S       �             .text$mn    	   �       .D�     .debug$S    
   0         	    .text$mn       .       ^�     .debug$S       �              .text$mn    
   .       ��     .debug$S       �          
    .text$mn               礻烿     .debug$S       �              .text$mn       :      眡�     .debug$S                    .text$mn       q       
z鈕     .debug$S       �             .text$mn       �  	   
墟     .debug$S       d
  T           .text$x        =      M錌�    .text$mn       d      "�
V     .debug$S       p             .text$mn       @       {阿�     .debug$S       4             .text$mn       ]     iV鶻     .debug$S       �             .text$mn       �     
!     .debug$S       �	  *           .text$mn        :      s懅n     .debug$S    !   L              .text$mn    "   #       v^
�     .debug$S    #   �          "    .text$mn    $   �      媾     .debug$S    %   8         $    .text$mn    &   o       WK�?     .debug$S    '   �         &    .text$mn    (          恶Lc     .debug$S    )   �          (    .text$mn    *   �     賓%&     .debug$S    +   @#        *    .text$x     ,         �4�*    .text$x     -         h櫛|*    .text$x     .         �様*    .text$x     /         煈?p*    .text$x     0         竼>�*    .text$x     1         鲉k�*    .text$x     2         *    .text$x     3         喣�,*    .text$x     4          樹�*    .text$x     5          jǚ*    .text$mn    6   <      .ズ     .debug$S    7   0  
       6    .text$mn    8   <      .ズ     .debug$S    9   L  
       8    .text$mn    :   !      :著�     .debug$S    ;   <         :    .text$mn    <   2      X于     .debug$S    =   <         <    .text$mn    >   "       坼	     .debug$S    ?   �         >    .text$mn    @         峦諡     .debug$S    A   ,         @    .text$mn    B   K       }'     .debug$S    C   �         B    .text$mn    D   K       }'     .debug$S    E   �         D    .text$mn    F   �      u`     .debug$S    G   �  "       F    .text$mn    H   j      ︳4}     .debug$S    I   �         H    .text$mn    J   	      趷     .debug$S    K   �          J    .text$mn    L          .B+�     .debug$S    M   �          L    .text$mn    N          .B+�     .debug$S    O   �          N    .text$mn    P   `      ,     .debug$S    Q   �         P    .text$mn    R          .B+�     .debug$S    S   �          R    .text$mn    T         ��#     .debug$S    U   �          T    .text$mn    V         ��#     .debug$S    W   �          V    .text$mn    X   +      *蘨�     .debug$S    Y   �          X    .text$mn    Z   d      �2�8     .debug$S    [   t         Z    .text$mn    \   8      z飨�     .debug$S    ]   �          \    .text$mn    ^   !       ��     .debug$S    _   �          ^    .text$mn    `   !       ��     .debug$S    a   �          `    .text$mn    b   B      贘S     .debug$S    c             b    .text$mn    d   B      贘S     .debug$S    e            d    .text$mn    f   B      贘S     .debug$S    g   �          f    .text$mn    h   H       襶.      .debug$S    i   �         h    .text$mn    j   q      仹�     .debug$S    k   �         j    .text$mn    l          .B+�     .debug$S    m   <         l    .text$mn    n          �3ん     .debug$S    o   h         n    .text$mn    p   =       �     .debug$S    q   �         p    .text$mn    r          a啓�     .debug$S    s   @         r    .text$mn    t   �       鐤;a     .debug$S    u   P         t    .text$mn    v          `.�,     .debug$S    w   �          v    .text$mn    x   
       ;�:     .debug$S    y   $         x    .text$mn    z   !       �     .debug$S    {   �         z    .text$mn    |   J      Yfv�     .debug$S    }            |    .text$mn    ~          G�7�     .debug$S       �          ~    .text$mn    �   &       柴     .debug$S    �   X  
       �    .text$mn    �          }G嗥     .debug$S    �   8         �    .text$mn    �          朏     .debug$S    �   �          �    .text$mn    �   9       怀k�     .debug$S    �   t         �    .text$mn    �          繋}T     .debug$S    �   $         �    .text$mn    �   �      堐_�     .debug$S    �   �         �    .text$x     �         S軍    .text$mn    �   �      �8匭     .debug$S    �            �    .text$x     �         S軑    .text$mn    �   (       �4     .debug$S    �   �         �    .text$mn    �   �  
   02$�     .debug$S    �   �  �       �    .text$mn    �   �
     鼬镫     .debug$S    �   T  �       �    .text$mn    �        X�>�     .debug$S    �   �  �       �    .text$mn    �         /�=v     .debug$S    �   �         �    .text$mn    �        +.�0     .debug$S    �   `  (       �    .text$x     �         S軞    .text$x     �         "E萷�    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �   
       肷瞰     .debug$S    �   P  
       �    .text$mn    �          �猴     .debug$S    �   ,         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   ,         �    .text$mn    �   A      俙Z%     .debug$S    �   �         �    .text$mn    �         崪覩     .debug$S    �   �          �        \       h        x                �                �                �                �                �                �                      <        "      V        <      �        \      f        {          if                   �      6        �      b        �          ij                   �      :              T        C      8        o      d        �          ip                   �      �        �               
      (        /               j               �      P        �      R        �      �              >        N      t        �               �      N        �      `                  i                   H      L        p      ^        �          i�                   �      F        F               �               �               7      J        ^      \        �          i�                   �      �        D      H        �      �        i	      *        �	      �        9
      �        �
      �        J      �        �      �        �      j        >      �        {      �        �      �        �      p        d
      |        �
      �        &      �        r      ~        �      v        �      n        B      x        �      z        �      �        *      �        r      r        �      �              l        T      Z        �          i�                   �      D        �               G               �               H               ~               �               �               X      B        �      &        �                            T              �               �      $                      �      @        ,      �        u      �        �      X        	          i�                   P              �              �      	        +              k      "        �              �              �      
        7              Z              �              �      ,        7      �        �      �        �      �              4        u      5        �      -        0       �        x       .        �       /        2!      0        �!      1        �!      2        I"      3        �"               �"               �"               �"               �"           memcpy           memmove          sqrtf            $LN13       h    $LN5        <    $LN10       f    $LN7        6    $LN13       b    $LN10       8    $LN16       d    $LN3        �    $LN4        �    $LN37   `   P    $LN40       P    $LN10       >    $LN52       t    $LN8        `    $LN8        ^    $LN57   �   F    $LN60       F    $LN11       \    $LN18   A   �    $LN21       �    $LN28   j   H    $LN31       H    $LN3       �    $LN4        �    $LN336  �  *    $LN340      *    $LN219      �    $LN244      �    $LN220      �    $LN65       �    $LN4        j    $LN35       �    $LN35       �    $LN16       p    $LN18       |    $LN6        �    $LN7        �    $LN7        r    $LN19       Z    $LN18       D    $LN18       B    $LN14       &    $LN55           $LN12           $LN20       $    $LN33           $LN8        X    $LN8            $LN4        	    $LN152          $LN84   �          
#         $LN89           $LN30           $LN14   :       $LN17           .xdata      �          F┑@h        /$      �    .pdata      �         X賦鷋        S$      �    .xdata      �          （亵<        v$      �    .pdata      �          T枨<        �$      �    .xdata      �          %蚘%f        �$      �    .pdata      �         惻竗f        �$      �    .xdata      �          （亵6        %      �    .pdata      �         2Fb�6        =%      �    .xdata      �          %蚘%b        e%      �    .pdata      �         惻竗b        �%      �    .xdata      �          （亵8        �%      �    .pdata      �         2Fb�8        �%      �    .xdata      �          %蚘%d        &      �    .pdata      �         惻竗d        K&      �    .xdata      �          懐j灓        |&      �    .pdata      �         Vbv        �&      �    .xdata      �          （亵P        �&      �    .pdata      �         粻胄P        '      �    .xdata      �         /
�>        &'      �    .pdata      �         +eS�>        `'      �    .xdata      �   	      �#荤>        �'      �    .xdata      �         j>        �'      �    .xdata      �          3狷 >        (      �    .xdata      �          （亵t        S(      �    .pdata      �         鶽t        �(      �    .xdata      �         雹�	t        �(      �    .pdata      �         q��t        4)      �    .xdata      �         ;O 衪        �)      �    .pdata      �         X妽踭        �)      �    .voltbl     �          �;簍    _volmd      �    .xdata      �          （亵`        *      �    .pdata      �         萣�5`        I*      �    .xdata      �          （亵^        y*      �    .pdata      �         萣�5^        �*      �    .xdata      �          釫�<F        �*      �    .pdata      �          *鬰F        f+      �    .xdata      �         驫@<F        �+      �    .pdata      �         "�bF        n,      �    .xdata      �         Dc団F        �,      �    .pdata      �         `獤F        x-      �    .xdata      �         蜼U跢        �-      �    .pdata      �         槜姚F        �.      �    .xdata      �         炖ＺF        /      �    .pdata      �         eNGaF        �/      �    .voltbl     �          灸ZF    _volmd      �    .xdata      �          %蚘%\        0      �    .pdata      �         菻(V\        E0      �    .xdata      �          �9��        x0      �    .pdata      �         s�7濞        1      �    .xdata      �          （亵H        �1      �    .pdata      �         s�+AH        ;2      �    .xdata      �          �9��        �2      �    .pdata      �         �1唉        o3      �    .xdata      �   $      i姖*        4      �    .pdata      �         *冂�*        a4      �    .xdata      �   	      � )9*        �4      �    .xdata      �   ?      �c*        5      �    .xdata      �          蝵蝣*        l5      �    .xdata      �          k�*        �5      �    .pdata      �         Vbv�*        *6      �    .xdata      �          k�*        �6      �    .pdata      �         Vbv�*        �6      �    .voltbl     �          v髑�*    _volmd      �    .xdata      �   H      �6[茢        Z7      �    .pdata      �         �
覧�        �7      �    .xdata      �   (      g�嵬�        m8      �    .pdata      �         確焌�        9      �    .xdata      �   4      遥M钖        �9      �    .pdata      �          3t�        S:      �    .xdata      �         X等貣        �:      �    .pdata      �         赒u諙        �;      �    .xdata      �   0      ^巧�        <<      �    .pdata      �         赉}j�        �<      �    .xdata      �   ,      镃        9=      �    .pdata      �         氐��        �=      �    .xdata      �         	*訏        9>      �    .pdata      �         w咽a�        �>      �    .xdata      �         V踫笟        9?      �    .pdata      �         抲�        z?      �    .xdata      �   	      � )9�        �?      �    .xdata      �   
      仓啔        �?      �    .xdata      �          �呔�        F@      �    .voltbl     �          怩臐�    _volmd      �    .xdata      �          #澈]j        堾      �    .pdata               扂`j        谸          .xdata              /·◢        &A         .pdata              忙
:�        kA         .xdata        	      � )9�        疉         .xdata              遱谸�        鯝         .xdata               b紌�        CB         .voltbl              �2w飴    _volmd         .xdata              .zF潑        夿         .pdata               磰        螧         .xdata      	  	      � )9�        C      	   .xdata      
        遱谸�        ZC      
   .xdata               兣�,�                 .voltbl              裃]硦    _volmd         .xdata      
         僣紁        頒      
   .pdata              現�p        ^D         .xdata               僣紎        虳         .pdata              %轢竱        ?E         .xdata               （亵�        癊         .pdata              裬?�        F         .xdata               （亵�        ]F         .pdata              �#洢�        璅         .xdata               （亵r        麱         .pdata              �#洢r        YG         .xdata              �酑Z        礕         .pdata              ATZ        鞧         .xdata        	      �#荤Z        $H         .xdata              jZ        ^H         .xdata               :�6\Z        濰         .xdata               （亵D        豀         .pdata              � 貲        I         .xdata              范^揇        WI         .pdata              鳶�D        業         .xdata               @鴚`D        買          .pdata      !        [7蹹        J      !   .voltbl     "         飾殪D    _volmd      "   .xdata      #         （亵B        [J      #   .pdata      $        � 貰        燡      $   .xdata      %        范^揃        銳      %   .pdata      &        鳶�B        *K      &   .xdata      '        @鴚`B        pK      '   .pdata      (        [7蹷        禟      (   .voltbl     )         飾殪B    _volmd      )   .xdata      *         眃街&        麷      *   .pdata      +        菜	&        NL      +   .xdata      ,         `$h�        烲      ,   .pdata      -        w噳�        週      -   .xdata      .  0       篷崉        M      .   .pdata      /        ｍTh        cM      /   .xdata      0         NTo�$              0   .pdata      1        暫`g$        頜      1   .xdata      2         n�v        4N      2   .pdata      3        V6�>         O      3   .xdata      4        0��        P      4   .pdata      5        筲sL        鳳      5   .xdata      6        很蓢        錛      6   .pdata      7        �癉        襌      7   .voltbl     8         3急
    _volmd      8   .xdata      9         （亵X        縎      9   .pdata      :         ~        T      :   .xdata      ;         �9�        \T      ;   .pdata      <        砺�)        睺      <   .xdata      =         ,t 	        U      =   .pdata      >        v��	        [U      >   .xdata      ?  $       「w        甎      ?   .pdata      @        <        鯱      @   .xdata      A        u苩�        =V      A   .pdata      B        �5�:        `W      B   .xdata      C  
      B>z]        俋      C   .xdata      D         �2g�              D   .xdata      E        T�8        襔      E   .xdata      F        r%�        鮗      F   .xdata      G  	       �5啒        ]      G   .xdata      H         M[�        A^      H   .pdata      I        現�        t_      I   .voltbl     J         熄�    _volmd      J   .voltbl     K                 _volmd      K   .xdata      L         （亵              L   .pdata      M        AT        補      M   .xdata      N         �9�        鸼      N   .pdata      O        礝
        Xc      O   .rdata      P                     碿     P   .rdata      Q         �;�         薱      Q   .rdata      R                     騝     R   .rdata      S                     	d     S   .rdata      T         �)         +d      T   .xdata$x    U                     Wd      U   .xdata$x    V        虼�)         yd      V   .data$r     W  /      嶼�         渄      W   .xdata$x    X  $      4��         羋      X   .data$r     Y  $      鎊=         e      Y   .xdata$x    Z  $      銸E�         0e      Z   .data$r     [  $      騏糡         oe      [   .xdata$x    \  $      4��         塭      \       萫           .rdata      ]         燺渾         踖      ]   .data       ^          烀�          f      ^       5f     ^   .rdata      _  p                   \f     _   .rdata      `                      }f     `   .rdata      a                           a   .rdata      b  p                   苀     b   .rdata      c  
       u$k         頵      c   .rdata      d         IM         
g      d   .rdata      e  (                   0g     e   .rdata$r    f  $      'e%�         og      f   .rdata$r    g        �          噂      g   .rdata$r    h                     漡      h   .rdata$r    i  $      Gv�:         砱      i   .rdata$r    j  $      'e%�         襣      j   .rdata$r    k        }%B         阦      k   .rdata$r    l                      h      l   .rdata$r    m  $      `         h      m   .rdata$r    n  $      'e%�         5h      n   .rdata$r    o        �弾         Xh      o   .rdata$r    p                     yh      p   .rdata$r    q  $      H衡�         歨      q   .data$rs    r  *      8V綊         膆      r   .rdata$r    s        �          鋒      s   .rdata$r    t                      i      t   .rdata$r    u  $      Gv�:         i      u   .rdata$r    v  $      'e%�         Ai      v   .data$rs    w  .      }	�4         ci      w   .rdata$r    x        �          噄      x   .rdata$r    y                           y   .rdata$r    z  $      Gv�:         莍      z   .rdata$r    {  $      'e%�         餴      {   .data$rs    |  2      逊l�         j      |   .rdata$r    }        �          >j      }   .rdata$r    ~                     bj      ~   .rdata$r      $      Gv�:         唈         .rdata$r    �  $      'e%�         砵      �   .data$rs    �  1      {审�         豭      �   .rdata$r    �        }%B         �j      �   .rdata$r    �                     "k      �   .rdata$r    �  $      `         Ek      �   .rdata$r    �  $      'e%�         qk      �   .data$rs    �  5      u脅�         歬      �   .rdata$r    �        }%B         舓      �   .rdata$r    �                     靕      �   .rdata$r    �  $      `         l      �   .rdata$r    �  $      'e%�         Cl      �   .data$rs    �  L      昩U         僱      �   .rdata$r    �        }%B         舕      �   .rdata$r    �                     m      �   .rdata$r    �  $      `         Am      �   .rdata      �         瑣�#         坢      �   .rdata      �         i��         榤      �   .rdata      �         =-f�         癿      �   .rdata      �         v靛�         續      �   .rdata      �         �腾�         衜      �   .rdata      �         燨bO         鑝      �        n           .rdata      �         殐尼         n      �   .rdata      �         _�         9n      �   .rdata      �         墒
s         `n      �   .rdata      �          錢         噉      �   .rdata      �         o冺�         畁      �   .rdata      �         �a�         課      �   _fltused         .debug$S    �  \          e   .debug$S    �  @          _   .debug$S    �  D          `   .debug$S    �  D          a   .debug$S    �  H          b   .debug$S    �  4          P   .debug$S    �  4          R   .debug$S    �  @          S   .chks64     �  
                黱  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ??0?$vector@M$02@math@donut@@QEAA@XZ ?getCorner@frustum@math@donut@@QEBA?AU?$vector@M$02@23@H@Z ?getFormatInfo@nvrhi@@YAAEBUFormatInfo@1@W4Format@1@@Z ??1TextureDesc@nvrhi@@QEAA@XZ ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ ?GetDirection@Light@engine@donut@@QEBA?AU?$vector@N$02@math@3@XZ ??1IShadowMap@engine@donut@@UEAA@XZ ??_GIShadowMap@engine@donut@@UEAAPEAXI@Z ??_EIShadowMap@engine@donut@@UEAAPEAXI@Z ??1ICompositeView@engine@donut@@UEAA@XZ ??_GICompositeView@engine@donut@@UEAAPEAXI@Z ??_EICompositeView@engine@donut@@UEAAPEAXI@Z ??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ ?AddView@CompositeView@engine@donut@@QEAAXV?$shared_ptr@VIView@engine@donut@@@std@@@Z ?GetNumChildViews@CompositeView@engine@donut@@UEBAIW4Enum@ViewType@23@@Z ?GetChildView@CompositeView@engine@donut@@UEBAPEBVIView@23@W4Enum@ViewType@23@I@Z ??1CompositeView@engine@donut@@UEAA@XZ ??_GCompositeView@engine@donut@@UEAAPEAXI@Z ??_ECompositeView@engine@donut@@UEAAPEAXI@Z ?deallocate@?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@2@_K@Z ??1?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@QEAA@XZ ?_Xlength@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@CAXXZ ??0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z ?SetupForPlanarView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@MMMMU?$vector@M$02@73@H@Z ?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z ?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z ?SetupPerObjectShadow@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@IAEBU?$box@M$02@math@3@@Z ?SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ ?Clear@CascadedShadowMap@render@donut@@QEAAXPEAVICommandList@nvrhi@@@Z ?SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z ?SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z ?SetNumberOfCascadesUnsafe@CascadedShadowMap@render@donut@@QEAAXH@Z ?GetCascadeView@CascadedShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@I@Z ?GetPerObjectView@CascadedShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@I@Z ?GetWorldToUvzwMatrix@CascadedShadowMap@render@donut@@UEBA?AU?$matrix@M$03$03@math@3@XZ ?GetView@CascadedShadowMap@render@donut@@UEBAAEBVICompositeView@engine@3@XZ ?GetTexture@CascadedShadowMap@render@donut@@UEBAPEAVITexture@nvrhi@@XZ ?GetNumberOfCascades@CascadedShadowMap@render@donut@@UEBAIXZ ?GetCascade@CascadedShadowMap@render@donut@@UEBAPEBVIShadowMap@engine@3@I@Z ?GetNumberOfPerObjectShadows@CascadedShadowMap@render@donut@@UEBAIXZ ?GetPerObjectShadow@CascadedShadowMap@render@donut@@UEBAPEBVIShadowMap@engine@3@I@Z ?GetTextureSize@CascadedShadowMap@render@donut@@UEBA?AU?$vector@H$01@math@3@XZ ?GetUVRange@CascadedShadowMap@render@donut@@UEBA?AU?$box@M$01@math@3@XZ ?GetFadeRangeInTexels@CascadedShadowMap@render@donut@@UEBA?AU?$vector@M$01@math@3@XZ ?IsLitOutOfBounds@CascadedShadowMap@render@donut@@UEBA_NXZ ?FillShadowConstants@CascadedShadowMap@render@donut@@UEBAXAEAUShadowConstants@@@Z ??_GCascadedShadowMap@render@donut@@UEAAPEAXI@Z ??_ECascadedShadowMap@render@donut@@UEAAPEAXI@Z ??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ ??0PlanarShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@PEAVITexture@4@IAEBUViewport@4@@Z ?SetupWholeSceneDirectionalLightView@PlanarShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@AEBU?$box@M$02@math@3@M@Z ?SetupDynamicDirectionalLightView@PlanarShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@11M@Z ?SetupProxyView@PlanarShadowMap@render@donut@@QEAAXXZ ?SetLitOutOfBounds@PlanarShadowMap@render@donut@@QEAAX_N@Z ?SetFalloffDistance@PlanarShadowMap@render@donut@@QEAAXM@Z ?GetPlanarView@PlanarShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@XZ ??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ ??$scaling@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU?$vector@N$02@01@@Z ??$?DN$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@0@Z ??$inverse@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@@Z ??$?YM@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@AEBU201@@Z ??$length@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z ??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z ??1?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@UEAAPEAXI@Z ??$diagonal@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU?$vector@N$02@01@@Z ??$?DN$02$02$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@0@Z ??$?DN@math@donut@@YA?AU?$vector@N$02@01@AEBU201@AEBU?$matrix@N$02$02@01@@Z ??$inverse@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@@Z ??$lengthSquared@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z ??$?_0N@math@donut@@YAAEAU?$vector@N$02@01@AEAU201@N@Z ??$?YN@math@donut@@YAAEAU?$vector@N$02@01@AEAU201@AEBU201@@Z ??$_Uninitialized_move@PEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z@4HA ?dtor$0@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$0@?0??SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z@4HA ?dtor$0@?0??SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z@4HA ?dtor$0@?0??SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ@4HA ?dtor$10@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$14@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$1@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$1@?0??SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ@4HA ?dtor$2@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$3@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$4@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$6@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$7@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA ?dtor$9@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1TextureDesc@nvrhi@@QEAA@XZ $pdata$??1TextureDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $pdata$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $chain$1$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $pdata$1$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $chain$3$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $pdata$3$?GetNode@SceneGraphLeaf@engine@donut@@QEBAPEAVSceneGraphNode@23@XZ $unwind$??_GIShadowMap@engine@donut@@UEAAPEAXI@Z $pdata$??_GIShadowMap@engine@donut@@UEAAPEAXI@Z $unwind$??_GICompositeView@engine@donut@@UEAAPEAXI@Z $pdata$??_GICompositeView@engine@donut@@UEAAPEAXI@Z $unwind$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$0$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$0$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$1$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$1$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$2$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$2$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$3$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$3$??1?$vector@V?$shared_ptr@VIView@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VIView@engine@donut@@@std@@@2@@std@@QEAA@XZ $unwind$??_GCompositeView@engine@donut@@UEAAPEAXI@Z $pdata$??_GCompositeView@engine@donut@@UEAAPEAXI@Z $unwind$?deallocate@?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@2@_K@Z $pdata$?deallocate@?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@QEAAXQEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@2@_K@Z $unwind$??1?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@CAXXZ $unwind$??0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z $pdata$??0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z $cppxdata$??0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z $stateUnwindMap$??0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z $ip2state$??0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z $unwind$?dtor$14@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA $pdata$?dtor$14@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA $unwind$?dtor$10@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA $pdata$?dtor$10@?0???0CascadedShadowMap@render@donut@@QEAA@PEAVIDevice@nvrhi@@HHHW4Format@4@_N@Z@4HA $unwind$?SetupForPlanarView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@MMMMU?$vector@M$02@73@H@Z $pdata$?SetupForPlanarView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@MMMMU?$vector@M$02@73@H@Z $unwind$?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z $pdata$?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z $chain$8$?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z $pdata$8$?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z $chain$9$?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z $pdata$9$?SetupForPlanarViewStable@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@Ufrustum@math@3@U?$affine@M$02@73@MMMMU?$vector@M$02@73@H@Z $unwind$?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z $pdata$?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z $chain$6$?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z $pdata$6$?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z $chain$7$?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z $pdata$7$?SetupForCubemapView@CascadedShadowMap@render@donut@@QEAA_NAEBVDirectionalLight@engine@3@U?$vector@M$02@math@3@MMMMH@Z $unwind$?SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ $pdata$?SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ $cppxdata$?SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ $stateUnwindMap$?SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ $ip2state$?SetupProxyViews@CascadedShadowMap@render@donut@@QEAAXXZ $unwind$?Clear@CascadedShadowMap@render@donut@@QEAAXPEAVICommandList@nvrhi@@@Z $pdata$?Clear@CascadedShadowMap@render@donut@@QEAAXPEAVICommandList@nvrhi@@@Z $unwind$?SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z $pdata$?SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z $cppxdata$?SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z $stateUnwindMap$?SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z $ip2state$?SetLitOutOfBounds@CascadedShadowMap@render@donut@@QEAAX_N@Z $unwind$?SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z $pdata$?SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z $cppxdata$?SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z $stateUnwindMap$?SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z $ip2state$?SetFalloffDistance@CascadedShadowMap@render@donut@@QEAAXM@Z $unwind$?GetCascadeView@CascadedShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@I@Z $pdata$?GetCascadeView@CascadedShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@I@Z $unwind$?GetPerObjectView@CascadedShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@I@Z $pdata$?GetPerObjectView@CascadedShadowMap@render@donut@@QEAA?AV?$shared_ptr@VPlanarView@engine@donut@@@std@@I@Z $unwind$?GetTextureSize@CascadedShadowMap@render@donut@@UEBA?AU?$vector@H$01@math@3@XZ $pdata$?GetTextureSize@CascadedShadowMap@render@donut@@UEBA?AU?$vector@H$01@math@3@XZ $unwind$?GetUVRange@CascadedShadowMap@render@donut@@UEBA?AU?$box@M$01@math@3@XZ $pdata$?GetUVRange@CascadedShadowMap@render@donut@@UEBA?AU?$box@M$01@math@3@XZ $unwind$?GetFadeRangeInTexels@CascadedShadowMap@render@donut@@UEBA?AU?$vector@M$01@math@3@XZ $pdata$?GetFadeRangeInTexels@CascadedShadowMap@render@donut@@UEBA?AU?$vector@M$01@math@3@XZ $unwind$??_GCascadedShadowMap@render@donut@@UEAAPEAXI@Z $pdata$??_GCascadedShadowMap@render@donut@@UEAAPEAXI@Z $cppxdata$??_GCascadedShadowMap@render@donut@@UEAAPEAXI@Z $stateUnwindMap$??_GCascadedShadowMap@render@donut@@UEAAPEAXI@Z $ip2state$??_GCascadedShadowMap@render@donut@@UEAAPEAXI@Z $unwind$??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VPlanarView@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@QEAA@XZ $unwind$??$scaling@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU?$vector@N$02@01@@Z $pdata$??$scaling@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU?$vector@N$02@01@@Z $unwind$??$?DN$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@0@Z $pdata$??$?DN$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@0@Z $unwind$??$inverse@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@@Z $pdata$??$inverse@N$02@math@donut@@YA?AU?$affine@N$02@01@AEBU201@@Z $unwind$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $pdata$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $unwind$??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $chain$0$??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $pdata$0$??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $chain$1$??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $pdata$1$??$_Destroy_range@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $unwind$??_G?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@UEAAPEAXI@Z $unwind$??$diagonal@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU?$vector@N$02@01@@Z $pdata$??$diagonal@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU?$vector@N$02@01@@Z $unwind$??$?DN@math@donut@@YA?AU?$vector@N$02@01@AEBU201@AEBU?$matrix@N$02$02@01@@Z $pdata$??$?DN@math@donut@@YA?AU?$vector@N$02@01@AEBU201@AEBU?$matrix@N$02$02@01@@Z $unwind$??$inverse@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@@Z $pdata$??$inverse@N$02@math@donut@@YA?AU?$matrix@N$02$02@01@AEBU201@@Z $unwind$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $pdata$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $cppxdata$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $tryMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $handlerMap$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $ip2state$??$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@?$vector@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@1@QEAV21@AEBV21@@Z@4HA $unwind$??$_Uninitialized_move@PEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $pdata$??$_Uninitialized_move@PEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@V?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@2@@std@@YAPEAV?$shared_ptr@VPlanarShadowMap@render@donut@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VPlanarShadowMap@render@donut@@@std@@@0@@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7IShadowMap@engine@donut@@6B@ ??_7ICompositeView@engine@donut@@6B@ ??_7CompositeView@engine@donut@@6B@ ??_7CascadedShadowMap@render@donut@@6B@ ??_C@_09HHFBJIPM@ShadowMap@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_7?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@6B@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4IShadowMap@engine@donut@@6B@ ??_R0?AVIShadowMap@engine@donut@@@8 ??_R3IShadowMap@engine@donut@@8 ??_R2IShadowMap@engine@donut@@8 ??_R1A@?0A@EA@IShadowMap@engine@donut@@8 ??_R4ICompositeView@engine@donut@@6B@ ??_R0?AVICompositeView@engine@donut@@@8 ??_R3ICompositeView@engine@donut@@8 ??_R2ICompositeView@engine@donut@@8 ??_R1A@?0A@EA@ICompositeView@engine@donut@@8 ??_R4CompositeView@engine@donut@@6B@ ??_R0?AVCompositeView@engine@donut@@@8 ??_R3CompositeView@engine@donut@@8 ??_R2CompositeView@engine@donut@@8 ??_R1A@?0A@EA@CompositeView@engine@donut@@8 ??_R4CascadedShadowMap@render@donut@@6B@ ??_R0?AVCascadedShadowMap@render@donut@@@8 ??_R3CascadedShadowMap@render@donut@@8 ??_R2CascadedShadowMap@render@donut@@8 ??_R1A@?0A@EA@CascadedShadowMap@render@donut@@8 ??_R4?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VPlanarShadowMap@render@donut@@@std@@8 __real@3dcccccd __real@3eb0c6f7a0000000 __real@3f000000 __real@3f800000 __real@3ff0000000000000 __real@bff0000000000000 __security_cookie __xmm@00000000000000003ff0000000000000 __xmm@3f8000003f8000003f8000003f800000 __xmm@3f800000bf800000bf800000bf800000 __xmm@7fffffffffffffff7fffffffffffffff __xmm@80000000000000008000000000000000 __xmm@80000000800000008000000080000000 