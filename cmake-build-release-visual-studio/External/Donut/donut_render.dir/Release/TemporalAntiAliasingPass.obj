d�.眈Gh�
 �      .drectve        <  DW               
 .debug$S        � �X  �#        @ B.debug$T        p   �#             @ B.rdata          @   P$             @ @@.text$mn        9  �$ �%         P`.debug$S        �  & �-     :   @B.text$mn        �   0              P`.debug$S        �  �0 [2        @B.text$mn        n  �2              P`.debug$S        �  4 �6        @B.text$mn        �   Y7              P`.debug$S        �  8 �9        @B.text$mn        �   :              P`.debug$S        ,  �: �;        @B.text$mn        .   $<              P`.debug$S        �   R< F=        @B.text$mn        =   �=              P`.debug$S        �   �= �>        @B.text$mn        ,   ?              P`.debug$S        �   C? 7@        @B.text$mn           嘆              P`.debug$S        �   汙 廇        @B.text$mn        :   逜 B         P`.debug$S          7B CD        @B.text$mn        }  螪 LF         P`.debug$S        �
  VF FQ     d   @B.text$mn          .U <V         P`.debug$S        D  俈 芢     2   @B.text$mn        1   篰 隵         P`.debug$S        �  鮚 閌        @B.text$mn        s  盿 $d     	    P`.debug$S        �  ~d .s     b   @B.text$x         C   w Ew         P`.text$mn           cw hw         P`.debug$S        X  rw 蕏        @B.text$mn        @   y              P`.debug$S        t  Fy 簔        @B.text$mn        �   2{ 穥         P`.debug$S        �  調 潁        @B.text$mn        �   ~              P`.debug$S        �  眫 檭        @B.text$mn        p   u�              P`.debug$S        ,  鍎 �        @B.text$mn        �  M� 靽         P`.debug$S          鰢 
�        @B.text$mn        1  鎷 �         P`.debug$S        �  +� 窂        @B.text$mn        �  k� 5�         P`.debug$S        �  g� 鳛        @B.text$mn        A  麩 <�         P`.debug$S        �  尋 ��        @B.text$mn        3   劕              P`.debug$S        ,  番 悱        @B.text$mn            �              P`.debug$S        T  ?� 摫        @B.text$mn           3�              P`.debug$S          >� N�        @B.text$mn          姵 ⒋         P`.debug$S        �  薮 苹     2   @B.text$mn        �   航 娋         P`.debug$S           ň 嚷     "   @B.text$mn        L   � h�         P`.debug$S        l  喣 蚺        @B.text$mn           B�              P`.debug$S        �   F� �        @B.text$mn        S  Z�          P`.debug$S        �
  呷 熡     D   @B.text$x            G� W�         P`.text$x            a� q�         P`.text$x            {� 嬛         P`.text$mn        
   曋              P`.debug$S        �   ⒅ f�        @B.text$mn        �   ⒆              P`.debug$S        �   d� 0�        @B.text$mn           X�              P`.debug$S        �   i� !�        @B.text$mn          I� N�     b    P`.debug$S        H�  "� j�     �  @B.text$x            B� N�         P`.text$x            X� d�         P`.text$x            n� z�         P`.text$x            勷 旔         P`.text$x            烉          P`.text$x            葛 瑞         P`.text$x            茵 怵         P`.text$x            祓          P`.text$x            � �         P`.text$x             � 0�         P`.text$x            :� J�         P`.text$x            T� d�         P`.text$x            n� ~�         P`.text$x            堮 橊         P`.text$x            Ⅰ 柴         P`.text$x            捡 锐         P`.text$x            荫 揆         P`.text$x            桉 赳         P`.text$x             
�         P`.text$x            �  �         P`.text$x            *� 6�         P`.text$x            @� L�         P`.text$x            V� b�         P`.text$x            l� x�         P`.text$x            傭 庲         P`.text$x            橋 を         P`.text$x             候         P`.text$x         -   尿 耱         P`.text$x            � �         P`.text$x         *   � E�         P`.text$x            Y� e�         P`.text$x         *   o� 欝         P`.text$x             贵         P`.text$x            皿 象         P`.text$x            袤 弩         P`.text$x            矬          P`.text$x             � %�         P`.text$x            /� ?�         P`.text$x            I� Y�         P`.text$x            c� s�         P`.text$x            }� 嶔         P`.text$x            楐          P`.text$x            濒 留         P`.text$mn           唆              P`.debug$S        �   拄 庻        @B.text$mn           鄂              P`.debug$S        �   王 欥        @B.text$mn        <   睁 �         P`.debug$S        0  /� _�     
   @B.text$mn        <   螟 ��         P`.debug$S        L  � i�     
   @B.text$mn        !   旺 铤         P`.debug$S        <  � >�        @B.text$mn        2   z�          P`.debug$S        <  傈         @B.text$mn        "   t�              P`.debug$S        �  桛 .         @B.text$mn        "   �               P`.debug$S        �  �  �        @B.text$mn        "   $              P`.debug$S        �  F �        @B.text$mn        "   r              P`.debug$S        �  � 0        @B.text$mn        "   �              P`.debug$S        �  � ~	        @B.text$mn        "   
              P`.debug$S        �  @
 �        @B.text$mn        "   �              P`.debug$S        �  � 6        @B.text$mn        "   �              P`.debug$S        �  � �        @B.text$mn        "   $              P`.debug$S        �  F �        @B.text$mn        "   r              P`.debug$S        �  �          @B.text$mn        e   � %         P`.debug$S           C c        @B.text$mn        [   + �         P`.debug$S          � �         @B.text$mn        ^   �! �!         P`.debug$S        T  " X%        @B.text$mn        }    & �&         P`.debug$S        �  �& �+        @B.text$mn        K   i,              P`.debug$S        �  �, �.        @B.text$mn        K    /              P`.debug$S        �  k/ ?1        @B.text$mn           �1              P`.debug$S        �  �1 �3        @B.text$mn        �   4 �4         P`.debug$S        �  �4 T8        @B.text$mn        �   X9 �9         P`.debug$S        h  : y>         @B.text$mn        `   �? @         P`.debug$S        �  -@ 鞡        @B.text$mn        ?    郈         P`.debug$S        \  鬋 PE        @B.text$mn        j   菶 2F         P`.debug$S        �  FF 蜨        @B.text$mn        �   襂 丣         P`.debug$S        �  旿 YN     *   @B.text$mn        �   齇          P`.debug$S        �  腜 HU     $   @B.text$mn           癡 肰         P`.debug$S        �   譜 籛        @B.text$mn           鉝 鯳         P`.debug$S        �   
X 闤        @B.text$mn        �   &Y              P`.debug$S          玒 痆        @B.text$mn        �   '\ 瞈         P`.debug$S        �  衆 `_        @B.text$mn        B   P` 抈         P`.debug$S           癭 癮        @B.text$mn        B   靉 .b         P`.debug$S          Lb \c        @B.text$mn        B   榗 赾         P`.debug$S        �   鴆 鬱        @B.text$mn        H   0e              P`.debug$S        �  xe <g        @B.text$mn        �   Th i         P`.debug$S        P  [i 玭     *   @B.text$mn        L  Op 況         P`.debug$S        �  Es 蓎     (   @B.text$mn          Y{ d�     
    P`.debug$S        �  鎯 聫     V   @B.text$mn           �              P`.debug$S        (  %� M�        @B.text$mn        �  潝 6�         P`.debug$S        
  r� ~�     J   @B.text$mn        �   b�              P`.debug$S        �  辚 猝        @B.text$mn        O  妯 5�         P`.debug$S           q� q�     F   @B.text$mn        A   -�              P`.debug$S        H  n� 抖        @B.text$mn            .� N�         P`.debug$S        �   l� 0�        @B.text$mn           l� }�         P`.debug$S        �   懜 E�        @B.text$mn           伖 捁         P`.debug$S                    @B.text$mn           夂 蠛         P`.debug$S          � �        @B.text$mn        `  W� 方         P`.debug$S        �  �      B   @B.text$mn        A   ?� ��         P`.debug$S        �  斏 D�        @B.text$mn           H� [�         P`.debug$S        �   e� 9�        @B.xdata             u�             @0@.pdata             壩 曃        @0@.xdata             澄             @0@.pdata             晃 俏        @0@.xdata             逦             @0@.pdata             裎         @0@.xdata             �             @0@.pdata             #� /�        @0@.xdata             M�             @0@.pdata             Y� e�        @0@.xdata             兿             @0@.pdata             嬒 椣        @0@.xdata             迪             @0@.pdata             料 拖        @0@.xdata             胂             @0@.pdata             笙 ��        @0@.xdata             �             @0@.pdata             %� 1�        @0@.xdata             O�             @0@.pdata             c� o�        @0@.xdata             嵭             @0@.pdata             曅 ⌒        @0@.xdata             啃             @0@.pdata             有 咝        @0@.xdata              �        @0@.pdata             /� ;�        @0@.xdata             Y� i�        @0@.pdata             囇 撗        @0@.xdata             毖 叛        @0@.pdata             阊 镅        @0@.xdata             
� �        @0@.pdata             '� 3�        @0@.xdata             Q�             @0@.pdata             Y� e�        @0@.xdata             円 撘        @0@.pdata             б 骋        @0@.xdata          	   岩 谝        @@.xdata             钜 粢        @@.xdata                          @@.xdata             � �        @0@.pdata             %� 1�        @0@.xdata          	   O� X�        @@.xdata             l� r�        @@.xdata             |�             @@.xdata             � 徲        @0@.pdata             Ｓ         @0@.xdata          	   陀 钟        @@.xdata             暧 鹩        @@.xdata                          @@.xdata              
�        @0@.pdata             !� -�        @0@.xdata          	   K� T�        @@.xdata             h� n�        @@.xdata             x�             @@.xdata              {� 浽        @0@.pdata              辉        @0@.xdata          	   僭 庠        @@.xdata             鲈         @@.xdata             �             @@.xdata              � 1�        @0@.pdata             E� Q�        @0@.xdata          	   o� x�        @@.xdata             屨 捳        @@.xdata             溦             @@.xdata             熣 痴        @0@.pdata             钦 诱        @0@.xdata          	   裾         @@.xdata             � �        @@.xdata             �             @@.xdata             $� 4�        @0@.pdata             H� T�        @0@.xdata          	   r� {�        @@.xdata             徶 曋        @@.xdata             熤             @@.xdata             ぶ             @0@.pdata             爸 贾        @0@.voltbl            谥               .xdata             苤             @0@.pdata             渲 鹬        @0@.xdata             � "�        @0@.pdata             @� L�        @0@.xdata             j� z�        @0@.pdata             樧 ぷ        @0@.voltbl            伦               .xdata             淖 宰        @0@.pdata             枳 糇        @0@.xdata          	   � �        @@.xdata             /� 5�        @@.xdata             ?�             @@.xdata             B� R�        @0@.pdata             f� r�        @0@.xdata          	   愗 欂        @@.xdata              池        @@.xdata             截             @@.xdata             镭 胸        @0@.pdata             湄 鹭        @0@.xdata          	   � �        @@.xdata             +� 1�        @@.xdata             ;�             @@.xdata             >� N�        @0@.pdata             b� n�        @0@.xdata          	   屬 曎        @@.xdata             ┵         @@.xdata             官             @@.xdata             假 藤        @0@.pdata             噘 熨        @0@.xdata          	   
� �        @@.xdata             '� -�        @@.xdata             7�             @@.xdata          (   :� b�        @0@.pdata             v� 傏        @0@.xdata          	   犣 ┶        @@.xdata            节 疼     .   @@.xdata          �   樰             @@.xdata              �             @0@.pdata             (� 4�        @0@.xdata             R�             @0@.pdata             Z� f�        @0@.xdata             勣             @0@.pdata             屴 樲        @0@.xdata             掇             @0@.pdata             巨 兽        @0@.voltbl            柁               .xdata             檗 �        @0@.pdata             � �        @0@.xdata          8   9� q�        @0@.pdata             忂 涍        @0@.xdata             惯 蛇        @0@.pdata             邕 筮        @0@.xdata              � 1�        @0@.pdata             ;� G�        @0@.xdata          $   e� 夃        @0@.pdata             о 赤        @0@.xdata             燕 徉        @0@.pdata             �� �        @0@.xdata             )� A�        @0@.pdata             U� a�        @0@.xdata          	   � 堘        @@.xdata             溼 ⑨        @@.xdata                          @@.xdata             贬 裴        @0@.pdata             厢 坩        @0@.xdata              �        @0@.pdata             /� ;�        @0@.xdata             Y� i�        @0@.pdata             団 撯        @0@.xdata             扁 菱        @0@.pdata             这 徕        @0@.xdata          	   �� �        @@.xdata             � "�        @@.xdata             ,�             @@.xdata             /� K�        @0@.pdata             _� k�        @0@.xdata          	   夈 掋        @@.xdata             ︺         @@.xdata             躲             @@.xdata             广             @0@.pdata             零 豌        @0@.xdata             脬             @0@.pdata             筱 ��        @0@.xdata             � 9�        @0@.pdata             M� Y�        @0@.xdata          	   w� ��        @@.xdata             斾 ╀        @@.xdata             唁             @@.xdata             劁 桎        @0@.pdata              �        @0@.xdata          	   &� /�        @@.xdata             C� I�        @@.xdata             S�             @@.xdata             Z� v�        @0@.pdata             婂 栧        @0@.xdata          	   村 藉        @@.xdata             彦 族        @@.xdata             徨             @@.xdata              溴 �        @0@.pdata             � $�        @0@.xdata          	   B� K�        @@.xdata             _� e�        @@.xdata             o�             @@.xdata             t�             @0@.pdata             |� 堟        @0@.xdata             ︽ 舵        @0@.pdata             舒 宙        @0@.xdata          	   翩         @@.xdata             � �        @@.xdata             !�             @@.xdata             $� 8�        @0@.pdata             L� X�        @0@.xdata          	   v� �        @@.xdata             撶 欑        @@.xdata             ｇ             @@.xdata                          @0@.pdata             茬 剧        @0@.xdata             茜             @0@.pdata             溏 痃        @0@.xdata             � "�        @0@.pdata             @� L�        @0@.xdata             j� z�        @0@.pdata             樿 よ        @0@.voltbl            妈               .xdata             蔫             @0@.pdata             惕 罔        @0@.xdata             鲨             @0@.pdata             � �        @0@.xdata             ,� @�        @0@.pdata             ^� j�        @0@.xdata             堥 橀        @0@.pdata             堕 麻        @0@.xdata             嚅             @0@.pdata             栝 糸        @0@.xdata          (   �             @0@.pdata             :� F�        @0@.xdata             d�             @0@.pdata             t� ��        @0@.xdata          ,   炾             @0@.pdata             赎 株        @0@.xdata             絷             @0@.pdata              �        @0@.xdata             &�             @0@.pdata             .� :�        @0@.xdata              X� x�        @0@.pdata             岆 橂        @0@.xdata          	   峨 侩        @@.xdata             与 垭        @@.xdata             咫             @@.xdata             鲭             @0@.pdata             
� �        @0@.xdata             4� H�        @0@.pdata             f� r�        @0@.xdata             愳 犾        @0@.pdata             眷 熟        @0@.xdata             桁         @0@.pdata             � &�        @0@.xdata             D� T�        @0@.pdata             r� ~�        @0@.xdata             滍             @0@.pdata              错        @0@.xdata             翼             @0@.pdata             觏 鲰        @0@.xdata          $   �             @0@.pdata             8� D�        @0@.xdata             b�             @0@.pdata             n� z�        @0@.xdata             橆 搭        @0@.pdata             阮 灶        @0@.xdata          
   蝾 ��        @@.xdata             �             @@.xdata              � (�        @@.xdata             2� 9�        @@.xdata          	   C�             @@.xdata             L�             @0@.pdata             X� d�        @0@.voltbl            傦               .xdata             冿             @0@.pdata             嬶 楋        @0@.xdata             碉             @0@.pdata             斤 娠        @0@.xdata             顼         @0@.pdata             � %�        @0@.xdata             C� S�        @0@.pdata             q� }�        @0@.rdata             涴 仇        @@@.rdata             佯             @@@.rdata             沭         @@@.rdata             � 1�        @@@.rdata             O�             @@@.xdata$x           d� ��        @@@.xdata$x           旕 榜        @@@.data$r         /   务         @@�.xdata$x        $   � +�        @@@.data$r         $   ?� c�        @@�.xdata$x        $   m� 戲        @@@.data$r         $   ヲ 沈        @@�.xdata$x        $   域 黩        @@@.rdata             �             @@@.data               �             @ @�.rdata             ;� S�        @@@.rdata             q�             @0@.rdata             s�             @0@.rdata             u�             @@@.rdata             侒             @0@.rdata          $   嗴             @@@.rdata          
                @@@.rdata             敷             @@@.rdata             误             @@@.rdata             珞             @@@.rdata             �             @@@.rdata             �             @@@.rdata             �             @@@.rdata$r        $   .� R�        @@@.rdata$r           p� 勽        @@@.rdata$r           庺 汈        @@@.rdata$r        $    若        @@@.rdata$r        $   荇  �        @@@.rdata$r           � 2�        @@@.rdata$r           <� P�        @@@.rdata$r        $   d� 堳        @@@.rdata$r        $   滜 栗        @@@.rdata$r           搋 蝓        @@@.rdata$r            �        @@@.rdata$r        $   6� Z�        @@@.rdata$r        $   n� 掱        @@@.data$rs        6   蚌 骣        @@�.rdata$r           瘀 �        @@@.rdata$r           � �        @@@.rdata$r        $   $� H�        @@@.rdata             \�             @0@.rdata             `�             @0@.rdata             d�             @0@.rdata             h�             @0@.rdata             l�             @0@.rdata             p�             @0@.rdata             t�             @0@.rdata             x�             @0@.rdata             |�             @P@.rdata             岟             @P@.rdata             滣             @P@.rdata                          @P@.rdata             槛             @P@.rdata             眺             @P@.rdata             荀             @P@.rdata             祺             @P@.rdata                          @P@.rdata             �             @P@.rdata             �             @P@.debug$S        H   ,� t�        @B.debug$S        4   堷 鉴        @B.debug$S        4   续 �        @B.debug$S        @   � X�        @B.chks64         p  l�              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   D  v     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\TemporalAntiAliasingPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $vfs  $math 	 $colors 	 $render 	 $stdext �   �>  #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align " ;    std::memory_order_relaxed + �    std::_Aligned_storage<64,8>::_Fits " ;   std::memory_order_consume " ;   std::memory_order_acquire " ;   std::memory_order_release " ;   std::memory_order_acq_rel " ;   std::memory_order_seq_cst * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits   8   _Mtx_try   8   _Mtx_recursive E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment  �8   std::_INVALID_ARGUMENT  �8   std::_NO_SUCH_PROCESS & �8   std::_OPERATION_NOT_PERMITTED , �8   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - �8   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % 7    _Atomic_memory_order_relaxed % 7   _Atomic_memory_order_consume % 7   _Atomic_memory_order_acquire % 7   _Atomic_memory_order_release % 7   _Atomic_memory_order_acq_rel % 7   _Atomic_memory_order_seq_cst � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size : #   std::integral_constant<unsigned __int64,2>::value $ %   std::_Locbase<int>::collate " %   std::_Locbase<int>::ctype % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos = �   donut::engine::c_MaxRenderPassConstantBufferVersions . %   donut::math::box<float,2>::numCorners � �   std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Same_size_and_compatible � �    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Bitcopy_constructible � �    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Bitcopy_assignable J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy � #   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment Z�    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]�   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable � #   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment � #   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment C #   std::allocator<double>::_Minimum_asan_allocation_alignment  �?   std::_Consume_header  �?   std::_Generate_header ��    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi ��   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard � �   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable � #   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment � �   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � �    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m#   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g�    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � �   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable ) �   nvrhi::ObjectTypes::SharedHandle - �  �  nvrhi::ObjectTypes::D3D11_Device 4 �  �  nvrhi::ObjectTypes::D3D11_DeviceContext / �  �  nvrhi::ObjectTypes::D3D11_Resource - �  �  nvrhi::ObjectTypes::D3D11_Buffer 7 �  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 �  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 �  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : �  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - �  �  nvrhi::ObjectTypes::D3D12_Device 3 �  �  nvrhi::ObjectTypes::D3D12_CommandQueue : �  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / �  �  nvrhi::ObjectTypes::D3D12_Resource A �  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A �  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F �  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos G �  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 �  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 �  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 �  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * �  �  nvrhi::ObjectTypes::VK_Device 2 �  �  nvrhi::ObjectTypes::VK_PhysicalDevice , �  �  nvrhi::ObjectTypes::VK_Instance ) �  �  nvrhi::ObjectTypes::VK_Queue 1 �  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 �  �  nvrhi::ObjectTypes::VK_DeviceMemory * �  �  nvrhi::ObjectTypes::VK_Buffer ) �  �  nvrhi::ObjectTypes::VK_Image - �  �	  nvrhi::ObjectTypes::VK_ImageView < �  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + �  �  nvrhi::ObjectTypes::VK_Sampler 0 �  �  nvrhi::ObjectTypes::VK_ShaderModule . �  �
  nvrhi::ObjectTypes::VK_RenderPass / �  �  nvrhi::ObjectTypes::VK_Framebuffer 2 �  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 �  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 �  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 �  �  nvrhi::ObjectTypes::VK_PipelineLayout , �  �  nvrhi::ObjectTypes::VK_Pipeline , �  �  nvrhi::ObjectTypes::VK_Micromap 3 �  �  nvrhi::ObjectTypes::VK_ImageCreateInfo �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size �#   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets ��    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment 8 �   std::atomic<unsigned long>::is_always_lock_free � #   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment / �  � nvrhi::rt::cluster::kClasByteAlignment . �   nvrhi::rt::cluster::kClasMaxTriangles - �   nvrhi::rt::cluster::kClasMaxVertices 2 �  ���� nvrhi::rt::cluster::kMaxGeometryIndex 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable W #   std::allocator<donut::engine::ShaderMacro>::_Minimum_asan_allocation_alignment % #   std::ctype<char>::table_size _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment a #   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified Z #   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment   %   std::_Iosb<int>::skipws ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase  �   U]  # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot c #   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment \ #   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ( �   donut::math::vector<int,2>::DIM ( �   donut::math::vector<int,4>::DIM ( �   donut::math::vector<int,3>::DIM  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN q %    std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::word_size r %  pstd::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::state_size r %  �std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::shift_size q %   std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::mask_bits w �  �甙檚td::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::parameter_a p %   std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::output_u p %   std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::output_s t �  ��V,漵td::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::output_b p %   std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::output_t t �  �  骑std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::output_c p %   std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::output_l t �  qstd::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::default_seed '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable q �  �����std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::_WMSK q �  �   �std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::_HMSK q �  ����std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::_LMSK . %   donut::math::box<float,3>::numCorners r %   std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::_One_mod_n p %  �std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>::_M_mod_n - �    std::chrono::system_clock::is_steady � #  �����std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::_Max 6 �   std::_Iterator_base0::_Unwrap_when_unverified $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den � #    std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::word_size � #  pstd::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::state_size � #  �std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::shift_size � #   std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::mask_bits � �  �甙檚td::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::xor_mask � #   std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_u � �  �����std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_d � #   std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_s � �  ��V,漵td::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_b � #   std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_t � �  �  骑std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_c � #   std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::tempering_l � �  �e�lstd::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::initialization_multiplier � �  qstd::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253>::default_seed 7 �   std::_Iterator_base12::_Unwrap_when_unverified � �   std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture> const &,nvrhi::RefCountPtr<nvrhi::ITexture> &>::_Same_size_and_compatible � �    std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture> const &,nvrhi::RefCountPtr<nvrhi::ITexture> &>::_Bitcopy_constructible  �   nvrhi::c_HeaderVersion " �   nvrhi::c_MaxRenderTargets � �    std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture> const &,nvrhi::RefCountPtr<nvrhi::ITexture> &>::_Bitcopy_assignable  �   nvrhi::c_MaxViewports % �   nvrhi::c_MaxVertexAttributes # �   nvrhi::c_MaxBindingLayouts & �  � nvrhi::c_MaxBindingsPerLayout 5 �   nvrhi::c_MaxVolatileConstantBuffersPerLayout , �    nvrhi::c_MaxVolatileConstantBuffers % �  � nvrhi::c_MaxPushConstantSize 3 �   nvrhi::c_ConstantBufferOffsetSizeAlignment  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < r  ��枠 std::integral_constant<__int64,10000000>::value � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible 1 r   std::integral_constant<__int64,1>::value � �   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable - �   std::chrono::steady_clock::is_steady & r   std::ratio<1,1000000000>::num * r  � 蕷;std::ratio<1,1000000000>::den W �   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den R �   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ) �   donut::math::vector<bool,2>::DIM 7 �  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 �  �����nvrhi::TextureSubresourceSet::AllArraySlices D #   ��std::basic_string_view<char,std::char_traits<char> >::npos ) �   donut::math::vector<bool,3>::DIM # �        nvrhi::AllSubresources ) �   donut::math::vector<bool,4>::DIM + �    std::_Aligned_storage<72,8>::_Fits * �    std::_Aligned<72,8,char,0>::_Fits + �    std::_Aligned<72,8,short,0>::_Fits ) �   std::_Aligned<72,8,int,0>::_Fits J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den . �   std::integral_constant<bool,1>::value L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos   3        nvrhi::EntireBuffer A #   std::allocator<bool>::_Minimum_asan_allocation_alignment L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den I #   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den 3   \ std::filesystem::path::preferred_separator - %    std::integral_constant<int,0>::value A #   std::allocator<char>::_Minimum_asan_allocation_alignment : #    std::integral_constant<unsigned __int64,0>::value ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity * �   donut::math::vector<float,3>::DIM X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val : �   std::numeric_limits<std::_Unsigned128>::is_modulo 7 %  � std::numeric_limits<std::_Unsigned128>::digits 9 %  & std::numeric_limits<std::_Unsigned128>::digits10 a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size a #   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment ) 9    std::_Invoker_functor::_Strategy , 9   std::_Invoker_pmf_object::_Strategy - 9   std::_Invoker_pmf_refwrap::_Strategy - 9   std::_Invoker_pmf_pointer::_Strategy , 9   std::_Invoker_pmd_object::_Strategy - 9   std::_Invoker_pmd_refwrap::_Strategy - 9   std::_Invoker_pmd_pointer::_Strategy T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM 5 %   std::numeric_limits<std::_Signed128>::digits 7 %  & std::numeric_limits<std::_Signed128>::digits10 8 �   std::numeric_limits<std::_Signed128>::is_signed � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment �    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi �   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard + �        nvrhi::rt::c_IdentityTransform  �5    std::denorm_absent  �5   std::denorm_present  �5    std::round_toward_zero  �5   std::round_to_nearest # �5    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ �5    std::_Num_base::round_style  %    std::_Num_base::digits � #   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix ) �5   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * �5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE ) �   donut::math::frustum::numCorners D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity 0 �   std::numeric_limits<wchar_t>::is_modulo - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask 5 �    std::filesystem::_File_time_clock::is_steady F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask T #   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -#   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets '�    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10    �   �  , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent   �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �   糍   x7  _CatchableType " 7  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & $7  $_TypeDescriptor$_extraBytes_24 6 �<  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  ?  _Ctypevec & 8<  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  5,  _Thrd_result  #   rsize_t - �<  __vc_attributes::event_sourceAttribute 9 �<  __vc_attributes::event_sourceAttribute::optimize_e 5 �<  __vc_attributes::event_sourceAttribute::type_e > �<  __vc_attributes::helper_attributes::v1_alttypeAttribute F }<  __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9 z<  __vc_attributes::helper_attributes::usageAttribute B v<  __vc_attributes::helper_attributes::usageAttribute::usage_e * s<  __vc_attributes::threadingAttribute 7 l<  __vc_attributes::threadingAttribute::threading_e - i<  __vc_attributes::aggregatableAttribute 5 b<  __vc_attributes::aggregatableAttribute::type_e / _<  __vc_attributes::event_receiverAttribute 7 V<  __vc_attributes::event_receiverAttribute::type_e ' S<  __vc_attributes::moduleAttribute / J<  __vc_attributes::moduleAttribute::type_e  
B  __std_fs_find_data & I7  $_TypeDescriptor$_extraBytes_23 - �7  $_s__CatchableTypeArray$_extraBytes_32 # ;G  __std_fs_reparse_data_buffer Z +T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ (T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` &T  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  B  __std_fs_dir_handle  K>  __std_access_rights  G<  _TypeDescriptor & k7  $_TypeDescriptor$_extraBytes_34  D<  _Stl_critical_section 	 !  tm %  7  _s__RTTICompleteObjectLocator2 $ a�  TemporalAntiAliasingConstants A @<  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  x7  _s__CatchableType & 7  $_TypeDescriptor$_extraBytes_19 & n7  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 <<  __vcrt_va_list_is_reference<wchar_t const * const>  
E  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & 17  $_TypeDescriptor$_extraBytes_20  p  va_list - v7  $_s__CatchableTypeArray$_extraBytes_16   aG  __std_fs_copy_file_result  鉇  __std_code_page � 2<  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � #<  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � <  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 鸖  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � 
T  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > z T  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> � 4<  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � %<  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > iT  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � T  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 腟  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > � <  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �:  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > � 齋  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > �<  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> c �;  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> � �;  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > s k:  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � l9  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � _:  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > � 鳶  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 籕  std::_Ptr_base<donut::vfs::IFileSystem> �霺  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � �;  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 釹  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 蠸  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> ��;  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> Q�;  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � 芐  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > 稴  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> �;  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > \R  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > � �:  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > [ 癝  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' 贘  std::default_delete<wchar_t [0]> . b+  std::_Conditionally_enabled_hash<int,1> A F  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? �6  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit  
-  std::timed_mutex � [;  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * C2  std::hash<enum nvrhi::ResourceType> + I�  std::numeric_limits<std::_Signed128> - 払  std::reverse_iterator<wchar_t const *> " H6  std::_Char_traits<char,int>  p>  std::_Fs_file � 琒  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >  "   std::_Atomic_counter_t  �5  std::_Num_base & n+  std::hash<std::error_condition> K M3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > � 淪  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >  �(  std::_Big_uint128  �,  std::condition_variable � I;  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > ) U6  std::_Narrow_char_traits<char,int>  z  std::hash<float> 6 3  std::allocator<nvrhi::rt::PipelineHitGroupDesc> R �.  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > 嘢  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > " 5;  std::_Align_type<double,64>  f+  std::hash<int>  �5  std::_Num_int_base  錊  std::ctype<wchar_t> " �+  std::_System_error_category / a2  std::_Conditionally_enabled_hash<bool,1>  �5  std::float_denorm_style z 拥  std::_Compressed_pair<std::default_delete<donut::engine::FramebufferFactory>,donut::engine::FramebufferFactory *,1>   std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> u 赑  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 2;  std::allocator_traits<std::allocator<wchar_t> >  &  std::bad_cast  玁  std::equal_to<void> 3 yI  std::_Ptr_base<donut::engine::ShaderFactory> � 鰿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > o 糝  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> 6 諭  std::initializer_list<nvrhi::BindingLayoutItem> " �5  std::numeric_limits<double>  C&  std::__non_rtti_object ( 0  std::_Basic_container_proxy_ptr12 � �.  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> > 却  std::vector<unsigned int,std::allocator<unsigned int> > T  ;  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1   std::array<nvrhi::FramebufferAttachment,8>  �5  std::_Num_float_base  *  std::logic_error � �:  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > 7 2  std::_Conditionally_enabled_hash<unsigned int,1> G K2  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  y&  std::pointer_safety ! �:  std::char_traits<char32_t>  �?  std::locale  �?  std::locale::_Locimp  �?  std::locale::facet   �?  std::locale::_Facet_guard  K?  std::locale::id s OQ  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �5  std::numeric_limits<bool>  ��  std::_Unsigned128 # �6  std::_WChar_traits<char16_t> P�0  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P K  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T x  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy     std::_Fake_proxy_ptr_impl * �5  std::numeric_limits<unsigned short> ' k%  std::hash<nvrhi::BindingSetDesc> � �.  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z 凱  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M |B  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � �:  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  �*  std::overflow_error % �/  std::_One_then_variadic_args_t  さ  std::uniform_real<float> + Y�  std::uniform_real<float>::param_type ' z�  std::_Circ_buf<unsigned int,624> D �2  std::_Constexpr_immortalize_impl<std::_System_error_category> W 5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * p-  std::_Vb_val<std::allocator<bool> > E  G  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j S  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �:  std::char_traits<wchar_t>  ~,  std::recursive_mutex   �  std::pmr::memory_resource !   std::array<nvrhi::Rect,16> 4 93  std::allocator<nvrhi::rt::PipelineShaderDesc> � 
S  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � �:  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � �:  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> n 8  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > �   std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  h:  std::false_type S �:  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #�0  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  �5  std::float_round_style T o  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j >  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy ! K,  std::hash<std::thread::id>  �  std::string B �:  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T 
C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , �  std::array<nvrhi::BindingSetItem,128> � A3  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > �   std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  �,  std::adopt_lock_t - G�  std::numeric_limits<std::_Unsigned128> � l3  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , �5  std::numeric_limits<unsigned __int64>  �>  std::_Locinfo 6 0E  std::_Ptr_base<std::filesystem::_Dir_enum_impl> \ �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > s P  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 鯮  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > $ �5  std::numeric_limits<char16_t> 0 a"  std::array<nvrhi::VertexBufferBinding,16> � �.  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void>  ;  std::string_view  w  std::wstring_view � �0  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % |9  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound  `,  std::_Mutex_base  艫  std::money_base  逺  std::money_base::pattern  �>  std::_Timevec  v:  std::defer_lock_t   �*  std::_Init_once_completer �'  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � �(  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > j 慒  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � `F  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �,  std::scoped_lock<> + D@  std::codecvt<wchar_t,char,_Mbstatet> h k/  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q 軷  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>     std::_Iterator_base12 � 蜶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  |L  std::_Pocma_values 7 �%  std::_Array_const_iterator<enum nvrhi::Format,8> ! \+  std::hash<std::error_code> N HC  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >  幍  std::mt19937 @ �6  std::_Default_allocator_traits<std::allocator<char32_t> >  a/  std::allocator<char32_t> ? 3G  std::unique_ptr<char [0],std::default_delete<char [0]> > $ m  std::_Atomic_integral<long,4>  e2  std::hash<bool>     std::streamsize 6 30  std::_String_val<std::_Simple_types<char32_t> > = O0  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` �/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> 2 iJ  std::initializer_list<nvrhi::IBindingSet *> " -  std::lock_guard<std::mutex> � u:  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >  �  std::hash<long double> 2 �1  std::equal_to<nvrhi::TextureSubresourceSet> � 緾  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � 嶤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W 5  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � �3  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o �.  std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  s:  std::try_to_lock_t � 1  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> H 鬒  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # �5  std::numeric_limits<wchar_t>  �  std::_Container_base0 R 揤  std::_Default_allocator_traits<std::allocator<donut::engine::ShaderMacro> >  �  std::hash<double> H U  std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> > O 	7  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & �8  std::bidirectional_iterator_tag . F%  std::hash<nvrhi::TextureSubresourceSet> D r:  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> " �,  std::_Align_type<double,72> x m:  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / �6  std::_Char_traits<char32_t,unsigned int>  �+  std::_System_error ( �%  std::hash<nvrhi::FramebufferInfo> 9 鶭  std::allocator<std::filesystem::_Find_file_handle>  N+  std::error_condition % h:  std::integral_constant<bool,0>  �  std::bad_exception 1 齌  std::allocator<donut::engine::ShaderMacro> & �.  std::_Zero_then_variadic_args_t � �0  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > >  �  std::u32string  �  std::_Fake_allocator / �  std::array<nvrhi::BindingLayoutItem,128>  D*  std::invalid_argument N c:  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � a:  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U 15  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >  �,  std::cv_status S P:  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R G/  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > + G  std::pair<enum __std_win_error,bool> � 9  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void>  !,  std::thread  ?,  std::thread::id S lB  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  [*  std::length_error F 錙  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � K  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! �5  std::numeric_limits<float>  褹  std::time_base   藺  std::time_base::dateorder ) ~  std::_Atomic_integral_facade<long>  �,  std::mutex % }2  std::hash<enum nvrhi::BlendOp>  q&  std::_Ref_count_base " +2  std::hash<unsigned __int64>  N:  std::ratio<60,1>  #  std::exception_ptr  L:  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > :  <  std::_Vector_val<std::_Simple_types<unsigned int> > ) q2  std::hash<enum nvrhi::BlendFactor> $ �5  std::numeric_limits<char32_t>  �*  std::once_flag  7+  std::error_code  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l h  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k d  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < 襌  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  SA  std::_Iosb<int>   OA  std::_Iosb<int>::_Seekdir ! MA  std::_Iosb<int>::_Openmode   KA  std::_Iosb<int>::_Iostate ! IA  std::_Iosb<int>::_Fmtflags # GA  std::_Iosb<int>::_Dummy_enum 7 J:  std::allocator_traits<std::allocator<char32_t> >  9  std::nano I 橵  std::allocator_traits<std::allocator<donut::engine::ShaderMacro> >  �  std::_Iterator_base0 � *6  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > M "3  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > jH:  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � 蠷  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  ]�  std::_Base128 1 �6  std::_Char_traits<char16_t,unsigned short> $ P%  std::hash<nvrhi::BufferRange> T 8C  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  R?  std::_Locbase<int> 9 紿  std::shared_ptr<donut::engine::CommonRenderPasses> ! �9  std::char_traits<char16_t> � �0  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<>  �  std::_Container_base12 W 縍  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  U+  std::io_errc  堿  std::ios_base  欰  std::ios_base::_Fnarray  擜  std::ios_base::_Iosarray  AA  std::ios_base::Init  4A  std::ios_base::failure  UA  std::ios_base::event E �2  std::_Constexpr_immortalize_impl<std::_Generic_error_category> ) �5  std::numeric_limits<unsigned char> � :/  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  |9  std::true_type   �5  std::numeric_limits<long> " w9  std::initializer_list<char>  9  std::_Invoker_strategy  	G  std::nothrow_t � n9  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > 禦  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �5  std::_Default_allocate_traits N C  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > >  �  std::random_device 3 _9  std::allocator_traits<std::allocator<char> > ! �5  std::numeric_limits<short>  u   std::_Vbase . d3  std::allocator<nvrhi::rt::GeometryDesc> # �,  std::unique_lock<std::mutex> ; ;  std::basic_string_view<char,std::char_traits<char> > �  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! A  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > < �%  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 E0  std::_String_val<std::_Simple_types<char16_t> > = Y0  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O PN  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P 軧  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > . O2  std::hash<enum nvrhi::TextureDimension> ! �&  std::_Shared_ptr_spin_lock  �  std::bad_alloc  �*  std::underflow_error B m2  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> J 酛  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D 蠮  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  軦  std::messages_base � U  std::_Compressed_pair<std::allocator<donut::engine::ShaderMacro>,std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> >,1>  r*  std::out_of_range # �5  std::numeric_limits<__int64> i SK  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  珸  std::ctype<char> R WV  std::_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> >  ;  std::memory_order ! /-  std::recursive_timed_mutex  ]9  std::ratio<3600,1> # e  std::_Atomic_storage<long,4> # �%  std::hash<nvrhi::BlendState> / 訯  std::shared_ptr<donut::vfs::IFileSystem>  S  std::atomic_flag f �/  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  �+  std::system_error < 36  std::_Default_allocator_traits<std::allocator<char> > W �6  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  [9  std::ratio<1,1> 3 ,J  std::initializer_list<nvrhi::BindingSetItem>   �8  std::forward_iterator_tag  �*  std::runtime_error ��0  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   	  std::bad_array_new_length ; �2  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> E x3  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > v W9  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > p �;  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>  �>  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z ZQ  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  L9  std::allocator<bool>  5  std::u16string ]�'  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> > B F.  std::initializer_list<nvrhi::RefCountPtr<nvrhi::ITexture> >  \  std::nested_exception  �  std::_Distance_unknown ( �5  std::numeric_limits<unsigned int> < 萇  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , %@  std::codecvt<char32_t,char,_Mbstatet> @ �2  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) "  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > ` 0  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ +  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> � �0  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & <9  std::initializer_list<char32_t> d $  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z �#  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 29  std::initializer_list<char16_t> % (9  std::initializer_list<wchar_t> C ?2  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' �5  std::numeric_limits<long double>  S+  std::errc ; {.  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > , EO  std::default_delete<std::_Facet_base>  �*  std::range_error  +&  std::bad_typeid > y2  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �,  std::_UInt_is_zero  9  std::ratio<1,1000000000>  /  std::allocator<char16_t> $ 蒍  std::default_delete<char [0]> . �-  std::vector<bool,std::allocator<bool> > ` �#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v c#  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy J 鞡  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  9  std::ratio<1,1000> � 9  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  �8  std::ratio<1,10000000> ; ,/  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  T?  std::_Crt_new_delete % �+  std::_Iostream_error_category2 * �8  std::_String_constructor_concat_tag d 嚨  std::mersenne_twister<unsigned int,32,624,397,31,2567483615,11,7,2636928640,15,4022730752,18>  麓  std::seed_seq  �/  std::allocator<char> G �2  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2>    std::nullptr_t & �8  std::random_access_iterator_tag 4 怚  std::shared_ptr<donut::engine::ShaderFactory> ; '2  std::_Conditionally_enabled_hash<unsigned __int64,1> R ?K  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  [&  std::bad_weak_ptr ) �5  std::numeric_limits<unsigned long>   0  std::_Atomic_padded<long> @ F  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> # �8  std::allocator<unsigned int>  ?  std::_Yarn<wchar_t> = V2  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  x  std::wstring  ご  std::_Signed128 ' �5  std::numeric_limits<signed char> � 鍯  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  -*  std::domain_error  �  std::u32string_view  �  std::_Container_base  �/  std::allocator<wchar_t> L �8  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ 72  std::hash<nvrhi::IResource *> 1 �%  std::hash<nvrhi::BlendState::RenderTarget>   �5  std::numeric_limits<char> D �8  std::_Default_allocator_traits<std::allocator<unsigned int> > 9 �)  std::chrono::duration<__int64,std::ratio<1,1000> >  !)  std::chrono::nanoseconds y �>  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? !)  std::chrono::duration<__int64,std::ratio<1,1000000000> > , EQ  std::chrono::duration_values<__int64>  �(  std::chrono::seconds 3 g)  std::chrono::duration<int,std::ratio<60,1> > 6 �(  std::chrono::duration<__int64,std::ratio<1,1> > s )  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   �8  std::chrono::steady_clock   �8  std::chrono::system_clock 6 |)  std::chrono::duration<double,std::ratio<60,1> > ; �)  std::chrono::duration<double,std::ratio<1,1000000> > >  *  std::chrono::duration<double,std::ratio<1,1000000000> > = �(  std::chrono::duration<__int64,std::ratio<1,10000000> > q �(  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 ;)  std::chrono::duration<int,std::ratio<3600,1> > 8 �)  std::chrono::duration<double,std::ratio<1,1000> > < �)  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �)  std::chrono::duration<double,std::ratio<1,1> > 8 Q)  std::chrono::duration<double,std::ratio<3600,1> >  丂  std::ctype_base  |D  std::filesystem::perms ' 馜  std::filesystem::directory_entry $ 丏  std::filesystem::copy_options ( lD  std::filesystem::filesystem_error 7 蜰  std::filesystem::_Path_iterator<wchar_t const *> ) B  std::filesystem::_Find_file_handle & 馎  std::filesystem::_Is_slash_oper . 餎  std::filesystem::_Should_recurse_result $ 獹  std::filesystem::perm_options 4 篎  std::filesystem::recursive_directory_iterator . 矰  std::filesystem::_File_status_and_error & aE  std::filesystem::_Dir_enum_impl 0 sE  std::filesystem::_Dir_enum_impl::_Creator @ yE  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! 婦  std::filesystem::file_type . 桬  std::filesystem::_Directory_entry_proxy " 滸  std::filesystem::space_info * 綞  std::filesystem::directory_iterator & �>  std::filesystem::file_time_type 0 頔  std::filesystem::_Recursive_dir_enum_impl ) E  std::filesystem::directory_options # 濪  std::filesystem::file_status u &D  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 怮  std::filesystem::_File_time_clock  罛  std::filesystem::path $ 7B  std::filesystem::path::format *   std::filesystem::_Normal_conversion \ Y4  std::_Uninitialized_backout_al<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > < 鳲  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , @  std::codecvt<char16_t,char,_Mbstatet>  �8  std::char_traits<char> � GK  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  +  std::error_category ) +  std::error_category::_Addr_storage ! �+  std::_System_error_message  �  std::_Unused_parameter " �2  std::hash<nvrhi::IShader *> h �/  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A w  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 IE  std::shared_ptr<std::filesystem::_Dir_enum_impl> � �0  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> 8   std::_Ptr_base<donut::engine::CommonRenderPasses> = 32  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q �1  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>  �?  std::_Codecvt_mode @ �6  std::_Default_allocator_traits<std::allocator<char16_t> > � =D  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > 0 k6  std::_Char_traits<wchar_t,unsigned short> ' r  std::array<enum nvrhi::Format,8> � �0  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > \ �4  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 �/  std::_String_val<std::_Simple_types<wchar_t> > < b0  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  �>  std::_Facet_base ' Y%  std::hash<nvrhi::BindingSetItem> " y6  std::_WChar_traits<wchar_t> 2 k@  std::codecvt<unsigned short,char,_Mbstatet> � 幍  std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253> # �+  std::_Generic_error_category = 痰  std::default_delete<donut::engine::FramebufferFactory>  46  std::streampos  �8  std::input_iterator_tag 2 軴  std::_Wrap<std::filesystem::_Dir_enum_impl> X MO  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> ^ �4  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > ' �2  std::hash<enum nvrhi::ColorMask>  �?  std::codecvt_base  闓  std::bad_function_call O L5  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > v 1  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> ; �8  std::allocator_traits<std::allocator<unsigned int> > ' 訥  std::hash<std::filesystem::path> " 泶  std::bernoulli_distribution . 状  std::bernoulli_distribution::param_type   2  std::hash<unsigned int> 7 �8  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers � �6  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m m(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � ;(  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy ,   std::uniform_real_distribution<float> F �8  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > q F�  std::unique_ptr<donut::engine::FramebufferFactory,std::default_delete<donut::engine::FramebufferFactory> > .   std::array<nvrhi::BindingLayoutItem,16> $ Z2  std::hash<enum nvrhi::Format>  �5  std::numeric_limits<int> Z 賂  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > p ═  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Reallocation_policy 2 �/  std::_String_val<std::_Simple_types<char> > 9 j0  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t # 4$  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �  nvrhi::BindingSetDesc  �8  nvrhi::SubresourceTiling " �  nvrhi::SamplerReductionType $ J  nvrhi::GraphicsPipelineHandle  w  nvrhi::ResourceType  u   nvrhi::ObjectType ) �   nvrhi::RefCountPtr<nvrhi::IShader>  �   nvrhi::InputLayoutHandle   �!  nvrhi::IndexBufferBinding   �8  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 8  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �  nvrhi::VulkanBindingOffsets  �  nvrhi::ResourceStates . �5  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  �!  nvrhi::GraphicsState * �8  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16> ! �  nvrhi::SharedResourceFlags  t  nvrhi::ShaderDesc  �"  nvrhi::IComputePipeline : �"  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  u  nvrhi::Rect  ^  nvrhi::BindingSetItem $ �  nvrhi::BindingLayoutItemArray ) "'  nvrhi::RefCountPtr<nvrhi::IDevice> !    nvrhi::ShadingRateCombiner  u   nvrhi::MipLevel  �!  nvrhi::IGraphicsPipeline ! q$  nvrhi::ShaderLibraryHandle  �  nvrhi::FramebufferInfoEx  �   nvrhi::IShader  �  nvrhi::TextureDesc 2 攡  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  t  nvrhi::ISampler ! �!  nvrhi::VertexBufferBinding ! r!  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �$  nvrhi::MeshletPipelineHandle  �  nvrhi::Format  �"  nvrhi::DrawArguments  �"  nvrhi::MeshletState  D  nvrhi::IBuffer  K  nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 �  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  �$  nvrhi::IDevice ! N!  nvrhi::BindingLayoutHandle ! �  nvrhi::BindingSetItemArray . �-  nvrhi::RefCountPtr<nvrhi::ICommandList>  "'  nvrhi::DeviceHandle   �8  nvrhi::TiledTextureRegion  �"  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & "   nvrhi::VariableRateShadingState  �8  nvrhi::IStagingTexture ! �-  nvrhi::utils::ScopedMarker $ C-  nvrhi::utils::BitSetAllocator . �   nvrhi::RefCountPtr<nvrhi::IInputLayout>  3  nvrhi::BufferRange !   nvrhi::VertexAttributeDesc " �  nvrhi::ShaderSpecialization 8   nvrhi::ShaderSpecialization::<unnamed-type-value>  �  nvrhi::TextureDimension 0 N!  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �"  nvrhi::DispatchIndirectArguments  �8  nvrhi::SamplerHandle * �"  nvrhi::DrawIndexedIndirectArguments # �$  nvrhi::DescriptorTableHandle  :  nvrhi::ShaderType  �$  nvrhi::TimerQueryHandle   �  nvrhi::BindlessLayoutDesc  T  nvrhi::CustomSemantic " @  nvrhi::CustomSemantic::Type ! �!  nvrhi::MeshletPipelineDesc 9 �  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  :$  nvrhi::HeapHandle # 攡  nvrhi::ComputePipelineHandle  }8  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice !    nvrhi::VariableShadingRate  �  nvrhi::IResource  :"  nvrhi::IBindingSet  y8  nvrhi::TileShape ; 0  nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - SJ  nvrhi::RefCountPtr<nvrhi::IBindingSet> * \$  nvrhi::SamplerFeedbackTextureHandle # �  nvrhi::SinglePassStereoState % �  nvrhi::ISamplerFeedbackTexture  ,$  nvrhi::CommandQueue  �  nvrhi::BlendFactor  y$  nvrhi::EventQueryHandle  �  nvrhi::BindingLayoutItem  �5  nvrhi::FramebufferHandle 1 �  nvrhi::static_vector<enum nvrhi::Format,8>  aI  nvrhi::BufferHandle  �  nvrhi::StencilOp  ]!  nvrhi::IBindingLayout  �  nvrhi::ColorMask  F  nvrhi::FramebufferInfo  �(  nvrhi::TextureHandle  u8  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  o8  nvrhi::IMessageCallback  .   nvrhi::PrimitiveType  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 J  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 1"  nvrhi::static_vector<nvrhi::IBindingSet *,5> " V   nvrhi::GraphicsPipelineDesc H +!  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) aI  nvrhi::RefCountPtr<nvrhi::IBuffer> $ Y8  nvrhi::TiledTextureCoordinate  U8  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  1"  nvrhi::BindingSetVector  SJ  nvrhi::BindingSetHandle ( N8  nvrhi::SamplerFeedbackTextureDesc ! +!  nvrhi::BindingLayoutVector " E$  nvrhi::StagingTextureHandle  �  nvrhi::Object  �   nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �  nvrhi::rt::AccelStructBuildFlags  �  nvrhi::rt::InstanceFlags " d  nvrhi::rt::IOpacityMicromap  �  nvrhi::rt::InstanceDesc  F  nvrhi::rt::GeometryFlags ! }  nvrhi::rt::GeometrySpheres # 8  nvrhi::rt::ShaderTableHandle + i  nvrhi::rt::OpacityMicromapUsageCount $ �"  nvrhi::rt::PipelineShaderDesc ! �  nvrhi::rt::AccelStructDesc   �$  nvrhi::rt::PipelineHandle ! �  nvrhi::rt::AffineTransform & #  nvrhi::rt::PipelineHitGroupDesc  �  nvrhi::rt::GeometryLss 3 D8  nvrhi::rt::cluster::OperationBlasBuildParams . @8  nvrhi::rt::cluster::OperationMoveParams ( :8  nvrhi::rt::cluster::OperationDesc 3 68  nvrhi::rt::cluster::OperationClasBuildParams , 28  nvrhi::rt::cluster::OperationSizeInfo * .8  nvrhi::rt::cluster::OperationParams  �  nvrhi::rt::GeometryType ' �$  nvrhi::rt::OpacityMicromapHandle  �  nvrhi::rt::GeometryDesc - �  nvrhi::rt::GeometryDesc::GeomTypeUnion % �  nvrhi::rt::OpacityMicromapDesc # V  nvrhi::rt::GeometryTriangles    nvrhi::rt::IAccelStruct # �$  nvrhi::rt::AccelStructHandle  $  nvrhi::rt::IShaderTable ' '$  nvrhi::rt::DispatchRaysArguments  $  nvrhi::rt::State  r  nvrhi::rt::GeometryAABBs  ##  nvrhi::rt::PipelineDesc  #8  nvrhi::rt::IPipeline    nvrhi::CpuAccessMode  �-  nvrhi::CommandListHandle # �"  nvrhi::DrawIndirectArguments ! 8  nvrhi::TextureTilesMapping  �  nvrhi::HeapDesc  @%  nvrhi::ICommandList  !  nvrhi::BufferDesc  8  nvrhi::IDescriptorTable * �(  nvrhi::RefCountPtr<nvrhi::ITexture>  �  nvrhi::BlendOp  �"  nvrhi::ComputeState  �!  nvrhi::IFramebuffer  �  nvrhi::ComparisonFunc  _  nvrhi::Viewport     nvrhi::RenderState  �   nvrhi::ShaderHandle  �  nvrhi::ITexture  8  nvrhi::ITimerQuery  1>  __std_win_error  %?  lconv   7  __RTTIBaseClassDescriptor 
    _off_t  �  stat  t   int32_t  0  timespec  pG  __std_fs_file_id 
 !   _ino_t 
 �,  _Cnd_t ' YG  __std_fs_create_directory_result  !   uint16_t  C>  __std_fs_stats $ H  donut::engine::ICompositeView  ?H  donut::engine::IView ( �7  donut::engine::CommonRenderPasses 5 �&  donut::engine::CommonRenderPasses::PsoCacheKey ; �&  donut::engine::CommonRenderPasses::PsoCacheKey::Hash $ �7  donut::engine::BlitParameters   fH  donut::engine::PlanarView ( �&  donut::engine::FramebufferFactory  哘  donut::engine::ViewType $ 
H  donut::engine::ViewType::Enum ! H  donut::engine::ShaderMacro # 荌  donut::engine::ShaderFactory " nH  donut::engine::StaticShader  繨  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  /  donut::math::float2 # �  donut::math::vector<float,3> ! #�  donut::math::vector<int,3>  u   donut::math::uint  �  donut::math::plane ! 蕕  donut::math::vector<int,4> # �  donut::math::vector<float,4>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes % 籮  donut::math::matrix<float,3,4>  �  donut::math::float4   d  donut::math::int2 % 繨  donut::math::matrix<float,4,4> # J  donut::math::affine<float,3> !  d  donut::math::vector<int,2>   �.  donut::math::box<float,2>   濲  donut::math::box<float,3> "   donut::math::vector<bool,2>  濲  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # /  donut::math::vector<float,2> 0 �  donut::render::TemporalAntiAliasingJitter 4 f�  donut::render::TemporalAntiAliasingParameters . %�  donut::render::TemporalAntiAliasingPass @ M�  donut::render::TemporalAntiAliasingPass::CreateParameters M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �7  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  c7  _s__RTTIBaseClassArray 
 �  ldiv_t  P>  __std_fs_file_flags  !?  _Cvtvec  u   _Thrd_id_t - )7  $_s__RTTIBaseClassArray$_extraBytes_24  E7  _CatchableTypeArray  U>  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  z7  _PMD      uint8_t  �%  type_info ' 7  _s__RTTIClassHierarchyDescriptor  t   errno_t  @>  __std_fs_reparse_tag  �  _lldiv_t  �%  __std_type_info_data & `7  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo  �>  __std_fs_convert_result  8>  __std_fs_stats_flags  c7  __RTTIBaseClassArray  �  __crt_locale_data_public - U7  $_s__CatchableTypeArray$_extraBytes_24  h,  _Mtx_internal_imp_t & ?7  $_TypeDescriptor$_extraBytes_25 % 7  __RTTIClassHierarchyDescriptor  ?  _Collvec   贔  __std_fs_volume_name_kind     __time64_t    FILE 
 \,  _Mtx_t 3 Z7  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  E7  _s__CatchableTypeArray  UG  __std_fs_remove_result  %,  _Thrd_t - 77  $_s__RTTIBaseClassArray$_extraBytes_16 - -7  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  3>  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  褾  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t  1  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers �   8      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽     L�9[皫zS�6;厝�楿绷]!��t  O   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �"睱建Bi圀対隤v��cB�'窘�n     鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  ^   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �     [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  [   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  %   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  e   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k     傊P棼r铞
w爉筫y;H+(皈LL��7縮  l   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  Q   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx     `k�"�1�^�`�d�.	*貎e挖芺
脑�  X   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   妇舠幸佦郒]泙茸餈u)	�位剎  �   靋!揕�H|}��婡欏B箜围紑^@�銵     �颠喲津,嗆y�%\峤'找_廔�Z+�  d   t�j噾捴忊��
敟秊�
渷lH�#  �   8僻�� L$烘錊�6敕秡敊1�+n夡s來  �   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  !	   �0�*е彗9釗獳+U叅[4椪 P"��  \	   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �	   �=蔑藏鄌�
艼�(YWg懀猊	*)  �	   �掆桄k覼濩FI痬�(�廿X~奞V筋脽�  #
   j轲P[塵5m榤g摏癭 鋍1O骺�*�  l
   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �
   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �
   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  ,   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  k   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   繃S,;fi@`騂廩k叉c.2狇x佚�  �   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  /   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  z   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�   
   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  C
   �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  �
   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �
   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �
   猯�諽!~�:gn菾�]騈购����'  ;   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   *u\{┞稦�3壅阱\繺ěk�6U�     険L韱#�簀O闚样�4莿Y丳堟3捜狰  >   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  ~   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     桅棙�萑�3�<)-~浰-�?>撎�6=Y}  V    狾闘�	C縟�&9N�┲蘻c蟝2  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   �'稌� 变邯D)\欅)	@'1:A:熾/�     馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  n   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  4   鹴y�	宯N卮洗袾uG6E灊搠d�  |   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠     
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  P   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   郖�Χ葦'S詍7,U若眤�M进`  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  -   G�膢刉^O郀�/耦��萁n!鮋W VS  l   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     $^IXV嫓進OI蔁
�;T6T@佮m琦�  =   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  {   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C     鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  B   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  H   +椬恡�
	#G許�/G候Mc�蜀煟-  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈     a�傌�抣?�g]}拃洘銌刬H-髛&╟  V   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  I   �暊M茀嚆{�嬦0亊2�;i[C�/a\  }   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5  Y    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  [   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   $澪饼q�=G栫	o\AO髛r�熌僴Q��  �   交�,�;+愱`�3p炛秓ee td�	^,  &   zY{���睃R焤�0聃
扨-瘜}  _   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4      副謐�斦=犻媨铩0
龉�3曃譹5D   b   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  )   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  u   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  
   f扥�,攇(�
}2�祛浧&Y�6橵�  H   曀"�H枩U传嫘�"繹q�>窃�8  �   j绅1ys帳鷙)裼]wMo x暅c�G-\昇E�  �   [届T藎秏1潴�藠?鄧j穊亘^a      噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  N    _O縋[HU-銌�鼪根�鲋薺篮�j��  �    _邙貧�3�"Ob切銣�2c�欕b"n2
  �    蜞憚>�/�狌b替T蕚鎸46槹n�洜9  4!   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �!   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �!   A縏 �;面褡8歸�-構�壋馵�2�-R癕  "   蜅�萷l�/费�	廵崹
T,W�&連芿  L"   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �"   D���0�郋鬔G5啚髡J竆)俻w��  �"   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  '#   dhl12� 蒑�3L� q酺試\垉R^{i�  f#   匐衏�$=�"�3�a旬SY�
乢�骣�  �#   チ畴�
�&u?�#寷K�資 +限^塌>�j  �#   悯R痱v 瓩愿碀"禰J5�>xF痧  1$   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  q$   矨�陘�2{WV�y紥*f�u龘��  �$   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �$   穫農�.伆l'h��37x,��
fO��  )%   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  a%   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  �%   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �%   �X�& 嗗�鹄-53腱mN�<杴媽1魫  '&   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  z&   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �&   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �&   鏀q�N�&}
;霂�#�0ncP抝  8'   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �'   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �'   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  (   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  ^(   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �(   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �   �
      f     B   g     H   h     Y   m     �   �  �  U   �  �  �   �  �  g   �    [   �    \   �  �  �  �  �  �  �  0  )   l  �  �  m  X  ,   p  �  `  q  �  j   r     >  u    �   {    �   |    �   }    �   ~    �   �  h  q   �    �   �  h  q   �  h  q   �  P  �  �  P  B  �  P  �
  �  P  �	  �  P  �	  �  �  �  �     4  �     u  �  p  �  �    �   �      �    �   �    �   �      �      �    �   �      �    �   �    �     P  �    P  �    P  @
    P  �    P  �    P  �    P  �    �  �      K                   �  �  #  p  "  $  �  1   (    �   )    �   *    �   A  P  �  B  P  D
  D  P  �  F  P  O   G  P  0   R  p  %   T     
  U  �  �  W  p  '  `  �  �  a  �  �  b  �  8   j  �  �   q  �  �   u  (  �  �    �   �  �  �  �  �  �  �  �  �  �  P  L
  �  P  L
  �  �  >  �       �  p  C  �  p  3  �  �  �  �  �  �   �  �  �  �  �  �  �  �  �  �  �  @   �  �  �   �  �  @   �  �  F    �  �    p  a  
  �  �      �   
    �       �     P  �    P  �  '  �  n   5  �  �  6  �  �  L  P  s  M  P  �  R  �  ]  \  �  �  c  (  �  u  �  G  w  �  �  �  P  )
  �  �  �  �  �  �  �  p  <  �  �  R  �  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  P  L
  �  �    �  �  �    �  �    �  �  5    �   8    �   9    �   >  �  �  ?    �   g    �   V    i   X    �   ^    )  `    n  a    w  c      d    L  e    [  7  �  Q   y      z    �   {    �   �      �    �   �      �    �   �  h  5   �  h  @   �  h  5   �  h  5   �    �   �      �    �   �    �   �    �   �  h  @   �  h  5   �  h  @   �  h  5   �  h  5   �  h  5   �  �  5     x   �       �     x   �   &      '    �   (    �   *      +    �   ,    �   /      0    �   1    �   2  �  t  :  h  q   <  �    \  x   d  ]  x   �   u    �   �  x   �   �  �  :   �  �  6   �  �  4   \    �   ^    �   >  �  �  @  	  2   D  �  j  E  �  �  F  �  `  G  �  �  J  �  Z  Z  �  �  [  �  t  i  �  �  k  �  �   l     T  m     	  n  x   9  p  �    q  �  �  s  �  D  y  �  >  ~  �  �  �  x   �  �  x   �  �  �  �   �  �  n  �  x   ^  �  �    �  �  �  �  �  �  �  �  ]  �  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �    �  �  @   �   �  Q   �   x   �  d!  �  �   �#      �#    �   �#    �   �#      �#    �    $    �   Y$    %  s$  h  x   t$  h  x   5&      6&    �   7&    �   H&  �  �   K&      L&    �   M&    �   �'    �   �+  �  �   e-  �  �   o-  �  �   �-  x   9  �-  x   k  �-  x   �   �-  x   �   �-  x   �   1  (  �  1    |   1    }   [1  �    s1  �  �
  t1  �  ]  w1  �  `  x1  �    y1  �    �1  P  <
  �1  �  �
  �1  �  g  �1  �  �
  �1  �  �  �1  �  �  �1  �  �
  �1     �   �1  �  S   �1  �  �   �1  �  �   �1    �   �1  �  �
  �1  P  �  �1  �  �  �1  �  �  �1  �  �  �1  �  �  �1    �   �1  �  �   �1  �  �   �1  �  �   �1  �  !  �1    �  �1    �  �1  �  �  �1    �   �1  �  �   �1  �  0  �1  �    �1  �  �  �1  �  �  �1  �  �  �   r)   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\RTXPT\External\Donut\src\render\TemporalAntiAliasingPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\render\TemporalAntiAliasingPass.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\RTXPT\External\Donut\include\donut\shaders\taa_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\random D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_int128.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_render.dir\Release\TemporalAntiAliasingPass.obj   �       L�1  wh     {h    
 俲     唈    
 韘      駍     
 鋣      鑩     
    j 5N章紩B宾蕿M潈   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_render.pdb 裥砓>Y7?樰�=      �?                  �?                  �?    H塡$H塴$H塼$H墊$ AVH冹 E3�W繧孁H嬯H嬞L塹L塹I;衭H茿   D�1槔   H+鼿�������H;�囋   H茿   H�wL嬊H墆�    D�4閲   H嬊H內H;苬)H�       �H兝'H嬋�    H吚t~L峱'I冩郔塅4�   H嬸H;罤B馠峃H侚   rH岮'H;羦Q刖H吷t�    L嬸L嬊L�3H嬚H墈I嬑H塻�    A�> H媗$8H嬅H媆$0H媡$@H媩$HH兡 A^描    惕    惕    蘵   n   �   �   �   �      n   (  �   .  �   4  �      �   V  � G            9     9  d        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0> 
 >	   this  AI  )     �   AJ        )  >p   _First  AK        &  AN  &     �   >p   _Last  AP        8p  �  � E 2  AP �     <    >   _Al  AQ        8y 	 �  � E 2  AQ �     <    DH    M        �  # M          ������ M        M  ) N N N M          9 Ni M        �  L%


#?
 Z   �   >#   _Count  AM  #     �   >#     _New_capacity  AH  �       AJ  �     h  E  AL  V     � B  t i  AH �       AJ �     _  L  AL 	     & M        �  ��	*
I >p   _Fancy_ptr  AV �     ;  Cn           e  Cn     �     � + 
 �    M          ��.
I  M        >  ��.
I. M        �  ��.		

D/ M        �  ��(%"
P	 Z   q  }   >#    _Block_size  AH  �     [  O  AH �       >#    _Ptr_container  AH  �     �  p  AH �      
 >`    _Ptr  AV  �       AV �     ;  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N N N N M          ��5 M        L  ��*) >#    _Masked  AH  �     _   N   AH �       M          �� N N N M        G   m N M        G   �� N N                       @ z h   �  �  �  �        C  E  G  �  �  �        L  M  e  �  �  �  �  �  �    >  @  X         $LN71  0   	  Othis  8   p  O_First  @   p  O_Last  H     O_Al  O  �   P           9  P     D       
 �4   
 �9   
 �D   
 �I   
 �	  #
 �'  
 �,   +   0   +  
 �   +   �   +  
 �   +   �   +  
   +     +  
   +   #  +  
 C  +   G  +  
 c  +   g  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +      +  
   +     +  
 (  +   ,  +  
 8  +   <  +  
 L  +   P  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
 �  +   �  +  
   +     +  
 $  +   (  +  
 �  +   �  +  
 �  +   �  +  
   �     �  
 l  +   p  +  
 3繪+罤�L嬞H堿H兟H堿A�   H堿堿 H嬃I岺D  A�   ff�     驛 �YBX � 驜L �YJX润��Y�X馏 H兝I冮u綡兟H冮I冭I冴u淚嬅�   �   @  I G            �       �   �-        �donut::math::operator*<float,3,3,3> 
 >:   a  AK         
 >:   b  AP          M        �-     M        �-   (
 N N                        H & h   &  '  O  P  �  �-  �-  �-      :  Oa     :  Ob     腏  Oresult  O�   `           �   x   	   T       9 �    : �   9 �   : �0   < �@   > ��   ; ��   ? ��   @ �,   .   0   .  
 k   .   o   .  
 �   .   �   .  
 T  .   X  .  
 H塡$WH冹@M嬋)t$0H孂荄$     H�$M嬝L+蒐岯W繦�$W蒆嬟$I峇A�   L$D  驛X   驛`A(ff�     (�(捏BY(腕BYD�X�Y�X畜X洋H兝H冮u薎兝H冴I冮I冴u欝s(�S$(铙AYk(迡D$ �c,(麦AY(蘃媆$P驛YK驛Ys�X梵AY[(麦AYC驛YS�X�(腆AYc 驛YK�X伢AXk$$�X蝮X�L$驛X[(�X�O塆 H嬊塍AXs,�o$�w,(t$0H兡@_�   �   4  E G            n     �   j-        �donut::math::operator*<float,3> 
 >�   a  AI  8     �  AK        8 
 >�   b  AP        $  AS  $     J% M          ��[

	 N M        j  �" N* M        �-  ��6@ N- M        �-  


5 M        �-  


' M        �-  


 N N N @                     @ > h   �  j  %  &  '  O  P    �  �-  �-  �-  �-  �-   X   �  Oa  `   �  Ob  P   翵  Oresult  O�   �           n     
   t       �  �
   �  �   �  �   �  �!   �  �$   �  �5   �  �8   �  ��   �  ��   �  �M  �  �P  �  �c  �  �,   !   0   !  
 g   !   k   !  
 w   !   {   !  
 �   !   �   !  
 �   !   �   !  
 H  !   L  !  
 H塡$3繪+罤�L嬞H堿H兟H堿�   H堿H堿 H堿(H堿0H堿8H嬃I岺0@ A�   ff�     驜 �YBX � 驜L �YJX润�T痼Y�X洋��YB�X麦 H兝I冴u獺兟H兞餓冭H冸u塇媆$I嬅�   �   B  I G            �      �   n        �donut::math::operator*<float,4,4,4> 
 >T   a  AK         
 >T   b  AP        
  M        �  , M        �  (- N N                        H & h   �  �  �  �  �  �  �  �      T  Oa     T  Ob     燡  Oresult  O  �   X           �   x      L       9 �   : �
   9 �   : �@   < �P   > ��   ; ��   @ �,   $   0   $  
 k   $   o   $  
 �   $   �   $  
 X  $   \  $  
 H冹�bH嬃�j(泽AYP(腕AYH)4$�2(�(摅AY 驛Yp驛YX�X�(捏AY`驛Y@�X�(腕AYh �X趔AYH�X伢�X躞X袤q(4$�YH兡�   �   �   C G            �   '   �   �-        �donut::math::operator*<float> 
 ><   a  AK        � 
 >:   b  AP        �                        H 
 h   %   (   <  Oa  0   :  Ob      �  Oresult  O�   8           �   x      ,       k �   m �	   p ��   q �,   -   0   -  
 e   -   i   -  
 �   -   �   -  
 �   -   �   -  
 �H嬃�X��J�XI�I�B�XA�A�   �   �   D G            .       -   d!        �donut::math::operator+=<float> 
 >o   a  AJ        . 
 ><   b  AK        .                         H     o  Oa     <  Ob  O�               .   �            �  �,   4   0   4  
 f   4   j   4  
 �   4   �   4  
 �   4   �   4  
 �H嬃�X��J�XI�I�B�XA�A�J�XI�I�   �   �   D G            =       <   �1        �donut::math::operator+=<float> 
 >篔   a  AJ        = 
 >�   b  AK        =                         H     篔  Oa     �  Ob  O�               =   �            �  �,   0   0   0  
 f   0   j   0  
 �   0   �   0  
 �   0   �   0  
 �H嬃�Q�^馏^洋�A�^馏Q�A�   �   �   D G            ,       +   �1        �donut::math::operator/=<float> 
 >o   a  AJ        , 
 >@    b  A�         ,                         H     o  Oa     @   Ob  O�               ,   �            �  �,   3   0   3  
 f   3   j   3  
 �   3   �   3  
 �   3   �   3  
 (罤嬃	评 ^�	�   �   �   D G                      �1        �donut::math::operator/=<float> 
 >篔   a  AJ         
 >@    b  A�         	                         H     篔  Oa     @   Ob  O�                  �            �  �,   /   0   /  
 f   /   j   /  
 �   /   �   /  
 �   /   �   /  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z   q  }   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   2   0   2  
 �   2   �   2  
 �   2   �   2  
 �   2   �   2  
   2     2  
 s  �   w  �  
 �  2   �  2  
 H塡$H塴$H塼$WAVAWH冹 I嬭H嬺L嬹H�9H婣H+荋柳L;纕BI嬓�    I�H呿劎   H+�D  H�H�H吷tH��P怘兠H冺u鉏塣轭   H媃L孄L+�I�I;飗lH;鹴<f怘�H9t!H呟t
H�H嬎�P怘�H�H吷tH��P怘兦H兤I媈H;鹵艻+飔 H+驢�H�H吷tH��P怘兠H冺u鉏塣雘N�<荋呿t9f怘�H9t!H呟t
H�H嬎�P怘�H�H吷tH��P怘兦H兤H冺u蒊媬I嬤L;�t3鯤�H吷t
H�3H��P怘兠H;遳錗墌H媆$@H媗$HH媡$PH兡 A_A^_�8         �   
  � G            }     d  �        �std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Assign_counted_range<nvrhi::RefCountPtr<nvrhi::ITexture> const *> 
 >(   this  AJ        !  AV  !     Z >�'   _First  AK         7 ? � e  AL  �     	 . AK �     �   (  1 	 T  e  �  �  �   AL �     � 1 	 B  e o  >#   _Newsize  AP         < : � e * AP �     �   (  T  e  �  �  �   >#    _Oldcapacity  AH  (     �    : h e * AH �     �   %  Q  e  �  �  �   >#    _Oldsize  AW  }     |   r   AW �     �  o 
 >�'    _Mid  AM  �     
  AM �     � - 
 e o  >\(    _Newlast  AW  �     k  AW d      M        u  .	? >#    _Count  AN  k       AN P      �  >Y4   _Backout  CI      ?       CI     g       CI     P      � � o  CI    P      �  M        �  P M        �  P M        }  P M        �  W N N N N N M        {  �� M        |  �� M        �  ��
 >�    temp  AI  �     2  N N M        �  �� N M        }  �� M        �  ��#	 N N N  M        u  .�� >#    _Count  AN  �     "  AN �       o  >Y4   _Backout  CI     �       CI    �     �   # o  M        �  �� M        �  �� M        }  �� M        �  �� N N N N N M        �  �>	 >�'   _First  AI  >    &  AI d      >\(   _Last  AM  ;    )  AM d      M        �  丒 M        |  丒 M        �  丒CE
 >�    temp  AJ  H      AJ E    8    N N N N! M        w   �� >�'   _First  AL       '- + r e  AL �     � 1 	 B  e k  >#    _Count  AN       I5 & � (  AN P      � � o  >�'   _Dest  AM  $     l e  AM �     � - 
 e o  M        {  �  M        |  � M        �  �
 >�    temp  AI      4  AI      >  4  N N M        �  � N M        }  � M        �  �#	 N N N N
 Z                           0@ b h   B  �  {  |  }  �  �  �    �  t  u  v  w  x  �  �  �  �  �  �  �  �   @   (  Othis  H   �'  O_First  P   #  O_Newsize  9_       �   9�       �   9�       �   9�       �   9      �   9%      �   9S      �   O  �   �           }  �     �       c �!   o �/   p �4   q �<   s �H   q �P   s �m   � �q   y �v   | ��   } ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �7  � �`  � �,   &   0   &  
 �   &   �   &  
   &   	  &  
 &  &   *  &  
 >  &   B  &  
 N  &   R  &  
 ~  &   �  &  
 �  &   �  &  
 �  &   �  &  
   &     &  
 $  &   (  &  
 c  &   g  &  
 {  &     &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
 +  &   /  &  
 ;  &   ?  &  
 f  &   j  &  
 z  &   ~  &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
 4  &   8  &  
 D  &   H  &  
 o  &   s  &  
 �  &   �  &  
 E  &   I  &  
 U  &   Y  &  
 u  &   y  &  
 �  &   �  &  
 �  &   �  &  
 �  &     &  
 V  &   Z  &  
 n  &   r  &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
 �  &   �  &  
 c  &   g  &  
 s  &   w  &  
 �	  &   �	  &  
 �	  &   �	  &  
 �	  &   �	  &  
 �	  &   �	  &  
 �	  &   �	  &  
 
  &   

  &  
 
  &   
  &  
 0
  &   4
  &  
 H塡$H塴$H墊$ AVH冹 H�������I嬝H嬯L嬹L;�囎   H茿   H凔wH塝�    B�3 閿   H嬅H塼$0H內H;莢)H�       �H兝'H嬋�    H吚t~H峱'H冩郒塅8�   H孁H;罤B鵋峅H侚   rH岮'H;羦Q刖H吷t
�    H嬸�3鯨嬅I�6H嬚I塣H嬑I墌�    � H媡$0H媆$8H媗$@H媩$HH兡 A^描    惕    惕    藾   n   u   �   �   �   �   n   �   �     �   	  �      �   g  � G                   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *> 
 >	   this  AJ        (  AV  (     � �  
 >C   _Arg  AK        %  AN  %     � �   >#   _Count  AI  "     � �   AP        "  >#     _New_capacity  AH  m       AJ  �     h   % >  AM       � N  � i  AH q       AJ q     c  P  AM �      % M        �  q	*I >p    _Fancy_ptr  AL  �       AL �     "  M          q.I M        >  q.I/ M        �  q.		
%
:. M        �  q(%"
P	 Z   q  }   >#    _Block_size  AH  �     [  O  AH q       >#    _Ptr_container  AH  y     �  p  AH �      
 >`    _Ptr  AL  �       AL �     "  M        �  q
 Z   �   N N M        �  ��
 Z   �   N N N N N M          R2! M        L  R') >#    _Masked  AH  ^     f   N  _   AH �       M          �� N N N M        G   C N M        G   �� N
 Z   �                         H N h   �  �  �      E  G  �  �      L  �  �  �    >  X         $LN56  0   	  Othis  8   C  O_Arg  @   #  O_Count  O �   �             P     �       L
 �   V
 �1   ^
 �?   _
 �C   f
 �H   g
 �M   q
 �R   u
 �q   v
 ��   u
 ��   v
 ��   
 ��   w
 ��   
 ��   y
 ��   
 ��   z
 ��   
 ��   �
 ��   �
 ��   v
 �  W
 �,   '   0   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
   '     '  
 '  '   +  '  
 O  '   S  '  
 _  '   c  '  
 w  '   {  '  
 �  '   �  '  
 �  '   �  '  
 �  '   �  '  
 �  '     '  
   '     '  
 �  '   �  '  
 �  '      '  
 %  '   )  '  
 9  '   =  '  
 X  '   \  '  
 h  '   l  '  
 '  '   +  '  
 C  '   G  '  
 '  �   +  �  
 |  '   �  '  
 H;蕋+H塡$WH冹 H孃H嬞H嬎�    H兠@H;遳颒媆$0H兡 _�         �   �  f G            1      1   y        �std::_Destroy_range<std::allocator<donut::engine::ShaderMacro> >  >oT   _First  AI         AJ          AJ 0       >颎   _Last  AK          AM         AK 0       >蠺   _Al  AP          AP          D@   
 Z   B                         H�  h   �  �  �   0   oT  O_First  8   颎  O_Last  @   蠺  O_Al  O�   @           1   �     4       > �    B �   > �   B �&   F �,   (   0   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
 �   (   �   (  
   (     (  
   (     (  
 �  (   �  (  
 L塂$H塗$H塋$SVWATAUAVAWH冹@L嬕H嬹H�L孃L+鳯媋L+郔咙I�������M;��  I�腍婭H+菻六H嬔H殃I嬃H+翲;葀L墝$�   I瞧���I峃'�<H�
I嬡I;腍C豂;�囁  L嬻I伶H墱$�   I侢   r@I峃'I;�啨  �    H吚劋  H峹'H冪郒塆鳫墊$ 3繪嫈$�   L媱$�   �:M咑t$I嬑�    H孁H塂$ 3繪嫈$�   L媱$�   �	3缷鳫塂$ H墱$�   I冪繫�,?I峕@H塡$0W繟E I塃I塃A AE AHAMI堾I茾   A�  W繟E I塃0I塃8A@ AE AH0AM0I堾0I茾8   A艪  L塴$(H媀H�L;襲H嬤�L嬑L嬊I嬕�    H墊$(H媽$�   H媀L嬑L嬅�    怘�H呟tNL媬I;遲H嬎�    H兠@I;遳颒�H媀H+袶冣繦侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�>I龄L鏛塮I�>H塏I嬇H兡@A_A^A]A\_^[描    惕    惕    碳   �   �   �   �  6   �  6   �     4  �   b  �   h     n  �      �   �
  � G            s     s  �        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Emplace_reallocate<donut::engine::ShaderMacro> 
 >lT   this  AJ        $  AL  $     O;  D�    >颎   _Whereptr  AK        !  AR  !     K� $ �  �� E  AR �      D�    >餑   <_Val_0>  AP        l� , �  �� f  AP �      D�    >#     _Newcapacity  AI  �     �5 3 � 9 AJ  }       AI �     �3 : y 9 AJ �       B�   r     
 & �  >#    _Newsize  AT  N     %�"   >#    _Whereoff  AW  *       >颎    _Constructed_last  AU  0    	  D0    >#    _Oldsize  AT  1     <    >oT    _Constructed_first  D(    >颎    _Newvec  AM  �     *    AM (    K6  D     M        �  �潃�佫 M        �  �潃�佫& M        �  ��)
@%	$丣( M        �  ��$	%)
亹
 Z   }   >#    _Block_size  AJ  �     	  AJ �     � � >#    _Ptr_container  AH  �       AH m     
 >`    _Ptr  AM  �       AM (    K6  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        �  
��
 N N N M        �  Nk >#    _Oldcapacity  AJ  R     �   +  ` < � !  AJ      F� �  >#    _Geometric  AH  �     �8 3 x  � H AH �       M        �  N N N M        �  0�<23 M        �  乷%
 M          0亅 M          亹$ N M        A  亅 N N M        �  
乷 M          乷�� M        M  乷 N N N N M        �  �<#
 M          0両 M          乗$ N M        A  両 N N M        �  
�< M          �<�� M        M  �< N N N N N, M        �  佲	I4#' M        i  *�_ M        �  �):
 Z     
 >   _Ptr  AJ 3      >#    _Bytes  AK      -    AK m     % M        �  �d#
=
 Z   q   >#    _Ptr_container  AP  #      AP 3    ?  5  >#    _Back_shift  AJ      ,  AJ 3    ?  5  N N N M        y  侇	
 >oT   _First  AI  �    {  AI m      >颎   _Last  AW  �    i  AW m      N N Z   �  �  �   @           8         0@ � h*   B  �  �  �  �  �        @  A  C  D  E  G  �  �  �  �  �  �  �    M  �  �  h  i  y  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN140  �   lT  Othis  �   颎  O_Whereptr  �   餑  O<_Val_0>  0   颎  O_Constructed_last  (   oT  O_Constructed_first  O�   �           s  �     �       * �$   3 �-   4 �8   6 �K   : �N   ; ��   = �   > �<  B ��  C ��  E ��  G ��  K ��  L ��  M ��  N ��  V �N  W �Q  X �a  = �g  7 �m  V ��   �  � F            C      C             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Emplace_reallocate<donut::engine::ShaderMacro>'::`1'::catch$7 
 >lT   this  EN  �         C  >颎   _Whereptr  EN  �         C  >餑   <_Val_0>  EN  �         C  Z   y  i   (                    � �        __catch$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z$0        $LN140  �   lT  Nthis  �   颎  N_Whereptr  �   餑  N<_Val_0>  0   颎  N_Constructed_last  (   oT  N_Constructed_first  O�   8           C   �     ,       P �   Q �"   R �9   S �,   1   0   1  
 �   1   �   1  
 �   1   �   1  
   1     1  
   1   #  1  
 ?  1   C  1  
 j  1   n  1  
 �  1   �  1  
 �  1   �  1  
 �  1   �  1  
 �  1   �  1  
 �  1   �  1  
 	  1   
  1  
 4  1   8  1  
 `  1   d  1  
 �  1   �  1  
 �  1   �  1  
   1   
  1  
   1     1  
 �  1   �  1  
 �  1   �  1  
   1   !  1  
 -  1   1  1  
 L  1   P  1  
 \  1   `  1  
    1   $  1  
 @  1   D  1  
 i  1   m  1  
 �  1   �  1  
 �  1   �  1  
 �  1     1  
   1     1  
 m  1   q  1  
 }  1   �  1  
 �  1   �  1  
 �  1   �  1  
 	  1   	  1  
 	  1   	  1  
 1	  1   5	  1  
 A	  1   E	  1  
 7
  �   ;
  �  
 �
  1   �
  1  
 �  8   �  8  
 {  8     8  
 �  8   �  8  
 �  8   �  8  
 
  �   
  �  
 �
  �   �
  �  
 x  8   |  8  
 H塗$SUH冹(H嬯L媴�   H婾0H婱(�    L媴�   H婾 H媿�   �    3�3设    �   (   5      >   g   �       7      �     � G                       �1        �std::_Nrand_impl<float,std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253> >  >钡   _Gx  AJ         
 Z   �1                          H     钡  O_Gx  O   �   (              �            0 �    8 �,   5   0   5  
 �   5   �   5  
 0  5   4  5  
 D嬄@ f�     H�筛吞烫A鬣陵堵类D�E蒃*罙��0D�D嬄呉u訦嬃�   �     P G            @       ?   c        �std::_UIntegral_to_buff<char,unsigned int>  >p   _RNext  AJ        @  >u    _UVal  A           A         >u     _UVal_trunc  Ah       = + 
                         H     p  O_RNext     u   O_UVal  O�   H           @   (     <       � �   � �5   � �8   � �<   � �?   � �,   *   0   *  
 w   *   {   *  
 �   *   �   *  
 �   *   �   *  
 �   *   �   *  
 ,  *   0  *  
 @SH冹PH�    H3腍塂$HD嬄H塋$(H嬞L峊$Efff�     I�矢吞烫A鬣陵堵类�蒁*罙��0E�D嬄呉u諰峀$ I嬕L岲$EH嬎�    H嬅H婰$HH3惕    H兡P[�	   z   k   +   {   l      �   |  R G            �      r   u        �std::_UIntegral_to_string<char,unsigned int> 
 >�   _Val  A         0  A  0       >�1    _Buff  D0    M        c  20# >p   _RNext  AR  %     J  >u     _UVal_trunc  Ah       O 4 
  N
 Z   d   P                     I  h   t     c  
 :H   O  h   �  O_Val  0   �1  O_Buff  O�   8           �   (     ,       � �    � �Z   � �r   � �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 I嬂H;�剰   L嬋L岮8L+蒃3��    W繫岪@ O塗業岺萇塗燗@� AH�W�HM塒楬兝@I茾�   E圥圕D∣塗窸塗繟@–DˋH窩L窶塒窱茾�   E圥℉;蕌吤   �   {  � G            �       �   �        �std::_Uninitialized_move<donut::engine::ShaderMacro *,std::allocator<donut::engine::ShaderMacro> >  >颎   _First  AJ           AJ       |  h  >颎   _Last  AK        �  >oT   _Dest  AP          AP �       >蠺   _Al  AQ          AQ �       D     >WV   _Backout  CH     T     G  CH          | 4 G  M        �  
 N# M        �  #(B M        �  'B M        �  `& M          0p M          ��$ N M        A  p N N M        �  ` M          `�� M        M  ` N N N N M        �  '
 M          0
8 M          L( N M        A  
8 N N M        �  ' M          '��	 M        M  ' N N N N N N                        @ � h"   B  �  �  �        @  A  C  D  E  G  �  �  �  �  �    M  �  o  y  �  �  �  �  �  �  �  �  �  �  �      颎  O_First     颎  O_Last     oT  O_Dest      蠺  O_Al  O �   X           �   �     L       � �   � �   � �#   � �/   � �3   � ��   � ��   � �,   6   0   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
   6     6  
   6     6  
 1  6   5  6  
 A  6   E  6  
 p  6   t  6  
 �  6   �  6  
 �  6   �  6  
 H塡$3跮峇L岮L岼D峓f�     A婣鳰峈A堾鳰岪A婣麺岻A堾霢婣鬉堾餉塟餓冸u覌B$H媆$堿0婤(堿4婤,堿8H嬃茿<  �?�   �   �   O G            p      Q   m        �donut::math::affineToHomogeneous<float,3> 
 >�   a  AK        p                         H " h   &  O  H  �  �  �  �      �  Oa     燡  Oresult  O�   @           p         4       	 �     �D    �N    �Q    �,   #   0   #  
 q   #   u   #  
 �   #   �   #  
 H冹�A簆  L嬌W葾;襲JL岮D  A婸麬3M岪候A3P鴭�$鲐裳陙徇�橝3�,  3蔄増�	  I冴u茿�殓   侜�  傐   L崙�	  H�$E�汇   A嬅E媄嬓M峈A3�候3袐�$鲐裳陙徇�橝3�0  3蔄墛<��H冸u腗崙P
  粚  E�D  A嬅E媄嬓M峈A3�候3袐�$鲐裳陙徇�橝3姲��3蔄墛<��H冸u腁媮�  嬓A3QH�$候3袐�$鲐裳陙徇�橝3�4  3�3褹墘�	  A�嬄W繟婰�岯A�嬃凌A#亜  3葖�%璛:�拎3葖�%屵��拎3葖亮�3馏H*荔\馏X馏Y    H兡脰  d      �   �  � G            �     �  �1        �std::generate_canonical<float,24,std::mersenne_twister_engine<unsigned int,32,624,397,31,2567483615,11,4294967295,7,2636928640,15,4022730752,18,1812433253> >  >钡   _Gx  AJ          AQ       �
 >@     _Ans  A�   �     ? M        �1  	97SXx+.,
 >u     _Res  A   �      A   R    3  M        �1  )&
 >�    _Tmp  A   $         N5 M        �1  $m(&&
 >�    _Tmp  A   �        
 >�    _Tmp  A   �        
 >�    _Tmp  A           N N                       H  h   �1  �1  �1      钡  O_Gx  O   �   H           �  �     <        �     �    �     ��  $ ��  % �,   7   0   7  
 �   7   �   7  
 �   7   �   7  
   7     7  
 v  7   z  7  
 �  7   �  7  
 �  7   �  7  
   7   #  7  
 B  7   F  7  
 e  7   i  7  
 �  7   �  7  
 H嬆H塜WH侅�   )p鐷孂)x豀峀$ D)@菻嬟D)H窪)P―)X樿    驞K,L崪$�   驞C$H嬊驞[(�    L$ T$0DW谼W菵W繣(芋D$@A(塍DYT$0A(Y|$$A(痼Yt$(A(袤Yl$4A(狍Yd$<驞X�W�G �X躞Y伢DX泽DY�(馏X�屏�驞Y�埔DY鼠EX伢EX袤D_$驞W(�w,I媅A(s餉({郋(C蠩(K繣(S癊([營嬨_�7   ,   \   �      �     C G            1  6   A   �1        �donut::math::inverse<float,3> 
 >�   a  AI  '     �  AK        '  >u   mInverted  C�       e     �  C�      j     �  D     M          
`XA N" M        �-  v>J
 N  M        k  ;	l	 M        �  ��	 >@    _x  A  v     `  >@    _y  A  n     x  >@    _z  A  r     }  N N
 Z   �1   �                     H  h   �  %    k  �-   �   �  Oa      u  OmInverted  �   翵  Oresult  O   �   X           1        L       �  �   �  �;     �A    �I     �O    �R     �
   �,      0     
 e      i     
 u      y     
 �      �     
 �      �     
 X     \    
 u     y    
 �     �    
 4     8    
 H嬆H塇USVWATAUAVAWH峢侅�   E3晒   J�   荅�  �?�%    A孂)p‥峺�5    E嬔)x楨嬞�=    D)@垕B 驞    塃疕岴廐塃H岴�E廐+豅塎o(    M烪塎wE�(�E�D  E峚H嬜L岹A凕�+  A�屲   H�9M嬎H峂汬�丄嬆餍L峬汳炅�I鬏�繢嬸E�$��    �A鬕��L�T�T蘃嬺/羦N�)�	K��D廔嬓T�T蘃F�/葀L岻鬖薎峆�IK��D�T�T�/葀I峆L��IK��D�T�T�/葀L岻L薎峆I兝H兞0I冾匸���A凕}A�   A+腖c菾�GI嬂I润L崗H�WH�T腆D崗T�/菻F翴�繦嬓I冮u蔐婱oH�WH鼠D崗T�/�嘪  H;譼\駼L廐�R�D晱B婰楎BD弸D晽�D暦B塂楎L晱駼L穳L晽B婰框BD穻D暱B塂框L暦塋暱H婨�.讂tl驜D忬BL楏^麦^鼠BD忬BD擉^麦BL楏BL惑BD擉BD敷^麦^鼠BD敷BD矿^麦BL惑BD緼�劯   驜l�(�T�/�啒   驜T揂W梵B\�(朋BYD忬M忬Y阵X润Y蒹E擉X麦BT惑Y阵M忬M敷E擉E楏X皿B\矿Y蒹E�(朋BYD敷X润E惑X麦M敷E惑E矿X皿E緼�劮   驜l�(�T�/�啒   驜T揂W梵B\�(朋BYD忬M涹Y阵X润Y蒹E燇X麦BT惑Y阵M涹M皿E燇EｓX皿B\矿Y蒹E�(朋BYD敷X润E求X麦M皿E求E梭X皿E薊�劗   驜l�(�T�/�啒   驜T揂W梵B\�(朋BYD忬MY阵X润Y蒹EX麦BT惑Y阵MM象EEX皿B\矿Y蒹E�(朋BYD敷X润E芋X麦M象E芋E左X皿E譎婱wA�罤兞L塎oH兝H塎wA�螲塃H�荋冸I兟I兠A��弇��H婱g婨�E�M�I堿 H嬃隑H婨g�   �茾  �茾  �茾  �茾  �茾  �茾  �茾  �茾   �(�$�   (|$pD(D$`H伳�   A_A^A]A\_^[]�=   �   P   g   b   v   s   �   �   }      �   �  C G            �  k   �  �1        �donut::math::inverse<float,3> 
 >:   m  AK        �  AK �      
 >u    b  D0   
 >u    a  D   
 >t     j  Ai      N  Ai �     & � : cN 
 >t     i  Al  �     �  Al �      �  >@     scale  A�       �� + E*  A�  �     F� �� �� + M          $		+ M        ]  $		; M        �  $		 N N N3 M        b  � C
 N M        '  伣 N* M        b  �			#			P N M        '  � ,"W N M        �-  伷 N M        �-  伓 N M        b  	侘 N M        '  侓 N M        �-  侅 N M        �-  卆 N" M        �1  �/%

 >�   _Tmp  C      P    M C     �    s N# M        �1  �

 >�   _Tmp  C      $    ,  N M        �-  � N M        �1  偍/ N M        �1  /倅6 N M        b  
傦
��
�� N, M        q  �;#
��#
��#
 NE M        d!  �!		
p		
o		
 N5 M        q  ����� N# M        d!  僈%-��%-��%- N �           @          @ > h   �  b  q  '  P    ]  �  d!  �-  �-  �1  �1  �1   �   :  Om  0   u  Ob     u  Oa  O �   �          �  x   V   �      � �   � �!   � �$   � �)   � �-   � �D   � �H   � �W   � �[   � �f   � �k   � �n   � �w   � �z   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �   � �  � �!  � �A  � �L  � �f  � �n  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �	  � �  � �/  � �5  � �@  � �G  � �K  � �j  � �y  � ��  � ��  � ��  � ��  � �  � �  � �  � �;  � �F  � �K  � �P  � �^  � �i  � �n  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �!  � �&  � �U  � �t  � �{  � �  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �C  � �a  � ��  � �,   ,   0   ,  
 e   ,   i   ,  
 u   ,   y   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �   ,   �   ,  
   ,     ,  
 %  ,   )  ,  
 =  ,   A  ,  
 r  ,   v  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 �  ,   �  ,  
 H嬆H塜H塸H墄 UATAUAVAWH峢侅�   E3蒐嬹JL峞�3��%    E嘇峲B E峣M�J0)p润5    E�(    M�(
    )x阁=    E荄)@�(    驞    M�(
    E�M鱉岮I嬔D孇凗�#  A凖尰   K�塋峕�儖器亓��缷谼�<��     驛K餓�戵D厙T�T腗嬓/润AT蘈F襅�戵D厙T�/葀M峆驛KK�戵D厙I岺T腎峆T�/润AK T蘄F蔍�夡D厙T�/菻F袸兝I兠@H冸卬���A�}^L嬟M嬓I零�   I菱A+荓峿嘜�<廐c伢CK�嬻D�T�T腎嬂/菼嬍HF翴�繧兟H嬓/菼F薒嬞H冸u肐�戵D厙T�/�嚀  I;裻+L=嘓�D諊D=�D涨L諊L=�D=�L涨驛$.莦t!(�D=�粕 ^�D=�D=�^�D=�3繦峌嘕��3蒁岶�fD  A;�勁   �*(�T�/�啿   �L=婣W梵T=�(朋YD=圀\=擉Y腕XD
圀Y阵Y蒹D
�(朋XL
嬻YD=求XD
求L
嬻XT
忬L=梭Y腕T
忬T=象X\
擉Y阵\
擉\=芋D
求XL
梭Y蒹L
梭XT
象T
象X\
芋\
�繦兟H兞凐�����艫�虸�罥兡H兦凗嶧��E�M譇E鏏NM鰽F AN0�A�  �A荈  �A荈  �A荈  �A荈  �A荈  �A荈  �A荈  �A荈   �A荈$  �A荈(  �A荈,  �A荈0  �A荈4  �A荈8  �A荈<  �L崪$�   I嬈I媅0I媠@I媨HA(s餉({郋(C蠭嬨A_A^A]A\]�:   �   ^   g   i   }   t   �   �   v   �   �   �   �   �   �      �     C G            A  �     �1        �donut::math::inverse<float,4> 
 >T   m  AK        �  AK �      
 >繨    b  D@   
 >繨    a  D    
 >t     i  Ao  �     �  Ao �     �
 ,
 >t     i  A   \    3 A  �     i! � � O :$ >@     scale  A�   �    �  A�  �     ��� ( M        �   0#( M           0#2 M        \  0 N N N* M        b  ��(	Z N' M        b  ��		
	S	 N M        �  ��"(S N M        �  �> N M        b  	侁 N M        �  佹 N  M        �  儚    N M        �1  �:%
 >�    _Tmp  A�   "     " A�  �     �F � � 7 VT �� �  N M        �1  �

 >�    _Tmp  A�         N M        �  � N M        �1  
侻 N M        �  �9 N M        �1  �<	 N M        b  
倅 N$ M        �1  偯	

 N$ M        �1  偗	
 N M        �1  倢
 N M        �1  傄> N �           (          @ > h   b     \  �  �  �  �  �   H&  �1  �1  �1  �1  �1   �   T  Om  @   繨  Ob      繨  Oa  O �   �          A  x   8   �      � �#   � �0   � �>   � �B   � �F   � �J   � �N   � �Z   � �b   � �f   � �m   � �q   � ��   � ��   � ��   � �*  � �.  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �  � �,  � �9  � �<  � �A  � �E  � �M  � �Z  � �p  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �   � �  � �
  � �>  � �Q  � �j  � ��  � �  � �,   "   0   "  
 e   "   i   "  
 u   "   y   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
 %  "   )  "  
 5  "   9  "  
    "     "  
   "     "  
 g  "   k  "  
   "      "  
 H�  �?3繦堿H茿  �?H堿茿   �?婤��A$堿,H嬃�   �   �   G G            3       2   l        �donut::math::translation<float,3> 
 ><   a  AK        3  M          0  N                        H  h       ]  �      <  Oa     翵  Oresult  O �   8           3         ,       T �    U �/   V �2   W �,       0      
 i       m      
 �       �      
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  �G                       �        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >  >0   _First  AJ          AJ       
   >0   _Last  AK          
 >u0   _Val  AP           >�3   _Backout  CJ            CJ          
   M        �    N M        �   N                        H & h   �  �  �  �  �  �          0  O_First     0  O_Last     u0  O_Val  O  �   H               �     <       � �    � �   � �   � �   � �   � �,   )   0   )  
 �  )   �  )  
 �  )   �  )  
 �  )   �  )  
   )     )  
 (  )   ,  )  
 <  )   @  )  
   )     )  
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AJ                                 H�     .!  Othis  O   �   0                   $       �  �    �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$H塼$H墊$AVH冹 3�W�H堿L嬺H堿H嬞H儂H媟vL�2H�������H;�嚻   H茿   H凗wH塹A閮   H嬑H兩H;蟰,H�       �H兝'H嬋�    H嬋H吚trH兝'H冟郒塇1�   H孂H;蔋B鶫峅H侚   rH岮'H;羦E牖H吷t�    L岶H�I嬛H塻H嬋H墈�    H媡$8H嬅H媆$0H媩$@H兡 A^描    惕    惕    虉   �   �   �   �   n     �   
  �     �      �   �  � G                   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >	   this  AI  +     � �   AJ        +  >   _Right  AK        �  � k  AK �     �  a U M        �  9.


?
 Z   �   >#     _New_capacity  AH  �       AJ  �     \  9  AM  C     � = " r ]  AH �       AJ �     `  L  AM �      & M        �  ��	-
= >p   _Fancy_ptr  AH �       C            "  C      9     � G " �  �    M          ��1
=  M        >  ��1
=. M        �  ��1		

8/ M        �  ��+%"
D	 Z   q  }   >#    _Block_size  AH  �     O  C  AH �       >#    _Ptr_container  AJ  �     |  d  AJ �      
 >`    _Ptr  AH  �       AH �       M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N N N N M          j8 M        L  j*, >#    _Masked  AJ  q     D    AJ �       M          �� N N N M        G   ^ N M        G   �� N N M          +	 >@    _Result  AV  $     � �   M        D  + N N M        �  
$ M          ������ M        M   N N N                       @ n h   �  �  �            D  G  �  �  �  �        L  M  �  �  �  �    >  X         $LN72  0   	  Othis  8     O_Right  O   �   8             P     ,       �	 �+   �	 ��   �	 �  �	 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �   	  �  
   �   !  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 Y  �   ]  �  
 m  �   q  �  
 �  �   �  �  
 h  �   l  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 S  �   W  �  
 l  |   p  |  
 �  �   �  �  
 H塡$H塴$H塼$H墊$ AVH冹0H嬟H孂H�    H塂$ L�
    �   D岯    怘荊(    H�3H媖H;鮰\H�H呟t
H�H嬎�P怘婫(L�4荌9t!H呟t
H�H嬎�P怚�I�H吷tH��P怘�G(H呟t
H�H嬎�P怘兤H;鮱嬊H媆$@H媗$HH媡$PH媩$XH兡0A^�#   �   /   �   =   �      �   �  � G            �      �   �        �nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> 
 >!   this  AJ           AM        �  D@    >鬒   il  AI       9  AK          AI V     d  Y  >�     <begin>$L0  AL  M     w  >�     <end>$L0  AN  Q     n 
 >N!    i  AI  Y     Y  AI V     d  Y  M        �  �� M        �  ��	 N N M        :  h M        u  p
 >.!   this  AV  p     B  AV V     y  B  M        �  �� M        �  �� N N M        �  �� N M        8  u M        
  u#	 N N N N M        8  V M        
  Y	 N N 0                    0@ 2 h   �  �  �  �  
  8  �  �  �  :  u   @   !  Othis  H   鬒  Oil  9d       �   9�       �   9�       �   9�       �   O   �   H           �   h     <       @  �B   ?  �J   A  �h   B  ��   A  ��   C  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 
  �     �  
   �   !  �  
 F  �   J  �  
 i  �   m  �  
 �  �   �  �  
 �  �   �  �  
 )  �   -  �  
 9  �   =  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹0H嬞W�3�AH堿 H�    H塂$ L�
    �   D岯    怘荂(    H嬅H兡0[�   �   (   �   6   �      �     � G            L      F   �        �nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> 
 >!   this  AI  	     B  AJ        	  D@    0                    0H 
 h   �   @   !  Othis  O   �   8           L   h     ,       5  �   3  �;   4  �C   5  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 4  �   8  �  
 H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >C   this  AJ                                 H     C  Othis  O   �                               �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 H塡$H塴$ H塋$VWAVH冹 H嬹H�    H�H塓H呉t
H�H嬍�P怢峷L塼$H3鞟�.I塶I塶峂(�    H� H堾I塅I塶I塶 I塶(I荈0   I荈8   A�  �?I媈嬐嬇H柳H凐sy箑   �    H孁I婲I婩(H+罤柳H吚t.H��    H侜   rH兟'L婣鳬+菻岮鳫凐wqI嬋�    I墌H崌�   I塅 I塅(H;鴗'@ H�H兦H;鴘綦H兞H灵H吷t3�H嬅驢獺塶PH塶XH塶`H塶hH塶pH嬈H媆$PH媗$XH兡 A^_^描    �   
   Q   �   �   �   �   �   N  �      �   U  [ G            S     S  �        �donut::engine::FramebufferFactory::FramebufferFactory 
 >�&   this  AJ          AL       92  D@    >�$   device  AK        6  AK 7       M        ~  �3 N M        ~  �/ N M        p  �# M        �  �# M          �# N N N M        q  �粿#��M
 >�'   this  AV  ;       BH   @     ! M        �  @,Hy#}M: M          ��h&M/E.$'$	,? >#   _Oldsize  AH  �     �  o  AH #      C       �       >0    _Newend  AH  �       AH #      >#    _Oldcapacity  AH  �     ,    AH �       >0    _Newvec  AM  �     � ^ M  AM      K    M        T  �� N M          �� N M        U  
�� M        �  
�� M        �  
��
 Z   �   N N N M        �  ��& >�3   _Backout  CM           CM         K    M        �  �� N M        �  �  N N M          .���� M        �  ��)n
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  �     � & i % M        �  ��d#
q
 Z   q   >#    _Ptr_container  AP  �     �  n  AP �       >#    _Back_shift  AJ  �     � 9 n  AJ �     n /   N N N M        �  .� N N M        �  ` M        �  ` M        �  ` N N N M        #  E M        W  M(# >,'    _Newhead  AH  U     6  M        a  M M        �  M M        �  M
 Z   �   N N N N M        �  E M          E N N N M        $  @ N N N M        �  $ M          (	 N N                      0@ � h=   �  �  �  �  �  �  p  q  ~  �  �  �             !  "  #  $  Q  R  T  U  W  a  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �          \  ]  �  �  �  �  �  �  �  �  �             $LN169  @   �&  Othis  H   �$  Odevice  93       �   O   �               S  0            (  ��   �   j F                                �`donut::engine::FramebufferFactory::FramebufferFactory'::`1'::dtor$0 
 >�&   this  EN  @                                  �  O   �   �   j F                                �`donut::engine::FramebufferFactory::FramebufferFactory'::`1'::dtor$7 
 >�&   this  EN  @                                  �  O   �   �   j F                                �`donut::engine::FramebufferFactory::FramebufferFactory'::`1'::dtor$8 
 >�&   this  EN  @                                  �  O   ,   
   0   
  
 �   
   �   
  
 �   
   �   
  
 �   
   �   
  
 �   
   �   
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 )  
   -  
  
 =  
   A  
  
 _  
   c  
  
 o  
   s  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 r  
   v  
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 -  
   1  
  
 A  
   E  
  
   
     
  
   �     �  
 Q  
   U  
  
 l  
   p  
  
 �  9   �  9  
 	  9   	  9  
 p	  a   t	  a  
 �	  a   �	  a  
 ,
  d   0
  d  
 �
  d   �
  d  
 H媻@   H兞�       	   H媻H   H兞�       
   H媻H   H兞�          3繦�H堿H嬃�   �   �   7 G            
          X        �nvrhi::Rect::Rect 
 >f   this  AJ        
                         H     f  Othis  O   �               
               �  �,   �   0   �  
 \   �   `   �  
 �   �   �   �  
 � 3狼AW狼A W汕A茿 茿茿 茿茿  茿$茿( 茿,茿0 茿4茿8 茿<茿@ H堿\H堿d茿D �� 茿H 茿L艫P茿T    f茿X  艫Z AlI|墎�   H嬃�   �   �   E G            �       �           �nvrhi::RenderState::RenderState 
 >�   this  AJ        �                         @ " h   �                  �  Othis  O ,   �   0   �  
 j   �   n   �  
 � H嬃茿�   �   �   S G                              �nvrhi::BlendState::RenderTarget::RenderTarget 
 >�   this  AJ                                 H�     �  Othis  O   ,   �   0   �  
 x   �   |   �  
 @USVWATAUAVAWH崿$埶��竫5  �    H+�)�$`5  H�    H3腍墔P4  L塎I嬂H塃圠孃H孂H墠�   H墔�   L墠�   H媿�4  L嫷�4  3跦�H塤I婣H吚t�@I�H�I婣H塆H塤H塤H塤 H塤(H塤0H塤8H塤@H塤HH塤PH塤XH塤`H塤h塤pA婩<塆tH墴�   墴�   H�E3繟峆�PH塃 I婲H��P L嬭I婲H��P H塃豂婲 H��P I婲(H��P 艱$P E2銩儈< t,艱$PI�H��P 禜��1uA�2�E朵�5   ��4DD�W荔E楬塢℉�    H�5    H嬛�|$P HE�厾  W审嵃  H敲����L嬅f�     I�繠�< u鯤崓�  �    �W��8  H菂H     H菂P     �    ��8  �   f墔@  �
   垍B  茀C   H崟8  H崓`  �    怘崟�  H崓�  �    怘婾燞;U╰a(卄  (峱  Jfo    f卲  茀`   3�(厐  B (崘  J0fo    f厫  垍�  H僂燖�L崊`  H峂樿    怘崓`  �    怘嫊P  H凓v5H�翲媿8  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐囻  �    怘嫊�  H凓v4H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚽  �    3繦塃`H塃hH塃pH塃xH墔04  H墔84  f荄$@ H岴楬塂$8H岴`H塂$0H岴pH塂$(H崊04  H塂$ L�
    L�    H峊$XH婱圚�	�    3襀崓�   H;萾
H�H�     H婳H塛H吷tH��P怘婰$XH吷tH荄$X    H��P�W荔D$p3繦塃�E婨L崟E4  fD  I�矢吞烫A鬣陵堵类�蒁*罙��0E�D嬄呉u諰峀$hL崊E4  I嬕H崓�  �    �W��8  H菂H     H菂P     �    ��8  �   墔@  茀D   H崟8  H崓`  �    怘崟�  H崓�  �    怘婽$xH;U�tb(卄  (峱  Jfo    f卲  茀`   3�(厐  B (崘  J0fo    f厫  垍�  H僁$x@�L崊`  H峀$p�    怘崓`  �    怘嫊P  H凓v5H�翲媿8  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘾  �    怘嫊�  H凓v4H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�.  �    A�~8 H�    HE�W�厾  W审嵃  H�脌< u鱈嬅H嬛H崓�  �    �W��8  �    嬎�    H嬋H墔8  H菂H     H菂P          �   堿�   f堿艫 H崟8  H崓`  �    怘崟�  H崓�  �    怘婽$xH;U�tc(卄  (峱  Jfo    f卲  茀`   3�(厐  B (崘  J0fo    f厫  @埖�  H僁$x@�L崊`  H峀$p�    3鯤崓`  �    怘嫊P  H凓v5H�翲媿8  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚇  �    怘嫊�  H凓v4H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘰  �    H壍�   H壍�   H壍�   H壍�   H壍�   H壍�   f塡$@H岲$pH塂$8H崊�   H塂$0H崊�   H塂$(H崊�   H塂$ L�
    L�    H峌癓媘圛婱 �    H嬛H崓�   H;萾H�H�0H婳H塛H吷tH��P怘婱癏吷tH塽癏��P怘荅�  �?荅�f荅� 艵�W�E郔�L岴郒峌圛嬒�惾   H嬛H崓�   H;萾H�H�0H婳 H塛 H吷tH��P怘婱圚吷tH塽圚��P怘婱貗AW审H*葖W荔H*荔Gx�O|H菂�      W�呧  fo
    f嶐  茀�   H菂       菂      茀   菂     f菂    壍  H菂�  �   A�   H�    H崓�  �    茀  茀  A婩@墔�  I�L崊�  H峊$`I嬒�悁   H嬛H崓   H;萾H�H�0H婳(H塛(H吷tH��P怘婰$`H吷tH塼$`H��P怚�> �$  壍  茀   3褹�   H崓   �    壍(  菂,  �   菂0     菂4  �  �   f墔  H塼$`艱$d
H婦$`H塃H塼$`艱$dH婦$`H塃H嬛H墪   H峂H�H墑�   H嫊   H�翲墪   H兞H岴 H;萿譎崓   H崊   A�   � HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   I冭u瓾� H�秚$P@匂t(3繦塂$X荄$X   艱$\H婦$XH墑�   H��   I�L崊  H峌窱嬒�怭  3襀崓(  H;萾
H�H�     H婳0H塛0H吷tH��P怘婱窰吷tH荅�    H��P惼�(4  H媉(5    H呟tH�H嬎�P �x; �
u�	3蓧M(圗,H塢 f塎-)u0圡/I�塎H荅L   H塢@5    )uP嬃H墠   H峂 怘拎�   I�  H媴   H�繦墔   H兞 H峌`H;蕌茿�  H崟   H崓 $  �    @匂tQ菂@     f菂D   H墲8  D垾F  茀G   H媴 4  H拎�8  � $  �0$  H�� 4  I�L婳0L崊 $  H峌怚嬒�恅  3鰦諬崓   H;萾H�H�0H婳8H塛8H吷tH��P怘婱怘吷tH塽怘��P惞x   �    H塃豀吚t
I嬜H嬋�    �H嬈H婳HH塆HH吷t
H��   �I婲H塎怘吷tH��P怘婳HH兞PA�   H峌愯    怢�
    �   D岯鵋峂愯    茀   壍  H壍  H壍  H壍  H壍   H壍(  H壍0  H崓8  �    菂�      H崓�  �    惼�   H�H媂HH媿  H;藅(H呟tH�H嬎�PH媿  H墲  H吷tH��P怘媉H媿0  H;藅(H呟tH�H嬎�PH媿0  H墲0  H吷tH��P怘婳0H塋$XH吷tH��P怘岲$XH墔�   H岲$`H墔�   H崟�   H崓�  �    H崟�  H崓�  �    怢�
    �   D岯鼿崓�  �    怢�
    �   D岯鵋峀$X�    茀�  茀y   茀|   H婾 H婳H�    L嬋I�L崊   H峌繧嬒��0  H嬛H崓  H;萾H�H�0H婳@H塛@H吷tH��P怘婱繦吷tH塽繦��P怢�
    �   D岯鼿崓�  �    怘媿0  H吷tH壍0  H��P怘媿(  H吷tH壍(  H��P怘媿   H吷tH壍   H��P怘媿  H吷tH壍  H��P怘媿  H吷tH壍  H��P怘媿  H吷tH壍  H��P惼�  H媉(5    H呟tH�H嬎�P �x; �
u�	壍(  垍,  H墲   f菂-    )�0  茀/   H婫 壍H  菂L     H墔@  H壍P  H壍X  I婩壍h  菂l     H墔`      )卲  I婩菂�     菂�     H墔�  )厫  I婩 菂�     菂�     H墔�  )叞  I婩壍�  H菂�     H墔�  H菂�     菂�  ����I婩(菂�     H菂�     H墔�  H菂�     菂�  ����H嬈H墔$  H崓   fD  H拎�  I�   H媴$  H�繦墔$  H兞 H崟   H;蕌腁�  H崟  H崓   �    I儈0 暲垏�   I婩0菂@     菂D     
    H吚uI婩H墔8  H媴   H拎�8  �   �  H��   �    H岹XH塂$(H岹PH塂$ L崓   E3繧嬒�    I婩(H墔�  I婩 H墔�  I�L婳PL崊   H峌菼嬒�恅  H嬛H崓0  H;萾H�H�0H婳`H塛`H吷tH��P怘婱菻吷tH塽菻��P怘壍`  H崓h  �    怘媉H媿`  H;藅(H呟tH�H嬎�PH媿`  H墲`  H吷tH��P怘婳PH塋$XH吷tH��P怘岲$XH墔�   H岲$`H墔�   H崟�   H崓�  �    H嬣H嬛H崊�  H肏崓  H;萾H�H�0H媽h  H墧h  H吷tH��P怘兠H凔(r籋媴�  H墔�  L�
    �   D岯鼿崓�  �    怢�
    �   D岯鵋峀$X�    I�L崊`  H峌蠭嬒��8  H嬛H崓  H;萾H�H�0H婳hH塛hH吷tH��P怘婱蠬吷tH塽蠬��P怢�
    �   D岯鼿崓h  �    怘媿`  H吷tH壍`  H��P怘嫊�  H凓v4H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘒  �    fo    f咅  茀�   H媆$pH呟tgH媡$xH;辴H嬎�    H兠@H;辵颒媆$pH婾�H+親冣繦嬅H侜   rH兟'H媅鳫+肏兝鳫凐囎   H嬎�    W荔D$p3繦塃�H媇楬呟tSH媢燞;辴H嬎�    H兠@H;辵颒媇楬婾℉+親冣繦嬅H侜   rH兟'H媅鳫+肏兝鳫凐wOH嬎�    怚婱H吷t�    怘婱�    H嬊H媿P4  H3惕    (�$`5  H伳x5  A_A^A]A\_^[]描    愯    愯    愯    愯    愯    �   k   -   z   d  
   k     �  '   �     �     �       �   ,  �   T  �   �  �   �  1   �     �  �   9  �   �     �     �     Q  +   z     �     �  �   �  �   �  �     �   ;  1   H     �  �   �  �   �  
     '   #  �   J     S     ]     x  �   �  �   �  �   �  �     1        \  �   �  �        	  "        	  �   d	  %   p	  �   

  p   �     �     N  n   
  �   
  
   b
  &   j
     |
  �   �
  �   �
  �   �  �   �  �   �  �   �  �   �  �   �  �   �     [  �   p  �        �     �  n   �     `     �  �   Z  �   �  �   �  �   �  �   �  �   B  �   W  �   �  �   �  �   �     ,  �   U     �  �   �  �   �  �   �  l   �  �   �  �   �  �   �  �   �  �      �      �   DH  g G              ;     S1        �donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass 
 >�   this  D�   AJ        L  AM  L     ��  D�5   >�$   device  AK        I  AW  I     ��  >軹   shaderFactory  B�   F     �  D�   AH  B     :  AJ  �      AP        B  AU      �
�
  AU �      D�5   >綡   commonPasses  D   D�   AQ        �  D�5   >H   compositeView  AJ  h       EO  (           D�5   >�   params  AV  o     �i	  EO  0           D�5   >�    samplerDesc  D�    >H    sampleView  B   �      >0     useStencil  A   
    � E6u P       � >!   constantBufferDesc  CK  (   }    	 & CK (   �    � 3  E C � 
 � 1 �   D�   >�    stencilFormat  A\  "    '  A\ U    ��  >賂    MotionVectorMacros  D�    >�    resolvedColorDesc  AJ  �    �  B�       �	 >賂    ResolveMacros  Dp    >�    unresolvedColorDesc  AU  �     �
 AU �      >�    depthFormat  A   ;      A  U    \  >�!    sampleFramebuffer  AQ        >�    layoutDesc  B  �     ��t >V     pipelineDesc  D    >�    bindingSetDesc  D %   >r!    pipelineDesc  B`  �     ��< l= <>  >�    bindingSetDesc  B   �     ��	� M        7  �� N M        �#  �� N M        \  �� N M        \  ��
 >3J   this  AH  D    	  N M        �  ��
 >.!   this  AH  M      N M        �1  �� M        �1  �� N N M        ^  �� N M        \  �� N M        �  �� N M        {  �� N M        7&  �� N M        �  �� N M        �  �� N M        J  q M        s  xM M        �  x	 M        >  �� N N N M        �  �q N N M        z  壨 M        (  壨HB
 >�    temp  AJ  �	      AJ �	    �& K  B`   �	    )g �  B繼  �     � N N M        y  (墹 M        z  壛 M        (  壛
 >�    temp  AJ  �	      AJ �	      B燸  �     � N N M        &  壒 >�    tmp  AK  �	    %  AK �	    �  / d  N M        '  墹C M        &  壋 N N N M        �  塠 M          塠
 Z      N N M        �  堼 M          �
 N M        �  堼 M          堼 M        M  堼 N N N N M        6&  埜 M        M&  埜GB
 >M    temp  AJ  �      AJ �      B�   �    @
"
  B^  �     � N N M        5&  (垙 M        6&  埇 M        M&  埇
 >M    temp  AJ  �      AJ �      B鴀  �     � N N M        K&  垽 >M    tmp  AK  �    %  AK �    �    N M        L&  垙C M        K&  垶 N N N M        �  圙 M        )  圙GB
 >;     temp  AJ  K      AJ [    .  B竇  �     � D�    N N M        �  (� M        �  �; M        )  �;
 >;     temp  AJ  7      AJ G      B榏  �     � N N M        �  �3 >;     tmp  AK  !    %  AK G    ?    N M        g  �C M        �  �- N N N M        �  A嘺 M          嘺4
 M        B  4噉 M        `  1噏 M        �  噞)
 Z     
 >   _Ptr  AH  {      AJ  x      AH �      >#    _Bytes  AK  q    1  AK �      M        �  噭d >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  A�幵 M          �4
幥 M        B  4�,幥 M        `  1�/幠  M        �  �9)帢
 Z     
 >   _Ptr  AH  9      AJ  6      AH [      >#    _Bytes  AK  /    �1 � M        �  嘊d帵
 Z   q   >#    _Ptr_container  AH  M      AJ  J      N N N N N N M        D  �亞� M        p  
啈+c
 Z   �   M        �  啘[ M        �  [啘 M        �  喨- M          0喪 M          嗋
 N M        A  喪 N N M        �  喨 M          ��喨 N N N M        �  ,啘 M          0啘 M          啽
 N M        A  啘 N N N N N N N M        @  唅 Z   �  �   N M        �  �N" M        �  T�/G+
 M        �  � >p    _Fancy_ptr  AJ  *    M  M          � M        >  � M        �  � M        �  �
 Z   �   N N N N N M        G   咷 N N M        �  � M          � M        M  � N N N N M        �  呫

 Z   �  
 >C   _Ptr  AL  o    �[5 �� AL �      M          	咍 N M        �  呫
 M          呫��
 M        M  呫 N N N N M        �  A厪 M          厪4
 M        B  4厹 M        `  1厽 M        �  叐)
 Z     
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    1  AK �      M        �  叢d >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  A匨悹 M          匨4
悡 M        B  4匷悡 M        `  1匽悙  M        �  単)恉
 Z     
 >   _Ptr  AH  g      AJ  d      AH �      >#    _Bytes  AK  ]    �1 _ M        �  卲d恟
 Z   q   >#    _Ptr_container  AH  {      AJ  x      N N N N N N M        D  ~劻 M        p  
劻+b
 Z   �   M        �  勌Z M        �  Z勌 M        �  匄, M          0匉 M          �
 N M        A  匉 N N M        �  匄 M          ��匄 N N N M        �  ,勌 M          0勌 M          勧
 N M        A  勌 N N N N N N N M        @  剻 Z   �  �   N M        �  刌9 M        �  &刞+ M        G   剉 N N M        �  刌 M          刌 M        M  刌 N N N N M        1  �E
 M        u  �
*
 Z   d   >�1    _Buff  B05  �     ���  M        c  2�# >p   _RNext  AR  
    K  >u     _UVal_trunc  Ah      C ) 
  N N N M        F  凅 M        q  凅 M        ~  凅 N N N M        �  冎 M        )  冎HB	
 >;     temp  AJ  �      AJ �    7  BX   �    0! t
�>� B郪  �     � N N M        �  +儶 M        �  兪 M        )  兪
 >;     temp  AJ  �      AJ �      B繴  �     � N N M        �  兟 >;    tmp  AK  �      AK �    E    C       �      C      �    Y   -   N M        g  儶B
 M        �  兏 N N N M        �  A傸 M          傸4
 M        B  4�	 M        `  1� M        �  �)
 Z     
 >   _Ptr  AH        AJ        AH 8      >#    _Bytes  AK      1  AK �      M        �  �d >#    _Ptr_container  AH  *      AJ  '      N N N N N N M        �  A偤�- M          偤4
�  M        B  4偳�  M        `  1偸�  M        �  傇)採
 Z     
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    "1 � M        �  傒d�
 Z   q   >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        D  {�1 M        p  
�1*a
 Z   �   M        �  �;Z M        �  Z�; M        �  俫, M          0俰 M          �
 N M        A  俰 N N M        �  俫 M          ��俫 N N N M        �  ,�; M          0�; M          侾
 N M        A  �; N N N N N N N M        @  �	 Z   �  �   N M        �  伜H M        �  &伭++ M        G   +佔 N N M        �  伜 M          伜 M        M  伜 N N N N M        �  亄


 Z   �  
 >C   _Ptr  AK  r    D  M          
亶
 N M        �  亄
 M          亄��
 M        M  亄 N N N N M        F  	乆 M        q  	乆 M        ~  	乆 N N N M        �  奷J$
 >�    <begin>$L0  AJ  r
    0  M        �  妑 N N M        `  
奞 >�    result  B`   V
    ��  N M        a  
�> >�    result  B`   C
      N M        �  夲$ N M        `  � >�    result  BX       D N M        t$  �&
 N M        �  +婹 M        �  媞 M        �  媞
 >D     temp  AJ  m      AJ }      B╢  �     � N N M        �  媔 >D    tmp  AK  b      AK }    �   8   C       S      C      i    �   +  L   N M        (  婹B
 M        �  媉 N N N M        �  	嬺n
, >_    <begin>$L0  AJ  �    N  M        �  �  N N M        c  嬞#' N M        �  媫 M        �  媫GB
 >D     temp  AJ  �      AJ �    ,    Bxc  �     � D�    N N) M        d  嫥%#D$
( N M        s$  �" N M        c  學*)' N M        ^  庍 N M        8  嶵	 M        
  嶿# N N M        �  �' M        �  嶩 M        )  嶩
 >;     temp  AJ  '    ,  
  AJ T      B`g  �     � N N M        �  嶢 >;     tmp  AI       � AI       N M        9  �, M          �,# N N N M        �  嶄' >�    other  AH  �
      AH 	    e   + 
 G  \   M        �  � M        )  �
 >;     temp  AJ  �
    ,  
  AJ       B0g  �     � N N M        �  �	 >;     tmp  AI  �
    8  N M        9  嶔 M          嶔# N N N M        �  嵃
 N M        �  崺 N M        �  崲 N M        �  崨 N M        �  崝 N M        �  崓 N M        l  峎
 Z   �  
 >(   this  AJ  S
      N M        �'  �; M        �  岰# N N M        �1  �$ M        �1  �$(
 M        �1  �$ >�(    _Old_val  AJ  (
      AJ ;
      N M        w1  
�1 N N N M        �1  #� Z   �  �   N M        �  岉 M        ,  岉GB
 >�!    temp  AJ  �      AJ 
      B�   �    I  B@e  �     � N N M        �  )屆 M        �  屷 M        ,  屷
 >�!    temp  AJ  �      AJ �      B e  �     � N N M        *  屬 >�!   tmp  AK  �      AK �        C       �      C      �    2   '   N M        +  屆D M        *  層 N N N M        �  忳 M        *  忳JB
 >7     temp  AJ  �      AJ     i   N N M        �  忀 M        )  忀JB
 >;     temp  AJ  �      AJ �      N N M        �  徝 M        )  徝JB
 >;     temp  AJ  �      AJ �      N N M        �  彥 M        )  彥JB
 >;     temp  AJ  �      AJ �      N N M        �  弿 M        )  弿JB
 >;     temp  AJ  �      AJ �      N N M        �  弖 M        )  弖JB
 >;     temp  AJ  |      AJ �      N N M        �  廌 M        1  廌GB
 >�!    temp  AJ  H      AJ X      BXd  �     � D�    N N M        �  (� M        �  �8 M        1  �8
 >�!    temp  AJ  4      AJ D      B竎  �     � N N M        /  �0 >�!    tmp  AK      %  AK D         N M        0  �C M        /  �* N N N M        �  
慽, >_    <begin>$L0  AJ  z    V  M        �  憖 N N M        Y$  �8*+ N M        Y$  �&+ N M        c  愥** >�   texture  AH  �    &  N M        c  惢** >�   texture  AH  �    &  N M        c  悞&* >�   texture  AH  �    )  N" M        e  恑&*�� >M   sampler  AH  i    )  N$ M        d  �#&&G) N M        �  :揳 M        �  搻 M        �  搻
 >D     temp  B郻  �     � N N M        �  搥 N M        (  揳C M        �  搝 N N N M        8  �	7 M        
  �'#2 N N M        �  掓' M        �  � M        )  �
 >;     temp  AJ  �    ,  
  AJ       B榖  �     � N N M        �  � >;     tmp  AI  �    w  N M        9  掱 M          掱# N N N M        �  捯 N M        �  捑 M        ,  捑GB
 >�!    temp  AJ  �      AJ �      Bhb  �     � D�    N N M        �  (挄 M        �  挷 M        ,  挷
 >�!    temp  AJ  �      AJ �      BHb  �     � N N M        *  挭 >�!    tmp  AK  �    %  AK �    '    N M        +  挄C M        *  挙 N N N M        G  暃 M        [  暃	
 Z   �   N N M        E  �?	4
{# M        Z  �?			4
{ M        i  -昺t M        �  晅)O
 Z     
 >   _Ptr  AH  t      AI  C    1    AH �      AI Q    �  >  >#    _Bytes  AK  j    |   0 G  M        �  晑d
Y
 Z   q   >#    _Ptr_container  AH  �      AI  �      N N N M        y  昈	
 >oT   _First  AI  ]    	  AI Q      >颎   _Last  AL  L    � O F  AL �    C  N N N! M        E  斘	8;��/ M        Z  斘

	8;	�� M        i  1��   M        �  �)��
 Z     
 >   _Ptr  AH        AI  �    3    AH (      AI �    a  Q  >#    _Bytes  AK  �    4    AK �      M        �  �d��
 Z   q   >#    _Ptr_container  AH        AI        N N N M        y  斷	
 >oT   _First  AI  �    
  AI �      >颎   _Last  AL  �    b  AL ?    � 
 O � !  N N N M        �  X攙亙& M          攙
4
�2 M        B  4攦乿 M        `  1攩乻  M        �  攼)丟
 Z     
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    1  AK �      M        �  敊d乁
 Z   q   >#    _Ptr_container  AH  �      AJ  �      N N N N N N M        �  擻 M        )  擻JB
 >;     temp  AJ  c     * AJ v    � * o  �  �   )B p  N N M        �#  �+ M         $  �+GB
 >�"    temp  AJ  /      AJ ?      B_  �     � D�    N N M        �#  (� M        �#  � M         $  �
 >�"    temp  AJ        AJ +      B鴁  �     � N N M        �#  � >�"    tmp  AK      %  AK +         N M        �#  �C M        �#  � N N N> Z   B  �  B  B  �  "  �  '  "  �  �1  �  "  �  # S�  )   nvrhi::AllSubresources  A�   �    �  A�  �      x5          @         A ^h�   B  �  �  �  �  �  �  �  �  �  ^  l  m  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �                (  )  *  @  A  B  C  D  E  G  `  t  u  �  �  �  �  �  �  �  �  �  �  �  �  
             L  M  c  p  �  �  �  �    8  9  >  X  g  ^  `  a  c  d  e               !  #  %  &  '  (  )  1  2  3  4  6  7  x  y  z  {  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  &  '  (  *  +  ,  /  0  1  �  �  \  ^  >  @  D  E  F  G  J  Z  [  h  i  o  p  q  s  y  ~    �  �  �  �  �  �  �  �  �  �  �#  �#  �#  �#  �#  �#  �#   $  Y$  s$  t$  �$  �$  %&  5&  6&  7&  K&  L&  M&  �'  1  _1  `1  a1  v1  w1  x1  �1  �1  �1  �1  �1  �1  �1  �1  �1  �1  �1  �1  
 :P5  O        $LN1435  �5  �  Othis  �5  �$  Odevice  �5  軹  OshaderFactory  �5  綡  OcommonPasses  �5  H  OcompositeView  �5  �  Oparams  �   �  OsamplerDesc  �  !  OconstantBufferDesc  �   賂  OMotionVectorMacros  p   賂  OResolveMacros    �  OlayoutDesc     V   OpipelineDesc   %  �  ObindingSetDesc  `  r!  OpipelineDesc     �  ObindingSetDesc  9�       H   9�       �   9�       �   9
      �   9      �   94      �   9�      �   9�      �   9C      �   9W      �   9�      v$   9�      �   9�      �   9�	      b$   9�	      �   9�	      �   9K      �$   9y      �   9�      �   9�      =   9�      �$   9�      �   9�      �   ^
     �&   99
      �&   9K
      �   9�
      �   9      �   97      �   9P      �   9e      �   9      �$   9@      �   9T      �   9�      �   9�      �   9�      �   9�      �   9�      �   9
      �   9.      =   9�      �$   9�      �   9�      �   9      �   9      �   9/      �   9�      �   9�      �$   9'      �   9;      �   9r      �   O�   �            (  P   �      B  �q   =  ��   B  ��   >  ��   ?  ��   @  ��   A  ��   C  ��   E  ��   F  �  G  �  H  �  Q  �  R  �"  S  �)  U  �.  W  �;  Y  �@  Z  �E  [  �X  a  �a  b  �=  c  ��  e  ��  f  ��  g  ��  h  �j  k  �w  l  �{  m  ��  o  ��  q  �P	  r  �[	  s  �t	  t  �{	  u  ��	  v  ��	  w  ��	  y  ��	  {  �2
  |  �>
  }  �  �  �  �  �:  �  ��  �  �R  �  �W  �  ��  �  �
  �  �;
  �  ��
  �  ��
  �  ��
  �  �  �  �T  �  ��  �  ��  �  ��  �  ��  �  �  �  �u  �  �  �  ��  �  ��  �  �  �  �  �  �d  �  �o  �  �z  �  ��  �  ��  �  �  �  ��  �  �\  �  �v  �  ��  b  ��  f  ��  g  ��  �  ��   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$0 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$1 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$2 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$3 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$4 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$5 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$6 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$7 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$8 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   t  v F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$9 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O�   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$10 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$11 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$12 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$13 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$14 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$15 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$16 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$17 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$51 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$18 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$20 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$21 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$22 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$69 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$23 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$24 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$25 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$84 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$26 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$29 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$146 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                �  O  �   u  w F            -      '             �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$35 
 >�   this  EN  �        '  EN  �5        '  >軹   shaderFactory  EN  �        '  EN  �5        '  >綡   commonPasses  EN          '  EN  �        '  EN  �5        '  >�    samplerDesc  EN  �         '  >!    constantBufferDesc  EN  �        '  >賂    MotionVectorMacros  EN  �         '  >賂    ResolveMacros  EN  p         '  >V     pipelineDesc  EN           '  >�    bindingSetDesc  EN   %        '                        �  O   �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$151 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$152 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$153 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$154 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$155 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$156 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$36 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F            *      $             �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$38 
 >�   this  EN  �        $  EN  �5        $  >軹   shaderFactory  EN  �        $  EN  �5        $  >綡   commonPasses  EN          $  EN  �        $  EN  �5        $  >�    samplerDesc  EN  �         $  >!    constantBufferDesc  EN  �        $  >賂    MotionVectorMacros  EN  �         $  >賂    ResolveMacros  EN  p         $  >V     pipelineDesc  EN           $  >�    bindingSetDesc  EN   %        $                        �  O   �   v  x F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$120 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O  �   u  w F                                �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$42 
 >�   this  EN  �          EN  �5          >軹   shaderFactory  EN  �          EN  �5          >綡   commonPasses  EN            EN  �          EN  �5          >�    samplerDesc  EN  �           >!    constantBufferDesc  EN  �          >賂    MotionVectorMacros  EN  �           >賂    ResolveMacros  EN  p           >V     pipelineDesc  EN             >�    bindingSetDesc  EN   %                                 �  O   �   u  w F            *      $             �`donut::render::TemporalAntiAliasingPass::TemporalAntiAliasingPass'::`1'::dtor$44 
 >�   this  EN  �        $  EN  �5        $  >軹   shaderFactory  EN  �        $  EN  �5        $  >綡   commonPasses  EN          $  EN  �        $  EN  �5        $  >�    samplerDesc  EN  �         $  >!    constantBufferDesc  EN  �        $  >賂    MotionVectorMacros  EN  �         $  >賂    ResolveMacros  EN  p         $  >V     pipelineDesc  EN           $  >�    bindingSetDesc  EN   %        $                        �  O   ,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 
         
 )     -    
 9     =    
 I     M    
 Y     ]    
 m     q    
 �     �    
 �     �    
 �     �    
          
 1     5    
 |     �    
 �     �    
 �     �    
 �     �    
 �     �    
 >     B    
 N     R    
 �     �    
 �     �    
      	    
          
 ?     C    
 O     S    
 {         
 �     �    
          
 @     D    
 �     �    
 �         
 �     �    
 �     �    
 �     �    
 
         
 w     {    
 �     �    
 �     �    
 �     �    
 �     �    
 N
     R
    
 ^
     b
    
 n
     r
    
 �
     �
    
 �
     �
    
 �
         
          
 O     S    
 _     c    
 �          
          
           
 �     �    
 �     �    
 �     �    
 �     �    
 
     
    
 �
     �
    
 �
         
          
 /     3    
 ?     C    
 �     �    
 �     �    
 e     i    
 u     y    
 �     �    
 �     �    
 �         
          
 a     e    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 I     M    
 Y     ]    
 *     .    
 :     >    
 J     N    
 k     o    
 �     �    
 �     �    
 �     �    
 �     �    
          
 �     �    
 �     �    
 �     �    
          
 o     s    
      �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 �          
          
           
 =     A    
 M     Q    
 �     �    
 �     �    
 s     w    
 �     �    
 �     �    
 �     �    
 
         
      !    
 �!     �!    
 �"     �"    
 6#     :#    
 s#     w#    
 �#     �#    
 Q$     U$    
 a$     e$    
 q$     u$    
 �$     �$    
 �$     �$    
 �$     �$    
 �$     �$    
 �%     �%    
 &      &    
 ,&     0&    
 @&     D&    
 ~'     �'    
 �'     �'    
 �'     �'    
 �'     �'    
 �'     �'    
 h(     l(    
 x(     |(    
 �(     �(    
 �(     �(    
 �(     �(    
 7)     ;)    
 W*     [*    
 +     
+    
 +     +    
 �+     �+    
 �+     �+    
 �+     �+    
 �+     �+    
 V,     Z,    
 f,     j,    
 v,     z,    
 �,     �,    
 �,     �,    
 �,     �,    
 �,     �,    
 �-     �-    
 �-     �-    
 .     .    
 .     .    
 m.     q.    
 }.     �.    
 �.     �.    
 �.     �.    
 C/     G/    
 S/     W/    
 �/     �/    
 �/     �/    
 0     0    
 )0     -0    
 90     =0    
 �0     �0    
 �0     �0    
 �0     �0    
 1     1    
 1     "1    
 �1     �1    
 R2     V2    
 �2     �2    
 �2     �2    
 $3     (3    
 �3     �3    
 �4     �4    
 �4     �4    
 �4     �4    
 ,5     05    
 �5     �5    
 �5     �5    
 �5     �5    
 n6     r6    
 ~6     �6    
 �6     �6    
 �6     �6    
 �6     �6    
 8     8    
 %8     )8    
 98     =8    
 I8     M8    
 n8     r8    
 �8     �8    
 �8     �8    
  9     $9    
 09     49    
 P9     T9    
 d9     h9    
 +:     /:    
 ;:     ?:    
 O:     S:    
 _:     c:    
 �:     �:    
 �:     �:    
 �:     �:    
 �:     ;    
 C;     G;    
 S;     W;    
 s;     w;    
 �;     �;    
 Z<     ^<    
 j<     n<    
 z<     ~<    
 �<     �<    
 �<     �<    
  =     =    
 =     =    
 {=     =    
 �=     �=    
 >     >    
 >     >    
 ">     &>    
 �>     �>    
 �>     �>    
 �>     �>    
 �>     �>    
 ?     ?    
 �?     �?    
 �?     �?    
 lC  �   pC  �  
 E     E    
  E     $E    
 0E     4E    
 @E     DE    
 PE     TE    
 `E     dE    
 pE     tE    
 �E     凟    
 怑     擡    
 燛         
 癊     碋    
 繣     腅    
 蠩     訣    
 郋     銭    
 餎     鬍    
  F     F    
 F     F    
  F     $F    
 0F     4F    
 @F     DF    
 PF     TF    
 `F     dF    
 pF     tF    
 �F     凢    
 怓     擣    
 燜         
 癋     碏    
 繤     腇    
 蠪     訤    
 郌     銯    
 餏     鬎    
  G     G    
 G     G    
  G     $G    
 0G     4G    
 @G     DG    
 PG     TG    
 `G     dG    
 pG     tG    
 �G     凣    
 怗     擥    
 燝         
 癎     碐    
 繥     腉    
 蠫     訥    
 郍     銰    
 餑     鬐    
  H     H    
 H     H    
  H     $H    
 0H     4H    
 @H     DH    
 XH     \H    
 K  :   K  :  
 婯  :   廗  :  
 烱  :     :  
 薑  :   螷  :  
 逰  :   鉑  :  
 
L  :   L  :  
 L  :   "L  :  
 2L  :   6L  :  
 \L  :   `L  :  
 峀  :   慙  :  
 綥  :   翷  :  
 闘  :   頛  :  
 M  :   M  :  
 BM  :   FM  :  
 擬  L   楳  L  
 N  L   N  L  
 N  L   N  L  
 GN  L   KN  L  
 [N  L   _N  L  
 哊  L   奛  L  
 歂  L   濶  L  
 甆  L   睳  L  
 豊  L   躈  L  
 	O  L   
O  L  
 :O  L   >O  L  
 fO  L   jO  L  
 慜  L   昈  L  
 綩  L   翺  L  
 P  U   P  U  
 働  U   嘝  U  
 桺  U   汸  U  
 肞  U   荘  U  
 譖  U   跴  U  
 Q  U   Q  U  
 Q  U   Q  U  
 *Q  U   .Q  U  
 TQ  U   XQ  U  
 匭  U   塓  U  
 禥  U   篞  U  
 釷  U   鍽  U  
 
R  U   R  U  
 :R  U   >R  U  
 孯  Y   怰  Y  
 �R  Y   S  Y  
 S  Y   S  Y  
 ?S  Y   CS  Y  
 SS  Y   WS  Y  
 ~S  Y   係  Y  
 扴  Y   朣  Y  
   Y   猄  Y  
 蠸  Y   許  Y  
 T  Y   T  Y  
 2T  Y   6T  Y  
 ^T  Y   bT  Y  
 塗  Y   峊  Y  
 禩  Y   篢  Y  
 U  \   U  \  
 {U  \   U  \  
 廢  \   揢  \  
 籙  \   縐  \  
 蟄  \   覷  \  
 鶸  \     \  
 V  \   V  \  
 "V  \   &V  \  
 LV  \   PV  \  
 }V  \   乂  \  
 甐  \   睼  \  
 赩  \   轛  \  
 W  \   	W  \  
 2W  \   6W  \  
 刉  ^   圵  ^  
 鱓  ^   鸚  ^  
 X  ^   X  ^  
 7X  ^   ;X  ^  
 KX  ^   OX  ^  
 vX  ^   zX  ^  
 奨  ^   嶺  ^  
 瀀  ^     ^  
 萖  ^   蘕  ^  
 鵛  ^   齒  ^  
 *Y  ^   .Y  ^  
 VY  ^   ZY  ^  
 乊  ^   匶  ^  
 甕  ^   瞃  ^  
  Z  `   Z  `  
 sZ  `   wZ  `  
 嘮  `   媄  `  
 砕  `   穁  `  
 荶  `   薢  `  
 騔  `   鯶  `  
 [  `   
[  `  
 [  `   [  `  
 D[  `   H[  `  
 u[  `   y[  `  
   `   猍  `  
 襕  `   諿  `  
 齕  `   \  `  
 *\  `   .\  `  
 |\  b   �\  b  
 颸  b   骪  b  
 ]  b   ]  b  
 /]  b   3]  b  
 C]  b   G]  b  
 n]  b   r]  b  
 俔  b   哴  b  
 朷  b   歖  b  
 繻  b   腯  b  
 馷  b   鮙  b  
 "^  b   &^  b  
 N^  b   R^  b  
 y^  b   }^  b  
   b   猑  b  
 鴁  e   黕  e  
 k_  e   o_  e  
 _  e   僟  e  
 玙  e   痏  e  
 縚  e   胈  e  
 阓  e   頮  e  
   e   `  e  
 `  e   `  e  
 <`  e   @`  e  
 m`  e   q`  e  
 瀈  e     e  
 蔪  e   蝋  e  
 鮜  e   鵣  e  
 "a  e   &a  e  
 ta  f   xa  f  
 鏰  f   隺  f  
 鸻  f   �a  f  
 'b  f   +b  f  
 ;b  f   ?b  f  
 fb  f   jb  f  
 zb  f   ~b  f  
 巄  f   抌  f  
 竍  f   糱  f  
 閎  f   韇  f  
 c  f   c  f  
 Fc  f   Jc  f  
 qc  f   uc  f  
 瀋  f     f  
 餭  ;   鬰  ;  
 dd  ;   hd  ;  
 xd  ;   |d  ;  
   ;   ╠  ;  
 竏  ;   糳  ;  
 鉪  ;   鏳  ;  
 鱠  ;   鹍  ;  
 e  ;   e  ;  
 5e  ;   9e  ;  
 fe  ;   je  ;  
 梕  ;   沞  ;  
 胑  ;   莈  ;  
 頴  ;   騟  ;  
 f  ;   f  ;  
 pf  <   tf  <  
 鋐  <   鑖  <  
 鴉  <   黤  <  
 $g  <   (g  <  
 8g  <   <g  <  
 cg  <   gg  <  
 wg  <   {g  <  
 媑  <   廹  <  
 礸  <   筭  <  
 鎔  <   阦  <  
 h  <   h  <  
 Ch  <   Gh  <  
 nh  <   rh  <  
 沨  <   焗  <  
 餳  >   鬶  >  
 di  >   hi  >  
 xi  >   |i  >  
   >   ╥  >  
 竔  >   糹  >  
 鉯  >   鏸  >  
 鱥  >   鹖  >  
 j  >   j  >  
 5j  >   9j  >  
 fj  >   jj  >  
 梛  >   沯  >  
 胘  >   莏  >  
 頹  >   騤  >  
 k  >   k  >  
 pk  ?   tk  ?  
 鋕  ?   鑛  ?  
 鴎  ?   黭  ?  
 $l  ?   (l  ?  
 8l  ?   <l  ?  
 cl  ?   gl  ?  
 wl  ?   {l  ?  
 媗  ?   弆  ?  
 祃  ?   筶  ?  
 鎙  ?   阬  ?  
 m  ?   m  ?  
 Cm  ?   Gm  ?  
 nm  ?   rm  ?  
 沵  ?   焟  ?  
 餸  A   鬽  A  
 dn  A   hn  A  
 xn  A   |n  A  
   A   ╪  A  
 竛  A   糿  A  
 鉵  A   鏽  A  
 鱪  A   鹡  A  
 o  A   o  A  
 5o  A   9o  A  
 fo  A   jo  A  
 梠  A   沷  A  
 胦  A   莖  A  
 頾  A   騩  A  
 p  A   p  A  
 pp  H   tp  H  
 鋚  H   鑠  H  
 鴓  H   黳  H  
 $q  H   (q  H  
 8q  H   <q  H  
 cq  H   gq  H  
 wq  H   {q  H  
 媞  H   弎  H  
 祋  H   筿  H  
 鎞  H   阸  H  
 r  H   r  H  
 Cr  H   Gr  H  
 nr  H   rr  H  
 況  H   焤  H  
 餽  I   魊  I  
 ds  I   hs  I  
 xs  I   |s  I  
   I   ╯  I  
 竤  I   約  I  
 鉺  I   鐂  I  
 鱯  I   鹲  I  
 t  I   t  I  
 5t  I   9t  I  
 ft  I   jt  I  
 梩  I   泃  I  
 胻  I   莟  I  
 顃  I   騮  I  
 u  I   u  I  
 pu  J   tu  J  
 鋟  J   鑥  J  
 鴘  J   黸  J  
 $v  J   (v  J  
 8v  J   <v  J  
 cv  J   gv  J  
 wv  J   {v  J  
 媣  J   弙  J  
 祐  J   箆  J  
 鎣  J   陃  J  
 w  J   w  J  
 Cw  J   Gw  J  
 nw  J   rw  J  
 泈  J   焪  J  
 饂  ]   魒  ]  
 dx  ]   hx  ]  
 xx  ]   |x  ]  
   ]   ▁  ]  
 竫  ]   紉  ]  
 鉿  ]   鐇  ]  
 鱴  ]   鹸  ]  
 y  ]   y  ]  
 5y  ]   9y  ]  
 fy  ]   jy  ]  
 梱  ]   泍  ]  
 脃  ]   莥  ]  
 顈  ]   騳  ]  
 z  ]   z  ]  
 pz  K   tz  K  
 鋤  K   鑪  K  
 鴝  K   鼁  K  
 ${  K   ({  K  
 8{  K   <{  K  
 c{  K   g{  K  
 w{  K   {{  K  
 媨  K   弡  K  
 祘  K   箋  K  
 鎨  K   陒  K  
 |  K   |  K  
 C|  K   G|  K  
 n|  K   r|  K  
 泑  K   焲  K  
 饇  M   魘  M  
 d}  M   h}  M  
 x}  M   |}  M  
   M   ▆  M  
 竲  M   紏  M  
 銄  M   鐌  M  
 鱹  M   鹽  M  
 ~  M   ~  M  
 5~  M   9~  M  
 f~  M   j~  M  
 梸  M   泘  M  
 脋  M   莮  M  
 額  M   騸  M  
   M     M  
 p  N   t  N  
 �  N   �  N  
 �  N   �  N  
 $�  N   (�  N  
 8�  N   <�  N  
 c�  N   g�  N  
 w�  N   {�  N  
 媭  N   弨  N  
 祤  N   箑  N  
 鎬  N   陘  N  
 �  N   �  N  
 C�  N   G�  N  
 n�  N   r�  N  
 泚  N   焷  N  
 饋  O   魜  O  
 d�  O   h�  O  
 x�  O   |�  O  
   O   ▊  O  
 競  O   紓  O  
 銈  O   鐐  O  
 鱾  O   麄  O  
 �  O   �  O  
 5�  O   9�  O  
 f�  O   j�  O  
 梼  O   泝  O  
 脙  O   莾  O  
 顑  O   騼  O  
 �  O   �  O  
 p�  _   t�  _  
 鋭  _   鑴  _  
 鴦  _   鼊  _  
 $�  _   (�  _  
 8�  _   <�  _  
 c�  _   g�  _  
 w�  _   {�  _  
 媴  _   弲  _  
 祬  _   箙  _  
 鎱  _   陞  _  
 �  _   �  _  
 C�  _   G�  _  
 n�  _   r�  _  
 泦  _   焼  _  
 饐  P   魡  P  
 d�  P   h�  P  
 x�  P   |�  P  
   P   ▏  P  
 竾  P   紘  P  
 銍  P   鐕  P  
 鲊  P   麌  P  
 �  P   �  P  
 5�  P   9�  P  
 f�  P   j�  P  
 棃  P   泩  P  
 脠  P   菆  P  
 顖  P   驁  P  
 �  P   �  P  
 p�  Q   t�  Q  
 鋲  Q   鑹  Q  
 鴫  Q   鼔  Q  
 $�  Q   (�  Q  
 8�  Q   <�  Q  
 c�  Q   g�  Q  
 w�  Q   {�  Q  
 媻  Q   強  Q  
 祳  Q   箠  Q  
 鎶  Q   陫  Q  
 �  Q   �  Q  
 C�  Q   G�  Q  
 n�  Q   r�  Q  
 泲  Q   煁  Q  
 饗  R   魦  R  
 d�  R   h�  R  
 x�  R   |�  R  
   R   ▽  R  
 笇  R   紝  R  
 銓  R   鐚  R  
 鲗  R   麑  R  
 �  R   �  R  
 5�  R   9�  R  
 f�  R   j�  R  
 棈  R   泹  R  
 脥  R   菎  R  
 顛  R   驆  R  
 �  R   �  R  
 p�  c   t�  c  
 鋷  c   鑾  c  
 鴰  c   鼛  c  
 $�  c   (�  c  
 8�  c   <�  c  
 c�  c   g�  c  
 w�  c   {�  c  
 嫃  c   弿  c  
 祻  c   箯  c  
 鎻  c   陱  c  
 �  c   �  c  
 C�  c   G�  c  
 n�  c   r�  c  
 洂  c   煇  c  
 饜  S   魫  S  
 d�  S   h�  S  
 x�  S   |�  S  
   S   ☉  S  
 笐  S   紤  S  
 銘  S   鐟  S  
 鲬  S   麘  S  
 �  S   �  S  
 5�  S   9�  S  
 f�  S   j�  S  
 棐  S   洅  S  
 脪  S   菕  S  
 顠  S   驋  S  
 �  S   �  S  
 p�  T   t�  T  
 鋼  T   钃  T  
 鴵  T   鼡  T  
 $�  T   (�  T  
 8�  T   <�  T  
 c�  T   g�  T  
 w�  T   {�  T  
 嫈  T   彅  T  
 禂  T   箶  T  
 鏀  T   陻  T  
 �  T   �  T  
 C�  T   G�  T  
 n�  T   r�  T  
 洉  T   煏  T  
 饡  @   魰  @  
 e�  @   i�  @  
 y�  @   }�  @  
   @   〇  @  
 箹  @   綎  @  
 鋿  @   钖  @  
 鴸  @   鼥  @  
 �  @   �  @  
 6�  @   :�  @  
 g�  @   k�  @  
 槜  @   湕  @  
 臈  @   葪  @  
 飾  @   髼  @  
 �  @    �  @  
 p�  V   t�  V  
 錁  V   铇  V  
 鴺  V   鼧  V  
 $�  V   (�  V  
 8�  V   <�  V  
 c�  V   g�  V  
 w�  V   {�  V  
 嫏  V   彊  V  
 禉  V   箼  V  
 鏅  V   隀  V  
 �  V   �  V  
 C�  V   G�  V  
 n�  V   r�  V  
 洑  V   煔  V  
 饸  B   魵  B  
 e�  B   i�  B  
 y�  B   }�  B  
   B     B  
 箾  B   經  B  
 錄  B   铔  B  
 鴽  B   鼪  B  
 �  B   �  B  
 6�  B   :�  B  
 g�  B   k�  B  
 槣  B   湝  B  
 臏  B   葴  B  
 餃  B   鬁  B  
 �  B    �  B  
 p�  C   t�  C  
 鍧  C   闈  C  
 鶟  C   凉  C  
 %�  C   )�  C  
 9�  C   =�  C  
 d�  C   h�  C  
 x�  C   |�  C  
 尀  C   悶  C  
 稙  C   簽  C  
 鐬  C   霝  C  
 �  C   �  C  
 D�  C   H�  C  
 o�  C   s�  C  
 湡  C   牊  C  
 馃  D   魺  D  
 e�  D   i�  D  
 y�  D   }�  D  
   D     D  
 範  D   綘  D  
 錉  D   锠  D  
 鵂  D   鼱  D  
 �  D   �  D  
 6�  D   :�  D  
 g�  D   k�  D  
 槨  D   湣  D  
 摹  D   取  D  
 铩  D   蟆  D  
 �  D    �  D  
 p�  E   t�  E  
 澧  E   棰  E  
   E     E  
 %�  E   )�  E  
 9�  E   =�  E  
 d�  E   h�  E  
 x�  E   |�  E  
 專  E   悾  E  
 叮  E   海  E  
 纾  E   耄  E  
 �  E   �  E  
 D�  E   H�  E  
 o�  E   s�  E  
 湦  E   牑  E  
 黏  F   簸  F  
 e�  F   i�  F  
 y�  F   }�  F  
 ゥ  F   ━  F  
 攻  F   渐  F  
 浈  F   瑗  F  
   F     F  
 �  F   �  F  
 6�  F   :�  F  
 g�  F   k�  F  
 槮  F   湨  F  
 摩  F   圈  F  
 铴  F   螃  F  
 �  F    �  F  
 p�  G   t�  G  
 濮  G   椐  G  
   G     G  
 %�  G   )�  G  
 9�  G   =�  G  
 d�  G   h�  G  
 x�  G   |�  G  
 尐  G   惃  G  
 定  G   酣  G  
 绋  G   毹  G  
 �  G   �  G  
 D�  G   H�  G  
 o�  G   s�  G  
 湬  G   牘  G  
 皓  W   舂  W  
 d�  W   h�  W  
 x�  W   |�  W  
 お  W   í  W  
 釜  W   吉  W  
 悛  W   绐  W  
 鳘  W     W  
 �  W   �  W  
 5�  W   9�  W  
 f�  W   j�  W  
 棲  W   洬  W  
 毛  W   谦  W  
 瞰  W   颢  W  
 �  W   �  W  
 p�  X   t�  X  
 洮  X   璎  X  
   X     X  
 $�  X   (�  X  
 8�  X   <�  X  
 c�  X   g�  X  
 w�  X   {�  X  
 嫮  X   彮  X  
 淡  X   弓  X  
 姝  X   戥  X  
 �  X   �  X  
 C�  X   G�  X  
 n�  X   r�  X  
 洰  X   煯  X  
 甬  =   舢  =  
 e�  =   i�  =  
 y�  =   }�  =  
 ク  =   ┋  =  
 汞  =   蒋  =  
 浏  =   璇  =  
   =     =  
 �  =   �  =  
 6�  =   :�  =  
 g�  =   k�  =  
 槹  =   湴  =  
 陌  =   劝  =  
 锇  =   蟀  =  
 �  =    �  =  
 p�  Z   t�  Z  
 浔  Z   璞  Z  
   Z     Z  
 $�  Z   (�  Z  
 8�  Z   <�  Z  
 c�  Z   g�  Z  
 w�  Z   {�  Z  
 嫴  Z   彶  Z  
 挡  Z   共  Z  
 娌  Z   瓴  Z  
 �  Z   �  Z  
 C�  Z   G�  Z  
 n�  Z   r�  Z  
 洺  Z   煶  Z  
 鸪  [   舫  [  
 d�  [   h�  [  
 x�  [   |�  [  
 ご  [   ù  [  
 复  [   即  [  
 愦  [   绱  [  
 鞔  [     [  
 �  [   �  [  
 5�  [   9�  [  
 f�  [   j�  [  
 椀  [   浀  [  
 玫  [   堑  [  
 畹  [   虻  [  
 �  [   �  [  
 H媻�  �       �   H媻�  �          H媻�  �       �   H媻�  H兞�       �   H媻�  H兞�       �   H媻�  H兞 �       �   H媻�  H兞(�       �   H媻�  H兞0�       �   H媻�  H兞8�       �   H媻�  H兞@�           H媻�  H兞H�          H媻�  H兞P�       �   H媻�  H兞X�       �   H媻�  H兞`�       �   H媻�  H兞h�          H崐�   �          H崐�  �       �   H崐8  �       �   H崐`  �          H崐p   �          H崐�  �       �   H崐8  �       �   H崐`  �          H崐�  �       �   H崐8  �       �   H崐`  �          H崐�  �       �   @UH冹 H嬯L�
    A�   �   H崓�   �    H兡 ]�      #   �   H崐   �       �   @UH冹 H嬯L�
    A�   �   H峂X�    H兡 ]�   �       �   H崐`  �       �   @UH冹 H嬯L�
    A�   �   H峂X�    H兡 ]�   �       �   H崐`  �       �   H崐`  �       �   H崐`  �       �   H崐`  �       �   @UH冹 H嬯簒   H媿�   �    H兡 ]�   �   H崐   H兞�       �   H崐   H兞�       �   H崐   H兞�       �   H崐   H兞 �       �   H崐   H兞(�       �   H崐   H兞0�       �   H�    H嬃�   �   �   U G                   
   �        �nvrhi::VertexBufferBinding::VertexBufferBinding 
 >�!   this  AJ                                 H�     �!  Othis  O ,   �   0   �  
 z   �   ~   �  
 3狼A  �?H�H堿堿H嬃�   �   �   ? G                      V        �nvrhi::Viewport::Viewport 
 >R   this  AJ                                 H     R  Othis  O   �                              i  �,   �   0   �  
 d   �   h   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       H� 
 h   g   0   �  Othis  8   �  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �   ,   �      �   =  U G            <      6   ~        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        g  :$
 Z   !   N                       @�  h   g     0   �  Othis  8   �  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           p        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        m    M        f    N N                        @�  h   f  m      �  Othis  O   �   8           !         ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �      �   �   ? G            2      ,   g        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   !                         H�  0   �  Othis  8   �  O_Other  O �   0           2         $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >.!   this  AH         AJ          AH        M        �  GCE
 >D     temp  AJ  
       AJ        N (                     0H� 
 h   �   0   .!  Othis  9       �   O�   0           "        $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 P  �   T  �  
 h  �   l  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >3J   this  AH         AJ          AH        M        ,  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   ,   0   3J  Othis  9       �   O  �   0           "        $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 J  �   N  �  
 d  �   h  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         z        �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >?I   this  AH         AJ          AH        M        (  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   (   0   ?I  Othis  9       �   O  �   0           "        $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   X  x G            "         �#        �nvrhi::RefCountPtr<nvrhi::IComputePipeline>::~RefCountPtr<nvrhi::IComputePipeline> 
 >q~   this  AH         AJ          AH        M         $  GCE
 >�"    temp  AJ  
       AJ        N (                     0H� 
 h    $   0   q~  Othis  9       �   O�   0           "        $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 T     X    
 l     p    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         u        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >�&   this  AH         AJ          AH        M        �  GCE
 >�$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �&  Othis  9       �   O  �   0           "        $       �  �   �  �   �  �,   	   0   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 B  	   F  	  
 \  	   `  	  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   Z  z G            "         �        �nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>::~RefCountPtr<nvrhi::IGraphicsPipeline> 
 >鸌   this  AH         AJ          AH        M        1  GCE
 >�!    temp  AJ  
       AJ        N (                     0H� 
 h   1   0   鸌  Othis  9       �   O  �   0           "        $       �  �   �  �   �  �,       0      
 �       �      
 �       �      
 �       �      
 �       �      
 	      
     
 V      Z     
 p      t     
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �        �nvrhi::RefCountPtr<nvrhi::IInputLayout>::~RefCountPtr<nvrhi::IInputLayout> 
 >^    this  AH         AJ          AH        M        *  GCE
 >7     temp  AJ  
       AJ        N (                     0H� 
 h   *   0   ^   Othis  9       �   O�   0           "        $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
 L  �   P  �  
 d  �   h  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         6&        �nvrhi::RefCountPtr<nvrhi::ISampler>::~RefCountPtr<nvrhi::ISampler> 
 >�8   this  AH         AJ          AH        M        M&  GCE
 >M    temp  AJ  
       AJ        N (                     0H� 
 h   M&   0   �8  Othis  9       �   O�   0           "        $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 D  �   H  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�    this  AH         AJ          AH        M        )  GCE
 >;     temp  AJ  
       AJ        N (                     0H� 
 h   )   0   �   Othis  9       �   O  �   0           "        $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         |        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >\(   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   \(  Othis  9       �   O�   0           "        $       �  �   �  �   �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 D     H    
 \     `    
 @SH冹 H嬞H婭H吷t?H婼(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁H塁 H塁(H岾H兡 [�    �    �?   �   [   
   `   �      �     �G            e      e   �        �std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::~_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> > 
 >?'   this  AI  	     \ Q   AJ        	  M        r  H	V" M        �  )I1& M          *F M        �  )!
 Z     
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        �  
&#
$
 Z   q   >#    _Ptr_container  AP  *     :  !  AP >       >#    _Back_shift  AJ  
     W 1 !  AJ >         N N N M           N N N                       @� & h   �  r  �        �  �         $LN33  0   ?'  Othis  O ,      0     
 �     �    
 	     
    
 �     �    
 �     �    
 *     .    
 >     B    
 d     h    
 x     |    
 �  �   �  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �   V   �      �   �  �G            [      [   r        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > 
 >�(   this  AI  	     R K   AJ        	 " M        �  )H1%
 M          *= M        �  )
 Z     
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z   q   >#    _Ptr_container  AP  )     1    AP =       >#    _Back_shift  AJ       N 1   AJ =       
  N N N M           N N                       H� " h   �  �        �  �         $LN30  0   �(  Othis  O �   8           [         ,       > �	   ? �O   D �U   ? �,      0     
 �     �    
 �     �    
 l     p    
 �     �    
 �     �    
          
 (     ,    
 <     @    
 �  �   �  �  
 �     �    
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �   Y   �      �   �  � G            ^      ^   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >	   this  AI  
     Q J   AJ        
 ) M          ,(
	 M        D   N M        B  ,E M        `  &? M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        �  
"#
!
 Z   q   >#    _Ptr_container  AP  &     7    AP :       >#    _Back_shift  AJ  -     0 
   N N N N N                       H� 6 h   �  �  �      B  C  D  E  `  �  �         $LN33  0   	  Othis  O�   H           ^   P     <       B �   C �
   B �
   C �R   J �X   C �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 ,  �   0  �  
 @  �   D  �  
 f  �   j  �  
 �  ~   �  ~  
   �     �  
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯 H吷tH塳 H��P惡(   H嬎�    H嬤H�u院(   H�H媆$0H媗$8H媡$@H兡 _�    P   �   y   �      �   �  �G            }      d   t        �std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >::~list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > 
 >�(   this  AJ          AL       \   M        �   M        �  
\ M        
  \ M        �  \ N N N' M        �  I*
 >,'   _Head  AI         >,'    _Pnode  AI  &     C  >,'    _Pnext  AM  3     )  AM 0     H  )  M        �  3
 M        �  

G M        
  
G M        �  
G
 Z      N N N M          3 M        5  3 M        ?  3DE
 >�!    temp  AJ  7       AJ G       N N N N N N                      0@� F h   �  �  �  !  �  �  �  
  �  �      *  +  5  ?   0   �(  Othis  9C       �   O  �   8           }   p     ,        �    �d    �x    �,   
   0   
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �        �std::shared_ptr<donut::engine::CommonRenderPasses>::~shared_ptr<donut::engine::CommonRenderPasses> 
 >慔   this  AJ        +  AJ @       M        2  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       @�  h   �  �  2   0   慔  Othis  9+       b&   9=       b&   O  �   0           K   �     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   G        �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >}I   this  AJ        +  AJ @       M        [  &, M        �  
 >a&   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  [   0   }I  Othis  9+       b&   9=       b&   O�   0           K   �     $       � �   � �E   � �,      0     
 �      �     
 �      �     
      
    
 |     �    
 �     �    
 �     �    
 H�	H吷tH��   H� �   �   �  � G                      `1        �std::unique_ptr<donut::engine::FramebufferFactory,std::default_delete<donut::engine::FramebufferFactory> >::~unique_ptr<donut::engine::FramebufferFactory,std::default_delete<donut::engine::FramebufferFactory> > 
 >-�   this  AJ          M        w1   N                        H�  h   v1  w1      -�  Othis  9       �&   O �   8              �     ,       � �    � �   � �   � �,      0     
      !    
 �     �    
 �     �    
 H塡$VH冹 H�H嬹H呟teH墊$0H媦H;遲H嬎�    H兠@H;遳颒�H媀H媩$0H+袶冣繦侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媆$8H兡 ^描    �'      i   �   �   �      �   L  � G            �   
   �   E        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::~vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > 
 >lT   this  AJ          AL       { t  . M        Z  
	
I9%	 M        i  ;*B M        �  G)
 Z     
 >   _Ptr  AJ h       >#    _Bytes  AK  ;     O   2  " M        �  
P#
 
 Z   q   >#    _Ptr_container  AP  T     6    AP h       >#    _Back_shift  AJ  7     S 1   AJ h       N N N M        y  	
 >oT   _First  AI  
     ~ r   >颎   _Last  AM       "  N N                       H� . h
   �  �  �  Z  h  i  y  �  �  �         $LN43  0   lT  Othis  O�   H           �   �     <       � �
   � �
   � �   � �z    ��   � �,      0     
 �      �     
 �          
 �     �    
 �     �    
          
 /     3    
 U     Y    
 i     m    
 �     �    
 �     �    
 4  �   8  �  
 `     d    
 H塡$H塴$H塼$WH冹 H嬹H�H呟tkH媦3鞨;遲!fD  H�H吷t
H�+H��P怘兠H;遳錒�H媀H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐w(I嬋�    H�.H塶H塶H媆$0H媗$8H媡$@H兡 _描    蘽   �   �   �      �     � G            �      �   o        �std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::~vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > 
 >(   this  AJ          AL       � �  . M        �  I4#	 M          *UJ M        �  Y)%
 Z     
 >   _Ptr  AJ z       >#    _Bytes  AK  R     R   -   $ M        �  bd#
(
 Z   q   >#    _Ptr_container  AP  j     :  %  AP z       >#    _Back_shift  AJ  N     V , %  AJ z       N N N M        �  #
	 >�'   _First  AI       � u   >\(   _Last  AM  #     � {   M        �  0 M        |  0 M        �  0CE
 >�    temp  AJ  3       AJ 0         N N N N N                      0@� 6 h   �  �  |  �  �      �  �  �  �  �         $LN46  0   (  Othis  9>       �   O  �   8           �   �     ,       � �   � ��    ��   � �,      0     
          
 %     )    
 �     �    
 �     �    
 @     D    
 T     X    
 z     ~    
 �     �    
 �     �    
 �         
 h     l    
 x     |    
 �  �   �  �  
          
 0     4    
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �   [   �      �   �  D G            `      `   #        �nvrhi::BufferDesc::~BufferDesc 
 >   this  AI  
     S L   AJ        
  M        �  GM) M          -(

 M        D   N M        B  -G M        `  &@ M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        �  
##
"
 Z   q   >#    _Ptr_container  AP  '     8    AP ;       >#    _Back_shift  AJ  .     1 
   N N N N N N                       H� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN37  0     Othis  O ,   �   0   �  
 i   �   m   �  
 }   �   �   �  
 ]  �   a  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 @SH冹 H嬞H兞L�
    �   D岯    怘�H吷tH�    H��P怘兡 [�   �      �      �   M  V G            ?      9   �#        �nvrhi::ComputePipelineDesc::~ComputePipelineDesc 
 >_!   this  AI  	     5  AJ        	  M        �  # M        )  #CE
 >;     temp  AJ  &       AJ 9       N N                      0H�  h   �  )  %  )   0   _!  Othis  95       �   O   ,   �   0   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 I  �   M  �  
 @SH冹 H嬞H婭pH吷tH荂p    H��P怘婯hH吷tH荂h    H��P怘岾P�    H岾�    怘婯H吷tH荂    H��P怘兡 [�>      G         �   S  \ G            j      d   �        �donut::engine::FramebufferFactory::~FramebufferFactory 
 >�&   this  AI  	     `  AJ        	  M        u  L M        �  LDE
 >�$    temp  AJ  P       AJ d       N N M        |  ! M        �  !DE
 >�    temp  AJ  %       AJ 9       N N M        |  	 M        �  IDE
 >�    temp  AJ  
       AJ !       N N
 Z   o                        0H�  h   �  u  |  �  �   0   �&  Othis  9       �   95       �   9`       �   O �               j   0            )  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 K     O    
 [     _    
 �     �    
 �     �    
 /     3    
 ?     C    
 O     S    
 h     l    
 H塡$WH冹 H嬞H伭�   L�
    �   D岯    怘婯03�H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘媆$0H兡 _�   �   %   �      �   �  X G            �   
   �   &        �nvrhi::GraphicsPipelineDesc::~GraphicsPipelineDesc 
 >,    this  AI  
     �  AJ        
  M        �  �� M        *  ��DE
 >7     temp  AJ  �       AJ �       N N M        �  | M        )  |DE
 >;     temp  AJ  �       AJ �       N N M        �  h M        )  hDE
 >;     temp  AJ  l       AJ |       N N M        �  T M        )  TDE
 >;     temp  AJ  X       AJ h       N N M        �  @ M        )  @DE
 >;     temp  AJ  D       AJ T       N N M        �  * M        )  *DG
 >;     temp  AJ  .       AJ @       N N                      0H�  h   �  �  )  *  %  )   0   ,   Othis  9<       �   9P       �   9d       �   9x       �   9�       �   9�       �   O  ,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 I  �   M  �  
 Y  �   ]  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 +  �   /  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �     �  
 b  �   f  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐wkI嬋�    H荂0    H荂8   艭  H婼H凓v,H�H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �<   �   �   �   �   �      �   x  N G            �      �   B        �donut::engine::ShaderMacro::~ShaderMacro 
 >颎   this  AI  
     � �   AJ        
  M        �  ITO& M          T
,(
	 M        D  T N M        B  ,^E M        `  ^&? M        �  d)
 Z     
 >   _Ptr  AJ  a     )  
  >#    _Bytes  AK  d     &  AK �      " M        �  
m#
!
 Z   q   >#    _Ptr_container  AP  q       AP �     #    >#    _Back_shift  AJ  x     
  AJ �       N N N N N N M        �  G$ M          -( M        D   N M        B  - M        `  & M        �  )
 Z     
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       &  AK �       M        �  
##
 >#    _Ptr_container  AP  '       AP ;     m  c  >#    _Back_shift  AJ  .     
  AJ �       N N N N N N                       @� : h
   �  �  �  �      B  C  D  E  `  �  �         $LN70  0   颎  Othis  O,      0     
 s      w     
 �      �     
 ^     b    
 �     �    
 �     �    
 �     �    
 �          
 &     *    
 6     :    
          
 @     D    
 P     T    
 �     �    
 �     �    
 �     �    
 �     �    
 `  �   d  �  
 H�    H�H兞�       �      �      �   �   V G                      r        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        h   	
 N                        H�  h   h  o      �  Othis  O ,   �   0   �  
 {   �      �  
 H�    H�H兞�       �      �      �   �   @ G                      h        �std::exception::~exception 
 >�   this  AJ         
 Z   C                          H�     �  Othis  O  �   (                           Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 H塡$H塴$H塼$WAVAWH冹0L嬺H嬮H嬟H嬹H+蚩   E3�怚嬊H峀$ H;藅H�L�;H�H�H吷tH��P怘兠H冿u螴婩(H塃(H嬇H媆$PH媗$XH媡$`H兡0A_A^_�   �   �  r G            �      l   '        �nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>::operator= 
 >!   this  AJ          AN       X  >!   __that  AK          AV       h  M        �  &0 M        �  K M        �  K N N M        �  C N M        (  0C
 M        �  = N N N 0                    0H�  h   �  �  �  �  (  (   P   !  Othis  X   !  O__that  9S       �   O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$WH冹 孃H嬞H婭pH吷tH荂p    H��P怘婯hH吷tH荂h    H��P怘岾P�    H岾�    怘婯H吷tH荂    H��P怈銮t
簒   H嬎�    H嬅H媆$0H兡 _肈      M      y   �      �   �  e G            �   
   �   �        �donut::engine::FramebufferFactory::`scalar deleting destructor' 
 >�&   this  AI       v  AJ          M        �  

 Z   o   M        u  R M        �  RDE
 >�$    temp  AJ  V       AJ j     !    N N M        |  ' M        �  'DE
 >�    temp  AJ  +       AJ ?       N N M        |   M        �  ODE
 >�    temp  AJ         AJ '       N N N                      0@�  h   �  �  u  |  �  �   0   �&  Othis  9#       �   9;       �   9f       �   O,      0     
 �      �     
 �      �     
          
 #     '    
 �     �    
 �     �    
 �     �    
 �     �    
 `     d    
 p     t    
 �     �    
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   n        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �     ] G            B   
   4   q        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @�  h   h  o  r   0   �  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �   0   �      �   �   R G            B   
   4   j        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        h  

	
 Z   C   N                       @� 
 h   h   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 H塡$H塼$WH冹0H嬞�ApH峺`H岮X3鯤�H峊$ H;衪H嬹3�3襆岲$(L;莟
H�H�    H�H吷tH��P怘�H�7H吷tH��P悆粓   uJ�儉   �X    �
    �    �儉   �儎   �X    �
    �    �儎   H媆$@H媡$HH兡0_脈   s   �   v   �   m   �   p   �   v   �   m      �   �  [ G            �      �   V1        �donut::render::TemporalAntiAliasingPass::AdvanceFrame 
 >�   this  AI       �  AJ          M        �1  $% >�=   _Left  AH       5  AH V     u   8 -  >�=   _Right  AM       �  M        �  V M        �  \ M        ,  \
 >�!    temp  AJ  Y       AJ h     c & -  N N M        *  V >�!   tmp  AL  /     �  C              C      1     �  N N M        �  $1 M        �  J M        ,  J
 >�!   temp  AJ  "     3  AJ V       C      1     ( $   N N M        *  G >�!   tmp  AK  @       AK V     u   8 -  C       3     
  C      G     �      G -  N M        +  1B

 M        *  = N N N M        +  E
 M        *  , N N N 0                    @ * h	   �  �  *  +  ,  �$  r1  �1  �1   @   �  Othis L �  �\惄?`donut::render::TemporalAntiAliasingPass::AdvanceFrame'::`5'::g M �  ��?A?`donut::render::TemporalAntiAliasingPass::AdvanceFrame'::`5'::a1 M �  �
�?`donut::render::TemporalAntiAliasingPass::AdvanceFrame'::`5'::a2  9R       �   9d       �   O  �   P           �   (     D       , �   - �   / �h   1 �q   9 ��   : ��   < �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 i     m    
 y     }    
 �     �    
 �     �    
 �     �    
 N     R    
 ^     b    
 r     v    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
           
 @W�  �    H+郒�    H3腍墑$�  H孃嫅�   呉劘  冴刧  冴�0  凓厰  婹pA筽  H墱$   �   )�$   塗$$莿$�  ����fD  嬄凌3耰衑�l褖T�$H�罥;蓃錎塋$ L岲$(W��     A婸麬3M岪候A3P鴭�$鲐裳陙徇�橝3�,  3蔄増�	  I冮u菋L$ 媆�$嬅凌��#�$�  3貕L$ 嬅H峀$ %璛:�拎3貗�%屵��拎3罔    �\    W蓩昧�3肏嫓$   �G驢*润\误X�(�$   �Y
    �\
    �榧   �憚   �亐   �\    �\    �W�閹   婣p�   冟�缷蠨嬓�    �\    A嬕�   ��    �\    �G隣婣p(    冟(
    )�$�  (    )�$�  (
    )�$�  )�$�  �勀�  �H嬊H媽$�  H3惕    H伳  _�   k      z   #  7   +  m   _  d   g  m   �  m   �  m   �  %   �  m   �  %   �  m   �  �   �  �      �     �   ?  l      �   �  d G            L  !   3  X1        �donut::render::TemporalAntiAliasingPass::GetCurrentPixelOffset 
 >�   this  AJ        0c �7  AJ 0      >吹    offsets  D�   >u     index  A   �    
  >幍   rng  C       �       D     M        7  � N M        7  価 N M        7  侇 N M        7  佷 N M        o-  伾
	 M        7  伾
	 N N M        7  伖 N M        o-  )乼 M        7  	仈 >@    _x  A�   �      A�  0      >@    _y  A�   �      A�  0      N N M        7  丄* N M        �1  �
 M        �1  �
 M        �1  �
 Z   �1   N N N# M        �1  ��W\&
# M        �1  ��W\&
# M        �1  ��W\&
% M        �1   ��W\&

 >@     _Ans  A�   S     - M        �1  ��
9(

 >u     _Res  A   9    ;  A   �     H  A  0      M        �1  ��&
 >�    _Tmp  A   �         N N N N N N M        [1  k  M        t1  k  M        �1  k >u     _Prev  A   P     0  A  �     
  >#    _Ix  AJ  �     K  C       c       C      �       N N N Z   W1  W1                       A F h   7  o-  Z1  [1  s1  t1  �1  �1  �1  �1  �1  �1  �1  �1  �1  �1  
 :�  O     �  Othis  �  吹  Ooffsets      幍  Orng  O   �   �           L  (     �       M �$   N �M   g ��   i �t  c ��  ^ ��  _ ��  Z ��  V ��  Z ��  V ��  W �  V �  W �#  Z �0  l �,      0     
 �      �     
 �      �     
 �      �     
 �          
          
          
 1     5    
 A     E    
 |     �    
 �     �    
 �     �    
 �     �    
 (     ,    
 �     �    
 �     �    
 �     �    
 �     �    
          
 �     �    
 @USWAUAVAWH崿$場��H侅x
  H�    H3腍墔�  H�L嬺I嬝L塋$XL孂H塡$PH�    I嬑I孂�怭  H�E3鞨嬎A峌�吚剈  H媴�	  H峂PH壌$p
  L墹$h
  )�$P
  )�$@
  D)�$0
  D)�$ 
  D)�$
  驞    D)�$ 
  驞D)�$�	  EW隗D`D)�$�	  EW怏DhH崊�   H+罞W闔塂$HH�E嬇�   H嬎�PH�E嬇�   L嬥L婭H嬒A�袽�$H崟@  I嬏H嬸A�P W繦峌P厫  厾  H�L媮�   H嬑A��(    H崟�   I�$(润D]鬑孁驞eDm麹媮�   I嬏E星E�  �?E郃�蠬嬓H崓�   H嬝�    敌   媴�      塃0驞K((企DC$EW鼠{,EW�破�A(润Y蜛W�(譇(袤Y谹(狍Y�(�婆X袤Y蠥(�(�破U�Y润X�(�(�婆��X狍Y畜]4u�X�m �e8祈UL岴畜DY虷峌H峂�砌DY苀n荔YEX润DX象DM<�    L嬊H峀$`H嬓�    H�H峌E3繦嬑�悩   3�L崊�   H嬝H峊$dH崊�   D峅fff�     婮麺岪塇麳岪�
H峈塇饗J鴫H鬉墄餓冮u袤E凥峌畜M圗3繧�$I嬏�吚   �E岓吶   �嵞   菂�     �?�悩   H嬓H崓�   �    H婽$HH峂PW�W�EPL岪A�   M`Ep崁   f�     驛X   驛`A(驛p@ (�(芋YT�(腕Y�X�YL
�X�(企YD
 �X洋X畜H兞H冭u縄兝H兟餓冴u�W繦峂�W蒆岴怘+貶峌TE怉�   M燣岰0E�M纅愺Z   �"�j�r�     (�(芋Y(腕YD�X驜YL 痼X�(企BY �X洋X畜H兝H冮u綡兟I兝餒冸I冴u�E怉婫tA範   M燣崊  I媁(�旸  I嬑)�  E皦厾  I�)�0  �匑  �\�)�   M繦墊$ �匬  �匧  )岪  �岺  �\馏峊  �昘  �匼  �Px3襀壗�  A竴  H崓�  �    �   H崊�  �     H墄鳫�8墄茾  �?H岪H冮u�3襀壗@  A�   H崓H  �    �   H崊P  @ ff�     H墄鳫�8H岪H冮u�W繦壗H  3缐絇  3襀墔�  A竫  H壗T  H崓�  H壗\  卙  @埥d  厁  H壗�  �    H崊�  �   @ �     H�8H岪H冮u驣婫@I嬙I婳HH墔�  H壗  H壗   H壗0  �    L$pH墔�  H崓@  I婫8�   H塂$`H崊�  D$`H荅�   峹  卙  E�厛  f�     H崁�   IH崏�   @�A�H�I�@�A�H�I�@�A�H�I�@�H餒冴u�H崟�  I嬑 I��惏   I�H峊$0I嬑H墊$8墊$@荄$4   荄$0   �惛   H媆$P�   H嬎A�臜��H媩$XD;�倃��D(�$�	  D(�$�	  D(�$ 
  D(�$
  D(�$ 
  D(�$0
  (�$@
  (�$P
  L嫟$h
  H嫶$p
  I�I嬑�怷  H媿�  H3惕    H伳x
  A_A^A]_[]�   z   C   (   �   �   f  }   �  ,   �  !   �  !   W  "   X  p   �  p   $  p   u     �  l      �   g
  b G              *   �  T1        �donut::render::TemporalAntiAliasingPass::RenderMotionVectors 
 >�   this  AJ        ;  AW  ;     � >�$   commandList  AK        0  AV  0     � >H   compositeView  BP   @     � AI  3     ��� AP        3  >H   compositeViewPrevious  BX   8     � AM  M     �4� AQ        M % >�   preViewTranslationDifference  AH  q     �  EO  (           D�
   >u     viewIndex  Am  Y     � >a�    taaConstants  D   >J    viewReprojection  B`   �    �
 >�"    args  D0    >�!    state  D�   >    viewportState  D@  
 >H    view  AT      � AT        >H    viewPrevious  AL  :    � AL      :  M        �  啎 M        �  啎 N N" M        �  呫	
*< N! M        �  呟
" N M        �  咈 N% M        �  厠	[ M        X  吚 N N" M        �  匒	& M        V  卲 N N M        1  劻. N M        1  � N% M        n  �%,B M        �  �%! M        �  �( N N N+ M        n  	僛?$
A M        �  僯 M        �  僯 N N N& M        m  
傕
	
 N# M        �1  
仼
����!
 Z   �1  
 >�   a  AH  �    
  AI  �     >u   mInverted  C�       �    �  C�      �    �  C       �    �  B�       $ M          伝	��=5 N, M        �-  侐%.

 N M        k   佉( M        �  佖 >@    _x  A  �    �  >@    _y  A  �    �  >@    _z  A�   �    �  N N N M        l  乧	# M          乧	 N N Z   j-  j-  �1  �   x
          0          A � h9   �  �  �  �  �  %  &  O  V  X     +  -  .  /  0  6  7  x  �  �  �  �  �  �  �  �  �  �  �  �  �      ]  �  H  k  l  m  n  �  �  �  �  �  �  �  �  �  �-  1  1  _1  b1  y1  �1  
 :�	  O  �
  �  Othis  �
  �$  OcommandList  �
  H  OcompositeView " �
  H  OcompositeViewPrevious ) �
  �  OpreViewTranslationDifference    a�  OtaaConstants  `   J  OviewReprojection  0   �"  Oargs  �  �!  Ostate  @    OviewportState  9M       &%   9`       H   9      H   9&      H   9:      #H   9`      *H   9�      *H   9�      +H   9F      +H   9>      �$   96      �$   9`      %   9y      H   9�      �$   O �   `            (  )   T      �  �*   �  �S   �  �   �  �  �  �)  �  �A  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �"  �  �&  �  �.  �  �>  �  �A  �  �\  �  �a  �  �p  �  �M  �  �Q  �  �_  �  �t  �  �y  �  ��  �  ��  �  ��  �  ��  �  ��  �  �&  �  �<  �  ��  �  ��  �  ��  �  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
      	    
          
 I     M    
 Y     ]    
 m     q    
 �     �    
 �     �    
 �     �    
 .     2    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
      	    
 �     �    
 �     �    
 �     �    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 
     
    
 
     
    
 #
     '
    
 3
     7
    
 C
     G
    
 S
     W
    
 c
     g
    
 |
     �
    
 墤�   �   �   �   X G                      Y1        �donut::render::TemporalAntiAliasingPass::SetJitter 
 >�   this  AJ          >�   jitter  A                                  @     �  Othis     �  Ojitter  O�   0              (     $       o �    p �   q �,      0     
 }      �     
 �      �     
 �      �     
 @USVATAUAVAWH崿$傈��H侅@  H�    H3腍墔�  H�H嬺H嫕�  H�    L嫮�  L孂H嬑D圠$0M嬸H塡$8�怭  I婨 E3銲嬐A峊$�吚勻  H壖$�  )�$0  )�$   �=    D)�$  驞    D)�$   驞
     I婨 E嬆�   I嬐�PH�E嬆�   H孁L婭H嬎A�袶�H峌`H嬝L婣 H嬒A�蠬�H峌`�@0H婣 H嬎�E��W蒆峌PH嬒�@�E�(�破U�\企u�M�M�(误U�莆E�(麦M�坡U�\�砌��\�(�剖�埔��\洋M旙E橌u岓U淗��惱   �e�(左A^Wx� �m橌]滙E�(求H驛^G|�M�(腆E^腕UU�(麦M绑^皿^祗E大m阁^贏�~ �]紅
驛F�E离荅�  �縺|$0 t驛�E碾荅�  �?A�~
 A(润A_N(求A]审^馏M润E蘴A�繉    荅�   u荅�    H�L岲$@I媁(A範   H嬑H荄$     �Px�,E業婳hH塎I婳XH塎郒嬑H荅   E鄡�M饳冣EE �<�,E溋�M(兝檭�E8�3繦塃HH峌H�聋�愗   H�A�   D嬅嬜H嬑�愢   I婨 �   I嬐A��H媆$8D;����D(�$   D(�$  (�$   (�$0  H嫾$�  H�H嬑�怷  H媿�  H3惕    H伳@  A_A^A]A\^[]�   z   <   +   �   v   �   j   �   y   �  l      �   �  ^ G            �  ,   w  U1        �donut::render::TemporalAntiAliasingPass::TemporalResolve 
 >�   this  AJ        J  AW  J     E >�$   commandList  AK        2  AL  2     d >�   params  AP        U  AV  U     < >0    feedbackIsValid  B0   R     G AY        `  >H   compositeViewInput  AU  G     L EO  (           D�   >H   compositeViewOutput  B8   Z     ? AI  9     ^� D EO  0           D�   >u     viewIndex  Al  g     . >H    viewOutput  AI  �      >a�    taaConstants  D@    > d   viewportSize  C       �    "  C      �    "    >�"    state  D   >_   viewportInput  C�       
    b  B�         >H    viewInput  AM  �     � >_   viewportOutput  C�       (    V  B�   2    � M        �  偛 M        �  偛 N N M        �  傰 N M        �1  傟 M        �   傟 N N M        �1  偺 M        �   偺 >t    _x  A   �      N N M        �1  偂$ N M        �  侳 M        �  侾 N M        �  
侳 N N M        e-  侕 N! M        e-  仧
"	
 N M        �+  	仱 N M        1  乕 N M        1  亃 N M        1  �2 N M        1  乲 N @          8          A r h   �  �  0  6  7  x  �  �  �  �  �  �  <  �  �  �  H  �   �#  �#  �+  e-  1  1  �1  �1  �1  
 :�  O  �  �  Othis  �  �$  OcommandList  �  �  Oparams  �  0   OfeedbackIsValid  �  H  OcompositeViewInput   �  H  OcompositeViewOutput  @   a�  OtaaConstants    �"  Ostate  �   _  OviewportInput  �   _  OviewportOutput  9Z       &%   9o       H   9�       H   9�       H   9�       #H   9      #H   9�      -H   9�      �$   9      %   9      %   91      H   9q      �$   O  �   8          �  (  D   ,        �,    �`    ��    ��    ��   
 ��    �   �2   �=   �M   �[   �^   �c   �k   �s   �z   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   �   �'   �A   �F   �a   �f   ��   ��   ��  ! ��  " ��  # ��  " ��   ��  " ��   ��  " ��   ��   ��   ��  " ��   ��  " ��   ��    ��  # �   �  # �  % �A   �k  ( �w  ) �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 $     (    
 4     8    
 a     e    
 u     y    
 �     �    
 �     �    
 �     �    
 �         
 $     (    
 n     r    
 �     �    
 �     �    
 �     �    
 �         
 +     /    
 ;     ?    
 $     (    
 
         
          
 *     .    
 :     >    
 J     N    
 Z     ^    
 j     n    
 z     ~    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 L嬄L嬌W�W跦吷x驢*匐I嬃H验冟H润H*袤X�(薍呉tOfff�     3襂嬂I黢W繪嬂H呉x驢*码H嬄冣H谚H麦H*荔X荔^馏Y梭X蠱吚u�(旅W烂   �   �  2 F            �       �   W1        �VanDerCorput 
 >#    base  AJ          AQ       �  >#    index  AK          AP       �  >@     ret  A�   {     	  A�  @     ;  >@     denominator  A�   0     X  A�          A�  -       >#     multiplier  AH  H       AH @     D  *                         @     #   Obase     #   Oindex  O   �   X           �   (     L       ? �	   A �0   B �@   D �H   E �s   G ��   I ��   J �,   %   0   %  
 W   %   [   %  
 g   %   k   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
 �   %   �   %  
   %     %  
 0  %   4  %  
 @  %   D  %  
 �  %   �  %  
 H塡$H塴$H塼$WAVAWH冹 L嬄H嬹I�������I;��  H�H婭H+薍六H嬔H殃I嬊H+翲;葀M嬿�L�4
M;餗B�3�H呟tgH媙H;輙H�H吷t
H�;H��P怘兠H;輚錒�H媀H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐噦   I嬋�    H�>H墌H墌M;鱳pJ��    H侞   r!H岾'H;藇V�    H吚tFH峹'H冪郒塆H呟tH嬎�    H孁H�>H墌H�;H塅H媆$@H媗$HH媡$PH兡 A_A^_描    惕    惕    炭   �   �   �     �   >  �   D  �   J        �   l  � G            O     O          �std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Clear_and_reserve_geometric 
 >(   this  AJ          AL       1
  >#   _Newsize  AK          AP       3U � & AP p     �   :  S  �  �  �   >#    _Newcapacity  AV  S     �   �   M        6  1ne >#    _Oldcapacity  AJ  8     8   " AJ p     �    @ x  �  �   >#    _Geometric  AV  Y       M        R  1 N N& M        5  ��G#! >\(   _Newvec  AM     '  C       b       C      p     � � 
 �   M        �  G��o M        �  G��o& M        �  ��)
!
3, M        �  ��$%%>	 Z   q  }   >#    _Block_size  AJ  �     
  AJ C      >#    _Ptr_container  AH  �       AH     -   
 >`    _Ptr  AM  �       AM     '  M        �  ��
 Z   �   N N M        �  �

 Z   �   N N M        �  
��
 N N N N M          .�� M        �  ��)
 Z     
 >   _Ptr  AJ �       >#    _Bytes  AK  �     1    AK =       M        �  ��d# >#    _Ptr_container  AP  �       AP �     �  z  >#    _Back_shift  AJ  �     0  AJ �     �  z  N N N M        �  k	 >�'   _First  AI  4     �  AI =      >\(   _Last  AN  k     c  AN �     { `   M        �  p M        |  p M        �  pCE
 >�    temp  AJ  s       AJ p         N N N N
 Z   4                        0@ V h   �  �  |  �      �  �  �  �  5  6  7  R  �  �  �  �  �  �         $LN84  @   (  Othis  H   #  O_Newsize  9~       �   O�   �           O  �  
   t       u �   � �1   � �`   � �g   � ��   � ��   � ��   � ��   � ��   � �$  � �=  � �I  � �,      0     
 �      �     
 �      �     
      
    
          
 *     .    
 i     m    
 �     �    
 �     �    
      
    
 m     q    
 �     �    
 �     �    
 a     e    
 q     u    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 8     <    
 H     L    
 r     v    
 �     �    
 �     �    
 �     �    
          
          
 {         
 �     �    
 -  �   1  �  
 h     l    
 �     �    
 H塡$WH冹 ����H嬞嬊�罙凐uH���羬�u	H�H嬎�PH媆$0H兡 _�   �   �   C G            A   
   6   �        �std::_Ref_count_base::_Decref 
 >a&   this  AI       )  AJ          M        �  #	
 N                       H� 
 h   �   0   a&  Othis  9!       b&   93       b&   O  �   @           A   �     4       � �
   � �   � �#   � �6   � �,   �   0   �  
 h   �   l   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H冹HH峀$ �    H�    H峀$ �    �
   �      �      g      �   �   F G                       }        坰td::_Throw_bad_array_new_length 
 Z   p   H                      @        $LN3  O  �   (               �            J �   K �,   �   0   �  
 �   x   �   x  
 �   �   �   �  
 H冹(H�
    �    �         �      �   w   7 G                     �        坰td::_Xlen_string 
 Z   j   (                      @        $LN3  O �   (              P            		 �   
	 �,   �   0   �  
 s   z   w   z  
 �   �   �   �  
 H冹(H�
    �    �   .      �      �   �   � G                     �        坰td::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Xlength 
 Z   j   (                      @        $LN3  O �   (              �            a �   b �,      0     
 �   �   �   �  
 �      �     
 H冹(H�
    �    �   .      �      �   �   � G                     4        坰td::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Xlength 
 Z   j   (                      @        $LN3  O  �   (              �            a �   b �,      0     
 �   �   �   �  
 �      �     
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   o   �   �   �   �   �   n   ,  �   O  �   U  �   [  �      �   '  r G            `     `          �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >	   this  AI       A&	  AJ         
 >C   _Ptr  AK          AW       D/  >#   _Count  AL       G4  AP         B M        �1  E
(?SD3$--K
 Z   �   >#     _New_capacity  AH  �     �  * N  V r  AM  O     =  b �  AH �     G  ,  AJ �       M        �1  �� M        G   �� N N M        �  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M          ��?�� M        >  ��?�� >#   _Count  AJ  �      * M        �  ��

*%
u- M        �  ��	)
��
 Z   }   >#    _Block_size  AJ  �     �  �  AJ �       >#    _Ptr_container  AH  �       AH �     }  b 
 >`    _Ptr  AV  �       AV �     ~ V "  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N N N N M          X(  M        L  X' >#    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M          
~ N N N M        B  -�W M        `  �&P M        �  �
)/
 Z     
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        �  
�#
2
 Z   q   >#    _Ptr_container  AP        AP +    4  *  >#    _Back_shift  AJ      
  AJ Z      N N N N N M        F  L4 N M          $# >p    _Result  AM  '       AM 8      M        D  ' N N                       @ n h   �  �  �        B  D  E  F  G  `  �  �  �      L  �  �  �    >  X  �1  �1         $LN93  @   	  Othis  H   C  O_Ptr  P   #  O_Count � ǘ  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_66f57f934f28d61049862f64df852ff0>  O �   h           `  P  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 ]  �   a  �  
 m  �   q  �  
 �  �   �  �  
 Y  �   ]  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 W  �   [  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 0  �   4  �  
 �  �   �  �  
 �  �   �  �  
 a  �   e  �  
 <  �   @  �  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �   <   �      �   d  \ G            A      A   i        �std::allocator<donut::engine::ShaderMacro>::deallocate 
 >鬞   this  AJ          AJ ,       D0   
 >颎   _Ptr  AK        @ /   >#   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        �  
#

 Z   q   >#    _Ptr_container  AJ       (    AJ ,       >#    _Back_shift  AH         AH ,       N N (                      H  h   �  �         $LN18  0   鬞  Othis  8   颎  O_Ptr  @   #  O_Count  O�   8           A   �     ,       � �   � �2   � �6   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
      "    
 ?     C    
 �     �    
 �     �    
 �     �    
 �     �    
 $  �   (  �  
 x     |    
 H婹H�    H呉HE旅   �      �   �   : G                      i        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                    $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H           q      q      �    20    2           r      r      �   
 
4 
2p    B           s      s      �    20    <           t      t      �   
 
4 
2p    B           u      u      �    20    <           v      v      �   
 
4 
2p    B           w      w      �    �                  y      y      �    B                 {      {      �    t d 4 2�              }      }      �    20    ^                           T
 4	 2�p`    [           �      �         ! �     [          �      �         [   8          �      �         !       [          �      �         8  T          �      �         !   �     [          �      �         T  `          �      �          �0    H      i       �           �      �      #    20    `           �      �      )    B             h      5       "           �      �      /   h           8      ;          �   2 B             h      D       "           �      �      >   h           G      J          �   2 B             h      S       "           �      �      M   h           V      Y          �   2 R0           h      b       L           �      �      \   (           e      h          �   j
 t d
 T	 4 R�           h      q       �           �      �      k   (           t      w          �   
xX 0, 
 d T 4
 R��p           h      �       �           �      �      z   h           �      �          �   �
 
4 
2p           h      �       �           �      �      �   h           �      �          �   H  20           h      �       ?           �      �      �   h           �      �          �   :8 
 
4 
2p    A           �      �      �   # 20               �      �      �   ! t               �      �      �      E           �      �      �   !                 �      �      �   E   K           �      �      �   - B             h      �       "           �      �      �   h           �      �          �   2 B             h      �       "           �      �      �   h           �      �          �   2 B             h      �       "           �      �      �   h           �      �          �   2 B             h      �       "           �      �      �   h           �      �          �   2 B             h             "           �      �         h           
      
          �   2; *hV"�
��	��p`0P        R5     j                        �      �         (                    \    �<    a<    A>    .    .    .    .    .    .    .    .    .    .    .    *    a:    �
:    �:    �	r    �	=    �    �:    �
:    �:    �	r    �	=    �    AF    n    �    �    .    .    .    .    .    U    F    n    .    �    �	E    �	F    n    .       �   	         �      Y      \   !   ^   &   `   +   b   0   e   5   f   :   ;   ?   <   D   >   I   ?   N   A   S      Z   �   a   �   h   �   o      w   �   }      �   �   �   �   �   �   �      �   �   �   �   �   �   �   @   �   V   �   B   �   C   �   D   �   E   �   F   �   G   �   �   �   �   �   X   �   �   �   �   �   �     �     [     �   |4 )� �"�$(&�(� Z*< �,�0(2�4E,..�0(249,T60,�6@,�8�:D89:H81:@8<*8N:,>48咼8LJN:L6N:L"NTP`R6L糔@:�8�
:@8TBX:V"XTZ~\rV淴@:n,)  2P                @      @          2P    -           V      V      %    2P    *           X      X      +    2P    *           [      [      1   �* O
���p0P    �	     i       u           �      �      :   !n n貫 [葻 M笭 ;ā 2槩 )垼  x� h� 腗dN    u       ,   �   0   �   4   :   u   �          �      �      @   !       u          �      �      :   �            �      �      F   ,	 � �
���`0P      �     i       y           �      �      L   !;
 ;楡 )圓 xB hC t�     y          �      �       L   y   k          �      �      R   !       y          �      �      L   k  �          �      �      X    d	 4 Rp           h      d       �           �      �      ^   (           g      j          �   �, ! �p      �     i       V           �      �      m   ! h@4�    V          �      �      m   V   t          �      �      s   !       V          �      �      m   t  L          �      �      y    B             h      �       "           �      �         h           �      �          �   2 d T 4 2p           h      �       }           �      �      �   h           �      �          �   � 20    [           �      �      �    20    e           �      �      �    T 4
 2�p`           h      �       S          �      �      �   (           �      �       6    .    .       �      9      a      d   f:� 20           h      �       j           �      �      �   h           �      �          �   :8 N d T 4 2p           h      �       �           �      �      �   h           �      �          �   |
 d
 T	 4 2��p           h      �       O          �      �      �   (           �      �          �   ��  B                 �      �      �    B             h      �       "           �      �      �   h           �      �          �   2
 
4 
2p           h              �           �      �      �   h                           �   F8 N 20    �           �      �      	    20               �      �         ! t               �      �            E           �      �         !                 �      �         E   K           �      �         - B      A           �      �      $   
 
4 
2`               �      �      *   ! t               �      �      *      P           �      �      0   !                 �      �      *   P   �           �      �      6    B                 �      �      <   6 6� 1� ,� $� x	 h
 4  p      1          �      �      B    h 
4
 
rp    n          �      �      H   � 崍 |x	 Zh
 #t #d #4 # ����P      A          �      �      N    4     p           �      �      T    4     �           �      �      Z   
 d
 T	 4 2��p           h      f       }          �      �      `   (           i      l       08   �   n P� ` \, T t	 T 4 2�    U           �      �      o   ! d     U          �      �      o   U   �           �      �      u   !       U          �      �      o   �   �           �      �      {   !   d     U          �      �      o   �             �      �      �   !       U          �      �      o               �      �      �    4 2p    1           �      �      �   
 t	 d T 4 2�    9          �      �      �   k k� [x Hh  ���
�p
`	0P    �          �      �      �   ' 'h  "      �           �      �      �    r����p`0           h      �       s          �      �      �   8               �      �   	   �            �   �       8   � ��  BP0      C           8      8      �     B      :           �      �      �          t           �      �      �   ! 4      t          �      �      �   t   H          �      �      �   !       t          �      �      �   H  �          �      �      �                               1      �      �   Unknown exception                             =      �      �                               I      �      �   bad array new length                                �      �                                 �      �      �                   .?AVbad_array_new_length@std@@                     ����                      �      �                   .?AVbad_alloc@std@@                    ����                      �      �                   .?AVexception@std@@                     ����                      �      �   string too long     ����    ����        ��������                            U               1 0 USE_STENCIL main donut/passes/motion_vectors_ps.hlsl SAMPLE_COUNT USE_CATMULL_ROM_FILTER donut/passes/taa_cs.hlsl TemporalAntiAliasingConstants MotionVectors TemporalAA vector too long                                       �      4      1                         7                   :               ����    @                   �      4                                         �      @      =                         C                           F      :              ����    @                   �      @                                         �      L      I                         O                                   R      F      :              ����    @                   �      L                                         X      [      U                   .?AVFramebufferFactory@engine@donut@@                               ^                   a               ����    @                   X      [     �/�7�5费8   ?
�?�?A?  �? 季L  �?                  �?                                 �?      �=  @�  ��  @>              �?������������   �   �   �   �  牼  �>  嗑  ��  �>  �=  @�  牼  @>  �>  �>  嗑   �   ;   9 
7        donut::engine::FramebufferFactory::`vftable'     
      
  
    �   (   & 
7        std::exception::`vftable'    �      �  
    �   (   & 
7        std::bad_alloc::`vftable'    �      �  
    �   3   1 
7        std::bad_array_new_length::`vftable'     �      �  
 噾姏@|#{夫侴�=*�(.瞗輩耾"0n�1r!J姹擈┎S属h嶗寳G質訤�sbW30夦膀O両籨n頁鎴萩瓠幈I7H��j7b,#煳龔w�s誒耦珗V猖�+Ug祇樫,+貤E郣巊竗|簕�:鉺怐u坄肧塈}9錌桔減鄱5舣I~�.h�K蜌�(嘈瞺味旙e眗洢�p�滔偩�\框�ItD�g1h>箞懲達荢R峌茘0俶�h墔媦註[聖u鳿�\桦�'洋m|躦箃:�-�瀍#珮癫[鮎�娰炌墎矙Pbg$0殡脴-'X*j�.喅Zk�2骹Y4膛"X2�6�鋏!-紾巟}男EK譪闙�.嚼秿�<皖潪両QM醢F蒲o�	_赨n�潞拹膈1笮陝败]	嚤厎t堀�(！
Z�8y硛s3>飖9屓恾皀褺髯P�<籔4)U珼蝩匊庳禋�泞_砵V�&颬茧\尋�=	l鞞鹚翇^=f瓵脍DE缾}�$價赗¨夬hk岢R妱隂S蛁7F蠍I�諜L程 T(倴
Q@y�W盹嚡�V[篠缲丟9未s�(�-
<鰏mk'-N眀E搟*蟉斝�..x虜嬙粱.密醽枋╡|�蓴�9疝-a渏e輵壡l猇#剓r釭贤鏱@)譻滦b媷�6@姽鷳Cn矏3鹠L]伭奈愊雊桤螪Sc<$牖S�&痮椲隈酟y[蜩_�ob斟霨腑n苗XaD螚y�+倧A糲�ob斟霨腑n苗XaD螚y畸ob斟霨腑n苗XaD螚y击,f薥牾�;亻Sp�4z鸑区wI揌P崂?kaD螚y�揌P崂?kaD螚y籥D螚y籥D螚y籥D螚y击�>�2ig
�W:�f橚uC鎹陫徆YW瞼 瑕枷lパU�1唣阈 �?3>飖9屓D6b�搿E閈原曫樥�6≒&嚤踖p禭诵鰐麌臂ep禭�6IB��6萪O��'��$x稫霵婬(︶辋
爌桗�'項j啢Cy1墽�'項j�4憆季�'項j�2�5�碮�'項j&VP妮	�4�'項j溹h{)g�'項j.:铥L�'項j<iur�麲�'項j��8�	�'項j砃G!甮嬬'項j�
�2zP u朵�+^{3癥�与啭翝\夂嫑�薛哃z%螅:b8�4n萧蹷E蕂D嶥�)笖dE熙xeA3$愜w獛啯蜠2t僖$愜w獛啯J��3�*籙`z�)皍M}!屆4Y癏^塔燮燳莩ゆU
(B�锏N耴+攋j猕up泸翬蟱_?+鲢�6�<藺瘧櫷y`&�K厨^�禜�1渭mZ繡)�M�1�4萉�5亐熫P"櫐O嶀預棊膬$R驯�-n嶀預棊膬B涵 =6]滟� 颲茻K-�!U,逐hP鱞彾忢萋徇�5>觙肒#`0柭徇�5>臎2P$AXt箩邆5> t0�	j!�� �蹰k�.�D�3燩2F闢j�6C碝熿傮Q9.��%／虫顕#9d'鯖)訃扺翵\:*~莇坠熔pRH�+櫟錭传蚩茓�BW7&�	b咁磟铡V褺倞�^�7［!笑吒豫�蟾jC蕧�=門{
�%I栶賑?TB誜S�-f]{謑p曉UtgE例f]{謑p╜~�o阃f]{謑p逺FP聼赑丽Υ兮杭础拑�?卢鑂侽�S灠"5猲N鵘J廒%e�%C2F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�5�蘫� 菘U睃-雵J-WV8o��腫62V^橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 塒u+(_b4級^d\蜩kU雵J-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G�В焦�1F�;攃�dd�a�:_棢杻#Q刁ブ;vH嚷b郜=籩项扭dd�a�:_棢杻#Q笧賑K箾￥�7楎V�>塣d\蜩kU咞taR�,F_棢杻#Q尅晪1 2﨏帲晗Di＄燤�'鹴aR�,F_棢杻#Q噤釺�
鮱蒘�8萀D罵��$m翲咞taR�,F_棢杻#Q惕屖碋箎9E\$L釉辸�>i,夿3'伽╪鮢雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G坹L椲DRT�,{-�2兀<dd�a�:T��%鋁�6�雖瑯议wyQ5R犵�喪樢閣yQ朏菜{.鶚议wyQj;想=虡议wyQj;想=蘗|蒔7�-P熥圐駜朎-;}k�;BH��8�込
醶中曈垪j0罿`a�2�#惯擌髟�'Q鶘2m46陾 T74(鲳蒄�>聦�r�P垞雭�0yR2�,�	L0郁Z囇�dd�a�:_棢杻#Q�
>鼾oK@*� HBW 9�~&?秮啰�,�
�0MC綘�C晟D�＄!镘嶀了5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊嘕-WV8om�M%>mb雵J-WV8o.*~襠[
BG嶊4K鋆7篓<帏dd�a�:KiｎT襏�
*a酜DS�8萀D磷Kiv褍| 咞taR�,F_棢杻#Q�,g世偫�.�?覡coq�	a%咞taR�,F_棢杻#Q=泳脸餿眩袞炗贿�嶺�	��dd�a�:_棢杻#Q侙刔跛阉-坓�(鬄�汬'这柫�5YJq覜垒�咞taR�,F_棢杻#Q`�G埻﨏帲晗D� 蛮l�(詤鹴aR�,F_棢杻#Q薝鄫5繉雵J-WV8o�.w⒇衞雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛-坓�(鬄鮳�>i,夿坁裗呔屸懚獲r貂筦绬靻3;�F瞁:2&櫇�"`Z_餔旉>R�-坓�(鬄�汬'这桖孓1EL砶贗EU�镃甉h嗓斠[G`嵇莚YLN婽�(悵q�?5*窆H絁媳躻5*窆H繈饊�/袞炗贿�%騏�唖,dd�a�:镑肐溱磧Q搕g 钵棰^@�(潮螣P雟禑)媗ㄣ(PI^ub&鷜3!敲:G火s昖廘�汚$S匜*媵-/�謚帊敲:G火�Ｈ屌座_簤�p畚佗勫r|x滑嶕俾&俱AR囔颯E涏?虶酥s2倃齼_*窶圹涩�6ツ�吤sM7髥榥夅y*�杜`颀l+�鞯.r擣�0G#盱谑f鵱�s觶籹;嗐8儧j� 頿噉4�硓�-坓�(鬄�/ｎ	蜍R<欠9h��<楁 佂M� .lX剺灷繥溠K狦?�:td輮覴3)滖麧悗隙睼�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H潗幭恫V掮濧嶩b5i漩
ψ笥
iP綀E{_$樐蜆{綔祐}徃O晩v淹m僿1Yl暆:�c�&慵懧酤Y鳺b黆鬜柩in<�懩�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2�=�1Y儧T�"�:邍A愦靮鸬2�>料C5�吩y�+@怚墐覴>掎/5態yV百瀀CRC冼bA鵕�"��!堗^笵A傮壖俹DRt摊o5yI~hU��('a�5]_иtr。�/鷮|N�7襷个耼O榖苃V觔.癔�sG﹋-诓铢缃_T葜桬lVq�
S�0,v�/騊越嶦荒�5u診x}V��@sh        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       �              .debug$T       p                 .rdata         @       5G碚                         7          .text$mn       9     碹�(     .debug$S       �  :           .text$mn       �       港�+     .debug$S       �             .text$mn    	   n      鐨$�     .debug$S    
   �         	    .text$mn       �       膒T     .debug$S       �             .text$mn    
   �       f聰s     .debug$S       ,         
    .text$mn       .       ^�     .debug$S       �              .text$mn       =       N�*Z     .debug$S       �              .text$mn       ,       l?     .debug$S       �              .text$mn              Nm軉     .debug$S       �              .text$mn       :      眡�     .debug$S                    .text$mn       }     変     .debug$S       �
  d           .text$mn            �<N�     .debug$S       D  2           .text$mn       1      瑓w�     .debug$S       �             .text$mn       s  	   皳�     .debug$S        �  b           .text$x     !   C      -�    .text$mn    "         �%     .debug$S    #   X         "    .text$mn    $   @       燡芊     .debug$S    %   t         $    .text$mn    &   �      �
袾     .debug$S    '   �         &    .text$mn    (   �       ��     .debug$S    )   �         (    .text$mn    *   p       戢輊     .debug$S    +   ,         *    .text$mn    ,   �     垗N$     .debug$S    -            ,    .text$mn    .   1     禕Xd     .debug$S    /   �         .    .text$mn    0   �     D�:A     .debug$S    1   �         0    .text$mn    2   A     �9��     .debug$S    3   �         2    .text$mn    4   3       盇�     .debug$S    5   ,         4    .text$mn    6           _葓�     .debug$S    7   T         6    .text$mn    8          �邆     .debug$S    9            8    .text$mn    :        0润�     .debug$S    ;   �  2       :    .text$mn    <   �      ��     .debug$S    =      "       <    .text$mn    >   L      蔫�     .debug$S    ?   l         >    .text$mn    @          恶Lc     .debug$S    A   �          @    .text$mn    B   S     2<刐     .debug$S    C   �
  D       B    .text$x     D         ��:B    .text$x     E         曍譧B    .text$x     F         �cB    .text$mn    G   
       �9�     .debug$S    H   �          G    .text$mn    I   �       (回     .debug$S    J   �          I    .text$mn    K          袁z\     .debug$S    L   �          K    .text$mn    M     b   �禞     .debug$S    N   H�  �      M    .text$x     O         莆蠤M    .text$x     P         �9誐    .text$x     Q         醤M    .text$x     R         珼橺M    .text$x     S         疗识M    .text$x     T         C鉤M    .text$x     U         l涟睲    .text$x     V         欰5]M    .text$x     W         髅f盡    .text$x     X         [LVM    .text$x     Y         6蜠篗    .text$x     Z         繬罸M    .text$x     [         捁M    .text$x     \         mI籕M    .text$x     ]          髓組    .text$x     ^         謊�/M    .text$x     _         a x鳰    .text$x     `         G�5塎    .text$x     a         )緻M    .text$x     b         %FZ甅    .text$x     c         a x鳰    .text$x     d         G�5塎    .text$x     e         )緻M    .text$x     f         a x鳰    .text$x     g         G�5塎    .text$x     h         )緻M    .text$x     i         疵�M    .text$x     j   -      
胂M    .text$x     k         
嫵贛    .text$x     l   *      =驺蚆    .text$x     m         )緻M    .text$x     n   *      =驺蚆    .text$x     o         )緻M    .text$x     p         )緻M    .text$x     q         )緻M    .text$x     r         )緻M    .text$x     s          闳M    .text$x     t         辵ウM    .text$x     u         (� IM    .text$x     v         Ews    .text$x     w         咈ZMM    .text$x     x         鑠	    .text$x     y         饘NM    .text$mn    z          �邆     .debug$S    {   �          z    .text$mn    |          痖I     .debug$S    }   �          |    .text$mn    ~   <      .ズ     .debug$S       0  
       ~    .text$mn    �   <      .ズ     .debug$S    �   L  
       �    .text$mn    �   !      :著�     .debug$S    �   <         �    .text$mn    �   2      X于     .debug$S    �   <         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   "       坼	     .debug$S    �   �         �    .text$mn    �   e      D远     .debug$S    �             �    .text$mn    �   [       荘�     .debug$S    �            �    .text$mn    �   ^      wP�     .debug$S    �   T         �    .text$mn    �   }      庹V     .debug$S    �   �         �    .text$mn    �   K       }'     .debug$S    �   �         �    .text$mn    �   K       }'     .debug$S    �   �         �    .text$mn    �          塴�     .debug$S    �   �         �    .text$mn    �   �      8耾^     .debug$S    �   �         �    .text$mn    �   �      吖     .debug$S    �   h          �    .text$mn    �   `      板@�     .debug$S    �   �         �    .text$mn    �   ?      劸惂     .debug$S    �   \         �    .text$mn    �   j      ゴ瓋     .debug$S    �   �         �    .text$mn    �   �      4;�     .debug$S    �   �  *       �    .text$mn    �   �      f綛a     .debug$S    �   �  $       �    .text$mn    �         ��#     .debug$S    �   �          �    .text$mn    �         ��#     .debug$S    �   �          �    .text$mn    �   �       肫v     .debug$S    �            �    .text$mn    �   �      猜     .debug$S    �   �         �    .text$mn    �   B      贘S     .debug$S    �             �    .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   B      贘S     .debug$S    �   �          �    .text$mn    �   H       襶.      .debug$S    �   �         �    .text$mn    �   �      W屜�     .debug$S    �   P  *       �    .text$mn    �   L     FO+�     .debug$S    �   �  (       �    .text$mn    �     
   瑘蔯     .debug$S    �   �  V       �    .text$mn    �          �OH     .debug$S    �   (         �    .text$mn    �   �     o湨�     .debug$S    �   
  J       �    .text$mn    �   �       灅A!     .debug$S    �   �         �    .text$mn    �   O     纭
     .debug$S    �      F       �    .text$mn    �   A       麔囝     .debug$S    �   H         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �             �    .text$mn    �         �ッ     .debug$S    �            �    .text$mn    �   `     匮�5     .debug$S    �   �  B       �    .text$mn    �   A      �园     .debug$S    �   �         �    .text$mn    �         崪覩     .debug$S    �   �          �        \       �        x                �                �                �                �                �                �                               *      �        K      �        e      �        �      �        �          i�                   �      ~        �      �                  i�                   "      �        G      �        l      �        �      �        �          i�                   �      �                       3      �        L      :        �      �        �      �        <      &        �      |        �      G        �      �        �      K              @        9      I        W      �        �      �        �      8        �      �        .      >        �      <              �        �      �        �      �        �      z        �      �              �        Z      �        �      �        �      �        �      �        -	      �        �	      �        �	      M        �
      �        N      �        �      �        (      �        �      �        �      �        
      �        �
      �        �      �        ,      B        k      �        �      �              �        �      �        0      �        b               �               +      �        \          i                   �      �        �               �               J      �        �      �        �      �        I      �        �      .        �      4        <      	        t      2        �      *        
              N      �        f              R              �              S      6        �      $                       l      0        �      
        �              9              p              �              o              �              �              8      "        R       (        !      ,        C"      !        #      D        c#      O        P$      Y        >%      Z        ,&      r        '      [        	(      \        �(      s        �)      ]        �*      t        �+      u        �,      v        �-      w        �.      x        /      y        n0      ^        \1      _        J2      `        83      a        &4      P        5      b        6      c        �6      d        �7      e        �8      f        �9      g        �:      h        �;      i        �<      Q        p=      j        ^>      k        L?      l        :@      R        'A      m        B      n        C      S        餋      o        轉      T        薊      p        笷      U              E        鬐      V        酘      q        螴      F        J      W        
K      X        鱇               
L               L               .L           __chkstk             CL           fmodf            memcpy           memmove          memset           $LN13       �    $LN5        �    $LN10       �    $LN7        ~    $LN13       �    $LN10       �    $LN16       �    $LN3        �    $LN4        �    $LN3       �    $LN4        �    $LN72     :    $LN77       :    $LN33   ^   �    $LN36       �    $LN93   `  �    $LN100      �    $LN16       &    $LN37   `   �    $LN40       �    $LN10       �    $LN10       �    $LN10       �    $LN9        >    $LN50       <    $LN30       �    $LN48       �    $LN18       �    $LN11       �    $LN18       �    $LN10       �    $LN10       �    $LN10       �    $LN10       �    $LN10       �    $LN1435   M    $LN1443     M    $LN307      �    $LN97       �    $LN64       �    $LN121      �    $LN10       �    $LN77       �    $LN30   [   �    $LN33       �    $LN33   e   �    $LN36       �    $LN169  S  B    $LN172      B    $LN27       �    $LN46   �   �    $LN49       �    $LN84   O  �    $LN88       �    $LN3       �    $LN4        �    $LN10       �    $LN31       �    $LN70   �   �    $LN73       �    $LN18       �    $LN18   A   �    $LN21       �    $LN43   �   �    $LN46       �    $LN3       �    $LN4        �    $LN12       .    $LN55       	    $LN202      2    $LN52       *    $LN46           $LN176          $LN56         $LN61           $LN19           $LN71   9      $LN76           $LN152      0    $LN4        
    $LN140  s          [L     !    $LN144          $LN14   :       $LN17           $LN46       ,    .xdata      �          F┑@�        'M      �    .pdata      �         X賦        KM      �    .xdata      �          （亵�        nM      �    .pdata      �          T枨�        桵      �    .xdata      �          %蚘%�        縈      �    .pdata      �         惻竗�        鍹      �    .xdata      �          （亵~        N      �    .pdata      �         2Fb襼        5N      �    .xdata      �          %蚘%�        ]N      �    .pdata      �         惻竗�        凬      �    .xdata      �          （亵�        狽      �    .pdata      �         2Fb襽        轓      �    .xdata      �          %蚘%�        O      �    .pdata      �         惻竗�        CO      �    .xdata      �          懐j炛        tO      �    .pdata      �         Vbv              �    .xdata      �          �9��        覱      �    .pdata      �         �1柏        鬙      �    .xdata      �          �F�:        P      �    .pdata      �         *!)	:        kP      �    .xdata      �          （亵�        罰      �    .pdata      �         翎珸�        Q      �    .xdata      �          蔜-遛        `Q      �    .pdata      �         愶L�        罳      �    .xdata      �         �qL冝        !R      �    .pdata      �         ~蕉睫        僐      �    .xdata      �         |鞭        錜      �    .pdata      �         瞚挨�        GS      �    .xdata               S!熐�        ㏒          .pdata              �o堔        T         .xdata              aX醴&        mT         .pdata              緥�&        豑         .xdata               （亵�        BU         .pdata              粻胄�        gU         .xdata              /
        婾         .pdata              +eS粧        蒛         .xdata        	      �#荤�        V         .xdata      	        j�        FV      	   .xdata      
         3狷 �        孷      
   .xdata              /
        蘓         .pdata              +eS粬        W         .xdata      
  	      �#荤�        =W      
   .xdata              j�        xW         .xdata               3狷 �        筗         .xdata              /
        鬢         .pdata              +eS粏        4X         .xdata        	      �#荤�        sX         .xdata              j�        礨         .xdata               3狷 �        齒         .xdata              簄餹>        ?Y         .pdata              ⒆2~>        沋         .xdata        	      � )9>        鯵         .xdata              j>        TZ         .xdata               o6榬>        竄         .xdata               娕�<        [         .pdata              v<        篬         .xdata        	      � )9<        ]\         .xdata              j<        ]         .xdata               <        痌         .xdata               欄満        U^         .pdata               緥��        繼          .xdata      !  	      �#荤�        *_      !   .xdata      "        j�        梍      "   .xdata      #         -�        
`      #   .xdata      $        �酑�        w`      $   .pdata      %         鮩s�              %   .xdata      &  	      �#荤�        訿      &   .xdata      '        j�        a      '   .xdata      (         爲飆�        <a      (   .xdata      )        蚲7M�        ma      )   .pdata      *        袮韁�        沘      *   .xdata      +  	      �#荤�        萢      +   .xdata      ,        j�        鴄      ,   .xdata      -         愔
~�        .b      -   .xdata      .         %蚘%�        ^b      .   .pdata      /        s�7逶        宐      /   .voltbl     0         'ι屧    _volmd      0   .xdata      1         （亵�        筨      1   .pdata      2        � 佗        c      2   .xdata      3        范^摙        Hc      3   .pdata      4        鳶��        慶      4   .xdata      5        @鴚`�        赾      5   .pdata      6        [7堍        #d      6   .voltbl     7         飾殪�    _volmd      7   .xdata      8        /
        ld      8   .pdata      9        +eS粩              9   .xdata      :  	      �#荤�        遜      :   .xdata      ;        j�        e      ;   .xdata      <         3狷 �        ]e      <   .xdata      =        /
        檈      =   .pdata      >        +eS粖        襡      >   .xdata      ?  	      �#荤�        
f      ?   .xdata      @        j�        Ef      @   .xdata      A         3狷 �        唂      A   .xdata      B        /
        羏      B   .pdata      C        +eS粓              C   .xdata      D  	      �#荤�        :g      D   .xdata      E        j�        yg      E   .xdata      F         3狷 �        緂      F   .xdata      G        /
        齡      G   .pdata      H        +eS粣        @h      H   .xdata      I  	      �#荤�        俬      I   .xdata      J        j�        莌      J   .xdata      K         3狷 �        i      K   .xdata      L        /
        Wi      L   .pdata      M        +eS粚        檌      M   .xdata      N  	      �#荤�        趇      N   .xdata      O        j�        j      O   .xdata      P         3狷 �        hj      P   .xdata      Q  (      魝疗M        琷      Q   .pdata      R        惂頛M        択      R   .xdata      S  	      � )9M        wl      S   .xdata      T    .   G7M        _m      T   .xdata      U  �       芙�M        Mn      U   .xdata      V         k筂        5o      V   .pdata      W        Vbv鵐        ,p      W   .xdata      X         k筂        "q      X   .pdata      Y        噖sbM        r      Y   .xdata      Z         k筂        
s      Z   .pdata      [        瀪秇M        t      [   .xdata      \         k筂        鴗      \   .pdata      ]        瀪秇M        顄      ]   .voltbl     ^         冻繗M    _volmd      ^   .xdata      _        Ｄ覄�        鉽      _   .pdata      `        魺颁�        zw      `   .xdata      a  8      tZ��        x      a   .pdata      b        诂sκ        ▁      b   .xdata      c        u鏬�        @y      c   .pdata      d        /頄i�        賧      d   .xdata      e         礳, �        rz      e   .pdata      f        粖澄        {      f   .xdata      g  $      C�
4�        粄      g   .pdata      h        ��        a|      h   .xdata      i        W`H
�        }      i   .pdata      j        @廖        瓆      j   .xdata      k        猐�        S~      k   .pdata      l        a[�'�        榽      l   .xdata      m  	      � )9�        軂      m   .xdata      n        j�        #      n   .xdata      o         R�<
�        p      o   .xdata      p        藞占�        �      p   .pdata      q        A鶬撊        �      q   .xdata      r        Gq�        ��      r   .pdata      s        2k+]�        鎬      s   .xdata      t        �*�        L�      t   .pdata      u        溃o�        瞾      u   .xdata      v        /
        �      v   .pdata      w        +eS粠        Q�      w   .xdata      x  	      �#荤�        墏      x   .xdata      y        j�        膫      y   .xdata      z         3狷 �        �      z   .xdata      {        vQ9	�        @�      {   .pdata      |        A刄7�        �      |   .xdata      }  	      �#荤�        麆      }   .xdata      ~        j�        蹍      ~   .xdata               強S��        羻         .xdata      �         （亵�              �   .pdata      �        愶L�        亪      �   .xdata      �         （亵�        `�      �   .pdata      �        弋槡        謯      �   .xdata      �        鸝�B        K�      �   .pdata      �        �迃B        拰      �   .xdata      �  	      � )9B        貙      �   .xdata      �        4�2鼴        !�      �   .xdata      �         (DB        p�      �   .xdata      �        蚲7M�        箥      �   .pdata      �        s�+A�        韻      �   .xdata      �  	      �#荤�         �      �   .xdata      �        j�        V�      �   .xdata      �         x憵	�        拵      �   .xdata      �        vQ9	�        葞      �   .pdata      �        栝�        M�      �   .xdata      �  	      �#荤�        褟      �   .xdata      �        j�        X�      �   .xdata      �         竷 n�        鍚      �   .xdata      �         溤h)�        l�      �   .pdata      �        踣蔞�        �      �   .xdata      �  	      � )9�        瘨      �   .xdata      �        j�        S�      �   .xdata      �          3%�        龘      �   .xdata      �         �9��              �   .pdata      �        �1败        +�      �   .xdata      �        /
        磿      �   .pdata      �        +eS粯        顣      �   .xdata      �  	      �#荤�        '�      �   .xdata      �        j�        c�      �   .xdata      �         3狷 �              �   .xdata      �        �酑�        釚      �   .pdata      �        寵Q�        �      �   .xdata      �  	      �#荤�        R�      �   .xdata      �        j�        崡      �   .xdata      �         耚zu�        螚      �   .xdata      �         （亵�        	�      �   .pdata      �        荡        6�      �   .xdata      �         （亵�        b�      �   .pdata      �        � 伽              �   .xdata      �        范^摛        鐦      �   .pdata      �        鳶��        +�      �   .xdata      �        @鴚`�        o�      �   .pdata      �        [7埭        硻      �   .voltbl     �         飾殪�    _volmd      �   .xdata      �         �9��        鳈      �   .pdata      �        s�7遴        `�      �   .xdata      �         �搀�        葰      �   .pdata      �        O?[4�        3�      �   .xdata      �        T�%~�        潧      �   .pdata      �        *i澚�        	�      �   .xdata      �        Ｕ崹        u�      �   .pdata      �        ��*2�        釡      �   .xdata      �         �9��        M�      �   .pdata      �        �1摆        綕      �   .xdata      �  (       呆垐.        ,�      �   .pdata      �        WX�.        q�      �   .xdata      �         ��		        禐      �   .pdata      �        =�c	        鯙      �   .xdata      �  ,       喉俛2        4�      �   .pdata      �        `鎥2        |�      �   .xdata      �         
*        脽      �   .pdata      �        悜P�*        $�      �   .xdata      �         
        劆      �   .pdata      �        7G        蜖      �   .xdata      �         溤h)        �      �   .pdata      �        鯊"�        	�      �   .xdata      �  	      � )9              �   .xdata      �        伏a        颍      �   .xdata      �         謪$=        瞍      �   .xdata      �         �-th        浈      �   .pdata      �        �        M�      �   .xdata      �        銎�        郸      �   .pdata      �        �g�        �      �   .xdata      �        N懁        墽      �   .pdata      �        
        螫      �   .xdata      �        Z�	W        ]�      �   .pdata      �        敵4        迁      �   .xdata      �        N懁        1�      �   .pdata      �        赴t        洨      �   .xdata      �         |釣�        �      �   .pdata      �        鉙gI              �   .xdata      �         U费�        T�      �   .pdata      �        釩�<        全      �   .xdata      �  $       &0        ;�      �   .pdata      �        Xt^�0        儸      �   .xdata      �         ,t 
        尸      �   .pdata      �        v��
        �      �   .xdata      �        �9供        q�      �   .pdata      �        Z嘆�        ;�      �   .xdata      �  
      B>z]        �      �   .xdata      �         �2g�        携      �   .xdata      �        T�8              �   .xdata      �        r%�        l�      �   .xdata      �  	       椷Kg        :�      �   .xdata      �         M[�        �      �   .pdata      �        ��        喑      �   .voltbl     �             !    _volmd      �   .xdata      �         �9�        勾      �   .pdata      �        礝
        �      �   .xdata      �         確,        r�      �   .pdata      �        j�(,        牰      �   .xdata      �        s�),        头      �   .pdata      �        ghC{,              �   .xdata      �        唘M�,        +�      �   .pdata      �        '�6�,        Z�      �   .rdata      �                     壖     �   .rdata      �         �;�         牸      �   .rdata      �                     羌     �   .rdata      �                     藜     �   .rdata      �         �)          �      �   .xdata$x    �                     ,�      �   .xdata$x    �        虼�)         N�      �   .data$r     �  /      嶼�         q�      �   .xdata$x    �  $      4��         柦      �   .data$r     �  $      鎊=         虢      �   .xdata$x    �  $      銸E�         �      �   .data$r     �  $      騏糡         D�      �   .xdata$x    �  $      4��         ^�      �       澗           .rdata      �         燺渾         熬      �   .data       �          烀�          志      �       
�     �   .rdata      �                     1�     �   .rdata      �         �]�         Z�      �   .rdata      �         �6F�         n�      �   .rdata      �         櫻繋         偪      �   .rdata      �         旲^         牽      �   .rdata      �  $       黣W,         房      �   .rdata      �  
       <         羁      �   .rdata                萌懰         �          .rdata               髋济         9�         .rdata               ?词C         i�         .rdata               歀彑         浝         .rdata               聣Y�         祭         .rdata               IM         诶         .rdata$r      $      'e%�          �         .rdata$r            �          �         .rdata$r                         .�         .rdata$r    	  $      Gv�:         D�      	   .rdata$r    
  $      'e%�         c�      
   .rdata$r            }%B         {�         .rdata$r                         懥         .rdata$r    
  $      `         Я      
   .rdata$r      $      'e%�         屏         .rdata$r            �弾         榱         .rdata$r                         
�         .rdata$r      $      H衡�         +�         .rdata$r      $      'e%�         U�         .data$rs      6      ム蜀         �         .rdata$r            �                   .rdata$r                         勇         .rdata$r      $      Gv�:                  .rdata               �         ,�         .rdata               紴V�         <�         .rdata               s塔�         L�         .rdata               =-f�         \�         .rdata               gl蹩         l�         .rdata               [@         |�         .rdata               v靛�         屆         .rdata               �&Zi         溍                        .rdata               z�         久         .rdata                �         迕          .rdata      !         � �         �      !   .rdata      "         �;�=         3�      "   .rdata      #         萧�         Z�      #   .rdata      $         v靛�         伳      $   .rdata      %         iI               %   .rdata      &         �a�         夏      &   .rdata      '         ﹌         瞿      '   .rdata      (         Z苽�         �      (   .rdata      )         ]p�         D�      )   _fltused         .debug$S    *  H          �   .debug$S    +  4          �   .debug$S    ,  4          �   .debug$S    -  @          �   .chks64     .  p                k�  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?_Xlen_string@std@@YAXXZ ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??$_UIntegral_to_string@DI@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@I@Z ??0Viewport@nvrhi@@QEAA@XZ ??0Rect@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0RenderTarget@BlendState@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??0RenderState@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@XZ ??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@V?$initializer_list@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@@std@@@Z ??4?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAAAEAU01@$$QEAU01@@Z ??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ ??1ComputePipelineDesc@nvrhi@@QEAA@XZ ??0VertexBufferBinding@nvrhi@@QEAA@XZ ?_Decref@_Ref_count_base@std@@QEAAXXZ ??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ ??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$unique_ptr@VFramebufferFactory@engine@donut@@U?$default_delete@VFramebufferFactory@engine@donut@@@std@@@std@@QEAA@XZ ??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ ??0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z ?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z ?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z ?AdvanceFrame@TemporalAntiAliasingPass@render@donut@@QEAAXXZ ?SetJitter@TemporalAntiAliasingPass@render@donut@@QEAAXW4TemporalAntiAliasingJitter@23@@Z ?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ ??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z ??1FramebufferFactory@engine@donut@@UEAA@XZ ??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ ?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z ?_Xlength@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z ?GetFramebuffer@FramebufferFactory@engine@donut@@QEAAPEAVIFramebuffer@nvrhi@@AEBVIView@23@@Z ??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z ??_EFramebufferFactory@engine@donut@@UEAAPEAXI@Z ??1ShaderMacro@engine@donut@@QEAA@XZ ?CreateAutoShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0UStaticShader@23@11PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ?CreateBindingSetAndLayout@utils@nvrhi@@YA_NPEAVIDevice@2@W4ShaderType@2@IAEBUBindingSetDesc@2@AEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@2@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@2@@Z ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z ??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ ?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ ??$inverse@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@@Z ??$translation@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z ??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z ??$inverse@M$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@@Z ??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z ??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z ?VanDerCorput@@YAM_K0@Z ??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??$_UIntegral_to_buff@DI@std@@YAPEADPEADI@Z ??$?0PEAD$0A@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@PEAD0AEBV?$allocator@D@1@@Z ??$inverse@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@@Z ??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z ??$?DM$02$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@0@Z ??$?_0M@math@donut@@YAAEAU?$vector@M$03@01@AEAU201@M@Z ??$?YM@math@donut@@YAAEAU?$vector@M$03@01@AEAU201@AEBU201@@Z ??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$?_0M@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@M@Z ??$?YM@math@donut@@YAAEAU?$vector@M$02@01@AEAU201@AEBU201@@Z ??$_Nrand_impl@MV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z ??$_Uninitialized_move@PEAUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@QEAU123@0PEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z ?catch$7@?0???$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z@4HA ?dtor$0@?0???0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$0@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$10@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$11@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$120@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$12@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$13@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$146@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$14@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$151@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$152@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$153@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$154@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$155@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$156@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$15@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$16@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$17@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$18@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$1@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$20@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$21@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$22@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$23@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$24@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$25@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$26@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$29@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$2@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$35@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$36@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$38@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$3@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$42@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$44@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$4@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$51@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$5@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$69@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$6@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$7@?0???0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$7@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$84@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$8@?0???0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$8@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA ?dtor$9@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??$_UIntegral_to_string@DI@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@I@Z $pdata$??$_UIntegral_to_string@DI@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@I@Z $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIInputLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@XZ $pdata$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@XZ $cppxdata$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@XZ $stateUnwindMap$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@XZ $ip2state$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@XZ $unwind$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@V?$initializer_list@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@@std@@@Z $pdata$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@V?$initializer_list@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@@std@@@Z $cppxdata$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@V?$initializer_list@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@@std@@@Z $stateUnwindMap$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@V?$initializer_list@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@@std@@@Z $ip2state$??0?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAA@V?$initializer_list@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@@std@@@Z $unwind$??4?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAAAEAU01@$$QEAU01@@Z $pdata$??4?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAAAEAU01@$$QEAU01@@Z $cppxdata$??4?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAAAEAU01@$$QEAU01@@Z $stateUnwindMap$??4?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAAAEAU01@$$QEAU01@@Z $ip2state$??4?$static_vector@V?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@$04@nvrhi@@QEAAAEAU01@$$QEAU01@@Z $unwind$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $pdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1GraphicsPipelineDesc@nvrhi@@QEAA@XZ $unwind$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $pdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ $pdata$?_Decref@_Ref_count_base@std@@QEAAXXZ $unwind$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $unwind$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIGraphicsPipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z $pdata$??0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z $cppxdata$??0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z $stateUnwindMap$??0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z $ip2state$??0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z $unwind$?dtor$146@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $pdata$?dtor$146@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $unwind$?dtor$35@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $pdata$?dtor$35@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $unwind$?dtor$38@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $pdata$?dtor$38@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $unwind$?dtor$44@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $pdata$?dtor$44@?0???0TemporalAntiAliasingPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@6@AEBVICompositeView@engine@2@AEBUCreateParameters@012@@Z@4HA $unwind$?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z $pdata$?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z $chain$9$?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z $pdata$9$?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z $chain$10$?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z $pdata$10$?RenderMotionVectors@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBVICompositeView@engine@3@1U?$vector@M$02@math@3@@Z $unwind$?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z $pdata$?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z $chain$4$?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z $pdata$4$?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z $chain$5$?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z $pdata$5$?TemporalResolve@TemporalAntiAliasingPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@AEBUTemporalAntiAliasingParameters@23@_NAEBVICompositeView@engine@3@3@Z $unwind$?AdvanceFrame@TemporalAntiAliasingPass@render@donut@@QEAAXXZ $pdata$?AdvanceFrame@TemporalAntiAliasingPass@render@donut@@QEAAXXZ $cppxdata$?AdvanceFrame@TemporalAntiAliasingPass@render@donut@@QEAAXXZ $stateUnwindMap$?AdvanceFrame@TemporalAntiAliasingPass@render@donut@@QEAAXXZ $ip2state$?AdvanceFrame@TemporalAntiAliasingPass@render@donut@@QEAAXXZ $unwind$?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ $pdata$?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ $chain$1$?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ $pdata$1$?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ $chain$2$?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ $pdata$2$?GetCurrentPixelOffset@TemporalAntiAliasingPass@render@donut@@QEAA?AU?$vector@M$01@math@3@XZ $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ $unwind$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $pdata$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $cppxdata$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $stateUnwindMap$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $ip2state$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $unwind$??1FramebufferFactory@engine@donut@@UEAA@XZ $pdata$??1FramebufferFactory@engine@donut@@UEAA@XZ $cppxdata$??1FramebufferFactory@engine@donut@@UEAA@XZ $stateUnwindMap$??1FramebufferFactory@engine@donut@@UEAA@XZ $ip2state$??1FramebufferFactory@engine@donut@@UEAA@XZ $unwind$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $cppxdata$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $ip2state$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $unwind$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $cppxdata$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $stateUnwindMap$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $ip2state$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $unwind$?_Xlength@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $pdata$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $cppxdata$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $stateUnwindMap$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $ip2state$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $unwind$??1ShaderMacro@engine@donut@@QEAA@XZ $pdata$??1ShaderMacro@engine@donut@@QEAA@XZ $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $unwind$?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z $pdata$?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z $unwind$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $chain$0$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$0$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $chain$1$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$1$??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ $unwind$??$inverse@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@@Z $pdata$??$inverse@M$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@@Z $unwind$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $pdata$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $unwind$??$inverse@M$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@@Z $pdata$??$inverse@M$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@@Z $unwind$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $pdata$??$affineToHomogeneous@M$02@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU?$affine@M$02@01@@Z $unwind$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $pdata$??$?DM$03$03$03@math@donut@@YA?AU?$matrix@M$03$03@01@AEBU201@0@Z $unwind$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $pdata$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $cppxdata$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $stateUnwindMap$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $ip2state$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $unwind$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $unwind$??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $unwind$??$?0PEAD$0A@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@PEAD0AEBV?$allocator@D@1@@Z $pdata$??$?0PEAD$0A@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@PEAD0AEBV?$allocator@D@1@@Z $unwind$??$inverse@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@@Z $pdata$??$inverse@M$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@@Z $unwind$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $pdata$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $unwind$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $pdata$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $cppxdata$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $stateUnwindMap$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $tryMap$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $handlerMap$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $ip2state$??$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z $unwind$?catch$7@?0???$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z@4HA $pdata$?catch$7@?0???$_Emplace_reallocate@UShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAPEAUShaderMacro@engine@donut@@QEAU234@$$QEAU234@@Z@4HA $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z $pdata$??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z $chain$0$??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z $pdata$0$??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z $chain$1$??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z $pdata$1$??$generate_canonical@M$0BI@V?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@std@@@std@@YAMAEAV?$mersenne_twister_engine@I$0CA@$0CHA@$0BIN@$0BP@$0JJAILANP@$0L@$0PPPPPPPP@$06$0JNCMFGIA@$0P@$0OPMGAAAA@$0BC@$0GMAHIJGF@@0@@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7FramebufferFactory@engine@donut@@6B@ ??_C@_01HIHLOKLC@1@ ??_C@_01GBGANLPD@0@ ??_C@_0M@KJFOIAJ@USE_STENCIL@ ??_C@_04GHJNJNPO@main@ ??_C@_0CE@LJBOCING@donut?1passes?1motion_vectors_ps?4@ ??_C@_0N@MMOMBOGJ@SAMPLE_COUNT@ ??_C@_0BH@EGMOBDHI@USE_CATMULL_ROM_FILTER@ ??_C@_0BJ@NFIMDEIH@donut?1passes?1taa_cs?4hlsl@ ??_C@_0BO@LMDBHFHF@TemporalAntiAliasingConstants@ ??_C@_0O@LBMLMKKC@MotionVectors@ ??_C@_0L@BMCBMHNB@TemporalAA@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R4FramebufferFactory@engine@donut@@6B@ ??_R0?AVFramebufferFactory@engine@donut@@@8 ??_R3FramebufferFactory@engine@donut@@8 ??_R2FramebufferFactory@engine@donut@@8 ??_R1A@?0A@EA@FramebufferFactory@engine@donut@@8 __real@2f800000 __real@358637bd __real@38d1b717 __real@3f000000 __real@3f11e10d __real@3f413fa9 __real@3f800000 __real@4cbebc20 __security_cookie __xmm@0000000000000000000000003f800000 __xmm@00000000000000003f80000000000000 __xmm@000000000000000f0000000000000000 __xmm@000000003f8000000000000000000000 __xmm@3e400000bd800000be4000003d800000 __xmm@3f800000000000000000000000000000 __xmm@7fffffff7fffffff7fffffff7fffffff __xmm@80000000800000008000000080000000 __xmm@bd800000bee000003ea00000bea00000 __xmm@bea00000be4000003d8000003ea00000 __xmm@bee000003ee000003ee000003e400000 