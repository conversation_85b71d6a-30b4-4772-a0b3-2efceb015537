prefix=D:/RTXPT/cmake-build-release-visual-studio/install
exec_prefix=${prefix}
includedir=D:/RTXPT/cmake-build-release-visual-studio/install/include
libdir=D:/RTXPT/cmake-build-release-visual-studio/install/lib

Name: GLFW
Description: A multi-platform library for OpenGL, window and input
Version: 3.4.0
URL: https://www.glfw.org/
Requires.private: 
Libs: -L${libdir} -lglfw3
Libs.private:  -lgdi32
Cflags: -I${includedir}
