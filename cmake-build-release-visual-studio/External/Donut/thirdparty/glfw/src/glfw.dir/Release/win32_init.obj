d唄 h馟h嶌  �      .drectve        ]   T               
 .debug$S        L�  �  龗         @ B.text$mn        
  %�  2�      G    P`.debug$S        \  鴻  T�         @B.text$mn        �     ��          P`.debug$S        �  袦  笧         @B.text$mn        �   ��  G�          P`.debug$S        �  棤  儮         @B.text$mn        �  K�  
�      /    P`.debug$S        �  悌  g�      H   @B.text$mn          7�  =�      	    P`.debug$S        �  棾  摰         @B.text$mn        �   G�  攵          P`.debug$S        �  1�  透         @B.text$mn        �   m�  C�          P`.debug$S          壓  暭         @B.text$mn          q�  w�          P`.debug$S        �    ]�      8   @B.text$mn          嵟  暻          P`.debug$S        �  �           @B.text$mn        �    勎      @    P`.debug$S        h  �  l�         @B.text$mn        �   ㄒ  @�          P`.debug$S        ,  r�  炚         @B.text$mn        !  z�  涃      _    P`.debug$S        �  Q�  龠         @B.xdata             �              @0@.pdata             !�  -�         @0@.xdata             K�  _�         @0@.pdata             i�  u�         @0@.xdata          (   撪  秽         @0@.pdata             汆  遴         @0@.xdata             �  �         @0@.pdata             1�  =�         @0@.xdata             [�              @0@.pdata             c�  o�         @0@.xdata             嶀              @0@.pdata             欋  メ         @0@.xdata             冕  揍         @0@.pdata             踽  �         @0@.xdata             �  3�         @0@.pdata             Q�  ]�         @0@.xdata             {�              @0@.pdata             忊  涒         @0@.xdata             光  砚         @0@.pdata             垅  玮         @0@.xdata             �  �         @0@.pdata             #�  /�         @0@.xdata             M�  i�         @0@.pdata             s�  �         @0@.xdata          (   濄  陪         @0@.pdata             香  坫         @0@.xdata               	�         @0@.pdata             �  �         @0@.xdata             =�  Q�         @0@.pdata             o�  {�         @0@.xdata             欎  ╀         @0@.pdata             卿  愉         @0@.xdata             皲              @0@.pdata               	�         @0@.xdata             '�              @0@.pdata             ;�  G�         @0@.rdata             e�              @ @@.rdata          ,   u�              @@@.rdata             ″              @@@.rdata          !                 @@@.rdata             湾              @@@.rdata             噱              @@@.rdata                           @@@.rdata             �              @@@.rdata             4�              @@@.rdata             D�              @@@.rdata             ]�              @@@.rdata             t�              @@@.rdata             ��              @@@.rdata             撴              @@@.rdata             ℃              @@@.rdata                           @@@.rdata             挎              @@@.rdata             玩              @@@.rdata             坻              @@@.rdata             矜              @@@.rdata              �              @@@.rdata             �              @@@.rdata          	   #�              @@@.rdata             ,�              @@@.rdata             F�              @@@.rdata             ^�              @@@.rdata             i�              @@@.rdata             ��              @@@.rdata          
   戠              @@@.rdata             涚              @@@.rdata             扮              @@@.rdata          .   淑              @@@.rdata          (                 @@@.rdata          &    �              @@@.rdata          +   F�              @@@.rdata          )   q�              @@@.rdata             氳              @0@.rdata             ¤              @P@.rdata             辫              @P@.rdata             凌              @P@.debug$T        |   谚              @ B.chks64         @  M�               
     /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   t     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\glfw.dir\Release\win32_init.obj : < `  �  & y�   & y�  Microsoft (R) Optimizing Compiler   �   粿       URLZONE_LOCAL_MACHINE     URLZONE_INTRANET     URLZONE_TRUSTED     URLZONE_INTERNET  n    URLZONEREG_DEFAULT  n   URLZONEREG_HKLM # �   BINDHANDLETYPES_DEPENDENCY  �    PIDMSI_STATUS_NORMAL  �   PIDMSI_STATUS_NEW  �   PIDMSI_STATUS_PRELIM  �   PIDMSI_STATUS_DRAFT ! �   PIDMSI_STATUS_INPROGRESS  �   PIDMSI_STATUS_EDIT  �   PIDMSI_STATUS_REVIEW  �   PIDMSI_STATUS_PROOF ! �    COINITBASE_MULTITHREADED ' �  �   CLSCTX_ACTIVATE_X86_SERVER , �   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL & ]   PROCESS_PER_MONITOR_DPI_AWARE  �    MDT_EFFECTIVE_DPI  �    NODE_INVALID  �   NODE_ELEMENT  �   NODE_ATTRIBUTE  �   NODE_TEXT  �   NODE_CDATA_SECTION  �   NODE_ENTITY_REFERENCE  �   NODE_ENTITY $ �   NODE_PROCESSING_INSTRUCTION  �   NODE_COMMENT  �  	 NODE_DOCUMENT  �  
 NODE_DOCUMENT_TYPE  �   NODE_DOCUMENT_FRAGMENT  �    XMLELEMTYPE_ELEMENT  �   XMLELEMTYPE_TEXT  �   XMLELEMTYPE_COMMENT  �   XMLELEMTYPE_DOCUMENT  �   XMLELEMTYPE_DTD  �   XMLELEMTYPE_PI  �   VT_I2  �   VT_I4  �   VT_BSTR  �  	 VT_DISPATCH  �  
 VT_ERROR  �   VT_VARIANT  �  
 VT_UNKNOWN  �   VT_I1  �   VT_I8  �  $ VT_RECORD  �  � �VT_RESERVED  
d        _glfw  �    TYSPEC_CLSID  �   TYSPEC_FILEEXT  �   TYSPEC_MIMETYPE  �   TYSPEC_FILENAME  �   TYSPEC_PROGID  �   TYSPEC_PACKAGENAME + w   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 w   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - w   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 w   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS * �   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP ( q        _glfw_GUID_DEVINTERFACE_HID     PowerUserMaximum  �    ServerApplication  ~    IdleShutdown  u   COR_VERSION_MAJOR_V2  	    TKIND_ENUM  	   TKIND_RECORD  	   TKIND_MODULE  	   TKIND_INTERFACE  	   TKIND_DISPATCH  	   TKIND_COCLASS  	   TKIND_ALIAS  	   TKIND_UNION  �   CC_CDECL  �   CC_MSCPASCAL  �   CC_PASCAL  �   CC_MACPASCAL  �   CC_STDCALL  �   CC_FPFASTCALL  �   CC_SYSCALL  �   CC_MPWCDECL  �   CC_MPWPASCAL  �    FUNC_VIRTUAL  �   FUNC_PUREVIRTUAL  �   FUNC_NONVIRTUAL  �   FUNC_STATIC  �    VAR_PERINSTANCE  �   VAR_STATIC  �   VAR_CONST # �   BINDSTATUS_FINDINGRESOURCE  �   BINDSTATUS_CONNECTING  �   BINDSTATUS_REDIRECTING % �   BINDSTATUS_BEGINDOWNLOADDATA # �   BINDSTATUS_DOWNLOADINGDATA # �   BINDSTATUS_ENDDOWNLOADDATA + �   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �   BINDSTATUS_INSTALLINGCOMPONENTS ) �  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �  
 BINDSTATUS_USINGCACHEDCOPY " �   BINDSTATUS_SENDINGREQUEST $ �   BINDSTATUS_CLASSIDAVAILABLE % �  
 BINDSTATUS_MIMETYPEAVAILABLE * �   BINDSTATUS_CACHEFILENAMEAVAILABLE & �   BINDSTATUS_BEGINSYNCOPERATION $ �   BINDSTATUS_ENDSYNCOPERATION # �   BINDSTATUS_BEGINUPLOADDATA ! �   BINDSTATUS_UPLOADINGDATA ! �   BINDSTATUS_ENDUPLOADDATA # �   BINDSTATUS_PROTOCOLCLASSID  �   BINDSTATUS_ENCODING - �   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �   BINDSTATUS_CLASSINSTALLLOCATION  �   BINDSTATUS_DECODING & �   BINDSTATUS_LOADINGMIMEHANDLER , �   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �   BINDSTATUS_FILTERREPORTMIMETYPE ' �   BINDSTATUS_CLSIDCANINSTANTIATE % �   BINDSTATUS_IUNKNOWNAVAILABLE  �   BINDSTATUS_DIRECTBIND  �   BINDSTATUS_RAWMIMETYPE " �    BINDSTATUS_PROXYDETECTING   �  ! BINDSTATUS_ACCEPTRANGES  �  " BINDSTATUS_COOKIE_SENT + �  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �  $ BINDSTATUS_COOKIE_SUPPRESSED ( �  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �  ' BINDSTATUS_COOKIE_STATE_REJECT ' �  ( BINDSTATUS_COOKIE_STATE_PROMPT & �  ) BINDSTATUS_COOKIE_STATE_LEASH * �  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �  + BINDSTATUS_POLICY_HREF  �  , BINDSTATUS_P3P_HEADER + �  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + �  / BINDSTATUS_SESSION_COOKIES_ALLOWED   �  0 BINDSTATUS_CACHECONTROL . �  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) �  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & �  3 BINDSTATUS_PUBLISHERAVAILABLE ( �  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �  5 BINDSTATUS_SSLUX_NAVBLOCKED , �  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �  8 BINDSTATUS_64BIT_PROGRESS  �  8 BINDSTATUS_LAST  �  9 BINDSTATUS_RESERVED_0  �  : BINDSTATUS_RESERVED_1  �  ; BINDSTATUS_RESERVED_2  �  < BINDSTATUS_RESERVED_3  �  = BINDSTATUS_RESERVED_4  �  > BINDSTATUS_RESERVED_5  �  ? BINDSTATUS_RESERVED_6  �  @ BINDSTATUS_RESERVED_7  �  A BINDSTATUS_RESERVED_8  �  B BINDSTATUS_RESERVED_9  �  C BINDSTATUS_RESERVED_A  �  D BINDSTATUS_RESERVED_B  �  E BINDSTATUS_RESERVED_C  �  F BINDSTATUS_RESERVED_D  �  G BINDSTATUS_RESERVED_E  �  H BINDSTATUS_RESERVED_F  �  I BINDSTATUS_RESERVED_10  �  J BINDSTATUS_RESERVED_11  �  K BINDSTATUS_RESERVED_12  �  L BINDSTATUS_RESERVED_13  �  M BINDSTATUS_RESERVED_14  �    DESCKIND_NONE  �   DESCKIND_FUNCDESC  �   DESCKIND_VARDESC  �   DESCKIND_TYPECOMP   �   DESCKIND_IMPLICITAPPOBJ  �    CIP_DISK_FULL  �   CIP_ACCESS_DENIED ! �   CIP_NEWER_VERSION_EXISTS ! �   CIP_OLDER_VERSION_EXISTS  �   CIP_NAME_CONFLICT 1 �   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + �   CIP_EXE_SELF_REGISTERATION_TIMEOUT  �   CIP_UNSAFE_TO_ABORT  �   CIP_NEED_REBOOT % �   TP_CALLBACK_PRIORITY_INVALID  �   BINDSTRING_HEADERS   �   BINDSTRING_ACCEPT_MIMES  �   BINDSTRING_EXTRA_URL  �   BINDSTRING_LANGUAGE  �   BINDSTRING_USERNAME  �   BINDSTRING_PASSWORD  �   BINDSTRING_UA_PIXELS  �   BINDSTRING_UA_COLOR  �  	 BINDSTRING_OS  �  
 BINDSTRING_USER_AGENT $ �   BINDSTRING_ACCEPT_ENCODINGS  �   BINDSTRING_POST_COOKIE " �  
 BINDSTRING_POST_DATA_MIME  �   BINDSTRING_URL  �   BINDSTRING_IID ' �   BINDSTRING_FLAG_BIND_TO_OBJECT $ �   BINDSTRING_PTR_BIND_CONTEXT  �   BINDSTRING_XDR_ORIGIN   �   BINDSTRING_DOWNLOADPATH  �   BINDSTRING_ROOTDOC_URL $ �   BINDSTRING_INITIAL_FILENAME " �   BINDSTRING_PROXY_USERNAME " �   BINDSTRING_PROXY_PASSWORD ! �   BINDSTRING_ENTERPRISE_ID  �   BINDSTRING_DOC_URL  �    SYS_WIN16  �   SYS_WIN32  �   SYS_MAC  �   PARSE_CANONICALIZE  �   PARSE_FRIENDLY  �   PARSE_SECURITY_URL  �   PARSE_ROOTDOCUMENT  �   PARSE_DOCUMENT  �   PARSE_ANCHOR ! �   PARSE_ENCODE_IS_UNESCAPE  �   PARSE_DECODE_IS_ESCAPE  �  	 PARSE_PATH_FROM_URL  �  
 PARSE_URL_FROM_PATH  �   PARSE_MIME  �   PARSE_SERVER  �  
 PARSE_SCHEMA  �   PARSE_SITE  �   PARSE_DOMAIN  �   PARSE_LOCATION  �   PARSE_SECURITY_DOMAIN  �   PARSE_ESCAPE  s   PSU_DEFAULT  �   QUERY_EXPIRATION_DATE " �   QUERY_TIME_OF_LAST_CHANGE  �   QUERY_CONTENT_ENCODING  �   QUERY_CONTENT_TYPE  �   QUERY_REFRESH  �   QUERY_RECOMBINE  �   QUERY_CAN_NAVIGATE  �   QUERY_USES_NETWORK  �  	 QUERY_IS_CACHED   �  
 QUERY_IS_INSTALLEDENTRY " �   QUERY_IS_CACHED_OR_MAPPED  �   QUERY_USES_CACHE  �  
 QUERY_IS_SECURE  �   QUERY_IS_SAFE ! �   QUERY_USES_HISTORYFOLDER  �    CHANGEKIND_ADDMEMBER   �   CHANGEKIND_DELETEMEMBER  �   CHANGEKIND_SETNAMES $ �   CHANGEKIND_SETDOCUMENTATION  �   CHANGEKIND_GENERAL  �   CHANGEKIND_INVALIDATE   �   CHANGEKIND_CHANGEFAILED    LPCDIDEVICEINSTANCEW  "   D3DCOLOR  �  _DICOLORSET # 8  LPDICONFIGUREDEVICESCALLBACK 
   LPLONG " :  LPDICONFIGUREDEVICESPARAMSW  �  DIDEVICEINSTANCEW    LPDIRECTINPUTDEVICE8W  �  _FILETIME    LPDIEFFECT    LPDIENUMDEVICESCALLBACKW  �  LPDIENVELOPE  �  DICOLORSET  �  IDirectInputEffect 
 *  LPGUID  �  _DIACTIONW $ 2  LPDIENUMDEVICESBYSEMANTICSCBW  �  DIEFFECTINFOW  X  LPDIPROPHEADER    IDirectInputEffectVtbl  �  RECT ! B  _DICONFIGUREDEVICESPARAMSW  ?  IDirectInput8WVtbl  �  GLFWdeallocatefun  �  LPDIDEVICEIMAGEINFOW  �  DIEFFECT  �  LPCDIFILEEFFECT + �  LPDIENUMCREATEDEFFECTOBJECTSCALLBACK  �  DIENVELOPE  �  LPDIOBJECTDATAFORMAT  �  LPCDIEFFECT  �  PFN_wglGetCurrentDC  �  PFN_wglMakeCurrent  �  HGLRC  �  FILETIME  �  LPDIACTIONW  �  LPDIEFFESCAPE  �  DIDEVCAPS  �  _DIDEVICEIMAGEINFOW  �  _GLFWtlsWin32  {  LPDIDEVICEINSTANCEW  ]  LPCDIPROPHEADER  �  GLFWallocatefun ( �  PFNWGLCREATECONTEXTATTRIBSARBPROC  �  DIDEVICEOBJECTDATA  �  _GLFWcontextWGL  g  LPDIDEVICEOBJECTDATA  �  LIST_ENTRY  �  DIPROPHEADER  �  DIDATAFORMAT % S  LPDIENUMDEVICEOBJECTSCALLBACKW  �  LPDIRECTINPUTEFFECT  �  GLFWreallocatefun   �  PFNWGLSWAPINTERVALEXTPROC  �  PFN_wglCreateContext  �  RTL_CRITICAL_SECTION  �  DIEFFESCAPE  �  _DIOBJECTDATAFORMAT  �  LPDIFILEEFFECT  �  PFN_wglGetCurrentContext  �  LPDIEFFECTINFOW  �  LPCDIDEVICEOBJECTDATA  �  _LIST_ENTRY " �  PRTL_CRITICAL_SECTION_DEBUG  J  LPDIDEVCAPS " �  _RTL_CRITICAL_SECTION_DEBUG  �  _DIACTIONFORMATW  /  LPDIACTIONFORMATW  m  LPCDIDATAFORMAT  �  XINPUT_VIBRATION  �  HGLRC__  �  PFNGLGETSTRINGIPROC  �  DIFILEEFFECT  �  _DIDATAFORMAT  �  PFNGLGETSTRINGPROC ' �  PFNWGLGETEXTENSIONSSTRINGEXTPROC ! �  LPDIDEVICEIMAGEINFOHEADERW 
 p   int8_t ! P  LPCDIDEVICEOBJECTINSTANCEW  �  PFN_wglShareLists      GLubyte 
 @  LPCSTR  �  IDirectInput8W  �  PROC  !   uint16_t   w  LPDIDEVICEOBJECTINSTANCEW  "  LPDWORD  �  LPDIENUMEFFECTSCALLBACKW  �  PFNGLGETINTEGERVPROC   �  _DIDEVICEIMAGEINFOHEADERW      uint8_t $ �  LPDIENUMEFFECTSINFILECALLBACK  �  DIDEVICEOBJECTINSTANCEW  �  PFN_wglGetProcAddress  ~  _GLFWtls  �  LPCDIEFFECTINFOW % E  PFN_OSMesaCreateContextAttribs  $  PFN_eglGetDisplay  �  IDirectInputDevice8WVtbl ' �  PFNWGLGETEXTENSIONSSTRINGARBPROC  �  GLFWallocator  8  PFN_eglSwapInterval    EGLConfig  �  _GLFWtimerWin32    _GLFWwindowNull  �  PFN_wglDeleteContext  Y  GLFWcharfun  �  _GLFWlibraryNull 
 �  HDC  �  _RTL_CRITICAL_SECTION  )  PFN_eglInitialize  �  CRITICAL_SECTION  �  _GLFWmutexWin32  5  PFN_eglMakeCurrent  �  HDC__  
  IDirectInputDevice8W  	  _GLFWjoyobjectWin32  J  GLFWwindowfocusfun   M  GLFWwindowcontentscalefun  +  PFN_eglTerminate  G  PFN_OSMesaDestroyContext * �  PFNWGLGETPIXELFORMATATTRIBIVARBPROC  &  PFN_eglGetError  �  _GLFWlibraryWGL 
 u   GLenum  J  GLFWcursorenterfun 
 u   GLuint    EGLContext  V  GLFWmonitor  M  PFN_OSMesaGetDepthBuffer  :  PFN_eglQueryString    OSMesaContext  D  GLFWwindowsizefun  2  PFN_eglDestroySurface  �  _GLFWcontext    PFN_eglGetConfigAttrib  2  GLFWglproc  J  GLFWwindowiconifyfun  2  PFN_eglDestroyContext  2  PFN_eglSwapBuffers  `  GLFWdropfun    _GLFWjoystickWin32  J  GLFWwindowmaximizefun  u   UINT  �  VkAllocationCallbacks  0  PFN_eglCreateContext  �  _TP_CALLBACK_PRIORITY  �  HICON  @  GLFWwindow  �  GLFWgammaramp  �  WNDPROC  ~  tagShutdownType  �  tagRAWMOUSE  �  MONITOR_DPI_TYPE  �  tagCALLCONV  	  tagTYPEKIND     LRESULT    IUnknownVtbl  #   rsize_t  &  PFN_SetProcessDPIAware ' �  _DEV_BROADCAST_DEVICEINTERFACE_W  |  XINPUT_STATE  z  _OSVERSIONINFOEXW  d  _GLFWlibrary ( w  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  w  _XINPUT_CAPABILITIES  M  PFN_OSMesaGetColorBuffer  �  tagRAWINPUTHEADER  s  _GLFWwindowWin32  s  _tagPSUACTION  J  PFN_OSMesaMakeCurrent $ =  PFN_EnableNonClientDpiScaling     LONG_PTR  Z  GLFWmonitorfun  j  PFN_RtlVerifyVersionInfo  �  tagBINDSTRING  !   ATOM / 0  PFNEGLCREATEPLATFORMWINDOWSURFACEEXTPROC  #   ULONG_PTR    tagURLZONE & =  PFNEGLGETPLATFORMDISPLAYEXTPROC    VkPhysicalDevice  �  __MIDL_ICodeInstall_0001  p  PCHAR  �  tagBINDSTATUS  G  GLFWwindowclosefun  q  _GUID  n  _URLZONEREG  n  _GLFWlibraryWin32 
 �  HRGN__  !   wchar_t  V  GLFWkeyfun    HINSTANCE    EGLNativeDisplayType 
 t   EGLint  !   WORD  �  _XINPUT_VIBRATION 
 �  tagMSG    _GLFWwndconfig  2  PFN_vkVoidFunction  �  HCURSOR  �  VkResult  #   uint64_t ' �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS    _USER_ACTIVITY_PRESENCE  �  _XINPUT_GAMEPAD  w  XINPUT_CAPABILITIES    PLONG  �  _GLFWmapelement    _GLFWinitconfig  �  XINPUT_GAMEPAD  p  va_list  P  GLFWmousebuttonfun ( ?  PFN_SetProcessDpiAwarenessContext 
    LPARAM  �  HRGN      BYTE  �  _GLFWcursorWin32 
 5  PCWSTR  �  PFN_OSMesaGetProcAddress  �  HMENU__    EGLDisplay  �  _GLFWmonitorWin32   S  PFN_vkGetInstanceProcAddr     LONG  -  PFN_eglBindAPI  u   EGLBoolean  �  _GLFWplatform   .  PFN_XInputGetCapabilities # F  PFN_AdjustWindowRectExForDpi  S  GLFWscrollfun  �  HINSTANCE__    EGLSurface    HDEVNOTIFY    HMODULE  �  DWM_BLURBEHIND  �  HBRUSH__ ! B  PFN_OSMesaCreateContextExt  ]  GLFWjoystickfun 
 #   SIZE_T ! 0  PFN_eglCreateWindowSurface # u  ReplacesCorHdrNumericDefines  "   DWORD  �  CHANGEFILTERSTRUCT  �  RAWMOUSE    EGLNativeWindowType  �  RAWINPUT 
   PSHORT  �  RAWINPUTHEADER  q  _GLFWcursor  "   TP_VERSION  @  LPCCH 
 5  LPCWCH  D  GLFWframebuffersizefun  �  tagRECT  �  tagWNDCLASSEXW  \  GLFWcharmodsfun  �  VARENUM    HWND 
 q  LPTSTR  �  LPMSG & �  DEV_BROADCAST_DEVICEINTERFACE_W  D  GLFWwindowposfun    VkInstance  �  HMONITOR__  l  GLFWvidmode  #   DWORD64  S  GLFWcursorposfun  G  GLFWwindowrefreshfun  j  _GLFWerror  !  PFN_eglGetConfigs  %  PFN_DirectInput8Create  �  HMONITOR      BOOLEAN  �  WNDCLASSEXW  �  tagTYSPEC $ V  PFN_DwmEnableBlurBehindWindow  #   VkSurfaceKHR 
 !   USHORT 
 �  MSG  f  _GLFWwindow  g  _GLFWmutex ! I  PFN_GetSystemMetricsForDpi  �  tagVARKIND  /  _GLFWctxconfig    PVOID  A  PFN_GetDpiForWindow  �  PFN_eglGetProcAddress  �  IUnknown  t   errno_t " Y  PFN_DwmGetColorizationColor  q   WCHAR     PBYTE  t   GLint  u   EGLenum  P  PFN_DwmFlush 
 q  IID  �  _tagQUERYOPTION    tagPOINT  �  _GLFWmapping 
 t  LPBOOL  3  PFN_XInputGetState     HRESULT    _GLFWmonitorNull ! �  __MIDL_IGetBindHandle_0001  ]  PROCESS_DPI_AWARENESS  z  OSVERSIONINFOEXW 
    LONG64  �  tagCOINITBASE  "   ULONG  �  RAWKEYBOARD  t   BOOL  �  tagApplicationType  5  LPCWSTR 
 �  RAWHID  �  tagDOMNodeType 
 #   WPARAM    HICON__  �  tagRAWHID  q  PWSTR  �  tagCHANGEKIND 
 u   UINT32  �  tagSYSKIND  �  tagRAWINPUT  �  _GLFWjoystick  #   uintptr_t 
 q  LPWSTR  #   UINT_PTR 
   LPVOID  q  GUID  �  tagRAWKEYBOARD  �  HMENU 
 C  LPRECT  �  tagFUNCKIND " N  PFN_DwmIsCompositionEnabled  t   INT32 
   HANDLE  |  _XINPUT_STATE  �  PIDMSI_STATUS_VALUE  #   ULONGLONG ( �  PTP_CLEANUP_GROUP_CANCEL_CALLBACK & :  PFN_ChangeWindowMessageFilterEx  2  GLFWproc 
 #   size_t  s  _GLFWfbconfig ! `  PFN_SetProcessDpiAwareness    POINT  �  tagGLOBALOPT_EH_VALUES 
 �  HBRUSH  "  LPUNKNOWN     SHORT  (  LPCVOID    PLONG64  �  GLFWimage  �  tagCLSCTX     INT_PTR  c  PFN_GetDpiForMonitor  u   uint32_t  t   GLFWbool  �  tagXMLEMEM_TYPE  p   CHAR  �  _tagPARSEACTION  p  LPSTR  �  tagDESCKIND  �  _GLFWmonitor 
 �  HWND__ �   X      頴}�穲町v�
c�.丨a� �+篬鰌�莩�  I    �-�雧n�5L屯�:I硾�鮎访~(梱  �    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �    )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     8�'预P�憖�0R�(3銖� pN*�  g   6觏v畿S倂9紵"�%��;_%z︹  �   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  E   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  �   D���0�郋鬔G5啚髡J竆)俻w��  �   錵s铿勃砓b棬偡遯鮓尛�9泂惻  *   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  x   �F9�6K�v�/亅S诵]t婻F廤2惶I  �   d2軇L沼vK凔J!女計j儨杹3膦���  
   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  ]   樸7 忁�珨��3]"Fキ�:�,郩�  �    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   衠琪槡铟钭}_XO>�蛭X�7Mp处d  B   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  �   �D;窼僞k渔A�;��?缞鳗5翰�?*R
     5 KO诹硃毣�'R烣�7`埀M@懅y榵  [   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  �   聤�苮g8鄞<aZ�%4)闪�|袉uh�  �   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  9   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  �   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  l   �:2K] �
j�苊赁e�
湿�3k椨�  �   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  	   6��7@L�.�梗�4�檕�!Q戸�$�  N	   �儔14褥緅�3]饃鹷�hK3g搋bA竑  �	   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  �	   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  0
   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  y
   鲃P莱砣5餈裛騕摻!Z�碔V(t伪壼�  �
   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  �
   渐袿.@=4L笴速婒m瑜;_琲M %q�  L   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/  �   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  >   �>2
^�﨟2W酟傲X{b?荼猲�;  }   死╇D�#/��4鶄>G63齛w�i�->M  �   E縄�7�g虩狱呂�/y蛨惏l斋�笵  
   c�#�'�縌殹龇D兺f�$x�;]糺z�  Z
   $G\|R_熖泤煡4勄颧绖�?(�~�:  �
   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠        栀��綔&@�.�)�C�磍萘k  G   釳�>�H?6蓙�� 祍<ベ垽�=j逃�  �   2W瓓�<X	綧]�龐IE?'笼t唰��  �   晁X歌符�2澋U�'煈覽b�
蟣;-�     |q�6桢赤汗mv訔�	
爟~胱�>?妼BK�,  a   �"睱建Bi圀対隤v��cB�'窘�n  �   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏     bRè1�5捘:.z錨{娯啹}坬麺P  Q   攄繠�
\b擫5`Om�1悑R钡h�:�47�  �   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  �   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒     5睔`&N_鏃|�<�$�獖�!銸]}"  d   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �   ┫緞A$窄�0� NG�%+�*�
!7�=b      镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  Q   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  N   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  �   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  A   噔园�,c珥珸洯濠�繗猍=sZ導  z   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   ��嵉氒髅嘁棭够*ヅ�
�'徺p4     孆x�0队<堛�猬dh梧`sR顛	k�7[M@  X   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  �   鷜E恳B�#蠠�,qC吾w�岧儁N篴  �   艶笊\眔Z%師}wы孜+HN鯥湔N     yk"&�棎墑�T-亴鏏愐煋搂脠+]�  Z   晊褃 �=�韀晝阓�!熝神�+k�2�<$]�  �   蠯3
掽K謈 � l�6襕鞜��H#�  �   �8��/X昋旒�.胱#h=J"髈篒go#     蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  q   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  X   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  /   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   Eム聂�
C�?潗'{胿D'x劵;釱�     r�L剟FsS鏴醼+E千I呯贄0鬬/�  ]   �*o驑瓂a�(施眗9歐湬

�  �   鹰杩@坓!)IE搒�;puY�'i憷n!  �   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  C    I嘛襨签.濟;剕��7啧�)煇9触�.  �    萾箒�$.潆�j閖i转pf-�稃陞��  �   �5�-�塎To>我c撙侹�迫�蛷铕.~�!     娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  i   G髼*悭�2睆�侻皣軁舃裄樘珱)  �   齛|)3h�2%籨糜/N_燿C虺r_�9仌     8蟴B或绢溵9"C dD揭鞧Vm5TB�  O   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  �   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  /   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  y   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   �~鴧傳.P怬WsP-"焫#N�:�&場璁     �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  \   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  �   "�挨	b�'+舒�5<O�呱_歲+/�P�?  �   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  A    ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  �    N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �    �呾��+h7晃O枖��*谵|羓嗡捬  !   j轲P[塵5m榤g摏癭 鋍1O骺�*�  ^!   綔)\�谑U⒊磒'�!W磼B0锶!;  �!   sL&%�znOdz垗�M,�:吶1B滖  �!   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  E"   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �"   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  �"   �n儹`
舔�	Y氀�:b
#p:  1#   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  y#   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �#    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  $   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  Z$   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �$   掴'圭,@H4sS裬�!泉:莠й�"fE)  �$   覽s鴧罪}�'v,�*!�
9E汲褑g;  F%   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �%   +椬恡�
	#G許�/G候Mc�蜀煟-  �%   0T砞獃钎藰�0逪喌I窐G(崹�  &   閯�価=�<酛皾u漑O�髦jx`-�4睲�  h&   戹�j-�99檽=�8熈讠鳖铮�  �&   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  '   ,�<鈬獿鍢憁�g$��8`�"�  N'   潝(綊r�*9�6}颞7V竅\剫�8値�#  �'   ^憖�眜蘓�y冊日/缁ta铁6殔  �'   魯f�u覬n\��zx騖笹笾骊q*砎�,�  2(   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  ~(   _%1糠7硘籺蚻q5饶昈v纪嗈�  �(   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  )   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  Z)   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �   4       �  �  �   �  �  t  �  �    �  �  M  �   *   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\xinput.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dinput.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_init.c C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dbt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_thread.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\internal.h D:\RTXPT\External\Donut\thirdparty\glfw\src\platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_platform.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\glfw.dir\Release\win32_init.obj    �       L  
           
 Z  #   ^  #  
 @UH崿$俐��H侅@  H�    �$  H塂$H�    H塂$H�    H塂$H�    H塂$ H�    H塂$(H�    H塂$0H�    H塂$8H�    H塂$@H�    H塂$HH�    H塂$PH�    H塂$XH�    H塂$`H�    H塂$hH�    H塂$pH�    H塂$xH�    H塃�H�    H塃圚�    H塃怘�    H塃楬�    H塃燞�    H塃℉�    H塃癏�    H塃窰�    H塃繦�    H塃菻�    H塃蠬�    H塃豀�    H塃郒�    H塃鐷�    H塃餒�    H塃鳫�    H塃 H�    H塃H�    H塃H�    H塃H�    H塃 H�    H塃(H�    H塃0H�    H塃8H�    H塃@H�    �   H塃HH�    H塃PH�    H塃XH�    H塃`H�    H塃hH�    H塃pH�    H塃xH�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔�   H�    H墔   H�    H墔  H�    H墔  H�    H墔  H�    H墔   H�    H墔(  H�    H墔0  H�    H墔8  H�$fff�     H崚�    HH崁�   B�@�J�H�B�@�J�H�B�@�J�H�B�J餒冮u� H@ JH0岮B J0H伳@  ]�   L    '   M    3   |    ?   }    K   ~    W   v    c   w    o   �    {   �    �   �    �   �    �       �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �      �      �      �    '  �    2  �    =  �    H  �    S  �    ^  U    i  V    t  W      X    �  Y    �  Z    �  [    �  \    �  ]    �  ^    �  _    �  `    �  a    �  b    �  c      d      e      f    )  g    4  h    B  i    P  j    ^  k    l  l    z  m    �  n    �  o    �  t    �  p    �  q    �  r    �  u    �  s    �  x      y      z    "  {    0  �    >  �    L  �    Z  �    h  �    v  �       �     7 G            
       �        �_glfwConnectWin32  >t    platformID  A         � DP   >T   platform  AK        � >�    win32  D     @                    B  P  t   OplatformID  X  T  Oplatform      �  Owin32  O �   @           
  �     4       Z �   [ ��  � ��  � �  � �,   K    0   K   
 b   K    f   K   
 �   K    �   K   
   K       K   
 H塴$H塼$WH冹@3鞨嬹H塴$8L嬃H塴$03覊l$(归�  D峂�H塴$ �    Hc鴧纔#H�    �  �    3繦媗$XH媡$`H兡@_肏嬒H塡$P�   �    H塴$8A����H塴$0L嬈墊$(3夜辇  H塂$ H嬝�    吚uH�    �  �    H嬎�    3离H嬅H媆$PH媗$XH媡$`H兡@_�7   J    E   �   O   R    s   �    �   J    �   �   �   R    �   �       �   R  H G            �      �   �        �_glfwCreateUTF8FromWideStringWin32  >5   source  AJ          AL       � K   >p    target  AH  w     &  AI  �     / 
 >t     size  A   ;       A   >     � &   Z   �  G  �  I   @                     B  P   5  Osource  95       �   9�       �   O  �   �           �   �  
   t       � �   � �>   � �B   � �S   � �U   � �e   � �w   � ��   � ��   � ��   � ��   � ��   � �,   O    0   O   
 o   O    s   O   
    O    �   O   
 �   O    �   O   
 �   O    �   O   
 �   O    �   O   
 �   O    �   O   
 >  O    B  O   
 N  O    R  O   
 h  O    l  O   
 H塼$WH冹03繦嬹L嬃塂$(3襀塂$ 归�  D岺��    Hc鴧纔H�    �  �    3繦媡$HH兡0_肏嬒H塡$@�   �    A����墊$(L嬈H塂$ 3夜辇  H嬝�    吚u+H�    �  �    H嬎�    H媆$@3繦媡$HH兡0_肏媡$HH嬅H媆$@H兡0_�(   I    6   �   @   R    _   �    �   I    �   �   �   R    �   �       �   _  H G            �   
   �   �        �_glfwCreateWideStringFromUTF8Win32  >@   source  AJ          AL       � <  �   >q    target  AH  c       AI       B ( 
  >t     count  A   ,       A   /     � !  �   Z   �  G  �  I   0                     B  @   @  Osource  9&       �   9       �   O �   x           �   �     l       � �
   � �/   � �3   � �D   � �F   � �Q   � �c   � ��   � ��   � ��   � ��   � �,   N    0   N   
 o   N    s   N   
    N    �   N   
 �   N    �   N   
 �   N    �   N   
 �   N    �   N   
 �   N    �   N   
 K  N    O  N   
 [  N    _  N   
 t  N    x  N   
 @UAUH崿$��H侅X  H�    H3腍墔0  �    吚凟  H墱$p  H壌$x  L墹$P  L壌$H  L壖$@  �    3襀峂癆�   �    3襀�
b"  A秆  �    L嵉�   H壖$�  I侇�  L�=�  E3鞮�%#  �    怉�7凗�凙  侞@  偋   侞N  嚊   菂�   `   菂�   a   菂�   b   菂�   c   菂�   d   菂�   e   菂�   f   菂�   g   菂�   h   菂�   i   菂�   n   菂�   o   菂   j   菂  m   菂  k   A�>��   嬑�    孁D塴$(L崓  L岴扒D$    嬛嬒�    凐�u"D塴$(L崓  L岴扒D$    嬛嬒�    凐|1L塴$8L崊  L塴$0A�   荄$(   3夜辇  L塪$ �    �肐兡I兤I兦侞\  帢��L嫾$@  H峂繪嫶$H  3襆嫟$P  A�  H嫾$�  H嫶$x  H嫓$p  荅�  H荅�
   荅甲:  �    A��   3�    A��   H嬋�    A��   H嬋�    �   H峂癓嬂�(*  吚uH橇�����)  �7E3繟峆岼�    吚t
�   �*  �3褽3缻J�    吚t��)  3狼D$`P   H塃峀$`W缐E�D$dH�    荄$d    H塂$hH�x  D$tH塂$xH�    E擧塃�E�    f��  f吚u-H�    �  �    3繦媿0  H3惕    H伳X  A]]肔塴$XL�    沸A�   H�x  �   H塂$PL塴$HL塴$@荄$8   荄$0   D塴$(D塴$ �    H��  H吚uH�    閞���3襀嬋�        H�
�  H崟  E3繦菂     �  D壄,  菂      �    H��  H崓�   E3蒆��  E3狼D$    �    吚t@H崓�   �    H崓�   �    H��  H崓�   E3汕D$    E3�    吚u黎    �   槎��   �   %   �    Z   �    k   �    t          �    �       �       y  G    �  F    �  F    �  J    k  �    {  7    �  7    �  7    �      �      �  P    �      �  P    �        �    2      C  }   U  B    \      h  �   r  R    �  �    �  �   �      �  C    �      �  �     E      #         B  >    I      Z      k  =    |  ;    �  <    �      �  =    �  T       �   �  4 G            �  $   �  �        �_glfwInitWin32 V M        �  傹h
-ZBG
G:-3
 >�    wc  D`    >�    msg  B�  Y    i >�    dbi  B  x    J �  N- M        �  � 

 >z    osvi  B�   [    g
 >#     cond  AH      4  NJ M        �  ^A$i��#BO"
"eQ"1&
 >t     key  A   �     � >�    state  B�   o     � >t     length  A   �    ]  A  �     ��  � ]  >t     scancode  A   �     � A  �       >�    chars  B  �     x >u     vk  A   n    �    A  �     ��  �   >�    vks  B�  �     � N Z   �  �  �  �  �    ( Sq  �)   _glfw_GUID_DEVINTERFACE_HID  A�       :  X                    C  h   �  �  �  
 :0  O  9w      �   9�      �   9�      �   9�      �   9y      �   9�      �   9�      �   9�      j   9�      ?   9�      `   9�      &   9S      �   9�      �   9�      �   9@      �   9i      �   9z      �   9�      �   9�      �   O   �   �           �  �     �       � �$   � �Y   � �^   � ��   � ��   � �   � �$  � �,  � �.  � �6  � �<  � �T  � ��  � ��  � ��  � ��  � ��  � ��  � �l  � �x  � ��  � ��  � ��  � �,   L    0   L   
 �   L    �   L   
 �   L    �   L   
 9  L    =  L   
 X  L    \  L   
 �  L    �  L   
 �  L    �  L   
   L      L   
   L      L   
 B  L    F  L   
 R  L    V  L   
 r  L    v  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 '  L    +  L   
 q  L    u  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
   L      L   
   L      L   
 !  L    %  L   
 1  L    5  L   
 A  L    E  L   
 Q  L    U  L   
 a  L    e  L   
 q  L    u  L   
 �  L    �  L   
 �  L    �  L   
 �  L    �  L   
 H塡$H塼$ WH侅P  H�    H3腍墑$@  H嬟孂3鯤崒$B  3襢壌$@  A羹  �    3褸坱$@A�  H峀$A�    �    H塼$03褼防A�   H崉$@  荄$(   �  H塂$ �    H塼$8H岲$@H塼$0D峃�荄$(   L崉$@  3襀塂$ 归�  �    L峀$@L嬅H�    嬒�    H媽$@  H3惕    L崪$P  I媅 I媠(I嬨_�   �   D   �    [   �    a   8    �   :    �   J    �   �   �   �    �   �       �   �  : G              $   �   �        �_glfwInputErrorWin32  >t    error  A         )  A   )     �  >@   description  AI  '     �  AK        '  >�    buffer  D@   >h    message  D@   
 Z   o   P                    C 
 :@  O  `  t   Oerror  h  @  Odescription  @  �  Obuffer  @   h  Omessage  9_       k   9�       �   9�       �   O�   P             �     D       � �)   � �H   � �_   � ��   � ��   � ��     �,   R    0   R   
 `   R    d   R   
 p   R    t   R   
 �   R    �   R   
 �   R    �   R   
 t  R    x  R   
 �  R    �  R   
 �  R    �  R   
 �  R    �  R   
 @SH侅P  H�    H3腍墑$@  妨3襀峀$0塂$,A�  荄$   H荄$$
   3坭    A�峉3�    A�峉H嬋�    A�峉H嬋�    L嬂峉H峀$ �(*  吚斆嬅H媽$@  H3惕    H伳P  [�   �   C   �    Q   7    `   7    o   7    �       �   �       �   6  I G            �      �   �        �_glfwIsWindows10BuildOrGreaterWin32  >!    build  A
         % 
 >z    osvi  D    
 >#     cond  AH  U     /  P                    K 
 :@  O  `  !   Obuild      z  Oosvi  9O       �   9^       �   9m       �   9~       j   O  �   P           �   �     D       M �   N �G   P �U   Q �d   R �s   V ��   W �,   Q    0   Q   
 o   Q    s   Q   
 �   Q    �   Q   
   Q      Q   
   Q      Q   
 "  Q    &  Q   
 2  Q    6  Q   
 L  Q    P  Q   
 H塡$ WH侅P  H�    H3腍墑$@  妨A坟塂$$H峀$6仿3�3覊D$(A羹   荄$   H墊$,f墊$4�    3纅墱$4  A�墑$6  峎f墑$:  3�    A�峎H嬋�    A�峎 H嬋�    L嬂峎#H峀$ �(*  吚@斍嬊H媽$@  H3惕    H嫓$x  H伳P  _�   �   S   �    z   7    �   7    �   7    �       �   �       �   �  I G            �      �   �        �_glfwIsWindowsVersionOrGreaterWin32  >!    major  A
         /  >!    minor  A         6  >!    sp  A   &     �  A`        & 
 >z    osvi  D    
 >#     cond  AH  ~     /  P                    C 
 :@  O  `  !   Omajor  h  !   Ominor  p  !   Osp      z  Oosvi  9x       �   9�       �   9�       �   9�       j   O�   P           �   �     D       > �   ? �a   A �~   B ��   C ��   G ��   H �,   P    0   P   
 o   P    s   P   
 �   P    �   P   
 �   P    �   P   
 �   P    �   P   
 �   P    �   P   
 t  P    x  P   
 �  P    �  P   
 �  P    �  P   
 �  P    �  P   
 �  P    �  P   
 H冹(H�
h)  H吷t�    H�
�  H吷t�    H�
�  H吷t�    ��  f吚tH�x  嬋�    ��  f吚tH�x  嬋�    H�
�  �    H�
X)  �    �    �    �    H�
�)  H吷t�    H�
p)  H吷t�    H�
�)  H吷t�    H�
�)  H吷t�    H�
*  H吷t�    H�
 *  H吷t	H兡(�    H兡(�          H           $   ?    +       6   D    =       I       Q   A    X       d       l   A    s       x   �           �   �    �   �    �   �    �   �    �       �   �    �       �   �    �       �   �    �       �   �    �       �   �    �       �   �       �   �  9 G                   �        �_glfwTerminateWin32 / M        �  ��,E,E,E,E, Z   �  �  �  �  �   N Z   I  I    (  6   >d  _glfw  CJ  �  /       C   �  A       C   �  \       CJ  �         CJ  h	         CJ  p	  �     
  CJ  �	  �     
  CJ  �	  �     
  CJ  �	  �     
  CJ  
  �     
  CJ   
  �       CJ �  :     =   0   C  �  U       C  �  p       CJ �  (       CJ h	         CJ p	  �       CJ �	  �       CJ �	  �       CJ �	  �       CJ 
  �       (                      B 
 h   �   9       �   9"       ?   94       =   9O       �   9j       �   O  �   �             �     �       � �   � �   � �   � �"   � �(   � �4   � �:   � �F   � �U   � �a   � �p   � �|   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �,   M    0   M   
 �   M    �   M   
 �   M    �   M   
 �   M    �   M   
   M      M   
    M    $  M   
 4  M    8  M   
 H  M    L  M   
 \  M    `  M   
 p  M    t  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
   M      M   
   M      M   
 ,  M    0  M   
 @  M    D  M   
 T  M    X  M   
 h  M    l  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 H塡$H塴$H塼$ ATAVAWH侅�  H�    H3腍墑$�  3襀崒$�   A�   �    3襀�
b"  A秆  �    L峵$@H壖$�  I侇�  H�-�  E3銵�=#  �    縰 凗��*  侞@  倕   侞N  w}荄$@`   荄$Da   荄$Hb   荄$Lc   荄$Pd   荄$Te   荄$Xf   荄$\g   荄$`h   荄$di   荄$hn   荄$lo   荄$pj   荄$tm   荄$xk   A�>��   嬑�    孁D塪$(L崒$�   L崉$�   荄$    嬛嬒�    凐�u'D塪$(L崒$�   L崉$�   荄$    嬛嬒�    凐|2L塪$8L崉$�   L塪$0A�   荄$(   3夜辇  L墊$ �    �肐兦I兤H兣侞\  幆��H嫾$�  H媽$�  H3惕    L崪$�  I媅(I媖0I媠8I嬨A_A^A\�   �   ?   �    H       S   �    n       x       (  G    Q  F    }  F    �  J    �  �       �   �  > G              .   �  �        �_glfwUpdateKeyNamesWin32  >t     key  A   �     u >�    state  D�    >t     length  A   U    c  A  �     ��  � c  >t     scancode  A   �     y A  �       >�    chars  D�    >u     vk  A       �    A  �     Y�  � �  >�    vks  D@    �                    K 
 :�  O  �   �  Ostate  �   �  Ochars  @   �  Ovks  9&      �   9O      �   9{      �   9�      �   O   �   �             �     �        �.    �C   	 ��    ��    ��    ��    �   �   �  ! �.  # �U  ' �Z  + ��  0 ��  3 ��   ��  8 �,   S    0   S   
 b   S    f   S   
 �   S    �   S   
 �   S    �   S   
 �   S    �   S   
 �   S    �   S   
   S      S   
 /  S    3  S   
 �  S    �  S   
 �  S    �  S   
 �  S    �  S   
 �  S    �  S   
   S      S   
 H塡$WH冹 H��  ����H嬎A�   �    H�=�  ����H嬒A负  �    fo    窾   f��  �
  f�F  �   f��  �  f�6  �  f�2  �8  f��  �9  f��  窾  f��  竁  f�^  竂  f�  �  ��  f�>  �  fo    �  f�B  窲  fo    ��  9 0 ��  A S �  B N �  C V ��  D F ��  E R ��  G H ��  I O ��  J K ��  L ; �  M , ��  P [ ��  Q W ��  T Y �   Z X ��  ' ` ��  T\ ��  - = �  . / ��  ] �T  � ,��  �L  �`  [\�H  �8  	
�2  �    �.  *+�X  -P�p  ��  67�  LV�  Z��  OY�J  C@�F  AB�>  DE�B  FN�6  GH�:  IMf�N  窴  f�  3��     H�f吷~f�O�繦兠=   |鍴媆$0H兡 _�
           �    '       :   �    B   �   N       Z       f       r       ~       �       �       �       �       �       �       �       �   �   �       �       �   �   �                         %      /      9      C      M      W      a      k      u            �      �      �      �      �      �      �      �      �      �      �      �                              +      1      ;      E      O      Y      c      m      w      �      �      �      �         �   |   5 F            �  
   �  �        �createKeyTables  >t     scancode  A   �    -                        B  O�   �           �  �     �       �  �
   �  �$   �  �>   �  �F   �  �R    �^    �j    �v   
 ��   % ��   & ��   ( ��   * ��   . ��   1 ��   2 ��   @ �9  D ��  A ��  G ��  I ��  J ��  G ��  L �,   �    0   �   
 ^   �    b   �   
 �   �    �   �   
 H塡$H塴$H塼$WH冹 I嬞I孁嬺H嬮凓~tI侜  uF�=�   t=I侙 �  uH呟t/A儁u(�    �!H��  uH呟tA儁u�    ��    L嬎L嬊嬛H嬐H媆$0H媗$8H媡$@H兡 _H�%    .       K   �    g   �    n   T    �   @       �   �  6 F            �      }   �        �helperWindowProc 
 >   hWnd  AJ          AN       h 
 >u    uMsg  A           A        p  >#    wParam  AM       w  AP          >    lParam  AI       k  AQ          Z   �  �                           B  0     OhWnd  8   u   OuMsg  @   #   OwParam  H      OlParam  9�       �   O  �   �           �   �     |       Q �   R �,   Z �5   ] �>   ` �J   a �O   b �Q   c �Z   f �f   g �m   U �r   n �}   o ��   n �,   �    0   �   
 [   �    _   �   
 k   �    o   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �       �   
 �  �    �  �   
 �  �    �  �   
 H冹hH�    H3腍塂$PL�x  �   H�    �    吚u%H�    �  �    3繦婰$PH3惕    H兡h肏�
    �    H��)  H吚u%H�    �  �    3繦婰$PH3惕    H兡h肏�    H塡$`H嬋�    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H�
    H��)  �    H�p)  H吚tH�    H嬋�    H�x)  H�
    3跦塋$(H�    H�
    H塂$ H塋$0H�
    H塋$8H�
    H塋$@H塡$HfD  H嬋�    H��)  H吚uH婦�(H�肏吚u唠0H�    H嬋�    H�
�)  H�    H��)  �    H��)  H�
    �    H媆$`H��)  H吚tdH�    H嬋�    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H�
�)  H�    H��)  �    H� *  H�
    �    H�*  H吚t0H�    H嬋�    H�
*  H�    H�*  �    H�*  H�
    �    H� *  H吚tH�    H嬋�    H�(*  �   H婰$PH3惕    H兡h�   �          "       (   9    3   &   =   R    L   �    X   )   ]   �    d       p   ,   z   R    �   �    �   /   �   �    �       �   2   �       �   �    �       �   5   �       �   �    �       �   8   �       �   �    �       �   ;         
  �            >         $  �    +      2  A   9      >  �    E  D   L      Q  �    X      d  G   l  �    s      z  M   �  J   �  P   �  S   �  V   �  �    �      �  Y   �  �    �      �  \           �            _     �    '      3  b   ;  �    B      I  e   P      U  �    \      c  h   j      o  �    v      }  k   �      �  �    �      �  n   �  �    �      �  q   �  �    �      �  t   �      �  �    �      �  w   �  �    �      �  z   �  �            �       �     3 F            !       �        �loadLibraries  >�    names  D    j Z   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   h                      C 
 :P   O      �  Onames  9&       z   O �   h          !  �  *   \      I  �   J  �0   O  �A   Q  �C   �  �U   T  �k   U  �m   W  �~   Y  ��   �  ��   \  ��   ^  ��   `  ��   b  ��   d  �  f  �(  h  �B  k  �_  l  �a  n  �w  t  ��  �  ��  �  ��  ~  ��  �  ��  �  ��  �  �  �  �.  �  �0  �  �?  �  �Y  �  �s  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �
  �  �  �  �,   �    0   �   
   �      �   
    �    $  �   
  H P      
          �       �       �    $ K �P    0     �        1           �       �       �    !a atP (鬑  銲 腏 dO 4N     1          �        �    $   �    1   �          �       �       �    !       1          �       �       �    �  �          �       �       �     B                �       �       �    
 
d	 
Rp    T           �       �       �    ! 4     T          �       �       �    T   �           �       �       �    !   4     T          �       �       �    �   �           �       �       �    m m4
 d T rp    �           �       �       �     
4/ 
* p      @     �        �           �       �       �     	* 0      @     �        �           �       �       �    $ d�4��p      @     �                  �       �       �    d
 dt: d= T< 4; 6 ���      �      �                  �       �       �     �      P      �        �           �       �          ! 4     �          �       �          �   0          �       �       
   !       �          �       �          0  !          �       �          
 
4 
2p    �          �       �           d T 4 2p    �           �       �          睻Mo裣埶   0Win32: Failed to retrieve own module handle user32.dll Win32: Failed to load user32.dll SetProcessDPIAware ChangeWindowMessageFilterEx EnableNonClientDpiScaling SetProcessDpiAwarenessContext GetDpiForWindow AdjustWindowRectExForDpi GetSystemMetricsForDpi dinput8.dll DirectInput8Create xinput1_4.dll xinput1_3.dll xinput9_1_0.dll xinput1_2.dll xinput1_1.dll XInputGetCapabilities XInputGetState dwmapi.dll DwmIsCompositionEnabled DwmFlush DwmEnableBlurBehindWindow DwmGetColorizationColor shcore.dll SetProcessDpiAwareness GetDpiForMonitor ntdll.dll RtlVerifyVersionInfo G L F W 3   H e l p e r   Win32: Failed to register helper window class G L F W   m e s s a g e   w i n d o w   Win32: Failed to create helper window Win32: Failed to convert string from UTF-8 Win32: Failed to convert string to UTF-8 %s: %s 1 2 3 4 5 6 7 8 "#$%&'()./012345   v ��3.]H�\�)r�   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\Release\glfw3.pdb �6j��恸�
mL;Y麺氶輪�=╣	娩}朕�櫬�+劳眊�畜塳硶獗岫芥_m棉箌e貫�-RV垓[�,拮]�d鏤�/b鍍H/�鄟�:]骈俒耮-嘴 <诅�<3�挖�(唆-燻6�1.�;繮讯3�:毲攢漏�%锬艘J盪﹡攚龘vX牢�h蠐诩0"矟鋳軠��"d侮OU
j^�5d环8�"旲C
埕J?#�8f聗[hSe抣浚愘錼|ǒu嶑树A�鍡~;\�8v�岓歨.Sy,仩�-坓�(鬄�6BE�$缼H〢}<礻.B沦�c6琒�3u懎Kj傰
絠�#EnU��
^E7^擓嚇�1駘�:�鍐騄}黻w$墥-縕Do搟郎GG}q喃顛垟w椻6BE搻FY9G忁橆	酼璺痳�4�聲P�
6E2�偦2峜P只kFUi偺g伾�9E\$L釉蕨嚏��8�嫫﹥�,尼鵉da�o�.�屆u嶱晪t僔Aq�?�,W�3-/<丳u艼�"=�#oH�5鞶鑑沿$d�� 媑矂AhIGqE�QV谶俎謕=x襐苒t看嶥��j:aF疌@蟦e囗頺�揧竾佼�=�:mT煀瀱毵|梚e荟
納Ux澀z黩嵜9|}鎡T磀5{U%� O/肻艦赳"u囿�9哭�Q�aN頕徬`:	-*i9姕�*^诡遍�4煊o攻钑鵉;9癶�	>抺矦�9o砵懡-j�*揚凷锾皇A鐵=rty%歋傷碁)湋σ# 垷_7M|cT曯識�>榌\�7耨�yUS�)���        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       ]                 .debug$S       L�               .text$mn       
  G   耉窢     .debug$S       \             .text$mn       �      ]F邖     .debug$S       �             .text$mn       �      PY!~     .debug$S       �             .text$mn    	   �  /   <閐3     .debug$S    
   �  H       	    .text$mn         	   8勑�     .debug$S       �             .text$mn    
   �      ��     .debug$S       �         
    .text$mn       �      �w�     .debug$S                    .text$mn            �罪     .debug$S       �  8           .text$mn            薾櫩     .debug$S       �             .text$mn       �  @   蝻禁     .debug$S       h             .text$mn       �      禲H�     .debug$S       ,             .text$mn       !  _   毾n     .debug$S       �                                                 1                J                _                v                �                �                �                �                �                               (               >               R               c               s               �               �               �               �              �      	        �                            &              I              m      
        �              �              �               �               �                                             6               M               d               |               �               �               �               �                              +               C               [               t               �               �               �               �               �                              "               :               T               l               �               �               �               �                              "               =               Y               {               �               �               �               �               �                                              :               S               j               �               �               �               �               �                              &               C               k               �               �               �               �               	               $	               <	               S	               j	               �	               �	               �	               �	               �	               
               
               7
               ]
               u
               �
               �
               �
               �
               �
               �
               �
                                          (               9           memset           $LN5            $LN53       	    $LN17           $LN9            $LN9            $LN4            $LN4        
    $LN4            $LN22           .xdata                \�2,        Q          .pdata               �1Jo        k          .xdata               ��-	        �          .pdata               鉙gI	        �          .xdata         (      F璑�	        �          .pdata                 �	        �           .xdata      !         �0�	        �      !    .pdata      "         髆謹	        �      "    .xdata      #          �9�              #    .pdata      $         3`        -      $    .xdata      %          g]
�        H      %    .pdata      &         <齦�        s      &    .xdata      '         趙r�        �      '    .pdata      (         .沽m        �      (    .xdata      )         v'∞        �      )    .pdata      *         �,�        !
      *    .xdata      +          �I�        M
      +    .pdata      ,         Sc睶        x
      ,    .xdata      -         B(U�        �
      -    .pdata      .         �r�        �
      .    .xdata      /         i�n
        �
      /    .pdata      0         v�.
        %      0    .xdata      1         �0        P      1    .pdata      2         3`        m      2    .xdata      3   (      q嶻�        �      3    .pdata      4         �
zD        �      4    .xdata      5         l厒�        �      5    .pdata      6         诏�0        �      6    .xdata      7         '獇b        �      7    .pdata      8         羸$�              8    .xdata      9         6E槈        #      9    .pdata      :         愾qH        :      :    .xdata      ;          %蚘%        Q      ;    .pdata      <         Qib�        i      <    .xdata      =          嘋c�        �      =    .pdata      >         D痚�        �      >    _glfw            .rdata      ?          E靜=          �      ?    .rdata      @   ,       箆f�         �      @    .rdata      A          沴凐               A    .rdata      B   !       ��         &      B    .rdata      C          ,閡h         `      C    .rdata      D          �;酀         �      D    .rdata      E          ,P�         �      E    .rdata      F          疬0H         �      F    .rdata      G          M�+�               G    .rdata      H          V�0�         ;      H    .rdata      I          焼蝌         h      I    .rdata      J          蝘         �      J    .rdata      K          �:宅         �      K    .rdata      L          �$�         �      L    .rdata      M          E�         �      M    .rdata      N          �$t               N    .rdata      O          嗲FE         C      O    .rdata      P          N狄�         e      P    .rdata      Q          �         �      Q    .rdata      R          :kz         �      R    .rdata      S          暝V�         �      S    .rdata      T          à捧         �      T    .rdata      U   	       掦;�               U    .rdata      V          *呝,         9      V    .rdata      W          �W         g      W    .rdata      X          5�+�         �      X    .rdata      Y          ~��         �      Y    .rdata      Z          愺做         �      Z    .rdata      [   
       5蠫               [    .rdata      \          G艕                \    .rdata      ]          危驴         G      ]    .rdata      ^   .       	圇�         �      ^    .rdata      _   (       gW钨         �      _    .rdata      `   &       旫炘         4      `    .rdata      a   +       M顡         n      a    .rdata      b   )       恰髟         �      b    .rdata      c          s�a         �      c                    .rdata      d          梂
r               d    .rdata      e           茔�         9      e    .rdata      f          颍a�         `      f    .debug$T    g   |                 .chks64     h   @                �  __imp_VerSetConditionMask __imp_GetLastError __imp_GetModuleHandleExW __imp_FormatMessageW __imp_TranslateMessage __imp_DispatchMessageW __imp_PeekMessageW __imp_RegisterDeviceNotificationW __imp_UnregisterDeviceNotification __imp_DefWindowProcW __imp_UnregisterClassW __imp_RegisterClassExW __imp_CreateWindowExW __imp_DestroyWindow __imp_ShowWindow __imp_ToUnicode __imp_MapVirtualKeyW __imp_DestroyIcon __imp_MultiByteToWideChar __imp_WideCharToMultiByte _glfwConnectWin32 _glfwInitWin32 _glfwTerminateWin32 _glfwCreateWideStringFromUTF8Win32 _glfwCreateUTF8FromWideStringWin32 _glfwIsWindowsVersionOrGreaterWin32 _glfwIsWindows10BuildOrGreaterWin32 _glfwInputErrorWin32 _glfwUpdateKeyNamesWin32 _glfwPollMonitorsWin32 _glfwCreateWindowWin32 _glfwDestroyWindowWin32 _glfwSetWindowTitleWin32 _glfwSetWindowIconWin32 _glfwGetWindowPosWin32 _glfwSetWindowPosWin32 _glfwGetWindowSizeWin32 _glfwSetWindowSizeWin32 _glfwSetWindowSizeLimitsWin32 _glfwSetWindowAspectRatioWin32 _glfwGetFramebufferSizeWin32 _glfwGetWindowFrameSizeWin32 _glfwGetWindowContentScaleWin32 _glfwIconifyWindowWin32 _glfwRestoreWindowWin32 _glfwMaximizeWindowWin32 _glfwShowWindowWin32 _glfwHideWindowWin32 _glfwRequestWindowAttentionWin32 _glfwFocusWindowWin32 _glfwSetWindowMonitorWin32 _glfwWindowFocusedWin32 _glfwWindowIconifiedWin32 _glfwWindowVisibleWin32 _glfwWindowMaximizedWin32 _glfwWindowHoveredWin32 _glfwFramebufferTransparentWin32 _glfwSetWindowResizableWin32 _glfwSetWindowDecoratedWin32 _glfwSetWindowFloatingWin32 _glfwSetWindowMousePassthroughWin32 _glfwGetWindowOpacityWin32 _glfwSetWindowOpacityWin32 _glfwSetRawMouseMotionWin32 _glfwRawMouseMotionSupportedWin32 _glfwPollEventsWin32 _glfwWaitEventsWin32 _glfwWaitEventsTimeoutWin32 _glfwPostEmptyEventWin32 _glfwGetCursorPosWin32 _glfwSetCursorPosWin32 _glfwSetCursorModeWin32 _glfwGetScancodeNameWin32 _glfwGetKeyScancodeWin32 _glfwCreateCursorWin32 _glfwCreateStandardCursorWin32 _glfwDestroyCursorWin32 _glfwSetCursorWin32 _glfwSetClipboardStringWin32 _glfwGetClipboardStringWin32 _glfwGetEGLPlatformWin32 _glfwGetEGLNativeDisplayWin32 _glfwGetEGLNativeWindowWin32 _glfwGetRequiredInstanceExtensionsWin32 _glfwGetPhysicalDevicePresentationSupportWin32 _glfwCreateWindowSurfaceWin32 _glfwFreeMonitorWin32 _glfwGetMonitorPosWin32 _glfwGetMonitorContentScaleWin32 _glfwGetMonitorWorkareaWin32 _glfwGetVideoModesWin32 _glfwGetVideoModeWin32 _glfwGetGammaRampWin32 _glfwSetGammaRampWin32 _glfwInitJoysticksWin32 _glfwTerminateJoysticksWin32 _glfwPollJoystickWin32 _glfwGetMappingNameWin32 _glfwUpdateGamepadGUIDWin32 _glfwTerminateWGL _glfwDetectJoystickConnectionWin32 _glfwDetectJoystickDisconnectionWin32 _glfwPlatformLoadModule _glfwPlatformFreeModule _glfwPlatformGetModuleSymbol _glfwInputError _glfwTerminateEGL _glfwTerminateOSMesa _glfw_calloc _glfw_free loadLibraries createKeyTables helperWindowProc __GSHandlerCheck __security_check_cookie $unwind$_glfwConnectWin32 $pdata$_glfwConnectWin32 $unwind$_glfwInitWin32 $pdata$_glfwInitWin32 $chain$5$_glfwInitWin32 $pdata$5$_glfwInitWin32 $chain$6$_glfwInitWin32 $pdata$6$_glfwInitWin32 $unwind$_glfwTerminateWin32 $pdata$_glfwTerminateWin32 $unwind$_glfwCreateWideStringFromUTF8Win32 $pdata$_glfwCreateWideStringFromUTF8Win32 $chain$0$_glfwCreateWideStringFromUTF8Win32 $pdata$0$_glfwCreateWideStringFromUTF8Win32 $chain$2$_glfwCreateWideStringFromUTF8Win32 $pdata$2$_glfwCreateWideStringFromUTF8Win32 $unwind$_glfwCreateUTF8FromWideStringWin32 $pdata$_glfwCreateUTF8FromWideStringWin32 $unwind$_glfwIsWindowsVersionOrGreaterWin32 $pdata$_glfwIsWindowsVersionOrGreaterWin32 $unwind$_glfwIsWindows10BuildOrGreaterWin32 $pdata$_glfwIsWindows10BuildOrGreaterWin32 $unwind$_glfwInputErrorWin32 $pdata$_glfwInputErrorWin32 $unwind$_glfwUpdateKeyNamesWin32 $pdata$_glfwUpdateKeyNamesWin32 $unwind$loadLibraries $pdata$loadLibraries $chain$0$loadLibraries $pdata$0$loadLibraries $chain$1$loadLibraries $pdata$1$loadLibraries $unwind$createKeyTables $pdata$createKeyTables $unwind$helperWindowProc $pdata$helperWindowProc _glfw_GUID_DEVINTERFACE_HID ??_C@_0CM@IALNOPFK@Win32?3?5Failed?5to?5retrieve?5own?5m@ ??_C@_0L@GMPLCCII@user32?4dll@ ??_C@_0CB@HHJIGJHK@Win32?3?5Failed?5to?5load?5user32?4dl@ ??_C@_0BD@ENICNPLM@SetProcessDPIAware@ ??_C@_0BM@OCGPLDNC@ChangeWindowMessageFilterEx@ ??_C@_0BK@NKEDGNNN@EnableNonClientDpiScaling@ ??_C@_0BO@LHMLBOLK@SetProcessDpiAwarenessContext@ ??_C@_0BA@LGGPEDOH@GetDpiForWindow@ ??_C@_0BJ@MBAAGFCG@AdjustWindowRectExForDpi@ ??_C@_0BH@HPKNFNCE@GetSystemMetricsForDpi@ ??_C@_0M@ONOEDIJO@dinput8?4dll@ ??_C@_0BD@IJCCAMHG@DirectInput8Create@ ??_C@_0O@LNFLKCMF@xinput1_4?4dll@ ??_C@_0O@KAFOJCHN@xinput1_3?4dll@ ??_C@_0BA@GHGALGFD@xinput9_1_0?4dll@ ??_C@_0O@GLACEBNI@xinput1_2?4dll@ ??_C@_0O@ONJGDDHG@xinput1_1?4dll@ ??_C@_0BG@ELBNFOBA@XInputGetCapabilities@ ??_C@_0P@FCCEJHCC@XInputGetState@ ??_C@_0L@FACOJKPJ@dwmapi?4dll@ ??_C@_0BI@KJPLJBHH@DwmIsCompositionEnabled@ ??_C@_08PEMNBIMD@DwmFlush@ ??_C@_0BK@HMMKMEOH@DwmEnableBlurBehindWindow@ ??_C@_0BI@LDADLCB@DwmGetColorizationColor@ ??_C@_0L@DMFDIJCG@shcore?4dll@ ??_C@_0BH@DGFLCCMF@SetProcessDpiAwareness@ ??_C@_0BB@MAMHPNNC@GetDpiForMonitor@ ??_C@_09FLKFJBLM@ntdll?4dll@ ??_C@_0BF@LOGLPPDD@RtlVerifyVersionInfo@ ??_C@_1BK@OPNBOCAD@?$AAG?$AAL?$AAF?$AAW?$AA3?$AA?5?$AAH?$AAe?$AAl?$AAp?$AAe?$AAr@ ??_C@_0CO@CNHHHABN@Win32?3?5Failed?5to?5register?5helpe@ ??_C@_1CI@MPNNJFCJ@?$AAG?$AAL?$AAF?$AAW?$AA?5?$AAm?$AAe?$AAs?$AAs?$AAa?$AAg?$AAe?$AA?5?$AAw?$AAi@ ??_C@_0CG@LDJIJKNI@Win32?3?5Failed?5to?5create?5helper?5@ ??_C@_0CL@JEJIBJNG@Win32?3?5Failed?5to?5convert?5string@ ??_C@_0CJ@EFIFNOJC@Win32?3?5Failed?5to?5convert?5string@ ??_C@_06DJHOIPC@?$CFs?3?5?$CFs@ __security_cookie __xmm@00380037003600350034003300320031 __xmm@01290128012701260125012401230122 __xmm@013501340133013201310130012f012e 