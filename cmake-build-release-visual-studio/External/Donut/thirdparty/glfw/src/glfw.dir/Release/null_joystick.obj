d� h馟h遲  '       .drectve        ]   l               
 .debug$S        l  �              @ B.text$mn           錸  韓          P`.debug$S        �   鱪  無         @B.text$mn           莖               P`.debug$S        �   蚾  qp         @B.text$mn           檖               P`.debug$S          減  皅         @B.text$mn            r               P`.debug$S        �   r           @B.text$mn           蟫               P`.debug$S        �   襯  畇         @B.rdata             阺              @@.debug$T        |   雜              @ B.chks64         x   gt               
     /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   w     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\glfw.dir\Release\null_joystick.obj : < `  �  & y�   & y�  Microsoft (R) Optimizing Compiler    �   /+       URLZONE_LOCAL_MACHINE     URLZONE_INTRANET     URLZONE_TRUSTED     URLZONE_INTERNET  n    URLZONEREG_DEFAULT  n   URLZONEREG_HKLM # �   BINDHANDLETYPES_DEPENDENCY  �    PIDMSI_STATUS_NORMAL  �   PIDMSI_STATUS_NEW  �   PIDMSI_STATUS_PRELIM  �   PIDMSI_STATUS_DRAFT ! �   PIDMSI_STATUS_INPROGRESS  �   PIDMSI_STATUS_EDIT  �   PIDMSI_STATUS_REVIEW  �   PIDMSI_STATUS_PROOF ! �    COINITBASE_MULTITHREADED ' �  �   CLSCTX_ACTIVATE_X86_SERVER , �   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL  �    MDT_EFFECTIVE_DPI  �    NODE_INVALID  �   NODE_ELEMENT  �   NODE_ATTRIBUTE  �   NODE_TEXT  �   NODE_CDATA_SECTION  �   NODE_ENTITY_REFERENCE  �   NODE_ENTITY $ �   NODE_PROCESSING_INSTRUCTION  �   NODE_COMMENT  �  	 NODE_DOCUMENT  �  
 NODE_DOCUMENT_TYPE  �   NODE_DOCUMENT_FRAGMENT  �    XMLELEMTYPE_ELEMENT  �   XMLELEMTYPE_TEXT  �   XMLELEMTYPE_COMMENT  �   XMLELEMTYPE_DOCUMENT  �   XMLELEMTYPE_DTD  �   XMLELEMTYPE_PI  �   VT_I2  �   VT_I4  �   VT_BSTR  �  	 VT_DISPATCH  �  
 VT_ERROR  �   VT_VARIANT  �  
 VT_UNKNOWN  �   VT_I1  �   VT_I8  �  $ VT_RECORD  �  � �VT_RESERVED  �    TYSPEC_CLSID  �   TYSPEC_FILEEXT  �   TYSPEC_MIMETYPE  �   TYSPEC_FILENAME  �   TYSPEC_PROGID  �   TYSPEC_PACKAGENAME + w   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 w   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - w   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 w   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS * �   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP     PowerUserMaximum  �    ServerApplication  ~    IdleShutdown  u   COR_VERSION_MAJOR_V2  	    TKIND_ENUM  	   TKIND_RECORD  	   TKIND_MODULE  	   TKIND_INTERFACE  	   TKIND_DISPATCH  	   TKIND_COCLASS  	   TKIND_ALIAS  	   TKIND_UNION  �   CC_CDECL  �   CC_MSCPASCAL  �   CC_PASCAL  �   CC_MACPASCAL  �   CC_STDCALL  �   CC_FPFASTCALL  �   CC_SYSCALL  �   CC_MPWCDECL  �   CC_MPWPASCAL  �    FUNC_VIRTUAL  �   FUNC_PUREVIRTUAL  �   FUNC_NONVIRTUAL  �   FUNC_STATIC  �    VAR_PERINSTANCE  �   VAR_STATIC  �   VAR_CONST # �   BINDSTATUS_FINDINGRESOURCE  �   BINDSTATUS_CONNECTING  �   BINDSTATUS_REDIRECTING % �   BINDSTATUS_BEGINDOWNLOADDATA # �   BINDSTATUS_DOWNLOADINGDATA # �   BINDSTATUS_ENDDOWNLOADDATA + �   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �   BINDSTATUS_INSTALLINGCOMPONENTS ) �  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �  
 BINDSTATUS_USINGCACHEDCOPY " �   BINDSTATUS_SENDINGREQUEST $ �   BINDSTATUS_CLASSIDAVAILABLE % �  
 BINDSTATUS_MIMETYPEAVAILABLE * �   BINDSTATUS_CACHEFILENAMEAVAILABLE & �   BINDSTATUS_BEGINSYNCOPERATION $ �   BINDSTATUS_ENDSYNCOPERATION # �   BINDSTATUS_BEGINUPLOADDATA ! �   BINDSTATUS_UPLOADINGDATA ! �   BINDSTATUS_ENDUPLOADDATA # �   BINDSTATUS_PROTOCOLCLASSID  �   BINDSTATUS_ENCODING - �   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �   BINDSTATUS_CLASSINSTALLLOCATION  �   BINDSTATUS_DECODING & �   BINDSTATUS_LOADINGMIMEHANDLER , �   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �   BINDSTATUS_FILTERREPORTMIMETYPE ' �   BINDSTATUS_CLSIDCANINSTANTIATE % �   BINDSTATUS_IUNKNOWNAVAILABLE  �   BINDSTATUS_DIRECTBIND  �   BINDSTATUS_RAWMIMETYPE " �    BINDSTATUS_PROXYDETECTING   �  ! BINDSTATUS_ACCEPTRANGES  �  " BINDSTATUS_COOKIE_SENT + �  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �  $ BINDSTATUS_COOKIE_SUPPRESSED ( �  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �  ' BINDSTATUS_COOKIE_STATE_REJECT ' �  ( BINDSTATUS_COOKIE_STATE_PROMPT & �  ) BINDSTATUS_COOKIE_STATE_LEASH * �  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �  + BINDSTATUS_POLICY_HREF  �  , BINDSTATUS_P3P_HEADER + �  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + �  / BINDSTATUS_SESSION_COOKIES_ALLOWED   �  0 BINDSTATUS_CACHECONTROL . �  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) �  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & �  3 BINDSTATUS_PUBLISHERAVAILABLE ( �  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �  5 BINDSTATUS_SSLUX_NAVBLOCKED , �  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �  8 BINDSTATUS_64BIT_PROGRESS  �  8 BINDSTATUS_LAST  �  9 BINDSTATUS_RESERVED_0  �  : BINDSTATUS_RESERVED_1  �  ; BINDSTATUS_RESERVED_2  �  < BINDSTATUS_RESERVED_3  �  = BINDSTATUS_RESERVED_4  �  > BINDSTATUS_RESERVED_5  �  ? BINDSTATUS_RESERVED_6  �  @ BINDSTATUS_RESERVED_7  �  A BINDSTATUS_RESERVED_8  �  B BINDSTATUS_RESERVED_9  �  C BINDSTATUS_RESERVED_A  �  D BINDSTATUS_RESERVED_B  �  E BINDSTATUS_RESERVED_C  �  F BINDSTATUS_RESERVED_D  �  G BINDSTATUS_RESERVED_E  �  H BINDSTATUS_RESERVED_F  �  I BINDSTATUS_RESERVED_10  �  J BINDSTATUS_RESERVED_11  �  K BINDSTATUS_RESERVED_12  �  L BINDSTATUS_RESERVED_13  �  M BINDSTATUS_RESERVED_14  �    DESCKIND_NONE  �   DESCKIND_FUNCDESC  �   DESCKIND_VARDESC  �   DESCKIND_TYPECOMP   �   DESCKIND_IMPLICITAPPOBJ  �    CIP_DISK_FULL  �   CIP_ACCESS_DENIED ! �   CIP_NEWER_VERSION_EXISTS ! �   CIP_OLDER_VERSION_EXISTS  �   CIP_NAME_CONFLICT 1 �   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + �   CIP_EXE_SELF_REGISTERATION_TIMEOUT  �   CIP_UNSAFE_TO_ABORT  �   CIP_NEED_REBOOT % �   TP_CALLBACK_PRIORITY_INVALID  �   BINDSTRING_HEADERS   �   BINDSTRING_ACCEPT_MIMES  �   BINDSTRING_EXTRA_URL  �   BINDSTRING_LANGUAGE  �   BINDSTRING_USERNAME  �   BINDSTRING_PASSWORD  �   BINDSTRING_UA_PIXELS  �   BINDSTRING_UA_COLOR  �  	 BINDSTRING_OS  �  
 BINDSTRING_USER_AGENT $ �   BINDSTRING_ACCEPT_ENCODINGS  �   BINDSTRING_POST_COOKIE " �  
 BINDSTRING_POST_DATA_MIME  �   BINDSTRING_URL  �   BINDSTRING_IID ' �   BINDSTRING_FLAG_BIND_TO_OBJECT $ �   BINDSTRING_PTR_BIND_CONTEXT  �   BINDSTRING_XDR_ORIGIN   �   BINDSTRING_DOWNLOADPATH  �   BINDSTRING_ROOTDOC_URL $ �   BINDSTRING_INITIAL_FILENAME " �   BINDSTRING_PROXY_USERNAME " �   BINDSTRING_PROXY_PASSWORD ! �   BINDSTRING_ENTERPRISE_ID  �   BINDSTRING_DOC_URL  �    SYS_WIN16  �   SYS_WIN32  �   SYS_MAC  �   PARSE_CANONICALIZE  �   PARSE_FRIENDLY  �   PARSE_SECURITY_URL  �   PARSE_ROOTDOCUMENT  �   PARSE_DOCUMENT  �   PARSE_ANCHOR ! �   PARSE_ENCODE_IS_UNESCAPE  �   PARSE_DECODE_IS_ESCAPE  �  	 PARSE_PATH_FROM_URL  �  
 PARSE_URL_FROM_PATH  �   PARSE_MIME  �   PARSE_SERVER  �  
 PARSE_SCHEMA  �   PARSE_SITE  �   PARSE_DOMAIN  �   PARSE_LOCATION  �   PARSE_SECURITY_DOMAIN  �   PARSE_ESCAPE  s   PSU_DEFAULT  �   QUERY_EXPIRATION_DATE " �   QUERY_TIME_OF_LAST_CHANGE  �   QUERY_CONTENT_ENCODING  �   QUERY_CONTENT_TYPE  �   QUERY_REFRESH  �   QUERY_RECOMBINE  �   QUERY_CAN_NAVIGATE  �   QUERY_USES_NETWORK  �  	 QUERY_IS_CACHED   �  
 QUERY_IS_INSTALLEDENTRY " �   QUERY_IS_CACHED_OR_MAPPED  �   QUERY_USES_CACHE  �  
 QUERY_IS_SECURE  �   QUERY_IS_SAFE ! �   QUERY_USES_HISTORYFOLDER  �    CHANGEKIND_ADDMEMBER   �   CHANGEKIND_DELETEMEMBER  �   CHANGEKIND_SETNAMES $ �   CHANGEKIND_SETDOCUMENTATION  �   CHANGEKIND_GENERAL  �   CHANGEKIND_INVALIDATE   �   CHANGEKIND_CHANGEFAILED    POINT 
   LPLONG  u   UINT  �  _FILETIME    LPDIEFFECT 
 p   int8_t  �  LPDIENVELOPE  �  tagRECT  �  IDirectInputEffect      uint8_t 
 *  LPGUID  �  _DIACTIONW    tagPOINT  �  DIEFFECTINFOW  X  LPDIPROPHEADER    IDirectInputEffectVtbl  �  RECT  "   ULONG  t   BOOL  �  LPDIDEVICEIMAGEINFOW  �  DIEFFECT  �  LPCDIFILEEFFECT + �  LPDIENUMCREATEDEFFECTOBJECTSCALLBACK 
   LPVOID  �  DIENVELOPE  �  LPDIOBJECTDATAFORMAT  �  LPCDIEFFECT 
   HANDLE  �  FILETIME  �  LPDIACTIONW  �  LPDIEFFESCAPE  �  DIDEVCAPS  �  _DIDEVICEIMAGEINFOW  {  LPDIDEVICEINSTANCEW  ]  LPCDIPROPHEADER  �  DIDEVICEOBJECTDATA  "  LPUNKNOWN  g  LPDIDEVICEOBJECTDATA  �  DIPROPHEADER 
 �  HWND__  �  DIDATAFORMAT % S  LPDIENUMDEVICEOBJECTSCALLBACKW  �  _TP_CALLBACK_PRIORITY  �  LPDIRECTINPUTEFFECT  ~  tagShutdownType  �  DIEFFESCAPE  �  tagCALLCONV  	  tagTYPEKIND    IUnknownVtbl  �  _DIOBJECTDATAFORMAT  #   rsize_t  �  LPDIFILEEFFECT ( w  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  s  _tagPSUACTION     LONG_PTR  �  tagBINDSTRING  #   ULONG_PTR    tagURLZONE  �  DIDEVICEINSTANCEW  �  LPDIEFFECTINFOW  �  __MIDL_ICodeInstall_0001  p  PCHAR  �  tagBINDSTATUS  q  _GUID  n  _URLZONEREG  �  LPCDIDEVICEOBJECTDATA  !   wchar_t    HINSTANCE  !   WORD  J  LPDIDEVCAPS ' �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS    _USER_ACTIVITY_PRESENCE    PLONG  �  _GLFWmapelement  �  _DIACTIONFORMATW  /  LPDIACTIONFORMATW  m  LPCDIDATAFORMAT      BYTE 
 5  PCWSTR  �  DIFILEEFFECT     LONG  �  HINSTANCE__  �  _DIDATAFORMAT ! �  LPDIDEVICEIMAGEINFOHEADERW ! P  LPCDIDEVICEOBJECTINSTANCEW 
 #   SIZE_T # u  ReplacesCorHdrNumericDefines  "   DWORD 
   PSHORT  "   TP_VERSION  �  VARENUM    HWND   w  LPDIDEVICEOBJECTINSTANCEW  "  LPDWORD  #   DWORD64  �  LPDIENUMEFFECTSCALLBACKW      BOOLEAN  �  tagTYSPEC   �  _DIDEVICEIMAGEINFOHEADERW  �  tagVARKIND    PVOID $ �  LPDIENUMEFFECTSINFILECALLBACK  �  IUnknown  t   errno_t  q   WCHAR     PBYTE  �  DIDEVICEOBJECTINSTANCEW  �  LPCDIEFFECTINFOW 
 q  IID  �  _tagQUERYOPTION  �  _GLFWmapping  �  IDirectInputDevice8WVtbl     HRESULT ! �  __MIDL_IGetBindHandle_0001 
    LONG64  �  tagCOINITBASE  �  tagApplicationType  5  LPCWSTR  �  tagDOMNodeType  q  PWSTR  �  tagCHANGEKIND 
 u   UINT32  �  tagSYSKIND  
  IDirectInputDevice8W  �  _GLFWjoystick  	  _GLFWjoyobjectWin32 
 q  LPWSTR  #   UINT_PTR  q  GUID  �  tagFUNCKIND  t   INT32  �  PIDMSI_STATUS_VALUE  �  MONITOR_DPI_TYPE 
 #   size_t  �  tagGLOBALOPT_EH_VALUES     SHORT    PLONG64  �  tagCLSCTX     INT_PTR  t   GLFWbool  �  tagXMLEMEM_TYPE  p   CHAR  �  _tagPARSEACTION  �  tagDESCKIND    _GLFWjoystickWin32  �   X      頴}�穲町v�
c�.丨a� �+篬鰌�莩�  I    �-�雧n�5L屯�:I硾�鮎访~(梱  �    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �    )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     8�'预P�憖�0R�(3銖� pN*�  g   6觏v畿S倂9紵"�%��;_%z︹  �   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  E   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  �   D���0�郋鬔G5啚髡J竆)俻w��  �   錵s铿勃砓b棬偡遯鮓尛�9泂惻  *   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  x   �F9�6K�v�/亅S诵]t婻F廤2惶I  �   d2軇L沼vK凔J!女計j儨杹3膦���  
   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  ]   樸7 忁�珨��3]"Fキ�:�,郩�  �    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   衠琪槡铟钭}_XO>�蛭X�7Mp处d  B   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  �   �D;窼僞k渔A�;��?缞鳗5翰�?*R
     5 KO诹硃毣�'R烣�7`埀M@懅y榵  [   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  �   聤�苮g8鄞<aZ�%4)闪�|袉uh�  �   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  9   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  �   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  l   �:2K] �
j�苊赁e�
湿�3k椨�  �   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  	   6��7@L�.�梗�4�檕�!Q戸�$�  N	   �儔14褥緅�3]饃鹷�hK3g搋bA竑  �	   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  �	   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  0
   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  y
   搚d故�)讠?�纯跋障腭�?p9J挄鹳  �
   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  �
   渐袿.@=4L笴速婒m瑜;_琲M %q�  O   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/  �   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  A   �>2
^�﨟2W酟傲X{b?荼猲�;  �   死╇D�#/��4鶄>G63齛w�i�->M  �   E縄�7�g虩狱呂�/y蛨惏l斋�笵  

   c�#�'�縌殹龇D兺f�$x�;]糺z�  ]
   $G\|R_熖泤煡4勄颧绖�?(�~�:  �
   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠       栀��綔&@�.�)�C�磍萘k  J   釳�>�H?6蓙�� 祍<ベ垽�=j逃�  �   2W瓓�<X	綧]�龐IE?'笼t唰��  �   晁X歌符�2澋U�'煈覽b�
蟣;-�     |q�6桢赤汗mv訔�	
爟~胱�>?妼BK�,  d   �"睱建Bi圀対隤v��cB�'窘�n  �   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏     bRè1�5捘:.z錨{娯啹}坬麺P  T   攄繠�
\b擫5`Om�1悑R钡h�:�47�  �   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  �   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒     5睔`&N_鏃|�<�$�獖�!銸]}"  g   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �   ┫緞A$窄�0� NG�%+�*�
!7�=b     镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  T   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  Q   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  �   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  D   噔园�,c珥珸洯濠�繗猍=sZ導  }   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   ��嵉氒髅嘁棭够*ヅ�
�'徺p4     孆x�0队<堛�猬dh梧`sR顛	k�7[M@  [   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  �   鷜E恳B�#蠠�,qC吾w�岧儁N篴  �   艶笊\眔Z%師}wы孜+HN鯥湔N     yk"&�棎墑�T-亴鏏愐煋搂脠+]�  ]   晊褃 �=�韀晝阓�!熝神�+k�2�<$]�  �   蠯3
掽K謈 � l�6襕鞜��H#�  �   �8��/X昋旒�.胱#h=J"髈篒go#  "   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  t   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  [   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  2   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   Eム聂�
C�?潗'{胿D'x劵;釱�     r�L剟FsS鏴醼+E千I呯贄0鬬/�  `   �*o驑瓂a�(施眗9歐湬

�  �   鹰杩@坓!)IE搒�;puY�'i憷n!  �   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  F    I嘛襨签.濟;剕��7啧�)煇9触�.  �    萾箒�$.潆�j閖i转pf-�稃陞��  �   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  !   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  l   G髼*悭�2睆�侻皣軁舃裄樘珱)  �   齛|)3h�2%籨糜/N_燿C虺r_�9仌     8蟴B或绢溵9"C dD揭鞧Vm5TB�  R   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  �   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  2   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  |   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   �~鴧傳.P怬WsP-"焫#N�:�&場璁     �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  _   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  �   "�挨	b�'+舒�5<O�呱_歲+/�P�?  �   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  D    ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  �    N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �    �呾��+h7晃O枖��*谵|羓嗡捬  !   j轲P[塵5m榤g摏癭 鋍1O骺�*�  a!   綔)\�谑U⒊磒'�!W磼B0锶!;  �!   sL&%�znOdz垗�M,�:吶1B滖  �!   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  H"   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �"   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  �"   �n儹`
舔�	Y氀�:b
#p:  4#   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  |#   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �#    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  $   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  ]$   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �$   掴'圭,@H4sS裬�!泉:莠й�"fE)  �$   覽s鴧罪}�'v,�*!�
9E汲褑g;  I%   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �%   +椬恡�
	#G許�/G候Mc�蜀煟-  �%   0T砞獃钎藰�0逪喌I窐G(崹�  &   閯�価=�<酛皾u漑O�髦jx`-�4睲�  k&   戹�j-�99檽=�8熈讠鳖铮�  �&   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  '   ,�<鈬獿鍢憁�g$��8`�"�  Q'   潝(綊r�*9�6}颞7V竅\剫�8値�#  �'   ^憖�眜蘓�y冊日/缁ta铁6殔  �'   魯f�u覬n\��zx騖笹笾骊q*砎�,�  5(   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  �(   _%1糠7硘籺蚻q5饶昈v纪嗈�  �(   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  )   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  ])   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �   �)   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\xinput.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dinput.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.c C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dbt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_thread.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\internal.h D:\RTXPT\External\Donut\thirdparty\glfw\src\platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_platform.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h   �       L�  H�    �   "       �   a   = G                      Y        �_glfwGetMappingNameNull                         B  O   �   0              �     $       1  �    2  �   3  �,       0      
 x       |      
 �   �   �   `   < G                      r        �_glfwInitJoysticksNull                         B  O�   0              �     $       #  �    $  �   %  �,       0      
 t       x      
 3烂   �   �   ; G                      I        �_glfwPollJoystickNull  >�   js  AJ          D   
 >t    mode  A           D                           B     �  Ojs     t   Omode  O �   0              �     $       ,  �    -  �   .  �,       0      
 ^       b      
 �       �      
 �       �      
 �     �   e   A G                       J        �_glfwTerminateJoysticksNull                         B  O   �   (              �            (  �    )  �,       0      
 |       �      
 �     �   �   @ G                       �        �_glfwUpdateGamepadGUIDNull 
 >p   guid  AJ          D                           B     p  Oguid  O  �   (              �            6  �    7  �,       0      
 e       i      
 �       �      
     v ��3.]H�\�)r�   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\Release\glfw3.pdb �6j���謂^蛏虦B�/铏B3襧B繣驿H3-�N	YyFM吲mr_匴K�$蟊惺�彿6}m5/端祆癜~t偼亁gOu俣遂祚皛t� Za�坅n4�硓樝)���        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       ]                 .debug$S       l                .text$mn             覲A     .debug$S       �              .text$mn              �+斏     .debug$S       �              .text$mn              �猴     .debug$S                    .text$mn    	          .B+�     .debug$S    
   �          	    .text$mn              .B+�     .debug$S       �                                        	        7               M               e           .rdata      
                       �       
    .debug$T       |                 .chks64        x                 �   _glfwInitJoysticksNull _glfwTerminateJoysticksNull _glfwPollJoystickNull _glfwGetMappingNameNull _glfwUpdateGamepadGUIDNull ??_C@_00CNPNBAHC@@ 