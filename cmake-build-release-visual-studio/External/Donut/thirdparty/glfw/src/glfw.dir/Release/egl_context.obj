d唺 h馟h  �      .drectve        ]   �               
 .debug$S        鋪    鍡         @ B.text$mn        +  鶙  $�      7    P`.debug$S           J�  J�      L   @B.text$mn        �  B�  �      �    P`.debug$S        �  Z�  B�      V   @B.text$mn        B   災  嗄          P`.debug$S        D  �  `�         @B.text$mn        _  炱  K�      &    P`.debug$S        �  撬  K�      J   @B.text$mn        �   /�  鹤          P`.debug$S        \  鲎  R�         @B.text$mn        @   寿  
�          P`.debug$S        H  (�  p�         @B.text$mn        �   枸  誊      !    P`.debug$S        �  �  赅      &   @B.text$mn        C   f�  ┾          P`.debug$S        �  砚  u�         @B.text$mn        K   )�  t�          P`.debug$S           掑  叉         @B.text$mn        ,   铈  �          P`.debug$S        �   8�  翮         @B.text$mn        K   �  g�          P`.debug$S           呰  ラ         @B.text$mn        �   衢  婈          P`.debug$S        �  �  鹅         @B.text$mn        J   V�  犿          P`.debug$S        0  茼  �      
   @B.text$mn           p�  ��          P`.debug$S        �   旓  t�         @B.xdata             酿              @0@.pdata             甜  仞         @0@.xdata             鲳              @0@.pdata               
�         @0@.xdata             (�              @0@.pdata             0�  <�         @0@.xdata             Z�  r�         @0@.pdata             |�  堮         @0@.xdata             ︸              @0@.pdata               厚         @0@.xdata              伛           @0@.pdata             �  �         @0@.xdata             ,�              @0@.pdata             D�  P�         @0@.xdata             n�  傭         @0@.pdata             狉           @0@.xdata             黍  隍         @0@.pdata               �         @0@.xdata             "�  6�         @0@.pdata             T�  `�         @0@.xdata             ~�  庴         @0@.pdata               阁         @0@.xdata             煮              @0@.pdata             摅  牦         @0@.xdata             �              @0@.pdata             �  �         @0@.xdata             :�              @0@.pdata             B�  N�         @0@.xdata             l�              @0@.pdata             t�  ��         @0@.xdata             烎              @0@.pdata             ︳  掺         @0@.rdata             恤              @@@.rdata          '   佤              @@@.rdata          '   ��              @@@.rdata          =   &�              @@@.rdata          N   c�              @P@.rdata          C   滨              @P@.rdata          K   趱              @P@.rdata          f   ?�              @P@.rdata          D   ヶ              @P@.rdata          Q   轹              @P@.rdata             :�              @@@.rdata          (   U�              @@@.rdata          D   }�              @P@.rdata          D   流              @P@.rdata          ;   �              @@@.rdata             @�              @@@.rdata          $   Y�              @@@.rdata             }�              @@@.rdata          .   欨              @@@.rdata          5   区              @@@.rdata          '                 @@@.rdata          )   #�              @@@.rdata          (   L�              @@@.rdata          )   t�              @@@.rdata          M   濝              @P@.rdata             犍              @@@.rdata             貔              @@@.rdata                           @@@.rdata             �              @0@.rdata             �              @@@.rdata             +�              @@@.rdata             9�              @@@.rdata             G�              @@@.rdata             S�              @@@.rdata          
   a�              @@@.rdata             n�              @@@.rdata             y�              @@@.rdata             婜              @@@.rdata             滫              @@@.rdata                           @@@.rdata             批              @@@.rdata             扎              @@@.rdata             泮              @@@.rdata             篾              @@@.rdata             �              @@@.rdata          *   �              @@@.rdata             >�              @@@.rdata             T�              @@@.rdata             i�              @@@.rdata             傷              @@@.rdata              淃              @@@.rdata             畸              @@@.rdata              佧              @@@.rdata                           @@@.rdata             �              @@@.rdata          "   0�              @@@.rdata          #   R�              @@@.rdata          "   u�              @@@.rdata             楛              @@@.rdata                            @@@.rdata             吸              @@@.rdata             潼              @@@.rdata             �              @@@.rdata             !�              @@@.rdata             8�              @@@.rdata          "   O�              @@@.rdata             q�              @@@.rdata          "   慅              @@@.rdata          )   昌              @@@.rdata             埤              @@@.rdata             辇              @@@.rdata                           @@@.rdata             �              @@@.rdata          #   �              @@@.debug$T        |   4�              @ B.chks64         P  剥               
     /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   u     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\glfw.dir\Release\egl_context.obj : < `  �  & y�   & y�  Microsoft (R) Optimizing Compiler  �   �>       URLZONE_LOCAL_MACHINE     URLZONE_INTRANET     URLZONE_TRUSTED     URLZONE_INTERNET  n    URLZONEREG_DEFAULT  n   URLZONEREG_HKLM # �   BINDHANDLETYPES_DEPENDENCY  �    PIDMSI_STATUS_NORMAL  �   PIDMSI_STATUS_NEW  �   PIDMSI_STATUS_PRELIM  �   PIDMSI_STATUS_DRAFT ! �   PIDMSI_STATUS_INPROGRESS  �   PIDMSI_STATUS_EDIT  �   PIDMSI_STATUS_REVIEW  �   PIDMSI_STATUS_PROOF ! �    COINITBASE_MULTITHREADED ' �  �   CLSCTX_ACTIVATE_X86_SERVER , �   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL  �    MDT_EFFECTIVE_DPI  �    NODE_INVALID  �   NODE_ELEMENT  �   NODE_ATTRIBUTE  �   NODE_TEXT  �   NODE_CDATA_SECTION  �   NODE_ENTITY_REFERENCE  �   NODE_ENTITY $ �   NODE_PROCESSING_INSTRUCTION  �   NODE_COMMENT  �  	 NODE_DOCUMENT  �  
 NODE_DOCUMENT_TYPE  �   NODE_DOCUMENT_FRAGMENT  �    XMLELEMTYPE_ELEMENT  �   XMLELEMTYPE_TEXT  �   XMLELEMTYPE_COMMENT  �   XMLELEMTYPE_DOCUMENT  �   XMLELEMTYPE_DTD  �   XMLELEMTYPE_PI  �   VT_I2  �   VT_I4  �   VT_BSTR  �  	 VT_DISPATCH  �  
 VT_ERROR  �   VT_VARIANT  �  
 VT_UNKNOWN  �   VT_I1  �   VT_I8  �  $ VT_RECORD  �  � �VT_RESERVED  
d        _glfw  �    TYSPEC_CLSID  �   TYSPEC_FILEEXT  �   TYSPEC_MIMETYPE  �   TYSPEC_FILENAME  �   TYSPEC_PROGID  �   TYSPEC_PACKAGENAME + w   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 w   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - w   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 w   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS * �   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP     PowerUserMaximum  �    ServerApplication  ~    IdleShutdown  u   COR_VERSION_MAJOR_V2  	    TKIND_ENUM  	   TKIND_RECORD  	   TKIND_MODULE  	   TKIND_INTERFACE  	   TKIND_DISPATCH  	   TKIND_COCLASS  	   TKIND_ALIAS  	   TKIND_UNION  �   CC_CDECL  �   CC_MSCPASCAL  �   CC_PASCAL  �   CC_MACPASCAL  �   CC_STDCALL  �   CC_FPFASTCALL  �   CC_SYSCALL  �   CC_MPWCDECL  �   CC_MPWPASCAL  �    FUNC_VIRTUAL  �   FUNC_PUREVIRTUAL  �   FUNC_NONVIRTUAL  �   FUNC_STATIC  �    VAR_PERINSTANCE  �   VAR_STATIC  �   VAR_CONST # �   BINDSTATUS_FINDINGRESOURCE  �   BINDSTATUS_CONNECTING  �   BINDSTATUS_REDIRECTING % �   BINDSTATUS_BEGINDOWNLOADDATA # �   BINDSTATUS_DOWNLOADINGDATA # �   BINDSTATUS_ENDDOWNLOADDATA + �   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �   BINDSTATUS_INSTALLINGCOMPONENTS ) �  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �  
 BINDSTATUS_USINGCACHEDCOPY " �   BINDSTATUS_SENDINGREQUEST $ �   BINDSTATUS_CLASSIDAVAILABLE % �  
 BINDSTATUS_MIMETYPEAVAILABLE * �   BINDSTATUS_CACHEFILENAMEAVAILABLE & �   BINDSTATUS_BEGINSYNCOPERATION $ �   BINDSTATUS_ENDSYNCOPERATION # �   BINDSTATUS_BEGINUPLOADDATA ! �   BINDSTATUS_UPLOADINGDATA ! �   BINDSTATUS_ENDUPLOADDATA # �   BINDSTATUS_PROTOCOLCLASSID  �   BINDSTATUS_ENCODING - �   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �   BINDSTATUS_CLASSINSTALLLOCATION  �   BINDSTATUS_DECODING & �   BINDSTATUS_LOADINGMIMEHANDLER , �   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �   BINDSTATUS_FILTERREPORTMIMETYPE ' �   BINDSTATUS_CLSIDCANINSTANTIATE % �   BINDSTATUS_IUNKNOWNAVAILABLE  �   BINDSTATUS_DIRECTBIND  �   BINDSTATUS_RAWMIMETYPE " �    BINDSTATUS_PROXYDETECTING   �  ! BINDSTATUS_ACCEPTRANGES  �  " BINDSTATUS_COOKIE_SENT + �  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �  $ BINDSTATUS_COOKIE_SUPPRESSED ( �  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �  ' BINDSTATUS_COOKIE_STATE_REJECT ' �  ( BINDSTATUS_COOKIE_STATE_PROMPT & �  ) BINDSTATUS_COOKIE_STATE_LEASH * �  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �  + BINDSTATUS_POLICY_HREF  �  , BINDSTATUS_P3P_HEADER + �  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + �  / BINDSTATUS_SESSION_COOKIES_ALLOWED   �  0 BINDSTATUS_CACHECONTROL . �  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) �  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & �  3 BINDSTATUS_PUBLISHERAVAILABLE ( �  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �  5 BINDSTATUS_SSLUX_NAVBLOCKED , �  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �  8 BINDSTATUS_64BIT_PROGRESS  �  8 BINDSTATUS_LAST  �  9 BINDSTATUS_RESERVED_0  �  : BINDSTATUS_RESERVED_1  �  ; BINDSTATUS_RESERVED_2  �  < BINDSTATUS_RESERVED_3  �  = BINDSTATUS_RESERVED_4  �  > BINDSTATUS_RESERVED_5  �  ? BINDSTATUS_RESERVED_6  �  @ BINDSTATUS_RESERVED_7  �  A BINDSTATUS_RESERVED_8  �  B BINDSTATUS_RESERVED_9  �  C BINDSTATUS_RESERVED_A  �  D BINDSTATUS_RESERVED_B  �  E BINDSTATUS_RESERVED_C  �  F BINDSTATUS_RESERVED_D  �  G BINDSTATUS_RESERVED_E  �  H BINDSTATUS_RESERVED_F  �  I BINDSTATUS_RESERVED_10  �  J BINDSTATUS_RESERVED_11  �  K BINDSTATUS_RESERVED_12  �  L BINDSTATUS_RESERVED_13  �  M BINDSTATUS_RESERVED_14  �    DESCKIND_NONE  �   DESCKIND_FUNCDESC  �   DESCKIND_VARDESC  �   DESCKIND_TYPECOMP   �   DESCKIND_IMPLICITAPPOBJ  �    CIP_DISK_FULL  �   CIP_ACCESS_DENIED ! �   CIP_NEWER_VERSION_EXISTS ! �   CIP_OLDER_VERSION_EXISTS  �   CIP_NAME_CONFLICT 1 �   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + �   CIP_EXE_SELF_REGISTERATION_TIMEOUT  �   CIP_UNSAFE_TO_ABORT  �   CIP_NEED_REBOOT % �   TP_CALLBACK_PRIORITY_INVALID  �   BINDSTRING_HEADERS   �   BINDSTRING_ACCEPT_MIMES  �   BINDSTRING_EXTRA_URL  �   BINDSTRING_LANGUAGE  �   BINDSTRING_USERNAME  �   BINDSTRING_PASSWORD  �   BINDSTRING_UA_PIXELS  �   BINDSTRING_UA_COLOR  �  	 BINDSTRING_OS  �  
 BINDSTRING_USER_AGENT $ �   BINDSTRING_ACCEPT_ENCODINGS  �   BINDSTRING_POST_COOKIE " �  
 BINDSTRING_POST_DATA_MIME  �   BINDSTRING_URL  �   BINDSTRING_IID ' �   BINDSTRING_FLAG_BIND_TO_OBJECT $ �   BINDSTRING_PTR_BIND_CONTEXT  �   BINDSTRING_XDR_ORIGIN   �   BINDSTRING_DOWNLOADPATH  �   BINDSTRING_ROOTDOC_URL $ �   BINDSTRING_INITIAL_FILENAME " �   BINDSTRING_PROXY_USERNAME " �   BINDSTRING_PROXY_PASSWORD ! �   BINDSTRING_ENTERPRISE_ID  �   BINDSTRING_DOC_URL  �    SYS_WIN16  �   SYS_WIN32  �   SYS_MAC  �   PARSE_CANONICALIZE  �   PARSE_FRIENDLY  �   PARSE_SECURITY_URL  �   PARSE_ROOTDOCUMENT  �   PARSE_DOCUMENT  �   PARSE_ANCHOR ! �   PARSE_ENCODE_IS_UNESCAPE  �   PARSE_DECODE_IS_ESCAPE  �  	 PARSE_PATH_FROM_URL  �  
 PARSE_URL_FROM_PATH  �   PARSE_MIME  �   PARSE_SERVER  �  
 PARSE_SCHEMA  �   PARSE_SITE  �   PARSE_DOMAIN  �   PARSE_LOCATION  �   PARSE_SECURITY_DOMAIN  �   PARSE_ESCAPE  s   PSU_DEFAULT  �   QUERY_EXPIRATION_DATE " �   QUERY_TIME_OF_LAST_CHANGE  �   QUERY_CONTENT_ENCODING  �   QUERY_CONTENT_TYPE  �   QUERY_REFRESH  �   QUERY_RECOMBINE  �   QUERY_CAN_NAVIGATE  �   QUERY_USES_NETWORK  �  	 QUERY_IS_CACHED   �  
 QUERY_IS_INSTALLEDENTRY " �   QUERY_IS_CACHED_OR_MAPPED  �   QUERY_USES_CACHE  �  
 QUERY_IS_SECURE  �   QUERY_IS_SAFE ! �   QUERY_USES_HISTORYFOLDER  �    CHANGEKIND_ADDMEMBER   �   CHANGEKIND_DELETEMEMBER  �   CHANGEKIND_SETNAMES $ �   CHANGEKIND_SETDOCUMENTATION  �   CHANGEKIND_GENERAL  �   CHANGEKIND_INVALIDATE   �   CHANGEKIND_CHANGEFAILED    POINT 
   LPLONG  �  _FILETIME    LPDIEFFECT  �  LPDIENVELOPE  �  IDirectInputEffect  �  _DIACTIONW    tagPOINT  �  DIEFFECTINFOW  X  LPDIPROPHEADER    IDirectInputEffectVtbl  �  RECT  �  LPDIDEVICEIMAGEINFOW    LPCDIDEVICEINSTANCEW  �  DIEFFECT  �  LPCDIFILEEFFECT + �  LPDIENUMCREATEDEFFECTOBJECTSCALLBACK  �  DIENVELOPE  �  LPDIOBJECTDATAFORMAT  �  LPCDIEFFECT  "   D3DCOLOR  �  FILETIME  �  LPDIACTIONW  �  LPDIEFFESCAPE  �  DIDEVCAPS  �  _DICOLORSET  �  _DIDEVICEIMAGEINFOW  {  LPDIDEVICEINSTANCEW  ]  LPCDIPROPHEADER # 8  LPDICONFIGUREDEVICESCALLBACK  �  DIDEVICEOBJECTDATA  g  LPDIDEVICEOBJECTDATA  �  DIPROPHEADER  �  DIDATAFORMAT % S  LPDIENUMDEVICEOBJECTSCALLBACKW  �  LPDIRECTINPUTEFFECT  �  DIEFFESCAPE  �  _DIOBJECTDATAFORMAT  �  LPDIFILEEFFECT " :  LPDICONFIGUREDEVICESPARAMSW  �  DIDEVICEINSTANCEW    LPDIRECTINPUTDEVICE8W  �  LPDIEFFECTINFOW  �  LPCDIDEVICEOBJECTDATA  J  LPDIDEVCAPS  �  _DIACTIONFORMATW  /  LPDIACTIONFORMATW  m  LPCDIDATAFORMAT  �  DIFILEEFFECT    LPDIENUMDEVICESCALLBACKW  �  _DIDATAFORMAT ! �  LPDIDEVICEIMAGEINFOHEADERW 
 p   int8_t ! P  LPCDIDEVICEOBJECTINSTANCEW  �  DICOLORSET   w  LPDIDEVICEOBJECTINSTANCEW  "  LPDWORD  �  LPDIENUMEFFECTSCALLBACKW   �  _DIDEVICEIMAGEINFOHEADERW 
 *  LPGUID $ �  LPDIENUMEFFECTSINFILECALLBACK  �  DIDEVICEOBJECTINSTANCEW  �  LPCDIEFFECTINFOW $ 2  LPDIENUMDEVICESBYSEMANTICSCBW  �  IDirectInputDevice8WVtbl ! B  _DICONFIGUREDEVICESPARAMSW  ?  IDirectInput8WVtbl  �  GLFWdeallocatefun 
 #   WPARAM  
  IDirectInputDevice8W  	  _GLFWjoyobjectWin32  q  GUID  �  tagRAWKEYBOARD  �  PFN_wglGetCurrentDC  �  PFN_wglMakeCurrent  �  GLFWallocatefun ( �  PFNWGLCREATECONTEXTATTRIBSARBPROC  �  LIST_ENTRY    _GLFWjoystickWin32  �  GLFWreallocatefun   �  PFNWGLSWAPINTERVALEXTPROC  �  PFN_wglCreateContext  �  RTL_CRITICAL_SECTION  �  tagRAWMOUSE    IUnknownVtbl  �  tagRAWINPUTHEADER  �  PFN_wglGetCurrentContext 
 �  HRGN__    HINSTANCE  �  _LIST_ENTRY  �  _XINPUT_VIBRATION " �  PRTL_CRITICAL_SECTION_DEBUG " �  _RTL_CRITICAL_SECTION_DEBUG  �  _XINPUT_GAMEPAD  w  XINPUT_CAPABILITIES  �  _GLFWmapelement  �  XINPUT_GAMEPAD ( ?  PFN_SetProcessDpiAwarenessContext  �  HRGN  �  XINPUT_VIBRATION  �  HGLRC__   .  PFN_XInputGetCapabilities # F  PFN_AdjustWindowRectExForDpi  �  HINSTANCE__    HDEVNOTIFY ' �  PFNWGLGETEXTENSIONSSTRINGEXTPROC  �  DWM_BLURBEHIND  �  PFN_wglShareLists  �  CHANGEFILTERSTRUCT  �  RAWMOUSE 
 @  LPCSTR  �  RAWINPUT  �  IDirectInput8W  �  RAWINPUTHEADER  �  PROC  !   uint16_t  �  tagRECT    HWND  �  HMONITOR__  %  PFN_DirectInput8Create  �  HMONITOR $ V  PFN_DwmEnableBlurBehindWindow  #   VkSurfaceKHR 
 !   USHORT      uint8_t ! I  PFN_GetSystemMetricsForDpi  A  PFN_GetDpiForWindow  �  IUnknown " Y  PFN_DwmGetColorizationColor  �  PFN_wglGetProcAddress  P  PFN_DwmFlush 
 q  IID % E  PFN_OSMesaCreateContextAttribs  �  _GLFWmapping  3  PFN_XInputGetState ' �  PFNWGLGETEXTENSIONSSTRINGARBPROC  �  GLFWallocator  �  _GLFWtimerWin32  �  PFN_wglDeleteContext  ]  PROCESS_DPI_AWARENESS  z  OSVERSIONINFOEXW  �  _GLFWlibraryNull  "   ULONG  �  RAWKEYBOARD  t   BOOL 
 �  HDC 
 �  RAWHID  �  _RTL_CRITICAL_SECTION    HICON__  �  CRITICAL_SECTION  �  tagRAWHID  �  _GLFWmutexWin32  �  HDC__  �  tagRAWINPUT  �  _GLFWjoystick 
   LPVOID  G  PFN_OSMesaDestroyContext * �  PFNWGLGETPIXELFORMATATTRIBIVARBPROC  �  _GLFWlibraryWGL 
 C  LPRECT " N  PFN_DwmIsCompositionEnabled 
   HANDLE  �  HGLRC  |  _XINPUT_STATE  #   ULONGLONG & :  PFN_ChangeWindowMessageFilterEx  V  GLFWmonitor  M  PFN_OSMesaGetDepthBuffer ! `  PFN_SetProcessDpiAwareness  �  _GLFWcontextWGL  "  LPUNKNOWN  �  GLFWimage  c  PFN_GetDpiForMonitor  u   uint32_t 
 �  HWND__  u   UINT  �  VkAllocationCallbacks  0  PFN_eglCreateContext  �  _TP_CALLBACK_PRIORITY  �  HICON  @  GLFWwindow  �  GLFWgammaramp  ~  tagShutdownType  �  MONITOR_DPI_TYPE  �  tagCALLCONV  	  tagTYPEKIND  #   rsize_t  &  PFN_SetProcessDPIAware  |  XINPUT_STATE  z  _OSVERSIONINFOEXW  d  _GLFWlibrary ( w  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  w  _XINPUT_CAPABILITIES  M  PFN_OSMesaGetColorBuffer  s  _GLFWwindowWin32  s  _tagPSUACTION  J  PFN_OSMesaMakeCurrent $ =  PFN_EnableNonClientDpiScaling     LONG_PTR  Z  GLFWmonitorfun  j  PFN_RtlVerifyVersionInfo  �  tagBINDSTRING  !   ATOM / 0  PFNEGLCREATEPLATFORMWINDOWSURFACEEXTPROC  #   ULONG_PTR    tagURLZONE & =  PFNEGLGETPLATFORMDISPLAYEXTPROC    VkPhysicalDevice  �  __MIDL_ICodeInstall_0001  p  PCHAR  �  tagBINDSTATUS  G  GLFWwindowclosefun  q  _GUID  n  _URLZONEREG  n  _GLFWlibraryWin32  !   wchar_t  V  GLFWkeyfun    EGLNativeDisplayType 
 t   EGLint  !   WORD    _GLFWwndconfig  2  PFN_vkVoidFunction  �  HCURSOR  �  VkResult  #   uint64_t ' �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS    _USER_ACTIVITY_PRESENCE    PLONG    _GLFWinitconfig  p  va_list  P  GLFWmousebuttonfun      BYTE  �  _GLFWcursorWin32 
 5  PCWSTR  �  PFN_OSMesaGetProcAddress  �  PFNGLGETSTRINGIPROC    EGLDisplay  �  _GLFWmonitorWin32   S  PFN_vkGetInstanceProcAddr     LONG  -  PFN_eglBindAPI  u   EGLBoolean  �  _GLFWplatform  S  GLFWscrollfun  �  PFNGLGETSTRINGPROC    EGLSurface ! B  PFN_OSMesaCreateContextExt  ]  GLFWjoystickfun 
 #   SIZE_T      GLubyte ! 0  PFN_eglCreateWindowSurface # u  ReplacesCorHdrNumericDefines  "   DWORD    EGLNativeWindowType 
   PSHORT  q  _GLFWcursor  "   TP_VERSION  D  GLFWframebuffersizefun  \  GLFWcharmodsfun  �  VARENUM  D  GLFWwindowposfun    VkInstance  l  GLFWvidmode  #   DWORD64  S  GLFWcursorposfun  G  GLFWwindowrefreshfun  j  _GLFWerror  !  PFN_eglGetConfigs      BOOLEAN  �  tagTYSPEC  �  PFNGLGETINTEGERVPROC  f  _GLFWwindow  g  _GLFWmutex  �  tagVARKIND  /  _GLFWctxconfig    PVOID  �  PFN_eglGetProcAddress  t   errno_t  q   WCHAR     PBYTE  t   GLint  u   EGLenum  ~  _GLFWtls  �  _tagQUERYOPTION  $  PFN_eglGetDisplay     HRESULT  8  PFN_eglSwapInterval    _GLFWmonitorNull    EGLConfig    _GLFWwindowNull ! �  __MIDL_IGetBindHandle_0001  Y  GLFWcharfun 
    LONG64  �  tagCOINITBASE  �  tagApplicationType  5  LPCWSTR  �  tagDOMNodeType  )  PFN_eglInitialize  q  PWSTR  5  PFN_eglMakeCurrent  �  tagCHANGEKIND 
 u   UINT32  �  tagSYSKIND  J  GLFWwindowfocusfun   M  GLFWwindowcontentscalefun  +  PFN_eglTerminate  #   uintptr_t 
 q  LPWSTR  #   UINT_PTR  &  PFN_eglGetError  �  tagFUNCKIND  t   INT32  �  PIDMSI_STATUS_VALUE ( �  PTP_CLEANUP_GROUP_CANCEL_CALLBACK 
 u   GLenum  J  GLFWcursorenterfun 
 u   GLuint    EGLContext  2  GLFWproc  :  PFN_eglQueryString    OSMesaContext 
 #   size_t  �  _GLFWtlsWin32  s  _GLFWfbconfig  D  GLFWwindowsizefun  2  PFN_eglDestroySurface  �  _GLFWcontext    PFN_eglGetConfigAttrib  �  tagGLOBALOPT_EH_VALUES     SHORT  2  GLFWglproc  J  GLFWwindowiconifyfun    PLONG64  �  tagCLSCTX  2  PFN_eglDestroyContext  2  PFN_eglSwapBuffers     INT_PTR  `  GLFWdropfun  t   GLFWbool  �  tagXMLEMEM_TYPE  p   CHAR  �  _tagPARSEACTION  �  tagDESCKIND  �  _GLFWmonitor  J  GLFWwindowmaximizefun  �   �      頴}�穲町v�
c�.丨a� �+篬鰌�莩�  I    �-�雧n�5L屯�:I硾�鮎访~(梱  �    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �    )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     8�'预P�憖�0R�(3銖� pN*�  g   6觏v畿S倂9紵"�%��;_%z︹  �   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  E   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  �   D���0�郋鬔G5啚髡J竆)俻w��  �   錵s铿勃砓b棬偡遯鮓尛�9泂惻  *   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  x   �F9�6K�v�/亅S诵]t婻F廤2惶I  �   d2軇L沼vK凔J!女計j儨杹3膦���  
   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  ]   樸7 忁�珨��3]"Fキ�:�,郩�  �    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   衠琪槡铟钭}_XO>�蛭X�7Mp处d  B   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  �   �D;窼僞k渔A�;��?缞鳗5翰�?*R
     5 KO诹硃毣�'R烣�7`埀M@懅y榵  [   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  �   聤�苮g8鄞<aZ�%4)闪�|袉uh�  �   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  9   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  �   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  l   �:2K] �
j�苊赁e�
湿�3k椨�  �   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  	   6��7@L�.�梗�4�檕�!Q戸�$�  N	   �儔14褥緅�3]饃鹷�hK3g搋bA竑  �	   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  �	   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  0
   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  y
   敌战:�權D叵�氏
p勣a灜尕壐玚)殔  �
   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  �
   渐袿.@=4L笴速婒m瑜;_琲M %q�  M   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/  �   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  ?   �>2
^�﨟2W酟傲X{b?荼猲�;  ~   死╇D�#/��4鶄>G63齛w�i�->M  �   E縄�7�g虩狱呂�/y蛨惏l斋�笵  
   c�#�'�縌殹龇D兺f�$x�;]糺z�  [
   $G\|R_熖泤煡4勄颧绖�?(�~�:  �
   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠       栀��綔&@�.�)�C�磍萘k  H   釳�>�H?6蓙�� 祍<ベ垽�=j逃�  �   2W瓓�<X	綧]�龐IE?'笼t唰��  �   晁X歌符�2澋U�'煈覽b�
蟣;-�     |q�6桢赤汗mv訔�	
爟~胱�>?妼BK�,  b   �"睱建Bi圀対隤v��cB�'窘�n  �   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏     bRè1�5捘:.z錨{娯啹}坬麺P  R   攄繠�
\b擫5`Om�1悑R钡h�:�47�  �   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  �   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒     5睔`&N_鏃|�<�$�獖�!銸]}"  e   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �   ┫緞A$窄�0� NG�%+�*�
!7�=b     镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  R   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  O   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  6   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  5   噔园�,c珥珸洯濠�繗猍=sZ導  n   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   ��嵉氒髅嘁棭够*ヅ�
�'徺p4  �   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  L   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  �   鷜E恳B�#蠠�,qC吾w�岧儁N篴  �   艶笊\眔Z%師}wы孜+HN鯥湔N     yk"&�棎墑�T-亴鏏愐煋搂脠+]�  N   晊褃 �=�韀晝阓�!熝神�+k�2�<$]�  �   蠯3
掽K謈 � l�6襕鞜��H#�  �   �8��/X昋旒�.胱#h=J"髈篒go#     蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  e   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  L   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  #   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  u   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   Eム聂�
C�?潗'{胿D'x劵;釱�     r�L剟FsS鏴醼+E千I呯贄0鬬/�  Q   �*o驑瓂a�(施眗9歐湬

�  �   鹰杩@坓!)IE搒�;puY�'i憷n!  �   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  7    I嘛襨签.濟;剕��7啧�)煇9触�.  w    萾箒�$.潆�j閖i转pf-�稃陞��  �   �5�-�塎To>我c撙侹�迫�蛷铕.~�!     娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  ]   G髼*悭�2睆�侻皣軁舃裄樘珱)  �   齛|)3h�2%籨糜/N_燿C虺r_�9仌  �   8蟴B或绢溵9"C dD揭鞧Vm5TB�  C   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  �   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  #   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  m   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   �~鴧傳.P怬WsP-"焫#N�:�&場璁      �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  P    K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  �    "�挨	b�'+舒�5<O�呱_歲+/�P�?  �    FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  5!   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  |!   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �!   �呾��+h7晃O枖��*谵|羓嗡捬  	"   j轲P[塵5m榤g摏癭 鋍1O骺�*�  R"   綔)\�谑U⒊磒'�!W磼B0锶!;  �"   sL&%�znOdz垗�M,�:吶1B滖  �"   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  9#   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �#   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  �#   �n儹`
舔�	Y氀�:b
#p:  %$   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  m$   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �$    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  %   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  N%   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �%   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �%   掴'圭,@H4sS裬�!泉:莠й�"fE)  7&   覽s鴧罪}�'v,�*!�
9E汲褑g;  �&   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �&   +椬恡�
	#G許�/G候Mc�蜀煟-  '   0T砞獃钎藰�0逪喌I窐G(崹�  Z'   閯�価=�<酛皾u漑O�髦jx`-�4睲�  �'   戹�j-�99檽=�8熈讠鳖铮�  �'   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  @(   ,�<鈬獿鍢憁�g$��8`�"�  �(   潝(綊r�*9�6}颞7V竅\剫�8値�#  �(   ^憖�眜蘓�y冊日/缁ta铁6殔  ()   魯f�u覬n\��zx騖笹笾骊q*砎�,�  p)   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  �)   _%1糠7硘籺蚻q5饶昈v纪嗈�  *   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  P*   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  �*   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �   (         �  P   $  �  *  (  �    �   �*   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\xinput.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dinput.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h D:\RTXPT\External\Donut\thirdparty\glfw\src\egl_context.c C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dbt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_thread.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\internal.h D:\RTXPT\External\Donut\thirdparty\glfw\src\platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_platform.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h    �       L.  �  �    �  �   
 @USVWATAVAWH峫$餒侅  H�    H3腍塃 E3銶嬸A嬡H孃H9�  H嬹E孅uH�    �  �    3篱�  H婤(H吚tL嫺�  L岲$ I嬛H嬒�    吚t諄?  u7範0  ��  吚u_�h  嬋�    L嬂H�    �  �    3篱R  耿0  ��  吚u(�h  嬋�    L嬂H�    �  �    3篱  9  ��  E嬏A嬆侚  u+婫婳髫纼�侚  uA�   �侚  uA�   D嬂A內9_DD缷G 吚t1= u
荄$4�1  �= u荄$4�1  �   荄$0�1  A內媁凓uD9gt)岾嬅荄�0�0  婫塗�0岻峇荄�0�0  塂�0峑D9gt!D9%  t嬅岾峐荄�0�1  荄�0   E吷t嬅岾峐荄�0�0  D塋�0E吚t3嬅岾峐荄�0�0  D塂�0�侚  u婫�   塂$4荄$0�0  D9%  t=婫$=P u嬅岾荄�0�   D塪�0�=P u嬅岾荄�0�   荄�0�   峐H�
�  L峀$0嬅M嬊H媆$ H嬘荄�080  荄�480  ��  H墕�  H吚u(�h  嬋�    L嬂H�    �  �    3篱5  A嬏A9N4t9
  t荄$0�0  �   荄$4�0  E9f8u嬃�燎D�0�0  荄�0�0  �羴=(     u'D9%0  t嬃峇A嬏A9N<斄荄�0�1  塋�0岼嬃H嬑荄�080  荄�480  �H  �
�  吷t"侚2  tH�
�  L峀$0L嬂H嬘��  �H�
�  L峀$0L嬂H嬘��  H墕�  H吚u(�h  嬋�    L嬂H�    �  �    3篱-  H墳�  D9%  吚   �?  H�    H塃鐷�    H塃餒�    H塃蠬�    H塃豅塭鳯塭郘塪$ ua�L島蠬岴鐻D餓�H呟tII孅A�   H�    H嬎�    吚A嬙斅9  uH嬎�    H墕�  H吚u
I媆�H�荋呟u篖9  uH�    �  �    3离YH�
    �   H墡X  H�
    H墡`  H�
    H墡h  H�
    H墡p  H�
    H墡x  H�
    H墡�  H婱 H3惕    H伳  A_A^A\_^[]�   �   4   �    C   �   M   H    t   Q    �   �    �   �    �   P    �   �   �   H    �   �    �   �    �   P    �   �   �   H    �   �    �  �    2  �    x  �    �  �    �  �    �  P    �  �   �  H    �  �      �    )  �    d  �    j  �    }  �    �  �    �  �    �  �    �  �    �  P    �  �   �  H    �  �    �  �     �     �     �   Y  '   a  ?    o  �    y  E    �  �   �  H    �  R    �  S    �  T    �  U    �  V      W      Y       �     ; G            +  %   
  )        �_glfwCreateContextEGL  >+   window  AJ        ;  AL  ;     � >)   ctxconfig  AK        1  AM  1      AM �    �  >q   fbconfig  AP        +  AV  +      AV �    �  >    config  AI  �    � B    Q     �   ��  >    share  AW  >     � >    native  AH  h    D *   >t     index * A   �    � # =  Z  t C �  � f �  A   �    � ~& A   .     ]b& � � �  4  A  
      >:    attribs  D0    >t     flags  A         Ah  <    �    A  9      Ah /    W 
 >t     mask  Ai       Ai /    R  >5    es2sonames  D�    >]    sonames  AV  E    Q  AV �    �  >5    es1sonames  D�    >;    glsonames  B    3    � : Z
   o       o    o    o    o  �  �  o   >d  _glfw  C   �	  n    -              8          C 
 :   O  P  +  Owindow  X  )  Octxconfig  `  q  Ofbconfig        Oconfig  0   :  Oattribs  �   5  Oes2sonames  �   5  Oes1sonames      ;  Oglsonames  9�       -   9�       &   9�       -   9�       &   9�      0   9�      &   9b      �   9�      0   9�      0   9�      &   O �   �          +  �  X   �      8 �%   ; �+   = �1   ? �@   A �Q   > �X   E �a   F �h   H �|   K ��   M ��   O ��   R ��   W ��   Y ��   \ ��   ` �   b �  d �  i �#  j �+  k �3  l �9  r �N  t �U  v �]  x �_  y �f  { �n   �  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �/  � �8  � �B  � �T  � �V  � �]  � �r  � �u  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �&  � �/  � �M  � �O  � �h  � �z  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��   �5   �H  " �M  � �P  & �u  ) ��  * ��  " ��  . ��  0 ��  2 ��  6 ��  = �
  > �,   M    0   M   
 b   M    f   M   
 r   M    v   M   
 �   M    �   M   
 �   M    �   M   
 �   M    �   M   
 �   M    �   M   
 �   M    �   M   
 �   M    �   M   
   M      M   
 *  M    .  M   
 R  M    V  M   
 s  M    w  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M      M   
 9  M    =  M   
 I  M    M  M   
 ]  M    a  M   
 m  M    q  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 ,  M    0  M   
 �  M    �  M   
   M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M    �  M   
 �  M      M   
   M      M   
 (  M    ,  M   
 @SH冹PH�    H3腍塂$@3跦�    H9H  H�
    H塡$ H塂$(H塋$0H塡$8t岰H婰$@H3惕    H兡P[肏塼$`嬻H墊$hH孄H嬋�    H�H  H嬋H吚uH婦�0H��艸吚u贖吷uH�    �  �    �  Hc蜨�    A�   H婰�(�    嬎H�    吚斄�
  H�
H  �    H�
H  H�    H�P  �    H�
H  H�    H�X  �    H�
H  H�    H�`  �    H�
H  H�    H�h  �    H�
H  H�    H�p  �    H�
H  H�    H�x  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H�
H  H�    H��  �    H9P  H嬋H��  勱  H9X  勢  H9`  勑  H9h  劽  H9p  劧  H9x  劑  H9�  劀  H9�  剰  H9�  剛  H9�  剈  H9�  刪  H9�  刐  H9�  凬  H9�  凙  H��  H吚�1  H吷�(  篣0  3�蠬孁H吚t�h  = 0  u�      �9   劏   H嬜H�
    �    H嬜�$  H�
    �    H嬜�(  H�
    �    H嬜�,  H�
    �    H嬜�4  H�
    �    H嬜�8  H�
    �    H嬜�<  H�
    �    H嬜�@  H�
    �    �D  9$  t(H�
    ��  H�
    H��  ��  H��  H峀$ �8  ��  吚t�@  L婦$ H嬓�
�  ��  ��@  H嬋�`  H婰$ H��  �    H�
�  H吷u+�h  嬋�    L嬂H�    �  �    �    樵  L�  H�   �p  吚u+�h  嬋�    L嬂H�    �  �    �    閼  H�
�  篣0  ��  H吚tH嬓H�
    �    吚�   u嬅H�
�  篣0  �  ��  H吚tH嬓H�
    �    吚�   u嬅H�
�  篣0  �  ��  H吚tH嬓H�
    �    吚�   u嬅H�
�  篣0  �  ��  H吚tH嬓H�
    �    吚�   u嬅H�
�  篣0  �  ��  H吚tH嬓H�
    �    吚�   u嬅H�
�  篣0  �  ��  H吚tH嬓H�
    �    吚t�   �0  �   隓H�    �  �    H�
�  H吷t
�x  H��  H�
H  H吷t�    H�H  3繦媡$`H媩$hH婰$@H3惕    H兡P[�	   �         !   �    (   !   N   Y    k   E    r   �    �   $   �   H    �   '   �   ?    �   *   �   �    �   �    �   G    �   �    �   -   �   �    �   G    �   �      0   
  �      G      �       3   '  �    ,  G    3  �    :  6   A  �    F  G    M  �    T  9   [  �    `  G    g  �    n  <   u  �    z  G    �  �    �  ?   �  �    �  G    �  �    �  B   �  �    �  G    �  �    �  E   �  �    �  G    �  �    �  H   �  �    �  G    �  �    �  K   �  �    �  G      �    
  N     �      G      �    $  Q   +  �    0  G    7  �    >  T   E  �    J  G    Q  �    X  W   _  �    d  G    k  �    u  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �      �      �      �    +  �    T  �    a  �    m  �    }  ]   �  I    �  �    �  `   �  I    �  �    �  c   �  I    �  �    �  f   �  I    �  �    �  i   �  I    �  �    �  l   �  I    �  �    �  o      I    	  �      r     I      �    !  �    *  u   0  �    7  x   >  �    D  �    K  �    V  �    \  �    f  �    t  �    z  �    �  �    �  �    �  �    �  O    �  �    �  �    �  P    �  {   �  H    �  L    �  �    �  �    �  �    �  �    �  P      ~     H      L      �    (  �    7  �   <  I    N  �    Y  �    _  �    n  �   s  I    �  �    �  �    �  �    �  �   �  I    �  �    �  �    �  �    �  �   �  I    �  �    �  �      �      �     I    *  �    5  �    ;  �    J  �   O  I    ^  �    l  Z   v  H    }  �    �  �    �  �    �  �    �  F    �  �    �  Y       �   4  2 G            �     �  '        �_glfwInitEGL 
 >t     i  A   _     S >5    sonames  D(    >@    extensions  AM  M     AM �      >t    attribs  D      M        (  唞L&gL
 Z   �   N M        $  �'&E
 Z   Z   >@    extensions  AH  ?      AH \      N# M        $  咅&E
 Z   Z   >@    extensions  AH        AH %      N# M        $  吂&E
 Z   Z   >@    extensions  AH  �      AH �      N# M        $  厒&E
 Z   Z   >@    extensions  AH  �      AH �      N# M        $  匥&E
 Z   Z   >@    extensions  AH  c      AH �      N" M        $  �E
 Z   Z   >@    extensions  AH  ,      AH I      N� Z#   �  o  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  Z  Z  Z  Z  Z  Z  Z  Z  I    o  (    o  (  o   >d  _glfw  CJ  �	  �    � % D � CJ  H
  �    
  CH  �
  /      CJ  �
  r      CJ �	  �      CJ H
  �      CH �
  i      P                     C  h   $  (  
 :@   O  (   5  Osonames      t  Oattribs  9H      :   9R      &   9.      �   9B      �   9T      �   9d      �   9x      =   9�      �   9�      $   9�      &   9�      )   9�      &   9&      :   9]      :   9�      :   9�      :   9      :   99      :   9�      +   O�   �          �  �  Q   �      h �   j �   l �    �B   � �E    �]   � �g   � �|   � �~   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �0  � �J  � �d  � �~  � ��  � ��  � ��  � ��  � �   � �  � �4  � �N  � �h  � �A  � �M  � �_  � �i  � �w  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �'  � �4  � �O  � �b  � �d  � �~  � ��  � ��  � ��  � ��  � ��  � ��  � ��   ��   �   �   �   �K  
 �W   �]  
 ��   ��  
 ��   ��   ��   ��   ��   ��   �   �'   �3   �9   �b   �i  � �z  � ��  � ��   �,   K    0   K   
 T   K    X   K   
 �   K    �   K   
 �   K    �   K   
 A  K    E  K   
 Q  K    U  K   
 �  K    �  K   
 �  K    �  K   
   K      K   
 %  K    )  K   
   K    �  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
 R  K    V  K   
 b  K    f  K   
   K    "  K   
 :  K    >  K   
 N  K    R  K   
 b  K    f  K   
 v  K    z  K   
 �  K    �  K   
 �  K    �  K   
   K      K   
    K    $  K   
 0  K    4  K   
 @  K    D  K   
 P  K    T  K   
 `  K    d  K   
 p  K    t  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
 �  K    �  K   
    K      K   
   K      K   
    K    $  K   
 0  K    4  K   
 H  K    L  K   
 H冹(H�
�  H吷t�x  H��      H�
H  H吷t�    H�H      H兡(�   �       �       �    $   �    .   F    5   �       �   �   7 G            B      =   (        �_glfwTerminateEGL 
 Z   �   >d  _glfw  CJ  �	         CJ  H
  (     
  CJ �	  !       CJ H
  =       (                      J  9       +   O �   X           B   �     L        �    �   ! �   " �!   % �-   ' �2   ( �=   * �,   L    0   L   
 m   L    q   L   
 �   L    �   L   
 �   L    �   L   
 �   L    �   L   
 �   L    �   L   
 �   L    �   L   
 L塂$H塗$H塋$UATAUAVAWH嬱H侅�   E3�L嬮�9  E嬬uA�   �   E9uDE耠A�   D9b,t$H�    �	  �    3繦伳�   A_A^A]A\]肏�
�  L峂HE3�3�X  HcEH吚u$H�    �  �    3繦伳�   A_A^A]A\]肏塡$xH嬋H塼$p�   H墊$h�    D婨HL峂HH�
�  H嬓H嬸H塃�X  HcMH篐   �    A嬒H塃蠥�塎�9MH帒   L媘8L峹H�L峂�
�  H嬘A�?0  �P  亇0  uFH�
�  L峂ˋ�30  H嬘�P  鯡�t&H�
�  L峂珹窣0  H嬘�P  D卽瑄SA�   婱�荋兤;}H|哃媘0E3�H婨蠬媢谼嬃H嬓H婱8�    H媩$hH嬝H吚�  H婬@H婨@H�镺  H�
�  L峂癆�$0  H嬘�P  婨癓峂碅塆鳤�#0  H�
�  H嬘�P  婨碙峂窤塆麬�"0  H�
�  H嬘�P  婨窵峂糀�A�!0  H�
�  H嬘�P  婨糒峂繟塆A�%0  H�
�  H嬘�P  婨繪峂腁塆A�&0  H�
�  H嬘�P  婨腖峂華塆A�10  H�
�  H嬘�P  婨葖M燗塆(�罙婨8A塆0I塤8I兦H塎犻楚��E呬t6A亇   uA儅H�    H�    �  HE须H�    �  �H�    �	  �    H嬑�    H婱需    H媡$pH呟H媆$xA暻A嬊H伳�   A_A^A]A\]肳      a   H    {   �    �   �    �      �   H    �   N    �   �    �   �    �   N    +  �    :  �    J  �    ]  �    j  �    }  �    �  J    �  �    �  �      �      �    )  �    2  �    I  �    R  �    j  �    s  �    �  �    �  �    �  �    �  �    �     �  	   
          %  H    -  O    6  O       �   �  5 F            _  "   N           �chooseEGLConfig  >)   ctxconfig  AJ        (  AU  (     3L  �  � � � D�    >q   fbconfig  AK        � [   AU      �� ;  D�    >�   result  AH  �      AP        � e   D�   
 >t     i  A   
    ��   >t     usableCount  A       �) a �  B        O >q    closest  AI  �    �  >�    nativeConfigs  AH  �       AL  �     T6 � �  AL !    �u D  BX   �     p >t     wrongApiAvailable  Al  1     ,E  �   >�    usableConfigs  AH  �      AH �      BP   
    U >t     apiBit  An  9        9  {   >t     nativeCount  AH  �     D    B�   e     �  
 >.    n  AI  $    �� 0  AI !    �  �  M          乬 >t     value  D,    N M          丟 >t     value  D(    N M          �$ >t     value  D$    N M          倹 >t     value  DH    N M          倆 >t     value  DD    N M          俌 >t     value  D@    N M          �9 >t     value  D<    N M          � >t     value  D8    N M          侘 >t     value  D4    N M          佢 >t     value  D0    N& Z   o  o  G  G  X  o  I  I   �           (          B 
 h      �   )  Octxconfig  �   q  Ofbconfig  �   �  Oresult  �   t   OnativeCount  9�       !   9�       !   98         9[         9{         9�         9         90         9P         9q         9�         9�         O�   �          _  �  ;   �      [  �"   `  �(   b  �3   h  �H   j  �N   l  �T   n  �e   o  �g   �  �x   r  ��   s  ��   u  ��   v  ��   �  ��   y  ��   z  ��   |  �  }  �
    �!  �  �$  �  �G  �  �g  �  ��  �  ��    ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �   �  �9  �  �=  �  �@  �  �Y  �  �]  �  �a  �  �z  �  �~  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �
  �  �  �  �  �  �$  �  �1  �  �:  �  �N  �  �,   Q    0   Q   
 _   Q    c   Q   
 o   Q    s   Q   
 �   Q    �   Q   
 �   Q    �   Q   
 �   Q    �   Q   
 �   Q    �   Q   
 #  Q    '  Q   
 M  Q    Q  Q   
 e  Q    i  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q    �  Q   
   Q      Q   
 K  Q    O  Q   
 [  Q    _  Q   
 k  Q    o  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q    �  Q   
   Q    
  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q    �  Q   
 �  Q       Q   
   Q      Q   
   Q       Q   
 ,  Q    0  Q   
 <  Q    @  Q   
 L  Q    P  Q   
 \  Q    `  Q   
 l  Q    p  Q   
 |  Q    �  Q   
 �  Q    �  Q   
 @SH冹 �=(     H嬞u伖    tH媺�  H吷t�    H莾�      H嫇�  H呉tH�
�  ��  H莾�      H嫇�  H呉tH�
�  ��  H莾�      H兡 [�   �    .   F    L   �    R   �    p   �    v   �       �   �   7 F            �      �   &        �destroyContextEGL  >+   window  AI       w  AJ         
 Z   �                         B  0   +  Owindow  9P       2   9t       2   O   �   x           �   �     l       F �   I �!   L �-   N �2   O �=   S �I   U �V   V �a   Y �m   [ �z   \ ��   ^ �,   W    0   W   
 ^   W    b   W   
 n   W    r   W   
 �   W    �   W   
 �   W    �   W   
 �   W    �   W   
 @SH冹 H嬞篣0  H�
�  ��  H吚tH嬓H嬎�    3蓞�暳嬃H兡 [肏兡 [�   �       �    '   I       �   �   ; F            @      :   $        �extensionSupportedEGL  >@   extension  AI  	     6 0   AJ        	  >@    extensions  AH       %   
 Z   Z                         J  0   @  Oextension  9       :   O�   @           @   �     4       * �	   + �   , �    . �4   3 �,   U    0   U   
 e   U    i   U   
 y   U    }   U   
 �   U    �   U   
 �   U    �   U   
   U      U   
 伭 ��凒噹   Hc罤�    媽�    H�酘�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    肏�    �                                                                �      `    (   �    0   �    8   �    @   �    H   �    P   �    X   �    `   �    h   �    p   �    x   �    �   �    �   �    �   �    �   �    �       �   a    �   b    �   c    �   d    �   e    �   g    �   f    �   h    �   i    �   k    �   m    �   n    �   l    �   j    �   o       �   �  7 F            �       �           �getEGLErrorString  >t    error  A                                  B 
                    $LN18         $LN17         $LN16         $LN15         $LN14         $LN13         $LN12         $LN11         $LN10         $LN9         $LN8         $LN7         $LN6         $LN5         $LN4     t   Oerror  O �   (          �   �  "         '  �    (  �%   +  �,   K  �-   -  �4   K  �5   /  �<   K  �=   1  �D   K  �E   3  �L   K  �M   5  �T   K  �U   7  �\   K  �]   9  �d   K  �e   ;  �l   K  �m   =  �t   K  �u   ?  �|   K  �}   A  ��   K  ��   C  ��   K  ��   E  ��   K  ��   G  ��   K  ��   I  ��   K  �,   P    0   P   
 ]   P    a   P   
 �   `    �   `   
 �   o    �   o   
 �   n    �   n   
 �   m    �   m   
 �   l    �   l   
 �   k    �   k   
 �   j    �   j   
 �   i       i   
 
  h      h   
   g    "  g   
 /  f    3  f   
 ?  e    C  e   
 O  d    S  d   
 _  c    c  c   
 o  b    s  b   
   a    �  a   
 �  P    �  P   
 @SH冹 H嬞H�
�  �    H媹�  H吷t
H嬘�    H吚uH嬎H兡 [H�%�  H兡 [�   �       C    %   G    9   �       �   -  7 F            C      =   %        �getProcAddressEGL  >@   procname  AI  	     9 -   AJ        	  >+    window  AH         AH .      
 >2    proc  AH  )         AH .       Z   �  �                         B  0   @  Oprocname  96       �   O   �   `           C   �  	   T       6 �	   7 �   : �!   < �)   > �.   B �1   C �6   B �=   C �,   V    0   V   
 `   V    d   V   
 t   V    x   V   
 �   V    �   V   
 �   V    �   V   
 �   V    �   V   
 �   V    �   V   
 )  V    -  V   
 D  V    H  V   
 H冹(�=     u3夜  �    3繦兡(脕�  ` t3夜
  �    3繦兡(肏媮�  H兡(�   �       H    4   H       �   �   7 G            K      F   +        �glfwGetEGLContext  >A   handle  AJ        K   3   Z   o  o   (                      B  0   A  Ohandle  O   �   `           K   �  	   T       u �   w �   � �    y �,   { �8   | �:   � �?    �F   � �,   A    0   A   
 ^   A    b   A   
 �   A    �   A   
 H冹(�=     u3夜  �    3繦兡(肏��  H兡(�   �       H    #   �       �   g   7 G            ,      '   *        �glfwGetEGLDisplay 
 Z   o   (                      B  O �   @           ,   �     4       o �   p �   r �    q �'   r �,   @    0   @   
 |   @    �   @   
 H冹(�=     u3夜  �    3繦兡(脕�  ` t3夜
  �    3繦兡(肏媮�  H兡(�   �       H    4   H       �   �   7 G            K      F   ,        �glfwGetEGLSurface  >A   handle  AJ        K   3   Z   o  o   (                      B  0   A  Ohandle  O   �   `           K   �  	   T       � �   � �   � �    � �,   � �8   � �:   � �?   � �F   � �,   B    0   B   
 ^   B    b   B   
 �   B    �   B   
 @SH冹 H嬞H吷tHH嫅�  L媺�  L嬄H�
�  ��  吚ue�h  嬋�    L嬂H�    �  H兡 [�    H�
�  E3蒃3�3��  吚u&�h  嬋�    L嬂H�    �  H兡 [�    H嬘H�
�  H兡 [�    "   �    (   �    2   �    9   P    C      R   H    Y   �    g   �    q   �    x   P    �      �   H    �   �    �   D       �   	  ; F            �      �   !        �makeContextCurrentEGL  >+   window  AI  	     � H  �   AJ        	  Z     o    o  c                         B  0   +  Owindow  9&       5   90       &   9e       5   9o       &   O   �   �           �   �  
   t       �  �	   �  �   �  �0   �  �L   
 �Q   �  �V     �o    ��   
 ��    ��    ��   
 ��    �,   R    0   R   
 b   R    f   R   
 z   R    ~   R   
 �   R    �   R   
 �   R    �   R   
 �   R    �   R   
   R    	  R   
    R    $  R   
 @SH冹 H嬞H�
�  �    H;豻H�    �  H兡 [�    H嫇�  H�
�  H兡 [H�%�     �       C          ,   H    :   �    F   �       �   �   4 F            J      >   "        �swapBuffersEGL  >+   window  AI  	     : "   AJ        	  Z   �  o                         B  0   +  Owindow  9C       2   O  �   X           J   �     L        �	    �    �&   " �+    �0   ! �>   " �C   ! �,   S    0   S   
 [   S    _   S   
 o   S    s   S   
 �   S    �   S   
 �   S    �   S   
 嬔H�
�  H�%�     �       �       �   �   5 F                   	   #        �swapIntervalEGL  >t    interval  A         	                         B     t   Ointerval  9	       8   O �   (              �            % �    & �,   T    0   T   
 ^   T    b   T   
 �   T    �   T   
 �   T    �   T   
  B      ,           Z       Z       r     B      K           [       [       x     B      K           \       \       ~    d dt
 ]d �0    @      X        �          ]       ]       �     B      B           ^       ^       �    %	 " �	��p`0P            X        +          _       _       �    �
 莇 �4 "�����P    �           Q       Q       �    ! t
     �          Q       Q       �    �   �          Q       Q       �    !       �          Q       Q       �    �  �          Q       Q       �    !   t
     �          Q       Q       �    �  �          Q       Q       �    !       �          Q       Q       �    �  _          Q       Q       �     20    �           R       R       �     20    J           S       S       �     20    @           U       U       �     20    C           V       V       �     20    �           W       W       �    Success EGL is not or could not be initialized EGL cannot access a requested resource EGL failed to allocate resources for the requested operation An unrecognized attribute or attribute value was passed in the attribute list An EGLContext argument does not name a valid EGL rendering context An EGLConfig argument does not name a valid EGL frame buffer configuration The current surface of the calling thread is a window, pixel buffer or pixmap that is no longer valid An EGLDisplay argument does not name a valid EGL display connection An EGLSurface argument does not name a valid surface configured for GL rendering Arguments are inconsistent One or more argument values are invalid A NativePixmapType argument does not refer to a valid native pixmap A NativeWindowType argument does not refer to a valid native window The application must destroy all contexts and reinitialise ERROR: UNKNOWN EGL ERROR EGL: Stereo rendering not supported EGL: No EGLConfigs returned EGL: Failed to find support for OpenGL ES 1.x EGL: Failed to find support for OpenGL ES 2 or later EGL: Failed to find support for OpenGL EGL: Failed to find a suitable EGLConfig EGL: Failed to make context current: %s EGL: Failed to clear current context: %s EGL: The context must be current on the calling thread when swapping buffers libEGL.dll EGL.dll EGL: Library not found lib eglGetConfigAttrib eglGetConfigs eglGetDisplay eglGetError eglInitialize eglTerminate eglBindAPI eglCreateContext eglDestroySurface eglDestroyContext eglCreateWindowSurface eglMakeCurrent eglSwapBuffers eglSwapInterval eglQueryString eglGetProcAddress EGL: Failed to load required entry points EGL_EXT_platform_base EGL_EXT_platform_x11 EGL_EXT_platform_wayland EGL_ANGLE_platform_angle EGL_ANGLE_platform_angle_opengl EGL_ANGLE_platform_angle_d3d EGL_ANGLE_platform_angle_vulkan EGL_ANGLE_platform_angle_metal eglGetPlatformDisplayEXT eglCreatePlatformWindowSurfaceEXT EGL: Failed to get EGL display: %s EGL: Failed to initialize EGL: %s EGL_KHR_create_context EGL_KHR_create_context_no_error EGL_KHR_gl_colorspace EGL_KHR_get_all_proc_addresses EGL_KHR_context_flush_control EGL_EXT_present_opaque EGL: API not available EGL: Failed to bind OpenGL ES: %s EGL: Failed to bind OpenGL: %s EGL: Failed to create context: %s EGL: Failed to create window surface: %s GLESv1_CM.dll libGLES_CM.dll GLESv2.dll libGLESv2.dll EGL: Failed to load client library    v ��3.]H�\�)r�   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\Release\glfw3.pdb �6j��鈻L玝p妇�=e�鲤B,嵩`烫C鴵M眙dk樏�壖y>�:E螼m義粘�#c俥	谏)�6H2SA鍄��4H佘铑頕ぞ濊m(姳�伮蚨?婔	0j鷒Q7囪叐]��+�鞐?梨ug6鴂膭kり验� �#�宻C�4盖�楆磓翅a�
P)рR墼掑袐2忺x2]F糊倱s0工閐Y瑸莦彏张珳鷎~邡 -坓�(鬄鮸睢�墒�-坓�(鬄�nk�#氷-坓�(鬄�nk�#氷:�}鐿�5椣隕=-坓�(鬄酲;[純o薥惂槎馯*冧騬$�糯G啭顶苢~u�豨S(鉤�%槭桬
雰焠苤茁�2髁'咶X=Bn@\eo僤\gQ厓焠苤茁编P嵉�=雵J-WV8o�.w⒇衞雵J-WV8o硋傘]-屾雵J-WV8o+$�"钞d碗嘕-WV8o儧j� 頿囯嘕-WV8o� 蛮l�(詵啷鈉p蓱�(+叨ǔ《b垎u�eXx茔颻醐扶R�<d%薷q\��胐�奍E㈡蘸��$Z玃i�閱�)&鐐\倡挳啚蕮��3佩真:疭垱偪Lrl効鞝;�.zepUP坧'廇.嗟
yk�0�K窀}J�-趽檿�>花廎;敃荚�& 襠簠A騱怦a蓳T袕�謪艉Jl�1�蛽馘秣飈W冻x銊┳墦f坿C�.}��5�4l<丝A蹺糄I^e|�,片蟰!苊5⒖∫峃9靪ト��a>��;�!'酞!*H2现PLlw4I/w%懟�Ru蝏V盨4l恼K6厜��%K橃嶒脊tO�8��嵕'-Md稡V{妱榎O協`�顃��婺p牫卦hy*蠂鰞1g5�*礁!X咱嘙;箒r駇儖* 鏩`<礝	wtRHjs7�a鎀驟H寎徔藄轒R蓲e碾:蘪窹�/8
�'�[WGc 誙.aA�\/K�+麜�.>�a�榬J蚜㈱SY:zh`�>=�<鮸k�)q题6 
丳J�?$�9Cg{[骄�'J塿c��4�	r%�污r(桔UW9訑�)���        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       ]                 .debug$S       鋪               .text$mn       +  7   >Vq_     .debug$S          L           .text$mn       �  �   襌C=     .debug$S       �  V           .text$mn       B      莼X     .debug$S       D             .text$mn    	   _  &   ㄑf1     .debug$S    
   �  J       	    .text$mn       �      岋8     .debug$S       \             .text$mn    
   @      $�     .debug$S       H         
    .text$mn       �   !   ?獷s     .debug$S       �  &           .text$mn       C      R靻�     .debug$S       �             .text$mn       K      嵚弯     .debug$S                     .text$mn       ,      跪厮     .debug$S       �              .text$mn       K      昤_     .debug$S                     .text$mn       �      �>觖     .debug$S       �             .text$mn       J      葩因     .debug$S       0  
           .text$mn             �'鈕     .debug$S       �              strncmp                                            (               :                N                b                z                �                �                �                �                �               �                             %               2               =              O      	        _              u              �              �      
        �              �              �               �           $LN5            $LN6            $LN6            $LN95           $LN6            $LN102          $LN21   �       $LN4    %       $LN5    -       $LN6    5       $LN7    =       $LN8    E       $LN9    M       $LN10   U       $LN11   ]       $LN12   e       $LN13   m       $LN14   u       $LN15   }       $LN16   �       $LN17   �       $LN18   �       .xdata                �9�        �          .pdata                w佼                   .xdata      !          �9�        *      !    .pdata      "         晦鱰        D      "    .xdata      #          �9�        ]      #    .pdata      $         晦鱰        w      $    .xdata      %         S.�        �      %    .pdata      &         舗秷        �      &    .xdata      '          �9�        �      '    .pdata      (         惻竗        �      (    .xdata      )          蚢Xd        �      )    .pdata      *         $�        
      *    .xdata      +          7�	        '      +    .pdata      ,         xR	-	        ?      ,    .xdata      -         幫O(	        V      -    .pdata      .         谂�	        o      .    .xdata      /         敻^�	        �      /    .pdata      0         褞稪	        �      0    .xdata      1         "潨l	        �      1    .pdata      2         ﹑倉	        �      2    .xdata      3         敻^�	        �      3    .pdata      4         4v�'	              4    .xdata      5          （亵              5    .pdata      6         �        <      6    .xdata      7          （亵        Y      7    .pdata      8         %轢�        p      8    .xdata      9          （亵
        �      9    .pdata      :         砺�)
        �      :    .xdata      ;          （亵        �      ;    .pdata      <         ��        �      <    .xdata      =          （亵        �      =    .pdata      >         寵Q              >    _glfw            .rdata      ?          蛝Vk         '      ?    .rdata      @   '       u         A      @    .rdata      A   '       }�#�         {      A    .rdata      B   =       髑N         �      B    .rdata      C   N       s8,�         �      C    .rdata      D   C       #
�         $      D    .rdata      E   K       9m�         \      E    .rdata      F   f       糄�         �      F    .rdata      G   D       #N弇         �      G    .rdata      H   Q       ~+               H    .rdata      I          �&
         >      I    .rdata      J   (       黫n�         o      J    .rdata      K   D       �5         �      K    .rdata      L   D       脃渚         �      L    .rdata      M   ;       澐俆               M    .rdata      N          :俙�         N      N    .rdata      O   $       {旄               O    .rdata      P          蟩`�         �      P    .rdata      Q   .       疘�         �      Q    .rdata      R   5       r~         &	      R    .rdata      S   '       悁�3         `	      S    .rdata      T   )       y萂         �	      T    .rdata      U   (       懎庂         �	      U    .rdata      V   )       �;鍬         
      V    .rdata      W   M       *3H�         I
      W    .rdata      X          �(疫         �
      X    .rdata      Y          藩{�         �
      Y    .rdata      Z          6`廥         �
      Z    .rdata      [          1�=a         �
      [    .rdata      \          敎卞               \    .rdata      ]          �惵         )      ]    .rdata      ^          �<�2         J      ^    .rdata      _          z�){         k      _    .rdata      `          �"裫         �      `    .rdata      a   
       掗講         �      a    .rdata      b          f翚�         �      b    .rdata      c          w偌�         �      c    .rdata      d          鞓               d    .rdata      e          k�         1      e    .rdata      f          崐漀         W      f    .rdata      g          �菡         �      g    .rdata      h          �2         �      h    .rdata      i          焈�         �      i    .rdata      j          籛圦         �      j    .rdata      k          E�?�         
      k    .rdata      l   *       s鴌)         2
      l    .rdata      m          澱C�         l
      m    .rdata      n          砯q�         �
      n    .rdata      o          棘镁         �
      o    .rdata      p          j��         �
      p    .rdata      q           徨�>               q    .rdata      r          %�         M      r    .rdata      s           <!{�         ~      s    .rdata      t          a SJ         �      t    .rdata      u          �8�         �      u    .rdata      v   "       �筯               v    .rdata      w   #       lU3�         F      w    .rdata      x   "       嬷f�         �      x    .rdata      y          F^J�         �      y    .rdata      z           鼸�         �      z    .rdata      {          ~柢9               {    .rdata      |          �惖         D      |    .rdata      }          �縷         w      }    .rdata      ~          窋d         �      ~    .rdata                A褈         �          .rdata      �   "       =�               �    .rdata      �          �+a         >      �    .rdata      �   "       p�         {      �    .rdata      �   )       闐腣         �      �    .rdata      �          _褉         �      �    .rdata      �          欑�               �    .rdata      �          F齝�         5      �    .rdata      �          c罝[         T      �    .rdata      �   #       �Ge         v      �        �               �           .debug$T    �   |                 .chks64     �   P                �  glfwGetEGLDisplay glfwGetEGLContext glfwGetEGLSurface _glfwPlatformGetTls _glfwPlatformSetTls _glfwPlatformLoadModule _glfwPlatformFreeModule _glfwPlatformGetModuleSymbol _glfwInputError _glfwStringInExtensionString _glfwChooseFBConfig _glfwInitEGL _glfwTerminateEGL _glfwCreateContextEGL _glfw_calloc _glfw_free getEGLErrorString chooseEGLConfig makeContextCurrentEGL swapBuffersEGL swapIntervalEGL extensionSupportedEGL getProcAddressEGL destroyContextEGL __GSHandlerCheck __security_check_cookie $unwind$glfwGetEGLDisplay $pdata$glfwGetEGLDisplay $unwind$glfwGetEGLContext $pdata$glfwGetEGLContext $unwind$glfwGetEGLSurface $pdata$glfwGetEGLSurface $unwind$_glfwInitEGL $pdata$_glfwInitEGL $unwind$_glfwTerminateEGL $pdata$_glfwTerminateEGL $unwind$_glfwCreateContextEGL $pdata$_glfwCreateContextEGL $unwind$chooseEGLConfig $pdata$chooseEGLConfig $chain$2$chooseEGLConfig $pdata$2$chooseEGLConfig $chain$3$chooseEGLConfig $pdata$3$chooseEGLConfig $chain$4$chooseEGLConfig $pdata$4$chooseEGLConfig $chain$5$chooseEGLConfig $pdata$5$chooseEGLConfig $unwind$makeContextCurrentEGL $pdata$makeContextCurrentEGL $unwind$swapBuffersEGL $pdata$swapBuffersEGL $unwind$extensionSupportedEGL $pdata$extensionSupportedEGL $unwind$getProcAddressEGL $pdata$getProcAddressEGL $unwind$destroyContextEGL $pdata$destroyContextEGL ??_C@_07PBILKAFL@Success@ ??_C@_0CH@KLCPDMM@EGL?5is?5not?5or?5could?5not?5be?5init@ ??_C@_0CH@MMCOKIKC@EGL?5cannot?5access?5a?5requested?5r@ ??_C@_0DN@GNDEENDO@EGL?5failed?5to?5allocate?5resource@ ??_C@_0EO@EKFIJHGG@An?5unrecognized?5attribute?5or?5at@ ??_C@_0ED@GAJEHCGA@An?5EGLContext?5argument?5does?5not@ ??_C@_0EL@BBAGGFOK@An?5EGLConfig?5argument?5does?5not?5@ ??_C@_0GG@ODICGIHF@The?5current?5surface?5of?5the?5call@ ??_C@_0EE@ICKHNPHB@An?5EGLDisplay?5argument?5does?5not@ ??_C@_0FB@EPGEGHDL@An?5EGLSurface?5argument?5does?5not@ ??_C@_0BL@OIILILNJ@Arguments?5are?5inconsistent@ ??_C@_0CI@KAHNKLLC@One?5or?5more?5argument?5values?5are@ ??_C@_0EE@NKCAIGPH@A?5NativePixmapType?5argument?5doe@ ??_C@_0EE@FBMMOIJB@A?5NativeWindowType?5argument?5doe@ ??_C@_0DL@GNLICFMC@The?5application?5must?5destroy?5al@ ??_C@_0BJ@KDFAHDEK@ERROR?3?5UNKNOWN?5EGL?5ERROR@ ??_C@_0CE@CNKFDGDO@EGL?3?5Stereo?5rendering?5not?5suppo@ ??_C@_0BM@KHOPPJNJ@EGL?3?5No?5EGLConfigs?5returned@ ??_C@_0CO@HAIMLOLL@EGL?3?5Failed?5to?5find?5support?5for@ ??_C@_0DF@BPPFPGID@EGL?3?5Failed?5to?5find?5support?5for@ ??_C@_0CH@EMJGILEP@EGL?3?5Failed?5to?5find?5support?5for@ ??_C@_0CJ@JPDPLHCM@EGL?3?5Failed?5to?5find?5a?5suitable?5@ ??_C@_0CI@MPJNGPNP@EGL?3?5Failed?5to?5make?5context?5cur@ ??_C@_0CJ@NBJEEEJD@EGL?3?5Failed?5to?5clear?5current?5co@ ??_C@_0EN@CIIGDABJ@EGL?3?5The?5context?5must?5be?5curren@ ??_C@_0L@ELKKGGMD@libEGL?4dll@ ??_C@_07EAKGIKCB@EGL?4dll@ ??_C@_0BH@NFNALLIN@EGL?3?5Library?5not?5found@ ??_C@_03LPIGJBNC@lib@ ??_C@_0BD@MAEGKKAE@eglGetConfigAttrib@ ??_C@_0O@OMNEJBKO@eglGetConfigs@ ??_C@_0O@BMJFLKOA@eglGetDisplay@ ??_C@_0M@PPADKGOK@eglGetError@ ??_C@_0O@EEJFKEKH@eglInitialize@ ??_C@_0N@IGFNFAOP@eglTerminate@ ??_C@_0L@HJOCIPHF@eglBindAPI@ ??_C@_0BB@KEKMNHDF@eglCreateContext@ ??_C@_0BC@ICHKG@eglDestroySurface@ ??_C@_0BC@BNOBCLNJ@eglDestroyContext@ ??_C@_0BH@MDMCFBDG@eglCreateWindowSurface@ ??_C@_0P@PNPBPNLI@eglMakeCurrent@ ??_C@_0P@DEBOOHKH@eglSwapBuffers@ ??_C@_0BA@BGJPOLDF@eglSwapInterval@ ??_C@_0P@HJKEKLKD@eglQueryString@ ??_C@_0BC@HONLJDPH@eglGetProcAddress@ ??_C@_0CK@DCPJCPHH@EGL?3?5Failed?5to?5load?5required?5en@ ??_C@_0BG@IPJFJLIC@EGL_EXT_platform_base@ ??_C@_0BF@FMIKFMMH@EGL_EXT_platform_x11@ ??_C@_0BJ@KIPDFNMM@EGL_EXT_platform_wayland@ ??_C@_0BJ@MBCLFEBK@EGL_ANGLE_platform_angle@ ??_C@_0CA@NIHAEPLD@EGL_ANGLE_platform_angle_opengl@ ??_C@_0BN@OHLJPDPM@EGL_ANGLE_platform_angle_d3d@ ??_C@_0CA@HGIOILGO@EGL_ANGLE_platform_angle_vulkan@ ??_C@_0BP@NMKFFDCO@EGL_ANGLE_platform_angle_metal@ ??_C@_0BJ@KEAIGPHD@eglGetPlatformDisplayEXT@ ??_C@_0CC@NGDHIGBI@eglCreatePlatformWindowSurfaceE@ ??_C@_0CD@CODEPNKE@EGL?3?5Failed?5to?5get?5EGL?5display?3@ ??_C@_0CC@DOIEFDD@EGL?3?5Failed?5to?5initialize?5EGL?3?5@ ??_C@_0BH@GIBFIFPN@EGL_KHR_create_context@ ??_C@_0CA@OJFEOPKP@EGL_KHR_create_context_no_error@ ??_C@_0BG@GOAKKGGB@EGL_KHR_gl_colorspace@ ??_C@_0BP@CDGGFNNF@EGL_KHR_get_all_proc_addresses@ ??_C@_0BO@IDEEMGNC@EGL_KHR_context_flush_control@ ??_C@_0BH@JADLEPAM@EGL_EXT_present_opaque@ ??_C@_0BH@PDIOJKKP@EGL?3?5API?5not?5available@ ??_C@_0CC@NIBJMOI@EGL?3?5Failed?5to?5bind?5OpenGL?5ES?3?5@ ??_C@_0BP@PHOHHIJJ@EGL?3?5Failed?5to?5bind?5OpenGL?3?5?$CFs@ ??_C@_0CC@GOIKODMM@EGL?3?5Failed?5to?5create?5context?3?5@ ??_C@_0CJ@MHLGDLLP@EGL?3?5Failed?5to?5create?5window?5su@ ??_C@_0O@KOJFNJDA@GLESv1_CM?4dll@ ??_C@_0P@MADFBLIB@libGLES_CM?4dll@ ??_C@_0L@CEBLLDFF@GLESv2?4dll@ ??_C@_0O@HFAAEHFL@libGLESv2?4dll@ ??_C@_0CD@JDEAKJCI@EGL?3?5Failed?5to?5load?5client?5libr@ __ImageBase __security_cookie 