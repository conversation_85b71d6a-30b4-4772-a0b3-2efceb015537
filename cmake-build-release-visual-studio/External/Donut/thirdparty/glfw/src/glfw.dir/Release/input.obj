d啣h馟h儁 �      .drectve        ]   躨               
 .debug$S        l�  9j  レ         @ B.data           h  挽  5�      m  @ P�.rdata          b   w             @P@.rdata            �             @P@.rdata          �   �             @P@.rdata             �             @P@.rdata          $  �	             @P@.rdata          %  �
             @P@.rdata          �   �             @P@.rdata          �   �             @P@.rdata          �   �
             @P@.rdata          �   P             @P@.rdata                          @P@.rdata          �   7             @P@.rdata          �   �             @P@.rdata          $  �             @P@.rdata          &  �             @P@.rdata          &  �             @P@.rdata            �             @P@.rdata                         @P@.rdata          #  5             @P@.rdata          �   X             @P@.rdata          !  &             @P@.rdata            G             @P@.rdata          %  c             @P@.rdata          �   �             @P@.rdata          �   "             @P@.rdata          �   �             @P@.rdata          �   f             @P@.rdata          �   �             @P@.rdata          !  �             @P@.rdata          !  �              @P@.rdata          �   �!             @P@.rdata          �   �"             @P@.rdata          %  ^#             @P@.rdata          %  �$             @P@.rdata          2  �%             @P@.rdata          &  �&             @P@.rdata          &   (             @P@.rdata          $  &)             @P@.rdata          �   J*             @P@.rdata          �   �*             @P@.rdata          �   �+             @P@.rdata          �   },             @P@.rdata          �   -             @P@.rdata            �-             @P@.rdata          '  �.             @P@.rdata            �/             @P@.rdata            	1             @P@.rdata          -  '2             @P@.rdata          -  T3             @P@.rdata          -  �4             @P@.rdata          -  �5             @P@.rdata          -  �6             @P@.rdata          -  8             @P@.rdata          -  59             @P@.rdata          *  b:             @P@.rdata          3  �;             @P@.rdata            �<             @P@.rdata            �=             @P@.rdata            �>             @P@.rdata          �   @             @P@.rdata          1  燖             @P@.rdata          %  袮             @P@.rdata            鯞             @P@.rdata          (  D             @P@.rdata            4E             @P@.rdata            OF             @P@.rdata            jG             @P@.rdata            匟             @P@.rdata            營             @P@.rdata            籎             @P@.rdata            覭             @P@.rdata          *  隠             @P@.rdata          *  N             @P@.rdata            ?O             @P@.rdata            ^P             @P@.rdata          %  yQ             @P@.rdata            濺             @P@.rdata            礢             @P@.rdata          ,  蟃             @P@.rdata          >  鸘             @P@.rdata          4  9W             @P@.rdata          7  mX             @P@.rdata          �                @P@.rdata          "  沍             @P@.rdata          !  絒             @P@.rdata          �   轡             @P@.rdata            砞             @P@.rdata          &  穅             @P@.rdata            輄             @P@.rdata          -  骮             @P@.rdata             b             @P@.rdata            9c             @P@.rdata            Rd             @P@.rdata            le             @P@.rdata            {f             @P@.rdata          O  }g             @P@.rdata          E  蘦             @P@.rdata            j             @P@.rdata          0  "k             @P@.rdata          )  Rl             @P@.rdata          �   {m             @P@.rdata          �   bn             @P@.rdata          �   Io             @P@.rdata          �   ,p             @P@.rdata          !  q             @P@.rdata            -r             @P@.rdata          �   Is             @P@.rdata          �   *t             @P@.rdata          $  u             @P@.rdata          �   ;v             @P@.rdata            鮲             @P@.rdata            	x             @P@.rdata            y             @P@.rdata          0  8z             @P@.rdata          �   h{             @P@.rdata          �   N|             @P@.rdata            4}             @P@.rdata          !  E~             @P@.rdata          %  f             @P@.rdata          &  媭             @P@.rdata            眮             @P@.rdata            蛡             @P@.rdata          !  閮             @P@.rdata          !  
�             @P@.rdata          !  +�             @P@.rdata          �   L�             @P@.rdata          !  �             @P@.rdata          0  5�             @P@.rdata          �   e�             @P@.rdata          �   5�             @P@.rdata            �             @P@.rdata            :�             @P@.rdata            O�             @P@.rdata            f�             @P@.rdata          �   }�             @P@.rdata            i�             @P@.rdata          %  垝             @P@.rdata            瓝             @P@.rdata          �   虜             @P@.rdata          �   禃             @P@.rdata          !  瑬             @P@.rdata            蜅             @P@.rdata          $  闃             @P@.rdata          $  
�             @P@.rdata          "  1�             @P@.rdata          "  S�             @P@.rdata          �   u�             @P@.rdata          �   D�             @P@.rdata          #  ��             @P@.rdata          $  "�             @P@.rdata          �   F�             @P@.rdata          �   �             @P@.rdata          �   擘             @P@.rdata            啵             @P@.rdata            颏             @P@.rdata          <  �             @P@.rdata          +  H�             @P@.rdata            s�             @P@.rdata          '  啯             @P@.rdata          �                @P@.rdata          �                @P@.rdata          �   f�             @P@.rdata          �   *�             @P@.rdata          �   瞽             @P@.rdata            伯             @P@.rdata            席             @P@.rdata          )  獍             @P@.rdata            �             @P@.rdata            $�             @P@.rdata            B�             @P@.rdata          0  a�             @P@.rdata            懚             @P@.rdata            Ψ             @P@.rdata             鸥             @P@.rdata             骞             @P@.rdata          �   �             @P@.rdata          �   慊             @P@.rdata          '  饧             @P@.rdata          0  	�             @P@.rdata          0  9�             @P@.rdata          4  i�             @P@.rdata          4  澚             @P@.rdata          1  崖             @P@.rdata          1  �             @P@.rdata          -  3�             @P@.rdata            `�             @P@.rdata            s�             @P@.rdata            喨             @P@.rdata            犐             @P@.rdata            皇             @P@.rdata            嗡             @P@.rdata            硖             @P@.rdata          
  
�             @P@.rdata          �   �             @P@.rdata          �   韵             @P@.rdata          6  菩             @P@.rdata                         @P@.rdata          &  �             @P@.rdata          &  8�             @P@.rdata          �   ^�             @P@.rdata            V�             @P@.rdata          =  i�             @P@.rdata          �   ω             @P@.rdata          /  Q�             @P@.rdata          &  ��             @P@.rdata          &               @P@.rdata          &  誊             @P@.rdata          &  蜉             @P@.rdata          .  �             @P@.rdata          !  F�             @P@.rdata          #  g�             @P@.rdata          �   娾             @P@.rdata          �   �             @P@.rdata          �   �             @P@.rdata            掬             @P@.rdata          �   礤             @P@.rdata          3               @P@.rdata          !  噻             @P@.rdata          .  �             @P@.rdata          �   /�             @P@.rdata          )  '�             @P@.rdata          2  P�             @P@.rdata          '  傢             @P@.rdata          $  ╊             @P@.rdata            惋             @P@.rdata          �   摒             @P@.rdata          �   礼             @P@.rdata                         @P@.rdata          �   麦             @P@.rdata          �                @P@.rdata          "  �             @P@.rdata          -  ■             @P@.rdata          /  西             @P@.rdata          /               @P@.rdata            ,�             @P@.rdata          "  F�             @P@.rdata            h�             @P@.rdata            価             @P@.rdata            燓             @P@.rdata            �             @P@.rdata            �              @P@.rdata          #  �             @P@.rdata            	             @P@.rdata            "             @P@.rdata          %  ;             @P@.rdata          #  `             @P@.rdata          #  �             @P@.rdata          #  �             @P@.rdata            �	             @P@.rdata          �   �
             @P@.rdata          #  �             @P@.rdata          #  �             @P@.rdata          #               @P@.rdata          -  :             @P@.rdata          �   g             @P@.rdata          �   %             @P@.rdata          �                @P@.rdata          �                @P@.rdata          �   �             @P@.rdata          �   �             @P@.rdata          <  �             @P@.rdata          =  �             @P@.rdata                         @P@.rdata          )               @P@.rdata          )  F             @P@.rdata            o             @P@.rdata            �             @P@.rdata          1  �             @P@.rdata            �             @P@.rdata             �             @P@.rdata          &  �              @P@.rdata          &  "             @P@.rdata          &  8#             @P@.rdata          &  ^$             @P@.rdata            �%             @P@.rdata          
  �&             @P@.rdata          '  �'             @P@.rdata          '  �(             @P@.rdata          �   �)             @P@.rdata          �   �*             @P@.rdata          �   {+             @P@.rdata          �   ,             @P@.rdata          .  �,             @P@.rdata          5  �-             @P@.rdata          0  '/             @P@.rdata          .  W0             @P@.rdata          .  �1             @P@.rdata          .  �2             @P@.rdata            �3             @P@.rdata             5             @P@.rdata          /  6             @P@.rdata          !  G7             @P@.rdata          �   h8             @P@.rdata          (  &9             @P@.rdata          !  N:             @P@.rdata          !  o;             @P@.rdata             �<             @P@.rdata          &  �=             @P@.rdata          "  �>             @P@.rdata          "  �?             @P@.rdata            A             @P@.rdata          x   7B             @P@.rdata          �   疊             @P@.rdata          �   dC             @P@.rdata          �   D             @P@.rdata          �   篋             @P@.rdata            yE             @P@.rdata            嶧             @P@.rdata            燝             @P@.rdata            綡             @P@.rdata          &  豂             @P@.rdata          �   﨡             @P@.rdata          �   霮             @P@.rdata          $  蚅             @P@.rdata          (  馦             @P@.rdata            O             @P@.rdata          �   /P             @P@.rdata            'Q             @P@.rdata          *  BR             @P@.rdata          $  lS             @P@.rdata          �   怲             @P@.rdata            ~U             @P@.rdata            廣             @P@.rdata          %  璚             @P@.rdata          +  襒             @P@.rdata          6  齓             @P@.rdata          6  3[             @P@.rdata          .  i\             @P@.rdata          /  梋             @P@.rdata          .  芧             @P@.rdata          "  鬫             @P@.rdata          "  a             @P@.rdata          �   8b             @P@.rdata            郻             @P@.rdata            鴆             @P@.rdata            e             @P@.rdata          '  *f             @P@.rdata             Qg             @P@.rdata            qh             @P@.rdata             媔             @P@.rdata            玧             @P@.rdata             羕             @P@.rdata            醠             @P@.rdata          &  鱩             @P@.rdata            o             @P@.rdata          �   7p             @P@.rdata          �   q             @P@.rdata          �   r             @P@.rdata            s             @P@.rdata          #  *t             @P@.rdata          )  Mu             @P@.rdata            vv             @P@.rdata          "  弚             @P@.rdata          '  眡             @P@.rdata          -  貀             @P@.rdata          $  {             @P@.rdata          #  )|             @P@.rdata          .  L}             @P@.rdata            z~             @P@.rdata            �             @P@.rdata          #  磤             @P@.rdata          #  讈             @P@.rdata             鷤             @P@.rdata            �             @P@.rdata            7�             @P@.text$mn        �  V� ?�     	    P`.debug$S        �  檲 q�        @B.text$mn        L   潔 閸         P`.debug$S        �  � 晱        @B.text$mn        9   5� n�         P`.debug$S          枑 獞        @B.text$mn        u   鷳 o�         P`.debug$S          祾 綋        @B.text$mn        n   
�              P`.debug$S          {� 嚃        @B.text$mn           嫍              P`.debug$S        ,  洍 菢     
   @B.text$mn        @   +�              P`.debug$S        \  k� 菤        @B.text$mn           ?�              P`.debug$S        T  O�         @B.text$mn        _   � z�         P`.debug$S        t  帩 �        @B.text$mn        
   師              P`.debug$S        8  洘 訝     
   @B.text$mn           7�              P`.debug$S        @  C� 儮     
   @B.text$mn        n   纰              P`.debug$S        �  U� 椁        @B.text$mn        �   墺              P`.debug$S        d  
� n�        @B.text$mn        J   ^�              P`.debug$S           ī ǐ        @B.text$mn           槵              P`.debug$S        \  ì �        @B.text$mn        �   |� �         P`.debug$S        d  � ~�        @B.text$mn        �   � 鼙         P`.debug$S        �  姹 毘     
   @B.text$mn        �    蚀     	    P`.debug$S          $� @�        @B.text$mn        �   �      	    P`.debug$S        �  �         @B.text$mn        �   "� 榛     
    P`.debug$S          M� Q�        @B.text$mn        +   窬 �         P`.debug$S          :� F�        @B.text$mn        Y   柪 锢         P`.debug$S        �  
� 谅        @B.text$mn        �   9� 呙     
    P`.debug$S        �  C� ?�        @B.text$mn        	  � 	�         P`.debug$S        t   m�     $   @B.text$mn        �   遮 m�         P`.debug$S        �  曐 =�     
   @B.text$mn        �   ≥ m�     
    P`.debug$S        \  艳 -�        @B.text$mn        �   � �         P`.debug$S        �  ~� �        @B.text$mn        �   鲦 ｇ     
    P`.debug$S        �  �         @B.text$mn        �   藐 忞     
    P`.debug$S        \  箅 O�        @B.text$mn        �   ?� 轱     
    P`.debug$S        �  M� A�        @B.text$mn        E   	� N�         P`.debug$S          l� t�        @B.text$mn        r   棒 "�         P`.debug$S        p  J� 忽     
   @B.text$mn        z   � 橑         P`.debug$S        �  憎 慁        @B.text$mn        S   0� 凓         P`.debug$S        $  碟 冫        @B.text$mn        m   )� 桙         P`.debug$S        |  军 :�     
   @B.text$mn        �   烚 =�         P`.debug$S        �   o� ?         @B.text$mn        )   g  �          P`.debug$S        �   �  j        @B.text$mn        )   � �         P`.debug$S        �   � �        @B.text$mn        �   � d     
    P`.debug$S        �  � �        @B.text$mn        �   � $     
    P`.debug$S        �  � t	        @B.text$mn        +   (
 S
         P`.debug$S        �   q
 A        @B.text$mn        3   } �         P`.debug$S        L  � 
     
   @B.text$mn        3   t
 �
         P`.debug$S        P  �
      
   @B.text$mn           o �         P`.debug$S        <  � �     
   @B.text$mn            L l         P`.debug$S        L  � �     
   @B.text$mn        3   : m         P`.debug$S        T  � �     
   @B.text$mn        �   9 4         P`.debug$S        �  � |        @B.text$mn        3   0 c         P`.debug$S        P  w �     
   @B.text$mn        3   + ^         P`.debug$S        L  r �     
   @B.text$mn        6  " X         P`.debug$S        �  � �#        @B.text$mn        e   �$ E%         P`.debug$S        �  �% 1'        @B.text$mn        6   �' �'         P`.debug$S        <  ( M)        @B.text$mn        3   �) �)         P`.debug$S        L  �) 0+     
   @B.text$mn        3   �+ �+         P`.debug$S        T  �+ /-     
   @B.text$mn        3   �- �-         P`.debug$S        P  �- */     
   @B.text$mn        �   �/ z0     	    P`.debug$S        ,  �0  2        @B.text$mn        �  P2 �5         P`.debug$S        d  �6 :        @B.text$mn        �  ; 錌     )    P`.debug$S        L  D 薑     6   @B.xdata             鏜             @0@.pdata             颩 鸐        @0@.xdata             N             @0@.pdata             %N 1N        @0@.xdata             ON             @0@.pdata             WN cN        @0@.xdata             丯             @0@.pdata             塏 昇        @0@.xdata             砃             @0@.pdata             籒 荖        @0@.xdata             錘             @0@.pdata             鞱 鵑        @0@.xdata             O             @0@.pdata             O +O        @0@.xdata             IO             @0@.pdata             YO eO        @0@.xdata             僌             @0@.pdata             揙 烵        @0@.xdata             絆 袿        @0@.pdata             颫 鸒        @0@.xdata             P -P        @0@.pdata             KP WP        @0@.xdata             uP 匬        @0@.pdata              疨        @0@.xdata             蚉             @0@.pdata             誔 酨        @0@.xdata             �P Q        @0@.pdata             1Q =Q        @0@.xdata             [Q oQ        @0@.pdata             峇 橯        @0@.xdata             稱             @0@.pdata             縌 薗        @0@.xdata             镼 齉        @0@.pdata             R 'R        @0@.xdata             ER YR        @0@.pdata             wR 僐        @0@.xdata              盧        @0@.pdata             蟁 跼        @0@.xdata             鵕 	S        @0@.pdata             'S 3S        @0@.xdata             QS             @0@.pdata             YS eS        @0@.xdata             僑             @0@.pdata             婼 桽        @0@.xdata             礢             @0@.pdata             絊 蒘        @0@.xdata             鏢             @0@.pdata             颯 鸖        @0@.xdata             T             @0@.pdata             !T -T        @0@.xdata             KT             @0@.pdata             ST _T        @0@.xdata             }T             @0@.pdata             匱 慣        @0@.xdata             疶             @0@.pdata             稵 肨        @0@.xdata             酺             @0@.pdata             門 鮐        @0@.xdata             U             @0@.pdata             U +U        @0@.xdata             IU             @0@.pdata             UU aU        @0@.xdata             U             @0@.pdata             婾 桿        @0@.xdata             礥             @0@.pdata             経 蒛        @0@.xdata             鏤             @0@.pdata             颱 鸘        @0@.xdata             V             @0@.pdata             !V -V        @0@.xdata             KV             @0@.pdata             SV _V        @0@.xdata             }V             @0@.pdata             匳 慥        @0@.xdata             疺 荲        @0@.pdata             裋 軻        @0@.xdata             鸙 W        @0@.pdata             -W 9W        @0@.xdata             WW kW        @0@.pdata             塛 昗        @0@.xdata             砏 肳        @0@.pdata             醀 鞼        @0@.xdata             X X        @0@.pdata             9X EX        @0@.xdata             cX {X        @0@.pdata             橷         @0@.xdata             肵             @0@.pdata             薠 譞        @0@.xdata             鮔             @0@.pdata             Y 
Y        @0@.xdata             +Y CY        @0@.pdata             aY mY        @0@.xdata             媃         @0@.pdata             罽 蚘        @0@.xdata             隮 Z        @0@.pdata             !Z -Z        @0@.xdata             KZ cZ        @0@.pdata             乑 峑        @0@.xdata             玓 籞        @0@.pdata             賈 錤        @0@.xdata             [             @0@.pdata             [ [        @0@.xdata             5[             @0@.pdata             =[ I[        @0@.xdata             g[ {[        @0@.pdata             橻         @0@.xdata             肹 譡        @0@.pdata             鮗 \        @0@.xdata             \             @0@.pdata             +\ 7\        @0@.xdata             U\ i\        @0@.pdata             嘰 揬        @0@.xdata             盶 羂        @0@.pdata             運 隲        @0@.xdata             	]             @0@.pdata             ] ]        @0@.xdata             ;]             @0@.pdata             C] O]        @0@.xdata             m]             @0@.pdata             }] 塢        @0@.xdata                          @0@.pdata             痌 籡        @0@.xdata             賋             @0@.pdata             錧 馷        @0@.xdata             ^             @0@.pdata             ^ +^        @0@.xdata             I^ e^        @0@.pdata             僞 廭        @0@.xdata             璣 羄        @0@.pdata             過 隵        @0@.xdata             	_ _        @0@.pdata             ;_ G_        @0@.xdata             e_             @0@.pdata             m_ y_        @0@.xdata             梍             @0@.pdata             焈 玙        @0@.xdata             蒧             @0@.pdata             誣 醎        @0@.xdata             �_             @0@.pdata             ` `        @0@.xdata             1` I`        @0@.pdata             S` _`        @0@.xdata             }` 檂        @0@.pdata             穈 胉        @0@.xdata             醏 馺        @0@.pdata             a a        @0@.xdata             9a Ua        @0@.pdata             sa a        @0@.xdata             漚 璦        @0@.pdata             薬 譨        @0@.rdata          	   鮝             @@@.rdata                          @0@.rdata              b             @0@.rdata             b             @0@.rdata             b             @0@.rdata             b             @0@.rdata             b             @0@.rdata             b             @0@.rdata          
   b             @@@.rdata             $b             @@@.rdata          
   2b             @@@.rdata             <b             @@@.rdata             Gb             @0@.rdata             Lb             @@@.rdata             Tb             @0@.rdata             [b             @0@.rdata             bb             @@@.rdata          
   nb             @@@.rdata             {b             @0@.rdata             乥             @0@.rdata             嘼             @0@.rdata             巄             @0@.rdata             昩             @0@.rdata             梑             @@@.rdata             眀             @@@.rdata          1   蘠             @@@.rdata             齜             @@@.rdata             c             @@@.rdata             $c             @@@.rdata          $   Bc             @@@.rdata             fc             @@@.rdata             卌             @@@.rdata             渃             @0@.rdata             焎             @@@.rdata             痗             @0@.rdata             砪             @@@.rdata             籧             @@@.rdata             胏             @@@.rdata             薱             @@@.rdata             觕             @0@.rdata             譪             @@@.debug$T        |   遚             @ B.chks64         (  [d              
     /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   o     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\glfw.dir\Release\input.obj : < `  �  & y�   & y�  Microsoft (R) Optimizing Compiler    �   ?       URLZONE_LOCAL_MACHINE     URLZONE_INTRANET     URLZONE_TRUSTED     URLZONE_INTERNET  n    URLZONEREG_DEFAULT  n   URLZONEREG_HKLM # �   BINDHANDLETYPES_DEPENDENCY  �    PIDMSI_STATUS_NORMAL  �   PIDMSI_STATUS_NEW  �   PIDMSI_STATUS_PRELIM  �   PIDMSI_STATUS_DRAFT ! �   PIDMSI_STATUS_INPROGRESS  �   PIDMSI_STATUS_EDIT  �   PIDMSI_STATUS_REVIEW  �   PIDMSI_STATUS_PROOF ! �    COINITBASE_MULTITHREADED ' �  �   CLSCTX_ACTIVATE_X86_SERVER , �   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL  �    MDT_EFFECTIVE_DPI  �    NODE_INVALID  �   NODE_ELEMENT  �   NODE_ATTRIBUTE  �   NODE_TEXT  �   NODE_CDATA_SECTION  �   NODE_ENTITY_REFERENCE  �   NODE_ENTITY $ �   NODE_PROCESSING_INSTRUCTION  �   NODE_COMMENT  �  	 NODE_DOCUMENT  �  
 NODE_DOCUMENT_TYPE  �   NODE_DOCUMENT_FRAGMENT  �    XMLELEMTYPE_ELEMENT  �   XMLELEMTYPE_TEXT  �   XMLELEMTYPE_COMMENT  �   XMLELEMTYPE_DOCUMENT  �   XMLELEMTYPE_DTD  �   XMLELEMTYPE_PI  �   VT_I2  �   VT_I4  �   VT_BSTR  �  	 VT_DISPATCH  �  
 VT_ERROR  �   VT_VARIANT  �  
 VT_UNKNOWN  �   VT_I1  �   VT_I8  �  $ VT_RECORD  �  � �VT_RESERVED  
d        _glfw  �    TYSPEC_CLSID  �   TYSPEC_FILEEXT  �   TYSPEC_MIMETYPE  �   TYSPEC_FILENAME  �   TYSPEC_PROGID  �   TYSPEC_PACKAGENAME + w   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 w   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - w   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 w   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS * �   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 �   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A �   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP ! 
s        _glfwDefaultMappings     PowerUserMaximum  �    ServerApplication  ~    IdleShutdown  u   COR_VERSION_MAJOR_V2  	    TKIND_ENUM  	   TKIND_RECORD  	   TKIND_MODULE  	   TKIND_INTERFACE  	   TKIND_DISPATCH  	   TKIND_COCLASS  	   TKIND_ALIAS  	   TKIND_UNION  �   CC_CDECL  �   CC_MSCPASCAL  �   CC_PASCAL  �   CC_MACPASCAL  �   CC_STDCALL  �   CC_FPFASTCALL  �   CC_SYSCALL  �   CC_MPWCDECL  �   CC_MPWPASCAL  �    FUNC_VIRTUAL  �   FUNC_PUREVIRTUAL  �   FUNC_NONVIRTUAL  �   FUNC_STATIC  �    VAR_PERINSTANCE  �   VAR_STATIC  �   VAR_CONST # �   BINDSTATUS_FINDINGRESOURCE  �   BINDSTATUS_CONNECTING  �   BINDSTATUS_REDIRECTING % �   BINDSTATUS_BEGINDOWNLOADDATA # �   BINDSTATUS_DOWNLOADINGDATA # �   BINDSTATUS_ENDDOWNLOADDATA + �   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( �   BINDSTATUS_INSTALLINGCOMPONENTS ) �  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # �  
 BINDSTATUS_USINGCACHEDCOPY " �   BINDSTATUS_SENDINGREQUEST $ �   BINDSTATUS_CLASSIDAVAILABLE % �  
 BINDSTATUS_MIMETYPEAVAILABLE * �   BINDSTATUS_CACHEFILENAMEAVAILABLE & �   BINDSTATUS_BEGINSYNCOPERATION $ �   BINDSTATUS_ENDSYNCOPERATION # �   BINDSTATUS_BEGINUPLOADDATA ! �   BINDSTATUS_UPLOADINGDATA ! �   BINDSTATUS_ENDUPLOADDATA # �   BINDSTATUS_PROTOCOLCLASSID  �   BINDSTATUS_ENCODING - �   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( �   BINDSTATUS_CLASSINSTALLLOCATION  �   BINDSTATUS_DECODING & �   BINDSTATUS_LOADINGMIMEHANDLER , �   BINDSTATUS_CONTENTDISPOSITIONATTACH ( �   BINDSTATUS_FILTERREPORTMIMETYPE ' �   BINDSTATUS_CLSIDCANINSTANTIATE % �   BINDSTATUS_IUNKNOWNAVAILABLE  �   BINDSTATUS_DIRECTBIND  �   BINDSTATUS_RAWMIMETYPE " �    BINDSTATUS_PROXYDETECTING   �  ! BINDSTATUS_ACCEPTRANGES  �  " BINDSTATUS_COOKIE_SENT + �  # BINDSTATUS_COMPACT_POLICY_RECEIVED % �  $ BINDSTATUS_COOKIE_SUPPRESSED ( �  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' �  & BINDSTATUS_COOKIE_STATE_ACCEPT ' �  ' BINDSTATUS_COOKIE_STATE_REJECT ' �  ( BINDSTATUS_COOKIE_STATE_PROMPT & �  ) BINDSTATUS_COOKIE_STATE_LEASH * �  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  �  + BINDSTATUS_POLICY_HREF  �  , BINDSTATUS_P3P_HEADER + �  - BINDSTATUS_SESSION_COOKIE_RECEIVED . �  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + �  / BINDSTATUS_SESSION_COOKIES_ALLOWED   �  0 BINDSTATUS_CACHECONTROL . �  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) �  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & �  3 BINDSTATUS_PUBLISHERAVAILABLE ( �  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ �  5 BINDSTATUS_SSLUX_NAVBLOCKED , �  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , �  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " �  8 BINDSTATUS_64BIT_PROGRESS  �  8 BINDSTATUS_LAST  �  9 BINDSTATUS_RESERVED_0  �  : BINDSTATUS_RESERVED_1  �  ; BINDSTATUS_RESERVED_2  �  < BINDSTATUS_RESERVED_3  �  = BINDSTATUS_RESERVED_4  �  > BINDSTATUS_RESERVED_5  �  ? BINDSTATUS_RESERVED_6  �  @ BINDSTATUS_RESERVED_7  �  A BINDSTATUS_RESERVED_8  �  B BINDSTATUS_RESERVED_9  �  C BINDSTATUS_RESERVED_A  �  D BINDSTATUS_RESERVED_B  �  E BINDSTATUS_RESERVED_C  �  F BINDSTATUS_RESERVED_D  �  G BINDSTATUS_RESERVED_E  �  H BINDSTATUS_RESERVED_F  �  I BINDSTATUS_RESERVED_10  �  J BINDSTATUS_RESERVED_11  �  K BINDSTATUS_RESERVED_12  �  L BINDSTATUS_RESERVED_13  �  M BINDSTATUS_RESERVED_14  �    DESCKIND_NONE  �   DESCKIND_FUNCDESC  �   DESCKIND_VARDESC  �   DESCKIND_TYPECOMP   �   DESCKIND_IMPLICITAPPOBJ  �    CIP_DISK_FULL  �   CIP_ACCESS_DENIED ! �   CIP_NEWER_VERSION_EXISTS ! �   CIP_OLDER_VERSION_EXISTS  �   CIP_NAME_CONFLICT 1 �   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + �   CIP_EXE_SELF_REGISTERATION_TIMEOUT  �   CIP_UNSAFE_TO_ABORT  �   CIP_NEED_REBOOT % �   TP_CALLBACK_PRIORITY_INVALID  �   BINDSTRING_HEADERS   �   BINDSTRING_ACCEPT_MIMES  �   BINDSTRING_EXTRA_URL  �   BINDSTRING_LANGUAGE  �   BINDSTRING_USERNAME  �   BINDSTRING_PASSWORD  �   BINDSTRING_UA_PIXELS  �   BINDSTRING_UA_COLOR  �  	 BINDSTRING_OS  �  
 BINDSTRING_USER_AGENT $ �   BINDSTRING_ACCEPT_ENCODINGS  �   BINDSTRING_POST_COOKIE " �  
 BINDSTRING_POST_DATA_MIME  �   BINDSTRING_URL  �   BINDSTRING_IID ' �   BINDSTRING_FLAG_BIND_TO_OBJECT $ �   BINDSTRING_PTR_BIND_CONTEXT  �   BINDSTRING_XDR_ORIGIN   �   BINDSTRING_DOWNLOADPATH  �   BINDSTRING_ROOTDOC_URL $ �   BINDSTRING_INITIAL_FILENAME " �   BINDSTRING_PROXY_USERNAME " �   BINDSTRING_PROXY_PASSWORD ! �   BINDSTRING_ENTERPRISE_ID  �   BINDSTRING_DOC_URL  �    SYS_WIN16  �   SYS_WIN32  �   SYS_MAC  �   PARSE_CANONICALIZE  �   PARSE_FRIENDLY  �   PARSE_SECURITY_URL  �   PARSE_ROOTDOCUMENT  �   PARSE_DOCUMENT  �   PARSE_ANCHOR ! �   PARSE_ENCODE_IS_UNESCAPE  �   PARSE_DECODE_IS_ESCAPE  �  	 PARSE_PATH_FROM_URL  �  
 PARSE_URL_FROM_PATH  �   PARSE_MIME  �   PARSE_SERVER  �  
 PARSE_SCHEMA  �   PARSE_SITE  �   PARSE_DOMAIN  �   PARSE_LOCATION  �   PARSE_SECURITY_DOMAIN  �   PARSE_ESCAPE  s   PSU_DEFAULT  �   QUERY_EXPIRATION_DATE " �   QUERY_TIME_OF_LAST_CHANGE  �   QUERY_CONTENT_ENCODING  �   QUERY_CONTENT_TYPE  �   QUERY_REFRESH  �   QUERY_RECOMBINE  �   QUERY_CAN_NAVIGATE  �   QUERY_USES_NETWORK  �  	 QUERY_IS_CACHED   �  
 QUERY_IS_INSTALLEDENTRY " �   QUERY_IS_CACHED_OR_MAPPED  �   QUERY_USES_CACHE  �  
 QUERY_IS_SECURE  �   QUERY_IS_SAFE ! �   QUERY_USES_HISTORYFOLDER  �    CHANGEKIND_ADDMEMBER   �   CHANGEKIND_DELETEMEMBER  �   CHANGEKIND_SETNAMES $ �   CHANGEKIND_SETDOCUMENTATION  �   CHANGEKIND_GENERAL  �   CHANGEKIND_INVALIDATE   �   CHANGEKIND_CHANGEFAILED    LPCDIDEVICEINSTANCEW  "   D3DCOLOR  �  _DICOLORSET # 8  LPDICONFIGUREDEVICESCALLBACK " :  LPDICONFIGUREDEVICESPARAMSW  �  DIDEVICEINSTANCEW    LPDIRECTINPUTDEVICE8W    LPDIENUMDEVICESCALLBACKW  �  DICOLORSET $ 2  LPDIENUMDEVICESBYSEMANTICSCBW ! B  _DICONFIGUREDEVICESPARAMSW  ?  IDirectInput8WVtbl  �  GLFWdeallocatefun 
 #   WPARAM  �  tagRAWKEYBOARD  �  PFN_wglGetCurrentDC  �  PFN_wglMakeCurrent  �  _GLFWtlsWin32  �  GLFWallocatefun    POINT ( �  PFNWGLCREATECONTEXTATTRIBSARBPROC  �  LIST_ENTRY 
   LPLONG  �  GLFWreallocatefun   �  PFNWGLSWAPINTERVALEXTPROC  �  PFN_wglCreateContext  �  RTL_CRITICAL_SECTION  �  tagRAWMOUSE  �  tagRAWINPUTHEADER  �  PFN_wglGetCurrentContext 
 �  HRGN__  �  _LIST_ENTRY  �  _XINPUT_VIBRATION " �  PRTL_CRITICAL_SECTION_DEBUG " �  _RTL_CRITICAL_SECTION_DEBUG  �  _XINPUT_GAMEPAD  w  XINPUT_CAPABILITIES  �  _FILETIME  �  XINPUT_GAMEPAD ( ?  PFN_SetProcessDpiAwarenessContext    LPDIEFFECT  �  HRGN  �  XINPUT_VIBRATION  �  HGLRC__   .  PFN_XInputGetCapabilities # F  PFN_AdjustWindowRectExForDpi    HDEVNOTIFY ' �  PFNWGLGETEXTENSIONSSTRINGEXTPROC  �  DWM_BLURBEHIND  �  PFN_wglShareLists  �  CHANGEFILTERSTRUCT  �  RAWMOUSE 
 @  LPCSTR  �  RAWINPUT  �  IDirectInput8W  �  RAWINPUTHEADER  �  PROC  !   uint16_t  �  LPDIENVELOPE  �  tagRECT  �  HMONITOR__  %  PFN_DirectInput8Create  �  HMONITOR  �  IDirectInputEffect $ V  PFN_DwmEnableBlurBehindWindow  #   VkSurfaceKHR 
 !   USHORT ! I  PFN_GetSystemMetricsForDpi 
 *  LPGUID  �  _DIACTIONW  A  PFN_GetDpiForWindow " Y  PFN_DwmGetColorizationColor  �  PFN_wglGetProcAddress  u   EGLenum  ~  _GLFWtls  P  PFN_DwmFlush % E  PFN_OSMesaCreateContextAttribs    tagPOINT  $  PFN_eglGetDisplay  3  PFN_XInputGetState  �  DIEFFECTINFOW ' �  PFNWGLGETEXTENSIONSSTRINGARBPROC  �  GLFWallocator  8  PFN_eglSwapInterval  �  _GLFWtimerWin32  X  LPDIPROPHEADER  �  PFN_wglDeleteContext    IDirectInputEffectVtbl  ]  PROCESS_DPI_AWARENESS  z  OSVERSIONINFOEXW  �  _GLFWlibraryNull  �  RECT  "   ULONG  �  RAWKEYBOARD  t   BOOL 
 �  HDC 
 �  RAWHID  �  LPDIDEVICEIMAGEINFOW  �  _RTL_CRITICAL_SECTION    HICON__  )  PFN_eglInitialize  �  CRITICAL_SECTION  �  tagRAWHID  �  _GLFWmutexWin32  5  PFN_eglMakeCurrent  �  DIEFFECT  �  HDC__  �  tagRAWINPUT  +  PFN_eglTerminate  #   uintptr_t  �  LPCDIFILEEFFECT + �  LPDIENUMCREATEDEFFECTOBJECTSCALLBACK 
   LPVOID  G  PFN_OSMesaDestroyContext * �  PFNWGLGETPIXELFORMATATTRIBIVARBPROC  �  DIENVELOPE  &  PFN_eglGetError  �  _GLFWlibraryWGL 
 C  LPRECT  �  LPDIOBJECTDATAFORMAT " N  PFN_DwmIsCompositionEnabled  �  LPCDIEFFECT 
   HANDLE  �  HGLRC  |  _XINPUT_STATE  #   ULONGLONG  �  FILETIME  �  LPDIACTIONW  �  LPDIEFFESCAPE & :  PFN_ChangeWindowMessageFilterEx  �  DIDEVCAPS  �  _DIDEVICEIMAGEINFOW  V  GLFWmonitor  M  PFN_OSMesaGetDepthBuffer  :  PFN_eglQueryString  s  _GLFWfbconfig ! `  PFN_SetProcessDpiAwareness  {  LPDIDEVICEINSTANCEW  2  PFN_eglDestroySurface  ]  LPCDIPROPHEADER    PFN_eglGetConfigAttrib  �  DIDEVICEOBJECTDATA  �  _GLFWcontextWGL  "  LPUNKNOWN  2  GLFWglproc  g  LPDIDEVICEOBJECTDATA  2  PFN_eglDestroyContext  2  PFN_eglSwapBuffers  c  PFN_GetDpiForMonitor  �  DIPROPHEADER 
 �  HWND__  u   UINT  �  VkAllocationCallbacks  �  DIDATAFORMAT  0  PFN_eglCreateContext % S  LPDIENUMDEVICEOBJECTSCALLBACKW  �  _TP_CALLBACK_PRIORITY  �  LPDIRECTINPUTEFFECT  �  HICON  @  GLFWwindow  �  GLFWgammaramp  ~  tagShutdownType  �  DIEFFESCAPE  �  MONITOR_DPI_TYPE  �  tagCALLCONV  	  tagTYPEKIND    IUnknownVtbl  �  _DIOBJECTDATAFORMAT  #   rsize_t  �  LPDIFILEEFFECT  &  PFN_SetProcessDPIAware  |  XINPUT_STATE  z  _OSVERSIONINFOEXW  d  _GLFWlibrary ( w  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  w  _XINPUT_CAPABILITIES  M  PFN_OSMesaGetColorBuffer  s  _GLFWwindowWin32  s  _tagPSUACTION  J  PFN_OSMesaMakeCurrent $ =  PFN_EnableNonClientDpiScaling     LONG_PTR  Z  GLFWmonitorfun  j  PFN_RtlVerifyVersionInfo  �  tagBINDSTRING  !   ATOM / 0  PFNEGLCREATEPLATFORMWINDOWSURFACEEXTPROC  #   ULONG_PTR    tagURLZONE  �  LPDIEFFECTINFOW & =  PFNEGLGETPLATFORMDISPLAYEXTPROC    VkPhysicalDevice  �  __MIDL_ICodeInstall_0001  p  PCHAR  �  tagBINDSTATUS  G  GLFWwindowclosefun  q  _GUID  n  _URLZONEREG  n  _GLFWlibraryWin32  �  LPCDIDEVICEOBJECTDATA  !   wchar_t  '  GLFWcursor  V  GLFWkeyfun    HINSTANCE    EGLNativeDisplayType 
 t   EGLint  !   WORD    _GLFWwndconfig  2  PFN_vkVoidFunction  �  HCURSOR  J  LPDIDEVCAPS  �  VkResult  #   uint64_t ' �  JOB_OBJECT_IO_RATE_CONTROL_FLAGS    _USER_ACTIVITY_PRESENCE    PLONG  �  _GLFWmapelement  �  _DIACTIONFORMATW  /  LPDIACTIONFORMATW    _GLFWinitconfig  m  LPCDIDATAFORMAT  P  GLFWmousebuttonfun      BYTE  �  _GLFWcursorWin32 
 5  PCWSTR  �  PFN_OSMesaGetProcAddress  �  PFNGLGETSTRINGIPROC    EGLDisplay  �  DIFILEEFFECT  �  _GLFWmonitorWin32   S  PFN_vkGetInstanceProcAddr     LONG  -  PFN_eglBindAPI  u   EGLBoolean  �  _GLFWplatform  S  GLFWscrollfun  �  HINSTANCE__  �  _DIDATAFORMAT  �  PFNGLGETSTRINGPROC    EGLSurface ! �  LPDIDEVICEIMAGEINFOHEADERW 
 p   int8_t ! P  LPCDIDEVICEOBJECTINSTANCEW ! B  PFN_OSMesaCreateContextExt  ]  GLFWjoystickfun 
 #   SIZE_T      GLubyte ! 0  PFN_eglCreateWindowSurface # u  ReplacesCorHdrNumericDefines  "   DWORD    EGLNativeWindowType 
   PSHORT  q  _GLFWcursor  "   TP_VERSION  D  GLFWframebuffersizefun  \  GLFWcharmodsfun  �  VARENUM    HWND   w  LPDIDEVICEOBJECTINSTANCEW  "  LPDWORD  D  GLFWwindowposfun    VkInstance  l  GLFWvidmode  #   DWORD64  S  GLFWcursorposfun  G  GLFWwindowrefreshfun  j  _GLFWerror  !  PFN_eglGetConfigs  �  LPDIENUMEFFECTSCALLBACKW      BOOLEAN  �  tagTYSPEC  �  PFNGLGETINTEGERVPROC   �  _DIDEVICEIMAGEINFOHEADERW  f  _GLFWwindow  g  _GLFWmutex      uint8_t  �  tagVARKIND  /  _GLFWctxconfig    PVOID $ �  LPDIENUMEFFECTSINFILECALLBACK  �  PFN_eglGetProcAddress  �  IUnknown  t   errno_t  q   WCHAR     PBYTE  �  DIDEVICEOBJECTINSTANCEW  t   GLint  �  LPCDIEFFECTINFOW 
 q  IID  �  _tagQUERYOPTION  �  _GLFWmapping  �  IDirectInputDevice8WVtbl     HRESULT    _GLFWmonitorNull    EGLConfig    _GLFWwindowNull ! �  __MIDL_IGetBindHandle_0001  Y  GLFWcharfun 
    LONG64  �  tagCOINITBASE  �  tagApplicationType  5  LPCWSTR  �  tagDOMNodeType  q  PWSTR  �  tagCHANGEKIND 
 u   UINT32  �  tagSYSKIND  
  IDirectInputDevice8W  �  _GLFWjoystick  	  _GLFWjoyobjectWin32  J  GLFWwindowfocusfun   M  GLFWwindowcontentscalefun 
 q  LPWSTR  #   UINT_PTR  q  GUID  �  tagFUNCKIND  t   INT32  �  PIDMSI_STATUS_VALUE  ;  GLFWgamepadstate ( �  PTP_CLEANUP_GROUP_CANCEL_CALLBACK 
 u   GLenum  J  GLFWcursorenterfun 
 u   GLuint    EGLContext    OSMesaContext 
 #   size_t  D  GLFWwindowsizefun  �  _GLFWcontext  �  tagGLOBALOPT_EH_VALUES     SHORT  J  GLFWwindowiconifyfun    PLONG64  �  GLFWimage  �  tagCLSCTX     INT_PTR  u   uint32_t  `  GLFWdropfun  t   GLFWbool  �  tagXMLEMEM_TYPE  p   CHAR  �  _tagPARSEACTION  �  tagDESCKIND  �  _GLFWmonitor    _GLFWjoystickWin32  J  GLFWwindowmaximizefun    �          頴}�穲町v�
c�.丨a� �+篬鰌�莩�  I    �-�雧n�5L屯�:I硾�鮎访~(梱  �    ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �    )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕     8�'预P�憖�0R�(3銖� pN*�  g   6觏v畿S倂9紵"�%��;_%z︹  �   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  E   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  �   D���0�郋鬔G5啚髡J竆)俻w��  �   錵s铿勃砓b棬偡遯鮓尛�9泂惻  *   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  x   �F9�6K�v�/亅S诵]t婻F廤2惶I  �   d2軇L沼vK凔J!女計j儨杹3膦���  
   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  ]   樸7 忁�珨��3]"Fキ�:�,郩�  �    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   衠琪槡铟钭}_XO>�蛭X�7Mp处d  B   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  �   �D;窼僞k渔A�;��?缞鳗5翰�?*R
     5 KO诹硃毣�'R烣�7`埀M@懅y榵  [   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  �   聤�苮g8鄞<aZ�%4)闪�|袉uh�  �   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  9   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  �   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  l   �:2K] �
j�苊赁e�
湿�3k椨�  �   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  	   6��7@L�.�梗�4�檕�!Q戸�$�  N	   �儔14褥緅�3]饃鹷�hK3g搋bA竑  �	   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  �	   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  0
   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  y
   R�
?;y濷6!y艕�3�鸎=鈱e�q園\�  �
   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  �
   渐袿.@=4L笴速婒m瑜;_琲M %q�  G   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  �   RX鰷稐蒋駏U	�>�5妆癫�
8A/  �   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  9   �>2
^�﨟2W酟傲X{b?荼猲�;  x   死╇D�#/��4鶄>G63齛w�i�->M  �   E縄�7�g虩狱呂�/y蛨惏l斋�笵  
   c�#�'�縌殹龇D兺f�$x�;]糺z�  U
   $G\|R_熖泤煡4勄颧绖�?(�~�:  �
   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   �
    栀��綔&@�.�)�C�磍萘k  B   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   繃S,;fi@`騂廩k叉c.2狇x佚�  �   釳�>�H?6蓙�� 祍<ベ垽�=j逃�     2W瓓�<X	綧]�龐IE?'笼t唰��  `   晁X歌符�2澋U�'煈覽b�
蟣;-�  �   |q�6桢赤汗mv訔�	
爟~胱�>?妼BK�,  �   �"睱建Bi圀対隤v��cB�'窘�n  A   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏  �   bRè1�5捘:.z錨{娯啹}坬麺P  �   攄繠�
\b擫5`Om�1悑R钡h�:�47�     ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  Z   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  �   5睔`&N_鏃|�<�$�獖�!銸]}"  �   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  ?   ┫緞A$窄�0� NG�%+�*�
!7�=b  �   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  �   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  -   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  �   ukj艔�7s�-d丈抭ug壍齅钜Y]     �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  `   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  �   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9     噔园�,c珥珸洯濠�繗猍=sZ導  ?   丩{F*}皦N誫l雘啫椊�梮,圶`�  �   ��嵉氒髅嘁棭够*ヅ�
�'徺p4  �   孆x�0队<堛�猬dh梧`sR顛	k�7[M@     屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  g   鷜E恳B�#蠠�,qC吾w�岧儁N篴  �   艶笊\眔Z%師}wы孜+HN鯥湔N  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�     晊褃 �=�韀晝阓�!熝神�+k�2�<$]�  [   蠯3
掽K謈 � l�6襕鞜��H#�  �   �8��/X昋旒�.胱#h=J"髈篒go#  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  6   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9     荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  h   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  F   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   Eム聂�
C�?潗'{胿D'x劵;釱�  �   r�L剟FsS鏴醼+E千I呯贄0鬬/�  "   �*o驑瓂a�(施眗9歐湬

�  j   鹰杩@坓!)IE搒�;puY�'i憷n!  �   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�      I嘛襨签.濟;剕��7啧�)煇9触�.  H    萾箒�$.潆�j閖i转pf-�稃陞��  �   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  �   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  .   G髼*悭�2睆�侻皣軁舃裄樘珱)  |   齛|)3h�2%籨糜/N_燿C虺r_�9仌  �   8蟴B或绢溵9"C dD揭鞧Vm5TB�     谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  \   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  >   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   �~鴧傳.P怬WsP-"焫#N�:�&場璁  �   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  !    K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  q    "�挨	b�'+舒�5<O�呱_歲+/�P�?  �    FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  !   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  M!   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  �!   �呾��+h7晃O枖��*谵|羓嗡捬  �!   j轲P[塵5m榤g摏癭 鋍1O骺�*�  #"   綔)\�谑U⒊磒'�!W磼B0锶!;  q"   sL&%�znOdz垗�M,�:吶1B滖  �"   鹴y�	宯N卮洗袾uG6E灊搠d�  #   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  R#   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �#   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  �#   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  ;$   �n儹`
舔�	Y氀�:b
#p:  �$   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  �$   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  #%    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  l%   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  �%   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  &   掴'圭,@H4sS裬�!泉:莠й�"fE)  V&   覽s鴧罪}�'v,�*!�
9E汲褑g;  �&   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �&   +椬恡�
	#G許�/G候Mc�蜀煟-  1'   0T砞獃钎藰�0逪喌I窐G(崹�  y'   閯�価=�<酛皾u漑O�髦jx`-�4睲�  �'   戹�j-�99檽=�8熈讠鳖铮�  (   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  _(   ,�<鈬獿鍢憁�g$��8`�"�  �(   潝(綊r�*9�6}颞7V竅\剫�8値�#  �(   ^憖�眜蘓�y冊日/缁ta铁6殔  G)   魯f�u覬n\��zx騖笹笾骊q*砎�,�  �)   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  �)   _%1糠7硘籺蚻q5饶昈v纪嗈�  %*   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  o*   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  �*   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  �   @       m  �  7   n  �  G   o  �  W   p  �  e   �  �  �  �    +   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\xinput.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dinput.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h D:\RTXPT\External\Donut\thirdparty\glfw\src\input.c C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_joystick.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dbt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_thread.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h D:\RTXPT\External\Donut\thirdparty\glfw\src\mappings.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h D:\RTXPT\External\Donut\thirdparty\glfw\src\win32_time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h D:\RTXPT\External\Donut\thirdparty\glfw\src\internal.h D:\RTXPT\External\Donut\thirdparty\glfw\src\platform.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\RTXPT\External\Donut\thirdparty\glfw\src\null_platform.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h �       L�  �  p   �  p  
 .  	    2  	   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              (       0       8   !    @   $    H   '    P   *    X   -    `   0    h   3    p   6    x   9    �   <    �   ?    �   B    �   E    �   H    �   K    �   N    �   Q    �   T    �   W    �   Z    �   ]    �   `    �   c    �   f    �   i       l      o      r      u       x    (  {    0  ~    8  �    @  �    H  �    P  �    X  �    `  �    h  �    p  �    x  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �       �      �      �      �       �    (  �    0  �    8  �    @  �    H  �    P  �    X  �    `  �    h  �    p  �    x  �    �  �    �  �    �     �     �     �     �     �     �     �     �     �     �      �  #   �  &   �  )      ,     /     2     5      8   (  ;   0  >   8  A   @  D   H  G   P  J   X  M   `  P   h  S   p  V   x  Y   �  \   �  _   �  b   �  e   �  h   �  k   �  n   �  q   �  t   �  w   �  z   �  }   �  �   �  �   �  �   �  �      �     �     �     �      �   (  �   0  �   8  �   @  �   H  �   P  �   X  �   `  �   h  �   p  �   x  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �      �     �     �     �      �   (  �   0  �   8     @     H     P  
   X  
   `     h     p     x     �     �     �  "   �  %   �  (   �  +   �  .   �  1   �  4   �  7   �  :   �  =   �  @   �  C   �  F   �  I      L     O     R     U      X   (  [   0  ^   8  a   @  d   H  g   P  j   X  m   `  p   h  s   p  v   x  y   �  |   �     �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �      �     �     �     �      �   (  �   0  �   8  �   @  �   H  �   P  �   X  �   `  �   h  �   p  �   x  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �      �     �     �  	                              (     0     8  !   @  $   H  '   P  *   X  -   `  0   h  3   p  6   x  9   �  <   �  ?   �  B   �  E   �  H   �  K   �  N   �  Q   �  T   �  W   �  Z   �  ]   �  `   �  c   �  f   �  i    	  l   	  o   	  r   	  u    	  x   (	  {   0	  ~   8	  �   @	  �   H	  �   P	  �   X	  �   `	  �   h	  �   p	  �   x	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �   �	  �    
  �   
  �   
  �   
  �    
  �   (
  �   0
  �   8
  �   @
  �   H
  �   P
  �   X
  �   `
  �   h
  �   p
  �   x
  �   �
  �   �
  �   �
     �
     �
     �
     �
     �
     �
     �
     �
     �
     �
      �
  #   �
  &   �
  )      ,     /     2     5      8   (  ;   0  >   8  A   @  D   H  G   P  J   X  M   `  P   03000000fa2d00000100000000000000,3DRUDDER,leftx:a0,lefty:a1,rightx:a5,righty:a2,platform:Windows, 03000000c82d00002038000000000000,8bitdo,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000951000000000000,8BitDo Dogbone Modkit,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,start:b11,platform:Windows, 03000000c82d000011ab000000000000,8BitDo F30,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00001038000000000000,8BitDo F30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a5,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000090000000000000,8BitDo FC30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000650000000000000,8BitDo M30,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:a4,lefttrigger:a5,leftx:a0,lefty:a1,rightshoulder:b6,righttrigger:b7,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00005106000000000000,8BitDo M30 Gamepad,a:b1,b:b0,back:b10,guide:b2,leftshoulder:b6,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b9,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000151000000000000,8BitDo M30 ModKit,a:b0,b:b1,back:b10,dpdown:+a2,dpleft:-a0,dpright:+a0,dpup:-a2,rightshoulder:b6,righttrigger:b7,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00000310000000000000,8BitDo N30,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00002028000000000000,8BitDo N30,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a5,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00008010000000000000,8BitDo N30,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00000451000000000000,8BitDo N30 Modkit,a:b1,b:b0,back:b10,dpdown:+a2,dpleft:-a0,dpright:+a0,dpup:-a2,start:b11,platform:Windows, 03000000c82d00000190000000000000,8BitDo N30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00001590000000000000,8BitDo N30 Pro 2,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a5,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00006528000000000000,8BitDo N30 Pro 2,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000022000000090000000000000,8Bitdo NES30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000203800000900000000000000,8Bitdo NES30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000360000000000000,8BitDo Pro 2,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00002867000000000000,8BitDo S30 Modkit,a:b0,b:b1,dpdown:+a2,dpleft:-a0,dpright:+a0,dpup:-a2,leftshoulder:b8,lefttrigger:b9,rightshoulder:b6,righttrigger:b7,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00000130000000000000,8BitDo SF30,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a5,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000060000000000000,8Bitdo SF30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000061000000000000,8Bitdo SF30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d000021ab000000000000,8BitDo SFC30,a:b1,b:b0,back:b10,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000102800000900000000000000,8Bitdo SFC30 GamePad,a:b1,b:b0,back:b10,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00003028000000000000,8Bitdo SFC30 GamePad,a:b1,b:b0,back:b10,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000030000000000000,8BitDo SN30,a:b1,b:b0,back:b10,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00001290000000000000,8BitDo SN30,a:b1,b:b0,back:b10,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b6,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d000020ab000000000000,8BitDo SN30,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a5,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00004028000000000000,8BitDo SN30,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a5,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00006228000000000000,8BitDo SN30,a:b1,b:b0,back:b10,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b6,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000351000000000000,8BitDo SN30 Modkit,a:b1,b:b0,back:b10,dpdown:+a2,dpleft:-a0,dpright:+a0,dpup:-a2,leftshoulder:b6,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000160000000000000,8BitDo SN30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000161000000000000,8BitDo SN30 Pro,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000121000000000000,8BitDo SN30 Pro for Android,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00000260000000000000,8BitDo SN30 Pro+,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000261000000000000,8BitDo SN30 Pro+,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b2,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00000031000000000000,8BitDo Wireless Adapter,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000c82d00001890000000000000,8BitDo Zero 2,a:b1,b:b0,back:b10,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b11,x:b4,y:b3,platform:Windows, 03000000c82d00003032000000000000,8BitDo Zero 2,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,rightx:a2,righty:a3,start:b11,x:b4,y:b3,platform:Windows, 03000000a00500003232000000000000,8Bitdo Zero GamePad,a:b0,b:b1,back:b10,dpdown:+a2,dpleft:-a0,dpright:+a0,dpup:-a2,leftshoulder:b6,rightshoulder:b7,start:b11,x:b3,y:b4,platform:Windows, 03000000a30c00002700000000000000,Astro City Mini,a:b2,b:b1,back:b8,leftx:a3,lefty:a4,rightshoulder:b4,righttrigger:b5,start:b9,x:b3,y:b0,platform:Windows, 03000000a30c00002800000000000000,Astro City Mini,a:b2,b:b1,back:b8,leftx:a3,lefty:a4,rightshoulder:b4,righttrigger:b5,start:b9,x:b3,y:b0,platform:Windows, 030000008f0e00001200000000000000,Acme GA-02,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b2,y:b3,platform:Windows, 03000000c01100000355000011010000,ACRUX USB GAME PAD,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000fa190000f0ff000000000000,Acteck AGJ-3200,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 030000006f0e00001413000000000000,Afterglow,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000341a00003608000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00000263000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00001101000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00001401000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00001402000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00001901000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00001a01000000000000,Afterglow PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000d62000001d57000000000000,Airflo PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000491900001904000000000000,Amazon Luna Controller,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b8,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,misc1:b9,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b7,x:b2,y:b3,platform:Windows, 03000000710100001904000000000000,Amazon Luna Controller,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b11,leftshoulder:b5,leftstick:b8,leftx:a0,lefty:a1,misc1:b9,rightshoulder:b4,rightstick:b7,rightx:a3,righty:a4,start:b6,x:b3,y:b2,platform:Windows, 03000000ef0500000300000000000000,AxisPad,a:b2,b:b3,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:b7,rightx:a3,righty:a2,start:b11,x:b0,y:b1,platform:Windows, 03000000d6200000e557000000000000,Batarang,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000c01100001352000000000000,Battalife Joystick,a:b6,b:b7,back:b2,leftshoulder:b0,leftx:a0,lefty:a1,rightshoulder:b1,start:b3,x:b4,y:b5,platform:Windows, 030000006f0e00003201000000000000,Battlefield 4 PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000d62000002a79000000000000,BDA PS4 Fightpad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000bc2000006012000000000000,Betop 2126F,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000bc2000000055000000000000,Betop BFM Gamepad,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000bc2000006312000000000000,Betop Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000bc2000006321000000000000,BETOP CONTROLLER,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000bc2000006412000000000000,Betop Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000c01100000555000000000000,Betop Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000c01100000655000000000000,Betop Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000790000000700000000000000,Betop Gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b3,y:b0,platform:Windows, 03000000808300000300000000000000,Betop Gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b3,y:b0,platform:Windows, 030000006b1400000055000000000000,Bigben PS3 Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000006b1400000103000000000000,Bigben PS3 Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b2,platform:Windows, 03000000120c0000210e000000000000,Brook Mars,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 0300000066f700000500000000000000,BrutalLegendTest,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b3,platform:Windows, 03000000d81d00000b00000000000000,BUFFALO BSGP1601 Series ,a:b5,b:b3,back:b12,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b8,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b9,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b13,x:b4,y:b2,platform:Windows, 03000000e82000006058000000000000,Cideko AK08b,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000457500000401000000000000,Cobra,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000005e0400008e02000000000000,Controller (XBOX 360 For Windows),a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:+a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:-a2,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 030000005e040000a102000000000000,Controller (Xbox 360 Wireless Receiver for Windows),a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:+a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:-a2,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 030000005e040000ff02000000000000,Controller (Xbox One For Windows) - Wired,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:+a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:-a2,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 030000005e040000ea02000000000000,Controller (Xbox One For Windows) - Wireless,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:+a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:-a2,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 03000000260900008888000000000000,Cyber Gadget GameCube Controller,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,lefttrigger:a5,leftx:a0,lefty:a1,rightshoulder:b6,righttrigger:a4,rightx:a2,righty:a3~,start:b7,x:b2,y:b3,platform:Windows, 03000000a306000022f6000000000000,Cyborg V.3 Rumble Pad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:+a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:-a3,rightx:a2,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000451300000830000000000000,Defender Game Racer X7,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000007d0400000840000000000000,Destroyer Tiltpad,+leftx:h0.2,+lefty:h0.4,-leftx:h0.8,-lefty:h0.1,a:b1,b:b2,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b4,rightshoulder:b5,x:b0,y:b3,platform:Windows, 03000000791d00000103000000000000,Dual Box WII,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b6,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b5,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000bd12000002e0000000000000,Dual USB Vibration Joystick,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b9,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b10,righttrigger:b5,rightx:a3,righty:a2,start:b11,x:b3,y:b0,platform:Windows, 030000008f0e00000910000000000000,DualShock 2,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b9,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b10,righttrigger:b5,rightx:a3,righty:a2,start:b11,x:b3,y:b0,platform:Windows, 030000006f0e00003001000000000000,EA SPORTS PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000b80500000410000000000000,Elecom Gamepad,a:b2,b:b3,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b1,platform:Windows, 03000000b80500000610000000000000,Elecom Gamepad,a:b2,b:b3,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b1,platform:Windows, 03000000120c0000f61c000000000000,Elite,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000008f0e00000f31000000000000,EXEQ,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b2,platform:Windows, 03000000341a00000108000000000000,EXEQ RF USB Gamepad 8206,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000006f0e00008401000000000000,Faceoff Deluxe+ Audio Wired Controller for Nintendo Switch,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00008001000000000000,Faceoff Wired Pro Controller for Nintendo Switch,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000852100000201000000000000,FF-GP1,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00008500000000000000,Fighting Commander 2016 PS3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00008400000000000000,Fighting Commander 5,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00008700000000000000,Fighting Stick mini 4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00008800000000000000,Fighting Stick mini 4,a:b1,b:b2,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b8,x:b0,y:b3,platform:Windows, 030000000d0f00002700000000000000,FIGHTING STICK V3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 78696e70757403000000000000000000,Fightstick TES,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,lefttrigger:a2,rightshoulder:b5,righttrigger:a5,start:b7,x:b2,y:b3,platform:Windows, 03000000790000002201000000000000,Game Controller for PC,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 0300000066f700000100000000000000,Game VIB Joystick,a:b2,b:b3,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:b7,rightx:a3,righty:a2,start:b11,x:b0,y:b1,platform:Windows, 03000000260900002625000000000000,Gamecube Controller,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b6,lefttrigger:a4,leftx:a0,lefty:a1,righttrigger:a5,rightx:a2,righty:a3,start:b7,x:b2,y:b3,platform:Windows, 03000000790000004618000000000000,GameCube Controller Adapter,a:b1,b:b2,dpdown:b14,dpleft:b15,dpright:b13,dpup:b12,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b5,rightx:a5,righty:a2,start:b9,x:b0,y:b3,platform:Windows, 030000008f0e00000d31000000000000,GAMEPAD 3 TURBO,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000280400000140000000000000,GamePad Pro USB,a:b1,b:b2,back:b8,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 03000000ac0500003d03000000000000,GameSir,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000ac0500004d04000000000000,GameSir,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000ffff00000000000000000000,GameStop Gamepad,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000c01100000140000000000000,GameStop PS4 Fun Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000009b2800003200000000000000,GC/N64 to USB v3.4,a:b0,b:b7,dpdown:b11,dpleft:b12,dpright:b13,dpup:b10,lefttrigger:+a5,leftx:a0,lefty:a1,rightshoulder:b2,righttrigger:+a2,rightx:a3,righty:a4,start:b3,x:b1,y:b8,platform:Windows, 030000009b2800006000000000000000,GC/N64 to USB v3.6,a:b0,b:b7,dpdown:b11,dpleft:b12,dpright:b13,dpup:b10,lefttrigger:+a5,leftx:a0,lefty:a1,rightshoulder:b2,righttrigger:+a2,rightx:a3,righty:a4,start:b3,x:b1,y:b8,platform:Windows, 030000008305000009a0000000000000,Genius,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000008305000031b0000000000000,Genius Maxfire Blaze 3,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000451300000010000000000000,Genius Maxfire Grandias 12,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000005c1a00003330000000000000,Genius MaxFire Grandias 12V,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b7,leftx:a0,lefty:a1,rightshoulder:b4,rightstick:b11,righttrigger:b5,rightx:a3,righty:a2,start:b9,x:b2,y:b3,platform:Windows, 03000000300f00000b01000000000000,GGE909 Recoil Pad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 03000000f0250000c283000000000000,Gioteck,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000f025000021c1000000000000,Gioteck PS3 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000f0250000c383000000000000,Gioteck VX2 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000f0250000c483000000000000,Gioteck VX2 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 030000007d0400000540000000000000,Gravis Eliminator GamePad Pro,a:b1,b:b2,back:b8,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 03000000341a00000302000000000000,Hama Scorpad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00004900000000000000,Hatsune Miku Sho Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000001008000001e1000000000000,Havit HV-G60,a:b2,b:b1,back:b8,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b3,y:b0,platform:Windows, 03000000d81400000862000000000000,HitBox Edition Cthulhu+,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b5,lefttrigger:b4,rightshoulder:b7,righttrigger:b6,start:b9,x:b0,y:b3,platform:Windows, 03000000632500002605000000000000,HJD-X,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 030000000d0f00002d00000000000000,Hori Fighting Commander 3 Pro,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00005f00000000000000,Hori Fighting Commander 4 (PS3),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00005e00000000000000,Hori Fighting Commander 4 (PS4),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00004000000000000000,Hori Fighting Stick Mini 3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b5,lefttrigger:b4,rightshoulder:b7,righttrigger:b6,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00005400000000000000,Hori Pad 3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00000900000000000000,Hori Pad 3 Turbo,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00004d00000000000000,Hori Pad A,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00009200000000000000,Hori Pokken Tournament DX Pro Pad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00001600000000007803,HORI Real Arcade Pro EX-SE (Xbox 360),a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,lefttrigger:a2,rightshoulder:b5,righttrigger:a5,start:b7,x:b2,y:b3,platform:Windows, 030000000d0f00009c00000000000000,Hori TAC Pro,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f0000c100000000000000,Horipad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00006e00000000000000,HORIPAD 4 (PS3),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00006600000000000000,HORIPAD 4 (PS4),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00005500000000000000,Horipad 4 FPS,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f0000ee00000000000000,HORIPAD mini4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000250900000017000000000000,HRAP2 on PS/SS/N64 Joypad to USB BOX,a:b2,b:b1,back:b9,leftshoulder:b5,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b6,start:b8,x:b3,y:b0,platform:Windows, 030000008f0e00001330000000000000,HuiJia SNES Controller,a:b2,b:b1,back:b8,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b6,rightshoulder:b7,start:b9,x:b3,y:b0,platform:Windows, 03000000d81d00000f00000000000000,iBUFFALO BSGP1204 Series,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000d81d00001000000000000000,iBUFFALO BSGP1204P Series,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000830500006020000000000000,iBuffalo SNES Controller,a:b1,b:b0,back:b6,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b4,rightshoulder:b5,start:b7,x:b3,y:b2,platform:Windows, 03000000b50700001403000000000000,Impact Black,a:b2,b:b3,back:b8,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b1,platform:Windows, 030000006f0e00002401000000000000,INJUSTICE FightStick PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 03000000ac0500002c02000000000000,IPEGA,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b8,leftstick:b13,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b9,rightstick:b14,righttrigger:b7,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000491900000204000000000000,Ipega PG-9023,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000491900000304000000000000,Ipega PG-9087 - Bluetooth Gamepad,+righty:+a5,-righty:-a4,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,start:b11,x:b3,y:b4,platform:Windows, 030000006e0500000a20000000000000,JC-DUX60 ELECOM MMO Gamepad,a:b2,b:b3,back:b17,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b8,leftstick:b14,lefttrigger:b12,leftx:a0,lefty:a1,rightshoulder:b11,rightstick:b15,righttrigger:b13,rightx:a3,righty:a4,start:b20,x:b0,y:b1,platform:Windows, 030000006e0500000520000000000000,JC-P301U,a:b2,b:b3,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:b7,rightx:a2,righty:a3,start:b11,x:b0,y:b1,platform:Windows, 030000006e0500000320000000000000,JC-U3613M (DInput),a:b2,b:b3,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b8,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:b7,rightx:a2,righty:a3,start:b11,x:b0,y:b1,platform:Windows, 030000006e0500000720000000000000,JC-W01U,a:b2,b:b3,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b1,platform:Windows, 030000007e0500000620000000000000,Joy-Con (L),+leftx:h0.2,+lefty:h0.4,-leftx:h0.8,-lefty:h0.1,a:b0,b:b1,back:b13,leftshoulder:b4,leftstick:b10,rightshoulder:b5,start:b8,x:b2,y:b3,platform:Windows, 030000007e0500000620000001000000,Joy-Con (L),+leftx:h0.2,+lefty:h0.4,-leftx:h0.8,-lefty:h0.1,a:b0,b:b1,back:b13,leftshoulder:b4,leftstick:b10,rightshoulder:b5,start:b8,x:b2,y:b3,platform:Windows, 030000007e0500000720000000000000,Joy-Con (R),+leftx:h0.2,+lefty:h0.4,-leftx:h0.8,-lefty:h0.1,a:b0,b:b1,back:b12,leftshoulder:b4,leftstick:b11,rightshoulder:b5,start:b9,x:b2,y:b3,platform:Windows, 030000007e0500000720000001000000,Joy-Con (R),+leftx:h0.2,+lefty:h0.4,-leftx:h0.8,-lefty:h0.1,a:b0,b:b1,back:b12,leftshoulder:b4,leftstick:b11,rightshoulder:b5,start:b9,x:b2,y:b3,platform:Windows, 03000000bd12000003c0000010010000,Joypad Alpha Shock,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000bd12000003c0000000000000,JY-P70UR,a:b1,b:b0,back:b5,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b7,leftx:a0,lefty:a1,rightshoulder:b8,rightstick:b11,righttrigger:b9,rightx:a3,righty:a2,start:b4,x:b3,y:b2,platform:Windows, 03000000242f00002d00000000000000,JYS Wireless Adapter,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000242f00008a00000000000000,JYS Wireless Adapter,a:b1,b:b4,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b0,y:b3,platform:Windows, 03000000790000000200000000000000,King PS3 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b3,y:b0,platform:Windows, 030000006d040000d1ca000000000000,Logitech ChillStream,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006d040000d2ca000000000000,Logitech Cordless Precision,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006d04000011c2000000000000,Logitech Cordless Wingman,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b9,leftstick:b5,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b10,rightstick:b2,righttrigger:b7,rightx:a3,righty:a4,x:b4,platform:Windows, 030000006d04000016c2000000000000,Logitech Dual Action,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006d04000018c2000000000000,Logitech F510 Gamepad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006d04000019c2000000000000,Logitech F710 Gamepad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006d0400001ac2000000000000,Logitech Precision Gamepad,a:b1,b:b2,back:b8,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000006d0400000ac2000000000000,Logitech WingMan RumblePad,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,lefttrigger:b7,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b2,rightx:a3,righty:a4,x:b3,y:b4,platform:Windows, 03000000380700006652000000000000,Mad Catz C.T.R.L.R,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000380700005032000000000000,Mad Catz FightPad PRO (PS3),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000380700005082000000000000,Mad Catz FightPad PRO (PS4),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008433000000000000,Mad Catz FightStick TE S+ (PS3),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008483000000000000,Mad Catz FightStick TE S+ (PS4),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008134000000000000,Mad Catz FightStick TE2+ PS3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b7,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b4,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008184000000000000,Mad Catz FightStick TE2+ PS4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b5,leftstick:b10,lefttrigger:a4,leftx:a0,lefty:a1,rightshoulder:b4,rightstick:b11,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000380700006252000000000000,Mad Catz Micro C.T.R.L.R,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008034000000000000,Mad Catz TE2 PS3 Fightstick,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008084000000000000,Mad Catz TE2 PS4 Fightstick,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000380700008532000000000000,Madcatz Arcade Fightstick TE S PS3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000380700003888000000000000,Madcatz Arcade Fightstick TE S+ PS3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000380700001888000000000000,MadCatz SFIV FightStick PS3,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b5,lefttrigger:b7,leftx:a0,lefty:a1,rightshoulder:b4,righttrigger:b6,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000380700008081000000000000,MADCATZ SFV Arcade FightStick Alpha PS4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000002a0600001024000000000000,Matricom,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a4,start:b9,x:b2,y:b3,platform:Windows, 030000009f000000adbb000000000000,MaxJoypad Virtual Controller,a:b1,b:b2,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b8,x:b3,y:b0,platform:Windows, 03000000250900000128000000000000,Mayflash Arcade Stick,a:b1,b:b2,back:b8,leftshoulder:b0,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b3,righttrigger:b7,start:b9,x:b5,y:b6,platform:Windows, 03000000790000004418000000000000,Mayflash GameCube Controller,a:b1,b:b2,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:a4,rightx:a5,righty:a2,start:b9,x:b0,y:b3,platform:Windows, 03000000790000004318000000000000,Mayflash GameCube Controller Adapter,a:b1,b:b2,back:b0,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b0,leftshoulder:b4,leftstick:b0,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b0,righttrigger:a4,rightx:a5,righty:a2,start:b9,x:b0,y:b3,platform:Windows, 03000000242f00007300000000000000,Mayflash Magic NS,a:b1,b:b4,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b0,y:b3,platform:Windows, 0300000079000000d218000000000000,Mayflash Magic NS,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000d620000010a7000000000000,Mayflash Magic NS,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000008f0e00001030000000000000,Mayflash USB Adapter for original Sega Saturn controller,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,lefttrigger:b5,rightshoulder:b2,righttrigger:b7,start:b9,x:b3,y:b4,platform:Windows, 0300000025090000e803000000000000,Mayflash Wii Classic Controller,a:b1,b:b0,back:b8,dpdown:b13,dpleft:b12,dpright:b14,dpup:b11,guide:b10,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b2,platform:Windows, 03000000790000000018000000000000,Mayflash WiiU Pro Game Controller Adapter (DInput),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000790000002418000000000000,Mega Drive,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,rightshoulder:b2,start:b9,x:b3,y:b4,platform:Windows, 03000000380700006382000000000000,MLG GamePad PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000c62400002a89000000000000,MOGA XP5-A Plus,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b15,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000c62400002b89000000000000,MOGA XP5-A Plus,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000c62400001a89000000000000,MOGA XP5-X Plus,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000c62400001b89000000000000,MOGA XP5-X Plus,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000efbe0000edfe000000000000,Monect Virtual Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a5,rightx:a3,righty:a4,start:b9,x:b3,y:b0,platform:Windows, 03000000250900006688000000000000,MP-8866 Super Dual Box,a:b2,b:b1,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b8,x:b3,y:b0,platform:Windows, 030000006b140000010c000000000000,NACON GC-400ES,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000921200004b46000000000000,NES 2-port Adapter,a:b1,b:b0,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,start:b11,platform:Windows, 03000000790000004518000000000000,NEXILUX GAMECUBE Controller Adapter,platform:Windows,a:b1,b:b0,x:b2,y:b3,start:b9,rightshoulder:b7,dpup:h0.1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,leftx:a0,lefty:a1,rightx:a5,righty:a2,lefttrigger:a3,righttrigger:a4, 030000001008000001e5000000000000,NEXT SNES Controller,a:b2,b:b1,back:b8,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b4,rightshoulder:b5,righttrigger:b6,start:b9,x:b3,y:b0,platform:Windows, 03000000152000000182000000000000,NGDS,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a4,start:b9,x:b3,y:b0,platform:Windows, 03000000bd12000015d0000000000000,Nintendo Retrolink USB Super SNES Classic Controller,a:b2,b:b1,back:b8,leftshoulder:b4,leftx:a0,lefty:a1,rightshoulder:b5,start:b9,x:b3,y:b0,platform:Windows, 030000007e0500000920000000000000,Nintendo Switch Pro Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000000d0500000308000000000000,Nostromo N45,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b9,leftshoulder:b4,leftstick:b12,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b10,x:b2,y:b3,platform:Windows, 03000000550900001472000000000000,NVIDIA Controller v01.04,a:b11,b:b10,back:b13,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b7,leftstick:b5,lefttrigger:a4,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b4,righttrigger:a5,rightx:a3,righty:a6,start:b3,x:b9,y:b8,platform:Windows, 030000004b120000014d000000000000,NYKO AIRFLO,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:a3,leftstick:a0,lefttrigger:b6,rightshoulder:b5,rightstick:a2,righttrigger:b7,start:b9,x:b2,y:b3,platform:Windows, 03000000d620000013a7000000000000,NSW wired controller,platform:Windows,a:b1,b:b2,x:b0,y:b3,back:b8,guide:b12,start:b9,leftstick:b10,rightstick:b11,leftshoulder:b4,rightshoulder:b5,dpup:h0.1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:b6,righttrigger:b7, 03000000782300000a10000000000000,Onlive Wireless Controller,a:b15,b:b14,back:b7,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b5,leftshoulder:b11,leftstick:b9,lefttrigger:a2,leftx:a0,lefty:a1,rightshoulder:b10,rightstick:b8,righttrigger:a5,rightx:a3,righty:a4,start:b6,x:b13,y:b12,platform:Windows, 03000000d62000006d57000000000000,OPP PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006b14000001a1000000000000,Orange Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,leftstick:b6,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b7,righttrigger:a4,rightx:a5,righty:a2,start:b9,x:b2,y:b3,platform:Windows, 03000000362800000100000000000000,OUYA Game Controller,a:b0,b:b3,dpdown:b9,dpleft:b10,dpright:b11,dpup:b8,guide:b14,leftshoulder:b4,leftstick:b6,lefttrigger:a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b7,righttrigger:b13,rightx:a3,righty:a4,x:b1,y:b2,platform:Windows, 03000000120c0000f60e000000000000,P4 Wired Gamepad,a:b1,b:b2,back:b12,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b8,leftshoulder:b5,lefttrigger:b7,rightshoulder:b4,righttrigger:b6,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00000901000000000000,PDP Versus Fighting Pad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000008f0e00000300000000000000,Piranha xtreme,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a3,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 030000004c050000da0c000000000000,PlayStation Classic Controller,a:b2,b:b1,back:b8,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b6,lefttrigger:b4,rightshoulder:b7,righttrigger:b5,start:b9,x:b3,y:b0,platform:Windows, 030000004c0500003713000000000000,PlayStation Vita,a:b1,b:b2,back:b8,dpdown:b13,dpleft:b15,dpright:b14,dpup:b12,leftshoulder:b4,leftx:a0,lefty:a1,rightshoulder:b5,rightx:a3,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000d62000006dca000000000000,PowerA Pro Ex,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000d62000009557000000000000,Pro Elite PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000d62000009f31000000000000,Pro Ex mini PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000d6200000c757000000000000,Pro Ex mini PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000632500002306000000000000,PS Controller,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a2,righty:a3,start:b11,x:b3,y:b4,platform:Windows, 03000000e30500009605000000000000,PS to USB convert cable,a:b2,b:b1,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b8,x:b3,y:b0,platform:Windows, 03000000100800000100000000000000,PS1 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a3,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 030000008f0e00007530000000000000,PS1 Controller,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b1,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000100800000300000000000000,PS2 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a4,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 03000000250900008888000000000000,PS2 Controller,a:b2,b:b1,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b8,x:b3,y:b0,platform:Windows, 03000000666600006706000000000000,PS2 Controller,a:b2,b:b1,back:b8,dpdown:b14,dpleft:b15,dpright:b13,dpup:b12,leftshoulder:b6,leftstick:b9,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b10,righttrigger:b5,rightx:a2,righty:a3,start:b11,x:b3,y:b0,platform:Windows, 030000006b1400000303000000000000,PS2 Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000009d0d00001330000000000000,PS2 Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000250900000500000000000000,PS3 Controller,a:b2,b:b1,back:b9,dpdown:h0.8,dpleft:h0.4,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b8,x:b0,y:b3,platform:Windows, 030000004c0500006802000000000000,PS3 Controller,a:b2,b:b1,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b10,lefttrigger:a3~,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:a4~,rightx:a2,righty:a5,start:b8,x:b3,y:b0,platform:Windows, 03000000632500007505000000000000,PS3 Controller,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000888800000803000000000000,PS3 Controller,a:b2,b:b1,back:b8,dpdown:h0.8,dpleft:h0.4,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b9,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b10,righttrigger:b7,rightx:a3,righty:a4,start:b11,x:b0,y:b3,platform:Windows, 030000008f0e00001431000000000000,PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000003807000056a8000000000000,PS3 RF pad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000100000008200000000000000,PS360+ v1.66,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:h0.4,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000004c050000a00b000000000000,PS4 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000004c050000c405000000000000,PS4 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000004c050000cc09000000000000,PS4 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000004c050000e60c000000000000,PS5 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,misc1:b13,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000ff000000cb01000000000000,PSP,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftx:a0,lefty:a1,rightshoulder:b5,start:b7,x:b2,y:b3,platform:Windows, 03000000300f00000011000000000000,QanBa Arcade JoyStick 1008,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,start:b10,x:b0,y:b3,platform:Windows, 03000000300f00001611000000000000,QanBa Arcade JoyStick 4018,a:b1,b:b2,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b9,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b8,x:b0,y:b3,platform:Windows, 03000000222c00000020000000000000,QANBA DRONE ARCADE JOYSTICK,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:a3,rightshoulder:b5,righttrigger:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000300f00001210000000000000,QanBa Joystick Plus,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,rightshoulder:b5,start:b9,x:b2,y:b3,platform:Windows, 03000000341a00000104000000000000,QanBa Joystick Q4RAF,a:b5,b:b6,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b0,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b3,righttrigger:b7,start:b9,x:b1,y:b2,platform:Windows, 03000000222c00000223000000000000,Qanba Obsidian Arcade Joystick PS3 Mode,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000222c00000023000000000000,Qanba Obsidian Arcade Joystick PS4 Mode,a:b1,b:b2,back:b13,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000321500000003000000000000,Razer Hydra,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:a2,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 03000000321500000204000000000000,Razer Panthera (PS3),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000321500000104000000000000,Razer Panthera (PS4),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000321500000507000000000000,Razer Raiju Mobile,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000321500000707000000000000,Razer Raiju Mobile,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000321500000011000000000000,Razer Raion Fightpad for PS4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000321500000009000000000000,Razer Serval,+lefty:+a2,-lefty:-a1,a:b0,b:b1,back:b12,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,leftstick:b8,leftx:a0,rightshoulder:b5,rightstick:b9,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 030000000d0f00001100000000000000,REAL ARCADE PRO.3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,rightshoulder:b5,rightstick:b11,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00006a00000000000000,Real Arcade Pro.4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00006b00000000000000,Real Arcade Pro.4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00008a00000000000000,Real Arcade Pro.4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00008b00000000000000,Real Arcade Pro.4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00007000000000000000,REAL ARCADE PRO.4 VLX,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,rightshoulder:b5,rightstick:b11,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00002200000000000000,REAL ARCADE Pro.V3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00005b00000000000000,Real Arcade Pro.V4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000000d0f00005c00000000000000,Real Arcade Pro.V4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000790000001100000000000000,Retrolink SNES Controller,a:b2,b:b1,back:b8,dpdown:+a4,dpleft:-a3,dpright:+a3,dpup:-a4,leftshoulder:b4,rightshoulder:b5,start:b9,x:b3,y:b0,platform:Windows, 03000000bd12000013d0000000000000,Retrolink USB SEGA Saturn Classic,a:b0,b:b1,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b5,lefttrigger:b6,rightshoulder:b2,righttrigger:b7,start:b8,x:b3,y:b4,platform:Windows, 0300000000f000000300000000000000,RetroUSB.com RetroPad,a:b1,b:b5,back:b2,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b3,x:b0,y:b4,platform:Windows, 0300000000f00000f100000000000000,RetroUSB.com Super RetroPort,a:b1,b:b5,back:b2,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,start:b3,x:b0,y:b4,platform:Windows, 030000006b140000010d000000000000,Revolution Pro Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000006b140000020d000000000000,Revolution Pro Controller 2(1/2),a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000006b140000130d000000000000,Revolution Pro Controller 3,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00001e01000000000000,Rock Candy PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00002801000000000000,Rock Candy PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00002f01000000000000,Rock Candy PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000004f04000003d0000000000000,run'n'drive,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b7,leftshoulder:a3,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:a4,rightstick:b11,righttrigger:b5,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 03000000a30600001af5000000000000,Saitek Cyborg,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000a306000023f6000000000000,Saitek Cyborg V.1 Game pad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000300f00001201000000000000,Saitek Dual Analog Pad,a:b2,b:b3,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b1,platform:Windows, 03000000a30600000701000000000000,Saitek P220,a:b2,b:b3,dpdown:+a1,dpleft:-a0,dpright:+a0,dpup:-a1,leftshoulder:b6,lefttrigger:b7,rightshoulder:b4,righttrigger:b5,x:b0,y:b1,platform:Windows, 03000000a30600000cff000000000000,Saitek P2500 Force Rumble Pad,a:b2,b:b3,back:b11,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:b7,rightx:a2,righty:a3,start:b10,x:b0,y:b1,platform:Windows, 03000000a30600000c04000000000000,Saitek P2900,a:b1,b:b2,back:b12,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b8,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b3,platform:Windows, 03000000300f00001001000000000000,Saitek P480 Rumble Pad,a:b2,b:b3,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b1,platform:Windows, 03000000a30600000b04000000000000,Saitek P990,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b3,platform:Windows, 03000000a30600000b04000000010000,Saitek P990 Dual Analog Pad,a:b1,b:b2,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b8,x:b0,y:b3,platform:Windows, 03000000a30600002106000000000000,Saitek PS1000,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000a306000020f6000000000000,Saitek PS2700,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b0,y:b3,platform:Windows, 03000000300f00001101000000000000,Saitek Rumble Pad,a:b2,b:b3,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b8,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b1,platform:Windows, 03000000730700000401000000000000,Sanwa PlayOnline Mobile,a:b0,b:b1,back:b2,leftx:a0,lefty:a1,start:b3,platform:Windows, 0300000000050000289b000000000000,Saturn_Adapter_2.0,a:b1,b:b2,leftshoulder:b6,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b5,start:b9,x:b0,y:b3,platform:Windows, 030000009b2800000500000000000000,Saturn_Adapter_2.0,a:b1,b:b2,leftshoulder:b6,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b5,start:b9,x:b0,y:b3,platform:Windows, 03000000a30c00002500000000000000,Sega Genesis Mini 3B controller,a:b2,b:b1,dpdown:+a4,dpleft:-a3,dpright:+a3,dpup:-a4,righttrigger:b5,start:b9,platform:Windows, 03000000a30c00002400000000000000,Sega Mega Drive Mini 6B controller,a:b2,b:b1,dpdown:+a4,dpleft:-a3,dpright:+a3,dpup:-a4,rightshoulder:b4,righttrigger:b5,start:b9,x:b3,y:b0,platform:Windows, 03000000341a00000208000000000000,SL-6555-SBK,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:-a4,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:a4,rightx:a3,righty:a2,start:b7,x:b2,y:b3,platform:Windows, 03000000341a00000908000000000000,SL-6566,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000008f0e00000800000000000000,SpeedLink Strike FX,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000c01100000591000000000000,Speedlink Torid,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000d11800000094000000000000,Stadia Controller,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,leftstick:b6,lefttrigger:b12,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b7,righttrigger:b11,rightx:a3,righty:a4,start:b9,x:b2,y:b3,platform:Windows, 03000000110100001914000000000000,SteelSeries,a:b0,b:b1,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftstick:b13,lefttrigger:b6,leftx:a0,lefty:a1,rightstick:b14,righttrigger:b7,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000381000001214000000000000,SteelSeries Free,a:b0,b:b1,back:b12,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftx:a0,lefty:a1,rightshoulder:b7,rightx:a2,righty:a3,start:b11,x:b3,y:b4,platform:Windows, 03000000110100003114000000000000,SteelSeries Stratus Duo,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000381000001814000000000000,SteelSeries Stratus XL,a:b0,b:b1,back:b18,dpdown:b13,dpleft:b14,dpright:b15,dpup:b12,guide:b19,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b2,y:b3,platform:Windows, 03000000790000001c18000000000000,STK-7024X,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000ff1100003133000000000000,SVEN X-PAD,a:b2,b:b3,back:b4,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,righttrigger:b9,rightx:a2,righty:a4,start:b5,x:b0,y:b1,platform:Windows, 03000000d620000011a7000000000000,Switch,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000457500002211000000000000,SZMY-POWER PC Gamepad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000004f04000007d0000000000000,T Mini Wireless,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000004f0400000ab1000000000000,T.16000M,a:b0,b:b1,back:b12,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b11,leftshoulder:b4,lefttrigger:b9,leftx:a0,lefty:a1,rightshoulder:b6,righttrigger:b7,start:b10,x:b2,y:b3,platform:Windows, 03000000fa1900000706000000000000,Team 5,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000b50700001203000000000000,Techmobility X6-38V,a:b2,b:b3,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b0,y:b1,platform:Windows, 030000004f04000015b3000000000000,Thrustmaster Dual Analog 4,a:b0,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b1,y:b3,platform:Windows, 030000004f04000023b3000000000000,Thrustmaster Dual Trigger 3-in-1,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000004f0400000ed0000000000000,ThrustMaster eSwap PRO Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 030000004f04000000b3000000000000,Thrustmaster Firestorm Dual Power,a:b0,b:b2,back:b9,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b8,leftshoulder:b4,leftstick:b11,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b12,righttrigger:b7,rightx:a2,righty:a3,start:b10,x:b1,y:b3,platform:Windows, 030000004f04000004b3000000000000,Thrustmaster Firestorm Dual Power 3,a:b0,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b5,leftx:a0,lefty:a1,rightshoulder:b6,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b1,y:b3,platform:Windows, 03000000666600000488000000000000,TigerGame PS/PS2 Game Controller Adapter,a:b2,b:b1,back:b9,dpdown:b14,dpleft:b15,dpright:b13,dpup:b12,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a2,righty:a3,start:b8,x:b3,y:b0,platform:Windows, 03000000d62000006000000000000000,Tournament PS3 Controller,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 030000005f140000c501000000000000,Trust Gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000b80500000210000000000000,Trust Gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 030000004f04000087b6000000000000,TWCS Throttle,dpdown:b8,dpleft:b9,dpright:b7,dpup:b6,leftstick:b5,lefttrigger:-a5,leftx:a0,lefty:a1,righttrigger:+a5,platform:Windows, 03000000d90400000200000000000000,TwinShock PS2,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a3,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 030000006e0500001320000000000000,U4113,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000101c0000171c000000000000,uRage Gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000300f00000701000000000000,USB 4-Axis 12-Button Gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a3,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 03000000341a00002308000000000000,USB gamepad,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 030000005509000000b4000000000000,USB gamepad,a:b10,b:b11,back:b5,dpdown:b1,dpleft:b2,dpright:b3,dpup:b0,guide:b14,leftshoulder:b8,leftstick:b6,lefttrigger:a4,leftx:a0,lefty:a1,rightshoulder:b9,rightstick:b7,righttrigger:a5,rightx:a2,righty:a3,start:b4,x:b12,y:b13,platform:Windows, 030000006b1400000203000000000000,USB gamepad,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000790000000a00000000000000,USB gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a4,start:b9,x:b3,y:b0,platform:Windows, 03000000f0250000c183000000000000,USB gamepad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000ff1100004133000000000000,USB gamepad,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b6,leftstick:b10,lefttrigger:b4,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b11,righttrigger:b5,rightx:a4,righty:a2,start:b9,x:b3,y:b0,platform:Windows, 03000000632500002305000000000000,USB Vibration Joystick (BM),a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000790000001a18000000000000,Venom,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b0,y:b3,platform:Windows, 03000000790000001b18000000000000,Venom Arcade Joystick,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00000302000000000000,Victrix Pro Fight Stick for PS4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 030000006f0e00000702000000000000,Victrix Pro Fight Stick for PS4,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,rightshoulder:b5,righttrigger:b7,start:b9,x:b0,y:b3,platform:Windows, 0300000034120000adbe000000000000,vJoy Device,a:b0,b:b1,back:b15,dpdown:b6,dpleft:b7,dpright:b8,dpup:b5,guide:b16,leftshoulder:b9,leftstick:b13,lefttrigger:b11,leftx:a0,lefty:a1,rightshoulder:b10,rightstick:b14,righttrigger:b12,rightx:a3,righty:a4,start:b4,x:b2,y:b3,platform:Windows, 030000005e0400000a0b000000000000,Xbox Adaptive Controller,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b8,lefttrigger:+a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:-a2,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 030000005e040000130b000000000000,Xbox Series Controller,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,leftstick:b8,lefttrigger:a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:a5,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 03000000341a00000608000000000000,Xeox,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000450c00002043000000000000,XEOX Gamepad SL-6556-BK,a:b0,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b2,y:b3,platform:Windows, 03000000ac0500005b05000000000000,Xiaoji Gamesir-G3w,a:b2,b:b1,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:b6,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:b7,rightx:a2,righty:a3,start:b9,x:b3,y:b0,platform:Windows, 03000000172700004431000000000000,XiaoMi Game Controller,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b20,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:a7,rightx:a2,righty:a5,start:b11,x:b3,y:b4,platform:Windows, 03000000786901006e70000000000000,XInput Controller,a:b0,b:b1,back:b6,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b10,leftshoulder:b4,leftstick:b8,lefttrigger:a2,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b9,righttrigger:a5,rightx:a3,righty:a4,start:b7,x:b2,y:b3,platform:Windows, 03000000790000004f18000000000000,ZD-T Android,a:b0,b:b1,back:b10,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b6,leftstick:b13,lefttrigger:b8,leftx:a0,lefty:a1,rightshoulder:b7,rightstick:b14,righttrigger:b9,rightx:a3,righty:a4,start:b11,x:b3,y:b4,platform:Windows, 03000000120c0000101e000000000000,ZEROPLUS P4 Wired Gamepad,a:b1,b:b2,back:b8,dpdown:h0.4,dpleft:h0.8,dpright:h0.2,dpup:h0.1,guide:b12,leftshoulder:b4,leftstick:b10,lefttrigger:a3,leftx:a0,lefty:a1,rightshoulder:b5,rightstick:b11,righttrigger:a4,rightx:a2,righty:a5,start:b9,x:b0,y:b3,platform:Windows, 78696e70757401000000000000000000,XInput Gamepad (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, 78696e70757402000000000000000000,XInput Wheel (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, 78696e70757403000000000000000000,XInput Arcade Stick (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, 78696e70757404000000000000000000,XInput Flight Stick (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, 78696e70757405000000000000000000,XInput Dance Pad (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, 78696e70757406000000000000000000,XInput Guitar (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, 78696e70757408000000000000000000,XInput Drum Kit (GLFW),platform:Windows,a:b0,b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8, @UATAUAVAWH冹 3鞰c馤嬮Mc鳧嬚H��  L嬧H�
�   9ㄠ��t49(t-9�   t9ˊ  tH�  A兟H;羱针A兟�	A兟�A�翧凓~3繦兡 A_A^A]A\]肐c翴嬒H塡$P�   H塼$XH墊$`H�4繦��  H伶H鹎   �    Hc|$p�   H塅I�捐    �   H塅H嬒�    H峃4H塅(A�   D墌I嬚D塿 墌0�    A�    H崕�   I嬙�    H崕�   �    H媩$`L嬓H媆$PH吚tyL嬐H崘�   怐禕�A���   �
灵;N0}WA��u�;F}II�罤兟I凒~薒嬇I崐�   �禥���uS�凌;F0}��u�;F}I�繦兞I凐~襂嬯H壆�   H嬈H媡$XH兡 A_A^A]A\]肁��u��;F 電��u��;F 攵   p   )   p   �   p   �   x   �   x   �   x   �   c     c     }      �     8 G            �     �  }        �_glfwAllocJoystick 
 >@   name  AJ          AU       �h  � 
 >@   guid  AK        &  AT  &     �Z  �  >t    axisCount  AW       �a  �  Ah          >t    buttonCount  AV       �i  �  Ai          >t    hatCount  AM  �     j  EO  (           Dp    >t     jid  Aj       �  >�    js  AL  �     ; ; M        p  �
&
 

 Z   n   >    mapping  AR  +    � " M        o  

�5	t N" M        o  

乽	
H N N Z   G  G  G  �  �               (          B  h   o  p   P   @  Oname  X   @  Oguid  `   t   OaxisCount  h   t   ObuttonCount  p   t   OhatCount  O�   �           �  �     �       � �   � �0     �L   � �]     �l    �r    �t    ��    ��   	 ��   
 ��    ��    �   �   ��   ��   ��   �,   u   0   u  
 ]   u   a   u  
 m   u   q   u  
 �   u   �   u  
 �   u   �   u  
 �   u   �   u  
 �   u   �   u  
   u     u  
 &  u   *  u  
 I  u   M  u  
 ]  u   a  u  
 �  u   �  u  
 �  u   �  u  
   u     u  
 0  u   4  u  
 @SH冹 L岲$8H嬞H峊$@�@  fnT$8H嬎fnL$@�嬉�嫔�Y    �Y
    �H   H兡 [�   p   4   �   <   �   B   p      �   8  D G            L      F           �_glfwCenterCursorInContentArea  >+   window  AI       =  AJ          >t     width  B@        3  >t     height  B8        3                        B  0   +  Owindow  @   t   Owidth  8   t   Oheight  9       �   9@       }   O�   8           L   �     ,       $ �   ' �   ( �F   ) �,   w   0   w  
 k   w   o   w  
 {   w      w  
 �   w   �   w  
 �   w   �   w  
 $  w   (  w  
 4  w   8  w  
 L  w   P  w  
 @SH冹 H嬞H婭�    H婯�    H婯(�    3褹�   H嬎H兡 [�       z      z       z   5   �      �   �   7 G            9      /   ~        �_glfwFreeJoystick  >�   js  AI  	     +  AJ        	  Z   I  I  I                         B  0   �  Ojs  O   �   P           9   �     D        �	    �    �    �$    �/    �4    �,   v   0   v  
 Z   v   ^   v  
 j   v   n   v  
 �   v   �   v  
 H塡$WH冹 乎   岼x�    H��  H�=    3踗�     Hc
�  H�;Hi甚   H辱    吚t��  H兠H侞h  s	H��  肫H媆$0H兡 _�   x      p   !   	    3   p   F      P   p   d   p      �   �   > G            u   
   j   _        �_glfwInitGamepadMappings  Z   G  q   >d  _glfw  CH  �	  h       CH �	  0                             B  O  �   H           u   �     <       � �
   � �0   � �N   � �T   � �j   � �,   t   0   t  
 x   t   |   t  
 �   t   �   t  
 �   t   �   t  
 H塡$H塼$WH冹 岯郃嬹E嬋孃H嬞凐^v侜�   r4H媮P  H吚tA冟蟽箞    EE�袇鰐H媰H  H吚t嬜H嬎�蠬媆$0H媡$8H兡 _�   �   �  4 G            n      ^   s        �_glfwInputChar  >+   window  AI       F  AJ          >u    codepoint  A           A        S 
 >t    mods  Ah          Ai       /  Ai G     '  >t    plain  A        S  Ai                                B  0   +  Owindow  8   u   Ocodepoint  @   t   Omods  H   t   Oplain  9E       \   9\       Y   O�   X           n   �     L       6 �   ; �*   A �E   B �G   D �K   F �W   G �^   I �,   i   0   i  
 [   i   _   i  
 k   i   o   i  
 �   i   �   i  
 �   i   �   i  
 �   i   �   i  
 �   i   �   i  
 �   i   �   i  
 �   i     i  
   i     i  
 �  i   �  i  
 �  i   �  i  
 �  i   �  i  
 H媮0  H吚tH�嗝   �   �   ; G                      w        �_glfwInputCursorEnter  >+   window  AJ          >t    entered  A                                  B     +  Owindow     t   Oentered  9       J   O   �   8              �     ,       � �    � �   � �   � �,   m   0   m  
 b   m   f   m  
 �   m   �   m  
 �   m   �   m  
 �   m   �   m  
 �侙  f.羫u��   f.聑tH媮(  �夬  ��   H吚tH�嗝   �     9 G            @       ?   v        �_glfwInputCursorPos  >+   window  AJ        @ 
 >A    xpos  A�         @ 
 >A    ypos  A�         @                         B     +  Owindow     A   Oxpos     A   Oypos  9<       S   O �   @           @   �     4       v �    } �    � �<   � �?   � �,   l   0   l  
 `   l   d   l  
    l   �   l  
 �   l   �   l  
   l     l  
   l      l  
 H媮X  H吚tH�嗝   �     4 G                      x        �_glfwInputDrop  >+   window  AJ          >t    count  A           >]   paths  AP                                 B     +  Owindow     t   Ocount     ]  Opaths  9       `   O  �   8              �     ,       � �    � �   � �   � �,   n   0   n  
 [   n   _   n  
 {   n      n  
 �   n   �   n  
   n     n  
   n      n  
 D嬄侜  u	茿   �A侙  u茿    L�
p  M吷t.H��  H+菻�9庛8庛8H鏖H嬍A嬓H六H嬃H凌?H菼�崦'   p   3   p      �     8 G            _       ^   y        �_glfwInputJoystick  >�   js  AJ        _ : $  >t    event  A           Ah       \  >d  _glfw  CQ  p  +     4                         B     �  Ojs     t   Oevent  9[       ]   O  �   X           _   �     L       � �   � �   � �   � �   � �$   � �0   � �^   � �,   o   0   o  
 [   o   _   o  
    o   �   o  
 �   o   �   o  
 �   o   �   o  
   o     o  
   o      o  
 H婣Hc殷惷   �   �   < G            
          z        �_glfwInputJoystickAxis  >�   js  AJ        
 
 >t    axis  A           >@    value  A�         
                         B     �  Ojs     t   Oaxis     @   Ovalue  O�   0           
   �     $       � �    � �   � �,   p   0   p  
 _   p   c   p  
 ~   p   �   p  
 �   p   �   p  
   p     p  
 H婣Hc褼��   �   �   > G                      {        �_glfwInputJoystickButton  >�   js  AJ          >t    button  A           >p    value  AX                                 B     �  Ojs     t   Obutton     p   Ovalue  O  �   0              �     $       � �    � �   � �,   q   0   q  
 a   q   e   q  
 �   q   �   q  
 �   q   �   q  
   q     q  
 H塡$婣 L嬞A矩D嬅A谚D�怉��H婣Mc袲端A��E�D嬎H婣嬎灵��A灵A��F圖I婥B圠I婥Hc蔉圠I婥(�H媆$�   �   0  ; G            n      h   |        �_glfwInputJoystickHat  >�   js  AJ          AS       c  >t    hat  A         n  >p    value  A        ^  AX         
 >t     base  Ai                                B     �  Ojs     t   Ohat     p   Ovalue  O�   P           n   �     D       � �   � �   � �7   � �?   � �Y   � �h   � �,   r   0   r  
 ^   r   b   r  
 n   r   r   r  
 �   r   �   r  
 �   r   �   r  
 �   r   �   r  
 �   r   �   r  
 D  r   H  r  
 D媆$(侜\  wOE3襀c翬吷uD8��   t^D9憖   t苿�   �'A凒u
D8��   uE嬔D垖�   E呉�   DE萀嫅@  M呉tA嬅冟蟽箞    AE脡D$(I�饷   �   �  3 G            �       �   r        �_glfwInputKey  >+   window  AJ        �  >t    key  A         �  >t    scancode  Ah        �  >t    action  Ai        � 
 >t    mods  D(    EO  (           >t     repeated  A        5  Aj         A  H     9   #   Aj H                              B     +  Owindow     t   Okey     t   Oscancode      t   Oaction  (   t   Omods  9}       V   O  �   x           �   �     l        �    �
    �    �"   " �+   # �5    �E     �H   ' �\   . �y   / ��   0 �,   h   0   h  
 Z   h   ^   h  
 x   h   |   h  
 �   h   �   h  
 �   h   �   h  
 �   h   �   h  
 
  h     h  
   h     h  
 *  h   .  h  
 B  h   F  h  
 �  h   �  h  
 �  h   �  h  
 Lc褹嬃A凓w=A冡蟽箞    DE菶吚u�D9亜   uA缎A垟
�   H媮   H吚tA嬕H�嗝   �   �  : G            J       I   u        �_glfwInputMouseClick  >+   window  AJ        J  >t    button  A           AR       G  >t    action  Ah          Ai         Ah I      
 >t    mods  A        8  Ai          A  I                              B     +  Owindow     t   Obutton     t   Oaction      t   Omods  9F       P   O  �   X           J   �     L       \ �   c �   i �    j �+   l �/   n �C   o �I   p �,   k   0   k  
 a   k   e   k  
 �   k   �   k  
 �   k   �   k  
 �   k   �   k  
 �   k   �   k  
 �   k   �   k  
 �   k   �   k  
   k     k  
   k     k  
 �  k   �  k  
 �  k   �  k  
 H媮8  H吚tH�嗝   �     6 G                      t        �_glfwInputScroll  >+   window  AJ          >A    xoffset  A�           >A    yoffset  A�                                  B     +  Owindow     A   Oxoffset     A   Oyoffset  9       S   O�   8              �     ,       N �    U �   V �   W �,   j   0   j  
 ]   j   a   j  
    j   �   j  
 �   j   �   j  
   j     j  
 $  j   (  j  
 H塡$H墊$D��  E3蒆孂E呉~SH��  @ f�     Ic罫嬊Li仵   L跧崈�   L+纅D  �B� +製H�绤蓇韰襱A�罞;蕓�3繦媆$H媩$肏媆$I嬅H媩$�
   p      p      �     1 F            �   
   |   n        �findMapping 
 >@   guid  AJ          AM       r d  
 >t     i  Ai       v  >d  _glfw  CI  �	  #     ^ L 
  Cj  �	       y  CI �	  o                              J     @  Oguid  O �   H           �   �     <       G  �
   J  �0   L  �g   J  �o   P  �q   Q  �,   }   0   }  
 V   }   Z   }  
 f   }   j   }  
 �   }   �   }  
 �   }   �   }  
 �   }   �   }  
 �   }   �   }  
   }      }  
 @SH冹 H嬞H伭�   �    L嬓H吚tvE3蒆崘�   E嬃fD  禞���uc�凌;C0}r��u�;C}eI�繦兟I凐~襂崐�   禥���u;�凌;C0}=��u�;C}0I�罤兞I凒~襂嬄H兡 [脌�u��;C 毽��u��;C 胛3繦兡 [�   }      �     6 F            �      �   p        �findValidMapping  >�   js  AI  	     � �   AJ        	  >    mapping  AR       � ! M        o  

	
X N M        o  ^
0 N
 Z   n                         J 
 h   o   0   �  Ojs  O �   �           �   �  
   t       e  �	   f  �   g  �   m  �O   k  �^   s  ��   q  ��   x  ��   y  ��   m  ��   s  ��   t  ��   y  �,   ~   0   ~  
 Y   ~   ]   ~  
 m   ~   q   ~  
 �   ~   �   ~  
 4  ~   8  ~  
 H塴$H塼$WH冹 �=     A嬸嬯H孂u3夜  �    3繦媗$8H媡$@H兡 _脙9 ~f儁 ~`�   H塡$0岼耔    H�
p  D嬑D嬇H嬜H嬝H�H嬋H�p  �h   吚uH嬎�    3繦媆$0H媗$8H媡$@H兡 _肏嬅腈H�    �  �    H媗$83繦媡$@H兡 _�   p   (   s   W   x   ^   p   w   p   }   p   �   D   �   �   �   s      �   �  6 G            �      �   �        �glfwCreateCursor  >�   image  AJ          AM       �   �  
 >t    xhot  A           A        �   ~  
 >t    yhot  A        �   �   Ah          ><    cursor  AI  n     ; &   Z   o  G  \  o                         B  0   �  Oimage  8   t   Oxhot  @   t   Oyhot  9{       �   O �   �           �   �  
   t       E �   K �.   ^ �>   M �I   S �[   T �b   W ��   Y ��   Z ��   ^ ��   ] ��   O ��   ^ �,   B   0   B  
 \   B   `   B  
 l   B   p   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
   B     B  
 �  B   �  B  
 �  B   �  B  
 @WH冹 �=     孂u3夜  �    3繦兡 _脥�燑�凐	vD嬊H�    �  �    3繦兡 _煤   H塡$0岼耔    H�
p  嬜H嬝H�H嬋H�p  �p   吚uH嬎�    H媆$03繦兡 _肏嬅H媆$0H兡 _�   p      s   6   �   @   s   Z   x   a   p   s   p   y   p   �   D      �   �   > G            �      �   �        �glfwCreateStandardCursor  >t    shape  A           A        �   <  �   ><    cursor  AI  j     4 $   Z   o  o  G  \                         B  0   t   Oshape  9w       �   O �   �           �   �     �       a �   d �   � �%   n �0   q �D   r �F   � �L   u �^   v �e   y ��   { ��   | ��   � ��    ��   � �,   C   0   C  
 d   C   h   C  
 t   C   x   C  
 �   C   �   C  
 �   C   �   C  
   C     C  
 @WH冹 �=     H孂u3夜  H兡 _�    H�剷   H塡$0H�x  H呟t=H塼$83鯤9{Xu#3�95    u�  �    �
H嬎H塻X��   H�H呟u螲媡$8H嬒�x   H�
p  H�p  H媆$0H;蟭D  H�H嬔H嬋H;莡騂�H嬒H�H兡 _�    H兡 _�   p      s   4   p   N   p   Z   s   i   p      p   �   p   �   p   �   z      �   N  7 G            �      �   \        �glfwDestroyCursor  >(   handle  AJ          AM       �   �   >+    window  AI  8     ^ 
 >&    prev  AK  �     0  M        �  
J

 Z   o   N Z   o  I                         B 
 h   �   0   (  Ohandle  9g       �   9}       �   O  �   �           �   �     �       � �   � �   � �   � �#   � �1   � �D   � �J   � �m   � �z   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �,   D   0   D  
 ^   D   b   D  
 n   D   r   D  
 �   D   �   D  
 �   D   �   D  
 :  D   >  D  
 J  D   N  D  
 d  D   h  D  
 H冹(�=     u3夜  �    3繦兡(肏兡(H�%�      p      s   '   p      �   �   < G            +          �        �glfwGetClipboardString  >A   handle  AJ        +    D0   
 Z   o   (                      B  0   A  Ohandle  9$       �   O  �   8           +   �     ,       � �   � �   � �$   � �,   \   0   \  
 c   \   g   \  
 �   \   �   \  
 �   \   �   \  
 3繦呉tH�M吚tI� 9    u3夜  �    伖�   @ uH呉t
H媮�  H�M吚tH媮   I� 肏�%@   �   p   "   s   T   p      �     6 G            Y       X   �        �glfwGetCursorPos  >A   handle  AJ        Y !  
 >A   xpos  AK        Y  
 
 >A   ypos  AP        Y 
 Z   o                          B     A  Ohandle     A  Oxpos     A  Oypos  9Q       z   O�   �           Y   �     |        �     �    �
    �    �    �&    �2    �7    �A    �F    �P   " �Q   ! �X   " �,   @   0   @  
 ]   @   a   @  
 �   @   �   @  
 �   @   �   @  
   @     @  
 ,  @   0  @  
 @SH冹 �=     Hc賣3夜  �    3繦兡 [脙�w_�=�   u��   吚u��   3繦兡 [肏�矍�     H零H��  H貎{ t03襀嬎��   吚t!H媰�   H兡 [肈嬅H�    �  �    3繦兡 [�   p      s   -   p   6   p   @   p   R   p   a   p   u   p   �   �   �   s      �   U  8 G            �      �   �        �glfwGetGamepadName  >t    jid  A           AI       �   ;  @ :  AI �       >�    js  AI  h     !  AI �       M        m  +I


 N Z   o  o                         B 
 h   m   0   t   Ojid  94       &   9>       2   9s       �   O   �   �           �   �     �       ? �   E �   [ �&   G �+   M �D   [ �L   P �P   M �Z   P �h   Q �n   T �}   W ��   [ ��   I ��   [ �,   Y   0   Y  
 \   Y   `   Y  
 l   Y   p   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 1  Y   5  Y  
 A  Y   E  Y  
 Q  Y   U  Y  
 l  Y   p  Y  
 H塡$WH冹@W繦c�3繦孃BH塀 9    u3夜  �    3繦媆$`H兡@_脙�嚄  9�  H塴$PH塼$Xu'��   吚u��   3繦媗$PH媡$XH媆$`H兡@_肏�矍�     H零H�-    兗+�   t艸崓�  �   H藡��   吚t獺兗+x   t�)t$0L峅)|$ A�   W蒆媱+x  B秾 �   ��u[F緮 �   F緶 �   B稊 �   H媽+�  fAn耭An�[�[荔Y戵X蠩勔xuE勠/蕇隭/裷SA艫�隠��u)B稊 �   H媱+�  嬍H灵�"述�t%A艫����uB秾 �   H媱+�  �A圛﨟媱+x  B秾 �   ��u[F緮 �   F緶 �   B稊 �   H媽+�  fAn耭An�[�[荔Y戵X蠩勔xuE勠/蕇隭/裷SA艫�隠��u)B稊 �   H媱+�  嬍H灵�"述�t%A艫����uB秾 �   H媱+�  �A圛�H媱+x  A秾 �   ��uZE緮 �   E緶 �   A稊 �   H媽+�  fAn耭An�[�[荔Y戵X蠩勔xuE勠/蕇險/裷PA�隞��u(A稊 �   H媱+�  嬍H灵�"述�t#A����uA秾 �   H媱+�  �A�	H媱+x  B秾 �   ��u[F緮 �   F緶 �   B稊 �   H媽+�  fAn耭An�[�[荔Y戵X蠩勔xuE勠/蕇隭/裷SA艫隠��u)B稊 �   H媱+�  嬍H灵�"述�t%A艫���uB秾 �   H媱+�  �A圛H媱+x  B秾 �   ��u[F緮 �   F緶 �   B稊 �   H媽+�  fAn耭An�[�[荔Y戵X蠩勔xuE勠/蕇隭/裷SA艫隠��u)B稊 �   H媱+�  嬍H灵�"述�t%A艫���uB秾 �   H媱+�  �A圛I兝I兞H冾匑��L媱+x  �=    I伬�   �5    A� <u@A継A禤H媽+�  fn繟継[纅n润Y�[审X�(翔    (舞    隤<u(A禤H媱+�  嬍H灵�"述�t(齐)(请$<u%A禜H媱+�  �fn�[荔X荔\企GL媱+x  I伬�   A� <u@A継A禤H媽+�  fn繟継[纅n润Y�[审X�(翔    (舞    隤<u(A禤H媱+�  嬍H灵�"述�t(齐)(请$<u%A禜H媱+�  �fn�[荔X荔\企GL媱+x  I伬�   A� <u@A継A禤H媽+�  fn繟継[纅n润Y�[审X�(翔    (舞    隤<u(A禤H媱+�  嬍H灵�"述�t(齐)(请$<u%A禜H媱+�  �fn�[荔X荔\企GL媱+x  I伬�   A� <u@A継A禤H媽+�  fn繟継[纅n润Y�[审X�(翔    (舞    隤<u(A禤H媱+�  嬍H灵�"述�t(齐)(请$<u%A禜H媱+�  �fn�[荔X荔\企GL媱+x  I伬�   A� <u@A継A禤H媽+�  fn繟継[纅n润Y�[审X�(翔    (舞    隤<u(A禤H媱+�  嬍H灵�"述�t(齐)(请$<u%A禜H媱+�  �fn�[荔X荔\企G L媱+x  I伬�   A� <u@A継(螦禤H媽+�  fn繟継[纅n畜Y�[殷X妈    (舞    隻<u:A禤H媱+�  嬍H灵�"述�u(黧w$�   (|$ (t$0槭��<u%A禤H媽+�  �fn�[荔X荔\企G$(|$ �   (t$0閸��D嬅H�    �  �    H媆$`3繦兡@_�"   p   0   s   L   p   ^   p   h   p   �   p   �   p   �   p   �  �   �  �   �  {     |   �  {   �  |   T  {   \  |      {     |   �  {   �  |   X  {   `  |   �  �   �  s      �   
  9 G            	  
   �  �        �glfwGetGamepadState  >t    jid  A           AI       �+  ^  w Z AI n       >7   state  AK          AM       �(  j   >�    js  AJ  �       >4    value  A�   5     � � � �� S� > A�  n     s ` � . u. 2- �. �. �
 >
 �
 �
 B
 �  >+    hat  A   p    � � � � }� 8� 
 >�    e & AP  �    6U 
 
 �
 Y
 
 �
  AP n       >4    value " A�   �    d � � � `� � ��  >+    hat " A   !    ` � � � \� � �� ! M        m  JB


! N> Z   o  �  �  �  �  �  �  �  �  �  �  �  �  o   @                     B 
 h   m   P   t   Ojid  X   7  Ostate  9\       &   9f       2   9�       �   O   �   P          	  �  �   D      ^ �   f �    h �6   � �A   j �J   p �l   { �x   � ��   s ��   p ��   s ��   t ��   w ��   z ��    ��   � ��   � �5  � �A  � �H  � �M  � �R  � �T  � �Y  � �b  � �{  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �
  � �  � �  � �  � �  � �8  � �=  � �?  � �D  � �]  � �s  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �-  � �i  � �u  � �|  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �&  � �2  � �9  � �>  � �C  � �E  � �J  � �S  � �l  � �q  � �s  � �x  � ��  } ��  � ��  � ��  � ��  � �  � �
  � �  � �  � �,  � �1  � �4  � �6  � �:  � �Z  � �n  � �v  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �"  � �P  � �`  � �b  � �f  � �k  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �  � �  � �  � �  � �0  � �5  � �8  � �:  � �>  � �^  � �r  � �z  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � ��  � �
  � �  � �&  � �+  � �d  � �f  � �j  � �o  � ��  � ��  � ��  � ��  � ��  � ��  � ��  l ��  � �,   Z   0   Z  
 ]   Z   a   Z  
 m   Z   q   Z  
 �   Z   �   Z  
 �   Z   �   Z  
 �   Z   �   Z  
 �   Z   �   Z  
 �   Z     Z  
   Z   "  Z  
 l  Z   p  Z  
 �  Z   �  Z  
 �  Z   �  Z  
 �  Z   �  Z  
   Z     Z  
 �  Z   �  Z  
 �  Z   �  Z  
 	  Z   
  Z  
 $  Z   (  Z  
 H冹(�=     L嬃u3夜  �    3繦兡(脣蕘�0 t_冮tN冮t=冮t,凒tD嬄�  H�    �    3繦兡(肁媭  H兡(肁媭�   H兡(肁媭�   H兡(肁媭�   H兡(肁媭�   H兡(�   p      s   L   �   Q   s      �   �   6 G            �      �   �        �glfwGetInputMode  >A   handle  AJ          AP       �   6  
 >t    mode  A         �   P   Z   o  o   (                      B  0   A  Ohandle  8   t   Omode  O  �   �           �   �     �       1 �   5 �   G �#   7 �A   E �U   F �W   G �\   B �c   G �h   @ �o   G �t   > �{   G ��   < ��   G ��   : ��   G �,   9   0   9  
 ]   9   a   9  
 m   9   q   9  
 �   9   �   9  
   9     9  
 H塡$WH冹 �    H孃�=     Hc賣3夜  �    3繦媆$0H兡 _脙�wn�=�   u��   吚u��   3繦媆$0H兡 _肏�矍�     H零H��  H貎{ t:�   H嬎��   吚t(婯�H婥H媆$0H兡 _肈嬅H�    �  �    H媆$03繦兡 _�   p   '   s   ?   p   H   p   R   p   i   p   x   p   �   p   �   �   �   s      �   �  9 G            �   
   �   �        �glfwGetJoystickAxes  >t    jid  A           AI       �   @  J D  AI �       >t   count  AK          AM       � $  O  �   >�    js  AI       &  AI �       M        m  =I


 N Z   o  o                         B 
 h   m   0   t   Ojid  8   t  Ocount  9F       &   9P       2   9�       �   O  �   �           �   �     �         �
   ' �   ) �+   = �8   + �=   1 �V   = �c   4 �g   1 �q   4 �   5 ��   8 ��   ; ��   < ��   = ��   - ��   = �,   O   0   O  
 ]   O   a   O  
 m   O   q   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 H塡$WH冹 �    H孃�=     Hc賣3夜  �    3繦媆$0H兡 _脙�噾   �=�   u��   吚u��   3繦媆$0H兡 _肏�矍�     H零H��  H貎{ t]�   H嬎��   吚tK�=h   t婥 婯0�垑H婥H媆$0H兡 _脣S �H婥H媆$0H兡 _肈嬅H�    �  �    H媆$03繦兡 _�   p   '   s   C   p   L   p   V   p   m   p   |   p   �   p   �   p   �   �   �   s      �   �  < G            �   
   �   �        �glfwGetJoystickButtons  >t    jid  A           AI       �   D  N g  AI �       >t   count  AK          AM       � $  S  �  �   >�    js  AI  �     I 5   AI �       M        m  AI


 N Z   o  o                         B 
 h   m   0   t   Ojid  8   t  Ocount  9J       &   9T       2   9�       �   O   �   �           �   �     �       @ �
   G �   I �+   a �8   K �A   Q �Z   a �g   T �k   Q �u   T ��   U ��   X ��   [ ��   \ ��   ` ��   a ��   ^ ��   ` ��   a ��   M ��   a �,   P   0   P  
 `   P   d   P  
 p   P   t   P  
 �   P   �   P  
 �   P   �   P  
 �   P   �   P  
 �   P   �   P  
 �   P     P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 @SH冹 �=     Hc賣3夜  �    3繦兡 [脙�wf�=�   u��   吚u��   3繦兡 [肏�矍�     H零H��  H貎{ t73襀嬎��   3襀崑�   吚HD蔋嬃H兡 [肈嬅H�    �  �    3繦兡 [�   p      s   -   p   6   p   @   p   R   p   a   p   u   p   �   �   �   s      �   V  9 G            �      �   �        �glfwGetJoystickGUID  >t    jid  A           AI       �   ;  @ A  AI �       >�    js  AI  h     (  AI �       M        m  +I


 N Z   o  o                         B 
 h   m   0   t   Ojid  94       &   9>       2   9s       �   O  �   �           �   �     |       � �   � �   � �&   � �+   � �D   � �L   � �P   � �Z   � �h   � �n   � ��   � ��   � ��   � �,   S   0   S  
 ]   S   a   S  
 m   S   q   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
 2  S   6  S  
 B  S   F  S  
 R  S   V  S  
 l  S   p  S  
 H塡$WH冹 �    H孃�=     Hc賣3夜  �    3繦媆$0H兡 _脙�wn�=�   u��   吚u��   3繦媆$0H兡 _肏�矍�     H零H��  H貎{ t:�   H嬎��   吚t(婯0�H婥(H媆$0H兡 _肈嬅H�    �  �    H媆$03繦兡 _�   p   '   s   ?   p   H   p   R   p   i   p   x   p   �   p   �   �   �   s      �   �  9 G            �   
   �   �        �glfwGetJoystickHats  >t    jid  A           AI       �   @  J D  AI �       >t   count  AK          AM       � $  O  �   >�    js  AI       &  AI �       M        m  =I


 N Z   o  o                         B 
 h   m   0   t   Ojid  8   t  Ocount  9F       &   9P       2   9�       �   O  �   �           �   �     �       d �
   k �   m �+   � �8   o �=   u �V   � �c   x �g   u �q   x �   y ��   | ��    ��   � ��   � ��   q ��   � �,   Q   0   Q  
 ]   Q   a   Q  
 m   Q   q   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 �   Q   �   Q  
 �  Q   �  Q  
 �  Q   �  Q  
 �  Q   �  Q  
 �  Q   �  Q  
 @SH冹 �=     Hc賣3夜  �    3繦兡 [脙�wc�=�   u��   吚u��   3繦兡 [肏�矍�     H零H��  H貎{ t43襀嬎��   3襀岾4吚HD蔋嬃H兡 [肈嬅H�    �  �    3繦兡 [�   p      s   -   p   6   p   @   p   R   p   a   p   u   p   �   �   �   s      �   V  9 G            �      �   �        �glfwGetJoystickName  >t    jid  A           AI       �   ;  @ >  AI �       >�    js  AI  h     %  AI �       M        m  +I


 N Z   o  o                         B 
 h   m   0   t   Ojid  94       &   9>       2   9s       �   O  �   �           �   �     |       � �   � �   � �&   � �+   � �D   � �L   � �P   � �Z   � �h   � �n   � ��   � ��   � ��   � �,   R   0   R  
 ]   R   a   R  
 m   R   q   R  
 �   R   �   R  
 �   R   �   R  
 �   R   �   R  
 2  R   6  R  
 B  R   F  R  
 R  R   V  R  
 l  R   p  R  
 H冹(�=     u3夜  �    3繦兡(肏c罤�
�  H�繦拎�< t酘媱�   H兡(�   p      s   &   p      �   �   @ G            E      @   �        �glfwGetJoystickUserPointer  >t    jid  A         *   
 Z   o   (                      B  0   t   Ojid  O�   P           E   �     D       � �   � �   � �    � �2   � �8   � �@   � �,   U   0   U  
 d   U   h   U  
 �   U   �   U  
 H冹(�=     L嬃u3夜  �    3繦兡(脥B�=<  w*Hc�緦�   ��uB苿 �    �   H兡(脣罤兡(肈嬄�  H�    �    3繦兡(�   p      s   b   �   g   s      �   �   0 G            r      m   �        �glfwGetKey  >A   handle  AJ          AP       L    >t    key  A         f    Z   o  o   (                      B  0   A  Ohandle  8   t   Okey  O  �   x           r   �     l       � �   � �   � �#   � �-   � �=   � �F   � �K   � �P   � �R   � �W   � �k   � �,   >   0   >  
 W   >   [   >  
 g   >   k   >  
 �   >   �   >  
 �   >   �   >  
 H冹(�=     嬄u3夜  �    3繦兡(脙�t+岮�=<  w.侚P  t崄俐��凐v岮賰鴞w��   嬋H兡(H�%�   D嬃H�    �  �    3繦兡(�   p      s   N   p   [   p   e   �   o   s      �   &  4 G            z      u   �        �glfwGetKeyName  >t    key  A         n   R 
  A       9  0  >t    scancode  A   
     R    (  A         
  Z   o  o   (                      B  0   t   Okey  8   t   Oscancode  9L       �   9X       �   O  �   �           z   �  
   t       � �   � �   � �"   � �'   � �1   � �L   � �R   � �T   � �X   � �_   � �s   � �u   � �,   <   0   <  
 X   <   \   <  
 p   <   t   <  
 �   <   �   <  
 �   <   �   <  
   <     <  
 "  <   &  <  
 <  <   @  <  
 H冹(�=     u3夜  �    3繦兡(脥A�=<  wH兡(H�%�   D嬃H�    �  �    ����H兡(�   p      s   1   p   ;   �   E   s      �   �   8 G            S      N   �        �glfwGetKeyScancode  >t    key  A         D    Z   o  o   (                      B  0   t   Okey  9.       �   O�   `           S   �  	   T       � �   � �   � �    � �*   � �.   � �5   � �I   � �N   � �,   =   0   =  
 \   =   `   =  
 �   =   �   =  
 �   =   �   =  
 H冹(�=     L嬃u3夜  �    3繦兡(脙�w*Hc�緦�   ��uB苿 �    �   H兡(脣罤兡(肈嬄�  H�    �    3繦兡(�   p      s   ]   �   b   s      �   �   8 G            m      h   �        �glfwGetMouseButton  >A   handle  AJ          AP       G    >t    button  A         a    Z   o  o   (                      B  0   A  Ohandle  8   t   Obutton  O�   x           m   �     l       � �   � �    �#   � �(    �8    �A    �F    �K   
 �M    �R   � �f    �,   ?   0   ?  
 _   ?   c   ?  
 o   ?   s   ?  
 �   ?   �   ?  
   ?     ?  
 H冹8�=     u3夜  �    W繦兡8�)t$ �    H嬋W鯤+
�  x騂*耠H嬃冡H谚H硫H*痱X鲨    H嬋W繦吚x騂*莉^�(�(t$ H兡8肏谚冡H硫H*莉X莉^�(�(t$ H兡8�   p      s   '   f   4   p   W   g      �   i   1 G            �      �   �        �glfwGetTime  Z   o  W  �   8                      B  O   �   P           �   �     D       � �   � �   � �&   � �w   � �|   � ��   � �,   ]   0   ]  
 �   ]   �   ]  
 H冹(�=     u3夜  �    3繦兡(肏兡(�       p      s   %   g      �   o   ; G            )          �        �glfwGetTimerFrequency  Z   o  �   (                      B  O �   8           )   �     ,       � �   � �   � �$   � �,   `   0   `  
 �   `   �   `  
 H冹(�=     u3夜  �    3繦兡(肏兡(�       p      s   %   f      �   k   7 G            )          �        �glfwGetTimerValue  Z   o  W   (                      B  O �   8           )   �     ,       � �   � �   � �$   � �,   _   0   _  
 �   _   �   _  
 @SH冹 �=     Hc賣3夜  �    3繦兡 [脙�wd�=�   u��   吚u��   3繦兡 [肏�矍�     H零H��  H貎{ t53襀嬎��   吚t&3繦9冭   暲H兡 [肈嬅H�    �  �    3繦兡 [�   p      s   -   p   6   p   @   p   R   p   a   p   u   p   �   �   �   s      �   X  ; G            �      �   �        �glfwJoystickIsGamepad  >t    jid  A           AI       �   ;  @ ?  AI �       >�    js  AI  h     &  AI �       M        m  +I


 N Z   o  o                         B 
 h   m   0   t   Ojid  94       &   9>       2   9s       �   O�   �           �   �     �       # �   ) �   < �&   + �+   1 �D   < �L   4 �P   1 �Z   4 �h   5 �n   8 �}   ; ��   < ��   - ��   < �,   V   0   V  
 _   V   c   V  
 o   V   s   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
 4  V   8  V  
 D  V   H  V  
 T  V   X  V  
 l  V   p  V  
 @SH冹 �=     Hc賣3夜  �    3繦兡 [脙�wQ�=�   u��   吚u��   3繦兡 [肏�矍�     H玲H��  H葍y t"3襀兡 [H�%�   D嬅H�    �  �    3繦兡 [�   p      s   -   p   6   p   @   p   R   p   a   p   x   p   �   �   �   s      �   F  9 G            �      �   �        �glfwJoystickPresent  >t    jid  A           AI       �   ;  e   >�    js  AJ  h       AJ �       M        m  +I


 N Z   o  o                         B 
 h   m   0   t   Ojid  94       &   9>       2   9u       �   O  �   �           �   �     �        �   
 �    �&    �+    �D    �L    �P    �Z    �h    �n    �p    �u    �|    ��    �,   N   0   N  
 ]   N   a   N  
 m   N   q   N  
 �   N   �   N  
 �   N   �   N  
 "  N   &  N  
 2  N   6  N  
 B  N   F  N  
 \  N   `  N  
 H冹(�=     u3夜  �    3繦兡(肏兡(H�%`      p      s   '   p      �   �   A G            +          �        �glfwRawMouseMotionSupported 
 Z   o   (                      B  9$       &   O   �   8           +   �     ,       � �   � �   � �$   � �,   ;   0   ;  
 }   ;   �   ;  
 �   ;   �   ;  
 H冹(�=     u3夜  �    3繦兡(肏媮H  H墤H  H兡(�   p      s      �   �   9 G            3      .   �        �glfwSetCharCallback  >A   handle  AJ        3    >Y   cbfun  AK        3   
 >Y    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   Y  Ocbfun  O �   @           3   �     4       � �   � �   � �    � �.   � �,   G   0   G  
 `   G   d   G  
 �   G   �   G  
 �   G   �   G  
   G     G  
 H冹(�=     u3夜  �    3繦兡(肏媮P  H墤P  H兡(�   p      s      �   �   = G            3      .   �        �glfwSetCharModsCallback  >A   handle  AJ        3    >\   cbfun  AK        3   
 >\    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   \  Ocbfun  O �   @           3   �     4       � �   � �   � �    � �.   � �,   H   0   H  
 d   H   h   H  
 �   H   �   H  
 �   H   �   H  
   H     H  
 �=     u3夜  �    H嬍H�%�      p      s      p      �   �   < G                      �        �glfwSetClipboardString  >A   handle  AJ            D    >@   string  AK          
 
 Z   o                          B     A  Ohandle     @  Ostring  9       �   O�   0              �     $       � �    � �   � �,   [   0   [  
 c   [   g   [  
 �   [   �   [  
 �   [   �   [  
   [     [  
 �=     u3夜  �    H塓XH�%�      p      s      p      �   �   3 G                       �        �glfwSetCursor  >A   windowHandle  AJ             >(   cursorHandle  AK           
 
 Z   o                          J     A  OwindowHandle     (  OcursorHandle  9       �   O �   8               �     ,       � �    � �   � �   � �,   E   0   E  
 `   E   d   E  
 �   E   �   E  
 �   E   �   E  
   E     E  
 H冹(�=     u3夜  �    3繦兡(肏媮0  H墤0  H兡(�   p      s      �   �   @ G            3      .   �        �glfwSetCursorEnterCallback  >A   handle  AJ        3    >J   cbfun  AK        3   
 >J    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   J  Ocbfun  O  �   @           3   �     4       � �   � �   � �    � �.   � �,   K   0   K  
 g   K   k   K  
 �   K   �   K  
 �   K   �   K  
   K     K  
 @SH冹@�=     H嬞)t$0(�)|$ (鷘3夜  (t$0(|$ H兡@[�    f.�妰   �
    f/蝫u�    f/饂gf.�zaf/蟱[f/鴚U��  吚tl伝�   @ u �厨  ��   (t$0(|$ H兡@[�(�(蜨嬎(t$0(|$ H兡@[H�%H   (逪�    fI~�(謋I~鸸  �    (t$0(|$ H兡@[�   p   9   s   K   �   Y   �   w   p   �   p   �   �   �   s      �   T  6 G            �      �   �        �glfwSetCursorPos  >A   handle  AI       � (  �  �   AJ         
 >A    xpos  A�         O  A�  �     ! 
 >A    ypos  A�         � { O  Z   o  o   @                     B  P   A  Ohandle  X   A   Oxpos  `   A   Oypos  9u       �   9�       }   O�   �           �   �     �       % �   ) �)   B �8   ) �=   , �u   4 �   7 ��   : ��   ; ��   B ��   @ ��   B ��   @ ��   . ��   B �,   A   0   A  
 ]   A   a   A  
 y   A   }   A  
 �   A   �   A  
 �   A   �   A  
 �   A   �   A  
 @  A   D  A  
 P  A   T  A  
 h  A   l  A  
 H冹(�=     u3夜  �    3繦兡(肏媮(  H墤(  H兡(�   p      s      �   �   > G            3      .   �        �glfwSetCursorPosCallback  >A   handle  AJ        3    >S   cbfun  AK        3   
 >S    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   S  Ocbfun  O�   @           3   �     4       � �   � �   � �    � �.   � �,   J   0   J  
 e   J   i   J  
 �   J   �   J  
 �   J   �   J  
   J     J  
 H冹(�=     u3夜  �    3繦兡(肏媮X  H墤X  H兡(�   p      s      �   �   9 G            3      .   �        �glfwSetDropCallback  >A   handle  AJ        3    >`   cbfun  AK        3   
 >`    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   `  Ocbfun  O �   @           3   �     4       � �    �    �     �.    �,   M   0   M  
 `   M   d   M  
 �   M   �   M  
 �   M   �   M  
   M     M  
 H塡$WH冹 �=     A孁H嬞u3夜  H媆$0H兡 _�    嬍侀0 剹  冮処  冮剶   冮tw凒tD嬄�  H�    H媆$0H兡 _�    �`   吚uH�    �  H媆$0H兡 _�    3蓞�暳9�  剕  墜  嬔H嬎H媆$0H兡 _H�%X   3蓞�暳墜�   H媆$0H兡 _�3蓞�暳9媱   �5  �厐   �粣   u@埢�   �粦   u苾�    �粧   u苾�    �粨   u苾�    �粩   u苾�    �粫   u苾�    �粬   u苾�    �粭   u苾�    墜�   H媆$0H兡 _�3蓞�暳9媭   剦   �uH崈�   篯  �8u�  H�繦冴u飰媭   H媆$0H兡 _肁崁�奎�凐vH�    �  H媆$0H兡 _�    9粚   t(L崈   壔�   H崜�  H嬎�@   嬜H嬎�P   H媆$0H兡 _�   p   +   s   d   �   s   s   y   p   �   �   �   s   �   p   �  �   �  s     p   '  p      �     6 G            6  
   +  �        �glfwSetInputMode  >A   handle . AI        
 V 
 { 
 �  �  r � �
  AJ         
 >t    mode " A           h  } N �" �  A  �    s  W  >t    value  A   �     �  * 4  L � . A          ^  �  � - �  � � }C �  Ah          A  +    
  Z   o  o  o  o                         B  0   A  Ohandle  8   t   Omode  @   t   Ovalue  9w       &   9�       �   9      z   9%      �   O  �   �          6  �  9   �      J �
   N �    � �*   N �/   P �Y   � �h   � �r   � �w   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �  � �  � �  � �'  � �.  � �7  � �>  � �G  � �N  � �W  � �^  � �g  � �n  � �w  � �~  � ��  � ��  m ��  n ��  q ��  x ��  y ��  v ��  } ��  � ��  V ��  Y ��  � ��  Y ��  _ �  d �   g �+  � �,   :   0   :  
 ]   :   a   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
   :     :  
 @  :   D  :  
 P  :   T  :  
 �  :   �  :  
 �  :   �  :  
 �  :   �  :  
 �  :     :  
   :     :  
 @SH冹 �=     H嬞u3夜  �    3繦兡 [脙=�   u��   吚u��   3繦兡 [肏�p  H�p  ��     H兡 [�   p      s   (   p   1   p   ;   p   J   p   Q   p   W   p      �   %  = G            e      _   �        �glfwSetJoystickCallback  >]   cbfun  AI       T   6   AJ         
 >]    t  AH  N       M        m  &I


 N
 Z   o                         B 
 h   m   0   ]  Ocbfun  9/       &   99       2   O   �   `           e   �  	   T       � �   � �    � �&   � �?   � �A   � �G   � �U   � �_   � �,   W   0   W  
 c   W   g   W  
 {   W      W  
 �   W   �   W  
   W     W  
 !  W   %  W  
 <  W   @  W  
 �=     u3夜  �    Hc罤�繦玲H��  �< tH墧�   �   p      s   #   p      �   �   @ G            6       5   �        �glfwSetJoystickUserPointer  >t    jid  A             >   pointer  AK        6  
 
 Z   o                          B     t   Ojid       Opointer  O�   H           6   �     <       � �    � �   � �'   � �-   � �5   � �,   T   0   T  
 d   T   h   T  
 �   T   �   T  
 �   T   �   T  
 H冹(�=     u3夜  �    3繦兡(肏媮@  H墤@  H兡(�   p      s      �   �   8 G            3      .   �        �glfwSetKeyCallback  >A   handle  AJ        3    >V   cbfun  AK        3   
 >V    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   V  Ocbfun  O  �   @           3   �     4       � �   � �   � �    � �.   � �,   F   0   F  
 _   F   c   F  
 �   F   �   F  
 �   F   �   F  
   F     F  
 H冹(�=     u3夜  �    3繦兡(肏媮   H墤   H兡(�   p      s      �   �   @ G            3      .   �        �glfwSetMouseButtonCallback  >A   handle  AJ        3    >P   cbfun  AK        3   
 >P    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   P  Ocbfun  O  �   @           3   �     4       � �   � �   � �    � �.   � �,   I   0   I  
 g   I   k   I  
 �   I   �   I  
 �   I   �   I  
   I     I  
 H冹(�=     u3夜  �    3繦兡(肏媮8  H墤8  H兡(�   p      s      �   �   ; G            3      .   �        �glfwSetScrollCallback  >A   handle  AJ        3    >S   cbfun  AK        3   
 >S    t  AH  '      
 Z   o   (                      B  0   A  Ohandle  8   S  Ocbfun  O   �   @           3   �     4       � �   � �   � �    � �.   � �,   L   0   L  
 b   L   f   L  
 �   L   �   L  
 �   L   �   L  
   L     L  
 H冹8�=     )t$ (饀3夜  (t$ H兡8�    f.�姈   W纅/�噳   f/5    wH塡$0�    H嬋W繦吚x騂*离H谚冡H硫H*莉X莉
    3莉Y苀/羠�\羏/羢
H�       �H嬃騂,豀罔    H+肏媆$0H��  (t$ H兡8�(諬�    fI~鸸  (t$ H兡8�       p   &   s   E   �   Q   g   }   �   �   f   �   p   �   �   �   s      �   �   1 G            �      �   �        �glfwSetTime 
 >A    time  A�         7  A�  �     "  Z   o  �  W  o   8                      B  @   A   Otime  O �   h           �   �  
   \       � �   � �   � �%   � �*   � �P   � ��   � ��   � ��   � ��   � �,   ^   0   ^  
 V   ^   Z   ^  
 f   ^   j   ^  
 �   ^   �   ^  
 H塡$ UH崿$宣��H侅0  H�    H3腍墔   �=     H嬞u3夜  �    3篱|  �	H壖$P  勆�*  H壌$H  岮�<	v8岮�<v1�锳��v)H�    H嬎�    H豀�    H嬎�    H亻�  H�    H嬎�    H嬸H=   儻  W榔D$  3繦峂 L嬈H塃慔嬘塃�D$!f塃�D$1圗�D$A塃D$Q圗D$aD$qE�E�E�E�E�E�E�E �    H峌 艱5  H峀$ �    吚�)  D��  E3襀�=�  E呟~J�    Ic翷岴燣i弱   L螴崄�   L+�D  �B� +製H�绤蓇韰�凢  A�翬;觸紸�肏嬒Ic肏i絮   D��  �    �
�  H��  �蒆c袶峀$ H崏�   Hi阴   H�I�JA�B I�J0A�B@I�JPA�B`H冴�I�J�IJA B I0J0A@B@IPJPA`B`婣p塀p禔t圔tH��勆呮��H嫶$H  H�x  H�=X  兓��� tH崑����    H�H伱   H;邁蹾嫾$P  �   H媿   H3惕    H嫓$X  H伳0  ]肕吷劰��H峀$ H崏�   AI怉IA燗A I癆I0A繟A@I蠥IPA郃A`I冮�I餉I�AIAIA AA I0AI0A@AA@IPAIPA`AA`婣pA堿p禔tA圓t檑��   �   (   p   :   s   y   �   �   a   �   �   �   d   �   �   �   a   $  �   7     F  p   P  p   �  p   �  y   �  p   �  p   �  p   �  p   �  ~   �  �      �   v  ? G            �  &   �  �        �glfwUpdateGamepadMappings  >@   string  AJ        H 9  
 >@    c  AI  0     aYX  AI �      >-    length  AL  �     ��r  AL `     "Q �
 >h    line  D    >�    mapping  D      M        n  丆 
 >t     i  Aj  M    Dr " Aj `     �%  N �E  N" Z   o  �  �  �  q  H  p   >d  _glfw  CM  �	  T    =x  Ck  �	  J    Gp ' CM �	  `     0�  Ck �	  `     �%  N �E  0                    C 
 h   n  
 :   O  @  @  Ostring     h  Oline      �  Omapping  O  �   �           �  �     �       � �&   � �E   � �`   � �v    ��    ��   � ��   � ��   � ��   � �(  � �C   ��   ��   ��  
 �l   �o  � ��   ��   ��   ��   ��    ��   ��   �,   X   0   X  
 f   X   j   X  
 �   X   �   X  
 �   X   �   X  
 �   X   �   X  
 �   X   �   X  
 B  X   F  X  
 V  X   Z  X  
 �  X   �  X  
 �  X   �  X  
 �  X   �  X  
 �  X   �  X  
 �  X   �  X  
 @USWATH崿$X���H侅�  H�    H3腍墔�   H�    H塗$ H塂$0L嬄H�    H荄$8    H塂$@H孂H崄�   H塂$HH�    H塂$PH崄�   H塂$XH�    H塂$`H崄�   H塂$hH�    H塂$pH崄�   H塂$xH�    H塃�H崄�   H塃圚�    H塃怘崄�   H塃楬�    H塃燞崄�   H塃℉�    H塃癏崄�   H塃窰�    H塃繦崄�   H塃菻�    H塃蠬崄�   H塃豀�    H塃郒崄�   H塃鐷�    H塃餒崄�   H塃鳫�    H塃 H崄�   H塃H�    H塃H崄�   H塃H�    H塃 H崄�   H塃(H�    H塃0H崄�   H塃8H�    H塃@H崄�   H塃HH�    H塃PH崄�   H塃XH�    H塃`H崄�   H塃hH�    H�    H塃pH崄�   H塃xH�    H墔�   H崄�   I嬋H墔�   �    H凐 �  H婰$ �y ,卲  H�    噣   IH兞!H塋$ 彁   �    H=�   �:  H婽$ �<,H��'  H壌$�  L嬂L壌$�  H嬒L壖$�  �    H岾H塋$ �劺勂  fD  ,+勡  3鯨峵$0I�H敲����D  H�脌< u鱈嬅�    H婰$ 吚u
�<:H�tH�艻兤H凗r篱"  H鯤岺H塋$ H媡�8H咑勛   �A�A���+uE2�H�岭��-uE2鯤岺H塋$ �<au��<bu!�H嬑H峊$ A�
   �H婰$ H�凌    隑<h叐   �H峊$ H婰$ A�
   H�凌    H婰$ H峊$ H�罙�
   嬝�    楞
脠F�>ubA段E鰽厩+雀   欦鵄段鲑團圢H婰$ �9~u<鲐D坴團�,��   I抢����I�繠�<  u鯤婰$ H嬓�    吚卻  H婰$ H�    �    H婰$ H�    H菻塋$ �    H婰$ H菻塋$ �劺匥��fff�     稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稄�   岮�<w	�� 垙�   稐�   岯�<w	�� 垪�   H崗�   ��   �   L嫶$�  H嫶$�  L嫾$�  H媿�   H3惕    H伳�  A\_[]�3离�3夜  �    3离�   �   )   s   =   v   a   y   y   |   �      �   �   �   �   �   �   �   �     �     �   -  �   C  �   Y  �   o  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �     �   -  a   P  �   p  a   �  �   �  b   �  e   �  e   �  e     p   7  b   K  �   P  a   \  �   i  d   y  p   �  �   �  s      �   �  2 F            �  &   �  q        �parseMapping  >   mapping  AJ        R  AM  R     xa  >@   string  AK        :  AP  :     � >    fields  D0   
 >@    c " AJ  @    :( R  G � � �B (
  AK  �    1  D    
 >#     i  AL  �    M  AL �    � l�$  >#     length  AH  1    ��  AI  �      AP  $      >p     maximum  A^  I      A  b    �   J �  A^ �    � � [ �, 
 >�    e  AL  7     AL �    � l�$  >p     minimum  A   T      A_  F        A  b    �   J �  A_ �    �v  �  >)    bit  A   �      >)    hat  A   �     
 >@    name  AH       . Z
   �  �  �  �  �  �  �  �  �  o   �                     C 
 :�  O  �    Omapping  �  @  Ostring  0     Ofields      @  Oc  9      �   9w      �   O  �   �          �  �  r   �      ~  �&   �  ��  �  �1  �  �J  �  �M  �  �t  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �&  �  �@  �  �I  �  �N  �  �T  �  �V  �  �[  �  �b  �  �n  �  �r  �  �v  �  �x  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �  �  �  �  �  �  �.  �  �C  �  �Y  �  ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   �   �   �   �(   �1   �?   �H   �V   �_   �m   �v   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   �    �   �   �%   �.   �<   �E   �S   �\   �j   �s   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   ��   �   �   �"   �+   �9   �B   �P   �Y   �g   �p   �}   ��   ��  �  ��  �  ��  �  �,      0     
 Z      ^     
 j      n     
 �      �     
 �      �     
 �      �     
 �      �     
           
 ,     0    
 U     Y    
 i     m    
 y     }    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 %     )    
 5     9    
 I     M    
 a     e    
 �     �    
 �     �    
 �     �    
 z     ~    
 �     �    
 �     �    
  B      �           �      �      �   
 
4 
2p    6          �      �      �    B      +           �      �      �    B      z           �      �      �    B      S           �      �      �    B      r           �      �      �    B      m           �      �      �    x h r0    �           �      �      �    d T 2p    N           �      �      �   ! 4     N          �      �      �   N   �           �      �      �   !   4     N          �      �      �   �   �           �      �      �   !       N          �      �      �   �   �           �      �      �    2p    Q           �      �      �   ! 4     Q          �      �      �   Q   �           �      �      �   !   4     Q          �      �      �   �   �           �      �          2p    ,           �      �      
   ! 4     ,          �      �      
   ,   =           �      �         ! d ,   =          �      �         =   z           �      �         !   ,   =          �      �         z   �           �      �         !       ,          �      �      
   �   �           �      �      "    B      3           �      �      (    B      3           �      �      .    B      3           �      �      4    B      3           �      �      :    B      3           �      �      @    B      3           �      �      F    B      3           �      �      L    B      3           �      �      R    20    �           �      �      X   
 
4 
2p    �           �      �      ^   
 
4 
2p    �           �      �      d   
 
4 
2p    �           �      �      j    20    �           �      �      p    20    �           �      �      v    B      E           �      �      |    20    �           �      �      �    20    e           �      �      �   & 4� � P            �       H           �      �      �   ! t�     H          �      �      �   H   X           �      �      �   ! d� H   X          �      �      �   X   �          �      �      �   !   H   X          �      �      �   �  �          �      �      �   !       H          �      �      �   �  �          �      �      �   !   t�  d�     H          �      �      �   �  �          �      �      �    20    �           �      �      �   
 
4 
rp    P           �      �      �   !
 
d T
     P          �      �      �   P   �           �      �      �   !   d  T
     P          �      �      �   �   �           �      �      �   ! x h �   �          �      �      �   �   �          �      �      �   !   x  h �   �          �      �      �   �  �          �      �      �   !       P          �      �      �   �  	          �      �      �    B      +           �      �      �    b      !           �      �      �   ! h     !          �      �      �   !   |           �      �      �   !   h     !          �      �      �   |   �           �      �      �    h b      K           �      �      �   ! 4     K          �      �      �   K   �           �      �      �   !       K          �      �      �   �   �           �      �           B      )           �      �          B      )           �      �          d 4 2p    n           �      �          4     n           �      �         
 
4 
2p    u           �      �          2
����P    �           �      �      $   ! t d 4
     �          �      �      $   �   5          �      �      *   !   d     �          �      �      $   5  �          �      �      0   !   d     �          �      �      $   �  �          �      �      6    20    9           �      �      <    20    L           �      �      B   
 
t 4     �           }      }      H    20    �           ~      ~      N   & 5 �p0P    �     �       �                      T   ! �4 �= d<     �                     T   �  �                      Z   !       �                     T   �  �                      `   !   �4  �=  d<     �                     T   �  �                      f   !       �                     T   �  �                      l   platform a b x y back start guide leftshoulder rightshoulder leftstick rightstick dpup dpright dpdown dpleft lefttrigger righttrigger leftx lefty rightx righty , Invalid input mode 0x%08X Invalid cursor mode 0x%08X Raw mouse motion is not supported on this system Invalid key %i Invalid mouse button %i Invalid cursor position %f %f Invalid image dimensions for cursor Invalid standard cursor 0x%08X Invalid joystick ID %i 
 Invalid time %f   �?      �?  $�.B      郈�������  ��������   v ��3.]H�\�)r�   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\Release\glfw3.pdb �6j���瑓鼾檘霖g�"�:�欸U�fy�$劚 w熨剔2毕诇犦=@:橼誥�M玌�0�.pP緞羔\誴� 鷽襬V稬�;)m澅凢 h嵊p嵱R楶殊璯V{尵(蘊L�n求S栒鲱m丱�Hn蠑遀嗉挖^$濵,癒`�習�,:�彞�噂�'��漆�R枹x(/ＢxO芜,Qk岰{�>酪鬹M0溞4戳鸕0

1D漱R>濈铍]らu艧q�$c	緍#Z�!�
�(秷{i骿y&�9遥罳�kz峔懙�>'謬侕K鵃峬NR=d誎O慀謞�
H[ㄝ瘖
旎%Z奘� � ��M╫v� �$铀FU楿Fxf���#\�%K��#%Or悚竝鹲.1Ac�
Cm%�-重婬�RQ 1�,脈� 增{Y-蠽�:6f杭郎_;眑諱XXO�9�=噞�,傝焤*里��Q�#!鈘�%gf狴rc沣�1��R魘9餓觝'\�Ko稖尙D�檱余aK硿瞽儿纄Ge満揅�9�悉覱碚$嘠M統:徐 睤硇▎_齈*�l� D墜cle棺Z�"灇[g韦蝄屻�丽碩�7�2蜱7L咻錄%棤櫿/HVT脎郗撪蠶Qoㄈ痉L.4萧+Jm`nX鬉恎9猳崣移I疞�$岳�+nx距$=s;馔加XKji腺Zi幎軍鎄 菝JR~鍛}F宼箪YJ��+嵋l/r字=駓1+譭．货r歞�
�>龄衷娒7�埾辗敳�6<W� 怩]�,澦鍲�h弤棸廛rh 筎m笢�昸謚]ㄓ悟鶆演摉詮c1M!q鲟�0�<屠>愕&x捕殤 絈懹~M屃呹G耙�珢鑛uN龎螡�{33瑰焗p@蟽�潍d疕騅╄玶?5E瓛瑷郄軉荇/>侊銫-t邾;拕4缱讛鵯燐cq觯缅貏碥i﹖�T+S�>捳怴zF邪爏?瀤齏g崉瑥n鳣莓誥櫼晉E�3摓e\翼襦n栱袸{�坣o?蒈~G捺甿F�0&d镳载Y� 堯,K3FE!�4;�9閥研_!﹂沕'HE�;V�!応跹捲H洅:%�zzZ葼廖稕_咜搢焤▔*+My�8B
檚@萠焪�W晱赑覾擧4a�:d�#�,A峱勈#Z灀飍~�惦^4"茈鼚擨�1H�U辶B:斘z筒服踠G)B牬�-緇�w49Y勶3煄�3�+伍逢r*;tⅰ2�6a饈9眧�T嚨沚(hQ%�4]谀哤:蒹憲埸A(劍^O秾��q�	�2�:X肠H^薡�/$疠^7責稟Gю匓.%s1� 食胗炦^�3�z釐	��(�-�R�脹D錉Iv�,~�3韑妘oa#p� 賚g5�ii齉ぃ缐痂"籿V[史繢X�5��$n埏橽�:b馶缿�'�%儮�:踯愁私<蒺声荍譐H谊Dl�	陳�賄�6to<"帿�*;Yo◢燌�-黻&v灺鮤仠帊��)匫っ+;燖j��=�!姶�C櫼3枂�鲚暨縶衱蠽!�:$殝輟{鯴�)��0�>o �-:蜉c芔�" K菵紐~鹭�5nL炾O窩CtB��隨w嵫輿
讷縇7�)^埞oBwj`.緧�3l!徢�=﹚绶畿=u*}悲�
V两闠皪餰a堢f>?X[^聴�0翬%�sp貸�芜<j稍鞨儿然�\G鉊�禶�1哺?;殻�!�t�>qE�?K囩竪 c絬膜�/眷鏗ML揪(Y2釩B矽+JQ旸硞鑚�
彁啩Ｂ烡<-仢邊铡虙揎/軍�1zP5�+8aQ掏涴2�#挅8P勜�'铁8亱�9賡6g憼u�0�.I�	Q薲&朞�5
賡d谸}qZYa婞<9厵蟣榋&-�8TzYN钀aB~{癢�諆5/Q箋ㄗ`茱�>v*�#D�R�舞涺銓箤�=+`JnX瀏⒕瞓DGr嬳l<wb�
宮"Zj1淊刮J�禯3�4�#层劍苸A�谉苦=
�#謨儸v羺P�$殟邮~帨Qh:啙澚*宯�&v�/頧0庹緌犲}GW�l釕�翥2淄%Y鮱|扡z鼄{鎸l)T笀觾謁 諊wq蜣嚭R謩菨薽x远(0��逖筞 }z郺� 栺� I~樀BK\U+*22N粨
)Q��'A粖俵衄B籚
n
扩苉钇i铐鳌V6鋰K�<鲲!龣婩�圚惢m?碟;幦鶸O�*茖莑v膅�'褼h他
'c來q熭訵犗4繯阮_/k嗘W8��3c錯麊掲�龉貢/壅�'Y擬煞枽�J早o}P	閈o儌盦&甇v]��<:�+m'l�8(痽 p邞尙��价�3�>殲矐髴ギ憙蟽	办WH�0窑+9髼�2惕m�=k姧�;^YE靍轭楧櫀�,aTB�!8踣�3庾宺7�餌苇4�5s�+'~tl誻>uu鹵�:�( z銜J柅�	噎紾*遭�;g╧穸'c$渌6臀搊X?y7k肌痔庫
�#灜�/骾jt檈娖髫	婹|�S絙P<皉}漠瓞X駻鑋堍稜�雱"5qgg|疟�Ii�嫌�
p�$+咽嚮JN�P黹�仵S旁9��)檥撦塕�A箈嗢衪эo陲談弪�9�錯N('	Kh*�7陴I员Α!t�!鈿接鎲�[9北���mb鰔!吇矔证稬A舗虑猌常.G叕B懛�嵲�a奲
n鸦I9病0@�#y霥罭|�T訤T6>L瑷�5慲R鱶�)@�?彁涅唡.潐鼕�!j么$`謙BH�>鞤�6貅-橑袸z圛0�	�	?B.�7C底$hц�4�1肤邻�n.UMv轶俗<Z��6K潡屉罦蓌,忀�恙蹾咥�0\pb哊A/皤閆狻P沋鑵妻6爃)鬋aj0阪l艻烽v�$�?2H頾.溟枮J吤p孼%C韻蛳淎宼[�'vj鲪签�
�7覍8xw|�刘Z觨畇蜓睇蘼镆x�汷痱゛93Y
慂歐峖胁~轜塒呓/�$|[椎C�冹�*w*鄌�8K缈畭�椡t>
i�滛}笌e�	�{M〒A鋑|�钕緞,兒]=怸?6伔.	"葖_j寽桁漴
;2E]SろP�.:据劦�7YO�滇N媊�螅��娊仩$I� �貛UJ鈸S扙�.厀��-嬣潗q坦G爨鱒e髟胳揜D檯P千1隰zb嵘{H�7旎騛rG憗朶f怨+ENt/遅���
~�+罊扛4)+�>@忉f��-紭榶餩"	遬/Et�>`\1 悍糔葄{)竍 "丘岍轷┝�ㄚ�>lc�)<�'>0�5`"┐也T爎緕��<m鏠鋦+菱汩瞎i�)I$+�繨鄬颉0駩沀藓J8舙�应�3沀藓J8宱Qp��晸s奄诎蠦屔觩�,壹,�]o|:4馉�. �%O�1悀b聊tS�<��睲{碃'd>殹羉V榾籶.wNYi鷰t�2~荭:`8�揊�	�
�/遏U	Gz9IoE嶴8疑�l6入夁�)3-史g~舚p賰蹼w脝鴆凬づ�~偵O�;墬"
`憔魉煩 ]&钣#:忼W9cd槱_;�*�*�I鵂�弳鉣K:ON|v8p嫬.ч媞⒍z-驴珑|蜰圣E�餲礫G2鉲Vb�s贗hEY�a/砻訬玵]�/丗枳輚	�蠻vD�H庡\W諡副-坓�(鬄跄狴Fda�9E\$L釉迊棪�2-坓�(鬄醵�	hQ�)-坓�(鬄鯁鰡畆殅W-坓�(鬄鯆r_蚴ノj-坓�(鬄醵l�6-坓�(鬄鮻險Fs獞�EQo磉�馑鏯沆C�,几Y�6	褔)轛C鏦,U��6�8�6芳穵�
辛]痊儲馒w�)鷋岛婩繌�bx攠�葤qA羂/	��$篟�们h�|�{yT~�&w~嫥骺弨bx攟睢�墒瀍�*��)�`扦�*�0V瓋=剣�=獱�y曲蕡^"v�6齑Q黑�苗摒藘盹b?�-坓�(鬄��闤�-坓�(鬄��闤�-坓�(鬄��闤�-坓�(鬄��闤�-坓�(鬄��闤�-坓�(鬄��闤�-坓�(鬄��闤�-坓�(鬄��闤怆嘕-WV8o尼鵉da�9E\$L釉搦顶苢~u9E\$L釉轆褹笣箕?9E\$L釉搦顶苢~u雵J-WV8o�%卑LcW雵J-WV8o綂硦贳3K-坓�(鬄�d�雵J-WV8o斀�&g卷雵J-WV8o.*~襠[
Bu2胞9nV愛褜斃才茉籨pbQ$i�呆駯8*1霳痮�?b煥w#-d,�=x�O捇V�38?囟~�=样~欄賲_Up>4映y]#�L雵J-WV8o闭J买烝嫌Яモ搢 +N癄i�:U#+邐]v琑o兀1怎帵�@HF蟯z掖~詌鷸敇烾!刳遌H�潼a蚌4�@&�'e'�蹎NT煢栻v崦-坓�(鬄醵�	hQ�)ㄈe孮.�>c8曀黩6艇�P坓^.━阧"�9奶驸为伊y3礙J曍nk�#氷晑垫e7g!w�跎G1Z裆{k搨K長葎岴 -坓�(鬄�)傂螨-坓�(鬄�)傂螨c闲�
墸g.9n袆W_)5*窆H.9n袆W_)9E\$L釉迻E-;}k�;�?裏(�0杉s鶷z�0/�毩[蠌�:
�(6r鸽]
鴆"楞�(6r鸽]窫劋>糂撾嘕-WV8o蝿壒eV嘕-WV8o1F�;攃�Tq��l1h狐鬀搽嘕-WV8o`迼�)f�,箛��(�５栛魪o騮搰區嘾5F谤T�=8艖洈
笤�:奩5磛 甥�魮坩吗=8艖洈�逎9玧娦2q婥E�楠辎⒄W�3哀��%不�-豭u赸犕(Io闵�P�3Zp峢,浜�9醒菳3栨n把T0w珺掂h纛豁鄰鰊珷s頠岋V}m�梎 福�翣)�6{汁廴~踴実�
%�D=�軹r溓伱苞5鳻<玪<崐l8R噲.�=�b}�)B佞2軥眮谾迀�p`ka钛橄s%儯�黹弖骒	}尟+�(H�0婒�1^鰎袊y�!瘞姗x&軰R瘑G�-咮e 镹髰臼琇�x^笵A傮L鶾B蔲�(A灭:�	蕐�$�:曒F梥@茍0牊迥獢�'w�)���        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       ]                 .debug$S       l�               .data          h  m                           .rdata         b       5��                    .rdata               饸箘         L           .rdata         �       宗圐         �           .rdata                塇k         �           .rdata         $      痈u�         �           .rdata      	   %      搝               	    .rdata      
   �       lo�         T      
    .rdata         �       �"�         �          .rdata         �       /靹         �          .rdata      
   �       �!�         �      
    .rdata                -鱿�         $          .rdata         �       7�         Y          .rdata         �       0         �          .rdata         $      2��         �          .rdata         &      別          �          .rdata         &      ��         *          .rdata               � �         _          .rdata               �.         �          .rdata         #      w.>�         �          .rdata         �       '鞌         �          .rdata         !      襴�         1          .rdata               �
         f          .rdata         %      qt�         �          .rdata         �       蝼伜         �          .rdata         �       *芐                   .rdata         �       ;扸�         8          .rdata         �       ;��         l          .rdata         �       [*鎶         �          .rdata          !      檢Zp         �           .rdata      !   !      眀^         	      !    .rdata      "   �       (�"�         >      "    .rdata      #   �       R觹1         r      #    .rdata      $   %      nH�8         �      $    .rdata      %   %      s蔈�         �      %    .rdata      &   2      O鄓b               &    .rdata      '   &      �.ZV         E      '    .rdata      (   &      凭�5         y      (    .rdata      )   $      [7渞         �      )    .rdata      *   �        +�         �      *    .rdata      +   �       氌蔕               +    .rdata      ,   �       恩謓         K      ,    .rdata      -   �       'M#�               -    .rdata      .   �       峹g\         �      .    .rdata      /         昴墾         �      /    .rdata      0   '      �%�&         	      0    .rdata      1         牋水         Q	      1    .rdata      2         |茑�         �	      2    .rdata      3   -      鬧�-         �	      3    .rdata      4   -      泧WM         �	      4    .rdata      5   -      Ukㄕ         $
      5    .rdata      6   -      ０鶆         Y
      6    .rdata      7   -      (�X         �
      7    .rdata      8   -      %瓉W         �
      8    .rdata      9   -      蚯饫         �
      9    .rdata      :   *      q0V1         -      :    .rdata      ;   3      辒�         b      ;    .rdata      <         X檭         �      <    .rdata      =         嫞氟         �      =    .rdata      >         舯嚜                >    .rdata      ?   �       摸�         5      ?    .rdata      @   1      0玥         i      @    .rdata      A   %      О�         �      A    .rdata      B         hㄕ�         �      B    .rdata      C   (      笒a�         
      C    .rdata      D         饶-         =
      D    .rdata      E         蘳�7         r
      E    .rdata      F         }?釖         �
      F    .rdata      G         禡5�         �
      G    .rdata      H         o黮<               H    .rdata      I         恲Ga         F      I    .rdata      J         3�44         {      J    .rdata      K   *      a(赕         �      K    .rdata      L   *      �=嘭         �      L    .rdata      M         5浴N               M    .rdata      N         ~�         N      N    .rdata      O   %      h \<         �      O    .rdata      P         V�         �      P    .rdata      Q         �4赱         �      Q    .rdata      R   ,      l玽�         "      R    .rdata      S   >      葶T�         W      S    .rdata      T   4      窕贤         �      T    .rdata      U   7      #騅�         �      U    .rdata      V   �       l �(         �      V    .rdata      W   "      nw�         *      W    .rdata      X   !      煢i�         _      X    .rdata      Y   �       Fz`         �      Y    .rdata      Z         kO葲         �      Z    .rdata      [   &      2鯟�         �      [    .rdata      \         0R�;         2      \    .rdata      ]   -      簜�         g      ]    .rdata      ^         E         �      ^    .rdata      _         2��         �      _    .rdata      `         橾�:               `    .rdata      a         �*N^         ;      a    .rdata      b          5l         p      b    .rdata      c   O      鷱躢         �      c    .rdata      d   E      &k�         �      d    .rdata      e         _弔�               e    .rdata      f   0      c鞬�         D      f    .rdata      g   )      �	�         y      g    .rdata      h   �       0{-�         �      h    .rdata      i   �       �&�         �      i    .rdata      j   �       M釿               j    .rdata      k   �       浫�         I      k    .rdata      l   !      R
�7         }      l    .rdata      m         L晷�         �      m    .rdata      n   �       |	魄         �      n    .rdata      o   �       Kl               o    .rdata      p   $      ):艕         N      p    .rdata      q   �       �)�         �      q    .rdata      r         �1         �      r    .rdata      s         鯧�         �      s    .rdata      t         #蠱�         !      t    .rdata      u   0      xb�	         V      u    .rdata      v   �       衟�<         �      v    .rdata      w   �       鐃儑         �      w    .rdata      x         mX�         �      x    .rdata      y   !      �y�         (      y    .rdata      z   %      顸?�         ]      z    .rdata      {   &      衁�          �      {    .rdata      |         爖         �      |    .rdata      }         鄨歮         �      }    .rdata      ~   !      欮�         1      ~    .rdata         !      ;�          f          .rdata      �   !      UE�         �      �    .rdata      �   �       �g�         �      �    .rdata      �   !      V7瑡               �    .rdata      �   0       �5         9      �    .rdata      �   �       5輫�         n      �    .rdata      �   �       骠蚄         �      �    .rdata      �         _Q�         �      �    .rdata      �         Y竦�               �    .rdata      �         F_         @      �    .rdata      �         Z�         u      �    .rdata      �   �       $l         �      �    .rdata      �         貶y         �      �    .rdata      �   %      I�?�               �    .rdata      �         媪K
         H      �    .rdata      �   �       v鸇w         |      �    .rdata      �   �       E�         �      �    .rdata      �   !      }恓b         �      �    .rdata      �         NIh}               �    .rdata      �   $      鮨寁         N      �    .rdata      �   $      '輟U         �      �    .rdata      �   "      �2s�         �      �    .rdata      �   "      |屙         �      �    .rdata      �   �       蕸�
         "      �    .rdata      �   �       隫S	         V      �    .rdata      �   #      Oe�         �      �    .rdata      �   $      紆�         �      �    .rdata      �   �       潄         �      �    .rdata      �   �       .)         (      �    .rdata      �   �       �         \      �    .rdata      �         YA�         �      �    .rdata      �         '��         �      �    .rdata      �   <      Pc�         �      �    .rdata      �   +      续 �         /       �    .rdata      �         b蝂A         d       �    .rdata      �   '      d|蘎         �       �    .rdata      �   �       @e�"         �       �    .rdata      �   �       p栒�         !      �    .rdata      �   �       鄜}         6!      �    .rdata      �   �       P疘�         j!      �    .rdata      �   �       繥�         �!      �    .rdata      �         礁亿         �!      �    .rdata      �         9g:	         "      �    .rdata      �   )      6�n         ;"      �    .rdata      �         顔fE         p"      �    .rdata      �         榚w�         �"      �    .rdata      �         磌%(         �"      �    .rdata      �   0      氓
G         #      �    .rdata      �         夗珥         D#      �    .rdata      �         K�         y#      �    .rdata      �          VK�         �#      �    .rdata      �          4��         �#      �    .rdata      �   �       ＋�0         $      �    .rdata      �   �       �#y%         K$      �    .rdata      �   '      ≤y{         $      �    .rdata      �   0      �滒A         �$      �    .rdata      �   0      *�:�         �$      �    .rdata      �   4      弰4         %      �    .rdata      �   4      禝*         S%      �    .rdata      �   1      m�:         �%      �    .rdata      �   1      摊"         �%      �    .rdata      �   -      f簼         �%      �    .rdata      �         '枒m         '&      �    .rdata      �         �'鳿         \&      �    .rdata      �         藏闲         �&      �    .rdata      �         凍xz         �&      �    .rdata      �         .�3�         �&      �    .rdata      �         �印         0'      �    .rdata      �         哭X         e'      �    .rdata      �   
      嘂橰         �'      �    .rdata      �   �       f戺         �'      �    .rdata      �   �       R��         (      �    .rdata      �   6      ^�         6(      �    .rdata      �         `
>         k(      �    .rdata      �   &      )O�         �(      �    .rdata      �   &      LA�>         �(      �    .rdata      �   �       N潕�         
)      �    .rdata      �         PT�
         >)      �    .rdata      �   =      �+t         s)      �    .rdata      �   �       �>K         �)      �    .rdata      �   /      WL谒         �)      �    .rdata      �   &      I敷�         *      �    .rdata      �   &      泻鲹         F*      �    .rdata      �   &      �         {*      �    .rdata      �   &      駦         �*      �    .rdata      �   .      YdN         �*      �    .rdata      �   !      峥卫         +      �    .rdata      �   #      �:s`         O+      �    .rdata      �   �       c+�         �+      �    .rdata      �   �       呁>�         �+      �    .rdata      �   �       W頭�         �+      �    .rdata      �         膵跘          ,      �    .rdata      �   �       C,6         T,      �    .rdata      �   3      �         �,      �    .rdata      �   !      :鹙�         �,      �    .rdata      �   .      ~�         �,      �    .rdata      �   �       g�         '-      �    .rdata      �   )      ^E�         [-      �    .rdata      �   2      梴膩         �-      �    .rdata      �   '      諺碐         �-      �    .rdata      �   $      v[$         �-      �    .rdata      �         �=冠         ..      �    .rdata      �   �       釜鸛         c.      �    .rdata      �   �       r蚆M         �.      �    .rdata      �         P>>�         �.      �    .rdata      �   �       �#          /      �    .rdata      �   �       3��         4/      �    .rdata      �   "      E�         h/      �    .rdata      �   -      訹匣         �/      �    .rdata      �   /      �1T         �/      �    .rdata      �   /      邯�         0      �    .rdata      �         Uo�*         <0      �    .rdata      �   "      
�         q0      �    .rdata      �         盻         �0      �    .rdata      �         ;�#�         �0      �    .rdata      �         o�         1      �    .rdata      �         dT         E1      �    .rdata      �         e%N�         z1      �    .rdata      �   #      z8         �1      �    .rdata      �         Uw硢         �1      �    .rdata      �         ﹑3         2      �    .rdata      �   %      lC�         N2      �    .rdata      �   #      p鵩�         �2      �    .rdata      �   #      ��         �2      �    .rdata      �   #      �
隵         �2      �    .rdata      �         傁N:         "3      �    .rdata      �   �       殄�          W3      �    .rdata      �   #      �21;         �3      �    .rdata      �   #      5敺�         �3      �    .rdata         #      2�4�         �3          .rdata        -      >�         *4         .rdata        �       UR�0         _4         .rdata        �       涋姟         �4         .rdata        �       W��         �4         .rdata        �       (辮         �4         .rdata        �       輍�         /5         .rdata        �       �7獐         c5         .rdata        <      厩c�         �5         .rdata      	  =      |赊$         �5      	   .rdata      
        g属�          6      
   .rdata        )      ��         56         .rdata        )      U         j6         .rdata      
        '?�!         �6      
   .rdata              蒗w�         �6         .rdata        1      �淶         	7         .rdata              	�	�         >7         .rdata               槢�         s7         .rdata        &      頲         �7         .rdata        &      獊セ         �7         .rdata        &      5D艎         8         .rdata        &      qη�         G8         .rdata              �(`�         |8         .rdata        
      髢y�         �8         .rdata        '      邹钻         �8         .rdata        '      ye�2         9         .rdata        �       睖Vm         P9         .rdata        �       憳D�         �9         .rdata        �       �         �9         .rdata        �       W哲�         �9         .rdata        .      �湨          :         .rdata        5      睿ｊ         U:         .rdata         0      綒�         �:          .rdata      !  .      撋1�         �:      !   .rdata      "  .      Qf         �:      "   .rdata      #  .      /篱�         );      #   .rdata      $        櫻p�         ^;      $   .rdata      %        kf爇         �;      %   .rdata      &  /      2s詌         �;      &   .rdata      '  !      q07�         �;      '   .rdata      (  �       #1M|         2<      (   .rdata      )  (      %wM�         f<      )   .rdata      *  !      竴m�         �<      *   .rdata      +  !      �3F         �<      +   .rdata      ,         �韾�         =      ,   .rdata      -  &      剤犴         9=      -   .rdata      .  "      瘩�	         n=      .   .rdata      /  "      宱E�         �=      /   .rdata      0        jx         �=      0   .rdata      1  x       �O         
>      1   .rdata      2  �       夲擩         A>      2   .rdata      3  �       c^         u>      3   .rdata      4  �       w潢         �>      4   .rdata      5  �       �憖         �>      5   .rdata      6        ;:         ?      6   .rdata      7        h�4�         F?      7   .rdata      8        溼咡         {?      8   .rdata      9        �6A         �?      9   .rdata      :  &      岟J�         �?      :   .rdata      ;  �       �0�5         @      ;   .rdata      <  �       ~澰�         N@      <   .rdata      =  $      劑%�         侤      =   .rdata      >  (      2栅d         稝      >   .rdata      ?        [U�         霡      ?   .rdata      @  �       gx         !A      @   .rdata      A        娡         UA      A   .rdata      B  *      u峝�         夾      B   .rdata      C  $      ��         続      C   .rdata      D  �       鞃x         駻      D   .rdata      E        IU@_         &B      E   .rdata      F        m-         [B      F   .rdata      G  %      攈lP         怋      G   .rdata      H  +      肝X         臖      H   .rdata      I  6      ��         鶥      I   .rdata      J  6      -��         /C      J   .rdata      K  .      k0Y         dC      K   .rdata      L  /      �         機      L   .rdata      M  .      獁橋         虲      M   .rdata      N  "      f�         D      N   .rdata      O  "      �楽G         7D      O   .rdata      P  �       �p�         lD      P   .rdata      Q        @览s         燚      Q   .rdata      R        �┺         訢      R   .rdata      S        嫔齠         	E      S   .rdata      T  '      庭         >E      T   .rdata      U          F	�         sE      U   .rdata      V        �\�         ‥      V   .rdata      W         +�         軪      W   .rdata      X        韬箮         F      X   .rdata      Y         �5d�         GF      Y   .rdata      Z        玻�         |F      Z   .rdata      [  &      l�         癋      [   .rdata      \        |ba�         錐      \   .rdata      ]  �       �揌         G      ]   .rdata      ^  �       蹥=�         MG      ^   .rdata      _  �       轎+v         丟      _   .rdata      `        �;嫔         礕      `   .rdata      a  #      陆>         闓      a   .rdata      b  )      嵡9�         H      b   .rdata      c        �:�3         TH      c   .rdata      d  "      ^鸿�         塇      d   .rdata      e  '      ?欢         綡      e   .rdata      f  -      (r#L         驢      f   .rdata      g  $      嬧�&         'I      g   .rdata      h  #      g︻D         \I      h   .rdata      i  .      贊*�         慖      i   .rdata      j        4�          艻      j   .rdata      k        腍麻         鸌      k   .rdata      l  #      偉�         0J      l   .rdata      m  #      i粎�         eJ      m   .rdata      n         液�         欽      n   .rdata      o        Q`议         蜫      o   .rdata      p        簁:�         K      p   .text$mn    q  �  	   倯Q     .debug$S    r  �         q   .text$mn    s  L      R:�     .debug$S    t  �         s   .text$mn    u  9      涐l     .debug$S    v           u   .text$mn    w  u      uu$     .debug$S    x           w   .text$mn    y  n       0}K�     .debug$S    z           y   .text$mn    {         O�1z     .debug$S    |  ,  
       {   .text$mn    }  @       披     .debug$S    ~  \         }   .text$mn             |Ａ.     .debug$S    �  T            .text$mn    �  _      磬e     .debug$S    �  t         �   .text$mn    �  
       Hd     .debug$S    �  8  
       �   .text$mn    �         /粲�     .debug$S    �  @  
       �   .text$mn    �  n       稜     .debug$S    �  �         �   .text$mn    �  �       u昘     .debug$S    �  d         �   .text$mn    �  J       ndG     .debug$S    �            �   .text$mn    �         "y2      .debug$S    �  \         �   .text$mn    �  �      ,p咄     .debug$S    �  d         �   .text$mn    �  �      N{�     .debug$S    �  �  
       �   .text$mn    �  �   	   9$゜     .debug$S    �           �   .text$mn    �  �   	   X玒�     .debug$S    �  �         �   .text$mn    �  �   
   逮牂     .debug$S    �           �   .text$mn    �  +      晪�     .debug$S    �           �   .text$mn    �  Y      洸
C     .debug$S    �  �         �   .text$mn    �  �   
   x�$>     .debug$S    �  �         �   .text$mn    �  	     龐Iq     .debug$S    �  t  $       �   .text$mn    �  �      �惜     .debug$S    �  �  
       �   .text$mn    �  �   
   瑬娽     .debug$S    �  \         �   .text$mn    �  �      繘馩     .debug$S    �  �         �   .text$mn    �  �   
   �J     .debug$S    �  �         �   .text$mn    �  �   
   {決R     .debug$S    �  \         �   .text$mn    �  �   
   <M澋     .debug$S    �  �         �   .text$mn    �  E      u扆G     .debug$S    �           �   .text$mn    �  r      唶風     .debug$S    �  p  
       �   .text$mn    �  z      �!<     .debug$S    �  �         �   .text$mn    �  S      |拑J     .debug$S    �  $         �   .text$mn    �  m      q��     .debug$S    �  |  
       �   .text$mn    �  �      窈�     .debug$S    �  �          �   .text$mn    �  )      � 疞     .debug$S    �  �          �   .text$mn    �  )      � 疞     .debug$S    �  �          �   .text$mn    �  �   
   擰厛     .debug$S    �  �         �   .text$mn    �  �   
   %4	     .debug$S    �  �         �   .text$mn    �  +      揺     .debug$S    �  �          �   .text$mn    �  3      頁躻     .debug$S    �  L  
       �   .text$mn    �  3      U茵�     .debug$S    �  P  
       �   .text$mn    �        鑝zt     .debug$S    �  <  
       �   .text$mn    �         +R�     .debug$S    �  L  
       �   .text$mn    �  3      v抟�     .debug$S    �  T  
       �   .text$mn    �  �      #5�     .debug$S    �  �         �   .text$mn    �  3      螣�     .debug$S    �  P  
       �   .text$mn    �  3      �;=     .debug$S    �  L  
       �   .text$mn    �  6     B&挤     .debug$S    �  �         �   .text$mn    �  e      �l�     .debug$S    �  �         �   .text$mn    �  6      缠稅     .debug$S    �  <         �   .text$mn    �  3      寒�     .debug$S    �  L  
       �   .text$mn    �  3      櫌5�     .debug$S    �  T  
       �   .text$mn    �  3      !�X     .debug$S    �  P  
       �   .text$mn    �  �   	   灎P     .debug$S    �  ,         �   .text$mn    �  �     @� 8     .debug$S    �  d         �   .text$mn    �  �  )   筚C     .debug$S    �  L  6       �       8K      �       IK      �       ZK      �       vK      �       匥      �       楰      �             �       禟      �       荎      �       豄      �       镵      �       L      �       L      �       "L      �       5L      �       IL      �       aL      �       |L      �       昄      �       癓      �       芁      �       贚      �       頛      �       M      �       M      �       -M      �       AM      �       UM      �       pM      �       婱      �             �       筂      �       覯      �       鍹      �       鶰      �       N      �       (N      �       4N      �       @N      �       RN      �   strcspn          strncmp          strncpy          strspn           strtoul              hN               僋                     �       癗      y       縉      �       蠳      �       錘      }       鵑      {       O             O      �       1O      �       HO      �       aO      �       wO               嘜      w       燨      q       砄      u       臤      s       銸               馩               �O           fmaxf            fminf                
P      �       P      �       'P      �       4P               EP           memcpy           memset           $LN12       �   $LN74       �   $LN5        �   $LN13       �   $LN7        �   $LN9        �   $LN9        �   $LN19       �   $LN13       �   $LN12       �   $LN29       �   $LN5        �   $LN5        �   $LN5        �   $LN5        �   $LN5        �   $LN5        �   $LN5        �   $LN5        �   $LN20       �   $LN24       �   $LN27       �   $LN24       �   $LN23       �   $LN23       �   $LN7        �   $LN24       �   $LN14       �   $LN58       �   $LN26       �   $LN196      �   $LN5        �   $LN12       �   $LN17       �   $LN5        �   $LN5        �   $LN15       y   $LN4        �   $LN19       w   $LN89       q   $LN4        u   $LN4        s   .xdata      �         �9��       ]P      �   .pdata      �        D痚       vP      �   .xdata      �         %蚘%�       嶱      �   .pdata      �        覭=杂             �   .xdata      �         �9��       縋      �   .pdata      �         ~ち       鉖      �   .xdata      �         �9��       Q      �   .pdata      �        X崘=�       Q      �   .xdata      �         �9��       3Q      �   .pdata      �        %舂鄢       NQ      �   .xdata      �         �9��       hQ      �   .pdata      �        頄u畀       {Q      �   .xdata      �         �9��       峇      �   .pdata      �        j殿K�       ≦      �   .xdata      �         RFP�       翾      �   .pdata      �        ⑼       跶      �   .xdata      �         jE甛�       驫      �   .pdata      �        咝<�       R      �   .xdata      �        �捈�       $R      �   .pdata      �        槏U鲹       >R      �   .xdata      �        _A鴵       XR      �   .pdata      �        纒蛭�       rR      �   .xdata      �        3:@厯       孯      �   .pdata      �        褴V葥             �   .xdata      �         3�倳       繰      �   .pdata      �        X髮檿       酭      �   .xdata      �        >,	�       S      �   .pdata               �1N�       #S          .xdata              抾赸�       ES         .pdata              娢瀛�       gS         .xdata               3�倵       塖         .pdata              w佼�                .xdata              ╘.�       糞         .pdata              招��       譙         .xdata              �
鄲�       騍         .pdata              埠窏       
T         .xdata      	        9:ず�       (T      	   .pdata      
        �0d�       CT      
   .xdata              鯘��       ^T         .pdata              6鷷�       yT         .xdata      
         �9��       擳      
   .pdata              濼B�       疶         .xdata               �9��       蒚         .pdata              濼B�       錞         .xdata               �9��        U         .pdata              濼B�        U         .xdata               �9��       ?U         .pdata              濼B�       bU         .xdata               �9��       刄         .pdata              濼B�                .xdata               �9��       臮         .pdata              濼B�       鑅         .xdata               �9��       
V         .pdata              濼B�       (V         .xdata               �9��       EV         .pdata              濼B�       aV         .xdata               （亵�       |V         .pdata              D痚       榁         .xdata               %蚘%�       砎         .pdata               xR	-�       蟅          .xdata      !         %蚘%�       闢      !   .pdata      "        �鋛�       	W      "   .xdata      #         %蚘%�       'W      #   .pdata      $        xR	-�       CW      $   .xdata      %         （亵�       ^W      %   .pdata      &        D麔;�       zW      &   .xdata      '         （亵�       昗      '   .pdata      (        ]騂1�       盬      (   .xdata      )         �9��       蘔      )   .pdata      *        壧}a�       颳      *   .xdata      +         （亵�       X      +   .pdata      ,        邴'鹘       /X      ,   .xdata      -         （亵�       LX      -   .pdata      .        弋樥       lX      .   .xdata      /        bH�3�       媂      /   .pdata      0        X賦       璛      0   .xdata      1        2﹨赆       蝀      1   .pdata      2        })�       馲      2   .xdata      3        冦R樶       Y      3   .pdata      4        |况坚       7Y      4   .xdata      5        懨�	�       ZY      5   .pdata      6        >踽       }Y      6   .xdata      7        �3/C�       燳      7   .pdata      8        h圞�       肶      8   .xdata      9        �'佱       鎅      9   .pdata      :        碭瀁�       	Z      :   .xdata      ;         （亵�       ,Z      ;   .pdata      <        �"l�       GZ      <   .xdata      =         僣X魺       aZ      =   .pdata      >        企&U�       }Z      >   .xdata      ?        �       榋      ?   .pdata      @        5叵釤       礪      @   .xdata      A        酓曔�       襔      A   .pdata      B        h颬:�       颶      B   .xdata      C        �;��       [      C   .pdata      D        �2FH�       )[      D   .xdata      E        簻o湡       F[      E   .pdata      F        ��       c[      F   .xdata      G        *q鞜       �[      G   .pdata      H        l霳緹       漑      H   .xdata      I         �9��       篬      I   .pdata      J         ~       賉      J   .xdata      K         1�7�       鱗      K   .pdata      L        萣�5�       \      L   .xdata      M        c檊�       \      M   .pdata      N        Hkx�       3\      N   .xdata      O        �/J#�       H\      O   .pdata      P        (j嘧�       ]\      P   .xdata      Q         )撨       r\      Q   .pdata      R        晦鱰�       哱      R   .xdata      S        �r暨       橽      S   .pdata      T        H棯�       甛      T   .xdata      U        W4犕�       肻      U   .pdata      V        _ 靚�       豛      V   .xdata      W         �9��       韁      W   .pdata      X        }y9婊       ]      X   .xdata      Y         �9��        ]      Y   .pdata      Z        }y9婀       >]      Z   .xdata      [         O韞       []      [   .pdata      \        壊a舮       r]      \   .xdata      ]         
�       圿      ]   .pdata      ^        壊a艊             ^   .xdata      _         %蚘%w       胅      _   .pdata      `        魺颁w       鋆      `   .xdata      a         Ekq       ^      a   .pdata      b        o炥�q       ^      b   .xdata      c        褥ǒq       9^      c   .pdata      d        砄�
q       U^      d   .xdata      e        虆雧q       q^      e   .pdata      f        e繽剄       峖      f   .xdata      g        虆雧q             g   .pdata      h        0狍Bq       臹      h   .xdata      i         （亵u       醊      i   .pdata      j        VH倸u       鸮      j   .xdata      k         （亵s       _      k   .pdata      l        ⒆2~s       ;_      l   .xdata      m         Uqi箯       a_      m   .pdata      n        欫聫       u_      n   .xdata      o         （亵�       坃      o   .pdata      p        暷|脩             p   .xdata      q        忸H坫       筥      q   .pdata      r        ;6�       蝊      r   .xdata      s        澇J炪       鈅      s   .pdata      t        ��8�       鴂      t   .xdata      u        譎菑�       `      u   .pdata      v        P烅z�       $`      v   .xdata      w        鳆聼�       :`      w   .pdata      x        fy0�       P`      x   .xdata      y        譎菑�       f`      y   .pdata      z        w�#欍       |`      z   _glfw            .rdata      {  	       瞬�8         抈      {   .rdata      |         鎆鎩         璥      |   .rdata      }         %	薟         羆      }   .rdata      ~         骁         誤      ~   .rdata               柯         閌         .rdata      �         ^s瓞         齚      �   .rdata      �         施X         a      �   .rdata      �         ��5         ,a      �   .rdata      �  
       �
�4         Da      �   .rdata      �         亅簻         da      �   .rdata      �  
       �;挢         卆      �   .rdata      �         /!牛               �   .rdata      �         \z         縜      �   .rdata      �         L.g�         謅      �   .rdata      �         k煅         餫      �   .rdata      �         *箬#         	b      �   .rdata      �         繈嵾         "b      �   .rdata      �  
       �#�         Ab      �   .rdata      �         爆穭         ab      �   .rdata      �         饾瑵         yb      �   .rdata      �         S貟5         慴      �   .rdata      �         閴,         猙      �   .rdata      �         甼19         胋      �   .rdata      �         7X6�         豣      �   .rdata      �         驉o         c      �   .rdata      �  1       帟i4         Ac      �   .rdata      �         璖-�         zc      �   .rdata      �         釋＂               �   .rdata      �         
�	m         觕      �   .rdata      �  $       Q8         d      �   .rdata      �         8掩         Gd      �   .rdata      �         +�z         �d      �   .rdata      �         賩7�         眃      �   .rdata      �         鰀#�         蔰      �   .rdata      �         v靛�         骴      �   .rdata      �         艳�         e      �   .rdata      �         y$@�         e      �   .rdata      �         沏         3e      �   .rdata      �         ��         Ke      �   .rdata      �         V6]`         ce      �   .rdata      �         $�         se      �       媏           _fltused         .debug$T    �  |                 .chks64     �  (                漞  _glfwDefaultMappings ??_C@_0GC@KIFEGHB@03000000fa2d0000010000000000000@ ??_C@_0BBM@KEDGELHN@03000000c82d0000203800000000000@ ??_C@_0JF@MFNKBEDG@03000000c82d0000095100000000000@ ??_C@_0BCA@BCGGNNCE@03000000c82d000011ab00000000000@ ??_C@_0BCE@CFNIKJCA@03000000c82d0000103800000000000@ ??_C@_0BCF@MBPBHECE@03000000c82d0000009000000000000@ ??_C@_0NN@EKBBEDOM@03000000c82d0000065000000000000@ ??_C@_0MI@LCKMPFJJ@03000000c82d0000510600000000000@ ??_C@_0LI@CLAPJPEB@03000000c82d0000015100000000000@ ??_C@_0MH@JBIEFOEI@03000000c82d0000031000000000000@ ??_C@_0BCA@OLMCGDIA@03000000c82d0000202800000000000@ ??_C@_0MH@GNCKGMLL@03000000c82d0000801000000000000@ ??_C@_0IN@KIPMJDI@03000000c82d0000045100000000000@ ??_C@_0BCE@CONCKJMB@03000000c82d0000019000000000000@ ??_C@_0BCG@EICBBMKF@03000000c82d0000159000000000000@ ??_C@_0BCG@FGNJGHJF@03000000c82d0000652800000000000@ ??_C@_0BBN@FMDJFAID@0300000002200000009000000000000@ ??_C@_0BBN@DDIMFOMP@0300000020380000090000000000000@ ??_C@_0BCD@KBJOCLC@03000000c82d0000036000000000000@ ??_C@_0MO@IJHIOCMG@03000000c82d0000286700000000000@ ??_C@_0BCB@FAGFKFBC@03000000c82d0000013000000000000@ ??_C@_0BBM@CNGNJMCJ@03000000c82d0000006000000000000@ ??_C@_0BCF@OOFHHKMG@03000000c82d0000006100000000000@ ??_C@_0JK@EKFMIBLA@03000000c82d000021ab00000000000@ ??_C@_0KC@BDMHLJAH@0300000010280000090000000000000@ ??_C@_0KC@KGMCONBG@03000000c82d0000302800000000000@ ??_C@_0JJ@DHPDMCKL@03000000c82d0000003000000000000@ ??_C@_0LC@FKHGODJA@03000000c82d0000129000000000000@ ??_C@_0BCB@NICJKIFJ@03000000c82d000020ab00000000000@ ??_C@_0BCB@PGBBGDNC@03000000c82d0000402800000000000@ ??_C@_0LC@FALCBLOD@03000000c82d0000622800000000000@ ??_C@_0LJ@DONCMJNG@03000000c82d0000035100000000000@ ??_C@_0BCF@BMOGEGNJ@03000000c82d0000016000000000000@ ??_C@_0BCF@KHBKMEME@03000000c82d0000016100000000000@ ??_C@_0BDC@PMNHHHHJ@03000000c82d0000012100000000000@ ??_C@_0BCG@DHLFHLM@03000000c82d0000026000000000000@ ??_C@_0BCG@GAMGMHOH@03000000c82d0000026100000000000@ ??_C@_0BCE@JJDBCGKI@03000000c82d0000003100000000000@ ??_C@_0JL@IDKPNGMK@03000000c82d0000189000000000000@ ??_C@_0NO@KCLNENJG@03000000c82d0000303200000000000@ ??_C@_0LK@IEAMBPJF@03000000a0050000323200000000000@ ??_C@_0JL@GGABLAON@03000000a30c0000270000000000000@ ??_C@_0JL@MEEFIFEH@03000000a30c0000280000000000000@ ??_C@_0BBF@CLNKBPGE@030000008f0e0000120000000000000@ ??_C@_0BCH@GKMNBEOK@03000000c0110000035500001101000@ ??_C@_0BBK@CPBMMLKK@03000000fa190000f0ff00000000000@ ??_C@_0BBO@ILBJPILH@030000006f0e0000141300000000000@ ??_C@_0BCN@GIEEMMPB@03000000341a0000360800000000000@ ??_C@_0BCN@IMCBAJO@030000006f0e0000026300000000000@ ??_C@_0BCN@JADNPMFA@030000006f0e0000110100000000000@ ??_C@_0BCN@MBGMCHKG@030000006f0e0000140100000000000@ ??_C@_0BCN@BNINGACN@030000006f0e0000140200000000000@ ??_C@_0BCN@BCBEDKCA@030000006f0e0000190100000000000@ ??_C@_0BCN@IFHHFAPH@030000006f0e00001a0100000000000@ ??_C@_0BCK@OFGGBLGN@03000000d62000001d5700000000000@ ??_C@_0BDD@MHIPFANA@0300000049190000190400000000000@ ??_C@_0BBE@DMHFNEP@0300000071010000190400000000000@ ??_C@_0BBC@LKELPOKL@03000000ef050000030000000000000@ ??_C@_0BBN@DJKOMBLI@03000000d6200000e55700000000000@ ??_C@_0JO@HBLEAOAI@03000000c0110000135200000000000@ ??_C@_0BDB@KNKJIBDH@030000006f0e0000320100000000000@ ??_C@_0BCF@NLOPKJKL@03000000d62000002a7900000000000@ ??_C@_0BBG@NOFGFFJE@03000000bc200000601200000000000@ ??_C@_0BCI@HBCHBLOC@03000000bc200000005500000000000@ ??_C@_0BBL@MNJAPGHD@03000000bc200000631200000000000@ ??_C@_0BBL@NHKNENLJ@03000000bc200000632100000000000@ ??_C@_0BBL@HCLFABAI@03000000bc200000641200000000000@ ??_C@_0BBL@BGGBHDMD@03000000c0110000055500000000000@ ??_C@_0BBL@NMDIMCBK@03000000c0110000065500000000000@ ??_C@_0BBI@BKECFDAD@0300000079000000070000000000000@ ??_C@_0BBI@EPDBKPKA@0300000080830000030000000000000@ ??_C@_0BCK@DCNKADHN@030000006b140000005500000000000@ ??_C@_0BCK@MNABGIK@030000006b140000010300000000000@ ??_C@_0BBP@ECGIDGCJ@03000000120c0000210e00000000000@ ??_C@_0BBL@EFBOJJAL@0300000066f70000050000000000000@ ??_C@_0BCF@BIADCONP@03000000d81d00000b0000000000000@ ??_C@_0BBH@EKFHCAMB@03000000e8200000605800000000000@ ??_C@_0BBK@NKANFPJB@0300000045750000040100000000000@ ??_C@_0BCM@OALNNLEB@030000005e0400008e0200000000000@ ??_C@_0BDO@LBKBGMKN@030000005e040000a10200000000000@ ??_C@_0BDE@CKLILCMC@030000005e040000ff0200000000000@ ??_C@_0BDH@LGMFPGNL@030000005e040000ea0200000000000@ ??_C@_0PH@HDPKEOEB@0300000026090000888800000000000@ ??_C@_0BCC@ICCBMGAM@03000000a306000022f600000000000@ ??_C@_0BCB@HKBKHEFP@0300000045130000083000000000000@ ??_C@_0NF@EPHCAKNM@030000007d040000084000000000000@ ??_C@_0BAE@PFJOMAIP@03000000791d0000010300000000000@ ??_C@_0BCG@MGGCIPBD@03000000bd12000002e000000000000@ ??_C@_0BBG@DBBHKPMM@030000008f0e0000091000000000000@ ??_C@_0BCN@FPGGBFLP@030000006f0e0000300100000000000@ ??_C@_0BBJ@GCDIHNCJ@03000000b8050000041000000000000@ ??_C@_0BBJ@MLHIDNOG@03000000b8050000061000000000000@ ??_C@_0BBK@LLHMDGJD@03000000120c0000f61c00000000000@ ??_C@_0BAP@BKIFCCAA@030000008f0e00000f3100000000000@ ??_C@_0BAC@DNICHFEF@03000000341a0000010800000000000@ ??_C@_0BEP@LJAOBMNM@030000006f0e0000840100000000000@ ??_C@_0BEF@OFEGMHGG@030000006f0e0000800100000000000@ ??_C@_0BBB@JKCCMJLF@0300000085210000020100000000000@ ??_C@_0BDA@CMOFFENL@030000000d0f0000850000000000000@ ??_C@_0BCJ@GDBCPJIF@030000000d0f0000840000000000000@ ??_C@_0OH@PKEJNDDF@030000000d0f0000870000000000000@ ??_C@_0OH@FOPKIOOK@030000000d0f0000880000000000000@ ??_C@_0OD@GOLNACF@030000000d0f0000270000000000000@ ??_C@_0OA@BPMCGMAH@78696e7075740300000000000000000@ ??_C@_0BCB@JPNPNPJC@0300000079000000220100000000000@ ??_C@_0BBM@IDFPDLMB@0300000066f70000010000000000000@ ??_C@_0OB@DOLNAILH@0300000026090000262500000000000@ ??_C@_0ON@DOLMEDL@0300000079000000461800000000000@ ??_C@_0BCE@HLGICLNK@030000008f0e00000d3100000000000@ ??_C@_0LK@DEPDOLNO@0300000028040000014000000000000@ ??_C@_0BBE@BNPLHIOK@03000000ac0500003d0300000000000@ ??_C@_0BBE@JFJIACOB@03000000ac0500004d0400000000000@ ??_C@_0BBL@BABJOOFG@03000000ffff0000000000000000000@ ??_C@_0BDA@LBDFNLMA@03000000c0110000014000000000000@ ??_C@_0OG@IHHMBDDK@030000009b280000320000000000000@ ??_C@_0OG@DMCABHAN@030000009b280000600000000000000@ ??_C@_0BBB@MOEBBOIH@030000008305000009a000000000000@ ??_C@_0BCB@CIAKNBHG@030000008305000031b000000000000@ ??_C@_0BCF@LMGAPCEG@0300000045130000001000000000000@ ??_C@_0BCG@HFMJCAPB@030000005c1a0000333000000000000@ ??_C@_0BBM@PJHGKLCN@03000000300f00000b0100000000000@ ??_C@_0BBM@ENBFFJGN@03000000f0250000c28300000000000@ ??_C@_0BCB@CDINELNI@03000000f025000021c100000000000@ ??_C@_0BCB@KIGAAAPL@03000000f0250000c38300000000000@ ??_C@_0BCB@GKHPJHJF@03000000f0250000c48300000000000@ ??_C@_0MI@KAOJFFGI@030000007d040000054000000000000@ ??_C@_0BCB@CDNPOFJG@03000000341a0000030200000000000@ ??_C@_0BDA@KMJLBBJI@030000000d0f0000490000000000000@ ??_C@_0NA@FFFKKMHD@030000001008000001e100000000000@ ??_C@_0OJ@OOLHGLBN@03000000d8140000086200000000000@ ??_C@_0BBM@NFNOIOJB@0300000063250000260500000000000@ ??_C@_0BBF@BFOGCKNH@030000000d0f00002d0000000000000@ ??_C@_0BBH@LKFOHLNE@030000000d0f00005f0000000000000@ ??_C@_0BBH@LCHOLIJA@030000000d0f00005e0000000000000@ ??_C@_0OM@PAPEIDEH@030000000d0f0000400000000000000@ ??_C@_0BBP@HFNLKKMF@030000000d0f0000540000000000000@ ??_C@_0BCF@IAGAPBPO@030000000d0f0000090000000000000@ ??_C@_0BBP@BICCDPK@030000000d0f00004d0000000000000@ ??_C@_0OJ@NCDOGEIN@030000000d0f0000920000000000000@ ??_C@_0PH@JHCLCGIM@030000000d0f0000160000000000780@ ??_C@_0BCB@MKBJECLN@030000000d0f00009c0000000000000@ ??_C@_0BBM@FNOHJIMD@030000000d0f0000c10000000000000@ ??_C@_0BCE@JNCBHNAG@030000000d0f00006e0000000000000@ ??_C@_0BCE@LONHMMNE@030000000d0f0000660000000000000@ ??_C@_0BCC@HHLPIDID@030000000d0f0000550000000000000@ ??_C@_0BCC@ICCBFEBO@030000000d0f0000ee0000000000000@ ??_C@_0MP@NNGGKFCK@0300000025090000001700000000000@ ??_C@_0LL@KLNOPNHB@030000008f0e0000133000000000000@ ??_C@_0BCD@KBOGKJIK@03000000d81d00000f0000000000000@ ??_C@_0BCE@PHFLGEEP@03000000d81d0000100000000000000@ ??_C@_0LN@ELDHBANA@0300000083050000602000000000000@ ??_C@_0OI@FJCLICAJ@03000000b5070000140300000000000@ ??_C@_0PF@DFFMCMNA@030000006f0e0000240100000000000@ ??_C@_0BBC@LOLNAEDE@03000000ac0500002c0200000000000@ ??_C@_0BBK@GEMHLGCN@0300000049190000020400000000000@ ??_C@_0BDM@BFJPFEKL@0300000049190000030400000000000@ ??_C@_0BCL@PCNFJELE@030000006e0500000a2000000000000@ ??_C@_0BBD@HKHEBCPH@030000006e050000052000000000000@ ??_C@_0BCH@BOPAENED@030000006e050000032000000000000@ ??_C@_0PF@BCABPIDK@030000006e050000072000000000000@ ??_C@_0ME@PNFMJONN@030000007e050000062000000000000@ ??_C@_0ME@GIJLHGEN@030000007e050000062000000100000@ ??_C@_0ME@JKMAKHPN@030000007e050000072000000000000@ ??_C@_0ME@PAHEPGN@030000007e050000072000000100000@ ??_C@_0BBN@EJPLMIPB@03000000bd12000003c000001001000@ ??_C@_0BBD@DCBELLKM@03000000bd12000003c000000000000@ ??_C@_0BCJ@OFAKENFF@03000000242f00002d0000000000000@ ??_C@_0BBJ@CMBLEBDK@03000000242f00008a0000000000000@ ??_C@_0BBO@PNIOEBFD@0300000079000000020000000000000@ ??_C@_0BBP@CEOMIJKI@030000006d040000d1ca00000000000@ ??_C@_0BDA@PPKEBMHL@030000006d040000d2ca00000000000@ ??_C@_0BBF@GOLEDGAH@030000006d04000011c200000000000@ ??_C@_0BBP@NCCKJBD@030000006d04000016c200000000000@ ??_C@_0BCA@IDEGIHPL@030000006d04000018c200000000000@ ??_C@_0BCA@PDBJCLJJ@030000006d04000019c200000000000@ ??_C@_0NO@NNNFDMKP@030000006d0400001ac200000000000@ ??_C@_0PP@COADMKLJ@030000006d0400000ac200000000000@ ??_C@_0BCH@DHEFONIG@0300000038070000665200000000000@ ??_C@_0BDA@PJFOCFDI@0300000038070000503200000000000@ ??_C@_0BDA@HBJEDFJC@0300000038070000508200000000000@ ??_C@_0BDE@NDAIINLM@0300000038070000843300000000000@ ??_C@_0BDE@MNGLEAIF@0300000038070000848300000000000@ ??_C@_0BDB@PPKHIPGK@0300000038070000813400000000000@ ??_C@_0BDB@OHAMDKML@0300000038070000818400000000000@ ??_C@_0BCN@NICPPBBL@0300000038070000625200000000000@ ??_C@_0BBD@FGLPEKLC@0300000038070000803400000000000@ ??_C@_0BBD@GHNGPLFD@0300000038070000808400000000000@ ??_C@_0BBK@FBBILDLI@0300000038070000853200000000000@ ??_C@_0BBL@JKCMMIPG@0300000038070000388800000000000@ ??_C@_0BBD@LABNEKLL@0300000038070000188800000000000@ ??_C@_0BBP@KNBKPMNJ@0300000038070000808100000000000@ ??_C@_0BBN@MLCKKOPD@030000002a060000102400000000000@ ??_C@_0BAK@EANFDDAF@030000009f000000adbb00000000000@ ??_C@_0MA@LPHGGGL@0300000025090000012800000000000@ ??_C@_0PC@EBDFKGFN@0300000079000000441800000000000@ ??_C@_0BDG@JHAIGEPC@0300000079000000431800000000000@ ??_C@_0BBG@BDLNPAJM@03000000242f0000730000000000000@ ??_C@_0BCG@NFDJDGAI@0300000079000000d21800000000000@ ??_C@_0BCG@GLPHDIGN@03000000d620000010a700000000000@ ??_C@_0PI@JHAKKPHF@030000008f0e0000103000000000000@ ??_C@_0BBD@DBNHIIMF@0300000025090000e80300000000000@ ??_C@_0BDN@CCAJAJCK@0300000079000000001800000000000@ ??_C@_0KL@BLLPLAOA@0300000079000000241800000000000@ ??_C@_0BCP@NIMGPLGA@0300000038070000638200000000000@ ??_C@_0BCG@LJNCMOGI@03000000c62400002a8900000000000@ ??_C@_0BCG@MGNGMDPB@03000000c62400002b8900000000000@ ??_C@_0BCG@PCDGGODL@03000000c62400001a8900000000000@ ??_C@_0BCG@NCIHPENA@03000000c62400001b8900000000000@ ??_C@_0BCO@DODDAFEB@03000000efbe0000edfe00000000000@ ??_C@_0BCB@GILNGNCB@0300000025090000668800000000000@ ??_C@_0BCD@MDFEPGHO@030000006b140000010c00000000000@ ??_C@_0JC@NGKIIHPA@03000000921200004b4600000000000@ ??_C@_0PJ@FPHAKAJD@0300000079000000451800000000000@ ??_C@_0MJ@DMAMFLIE@030000001008000001e500000000000@ ??_C@_0BAP@FBAIDBI@0300000015200000018200000000000@ ??_C@_0MA@ECCMLEBD@03000000bd12000015d000000000000@ ??_C@_0BDD@DODHFLKI@030000007e050000092000000000000@ ??_C@_0BCB@EPBJCJPK@030000000d050000030800000000000@ ??_C@_0BCO@JIFBCBLK@0300000055090000147200000000000@ ??_C@_0PI@JDIGFFDA@030000004b120000014d00000000000@ ??_C@_0BCJ@JCLGLLDN@03000000d620000013a700000000000@ ??_C@_0BDC@BPGFOMKB@03000000782300000a1000000000000@ ??_C@_0BCH@LIIGHPB@03000000d62000006d5700000000000@ ??_C@_0BCE@MPKCEKIF@030000006b14000001a100000000000@ ??_C@_0BBB@PJOPHLEI@0300000036280000010000000000000@ ??_C@_0OC@FELEMKIB@03000000120c0000f60e00000000000@ ??_C@_0OJ@OIDHFCIJ@030000006f0e0000090100000000000@ ??_C@_0BBJ@KBEDPJIE@030000008f0e0000030000000000000@ ??_C@_0OC@CPLNMNJF@030000004c050000da0c00000000000@ ??_C@_0NL@JGIMNLNL@030000004c050000371300000000000@ ??_C@_0BCC@FJIJNHMA@03000000d62000006dca00000000000@ ??_C@_0BCN@POFKMMNB@03000000d6200000955700000000000@ ??_C@_0BCP@EHCNAHCG@03000000d62000009f3100000000000@ ??_C@_0BCP@LIBLBNIN@03000000d6200000c75700000000000@ ??_C@_0BBK@KLAOAEFP@0300000063250000230600000000000@ ??_C@_0BCC@CJMNKOGI@03000000e3050000960500000000000@ ??_C@_0BBJ@MEIBJIGF@0300000010080000010000000000000@ ??_C@_0BBO@LNNKKDPA@030000008f0e0000753000000000000@ ??_C@_0BBJ@HBGMHFLL@0300000010080000030000000000000@ ??_C@_0BBJ@PKNJJDLA@0300000025090000888800000000000@ ??_C@_0BBF@DJBNPOOL@0300000066660000670600000000000@ ??_C@_0BCD@JLFNAMGI@030000006b140000030300000000000@ ??_C@_0BBJ@OPMOLAIB@030000009d0d0000133000000000000@ ??_C@_0BBJ@FKANGOML@0300000025090000050000000000000@ ??_C@_0BCF@OCBMGCLB@030000004c050000680200000000000@ ??_C@_0BCD@GAEBDFLF@0300000063250000750500000000000@ ??_C@_0BCD@DJDMNCGP@0300000088880000080300000000000@ ??_C@_0BCD@PNMMMGBA@030000008f0e0000143100000000000@ ??_C@_0BBP@DGIHCNJO@030000003807000056a800000000000@ ??_C@_0OJ@KFMPHKBC@0300000010000000820000000000000@ ??_C@_0BCD@JIBGPOCI@030000004c050000a00b00000000000@ ??_C@_0BCD@GAJAFIPA@030000004c050000c40500000000000@ ??_C@_0BCD@BABDCDPH@030000004c050000cc0900000000000@ ??_C@_0BCN@ECJBHADL@030000004c050000e60c00000000000@ ??_C@_0LO@BOPGPFGO@03000000ff000000cb0100000000000@ ??_C@_0PF@JBCCEDOB@03000000300f0000001100000000000@ ??_C@_0OM@HGBIMFGF@03000000300f0000161100000000000@ ??_C@_0ON@KEJKIAHI@03000000222c0000002000000000000@ ??_C@_0LE@DMNOHJBL@03000000300f0000121000000000000@ ??_C@_0PI@OKGGAFMJ@03000000341a0000010400000000000@ ??_C@_0BDM@IEGKPAEF@03000000222c0000022300000000000@ ??_C@_0BDN@OKDOLNP@03000000222c0000002300000000000@ ??_C@_0BAF@EBPNLKIJ@0300000032150000000300000000000@ ??_C@_0BCJ@BOGEPGKL@0300000032150000020400000000000@ ??_C@_0BCJ@NOGEFCJN@0300000032150000010400000000000@ ??_C@_0BBP@CNEONNDL@0300000032150000050700000000000@ ??_C@_0BBP@NILOBHMB@0300000032150000070700000000000@ ??_C@_0BDB@JPJOJNNC@0300000032150000001100000000000@ ??_C@_0BAO@BBCMHMGF@0300000032150000000900000000000@ ??_C@_0BAA@HNPCOCLG@030000000d0f0000110000000000000@ ??_C@_0BCG@JDIGBKMP@030000000d0f00006a0000000000000@ ??_C@_0BCG@OOIEPIIL@030000000d0f00006b0000000000000@ ??_C@_0BCG@NPOEDNBE@030000000d0f00008a0000000000000@ ??_C@_0BCG@KCOGNPFA@030000000d0f00008b0000000000000@ ??_C@_0BAE@KADGKHDN@030000000d0f0000700000000000000@ ??_C@_0BAK@PBDFELHA@030000000d0f0000220000000000000@ ??_C@_0BCH@KGOLOPPA@030000000d0f00005b0000000000000@ ??_C@_0BCH@HONEFEFO@030000000d0f00005c0000000000000@ ??_C@_0LO@EDCIDLIK@0300000079000000110000000000000@ ??_C@_0NN@GDFALEBB@03000000bd12000013d000000000000@ ??_C@_0KB@CIFFNJNE@0300000000f00000030000000000000@ ??_C@_0KI@EPMKBGDM@0300000000f00000f10000000000000@ ??_C@_0BCO@NGLDHPOC@030000006b140000010d00000000000@ ??_C@_0BDF@FFJELFPB@030000006b140000020d00000000000@ ??_C@_0BDA@ENLICDAF@030000006b140000130d00000000000@ ??_C@_0BCO@NNBOKIIL@030000006f0e00001e0100000000000@ ??_C@_0BCO@BGDMHMEJ@030000006f0e0000280100000000000@ ??_C@_0BCO@NPMGKBDH@030000006f0e00002f0100000000000@ ??_C@_0BBP@LNLJDDIF@030000004f04000003d000000000000@ ??_C@_0BBI@BAKFEMPI@03000000a30600001af500000000000@ ??_C@_0BCP@HPMIMEAF@03000000a306000023f600000000000@ ??_C@_0BCB@EEEEOCLB@03000000300f0000120100000000000@ ??_C@_0LO@FCDDJGBI@03000000a3060000070100000000000@ ??_C@_0BCI@EGALPOHP@03000000a30600000cff00000000000@ ??_C@_0BCB@GBOFCHI@03000000a30600000c0400000000000@ ??_C@_0BCB@OOEANFEH@03000000300f0000100100000000000@ ??_C@_0BCA@LOJNHICN@03000000a30600000b0400000000000@ ??_C@_0BCG@LIMAPBKF@03000000a30600000b0400000001000@ ??_C@_0BCC@JCAIEANG@03000000a3060000210600000000000@ ??_C@_0BCC@CBIJNOOO@03000000a306000020f600000000000@ ??_C@_0BBN@OLDKGLCG@03000000300f0000110100000000000@ ??_C@_0HI@IJLNGEMH@0300000073070000040100000000000@ ??_C@_0LF@DNFJHMLL@0300000000050000289b00000000000@ ??_C@_0LF@CJKOEMML@030000009b280000050000000000000@ ??_C@_0KB@PHAGMDLE@03000000a30c0000250000000000000@ ??_C@_0LP@DBLEIEDM@03000000a30c0000240000000000000@ ??_C@_0BBF@IGGJOAJN@03000000341a0000020800000000000@ ??_C@_0BBC@NNMIKKEI@03000000341a0000090800000000000@ ??_C@_0BBO@IBHMMFFH@030000008f0e0000080000000000000@ ??_C@_0BBK@JNJGFNNI@03000000c0110000059100000000000@ ??_C@_0BCG@JIGLIOKN@03000000d1180000009400000000000@ ??_C@_0OO@GFAPAFAG@0300000011010000191400000000000@ ??_C@_0OB@DNKPJMLF@0300000038100000121400000000000@ ??_C@_0BCE@DAIILIHH@0300000011010000311400000000000@ ??_C@_0BCI@MBOCFMGI@0300000038100000181400000000000@ ??_C@_0BBG@IKJMKIKH@03000000790000001c1800000000000@ ??_C@_0PI@MHCFEKFM@03000000ff110000313300000000000@ ??_C@_0BBL@JOPBPDPP@03000000d620000011a700000000000@ ??_C@_0BCK@KFEKGGJ@0300000045750000221100000000000@ ??_C@_0BCE@LLLAMHO@030000004f04000007d000000000000@ ??_C@_0OO@CIFMNJJH@030000004f0400000ab100000000000@ ??_C@_0BBB@HMBGBDKD@03000000fa190000070600000000000@ ??_C@_0BBO@FCIHICKG@03000000b5070000120300000000000@ ??_C@_0BCF@HEDDGGCD@030000004f04000015b300000000000@ ??_C@_0BCL@EMNJKCNM@030000004f04000023b300000000000@ ??_C@_0BDG@HFKCDPDO@030000004f0400000ed000000000000@ ??_C@_0BDG@FAKOKHMO@030000004f04000000b300000000000@ ??_C@_0BCO@GFHGFBHD@030000004f04000004b300000000000@ ??_C@_0BCP@ONIMJMJ@0300000066660000048800000000000@ ??_C@_0BCO@ICLHBGLC@03000000d6200000600000000000000@ ??_C@_0BCC@BGKKLNHG@030000005f140000c50100000000000@ ??_C@_0BCC@NMJPCJOC@03000000b8050000021000000000000@ ??_C@_0KI@EKGGNLIH@030000004f04000087b600000000000@ ??_C@_0BBI@IMFOKND@03000000d9040000020000000000000@ ??_C@_0BBK@FPHOHOII@030000006e050000132000000000000@ ??_C@_0BBI@BNPIODHF@03000000101c0000171c00000000000@ ??_C@_0BCH@KDJCJEOK@03000000300f0000070100000000000@ ??_C@_0BCA@NJAENDKN@03000000341a0000230800000000000@ ??_C@_0BBK@DBILHFPC@030000005509000000b400000000000@ ??_C@_0BCA@MGCGIKLM@030000006b140000020300000000000@ ??_C@_0BBG@IEDKEHBE@03000000790000000a0000000000000@ ??_C@_0BCA@JHGJKADI@03000000f0250000c18300000000000@ ??_C@_0BBG@ODDFOEO@03000000ff110000413300000000000@ ??_C@_0BCG@JCENNNEN@0300000063250000230500000000000@ ??_C@_0BBK@ECLGAJHG@03000000790000001a1800000000000@ ??_C@_0OH@IPHKKKC@03000000790000001b1800000000000@ ??_C@_0PB@DCMCKKFD@030000006f0e0000030200000000000@ ??_C@_0PP@HNFBKAIN@030000006f0e0000070200000000000@ ??_C@_0BBM@OJGJOKHO@0300000034120000adbe00000000000@ ??_C@_0BCD@KNBJHBAH@030000005e0400000a0b00000000000@ ??_C@_0BCJ@BECCDJOO@030000005e040000130b00000000000@ ??_C@_0BBJ@FKKMPNGK@03000000341a0000060800000000000@ ??_C@_0BCC@FCCEALDM@03000000450c0000204300000000000@ ??_C@_0BCH@PKIHDDBI@03000000ac0500005b0500000000000@ ??_C@_0BCN@JLGOFCN@0300000017270000443100000000000@ ??_C@_0BCE@MNGEPDHI@03000000786901006e7000000000000@ ??_C@_0BCD@OHMJGKKC@03000000790000004f1800000000000@ ??_C@_0BCO@LKAFPMMB@03000000120c0000101e00000000000@ ??_C@_0BBO@HLNJIBPP@78696e7075740100000000000000000@ ??_C@_0BBM@MJENJJEJ@78696e7075740200000000000000000@ ??_C@_0BCD@BBICEOLK@78696e7075740300000000000000000@ ??_C@_0BCD@CGKCHHKM@78696e7075740400000000000000000@ ??_C@_0BCA@KPDCPHP@78696e7075740500000000000000000@ ??_C@_0BBN@HKPLBABN@78696e7075740600000000000000000@ ??_C@_0BBP@NMPDIJKG@78696e7075740800000000000000000@ glfwGetInputMode glfwSetInputMode glfwRawMouseMotionSupported glfwGetKeyName glfwGetKeyScancode glfwGetKey glfwGetMouseButton glfwGetCursorPos glfwSetCursorPos glfwCreateCursor glfwCreateStandardCursor glfwDestroyCursor glfwSetCursor glfwSetKeyCallback glfwSetCharCallback glfwSetCharModsCallback glfwSetMouseButtonCallback glfwSetCursorPosCallback glfwSetCursorEnterCallback glfwSetScrollCallback glfwSetDropCallback glfwJoystickPresent glfwGetJoystickAxes glfwGetJoystickButtons glfwGetJoystickHats glfwGetJoystickName glfwGetJoystickGUID glfwSetJoystickUserPointer glfwGetJoystickUserPointer glfwJoystickIsGamepad glfwSetJoystickCallback glfwUpdateGamepadMappings glfwGetGamepadName glfwGetGamepadState glfwSetClipboardString glfwGetClipboardString glfwGetTime glfwSetTime glfwGetTimerValue glfwGetTimerFrequency _glfwPlatformGetTimerValue _glfwPlatformGetTimerFrequency _glfwInputKey _glfwInputChar _glfwInputScroll _glfwInputMouseClick _glfwInputCursorPos _glfwInputCursorEnter _glfwInputDrop _glfwInputJoystick _glfwInputJoystickAxis _glfwInputJoystickButton _glfwInputJoystickHat _glfwInputError _glfwInitGamepadMappings _glfwAllocJoystick _glfwFreeJoystick _glfwCenterCursorInContentArea _glfw_calloc _glfw_realloc _glfw_free findMapping findValidMapping parseMapping __GSHandlerCheck __security_check_cookie $unwind$glfwGetInputMode $pdata$glfwGetInputMode $unwind$glfwSetInputMode $pdata$glfwSetInputMode $unwind$glfwRawMouseMotionSupported $pdata$glfwRawMouseMotionSupported $unwind$glfwGetKeyName $pdata$glfwGetKeyName $unwind$glfwGetKeyScancode $pdata$glfwGetKeyScancode $unwind$glfwGetKey $pdata$glfwGetKey $unwind$glfwGetMouseButton $pdata$glfwGetMouseButton $unwind$glfwSetCursorPos $pdata$glfwSetCursorPos $unwind$glfwCreateCursor $pdata$glfwCreateCursor $chain$0$glfwCreateCursor $pdata$0$glfwCreateCursor $chain$2$glfwCreateCursor $pdata$2$glfwCreateCursor $chain$3$glfwCreateCursor $pdata$3$glfwCreateCursor $unwind$glfwCreateStandardCursor $pdata$glfwCreateStandardCursor $chain$0$glfwCreateStandardCursor $pdata$0$glfwCreateStandardCursor $chain$2$glfwCreateStandardCursor $pdata$2$glfwCreateStandardCursor $unwind$glfwDestroyCursor $pdata$glfwDestroyCursor $chain$0$glfwDestroyCursor $pdata$0$glfwDestroyCursor $chain$1$glfwDestroyCursor $pdata$1$glfwDestroyCursor $chain$2$glfwDestroyCursor $pdata$2$glfwDestroyCursor $chain$3$glfwDestroyCursor $pdata$3$glfwDestroyCursor $unwind$glfwSetKeyCallback $pdata$glfwSetKeyCallback $unwind$glfwSetCharCallback $pdata$glfwSetCharCallback $unwind$glfwSetCharModsCallback $pdata$glfwSetCharModsCallback $unwind$glfwSetMouseButtonCallback $pdata$glfwSetMouseButtonCallback $unwind$glfwSetCursorPosCallback $pdata$glfwSetCursorPosCallback $unwind$glfwSetCursorEnterCallback $pdata$glfwSetCursorEnterCallback $unwind$glfwSetScrollCallback $pdata$glfwSetScrollCallback $unwind$glfwSetDropCallback $pdata$glfwSetDropCallback $unwind$glfwJoystickPresent $pdata$glfwJoystickPresent $unwind$glfwGetJoystickAxes $pdata$glfwGetJoystickAxes $unwind$glfwGetJoystickButtons $pdata$glfwGetJoystickButtons $unwind$glfwGetJoystickHats $pdata$glfwGetJoystickHats $unwind$glfwGetJoystickName $pdata$glfwGetJoystickName $unwind$glfwGetJoystickGUID $pdata$glfwGetJoystickGUID $unwind$glfwGetJoystickUserPointer $pdata$glfwGetJoystickUserPointer $unwind$glfwJoystickIsGamepad $pdata$glfwJoystickIsGamepad $unwind$glfwSetJoystickCallback $pdata$glfwSetJoystickCallback $unwind$glfwUpdateGamepadMappings $pdata$glfwUpdateGamepadMappings $chain$0$glfwUpdateGamepadMappings $pdata$0$glfwUpdateGamepadMappings $chain$1$glfwUpdateGamepadMappings $pdata$1$glfwUpdateGamepadMappings $chain$2$glfwUpdateGamepadMappings $pdata$2$glfwUpdateGamepadMappings $chain$3$glfwUpdateGamepadMappings $pdata$3$glfwUpdateGamepadMappings $chain$4$glfwUpdateGamepadMappings $pdata$4$glfwUpdateGamepadMappings $unwind$glfwGetGamepadName $pdata$glfwGetGamepadName $unwind$glfwGetGamepadState $pdata$glfwGetGamepadState $chain$1$glfwGetGamepadState $pdata$1$glfwGetGamepadState $chain$3$glfwGetGamepadState $pdata$3$glfwGetGamepadState $chain$5$glfwGetGamepadState $pdata$5$glfwGetGamepadState $chain$7$glfwGetGamepadState $pdata$7$glfwGetGamepadState $chain$9$glfwGetGamepadState $pdata$9$glfwGetGamepadState $unwind$glfwGetClipboardString $pdata$glfwGetClipboardString $unwind$glfwGetTime $pdata$glfwGetTime $chain$0$glfwGetTime $pdata$0$glfwGetTime $chain$2$glfwGetTime $pdata$2$glfwGetTime $unwind$glfwSetTime $pdata$glfwSetTime $chain$0$glfwSetTime $pdata$0$glfwSetTime $chain$1$glfwSetTime $pdata$1$glfwSetTime $unwind$glfwGetTimerValue $pdata$glfwGetTimerValue $unwind$glfwGetTimerFrequency $pdata$glfwGetTimerFrequency $unwind$_glfwInputChar $pdata$_glfwInputChar $unwind$_glfwInputJoystickHat $pdata$_glfwInputJoystickHat $unwind$_glfwInitGamepadMappings $pdata$_glfwInitGamepadMappings $unwind$_glfwAllocJoystick $pdata$_glfwAllocJoystick $chain$2$_glfwAllocJoystick $pdata$2$_glfwAllocJoystick $chain$3$_glfwAllocJoystick $pdata$3$_glfwAllocJoystick $chain$5$_glfwAllocJoystick $pdata$5$_glfwAllocJoystick $unwind$_glfwFreeJoystick $pdata$_glfwFreeJoystick $unwind$_glfwCenterCursorInContentArea $pdata$_glfwCenterCursorInContentArea $unwind$findMapping $pdata$findMapping $unwind$findValidMapping $pdata$findValidMapping $unwind$parseMapping $pdata$parseMapping $chain$2$parseMapping $pdata$2$parseMapping $chain$3$parseMapping $pdata$3$parseMapping $chain$4$parseMapping $pdata$4$parseMapping $chain$5$parseMapping $pdata$5$parseMapping ??_C@_08CBHCFJJK@platform@ ??_C@_01MCMALHOG@a@ ??_C@_01OJONOECF@b@ ??_C@_01FJMABOPO@x@ ??_C@_01EANLCPLP@y@ ??_C@_04JFCNHLLM@back@ ??_C@_05FAGFPHJG@start@ ??_C@_05HLIEEKJG@guide@ ??_C@_0N@MEFCLEJO@leftshoulder@ ??_C@_0O@LCPOPKLJ@rightshoulder@ ??_C@_09MEKLKMGE@leftstick@ ??_C@_0L@DHLNGPDM@rightstick@ ??_C@_04EDFFKKLO@dpup@ ??_C@_07FLLKAONK@dpright@ ??_C@_06LDHPELJC@dpdown@ ??_C@_06EBHLNDKL@dpleft@ ??_C@_0M@FLKHLCCP@lefttrigger@ ??_C@_0N@CLKIAFGP@righttrigger@ ??_C@_05MKIKPCON@leftx@ ??_C@_05NDJBMDKM@lefty@ ??_C@_06FHABPINC@rightx@ ??_C@_06EOBKMJJD@righty@ ??_C@_01IHBHIGKO@?0@ ??_C@_0BK@PDCFBJPK@Invalid?5input?5mode?50x?$CF08X@ ??_C@_0BL@IKJGDNLO@Invalid?5cursor?5mode?50x?$CF08X@ ??_C@_0DB@JJADGKIE@Raw?5mouse?5motion?5is?5not?5support@ ??_C@_0P@PKABKPLF@Invalid?5key?5?$CFi@ ??_C@_0BI@POJNLJDO@Invalid?5mouse?5button?5?$CFi@ ??_C@_0BO@JCPCDDEA@Invalid?5cursor?5position?5?$CFf?5?$CFf@ ??_C@_0CE@KNFDBMCE@Invalid?5image?5dimensions?5for?5cu@ ??_C@_0BP@EMCHFHHH@Invalid?5standard?5cursor?50x?$CF08X@ ??_C@_0BH@PHEJANJA@Invalid?5joystick?5ID?5?$CFi@ ??_C@_02PCIJFNDE@?$AN?6@ ??_C@_0BA@KMGHNAFM@Invalid?5time?5?$CFf@ __real@3f800000 __real@3fe0000000000000 __real@42112e0be8240000 __real@43e0000000000000 __real@7fefffffffffffff __real@bf800000 __real@ffefffffffffffff __security_cookie 