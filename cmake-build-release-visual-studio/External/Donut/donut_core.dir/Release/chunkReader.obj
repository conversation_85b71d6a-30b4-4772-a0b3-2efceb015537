d啌烍Gha� �      .drectve        <  ?               
 .debug$S          H@  HK        @ B.debug$T        p   \K             @ B.rdata             蘇             @ @@.text$mn        :   豄 L         P`.debug$S          0L <N        @B.text$mn        0   萅 鳱         P`.debug$S        �  O 甈        @B.text$mn        ^  :Q 楻         P`.debug$S        �
  鑂 碷     V   @B.text$x         (   a 8a         P`.text$mn           La ka         P`.debug$S        <  ua 眀     
   @B.text$mn           c 4c         P`.debug$S        L  >c 奷     
   @B.text$mn        �   頳 鎒         P`.debug$S        p  Tf 膆     
   @B.text$mn        �   (i  j         P`.debug$S        t  巎 m     
   @B.text$mn        �   fm ^n         P`.debug$S        p  蘮 <q     
   @B.text$mn        �   爍 榬         P`.debug$S        l  s ru     
   @B.text$mn        (   謚              P`.debug$S        �   鈝        @B.text$mn        (   2x              P`.debug$S        �  Zx Bz        @B.text$mn        �   抸 {         P`.debug$S        �  Q{ 1}        @B.text$mn        �   ﹠ ,~         P`.debug$S        �  h~ L�        @B.text$mn        �   膧 G�         P`.debug$S        �  儊 c�        @B.text$mn        �   蹆 ^�         P`.debug$S        �  殑 z�        @B.text$mn        �   騿 u�         P`.debug$S        �  眹 崏        @B.text$mn        �   � 垔         P`.debug$S        �  膴 ▽        @B.text$mn        <    � \�         P`.debug$S        0  z� 獛     
   @B.text$mn        <   � J�         P`.debug$S        L  h� 磹     
   @B.text$mn        !   � 9�         P`.debug$S        <  M� 墥        @B.text$mn        2   艗 鲯         P`.debug$S        <  � G�        @B.text$mn           繑 蕯         P`.debug$S          詳 鞎        @B.text$mn           (� 3�         P`.debug$S           =� ]�        @B.text$mn        K   櫁              P`.debug$S        �  錀 皺        @B.text$mn        K   <�              P`.debug$S        �  嚉 O�        @B.text$mn        K   蹨              P`.debug$S        �  &� 鰹        @B.text$mn        K   偀              P`.debug$S        �  蜔 櫋        @B.text$mn        [   %� ��         P`.debug$S        (  敘 讥        @B.text$mn            槮              P`.debug$S        �  甫 <�     
   @B.text$mn        �   牗 <�         P`.debug$S        P  P� 牞        @B.text$mn           き              P`.debug$S        �   Л 嚠        @B.text$mn           卯 之         P`.debug$S        �   戤 委        @B.text$mn           霪 	�         P`.debug$S        �   �         @B.text$mn        +   9� d�         P`.debug$S        �   x� h�        @B.text$mn        +   げ 喜         P`.debug$S        �   悴 映        @B.text$mn        B   � Q�         P`.debug$S           o� o�        @B.text$mn        B    淼         P`.debug$S          � �        @B.text$mn        B   W� 櫡         P`.debug$S        �   贩 掣        @B.text$mn        H   锔              P`.debug$S        �  7�         @B.text$mn           �              P`.debug$S          %� 1�        @B.text$mn           伣              P`.debug$S          摻 熅        @B.text$mn        N   锞              P`.debug$S        �  =� 5�        @B.text$mn        N   亮              P`.debug$S        �  � �        @B.text$mn           椖              P`.debug$S        ,  毮 婆        @B.text$mn            � 6�         P`.debug$S        �   T� �        @B.text$mn           T� e�         P`.debug$S        �   y� ]�        @B.text$mn        B   櫲 廴         P`.debug$S        �  锶 徦        @B.text$mn          撎 ┰         P`.debug$S        �'  氛 [�     J  @B.text$x            ?
 K
         P`.text$x            U
 a
         P`.text$x            k
 w
         P`.text$x            �
 �
         P`.text$mn          �
 �
         P`.debug$S        �  A �     f   @B.text$x            � �         P`.text$mn        �  � �          P`.debug$S        �  S! �(     D   @B.text$x            �+ �+         P`.text$mn        �  �+ �-         P`.debug$S        <  �- 55     @   @B.text$x            �7 �7         P`.text$mn        �  �7 s@     %    P`.debug$S        �  錋 匲     `   @B.text$x            EY QY         P`.text$x            [Y gY         P`.text$x            qY }Y         P`.text$mn        �  嘫 \[         P`.debug$S        �  \ 鎐     :   @B.text$mn        q  *f 沢         P`.debug$S        x  雊 cm     .   @B.text$mn           /o Bo         P`.debug$S        �   Lo  p        @B.xdata             \p             @0@.pdata             pp |p        @0@.xdata             歱             @0@.pdata              畃        @0@.xdata             蘰             @0@.pdata             豴 鋚        @0@.xdata             q             @0@.pdata             
q q        @0@.xdata             4q             @0@.pdata             @q Lq        @0@.xdata             jq             @0@.pdata             rq ~q        @0@.xdata             渜             @0@.pdata             ╭ 磓        @0@.xdata             襮             @0@.pdata             趒 鎞        @0@.xdata             r             @0@.pdata             r r        @0@.xdata             6r Jr        @0@.pdata             hr tr        @0@.xdata             抮         @0@.pdata             纑 蘲        @0@.voltbl            阹               .xdata          $   靣 s        @0@.pdata             $s 0s        @0@.xdata          	   Ns Ws        @@.xdata             ks 卻        @@.xdata             璼             @@.voltbl         6   舠                .xdata             鹲             @0@.pdata             t t        @0@.xdata             -t Et        @0@.pdata             ct ot        @0@.xdata             峵         @0@.pdata             胻 蟭        @0@.xdata             韙 u        @0@.pdata             u +u        @0@.xdata             Iu Yu        @0@.pdata             wu 僽        @0@.xdata              眜        @0@.pdata             蟯 踰        @0@.xdata             鵸 	v        @0@.pdata             'v 3v        @0@.xdata             Qv             @0@.pdata             ev qv        @0@.xdata             弙         @0@.pdata             籿 莢        @0@.xdata          	   鍁 顅        @@.xdata             w 	w        @@.xdata          
   w             @@.voltbl            w                .xdata             )w Aw        @0@.pdata             Uw aw        @0@.xdata          	   w 坵        @@.xdata             渨         @@.xdata          
   瓀             @@.voltbl            穡                .xdata             縲 譿        @0@.pdata             雡 鱳        @0@.xdata          	   x x        @@.xdata             2x 9x        @@.xdata          
   Cx             @@.voltbl            Mx                .xdata          (   Ux }x        @0@.pdata             憍 漻        @0@.xdata          	   粁 膞        @@.xdata             豿 韝        @@.xdata             y             @@.voltbl            &y                .xdata             @y             @0@.pdata             Hy Ty        @0@.xdata             ry             @0@.pdata             zy 唝        @0@.xdata                          @0@.pdata             瑈 竬        @0@.xdata             謞 陏        @0@.pdata             z z        @0@.xdata             2z Bz        @0@.pdata             `z lz        @0@.voltbl            妟               .xdata             寊             @0@.pdata             攝 爖        @0@.xdata             緕             @0@.pdata             苲 襷        @0@.xdata             饅 {        @0@.pdata             "{ .{        @0@.xdata             L{ \{        @0@.pdata             z{ 唟        @0@.voltbl                           .xdata                          @0@.pdata             畕 簕        @0@.xdata             貃 靮        @0@.pdata             
| |        @0@.xdata             4| H|        @0@.pdata             f| r|        @0@.xdata             恷         @0@.pdata             聕 蝲        @0@.xdata             靯  }        @0@.pdata             } *}        @0@.xdata             H} \}        @0@.pdata             z} 唥        @0@.voltbl                           .xdata                          @0@.pdata             瓆 箎        @0@.xdata             讅 雧        @0@.pdata             	~ ~        @0@.xdata             3~ C~        @0@.pdata             a~ m~        @0@.voltbl            媬               .voltbl            崀               .xdata             巭             @0@.pdata             杶         @0@.xdata             纞 詞        @0@.pdata             騸         @0@.xdata              ,        @0@.pdata             J V        @0@.voltbl            t               .xdata             v             @0@.pdata             ~ �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata             6� B�        @0@.xdata             `� t�        @0@.pdata             拃 瀫        @0@.xdata             紑 衻        @0@.pdata             顎 鷢        @0@.xdata             � ,�        @0@.pdata             J� V�        @0@.xdata             t�             @0@.pdata             |� 垇        @0@.xdata              簛        @0@.pdata             貋 鋪        @0@.xdata             � �        @0@.pdata             4� @�        @0@.xdata             ^� r�        @0@.pdata             悅 渹        @0@.xdata             簜 蝹        @0@.pdata             靷 鴤        @0@.xdata             � *�        @0@.pdata             H� T�        @0@.xdata             r�             @0@.pdata             z� 唭        @0@.xdata              竷        @0@.pdata             謨 鈨        @0@.xdata              � �        @0@.pdata             2� >�        @0@.xdata             \� p�        @0@.pdata             巹 殑        @0@.xdata             竸 虅        @0@.pdata             陝 鰟        @0@.xdata             � (�        @0@.pdata             F� R�        @0@.xdata             p�             @0@.pdata             x� 剠        @0@.voltbl                           .xdata                          @0@.pdata             珔 穮        @0@.xdata             諈             @0@.pdata             輩 閰        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             9�             @0@.pdata             A� M�        @0@.xdata             k�             @0@.pdata             s� �        @0@.xdata             潌             @0@.pdata              眴        @0@.xdata             蠁 銌        @0@.pdata             � 
�        @0@.xdata             +� ;�        @0@.pdata             Y� e�        @0@.voltbl            儑               .xdata             厙             @0@.pdata             崌 檱        @0@.xdata             穱             @0@.pdata             繃 藝        @0@.xdata             閲 龂        @0@.pdata             � '�        @0@.xdata             E� U�        @0@.pdata             s� �        @0@.voltbl            潏               .xdata             焾             @0@.pdata              硤        @0@.xdata             褕 閳        @0@.pdata             龍 	�        @0@.xdata          
   '� 4�        @@.xdata             R�             @@.xdata             U� ]�        @@.xdata             g� n�        @@.xdata          	   x�             @@.xdata             亯             @0@.pdata             墘 晧        @0@.voltbl            硥               .xdata             磯             @0@.pdata             級 葔        @0@.xdata             鎵             @0@.pdata             顗 鷫        @0@.xdata             �             @0@.pdata             $� 0�        @0@.xdata             N�             @0@.pdata             V� b�        @0@.rdata             �� 槉        @@@.rdata             秺             @@@.rdata             葕 鄪        @@@.rdata              �        @@@.rdata             4�             @@@.xdata$x           I� e�        @@@.xdata$x           y� 晪        @@@.data$r         /   硧 鈰        @@�.xdata$x        $   鞁 �        @@@.data$r         $   $� H�        @@�.xdata$x        $   R� v�        @@@.data$r         $   妼 畬        @@�.xdata$x        $   笇 軐        @@@.rdata          &   饘             @@@.rdata          !   �             @@@.rdata          %   7�             @@@.rdata          !   \�             @@@.rdata          /   }�             @@@.rdata          6   瑣             @@@.rdata          3   鈲             @@@.rdata          3   �             @@@.rdata          =   H�             @@@.rdata             厧             @@@.rdata          	   潕             @@@.rdata          
                @@@.rdata          
   皫             @@@.rdata             簬             @0@.rdata             翈             @@@.rdata          
   蓭             @@@.rdata             訋             @@@.rdata          
   蹘             @@@.rdata          	   鍘             @@@.rdata             顜             @@@.rdata          H                @P@.rdata          C   F�             @P@.rdata          4   墢             @@@.rdata             綇             @@@.rdata          )   蛷             @@@.rdata          ,   鰪             @@@.rdata             "�             @@@.rdata             8�             @@@.rdata             O�             @@@.rdata          (   d� 寪        @@@.rdata          (   緪 鎼        @@@.rdata$r        $   � <�        @@@.rdata$r           Z� n�        @@@.rdata$r           x� 剳        @@@.rdata$r        $   帒 矐        @@@.rdata$r        $   茟 陸        @@@.rdata$r           � �        @@@.rdata$r           &� :�        @@@.rdata$r        $   N� r�        @@@.rdata$r        $   啋 獟        @@@.rdata$r           葤 軖        @@@.rdata$r           鎾 �        @@@.rdata$r        $    � D�        @@@.data$rs        *   X� 倱        @@�.rdata$r           寭 爴        @@@.rdata$r           獡 稉        @@@.rdata$r        $   罁 鋼        @@@.rdata$r        $   鴵 �        @@@.data$rs        C   :� }�        @P�.rdata$r           嚁 洈        @@@.rdata$r            箶        @@@.rdata$r        $   蛿 駭        @@@.rdata$r        $   � )�        @@@.data$rs        F   G� 崟        @P�.rdata$r           棔 珪        @@@.rdata$r           禃 蓵        @@@.rdata$r        $   輹 �        @@@.debug$S        T   � i�        @B.debug$S        T   }� 褨        @B.debug$S        4   鍠 �        @B.debug$S        4   -� a�        @B.debug$S        @   u� 禇        @B.chks64         �  蓷              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �  g     D:\RTXPT\cmake-build-release-visual-studio\External\Donut\donut_core.dir\Release\chunkReader.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $__vc_attributes  $helper_attributes  $atl  $std  $pmr  $_Has_ADL_swap_detail 
 $rel_ops  $_Ensure_adl  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem  $placeholders  $_Binary_hypot  $donut  $vfs 	 $status  $math 	 $colors  $log  $chunk 	 $stdext    �   +  E #   std::allocator<char32_t>::_Minimum_asan_allocation_alignment " :    std::memory_order_relaxed " :   std::memory_order_consume " :   std::memory_order_acquire " :   std::memory_order_release " :   std::memory_order_acq_rel " :   std::memory_order_seq_cst 5 �    std::filesystem::_File_time_clock::is_steady C #   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q #   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j �   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k #    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size ` #   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos : #   std::integral_constant<unsigned __int64,2>::value x #   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment $ %   std::_Locbase<int>::collate " %   std::_Locbase<int>::ctype - %    std::integral_constant<int,0>::value % %   std::_Locbase<int>::monetary $ %   std::_Locbase<int>::numeric ! %   std::_Locbase<int>::time % %    std::_Locbase<int>::messages   %  ? std::_Locbase<int>::all ! %    std::_Locbase<int>::none l �   std::_Trivial_cat<char const *,char const *,char const * &&,char const * &>::_Bitcopy_constructible i �   std::_Trivial_cat<char const *,char const *,char const * &&,char const * &>::_Bitcopy_assignable O�   std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Same_size_and_compatible L�    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_constructible I�    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_assignable  �!   std::_Consume_header  �!   std::_Generate_header � �   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � �    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable � �   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified  %    donut::vfs::status::OK $ %   ��donut::vfs::status::Failed * %   �onut::vfs::status::PathNotFound , %   �齞onut::vfs::status::NotImplemented � #   std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Minimum_asan_allocation_alignment 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable - �    std::chrono::system_clock::is_steady $ r   std::ratio<1,10000000>::num ( r  ��枠 std::ratio<1,10000000>::den  r   std::ratio<1,1>::num  r   std::ratio<1,1>::den % #   std::ctype<char>::table_size J r   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N r  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( r  ��枠 std::ratio<10000000,1>::num $ r   std::ratio<10000000,1>::den P r  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 K r   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy J r   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P r  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 < r  ��枠 std::integral_constant<__int64,10000000>::value L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K r   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy 1 r   std::integral_constant<__int64,1>::value - �   std::chrono::steady_clock::is_steady & r   std::ratio<1,1000000000>::num * r  � 蕷;std::ratio<1,1000000000>::den � #   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IFileSystem> > >::_Minimum_asan_allocation_alignment D #   ��std::basic_string_view<char,std::char_traits<char> >::npos   r  std::ratio<3600,1>::num   r   std::ratio<3600,1>::den  �  ��I@donut::math::PI_f " �  
�-DT�!	@donut::math::PI_d ! �  ��7�5donut::math::epsilon " �  �  �donut::math::infinity  �  �  �donut::math::NaN   %   std::_Iosb<int>::skipws J #   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos ! %   std::_Iosb<int>::unitbuf # %   std::_Iosb<int>::uppercase " %   std::_Iosb<int>::showbase # %   std::_Iosb<int>::showpoint ! %    std::_Iosb<int>::showpos  %  @ std::_Iosb<int>::left  %  � std::_Iosb<int>::right " %   std::_Iosb<int>::internal  %   std::_Iosb<int>::dec  %   std::_Iosb<int>::oct  %   std::_Iosb<int>::hex $ %   std::_Iosb<int>::scientific  %    std::_Iosb<int>::fixed " %   0std::_Iosb<int>::hexfloat # %   @std::_Iosb<int>::boolalpha " %  � �std::_Iosb<int>::_Stdio % %  �std::_Iosb<int>::adjustfield # %   std::_Iosb<int>::basefield $ %   0std::_Iosb<int>::floatfield ! %    std::_Iosb<int>::goodbit   %   std::_Iosb<int>::eofbit ! %   std::_Iosb<int>::failbit   %   std::_Iosb<int>::badbit  %   std::_Iosb<int>::in  %   std::_Iosb<int>::out  %   std::_Iosb<int>::ate  %   std::_Iosb<int>::app  %   std::_Iosb<int>::trunc # %  @ std::_Iosb<int>::_Nocreate $ %  � std::_Iosb<int>::_Noreplace 6 �   std::_Iterator_base0::_Unwrap_when_unverified   %    std::_Iosb<int>::binary  %    std::_Iosb<int>::beg  %   std::_Iosb<int>::cur  %   std::_Iosb<int>::end , %  @ std::_Iosb<int>::_Default_open_prot 7 �   std::_Iterator_base12::_Unwrap_when_unverified . %   donut::math::box<float,3>::numCorners L #   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos  r  < std::ratio<60,1>::num  r   std::ratio<60,1>::den _ #   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment L #   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos   r   std::ratio<1,1000>::num   r  �std::ratio<1,1000>::den 4 �  �����donut::chunk::ChunkId::INVALID_CHUNK_ID � #   std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > >::_Minimum_asan_allocation_alignment # r   std::ratio<1,1000000>::num ' r  �@B std::ratio<1,1000000>::den ) �   donut::math::vector<bool,2>::DIM ) �   donut::math::vector<bool,3>::DIM ) �   donut::math::vector<bool,4>::DIM R �    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified ' �7   donut::chunk::CHUNKTYPE_STREAM . �7  donut::chunk::CHUNKTYPE_STRINGS_TABLE ( �7   donut::chunk::CHUNKTYPE_MESHSET + �7  donut::chunk::CHUNKTYPE_MESH_INFOS / �7  donut::chunk::CHUNKTYPE_MESH_INSTANCES + �7  donut::chunk::CHUNKTYPE_MESH_NODES < �   donut::chunk::StringsTable_ChunkDesc_0x100::version > �7  donut::chunk::StringsTable_ChunkDesc_0x100::chunktype  �7   donut::chunk::UINT8  �7   donut::chunk::UINT32  �7   donut::chunk::FP32   �7   donut::chunk::VARY_NONE  �7   donut::chunk::VERTEX  �7   donut::chunk::POSITION  �7   donut::chunk::NORMAL  �7   donut::chunk::TANGENT   �7   donut::chunk::BITANGENT  �7   donut::chunk::TEXCOORD  �7   donut::chunk::INDEX # �7   donut::chunk::MESHLET_INFO 6 �   donut::chunk::Stream_ChunkDesc_0x100::version 8 �7   donut::chunk::Stream_ChunkDesc_0x100::chunktype . �   std::integral_constant<bool,1>::value 9 �   donut::chunk::MeshInfos_ChunkDesc_0x100::version ; �7  donut::chunk::MeshInfos_ChunkDesc_0x100::chunktype 9 �   donut::chunk::MeshNodes_ChunkDesc_0x100::version ; �7  donut::chunk::MeshNodes_ChunkDesc_0x100::chunktype = �   donut::chunk::MeshInstances_ChunkDesc_0x100::version ? �7  donut::chunk::MeshInstances_ChunkDesc_0x100::chunktype 7 �   donut::chunk::MeshSet_ChunkDesc_0x100::version 9 �7   donut::chunk::MeshSet_ChunkDesc_0x100::chunktype 4 #  @ _Mtx_internal_imp_t::_Critical_section_size 5 #   _Mtx_internal_imp_t::_Critical_section_align + �    std::_Aligned_storage<64,8>::_Fits * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits A #   std::allocator<char>::_Minimum_asan_allocation_alignment I #   std::allocator<char const *>::_Minimum_asan_allocation_alignment % �5    _Atomic_memory_order_relaxed % �5   _Atomic_memory_order_consume % �5   _Atomic_memory_order_acquire    �   �  % �5   _Atomic_memory_order_release % �5   _Atomic_memory_order_acq_rel % �5   _Atomic_memory_order_seq_cst ? #   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A #   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L #   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE    �   �)  Z #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e #   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ �   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ #    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size T #   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos '�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !�   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable D #   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment B #   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D #   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O #   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity a #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n #   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n #  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g �   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val : #    std::integral_constant<unsigned __int64,0>::value j #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h #    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size / �   std::atomic<long>::is_always_lock_free * �   donut::math::vector<float,3>::DIM ] #   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy . �    std::integral_constant<bool,0>::value * �   donut::math::vector<float,4>::DIM * �        donut::math::lumaCoefficients * �   donut::math::vector<float,2>::DIM X #   std::allocator<donut::chunk::Chunk const *>::_Minimum_asan_allocation_alignment  2    std::denorm_absent  2   std::denorm_present E #   std::allocator<char16_t>::_Minimum_asan_allocation_alignment  5    std::round_toward_zero  5   std::round_to_nearest # 2    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ 5    std::_Num_base::round_style  %    std::_Num_base::digits ! %    std::_Num_base::digits10 % %    std::_Num_base::max_digits10 % %    std::_Num_base::max_exponent ' %    std::_Num_base::max_exponent10 % %    std::_Num_base::min_exponent ' %    std::_Num_base::min_exponent10  %    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " %   std::_Num_int_base::radix ) 2   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * 5   std::_Num_float_base::round_style $ %   std::_Num_float_base::radix * %   std::numeric_limits<bool>::digits C #   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E #   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P #   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * %   std::numeric_limits<char>::digits , %   std::numeric_limits<char>::digits10 3   \ std::filesystem::path::preferred_separator 4 �   std::numeric_limits<signed char>::is_signed 1 %   std::numeric_limits<signed char>::digits 3 %   std::numeric_limits<signed char>::digits10 d #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q #   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q #  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j �   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k #    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 6 �   std::numeric_limits<unsigned char>::is_modulo 3 %   std::numeric_limits<unsigned char>::digits 5 %   std::numeric_limits<unsigned char>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . %   std::numeric_limits<char16_t>::digits 0 %   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . %    std::numeric_limits<char32_t>::digits 0 %  	 std::numeric_limits<char32_t>::digits10 ) �   donut::math::frustum::numCorners 0 �   std::numeric_limits<wchar_t>::is_modulo - %   std::numeric_limits<wchar_t>::digits / %   std::numeric_limits<wchar_t>::digits10 ` #   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos . �   std::numeric_limits<short>::is_signed + %   std::numeric_limits<short>::digits - %   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) %   std::numeric_limits<int>::digits + %  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * %   std::numeric_limits<long>::digits , %  	 std::numeric_limits<long>::digits10 : %   std::_Floating_type_traits<float>::_Mantissa_bits : %   std::_Floating_type_traits<float>::_Exponent_bits D %   std::_Floating_type_traits<float>::_Maximum_binary_exponent E %   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : %   std::_Floating_type_traits<float>::_Exponent_bias 7 %   std::_Floating_type_traits<float>::_Sign_shift ; %   std::_Floating_type_traits<float>::_Exponent_shift 0 �   std::numeric_limits<__int64>::is_signed : �  � std::_Floating_type_traits<float>::_Exponent_mask - %  ? std::numeric_limits<__int64>::digits E �  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask / %   std::numeric_limits<__int64>::digits10 G �  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J �  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B �  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F �  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; %  5 std::_Floating_type_traits<double>::_Mantissa_bits ; %   std::_Floating_type_traits<double>::_Exponent_bits E %  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G %  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; %  �std::_Floating_type_traits<double>::_Exponent_bias 7 �   std::numeric_limits<unsigned short>::is_modulo 8 %  ? std::_Floating_type_traits<double>::_Sign_shift < %  4 std::_Floating_type_traits<double>::_Exponent_shift 4 %   std::numeric_limits<unsigned short>::digits 6 %   std::numeric_limits<unsigned short>::digits10 ; #  �std::_Floating_type_traits<double>::_Exponent_mask J #  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L #  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O #  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G #  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K #  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask 5 �   std::numeric_limits<unsigned int>::is_modulo 2 %    std::numeric_limits<unsigned int>::digits 4 %  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 %    std::numeric_limits<unsigned long>::digits 5 %  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 %  @ std::numeric_limits<unsigned __int64>::digits 8 %   std::numeric_limits<unsigned __int64>::digits10 + %   std::numeric_limits<float>::digits - %   std::numeric_limits<float>::digits10 1 %  	 std::numeric_limits<float>::max_digits10 1 %  � std::numeric_limits<float>::max_exponent 3 %  & std::numeric_limits<float>::max_exponent10 2 %   �僺td::numeric_limits<float>::min_exponent 4 %   �踫td::numeric_limits<float>::min_exponent10 �   �  , %  5 std::numeric_limits<double>::digits . %   std::numeric_limits<double>::digits10 2 %   std::numeric_limits<double>::max_digits10 2 %   std::numeric_limits<double>::max_exponent 4 %  4std::numeric_limits<double>::max_exponent10 4 %  �黶td::numeric_limits<double>::min_exponent 6 %  �威std::numeric_limits<double>::min_exponent10 1 %  5 std::numeric_limits<long double>::digits 3 %   std::numeric_limits<long double>::digits10 7 %   std::numeric_limits<long double>::max_digits10 7 %   std::numeric_limits<long double>::max_exponent 9 %  4std::numeric_limits<long double>::max_exponent10 9 %  �黶td::numeric_limits<long double>::min_exponent   �   =   ; %  �威std::numeric_limits<long double>::min_exponent10    �   妉  H �    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified  t   int32_t  �  _CatchableType " l  _s__RTTIBaseClassDescriptor ? 1  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 0  __vcrt_va_list_is_reference<char const * const> G ;  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �   _Ctypevec & �6  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t - (  __vc_attributes::event_sourceAttribute 9 !  __vc_attributes::event_sourceAttribute::optimize_e 5   __vc_attributes::event_sourceAttribute::type_e >   __vc_attributes::helper_attributes::v1_alttypeAttribute F   __vc_attributes::helper_attributes::v1_alttypeAttribute::type_e 9   __vc_attributes::helper_attributes::usageAttribute B   __vc_attributes::helper_attributes::usageAttribute::usage_e *   __vc_attributes::threadingAttribute 7   __vc_attributes::threadingAttribute::threading_e -   __vc_attributes::aggregatableAttribute 5 �  __vc_attributes::aggregatableAttribute::type_e / �  __vc_attributes::event_receiverAttribute 7 �  __vc_attributes::event_receiverAttribute::type_e ' �  __vc_attributes::moduleAttribute / �  __vc_attributes::moduleAttribute::type_e  �#  __std_fs_find_data & �5  $_TypeDescriptor$_extraBytes_23 - �5  $_s__CatchableTypeArray$_extraBytes_32 # A)  __std_fs_reparse_data_buffer Z �6  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^ �6  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> ` �6  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �#  __std_fs_dir_handle  �  __std_access_rights  �  _TypeDescriptor & �5  $_TypeDescriptor$_extraBytes_34 	 �  tm % q  _s__RTTICompleteObjectLocator2 A �  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & �;  $_TypeDescriptor$_extraBytes_51  �  _s__CatchableType & �5  $_TypeDescriptor$_extraBytes_19 & �5  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9 �  __vcrt_va_list_is_reference<wchar_t const * const>  '  __std_fs_filetime E   __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & z  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16   g)  __std_fs_copy_file_result  �#  __std_code_page . h6  std::_Ptr_base<donut::vfs::IFileSystem> 8 �9  std::shared_ptr<donut::chunk::MeshSetBase const > ' -  std::default_delete<wchar_t [0]> . #  std::_Conditionally_enabled_hash<int,1> A $(  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? �4  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit - B$  std::reverse_iterator<wchar_t const *> " 5  std::_Char_traits<char,int>     std::_Fs_file  �9  std::_Value_init_tag  "   std::_Atomic_counter_t  8  std::_Num_base & /  std::hash<std::error_condition>  �  std::_Big_uint128 ) #5  std::_Narrow_char_traits<char,int>  z  std::hash<float> } �5  std::_Default_allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  '  std::hash<int>  :  std::_Num_int_base  �"  std::ctype<wchar_t> " �  std::_System_error_category  2  std::float_denorm_style u �4  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 �6  std::allocator_traits<std::allocator<wchar_t> >  3  std::bad_cast  �1  std::equal_to<void> � �%  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > , �8  std::_Ptr_base<donut::chunk::MeshSet> " _  std::numeric_limits<double>  c  std::__non_rtti_object I �,  std::_Vector_val<std::_Simple_types<donut::chunk::Chunk const *> > ( 0  std::_Basic_container_proxy_ptr12  [  std::_Num_float_base  _  std::logic_error 5 �:  std::_Ref_count_obj2<donut::chunk::MeshletSet>  �  std::pointer_safety ! �6  std::char_traits<char32_t>  !  std::locale  Q!  std::locale::_Locimp  -!  std::locale::facet   5!  std::locale::_Facet_guard  �   std::locale::id s M5  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   <  std::numeric_limits<bool> � �-  std::_Vector_val<std::_Simple_types<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > > # �5  std::_WChar_traits<char16_t> P O-  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T V  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy . ,  std::weak_ptr<donut::vfs::IBlob const >     std::_Fake_proxy_ptr_impl * R  std::numeric_limits<unsigned short> Z �4  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > M ,$  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > \ �+  std::vector<donut::chunk::Chunk const *,std::allocator<donut::chunk::Chunk const *> > r �+  std::vector<donut::chunk::Chunk const *,std::allocator<donut::chunk::Chunk const *> >::_Reallocation_policy  �  std::overflow_error % '.  std::_One_then_variadic_args_t � W5  std::_Default_allocator_traits<std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > > D 1  std::_Constexpr_immortalize_impl<std::_System_error_category> E )  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j �6  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   �6  std::char_traits<wchar_t>     std::pmr::memory_resource 0 =9  std::shared_ptr<donut::chunk::MeshletSet>  �6  std::false_type  5  std::float_round_style \ R2  std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  �  std::string T �$  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > , X  std::numeric_limits<unsigned __int64>  }   std::_Locinfo 6 3'  std::_Ptr_base<std::filesystem::_Dir_enum_impl> �-  std::_Compressed_pair<std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > >,std::_Vector_val<std::_Simple_types<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > >,1> s �3  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � m;  std::_Func_impl_no_alloc<<lambda_be0255e1888a8684391b3d47a840c833>,void,std::basic_string_view<char,std::char_traits<char> > > $ D  std::numeric_limits<char16_t>    std::string_view  U  std::wstring_view % �6  std::integral_constant<bool,1>   !  std::_Leave_proxy_unbound  s#  std::money_base  �6  std::money_base::pattern s f2  std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  M   std::_Timevec   2  std::_Init_once_completer j �(  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � e(  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + �!  std::codecvt<wchar_t,char,_Mbstatet> h .  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> Q �6  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >     std::_Iterator_base12  /  std::_Pocma_values !   std::hash<std::error_code> N 5%  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > 5 ,  std::_Ptr_base<donut::chunk::ChunkFile const > : �9  std::_Vector_val<std::_Simple_types<char const *> > @ �5  std::_Default_allocator_traits<std::allocator<char32_t> >  �$  std::allocator<char32_t> ? 9)  std::unique_ptr<char [0],std::default_delete<char [0]> > $ i  std::_Atomic_integral<long,4>     std::streamsize 6 
.  std::_String_val<std::_Simple_types<char32_t> > = �/  std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` ,/  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> M j*  std::_Arg_types<std::basic_string_view<char,std::char_traits<char> > >  �  std::hash<long double> � �%  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � {%  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy # H  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> & #6  std::bidirectional_iterator_tag / k5  std::_Char_traits<char32_t,unsigned int>  G  std::_System_error 9 ;-  std::allocator<std::filesystem::_Find_file_handle>    std::error_condition % �6  std::integral_constant<bool,0>  �  std::bad_exception # �9  std::allocator<char const *> & e-  std::_Zero_then_variadic_args_t  �  std::u32string  �  std::_Fake_allocator  �  std::invalid_argument + �)  std::pair<enum __std_win_error,bool> S $  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �  std::length_error F n3  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � C-  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! ]  std::numeric_limits<float>  #  std::time_base   y#  std::time_base::dateorder ) z  std::_Atomic_integral_facade<long>  �  std::_Ref_count_base  �6  std::ratio<60,1> S �*  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > > ] p0  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > >::_Storage  #  std::exception_ptr  �6  std::ratio<1,1000000> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > 2 �:  std::_Ref_count_obj2<donut::chunk::MeshSet> $ F  std::numeric_limits<char32_t>  *  std::once_flag  �  std::error_code  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l F  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k B  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �6  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �"  std::_Iosb<int>   �"  std::_Iosb<int>::_Seekdir ! �"  std::_Iosb<int>::_Openmode   �"  std::_Iosb<int>::_Iostate ! �"  std::_Iosb<int>::_Fmtflags # �"  std::_Iosb<int>::_Dummy_enum 0 �+  std::shared_ptr<donut::vfs::IBlob const > 7 �6  std::allocator_traits<std::allocator<char32_t> >  .6  std::nano  �  std::_Iterator_base0 1 5  std::_Char_traits<char16_t,unsigned short> T %%  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  �   std::_Locbase<int> ! �6  std::char_traits<char16_t>  �  std::tuple<>  �  std::_Container_base12    std::io_errc  6#  std::ios_base  G#  std::ios_base::_Fnarray  A#  std::ios_base::_Iosarray  �"  std::ios_base::Init  �"  std::ios_base::failure   #  std::ios_base::event E �0  std::_Constexpr_immortalize_impl<std::_Generic_error_category> ) B  std::numeric_limits<unsigned char>  �6  std::true_type   N  std::numeric_limits<long> " �6  std::initializer_list<char>  �  std::_Invoker_strategy  )  std::nothrow_t $ �  std::_Default_allocate_traits N %  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 �6  std::allocator_traits<std::allocator<char> > ! J  std::numeric_limits<short> ;   std::basic_string_view<char,std::char_traits<char> > ! �"  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > 9 �;  std::initializer_list<donut::chunk::Chunk const *> 6 ;.  std::_String_val<std::_Simple_types<char16_t> > = �/  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O (1  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > P �$  std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > ! �  std::_Shared_ptr_spin_lock  �  std::bad_alloc     std::underflow_error J !-  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> t �-  std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > D -  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  �#  std::messages_base o ;  std::_Tidy_guard<std::vector<donut::chunk::Chunk const *,std::allocator<donut::chunk::Chunk const *> > >  �  std::out_of_range # P  std::numeric_limits<__int64> i �-  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  V"  std::ctype<char>  :  std::memory_order � Z2  std::_Compressed_pair<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1>  �6  std::ratio<3600,1> � �  std::vector<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > >,std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > > � �  std::vector<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > >,std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > >::_Reallocation_policy # a  std::_Atomic_storage<long,4> / �6  std::shared_ptr<donut::vfs::IFileSystem>  P  std::atomic_flag f q.  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> � T6  std::allocator_traits<std::allocator<std::unique_ptr<donut::chunk::Chunk const ,std::default_delete<donut::chunk::Chunk const > > > >  j  std::system_error < 5  std::_Default_allocator_traits<std::allocator<char> >  R6  std::ratio<1,1> ; �;  std::allocator_traits<std::allocator<char const *> >   !6  std::forward_iterator_tag  �  std::runtime_error   	  std::bad_array_new_length D �;  std::_Default_allocator_traits<std::allocator<char const *> >  �   std::_Yarn<char>  �  std::_Container_proxy ( P6  std::_Facetptr<std::ctype<char> > Z �5  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > � �,  std::_Compressed_pair<std::allocator<donut::chunk::Chunk const *>,std::_Vector_val<std::_Simple_types<donut::chunk::Chunk const *> >,1>    std::u16string  \  std::nested_exception  �  std::_Distance_unknown ( T  std::numeric_limits<unsigned int> < Q3  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> , �!  std::codecvt<char32_t,char,_Mbstatet> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _ 	  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> & K6  std::initializer_list<char32_t> & A6  std::initializer_list<char16_t> % 76  std::initializer_list<wchar_t>   �  std::hash<std::nullptr_t> ' a  std::numeric_limits<long double>    std::errc - 9  std::shared_ptr<donut::chunk::MeshSet> 2 �,  std::allocator<donut::chunk::Chunk const *> , �2  std::default_delete<std::_Facet_base>    std::range_error  K  std::bad_typeid  .6  std::ratio<1,1000000000>  x$  std::allocator<char16_t> $ 	-  std::default_delete<char [0]> J �$  std::_String_iterator<std::_String_val<std::_Simple_types<char> > >  ,6  std::ratio<1,1000> S Z4  std::_Uninitialized_backout_al<std::allocator<donut::chunk::Chunk const *> >  *6  std::ratio<1,10000000>  �   std::_Crt_new_delete % �  std::_Iostream_error_category2 * (6  std::_String_constructor_concat_tag > {8  std::vector<char const *,std::allocator<char const *> > T I8  std::vector<char const *,std::allocator<char const *> >::_Reallocation_policy  a$  std::allocator<char> G �0  std::_Constexpr_immortalize_impl<std::_Iostream_error_category2> / �+  std::_Ptr_base<donut::vfs::IBlob const > 7 �9  std::_Ptr_base<donut::chunk::MeshSetBase const >    std::nullptr_t & %6  std::random_access_iterator_tag R �-  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  �  std::bad_weak_ptr ) V  std::numeric_limits<unsigned long>   �-  std::_Atomic_padded<long> @ 
(  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  �   std::_Yarn<wchar_t> 1 �8  std::shared_ptr<donut::chunk::MeshSetBase>  V  std::wstring } q4  std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > ' @  std::numeric_limits<signed char> � �%  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  v  std::domain_error  �  std::u32string_view  �  std::_Container_base p �9  std::_Compressed_pair<std::allocator<char const *>,std::_Vector_val<std::_Simple_types<char const *> >,1>  �%  std::allocator<wchar_t> / $9  std::_Ptr_base<donut::chunk::MeshletSet>   >  std::numeric_limits<char> 9 �  std::chrono::duration<__int64,std::ratio<1,1000> >  i  std::chrono::nanoseconds y )   std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? i  std::chrono::duration<__int64,std::ratio<1,1000000000> > , �4  std::chrono::duration_values<__int64>  =  std::chrono::seconds 3 �  std::chrono::duration<int,std::ratio<60,1> > 6 =  std::chrono::duration<__int64,std::ratio<1,1> > s Z  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   6  std::chrono::steady_clock   6  std::chrono::system_clock 6 �  std::chrono::duration<double,std::ratio<60,1> > ; 2  std::chrono::duration<double,std::ratio<1,1000000> > > I  std::chrono::duration<double,std::ratio<1,1000000000> > = $  std::chrono::duration<__int64,std::ratio<1,10000000> > q   std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 �  std::chrono::duration<int,std::ratio<3600,1> > 8   std::chrono::duration<double,std::ratio<1,1000> > <   std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �  std::chrono::duration<double,std::ratio<1,1> > 8 �  std::chrono::duration<double,std::ratio<3600,1> >  +"  std::ctype_base  ~&  std::filesystem::perms ' �&  std::filesystem::directory_entry $ �&  std::filesystem::copy_options ( n&  std::filesystem::filesystem_error 7 �1  std::filesystem::_Path_iterator<wchar_t const *> ) �#  std::filesystem::_Find_file_handle & �#  std::filesystem::_Is_slash_oper . �'  std::filesystem::_Should_recurse_result $ �)  std::filesystem::perm_options 4 �(  std::filesystem::recursive_directory_iterator . �&  std::filesystem::_File_status_and_error & e'  std::filesystem::_Dir_enum_impl 0 w'  std::filesystem::_Dir_enum_impl::_Creator @ }'  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! �&  std::filesystem::file_type . �'  std::filesystem::_Directory_entry_proxy " �)  std::filesystem::space_info * �'  std::filesystem::directory_iterator & )   std::filesystem::file_time_type 0 �'  std::filesystem::_Recursive_dir_enum_impl ) '  std::filesystem::directory_options # �&  std::filesystem::file_status u (&  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( 6  std::filesystem::_File_time_clock  �$  std::filesystem::path $ �#  std::filesystem::path::format * z1  std::filesystem::_Normal_conversion ( �:  std::_Wrap<donut::chunk::MeshSet> < �3  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �!  std::codecvt<char16_t,char,_Mbstatet>  6  std::char_traits<char> � �-  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �  std::error_category ) �  std::error_category::_Addr_storage ! �  std::_System_error_message  �  std::_Unused_parameter h J.  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A U  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 M'  std::shared_ptr<std::filesystem::_Dir_enum_impl>  �!  std::_Codecvt_mode  A   std::max_align_t @ �5  std::_Default_allocator_traits<std::allocator<char16_t> > � ?&  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > > 0 85  std::_Char_traits<wchar_t,unsigned short> 5 e.  std::_String_val<std::_Simple_types<wchar_t> > < �/  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  <   std::_Facet_base " C5  std::_WChar_traits<wchar_t> 2 "  std::codecvt<unsigned short,char,_Mbstatet> S �5  std::_Default_allocator_traits<std::allocator<donut::chunk::Chunk const *> > # �  std::_Generic_error_category  5  std::streampos  6  std::input_iterator_tag 2 f3  std::_Wrap<std::filesystem::_Dir_enum_impl> X �2  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1>  y!  std::codecvt_base t 6  std::allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > J 6  std::allocator_traits<std::allocator<donut::chunk::Chunk const *> >  �  std::bad_function_call � ^*  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � .*  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Reallocation_policy X �*  std::function<void __cdecl(std::basic_string_view<char,std::char_traits<char> >)> D �;  std::_Uninitialized_backout_al<std::allocator<char const *> > ' �)  std::hash<std::filesystem::path> R �/  std::_Func_base<void,std::basic_string_view<char,std::char_traits<char> > > 7 6  std::allocator_traits<std::allocator<char16_t> > 6 3,  std::shared_ptr<donut::chunk::ChunkFile const > " �  std::_Asan_aligned_pointers 0 �8  std::_Ptr_base<donut::chunk::MeshSetBase> + 
;  std::_Wrap<donut::chunk::MeshletSet>  L  std::numeric_limits<int> 2 >/  std::_String_val<std::_Simple_types<char> > 9 �/  std::_String_val<std::_Simple_types<char> >::_Bxty  y  std::bad_variant_access 
 !   wint_t  �  __std_win_error  �   lconv   l  __RTTIBaseClassDescriptor 
    _off_t & �;  $_TypeDescriptor$_extraBytes_54  �  stat  �  timespec  v)  __std_fs_file_id 
 !   _ino_t ' _)  __std_fs_create_directory_result  !   uint16_t  �  __std_fs_stats ' �*  donut::vfs::enumerate_callback_t % �*  donut::vfs::RelativeFileSystem  �*  donut::vfs::IBlob  �*  donut::vfs::IFileSystem  R  donut::math::float4x4 " f  donut::math::vector<bool,4>  �  donut::math::float3  J  donut::math::affine3  �  donut::math::float2 # �  donut::math::vector<float,3>  u   donut::math::uint  �  donut::math::plane # �  donut::math::vector<float,4>    donut::math::frustum $ �  donut::math::frustum::Corners # �  donut::math::frustum::Planes  �  donut::math::float4 # J  donut::math::affine<float,3>   �  donut::math::box<float,3> "   donut::math::vector<bool,2>  �  donut::math::box3 % u  donut::math::matrix<float,3,3> " @  donut::math::vector<bool,3> # �  donut::math::vector<float,2>  h7  donut::chunk::MeshSet ! �8  donut::chunk::MeshInfoBase 1 �7  donut::chunk::StringsTable_ChunkDesc_0x100 = �8  donut::chunk::StringsTable_ChunkDesc_0x100::TableEntry ! H9  donut::chunk::MeshInstance 2 �7  donut::chunk::MeshInstances_ChunkDesc_0x100 . �7  donut::chunk::MeshNodes_ChunkDesc_0x100   I7  donut::chunk::MeshSetBase / N7  donut::chunk::MeshSetBase::VertexStreams & :7  donut::chunk::MeshSetBase::Type  �7  donut::chunk::Type  (+  donut::chunk::Chunk  �7  donut::chunk::Semantic  �7  donut::chunk::Vary + �7  donut::chunk::Stream_ChunkDesc_0x100  �7  donut::chunk::ChunkType  �8  donut::chunk::MeshInfo   �8  donut::chunk::MeshletInfo . �7  donut::chunk::MeshInfos_ChunkDesc_0x100 4 �7  donut::chunk::MeshInfos_ChunkDesc_0x100::Type ! �7  donut::chunk::StreamHandle  +  donut::chunk::ChunkId  `  donut::chunk::ChunkFile   8  donut::chunk::ChunkReader  R9  donut::chunk::MeshNode  �7  donut::chunk::MeshletSet , �7  donut::chunk::MeshSet_ChunkDesc_0x100 5 �7  donut::chunk::MeshSet_ChunkDesc_0x100::Streams 2 �7  donut::chunk::MeshSet_ChunkDesc_0x100::Type M   __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  �  _Mbstatet    _locale_t B G  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  �  _s__RTTIBaseClassArray 
 �  ldiv_t  �  __std_fs_file_flags  �   _Cvtvec - v  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray  �  __std_fs_copy_options     ptrdiff_t  �  _stat64i32  �  _PMD      uint8_t     type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  __std_fs_reparse_tag  �  _lldiv_t    __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  E  _s__ThrowInfo     __std_fs_convert_result  �  __std_fs_stats_flags  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24 & �5  $_TypeDescriptor$_extraBytes_25 % �  __RTTIClassHierarchyDescriptor  �   _Collvec   �(  __std_fs_volume_name_kind     __time64_t    FILE 3 �  __vcrt_va_list_is_reference<wchar_t const *>  �  mbstate_t  =  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  [)  __std_fs_remove_result -   $_s__RTTIBaseClassArray$_extraBytes_16 - �5  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  �(  __std_ulong_and_error  �  lldiv_t  �  _ldiv_t  �  _timespec64     intptr_t  u   uint32_t 
   _iobuf    __crt_locale_pointers   �   0      隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  R    U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �    矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �    
9信�;cGHR{匞U鐉�gPW$Y(厊\^s     �	玮媔=zY沚�c簐P`尚足,\�>:O  B   �"睱建Bi圀対隤v��cB�'窘�n  �   郖�Χ葦'S詍7,U若眤�M进`  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  $   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  c   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  0   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �    d蜯�:＠T邱�"猊`�?d�B�#G騋     溶�$椉�
悇� 騐`菚y�0O腖悘T  [   y害20PQ后埽<揿~
R黡�5�a�  �   +FK茂c�G1灈�7ほ��F�鳺彷餃�  �   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx     x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  P   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   猯�諽!~�:gn菾�]騈购����'  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  
   `k�"�1�^�`�d�.	*貎e挖芺
脑�  L   ?荒�c癕匨R洆~���1(屹z艝鈭�
  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   q=H?誋g戍鳼毸{眎r憴¤�2刀渀|<  �   �0�*е彗9釗獳+U叅[4椪 P"��  4   �=蔑藏鄌�
艼�(YWg懀猊	*)  u   j�3鉡劯�6瞫谤毇�'扰靐�*cj�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  (   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  g   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  2	   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  k	   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �	   繃S,;fi@`騂廩k叉c.2狇x佚�  �	   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  >
   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �
   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �
   +YE擋%1r+套捑@鸋MT61' p廝 飨�     }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  J   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.     f扥�,攇(�
}2�祛浧&Y�6橵�  M   曀"�H枩U传嫘�"繹q�>窃�8  �   [届T藎秏1潴�藠?鄧j穊亘^a  �   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  
   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  Y
   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �
   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �
   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  4   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  r   鹴y�	宯N卮洗袾uG6E灊搠d�  �   dhl12� 蒑�3L� q酺試\垉R^{i�  �   副謐�斦=犻媨铩0
龉�3曃譹5D   ;   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   �(M↙溋�
q�2,緀!蝺屦碄F觡     G�膢刉^O郀�/耦��萁n!鮋W VS  V   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  '   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  e   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  *   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  s   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �
bH<j峪w�/&d[荨?躹耯=�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  ;   +椬恡�
	#G許�/G候Mc�蜀煟-  {   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈     a�傌�抣?�g]}拃洘銌刬H-髛&╟  I   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说     �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  a   +4[(広
倬禼�溞K^洞齹誇*f�5  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  h   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �    狾闘�	C縟�&9N�┲蘻c蟝2  %   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  d   �'稌� 变邯D)\欅)	@'1:A:熾/�  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  9   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  x   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  <   v-�+鑟臻U裦@驍�0屽锯
砝簠@  w   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  	   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  I   交�,�;+愱`�3p炛秓ee td�	^,  �   zY{���睃R焤�0聃
扨-瘜}  �   _臒~I��歌�0蘏嘺QU5<蝪祰S     娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  R   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   妇舠幸佦郒]泙茸餈u)	�位剎  ,   A縏 �;面褡8歸�-構�壋馵�2�-R癕  k   靋!揕�H|}��婡欏B箜围紑^@�銵  �   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   t�j噾捴忊��
敟秊�
渷lH�#  3   _O縋[HU-銌�鼪根�鲋薺篮�j��  |   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  L   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  1   蜅�萷l�/费�	廵崹
T,W�&連芿  n   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   D���0�郋鬔G5啚髡J竆)俻w��  
    -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  I    匐衏�$=�"�3�a旬SY�
乢�骣�  �    �咹怓%旗t暐GL慚ヌ��\T鳃�  �    悯R痱v 瓩愿碀"禰J5�>xF痧  !   *u\{┞稦�3壅阱\繺ěk�6U�  S!   矨�陘�2{WV�y紥*f�u龘��  �!    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �!   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  "   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  T"   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �"   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �"   鏀q�N�&}
;霂�#�0ncP抝  #   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  d#   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �#   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �#   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  B$   V� c鯐鄥杕me綻呥EG磷扂浝W)  �$   k�8.s��鉁�-[粽I*1O鲠-8H� U  �$   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  %   L�9[皫zS�6;厝�楿绷]!��t  M%   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   $      �  �  B   �  �  H   �  �  Y   �  �  �   �    U   �    �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  x    �  8     �      �  �    �  5  s  �  �  t  �  �  v  �  5  �  �    �  �     �  �  t  !  �  �  #  �  Z  $  �  �  %  �  t  ]  �  �  ^  �  �    �  z  K  �  c  �  �  :  �  �  D  �  �	  �   �    �   �  �	  �   �  �  n  �    �      �  0    �   2    @   @    �  n    �   �  �  �  �  �  e   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  	  �    6   �    A   �    �   �    �   �  �     �  �  5  �  �     �    S  �    �  �    i  �  �  "  �  �  5  �  �  �  �  �  �  �  �  �  �  �  d  �  �  Z  �  �  "  �    S  �    4  �    �  �    `  �  �  �      �    �  t    �  t    �  t    �  t      �    �  �    �  d  #    �  $  �  z  (  �  �   *  �  �   ,  �  �   -  �  �   /  �  �  1  �  �  3  �  �   4  �  �   5  �  �   6  �  �   7  �  �
  9  �  �
  ;  �  �  <  �  i  =    �  >      @  �  :  A  �  D  B    
  C    �  K  �  :  O  �	  �   Q  �  5  S  �  5  [    �  \    �  a  �  �  b  �  n  c    �  k    �  l  �  z  p  �  i  q  �  i  t  �  �   u  �  O  v  �  O  w  �  �   x  �  �   y  �  �   z  �  &  |  �  9  ~  �  &  �  �  9  �  �  �  �  �  :  �    �  �    �  �    /  �    /  �  �  :  �  �  :  �  �	  �   �    �  �    �  �    ]  �  �  :  �  �  :  �  �  M  �  �  M  �  `	  �   �  `	  �   �    �  �    �  �    �  �    �  �    �  �    �  �  `	  �  �    �  �   �%   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\External\Donut\include\donut\core\chunk\chunkFile.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\RTXPT\External\Donut\src\core\chunk\chunkDescs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\src\core\chunk\chunkReader.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\core\chunk\chunk.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert D:\RTXPT\External\Donut\include\donut\core\math\basics.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\RTXPT\External\Donut\include\donut\core\math\vector.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Donut\include\donut\core\vfs\VFS.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept   �       L�  �;      �;     
    j 1嗕N鍞O摤*	�   D:\RTXPT\cmake-build-release-visual-studio\External\Donut\Release\donut_core.pdb 篁裥砓>Y7?樰�=H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /      5         �   �  k G            :      :   n        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >#   _Bytes  AJ        9  $  >#    _Block_size  AH       1 
   >#    _Ptr_container  AJ        
 >`    _Ptr  AH  %     	  M        �  
 Z   �   N Z     �   (                      H 
 h   �         $LN14  0   #  O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   H   0   H  
 �   H   �   H  
 �   H   �   H  
 �   H   �   H  
   H     H  
 s  �   w  �  
 �  H   �  H  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   V      �   V  W G            0   
   %   �        �std::_Copy_memmove<char const * *,char const * *>  >�   _First  AJ          >�   _Last  AK          >�   _Dest  AM         AP          >#    _Count  AI  
                             H 
 h   �   0   �  O_First  8   �  O_Last  @   �  O_Dest  O  �   @           0   `	     4       � �   � �   � �!   � �%   � �,   G   0   G  
 ~   G   �   G  
 �   G   �   G  
 �   G   �   G  
 �   G   �   G  
 �   G   �   G  
 l  G   p  G  
 H塋$SVWAVAWH冹 L嬺H孂I�������I;��)  L媦L+9I�H婭H+H六H嬔H殃I嬂H+翲;�國   H�
I;苨
I嬈H塂$h�H塂$hI;�囎   H�4�    H侢   r)H峃'H;�喒   �    H吚劮   H峏'H冦郒塁H咑t
H嬑�    H嬝�3跦塡$XJ�鸐嬈M+荖��    3诣    L婫H�L+翲嬎�    怘�H吷t1H媁H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐w3I嬋�    H�J�驢塆H�H塆H兡 A_A^_^[描    惕    惕    虜   �    �   �    �   W   �   V   )      M     S  %   Y        �     � G            ^     ^  �        �std::vector<char const *,std::allocator<char const *> >::_Resize_reallocate<std::_Value_init_tag> 
 >8   this  AJ          AM       H3  DP    >#   _Newsize  AK          AV       K5 
 >�9   _Val  AP           D`    >#     _Newcapacity  AH  `       AH u     � #  G  l k  Bh   e     �   �   >#    _Oldsize  AW  -     1   %  >j8    _Appended_first  AJ  �       >j8    _Newvec  AI  �       AI �     � � 
  BX   �     � �   M        �  Wl�� M        �  Wl��% M        0  })
)%
��( M        n  ��$	%)
��
 Z   �   >#    _Block_size  AJ  �       AJ L      >#    _Ptr_container  AH  �       AH �     �  w 
 >`    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        2  
l
	 N N N! M        �  4kD%
 >#    _Oldcapacity  AJ  8     �   R %  
  AJ �     � 	 �  >#    _Geometric  AH  X         AH u     � #  G  l k  M        �  4 N N M        �  �� >#    _Count  AP  �       M        �  �� N N M        �  �� >j8   _First  AK  �       >j8   _Last  AP  �       M        �  ��c >#    _Count  AP  �       N N% M        �  ��h1#  M        #  *�U M        �  �)0
 Z   �  
 >   _Ptr  AJ (      >#    _Bytes  AK       -    AK X     % M        �  �d#
3
 Z      >#    _Ptr_container  AP        AP (    5  +  >#    _Back_shift  AJ  �     1  AJ (    5 $   N N N N
 Z   �               (         0@ � h!   �  �  �  �  �  0  2  n  "  #  J  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN97  P   8  Othis  X   #  O_Newsize  `   �9  O_Val  O  �   �           ^    
   t       � �   � �)   � �4   � �l   � ��   � ��   � ��   � ��   	 �@  
 �L  � �R  � �X  	 ��   �  � F            (   
   (             �`std::vector<char const *,std::allocator<char const *> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >8   this  EN  P         ( 
 >�9   _Val  EN  `         ( 
 Z   #                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN97  P   8  Nthis  X   #  N_Newsize  `   �9  N_Val  O �   0           (        $        �
    �    �,   D   0   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
 �   D   �   D  
   D   "  D  
 M  D   Q  D  
 ]  D   a  D  
 y  D   }  D  
 �  D   �  D  
 �  D   �  D  
 �  D      D  
   D     D  
    D   $  D  
 �  D   �  D  
 �  D   �  D  
   D     D  
 "  D   &  D  
 E  D   I  D  
 U  D   Y  D  
   D      D  
 8  D   <  D  
 a  D   e  D  
 u  D   y  D  
 �  D   �  D  
 *  D   .  D  
 J  D   N  D  
 �  D   �  D  
   D     D  
 <  D   @  D  
 P  D   T  D  
 �  D   �  D  
 �  D   �  D  
 �  D   �  D  
 �  D   �  D  
 �  �   �  �  
 $  D   (  D  
 �  I   �  I  
 `	  I   d	  I  
 �	  I   �	  I  
 �	  �   �	  �  
 E
  �   I
  �  
 �
  I   �
  I  
 H塗$UH冹 H嬯L婨hH婾XH婱P�    3�3设    �   $   #   T   @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�   W      �   �   F G                     �        �std::_Zero_range<char const * *>  >j8   _First  AJ          >j8   _Last  AI         AK                                H 
 h   �   0   j8  O_First  8   j8  O_Last  O�   8                   ,       � �   � �   � �   � �,   F   0   F  
 m   F   q   F  
 �   F   �   F  
 �   F   �   F  
   F     F  
 @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�   W      �   �   U G                     �        �std::_Zero_range<donut::chunk::Chunk const * *>  >�+   _First  AJ          >�+   _Last  AI         AK                                H 
 h   c   0   �+  O_First  8   �+  O_Last  O �   8                   ,       � �   � �   � �   � �,   E   0   E  
 |   E   �   E  
 �   E   �   E  
 �   E   �   E  
   E     E  
 H冹(凓�uH�
    �    3繦兡(肏塡$ �    H嬝H吚劗   D婡A侙  t(�H�
    A�  �    3�2蓜蒆E肏媆$ H兡(肈婡A侙   t(�H�
    A�   �    3�2蓜蒆E肏媆$ H兡(肏儀 tH儀  t3辣勆HE肏媆$ H兡(脣H�
    �    3�2蓜蒆E肏媆$ H兡(肏�
    �    H媆$ 3繦兡(�   d         "      D   [   O      y   ^   �      �   a   �      �   g   �         �   �  p G            �      �   -        �donut::chunk::ChunkFile::getChunk<donut::chunk::MeshInfos_ChunkDesc_0x100> 
 >3   this  AJ        &    >+   chunkId  A         &    >N+    chunk  AI  )     � 9  n  �  �   M           N/ M        t  2
(
(
 Z   �  �  �   N Z   �    �   (                      H  h     t   0   3  Othis  8   +  OchunkId  O  �   �           �   �     �       �  �   �  �	   �  �   �  �   �  �!   �  �2   �  �b   �  �g   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �,   )   0   )  
 �   )   �   )  
 �   )   �   )  
 �   )   �   )  
 �  )   �  )  
 H冹(凓�uH�
    �    3繦兡(肏塡$ �    H嬝H吚劗   D婡A侙  t(�H�
    A�  �    3�2蓜蒆E肏媆$ H兡(肈婡A侙   t(�H�
    A�   �    3�2蓜蒆E肏媆$ H兡(肏儀 tH儀  t3辣勆HE肏媆$ H兡(脣H�
    �    3�2蓜蒆E肏媆$ H兡(肏�
    �    H媆$ 3繦兡(�   d         "      D   [   O      y   ^   �      �   a   �      �   g   �         �   �  t G            �      �   3        �donut::chunk::ChunkFile::getChunk<donut::chunk::MeshInstances_ChunkDesc_0x100> 
 >3   this  AJ        &    >+   chunkId  A         &    >N+    chunk  AI  )     � 9  n  �  �   M           N/ M        w  2
(
(
 Z   �  �  �   N Z   �    �   (                      H  h     w   0   3  Othis  8   +  OchunkId  O  �   �           �   �     �       �  �   �  �	   �  �   �  �   �  �!   �  �2   �  �b   �  �g   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �,   .   0   .  
 �   .   �   .  
 �   .   �   .  
 �   .   �   .  
 �  .   �  .  
 H冹(凓�uH�
    �    3繦兡(肏塡$ �    H嬝H吚劗   D婡A侙  t(�H�
    A�  �    3�2蓜蒆E肏媆$ H兡(肈婡A侙   t(�H�
    A�   �    3�2蓜蒆E肏媆$ H兡(肏儀 tH儀  t3辣勆HE肏媆$ H兡(脣H�
    �    3�2蓜蒆E肏媆$ H兡(肏�
    �    H媆$ 3繦兡(�   d         "      D   [   O      y   ^   �      �   a   �      �   g   �         �   �  p G            �      �   4        �donut::chunk::ChunkFile::getChunk<donut::chunk::MeshNodes_ChunkDesc_0x100> 
 >3   this  AJ        &    >+   chunkId  A         &    >N+    chunk  AI  )     � 9  n  �  �   M           N/ M        x  2
(
(
 Z   �  �  �   N Z   �    �   (                      H  h     x   0   3  Othis  8   +  OchunkId  O  �   �           �   �     �       �  �   �  �	   �  �   �  �   �  �!   �  �2   �  �b   �  �g   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �,   /   0   /  
 �   /   �   /  
 �   /   �   /  
 �   /   �   /  
 �  /   �  /  
 H冹(凓�uH�
    �    3繦兡(肏塡$ �    H嬝H吚劗   D婡A侙   t(�H�
    A�   �    3�2蓜蒆E肏媆$ H兡(肈婡A侙   t(�H�
    A�   �    3�2蓜蒆E肏媆$ H兡(肏儀 tH儀  t3辣勆HE肏媆$ H兡(脣H�
    �    3�2蓜蒆E肏媆$ H兡(肏�
    �    H媆$ 3繦兡(�   d         "      D   [   O      y   ^   �      �   a   �      �   g   �         �   �  m G            �      �   5        �donut::chunk::ChunkFile::getChunk<donut::chunk::Stream_ChunkDesc_0x100> 
 >3   this  AJ        &    >+   chunkId  A         &    >N+    chunk  AI  )     � 9  n  �  �   M           N/ M        y  2
(
(
 Z   �  �  �   N Z   �    �   (                      H  h     y   0   3  Othis  8   +  OchunkId  O �   �           �   �     �       �  �   �  �	   �  �   �  �   �  �!   �  �2   �  �b   �  �g   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �,   0   0   0  
 �   0   �   0  
 �   0   �   0  
 �   0   �   0  
 �  0   �  0  
 L�3繦�H堿H婤H吚t�@H婤H堿H嬃L��   �   �  o G            (       '   /        �std::static_pointer_cast<donut::chunk::MeshSet,donut::chunk::MeshSetBase>  >�8   _Other  AK        ( 
 >T7    _Ptr  AP       %  M        u  	 M        �  L
 M        b  ,	 M        �   N N N M        Q  � N N                        H�  h   �    Q  b  u  �      �8  O_Other  O�   8           (   �     ,       � �    � �   � �'   � �,   *   0   *  
 �   *   �   *  
 �   *   �   *  
 �  *   �  *  
 L�3繦�H堿H婤H吚t�@H婤H堿H嬃L��   �   �  r G            (       '   1        �std::static_pointer_cast<donut::chunk::MeshletSet,donut::chunk::MeshSetBase>  >�8   _Other  AK        ( 
 >l7    _Ptr  AP       %  M        v  	 M        �  L
 M        b  ,	 M        �   N N N M        S  � N N                        H�  h   �    S  b  v  �      �8  O_Other  O �   8           (   �     ,       � �    � �   � �'   � �,   ,   0   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 �  ,   �  ,  
 H冹(H呉tsD婤A侙  t�H�
    A�  �    2繦兡(肈婤A侙   t�H�
    A�   �    2繦兡(肏儂 tH儂  t�H兡(脣H�
    �    2繦兡(�   [   &      C   ^   N      s   a   x         �   K  u G            �      ~   t        �donut::chunk::ChunkFile::validateChunk<donut::chunk::MeshInfos_ChunkDesc_0x100> 
 >3   this  AJ        w   G   AJ |       D0    >L   chunk  AK        p   @   AK |       Z   �  �  �   (                      H  0   3  Othis  8   L  Ochunk  O �   �           �   �  
   t       �  �   �  �	   �  �   �  �*   �  �1   �  �>   �  �R   �  �Y   �  �g   �  �i   �  �n   �  �|   �  �,   4   0   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 �   4   �   4  
 `  4   d  4  
 H冹(H呉tsD婤A侙  t�H�
    A�  �    2繦兡(肈婤A侙   t�H�
    A�   �    2繦兡(肏儂 tH儂  t�H兡(脣H�
    �    2繦兡(�   [   &      C   ^   N      s   a   x         �   O  y G            �      ~   w        �donut::chunk::ChunkFile::validateChunk<donut::chunk::MeshInstances_ChunkDesc_0x100> 
 >3   this  AJ        w   G   AJ |       D0    >L   chunk  AK        p   @   AK |       Z   �  �  �   (                      H  0   3  Othis  8   L  Ochunk  O �   �           �   �  
   t       �  �   �  �	   �  �   �  �*   �  �1   �  �>   �  �R   �  �Y   �  �g   �  �i   �  �n   �  �|   �  �,   5   0   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 �   5   �   5  
 d  5   h  5  
 H冹(H呉tsD婤A侙  t�H�
    A�  �    2繦兡(肈婤A侙   t�H�
    A�   �    2繦兡(肏儂 tH儂  t�H兡(脣H�
    �    2繦兡(�   [   &      C   ^   N      s   a   x         �   K  u G            �      ~   x        �donut::chunk::ChunkFile::validateChunk<donut::chunk::MeshNodes_ChunkDesc_0x100> 
 >3   this  AJ        w   G   AJ |       D0    >L   chunk  AK        p   @   AK |       Z   �  �  �   (                      H  0   3  Othis  8   L  Ochunk  O �   �           �   �  
   t       �  �   �  �	   �  �   �  �*   �  �1   �  �>   �  �R   �  �Y   �  �g   �  �i   �  �n   �  �|   �  �,   6   0   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 `  6   d  6  
 H冹(H呉tsD婤A侙   t�H�
    A�   �    2繦兡(肈婤A侙   t�H�
    A�   �    2繦兡(肏儂 tH儂  t�H兡(脣H�
    �    2繦兡(�   [   &      C   ^   N      s   a   x         �   I  s G            �      ~   6        �donut::chunk::ChunkFile::validateChunk<donut::chunk::MeshSet_ChunkDesc_0x100> 
 >3   this  AJ        w   G   AJ |       D0    >L   chunk  AK        p   @   AK |       Z   �  �  �   (                      H  0   3  Othis  8   L  Ochunk  O   �   �           �   �  
   t       �  �   �  �	   �  �   �  �*   �  �1   �  �>   �  �R   �  �Y   �  �g   �  �i   �  �n   �  �|   �  �,   1   0   1  
 �   1   �   1  
 �   1   �   1  
 �   1   �   1  
 �   1   �   1  
 `  1   d  1  
 H冹(H呉tsD婤A侙   t�H�
    A�   �    2繦兡(肈婤A侙   t�H�
    A�   �    2繦兡(肏儂 tH儂  t�H兡(脣H�
    �    2繦兡(�   [   &      C   ^   N      s   a   x         �   H  r G            �      ~   y        �donut::chunk::ChunkFile::validateChunk<donut::chunk::Stream_ChunkDesc_0x100> 
 >3   this  AJ        w   G   AJ |       D0    >L   chunk  AK        p   @   AK |       Z   �  �  �   (                      H  0   3  Othis  8   L  Ochunk  O�   �           �   �  
   t       �  �   �  �	   �  �   �  �*   �  �1   �  �>   �  �R   �  �Y   �  �g   �  �i   �  �n   �  �|   �  �,   7   0   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
 �   7   �   7  
 \  7   `  7  
 H冹(H呉tsD婤A侙  t�H�
    A�  �    2繦兡(肈婤A侙   t�H�
    A�   �    2繦兡(肏儂 tH儂  t�H兡(脣H�
    �    2繦兡(�   [   &      C   ^   N      s   a   x         �   N  x G            �      ~   ,        �donut::chunk::ChunkFile::validateChunk<donut::chunk::StringsTable_ChunkDesc_0x100> 
 >3   this  AJ        w   G   AJ |       D0    >L   chunk  AK        p   @   AK |       Z   �  �  �   (                      H  0   3  Othis  8   L  Ochunk  O  �   �           �   �  
   t       �  �   �  �	   �  �   �  �*   �  �1   �  �>   �  �R   �  �Y   �  �g   �  �i   �  �n   �  �|   �  �,   '   0   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 �   '   �   '  
 d  '   h  '  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %      ,   �      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   
   0   
  
 d   
   h   
  
 t   
   x   
  
 �   
   �   
  
 �   
   �   
  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %      ,   �      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,      0     
 z      ~     
 �      �     
 �      �     
 �      �     
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,      0     
 z      ~     
          
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %         �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,      0     
 d      h     
 t      x     
 �      �     
 �      �     
          
 H�    H��   j      �   �   z G                   
   Z        �std::_Ref_count_obj2<donut::chunk::MeshSet>::~_Ref_count_obj2<donut::chunk::MeshSet> 
 >�:   this  AJ                                 H� 
 h   �      �:  Othis  O�   (              �            2 �
   8 �,   8   0   8  
 �   8   �   8  
 �   8   �   8  
 H�    H��   m      �   �   � G                   
   W        �std::_Ref_count_obj2<donut::chunk::MeshletSet>::~_Ref_count_obj2<donut::chunk::MeshletSet> 
 >�:   this  AJ                                 H� 
 h   �      �:  Othis  O  �   (              �            2 �
   8 �,   >   0   >  
 �   >   �   >  
 �   >   �   >  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  v G            K      E   t        �std::shared_ptr<donut::vfs::IBlob const >::~shared_ptr<donut::vfs::IBlob const > 
 >�+   this  AJ        +  AJ @       M        %  &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �  %   0   �+  Othis  9+       �   9=       �   O�   0           K   �     $       � �   � �E   � �,      0     
 �      �     
 �      �     
 �          
 t     x    
 �     �    
 �     �    
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  p G            K      E   �        �std::shared_ptr<donut::chunk::MeshSet>::~shared_ptr<donut::chunk::MeshSet> 
 >�8   this  AJ        +  AJ @       M          &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �     0   �8  Othis  9+       �   9=       �   O  �   0           K   �     $       � �   � �E   � �,   +   0   +  
 �   +   �   +  
 �   +   �   +  
 �   +   �   +  
 n  +   r  +  
 ~  +   �  +  
 �  +   �  +  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  x G            K      E   �        �std::shared_ptr<donut::chunk::MeshSetBase>::~shared_ptr<donut::chunk::MeshSetBase> 
 >�8   this  AJ        +  AJ @       M          &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �     0   �8  Othis  9+       �   9=       �   O  �   0           K   �     $       � �   � �E   � �,   (   0   (  
 �   (   �   (  
 �   (   �   (  
    (     (  
 v  (   z  (  
 �  (   �  (  
 �  (   �  (  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  v G            K      E   �        �std::shared_ptr<donut::chunk::MeshletSet>::~shared_ptr<donut::chunk::MeshletSet> 
 >(9   this  AJ        +  AJ @       M          &, M        �  
 >�   this  AI  
     @  M        �  -	
 N N N                       H�  h   �  �     0   (9  Othis  9+       �   9=       �   O�   0           K   �     $       � �   � �E   � �,   -   0   -  
 �   -   �   -  
 �   -   �   -  
 �   -     -  
 t  -   x  -  
 �  -   �  -  
 �  -   �  -  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>       V         �   �  � G            [      [   �        �std::vector<donut::chunk::Chunk const *,std::allocator<donut::chunk::Chunk const *> >::~vector<donut::chunk::Chunk const *,std::allocator<donut::chunk::Chunk const *> > 
 >[+   this  AI  	     R K   AJ        	 $ M          	h1%	
 M        �  *= M        �  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z      >#    _Ptr_container  AP  )     1    AP =       >#    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� " h   �  �  �  L  �  �           $LN28  0   [+  Othis  O  �   8           [        ,       � �	   � �O    �U   � �,   3   0   3  
 �   3   �   3  
   3     3  
 �  3   �  3  
 �  3   �  3  
   3     3  
 &  3   *  3  
 L  3   P  3  
 `  3   d  3  
 �  y   �  y  
 �  3   �  3  
 H婭H吷t�����罙凐uH�H�`�   �   =  r G                               �std::weak_ptr<donut::vfs::IBlob const >::~weak_ptr<donut::vfs::IBlob const > 
 >�+   this  AJ          M        $   	 M        �  )
 >�   this  AJ         N N                        H�  h   �  $      �+  Othis  9       �   O   �   0               �     $       � �    � �   � �,   2   0   2  
 �   2   �   2  
 �   2   �   2  
 9  2   =  2  
 T  2   X  2  
 @SH冹 H嬞H婭H吷t?H婼 H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w[I嬋�    3繦塁H塁H塁 H媅H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [描    �?       �         �   A  M G            �      �   �        �donut::chunk::ChunkReader::~ChunkReader 
 >�7   this  AI  	     � L A  AJ        	  M          :Q M        �  Q, M        �  _
 >�   this  AI  U     @  M        �  x	
 N N N N M        �  H	��$ M          	i1&	I M        #  *} M        �  )X
 Z   �  
 >   _Ptr  AJ >       >#    _Bytes  AK       �   - S " M        �  
&#
[
 Z      >#    _Ptr_container  AP  *     q  X  AP >       >#    _Back_shift  AJ  
     � 1 X  AJ >     X   8   N N N N N                       H� 6 h   �  �  �  �    �  �  �    "  #  J         $LN46  0   �7  Othis  9v       �   9�       �   O   ,   &   0   &  
 r   &   v   &  
 �   &   �   &  
 �   &   �   &  
 �  &   �  &  
 �  &   �  &  
 =  &   A  &  
 Q  &   U  &  
 w  &   {  &  
 �  &   �  &  
 	  n   
  n  
 -  &   1  &  
 =  &   A  &  
 �     �   �   L G                       �        �std::_Ref_count_base::~_Ref_count_base 
 >�   this  AJ          D                           H�     �  Othis  O  �                  �            ~ �,      0     
 q      u     
 �      �     
 H�    H�H兞�       �            �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,      0     
 {           
 H�    H�H兞�       �            �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,      0     
 e      i     
 �      �     
 @SH冹 H�    H嬞H�雎t
焊   �    H嬅H兡 [�	   j             �   �   o G            +      %   j        �std::_Ref_count_obj2<donut::chunk::MeshSet>::`scalar deleting destructor' 
 >�:   this  AI         AJ                                @� 
 h   Z   0   �:  Othis  O   ,   ;   0   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 @SH冹 H�    H嬞H�雎t
亨   �    H嬅H兡 [�	   m             �   �   r G            +      %   i        �std::_Ref_count_obj2<donut::chunk::MeshletSet>::`scalar deleting destructor' 
 >�:   this  AI         AJ                                @� 
 h   W   0   �:  Othis  O,   A   0   A  
 �   A   �   A  
 �   A   �   A  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �         0          �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   �   N                       @�  h   �  �   0   �  Othis  O ,      0     
 w      {     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �         0          �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   �   N                       @�  h   �  �  �   0   �  Othis  O  ,      0     
 �      �     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �         0          �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   �   N                       @� 
 h   �   0   �  Othis  O ,      0     
 w      {     
 �      �     
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H     O__f  9(          O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H吷tH��   H�`�   �   �   _ G                      X        �std::_Ref_count_obj2<donut::chunk::MeshSet>::_Delete_this 
 >�:   this  AJ                                 @�     �:  Othis  9
       �:   O   �   0              �     $       C �    D �   E �,   :   0   :  
 �   :   �   :  
 �   :   �   :  
 �   :   �   :  
 H吷tH��   H�`�   �   �   b G                      U        �std::_Ref_count_obj2<donut::chunk::MeshletSet>::_Delete_this 
 >�:   this  AJ                                 @�     �:  Othis  9
       �:   O�   0              �     $       C �    D �   E �,   @   0   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 @SH冹 H嫏�   H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  [ G            N      H   Y        �std::_Ref_count_obj2<donut::chunk::MeshSet>::_Destroy 
 >�:   this  AJ        .  AJ C       M        �  = M        t  = M        %  &, M        �  
 >�   this  AI  
     @  M        �  0	
 N N N N N                       @� & h   �  �  t  %  �  �  �  �   0   �:  Othis  9.       �   9@       �   O �   0           N   �     $       ? �   @ �H   A �,   9   0   9  
 �   9   �   9  
 �   9   �   9  
 
  9     9  
 �  9   �  9  
 �  9   �  9  
 �  9   �  9  
 @SH冹 H嫏�   H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ^ G            N      H   V        �std::_Ref_count_obj2<donut::chunk::MeshletSet>::_Destroy 
 >�:   this  AJ        .  AJ C       M        �  = M        t  = M        %  &, M        �  
 >�   this  AI  
     @  M        �  0	
 N N N N N                       @� & h   �  �  t  %  �  �  �  �   0   �:  Othis  9.       �   9@       �   O  �   0           N   �     $       ? �   @ �H   A �,   ?   0   ?  
 �   ?   �   ?  
 �   ?   �   ?  
   ?     ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 3烂   �   �   H G                      �        �std::_Ref_count_base::_Get_deleter 
 >�   this  AJ          D    >�   __formal  AK          D                           @�     �  Othis     �  O__formal  O�   0              �     $       � �    � �   � �,      0     
 m      q     
 �      �     
 �           
 H冹HH峀$ �    H�    H峀$ �    �
         �      T      �   �   F G                       �        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               `            J �   K �,      0     
 �   _   �   _  
 �      �     
 H冹(H�
    �    �   X            �   �   g G                     �        坰td::vector<char const *,std::allocator<char const *> >::_Xlength 
 Z   �   (                      @        $LN3  O �   (                          a �   b �,   %   0   %  
 �   l   �   l  
 �   %   �   %  
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8       =         �   R  N G            B      B   #        �std::allocator<char const *>::deallocate 
 >�9   this  AJ          AJ 0       D0   
 >j8   _Ptr  AK          >#   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        �  
#

 Z      >#    _Ptr_container  AJ       %    AJ 0       >#    _Back_shift  AH          AH 0       N N (                      H  h   �  �         $LN18  0   �9  Othis  8   j8  O_Ptr  @   #  O_Count  O  �   8           B        ,       � �   � �3   � �7   � �,   $   0   $  
 s   $   w   $  
 �   $   �   $  
 �   $   �   $  
 �   $   �   $  
   $     $  
 -  $   1  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
 �  $   �  $  
   j     j  
 h  $   l  $  
 H塡$L塂$H塗$UVWATAUAVAWH峫$貶侅�   I嬂L嬯L嬦E3�W荔E�W审M螸塽遞M桯婻H呉tD婤吚tD  岺�盝t吚u騂媇焒oM桯婨w�*I婨 H塃桰媇H塢焒oM桯婨w�fs�fH~胒諱椏����H儅� 劮  W纅E鱄呟t
fM黟�CH媇烲嬂H峌鱄峂�    L�8H媝L�0L塸L墋縃塽荓媢M咑t/嬊餉罠凐uI�I嬑�嬊餉罠凐u	I�I嬑�PH媇烢3鯩�凜  W荔E塽稟峃�    H塃岺H塎�3襀�H塎疞岴Ш  H婱胯    H婨疕婱+罤柳H凐勠   H婾wH�
    �    M�4$M塼$H婱吷tIH婨稨+罤柳H��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    W荔E塽稨呟t/嬊�罜凐u#H媇烪�H嬎�嬊�罜凐uH婱烪��P怘咑凙  嬊�罠凐�1  H�H嬑�嬊�罠凐�  H�H嬑�
  H�H呉�0  D婤A侙  tA�  �H�
    �    �  D婤A侙   tA�   �H�
    �    猷  H儂 勄  L媟 M咑労  E媬A嬿H伶I鯥兤M�t6L岴A嬜H峂翔    L岶3襀媢�@ I�I菻�諬�翸峷I;譺觌H媢螸岴Ш   H婱胯    H婨疞婨+繦柳H凐刌  H婾wH�
    �    E3鯩�4$M塼$H婱吷tIH婨稨+罤柳H��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘝  �    W荔E塽稨呟t2嬊�罜凐u"H媇烪�H嬎�嬊�罜凐u
H婱烪��PH媢螲咑t@H婨逪+艸柳H��    H嬈H侜   rH兟'H媣鳫+艸兝鳫凐囇  H嬑�    H婱荋吷t/嬊�罙凐u#H媇荋�H嬎�嬊�罜凐uH婱荋��P怚婱H吷t�羪�uH��PI嬆H嫓$�   H伳�   A_A^A]A\_^]肕� H峌鏗峂胯    H婱鏗吷�?  H呟t�CH媇烪婨桯墎�   H嫳�   H墮�   H咑t-嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH媇�3鯤婨鏘�$H婨颕塂$H婱吷tIH婨稨+罤柳H��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐噵  �    W荔E塽稨呟t/嬊�罜凐u#H媇烪�H嬎�嬊�罜凐uH婱烪��P怘婱螲吷剉��H婨逪+罤柳H��    H嬃H侜   侼��H兟'H婭鳫+罤兝鳫凐�  �0��H婱颒吷t2嬊�罙凐u"H媇颒�H嬎�嬊�罜凐u
H婱颒��PH媇烪婱吷t=H婨稨+罤柳H��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐噟  �    E3鲩�   �H�
    �    H婱�4$M塼$H吷tIH婨稨+罤柳H��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    W荔E塽稨呟t/嬊�罜凐u#H媇烪�H嬎�嬊�罜凐uH婱烪��P怘咑�>��嬊�罠凐�.��H�H嬑�嬊�罠凐���H�H嬑���H嬓H�
    �    怘呟t.嬊�罜凐u"H媇烪�H嬎�嬊�罜凐u
H婱烪��PM�4$M塼$H婱螲吷剒��H婨逪+罤柳H��    H嬃H侜   俁��H兟'H婭鳫+罤兝鳫凐w�8���    愯    惕    梯      L  �    s     �  O   �     �      �  [   �     �  ^   �     �  D   :     ^  R   c     �      =      �  #   z      �      �  a   �     �      t  U   y                       �   '!  ? G              &     �        �donut::chunk::deserialize  >s,   iblob  AK        ,  AU  ,     �~  D�    >@   assetpath  AH  )     T. $ T  � � AP        )  D    >8   reader  CI     Z      CJ     E    0    CI    y    $  CJ    u        DH   
 >�+   blob  A�   w     !    A�  �     �: �. CI     r     9  & 
 5 i����E\�h " CJ     (    � ����n	z 2 CI    �     oi 3 � � � ~ l �  D     >�+   chunks  CP      F    m! I D0   
 >�8   mset  CJ      �    LF  CI     %      CJ         3    CJ     !      CI    C      CJ    C      Dp    M        �  	= M        C  	= M        c  	= N N N M        �  5 M          �5 N N M          F*! M        K  K(  M        �  
T)( >     _Count  A   W       A  n     
  N N M        �  F M        v  �F N N N M          4�� M        �  ��+	 M        �  � -
 >�   this  AV  �     7  M        �  �	
 N N N N M        �  �� M          �� M        $  �� M        �  ��
 >�    _Tmp  AL  �     ��� AL <    �r ��  N M        O  �� N N N M          �� M        K  ��#D N N N M        ;  �� M        �  ��% M        �  �� N N M        v  ��� N N M        �  �>	 M        >  丟	 M        \  丟 M        k  丟) >�+    _Newvec  AH  P    '  M          	丟 M        @  	丟 M        0  	丟 M        �  	丟
 Z   �   N N N N N N M        �  乗 M        �  乗 N N N M        =  	�> M        [  	�> N N N M        �  亀 N M          7�/ M        �  �/.	 M        �  �8 M        �  侾 N N N N M        t  3侞 M        %  侞. M        �  � , M        �  �
 N N N N M        �  F仼	I! M          仼=	@ M        �  2伣 >#   _Count  AH  �        M        �  伻)
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      AJ �    �  S + >#    _Bytes  AK  �    *  AK       M        �  佈d >#    _Ptr_container  AH  �      AJ  �      N N N N N M        �  	仩 M        �  �仩 N N1 M        �  俷mDN
:
僶 >L   chunk  AK  n    )  D  � / � i AK ,    '	 D���6 �  >#     tableSize  AL  �      AV  �    ? #   AV        Z � � �t
 >.    data  AV  �      AV u    �3 ���  >@    stringsData  AP      "  AP ,      >#     nstrings  AW  �    1]t �u AW <    �j �� 
 >#    i  AK        AK ,    	  C             C          
 . M        ,  俷iME-円 Z   �  �  �   N M        �  傢9 M        B   傢4
 Z   �   N N N M          7�2 M        �  �2.	 M        �  �; M        �  嘢 N N N N M        t  3嘅 M        %  嘅. M        �  �, M        �  �
 N N N N M        �  B啺	E! M          啺=@ M        �  2喞 >#   _Count  AH  �        M        �  喫)
 Z   �  
 >   _Ptr  AH  �     & AJ      L � 
 :
 ~+ �i	  AH �      AJ ,    %
 @Q��  >#    _Bytes  AK  �    *  AK       M        �  喸d >#    _Ptr_container  AH  �      AJ  �      N N N N N M        �  
啠 M        �  �啠 N N M        �  �> N M        �  @凕 M          凕; M        #  0� >#   _Count  AH          M        �  �)
 >   _Ptr  AH        AL      �  � �  AH 9      AL <    �r ��  >#    _Bytes  AK      (  AK <    � � M        �  � d >#    _Ptr_container  AH  +      AL  (      N N N N N M        t  3兣 M        %  兣. M        �  兪, M        �  冣
 N N N N M        �  F僺	I! M          僺=	@ M        �  2儑 >#   _Count  AH  �        M        �  儝)
 Z   �  
 >   _Ptr  AH  �      AJ  w      AH �      AJ �    J  w  � � >#    _Bytes  AK  �    *  AK       M        �  儧d >#    _Ptr_container  AH  �      AJ  �      N N N N N M        �  僩 M        �  �僩 N N M        �  7� M          �.	 M        �  �, M        �  �-
 N N N N M        �  F咷伣 M          咷=	伌 M        �  2哰仼 >#   _Count  AH  T         M        �  唂)亅
 Z   �  
 >   _Ptr  AH  f      AJ  K      AH �      AJ �    1 �   >#    _Bytes  AK  c    *  AK       M        �  唎d亰
 Z      >#    _Ptr_container  AH  z      AJ  w      N N N N N M        �  I吘侺 M          吘<
�? M        #  1呏�4 >#   _Count  AH  �        M        �  呩
�
 >   _Ptr  AH  �      AJ  �      AH <      AJ A      >#    _Bytes  AK  �    .  AK <    � � M        �  咁d�
 Z      >#    _Ptr_container  AH  �      AJ  �      N N N N N M        t  3厞 M        %  厞. M        �  厪, M        �  収
 N N N N M        �  F�8	I! M          �8=	@ M        �  2匧 >#   _Count  AH  E        M        �  匴)
 Z   �  
 >   _Ptr  AH  W      AJ  <      AH y      AJ �    8    >#    _Bytes  AK  T    *  AK       M        �  卄d >#    _Ptr_container  AH  k      AJ  h      N N N N N M        <  �! M        �  �'	 N M        �  �
�! N N M        s  T勍 M        t  +匂 M        %  匂) M        �  匄, M        �  �	 N N N N M        !  勞 M          勞 M        �  勫
 N M        �  勞 N N N M        #  
勍 M        �  
勍 M        �  勍 M        �  勔 N N N N N M        �  E嚭V M          嚭8
I M        #  -囈> >#   _Count  AH  �        M        �  囕
&
 >   _Ptr  AH  �      AJ  �      AH <      AJ A      >#    _Bytes  AK  �    ; *   AK <      M        �  囮d

 Z      >#    _Ptr_container  AH  �      AJ  �      N N N N N M        �  	嚤 M        �  �嚤 N N M        t  3噡 M        %  噡. M        �  噧, M        �  嚊
 N N N N& Z       �    �  �  �  �   �           8         @ �hu   �  �  �  �  �  �  �  �  �  �  �          q  s  t  v  �  �  �  !  #  $  %  &    K  L  �  �  �  �  �  �  �  �  �      0  2  =  >  ?  @  c  n  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �                "  #  $  +  ,  .  ;  <  =  >  ?  B  C  D  J  K  O  T  [  \  c  d  e  k  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN726  �   s,  Oiblob     @  Oassetpath  H   8  Oreader      �+  Oblob  0   �+  Ochunks  p   �8  Omset  9      �   9(      �   9      �   9+      �   9N      �   9�      �   9�      �   9`      �   9u      �   9�      �   9
      �   9      �   9�      �   9�      �   9+      �   9@      �   9      �   9.      �   9Q      �   9�      �   9�      �   O �                    �       � �5   � �F   � ��   � �>  � �e  � �w  � ��  � ��  � �k  � �,  � �>  � �W  � �g  � �<  � ��  � ��  � ��  � ��  � �!  � �  � ��  � ��  � ��  � �n  � �~  � ��  � �  � �
  � �  � ��   N  N F                                �`donut::chunk::deserialize'::`1'::dtor$0  >s,   iblob  EN  �           >@   assetpath  EN             >8    reader  EN  H          
 >�+    blob  EN              >�+    chunks  EN  0          
 >�8    mset  EN  p                                  �  O  �   N  N F                                �`donut::chunk::deserialize'::`1'::dtor$1  >s,   iblob  EN  �           >@   assetpath  EN             >8    reader  EN  H          
 >�+    blob  EN              >�+    chunks  EN  0          
 >�8    mset  EN  p                                  �  O  �   N  N F                                �`donut::chunk::deserialize'::`1'::dtor$2  >s,   iblob  EN  �           >@   assetpath  EN             >8    reader  EN  H          
 >�+    blob  EN              >�+    chunks  EN  0          
 >�8    mset  EN  p                                  �  O  �   N  N F                                �`donut::chunk::deserialize'::`1'::dtor$5  >s,   iblob  EN  �           >@   assetpath  EN             >8    reader  EN  H          
 >�+    blob  EN              >�+    chunks  EN  0          
 >�8    mset  EN  p                                  �  O  ,      0     
 e      i     
 u      y     
 �      �     
 �      �     
 �      �     
          
          
 .     2    
 Y     ]    
 m     q    
 �     �    
 �     �    
 �     �    
 &     *    
 U     Y    
 m     q    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 |     �    
 !     %    
 5     9    
 �     �    
 �     	    
 H	     L	    
 X	     \	    
 h	     l	    
 x	     |	    
 �	     �	    
 �	     �	    
 �	     �	    
 
     

    
 �
     �
    
 �
     �
    
 �
     �
    
          
           
 K     O    
 [     _    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
           
 0     4    
 )     -    
 t     x    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 >     B    
 N     R    
          
 Y     ]    
 i     m    
 �     �    
 �     �    
 �     �    
 �     �    
          
 '     +    
 $     (    
 o     s    
      �    
 �     �    
 �     �    
 �     �    
 �     �    
 !     %    
 1     5    
 e     i    
 �     �    
 �     �    
 �     �    
 �     �    
          
      #    
 t     x    
 �     �    
          
 L     P    
 \     `    
 l     p    
 |     �    
 �     �    
 �     �    
      
    
          
          
 ^     b    
 n     r    
 ~     �    
 �     �    
 �     �    
 �     �    
          
          
 ]     a    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 V     Z    
 f     j    
 U  b   Y  b  
 �     �    
 �     �    
            
            
 #      '     
 3      7     
 C      G     
 S      W     
 c      g     
 s      w     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 !     !    
 !     !    
 #!     '!    
 <!     @!    
 l"  J   p"  J  
 �"  J   �"  J  
 �"  J   �"  J  
 #  J   	#  J  
 (#  J   ,#  J  
 M#  J   Q#  J  
 p#  J   t#  J  
 �#  N   �#  N  
 $  N   $  N  
 8$  N   <$  N  
 ]$  N   a$  N  
 �$  N   �$  N  
 �$  N   �$  N  
 �$  N   �$  N  
 %  P    %  P  
 h%  P   l%  P  
 �%  P   �%  P  
 �%  P   �%  P  
 �%  P   �%  P  
 �%  P   &  P  
  &  P   $&  P  
 t&  R   x&  R  
 �&  R   �&  R  
 �&  R   �&  R  
 
'  R   '  R  
 0'  R   4'  R  
 U'  R   Y'  R  
 x'  R   |'  R  
 H媻�   �       2   H崐H   �       &   H崐    �          H崐0   �       3   H塡$H塼$L塂$WH冹 I孁H嬹凓�uSI媥H�勦  ����嬅�罣凐吽  H�H嬒��羅凔叺  H�H嬒�P2繦媆$0H媡$8H兡 _肏�	�    H嬝H吚刋  D婡A侙  tA�  �H�
    �    隒D婡A侙   tA�   �H�
    �    � H儀 tH儀  t���H�
    �    2�3蓜繦E薍吷勲  H婹 �
冡t&凒tH�H儂vH�H�
    橹  H��9�H��9吶  婤堿DL峑�
冡劎   凒卝  E3蒆�D9HDvuH峍fD  A嬃Hk�8N�I凐�tL�H婤I+翲柳L;纒K�码3繨�N婦I凐�tL�H婤I+翲柳L;纒
K�码H峍3繨塂A�罤�D;HDr旽婳H吷t�AH媤L墭�   闅   E3蒆�D9HDvvH峃�     A嬔H菱I覮�I凐�tL�H婣I+翲柳L;纒K�码3繦�L婤I凐�tL�H婣I+翲柳L;纒
K�码H峃3繦塀A�罤�D;HDr朒婳H吷t�AH媤L墭�   H咑t-����嬅�罠凐uH�H嬑��羄凔u
H�H嬑�P怘嬒�    �H媆$0H媡$8H兡 _肏�
    �    H�H儂vH�H�
    �    怘嬒�    2繦媆$0H媡$8H兡 _胿      �   [   �      �   ^   �      �   a   �           �  (   �  g   �     �     �       (      �   �
  Y G                   �        �donut::chunk::ChunkReader::loadMeshInfosChunk_0x100 
 >�7   this  AJ          AL       �R  � qS  AL �      >+   chunkId  A         z I )  A       
 >�8   mset  AM       � O �  AP          AM       D@    >L   chunk  AJ  �       AJ �      C       �       >.    chunkData  AK      
  g �  AK �    1  8 g �  � 8  >.    minfosData  AS  F    j AS �      M           N M        �  A M          4
 M        �  ,
 >�   this  AM  #     N  AM       M        �  I	 N N N N M        -  rp俋 Z     �   >N+    chunk  AI  }     �B  AI �    O  9 , M        t  ��MB-B
 Z   �  �  �   N N M        �  � M        *  � N N M        �  
� M        ^  �
 >2/   this  AK      
  AK     � � >@    _Result  AK     � � N N M        �  丗 M        *  丗 N N M        /  倆
 >T7    _Ptr  AH  t      AH     �   . < � &  M        u  倆 M        �  倆
 M        b  倆	 M        �  們 N N N N N$ M        �  	侙#1'
 >u     i  A   �      Ai  �      s  A      �   . < � &  Ai     � a 	 �   M        �  侲
 >#    index  AP  E    5  AP     � 
 ] �   M        �  侹 N N M        �  � >#    index  AP      (  M        �  �# N N N M        1  佦
 >l7    _Ptr  AH  �      AH p    \ h � � .&  M        v  佦 M        �  佦
 M        b  佦	 M        �  佷 N N N N N$ M        �  	乑$0(
 >u     i  A   `      Ai  ]    ~  r  A  p    \ h � � .&  Ai p    \b 	 � � @  M        �  仴
 >#    index  AP  �    6  AP p    \ ` � � @  M        �  伀 N N M        �  亄 >#    index  AP  {    *  M        �  亖 N N N M        �  
傟 M        ^  傢
 >2/   this  AK  �    
  AK �      >@    _Result  AK �      N N Z   �  �  �                        @ � h'   �  �  �    �  ]  ^  �  �  �  �  �  �  �  �  �  �  �  �          *  +  -  .  /  0  1  2  Q  S  b  t  u  v  �  �   0   �7  Othis  8   +  OchunkId  @   �8  Omset  �7  Desc k �8  donut::chunk::ChunkReader::loadMeshInfosChunk_0x100::__l7::<lambda_b633493344e122617b3432c165b58408>  9G       �   9]       �   9�      �   9�      �   O   �   �                  �       ]  �   `  �   a  �`   �  �r   e  ��   g  �  k  �  v  �#  w  �(  r  �.  t  �0  n  �6  z  �B  |  �F  �  �Z  �  ��  �  ��  �  ��  �  �z  �  ��  �  ��  �  ��  e  ��  �  ��  �  ��   �   h F                                �`donut::chunk::ChunkReader::loadMeshInfosChunk_0x100'::`1'::dtor$0 
 >�8   mset  EN  @                                  �  O ,       0      
 ~       �      
 �       �      
 �       �      
 �       �      
 �       �      
 �            
            
 '      +     
 O      S     
 _      c     
 s      w     
 �      �     
 �      �     
 �      �     
 �      �     
 u      y     
 �      �     
 �           
            
 �      �     
 �      �     
            
 �      �     
 �      �     
 _      c     
 o      s     
 �      �     
 �      �     
 �      �     
 �      �     
 Z      ^     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
            
       "     
 �      �     
 �      �     
 	      	     
 )	      -	     
 �
      �
     
 �
      �
     
 �
      �
     
 �
      �
     
            
   K      K  
 �  K   �  K  
 H媻@   �       (   H塡$H塼$L塂$WH冹 I孁H嬹凓�uSI媥H�劄  ����嬅�罣凐厜  H�H嬒��羅凔卻  H�H嬒�P2繦媆$0H媡$8H兡 _肏�	�    H嬝H吚�  D婡A侙  tA�  �H�
    �    隒D婡A侙   tA�   �H�
    �    � H儀 tH儀  t���H�
    �    2�3蓜繦E薍吷劑   H婣 D�L峏E呉t8I嬎E嬄H�H凓�tL婲H婩I+罤柳H;衧I�央3繦�H兞hI冭u蜨�L塜HH�D塒PH�H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�P�H媆$0H媡$8H兡 _肏�
    �    H�H儂vH�H�
    �    怘嬒�    2繦媆$0H媡$8H兡 _胿      �   [   �      �   ^   �      �   a   �      �  g   �     �     �     �  (      �     ] G            �     �  �        �donut::chunk::ChunkReader::loadMeshInstancesChunk_0x100 
 >�7   this  AJ          AL       �R  |  >+   chunkId  A         z I )  A  �     
 >�8   mset  AM       � O BC  AP          AM �      D@    >L   chunk  AJ  �       AJ G    u .  U   C       �       >u     ninstances  Aj      q  Aj �      >.    chunkData  AH        AH     5   % 
  >D9    instancesData  AS  
    m  AS �      M           N M        �  A M          4
 M        �  ,
 >�   this  AM  #     N  AM �      M        �  I	 N N N N M        3  rp� Z     �   >N+    chunk  AI  }     M� 9  AI �    G  9 , M        w  ��MB-B
 Z   �  �  �   N N M        �  5乁 M          乁,	 M        �  乛
 >�   this  AM  Y    B  M        �  亀	
 N N N N M        �  � >#    index  AK      /  AK     �  / b   M        �  � N N M        �  
仺 M        ^  伀
 >2/   this  AK  �    
  AK �      >@    _Result  AK �      N N Z   �  �                        @ J h   �  �    �  ]  ^  �  �  �  �  �  �    +  .  3  w   0   �7  Othis  8   +  OchunkId  @   �8  Omset  �7  Desc  9G       �   9]       �   9u      �   9�      �   O  �   �           �       �       �  �   �  �   �  �`   �  �r   �  ��   �  �  �  �  �  �
  �  �  �  �=  �  �G  �  �N  �  �U  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��   �   l F                                �`donut::chunk::ChunkReader::loadMeshInstancesChunk_0x100'::`1'::dtor$0 
 >�8   mset  EN  @                                  �  O ,   !   0   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
 �   !   �   !  
   !     !  
   !     !  
 ?  !   C  !  
 O  !   S  !  
 k  !   o  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
 �  !   �  !  
   !     !  
   !     !  
 �  !   �  !  
 �  !   �  !  
   !   !  !  
 1  !   5  !  
 �  !   �  !  
 T  !   X  !  
 d  !   h  !  
 �  !   �  !  
 �  !   �  !  
   !     !  
 �  !   �  !  
 �  !   �  !  
   !     !  
   !     !  
 ,  !   0  !  
   L     L  
 m  L   q  L  
 H媻@   �       (   H塡$H塼$L塂$WH冹 I嬸H孂凓�uH�
    镚  H�	�    H嬝H吚�,  D婡A侙  tA�  �H�
    �    隒D婡A侙   tA�   �H�
    �    � H儀 tH儀  t���H�
    �    2繣3蓜繪E薓吷劸   M婭 M峐3褹9vB�     嬄Li罉   K�H凒�tL媁H婫I+翲柳H;萻I�孰3繩��翧;r艸�L塜XA�	H�塇`A婭H�塇dH媬H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�P�H媆$0H媡$8H兡 _肏�
    �    H�H儂vH�H�
    �    怘媬H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�P2繦媆$0H媡$8H兡 _�"   d   /      W   [   \      z   ^         �   a   �      n  g   s     �     �        �   �  Y G            �     �  �        �donut::chunk::ChunkReader::loadMeshNodesChunk_0x100 
 >�7   this  AJ          AM       {C  >+   chunkId  A         3  A  r     
 >�8   mset  AL       �N  AP          D@    >L   chunk  AQ  �     
  AQ w      Ci      �       >N9    nodesData  AS  �     �  AS Y      >.    chunkData  AQ  �     �  AQ Y     
 >u     i  A   �     �  A  Y     # M        4  E'Eq�,
 Z      >N+    chunk  AI  6     <� 9  AI Y    t   F '  M           N+ M        x  ?MB-B
 Z   �  �  �   N N M        �  5�$ M          �$,	 M        �  �-
 >�   this  AM  (    B  M        �  丗	
 N N N N M        �  �� >#    index  AJ  �     -  AJ �     D 
 -  M        �  �� N N M        �  5亼 M          亼,	 M        �  仛
 >�   this  AM  �    B  M        �  伋	
 N N N N M        �  乺
 Z   �   M        ^  亃
 >2/   this  AK  z    
  AK �      >@    _Result  AK �      N N
 Z   �                        @ J h   �  �    �  ]  ^  �  �  �  �  �  �    +  .  4  x   0   �7  Othis  8   +  OchunkId  @   �8  Omset  �7  Desc  9D      �   9V      �   9�      �   9�      �   O  �   �           �       �       �  �   �  ��   �  ��   �  ��   �  ��   �  �  �  �
  �  �  �  �  �  �$  �  �[  �  �k  �  �r  �  ��  �  ��  �  ��   �   h F                                �`donut::chunk::ChunkReader::loadMeshNodesChunk_0x100'::`1'::dtor$0 
 >�8   mset  EN  @                                  �  O ,   "   0   "  
 ~   "   �   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
 �   "   �   "  
   "   #  "  
 /  "   3  "  
 C  "   G  "  
 g  "   k  "  
 w  "   {  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 (  "   ,  "  
 <  "   @  "  
   "     "  
 y  "   }  "  
 �  "   �  "  
   "     "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 �  M   �  M  
   M     M  
 H媻@   �       (   H塡$H塼$H墊$ UATAUAVAWH峫$蒆侅�   I嬂L嬺L嬮3�M吚凟  E婡A侙   tA�   �H�
    �    �  D婡A侙   tA�   �H�
    �    轾  H9x勧  L媥 M�勗  W纅D$ E�'A冧劒   A凕tA嬙H�
    �    I�>I墌椴  灌   �    H嬝H塃gH吚tdW� 茾   茾   H�    H�H岾AA A0A@APA`Ap亐   3褹感   �    H峴�   閴   H嬤H峴�   雤垢   �    H嬝H塃gH吚tWW� 茾   茾   H�    H�H岾AA A0A@APA`Ap亐   3褹辅   �    �H嬤H峴�   H塡$(H塼$ I婳H凒�tI婾I婨H+翲柳H;萻H�孰H嬊H塅AOd駻GtNh�FxH�    H塃噁荅�艵�H墋桯荅�   H墋�E�E�M�M框EE螸岴疉媁I嬐�    劺劰  H婨螲塅婨繅F@H�
    H塎噁荅�艵�H塃桯荅�   H墋�E�E�M�M框EE螸岴疉媁I嬐�    劺tH婨螲塅0H�    H塃噁荅�艵�婩@H塃桯荅�   H墋�E�E�M�M框EE螸岴疉媁 I嬐�    劺tH婨螲塅8H�    H塃噁荅�艵�婩@H塃桯荅�   H墋�E�E�M�M框EE螸岴疉媁$I嬐�    劺tH婨螲塅H�    H塃噁荅�艵�婩@H塃桯荅�   H墋�E�E�M�M框EE螸岴疉媁(I嬐�    劺tH婨螲塅 H�    H塃噁荅�艵�婩@H塃桯荅�   H墋�E�E�M�M框EE螸岴疉媁,I嬐�    劺tH婨螲塅(����E呬�5  H呟t�CH媆$(H媡$ (D$ fEE3銵墻�   H�    H塃噁荅�艵�L塭桯荅�   L塭�E�E�M�M框EE螸岴疉媁0I嬐�    劺tbH婨螲墕�   婨繅啒   H呟刜  嬊�罜凐�4  H媆$(H�H嬎�嬊�罜凐�  H媆$(H�H嬎�PH媡$ �  M�&M塮����H呟t:嬊�罜凐u)H媆$(H�H嬎�嬊�罜凐uH媆$(H�H嬎�P�H媆$(H呟勷  棣  A凕叴  H呟t�CH媆$(H媡$ (D$ fE'E3銵墻�   H�    H塃噁荅�艵�L塭桯荅�   L塭�E�E�M�M框EE螸岴疉媁0I嬐�    劺�*  H婨螲墕�   婨繅啝   H�    H塃噁荅�艵�L塭桯荅�   L塭�E�E�M�M框EE螸岴疉媁4I嬐�    劺効   H婨螲墕�   婨繅啺   H�    H塃噁荅�艵�L塭桳塭烲塭�E�E�M�M框EE螸岴疉媁8I嬐�    劺t\H婨螲墕�   婨繅喞   H婨荋凌垎�   H呟tD嬊�罜凐uH媆$(H�H嬎�嬊�罜凐勯��H媆$(H媡$ �M�&M塮轲��E3�W纅E鏗呟t�CH媆$(H媡$ (D$ fE鏛岴鏏媁XI嬐�    劺uM�&M塮H呟勧   闂   W纅E鱄呟t�CH媆$(H媡$ (D$ fE鱈岴鰽媁\I嬐�    劺t睞�`�t=W纅EH呟t�CH媆$(H媡$ (D$ fEL岴A媁`I嬐�    劺刵���I�6I塣隫I�>I墌H呟tJ����嬊�罜凐u9H媆$(H�H嬎��羬�u"H婰$(H��P��H�
    �    I�>I墌I嬈L崪$�   I媅8I媠@I媨HI嬨A_A^A]A\]肞   [   U      v   ^   {      �   .   �      �   �    �   m   9  W   a  �    �  j   �  W   !  1   i     �  4   �     �  7   ,     ?  :   �     �  =   �     �  @   F     �  C   �     �  F        8  I   �     �  L   �     �      �  !     "   t  a   y        �   �
  W G            �  $   �  �        �donut::chunk::ChunkReader::loadMeshSetChunk_0x100 
 >�7   this  AJ        -  AU  -     w >L   chunk " AH  '     V2  X  �  �  >
 AP        '  AH }    
 
 >[9    desc  AW  �     � AW o    1 
 >�8   mset " CL      A    
   t � \�	6 CI     r    � � � " + Dq� �% I Q; �M  CJ     g     * CL     w     �  " � � � � > CI    w    � 0 � $ " � � � � G � � �4  D     >�7   handle  C      �    R  DX    >�7    stype  Al  �     �����  Al ?    g 0   >9    set  D�    >=9    set  D�   - M        6  /iME-囯 Z   �  �  �   N M        �  �� M        *  �� N N M        �  �� M        �  ��� N N M        �  坿 M        �  �坿 N N M        �  �� M        �  ��� N N" M        9  ��R_
 Z   �   >�:    _Rx  AI  �     | 6 AI �    �� ;0 �$ � R o � � ) {4  B  �     �x z �  M        ~  ��7 M        �  	�� N M        �  3�
 M        �  3�
 M        �  &�
 N N N N N M        7  乕W
 Z   �   >�:    _Rx  AI  h    m 6 AI �    �� ;0 �$ � R o � � ) {4  B  l    <  M        z  乼7 M        �  	亀 N M        �  3亾 M        �  3亾 M        �  &亾 N N N N N M        �  併 >#    index  AJ  �    �  M        �  侀 N N M        �  
�5 M          �5 M        �  �: N N N M        �  �. M        �  ��. N N M        /  刣 M        u  刣 M        �  刣
 M        b  刣 M        �  刬 N N N N N M        �  3匁 M          匁- M        �  匊
 M        �  � N N N N M        �  匑 M        �  �匑 N N M        1  叄 M        v  叄 M        �  叄
 M        b  叄 M        �  叏 N N N N N M        �  +� M          �) M        �  �,
 M        �  �/ N N N N M        �  �(� M        A  嘼
 M        b  嘼 M        �  噂 N N N M        �  ��(� N N M        �  嚝 M        A  嚢
 M        b  嚢 M        �  嚨 N N N M        �  �嚝 N N M          囜 N M        �  囯 M        A  囸
 M        b  囸 M        �  圂 N N N M        �  �囯 N N M        �  �% M        @  �% N N> Z   �  �  �  �  �  �  �  �  �  �  �  �  �  �   �           (         @ &hH   �  �  �  �  �    v  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �          *  +  .  /  0  1  2  6  7  8  9  :  ?  @  A  P  Q  R  S  a  b  l  p  q  u  v  z  {  |  }  ~    �  �  �  �  �  �  �  �  �  �     �7  Othis     L  Ochunk  �7  Desc      �8  Omset  X   �7  Ohandle  �   9  Oset  �   =9  Oset  ^�      �:   ^`     �:   9      �   93      �   9h      �   9�      �   9-      �   9V      �   9j      �   O   �   �          �    M   t       �/    ��    ��   # ��   % ��   2 ��   3 ��   - �A  . �G  / �L  - �S  . �Y  / �[  ( ��  ) ��  6 �
  7 �  ; �]  < �u  > �}  ? ��  C ��  D ��  E ��  G �   H �4  I �<  K �~  L ��  M ��  O ��  P ��  Q ��  S �:  T �N  U �V  W �d  Y ��  [ ��  ] ��  ^ ��  ` ��  a ��  e �(  � �@  c �G  A ��  f ��  h ��  j ��  l �	  m �!  o �,  p �5  t �t  u ��  w ��  x ��  | ��  } ��   ��  � �  � �  � �?  � �K  A �W  f �]  � ��  A ��  � ��  � ��  � �%  � �.  A �o   �}   ��  � ��     f F                                �`donut::chunk::ChunkReader::loadMeshSetChunk_0x100'::`1'::dtor$1 
 >�8    mset  EN              >�7    handle  EN  X           >9    set  EN  �           >=9    set  EN  �                                  �  O  �     f F                                �`donut::chunk::ChunkReader::loadMeshSetChunk_0x100'::`1'::dtor$4 
 >�8    mset  EN              >�7    handle  EN  X           >9    set  EN  �           >=9    set  EN  �                                  �  O  �     f F                                �`donut::chunk::ChunkReader::loadMeshSetChunk_0x100'::`1'::dtor$5 
 >�8    mset  EN              >�7    handle  EN  X           >9    set  EN  �           >=9    set  EN  �                                  �  O  ,   #   0   #  
 |   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #     #  
   #     #  
 2  #   6  #  
 V  #   Z  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 '  #   +  #  
 O  #   S  #  
 k  #   o  #  
   #   	  #  
   #     #  
 M  #   Q  #  
 $  #   (  #  
 4  #   8  #  
 l  #   p  #  
 6  #   :  #  
 %
  #   )
  #  
 5
  #   9
  #  
 E
  #   I
  #  
 U
  #   Y
  #  
 e
  #   i
  #  
 u
  #   y
  #  
 �
  #   �
  #  
 �
  #   �
  #  
 �
  #   �
  #  
 �
  #   �
  #  
 h  O   l  O  
 �  O   �  O  
 �  O   �  O  
   O     O  
 4  O   8  O  
 �  Q   �  Q  
 �  Q   �  Q  
   Q     Q  
 2  Q   6  Q  
 T  Q   X  Q  
 �  S   �  S  
   S     S  
 0  S   4  S  
 R  S   V  S  
 t  S   x  S  
 H崐    �       (   H崐�   �       +   H崐�   �       -   H塡$H塴$VH冹 I嬭嬟H嬹凓�u2繦媆$8H媗$@H兡 ^肏�	H墊$0�    H孁H吚凴  D婡A侙   t�H�
    A�   �    隒D婡A侙   t�H�
    A�   �    � H儀 tH儀  t���H�
    �    2�3覄缷蔋E螲吷勩   H婭 H塙H塙 �$:Et L�I儀vM� 嬘H�
    �    槠   H�堵黎$:E	t L�I儀vM� 嬘H�
    �    闁   H陵��:U
tL�I儀vM� 嬘H�
    �    雖H婨H吚t#H9AtL�I儀vM� 嬘H�
    �    階H婣H塃H婣H塃H岮H塃 ��'H�
    �    H�H儂vH�H�
    �    2繦媩$0H媆$8H媗$@H兡 ^�7      Y   [   d      |   ^   �      �   a   �      �      �        "        F  %   K     r  (   w     �  g   �     �  +   �        �   �  V G            �     �  �        �donut::chunk::ChunkReader::loadStreamChunk_0x100 
 >�7   this  AJ          AL       �   >+   chunkId  A           A        �   >�7   handle  AN       �   AP          >L   chunk  AJ  �     
  AJ �      C       �       >.    chunkData  AJ  �     � ( 
 X 
 �  �   AJ �      M           N  M        5  .r丷 Z     �   >N+    chunk  AM  >     �+ M        y  GMB-B
 Z   �  �  �   N N M        �  �� M        (  �� N N M        �  
�� M        ^  ��
 >2/   this  AP  �     
  AP �       >@    _Result  AP �       M        ]  �� N N N M        �  �� M        (  �� N N M        �  
� M        ^  �
 >2/   this  AP      
  AP       >@    _Result  AP       M        ]  � N N N M        �  �( M        (  �( N N M        �  
�4 M        ^  �7
 >2/   this  AP  7    
  AP A      >@    _Result  AP A      M        ]  �7 N N N M        �  
乣 M        ^  乧
 >2/   this  AP  c    
  AP m      >@    _Result  AP m      M        ]  乧 N N N M        �  
仴 M        ^  仺
 >2/   this  AK  �    
  AK �      >@    _Result  AK �      M        ]  仺 N N N Z   �  �  �  �  �                         @ : h
     �  ]  ^  �  �  �  �  �  (  +  5  y   0   �7  Othis  8   +  OchunkId  @   �7  Ohandle  �7  Desc  O�   �           �       �       �  �   �  �   �  �    �.   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �#  �  �(  �  �4  �  �O  �  �Q  �  �`   �{   �}   ��  
 ��   ��   ��  �  ��   ��   ��   �,      0     
 {           
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 *     .    
 >     B    
 b     f    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 e     i    
 u     y    
 �     �    
          
          
 :     >    
 �     �    
 �     �    
 �     �    
 �     �    
 @UH冹0H呉刏  D婤A侙  t�H�
    A�  �    2繦兡0]肈婤A侙   t�H�
    A�   �    2繦兡0]肏儂 勶   H媕 H呿勨   H婹H塼$PH峲H墊$X媫L塼$(D嬿L墊$ L媬I嬒I伶H+蔋六H;鵶H�MvOH婩H+翲柳H;鴙L岲$@H嬜H嬑�    �-H嬊H塡$HH+�3襂嬒H��    L嬅�    J�;H媆$HH塅L媩$ L峂M蜭岴L媡$(3襀�t$fff�     I�M岪H�I蒆�蠬�翲;譺鏗媩$X�H媡$PH兡0]脣H�
    �    2繦兡0]�!   [   ,      J   ^   U      �   D   �   W   `  a   e        �   l  \ G            q     k  �        �donut::chunk::ChunkReader::loadStringsTableChunk_0x100 
 >�7   this  AJ        d%  N  � �  AJ i      >L   chunk  AK        ]  G  } �  AK i      >#     tableSize  AV  �     x  >�8    table  AP        AP 0    +   
 >.    data  AN  p     �  AN [      >@    stringsData  AQ      I  >#     nstrings  AM  �     � 
 >#    i  AK  0    +  C              C      0    +   7 M        ,  &i

	�� Z   �  �  �   N M        �  y"9!>4 M        B  y"'%
b"+%!
 Z   �   >r8    _Al  AL  �     �  >#    _Oldsize  AJ  �     J   3   AJ     V .   >j8    _Newlast  AH  �       AH     K 5   >j8    _Oldlast  AW  �     o  >#    _Oldcapacity  AH  �         M        �  �� M        �  ��
 N N N N 0                     H N h   �  �  �  "  +  ,  B  J  d  �  �  �  �  �  �  �  �  �   @   �7  Othis  H   L  Ochunk  �7  Desc  O�   �           q       �       A  �   D  �0   E  �2   Y  �8   D  �Y   E  �[   Y  �a   D  �y   O  �}   D  ��   O  ��   D  ��   K  ��   D  ��   M  ��   D  ��   O  ��   M  ��   O  �  Q  �  S  �   U  �0  V  �I  X  �U  Y  �[  D  �i  E  �k  Y  �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
      !    
 -     1    
 P     T    
 `     d    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 "     &    
 2     6    
 Y     ]    
 �     �    
 �     �    
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,      0     
 _      c     
 �      �     
  d T 4 2p    H           X      X      �    20    2           Y      Y      �   
 
4 
2p    B           Z      Z      �    20    <           [      [      �   
 
4 
2p    B           \      \      �    20    <           ]      ]      �   
 
4 
2p    B           ^      ^      �    �                  `      `      �    20               a      a      �   ! t               a      a      �      E           a      a      �   !                 a      a      �   E   K           a      a      �   -& &4 & ����p`P             U       �                 c      c      �   (           �      �       �:    �2    @2    `   2   	   &            3   q%���!�9T c � :R��Ld�����/=U�� RP    }           d      d      �   ! t d
     }          d      d      �   }   �           d      d      �   !
 
� � }   �          d      d      �   �   �           d      d      �   ! 4	 �   �          d      d      �   �             d      d      �   !   �   �          d      d      �     0          d      d      �   !   }   �          d      d      �   0  [          d      d         !       }          d      d      �   [  q          d      d         6 6t T 4 2`    �          e      e          d 4 2p           U                       f      f         (                         �   (   �=	 B> 3 I ���� d 4 2p           U      ,       �          g      g      &   (           /      2       �   (   �� �> 3 I ew d 4 2p           U      >       �          h      h      8   (           A      D       �   (   \� �> 4F��$
 $t% $d$ $4# $ ����P              U   $   P       �          i      i      J   (           S      V   
    @2    j    A   (      +      -   � �R ����% |� i�Sl�1g��AX B      B           k      k      \    B                 m      m      b    20    Z           o      o      h   ! t     Z          o      o      h   Z   �           o      o      n   !       Z          o      o      h   �   �           o      o      t   fx B      �           p      p      }    20               q      q      �   ! t               q      q      �      E           q      q      �   !                 q      q      �   E   K           q      q      �   - B                 r      r      �   ! 4               r      r      �      g           r      r      �   !   4               r      r      �   g   �           r      r      �   !   4               r      r      �   �   �           r      r      �   !   4               r      r      �   �   �           r      r      �   !   4               r      r      �   �   �           r      r      �    20               s      s      �   ! t               s      s      �      E           s      s      �   !                 s      s      �   E   K           s      s      �   - 20               t      t      �   ! t               t      t      �      E           t      t      �   !                 t      t      �   E   K           t      t      �   - B                 u      u      �   ! 4               u      u      �      g           u      u      �   !   4               u      u      �   g   �           u      u      �   !   4               u      u      �   �   �           u      u      �   !   4               u      u      �   �   �           u      u         !   4               u      u      �   �   �           u      u      
    B                 v      v         ! 4               v      v            g           v      v         !   4               v      v         g   �           v      v         !   4               v      v         �   �           v      v      "   !   4               v      v         �   �           v      v      (   !   4               v      v         �   �           v      v      .    B                 w      w      4   ! 4               w      w      4      g           w      w      :   !   4               w      w      4   g   �           w      w      @   !   4               w      w      4   �   �           w      w      F   !   4               w      w      4   �   �           w      w      L   !   4               w      w      4   �   �           w      w      R    B      �           x      x      X    20    [           z      z      a    B      �           {      {      g    B      �           |      |      m    B      �           }      }      s    B      �           ~      ~      y    20                              ! t                                 H                       �   !                                H   N                       �   0 20    +           �      �      �    20               �      �      �   ! t               �      �      �      H           �      �      �   !                 �      �      �   H   N           �      �      �   0 20    +           �      �      �    2�
�p`0           U      �       ^          �      �      �   8               �      �   	   �            �   �       I   M r 
 
2P    (           I      I      �     20               �      �      �    20               �      �      �   
 
4 
2p    0           �      �      �    B      :           �      �      �                               p               Unknown exception                             |                                           �               bad array new length                                      �                                       	                         .?AVbad_array_new_length@std@@                    ����                                                .?AVbad_alloc@std@@                   ����                            
                   .?AVexception@std@@                    ����                               incorrect meshinfo type in asset '%s' bad MeshInfo chunk in asset '%s' bad MeshInstance chunk in asset '%s' bad MeshNode chunk in asset '%s' datastream chunk (%d) : bad type in asset '%s' datastream chunk (%d) : bad vertex vary in asset '%s' datastream chunk (%d) : bad semantic in asset '%s' datastream chunk (%d) : bad elemSize in asset '%s' Chunk deserialize : invalid ChunkId for stream in asset '%s' incorrect Set type (%d) Position TexCoord0 TexCoord1 Normal Tangent Bitangent Indices Indices32 Indices8 Meshlet Headers Chunk deserialize : invalid number of string table chunks in asset '%s' Chunk deserialize : invalid number of meshset chunks in asset '%s' Chunk deserialize : invalid data blob in asset '%s' vector too long chunk (%d) : wrong type %d (expected %d) chunk (%d) : wrong version %d (expected %d) no data in chunk (%d) chunkId (%d) not valid chunk (%d) not found                                             �      9      :      <                                                      �      ?      @      B                                                      s      p                         v                   y               ����    @                         s                                                     |                         �                           �      y              ����    @                                                                         �      �                         �                                   �      �      y              ����    @                          �                   .?AV_Ref_count_base@std@@                              �                   �               ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@                              �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@                              �                           �      �              ����    @                   �      �      �   E   C 
�5        std::_Ref_count_obj2<donut::chunk::MeshSet>::`vftable'       j      j  
    �   H   F 
�5        std::_Ref_count_obj2<donut::chunk::MeshletSet>::`vftable'    m      m  
    �   (   & 
b        std::exception::`vftable'    �      �  
    �   (   & 
b        std::bad_alloc::`vftable'    �      �  
    �   3   1 
b        std::bad_array_new_length::`vftable'     �      �  
 噾姏@|#T稟�.�磘A%�-弁@芧�>&楍h�K蜌�(檰�2挄憍A"R���︳�<f賾��L<K聀�
d戮翁r�嚻址Z嚙E9屷	�嚻址Zvh襷.Q恜姳矒I;N顺.W~鏵9�>鏟�茒慰浤�庎E$龥鼳鑳�6S,鬜K�"nr鈑鴏�1�z	G�l瞖9 ┢�z	G�l0莺醉1霶劢�"S柂輽箐谇巙�燗荈栆� 鵉#詣,a堀$=o�9pLO沪鷘uC"AI� 憸↖�讚�rYI葩5�?臕忘銨釃c煥]V嚤踖p禭�歱嚤踖p禭(跦 �	6萪O�]7屾N錕霵婬(�3烽�-\*劫Fk{*5%C�劫Fk{b� �D(�$愜w獛啯L鈄炧�$愜w獛啯a�.顛��$愜w獛啯療&乡\b$愜w獛啯钼枒菇窢漒夂嫑�孲疡>鋐iC<*诌G王Z琪<E='X^噴A79①P豬端祆癜~t�#碝
W闱嶀預棊膬�-P猗勧�嶀預棊膬迀L�%趼挞堁�М胈w7煠&搣S﹕�1[{意!O侣徇�5><崑ㄙ�"徇�5>L亂慿�徇�5>苀r伩跾�� �蹰k灅a稠g岝�7颦硣K�鲺�鵰躔7颦硣K!-}�<N咂L�?F:�OI"m諰�?F:豩FwpY騂K�$蟊惺艋;蕧�%鐋%I栶賑?T轞钖�{踗]{謑p_倱�<^9tV �汩r勛U鑕瘩頢�(ibxy�9.襝F7$;4�BI)#hv瓯�昸鳐3鐳彞_厷_�-?侲s溪�&開m.o9y�5V�撊�l-�劕&開m.o9藰瘏 �7.貳��4�&開m.o9字Z�A�*�!Q%7G濄訧)#hv瓯�/E�
:娭圄|饴線忢d慐4�6缿蛼<憇�46�蚿弦'羘N鵘J鈙ca3媞�F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛n� 脕完BW鰨镫繪dd�a�:y趓�
M{尡Q|伩z%	m�U决|賊__憵凡K劯劬W�<藺u'\嵼<�'╃\> "��	?艻%脁诗<�0]憚�矷鄻�	筰箪�/\磩(R崑�;};W虉wKX��硈熧f�!r艛糄$R鍭`氇Qn佺e鍦i钅6襗oЛ~嘕眲��dd�a�:＼%�:缃嶾凵8墆汷!o薔鲛6襗oЛ~圅�蚙枎WNdd�a�:＼%�:缃2蠰璉7v放�#�	�6襗oЛ~�櫤�)襒Add�a�:＼%�:缃/�枾驋^p汶狘%痒�	c�r緶ｎ鲘�dd�a�:'飔>W惛�5s竿y氶/碁岃蠛-坓�(鬄酲;[純o�-坓�(鬄�汬'这栯嘕-WV8o廊l�@9蕂I洔�?軎爈X~�<$G蝳�	噑{6蹰绂%iV徇齢Q�-坓�(鬄鯂/歾a�+g雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛-坓�(鬄鯌T街R三�1婼7>V��吽D|Qp冧U麿0sj�/�Qp冧U貤�;薆｜鸔p冧U)'�-<
Qp冧U黌V突銎/谚泹嵴雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛/谚泹嵴雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛-坓�(鬄鯌T街R三�1婼7>V��吽D|Qp冧U麿0sj�/�Qp冧U貤�;薆｜鸔p冧U)'�-<
Qp冧U黌V突銎-坓�(鬄鯌T街R三�1婼7>V��吽D|Qp冧U麿0sj�/�Qp冧U貤�;薆｜鸔p冧U)'�-<
Qp冧U黌V突銎-坓�(鬄鯌T街R三�1婼7>V��吽D|Qp冧U麿0sj�/�Qp冧U貤�;薆｜鸔p冧U)'�-<
Qp冧U黌V突銎-坓�(鬄鯂/歾a�+gM{>�0
蟨雵J-WV8om�M%>mb-坓�(鬄鯂/歾a�+g-坓�(鬄鯂/歾a�+g-坓�(鬄鯂/歾a�+g-坓�(鬄鯂/歾a�+g雵J-WV8o�(i9x�<Y守Q�1覞.O^簵N瘟妬櫽��$W嵯�. 雵J-WV8o额	hQ�)雵J-WV8o�(i9x�<Y守Q�1覞.O^簵N瘟妬櫽��$W嵯�. 雵J-WV8o额	hQ�)v暕妝�#(戼{1�4焄y*�杜`颀l+�鞯.r擣�0G#盱谑喪仜丩⒚(��苳乮5絚_}4n4�硓橂嘕-WV8o�%-<$濍嘕-WV8o�%-<$�9E\$L釉��E光-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|D�(Z�#�=�/膃5枈Lf)4h�}�'嬸e5�%繰澼瀦�'�7�萿	設�=慮r�*#��ъM(烡AvH洖唟鼖娒猳06卬�,>P1'佱*媒2R&49鑞J3O4猄5�+墙洮氺逸|X胓.#蒹灿獊羻ヾ�P鷾	袔y鼟挰�2=�F竏拀M3%G>禡h槗"J���徴6阾帵讯厡驡5蓶�靾�(擽P_,4��;儗,4��;儗�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2� 9�t榖%ZZ�$为赞G刹~赣 "^惋砤��\&2渆?1_�^,%ZZ�$为赞G刹~赣 "^惋砤萠^Huy駈rE婳�%+騆5j毆傻�7�鸼`I�蔮�>        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S                      .debug$T       p                 .rdata                �3�                     .text$mn       :      眡�     .debug$S                    .text$mn       0      燥"V     .debug$S       �             .text$mn    	   ^     �岷     .debug$S    
   �
  V       	    .text$x        (      纥吨	    .text$mn             憟⑸     .debug$S    
   <  
           .text$mn             憟⑸     .debug$S       L  
           .text$mn       �      d%榁     .debug$S       p  
           .text$mn       �      "錨     .debug$S       t  
           .text$mn       �      �"犸     .debug$S       p  
           .text$mn       �      莧     .debug$S       l  
           .text$mn       (       芌     .debug$S       �             .text$mn       (       芌     .debug$S       �             .text$mn       �      圳�     .debug$S       �             .text$mn       �      &V     .debug$S       �             .text$mn        �      0-~�     .debug$S    !   �              .text$mn    "   �      
��     .debug$S    #   �         "    .text$mn    $   �      �2哏     .debug$S    %   �         $    .text$mn    &   �      "塳     .debug$S    '   �         &    .text$mn    (   <      .ズ     .debug$S    )   0  
       (    .text$mn    *   <      .ズ     .debug$S    +   L  
       *    .text$mn    ,   !      :著�     .debug$S    -   <         ,    .text$mn    .   2      X于     .debug$S    /   <         .    .text$mn    0         峦諡     .debug$S    1            0    .text$mn    2         峦諡     .debug$S    3             2    .text$mn    4   K       }'     .debug$S    5   �         4    .text$mn    6   K       }'     .debug$S    7   �         6    .text$mn    8   K       }'     .debug$S    9   �         8    .text$mn    :   K       }'     .debug$S    ;   �         :    .text$mn    <   [       荘�     .debug$S    =   (         <    .text$mn    >           哷/�     .debug$S    ?   �  
       >    .text$mn    @   �      帎=     .debug$S    A   P         @    .text$mn    B          .B+�     .debug$S    C   �          B    .text$mn    D         ��#     .debug$S    E   �          D    .text$mn    F         ��#     .debug$S    G   �          F    .text$mn    H   +      鱈樂     .debug$S    I   �          H    .text$mn    J   +      兾�4     .debug$S    K   �          J    .text$mn    L   B      贘S     .debug$S    M             L    .text$mn    N   B      贘S     .debug$S    O            N    .text$mn    P   B      贘S     .debug$S    Q   �          P    .text$mn    R   H       襶.      .debug$S    S   �         R    .text$mn    T          c淖�     .debug$S    U            T    .text$mn    V          c淖�     .debug$S    W            V    .text$mn    X   N       z4^,     .debug$S    Y   �         X    .text$mn    Z   N       z4^,     .debug$S    [   �         Z    .text$mn    \          �猴     .debug$S    ]   ,         \    .text$mn    ^          aJ鄔     .debug$S    _   �          ^    .text$mn    `         �ッ     .debug$S    a   �          `    .text$mn    b   B      mr{V     .debug$S    c   �         b    .text$mn    d        � �     .debug$S    e   �'  J      d    .text$x     f         1.b沝    .text$x     g         �$�;d    .text$x     h         S躣    .text$x     i         "E萷d    .text$mn    j        �肂     .debug$S    k   �  f       j    .text$x     l         碙辢j    .text$mn    m   �     C7徑     .debug$S    n   �  D       m    .text$x     o         碙辢m    .text$mn    p   �     [焋�     .debug$S    q   <  @       p    .text$x     r         碙辢p    .text$mn    s   �  %   � #K     .debug$S    t   �  `       s    .text$x     u         S躶    .text$x     v         竼>鎠    .text$x     w         赡﨡s    .text$mn    x   �     F咣�     .debug$S    y   �  :       x    .text$mn    z   q     棔iv     .debug$S    {   x  .       z    .text$mn    |         崪覩     .debug$S    }   �          |        7       R        S                b                r                �                �                �       .        �       F        �       |              P        <          i                   [      (        |      L        �          i                   �      ,        �      D              *        0      N        Z          i                   �      ^        �               �      B        �      \        (      4        \      d        �               �               x               �               8      z        �      x        �      j        S      m        �      p        C      s        �      b        �      `        %      @        I      &        �      8        �              W	              �	      6        *
              �
      :                      v              �              K      "        �      >        �      <        =
              �
                             v      $        �      0              X        S      T        �      H        �          i;                         2        O      Z        �      V        �      J                  iA                   [      	        �              ,              \              �              �              r      f        �      l        �      o              r        �      g              u        �      h        *      v        �      i        9      w        �               �           memmove          memset           $LN13       R    $LN5        .    $LN10       P    $LN7        (    $LN13       L    $LN10       *    $LN16       N    $LN3        ^    $LN4        ^    $LN18       4    $LN726    d    $LN738      d    $LN55       z    $LN106      x    $LN187      j    $LN69       m    $LN67       p    $LN500      s    $LN18   B   b    $LN21       b    $LN3       `    $LN4        `    $LN46   �   @    $LN49       @    $LN10       &    $LN18       8    $LN24           $LN18       6    $LN18       :    $LN24           $LN24           $LN24           $LN10       "    $LN28   [   <    $LN31       <    $LN10           $LN10           $LN10            $LN10       $    $LN31       X    $LN8        H    $LN31       Z    $LN8        J    $LN97   ^  	        �  
       $LN101      	    $LN4            $LN4            $LN4            $LN14   :       $LN17           .xdata      ~          F┑@R        d      ~    .pdata               X賦鶵        �          .xdata      �          （亵.        �      �    .pdata      �          T枨.        �      �    .xdata      �          %蚘%P        �      �    .pdata      �         惻竗P        #      �    .xdata      �          （亵(        I      �    .pdata      �         2Fb�(        r      �    .xdata      �          %蚘%L        �      �    .pdata      �         惻竗L        �      �    .xdata      �          （亵*        �      �    .pdata      �         2Fb�*              �    .xdata      �          %蚘%N        N      �    .pdata      �         惻竗N        �      �    .xdata      �          懐j瀆        �      �    .pdata      �         Vbv鵡        �      �    .xdata      �          （亵4              �    .pdata      �         � �4        L      �    .xdata      �         范^�4        �      �    .pdata      �         鳶�4        �      �    .xdata      �         @鴚`4              �    .pdata      �         [7�4        >      �    .voltbl     �          飾殪4    _volmd      �    .xdata      �   $      勁J        {      �    .pdata      �         藇;塪        �      �    .xdata      �   	      � )9d        �      �    .xdata      �         XAO蚫              �    .xdata      �          赤暤d        �      �    .voltbl     �   6       �d檇    _volmd      �    .xdata      �          �捡z               �    .pdata      �         A刄7z        n       �    .xdata      �         �鸯z        �       �    .pdata      �         �T鼁        !      �    .xdata      �         ⊙釆z        k!      �    .pdata      �         N厮Vz        �!      �    .xdata      �         蕹趢z        "      �    .pdata      �         M纙        j"      �    .xdata      �         �2滐z        �"      �    .pdata      �         饤zz        #      �    .xdata      �         �Ez        i#      �    .pdata      �         �
渰z        �#      �    .xdata      �         璶巣        $      �    .pdata      �         x魂Jz        h$      �    .xdata      �          t@犾x        �$      �    .pdata      �         蘳婅x        %      �    .xdata      �         �!jdj        ~%      �    .pdata      �         �9賆j        �%      �    .xdata      �   	      � )9j        y&      �    .xdata      �         rR�
j        �&      �    .xdata      �   
       �
cj        '      �    .voltbl     �          蔖�j    _volmd      �    .xdata      �         �!jdm        �'      �    .pdata      �         鏷坯m        �(      �    .xdata      �   	      � )9m        )      �    .xdata      �         rR�
m        �)      �    .xdata      �   
       N茹m        *      �    .voltbl     �          浗V9m    _volmd      �    .xdata      �         �!jdp        �*      �    .pdata      �         f弒p        +      �    .xdata      �   	      � )9p        �+      �    .xdata      �         rR�
p        ,      �    .xdata      �   
       躊屓p        �,      �    .voltbl     �          HOkp    _volmd      �    .xdata      �   (      �働s        -      �    .pdata      �         ^睪Js        �-      �    .xdata      �   	      � )9s        .      �    .xdata      �         iq2鐂        �.      �    .xdata      �          礼幁s        /      �    .voltbl     �          郍�s    _volmd      �    .xdata      �          �9�b        �/      �    .pdata      �         惻竗b        �/      �    .xdata      �          �9�`        0      �    .pdata      �         �1癭        H0      �    .xdata      �          （亵@        �0      �    .pdata      �         镦腀        �0      �    .xdata      �         K槑@        �0      �    .pdata      �         �贴@        
1      �    .xdata      �         �眪@        :1      �    .pdata      �         �#�@        g1      �    .voltbl     �          /5ym@    _volmd      �    .xdata      �          �9�&        �1      �    .pdata      �         9偞�&        2      �    .xdata      �          （亵8        w2      �    .pdata      �         � �8        �2      �    .xdata      �         范^�8        �2      �    .pdata      �         鳶�8        73      �    .xdata      �         @鴚`8        x3      �    .pdata      �         [7�8        �3      �    .voltbl     �          飾殪8    _volmd      �    .xdata      �          �9�        �3      �    .pdata      �         d$+        n4      �    .xdata      �         �?�J        �4      �    .pdata      �         �	<�        V5      �    .xdata      �         o,        �5      �    .pdata      �         9$�$        @6      �    .xdata      �         o,        �6      �    .pdata      �         ┆        *7      �    .xdata      �         o,        �7      �    .pdata      �         洴        8      �    .xdata      �         o,        �8      �    .pdata      �         d        �8      �    .voltbl     �          脘輒    _volmd      �    .xdata      �          （亵6        s9      �    .pdata      �         � �6        �9      �    .xdata      �         范^�6        �9      �    .pdata      �         鳶�6        ':      �    .xdata      �         @鴚`6        d:      �    .pdata      �         [7�6        �:      �    .voltbl     �          飾殪6    _volmd      �    .voltbl     �          脘輒    _volmd      �    .xdata      �          （亵:        �:      �    .pdata      �         � �:        ;      �    .xdata      �         范^�:        [;      �    .pdata      �         鳶�:        �;      �    .xdata      �         @鴚`:        �;      �    .pdata      �         [7�:        <      �    .voltbl     �          飾殪:    _volmd      �    .xdata      �          �9�        [<      �    .pdata      �         d$+        �<      �    .xdata      �         �?�J        J=      �    .pdata      �         �	<�        �=      �    .xdata      �         o,        <>      �    .pdata      �         9$�$        �>      �    .xdata      �         o,        .?      �    .pdata      �         ┆        �?      �    .xdata      �         o,         @      �    .pdata      �         洴        橜      �    .xdata      �         o,        A      �    .pdata      �         d        婣      �    .xdata      �          �9�        B      �    .pdata               d$+        xB          .xdata              �?�J        隑         .pdata              �	<�        `C         .xdata              o,        誄         .pdata              9$�$        JD         .xdata              o,        緿         .pdata              ┆        4E         .xdata              o,        〦         .pdata              洴        F         .xdata      	        o,        揊      	   .pdata      
        d        G      
   .xdata               �9�        }G         .pdata              d$+        頖         .xdata      
        �?�J        ^H      
   .pdata              �	<�        蠬         .xdata              o,        BI         .pdata              9$�$        碔         .xdata              o,        &J         .pdata              ┆        楯         .xdata              o,        
K         .pdata              洴        |K         .xdata              o,        頚         .pdata              d        `L         .xdata               �9�"        襆         .pdata              9偞�"        ?M         .voltbl              -哥>    _volmd         .xdata               （亵<        玀         .pdata              愶L<        N         .xdata               �9�        pN         .pdata              9偞�        逳         .xdata               �9�        MO         .pdata              9偞�        繭         .xdata                �9�         2P          .pdata      !        9偞�               !   .xdata      "         �9�$        Q      "   .pdata      #        9偞�$        {Q      #   .xdata      $         （亵X        鍽      $   .pdata      %        V6�>X        .R      %   .xdata      &        M掄tX        uR      &   .pdata      '        ;猍X        綬      '   .xdata      (        很蓢X        S      (   .pdata      )        �x
X        PS      )   .voltbl     *         s橋X    _volmd      *   .xdata      +         （亵H        橲      +   .pdata      ,         ~        逽      ,   .xdata      -         （亵Z        $T      -   .pdata      .        V6�>Z        oT      .   .xdata      /        M掄tZ        筎      /   .pdata      0        ;猍Z        U      0   .xdata      1        很蓢Z        QU      1   .pdata      2        �x
Z        漊      2   .voltbl     3         s橋Z    _volmd      3   .xdata      4         （亵J        閁      4   .pdata      5         ~        2V      5   .xdata      6        啄qJ	        zV      6   .pdata      7        @贳�	        鵙      7   .xdata      8  
      B>z]	        wW      8   .xdata      9         �2g�	        鳺      9   .xdata      :        T�8	        X      :   .xdata      ;        r%�	              ;   .xdata      <  	       舔L	        乊      <   .xdata      =         3賟P	        Z      =   .pdata      >        銀�*	        慫      >   .voltbl     ?                 _volmd      ?   .xdata      @         （亵        [      @   .pdata      A        �#洢        乕      A   .xdata      B         （亵        鈁      B   .pdata      C        �#洢        \      C   .xdata      D         %蚘%        Q\      D   .pdata      E        }S蛥        揬      E   .xdata      F         �9�        診      F   .pdata      G        礝
        1]      G   .rdata      H                     峕     H   .rdata      I         �;�               I   .rdata      J                     薦     J   .rdata      K                     鈃     K   .rdata      L         �)         ^      L   .xdata$x    M                     0^      M   .xdata$x    N        虼�)         R^      N   .data$r     O  /      嶼�         u^      O   .xdata$x    P  $      4��         歗      P   .data$r     Q  $      鎊=         颺      Q   .xdata$x    R  $      銸E�         	_      R   .data$r     S  $      騏糡         H_      S   .xdata$x    T  $      4��         b_      T                  .rdata      U  &       卑閺         確      U   .rdata      V  !       ��         靇      V   .rdata      W  %       :J�         )`      W   .rdata      X  !       %达�         a`      X   .rdata      Y  /       $頼         瀈      Y   .rdata      Z  6       ~蚦'         醏      Z   .rdata      [  3       Oぽ         $a      [   .rdata      \  3       �(�         fa      \   .rdata      ]  =       4y         ゛      ]   .rdata      ^         2�         鈇      ^   .rdata      _  	       鱏k{         b      _   .rdata      `  
       g3         5b      `   .rdata      a  
       RV(         Pb      a   .rdata      b         f獣'         lb      b   .rdata      c         �"｀         卋      c   .rdata      d  
       菭Zs         焍      d   .rdata      e         蛠�         籦      e   .rdata      f  
       (T溲         誦      f   .rdata      g  	       颭t         馼      g   .rdata      h         咼�6         c      h   .rdata      i  H       ]�;.         1c      i   .rdata      j  C       闧~�         jc      j   .rdata      k  4       1               k   .rdata      l         IM         躢      l   .rdata      m  )       �)畩         d      m   .rdata      n  ,       
倇�         Ld      n   .rdata      o         �猤         杁      o   .rdata      p         &叩�         蚫      p   .rdata      q         ?饅q         e      q   .rdata      r  (                   9e     r   .rdata      s  (                   oe     s   .rdata$r    t  $      'e%�         ╡      t   .rdata$r    u        �          纄      u   .rdata$r    v                     謊      v   .rdata$r    w  $      Gv�:         靍      w   .rdata$r    x  $      'e%�         f      x   .rdata$r    y        }%B         #f      y   .rdata$r    z                     9f      z   .rdata$r    {  $      `         Of      {   .rdata$r    |  $      'e%�         nf      |   .rdata$r    }        �弾         慺      }   .rdata$r    ~                     瞗      ~   .rdata$r      $      H衡�         觙         .data$rs    �  *      8V綊         齠      �   .rdata$r    �        �          g      �   .rdata$r    �                     9g      �   .rdata$r    �  $      Gv�:         Ug      �   .rdata$r    �  $      'e%�         zg      �   .data$rs    �  C      �9圵         眊      �   .rdata$r    �        }%B         阦      �   .rdata$r    �                     h      �   .rdata$r    �  $      `         Th      �   .rdata$r    �  $      'e%�         抙      �   .data$rs    �  F      �8,�         蘦      �   .rdata$r    �        }%B         i      �   .rdata$r    �                     @i      �   .rdata$r    �  $      `         xi      �   .debug$S    �  T          r   .debug$S    �  T          s   .debug$S    �  4          H   .debug$S    �  4          J   .debug$S    �  @          K   .chks64     �  �                筰  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ ?deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z ?error@log@donut@@YAXPEBDZZ ?deserialize@ChunkFile@chunk@donut@@SA?AV?$shared_ptr@$$CBVChunkFile@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@5@PEBD@Z ?getChunk@ChunkFile@chunk@donut@@QEBAPEBUChunk@23@VChunkId@23@@Z ?getChunks@ChunkFile@chunk@donut@@QEBAXIAEAV?$vector@PEBUChunk@chunk@donut@@V?$allocator@PEBUChunk@chunk@donut@@@std@@@std@@@Z ?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z ?loadStreamChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@PEAUStreamHandle@23@@Z ?loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z ?loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z ?loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z ?loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z ?deallocate@?$allocator@PEBD@std@@QEAAXQEAPEBD_K@Z ?_Xlength@?$vector@PEBDV?$allocator@PEBD@std@@@std@@CAXXZ ??1ChunkReader@chunk@donut@@QEAA@XZ ??$validateChunk@UStringsTable_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z ??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ ??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z ??$static_pointer_cast@UMeshSet@chunk@donut@@UMeshSetBase@23@@std@@YA?AV?$shared_ptr@UMeshSet@chunk@donut@@@0@AEBV?$shared_ptr@UMeshSetBase@chunk@donut@@@0@@Z ??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ ??$static_pointer_cast@UMeshletSet@chunk@donut@@UMeshSetBase@23@@std@@YA?AV?$shared_ptr@UMeshletSet@chunk@donut@@@0@AEBV?$shared_ptr@UMeshSetBase@chunk@donut@@@0@@Z ??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ ??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z ??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z ??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z ??$validateChunk@UMeshSet_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z ??1?$weak_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ ??1?$vector@PEBUChunk@chunk@donut@@V?$allocator@PEBUChunk@chunk@donut@@@std@@@std@@QEAA@XZ ??$validateChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z ??$validateChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z ??$validateChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z ??$validateChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z ??1?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@UEAAPEAXI@Z ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Zero_range@PEAPEBUChunk@chunk@donut@@@std@@YAPEAPEBUChunk@chunk@donut@@QEAPEBU123@0@Z ??$_Zero_range@PEAPEBD@std@@YAPEAPEBDQEAPEBD0@Z ??$_Copy_memmove@PEAPEBDPEAPEBD@std@@YAPEAPEBDPEAPEBD00@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0??deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z@4HA ?dtor$0@?0??loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z@4HA ?dtor$0@?0??loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z@4HA ?dtor$0@?0??loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z@4HA ?dtor$1@?0??deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z@4HA ?dtor$1@?0??loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z@4HA ?dtor$2@?0??deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z@4HA ?dtor$4@?0??loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z@4HA ?dtor$5@?0??deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z@4HA ?dtor$5@?0??loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z@4HA _CxxThrowException __CxxFrameHandler4 __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@$$CBVIBlob@vfs@donut@@@std@@QEAA@XZ $unwind$?deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z $pdata$?deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z $cppxdata$?deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z $stateUnwindMap$?deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z $ip2state$?deserialize@chunk@donut@@YA?AV?$shared_ptr@$$CBUMeshSetBase@chunk@donut@@@std@@V?$weak_ptr@$$CBVIBlob@vfs@donut@@@4@PEBD@Z $unwind$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $chain$1$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$1$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $chain$3$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$3$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $chain$4$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$4$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $chain$5$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$5$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $chain$6$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$6$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $chain$7$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $pdata$7$?loadStringsTableChunk_0x100@ChunkReader@chunk@donut@@QEAA_NPEBUChunk@23@@Z $unwind$?loadStreamChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@PEAUStreamHandle@23@@Z $pdata$?loadStreamChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@PEAUStreamHandle@23@@Z $unwind$?loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $pdata$?loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $cppxdata$?loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $stateUnwindMap$?loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $ip2state$?loadMeshInfosChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $unwind$?loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $pdata$?loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $cppxdata$?loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $stateUnwindMap$?loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $ip2state$?loadMeshInstancesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $unwind$?loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $pdata$?loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $cppxdata$?loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $stateUnwindMap$?loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $ip2state$?loadMeshNodesChunk_0x100@ChunkReader@chunk@donut@@QEAA_NVChunkId@23@V?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@@Z $unwind$?loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z $pdata$?loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z $cppxdata$?loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z $stateUnwindMap$?loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z $ip2state$?loadMeshSetChunk_0x100@ChunkReader@chunk@donut@@QEAA?AV?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@PEBUChunk@23@@Z $unwind$?deallocate@?$allocator@PEBD@std@@QEAAXQEAPEBD_K@Z $pdata$?deallocate@?$allocator@PEBD@std@@QEAAXQEAPEBD_K@Z $unwind$?_Xlength@?$vector@PEBDV?$allocator@PEBD@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@PEBDV?$allocator@PEBD@std@@@std@@CAXXZ $unwind$??1ChunkReader@chunk@donut@@QEAA@XZ $pdata$??1ChunkReader@chunk@donut@@QEAA@XZ $chain$0$??1ChunkReader@chunk@donut@@QEAA@XZ $pdata$0$??1ChunkReader@chunk@donut@@QEAA@XZ $chain$1$??1ChunkReader@chunk@donut@@QEAA@XZ $pdata$1$??1ChunkReader@chunk@donut@@QEAA@XZ $unwind$??$validateChunk@UStringsTable_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $pdata$??$validateChunk@UStringsTable_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $unwind$??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@UMeshSetBase@chunk@donut@@@std@@QEAA@XZ $unwind$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$0$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$0$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$2$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$2$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$4$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$4$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$6$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$6$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$8$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$8$??$getChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $unwind$??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@UMeshSet@chunk@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@UMeshletSet@chunk@donut@@@std@@QEAA@XZ $unwind$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$0$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$0$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$2$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$2$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$4$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$4$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$6$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$6$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$8$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$8$??$getChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $unwind$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$0$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$0$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$2$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$2$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$4$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$4$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$6$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$6$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$8$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$8$??$getChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $unwind$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$0$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$0$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$2$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$2$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$4$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$4$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$6$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$6$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $chain$8$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $pdata$8$??$getChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBAPEBUChunk@12@VChunkId@12@@Z $unwind$??$validateChunk@UMeshSet_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $pdata$??$validateChunk@UMeshSet_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $unwind$??1?$vector@PEBUChunk@chunk@donut@@V?$allocator@PEBUChunk@chunk@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@PEBUChunk@chunk@donut@@V?$allocator@PEBUChunk@chunk@donut@@@std@@@std@@QEAA@XZ $unwind$??$validateChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $pdata$??$validateChunk@UMeshInfos_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $unwind$??$validateChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $pdata$??$validateChunk@UMeshInstances_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $unwind$??$validateChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $pdata$??$validateChunk@UMeshNodes_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $unwind$??$validateChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $pdata$??$validateChunk@UStream_ChunkDesc_0x100@chunk@donut@@@ChunkFile@chunk@donut@@QEBA_NPEBUChunk@12@@Z $unwind$?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ $pdata$?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ $chain$0$?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ $pdata$0$?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ $chain$1$?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ $pdata$1$?_Destroy@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@EEAAXXZ $unwind$??_G?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@UEAAPEAXI@Z $unwind$?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ $pdata$?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ $chain$0$?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ $pdata$0$?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ $chain$1$?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ $pdata$1$?_Destroy@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@EEAAXXZ $unwind$??_G?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@UEAAPEAXI@Z $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEBDV?$allocator@PEBD@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Zero_range@PEAPEBUChunk@chunk@donut@@@std@@YAPEAPEBUChunk@chunk@donut@@QEAPEBU123@0@Z $pdata$??$_Zero_range@PEAPEBUChunk@chunk@donut@@@std@@YAPEAPEBUChunk@chunk@donut@@QEAPEBU123@0@Z $unwind$??$_Zero_range@PEAPEBD@std@@YAPEAPEBDQEAPEBD0@Z $pdata$??$_Zero_range@PEAPEBD@std@@YAPEAPEBDQEAPEBD0@Z $unwind$??$_Copy_memmove@PEAPEBDPEAPEBD@std@@YAPEAPEBDPEAPEBD00@Z $pdata$??$_Copy_memmove@PEAPEBDPEAPEBD@std@@YAPEAPEBDPEAPEBD00@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0CG@OIOPNEPN@incorrect?5meshinfo?5type?5in?5asse@ ??_C@_0CB@HJJMNELB@bad?5MeshInfo?5chunk?5in?5asset?5?8?$CFs@ ??_C@_0CF@GDGEJLKB@bad?5MeshInstance?5chunk?5in?5asset@ ??_C@_0CB@EMGMHBFH@bad?5MeshNode?5chunk?5in?5asset?5?8?$CFs@ ??_C@_0CP@HHMIEMKO@datastream?5chunk?5?$CI?$CFd?$CJ?5?3?5bad?5typ@ ??_C@_0DG@ONDPFPHM@datastream?5chunk?5?$CI?$CFd?$CJ?5?3?5bad?5ver@ ??_C@_0DD@KEJOFHN@datastream?5chunk?5?$CI?$CFd?$CJ?5?3?5bad?5sem@ ??_C@_0DD@HGOBICNJ@datastream?5chunk?5?$CI?$CFd?$CJ?5?3?5bad?5ele@ ??_C@_0DN@FKPFHAPN@Chunk?5deserialize?5?3?5invalid?5Chu@ ??_C@_0BI@JFAMILHB@incorrect?5Set?5type?5?$CI?$CFd?$CJ@ ??_C@_08GCJNLIKG@Position@ ??_C@_09DEGPAJK@TexCoord0@ ??_C@_09BKFNMBNL@TexCoord1@ ??_C@_06EFAGIKOH@Normal@ ??_C@_07HKHOACGF@Tangent@ ??_C@_09GPCPDHEO@Bitangent@ ??_C@_07BDFMONIL@Indices@ ??_C@_09MNJBMDKB@Indices32@ ??_C@_08GNOGLKLO@Indices8@ ??_C@_0BA@CFMMPOCM@Meshlet?5Headers@ ??_C@_0EI@NOAGLEPA@Chunk?5deserialize?5?3?5invalid?5num@ ??_C@_0ED@DFPPCDKJ@Chunk?5deserialize?5?3?5invalid?5num@ ??_C@_0DE@CBNMOKHO@Chunk?5deserialize?5?3?5invalid?5dat@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_C@_0CJ@BLNMFGII@chunk?5?$CI?$CFd?$CJ?5?3?5wrong?5type?5?$CFd?5?$CIexp@ ??_C@_0CM@PLKABLOJ@chunk?5?$CI?$CFd?$CJ?5?3?5wrong?5version?5?$CFd?5?$CI@ ??_C@_0BG@DAHMDBOK@no?5data?5in?5chunk?5?$CI?$CFd?$CJ@ ??_C@_0BH@CGOKAEJN@chunkId?5?$CI?$CFd?$CJ?5not?5valid@ ??_C@_0BF@MPIBMKEL@chunk?5?$CI?$CFd?$CJ?5not?5found@ ??_7?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@6B@ ??_7?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@6B@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@8 ??_R2?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@UMeshSet@chunk@donut@@@std@@8 ??_R4?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@8 ??_R2?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@UMeshletSet@chunk@donut@@@std@@8 