<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\2bb3fffc077247a453df6e9ce14d7f32\donut_shaders.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\RTXPT\External\Donut\shaders\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\binding_helpers.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\bindless.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\blit_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\bloom_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\brdf.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\deferred_lighting_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\depth_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\forward_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\forward_vertex.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\gbuffer.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\gbuffer_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\hash_based_rng.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\light_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\light_probe_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\light_types.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\lighting.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\material_bindings.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\material_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\mipmapgen_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\motion_vectors.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\packing.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\pixel_readback_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\scene_material.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\shadows.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\skinning_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\sky.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\ssao_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\surface.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\taa_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\tonemapping_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\utils.hlsli" />
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="D:\RTXPT\External\Donut\include\donut\shaders\vulkan.hlsli" />
    <None Include="D:\RTXPT\External\Donut\shaders\blit_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\fullscreen_vs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\ies_profile_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\imgui_pixel.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\imgui_vertex.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\bloom_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\cubemap_gs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\deferred_lighting_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\depth_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\depth_vs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\environment_map_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\exposure_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\forward_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\forward_vs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\gbuffer_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\gbuffer_vs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\histogram_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\joints.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\light_probe.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\material_id_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\mipmapgen_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\motion_vectors_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\pixel_readback_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\sky_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\ssao_blur_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\ssao_compute_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\ssao_deinterleave_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\taa_cs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\passes\tonemapping_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\rect_vs.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\sharpen_ps.hlsl" />
    <None Include="D:\RTXPT\External\Donut\shaders\skinning_cs.hlsl" />
    <None Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\shaders\CMakeFiles\donut_shaders" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{CB6CC40D-9148-3FBA-B040-B60AFF52F06D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{5EC2F7F3-BBD7-3565-BDE5-088B37019277}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
