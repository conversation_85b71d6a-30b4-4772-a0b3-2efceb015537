^D:\RTXPT\EXTERNAL\NRD\SOURCE\TIMER.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\TIMER.CPP
^D:\RTXPT\EXTERNAL\NRD\SOURCE\INSTANCEIMPL.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\INSTANCEIMPL.CPP
^D:\RTXPT\EXTERNAL\NRD\SOURCE\REBLUR.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\REBLUR.CPP
^D:\RTXPT\EXTERNAL\NRD\SOURCE\REFERENCE.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\REFERENCE.CPP
^D:\RTXPT\EXTERNAL\NRD\SOURCE\RELAX.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\RELAX.CPP
^D:\RTXPT\EXTERNAL\NRD\SOURCE\SIGMA.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\SIGMA.CPP
^D:\RTXPT\EXTERNAL\NRD\SOURCE\WRAPPER.CPP
/c /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\_DEPS\MATHLIB-SRC\." /Zi /nologo /W4 /WX /diagnostics:column /O2 /Ob2 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "NRD_API=extern \"C\" __declspec(dllexport)" /D NRD_NORMAL_ENCODING=2 /D NRD_ROUGHNESS_ENCODING=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D _ENFORCE_MATCHING_ALLOCATORS=0 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /D NRD_EXPORTS /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"NRD.DIR\RELEASE\\" /Fd"NRD.DIR\RELEASE\VC143.PDB" /external:W4 /Gd /TP /wd4324 D:\RTXPT\EXTERNAL\NRD\SOURCE\WRAPPER.CPP
