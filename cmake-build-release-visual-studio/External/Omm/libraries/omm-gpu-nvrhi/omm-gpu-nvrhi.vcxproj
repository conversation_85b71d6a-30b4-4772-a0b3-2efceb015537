<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A7F00D95-BC26-3E64-BFFC-EE5548716431}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>omm-gpu-nvrhi</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">omm-gpu-nvrhi.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">omm-gpu-nvrhi</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">omm-gpu-nvrhi.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">omm-gpu-nvrhi</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi;D:\RTXPT\External\Omm\libraries\omm-lib\include;D:\RTXPT\External\Omm\external\glm;D:\RTXPT\External\Omm\external\stb;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\..;D:\RTXPT\External\Omm\external\lz4\build\cmake\..\..\lib;D:\RTXPT\External\Donut\nvrhi\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OpenMPSupport>true</OpenMPSupport>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;_ITERATOR_DEBUG_LEVEL=1;RTXPT_LOCAL_CONFIG_ID_STRING=std::string("NONAME");CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;_ITERATOR_DEBUG_LEVEL=1;RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\");CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi;D:\RTXPT\External\Omm\libraries\omm-lib\include;D:\RTXPT\External\Omm\external\glm;D:\RTXPT\External\Omm\external\stb;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\..;D:\RTXPT\External\Omm\external\lz4\build\cmake\..\..\lib;D:\RTXPT\External\Donut\nvrhi\include;D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include;D:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi;D:\RTXPT\External\Omm\libraries\omm-lib\include;D:\RTXPT\External\Omm\external\glm;D:\RTXPT\External\Omm\external\stb;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\..;D:\RTXPT\External\Omm\external\lz4\build\cmake\..\..\lib;D:\RTXPT\External\Donut\nvrhi\include;D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include;D:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi;D:\RTXPT\External\Omm\libraries\omm-lib\include;D:\RTXPT\External\Omm\external\glm;D:\RTXPT\External\Omm\external\stb;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\..;D:\RTXPT\External\Omm\external\lz4\build\cmake\..\..\lib;D:\RTXPT\External\Donut\nvrhi\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OpenMPSupport>true</OpenMPSupport>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NDEBUG;RTXPT_LOCAL_CONFIG_ID_STRING=std::string("NONAME");CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NDEBUG;RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\");CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi;D:\RTXPT\External\Omm\libraries\omm-lib\include;D:\RTXPT\External\Omm\external\glm;D:\RTXPT\External\Omm\external\stb;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\..;D:\RTXPT\External\Omm\external\lz4\build\cmake\..\..\lib;D:\RTXPT\External\Donut\nvrhi\include;D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include;D:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi;D:\RTXPT\External\Omm\libraries\omm-lib\include;D:\RTXPT\External\Omm\external\glm;D:\RTXPT\External\Omm\external\stb;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\..;D:\RTXPT\External\Omm\external\lz4\build\cmake\..\..\lib;D:\RTXPT\External\Donut\nvrhi\include;D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\include;D:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/RTXPT/External/Omm/libraries/omm-gpu-nvrhi/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/External/Omm/libraries/omm-gpu-nvrhi/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/RTXPT/External/Omm/libraries/omm-gpu-nvrhi/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/External/Omm/libraries/omm-gpu-nvrhi/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi\omm-gpu-nvrhi.h" />
    <ClCompile Include="D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi\omm-gpu-nvrhi.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\ZERO_CHECK.vcxproj">
      <Project>{58F23B37-BE28-30E3-A181-C3BE205A48B0}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\DirectX-Guids.vcxproj">
      <Project>{A58D43BF-C508-34A3-B16F-AFEA1A56C387}</Project>
      <Name>DirectX-Guids</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\DirectX-Headers.vcxproj">
      <Project>{B2FB3632-CD03-3F1A-8F9D-7356D959E51E}</Project>
      <Name>DirectX-Headers</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\glm\glm\glm.vcxproj">
      <Project>{C3907F60-9DC9-3CD2-B57F-1B505CF66EAA}</Project>
      <Name>glm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\lz4\build\cmake\lz4_static.vcxproj">
      <Project>{B1224FE2-79E6-34A6-90E7-D54952731B31}</Project>
      <Name>lz4_static</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\nvrhi.vcxproj">
      <Project>{6B247773-3AD0-3804-80D4-58B109DC712F}</Project>
      <Name>nvrhi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\nvrhi_d3d12.vcxproj">
      <Project>{F0155E33-9BA4-3B64-BD3E-2E832D3FA9E5}</Project>
      <Name>nvrhi_d3d12</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-lib\omm-lib.vcxproj">
      <Project>{72FCE207-945F-3B03-B614-AB3880082C5C}</Project>
      <Name>omm-lib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\xxHash\cmake_unofficial\xxhash.vcxproj">
      <Project>{7B810F7C-4762-34AC-B6B0-C1504DA3EA29}</Project>
      <Name>xxhash</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>