^D:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM\GLM\CMAKELISTS.TXT
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/glm/glm/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
