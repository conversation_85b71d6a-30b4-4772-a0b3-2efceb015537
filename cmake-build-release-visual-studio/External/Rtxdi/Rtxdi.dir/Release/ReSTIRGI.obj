d�0 緢�0  �       .drectve        /   �               
 .debug$S          �              @ B.debug$T        h   �              @ B.text$mn        .  G  u          P`.debug$S            �      
   @B.text$mn           �               P`.debug$S        �   �  �         @B.text$mn                          P`.debug$S        �   5  %         @B.text$mn           M               P`.debug$S        �   f  >         @B.text$mn        /   f               P`.debug$S        �   �  q         @B.text$mn        G   �               P`.debug$S        �   �  �         @B.text$mn           �               P`.debug$S        �   �  �         @B.text$mn           *               P`.debug$S        �   .           @B.text$mn           R               P`.debug$S        �   V  B         @B.text$mn           ~               P`.debug$S        �   �  }         @B.text$mn           �               P`.debug$S        �   �  �          @B.text$mn           !               P`.debug$S        �   !  �!         @B.text$mn           9"               P`.debug$S        �   T"  L#         @B.text$mn           �#               P`.debug$S        @  �#  �$         @B.text$mn        #   #%  F%          P`.debug$S        \  Z%  �&      
   @B.text$mn           '  "'          P`.debug$S        <  ,'  h(         @B.text$mn           �(               P`.debug$S        P  �(  *         @B.text$mn        1   k*  �*          P`.debug$S        x  �*  ,      
   @B.text$mn        v   �,               P`.debug$S        l  �,  d.         @B.xdata             �.              @0@.pdata             �.  �.         @0@.xdata             �.              @0@.pdata             �.  /         @0@.xdata              /              @0@.pdata             (/  4/         @0@.chks64         �  R/               
     /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   _     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReSTIRGI.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  �   �  - L   rtxdi::c_NumReSTIRGIReservoirBuffers + 
  ReSTIRGI_SpatialResamplingParameters & A  rtxdi::ReSTIRGIStaticParameters  8  rtxdi::ReSTIRGIContext %    rtxdi::ReSTIRGI_ResamplingMode &   ReSTIRGI_FinalShadingParameters ) 	  ResTIRGI_SpatialBiasCorrectionMode *   ResTIRGI_TemporalBiasCorrectionMode & G  RTXDI_ReservoirBufferParameters ,   ReSTIRGI_TemporalResamplingParameters    ReSTIRGI_BufferIndices 
 #   size_t  u   uint32_t   �         齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  4    逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  l    檒Gq$�#嗲RR�錨账��K諻刮g�   �    ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �    蜅駠x馘Qf^��=夸餕V�G窄憫尢25  .   躅生.�=�;V宖A�kZ@lxV_�  Z   �	R\�5甕:7峡铻崑p!騎P与�3�%�;  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1     蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  N   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   V8追i顚�^�k细�;>牧惺扴	�\s  �   4         �        �        �   +     �   6   �      D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\RTXPT\External\Rtxdi\Source\ReSTIRGI.cpp D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h �       L2     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb H塡$H墊$UH嬱H冹P�3��H嬞婤堿墆H峂蠨婮D婤��     H墋豀嬅H墋蠬墋�C墋�E袎}貕}�M嗲E�   C$荅型烫=荅詺�?E星E柰蘈>K4荅�   CD荅�   荅�   M嗲E�   荅型烫=KT荅詺�?荅�   BM蠬墋魤}麳墋鋲}�KtH墋貕{ H媩$h荅�   E鹎E�   荅�   Cd荅�   E�儎   E�償   H媆$`H兡P]�7   U       �   �  M G            .     #          �rtxdi::ReSTIRGIContext::ReSTIRGIContext 
 >   this  AI       	 AJ          >   staticParams  AK        6  M          �� N) M          ��+" N< M          X
	+/ N" M          >

 N
 Z   $   P                     @  h            `     Othis  h     OstaticParams  O  �             .  �             F  �   >  �   ?  �(   @  �>   B  �B   G  �E   B  �M   @  �Q   B  �X   C  �[   B  �b   C  �i   B  �m   C  ��   B  ��   C  ��   D  ��   C  ��   D  ��   C  ��   D  ��   E  ��   A  ��   G  ��   C  ��   D  ��   E  �  C  �  E  �
  D  �  E  �#  G  �,   [    0   [   
 r   [    v   [   
 �   [    �   [   
 �   [    �   [   
 �  [    �  [   
 A$H嬄I4J�   �   �   N G                              �rtxdi::ReSTIRGIContext::GetBufferIndices 
 >   this  AJ                                 @       Othis  O�   0              �      $       ^  �    _  �   `  �,   `    0   `   
 s   `    w   `   
 �   `    �   `   
 3繦堿堿H�H堿堿H嬃�   �   �   L G                              �rtxdi::GetDefaultReSTIRGIBufferIndices                         H         ObufferIndices  O�   P              �      D         �      �	     �     �     �     �     �,   W    0   W   
 �   W    �   W   
 H茿    H嬃茿   �   �   �   �   Q G                              �rtxdi::GetDefaultReSTIRGIFinalShadingParams                         H         Oparams  O  �   8              �      ,       6  �    7  �   :  �   ;  �,   Z    0   Z   
 �   Z    �   Z   
 3繦堿堿H嬃茿   茿   �吞�=茿殭?茿   B�   �   �   V G            /       .           �rtxdi::GetDefaultReSTIRGISpatialResamplingParams                         H         Oparams  O �   8           /   �      ,       +  �    ,  �	   2  �.   3  �,   Y    0   Y   
 �   Y    �   Y   
 3繦堿$堿,堿H嬃茿吞L>�吞�=茿   茿   茿   茿   茿殭?茿    �   �   �   W G            G       F           �rtxdi::GetDefaultReSTIRGITemporalResamplingParams                         H         Oparams  O�   @           G   �      4         �      �	   "  �   '  �F   (  �,   X    0   X   
 �   X    �   X   
 仈   H嬄�   �   �   W G                   
           �rtxdi::ReSTIRGIContext::GetFinalShadingParameters 
 >   this  AJ                                 @       Othis  O   �   0              �      $       m  �    n  �
   o  �,   c    0   c   
 |   c    �   c   
 �   c    �   c   
 婣�   �   �   K G                              �rtxdi::ReSTIRGIContext::GetFrameIndex 
 >   this  AJ                                 @       Othis  O   �   0              �      $       O  �    P  �   Q  �,   ]    0   ]   
 p   ]    t   ]   
 �   ]    �   ]   
 婣 �   �   �   O G                              �rtxdi::ReSTIRGIContext::GetResamplingMode 
 >   this  AJ                                 @       Othis  O   �   0              �      $       Y  �    Z  �   [  �,   _    0   _   
 t   _    x   _   
 �   _    �   _   
 AH嬄�   �   �   Z G                   
           �rtxdi::ReSTIRGIContext::GetReservoirBufferParameters 
 >   this  AJ                                 @       Othis  O�   0              �      $       T  �    U  �
   V  �,   ^    0   ^   
    ^    �   ^   
 �   ^    �   ^   
 AtH嬄墑   J�   �   �   \ G                              �rtxdi::ReSTIRGIContext::GetSpatialResamplingParameters 
 >   this  AJ                                 @       Othis  O  �   0              �      $       h  �    i  �   j  �,   b    0   b   
 �   b    �   b   
 �   b    �   b   
 婣��塀H嬄�   �   �   M G                              �rtxdi::ReSTIRGIContext::GetStaticParams 
 >   this  AJ                                 @       Othis  O �   0              �      $       J  �    K  �   L  �,   \    0   \   
 r   \    v   \   
 �   \    �   \   
 ADH嬄ITAdJB �   �   �   ] G                              �rtxdi::ReSTIRGIContext::GetTemporalResamplingParameters 
 >   this  AJ                                 @       Othis  O �   0              �      $       c  �    d  �   e  �,   a    0   a   
 �   a    �   a   
 �   a    �   a   
 仈   �   �   �   W G                   
   !        �rtxdi::ReSTIRGIContext::SetFinalShadingParameters 
 >   this  AJ          >3   finalShadingParams  AK                                 @       Othis     3  OfinalShadingParams  O �   0              �      $       �  �    �  �
   �  �,   h    0   h   
 |   h    �   h   
 �   h    �   h   
   h      h   
 @SH冹 H嬞塓嬍�    H嬎塁hH兡 [�       V       i       �   �   K G            #                 �rtxdi::ReSTIRGIContext::SetFrameIndex 
 >   this  AI  	       AJ        	  >u    frameIndex  A           Z   #  "                         @  0     Othis  8   u   OframeIndex  O �   H           #   �      <       r  �	   s  �   t  �   u  �   v  �   u  �,   d    0   d   
 p   d    t   d   
 �   d    �   d   
 �   d    �   d   
   d      d   
 塓 �       i       �   �   O G                              �rtxdi::ReSTIRGIContext::SetResamplingMode 
 >   this  AJ          >    resamplingMode  A          
 Z   "                          @       Othis        OresamplingMode  O �   0              �      $       y  �    z  �   {  �,   e    0   e   
 t   e    x   e   
 �   e    �   e   
   e      e   
 AtJ墑   �   �   
  \ G                               �rtxdi::ReSTIRGIContext::SetSpatialResamplingParameters 
 >   this  AJ           >/   spatialResamplingParams  AK                                 @       Othis $    /  OspatialResamplingParams  O  �   0              �      $       �  �    �  �   �  �,   g    0   g   
 �   g    �   g   
 �   g    �   g   
    g    $  g   
 @SH冹 H嬞ADJITB Ad婭�    塁hH兡 [�$   V       �   )  ] G            1      +           �rtxdi::ReSTIRGIContext::SetTemporalResamplingParameters 
 >   this  AI       $  AJ         ! >+   temporalResamplingParams  AK        ( 
 Z   #                         @  0     Othis % 8   +  OtemporalResamplingParams  O   �   8           1   �      ,         �   �  �    �  �+   �  �,   f    0   f   
 �   f    �   f   
 �   f    �   f   
 �   f    �   f   
 @  f    D  f   
 H嬔婭 吷tc冮tF冮t*冮t凒uW婮冡嬃塉$凁塉4塀(塉8肏荁(   3狼B4   塀0塀$荁8   脣J冡嬃塉$凁塉,塀(塉8�3缐B8塀$�   �   �   Q G            v       u   "        �rtxdi::ReSTIRGIContext::UpdateBufferIndices 
 >   this  AJ          AK       s                         @       Othis  O �   �           v   �      �       �  �   �  �   �  �$   �  �,   �  �2   �  �5   �  �6   �  �>   �  �U   �  �[   �  �c   �  �i   �  �l   �  �m   �  �o   �  �r   �  �,   i    0   i   
 v   i    z   i   
 �   i    �   i   
 �   i    �   i   
  t
 4 �P    .          j       j       o     20    #           k       k       u     20    1           l       l       {    #f'�欮嬓$吠h鈊&连0�蛢锿l畬u)W*o諱佮@焵ゼ>七�jR�
S�韌斏]a蒔0\�\\8�=/i秃亄>嬃竆癖�.e擁�K拚痻页Oｑ/繺�2廗构芤3e
:[C>�,.3溰]�7靈磝j�6�:�:P甭q噜 G6�%嗸i+#Z$,铸�"l n0I压袢跪屰,婟��譏(穽睶S��丳勠)扃 �U+軃儆炜�郾觢_.つ&xnq��
8}�0Cj�!A怾!rAb軤驲>媐秋8镆 肛xn�褒�}傐% 嵱掑$髍kba�Mi[蓓E匶昹�'[縧�,	雵J-WV8oti觧vmGc雵J-WV8o佗勫r|        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       /                 .debug$S                       .debug$T       h                 .text$mn       .     魶诬     .debug$S         
           .text$mn              ,軻     .debug$S       �              .text$mn              >嵽/     .debug$S    	   �              .text$mn    
          W�)�     .debug$S       �          
    .text$mn       /       �'�=     .debug$S    
   �              .text$mn       G       !邸     .debug$S       �              .text$mn              R�     .debug$S       �              .text$mn              �
劐     .debug$S       �              .text$mn              ea檐     .debug$S       �              .text$mn              f炰     .debug$S       �              .text$mn              駥瑌     .debug$S       �              .text$mn              OX�     .debug$S       �              .text$mn              ?綾     .debug$S       �              .text$mn              �
.q     .debug$S       @             .text$mn        #      �+暟     .debug$S    !   \  
            .text$mn    "         j芫�     .debug$S    #   <         "    .text$mn    $          %矎�     .debug$S    %   P         $    .text$mn    &   1      駛T     .debug$S    '   x  
       &    .text$mn    (   v       *>t�     .debug$S    )   l         (                        o                �               �               4              �      
        �              +              x              �                            U              �              	              p              �               �      "        J      &        �      $                      }      (    $LN12           $LN4             $LN4        &    .xdata      *          �! x        �      *    .pdata      +         衶婮        �      +    .xdata      ,          （亵         B      ,    .pdata      -         礶鵺         y      -    .xdata      .          （亵&        �      .    .pdata      /         鉙gI&        "      /    _fltused         .chks64     0   �                �  ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?JenkinsHash@rtxdi@@YAII@Z ?GetDefaultReSTIRGIBufferIndices@rtxdi@@YA?AUReSTIRGI_BufferIndices@@XZ ?GetDefaultReSTIRGITemporalResamplingParams@rtxdi@@YA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?GetDefaultReSTIRGISpatialResamplingParams@rtxdi@@YA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRGIFinalShadingParams@rtxdi@@YA?AUReSTIRGI_FinalShadingParameters@@XZ ??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z ?GetStaticParams@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGIStaticParameters@2@XZ ?GetFrameIndex@ReSTIRGIContext@rtxdi@@QEBAIXZ ?GetReservoirBufferParameters@ReSTIRGIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetResamplingMode@ReSTIRGIContext@rtxdi@@QEBA?AW4ReSTIRGI_ResamplingMode@2@XZ ?GetBufferIndices@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_BufferIndices@@XZ ?GetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_FinalShadingParameters@@XZ ?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z ?SetResamplingMode@ReSTIRGIContext@rtxdi@@QEAAXW4ReSTIRGI_ResamplingMode@2@@Z ?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_SpatialResamplingParameters@@@Z ?SetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_FinalShadingParameters@@@Z ?UpdateBufferIndices@ReSTIRGIContext@rtxdi@@AEAAXXZ $unwind$??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z $pdata$??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z $unwind$?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z $pdata$?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z $unwind$?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z $pdata$?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z 