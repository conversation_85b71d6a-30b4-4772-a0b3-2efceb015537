d�? 緢菬  �       .drectve        <  �	               
 .debug$S        爂  (  萺         @ B.debug$T        h   躵              @ B.rdata             Ds              @ 0@.text$mn        9  Hs  乽          P`.debug$S        �  媢  #y         @B.text$mn           脃               P`.debug$S        �   謞  緕         @B.text$mn           鷝               P`.debug$S        �   {  �{         @B.text$mn        5   '|               P`.debug$S        �   \|  8}         @B.text$mn        )   `}               P`.debug$S        �   墋  ]~         @B.text$mn        5   厏               P`.debug$S        �   簙  �         @B.text$mn        E   �               P`.debug$S        �   �  還         @B.text$mn           �               P`.debug$S        �   �  髞         @B.text$mn           /�               P`.debug$S        �   B�  6�         @B.text$mn           r�               P`.debug$S        �   v�  b�         @B.text$mn           瀯               P`.debug$S        �   ﹦  潊         @B.text$mn           賲               P`.debug$S        �   鋮  虇         @B.text$mn           �               P`.debug$S        �   !�  
�         @B.text$mn           I�               P`.debug$S        �   b�  Z�         @B.text$mn           枆               P`.debug$S        �   泬  噴         @B.text$mn        $   脢               P`.debug$S        �   鐘  邒         @B.text$mn        g   �  倢          P`.debug$S        �  枌  :�      
   @B.text$mn           瀻               P`.debug$S        H  畮  鰪         @B.text$mn           F�  N�          P`.debug$S        <  X�  攽         @B.text$mn           鋺               P`.debug$S        0  鷳  *�         @B.text$mn        2   z�               P`.debug$S        �  瑩  H�      
   @B.text$mn        =   瑫  闀          P`.debug$S        x  髸  k�      
   @B.text$mn        �   蠗               P`.debug$S        |  v�  驒         @B.text$mn        2   j�               P`.debug$S          湚           @B.text$mn           鄾               P`.debug$S        �   銢  脺         @B.xdata             ��              @0@.pdata             �  �         @0@.xdata             9�              @0@.pdata             A�  M�         @0@.xdata             k�              @0@.pdata             s�  �         @0@.xdata             潩              @0@.pdata               睗         @0@.chks64         �  蠞               
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   R  _     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReSTIRDI.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data   �   <  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable ? �   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ �    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E �   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask - L   rtxdi::c_NumReSTIRDIReservoirBuffers 8 
L        rtxdi::ReSTIRDIContext::NumReservoirBuffers . �    std::integral_constant<bool,0>::value . �   std::integral_constant<bool,1>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 4 �  @ _Mtx_internal_imp_t::_Critical_section_size 5 �   _Mtx_internal_imp_t::_Critical_section_align 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 + �    std::_Aligned_storage<64,8>::_Fits 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits % \    _Atomic_memory_order_relaxed % \   _Atomic_memory_order_consume % \   _Atomic_memory_order_acquire % \   _Atomic_memory_order_release % \   _Atomic_memory_order_acq_rel % \   _Atomic_memory_order_seq_cst $ �    std::strong_ordering::equal : �    std::integral_constant<unsigned __int64,0>::value ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy : �   std::integral_constant<unsigned __int64,2>::value / �   std::atomic<long>::is_always_lock_free : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ; �   std::atomic<unsigned __int64>::is_always_lock_free  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> * �  ReSTIRDI_TemporalBiasCorrectionMode  .  ReSTIRDI_BufferIndices &   $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t  (  _TypeDescriptor 	 )  tm % k  _s__RTTICompleteObjectLocator2 A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType & L  rtxdi::ReSTIRDIStaticParameters  =  rtxdi::CheckerboardMode % �  rtxdi::ReSTIRDI_ResamplingMode  �  rtxdi::ReSTIRDIContext & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16  �  std::_Lockit  "   std::_Atomic_counter_t    std::_Num_base # �  std::numeric_limits<char8_t>  �  std::hash<float>    std::_Num_int_base    std::float_denorm_style  q  std::bad_cast     std::_Compare_t " A  std::numeric_limits<double>  �  std::__non_rtti_object ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  \  std::pointer_safety  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short>   |  std::pmr::memory_resource    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t>     std::_Leave_proxy_unbound  �  std::_Iterator_base12 $ �  std::_Atomic_integral<long,4>  �  std::hash<long double>   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double>  
  std::bad_exception  �  std::_Fake_allocator ! ?  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long>  T  std::_Ref_count_base  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits ! ,  std::numeric_limits<short> ! c  std::_Shared_ptr_spin_lock     std::bad_alloc # 2  std::numeric_limits<__int64>  C  std::memory_order # �  std::_Atomic_storage<long,4>  l  std::atomic_flag   6  std::bad_array_new_length  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int>  �  std::atomic<long>   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::bad_typeid  �  std::_Compare_eq    std::nullptr_t  <  std::bad_weak_ptr ) 8  std::numeric_limits<unsigned long>   K  std::_Atomic_padded<long> ' "  std::numeric_limits<signed char>  �  std::_Literal_zero      std::numeric_limits<char>  �  std::_Unused_parameter * �  std::ranges::_Uninitialized_fill_fn 7 &  std::ranges::_Uninitialized_value_construct_n_fn # G  std::ranges::_Find_if_not_fn , �  std::ranges::_Uninitialized_move_n_fn !   std::ranges::_Destroy_n_fn $ �  std::ranges::_Construct_at_fn "   std::ranges::_Destroy_at_fn  ;  std::ranges::_Find_fn ! �  std::ranges::subrange_kind    std::ranges::_Next_fn % M  std::ranges::_Adjacent_find_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn 7   std::ranges::_Uninitialized_default_construct_fn * �  std::ranges::_Uninitialized_move_fn , �  std::ranges::_Uninitialized_copy_n_fn   5  std::ranges::_Mismatch_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag  k  std::ranges::_Min_fn  )  std::ranges::_Copy_fn * �  std::ranges::_Uninitialized_copy_fn    std::ranges::_Destroy_fn , �  std::ranges::_Uninitialized_fill_n_fn  #  std::ranges::dangling  S  std::ranges::_Search_fn    std::ranges::_Prev_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn    std::ranges::_Advance_fn 5    std::ranges::_Uninitialized_value_construct_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn 9   std::ranges::_Uninitialized_default_construct_n_fn " �  std::_Asan_aligned_pointers  �  std::partial_ordering  .  std::numeric_limits<int>  �  std::bad_variant_access ) �  ReSTIRDI_SpatialBiasCorrectionMode   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat  8  timespec 
 !   _ino_t , �  ReSTIRDI_TemporalResamplingParameters M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> + �  ReSTIRDI_SpatialResamplingParameters  9  terminate_handler  �  _s__RTTIBaseClassArray 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24 ) �  ReSTIRDI_InitialSamplingParameters  �  _CatchableTypeArray  "  _stat64i32  �  _PMD  >  type_info ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t  U  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 ! �  ReSTIRDI_ShadingParameters % |  __RTTIClassHierarchyDescriptor & G  RTXDI_ReservoirBufferParameters 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray & �  ReSTIRDI_LocalLightSamplingMode 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  r  RTXDI_RuntimeParameters  �  _ldiv_t  9  _timespec64  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers  �   �      ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  Q    �"睱建Bi圀対隤v��cB�'窘�n  �    f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �    桅棙�萑�3�<)-~浰-�?>撎�6=Y}  ,   *u\{┞稦�3壅阱\繺ěk�6U�  j   �%逽|犟�1剝%sh鵺K媡簂蹶#楎`{w  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  0   �=蔑藏鄌�
艼�(YWg懀猊	*)  q   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  D   匐衏�$=�"�3�a旬SY�
乢�骣�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u     攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  W   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   鹴y�	宯N卮洗袾uG6E灊搠d�  ;   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     �*M�现.凿萰閱寴诃缶鲍6�#�+�4  U   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  �   K.+6螶�娷滕L昋�=Gn瑺�   �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg     傊P棼r铞
w爉筫y;H+(皈LL��7縮  R   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥     溶�$椉�
悇� 騐`菚y�0O腖悘T  j   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  	   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  R	   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �	   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �	   �0�*е彗9釗獳+U叅[4椪 P"��  &
   齝D屜u�偫[篔聤>橷�6酀嘧0稈  d
   齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  �
   _O縋[HU-銌�鼪根�鲋薺篮�j��  �
   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(     l籴靈LN~噾2u�< 嵓9z0iv&jザ  j   檒Gq$�#嗲RR�錨账��K諻刮g�   �   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  -   副謐�斦=犻媨铩0
龉�3曃譹5D   o   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   �疀�4�A圏,oHB瓳HJ��2�0(v/  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  !
   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  _
   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �
   繃S,;fi@`騂廩k叉c.2狇x佚�  �
   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  %   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  c   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  ?   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  }   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  G   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎     D���0�郋鬔G5啚髡J竆)俻w��  k   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  R   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  a   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  6   G�膢刉^O郀�/耦��萁n!鮋W VS  u   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  c   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   +椬恡�
	#G許�/G候Mc�蜀煟-  ?   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  )   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  l   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  7   �
bH<j峪w�/&d[荨?躹耯=�  v   交�,�;+愱`�3p炛秓ee td�	^,  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  =   `k�"�1�^�`�d�.	*貎e挖芺
脑�     +4[(広
倬禼�溞K^洞齹誇*f�5  �   L       �  �     �  �  5   �  �  D   �  �  P     �  �     �  �   �   �   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\numeric D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\RTXPT\External\Rtxdi\Source\ReSTIRDI.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDIParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDI.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp   �       L  v      z     
    b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb    H塡$H墊$UH嬱H冹P3�H嬞H�9墆茿   AD婮H峂蠨婤婻�     H墋鐷墋�M郒墋豀墋�C E�C@KP荂`   荂d   荂h   荂l   荂p费8荂t   H荂x   婯荅�   H荅焱蘈>荅�   荅�   荅鋐ff?荅�   荅型烫=荅�   ?H墋魤}�M星E�   荅�   荅型烫=荅�   ?E郒墋枨E�   荅�   B媭   M�儛   E郒墋鋲}�嫚   M蠬荅�  �A荅�   荅�   荅�   嫲   M�兝   E�冃   嬥   吷t冮t
凒u媨�媨髯冪�菈{4婥D婯�葔C0A岮�vb�E2覊CDD岪斧狝鬣殃�RD+繟岺D塁@斧狣塁H麽D塁L殃�R+菶勔塊PAD菵嬃D塁TH嬅H媩$hD塁H媆$`H兡P]脣斧獕KDA�D岮A鬣殃�RD+繟凒u楧塁@虢;   p       �   k  M G            9     9  �        �rtxdi::ReSTIRDIContext::ReSTIRDIContext 
 >�   this  AI       "�  AJ          >�   params  AK        : 5 M          仢
	(%
'" >�    useSpatialResampling  AX  �      AZ  �    �  c  AZ �    F  N" M          ����#	 N M        �  �' N' M        �  ��''$
 N M        �  ��+ N M        �  B
 N
 Z   $   P                     @ & h   �  �  �  �  �  �       `   �  Othis  h   �  Oparams  O �             9  �            l  �   a  �   c  �   d  �    e  �+   f  �B   g  �V   f  �Z   g  �f   h  ��   n  ��   i  ��   j  ��   i  ��   j  �  i  �#  j  �'  k  �.  i  �5  j  �9  k  �V  j  �]  k  �z  n  ��  o  ��  p  ��  o  ��  p  ��  q  �  p  �  q  �  p  �,   w    0   w   
 r   w    v   w   
 �   w    �   w   
 �   w    �   w   
 
  w      w   
   w    !  w   
 1  w    5  w   
 �  w    �  w   
 A@H嬄IPJ�   �   �   N G                      �        �rtxdi::ReSTIRDIContext::GetBufferIndices 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       �  �    �  �   �  �,   {    0   {   
 s   {    w   {   
 �   {    �   {   
 3繦堿H�H堿H堿H嬃�   �   �   L G                      �        �rtxdi::GetDefaultReSTIRDIBufferIndices                         H         ObufferIndices  O�   P              �     D         �      �     �	     �
   !  �   #  �   $  �,   r    0   r   
 �   r    �   r   
 茿费8H嬃茿   H茿   茿   茿   茿   �   �   �   �   T G            5       4   �        �rtxdi::GetDefaultReSTIRDIInitialSamplingParams                         H         Oparams  O   �   8           5   �     ,       '  �    )  �   1  �4   2  �,   s    0   s   
 �   s    �   s   
 3繦堿堿H嬃H茿  �A�   茿   茿   �   �   �   L G            )       (   �        �rtxdi::GetDefaultReSTIRDIShadingParams                         H         Oparams  O   �   8           )   �     ,       P  �    Q  �	   W  �(   X  �,   v    0   v   
 �   v    �   v   
 H茿    H嬃茿   茿   茿   �吞�=茿   ?茿   B�   �   �   V G            5       4   �        �rtxdi::GetDefaultReSTIRDISpatialResamplingParams                         H         Oparams  O �   8           5   �     ,       D  �    E  �   L  �4   M  �,   u    0   u   
 �   u    �   u   
 3繦堿$堿,H嬃H茿吞L>茿   茿   茿   茿fff?茿   �吞�=茿   ?�   �   �   W G            E       D   �        �rtxdi::GetDefaultReSTIRDITemporalResamplingParams                         H         Oparams  O�   8           E   �     ,       5  �    6  �	   @  �D   A  �,   t    0   t   
 �   t    �   t   
 婣�   �   �   K G                      �        �rtxdi::ReSTIRDIContext::GetFrameIndex 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       �  �    �  �   �  �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 A`H嬄IpJ�   �   �   Z G                      �        �rtxdi::ReSTIRDIContext::GetInitialSamplingParameters 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       �  �    �  �   �  �,   |    0   |   
    |    �   |   
 �   |    �   |   
 婣�   �   �   O G                      �        �rtxdi::ReSTIRDIContext::GetResamplingMode 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       t  �    u  �   v  �,   y    0   y   
 t   y    x   y   
 �   y    �   y   
 A H嬄�   �   �   Z G                   
   �        �rtxdi::ReSTIRDIContext::GetReservoirBufferParameters 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ~  �      �
   �  �,   x    0   x   
    x    �   x   
 �   x    �   x   
 A0H嬄�   �   �   N G                   
   �        �rtxdi::ReSTIRDIContext::GetRuntimeParams 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       y  �    z  �
   {  �,   z    0   z   
 s   z    w   z   
 �   z    �   z   
 佇   H嬄夃   J�   �   �   R G                      �        �rtxdi::ReSTIRDIContext::GetShadingParameters 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       �  �    �  �   �  �,       0      
 w       {      
 �       �      
 伆   H嬄壚   J�   �   �   \ G                      �        �rtxdi::ReSTIRDIContext::GetSpatialResamplingParameters 
 >�   this  AJ                                 @     �  Othis  O  �   0              �     $       �  �    �  �   �  �,   ~    0   ~   
 �   ~    �   ~   
 �   ~    �   ~   
 H岮�   �   �   Q G                      �        �rtxdi::ReSTIRDIContext::GetStaticParameters 
 >�   this  AJ                                 @     �  Othis  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 亐   H嬄墣   仩   JB �   �   �   ] G            $       #   �        �rtxdi::ReSTIRDIContext::GetTemporalResamplingParameters 
 >�   this  AJ        $                         @     �  Othis  O �   0           $   �     $       �  �    �  �#   �  �,   }    0   }   
 �   }    �   }   
 �   }    �   }   
 @SH冹 H嬞塓嬍�    墐�   H嬎婥��    婯吷t-冮t凒u#婥#�缐C4H兡 [脣C餍冟�缐C4H兡 [们C4    H兡 [�   q    "   �       �   5  K G            g      a   �        �rtxdi::ReSTIRDIContext::SetFrameIndex 
 >�   this  AI  	     ] =  P   AJ        	  >u    frameIndex  A           M          &
 N Z   #                           @ 
 h      0   �  Othis  8   u   OframeIndex  O   �   X           g   �     L       �  �	   �  �   �  �   �  �&   �  �>   �  �G   �  �Q   �  �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 �   �    �   �   
 L  �    P  �   
 A`JIp�   �     Z G                              �rtxdi::ReSTIRDIContext::SetInitialSamplingParameters 
 >�   this  AJ          >�   initialSamplingParams  AK                                 @     �  Othis "    �  OinitialSamplingParams  O�   0              �     $       �  �    �  �   �  �,   �    0   �   
    �    �   �   
 �   �    �   �   
   �      �   
 塓�       �       �   �   O G                               �rtxdi::ReSTIRDIContext::SetResamplingMode 
 >�   this  AJ          >�   resamplingMode  A          
 Z                             @     �  Othis     �  OresamplingMode  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 t   �    x   �   
 �   �    �   �   
   �      �   
 佇   J夃   �   �   �   R G                              �rtxdi::ReSTIRDIContext::SetShadingParameters 
 >�   this  AJ          >�   shadingParams  AK                                 @     �  Othis     �  OshadingParams  O�   0              �     $       �  �    �  �   �  �,   �    0   �   
 w   �    {   �   
 �   �    �   �   
    �      �   
 H冹(B媮�   
D$塂$D$壈   伬   H兡(�   �   F  \ G            2      -           �rtxdi::ReSTIRDIContext::SetSpatialResamplingParameters 
 >�   this  AJ        2   >�   spatialResamplingParams  AK        2  >�   srp  C�            !  D     (                      @  0   �  Othis $ 8   �  OspatialResamplingParams      �  Osrp  O  �   @           2   �     4       �  �   �  �   �  �   �  �-   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 \  �    `  �   
 @SH冹 H嬞亐   J墣   B 仩   婭�    墐�   H兡 [�-   q       �   )  ] G            =      7           �rtxdi::ReSTIRDIContext::SetTemporalResamplingParameters 
 >�   this  AI       0  AJ         ! >�   temporalResamplingParams  AK        1 
 Z   #                         @  0   �  Othis % 8   �  OtemporalResamplingParams  O   �   8           =   �     ,       �  �   �  �)   �  �7   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 @  �    D  �   
 D婹L嬌A岯�斧獀AD�E峆A麾斧陯RD+袳塓@E堿DE塓HA岼E塓L麽殃�R+華塈PE塓TE塓脣	D岮A鬣殃�RD+繣堿@A塈DA凓u	E堿TE堿肊峆E堿H斧狤堿LA麾殃�RD+蠩塓PE塓TE塓�   �   �   Q G            �       �           �rtxdi::ReSTIRDIContext::UpdateBufferIndices 
 >�   this  AJ          AQ       �  >�    useSpatialResampling  A   <     
  AZ                                H     �  Othis  O  �   p           �   �     d       �  �    �  �   �  �M   �  �U   �  �V   �  �u   �  �}   �  �~   �  ��   �  ��   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 婹呉t#冴t凓u婣#�缐A4脣A餍冟�缐A4们A4    �   �   �   U G            2       1           �rtxdi::ReSTIRDIContext::UpdateCheckerboardField 
 >�   this  AJ        2                         H     �  Othis  O �   H           2   �     <       �  �    �  �   �  �   �  �   �  �&   �  �,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �     �   �   A G                       �        �rtxdi::debugCheckParameters  >�   params  AJ          D                           H     �  Oparams  O �   (              �            [  �    ^  �,   �    0   �   
 h   �    l   �   
 �   �    �   �   
  t
 4 �P    9          �       �       �     20    g           �       �       �     20    =           �       �       �     B      2           �       �       �    E褼5`苁<b　�撏h鈊&连0潫)'b鸽,et蜴眆u懛葵c筋>qTj吏�EM]z溧y宑FW拔i呛幄�'醚X榀�)'�裨LE唖�X礩T�鏴盈Gm�W泾n��&�(�CL.昬槗賐�:7� 蓱j�
｝v�AmCreF雥S�F憣^xG;:V�?U饁f鏛氜铼�=fpo^id�訑+��N1v 禍z姡!pd�君�F�)FJ�uW賁芌欌氰�1K鄥E閙%蠴�y@穚rg,q雛Lx婕d=犺±&J衄�璗X喼篱 狨� 
=�T涮緘T菱駺G呰A剀5磼�%ォP崣`N謪亇�茊瑚�I棳 `@筯� h�$�/琢k�-钘�牴&@禄L(夊敜�>廃F趃蚁8遂祚皛t�	á;J蓓E匶昹K凈鵒秝k雵J-WV8o冗�=.g�雵J-WV8o�1�8]Z�-坓�(鬄�3,�4q胭        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       爂               .debug$T       h                 .rdata                畀�                     .text$mn       9     聏     .debug$S       �             .text$mn              勌╇     .debug$S       �              .text$mn    	          佹襾     .debug$S    
   �          	    .text$mn       5       触憂     .debug$S       �              .text$mn    
   )       �79d     .debug$S       �          
    .text$mn       5       z�>     .debug$S       �              .text$mn       E       觠h�     .debug$S       �              .text$mn              舷磥     .debug$S       �              .text$mn              Z5g~     .debug$S       �              .text$mn              ��     .debug$S       �              .text$mn              LSa     .debug$S       �              .text$mn              0}�     .debug$S       �              .text$mn              q<!     .debug$S       �              .text$mn              峫躿     .debug$S        �              .text$mn    !          烣0�     .debug$S    "   �          !    .text$mn    #   $       M�w     .debug$S    $   �          #    .text$mn    %   g      惜     .debug$S    &   �  
       %    .text$mn    '          铵I%     .debug$S    (   H         '    .text$mn    )         ��     .debug$S    *   <         )    .text$mn    +          �?鍎     .debug$S    ,   0         +    .text$mn    -   2       俢�-     .debug$S    .   �  
       -    .text$mn    /   =      wU     .debug$S    0   x  
       /    .text$mn    1   �       �1�     .debug$S    2   |         1    .text$mn    3   2       衞j!     .debug$S    4            3    .text$mn    5          .B+�     .debug$S    6   �          5        4                �                �       	                      ^              �                     
        l              �              
              \              �              �              V      #        �              &              y              �      !        �      %        (      )        v      '        �      /        F      -        �      +              1        8      3        p      5    $LN41           $LN15       %    $LN4        /    $LN4        -    .xdata      7          �! x        �      7    .pdata      8         Y
        �      8    .xdata      9          （亵%        C	      9    .pdata      :         ⅸ.�%        z	      :    .xdata      ;          （亵/        �	      ;    .pdata      <         現�/        #
      <    .xdata      =          �9�-        �
      =    .pdata      >          T枨-              >    _fltused         .chks64     ?   �                v  ?NumReservoirBuffers@ReSTIRDIContext@rtxdi@@2IB ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?JenkinsHash@rtxdi@@YAII@Z ?GetDefaultReSTIRDIBufferIndices@rtxdi@@YA?AUReSTIRDI_BufferIndices@@XZ ?GetDefaultReSTIRDIInitialSamplingParams@rtxdi@@YA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetDefaultReSTIRDITemporalResamplingParams@rtxdi@@YA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetDefaultReSTIRDISpatialResamplingParams@rtxdi@@YA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRDIShadingParams@rtxdi@@YA?AUReSTIRDI_ShadingParameters@@XZ ??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z ?GetReservoirBufferParameters@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetResamplingMode@ReSTIRDIContext@rtxdi@@QEBA?AW4ReSTIRDI_ResamplingMode@2@XZ ?GetRuntimeParams@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_RuntimeParameters@@XZ ?GetBufferIndices@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_BufferIndices@@XZ ?GetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetShadingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_ShadingParameters@@XZ ?GetFrameIndex@ReSTIRDIContext@rtxdi@@QEBAIXZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z ?SetResamplingMode@ReSTIRDIContext@rtxdi@@QEAAXW4ReSTIRDI_ResamplingMode@2@@Z ?SetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_InitialSamplingParameters@@@Z ?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z ?SetShadingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_ShadingParameters@@@Z ?UpdateBufferIndices@ReSTIRDIContext@rtxdi@@AEAAXXZ ?UpdateCheckerboardField@ReSTIRDIContext@rtxdi@@AEAAXXZ ?debugCheckParameters@rtxdi@@YAXAEBUReSTIRDIStaticParameters@1@@Z $unwind$??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z $pdata$??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z $unwind$?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z $pdata$?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z $unwind$?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z $pdata$?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z $unwind$?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z $pdata$?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z 