d嗶 Y驡h烘 �      .drectve        <  4&               
 .debug$S        @h  p'              @ B.text$mn        :   皬  陱          P`.debug$S          �  �         @B.text$mn           爳  窉          P`.debug$S        �  翏  Y�         @B.text$mn        0   鍞  �          P`.debug$S        �  �  粬         @B.text$mn        0   G�  w�          P`.debug$S        �  仐  =�         @B.text$mn        0   蓹  鶛          P`.debug$S        �  �  硾         @B.text$mn        �  ?�  轁          P`.debug$S        �  -�  
�      f   @B.text$x         (   	�  1�          P`.text$mn        #  E�  h�          P`.debug$S        $  赴  芗      `   @B.text$x         (   溊  睦          P`.text$mn        �  乩  y�          P`.debug$S        X  陕  !�      f   @B.text$x         (   �  E�          P`.text$mn        �  Y�               P`.debug$S          嘣  枸          @B.text$mn        �  (�  暹          P`.debug$S        �  镞  腓      >   @B.text$mn          W�  _�          P`.debug$S        �	  囶  c�      l   @B.text$mn        �   淈  堼          P`.debug$S        �  褒  P        @B.text$x                      P`.text$mn           .         P`.debug$S        �
  � R     J   @B.text$x            6 F         P`.text$mn        <   P �         P`.debug$S        0  � �     
   @B.text$mn        <   > z         P`.debug$S        L  � �     
   @B.text$mn        !   H i         P`.debug$S        <  } �        @B.text$mn        2   � '         P`.debug$S        <  ; w        @B.text$mn        [   � J         P`.debug$S        �  ^ 2        @B.text$mn                     P`.debug$S        T   q        @B.text$mn        e   �           P`.debug$S        �  0  �"        @B.text$mn           �# �#         P`.debug$S        �   �# �$        @B.text$mn           �$ %         P`.debug$S        �   % �%        @B.text$mn        B   6& x&         P`.debug$S           �& �'        @B.text$mn        B   �' (         P`.debug$S          2( B)        @B.text$mn        B   ~) �)         P`.debug$S        �   �) �*        @B.text$mn        V   + l+         P`.debug$S        �  �+ $-        @B.text$mn           �-              P`.debug$S        �   �- �.        @B.text$mn        z  �. X4         P`.debug$S        P  H5 楪     �   @B.text$x            腗 蠱         P`.text$x            贛 鍹         P`.text$mn           餗              P`.debug$S        �   驧 跱        @B.text$mn           O              P`.debug$S        �   *O P        @B.text$mn           VP              P`.debug$S        �   dP \Q        @B.text$mn           楺              P`.debug$S          稱 薘        @B.text$mn           S %S         P`.debug$S          /S 3T        @B.text$mn           oT              P`.debug$S        �   奣 zU        @B.text$mn        x  禪 .Y     
    P`.debug$S        D  扽 謅     >   @B.text$mn           Bd              P`.debug$S           Td Te        @B.text$mn           恊              P`.debug$S        @  爀 鄁        @B.text$mn            0g Pg         P`.debug$S        �   ng 2h        @B.text$mn        z   nh 鑘         P`.debug$S        �  黨 糼        @B.text$mn           榣 ﹍         P`.debug$S        �   絣 昺        @B.text$mn           裮 鈓         P`.debug$S        �   鰉 頽        @B.text$mn           *o ;o         P`.debug$S        �   Oo ;p        @B.text$mn        B   wp 筽         P`.debug$S        �  蚿 is        @B.text$mn        B   mt 痶         P`.debug$S        �  胻 ow        @B.text$mn        A   sx 磝         P`.debug$S        �  葂 t{        @B.text$mn           x| 媩         P`.debug$S        �   晐 i}        @B.xdata                          @0@.pdata             瓆 箎        @0@.xdata             讅             @0@.pdata             銄 飣        @0@.xdata             
~             @0@.pdata             ~ !~        @0@.xdata             ?~             @0@.pdata             K~ W~        @0@.xdata             u~             @0@.pdata             }~ 墌        @0@.xdata                          @0@.pdata             硚 縹        @0@.xdata             輣             @0@.pdata             鍉 駘        @0@.xdata                          @0@.pdata              #        @0@.xdata             A             @0@.pdata             I U        @0@.xdata             s             @0@.pdata             { �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             	�             @0@.pdata             � �        @0@.xdata              ;� [�        @0@.pdata             o� {�        @0@.xdata          	   檧         @@.xdata             秬 紑        @@.xdata             苺             @@.xdata             褉 閫        @0@.pdata             齹 	�        @0@.xdata          	   '� 0�        @@.xdata             D� J�        @@.xdata             T�             @@.xdata             Z�             @0@.pdata             b� n�        @0@.xdata             寔             @0@.pdata              皝        @0@.xdata          8   蝸 �        @0@.pdata             $� 0�        @0@.xdata             N� ^�        @0@.pdata             |� 垈        @0@.xdata          H    顐        @0@.pdata             � �        @0@.xdata          	   ,� 5�        @@.xdata          
   I� V�        @@.xdata             j�             @@.xdata             q�             @0@.pdata             y� 厓        @0@.xdata                          @0@.pdata             珒 穬        @0@.xdata             諆             @0@.pdata             輧 閮        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             9� U�        @0@.pdata             i� u�        @0@.xdata          
   搫 爠        @@.xdata             緞             @@.xdata             羷 蓜        @@.xdata             觿 趧        @@.xdata          	   鋭             @@.xdata             韯             @0@.pdata             鮿 �        @0@.voltbl            �               .xdata              � <�        @0@.pdata             P� \�        @0@.xdata          
   z� 噮        @@.xdata                          @@.xdata             ▍ 皡        @@.xdata             簠 羺        @@.xdata          	   藚             @@.xdata             詤             @0@.pdata             軈 鑵        @0@.voltbl            �               .xdata             � #�        @0@.pdata             7� C�        @0@.xdata          
   a� n�        @@.xdata             寙             @@.xdata             弳 梿        @@.xdata              ▎        @@.xdata          	   矄             @@.xdata             粏             @0@.pdata             脝 蠁        @0@.voltbl            韱               .xdata             顔             @0@.pdata             � �        @0@.xdata             0� D�        @0@.pdata             b� n�        @0@.xdata             寚 渿        @0@.pdata             簢 茋        @0@.xdata             鋰 鴩        @0@.pdata             � "�        @0@.xdata             @� P�        @0@.pdata             n� z�        @0@.xdata             槇             @0@.pdata              皥        @0@.xdata             螆             @0@.pdata             趫 鎴        @0@.xdata             �             @0@.pdata             � $�        @0@.xdata             B�             @0@.pdata             N� Z�        @0@.xdata             x�             @0@.pdata             �� 寜        @0@.rdata             獕 聣        @@@.rdata             鄩             @@@.rdata             驂 
�        @@@.rdata             (� @�        @@@.rdata             ^�             @@@.xdata$x           s� 強        @@@.xdata$x            繆        @@@.data$r         /   輮 �        @@�.xdata$x        $   � :�        @@@.data$r         $   N� r�        @@�.xdata$x        $   |� 爧        @@@.data$r         $   磱 貗        @@�.xdata$x        $   鈰 �        @@@.rdata             �             @@@.rdata$r        $   *� N�        @@@.rdata$r           l� ��        @@@.rdata$r           妼 枌        @@@.rdata$r        $   爩 膶        @@@.rdata$r        $   貙 鼘        @@@.rdata$r           � .�        @@@.rdata$r           8� L�        @@@.rdata$r        $   `� 剭        @@@.rdata$r        $   槏 紞        @@@.rdata$r           趰 顛        @@@.rdata$r           鴯 �        @@@.rdata$r        $   2� V�        @@@.rdata             j�             @0@.rdata             n�             @0@.rdata             r�             @0@.rdata             v�             @0@.rdata             z�             @0@.debug$S        4   ~� 矌        @B.debug$S        4   茙 鷰        @B.debug$S        @   � N�        @B.debug$T        窸 b�             @ B.chks64         �  �              
     /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   r  \     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReGIR.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $?A0x23112da8  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $_Binary_hypot   �   �
  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable S �   std::_Trivial_cat<float,float,float &&,float &>::_Same_size_and_compatible P �   std::_Trivial_cat<float,float,float &&,float &>::_Bitcopy_constructible M �   std::_Trivial_cat<float,float,float &&,float &>::_Bitcopy_assignable { �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Same_size_and_compatible x �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Bitcopy_constructible u �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Bitcopy_assignable � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Same_size_and_compatible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Bitcopy_constructible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Bitcopy_assignable . "    std::integral_constant<bool,0>::value : �    std::integral_constant<unsigned __int64,0>::value )     std::_Invoker_functor::_Strategy ,    std::_Invoker_pmf_object::_Strategy -    std::_Invoker_pmf_refwrap::_Strategy -    std::_Invoker_pmf_pointer::_Strategy ,    std::_Invoker_pmd_object::_Strategy -    std::_Invoker_pmd_refwrap::_Strategy -    std::_Invoker_pmd_pointer::_Strategy 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified : 1   std::_Floating_type_traits<float>::_Mantissa_bits : 1   std::_Floating_type_traits<float>::_Exponent_bits D 1   std::_Floating_type_traits<float>::_Maximum_binary_exponent E 1   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : 1   std::_Floating_type_traits<float>::_Exponent_bias 7 1   std::_Floating_type_traits<float>::_Sign_shift ; 1   std::_Floating_type_traits<float>::_Exponent_shift : 2  � std::_Floating_type_traits<float>::_Exponent_mask E 2  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G 2  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J 2  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B 2  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F 2  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; 1  5 std::_Floating_type_traits<double>::_Mantissa_bits ; 1   std::_Floating_type_traits<double>::_Exponent_bits E 1  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G 1  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; 1  �std::_Floating_type_traits<double>::_Exponent_bias 8 1  ? std::_Floating_type_traits<double>::_Sign_shift < 1  4 std::_Floating_type_traits<double>::_Exponent_shift ; 3  �std::_Floating_type_traits<double>::_Exponent_mask J 3  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L 3  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O 3  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G 3  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K 3  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask  �   ^-  $ �    std::strong_ordering::equal ' 7   std::_Comparison_category_none * 7   std::_Comparison_category_partial ' 7   std::_Comparison_category_weak ) 7    std::_Comparison_category_strong ( �  ��I@`anonymous namespace'::c_pi B �   std::allocator<float>::_Minimum_asan_allocation_alignment : 9   std::integral_constant<unsigned __int64,2>::value R �   std::allocator<ReGIR_OnionLayerGroup>::_Minimum_asan_allocation_alignment L �   std::allocator<ReGIR_OnionRing>::_Minimum_asan_allocation_alignment  U    std::denorm_absent  U   std::denorm_present  Z    std::round_toward_zero  Z   std::round_to_nearest # U    std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $ Z    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) U   std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized * Z   std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 z �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Same_size_and_compatible w �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Bitcopy_constructible t �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Bitcopy_assignable - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Same_size_and_compatible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Bitcopy_constructible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Bitcopy_assignable .    std::integral_constant<bool,1>::value  t   int32_t  �  _CatchableType  #   rsize_t  �  _TypeDescriptor  �  _s__CatchableType    rtxdi::ReGIRContext $ �  rtxdi::ReGIRDynamicParameters  �  rtxdi::float3 1 �  rtxdi::LocalLightReGIRFallbackSamplingMode # �  rtxdi::ReGIRStaticParameters ' �  rtxdi::RISBufferSegmentAllocator ' �  rtxdi::ReGIRGridStaticParameters ( �  rtxdi::ReGIROnionStaticParameters  �  rtxdi::ReGIRMode  �  rtxdi::uint3 , �  rtxdi::ReGIROnionCalculatedParameters + �  rtxdi::ReGIRGridCalculatedParameters , �  rtxdi::LocalLightReGIRPresamplingMode  #   uint64_t  p  va_list D >  std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >  ^  std::_Num_base # U  std::numeric_limits<char8_t> & �  std::allocator<ReGIR_OnionRing>  p  std::less<void>  a  std::_Num_int_base ,   std::allocator<ReGIR_OnionLayerGroup>  U  std::float_denorm_style = D  std::_Default_allocator_traits<std::allocator<float> >     std::_Compare_t " �  std::numeric_limits<double>  <  std::_Num_float_base   A  std::numeric_limits<bool>   �  std::_Fake_proxy_ptr_impl * x  std::numeric_limits<unsigned short> % `  std::_One_then_variadic_args_t  '  std::false_type  Z  std::float_round_style  �  std::weak_ordering , �  std::numeric_limits<unsigned __int64> $ Z  std::numeric_limits<char16_t> % 
  std::integral_constant<bool,1>   �  std::_Leave_proxy_unbound b Q  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> � �  std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1>  /  std::_Iterator_base12 C U  std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >  t  std::plus<void> = '  std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >   7  std::_Comparison_category # d  std::numeric_limits<wchar_t>  �  std::_Container_base0 & m  std::bidirectional_iterator_tag % '  std::integral_constant<bool,0>  6  std::bad_exception & \  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator 3 \  std::_Vector_val<std::_Simple_types<float> > ! �  std::numeric_limits<float> G 
  std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> >  �  std::exception_ptr  �  std::strong_ordering =   std::_Uninitialized_backout_al<std::allocator<float> > $ _  std::numeric_limits<char32_t>  !  std::exception  �  std::_Iterator_base0  (  std::_Container_base12 ) P  std::numeric_limits<unsigned char>  
  std::true_type   s  std::numeric_limits<long>    std::_Invoker_strategy $ �  std::_Default_allocate_traits P �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > f �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy ! i  std::numeric_limits<short>  M  std::bad_alloc # e  std::numeric_limits<__int64> !   std::pair<float *,float *> >   std::allocator_traits<std::allocator<ReGIR_OnionRing> >   j  std::forward_iterator_tag   �  std::bad_array_new_length v �  std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1>  $  std::_Container_proxy  �  std::nested_exception ( }  std::numeric_limits<unsigned int> 0 U  std::vector<float,std::allocator<float> > F 
  std::vector<float,std::allocator<float> >::_Reallocation_policy ' �  std::numeric_limits<long double> M ;  std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >    std::nullptr_t & p  std::random_access_iterator_tag ) �  std::numeric_limits<unsigned long> K �  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > ' K  std::numeric_limits<signed char>  �  std::_Container_base G �  std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > M �  std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> >   F  std::numeric_limits<char>  -  std::allocator<float> 4 G  std::allocator_traits<std::allocator<float> >  f  std::input_iterator_tag c �  std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > W }  std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > # s  std::contiguous_iterator_tag  �  std::partial_ordering Q �  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > >  n  std::numeric_limits<int>  e  std::bad_variant_access D �  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > Z i  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy 
    _off_t 
 !   _ino_t  �  terminate_handler    ReGIR_OnionLayerGroup  �  _CatchableTypeArray     ptrdiff_t  �  _PMD  t   errno_t     __time64_t  �  ReGIR_OnionRing  �  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray 
 #   size_t 
    time_t    __std_exception_data 
 u   _dev_t  u   uint32_t   �   p
      v�%啧4壽/�.A腔$矜!洎\,Jr敎  K    娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �    D���0�郋鬔G5啚髡J竆)俻w��  �    c�#�'�縌殹龇D兺f�$x�;]糺z�  :   +4[(広
倬禼�溞K^洞齹誇*f�5  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎     ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  U   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   鹴y�	宯N卮洗袾uG6E灊搠d�      豊+�丟uJo6粑'@棚荶v�g毩笨C  c   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  J   �
bH<j峪w�/&d[荨?躹耯=�  �   �0�*е彗9釗獳+U叅[4椪 P"��  �   +椬恡�
	#G許�/G候Mc�蜀煟-     {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  D   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷     匐衏�$=�"�3�a旬SY�
乢�骣�  P   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  0   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  }   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   _O縋[HU-銌�鼪根�鲋薺篮�j��     っ8OI枣鮌赊jr�燳;鞾孌j  B   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   �%逽|犟�1剝%sh鵺K媡簂蹶#楎`{w  (	   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  q	   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �	   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �	   繃S,;fi@`騂廩k叉c.2狇x佚�  6
   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  t
   檒Gq$�#嗲RR�錨账��K諻刮g�   �
   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �
   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  @   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  
   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  W   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  G
    d蜯�:＠T邱�"猊`�?d�B�#G騋  �
   溶�$椉�
悇� 騐`菚y�0O腖悘T  �
   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     `k�"�1�^�`�d�.	*貎e挖芺
脑�  [   �"睱建Bi圀対隤v��cB�'窘�n  �   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  6   蜅�萷l�/费�	廵崹
T,W�&連芿  s   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   泽閇�R鯄呙+困胢p=�R刐鉍籫�8[     -�
�捂�
y�*犯丁�02?栕9/�Q  K   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1      ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  j   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   �*o驑瓂a�(施眗9歐湬

�  0    I嘛襨签.濟;剕��7啧�)煇9触�.  p   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   交�,�;+愱`�3p炛秓ee td�	^,  3   _臒~I��歌�0蘏嘺QU5<蝪祰S  x   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  ?   a�傌�抣?�g]}拃洘銌刬H-髛&╟  }   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��     п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  p   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  ;   �	玮媔=zY沚�c簐P`尚足,\�>:O  |   G�膢刉^O郀�/耦��萁n!鮋W VS  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  S   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   �      k  X  B   l  X  H   m  X  Y   p  X  �   �  �   U   �  �   �   �  �  ,   �  �  4   �  �  �   �  �  �   �  �   S  �  �     �  �   f  �  �   �  �  �   `  �  �   S  �  �   f  �  �   �  �  �   �  �  �   `  �  �   S  �  �   f  �  �   �  �  �   �  �  �   `  �  �   *   �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �  K   �  �  K   �  �  �  �     ,   �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �     �  �   �    �       �   �    �   �     �   �    �   �    �   �    �   �    �   �     �   �  #  �   �  *  �  �  8        >  �     G  �     `  �     k  �     �  �     �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   ]  �  �   ]  �  �   ]  �  �  �   �  X  �   �  �   �  �  �   �  �  �   �  �  X  �   �  �   �  �  �   �  �  X  �     X  �     �   �    �   �    X  �  #  �   @   &  �   �   (  �   @   ,  �   �  0  �  �  4  �  �  8  �  �  D  X  �   H  X  �  Y  X  �   h  �   �  r  X  �   �  �   �  �  �   �   �  �   @   �  x
  �	  �  X  3  �  �  ,  �  �  T  �  �  �   �  �   �  �  �   �  �  X  �   �  �   �  �  X  �   �  X  �  �  �  �  �  �  I  �  �    �  X  �   �  X  �   �  X  �   �   �   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\Source\ReGIR.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\numeric C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h    �       L�  H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   �        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >�   _Bytes  AJ        9  $  >�    _Block_size  AH       1 
   >�    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        �  
 Z   �   N Z   �  x   (                      H 
 h   �         $LN14  0   �  O_Bytes  O   �   h           :   �   
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  H   w  H  
 �     �    
 H+袸嬂H+翷嬄H嬔H嬋�             �   Q  R G                      �        �std::_Copy_backward_memmove<float *,float *>  >@   _First  AJ          >@   _Last  AK          >@   _Dest  AH         AP          >�    _Count  AK                                H 
 h   L      @  O_First     @  O_Last     @  O_Dest  O   �   0              X     $       � �    � �   � �,      0     
 y      }     
 �      �     
 �      �     
 �      �     
 �      �     
 h     l    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   H  I G            0   
   %   H        �std::_Copy_memmove<float *,float *>  >@   _First  AJ          >@   _Last  AK          >@   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   L   0   @  O_First  8   @  O_Last  @   @  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 p      t     
 �      �     
 �      �     
 �      �     
 �      �     
 \     `    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   h  i G            0   
   %           �std::_Copy_memmove<ReGIR_OnionLayerGroup *,ReGIR_OnionLayerGroup *>  >�   _First  AJ          >�   _Last  AK          >�   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   �   0   �  O_First  8   �  O_Last  @   �  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 |     �    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   \  ] G            0   
   %   �        �std::_Copy_memmove<ReGIR_OnionRing *,ReGIR_OnionRing *>  >�   _First  AJ          >�   _Last  AK          >�   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   e   0   �  O_First  8   �  O_Last  @   �  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 p     t    
 L塂$H塋$SVWATAUAVAWH冹 L嬧H孂L�	L孃M+鵌�H婣I+罤柳I�������?I;�凥  L峢H婭I+蒆六H嬔H殃I嬂H+翲;��  H�
M嬽I;臠C餗;��  J�4�    L塼$hH侢   r)H峃'H;�嗕   �    H吚勨   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL塼$h�3跮塼$hH塡$xN�4籋婦$p� A�L婫H�H嬎M;鄒L+码M嬆L+妈    I峃L婫M+腎嬙�    怘�H吷t1H媁H+袶冣麳侜   rH兟'L婣鳬+菻岮鳫凐w:I嬋�    H�J�獺塐H�H塐I嬈H兡 A_A^A]A\_^[描    惕    惕    泰   �    �   �         (     b  �    �  �    �  �    �  �       �   	  s G            �     �  A        �std::vector<float,std::allocator<float> >::_Emplace_reallocate<float const &> 
 >�   this  AJ          AM       j  D`    >6   _Whereptr  AK          AT       �l  >L   <_Val_0>  AH  �       AP        A  Dp    >#     _Newcapacity  AV  u     {  AV �        Bh   �        >�    _Newsize  AU  N     P8 D  >�    _Whereoff  AW  %     y  ]
  >�    _Oldsize  AH  0     g  2 0 >6    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M        �  ur� M        ,  ur�& M        &  ��)
)%��( M        �  ��$	%)
��
 Z   x   >�    _Block_size  AJ  �       AJ �      >�    _Ptr_container  AH  �       AH �     �  � 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        �  
r

 N N N M        �  Nk >�    _Oldcapacity  AJ  R     �   M % z   AJ �     �  �  >�    _Geometric  AH  r     u ;  _   AH �     �  �  M        �  N N N M        �  
�� M        D  
�� N N M        �  �	 M        H  �	 >�    _Count  AP  �       AP '      N N M        �  �! >�   _Last  AP  !      >@   _Dest  AJ      
  AJ '      M        H  �! >�    _Count  AP  $      AP '      N N M        �  � M        H  � >
    _First_ch  AK        AK '      >�    _Count  AP        N N% M        �  �-h1#' M        �  *�<\ M          丂)7
 Z   �  
 >   _Ptr  AJ a      >#    _Bytes  AK  9    -    AK �     % M        �  両d#
:
 Z   �   >�    _Ptr_container  AP  Q      AP a    <  2  >�    _Back_shift  AJ  0    1  AJ a    <  +  N N N N
 Z   �               8         0@ � h   P  j  �  �  �  �  �      �  �  �  �  �  �  �  �  �  &  )  *  +  ,  D  H  L  O  �  �  �  �         $LN145  `   �  Othis  h   6  O_Whereptr  p   L  O<_Val_0>  O   �   �           �  �      �       * �   3 �,   4 �7   6 �J   : �N   ; �r   = ��   A ��   B ��   E �	  G �  K �  L �  N �-  V �y  W �|  X ��  = ��  7 ��  V ��   �  � F            (   
   (             �`std::vector<float,std::allocator<float> >::_Emplace_reallocate<float const &>'::`1'::catch$0 
 >�   this  EN  `         (  >L   <_Val_0>  EN  p         ( 
 Z   �                        � f        __catch$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z$0        $LN145  `   �  Nthis  h   6  N_Whereptr  p   L  N<_Val_0>  O   �   0           (   �      $       P �
   R �   S �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �    #  �   
 N  �    R  �   
 ^  �    b  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 .  �    2  �   
 B  �    F  �   
    �      �   
   �      �   
 9  �    =  �   
 I  �    M  �   
 l  �    p  �   
 |  �    �  �   
 ;  �    ?  �   
 W  �    [  �   
 �  �    �  �   
 �  �    �  �   
 :  �    >  �   
 J  �    N  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 _  �    c  �   
 o  �    s  �   
 �  �    �  �   
 $  �    (  �   
 E  �    I  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 �  :   �  :  
 ,	  �    0	  �   
 
     
    
 �
     �
    
 �
     �
    
 �
  ;   �
  ;  
 Q  :   U  :  
 �     �    
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      L塂$H塋$SVWATAUAVAWH冹 L嬧L嬹L�L嬍M+蔍猾*I嬅I鏖L嬯I笼I嬇H凌?L鐷婭I+蔍嬅H鏖H龙H嬄H凌?H蠭窾UUUUUUI;�劆  L峼I婲I+蔍嬅H鏖H龙H嬄H凌?H蠬嬍H验I嬂H+罤;�噁  H�I嬿I;荋C餓;�嘜  H�<vH羚H塼$hH�   r)H峅'H;��,  �    H吚�*  H峏'H冦郒塁#H�tH嬒�    H嬝H塂$xH塼$h�3跦塼$hH塡$xJ�4m    I鮄伶H驢婦$p HN@ F M婩I�H嬎M;鄒L+码M嬆L+妈    H峃0M婩M+腎嬙�    怣�M吚tSI婲I+菻斧*H鏖H龙H嬄H凌?H蠬�RH菱H侜   rH兟'I婬鳯+罥岪鳫凐wDL嬃I嬋�    I�K�H玲H薎塏H�I塏H嬈H兡 A_A^A]A\_^[描    惕    惕    替   �    
  �    q     �     �  �      �      �      �       �   �  � G            #     #  n        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Emplace_reallocate<ReGIR_OnionLayerGroup const &> 
 >[   this  AJ          AV       �  D`    >�   _Whereptr  AK          AT       �  >C   <_Val_0>  AH  @    5  AP        n  Dp    >#     _Newcapacity  AL  �       AL         Bh   �     TH  >n    _Newsize  AW  {     ��
 �  >n    _Whereoff  AU  ;       >�    _Newvec  AI          AI $    � � 
  Bx       
  �   M        �  u��乥 M        �  u��乥& M        &  ��)
)%��( M        �  ��$	%)
�
 Z   x   >�    _Block_size  AJ  �       AJ       >�    _Ptr_container  AH  �       AH $    �  � 
 >�    _Ptr  AI  �       AI $    � � 
  M        �  ��
 Z   �   N N M        �  �
 Z   �   N N M        (  
��

 N N N M        �  { >n    _Geometric  AH  �     u ;  _   AH $    �  �  M        �  { N N M          �; M        r  �; N N M          乪 M          乪 >�    _Count  AP  Z      AP �      N N M          亇 >�   _Last  AP  }      >l   _Dest  AJ  y    
  AJ �      M          亇 >�    _Count  AP  �      AP �      N N M          乯 >)    _UFirst  AK  ]      AK �      M          乯 >�    _Count  AP  m      N N% M        �  亯hS#' M        �  1伋j M          伝)A
 Z   �  
 >   _Ptr  AP �      >#    _Bytes  AK  �    )  AK      % M        �  伳d#
D
 Z   �   >�    _Ptr_container  AJ  �      AJ �    F  >  >�    _Back_shift  AP  �    P  AP �    F 5   N N N N
 Z   "               8         0@ � h   P  j  �  �  �  �  �      $  �  �  �  �  �  �  �  �  �            &  (  r  y  �  �  �         $LN145  `   [  Othis  h   �  O_Whereptr  p   C  O<_Val_0>  O  �   �           #  �      �       * �   3 �I   4 �d   6 �w   : �{   ; ��   = �$  A �)  B �V  E �e  G �h  K �j  L �u  N ��  V ��  W �  X �  = �  7 �  V ��   I  � F            (   
   (             �`std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Emplace_reallocate<ReGIR_OnionLayerGroup const &>'::`1'::catch$0 
 >[   this  EN  `         (  >C   <_Val_0>  EN  p         ( 
 Z   �                        � �        __catch$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z$0        $LN145  `   [  Nthis  h   �  N_Whereptr  p   C  N<_Val_0>  O   �   0           (   �      $       P �
   R �   S �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �      �   
 ?  �    C  �   
 O  �    S  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 +  �    /  �   
 ?  �    C  �   
 �  �      �   
   �      �   
 8  �    <  �   
 H  �    L  �   
 k  �    o  �   
 {  �      �   
 ;  �    ?  �   
 S  �    W  �   
 �  �    �  �   
   �    	  �   
 C  �    G  �   
 c  �    g  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 I  �    M  �   
 �  �    �  �   
 �  �      �   
   �      �   
 j  �    n  �   
 z  �    ~  �   
 �  �    �  �   
 �  �    �  �   
 �  @   �  @  
 �  �    �  �   
 �	  	   �	  	  
 p
  	   t
  	  
 �
  	   �
  	  
 �
  A   �
  A  
 �  @   �  @  
 �  	   �  	  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      L塂$H塋$SVWATAUAVAWH冹 L嬯H孂H�L嬧L+郘媞L+餓窿I�������M;�凮  I�艸婭H+菻六H嬔H殃I嬂H+翲;��&  H�
M孇I;芁C鳰;��  I嬿H伶L墊$hH侢   r)H峃'H;�嗧   �    H吚勲   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL墊$h�3跮墊$hH塡$xI冧餗�<H婦$p AL婫H�H嬎M;鑥L+码M嬇L+妈    I峅L婫M+臝嬚�    怘�H吷t1H媁H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I伶L驦墂H�H塐I嬊H兡 A_A^A]A\_^[描    惕    惕    蹋   �    �   �         (     b  �    �  �    �  �    �  �       �   )	  � G            �     �  J        �std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Emplace_reallocate<ReGIR_OnionRing const &> 
 >-   this  AJ          AM       �m  D`    >�   _Whereptr  AK          AU       �m  >   <_Val_0>  AH  �     &  AP        =  Dp    >#     _Newcapacity  AW  p     ~  AW �        Bh   �     	  >@    _Newsize  AV  I     X$" L  >@    _Whereoff  AT  %       >@    _Oldsize  AV  ,     o   L >�    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M        �  tm�" M        �  tm�"& M        &  ��)
)%��( M        �  ��$	%)
��
 Z   x   >�    _Block_size  AJ  �       AJ �      >�    _Ptr_container  AH  �       AH �     �  � 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        #  
m
 N N N M        �  Ik >@    _Oldcapacity  AJ  M     �   L % y   AJ �     � # �  >@    _Geometric  AH  m     t :  ^   AH �     �  �  M        �  I N N M        �  �� M        Y  �� N N M        �  �	 M        �  �	 >�    _Count  AP  �       AP '      N N M        �  �! >�   _Last  AP  !      >i   _Dest  AJ      
  AJ '      M        �  �! >�    _Count  AP  $      AP '      N N M        �  � >"    _UFirst  AK        AK '      M        �  � >�    _Count  AP        N N% M        �  �-h1#' M        �  *�<_ M          丂):
 Z   �  
 >   _Ptr  AJ a      >#    _Bytes  AK  9    -    AK �     % M        �  両d#
=
 Z   �   >�    _Ptr_container  AP  Q      AP a    ?  5  >�    _Back_shift  AJ  0    1  AJ a    ?  #  N N N N
 Z                  8         0@ � h   P  j  �  �  �  �  �      !  e  �  �  �  �  �  �  �  �  �  �  �  �  �  #  &  Y  `  �  �  �         $LN145  `   -  Othis  h   �  O_Whereptr  p     O<_Val_0>  O   �   �           �  �      �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   A ��   B ��   E �	  G �  K �  L �  N �-  V �|  W �  X ��  = ��  7 ��  V ��     � F            (   
   (             �`std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Emplace_reallocate<ReGIR_OnionRing const &>'::`1'::catch$0 
 >-   this  EN  `         (  >   <_Val_0>  EN  p         ( 
 Z   �                        � �        __catch$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z$0        $LN145  `   -  Nthis  h   �  N_Whereptr  p     N<_Val_0>  O �   0           (   �      $       P �
   R �   S �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    
  �   
 -  �    1  �   
 =  �    A  �   
 l  �    p  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
 0  �    4  �   
 D  �    H  �   
 X  �    \  �   
   �      �   
 &  �    *  �   
 O  �    S  �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 Q  �    U  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
 P  �    T  �   
 `  �    d  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 	  �    
  �   
   �      �   
 Y  �    ]  �   
 i  �    m  �   
 �  �    �  �   
 8  �    <  �   
 Y  �    ]  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 �  =   �  =  
 @	  �    D	  �   
  
  
   $
  
  
 �
  
   �
  
  
 �
  
   �
  
  
   >     >  
 �  =   �  =  
 (  
   ,  
  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      �	I嬂H+罬嬋H柳H凐(�2  H�繦柳L��    H��    �/葀	��驛�/葀驛��/衯	���L嬄L+荔A/葀	�
驛 ��
/葀��驛/衯	�驛 M嬃M嬔L+繫+芋A 驛
/葀
驛驛驛驛/葀驛	驛 驛/衯
驛驛��/葀	�
�驛 �
/葀V驛��/衯C��皿/葀�
�驛 �
/葀驛��/衯���   �   �  \ G            �      �  �        �std::_Guess_median_unchecked<float *,std::less<void> >  >@   _First  AJ        �
 >@   _Mid  AK        � >@   _Last  AP        
  AQ  
     z >p   _Pred  AY        
  D     >F    _Count  AH          / AH �      >F    _Step  AH  "        M        �  �.i.I	
 >@   _Last  AP  �       M        �  � N M        �  � N M        �  �" N M        �  �0 N M        �  �9 N M        �  丆 N N  M        �  ��/j/J

 >@   _First  AR  �     �  AR �     
 >@   _Mid  AP  �     �  AP �      M        �  
�� N M        �  �� N M        �  
�� N M        �  �� N M        �  �� N M        �  � N N" M        �  r
*i.I	
 >@   _First  AP  y     C  M        �  r
 N M        �  �� N M        �  �� N M        �  �� N M        �  �� N M        �  �� N N! M        �  2*iK		 M        �  2 N M        �  < N M        �  E N M        �  U N M        �  ` N M        �  i N N  M        �  丮)h.I	 M        �  丮 N M        �  乂 N M        �  乛 N M        �  乴 N M        �  乽 N M        �  亊 N N                        @  h   �  �  �      @  O_First     @  O_Mid     @  O_Last      p  O_Pred     _Diff  O   �   h           �  �  
   \        �     �    �"    �r    ��     �  ! �L  % �M  # ��  % �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 3     7    
 S     W    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 H塡$H塼$H墊$AVH冹 I嬂H嬺H+翴孁H柳L嬹I兝麳嬑H�侶嬘�    H峉H;髎*��     �C麳岰�/葁/羨H嬝(菻;餽釮;譻��/葁/羨	H兟H;譺镠嬍L嬎H;��'  H嬊H+罤兝H冟麳凐傏   L岮�    ��	/葁/�囶   H;裻��
�H兟驛H麵岪/葁/��9  H;衪��
� H兟�驛/葁/��  I;衪
��
驛 H兟驛HI岪�/葁/�囒   H;衪��
� H兟H兞H岹鬒兝H;��1���H;蟬0��	/葁/羨H;裻��
�H兟H兞H;蟫蠰;蝪>I岮� ��/羨/葁"H冸H;豻��� I冮H冭I;駌蘈;蝩GH;�剫   H;裻�����H兟��H兠�H兞門��H嬋閨���I嬋閠���I兞麳;蟯-H冸L;藅驛�A��婤麳冴�������A��H兞驛轺��H媡$8I嬈H媩$@I�H媆$0I塚H兡 A^�7         �   �  i G            �     �  <        �std::_Partition_by_median_guess_unchecked<float *,std::less<void> >  >@   _First  AK          AL       � >@   _Last  AM  !     � AP        !  >p   _Pred  AY        ;  >@    _Plast  AK  ?     ~
 >@    _Mid  AI  3       AI P     b  � �
 
)  >@    _Pfirst  A�   i       AI  f       }�< � * A�  P     m  '  x � L( �3 � 
)  AI �     R �
 �)  >@    _Glast  AQ  �    	  AQ �     )g	 �I  >@    _Gfirst  AJ  �     , M        �  ^ N M        �  P N M        �  �� N M        �  s N  M        �  ��'1-O N  M        �  ��1,2E NF M        �  ��$)$$$*$9$
 >@     _Tmp  A�   �     �  % =   j % � 5 : A�  �     � ' 9 # ` ) � # � # / [ g) �	 � �  N M        �  佇 N M        �  佪 N M        �  侂$
 >@     _Tmp  A�   �     & A�  �    �   K  W ) � 	 �  �   N M        �  偑 N M        �  �$
 >@     _Tmp  A�         A�  #      N M        �  �#
 >@     _Tmp  A�   '     . A�  �     )0 � , D/ � �) �	 � �  N M        �  俠%
 >@     _Tmp  A�   g    	  A�  p      N M        �  俻7$
 >@     _Tmp  A�   {     . A�  �     )0 � , D/ � �) �	 � �  N M        �  倖	
 >@     _Tmp  A�   �     . A�  �     )0 � , D/ � �) �	 � �  N
 Z   �                         @  h   �  �  �  �  �   8   @  O_First  @   @  O_Last  H   p  O_Pred  O �              �  �  A         ( �   * �(   + �;   - �?   0 �c   1 �n   4 ��   5 ��   8 ��   9 ��   < ��   = ��   ? ��   A ��   B ��   = ��   ? �  A �  B �  = �*  ? �3  A �8  B �E  = �\  ? �e  A �j  B �v  E ��  < ��  = ��  ? ��  A ��  B ��  < ��  I ��  K ��  M ��  O ��  P ��  I �  T �  Y �  Z �#  ^ �'  ] �+  ^ �/  _ �3  ^ �7  ` �@  I �H  ? �P  a �Y  b �b  c �p  f ��  g ��  h ��  i ��  h ��  k ��  l ��  U ��  l ��  U ��  l �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 0     4    
 @     D    
 r     v    
 �     �    
 �     �    
 �     �    
 �     �    
          
 1     5    
 I     M    
 i     m    
          
          
 �     �    
 �     �    
 �     �    
 �     �    
 L     P    
 \     `    
 �     �    
 �     �    
          
       $    
 �     �    
 H塡$H塴$H塼$WAVAWH冹@H嬄A顿H+罥嬸H冟麳嬯H孂H=�   帊   D  H咑幰   D端H峀$ L嬇H嬜�    L媩$ H嬑L媡$(I嬊H六H+荋瑶H冟麳馜端H嬐L嬈I+蜨冡麳;羮I嬜H嬒�    I孇�H嬚I嬑�    I嬶H嬇H+荋冟麳=�   弜���H;��  L峸I嬣L;��  )t$0�   D  �3H嬎�/�啩  L嬈H嬜I嬑�    �7榫  L嬚L+譏龙M嬄I养M吚帹   M岼�I样D  驜L圏I�菼嬋I嬓M;羮3fff�     H�3殷D�/D�柭H�翲袐棄廐嬍I;褆豂;蓇A雎uB婦楛�廔岼�L;羮*@ �     H岮�H养��/葀
�廐嬋L;纜怏廙吚廳���I凓�	  �   L峌麳+黧A
M嬄�L+荌柳3�3褹�M岺�I样M吷~2ff�     H�3殷D�/D�柭H�翲袐棄廐嬍I;褆豂;蓇A隼uB婦圏�廔岺�H吷~*@ �     H岮�H养��/葀
�廐嬋H吚釯冴�廕�H冟麳凐岻���隓H岶麳求 /苬D  �H嬋�@麳冭/苭塍1H兠H兤H;����(t$0H媆$`H媗$hH媡$pH兡@A_A^_肶      �       �               �   .	  T G                 �  �        �std::_Sort_unchecked<float *,std::less<void> >  >@   _First  AJ        /  AM  /     � >@   _Last  AK        ,  AN  ,     � >    _Ideal  AL  %     �R  � 0  AP        %  AL �      >p   _Pred  A        �� C  AY          A  �     
 >   _Mid  CW      b     f  CV     j     ^  CW     @     �" f  CV    @     �* ^ � F kD  D    < M        0  ��	G-.仌7
 >@    _Mid  AI  �     C � AI �      >@    _Hole  AJ  �     � � AJ �      �
 >@     _Val  A�   �     �' � A�  �       >@    _Prev  AH  �    &    AH �     " �� �  M        �  �� N M        �  � M        �  � N N M        �  偛	 N N) M        8  
佨$D.P$! M        �  侐D)P"
 >@     _Val  A�   �    �  A�  �     � % M        �  侘DP"8 M        �  �/h+(" >    _Hole  AJ      k  AJ �     k �  � D  >    _Bottom  AP  �    �    AP �     �  >C    _Max_sequence_non_leaf  AQ      �  AQ �     � 
 >    _Idx  AK        AK �      4 " � D  C       &     & M        �  俖'J >    _Hole  AJ  �      AJ �     k �  � D 
 >     _Idx  AH  w      AH p    +    M        �  倃 N N N N N N+ M        4  �

DP		# >     _Bottom  AR      �    AR �      >     _Hole  AP  (    �  AP �     � 2 M        �  丮
h+(# >    _Hole  AJ  M    c  AJ @    �
 c �  � �  >C    _Max_sequence_non_leaf  AQ  8    �  AQ �    ,2 � 
 >    _Idx  AK  P      AK @    �  & " � �  C       f     # M        �  仧'J% >    _Hole  AJ  �      AJ @    �
 c �  � � 
 >     _Idx  AH  �      AH @    �$ $ X  t  � �  M        �  伔 N N N N Z   <  �  �   @                     @ 6 h   P  0  4  8  L  �  �  �  �  �  �  �   `   @  O_First  h   @  O_Last  p      O_Ideal  x   p  O_Pred        O_Mid  O  �   �             �     �       o �   r �@   w �I   ~ �]   � ��   � ��   � ��   � ��   � ��   � ��   r ��   s �  x ��  y ��  s ��  � �,       0      
 {             
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
            
 $      (     
 8      <     
 H      L     
 k      o     
       �     
 �      �     
 �      �     
             
 0      4     
 P      T     
 d      h     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 O      S     
 _      c     
 �      �     
 �      �     
 �      �     
 �      �     
 	      
     
            
 9      =     
 �      �     
 �      �     
 �      �     
 �      �     
 M      Q     
 a      e     
 �      �     
 �      �     
 �      �     
 �      �     
 6      :     
 F      J     
 i      m     
 y      }     
 �      �     
 �      �     
 �      �     
            
 )      -     
 D	      H	     
 H塡$H塼$H塋$WH冹 I孁H嬞3鰤1A�J�I婤堿H茿   �?H塹(茿0   茿4   茿8  �?H茿<   塹DH塹HH塹PH塹XH塹`H塹hH塹pH塹xH壉�   婣疉疉疉墎�   �    H嬎�    婯吷t+冮t凒u!婼@H嬒�    ��嫇�   H嬒�    ���3H嬅H媆$8H媡$@H兡 _脷   �    �   �    �   �    �   �       �   �  G G            �      �   �        �rtxdi::ReGIRContext::ReGIRContext 
 >�   this  AI       �  AJ          D0    >�   params  AK        � " >   risBufferSegmentAllocator  AM       �  AP         " M        �  ��	 Z   �  �   N M        �  �� N M        �  m M        �  m M          m N N N M        �  a M          a M          a N N N Z   �  �                        @ > h   �  �  �  �  �  �  �  �  �             0   �  Othis  8   �  Oparams & @     OrisBufferSegmentAllocator  O �   `           �   �  	   T       $  �   "  �   #  �a   $  ��   %  ��   &  ��   '  ��   (  ��   )  ��   �   V F                                �`rtxdi::ReGIRContext::ReGIRContext'::`1'::dtor$0 
 >�   this  EN  0                                  �  O   ,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
       $    
 s     w    
 H媻0   H兞@�       �    H塡$H塴$H塼$ H塋$WAVAWH冹 L嬺H嬹��婤堿3鞨塱H塱H塱H婮H+JI揩*I嬊H鏖H龙H嬄H凌?H�劘   H窾UUUUUUH;�噚  H�RH零H侞   r,H岾'H;�哷  �    H嬋H吚刄  H兝'H冟郒塇H呟t
H嬎�    �H嬇H塅H塅H�H塏H媬I媈I媀H+贚嬅H嬒�    I嬊H麟H龙H嬄H凌?H蠬�RH拎H荋塅H塶 H塶(H塶0I媈(I+^ H聋H呟剫   H�������H;�嚭   H零H侞   r%H岾'H;�啨   �    H吚t~H峢'H冨郒塃H呟tH嬎�    H嬭H塶 H塶(H�+H塅0H媬 I媈(I媀 H+贚嬅H嬒�    H冦餒�;H塅(A婩8塅8A婩<塅<H嬈H媆$HH媗$PH媡$XH兡 A_A^_描    愯    惕    惕    愯    惕    虣   �    �   �    �      e  �    �  �    �     �  �    �  �    �  �    �  �      �      �       �   �	  k G                   �        �rtxdi::ReGIROnionCalculatedParameters::ReGIROnionCalculatedParameters 
 >�   this  AJ        #  AL  #     ��
  D@    >�   __that  AK           AV        �� # M        �  �L���� >@    _Count  AI  &    �   ( �  AI �     % M        �  �.)&eP& M          �7R��
 Z     & M           丣B$X >�   _Newvec  AN �    Q  C       /     �  C      �     Y� 
 &  M        �  B丣�� M        �  B丣��& M        &  丯)
%
k, M        �  乄$	%%v	 Z   �  x   >�    _Block_size  AJ  [    �  �  >�    _Ptr_container  AH  i    �  p  AH �     
 >�    _Ptr  AN  v      AN �    Q  M        �  乨
 Z   �   N N M        �  亖
 Z   �   N N M        #  丣 N N N N N M        `  仱$ >   _First  AK  �      >�   _Last  AI  �      >i   _Dest  AH  �      AM  �      AH �      M        �  仺 >�    _Count  AI  �      N N N M        �  � M          � N N N" M        �  -N��&亼% M          a&=r�% M          g_亁
 Z   "  & M        #  zO$�' >�    _Newvec  AH  �       AH �     *  M        �  Oz亊 M        �  Oz亊) M        &  ��)
,%
�2- M        �  ��$	()
丟 Z   x  �   >�    _Block_size  AJ  �     n [ >�    _Ptr_container  AJ  �     c G AJ �      
 >�    _Ptr  AH  �       AH �     *  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        (  z N N N N N M        �  ��$ >�   _First  AK  �       >�   _Last  AI  �       >l   _Dest  AH      /  AM  �     5  AH �      M          �� >�    _Count  AI  �     >  N N N M        �  - M          - N N N                      0@ � h;   P  j  �  �  �  �  �  �  �  �  �  �  �  �  �  �                   !  #  $  X  \  `  e  |  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         #  &  (  h  �  �  �  �         $LN154  @   �  Othis  H   �  O__that  O �   �   z F                                �`rtxdi::ReGIROnionCalculatedParameters::ReGIROnionCalculatedParameters'::`1'::dtor$0 
 >�   this  EN  @                                  �  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 '  �    +  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
   �      �   
   �      �   
 :  �    >  �   
 J  �    N  �   
   �      �   
 '  �    +  �   
 G  �    K  �   
 W  �    [  �   
 g  �    k  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 I  �    M  �   
 �	  ,   �	  ,  
 
     
    
 
     �
    
 H媻@   H兞�       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   #  ? G            <      6   z        �std::bad_alloc::bad_alloc 
 >9   this  AI  	     2  AJ        	  >>   __that  AH         AK          M        l  :$
 Z   �   N                       H� 
 h   l   0   9  Othis  8   >  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   =  U G            <      6   y        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        l  :$
 Z   �   N                       @�  h   l  z   0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           s        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        p    M        k    N N                        @�  h   k  p      �  Othis  O   �   8           !   X     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �       �   �   ? G            2      ,   l        �std::exception::exception 
 >   this  AI  	     (  AJ        	  >   _Other  AH         AK         
 Z   �                         H�  0     Othis  8     O_Other  O �   0           2   X     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 @SH冹 H嬞H�	H吷t>H婼H+袶冣麳侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �   �  v G            [      [   �        �std::vector<float,std::allocator<float> >::~vector<float,std::allocator<float> > 
 >�   this  AI  	     R K   AJ        	 $ M        �  	h1%	
 M        �  *= M          )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        �  
%#

 Z   �   >�    _Ptr_container  AP  )     1    AP =       >�    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� & h   P  �  �  �  �  �             $LN30  0   �  Othis  O  �   8           [   �      ,       � �	   � �O    �U   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 8  �    <  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 n  6   r  6  
 �  �    �  �   
 �       �       �     � G                       �        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::~vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 
 >[   this  AJ         
 Z   �                          H�     [  Othis  O�   (              �             � �    � �,   �    0   �   
 �   �    �   �   
 ,  �    0  �   
 @SH冹 H嬞H婭 H吷t?H婼0H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁 H塁(H塁0H岾H兡 [�    �    �?   �    [   �    `   �       �   �  l G            e      e   �        �rtxdi::ReGIROnionCalculatedParameters::~ReGIROnionCalculatedParameters 
 >�   this  AI  	     \ Q   AJ        	  M        �  Z N M        �  H	V$ M        �  	i1&	 M        �  *F M          )!
 Z   �  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        �  
&#
$
 Z   �   >�    _Ptr_container  AP  *     :  !  AP >       >�    _Back_shift  AJ  
     W 1 !  AJ >         N N N N N                       @� . h
   P  �  �  �  �  �  �  �             $LN35  0   �  Othis  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 /  �    3  �   
 �  *   �  *  
 H�    H�H兞�       �      �       �   �   V G                      u        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        m   	
 N                        H�  h   m  r      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       �      �       �   �   @ G                      m        �std::exception::~exception 
 >   this  AJ         
 Z   �                          H�       Othis  O  �   (              X            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   q        �std::bad_alloc::`scalar deleting destructor' 
 >9   this  AJ          AM       -  M        m  

	
 Z   �   N                       @�  h   m  r   0   9  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �     ] G            B   
   4   t        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        m  

	
 Z   �   N                       @�  h   m  r  u   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   o        �std::exception::`scalar deleting destructor' 
 >   this  AJ          AM       -  M        m  

	
 Z   �   N                       @� 
 h   m   0     Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 @SH冹 D婣L嬍H嬞E吚t5A冭tA凐u)婹@I嬌�    �H兡 [脣憖   I嬌�    �H兡 [们    H兡 [�(   �    >   �       �   E  S G            V      P   �        �rtxdi::ReGIRContext::AllocateRISBufferSegment 
 >�   this  AI       E #  9   AJ         " >   risBufferSegmentAllocator  AK        
  AQ  
     I   5   Z   �  �                         H  0   �  Othis & 8     OrisBufferSegmentAllocator  O   �   H           V   �     <       4  �   5  �!   ?  �,   A  �4   <  �B   A  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 \  �    `  �   
 婣疉疉疉墎�   �   �   �   T G                      �        �rtxdi::ReGIRContext::ComputeGridLightSlotCount 
 >�   this  AJ                                 H     �  Othis  O  �   0              �     $       ,  �    -  �   1  �,   �    0   �   
 y   �    }   �   
 �   �    �   �   
 H嬆H塇USVWATAUAVAWH峢圚侅8  )p�)x楧)@圖)坸���D)恏���D)榅���D)燞���D)�8���D)�(���D)����L嬦W荔D$H3�H墊$8H墊$X�D$`E3鞮塴$0L塴$p3缐厛   H婭PH塎�M媩$HL墊$@W�L媡$PH媡$hL;��%  驞
    f�     E3鞥9o庣   驟$fAn�[葾(氰    驛Y驞Y鳨(黧DX痼EY袂D$     E媑3跠(譋呬幁  驟G 驞厴   Ic(鱄媴�   H婡`H塂$xE(梵EY�(氰    D(囿EY骟Dd$((氰    驛Y企厫   H羚L媩$x恌n�[�驛Y鴧踰E(蓦
D(唧E\軩(煮E?(氰    D(润EY藺(黎    (痼AY鰽(描    �Y痼D\�(氰    D(囿EY鍭(描    驛Y求D\�(氰    (Y綈   A(黎    (痼AY鰽(描    �Y痼\EY潴EY审EX狍Y�驞X�W繟.膚
W荔AQ碾	A(蔫    �D$$H岲$$H峀$ A/翲F馏D驞T$ �肏兦A(駻;荏D厴   驞d$(屜��W�驞
    L媩$@H媩$8L嫢�   I婰$PI+L$HH斧*H鏖H龙H嬄H凌?H��9厛   }X�
    A(畦    驛Y麦厫   L;鱰驛I兤L塼$P雖L崊�   I嬛H峀$H�    H媩$XH墊$8L媡$P際驟^煮D晲   H;t$0t驞H兤H塼$h�#L崊�   H嬛H峀$`�    H婦$pH塂$0H媡$hA�臙;o�&��媴�   H婱��缐厛   I兦0L墊$@L;�咈��L媗$0H媩$HI;%I嬣H+逪聋E3蒐嬅I嬛H嬒�    H央�熾(求AD$xH婰$`H;蝨H嬃�    �X8H兝H;苪篌
    菂�     �?H+馠窿W繦咑x驢*齐H嬈H谚冩H企H*荔X荔厛   H崊�   H崟�   /菻F麦^8驛|$|H吷t:L+镮笼J��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚈   �    怘�t5H婽$8H+譎冣麳嬊H侜   rH兟'H�鳫+荋兝鳫凐w[H嬒�    L崪$8  A(s鐰({谽(C菶(K窫(S‥([楨(c圗(玿���E(砲���E(籜���I嬨A_A^A]A\_^[]描    愯    惷   �   �      ]     u     �     �     �     �               )     :     v     �  �     �        P  �    �  �          A  �   �  �      �    o  �    u  �       �     R G            z  c   z  �        �rtxdi::ReGIRContext::ComputeOnionJitterCurve 
 >�   this  AH  G      AJ          AT  f     � �  B�       H >t     layerGroupIndex  A   �     �P �x
  A  �     � F�  B�  �     � >U   cubicRootFactors  CV     �     �  i�%  CV    �     �i � �	  DH    >U    linearFactors  D`    >�    <begin>$L0  AW  �     �� C�  B@   �     � >�    <end>$L0  AJ  �     �F �i  AJ �       B�   �     � >t     layerIndex  Am  �      Am �       >�    innerRadius  A�   �     `  A�  �     E" �IL � 8
  >�    middleRadius  A      � A �     �5 ��  >@     maxCellRadius  D     >�    outerRadius  A       � A  �     � ��  >t     ringIndex  A       � A  �     �M � " �  >�    middleElevation  A�   �    �  A  Y    7  A �      >�    cellRadius  D$    >�    vertexAzimuth  A  �    �  >�    vertexElevation  A  �        A �    �  A �     ��  q-  >@     cubicRootFactor  B�  �     �� Q�:  >@     linearFactor  A  j    	  B�  �     �� Q[:  M        �  { M        �  { M          { N N N M        �  i
5 M        �  i
5 M          i N N N M        �  
個 N! M        �  佽$6	
 >�   d  C      �    c  C         :  C�      F      C     K    
  N M        �  伿4& N  M        �  乊H4$ N M        �  ,傋 N M        �  )�+ M        �  
�+%
 Z   A   M        >  �0	 M        �  �0 N N N N M        �  :僺 M        �  
僺'#
 Z   A   M        >  儂	 M        �  儂 N N N N M        �  冺 M        *  冺
 Z   �   N N M        �  :勧�� M        �  勧5�� M        �  -勵�� M          匁)[
 Z   �  
 >   _Ptr  AH  �      AM  �    �
  AH       AM     O  >#   	 _Bytes  B8   v      AK  �    �   0 S  AM  �     m � C       q     _  C      �     m ��  M        �  匎d
e
 Z   �   >�    _Ptr_container  AH  	      AM        N N N N N M        �  >劉�� M        �  劉9�� M        �  2劗�� >�  	 _Count  B0   �     � AH  �    
  AU  �    ��  �  AH �     � � Cm      �     L  Cm     �     � �7 �   M          劰)��
 Z   �  
 >   _Ptr  AH  �      AJ  !    �  AH �      AJ �    � 5   >#    _Bytes  AK  �    � * �  M        �  劼d��
 Z   �   >�    _Ptr_container  AH  �      AJ  �      N N N N N M        �  剛 N M        �  凮 N M        �  
�
 M        8  

�	
 >@    _Val  A�   4    	  A�  0    k  	  >@    _UFirst  AH  )      AH =    L +   N N M        �  � M        �  � N N 8          @         @ � h.   P  j  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �          *  .  3  8  >  �  �  �  �  @  D         $LN193  �  �  Othis  H   U  OcubicRootFactors  `   U  OlinearFactors      @   OmaxCellRadius  $   �  OcellRadius  �  @   OcubicRootFactor  �  @   OlinearFactor  O   �   �          z  �  2   �      �  �i   �  �{   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �  �  �  �  �  �  �!  �  �*  �  �Y  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �$  �  �B  �  ��  �  ��  �  ��  �  ��  �  ��  �  �
  �  �+  �  �T  �  �e  �  �s  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �  �  �=  �  ��  �  ��   5  a F                                �`rtxdi::ReGIRContext::ComputeOnionJitterCurve'::`1'::dtor$0  >U    cubicRootFactors  EN  H           >U    linearFactors  EN  `           >@     maxCellRadius  EN              >�    cellRadius  EN  $                                  �  O   �   5  a F                                �`rtxdi::ReGIRContext::ComputeOnionJitterCurve'::`1'::dtor$1  >U    cubicRootFactors  EN  H           >U    linearFactors  EN  `           >@     maxCellRadius  EN              >�    cellRadius  EN  $                                  �  O   ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 	  �    
  �   
 8  �    <  �   
 T  �    X  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
   �      �   
 5  �    9  �   
 E  �    I  �   
 k  �    o  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
   �      �   
    �    $  �   
 L  �    P  �   
 \  �    `  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 !  �    %  �   
 5  �    9  �   
 E  �    I  �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 (  �    ,  �   
 8  �    <  �   
 L  �    P  �   
 \  �    `  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 -	  �    1	  �   
 =	  �    A	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �     
  �   
 
  �    
  �   
 (
  �    ,
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 /  �    3  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 '  �    +  �   
 O
  1   S
  1  
 (  �    ,  �   
 �  
   �  
  
 b  
   f  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 8     <    
 �     �    
 �     �    
 �     �    
 #     '    
 H崐H   �       �    H崐`   �       �    ��   �   �   M G                      �        �rtxdi::ReGIRContext::GetReGIRCellOffset 
 >   this  AJ                                 @       Othis  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 r   �    v   �   
 �   �    �   �   
 A H嬄I0J�   �   �   T G                      �        �rtxdi::ReGIRContext::GetReGIRDynamicParameters 
 >   this  AJ                                 @       Othis  O  �   0              �     $       �  �    �  �   �  �,   �    0   �   
 y   �    }   �   
 �   �    �   �   
 H媮�   H�H嬄�   �   �   [ G                   
   �        �rtxdi::ReGIRContext::GetReGIRGridCalculatedParameters 
 >   this  AJ                                 @       Othis  O   �   0              �     $       �  �    �  �
   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 婹呉t冴t	凓u婣@脣亐   �3烂   �   �   Q G                      �        �rtxdi::ReGIRContext::GetReGIRLightSlotCount 
 >   this  AJ                                 @       Othis  O �   X              �     L       �  �    �  �   �  �   �  �   �  �   �  �   �  �   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 @SH冹0H嬟H峇@H嬎�    H嬅H兡0[�   �       �   �   \ G                     �        �rtxdi::ReGIRContext::GetReGIROnionCalculatedParameters 
 >   this  AJ         
 Z   �   0                     @  @     Othis  O  �   0              �     $       �  �	   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 婣A�I�J塀H嬄�   �   �   S G                      �        �rtxdi::ReGIRContext::GetReGIRStaticParameters 
 >   this  AJ                                 @       Othis  O   �   0              �     $       �  �    �  �   �  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 L嬡I塖UVAUAVI峩菻侅  �   A){�9rL嬮�   E)揾���驞    D嬈DLBA�   D;翂uXA(鶧塽@AO�3缐L$ 塃P吷庉  I塠�蒊墈蠱塩萂墈繟)s‥)C圗)媥���E)沊���驞    E)���D)l$p驞-    塋$$�;羮A�   �D媧A�莊Dn艵[纅An螦(餉(荔AX躞A\�[审^�(畦    I媇hD(囿DY鏓(薍嬅荄$d    (企|$8I+E`H柳驞d$<塂$P驟^辱    嬈D塼$`檳t$4冣D�$3繟咙A�腍塂$(D塪$T塂$0A(鼠DL$XA(殷t$\�^華(荔A^皿L$@A(鼠^润A^洋L$(�T$H�D$,I;]pt��C塁塻I僂h�L岲$(H嬘I峂`�    A�   嬣E;�帓   fAn�[荔AY凌    驛Y黎    I婾h�   �,缐\$0A(�;�O饓t$4fn�[荔A^皿^润D$,�L$(I;Upt�
�B塟塺I僂h�L岲$(I峂`�    A�茘sE;�宷���媢XI婾P塡$LD墊$DI;UXt!D$8L$HD$XJB I僂P0�L岲$8I峂H�    D媢@兤婨PA(麳婾H�缷L$$A塃P塽XD驞塽@;D$ 屍��D(l$pD(�$�   D(�$�   D(�$�   D(�$�   (�$�   L嫾$�   L嫟$   H嫾$  H嫓$  (�$�   D(�$�   E塽DE痷E塽@H伳  A^A]^]�:   �   �   �   �   �   �      @     �  �              o  �    �  �       �   `  J G            x  5   L  �        �rtxdi::ReGIRContext::InitializeOnion 
 >�   this  AJ        (  AU  (     M >�   params  AK        x� DH   >@     innerRadius  A�   V     � >t     totalCells  An  L     ��  B@  Z      >t     numLayerGroups  A   -     H  A  L    ,  B    d      >t     layerGroupIndex  A   `     � � BP  g      >�    equatorialAngle  A  ?    � A �     S 
 >�   ring  C�      �    � R W  C�      4      C          [  C�     �    �  { �   C     �    � % [  D(    >�    layerCount  Ao  �     j   Ao �      
   >t     cellsPerLayer  A   �    �  >�    radiusRatio  A�   �     2 A�  �     %  >�    outerRadius  A      � A �     J  >   layerGroup  Cl     `    
  D8    >t     ringIndex  An  �    � % M        �  
 N M        �  -伒 M        �  
伒&
 Z   J   M        G  伝 M        �  伝 N N N N M        �  � N M        �  倕5
 M        �  
倕
&!
 Z   n   M        k  倶 M           倶
 >�   _Obj  AK  �    >  AK �      N N N N M        �  �*: M        �  
�:&
 Z   J   M        G  侽 M        �  侽 N N N N M        �  � N                      @ R h   P  j  �  �  �  �  �  �  �  G  k  �  �  �     T  Y  m  r   @  �  Othis  H  �  Oparams  (   �  Oring  8     OlayerGroup  O�   �          x  �  7   �      D  �   E  �   D  �"   E  �%   D  �(   E  �-   D  �5   G  �>   E  �F   J  �L   E  �R   G  �V   J  �Z   E  �g   L  ��   O  ��   Q  ��   R  �  V  �  R  �  S  �  V  �  U  �  Y  �'  V  �/  X  �?  Y  �D  \  �F  ^  �L  a  �j  b  ��  c  ��  d  ��  e  ��  g  ��  h  ��  j  �  n  �  j  �  k  �   l  �$  j  �1  k  �4  l  �C  m  �I  n  �s  h  �v  p  ��  u  ��  s  ��  t  ��  u  ��  y  ��  L  �L    �,   �    0   �   
 o   �    s   �   
    �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 4  �    8  �   
 D  �    H  �   
 T  �    X  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 /  �    3  �   
 K  �    O  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 Q  �    U  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 t  �    x  �   
 儁4t	儁0t2烂��   �   �   U G                      �        �rtxdi::ReGIRContext::IsLocalLightPowerRISEnable 
 >   this  AJ                                 @       Othis  O �   @              �     4        �     �    �    �    �,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 A JI0�   �   �   O G                      �        �rtxdi::ReGIRContext::SetDynamicParameters 
 >�   this  AJ          >
   regirDynamicParameters  AK                                 @     �  Othis #    
  OregirDynamicParameters  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 t   �    x   �   
 �   �    �   �   
   �      �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       �            �   �   F G                       x        坰td::_Throw_bad_array_new_length 
 Z   s   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �      �     
 �   �    �   �   
 @SH冹 H嬞H�	H吷t]H婼H斧*H+袶麝H龙H嬄H凌?H蠬�RH菱H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    蘛   �    u   �       �   T  v G            z      z   �        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Tidy 
 >[   this  AI  	     q j   AJ        	  M        �  .3A M          ;)
 Z   �  
 >   _Ptr  AJ \       >#    _Bytes  AK  ;     > &  " M        �  
D#

 Z   �   >�    _Ptr_container  AP  H     1    AP \       >�    _Back_shift  AJ       m P   AJ \       
  N N N                       @� " h   P  �  �  �  �             $LN27  0   [  Othis  O�   X           z   �      L       � �	    �    �a   	 �f   
 �j    �n   
 �t    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 3  �    7  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 <  "   @  "  
 h  �    l  �   
 H冹(H�
    �    �   �      �       �   �   Y G                     �        坰td::vector<float,std::allocator<float> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   8   �   8  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   y G                     "        坰td::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   $   �   $  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   m G                             坰td::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   (   �   (  
 �   �    �   �   
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   O  G G            B      B   �        �std::allocator<float>::deallocate 
 >&   this  AJ          AJ 0       D0   
 >(   _Ptr  AK          >�   _Count  AP        A   M          )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        �  
#

 Z   �   >�    _Ptr_container  AJ       %    AJ 0       >�    _Back_shift  AH          AH 0       N N (                      H  h   P  �           $LN20  0   &  Othis  8   (  O_Ptr  @   �  O_Count  O �   8           B   �      ,       � �   � �3   � �7   � �,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    	  �   
 &  �    *  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   4     4  
 d  �    h  �   
 H冹(H嬄K�@H菱H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   _  W G            B      B   �        �std::allocator<ReGIR_OnionLayerGroup>::deallocate 
 >   this  AJ          AJ 0       D0   
 >   _Ptr  AK          >�   _Count  AP        A   M          )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        �  
#

 Z   �   >�    _Ptr_container  AJ       %    AJ 0       >�    _Back_shift  AH          AH 0       N N (                      H  h   P  �           $LN20  0     Othis  8     O_Ptr  @   �  O_Count  O �   8           B   �      ,       � �   � �3   � �7   � �,   �    0   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 6  �    :  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
       #     
 t  �    x  �   
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �    <   �       �   ]  Q G            A      A   �        �std::allocator<ReGIR_OnionRing>::deallocate 
 >�   this  AJ          AJ ,       D0   
 >�   _Ptr  AK        @ /   >�   _Count  AP           M          )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        �  
#

 Z   �   >�    _Ptr_container  AJ       (    AJ ,       >�    _Back_shift  AH         AH ,       N N (                      H  h   P  �           $LN20  0   �  Othis  8   �  O_Ptr  @   �  O_Count  O   �   8           A   �      ,       � �   � �2   � �6   � �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 4  �    8  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   &   !  &  
 t  �    x  �   
 H婹H�    H呉HE旅   �      �   �   : G                      n        �std::exception::what 
 >   this  AJ                                 @       Othis  O�   0              X     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  20    2                       L   
 
4 
2p    B                       R    20    <                       X   
 
4 
2p    B                       ^    20    <                       d   
 
4 
2p    B                       j    �                              p    B      B           !      !      v    20    z           #      #      |    B                 %      %      �    B      A           '      '      �    B                 )      )      �    20    e           +      +      �   
 d T
 4	 2��p                 �                 -      -      �   (           �      �             a �� h d 4 2p                 �       �           .      .      �   (           �      �             e�  R0               /      /      �   5
 5�
 "x
 # 
��	`P    o           0      0      �   !C C� =� ,�	 $� � h � �  
t! 4"     o       ,   0   0   0   4   �   o   L          0      0      �   !       o          0      0      �   L  x          0      0      �   c c�	 [�
 S� K� C�
 ;� 3� +� &x "h ' ���
�p
`	0P        @      D   �       z          2      2      �   (           �      �   
    �2    �   �       �    =
M 20    V           3      3      �    B      B           5      5      �    20    [           7      7      �    B                 9      9      �    2����
p`0                 �       �          <      <      �   8                         	   	               �          � �� 
 
2P    (                            2����
p`0                        �          ?      ?         8                     !   	   '            $   �       
   � �� 
 
2P    (           
      
      *     2����
p`0                 9       #          B      B      3   8               <      ?   	   E            B   �       	   � �� 
 
2P    (           	      	      H    
 d T
 4 r��p    �           C      C      Q   ! h     �          C      C      Q   �             C      C      W   !       �          C      C      Q     �          C      C      ]   !   h     �          C      C      Q   �  �          C      C      c   !       �          C      C      Q   �            C      C      i   
 
4 
2p    0           D      D      o   
 
4 
2p    0           E      E      u    t d 4 2�    �          F      F      {   
 
4 
2p    0           G      G      �    B      :           I      I      �                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                    .?AVbad_alloc@std@@     �              ����                      �      �                    .?AVexception@std@@     �               ����                      �      �    vector too long                                       �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                         �                           �      �              ����    @                   �      �                                         �      �      �                         �                                   �      �      �              ����    @                   �      �      ?  �?�I@�葽   �   (   & 
5        std::exception::`vftable'    �      �  
    �   (   & 
5        std::bad_alloc::`vftable'    �      �  
    �   3   1 
5        std::bad_array_new_length::`vftable'     �      �  
    B   �              std::bad_exception .?AVbad_exception@std@@ 篁�:   �              std::exception .?AVexception@std@@ 篁� 
  U�
     
    
    蝰
   ,  
       	        
       
 p    蝰
 	       
  t    	        
       
    
   	        
 
            	        
        "                     
   ,   	               	                
 	    
     	                F   �              __std_exception_data .?AU__std_exception_data@@ 蝰& 
     _What 
 0    _DoFree 蝰F              __std_exception_data .?AU__std_exception_data@@ 蝰V     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h 篁�        
    u    	              � 	       exception 蝰   operator= 蝰       ~exception �      what 篁�
    _Data   __local_vftable_ctor_closure 篁�      __vecDelDtor 篁� 
  U�:  &          std::exception .?AVexception@std@@ 篁� !    0   
     
    �  
    $   	      #   
 %      
     蝰
 '  ,  
    (   	      #   
 )       	      #   
           &    *     +   	      #           
    ,   	.     #    %       	.     #    )         /    0   	      #            	     #          �       蝰 ,  bad_exception 蝰-  ~bad_exception � 1  operator= 蝰2  __local_vftable_ctor_closure 篁�3      __vecDelDtor 篁� 
  U馚 	 &4      5   std::bad_exception .?AVbad_exception@std@@ 篁� 6    g   :   �              std::bad_alloc .?AVbad_alloc@std@@ 篁�
 8   
 8  �  
    :   	   8  9   
 ;      
 8   蝰
 =  ,  
    >   	   8  9   
 ?       	   8  9   
 
       	   8  9   
        "   <    @     A     B   	   8  9           
 8  ,   	E  8  9    ;       	E  8  9    ?         F    G   	   8  9            	  8  9          �       蝰 C  bad_alloc 蝰D  ~bad_alloc � H  operator= 蝰I  __local_vftable_ctor_closure 篁�J      __vecDelDtor 篁� 
  U�: 
 &K      L   std::bad_alloc .?AVbad_alloc@std@@ 篁� M    r   J   �              std::bad_variant_access .?AVbad_variant_access@std@@ �
 O   
 O  �  
    Q   	   O  P   
 R      
 O   蝰
 T  ,  
    U   	   O  P   
 V       	   O  P   
           S    W     X  
 T    	  O  Z            	   O  P           
 O  ,   	]  O  P    R       	]  O  P    V         ^    _   	   O  P            	  O  P          �       蝰 Y  bad_variant_access � [  what 篁�\  ~bad_variant_access  `  operator= 蝰a  __local_vftable_ctor_closure 篁�b      __vecDelDtor 篁� 
  U馢 
 &c      d   std::bad_variant_access .?AVbad_variant_access@std@@ 馢     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception 篁� e  f  �  F   �              std::nested_exception .?AVnested_exception@std@@ � 
  P�
 i    
 h   
 h   蝰
 l  ,  
    m   	   h  k   
 n       	   h  k   
            o     p  
 h  ,   	r  h  k    n       	   h  k           
 l    	   h  u            B   �              std::exception_ptr .?AVexception_ptr@std@@ 篁�
 w   
 w   蝰
 y  ,  
    z   	   w  x   
 {      
       	   w  x   
 }       	   w  x   
            |     ~        	   w  x           
 w  ,   	�  w  x    }       	�  w  x    {          �     �  
 y    	0   w  �            	w  w       	        
     蝰
 �          �   	w  w        �       	  w  x          �  �  exception_ptr 蝰 �  ~exception_ptr � �  operator= 蝰 �  operator bool 蝰 �  _Current_exception � �  _Copy_exception 
     _Data1 篁�
    _Data2 篁��  __vecDelDtor 篁馚  f�           std::exception_ptr .?AVexception_ptr@std@@ 篁� �  f  �    	w  h  u   	         	  h  k          � 	  j   q  nested_exception 篁� s  operator= 蝰 t      ~nested_exception 蝰 v  rethrow_nested � �  nested_ptr �
 w   _Exc �t  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁� 
  P馞 
 &�      �   std::nested_exception .?AVnested_exception@std@@ � �  f  N  N   �              std::bad_array_new_length .?AVbad_array_new_length@std@@ �
 �   
 �  �  
    �   	   �  �   
 �      
 �   蝰
 �  ,  
    �   	   �  �   
 �       	   �  �   
           �    �     �   	   �  �           
 �  ,   	�  �  �    �       	�  �  �    �         �    �   	   �  �            	  �  �          �   8    蝰 �  bad_array_new_length 篁��  ~bad_array_new_length 蝰 �  operator= 蝰�  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁� 
  U馧 	 &�      �   std::bad_array_new_length .?AVbad_array_new_length@std@@ � �    �   
 #    蝰
 t    蝰
     
 @    蝰
 #   ,  
   ,  
 #    蝰F   �              std::_Container_base0 .?AU_Container_base0@std@@ �
 �    	   �  �           
 �  ,  
    �   	   �  �    �      F   �              std::_Fake_allocator .?AU_Fake_allocator@std@@ 篁� F    �           std::_Fake_allocator .?AU_Fake_allocator@std@@ 篁馞     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory � �  �  �  
 �   蝰
 �  ,  
    �   	   �  �    �          �  �   	   �  �    �      j  �  _Orphan_all  �  _Swap_proxy_and_iterators 蝰 �  _Alloc_proxy 篁� �  _Reload_proxy 蝰F   �           std::_Container_base0 .?AU_Container_base0@std@@ � �  �  �  F   �              std::_Iterator_base0 .?AU_Iterator_base0@std@@ 篁�
 �   
    �   	   �  �    �      
 �   蝰
 �    
 �   蝰
 �    	�  �  �           
 0    蝰F  �  _Adopt � �  _Getcont 篁� �  _Unwrap_when_unverified F   �           std::_Iterator_base0 .?AU_Iterator_base0@std@@ 篁� �  �  �  N   �              std::_Fake_proxy_ptr_impl .?AU_Fake_proxy_ptr_impl@std@@ �
 �   
 �  ,      �  �   	   �  �   
 �      N   �              std::_Leave_proxy_unbound .?AU_Leave_proxy_unbound@std@@ � N    �           std::_Leave_proxy_unbound .?AU_Leave_proxy_unbound@std@@ � �  �  �      �  �   	   �  �   
 �      
 �   蝰
 �  ,  
    �   	   �  �    �          �     �     �  
 �  ,   	�  �  �     �      
 �        �  �   	   �  �    �       	   �  �           Z  �  _Fake_proxy_ptr_impl 篁� �  operator= 蝰 �  _Bind 蝰 �  _Release 篁馧  &�           std::_Fake_proxy_ptr_impl .?AU_Fake_proxy_ptr_impl@std@@ � �  �  �  V   �              rtxdi::ReGIRDynamicParameters .?AUReGIRDynamicParameters@rtxdi@@ �6   �              rtxdi::float3 .?AUfloat3@rtxdi@@ �& 
 @     x 
 @    y 
 @    z 6   
�           rtxdi::float3 .?AUfloat3@rtxdi@@ �:     D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h  �  �      "    Uniform 蝰  Power_RIS j   u   �  rtxdi::LocalLightReGIRFallbackSamplingMode .?AW4LocalLightReGIRFallbackSamplingMode@rtxdi@@  �  �  n   "    Uniform 蝰  Power_RIS b   u   �  rtxdi::LocalLightReGIRPresamplingMode .?AW4LocalLightReGIRPresamplingMode@rtxdi@@ 蝰 �  �  h   
 �    	   �  �   
        � 
 @     regirCellSize 
 �   center 篁�
 �   fallbackSamplingMode �
 �   presamplingMode 蝰
 @    regirSamplingJitter 蝰
 u    regirNumBuildSamples ��  ReGIRDynamicParameters 馰  �            rtxdi::ReGIRDynamicParameters .?AUReGIRDynamicParameters@rtxdi@@ � �  �  v   f   �              rtxdi::ReGIROnionCalculatedParameters .?AUReGIROnionCalculatedParameters@rtxdi@@ 穸   �              std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > .?AV?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 駄   �              std::allocator<ReGIR_OnionLayerGroup> .?AV?$allocator@UReGIR_OnionLayerGroup@@@std@@ 馞   �              ReGIR_OnionLayerGroup .?AUReGIR_OnionLayerGroup@@ 2
 @     innerRadius 蝰
 @    outerRadius 蝰
 @    invLogLayerScale �
 t    layerCount 篁�
 @    invEquatorialCellAngle 篁�
 t    cellsPerLayer 
 t    ringOffset 篁�
 t    ringCount 
 @     equatorialCellAngle 蝰
 @   $ layerScale 篁�
 t   ( layerCellOffset 蝰
 t   , pad1 馞             0 ReGIR_OnionLayerGroup .?AUReGIR_OnionLayerGroup@@ F     D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h 蝰        Z   �              std::integral_constant<bool,1> .?AU?$integral_constant@_N$00@std@@ 篁�
 0    蝰
    蝰
 	    	0     
           b    value 蝰  0   value_type �    type 篁�   operator bool 蝰   operator() 馴  T           std::integral_constant<bool,1> .?AU?$integral_constant@_N$00@std@@ 篁馢     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common 蝰 
    *   
     	        
        
          �   	                
     
    �   	               "    _From_primary 蝰    value_type �  #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal    allocator<ReGIR_OnionLayerGroup> 篁�   deallocate �   allocate 篁� �  _Minimum_asan_allocation_alignment 駄 
            std::allocator<ReGIR_OnionLayerGroup> .?AV?$allocator@UReGIR_OnionLayerGroup@@@std@@ 馞     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector 蝰     �  �   �              std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 篁癞   �              std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �
     
    蝰
     Z   �              std::integral_constant<bool,0> .?AU?$integral_constant@_N$0A@@std@@ 蝰
 0    蝰
 !   蝰
 #    	0   !  $           b  "  value 蝰  0   value_type �  !  type 篁� %  operator bool 蝰 %  operator() 馴  T&           std::integral_constant<bool,0> .?AU?$integral_constant@_N$0A@@std@@ 蝰 '    *   
     
   ,  
 #    蝰
 �        *  +  ,   	)           -          *  +   	)           /          .     0  
        *  2  +   	            3      
    蝰
 5  ,  
    6   	#           7       	          7      �    allocator_type �    value_type �    pointer      const_pointer 蝰    void_pointer 篁�  �  const_void_pointer �  #   size_type 蝰     difference_type   !  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  !  propagate_on_container_swap     is_always_equal  1  allocate 篁� 4  deallocate � 8  max_size 篁� 9  select_on_container_copy_construction 蝰�  :           std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ � ;  �  �         蝰�   =           std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 篁� >  �    
     
   ,  
    蝰
 B  ,  �   �              std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@ �
     
    蝰
 F    
   ,  
    蝰
 I  ,  
 D   
         L  L  L   	   D  K   
 M       	   D  K   
            N     O  
 D  ,  
    Q   	   D  K    R      J  �    蝰    value_type �  #   size_type 蝰     difference_type   E  pointer   G  const_pointer 蝰  H  reference 蝰  J  const_reference  P  _Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >  S  _Swap_val 蝰 S  _Take_contents �
 L    _Myfirst �
 L   _Mylast 蝰
 L   _Myend 篁駳  T           std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@ � U    W  �   �              std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ 篁褛   �              std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ 篁颃   �              std::reverse_iterator<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > > .?AV?$reverse_iterator@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@@std@@ �
  �              std::reverse_iterator<std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > > .?AV?$reverse_iterator@V?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@@std@@ �
     
    �  
    蝰
 ]  ,      \  ^   	      [    _      
    \   	      [   
 a      
     蝰
 c  ,      d  ^   	      [    e      
    d   	      [    g      z   �              std::initializer_list<ReGIR_OnionLayerGroup> .?AV?$initializer_list@UReGIR_OnionLayerGroup@@@std@@ 篁�
    蝰
 j  ,      i  k   	      [    l      
 #    蝰    n  C  k   	      [    o          n  k   	      [    q      
    k   	      [   
 s       	      [   
        J    `     b     f     h     m     p     r     t     u  
    ,  
    i   	w     [     x       	w     [     g       	w     [     a          y     z     {   	      [           
   �  
    ~   	      [           
    C   	      [     �          �     �      X  i   	W     [    �          X  n  C   	W     [    �          X  ~   	W     [    �          X  C   	W     [    �      "    �     �     �     �  
 i   蝰
    �   	      [     �          n  C   	      [     �          �     �  
    n   	      [     �          �     �  "    _At_least   _Exactly 褶  t   �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy .?AW4_Reallocation_policy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 篁� �    ?  
    #    	      [     �       	      [                X  X   	W     [    �      
    X   	W     [    �          �     �  
    w   	      [    �      
 B    
 c    	�     �           
      	�     [               �     �   	X     �   	         	W     [   	            �     �      �     �   	Z     �   	         	Y     [   	            �     �      �     �  
      	�     �           
      	�     [               �     �      �     �   	0      �            	#      �            	C     �    �       	A     [    �          �     �   	C     �     �       	A     [     �          �     �   	C     �            	A     [               �     �      �     �   	     �   	         	#      �     �      
        �  n  n   	      [     �       	      [     �       	                        �  �   	      �     �      
    蝰
 �  ,   	�     �           
   ,   	�     [               �     �  
    �   	W     [   	 �       	W     [   	 �        �              std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionLayerGroup@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@2@$00@std@@ �
    蝰
 �  ,  
 �   蝰
 �    	�  �  �           
   ,  
 �    	�  �  �               �     �  F       蝰
 D    _Myval2 蝰    _Mybase  �  _Get_first � �           std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionLayerGroup@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@2@$00@std@@ � �    �   	     [          �    _Alty 蝰    _Alty_traits 篁�    value_type �    allocator_type �  )  pointer   @  const_pointer 蝰  A  reference 蝰  C  const_reference   #   size_type 蝰     difference_type   D  _Scary_val �  W  iterator 篁�  X  const_iterator �  Y  reverse_iterator 篁�  Z  const_reverse_iterator �	 v  vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 篁� |  operator= 蝰 }  ~vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 蝰 �  push_back 蝰 �  insert � �  assign � �  resize �  �  _Reallocation_policy 篁� �  _Clear_and_reserve_geometric 篁� �  reserve  �  shrink_to_fit 蝰 }  pop_back 篁� �  erase 蝰 }  clear 蝰 �  swap 篁� �  data 篁� �  begin 蝰 �  end  �  rbegin � �  rend 篁� �  cbegin � �  cend 篁� �  crbegin  �  crend 蝰 �  _Unchecked_begin 篁� �  _Unchecked_end � �  empty 蝰 �  size 篁� �  max_size 篁� �  capacity 篁� �  operator[] � �  at � �  front 蝰 �  back 篁� �  get_allocator 蝰 �  _Calculate_growth 蝰 �  _Buy_raw 篁� �  _Buy_nonzero 篁� �  _Change_array 蝰 }  _Tidy 蝰 �  _Move_assign_unequal_alloc �	 �  _Xlength 篁�	 �  _Xrange  �  _Orphan_range 蝰 �  _Getal � �  _Make_iterator � �  _Make_iterator_offset 蝰
 �    _Mypair 蝰�  __vecDelDtor 篁穸 ] 6�           std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > .?AV?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ � �  �  b   �   �              std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > .?AV?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 馸   �              std::allocator<ReGIR_OnionRing> .?AV?$allocator@UReGIR_OnionRing@@@std@@ �:   �              ReGIR_OnionRing .?AUReGIR_OnionRing@@ Z 
 @     cellAngle 
 @    invCellAngle �
 t    cellOffset 篁�
 t    cellCount :   �           ReGIR_OnionRing .?AUReGIR_OnionRing@@  �    -   
 �    	   �  �   
        
 �       �  �   	   �  �     �      
 �     	�  �  �             �  _From_primary 蝰  �  value_type �  #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal  �  allocator<ReGIR_OnionRing> � �  deallocate � �  allocate 篁� �  _Minimum_asan_allocation_alignment 馸 
 �           std::allocator<ReGIR_OnionRing> .?AV?$allocator@UReGIR_OnionRing@@@std@@ � �    �  �   �              std::allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 篁瘼   �              std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �
 �    
 �   蝰
 �    
 �    
 �  ,  
 #    蝰
 �        �  �  �   	�  �         �          �  �   	�  �                           
 �       �    �   	   �               
 �   蝰
   ,  
       	#   �        	       	�  �        	      �  �  allocator_type �  �  value_type �  �  pointer   �  const_pointer 蝰    void_pointer 篁�  �  const_void_pointer �  #   size_type 蝰     difference_type   !  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  !  propagate_on_container_swap     is_always_equal    allocate 篁�   deallocate � 
  max_size 篁�   select_on_container_copy_construction 蝰�             std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ � 
  �  �     �    蝰�              std::allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 篁�   �    
 �    
 �  ,  
 �   蝰
   ,  �   �              std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@ �
 �    
 �   蝰
     
 �  ,  
 �   蝰
   ,  
    
 �               	        
        	        
                  !  
   ,  
    #   	         $      F  �    蝰  �  value_type �  #   size_type 蝰     difference_type     pointer     const_pointer 蝰    reference 蝰    const_reference  "  _Vector_val<std::_Simple_types<ReGIR_OnionRing> > 蝰 %  _Swap_val 蝰 %  _Take_contents �
     _Myfirst �
    _Mylast 蝰
    _Myend 篁駧  &           std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@ � '    W  �   �              std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@ 篁裎   �              std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@ 篁耱   �              std::reverse_iterator<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > > .?AV?$reverse_iterator@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@@std@@ 颃   �              std::reverse_iterator<std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > > .?AV?$reverse_iterator@V?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@@std@@ �
 �   
 �  �  
 �   蝰
 /  ,      .  0   	   �  -    1      
    .   	   �  -   
 3      
 �   蝰
 5  ,      6  0   	   �  -    7      
    6   	   �  -    9      n   �              std::initializer_list<ReGIR_OnionRing> .?AV?$initializer_list@UReGIR_OnionRing@@@std@@ 篁�
 �   蝰
 <  ,      ;  =   	   �  -    >      
 #    蝰    @    =   	   �  -    A          @  =   	   �  -    C      
    =   	   �  -   
 E       	   �  -   
        J    2     4     8     :     ?     B     D     F     G  
 �  ,  
    ;   	I  �  -     J       	I  �  -     9       	I  �  -     3          K     L     M   	   �  -           
 �  �  
    P   	   �  -     Q      
       	   �  -     S          R     T      *  ;   	)  �  -    V          *  @     	)  �  -    X          *  P   	)  �  -    Z          *     	)  �  -    \      "    W     Y     [     ]  
 ;   蝰
    _   	   �  -     `          @     	   �  -     b          a     c  
    @   	   �  -     e          c     f  "    _At_least   _Exactly 衿  t   h  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy .?AW4_Reallocation_policy@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 篁� i    ?  
    #    	   �  -     k       	   �  -                *  *   	)  �  -    n      
    *   	)  �  -    p          o     q  
    I   	   �  -    s      
     
 5    	u  �  v           
 �     	x  �  -               w     y   	*  �  v   	         	)  �  -   	            {     |      {     |   	,  �  v   	         	+  �  -   	                 �           �  
 �     	�  �  v           
 �     	�  �  -               �     �      �     �   	0   �  v            	#   �  v            	  �  v    e       	  �  -    e          �     �   	  �  v     e       	  �  -     e          �     �   	  �  v            	  �  -               �     �      �     �   	�  �  v   	         	#   �  v     e      
 �       �  @  @   	   �  -     �       	   �  -     s       	   �                    �  �   	   �  v     �      
 �   蝰
 �  ,   	�  �  v           
 �  ,   	�  �  -               �     �  
    �   	)  �  -   	 �       	)  �  -   	 e        �              std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionRing@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@2@$00@std@@ �
 �   蝰
 �  ,  
 �   蝰
 �    	�  �  �           
 �  ,  
 �    	�  �  �               �     �  F   �    蝰
     _Myval2 蝰  �  _Mybase  �  _Get_first � �           std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionRing@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@2@$00@std@@ � �    �   	  �  -          �  �  _Alty 蝰  �  _Alty_traits 篁�  �  value_type �  �  allocator_type �  �  pointer     const_pointer 蝰    reference 蝰    const_reference   #   size_type 蝰     difference_type     _Scary_val �  )  iterator 篁�  *  const_iterator �  +  reverse_iterator 篁�  ,  const_reverse_iterator �	 H  vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > 篁� N  operator= 蝰 O  ~vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > 蝰 U  push_back 蝰 ^  insert � d  assign � g  resize �  i  _Reallocation_policy 篁� f  _Clear_and_reserve_geometric 篁� l  reserve  m  shrink_to_fit 蝰 O  pop_back 篁� r  erase 蝰 O  clear 蝰 t  swap 篁� z  data 篁� }  begin 蝰 ~  end  �  rbegin � �  rend 篁� {  cbegin � {  cend 篁�   crbegin    crend 蝰 �  _Unchecked_begin 篁� �  _Unchecked_end � �  empty 蝰 �  size 篁� �  max_size 篁� �  capacity 篁� �  operator[] � �  at � �  front 蝰 �  back 篁� �  get_allocator 蝰 �  _Calculate_growth 蝰 l  _Buy_raw 篁� f  _Buy_nonzero 篁� �  _Change_array 蝰 O  _Tidy 蝰 �  _Move_assign_unequal_alloc �	 �  _Xlength 篁�	 �  _Xrange  �  _Orphan_range 蝰 �  _Getal � �  _Make_iterator � �  _Make_iterator_offset 蝰
 �    _Mypair 蝰�  __vecDelDtor 篁駷 ] 6�           std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > .?AV?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ � �  �  c   
 �   
 �  �  
    �   	   �  �   
 �      
 �   蝰
 �  ,  
    �   	   �  �    �       	   �  �   
           �    �    �   	   �  �           
 �  ,   	�  �  �     �       	�  �  �     �         �    �   	  �  �          6
 u     lightSlotCount 篁�
 u    regirOnionCells 蝰
     regirOnionLayers �
 �    regirOnionRings 蝰
 @   8 regirOnionCubicRootFactor 
 @   < regirOnionLinearFactor 篁� �  ReGIROnionCalculatedParameters ��  ~ReGIROnionCalculatedParameters  �  operator= 蝰�  __vecDelDtor 篁駀 
 &�          @ rtxdi::ReGIROnionCalculatedParameters .?AUReGIROnionCalculatedParameters@rtxdi@@ � �  �  ^   f   �              rtxdi::ReGIRGridCalculatedParameters .?AUReGIRGridCalculatedParameters@rtxdi@@ 篁�
 �    	   �  �   
        V 
 u     lightSlotCount 篁�
 u    pad 蝰�  ReGIRGridCalculatedParameters 蝰f  �           rtxdi::ReGIRGridCalculatedParameters .?AUReGIRGridCalculatedParameters@rtxdi@@ 篁� �  �  U   B   �              rtxdi::ReGIRContext .?AVReGIRContext@rtxdi@@ �
 �   
 �  �  
    �   	   �  �   
 �      
 �   蝰
 �  ,  
    �   	   �  �    �      V   �              rtxdi::ReGIRStaticParameters .?AUReGIRStaticParameters@rtxdi@@ 篁�*    Disabled �  Grid �  Onion 6   u   �  rtxdi::ReGIRMode .?AW4ReGIRMode@rtxdi@@  �  �  '   ^   �              rtxdi::ReGIRGridStaticParameters .?AUReGIRGridStaticParameters@rtxdi@@ 篁�6   �              rtxdi::uint3 .?AUuint3@rtxdi@@ 篁�& 
 u     x 
 u    y 
 u    z 6   �           rtxdi::uint3 .?AUuint3@rtxdi@@ 篁� �  �     
 �    	   �  �           : 
 �    GridSize ��  ReGIRGridStaticParameters 蝰^  �           rtxdi::ReGIRGridStaticParameters .?AUReGIRGridStaticParameters@rtxdi@@ 篁� �  �  .   ^   �              rtxdi::ReGIROnionStaticParameters .?AUReGIROnionStaticParameters@rtxdi@@ �
 �    	   �  �           b 
 u     OnionDetailLayers 
 u    OnionCoverageLayers 蝰�  ReGIROnionStaticParameters 馸  �           rtxdi::ReGIROnionStaticParameters .?AUReGIROnionStaticParameters@rtxdi@@ � �  �  4   
 �    	   �  �           � 
 �    Mode �
 u    LightsPerCell 
 �   gridParameters 篁�
 �   onionParameters 蝰�  ReGIRStaticParameters 蝰V  �           rtxdi::ReGIRStaticParameters .?AUReGIRStaticParameters@rtxdi@@ 篁� �  �  G   
 �   蝰
 �  ,  ^   �              rtxdi::RISBufferSegmentAllocator .?AVRISBufferSegmentAllocator@rtxdi@@ 篁�
 �    	   �  �           
    u    	u   �  �     �      
 �   蝰
 �    	u   �  �            ~  �  RISBufferSegmentAllocator 蝰 �  allocateSegment  �  getTotalSizeInElements �
 u     m_totalSizeInElements ^  �           rtxdi::RISBufferSegmentAllocator .?AVRISBufferSegmentAllocator@rtxdi@@ 篁馰     D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h  �  �     
 �  ,      �     	   �  �             �    �       
 �    	0   �               	u   �               	�  �              	�  �              	�  �              	�  �             
 �   蝰
   ,  
    
   	   �  �           
    �   	   �  �            	   �  �            
       	   �  �            	   �  �           
 �  ,   	  �  �     �       	  �  �     �                	  �  �          �   ReGIRContext 篁�   IsLocalLightPowerRISEnable �   GetReGIRCellOffset �   GetReGIRLightSlotCount �   GetReGIRGridCalculatedParameters 篁� 	  GetReGIROnionCalculatedParameters 蝰 
  GetReGIRDynamicParameters 蝰   GetReGIRStaticParameters 篁�   SetDynamicParameters 篁�   InitializeOnion    ComputeOnionJitterCurve    ComputeGridLightSlotCount 蝰   AllocateRISBufferSegment 篁�
 u     m_regirCellOffset 
 �   m_regirStaticParameters 蝰
 �    m_regirDynamicParameters �
 �  @ m_regirOnionCalculatedParameters �
 �  � m_regirGridCalculatedParameters 蝰  ~ReGIRContext 蝰   operator= 蝰  __vecDelDtor 篁馚  &          � rtxdi::ReGIRContext .?AVReGIRContext@rtxdi@@ �   �  �   
 t    蝰
   ,  
 t    蝰
    ,  
 �   蝰
 "  ,  j   �              std::vector<float,std::allocator<float> > .?AV?$vector@MV?$allocator@M@std@@@std@@ 篁馚   �              std::allocator<float> .?AV?$allocator@M@std@@ 
 %    	   %  &   
        
 @        (  �   	   %  &     )       	@  %  &             %  _From_primary 蝰  @   value_type �  #   size_type 蝰     difference_type     propagate_on_container_move_assignment �    is_always_equal  '  allocator<float> 篁� *  deallocate � +  allocate 篁� �  _Minimum_asan_allocation_alignment 馚 
 ,           std::allocator<float> .?AV?$allocator@M@std@@  -    �  v   �              std::allocator_traits<std::allocator<float> > .?AU?$allocator_traits@V?$allocator@M@std@@@std@@ 蝰�   �              std::_Default_allocator_traits<std::allocator<float> > .?AU?$_Default_allocator_traits@V?$allocator@M@std@@@std@@ 
 @    蝰
 1    
 %  ,  
 #    蝰
 �        3  4  5   	@  0         6          3  4   	@  0         8          7     9  
 @        3  ;  4   	   0         <      
 %   蝰
 >  ,  
    ?   	#   0        @       	%  0        @      �  %  allocator_type �  @   value_type �  @  pointer   2  const_pointer 蝰    void_pointer 篁�  �  const_void_pointer �  #   size_type 蝰     difference_type   !  propagate_on_container_copy_assignment �    propagate_on_container_move_assignment �  !  propagate_on_container_swap     is_always_equal  :  allocate 篁� =  deallocate � A  max_size 篁� B  select_on_container_copy_construction 蝰�  C           std::_Default_allocator_traits<std::allocator<float> > .?AU?$_Default_allocator_traits@V?$allocator@M@std@@@std@@  D  �  �     0    蝰v   F           std::allocator_traits<std::allocator<float> > .?AU?$allocator_traits@V?$allocator@M@std@@@std@@ 蝰 G  �    
 1    
 @   ,  
 @    蝰
 K  ,  r   �              std::_Vector_val<std::_Simple_types<float> > .?AV?$_Vector_val@U?$_Simple_types@M@std@@@std@@ 
 @    蝰
 N    
 @   ,  
 @    蝰
 Q  ,  
 M       @  @  @   	   M  S   
 T       	   M  S   
            U     V  
 M  ,  
    X   	   M  S    Y      :  �    蝰  @   value_type �  #   size_type 蝰     difference_type   @  pointer   O  const_pointer 蝰  P  reference 蝰  R  const_reference  W  _Vector_val<std::_Simple_types<float> >  Z  _Swap_val 蝰 Z  _Take_contents �
 @    _Myfirst �
 @   _Mylast 蝰
 @   _Myend 篁駌  [           std::_Vector_val<std::_Simple_types<float> > .?AV?$_Vector_val@U?$_Simple_types@M@std@@@std@@  \    W  �   �              std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰�   �              std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰V   �              std::contiguous_iterator_tag .?AUcontiguous_iterator_tag@std@@ 篁馴   �              std::random_access_iterator_tag .?AUrandom_access_iterator_tag@std@@ 馴   �              std::bidirectional_iterator_tag .?AUbidirectional_iterator_tag@std@@ 馧   �              std::forward_iterator_tag .?AUforward_iterator_tag@std@@ 馢   �              std::input_iterator_tag .?AUinput_iterator_tag@std@@ � J    e           std::input_iterator_tag .?AUinput_iterator_tag@std@@ 馬     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp  f  g  (      d    蝰N   i           std::forward_iterator_tag .?AUforward_iterator_tag@std@@ � j  g  ,      c    蝰Z   l           std::bidirectional_iterator_tag .?AUbidirectional_iterator_tag@std@@ � m  g  .      b    蝰Z   o           std::random_access_iterator_tag .?AUrandom_access_iterator_tag@std@@ � p  g  0      a    蝰V   r           std::contiguous_iterator_tag .?AUcontiguous_iterator_tag@std@@ 篁� s  g  3   
 N    
 @    蝰
 v  ,  
 _   
 �   蝰
 y        @  z   	   _  x   
 {       	   _  x   
            |     }  
 v  ,  
 _   蝰
 �    	  _  �           
 N     	�  _  �           
    t    	_  _  x   	 �      
 _  ,   	�  _  x               �     �      �     �  
     蝰
    �   	   _  �    �       	�  _  x    �       	_  _  �   	 �      
 �  ,  
    �   	   _  �    �          �     �   	  _  �    �       	0   _  �    �      F   �              std::strong_ordering .?AUstrong_ordering@std@@ 篁�
 �   蝰F   �              std::partial_ordering .?AUpartial_ordering@std@@ �
 �   蝰^  �  less 篁� �  equivalent � �  greater  �  unordered 蝰
      _Value 篁馞   �           std::partial_ordering .?AUpartial_ordering@std@@ 馞     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare � �  �  /   
 �    	�  �  �   	        B   �              std::weak_ordering .?AUweak_ordering@std@@ 篁�
 �   蝰
 �    	�  �  �   	        z  �  less 篁� �  equivalent � �  greater  �  operator struct std::partial_ordering 蝰
      _Value 篁馚  D�           std::weak_ordering .?AUweak_ordering@std@@ 篁� �  �  q    	�  �  �   	        �  �  less 篁� �  equal 蝰 �  equivalent � �  greater  �  operator struct std::partial_ordering 蝰 �  operator struct std::weak_ordering �
      _Value 篁馞  D�           std::strong_ordering .?AUstrong_ordering@std@@ 篁� �  �  �    	�  _  �   	 �       	   _  �    �      
 v     	�  _  �           
    �   	   _  x    �      V  �    蝰  `  iterator_concept 篁�  a  iterator_category 蝰  @   value_type �     difference_type   u  pointer   w  reference 蝰  @  _Tptr 蝰 ~  _Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > 蝰 �  operator* 蝰 �  operator-> � �  operator++ � �  operator-- � �  _Verify_offset � �  operator+= � �  operator+ 蝰 �  operator-= � �  operator- 蝰 �  operator[] � �  operator== � �  operator<=>  �  _Compat   _  _Prevent_inheriting_unwrap � �  _Unwrapped � �  _Seek_to 篁�
 @    _Ptr 癫  �           std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰 �    \  
 @   ,  
 @   ,  
 ^   蝰
 �    	�  ^  �            	@  ^  �           
 ^    	^  ^  �   	 �      
 ^  ,   	�  ^  �               �     �      �     �  
     蝰
    �   	�  ^  �    �       	^  ^  �   	 �       	�  ^  �    �       	@  ^  �            	   ^  �   
 {       	   ^  �   
           �    �  �  _    蝰  _  _Mybase   `  iterator_concept 篁�  a  iterator_category 蝰  @   value_type �     difference_type   @  pointer   �  reference 蝰 �  operator* 蝰 �  operator-> � �  operator++ � �  operator-- � �  operator+= � �  operator+ 蝰 �  operator-= � �  operator- 蝰 �  operator[] �  ^  _Prevent_inheriting_unwrap � �  _Unwrapped � �  _Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > �  �           std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰 �    [  �   �              std::reverse_iterator<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > > .?AV?$reverse_iterator@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@@std@@ �   �              std::reverse_iterator<std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > > .?AV?$reverse_iterator@V?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@@std@@ 
 $   
 $  �  
 %   蝰
 �  ,      �  �   	   $  �    �      
    �   	   $  �   
 �      
 $   蝰
 �  ,      �  �   	   $  �    �      
    �   	   $  �    �      R   �              std::initializer_list<float> .?AV?$initializer_list@M@std@@ 蝰
 %   蝰
 �  ,      �  �   	   $  �    �      
 #    蝰    �  L  �   	   $  �    �          �  �   	   $  �    �      
    �   	   $  �   
 �       	   $  �   
        J    �     �     �     �     �     �     �     �     �  
 $  ,  
    �   	�  $  �     �       	�  $  �     �       	�  $  �     �          �     �     �   	   $  �           
 @   �  
    �   	   $  �     �      
    L   	   $  �     �          �     �      _  �   	^  $  �    �          _  �  L   	^  $  �    �          _  �   	^  $  �    �          _  L   	^  $  �    �      "    �     �     �     �  
 �   蝰
        	   $  �               �  L   	   $  �                      
    �   	   $  �                      "    _At_least   _Exactly 駧  t   	  std::vector<float,std::allocator<float> >::_Reallocation_policy .?AW4_Reallocation_policy@?$vector@MV?$allocator@M@std@@@std@@ � 
    ?  
    #    	   $  �            	   $  �                _  _   	^  $  �          
    _   	^  $  �                     
    �   	   $  �          
 K    
 �    	  $              	@  $  �                       	_  $     	         	^  $  �   	                               	�  $     	         	�  $  �   	                                
 1     	#  $              	@  $  �               $     %      $     %   	0   $              	#   $              	L  $             	J  $  �              *     +   	L  $              	J  $  �               -     .   	L  $              	J  $  �               0     1      0     1   	%  $     	         	#   $             
 @        6  �  �   	   $  �     7       	   $  �            	   $                    @  @   	   $       ;      
 %   蝰
 =  ,   	>  $             
 %  ,   	@  $  �               ?     A  
    6   	^  $  �   	 C       	^  $  �   	       �   �              std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> .?AV?$_Compressed_pair@V?$allocator@M@std@@V?$_Vector_val@U?$_Simple_types@M@std@@@2@$00@std@@ 篁�
 %   蝰
 G  ,  
 F   蝰
 I    	H  F  J           
 %  ,  
 F    	L  F  M               K     N  F   %    蝰
 M    _Myval2 蝰  %  _Mybase  O  _Get_first 褚  P           std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> .?AV?$_Compressed_pair@V?$allocator@M@std@@V?$_Vector_val@U?$_Simple_types@M@std@@@2@$00@std@@ 篁� Q    �   	  $  �          b  %  _Alty 蝰  /  _Alty_traits 篁�  @   value_type �  %  allocator_type �  @  pointer   I  const_pointer 蝰  J  reference 蝰  L  const_reference   #   size_type 蝰     difference_type   M  _Scary_val �  ^  iterator 篁�  _  const_iterator �  �  reverse_iterator 篁�  �  const_reverse_iterator �	 �  vector<float,std::allocator<float> > 篁� �  operator= 蝰 �  ~vector<float,std::allocator<float> > 蝰 �  push_back 蝰 �  insert �   assign �   resize �  
  _Reallocation_policy 篁�   _Clear_and_reserve_geometric 篁� 
  reserve    shrink_to_fit 蝰 �  pop_back 篁�   erase 蝰 �  clear 蝰   swap 篁�   data 篁�   begin 蝰   end  !  rbegin � "  rend 篁�   cbegin �   cend 篁�   crbegin    crend 蝰 &  _Unchecked_begin 篁� '  _Unchecked_end � (  empty 蝰 )  size 篁� )  max_size 篁� )  capacity 篁� ,  operator[] � /  at � 2  front 蝰 3  back 篁� 4  get_allocator 蝰 5  _Calculate_growth 蝰 
  _Buy_raw 篁�   _Buy_nonzero 篁� 8  _Change_array 蝰 �  _Tidy 蝰 9  _Move_assign_unequal_alloc �	 :  _Xlength 篁�	 :  _Xrange  <  _Orphan_range 蝰 B  _Getal � D  _Make_iterator � E  _Make_iterator_offset 蝰
 F    _Mypair 蝰S  __vecDelDtor 篁駄 ] 6T           std::vector<float,std::allocator<float> > .?AV?$vector@MV?$allocator@M@std@@@std@@ 篁�2     D:\RTXPT\External\Rtxdi\Source\ReGIR.cpp 篁� U  V  �   
 @    蝰
 X  ,  
 M  ,   Z    [           std::_Zero_then_variadic_args_t .?AU_Zero_then_variadic_args_t@std@@ � \  �  �  
 �  �   Z    _           std::_One_then_variadic_args_t .?AU_One_then_variadic_args_t@std@@ 篁� `  �  �  
 �   
 b  ,  
   �  
    
 e  ,  
 @    
 %  ,  
 �    
 �   
 �  ,  
     
    
   ,     t   is_transparent �:  o           std::less<void> .?AU?$less@X@std@@ 篁馢     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits � p  q  �	     t   is_transparent �:  s           std::plus<void> .?AU?$plus@X@std@@ 篁馞     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility  t  u  �  �   �              std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@@std@@ �
 �    
 w    	   w  y            	  w  y          � 
 x    _Target 蝰 z  ~_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > 篁�{  __vecDelDtor 篁衿  |           std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@@std@@ � }    �  
 �    
 �    
 �  ,  �   �              std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ �
      
 �    	   �  �            	  �  �          � 
 �    _Target 蝰 �  ~_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > 篁��  __vecDelDtor 篁褶  �           std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ � �    �  
     
     
   ,  
 �  ,  
 #   ,  
 #    蝰
 �  ,  
   ,  
 #   ,  
 @   ,  
 ^   蝰
 �  ,  :   �              std::less<void> .?AU?$less@X@std@@ 篁�
 �  ,  
 ^   蝰
 �  ,  
 ^   蝰
 �  ,  
 @   �  
 @   ,  :   �              std::plus<void> .?AU?$plus@X@std@@ 篁�
 �   蝰
 �   
 @    蝰
 �  ,  
 @   ,  
 %  ,  
 #   ,  
 @    
 @    
 %  ,  
 �   蝰
 �  ,  
 �  ,  
 �   
 �    
 �   
 �  ,  
 �  ,  
 �   
 �  ,  
 �    
 �  �  
 �    
 �  �  
 �   
 �   
 �  ,  
 �    
 �    
 �    
 �  ,  �   �              std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �
 �   
 �   蝰
 �  ,  
    �   	   �  �    �          �  �   	   �  �    �          �     �  
 �  ,   	�  �  �     �       	   �  �            	�  �  �             	  �  �            i  pointer  �  _Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> >  �  operator= 蝰 �  ~_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > 篁� �  _Release 篁�
 �    _First 篁�
 �   _Last 
 �   _Al 蝰�  __vecDelDtor 篁瘼 
 6�           std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ � �  �  =  
 �  ,  
    蝰
 �  ,  
   ,  
    
     
    
   ,  
   ,  
    
 �  ,  
     
 �  �  
     
 �  �  
    
    
 �  ,  
     
     
     
   ,  �   �              std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �
 �   
 �   蝰
 �  ,  
    �   	   �  �    �          �  �   	   �  �    �          �     �  
 �  ,   	�  �  �     �       	   �  �            	�  �  �             	  �  �            l  pointer  �  _Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > 蝰 �  operator= 蝰 �  ~_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > � �  _Release 篁�
 �    _First 篁�
 �   _Last 
 �   _Al 蝰�  __vecDelDtor 篁癞 
 6�           std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ � �  �  =  
   ,  
 @    J   �              std::pair<float *,float *> .?AU?$pair@PEAMPEAM@std@@ �
 �   
 �  �  
    �   	   �  �   
 �      
 �  ,  
 �   蝰
 �  ,  
    �   	�  �  �            
    �   	   �  �           �   @  first_type �  @  second_type  �  pair<float *,float *> 蝰   operator= 蝰   swap 篁�
 @    first 
 @   second 篁馢  6           std::pair<float *,float *> .?AU?$pair@PEAMPEAM@std@@ 馞     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility �     �   
 @   �  
 @   ,  
 @    
 @   ,  
 �  ,  
 %  ,  �   �              std::_Uninitialized_backout_al<std::allocator<float> > .?AV?$_Uninitialized_backout_al@V?$allocator@M@std@@@std@@ 
    
    蝰
   ,  
       	                   @  
   	                          
   ,   	                	                 	@                 	              �   @  pointer    _Uninitialized_backout_al<std::allocator<float> > 蝰   operator= 蝰   ~_Uninitialized_backout_al<std::allocator<float> > �   _Release 篁�
 @    _First 篁�
 @   _Last 
 
   _Al 蝰  __vecDelDtor 篁駟 
 6           std::_Uninitialized_backout_al<std::allocator<float> > .?AV?$_Uninitialized_backout_al@V?$allocator@M@std@@@std@@    �  �  
 �   
 �  ,  
 �  ,  
 �    
 �  �  
   ,  
 �   
    
   ,  
 �  ,  
     
   �  
 �  ,  
    
 @   ,  
 @   ,  
 �   蝰
 /   
 @   �  
 @  ,  
 @  ,  
 @    
 @    
 5  ,  
 @    
 @   �  
 �  ,  
 �   
 �  �  
 �  ,  
 �   
   ,  
    
   �  
   ,  
    
     蝰
 @   �  
 @   �  
     蝰
 @   ,  
 @    
 �  ,  
 �   
   ,  
    
 @  ,  
     std  0        * N  O  is_constant_evaluated  oTN蝰R   �              std::numeric_limits<__int64> .?AV?$numeric_limits@_J@std@@ 篁馚   �              std::_Num_int_base .?AU_Num_int_base@std@@ 篁�:   �              std::_Num_base .?AU_Num_base@std@@ 篁馢   ��denorm_indeterminate    denorm_absent   denorm_present 篁馞   t   T  std::float_denorm_style .?AW4float_denorm_style@std@@ 蝰F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits 蝰 U  V     
 U   蝰�   ��round_indeterminate �   round_toward_zero   round_to_nearest �  round_toward_infinity   round_toward_neg_infinity B   t   Y  std::float_round_style .?AW4float_round_style@std@@  Z  V      
 Z   蝰� X  has_denorm � �  has_denorm_loss  �  has_infinity 篁� �  has_quiet_NaN 蝰 �  has_signaling_NaN 蝰 �  is_bounded � �  is_exact 篁� �  is_iec559 蝰 �  is_integer � �  is_modulo 蝰 �  is_signed 蝰 �  is_specialized � �  tinyness_before  �  traps 蝰 \  round_style  �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 � �  radix 蝰:   ]           std::_Num_base .?AU_Num_base@std@@ 篁� ^  V  (   r   S    蝰 �  is_bounded � �  is_exact 篁� �  is_integer � �  is_specialized � �  radix 蝰B   `           std::_Num_int_base .?AU_Num_int_base@std@@ 篁� a  V  v    	   Q               �   R    蝰 c  min  c  max  c  lowest � c  epsilon  c  round_error  c  denorm_min � c  infinity 篁� c  quiet_NaN 蝰 c  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馬 
  d           std::numeric_limits<__int64> .?AV?$numeric_limits@_J@std@@ 篁� e  V  u   Q  c  max �5�=E�+    #          h  "     i  operator new 莜O颠鍈篁�     {ctor} 比犍,w�     {ctor} /� �J疡     {dtor} Й@珪g蓠     what i岷kw�!l篁�     __delDtor m咄�'忩� 8  A  {ctor} 关�/棉喟� 8  J  __delDtor y"吙魔膀� 8  D  {dtor} 箦9R廟撷� �  �  {ctor} \瞾埌#涶 �  �  __delDtor sV耮向蝰 �  �  {dtor} 	�#肨絟~�
     std           . v  w  _Throw_bad_array_new_length 擠諚聖 �  �  {ctor} ��;嘽� 8  @  {ctor} 盕~M垊[捡V   �              std::_Default_allocate_traits .?AU_Default_allocate_traits@std@@ � 	  {                   �  �   	  {         }      2  |  _Allocate 蝰 ~  _Allocate_aligned 蝰V              std::_Default_allocate_traits .?AU_Default_allocate_traits@std@@ � �  �  P    {  |  _Allocate yC羡E抖�
     std     �  �         �  2 �  �  _Adjust_manually_vector_aligned %�劻,旰 �  �  _Orphan_all 繅fS遉�" �  �  _Alloc_proxy 敤dy.躰篁� �  �  _Adopt Jd�:��!� �  �  {ctor} 轊允A熍� �  �  _Release ;F撕;�"篁� �    {ctor} 係郫銾Q赳 �  �  {ctor} 怆S攱N甓� �  �  {ctor} 愐裼*壯 �  �  {ctor} W朅めe覰� �  �  {dtor} ]��Ⅰ. �    ComputeGridLightSlotCount ~撜cf粕
蝰. �    AllocateRISBufferSegment 畘[粀s?篁�" �    InitializeOnion :L5猧賞O     rtxdi 蝰    �  �  �   �     �  * �  �  SphericalToCartesian 揺b!齴鶤篁�     rtxdi 蝰    #  #   @      �   �  �  Distance o<茰&�#篁�* �    ComputeOnionJitterCurve ��9⑼榣6 �    GetReGIRGridCalculatedParameters �4╘�.后蝰6 �  	  GetReGIROnionCalculatedParameters �:"厪�蝰 �  �  {ctor} 揭N:�3�& �    GetReGIRCellOffset 鮦 傫* �    GetReGIRLightSlotCount 凳魲幗蕚�. �  
  GetReGIRDynamicParameters L握珻`婒�. �    GetReGIRStaticParameters 69J辿p篁�* �    SetDynamicParameters r桋[賣t阁蝰. �    IsLocalLightPowerRISEnable 苍爋h{� $  +  operator[] 菜zV訙� $  )  size 4В飅嫵矿蝰 $  (  empty ��+�+┓蝰 $    end �I'溠Z ^  �  {ctor} 峊獽�/�N� $    begin 褥溅奔暊蝰 $  �  push_back 袢O8�1蝰 $  �  {dtor} ME7c濇n� $  �  {ctor} 秦O6�7P� �  �  operator[] n�迸鏥桉 �  �  size 萸豑�儵篁� �  T  push_back ,鍬i眙_衮� �  O  {dtor} 荰蘨魁漻� �  :  {ctor} yX祆尠� �  G  {ctor} 发�	C�J�    �  size 嗻`rCソ殷蝰"    �  _Unchecked_end 瑰銸�1紧�&    �  _Unchecked_begin 歬y0�#f趔蝰    �  push_back u枓万�    }  {dtor} w媩ss<4滖    h  {ctor} |p仝㏎D像    u  {ctor} 絽A蔼弶� _  |  {ctor} ��>�薪� $  �  _Tidy o��矰� �  �  _Getal 椏A痨w�� �  O  _Tidy 栿3[y鹖蝰: �    select_on_container_copy_construction `R:/]`�    �  _Getal 怛T`�燆�    }  _Tidy B竩葅uD蝰:   9  select_on_container_copy_construction �桯肛X)蝰 $  A  _Getal EA>辌D�� %  *  deallocate �,\R�
荫 �  �  _Get_first 坤zh�
� �  �  _Getal 辬N剙槶锐 �  �  deallocate 颚J&�9栥� �  �  _Get_first �4G/j铳隈    �  _Getal }糇c#TR隈     deallocate 戍�9|荞 F  N  _Get_first 捤`!p^� �  �  _Get_first 魧k龠�&像 �  �  _Get_first Z苭刄l{!�
     std     !  !   !    �   �  �  min �?R薧a侱
     std              �   �  �  max 4犊緲�
     std     Y  Y   Y    �   �  �  max >膖}H]撎
     std 
 ^   蝰    �  �         �   �  �  sort w傾娊蝰
     std 
 ^   蝰    �  �  @    @      �   �  �  accumulate RxW:�鸟
     std 
 M    
    Z   �    �   �  �  addressof W冑�[醹蝰 	J  $  �     �      * $  �  _Emplace_one_at_back  �X;N篁馴   �              std::_Zero_then_variadic_args_t .?AU_Zero_then_variadic_args_t@std@@ �
    �   	   F  M   
 �       F  �  {ctor} 
醮�<c兩� 	  �  -     S      * �  �  _Emplace_one_at_back �b銆股G祗蝰Z   �              std::_One_then_variadic_args_t .?AU_One_then_variadic_args_t@std@@ 篁�    �  ^   	   �  �   
 �       �  �  {ctor} 隱埥b�    @  c  c   	   �  -     �      " �  �  _Construct_n n6&Q鷣�篁� 	   �  �   
 �       �  �  {ctor} ru炂箋�)� 	A     [     �      *    �  _Emplace_one_at_back 加燍�皃篁�    �  d   	   �  �   
 �       �  �  {ctor} E懎 鶦像    n  f  f   	      [            "      _Construct_n T�0型b碿篁� 	   �  �   
 �       �    {ctor} 埙�窊'嚇�
     std     @  g  h          "     _Destroy_range }
�5�詁�
     std     i  j  k        
  " 	    _Destroy_range \!3JS瘪
     std     l  m  n          " 
    _Destroy_range 騨etq�=&�
     std       #                _Deallocate �テ櫈褒 �  �  {dtor} 	槦r苑� w  z  {dtor} � �� M  V  {ctor} 
Nw?滛 %  '  {ctor} 雂鳐oo嬠�   !  {ctor} ┣儽Bf铷�" �  f  _Buy_nonzero �(8w蟂`凅蝰 �  �  {ctor} 蠨堮�' D  O  {ctor} �碽EU�"    �  _Buy_nonzero �<闂赝3蝰     {ctor} gM轛哺恶 �  �  _Xlength XZ崘禅4篁� �  l  _Buy_raw *銟u0>篁� �  �  max_size Z蘛p鸷G篁�    �  _Xlength 惜$0{隗蝰    �  _Buy_raw 邸踨,A堙篁�    �  max_size MC殥FB|篁� �  
  max_size 冔蜣戽^弩蝰   8  max_size 
23棹?�S篁�
     std     �  �  �         (   '  )  sort 4 �兲豑篁�
     std 
    �   @    ,  " +  -  _Get_unwrapped bVV戛咇 ^  �  _Unwrapped C-藧rL�
     std 
    �   �    1   0  2  _Pass_fn 搆z穚刍篁�
     std 
 ^   蝰    5  5  @   �   @      6   4  7  accumulate 磓D#sVe�
     std 
 K  ,  
    �   :    ;   9  <  forward  sO仔鮓6 $  �  _Emplace_back_with_unused_capacity 墭鼹U=�    6  L   	@  $  �     ?      & $  @  _Emplace_reallocate 9P胤遉 �
     std 
   ,  
    �   C    D   B  E  forward �	媅�(+(6 �  �  _Emplace_back_with_unused_capacity wv�m	k�    �     	�  �  -     H      & �  I  _Emplace_reallocate �+鑓�
     std 
 �  �  
    �   L    M   K  N  forward 椟�:S洀
     std 
 b  ,  
    �   Q    R   P  S  forward 桯煦櫳     std::ranges 
    �   �    V  " U  W  _Unwrap_iter .<)�
钎c篁�     std::ranges 
    �   �    Z  " Y  [  _Unwrap_sent �睛D戵蝰
     std       �  i  �   i     ^  & ]  _  _Uninitialized_copy 賃燪5溴
     std 
 �    
    �   b    c   a  d  _To_address :Mz黽
S�
     std 
 B  ,  
    �   g    h   f  i  forward �|

i�6    �  _Emplace_back_with_unused_capacity D腺簷�    �  C   	�     [     l      &    m  _Emplace_reallocate 胸h绞�
     std 
   �  
    �   p    q   o  r  forward }5O,闈
     std 
 e  ,  
    �   u    v   t  w  forward gn募
棝     std::ranges 
    �   �    z  " y  {  _Unwrap_iter Y蚂Gl]篁�     std::ranges 
    �   �    ~  " }    _Unwrap_sent 彩桀�漬篁�
     std     �  �  l  �   l     �  & �  �  _Uninitialized_copy 捰椴5�
     std 
     
    �   �    �   �  �  _To_address 頖A+踢竤
     std     �  �   �     �  . �  �  _Allocate_at_least_helper �鎐昍蝰
     std     �  �   �    �   �  �  min Zv珵�	�
     std     �  �   )     �  . �  �  _Allocate_at_least_helper 蒧燊偁]蝰 �  �  _Release a笀/$矊|篁� �  �  {dtor} 閃+y崣瘃 �  �  {ctor} �i*聫 �  �  _Release �犱N篁� �  �  {dtor} ┗攧嚶@� �  �  {ctor} 乗d#皈T唏" $  <  _Orphan_range H	�;ヲ� $  :  _Xlength Z鍠椶s5朋蝰" $  8  _Change_array o馢楇Y蝰& $  5  _Calculate_growth 漪]鞢蝰 $  )  max_size `癶�%Y婠篁�" �  �  _Orphan_range t檫�畃蝰" �  �  _Change_array 4�B*唑�& �  �  _Calculate_growth ENZ� �
蝰 �  �  allocate l]3讚庴:篁�"    �  _Orphan_range ke� 蝰"    �  _Change_array H氝薬妠R蝰&    �  _Calculate_growth kA�v:t蝰     allocate �5b礃hu篁� $  ?  _Getal 瘴�:拖>!� $  )  capacity A菫D溶蘇篁� 0  A  max_size !*/T�篁� �  �  capacity 野;H咼篁�    �  capacity 崞`�2An篁� F  K  _Get_first t娢戋�<�
     std     �  �        �   �  �  swap ゃ�*N�篁�
     std     �  �         �  & �  �  _Adl_verify_range (v{X'蝰
     std 
    @   @    �   �  �  _Unfancy 糄倯埩�9篁�
     std     @  @     �         �  " �  �  _Sort_unchecked ��︸鰧
     std 
    	       �   �  �  move 崈郼蔆{戵蝰    �  �   	@   �  �    �       �  �  operator() 3:�m甃w�
     std     �  L        �  & �  �  _Construct_in_place N�
�Ъ�
     std     �  �   @     �  . �  �  _Allocate_at_least_helper iWgI菿轵�    3  �  L   	   0         �       0  �  construct 綂fr敋蝰
     std 
       @    �  " �  �  _Get_unwrapped '\7�S�
     std     �  �  @  �   @     �  & �  �  _Uninitialized_move �2@磹:�
     std     �          �  & �  �  _Construct_in_place 補)q�/?�
     std 
    �   �    �   �  �  _Unfancy 叀澨/S~阵蝰    �  �     	   �         �       �  �  construct �6r>Yo旁蝰
     std 
    !   "    �  " �  �  _Get_unwrapped q朧�怄�
     std     �  �  i  �   i     �  & �  �  _Uninitialized_move 褡%E嘣�
     std 
 �    
 �  �  
    $   �    �   �  �  move 9�k�%瞒篁�
     std 
    �   b    �   �  �  to_address v^P嫇
     std     �  �  �   �     �  " �  �  _Copy_memmove �+E亨v{G蝰
    �   	   �  �     �      " �  �  _Emplace_back SC壨蝰
     std     �  C        �  & �  �  _Construct_in_place /�捫
     std 
    �   �           _Unfancy UH)PY结篁�    *  �  C   	                       construct *堁�;�0蝰
     std 
    (   )    	  "   
  _Get_unwrapped |�瀲肳
     std     �  �  l  �   l     
  &     _Uninitialized_move �/��=憕
     std 
     
   �  
    +              move �;P�Us篁�
     std 
    �   �           to_address 柈at槆e_�
     std     �  �  �   �       "     _Copy_memmove ?睼&硩盺蝰
    �   	   �  �           " �    _Emplace_back 祕O鲀�3蝰
     std  #        " !  "  _Get_size_of_n [ㄐ+7︸
     std          $  %  _Allocate ㎜苾�$舔�
     std " '  "  _Get_size_of_n 前箩蹒�     _Release 肇�#Y�&篁�     {dtor} @硞_N�     {ctor} 袍篼<Njm� %  +  allocate gj灦Z壌蝰
     std     �  �  �   @     .  . -  /  _Insertion_sort_unchecked 涣)搋伮候�
     std     @  @  �         2  * 1  3  _Make_heap_unchecked 錴�?�
篁�
     std     @  @  �         6  * 5  7  _Sort_heap_unchecked 跇
|嗶B 篁�
     std     @  @  �   �    :  : 9  ;  _Partition_by_median_guess_unchecked W釋斚j栿蝰
     std 
       @    >   =  ?  addressof ap鶛q�4蝰
     std     
  L   @    B  " A  C  construct_at 靸縤�".篁�
     std     @  @  @   @     F  " E  G  _Copy_memmove �4К諐狐蝰
     std 
    6   @    J   I  K  _To_address 眐a撰
       	          M      "   N  _Emplace_back ;誶锭0`蝰
     std 
 �    
        Q    R   P  S  addressof z咽4�%买�
     std 
 �             V    W  " U  X  construct_at 4澁2昘蝰
     std 
    9   #    [   Z  \  move 粯�&兏胟篁�
    #   	   �  �     ^      " �  _  _Emplace_back 列J>A-s2蝰
     std 
 �  ,  
    <   b    c   a  d  forward e溞侫+$�    �  %  �   	   �         f       �  g  construct �(榫8蝰
     std 
     
    '   j    k   i  l  addressof O$樳oU苠蝰
     std 
         &  C   o    p  " n  q  construct_at ǜ�	C-篁�
     std 
    >   *    t   s  u  move �- ck~Jb篁�
    *   	   �  �     w      " �  x  _Emplace_back 濷 龈仳�
     std 
   ,  
    A   {    |   z  }  forward H(=t銊s    *  ,  �   	                     �  construct Q誱溷g
瞅�
     std 6 �  %  _Allocate_manually_vector_aligned .m?N瓿9.蝰
     std " �  "  _Get_size_of_n 腹,_嘠,�    -  .   	0   �  0    �       �  �  operator() cT煐�
     std     @  @  @   @     �  . �  �  _Move_backward_unchecked F�咎偙篁�
     std     @        1  �         �  * �  �  _Pop_heap_hole_by_index �殖佃傿
     std     @  @  �         �  & �  �  _Pop_heap_unchecked k]块�
     std 
    @   @     �   �  �  _Prev_iter 鞜M代�
     std     @  @  @  �         �  * �  �  _Guess_median_unchecked �鲛e� 
     std 
    @   @     �   �  �  _Next_iter 筐K謯�    2  3   	   �  �   
 �       �  �  {ctor} 得#&褆琋�
     std 
    4   @    �   �  �  to_address 鹃o圥�>�
     std 
    G   8    �   �  �  forward ︸ ap螫    3  7  8   	   0         �       0  �  construct 廧〩釺_膨�
     std 
    I   ;    �   �  �  forward 蕽魍欛顝    �  :  ;   	   �         �       �  �  construct l	飗�#蝰
     std 
 �        =  �   �    �  " �  �  construct_at y�,6塖�<篁�
     std 
    K   @    �   �  �  forward z.夆鐔勮    *  ?  @   	            �         �  construct I�蝰
     std 
         B  �   �    �  " �  �  construct_at �#Rs填篁�
     std     @  @  @   @     �  * �  �  _Copy_backward_memmove �4MS�.��
     std     @        D  �         �  & �  �  _Push_heap_by_index 剑=�/�
     std     @  @  @  E  �         �  . �  �  _Pop_heap_hole_unchecked ]!N瑑｝篁�
     std     @  @  @  �         �  " �  �  _Med3_unchecked �2V�$<�
     std 
 @  ,  
    M   �    �   �  �  forward =箚^F?
     std     H  8   @    �  " �  �  construct_at 鶕磩,J=篁�
     std 
 �        J  ;   �    �  " �  �  construct_at ='�+3呦篁�
     std 
         L  @   �    �  " �  �  construct_at 樎\!抍峰篁�          6     �  _invalid_parameter_noinfo_noreturn 7毺欛詮�        "     �  operator delete ln%�=
    蝰
 �    
         �  �         �  *     �  __std_exception_copy ＼?)恉�	篁�
     std 
             �  " �  �  _Xlength_error 蠫Vn泑�
    #         �  "     �  operator new 煮g貙I汅蝰" �  �  allocateSegment 麺J�9�
    �         �  *     �  __std_exception_destroy �-�-穞�2
 #    蝰�    _Functor �  _Pmf_object 蝰  _Pmf_refwrap �  _Pmf_pointer �  _Pmd_object 蝰  _Pmd_refwrap �  _Pmd_pointer 馚   t      std::_Invoker_strategy .?AW4_Invoker_strategy@std@@    q  U  F   �              std::_Iterator_base12 .?AU_Iterator_base12@std@@ �
    
    蝰
   ,  
       	        
        	        
                 	  
   ,   	              J   �              std::_Container_base12 .?AU_Container_base12@std@@ 篁�
 
   
 
   蝰
   ,  
       	   
             	   
     
                   
 
  ,   	  
              	   
             
       	   
            F   �              std::_Container_proxy .?AU_Container_proxy@std@@ �
    
 
    
       	        
        	        
                   
     
     J     _Container_proxy 篁�
 !    _Mycont 蝰
 "   _Myfirstiter 馞  #           std::_Container_proxy .?AU_Container_proxy@std@@ � $  �  �  
        _Container_base12 蝰   operator= 蝰   _Orphan_all    _Swap_proxy_and_iterators 蝰
 &    _Myproxy �   _Orphan_all_unlocked_v3    _Swap_proxy_and_iterators_unlocked �   _Orphan_all_locked_v3 蝰   _Swap_proxy_and_iterators_locked 篁馢 
 &'           std::_Container_base12 .?AU_Container_base12@std@@ 篁� (  �  �  
    !   	         *      
     	!    ,           �  
  _Iterator_base12 篁�   operator= 蝰 +  _Adopt � -  _Getcont 篁� �  _Unwrap_when_unverified 
 &    _Myproxy �
 "   _Mynextiter 蝰F  &.           std::_Iterator_base12 .?AU_Iterator_base12@std@@ � /  �  �  
 t    蝰
 u    蝰
 #    蝰
 w    4  #     駣   _Comparison_category_none   _Comparison_category_partial �  _Comparison_category_weak    _Comparison_category_strong 蝰J       6  std::_Comparison_category .?AW4_Comparison_category@std@@ 蝰 7  �    
 #    蝰F   �              std::_Num_float_base .?AU_Num_float_base@std@@ 篁矜   S    蝰 X  has_denorm � �  has_infinity 篁� �  has_quiet_NaN 蝰 �  has_signaling_NaN 蝰 �  is_bounded � �  is_iec559 蝰 �  is_signed 蝰 �  is_specialized � \  round_style  �  radix 蝰F   ;           std::_Num_float_base .?AU_Num_float_base@std@@ 篁� <  V  ~   N   �              std::numeric_limits<bool> .?AV?$numeric_limits@_N@std@@ 蝰 	0   >               �   R    蝰 ?  min  ?  max  ?  lowest � ?  epsilon  ?  round_error  ?  denorm_min � ?  infinity 篁� ?  quiet_NaN 蝰 ?  signaling_NaN 蝰 �  digits 馧   @           std::numeric_limits<bool> .?AV?$numeric_limits@_N@std@@ 蝰 A  V  �   N   �              std::numeric_limits<char> .?AV?$numeric_limits@D@std@@ 篁� 	p   C               �   R    蝰 D  min  D  max  D  lowest � D  epsilon  D  round_error  D  denorm_min � D  infinity 篁� D  quiet_NaN 蝰 D  signaling_NaN 蝰 �  is_signed 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馧   E           std::numeric_limits<char> .?AV?$numeric_limits@D@std@@ 篁� F  V  �   R   �              std::numeric_limits<signed char> .?AV?$numeric_limits@C@std@@  	   H               �   R    蝰 I  min  I  max  I  lowest � I  epsilon  I  round_error  I  denorm_min � I  infinity 篁� I  quiet_NaN 蝰 I  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馬 
  J           std::numeric_limits<signed char> .?AV?$numeric_limits@C@std@@  K  V  �   V   �              std::numeric_limits<unsigned char> .?AV?$numeric_limits@E@std@@ 蝰 	    M               �   R    蝰 N  min  N  max  N  lowest � N  epsilon  N  round_error  N  denorm_min � N  infinity 篁� N  quiet_NaN 蝰 N  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  O           std::numeric_limits<unsigned char> .?AV?$numeric_limits@E@std@@ 蝰 P  V    R   �              std::numeric_limits<char8_t> .?AV?$numeric_limits@_Q@std@@ 篁� 	|   R               �   R    蝰 S  min  S  max  S  lowest � S  epsilon  S  round_error  S  denorm_min � S  infinity 篁� S  quiet_NaN 蝰 S  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  T           std::numeric_limits<char8_t> .?AV?$numeric_limits@_Q@std@@ 篁� U  V  ?  R   �              std::numeric_limits<char16_t> .?AV?$numeric_limits@_S@std@@ 蝰 	z   W               �   R    蝰 X  min  X  max  X  lowest � X  epsilon  X  round_error  X  denorm_min � X  infinity 篁� X  quiet_NaN 蝰 X  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  Y           std::numeric_limits<char16_t> .?AV?$numeric_limits@_S@std@@ 蝰 Z  V  l  R   �              std::numeric_limits<char32_t> .?AV?$numeric_limits@_U@std@@ 蝰 	{   \               �   R    蝰 ]  min  ]  max  ]  lowest � ]  epsilon  ]  round_error  ]  denorm_min � ]  infinity 篁� ]  quiet_NaN 蝰 ]  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  ^           std::numeric_limits<char32_t> .?AV?$numeric_limits@_U@std@@ 蝰 _  V  �  R   �              std::numeric_limits<wchar_t> .?AV?$numeric_limits@_W@std@@ 篁� 	q   a               �   R    蝰 b  min  b  max  b  lowest � b  epsilon  b  round_error  b  denorm_min � b  infinity 篁� b  quiet_NaN 蝰 b  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  c           std::numeric_limits<wchar_t> .?AV?$numeric_limits@_W@std@@ 篁� d  V  �  N   �              std::numeric_limits<short> .?AV?$numeric_limits@F@std@@ 蝰 	   f               �   R    蝰 g  min  g  max  g  lowest � g  epsilon  g  round_error  g  denorm_min � g  infinity 篁� g  quiet_NaN 蝰 g  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馧 
  h           std::numeric_limits<short> .?AV?$numeric_limits@F@std@@ 蝰 i  V  �  J   �              std::numeric_limits<int> .?AV?$numeric_limits@H@std@@  	t   k               �   R    蝰 l  min  l  max  l  lowest � l  epsilon  l  round_error  l  denorm_min � l  infinity 篁� l  quiet_NaN 蝰 l  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馢 
  m           std::numeric_limits<int> .?AV?$numeric_limits@H@std@@  n  V    N   �              std::numeric_limits<long> .?AV?$numeric_limits@J@std@@ 篁� 	   p               �   R    蝰 q  min  q  max  q  lowest � q  epsilon  q  round_error  q  denorm_min � q  infinity 篁� q  quiet_NaN 蝰 q  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馧 
  r           std::numeric_limits<long> .?AV?$numeric_limits@J@std@@ 篁� s  V  H  V   �              std::numeric_limits<unsigned short> .?AV?$numeric_limits@G@std@@ � 	!   u               �   R    蝰 v  min  v  max  v  lowest � v  epsilon  v  round_error  v  denorm_min � v  infinity 篁� v  quiet_NaN 蝰 v  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  w           std::numeric_limits<unsigned short> .?AV?$numeric_limits@G@std@@ � x  V  �  V   �              std::numeric_limits<unsigned int> .?AV?$numeric_limits@I@std@@ 篁� 	u   z               �   R    蝰 {  min  {  max  {  lowest � {  epsilon  {  round_error  {  denorm_min � {  infinity 篁� {  quiet_NaN 蝰 {  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  |           std::numeric_limits<unsigned int> .?AV?$numeric_limits@I@std@@ 篁� }  V  �  V   �              std::numeric_limits<unsigned long> .?AV?$numeric_limits@K@std@@ 蝰 	"                  �   R    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  �           std::numeric_limits<unsigned long> .?AV?$numeric_limits@K@std@@ 蝰 �  V  �  Z   �              std::numeric_limits<unsigned __int64> .?AV?$numeric_limits@_K@std@@ 蝰 	#   �               �   R    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馴 
  �           std::numeric_limits<unsigned __int64> .?AV?$numeric_limits@_K@std@@ 蝰 �  V  (  N   �              std::numeric_limits<float> .?AV?$numeric_limits@M@std@@ 蝰 	@   �               J  :    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 馧   �           std::numeric_limits<float> .?AV?$numeric_limits@M@std@@ 蝰 �  V  T  N   �              std::numeric_limits<double> .?AV?$numeric_limits@N@std@@ � 	A   �               J  :    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 馧   �           std::numeric_limits<double> .?AV?$numeric_limits@N@std@@ � �  V  �  R   �              std::numeric_limits<long double> .?AV?$numeric_limits@O@std@@  	A   �               J  :    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 馬   �           std::numeric_limits<long double> .?AV?$numeric_limits@O@std@@  �  V  �  :   �              _TypeDescriptor .?AU_TypeDescriptor@@  p   #      �6 
 �    pVFTable �
    spare 
 �   name �:   �           _TypeDescriptor .?AU_TypeDescriptor@@ j     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\predefined C++ types (compiler internal) � �  �  �   
 �    &   �              _PMD .?AU_PMD@@ 蝰2 
 t     mdisp 
 t    pdisp 
 t    vdisp &   �           _PMD .?AU_PMD@@ 蝰 �  �  {   
             �  
 �    v 
 u     properties 篁�
 �   pType 
 �   thisDisplacement �
 t    sizeOrOffset �
 �   copyFunction �>   �          $ _s__CatchableType .?AU_s__CatchableType@@  �  �  �   
 w    >   �              _s__CatchableType .?AU_s__CatchableType@@ 
 �   蝰
 �     �  #      �> 
 t     nCatchableTypes 蝰
 �   arrayOfCatchableTypes J   �           _s__CatchableTypeArray .?AU_s__CatchableTypeArray@@ 蝰 �  �  �   
 �    B     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi 蝰N     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\bin\HostX64\x64\CL.exe 蝰2     D:\RTXPT\External\Rtxdi\Source\ReGIR.cpp 篁馬     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb �     -c -ID:\RTXPT\External\Rtxdi\include -Z7 -nologo -W1 -WX -diagnostics:column -MP -O2 -Ob2 -D_MBCS -DWIN32 -D_WINDOWS -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DNDEBUG -DRTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\") -DCMAKE_INTDIR=\"Release\" 聱      -EHs -EHc -MT -GS -fp:precise -Zc:wchar_t -Zc:forScope -Zc:inline -GR -std:c++20 -external:W1 -Gd -TP -errorreport:prompt -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include �      -ID:\1softwares\VS2022\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" �      -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -I"C:\Program 篁颃      Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\include 蝰�      -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" -external:I"C:\Program 聱      Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -external:I"C:\Program 蝰"    �  �  �  �  �  �  �  > �   Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -X 蝰  �  �  �  �  �  蝰坂晗i�ku�7vā膆�K蜌�(杯聍氫m�"嘇�%f僱ㄒ餌貯"R�禘]禷W罙"R�秈捶?}┚8A"R�秖臁D嗽镜F绣稴勩�諓裛s鲚溏q兗�&�6督抋�裛s鲚溏qv諲d睇Rk滯阴苎`s鲚溏qg?F鷾1秄M!d0钒u@<B痶泳=仩]豢,�6
>孔]Mbkl�梔傻襖uN/蠲迿窸`Ｈ�圢紟�8~�	�(蛁吿�o瘽册砇妱隂S嚤踖p禭穸培盪刚嚤踖p禭�1~
/(�6萪O�[D攨G筀霵婬(κ�)�*�)荦�7陘<U埯j廦¤胴'洋m|!纤檊��;}溉翜熘JR湼_嶀預棊膬=伉僪�;墠犷A棊膬�<�7F德徇�5>湽喀B)鳥箩邆5>�D蛌u箩邆5>饷!勩�稽�貵蓽k羓謼� =蒘歖S┌,闉晦甬]啃胊h椡�8D9b�
B鴈$;4�B緤`QI蚶� MI矱┧}箔
伤殐l頟P%HKS骅G;x氪袣撕�7鋕讀ル
殬z?垽鏈鐨崬斜[砡廈Qw舽Q7蓑谠W網贜��3H昸鍻瓾`哷翮$�恮t+柶,�7L�$亴勅l�W�┧�飌�%I栶賑?T巼庒'�2�Ｗ鲴oa�Ojf]{謑pて蘷UEBhf]{謑p+8邢KbdAf]{謑p荂櫫qfdpX墺鉪d柲^�s鱒酕嵛奕腪ZW耽RA鮧�\]S0魪1jnN鵘J庖獩蝖@B秒嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄酲;[純o穗嘕-WV8o嗹啴r殅W-坓�(鬄�汬'这�-坓�(鬄鮳�>i,夿-坓�(鬄�汬'这栯嘕-WV8o.*~襠[
B���>H3*臄褼搢dd�a�:_棢杻#Q�賴矖蚳憎X觨椗
a�>20dd�a�:_棢杻#Q8�,惝�烗餯繅鬮�摮Dk.,;U��%�L扟�9竮e{E佋~y�)^肶8vIⅰ婗-~�(	"鳫h遙=�C�H�$�dd�a�:j�=�蓈痔r�=觱釄氹嘕-WV8oW 9�~&-坓�(鬄酲;[純o穗嘕-WV8om�M%>mb-坓�(鬄�汬'这�)�8蛾爨lVGd匱韣y*�杜`颀l+�鞯.r擣�0G#盱谑﹝j�り"(��苳乮5絚_}4n4�硓�)�8蛾爨昉}s畏巠*�杜`颀l+�鞯.r擣�0G#盱谑j啣�q鰱(��苳乮5絚_}4n4�硓�)�8蛾爨鏷蹘洦昖y*�杜`颀l+�鞯.r擣�0G#盱谑坦�褮u(��苳乮5絚_}4n4�硓榌<�<騙螀Q5洪m!悞⌒��*嚮oG�`?烹|W\-0i�! T坿弰囚�<毘罿�*埗玚?烹|W\�]�)�7~9E\$L釉��E光9E\$L釉��E光5�蘫� 輽� �9E\$L釉��E光-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 贋XCRC冼�^笵A傮:塪逮0[豍&��1�揰煺匵!祦佪�7蜋}凲邌�.躜;� +h[謧�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       @h                .text$mn       :      眡�     .debug$S                    .text$mn             補胑     .debug$S       �             .text$mn       0      燥"V     .debug$S       �             .text$mn    	   0      燥"V     .debug$S    
   �         	    .text$mn       0      燥"V     .debug$S       �             .text$mn    
   �     桒�     .debug$S       �  f       
    .text$x        (      镽=
    .text$mn       #     碡(G     .debug$S       $  `           .text$x        (      镽=    .text$mn       �     z-�     .debug$S       X  f           .text$x        (      镽=    .text$mn       �      W�     .debug$S                     .text$mn       �     G橞�     .debug$S       �  >           .text$mn            兠J�     .debug$S       �	  l           .text$mn       �      搷壖     .debug$S       �             .text$x              }�+c    .text$mn            绩�     .debug$S        �
  J           .text$x     !         ��:    .text$mn    "   <      .ズ     .debug$S    #   0  
       "    .text$mn    $   <      .ズ     .debug$S    %   L  
       $    .text$mn    &   !      :著�     .debug$S    '   <         &    .text$mn    (   2      X于     .debug$S    )   <         (    .text$mn    *   [      穥�     .debug$S    +   �         *    .text$mn    ,         �%     .debug$S    -   T         ,    .text$mn    .   e      凚Kr     .debug$S    /   �         .    .text$mn    0         ��#     .debug$S    1   �          0    .text$mn    2         ��#     .debug$S    3   �          2    .text$mn    4   B      贘S     .debug$S    5             4    .text$mn    6   B      贘S     .debug$S    7            6    .text$mn    8   B      贘S     .debug$S    9   �          8    .text$mn    :   V      湴�-     .debug$S    ;   �         :    .text$mn    <          罂橌     .debug$S    =   �          <    .text$mn    >   z     <b喻     .debug$S    ?   P  �       >    .text$x     @         �$�;>    .text$x     A         T�>    .text$mn    B          *V�     .debug$S    C   �          B    .text$mn    D          Ю嫀     .debug$S    E   �          D    .text$mn    F          x�:�     .debug$S    G   �          F    .text$mn    H          彘|T     .debug$S    I            H    .text$mn    J         c枎]     .debug$S    K            J    .text$mn    L          fi:/     .debug$S    M   �          L    .text$mn    N   x  
   嶬踨     .debug$S    O   D  >       N    .text$mn    P          SPG     .debug$S    Q             P    .text$mn    R          J飱K     .debug$S    S   @         R    .text$mn    T          aJ鄔     .debug$S    U   �          T    .text$mn    V   z      �
     .debug$S    W   �         V    .text$mn    X         �ッ     .debug$S    Y   �          X    .text$mn    Z         �ッ     .debug$S    [   �          Z    .text$mn    \         �ッ     .debug$S    ]   �          \    .text$mn    ^   B      鸮     .debug$S    _   �         ^    .text$mn    `   B      臣6     .debug$S    a   �         `    .text$mn    b   A      俙Z%     .debug$S    c   �         b    .text$mn    d         崪覩     .debug$S    e   �          d                                        #                F                [                s       (        �       2        �       d        �       8        �           i�                          "        -      4        L          i�                    k      &        �      0        �      $        �      6                  i�                    5      T        ]               |      `        �      ,        4      V        �      Z        �      b        F      \        �      .        �                            `      P        �      B        �      H        �      F        ]      J        �      D              L        a      R        �      N        �      >        .      <        e      :        �               �      ^        #	      *        R	      X        �	      
        �	              t
              '              l              �      	                       �              �              
              [
              �
              �
              �              c              �      !              @        X      A        �               �           cosf             floorf           logf             memmove          powf             sinf             sqrtf            $LN5        (    $LN10       8    $LN7        "    $LN13       4    $LN10       $    $LN16       6    $LN3        T    $LN4        T    $LN20   B   `    $LN23       `    $LN27   z   V    $LN30       V    $LN3       Z    $LN4        Z    $LN20   A   b    $LN23       b    $LN3       \    $LN4        \    $LN35   e   .    $LN38       .    $LN154        $LN161          $LN36           $LN7        J    $LN75       N    $LN193  z  >    $LN198      >    $LN13       :    $LN20   B   ^    $LN23       ^    $LN30   [   *    $LN33       *    $LN3       X    $LN4        X    $LN145  �  
        �  
       $LN150      
    $LN145  �            
       $LN150          $LN145  #          �  
       $LN150          $LN146          $LN4            $LN4        	    $LN152          $LN4            $LN14   :       $LN17           .xdata      f          （亵(        �      f    .pdata      g          T枨(        �      g    .xdata      h          %蚘%8        �      h    .pdata      i         惻竗8        �      i    .xdata      j          （亵"              j    .pdata      k         2Fb�"        H      k    .xdata      l          %蚘%4        p      l    .pdata      m         惻竗4        �      m    .xdata      n          （亵$        �      n    .pdata      o         2Fb�$        �      o    .xdata      p          %蚘%6        $      p    .pdata      q         惻竗6        V      q    .xdata      r          懐j濼        �      r    .pdata      s         Vbv鵗        �      s    .xdata      t          �9�`        �      t    .pdata      u         惻竗`        I      u    .xdata      v          （亵V        �      v    .pdata      w         X崘=V              w    .xdata      x          �9�Z        |      x    .pdata      y         �1癦        �      y    .xdata      z          �9�b        O      z    .pdata      {         s�7錬        �      {    .xdata      |          �9�\        �      |    .pdata      }         �1癨        Z      }    .xdata      ~          （亵.        �      ~    .pdata               弋�.        �          .xdata      �          (臵        (      �    .pdata      �          $�        h      �    .xdata      �   	      � )9        �      �    .xdata      �         j        �      �    .xdata      �          嵑釄        1      �    .xdata      �         鉪        s      �    .pdata      �         �0�        �      �    .xdata      �   	      � )9        8      �    .xdata      �         j        �      �    .xdata      �          \{j              �    .xdata      �          僣糐        m      �    .pdata      �         #1iJ        �      �    .xdata      �          >�5擭        @      �    .pdata      �         菜	N        �      �    .xdata      �   8      G架^N        �      �    .pdata      �         F>N        3      �    .xdata      �         鸛湴N        �      �    .pdata      �         �肘N        �      �    .xdata      �   H      �>        +      �    .pdata      �         �ⅷo>        h      �    .xdata      �   	      � )9>        �      �    .xdata      �   
      輴�>        �      �    .xdata      �          |鑒�>        (       �    .xdata      �          （亵:        g       �    .pdata      �         A鶬�:        �       �    .xdata      �          �9�^        "!      �    .pdata      �         惻竗^        W!      �    .xdata      �          （亵*        �!      �    .pdata      �         愶L*        �!      �    .xdata      �          �9�X        �!      �    .pdata      �         �1癤        4"      �    .xdata      �         萦[�
        o"      �    .pdata      �         w瓆�
        �"      �    .xdata      �   
      B>z]
        $#      �    .xdata      �          �2g�
        �#      �    .xdata      �         T�8
        �#      �    .xdata      �         r%�
        ?$      �    .xdata      �   	       \�<{
        �$      �    .xdata      �          3賟P
        �$      �    .pdata      �         銀�*
        f%      �    .voltbl     �                  _volmd      �    .xdata      �         萦[�        �%      �    .pdata      �         惱        s&      �    .xdata      �   
      B>z]        '      �    .xdata      �          �2g�        �'      �    .xdata      �         T�8        e(      �    .xdata      �         r%�        )      �    .xdata      �   	       �8雗        �)      �    .xdata      �          3賟P        T*      �    .pdata      �         銀�*        +      �    .voltbl     �                  _volmd      �    .xdata      �         萦[�        �+      �    .pdata      �         渢f�        t,      �    .xdata      �   
      B>z]        .-      �    .xdata      �          �2g�        �-      �    .xdata      �         T�8        �.      �    .xdata      �         r%�        i/      �    .xdata      �   	       螏汖        (0      �    .xdata      �          3賟P        �0      �    .pdata      �         銀�*        �1      �    .voltbl     �                  _volmd      �    .xdata      �          Qsv        z2      �    .pdata      �         �%zO        �2      �    .xdata      �         "�0�        3      �    .pdata      �         Ni朤        a3      �    .xdata      �         �-�        �3      �    .pdata      �         羭架        �3      �    .xdata      �         帀隳        K4      �    .pdata      �         3廹�        �4      �    .xdata      �         �-�        �4      �    .pdata      �         p$`�        55      �    .xdata      �          %蚘%        �5      �    .pdata      �         }S蛥        �5      �    .xdata      �          %蚘%	        :6      �    .pdata      �         }S蛥	        �6      �    .xdata      �          �F�        	7      �    .pdata      �         _襩�        }7      �    .xdata      �          %蚘%        �7      �    .pdata      �         }S蛥        &8      �    .xdata      �          �9�        [8      �    .pdata      �         礝
        �8      �    .rdata      �                      9     �    .rdata      �          �;�         +9      �    .rdata      �                      R9     �    .rdata      �                      i9     �    .rdata      �          �)         �9      �    .xdata$x    �                      �9      �    .xdata$x    �         虼�)         �9      �    .data$r     �   /      嶼�         �9      �    .xdata$x    �   $      4��         !:      �    .data$r     �   $      鎊=         v:      �    .xdata$x    �   $      銸E�         �:      �    .data$r     �   $      騏糡         �:      �    .xdata$x    �   $      4��         �:      �        (;           .rdata      �          IM         ;;      �    .rdata$r    �   $      'e%�         a;      �    .rdata$r    �         �          y;      �    .rdata$r    �                      �;      �    .rdata$r    �   $      Gv�:         �;      �    .rdata$r    �   $      'e%�         �;      �    .rdata$r    �         }%B         �;      �    .rdata$r    �                      �;      �    .rdata$r    �   $      `         <      �    .rdata$r    �   $      'e%�         '<      �    .rdata$r    �         �弾         J<      �    .rdata$r    �                      k<      �    .rdata$r    �   $      H衡�         �<      �    .rdata      �          =-f�         �<      �    .rdata      �          v靛�         �<      �    .rdata      �          y蘮�         �<      �    .rdata      �          2T頄         �<      �    .rdata      �          Z尨6         �<      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .debug$T    �   窸               .chks64     �   �                =  ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z ??1?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@QEAA@XZ ?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ ?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ ?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z ?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ ??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ ??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ?IsLocalLightPowerRISEnable@ReGIRContext@rtxdi@@QEBA_NXZ ?GetReGIRCellOffset@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRLightSlotCount@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRGridCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRGridCalculatedParameters@2@XZ ?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ?GetReGIRStaticParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRStaticParameters@2@XZ ?SetDynamicParameters@ReGIRContext@rtxdi@@QEAAXAEBUReGIRDynamicParameters@2@@Z ?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z ?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ ?ComputeGridLightSlotCount@ReGIRContext@rtxdi@@AEAAXXZ ?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z ??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ ?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ ??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z ??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z ??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z ??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z ??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z ??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z ??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z ??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Guess_median_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM00U?$less@X@0@@Z ??$_Copy_backward_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA ?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA ?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA ?dtor$0@?0???0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z@4HA ?dtor$0@?0???0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z@4HA ?dtor$0@?0??ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ@4HA ?dtor$1@?0??ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ@4HA _CxxThrowException __CxxFrameHandler4 __catch$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z$0 __catch$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z$0 __catch$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z$0 $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z $pdata$?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z $unwind$?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ $pdata$?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ $unwind$?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z $pdata$?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z $unwind$?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ $unwind$??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ $pdata$??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ $unwind$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $pdata$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $cppxdata$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $stateUnwindMap$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $ip2state$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $unwind$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $pdata$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $cppxdata$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $stateUnwindMap$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $ip2state$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $unwind$?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ $pdata$?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ $unwind$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $chain$9$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$9$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $chain$10$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$10$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $unwind$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $pdata$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $cppxdata$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $stateUnwindMap$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $ip2state$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $unwind$?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z $pdata$?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z $unwind$?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z $pdata$?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z $unwind$??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ $pdata$??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ $unwind$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $pdata$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $cppxdata$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $stateUnwindMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $tryMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $handlerMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $ip2state$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA $unwind$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$0$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$0$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$1$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$1$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$2$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$2$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$3$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$3$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $unwind$??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z $unwind$??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z $unwind$??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z $pdata$??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z $unwind$??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z $pdata$??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@3f000000 __real@3f800000 __real@40490fdb __real@40c90fdb __real@beaaaaab 