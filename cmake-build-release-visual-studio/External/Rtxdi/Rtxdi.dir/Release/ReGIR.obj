d嗶 緢� �      .drectve        <  4&               
 .debug$S        軀  p'              @ B.debug$T        h   L�              @ B.text$mn        :   搐  瞍          P`.debug$S          �  �         @B.text$mn           ぇ  户          P`.debug$S        �  脓  ]�         @B.text$mn        0   椹  �          P`.debug$S        �  #�  揩         @B.text$mn        0   K�  {�          P`.debug$S        �  叕  A�         @B.text$mn        0   彤            P`.debug$S        �  �  钒         @B.text$mn        �  C�  岵          P`.debug$S        �  1�  �      f   @B.text$x         (   
�  5�          P`.text$mn        #  I�  l�          P`.debug$S        $  寂  嘌      `   @B.text$x         (   犝  日          P`.text$mn        �  苷  }�          P`.debug$S        X  妥  %�      f   @B.text$x         (   !�  I�          P`.text$mn        �  ]�               P`.debug$S          溟  祓          @B.text$mn        �  ,�  轸          P`.debug$S        �  篝  稞      >   @B.text$mn          [  c         P`.debug$S        �	  � g
     l   @B.text$mn        �   � �         P`.debug$S        �  � T        @B.text$x                      P`.text$mn          " 2         P`.debug$S        �
  � V$     J   @B.text$x            :' J'         P`.text$mn        <   T' �'         P`.debug$S        0  �' �(     
   @B.text$mn        <   B) ~)         P`.debug$S        L  �) �*     
   @B.text$mn        !   L+ m+         P`.debug$S        <  �+ �,        @B.text$mn        2   �, +-         P`.debug$S        <  ?- {.        @B.text$mn        [   �. N/         P`.debug$S        �  b/ 62        @B.text$mn           3 3         P`.debug$S        T  !3 u4        @B.text$mn        e   �4 5         P`.debug$S        �  45 �7        @B.text$mn           �8 �8         P`.debug$S        �   �8 �9        @B.text$mn           �9 
:         P`.debug$S        �   : �:        @B.text$mn        B   :; |;         P`.debug$S           �; �<        @B.text$mn        B   �< =         P`.debug$S          6= F>        @B.text$mn        B   �> �>         P`.debug$S        �   �> �?        @B.text$mn        V   @ p@         P`.debug$S        �  凘 (B        @B.text$mn           燘              P`.debug$S        �   禕         @B.text$mn        z  釩 \I         P`.debug$S        P  LJ 淺     �   @B.text$x            萣 詁         P`.text$x            辀 阞         P`.text$mn           鬮              P`.debug$S        �   鱞 遚        @B.text$mn           d              P`.debug$S        �   .d e        @B.text$mn           Ze              P`.debug$S        �   he `f        @B.text$mn           渇              P`.debug$S          籪 蟝        @B.text$mn           h )h         P`.debug$S          3h 7i        @B.text$mn           si              P`.debug$S        �   巌 ~j        @B.text$mn        x  簀 2n     
    P`.debug$S        D  杗 趘     >   @B.text$mn           Fy              P`.debug$S           Xy Xz        @B.text$mn           攝              P`.debug$S        @   鋥        @B.text$mn            4| T|         P`.debug$S        �   r| 6}        @B.text$mn        z   r} 靰         P`.debug$S        �   ~ 纮        @B.text$mn           渷 瓉         P`.debug$S        �   羴 檪        @B.text$mn           諅 鎮         P`.debug$S        �   鷤 騼        @B.text$mn           .� ?�         P`.debug$S        �   S� ?�        @B.text$mn        B   {� 絽         P`.debug$S        �  褏 m�        @B.text$mn        B   q� 硥         P`.debug$S        �  菈 s�        @B.text$mn        A   w� 笉         P`.debug$S        �  虓 x�        @B.text$mn           |� 彂         P`.debug$S        �   檻 m�        @B.xdata                          @0@.pdata             睊 綊        @0@.xdata             蹝             @0@.pdata             鐠 髵        @0@.xdata             �             @0@.pdata             � %�        @0@.xdata             C�             @0@.pdata             O� [�        @0@.xdata             y�             @0@.pdata             亾 崜        @0@.xdata             珦             @0@.pdata             窊 脫        @0@.xdata             釗             @0@.pdata             閾 鯎        @0@.xdata             �             @0@.pdata             � '�        @0@.xdata             E�             @0@.pdata             M� Y�        @0@.xdata             w�             @0@.pdata             � 嫈        @0@.xdata                          @0@.pdata             睌 綌        @0@.xdata             蹟             @0@.pdata             銛 飻        @0@.xdata             
�             @0@.pdata             � !�        @0@.xdata              ?� _�        @0@.pdata             s� �        @0@.xdata          	   潟         @@.xdata             簳 罆        @@.xdata             蕰             @@.xdata             諘 頃        @0@.pdata             � 
�        @0@.xdata          	   +� 4�        @@.xdata             H� N�        @@.xdata             X�             @@.xdata             ^�             @0@.pdata             f� r�        @0@.xdata             悥             @0@.pdata              礀        @0@.xdata          8   覗 
�        @0@.pdata             (� 4�        @0@.xdata             R� b�        @0@.pdata             �� 寳        @0@.xdata          H   獥 驐        @0@.pdata             � �        @0@.xdata          	   0� 9�        @@.xdata          
   M� Z�        @@.xdata             n�             @@.xdata             u�             @0@.pdata             }� 墭        @0@.xdata                          @0@.pdata             瘶 粯        @0@.xdata             贅             @0@.pdata             針 順        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             =� Y�        @0@.pdata             m� y�        @0@.xdata          
   棛         @@.xdata             聶             @@.xdata             艡 蜋        @@.xdata             讬 迿        @@.xdata          	   铏             @@.xdata             駲             @0@.pdata             鶛 �        @0@.voltbl            #�               .xdata             $� @�        @0@.pdata             T� `�        @0@.xdata          
   ~� 嫐        @@.xdata                          @@.xdata             瑲 礆        @@.xdata             練 艢        @@.xdata          	   蠚             @@.xdata             貧             @0@.pdata             鄽 鞖        @0@.voltbl            
�               .xdata             � '�        @0@.pdata             ;� G�        @0@.xdata          
   e� r�        @@.xdata             悰             @@.xdata             摏 洓        @@.xdata              瑳        @@.xdata          	   稕             @@.xdata             繘             @0@.pdata             菦 記        @0@.voltbl            駴               .xdata             驔             @0@.pdata             
� �        @0@.xdata             4� H�        @0@.pdata             f� r�        @0@.xdata             悳 牅        @0@.pdata             緶 蕼        @0@.xdata             铚 鼫        @0@.pdata             � &�        @0@.xdata             D� T�        @0@.pdata             r� ~�        @0@.xdata             湞             @0@.pdata              礉        @0@.xdata             覞             @0@.pdata             逎 隄        @0@.xdata             �             @0@.pdata             � (�        @0@.xdata             F�             @0@.pdata             R� ^�        @0@.xdata             |�             @0@.pdata             劄 悶        @0@.rdata             疄 茷        @@@.rdata             錇             @@@.rdata             鰹 �        @@@.rdata             ,� D�        @@@.rdata             b�             @@@.xdata$x           w� 摕        @@@.xdata$x            脽        @@@.data$r         /   釤 �        @@�.xdata$x        $   � >�        @@@.data$r         $   R� v�        @@�.xdata$x        $   ��         @@@.data$r         $   笭 軤        @@�.xdata$x        $   鏍 
�        @@@.rdata             �             @@@.rdata$r        $   .� R�        @@@.rdata$r           p� 劇        @@@.rdata$r           帯 殹        @@@.rdata$r        $   ぁ 取        @@@.rdata$r        $   堋  �        @@@.rdata$r           � 2�        @@@.rdata$r           <� P�        @@@.rdata$r        $   d� 垻        @@@.rdata$r        $   湤 愧        @@@.rdata$r           蔻 颌        @@@.rdata$r            �        @@@.rdata$r        $   6� Z�        @@@.rdata             n�             @0@.rdata             r�             @0@.rdata             v�             @0@.rdata             z�             @0@.rdata             ~�             @0@.debug$S        4   偅 叮        @B.debug$S        4   剩         @B.debug$S        @   � R�        @B.chks64         �  f�              
     /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   r  \     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReGIR.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $?A0x23112da8  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $_Binary_hypot   �   �
  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable S �   std::_Trivial_cat<float,float,float &&,float &>::_Same_size_and_compatible P �   std::_Trivial_cat<float,float,float &&,float &>::_Bitcopy_constructible M �   std::_Trivial_cat<float,float,float &&,float &>::_Bitcopy_assignable { �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Same_size_and_compatible x �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Bitcopy_constructible u �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Bitcopy_assignable � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Same_size_and_compatible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Bitcopy_constructible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Bitcopy_assignable . �    std::integral_constant<bool,0>::value : �    std::integral_constant<unsigned __int64,0>::value ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask  �   鸄  $ �    std::strong_ordering::equal ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong ( �  ��I@`anonymous namespace'::c_pi B �   std::allocator<float>::_Minimum_asan_allocation_alignment : �   std::integral_constant<unsigned __int64,2>::value R �   std::allocator<ReGIR_OnionLayerGroup>::_Minimum_asan_allocation_alignment L �   std::allocator<ReGIR_OnionRing>::_Minimum_asan_allocation_alignment      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 z �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Same_size_and_compatible w �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Bitcopy_constructible t �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Bitcopy_assignable - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Same_size_and_compatible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Bitcopy_constructible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Bitcopy_assignable . �   std::integral_constant<bool,1>::value  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  #   rsize_t  (  _TypeDescriptor % k  _s__RTTICompleteObjectLocator2 A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType  �  rtxdi::ReGIRContext $ R  rtxdi::ReGIRDynamicParameters    rtxdi::float3 1 �  rtxdi::LocalLightReGIRFallbackSamplingMode # �  rtxdi::ReGIRStaticParameters ' t  rtxdi::RISBufferSegmentAllocator '   rtxdi::ReGIRGridStaticParameters (   rtxdi::ReGIROnionStaticParameters  {  rtxdi::ReGIRMode  !  rtxdi::uint3 , Y  rtxdi::ReGIROnionCalculatedParameters + d  rtxdi::ReGIRGridCalculatedParameters , �  rtxdi::LocalLightReGIRPresamplingMode  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16 D   std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >  �  std::_Lockit    std::_Num_base # �  std::numeric_limits<char8_t> & s  std::allocator<ReGIR_OnionRing>  �  std::hash<float>  �  std::less<void>    std::_Num_int_base , �  std::allocator<ReGIR_OnionLayerGroup>    std::float_denorm_style = �  std::_Default_allocator_traits<std::allocator<float> >     std::_Compare_t " A  std::numeric_limits<double> ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short> % u  std::_One_then_variadic_args_t   |  std::pmr::memory_resource  �  std::false_type    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t> % �  std::integral_constant<bool,1>     std::_Leave_proxy_unbound b J  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> � �  std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1>  �  std::_Iterator_base12 C �  std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >  �  std::plus<void>  �  std::hash<long double> = �  std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> & �  std::bidirectional_iterator_tag % �  std::integral_constant<bool,0>  
  std::bad_exception & �  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator 3 V  std::_Vector_val<std::_Simple_types<float> > ! ?  std::numeric_limits<float> G �  std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> >  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy = m  std::_Uninitialized_backout_al<std::allocator<float> > $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>  �  std::true_type   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits P ]  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > f �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy ! ,  std::numeric_limits<short>     std::bad_alloc # 2  std::numeric_limits<__int64>  C  std::memory_order ! S  std::pair<float *,float *> > �  std::allocator_traits<std::allocator<ReGIR_OnionRing> >   �  std::forward_iterator_tag   6  std::bad_array_new_length v ~  std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1>  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int> 0 �  std::vector<float,std::allocator<float> > F �  std::vector<float,std::allocator<float> >::_Reallocation_policy   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::_Compare_eq M   std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >    std::nullptr_t & �  std::random_access_iterator_tag ) 8  std::numeric_limits<unsigned long> K "  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > ' "  std::numeric_limits<signed char>  �  std::_Container_base G   std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> >  �  std::_Literal_zero M 3  std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> >      std::numeric_limits<char>  B  std::allocator<float>  �  std::_Unused_parameter ! �  std::ranges::_Set_union_fn # A  std::ranges::_Unique_copy_fn '   std::ranges::_Replace_copy_if_fn & �  std::ranges::_Is_partitioned_fn ( q  std::ranges::_Stable_partition_fn !   std::ranges::_Is_sorted_fn # G  std::ranges::_Find_if_not_fn    std::ranges::_Clamp_fn % �  std::ranges::_Is_heap_until_fn ' �  std::ranges::_Partition_point_fn ( 
  std::ranges::_Prev_permutation_fn  �  std::ranges::_All_of_fn "   std::ranges::_Generate_n_fn / %  std::ranges::_Lexicographical_compare_fn  e  std::ranges::_Shuffle_fn ! �  std::ranges::_Make_heap_fn '   std::ranges::_Is_sorted_until_fn   �  std::ranges::_Count_if_fn  G  std::ranges::_Reverse_fn    std::ranges::_Minmax_fn & �  std::ranges::_Minmax_element_fn  �  std::ranges::_Sort_fn # Y  std::ranges::_Rotate_copy_fn # /  std::ranges::_Remove_copy_fn # �  std::ranges::_Nth_element_fn   �  std::ranges::_Search_n_fn   �  std::ranges::_Find_end_fn  #  std::ranges::_Remove_fn  ;  std::ranges::_Find_fn & 5  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  �  std::ranges::_Equal_fn ! �  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! )  std::ranges::_Remove_if_fn   u  std::ranges::_For_each_fn   }  std::ranges::_Pop_heap_fn & �  std::ranges::_Set_difference_fn ) �  std::ranges::_Partial_sort_copy_fn  �  std::ranges::_Is_heap_fn ! w  std::ranges::_Push_heap_fn ! k  std::ranges::_Partition_fn % M  std::ranges::_Adjacent_find_fn $ �  std::ranges::_Partial_sort_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn % �  std::ranges::_Binary_search_fn " {  std::ranges::_For_each_n_fn & �  std::ranges::_Partition_copy_fn  �  std::ranges::_Copy_n_fn $ M  std::ranges::_Reverse_copy_fn # �  std::ranges::_Equal_range_fn  �  std::ranges::_Move_fn $   std::ranges::_Replace_copy_fn     std::ranges::_Generate_fn   5  std::ranges::_Mismatch_fn   �  std::ranges::_Includes_fn  �  std::ranges::_Count_fn  _  std::ranges::_Sample_fn  �  std::ranges::_Merge_fn # �  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �  std::ranges::_Move_backward_fn  k  std::ranges::_Min_fn  �  std::ranges::_Copy_if_fn " �  std::ranges::_Replace_if_fn & �  std::ranges::_Is_permutation_fn  )  std::ranges::_Copy_fn  �  std::ranges::_Replace_fn    std::ranges::_Fill_fn ( �  std::ranges::_Set_intersection_fn % �  std::ranges::_Inplace_merge_fn 0 �  std::ranges::_Set_symmetric_difference_fn  #  std::ranges::dangling % �  std::ranges::_Copy_backward_fn  S  std::ranges::_Search_fn    std::ranges::_Prev_fn # �  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn (   std::ranges::_Next_permutation_fn # �  std::ranges::_Lower_bound_fn  ;  std::ranges::_Unique_fn  �  std::ranges::_None_of_fn    std::ranges::_Advance_fn  �  std::ranges::_Any_of_fn % �  std::ranges::_Find_first_of_fn ! �  std::ranges::_Transform_fn # �  std::ranges::_Stable_sort_fn  S  std::ranges::_Rotate_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn 4 �  std::allocator_traits<std::allocator<float> >  �  std::input_iterator_tag c �  std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > W �  std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > # �  std::contiguous_iterator_tag " �  std::_Asan_aligned_pointers  �  std::partial_ordering Q   std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > >  .  std::numeric_limits<int>  �  std::bad_variant_access D a  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > Z 8  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat 
 !   _ino_t M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *>  9  terminate_handler  �  _s__RTTIBaseClassArray  �  ReGIR_OnionLayerGroup 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray     ptrdiff_t  "  _stat64i32  �  _PMD ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 % |  __RTTIClassHierarchyDescriptor 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE  �  ReGIR_OnionRing 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  �  _ldiv_t  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers  �   p
      v�%啧4壽/�.A腔$矜!洎\,Jr敎  K    娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �    D���0�郋鬔G5啚髡J竆)俻w��  �    c�#�'�縌殹龇D兺f�$x�;]糺z�  :   +4[(広
倬禼�溞K^洞齹誇*f�5  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎     ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  U   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   鹴y�	宯N卮洗袾uG6E灊搠d�      豊+�丟uJo6粑'@棚荶v�g毩笨C  c   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  J   �
bH<j峪w�/&d[荨?躹耯=�  �   �0�*е彗9釗獳+U叅[4椪 P"��  �   +椬恡�
	#G許�/G候Mc�蜀煟-     {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  D   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷     匐衏�$=�"�3�a旬SY�
乢�骣�  P   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  0   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  }   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   _O縋[HU-銌�鼪根�鲋薺篮�j��     っ8OI枣鮌赊jr�燳;鞾孌j  B   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   �%逽|犟�1剝%sh鵺K媡簂蹶#楎`{w  (	   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  q	   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �	   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �	   繃S,;fi@`騂廩k叉c.2狇x佚�  6
   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  t
   檒Gq$�#嗲RR�錨账��K諻刮g�   �
   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �
   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  @   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  
   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  W   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  G
    d蜯�:＠T邱�"猊`�?d�B�#G騋  �
   溶�$椉�
悇� 騐`菚y�0O腖悘T  �
   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     `k�"�1�^�`�d�.	*貎e挖芺
脑�  [   �"睱建Bi圀対隤v��cB�'窘�n  �   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  6   蜅�萷l�/费�	廵崹
T,W�&連芿  s   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   泽閇�R鯄呙+困胢p=�R刐鉍籫�8[     -�
�捂�
y�*犯丁�02?栕9/�Q  K   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1      ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  j   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   �*o驑瓂a�(施眗9歐湬

�  0    I嘛襨签.濟;剕��7啧�)煇9触�.  p   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   交�,�;+愱`�3p炛秓ee td�	^,  3   _臒~I��歌�0蘏嘺QU5<蝪祰S  x   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  ?   a�傌�抣?�g]}拃洘銌刬H-髛&╟  }   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��     п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  p   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  ;   �	玮媔=zY沚�c簐P`尚足,\�>:O  |   G�膢刉^O郀�/耦��萁n!鮋W VS  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  S   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   �      �  X  B   �  X  H   �  X  Y   �  X  �   }  �   �   �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �   �  �   U   �  �  ,   �  �  4   �  �  �   �  �  �   �  �   S  �  �     �  �   f  �  �   �  �  �   `  �  �   S  �  �   f  �  �   �  �  �   `  �  �   S  �  �   f  �  �   �  �  �   `  �  �   *   �  �   �  �  �   �  �  �  K   �  �  K   �  �  �  �     ,   �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �  �  �        �  �     �  �     �  �        �       �       �   �  
  �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   ]  "  �   ]  #  �   ]  '  �  �   3  X  �   4  �   �  5  �   �  7  �   �  8  X  �   :  �   �  <  �   �  ?  X  �  A  X  �   C  �   �  E  �   �  H  X  �  J  �   @   K  �   �   L  �   @   P  �   �  V  �  �  W  �  �  X  �  �  [  X  �   \  X  �  `  X  �   d  �   �  f  X  �   j  �   �  k  �   �   l  �   @   m  x
  �	  n  X  3  o  �  ,  p  �  T  t  �  �   w  �   �  y  �   �  z  X  �   |  �   �  }  X  �   ~  X  �    �  �  �  �  I  �  �    �  X  �   �  X  �   �  X  �   �   �   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\Source\ReGIR.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\numeric C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h    �       L�     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   k        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >�   _Bytes  AJ        9  $  >�    _Block_size  AH       1 
   >�    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        �  
 Z   �   N Z   3  �   (                      H 
 h   �         $LN14  0   �  O_Bytes  O   �   h           :   �   
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  J   w  J  
 �     �    
 H+袸嬂H+翷嬄H嬔H嬋�             �   Q  R G                      ~        �std::_Copy_backward_memmove<float *,float *>  >@   _First  AJ          >@   _Last  AK          >@   _Dest  AH         AP          >�    _Count  AK                                H 
 h   ]      @  O_First     @  O_Last     @  O_Dest  O   �   0              X     $       � �    � �   � �,   	   0   	  
 y   	   }   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 h  	   l  	  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   H  I G            0   
   %   \        �std::_Copy_memmove<float *,float *>  >@   _First  AJ          >@   _Last  AK          >@   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   ]   0   @  O_First  8   @  O_Last  @   @  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 p      t     
 �      �     
 �      �     
 �      �     
 �      �     
 \     `    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   h  i G            0   
   %   H        �std::_Copy_memmove<ReGIR_OnionLayerGroup *,ReGIR_OnionLayerGroup *>  >|   _First  AJ          >|   _Last  AK          >|   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h      0   |  O_First  8   |  O_Last  @   |  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 |     �    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   \  ] G            0   
   %   ?        �std::_Copy_memmove<ReGIR_OnionRing *,ReGIR_OnionRing *>  >�   _First  AJ          >�   _Last  AK          >�   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   �   0   �  O_First  8   �  O_Last  @   �  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 p     t    
 L塂$H塋$SVWATAUAVAWH冹 L嬧H孂L�	L孃M+鵌�H婣I+罤柳I�������?I;�凥  L峢H婭I+蒆六H嬔H殃I嬂H+翲;��  H�
M嬽I;臠C餗;��  J�4�    L塼$hH侢   r)H峃'H;�嗕   �    H吚勨   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL塼$h�3跮塼$hH塡$xN�4籋婦$p� A�L婫H�H嬎M;鄒L+码M嬆L+妈    I峃L婫M+腎嬙�    怘�H吷t1H媁H+袶冣麳侜   rH兟'L婣鳬+菻岮鳫凐w:I嬋�    H�J�獺塐H�H塐I嬈H兡 A_A^A]A\_^[描    惕    惕    泰   �    �   �         (     b  �    �  �    �  �    �  �       �   	  s G            �     �  �        �std::vector<float,std::allocator<float> >::_Emplace_reallocate<float const &> 
 >{   this  AJ          AM       j  D`    >�   _Whereptr  AK          AT       �l  >�   <_Val_0>  AH  �       AP        A  Dp    >#     _Newcapacity  AV  u     {  AV �        Bh   �        >�    _Newsize  AU  N     P8 D  >�    _Whereoff  AW  %     y  ]
  >�    _Oldsize  AH  0     g  2 0 >�    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M        4  ur� M        P  ur�& M        K  ��)
)%��( M        k  ��$	%)
��
 Z   �   >�    _Block_size  AJ  �       AJ �      >�    _Ptr_container  AH  �       AH �     �  � 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        l  
r

 N N N M          Nk >�    _Oldcapacity  AJ  R     �   M % z   AJ �     �  �  >�    _Geometric  AH  r     u ;  _   AH �     �  �  M          N N N M        5  
�� M        [  
�� N N M        7  �	 M        \  �	 >�    _Count  AP  �       AP '      N N M        7  �! >�   _Last  AP  !      >@   _Dest  AJ      
  AJ '      M        \  �! >�    _Count  AP  $      AP '      N N M        7  � M        \  � >�    _First_ch  AK        AK '      >�    _Count  AP        N N% M          �-h1#' M        �  *�<\ M        �  丂)7
 Z   {  
 >   _Ptr  AJ a      >#    _Bytes  AK  9    -    AK �     % M        }  両d#
:
 Z   3   >�    _Ptr_container  AP  Q      AP a    <  2  >�    _Back_shift  AJ  0    1  AJ a    <  +  N N N N
 Z                  8         0@ � h   �  �  }  ~  �  �  �  �  �          /  4  5  6  7  K  M  N  O  P  [  \  ]  ^  k  l  w  �         $LN145  `   {  Othis  h   �  O_Whereptr  p   �  O<_Val_0>  O   �   �           �  �      �       * �   3 �,   4 �7   6 �J   : �N   ; �r   = ��   A ��   B ��   E �	  G �  K �  L �  N �-  V �y  W �|  X ��  = ��  7 ��  V ��   �  � F            (   
   (             �`std::vector<float,std::allocator<float> >::_Emplace_reallocate<float const &>'::`1'::catch$0 
 >{   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �                        � f        __catch$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z$0        $LN145  `   {  Nthis  h   �  N_Whereptr  p   �  N<_Val_0>  O   �   0           (   �      $       P �
   R �   S �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �    #  �   
 N  �    R  �   
 ^  �    b  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 .  �    2  �   
 B  �    F  �   
    �      �   
   �      �   
 9  �    =  �   
 I  �    M  �   
 l  �    p  �   
 |  �    �  �   
 ;  �    ?  �   
 W  �    [  �   
 �  �    �  �   
 �  �    �  �   
 :  �    >  �   
 J  �    N  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 _  �    c  �   
 o  �    s  �   
 �  �    �  �   
 $  �    (  �   
 E  �    I  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 �  <   �  <  
 ,	  �    0	  �   
 
  
   
  
  
 �
  
   �
  
  
 �
  
   �
  
  
 �
  =   �
  =  
 Q  <   U  <  
 �  
   �  
  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      L塂$H塋$SVWATAUAVAWH冹 L嬧L嬹L�L嬍M+蔍猾*I嬅I鏖L嬯I笼I嬇H凌?L鐷婭I+蔍嬅H鏖H龙H嬄H凌?H蠭窾UUUUUUI;�劆  L峼I婲I+蔍嬅H鏖H龙H嬄H凌?H蠬嬍H验I嬂H+罤;�噁  H�I嬿I;荋C餓;�嘜  H�<vH羚H塼$hH�   r)H峅'H;��,  �    H吚�*  H峏'H冦郒塁#H�tH嬒�    H嬝H塂$xH塼$h�3跦塼$hH塡$xJ�4m    I鮄伶H驢婦$p HN@ F M婩I�H嬎M;鄒L+码M嬆L+妈    H峃0M婩M+腎嬙�    怣�M吚tSI婲I+菻斧*H鏖H龙H嬄H凌?H蠬�RH菱H侜   rH兟'I婬鳯+罥岪鳫凐wDL嬃I嬋�    I�K�H玲H薎塏H�I塏H嬈H兡 A_A^A]A\_^[描    惕    惕    替   �    
  �    q     �     �  �      �      �      �       �   �  � G            #     #          �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Emplace_reallocate<ReGIR_OnionLayerGroup const &> 
 >�   this  AJ          AV       �  D`    >�   _Whereptr  AK          AT       �  >�   <_Val_0>  AH  @    5  AP        n  Dp    >#     _Newcapacity  AL  �       AL         Bh   �     TH  >�    _Newsize  AW  {     ��
 �  >�    _Whereoff  AU  ;       >�    _Newvec  AI          AI $    � � 
  Bx       
  �   M        
  u��乥 M          u��乥& M        K  ��)
)%��( M        k  ��$	%)
�
 Z   �   >�    _Block_size  AJ  �       AJ       >�    _Ptr_container  AH  �       AH $    �  � 
 >�    _Ptr  AI  �       AI $    � � 
  M        �  ��
 Z   �   N N M        �  �
 Z   �   N N M        L  
��

 N N N M          { >�    _Geometric  AH  �     u ;  _   AH $    �  �  M        #  { N N M        C  �; M        f  �; N N M        E  乪 M        H  乪 >�    _Count  AP  Z      AP �      N N M        E  亇 >�   _Last  AP  }      >|   _Dest  AJ  y    
  AJ �      M        H  亇 >�    _Count  AP  �      AP �      N N M        E  乯 >|    _UFirst  AK  ]      AK �      M        H  乯 >�    _Count  AP  m      N N% M          亯hS#' M        �  1伋j M        �  伝)A
 Z   {  
 >   _Ptr  AP �      >#    _Bytes  AK  �    )  AK      % M        }  伳d#
D
 Z   3   >�    _Ptr_container  AJ  �      AJ �    F  >  >�    _Back_shift  AP  �    P  AP �    F 5   N N N N
 Z   �               8         0@ � h   �  �  }  ~  �  �  �  �  �  �    
      
        #  B  C  D  E  H  K  L  f  h  k  |  �         $LN145  `   �  Othis  h   �  O_Whereptr  p   �  O<_Val_0>  O  �   �           #  �      �       * �   3 �I   4 �d   6 �w   : �{   ; ��   = �$  A �)  B �V  E �e  G �h  K �j  L �u  N ��  V ��  W �  X �  = �  7 �  V ��   I  � F            (   
   (             �`std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Emplace_reallocate<ReGIR_OnionLayerGroup const &>'::`1'::catch$0 
 >�   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �                        � �        __catch$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z$0        $LN145  `   �  Nthis  h   �  N_Whereptr  p   �  N<_Val_0>  O   �   0           (   �      $       P �
   R �   S �,      0     
 �      �     
 �      �     
          
          
 ?     C    
 O     S    
 ~     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 +     /    
 ?     C    
 �         
          
 8     <    
 H     L    
 k     o    
 {         
 ;     ?    
 S     W    
 �     �    
      	    
 C     G    
 c     g    
 s     w    
 �     �    
 �     �    
 �         
          
 I     M    
 �     �    
 �         
          
 j     n    
 z     ~    
 �     �    
 �     �    
 �  B   �  B  
 �     �    
 �	     �	    
 p
     t
    
 �
     �
    
 �
  C   �
  C  
 �  B   �  B  
 �     �    
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      L塂$H塋$SVWATAUAVAWH冹 L嬯H孂H�L嬧L+郘媞L+餓窿I�������M;�凮  I�艸婭H+菻六H嬔H殃I嬂H+翲;��&  H�
M孇I;芁C鳰;��  I嬿H伶L墊$hH侢   r)H峃'H;�嗧   �    H吚勲   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL墊$h�3跮墊$hH塡$xI冧餗�<H婦$p AL婫H�H嬎M;鑥L+码M嬇L+妈    I峅L婫M+臝嬚�    怘�H吷t1H媁H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I伶L驦墂H�H塐I嬊H兡 A_A^A]A\_^[描    惕    惕    蹋   �    �   �         (     b  �    �  �    �  �    �  �       �   )	  � G            �     �  �        �std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Emplace_reallocate<ReGIR_OnionRing const &> 
 >   this  AJ          AM       �m  D`    >Y   _Whereptr  AK          AU       �m  >�   <_Val_0>  AH  �     &  AP        =  Dp    >#     _Newcapacity  AW  p     ~  AW �        Bh   �     	  >�    _Newsize  AV  I     X$" L  >�    _Whereoff  AT  %       >�    _Oldsize  AV  ,     o   L >Y    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M          tm�" M          tm�"& M        K  ��)
)%��( M        k  ��$	%)
��
 Z   �   >�    _Block_size  AJ  �       AJ �      >�    _Ptr_container  AH  �       AH �     �  � 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        J  
m
 N N N M          Ik >�    _Oldcapacity  AJ  M     �   L % y   AJ �     � # �  >�    _Geometric  AH  m     t :  ^   AH �     �  �  M        "  I N N M        :  �� M        `  �� N N M        <  �	 M        ?  �	 >�    _Count  AP  �       AP '      N N M        <  �! >Y   _Last  AP  !      >�   _Dest  AJ      
  AJ '      M        ?  �! >�    _Count  AP  $      AP '      N N M        <  � >�    _UFirst  AK        AK '      M        ?  � >�    _Count  AP        N N% M          �-h1#' M        �  *�<_ M        �  丂):
 Z   {  
 >   _Ptr  AJ a      >#    _Bytes  AK  9    -    AK �     % M        }  両d#
=
 Z   3   >�    _Ptr_container  AP  Q      AP a    ?  5  >�    _Back_shift  AJ  0    1  AJ a    ?  #  N N N N
 Z   �               8         0@ � h   �  �  }  ~  �  �  �  �  �  �  �                "  9  :  ;  <  ?  J  K  `  b  k  y  �         $LN145  `     Othis  h   Y  O_Whereptr  p   �  O<_Val_0>  O   �   �           �  �      �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   A ��   B ��   E �	  G �  K �  L �  N �-  V �|  W �  X ��  = ��  7 ��  V ��     � F            (   
   (             �`std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Emplace_reallocate<ReGIR_OnionRing const &>'::`1'::catch$0 
 >   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �                        � �        __catch$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z$0        $LN145  `     Nthis  h   Y  N_Whereptr  p   �  N<_Val_0>  O �   0           (   �      $       P �
   R �   S �,       0      
 �       �      
 �       �      
 �       �      
       
     
 -      1     
 =      A     
 l      p     
 |      �     
 �      �     
 �      �     
 �      �     
       
     
 0      4     
 D      H     
 X      \     
            
 &      *     
 O      S     
 _      c     
 �      �     
 �      �     
 Q      U     
 m      q     
 �      �     
 �      �     
 P      T     
 `      d     
 �      �     
 �      �     
 �      �     
 	      
     
            
 Y      ]     
 i      m     
 �      �     
 8      <     
 Y      ]     
 m      q     
 �      �     
 �      �     
            
            
 �  ?   �  ?  
 @	      D	     
  
     $
    
 �
     �
    
 �
     �
    
   @     @  
 �  ?   �  ?  
 (     ,    
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      �	I嬂H+罬嬋H柳H凐(�2  H�繦柳L��    H��    �/葀	��驛�/葀驛��/衯	���L嬄L+荔A/葀	�
驛 ��
/葀��驛/衯	�驛 M嬃M嬔L+繫+芋A 驛
/葀
驛驛驛驛/葀驛	驛 驛/衯
驛驛��/葀	�
�驛 �
/葀V驛��/衯C��皿/葀�
�驛 �
/葀驛��/衯���   �   �  \ G            �      �  r        �std::_Guess_median_unchecked<float *,std::less<void> >  >@   _First  AJ        �
 >@   _Mid  AK        � >@   _Last  AP        
  AQ  
     z >�   _Pred  AY        
  D     >�    _Count  AH          / AH �      >�    _Step  AH  "        M        �  �.i.I	
 >@   _Last  AP  �       M        m  � N M        '  � N M        m  �" N M        '  �0 N M        m  �9 N M        '  丆 N N  M        �  ��/j/J

 >@   _First  AR  �     �  AR �     
 >@   _Mid  AP  �     �  AP �      M        m  
�� N M        '  �� N M        m  
�� N M        '  �� N M        m  �� N M        '  � N N" M        �  r
*i.I	
 >@   _First  AP  y     C  M        m  r
 N M        '  �� N M        m  �� N M        '  �� N M        m  �� N M        '  �� N N! M        �  2*iK		 M        m  2 N M        '  < N M        m  E N M        '  U N M        m  ` N M        '  i N N  M        �  丮)h.I	 M        m  丮 N M        '  乂 N M        m  乛 N M        '  乴 N M        m  乽 N M        '  亊 N N                        @  h   '  m  �      @  O_First     @  O_Mid     @  O_Last      �  O_Pred     _Diff  O   �   h           �  �  
   \        �     �    �"    �r    ��     �  ! �L  % �M  # ��  % �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 3     7    
 S     W    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 H塡$H塼$H墊$AVH冹 I嬂H嬺H+翴孁H柳L嬹I兝麳嬑H�侶嬘�    H峉H;髎*��     �C麳岰�/葁/羨H嬝(菻;餽釮;譻��/葁/羨	H兟H;譺镠嬍L嬎H;��'  H嬊H+罤兝H冟麳凐傏   L岮�    ��	/葁/�囶   H;裻��
�H兟驛H麵岪/葁/��9  H;衪��
� H兟�驛/葁/��  I;衪
��
驛 H兟驛HI岪�/葁/�囒   H;衪��
� H兟H兞H岹鬒兝H;��1���H;蟬0��	/葁/羨H;裻��
�H兟H兞H;蟫蠰;蝪>I岮� ��/羨/葁"H冸H;豻��� I冮H冭I;駌蘈;蝩GH;�剫   H;裻�����H兟��H兠�H兞門��H嬋閨���I嬋閠���I兞麳;蟯-H冸L;藅驛�A��婤麳冴�������A��H兞驛轺��H媡$8I嬈H媩$@I�H媆$0I塚H兡 A^�7         �   �  i G            �     �  Y        �std::_Partition_by_median_guess_unchecked<float *,std::less<void> >  >@   _First  AK          AL       � >@   _Last  AM  !     � AP        !  >�   _Pred  AY        ;  >@    _Plast  AK  ?     ~
 >@    _Mid  AI  3       AI P     b  � �
 
)  >@    _Pfirst  A�   i       AI  f       }�< � * A�  P     m  '  x � L( �3 � 
)  AI �     R �
 �)  >@    _Glast  AQ  �    	  AQ �     )g	 �I  >@    _Gfirst  AJ  �     , M        m  ^ N M        m  P N M        m  �� N M        m  s N  M        m  ��'1-O N  M        m  ��1,2E NF M        '  ��$)$$$*$9$
 >@     _Tmp  A�   �     �  % =   j % � 5 : A�  �     � ' 9 # ` ) � # � # / [ g) �	 � �  N M        m  佇 N M        m  佪 N M        '  侂$
 >@     _Tmp  A�   �     & A�  �    �   K  W ) � 	 �  �   N M        t  偑 N M        '  �$
 >@     _Tmp  A�         A�  #      N M        '  �#
 >@     _Tmp  A�   '     . A�  �     )0 � , D/ � �) �	 � �  N M        '  俠%
 >@     _Tmp  A�   g    	  A�  p      N M        '  俻7$
 >@     _Tmp  A�   {     . A�  �     )0 � , D/ � �) �	 � �  N M        '  倖	
 >@     _Tmp  A�   �     . A�  �     )0 � , D/ � �) �	 � �  N
 Z   r                         @  h   '  m  q  s  t   8   @  O_First  @   @  O_Last  H   �  O_Pred  O �              �  �  A         ( �   * �(   + �;   - �?   0 �c   1 �n   4 ��   5 ��   8 ��   9 ��   < ��   = ��   ? ��   A ��   B ��   = ��   ? �  A �  B �  = �*  ? �3  A �8  B �E  = �\  ? �e  A �j  B �v  E ��  < ��  = ��  ? ��  A ��  B ��  < ��  I ��  K ��  M ��  O ��  P ��  I �  T �  Y �  Z �#  ^ �'  ] �+  ^ �/  _ �3  ^ �7  ` �@  I �H  ? �P  a �Y  b �b  c �p  f ��  g ��  h ��  i ��  h ��  k ��  l ��  U ��  l ��  U ��  l �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 0     4    
 @     D    
 r     v    
 �     �    
 �     �    
 �     �    
 �     �    
          
 1     5    
 I     M    
 i     m    
          
          
 �     �    
 �     �    
 �     �    
 �     �    
 L     P    
 \     `    
 �     �    
 �     �    
          
       $    
 �     �    
 H塡$H塴$H塼$WAVAWH冹@H嬄A顿H+罥嬸H冟麳嬯H孂H=�   帊   D  H咑幰   D端H峀$ L嬇H嬜�    L媩$ H嬑L媡$(I嬊H六H+荋瑶H冟麳馜端H嬐L嬈I+蜨冡麳;羮I嬜H嬒�    I孇�H嬚I嬑�    I嬶H嬇H+荋冟麳=�   弜���H;��  L峸I嬣L;��  )t$0�   D  �3H嬎�/�啩  L嬈H嬜I嬑�    �7榫  L嬚L+譏龙M嬄I养M吚帹   M岼�I样D  驜L圏I�菼嬋I嬓M;羮3fff�     H�3殷D�/D�柭H�翲袐棄廐嬍I;褆豂;蓇A雎uB婦楛�廔岼�L;羮*@ �     H岮�H养��/葀
�廐嬋L;纜怏廙吚廳���I凓�	  �   L峌麳+黧A
M嬄�L+荌柳3�3褹�M岺�I样M吷~2ff�     H�3殷D�/D�柭H�翲袐棄廐嬍I;褆豂;蓇A隼uB婦圏�廔岺�H吷~*@ �     H岮�H养��/葀
�廐嬋H吚釯冴�廕�H冟麳凐岻���隓H岶麳求 /苬D  �H嬋�@麳冭/苭塍1H兠H兤H;����(t$0H媆$`H媗$hH媡$pH兡@A_A^_肶      �      �              �   .	  T G                 �  0        �std::_Sort_unchecked<float *,std::less<void> >  >@   _First  AJ        /  AM  /     � >@   _Last  AK        ,  AN  ,     � >    _Ideal  AL  %     �R  � 0  AP        %  AL �      >�   _Pred  A        �� C  AY          A  �     
 >S   _Mid  CW      b     f  CV     j     ^  CW     @     �" f  CV    @     �* ^ � F kD  D    < M        V  ��	G-.仌7
 >@    _Mid  AI  �     C � AI �      >@    _Hole  AJ  �     � � AJ �      �
 >@     _Val  A�   �     �' � A�  �       >@    _Prev  AH  �    &    AH �     " �� �  M        m  �� N M        n  � M        ~  � N N M        m  偛	 N N) M        X  
佨$D.P$! M        p  侐D)P"
 >@     _Val  A�   �    �  A�  �     � % M        �  侘DP"8 M        o  �/h+(" >    _Hole  AJ      k  AJ �     k �  � D  >    _Bottom  AP  �    �    AP �     �  >�    _Max_sequence_non_leaf  AQ      �  AQ �     � 
 >    _Idx  AK        AK �      4 " � D  C       &     & M          俖'J >    _Hole  AJ  �      AJ �     k �  � D 
 >     _Idx  AH  w      AH p    +    M        m  倃 N N N N N N+ M        W  �

DP		# >     _Bottom  AR      �    AR �      >     _Hole  AP  (    �  AP �     � 2 M        o  丮
h+(# >    _Hole  AJ  M    c  AJ @    �
 c �  � �  >�    _Max_sequence_non_leaf  AQ  8    �  AQ �    ,2 � 
 >    _Idx  AK  P      AK @    �  & " � �  C       f     # M          仧'J% >    _Hole  AJ  �      AJ @    �
 c �  � � 
 >     _Idx  AH  �      AH @    �$ $ X  t  � �  M        m  伔 N N N N Z   Y  0  0   @                     @ 6 h   �  V  W  X  ]  m  n  o  p  ~    �   `   @  O_First  h   @  O_Last  p      O_Ideal  x   �  O_Pred      S  O_Mid  O  �   �             �     �       o �   r �@   w �I   ~ �]   � ��   � ��   � ��   � ��   � ��   � ��   r ��   s �  x ��  y ��  s ��  � �,      0     
 {           
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 $     (    
 8     <    
 H     L    
 k     o    
      �    
 �     �    
 �     �    
           
 0     4    
 P     T    
 d     h    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 O     S    
 _     c    
 �     �    
 �     �    
 �     �    
 �     �    
 	     
    
          
 9     =    
 �     �    
 �     �    
 �     �    
 �     �    
 M     Q    
 a     e    
 �     �    
 �     �    
 �     �    
 �     �    
 6     :    
 F     J    
 i     m    
 y     }    
 �     �    
 �     �    
 �     �    
          
 )     -    
 D	     H	    
 H塡$H塼$H塋$WH冹 I孁H嬞3鰤1A�J�I婤堿H茿   �?H塹(茿0   茿4   茿8  �?H茿<   塹DH塹HH塹PH塹XH塹`H塹hH塹pH塹xH壉�   婣疉疉疉墎�   �    H嬎�    婯吷t+冮t凒u!婼@H嬒�    ��嫇�   H嬒�    ���3H嬅H媆$8H媡$@H兡 _脷   �    �   �    �   �    �   �       �   �  G G            �      �   �        �rtxdi::ReGIRContext::ReGIRContext 
 >�   this  AI       �  AJ          D0    >u   params  AK        � " >�   risBufferSegmentAllocator  AM       �  AP         " M        �  ��	 Z   6  6   N M        �  �� N M        �  m M        �  m M        �  m N N N M        �  a M        �  a M        �  a N N N Z   �  �                        @ > h   �  �  �  �  �  �  �  �  �  �  �  �  �  �   0   �  Othis  8   u  Oparams & @   �  OrisBufferSegmentAllocator  O �   `           �   �  	   T       $  �   "  �   #  �a   $  ��   %  ��   &  ��   '  ��   (  ��   )  ��   �   V F                                �`rtxdi::ReGIRContext::ReGIRContext'::`1'::dtor$0 
 >�   this  EN  0                                  �  O   ,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
    
   $  
  
 s  
   w  
  
 H媻0   H兞@�       �    H塡$H塴$H塼$ H塋$WAVAWH冹 L嬺H嬹��婤堿3鞨塱H塱H塱H婮H+JI揩*I嬊H鏖H龙H嬄H凌?H�劘   H窾UUUUUUH;�噚  H�RH零H侞   r,H岾'H;�哷  �    H嬋H吚刄  H兝'H冟郒塇H呟t
H嬎�    �H嬇H塅H塅H�H塏H媬I媈I媀H+贚嬅H嬒�    I嬊H麟H龙H嬄H凌?H蠬�RH拎H荋塅H塶 H塶(H塶0I媈(I+^ H聋H呟剫   H�������H;�嚭   H零H侞   r%H岾'H;�啨   �    H吚t~H峢'H冨郒塃H呟tH嬎�    H嬭H塶 H塶(H�+H塅0H媬 I媈(I媀 H+贚嬅H嬒�    H冦餒�;H塅(A婩8塅8A婩<塅<H嬈H媆$HH媗$PH媡$XH兡 A_A^_描    愯    惕    惕    愯    惕    虣   �    �   �    �      e  �    �  �    �     �  �    �  �    �  �    �  �      �      �       �   �	  k G                   �        �rtxdi::ReGIROnionCalculatedParameters::ReGIROnionCalculatedParameters 
 >b   this  AJ        #  AL  #     ��
  D@    >i   __that  AK           AV        �� # M        �  �L���� >�    _Count  AI  &    �   ( �  AI �     % M        �  �.)&eP& M        �  �7R��
 Z   �  & M        �  丣B$X >Y   _Newvec  AN �    Q  C       /     �  C      �     Y� 
 &  M          B丣�� M          B丣��& M        K  丯)
%
k, M        k  乄$	%%v	 Z   3  �   >�    _Block_size  AJ  [    �  �  >�    _Ptr_container  AH  i    �  p  AH �     
 >�    _Ptr  AN  v      AN �    Q  M        �  乨
 Z   �   N N M        �  亖
 Z   �   N N M        J  丣 N N N N N M        �  仱$ >�   _First  AK  �      >�   _Last  AI  �      >�   _Dest  AH  �      AM  �      AH �      M        ?  仺 >�    _Count  AI  �      N N N M        �  � M        �  � N N N" M        �  -N��&亼% M        �  a&=r�% M        �  g_亁
 Z   �  & M        �  zO$�' >�    _Newvec  AH  �       AH �     *  M        
  Oz亊 M          Oz亊) M        K  ��)
,%
�2- M        k  ��$	()
丟 Z   �  3   >�    _Block_size  AJ  �     n [ >�    _Ptr_container  AJ  �     c G AJ �      
 >�    _Ptr  AH  �       AH �     *  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        L  z N N N N N M          ��$ >|   _First  AK  �       >|   _Last  AI  �       >|   _Dest  AH      /  AM  �     5  AH �      M        H  �� >�    _Count  AI  �     >  N N N M        �  - M        �  - N N N                      0@ � h;   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �            
      
            9  ?  @  B  H  I  J  K  L  d  j  k  z  }         $LN154  @   b  Othis  H   i  O__that  O �   �   z F                                �`rtxdi::ReGIROnionCalculatedParameters::ReGIROnionCalculatedParameters'::`1'::dtor$0 
 >b   this  EN  @                                  �  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 '  �    +  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
   �      �   
   �      �   
 :  �    >  �   
 J  �    N  �   
   �      �   
 '  �    +  �   
 G  �    K  �   
 W  �    [  �   
 g  �    k  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 I  �    M  �   
 �	  .   �	  .  
 
     
    
 
     �
    
 H媻@   H兞�       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >   this  AI  	     2  AJ        	  >   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0     Othis  8     O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   =  U G            <      6           �std::bad_array_new_length::bad_array_new_length 
 >%   this  AI  	     2  AJ        	  >*   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �     0   %  Othis  8   *  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >%   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      %  Othis  O   �   8           !   X     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �       �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   X     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 @SH冹 H嬞H�	H吷t>H婼H+袶冣麳侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �   �  v G            [      [   �        �std::vector<float,std::allocator<float> >::~vector<float,std::allocator<float> > 
 >{   this  AI  	     R K   AJ        	 $ M        �  	h1%	
 M        �  *= M        �  )
 Z   {  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        }  
%#

 Z   3   >�    _Ptr_container  AP  )     1    AP =       >�    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� & h   �  }  ~  �  �  �  �  �         $LN30  0   {  Othis  O  �   8           [   �      ,       � �	   � �O    �U   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 8  �    <  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 n  8   r  8  
 �  �    �  �   
 �       �       �     � G                       �        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::~vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O�   (              �             � �    � �,   �    0   �   
 �   �    �   �   
 ,  �    0  �   
 @SH冹 H嬞H婭 H吷t?H婼0H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁 H塁(H塁0H岾H兡 [�    �    �?   �    [   �    `   �       �   �  l G            e      e   �        �rtxdi::ReGIROnionCalculatedParameters::~ReGIROnionCalculatedParameters 
 >b   this  AI  	     \ Q   AJ        	  M        �  Z N M        �  H	V$ M        �  	i1&	 M        �  *F M        �  )!
 Z   {  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        }  
&#
$
 Z   3   >�    _Ptr_container  AP  *     :  !  AP >       >�    _Back_shift  AJ  
     W 1 !  AJ >         N N N N N                       @� . h
   �  }  ~  �  �  �  �  �  �  �         $LN35  0   b  Othis  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 /  �    3  �   
 �  ,   �  ,  
 H�    H�H兞�       �      �       �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >%   this  AJ          M        �   	
 N                        H�  h   �  �      %  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       �      �       �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              X            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >   this  AJ          AM       -  M        �  

	
 Z   �   N                       @�  h   �  �   0     Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >%   this  AJ          AM       -  M        �  

	
 Z   �   N                       @�  h   �  �  �   0   %  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   �   N                       @� 
 h   �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 @SH冹 D婣L嬍H嬞E吚t5A冭tA凐u)婹@I嬌�    �H兡 [脣憖   I嬌�    �H兡 [们    H兡 [�(   �    >   �       �   E  S G            V      P   �        �rtxdi::ReGIRContext::AllocateRISBufferSegment 
 >�   this  AI       E #  9   AJ         " >�   risBufferSegmentAllocator  AK        
  AQ  
     I   5   Z   6  6                         H  0   �  Othis & 8   �  OrisBufferSegmentAllocator  O   �   H           V   �     <       4  �   5  �!   ?  �,   A  �4   <  �B   A  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 \  �    `  �   
 婣疉疉疉墎�   �   �   �   T G                      �        �rtxdi::ReGIRContext::ComputeGridLightSlotCount 
 >�   this  AJ                                 H     �  Othis  O  �   0              �     $       ,  �    -  �   1  �,   �    0   �   
 y   �    }   �   
 �   �    �   �   
 H嬆H塇USVWATAUAVAWH峢圚侅8  )p�)x楧)@圖)坸���D)恏���D)榅���D)燞���D)�8���D)�(���D)����L嬦W荔D$H3�H墊$8H墊$X�D$`E3鞮塴$0L塴$p3缐厛   H婭PH塎�M媩$HL墊$@W�L媡$PH媡$hL;��%  驞
    f�     E3鞥9o庣   驟$fAn�[葾(氰    驛Y驞Y鳨(黧DX痼EY袂D$     E媑3跠(譋呬幁  驟G 驞厴   Ic(鱄媴�   H婡`H塂$xE(梵EY�(氰    D(囿EY骟Dd$((氰    驛Y企厫   H羚L媩$x恌n�[�驛Y鴧踰E(蓦
D(唧E\軩(煮E?(氰    D(润EY藺(黎    (痼AY鰽(描    �Y痼D\�(氰    D(囿EY鍭(描    驛Y求D\�(氰    (Y綈   A(黎    (痼AY鰽(描    �Y痼\EY潴EY审EX狍Y�驞X�W繟.膚
W荔AQ碾	A(蔫    �D$$H岲$$H峀$ A/翲F馏D驞T$ �肏兦A(駻;荏D厴   驞d$(屜��W�驞
    L媩$@H媩$8L嫢�   I婰$PI+L$HH斧*H鏖H龙H嬄H凌?H��9厛   }X�
    A(畦    驛Y麦厫   L;鱰驛I兤L塼$P雖L崊�   I嬛H峀$H�    H媩$XH墊$8L媡$P際驟^煮D晲   H;t$0t驞H兤H塼$h�#L崊�   H嬛H峀$`�    H婦$pH塂$0H媡$hA�臙;o�&��媴�   H婱��缐厛   I兦0L墊$@L;�咈��L媗$0H媩$HI;%I嬣H+逪聋E3蒐嬅I嬛H嬒�    H央�熾(求AD$xH婰$`H;蝨H嬃�    �X8H兝H;苪篌
    菂�     �?H+馠窿W繦咑x驢*齐H嬈H谚冩H企H*荔X荔厛   H崊�   H崟�   /菻F麦^8驛|$|H吷t:L+镮笼J��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚈   �    怘�t5H婽$8H+譎冣麳嬊H侜   rH兟'H�鳫+荋兝鳫凐w[H嬒�    L崪$8  A(s鐰({谽(C菶(K窫(S‥([楨(c圗(玿���E(砲���E(籜���I嬨A_A^A]A\_^[]描    愯    惷   �   �      ]     u     �     �     �     �               )     :     v     �  �     �        P  �    �  �         A  �   �  �      �    o  �    u  �       �     R G            z  c   z  �        �rtxdi::ReGIRContext::ComputeOnionJitterCurve 
 >�   this  AH  G      AJ          AT  f     � �  B�       H >t     layerGroupIndex  A   �     �P �x
  A  �     � F�  B�  �     � >�   cubicRootFactors  CV     �     �  i�%  CV    �     �i � �	  DH    >�    linearFactors  D`    >|    <begin>$L0  AW  �     �� C�  B@   �     � >|    <end>$L0  AJ  �     �F �i  AJ �       B�   �     � >t     layerIndex  Am  �      Am �       >�    innerRadius  A�   �     `  A�  �     E" �IL � 8
  >�    middleRadius  A      � A �     �5 ��  >@     maxCellRadius  D     >�    outerRadius  A       � A  �     � ��  >t     ringIndex  A       � A  �     �M � " �  >�    middleElevation  A�   �    �  A  Y    7  A �      >�    cellRadius  D$    >�    vertexAzimuth  A  �    �  >�    vertexElevation  A  �        A �    �  A �     ��  q-  >@     cubicRootFactor  B�  �     �� Q�:  >@     linearFactor  A  j    	  B�  �     �� Q[:  M        �  { M        �  { M        �  { N N N M        �  i
5 M        �  i
5 M        �  i N N N M        �  
個 N! M        �  佽$6	
 >   d  C      �    c  C         :  C�      F      C     K    
  N M        �  伿4& N  M        �  乊H4$ N M        �  ,傋 N M        �  )�+ M        �  
�+%
 Z   �   M        �  �0	 M        3  �0 N N N N M        �  :僺 M        �  
僺'#
 Z   �   M        �  儂	 M        3  儂 N N N N M        �  冺 M        �  冺
 Z   0   N N M        �  :勧�� M        �  勧5�� M        �  -勵�� M        �  匁)[
 Z   {  
 >   _Ptr  AH  �      AM  �    �
  AH       AM     O  >#   	 _Bytes  B8   v      AK  �    �   0 S  AM  �     m � C       q     _  C      �     m ��  M        }  匎d
e
 Z   3   >�    _Ptr_container  AH  	      AM        N N N N N M        �  >劉�� M        �  劉9�� M        �  2劗�� >�  	 _Count  B0   �     � AH  �    
  AU  �    ��  �  AH �     � � Cm      �     L  Cm     �     � �7 �   M        �  劰)��
 Z   {  
 >   _Ptr  AH  �      AJ  !    �  AH �      AJ �    � 5   >#    _Bytes  AK  �    � * �  M        }  劼d��
 Z   3   >�    _Ptr_container  AH  �      AJ  �      N N N N N M        �  剛 N M        �  凮 N M        �  
�
 M        �  

�	
 >@    _Val  A�   4    	  A�  0    k  	  >@    _UFirst  AH  )      AH =    L +   N N M        �  � M        �  � N N 8          @         @ � h.   �  �  }  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �    .  2  3  Z  [         $LN193  �  �  Othis  H   �  OcubicRootFactors  `   �  OlinearFactors      @   OmaxCellRadius  $   �  OcellRadius  �  @   OcubicRootFactor  �  @   OlinearFactor  O   �   �          z  �  2   �      �  �i   �  �{   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �  �  �  �  �  �  �!  �  �*  �  �Y  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �$  �  �B  �  ��  �  ��  �  ��  �  ��  �  ��  �  �
  �  �+  �  �T  �  �e  �  �s  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �  �  �=  �  ��  �  ��   5  a F                                �`rtxdi::ReGIRContext::ComputeOnionJitterCurve'::`1'::dtor$0  >�    cubicRootFactors  EN  H           >�    linearFactors  EN  `           >@     maxCellRadius  EN              >�    cellRadius  EN  $                                  �  O   �   5  a F                                �`rtxdi::ReGIRContext::ComputeOnionJitterCurve'::`1'::dtor$1  >�    cubicRootFactors  EN  H           >�    linearFactors  EN  `           >@     maxCellRadius  EN              >�    cellRadius  EN  $                                  �  O   ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 	  �    
  �   
 8  �    <  �   
 T  �    X  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
   �      �   
 5  �    9  �   
 E  �    I  �   
 k  �    o  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
   �      �   
    �    $  �   
 L  �    P  �   
 \  �    `  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 !  �    %  �   
 5  �    9  �   
 E  �    I  �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 (  �    ,  �   
 8  �    <  �   
 L  �    P  �   
 \  �    `  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 -	  �    1	  �   
 =	  �    A	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �     
  �   
 
  �    
  �   
 (
  �    ,
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 /  �    3  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 '  �    +  �   
 O
  3   S
  3  
 (  �    ,  �   
 �     �    
 b     f    
 �     �    
 �     �    
 �     �    
 8     <    
 �     �    
 �     �    
 �     �    
 #     '    
 H崐H   �       �    H崐`   �       �    ��   �   �   M G                      �        �rtxdi::ReGIRContext::GetReGIRCellOffset 
 >�   this  AJ                                 @     �  Othis  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 r   �    v   �   
 �   �    �   �   
 A H嬄I0J�   �   �   T G                      �        �rtxdi::ReGIRContext::GetReGIRDynamicParameters 
 >�   this  AJ                                 @     �  Othis  O  �   0              �     $       �  �    �  �   �  �,   �    0   �   
 y   �    }   �   
 �   �    �   �   
 H媮�   H�H嬄�   �   �   [ G                   
   �        �rtxdi::ReGIRContext::GetReGIRGridCalculatedParameters 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       �  �    �  �
   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 婹呉t冴t	凓u婣@脣亐   �3烂   �   �   Q G                      �        �rtxdi::ReGIRContext::GetReGIRLightSlotCount 
 >�   this  AJ                                 @     �  Othis  O �   X              �     L       �  �    �  �   �  �   �  �   �  �   �  �   �  �   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 @SH冹0H嬟H峇@H嬎�    H嬅H兡0[�   �       �   �   \ G                     �        �rtxdi::ReGIRContext::GetReGIROnionCalculatedParameters 
 >�   this  AJ         
 Z   �   0                     @  @   �  Othis  O  �   0              �     $       �  �	   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 婣A�I�J塀H嬄�   �   �   S G                      �        �rtxdi::ReGIRContext::GetReGIRStaticParameters 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       �  �    �  �   �  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 L嬡I塖UVAUAVI峩菻侅  �   A){�9rL嬮�   E)揾���驞    D嬈DLBA�   D;翂uXA(鶧塽@AO�3缐L$ 塃P吷庉  I塠�蒊墈蠱塩萂墈繟)s‥)C圗)媥���E)沊���驞    E)���D)l$p驞-    塋$$�;羮A�   �D媧A�莊Dn艵[纅An螦(餉(荔AX躞A\�[审^�(畦    I媇hD(囿DY鏓(薍嬅荄$d    (企|$8I+E`H柳驞d$<塂$P驟^辱    嬈D塼$`檳t$4冣D�$3繟咙A�腍塂$(D塪$T塂$0A(鼠DL$XA(殷t$\�^華(荔A^皿L$@A(鼠^润A^洋L$(�T$H�D$,I;]pt��C塁塻I僂h�L岲$(H嬘I峂`�    A�   嬣E;�帓   fAn�[荔AY凌    驛Y黎    I婾h�   �,缐\$0A(�;�O饓t$4fn�[荔A^皿^润D$,�L$(I;Upt�
�B塟塺I僂h�L岲$(I峂`�    A�茘sE;�宷���媢XI婾P塡$LD墊$DI;UXt!D$8L$HD$XJB I僂P0�L岲$8I峂H�    D媢@兤婨PA(麳婾H�缷L$$A塃P塽XD驞塽@;D$ 屍��D(l$pD(�$�   D(�$�   D(�$�   D(�$�   (�$�   L嫾$�   L嫟$   H嫾$  H嫓$  (�$�   D(�$�   E塽DE痷E塽@H伳  A^A]^]�:   �   �   �   �   �   �      @     �                o      �        �   `  J G            x  5   L  �        �rtxdi::ReGIRContext::InitializeOnion 
 >�   this  AJ        (  AU  (     M >u   params  AK        x� DH   >@     innerRadius  A�   V     � >t     totalCells  An  L     ��  B@  Z      >t     numLayerGroups  A   -     H  A  L    ,  B    d      >t     layerGroupIndex  A   `     � � BP  g      >�    equatorialAngle  A  ?    � A �     S 
 >�   ring  C�      �    � R W  C�      4      C          [  C�     �    �  { �   C     �    � % [  D(    >�    layerCount  Ao  �     j   Ao �      
   >t     cellsPerLayer  A   �    �  >�    radiusRatio  A�   �     2 A�  �     %  >�    outerRadius  A      � A �     J  >�   layerGroup  Cl     `    
  D8    >t     ringIndex  An  �    � % M        �  
 N M        �  -伒 M        �  
伒&
 Z   �   M        �  伝 M        8  伝 N N N N M        �  � N M        �  倕5
 M        �  
倕
&!
 Z      M           倶 M        A  倶
 >   _Obj  AK  �    >  AK �      N N N N M        �  �*: M        �  
�:&
 Z   �   M        �  侽 M        8  侽 N N N N M        �  � N                      @ R h   �  �  �  �  �  �  �  �  �  �         8  A  _  `  e  f   @  �  Othis  H  u  Oparams  (   �  Oring  8   �  OlayerGroup  O�   �          x  �  7   �      D  �   E  �   D  �"   E  �%   D  �(   E  �-   D  �5   G  �>   E  �F   J  �L   E  �R   G  �V   J  �Z   E  �g   L  ��   O  ��   Q  ��   R  �  V  �  R  �  S  �  V  �  U  �  Y  �'  V  �/  X  �?  Y  �D  \  �F  ^  �L  a  �j  b  ��  c  ��  d  ��  e  ��  g  ��  h  ��  j  �  n  �  j  �  k  �   l  �$  j  �1  k  �4  l  �C  m  �I  n  �s  h  �v  p  ��  u  ��  s  ��  t  ��  u  ��  y  ��  L  �L    �,   �    0   �   
 o   �    s   �   
    �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 4  �    8  �   
 D  �    H  �   
 T  �    X  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 /  �    3  �   
 K  �    O  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 Q  �    U  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 t  �    x  �   
 儁4t	儁0t2烂��   �   �   U G                      �        �rtxdi::ReGIRContext::IsLocalLightPowerRISEnable 
 >�   this  AJ                                 @     �  Othis  O �   @              �     4        �     �    �    �    �,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 A JI0�   �   �   O G                      �        �rtxdi::ReGIRContext::SetDynamicParameters 
 >�   this  AJ          >�   regirDynamicParameters  AK                                 @     �  Othis #    �  OregirDynamicParameters  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 t   �    x   �   
 �   �    �   �   
   �      �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       �            �   �   F G                       �        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �       �      
 �   �    �   �   
 @SH冹 H嬞H�	H吷t]H婼H斧*H+袶麝H龙H嬄H凌?H蠬�RH菱H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    蘛   �    u   �       �   T  v G            z      z   �        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Tidy 
 >�   this  AI  	     q j   AJ        	  M        �  .3A M        �  ;)
 Z   {  
 >   _Ptr  AJ \       >#    _Bytes  AK  ;     > &  " M        }  
D#

 Z   3   >�    _Ptr_container  AP  H     1    AP \       >�    _Back_shift  AJ       m P   AJ \       
  N N N                       @� " h   �  }  ~  �  �  �  �         $LN27  0   �  Othis  O�   X           z   �      L       � �	    �    �a   	 �f   
 �j    �n   
 �t    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 3  �    7  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 <  $   @  $  
 h  �    l  �   
 H冹(H�
    �    �   �      �       �   �   Y G                             坰td::vector<float,std::allocator<float> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   :   �   :  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   y G                     �        坰td::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   &   �   &  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   m G                     �        坰td::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   *   �   *  
 �   �    �   �   
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   O  G G            B      B   �        �std::allocator<float>::deallocate 
 ><   this  AJ          AJ 0       D0   
 >�   _Ptr  AK          >�   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        }  
#

 Z   3   >�    _Ptr_container  AJ       %    AJ 0       >�    _Back_shift  AH          AH 0       N N (                      H  h   �  }  �         $LN20  0   <  Othis  8   �  O_Ptr  @   �  O_Count  O �   8           B   �      ,       � �   � �3   � �7   � �,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    	  �   
 &  �    *  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   6     6  
 d  �    h  �   
 H冹(H嬄K�@H菱H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   _  W G            B      B   �        �std::allocator<ReGIR_OnionLayerGroup>::deallocate 
 >�   this  AJ          AJ 0       D0   
 >�   _Ptr  AK          >�   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        }  
#

 Z   3   >�    _Ptr_container  AJ       %    AJ 0       >�    _Back_shift  AH          AH 0       N N (                      H  h   �  }  �         $LN20  0   �  Othis  8   �  O_Ptr  @   �  O_Count  O �   8           B   �      ,       � �   � �3   � �7   � �,   �    0   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 6  �    :  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   "   #  "  
 t  �    x  �   
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �    <   �       �   ]  Q G            A      A   �        �std::allocator<ReGIR_OnionRing>::deallocate 
 >m   this  AJ          AJ ,       D0   
 >Y   _Ptr  AK        @ /   >�   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        }  
#

 Z   3   >�    _Ptr_container  AJ       (    AJ ,       >�    _Back_shift  AH         AH ,       N N (                      H  h   �  }  �         $LN20  0   m  Othis  8   Y  O_Ptr  @   �  O_Count  O   �   8           A   �      ,       � �   � �2   � �6   � �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 4  �    8  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   (   !  (  
 t  �    x  �   
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              X     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  20    2                       N   
 
4 
2p    B                       T    20    <                       Z   
 
4 
2p    B                       `    20    <                       f   
 
4 
2p    B                       l    �                  !      !      r    B      B           #      #      x    20    z           %      %      ~    B                 '      '      �    B      A           )      )      �    B                 +      +      �    20    e           -      -      �   
 d T
 4	 2��p                 �                 /      /      �   (           �      �             a �� h d 4 2p                 �       �           0      0      �   (           �      �          
   e�  R0               1      1      �   5
 5�
 "x
 # 
��	`P    o           2      2      �   !C C� =� ,�	 $� � h � �  
t! 4"     o       ,   2   0   2   4   �   o   L          2      2      �   !       o          2      2      �   L  x          2      2      �   c c�	 [�
 S� K� C�
 ;� 3� +� &x "h ' ���
�p
`	0P        @      D   �       z          4      4      �   (           �      �   
    �2    �   �       �    =
M 20    V           5      5      �    B      B           7      7      �    20    [           9      9      �    B                 ;      ;      �    2����
p`0                 �       �          >      >      �   8                        	                  �       
   � �� 
 
2P    (           
      
           2����
p`0                        �          A      A         8                      #   	   )            &   �          � �� 
 
2P    (                       ,     2����
p`0                 ;       #          D      D      5   8               >      A   	   G            D   �          � �� 
 
2P    (                       J    
 d T
 4 r��p    �           E      E      S   ! h     �          E      E      S   �             E      E      Y   !       �          E      E      S     �          E      E      _   !   h     �          E      E      S   �  �          E      E      e   !       �          E      E      S   �            E      E      k   
 
4 
2p    0           F      F      q   
 
4 
2p    0           G      G      w    t d 4 2�    �          H      H      }   
 
4 
2p    0           I      I      �    B      :           K      K      �                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                    .?AVbad_alloc@std@@     �              ����                      �      �                    .?AVexception@std@@     �               ����                      �      �    vector too long                                       �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                         �                           �      �              ����    @                   �      �                                         �      �      �                         �                                   �      �      �              ����    @                   �      �      ?  �?�I@�葽   �   (   & 
Z        std::exception::`vftable'    �      �  
    �   (   & 
Z        std::bad_alloc::`vftable'    �      �  
    �   3   1 
Z        std::bad_array_new_length::`vftable'     �      �  
 坂晗i�靁��#p\溚h鈊&连0h�K蜌�(�}S�艳"嘇�%kz臲摢QA"R�禶+椿+凑腁"R�尔g樮�7暠A"R�缎k趶溙G�F绣穭?z*譗8$裛s鲚溏q兗�&�6督╙�?肳m愌`s鲚溏qv諲d碜茢+Rh徰`s鲚溏qg?F鷾1秄xD�7膀<B痶泳=亙漽�"�.J
>孔]Mbk→!悪�?狄\uN/蠲BC�1鹥�圢紟�8~�	�(蛁�<C轁棕裔砇妱隂S嚤踖p禭Qb希G袊臂ep禭兣o�=疁6萪O�耝8蘹W誎霵婬(�0-H皘趁7陘�
羟�.桦�'洋m|Χ�q褎�;}溉翜歃埛�2鵴嶀預棊膬:eX6K#犷A棊膬副heiCT侣徇�5>aL剾[挪惵徇�5> �%�=炭箩邆5>R琋t鮚�稽�貵蓽H
 U蕋殿蒘歖S┌,uD"0氆忻ah椡�8N茭瘡j'=$;4�B緤`QI蚶� MI矱�魋2I嫔藲僱頟P�
5'�x氪袣撕V渲�)决M
殬z?垽�勯�,浠兵覽砡廸厄#�V蜈訵網T驌n>YJoH昸鍻瓾uH羍td邌wt+柶,瀉t葠X掇寗萳�W埣�_嘊%I栶賑?T
zW嵫�Ｗ鲴o帵SH翆釽f]{謑pS乨靛=闦f]{謑pゆ蟰�d醘]{謑p豝~衜k容X墺鉪d朿鉻&糗鱒酕嵛奕P{M鏞鈎鮧�槉�8兙﹣nN鵘J庾賖脎Kv入嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄酲;[純o穗嘕-WV8o嗹啴r殅W-坓�(鬄�汬'这�-坓�(鬄鮳�>i,夿-坓�(鬄�汬'这栯嘕-WV8o.*~襠[
B���>H3*臄褼搢dd�a�:_棢杻#Q�賴矖蚳憎X觨椗
a�>20dd�a�:_棢杻#Q8�,惝�烗餯繅鬮�摮Dk.,;U��%�L扟�9竮e{E佋~y�)^肶8vIⅰ婗-~�(	"鳫h遙=�C�H�$�dd�a�:j�=�蓈痔r�=觱釄氹嘕-WV8oW 9�~&-坓�(鬄酲;[純o穗嘕-WV8om�M%>mb-坓�(鬄�汬'这�)�8蛾爨lVGd匱韣y*�杜`颀l+�鞯.r擣�0G#盱谑﹝j�り"(��苳乮5絚_}4n4�硓�)�8蛾爨昉}s畏巠*�杜`颀l+�鞯.r擣�0G#盱谑j啣�q鰱(��苳乮5絚_}4n4�硓�)�8蛾爨鏷蹘洦昖y*�杜`颀l+�鞯.r擣�0G#盱谑坦�褮u(��苳乮5絚_}4n4�硓榌<�<騙螀Q5洪m!悞⌒��*嚮oG�`?烹|W\-0i�! T坿弰囚�<毘罿�*埗玚?烹|W\�]�)�7~9E\$L釉��E光9E\$L釉��E光5�蘫� 輽� �9E\$L釉��E光-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 贋XCRC冼�^笵A傮:塪逮0[豍&��1�揰煺匵注徔MB叕xsW論7":��        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       軀                .debug$T       h                 .text$mn       :      眡�     .debug$S                    .text$mn             補胑     .debug$S       �             .text$mn       0      燥"V     .debug$S    	   �             .text$mn    
   0      燥"V     .debug$S       �         
    .text$mn       0      燥"V     .debug$S    
   �             .text$mn       �     桒�     .debug$S       �  f           .text$x        (      镽=    .text$mn       #     碡(G     .debug$S       $  `           .text$x        (      镽=    .text$mn       �     z-�     .debug$S       X  f           .text$x        (      镽=    .text$mn       �      W�     .debug$S                     .text$mn       �     G橞�     .debug$S       �  >           .text$mn            兠J�     .debug$S       �	  l           .text$mn       �      搷壖     .debug$S       �             .text$x              }�+c    .text$mn             绩�     .debug$S    !   �
  J            .text$x     "         ��:     .text$mn    #   <      .ズ     .debug$S    $   0  
       #    .text$mn    %   <      .ズ     .debug$S    &   L  
       %    .text$mn    '   !      :著�     .debug$S    (   <         '    .text$mn    )   2      X于     .debug$S    *   <         )    .text$mn    +   [      穥�     .debug$S    ,   �         +    .text$mn    -         �%     .debug$S    .   T         -    .text$mn    /   e      凚Kr     .debug$S    0   �         /    .text$mn    1         ��#     .debug$S    2   �          1    .text$mn    3         ��#     .debug$S    4   �          3    .text$mn    5   B      贘S     .debug$S    6             5    .text$mn    7   B      贘S     .debug$S    8            7    .text$mn    9   B      贘S     .debug$S    :   �          9    .text$mn    ;   V      湴�-     .debug$S    <   �         ;    .text$mn    =          罂橌     .debug$S    >   �          =    .text$mn    ?   z     <b喻     .debug$S    @   P  �       ?    .text$x     A         �$�;?    .text$x     B         T�?    .text$mn    C          *V�     .debug$S    D   �          C    .text$mn    E          Ю嫀     .debug$S    F   �          E    .text$mn    G          x�:�     .debug$S    H   �          G    .text$mn    I          彘|T     .debug$S    J            I    .text$mn    K         c枎]     .debug$S    L            K    .text$mn    M          fi:/     .debug$S    N   �          M    .text$mn    O   x  
   嶬踨     .debug$S    P   D  >       O    .text$mn    Q          SPG     .debug$S    R             Q    .text$mn    S          J飱K     .debug$S    T   @         S    .text$mn    U          aJ鄔     .debug$S    V   �          U    .text$mn    W   z      �
     .debug$S    X   �         W    .text$mn    Y         �ッ     .debug$S    Z   �          Y    .text$mn    [         �ッ     .debug$S    \   �          [    .text$mn    ]         �ッ     .debug$S    ^   �          ]    .text$mn    _   B      鸮     .debug$S    `   �         _    .text$mn    a   B      臣6     .debug$S    b   �         a    .text$mn    c   A      俙Z%     .debug$S    d   �         c    .text$mn    e         崪覩     .debug$S    f   �          e                                        #                F                [                s       )        �       3        �       e        �       9        �           i�                          #        -      5        L          i�                    k      '        �      1        �      %        �      7                  i�                    5      U        ]               |      a        �      -        4      W        �      [        �      c        F      ]        �      /        �                             `      Q        �      C        �      I        �      G        ]      K        �      E              M        a      S        �      O        �      ?        .      =        e      ;        �               �      _        #	      +        R	      Y        �	              �	              t
              '              l              �      
                       �              �              
              [
              �
              �
              �              c              �      "              A        X      B        �               �           cosf             floorf           logf             memmove          powf             sinf             sqrtf            $LN5        )    $LN10       9    $LN7        #    $LN13       5    $LN10       %    $LN16       7    $LN3        U    $LN4        U    $LN20   B   a    $LN23       a    $LN27   z   W    $LN30       W    $LN3       [    $LN4        [    $LN20   A   c    $LN23       c    $LN3       ]    $LN4        ]    $LN35   e   /    $LN38       /    $LN154         $LN161           $LN36           $LN7        K    $LN75       O    $LN193  z  ?    $LN198      ?    $LN13       ;    $LN20   B   _    $LN23       _    $LN30   [   +    $LN33       +    $LN3       Y    $LN4        Y    $LN145  �          �  
       $LN150          $LN145  �            
       $LN150          $LN145  #          �  
       $LN150          $LN146          $LN4            $LN4        
    $LN152          $LN4            $LN14   :       $LN17           .xdata      g          （亵)        �      g    .pdata      h          T枨)        �      h    .xdata      i          %蚘%9        �      i    .pdata      j         惻竗9        �      j    .xdata      k          （亵#              k    .pdata      l         2Fb�#        H      l    .xdata      m          %蚘%5        p      m    .pdata      n         惻竗5        �      n    .xdata      o          （亵%        �      o    .pdata      p         2Fb�%        �      p    .xdata      q          %蚘%7        $      q    .pdata      r         惻竗7        V      r    .xdata      s          懐j濽        �      s    .pdata      t         Vbv鵘        �      t    .xdata      u          �9�a        �      u    .pdata      v         惻竗a        I      v    .xdata      w          （亵W        �      w    .pdata      x         X崘=W              x    .xdata      y          �9�[        |      y    .pdata      z         �1癧        �      z    .xdata      {          �9�c        O      {    .pdata      |         s�7錭        �      |    .xdata      }          �9�]        �      }    .pdata      ~         �1癩        Z      ~    .xdata                （亵/        �          .pdata      �         弋�/        �      �    .xdata      �          (臵         (      �    .pdata      �          $�         h      �    .xdata      �   	      � )9         �      �    .xdata      �         j         �      �    .xdata      �          嵑釄         1      �    .xdata      �         鉪        s      �    .pdata      �         �0�        �      �    .xdata      �   	      � )9        8      �    .xdata      �         j        �      �    .xdata      �          \{j              �    .xdata      �          僣糑        m      �    .pdata      �         #1iK        �      �    .xdata      �          >�5擮        @      �    .pdata      �         菜	O        �      �    .xdata      �   8      G架^O        �      �    .pdata      �         F>O        3      �    .xdata      �         鸛湴O        �      �    .pdata      �         �肘O        �      �    .xdata      �   H      �?        +      �    .pdata      �         �ⅷo?        h      �    .xdata      �   	      � )9?        �      �    .xdata      �   
      輴�?        �      �    .xdata      �          |鑒�?        (       �    .xdata      �          （亵;        g       �    .pdata      �         A鶬�;        �       �    .xdata      �          �9�_        "!      �    .pdata      �         惻竗_        W!      �    .xdata      �          （亵+        �!      �    .pdata      �         愶L+        �!      �    .xdata      �          �9�Y        �!      �    .pdata      �         �1癥        4"      �    .xdata      �         萦[�        o"      �    .pdata      �         w瓆�        �"      �    .xdata      �   
      B>z]        $#      �    .xdata      �          �2g�        �#      �    .xdata      �         T�8        �#      �    .xdata      �         r%�        ?$      �    .xdata      �   	       \�<{        �$      �    .xdata      �          3賟P        �$      �    .pdata      �         銀�*        f%      �    .voltbl     �                  _volmd      �    .xdata      �         萦[�        �%      �    .pdata      �         惱        s&      �    .xdata      �   
      B>z]        '      �    .xdata      �          �2g�        �'      �    .xdata      �         T�8        e(      �    .xdata      �         r%�        )      �    .xdata      �   	       �8雗        �)      �    .xdata      �          3賟P        T*      �    .pdata      �         銀�*        +      �    .voltbl     �                  _volmd      �    .xdata      �         萦[�        �+      �    .pdata      �         渢f�        t,      �    .xdata      �   
      B>z]        .-      �    .xdata      �          �2g�        �-      �    .xdata      �         T�8        �.      �    .xdata      �         r%�        i/      �    .xdata      �   	       螏汖        (0      �    .xdata      �          3賟P        �0      �    .pdata      �         銀�*        �1      �    .voltbl     �                  _volmd      �    .xdata      �          Qsv        z2      �    .pdata      �         �%zO        �2      �    .xdata      �         "�0�        3      �    .pdata      �         Ni朤        a3      �    .xdata      �         �-�        �3      �    .pdata      �         羭架        �3      �    .xdata      �         帀隳        K4      �    .pdata      �         3廹�        �4      �    .xdata      �         �-�        �4      �    .pdata      �         p$`�        55      �    .xdata      �          %蚘%        �5      �    .pdata      �         }S蛥        �5      �    .xdata      �          %蚘%
        :6      �    .pdata      �         }S蛥
        �6      �    .xdata      �          �F�        	7      �    .pdata      �         _襩�        }7      �    .xdata      �          %蚘%        �7      �    .pdata      �         }S蛥        &8      �    .xdata      �          �9�        [8      �    .pdata      �         礝
        �8      �    .rdata      �                      9     �    .rdata      �          �;�         +9      �    .rdata      �                      R9     �    .rdata      �                      i9     �    .rdata      �          �)         �9      �    .xdata$x    �                      �9      �    .xdata$x    �         虼�)         �9      �    .data$r     �   /      嶼�         �9      �    .xdata$x    �   $      4��         !:      �    .data$r     �   $      鎊=         v:      �    .xdata$x    �   $      銸E�         �:      �    .data$r     �   $      騏糡         �:      �    .xdata$x    �   $      4��         �:      �        (;           .rdata      �          IM         ;;      �    .rdata$r    �   $      'e%�         a;      �    .rdata$r    �         �          y;      �    .rdata$r    �                      �;      �    .rdata$r    �   $      Gv�:         �;      �    .rdata$r    �   $      'e%�         �;      �    .rdata$r    �         }%B         �;      �    .rdata$r    �                      �;      �    .rdata$r    �   $      `         <      �    .rdata$r    �   $      'e%�         '<      �    .rdata$r    �         �弾         J<      �    .rdata$r    �                      k<      �    .rdata$r    �   $      H衡�         �<      �    .rdata      �          =-f�         �<      �    .rdata      �          v靛�         �<      �    .rdata      �          y蘮�         �<      �    .rdata      �          2T頄         �<      �    .rdata      �          Z尨6         �<      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �   �                =  ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z ??1?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@QEAA@XZ ?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ ?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ ?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z ?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ ??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ ??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ?IsLocalLightPowerRISEnable@ReGIRContext@rtxdi@@QEBA_NXZ ?GetReGIRCellOffset@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRLightSlotCount@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRGridCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRGridCalculatedParameters@2@XZ ?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ?GetReGIRStaticParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRStaticParameters@2@XZ ?SetDynamicParameters@ReGIRContext@rtxdi@@QEAAXAEBUReGIRDynamicParameters@2@@Z ?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z ?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ ?ComputeGridLightSlotCount@ReGIRContext@rtxdi@@AEAAXXZ ?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z ??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ ?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ ??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z ??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z ??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z ??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z ??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z ??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z ??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z ??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Guess_median_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM00U?$less@X@0@@Z ??$_Copy_backward_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA ?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA ?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA ?dtor$0@?0???0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z@4HA ?dtor$0@?0???0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z@4HA ?dtor$0@?0??ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ@4HA ?dtor$1@?0??ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ@4HA _CxxThrowException __CxxFrameHandler4 __catch$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z$0 __catch$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z$0 __catch$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z$0 $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z $pdata$?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z $unwind$?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ $pdata$?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ $unwind$?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z $pdata$?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z $unwind$?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ $unwind$??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ $pdata$??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ $unwind$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $pdata$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $cppxdata$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $stateUnwindMap$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $ip2state$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $unwind$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $pdata$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $cppxdata$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $stateUnwindMap$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $ip2state$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $unwind$?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ $pdata$?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ $unwind$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $chain$9$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$9$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $chain$10$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$10$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $unwind$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $pdata$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $cppxdata$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $stateUnwindMap$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $ip2state$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $unwind$?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z $pdata$?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z $unwind$?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z $pdata$?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z $unwind$??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ $pdata$??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ $unwind$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $pdata$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $cppxdata$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $stateUnwindMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $tryMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $handlerMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $ip2state$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA $unwind$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$0$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$0$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$1$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$1$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$2$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$2$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$3$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$3$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $unwind$??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z $unwind$??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z $unwind$??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z $pdata$??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z $unwind$??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z $pdata$??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@3f000000 __real@3f800000 __real@40490fdb __real@40c90fdb __real@beaaaaab 