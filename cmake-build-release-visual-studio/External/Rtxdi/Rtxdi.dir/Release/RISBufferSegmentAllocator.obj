d�
 緢�         .drectve        /   �               
 .debug$S        �  �              @ B.debug$T        h   �              @ B.text$mn        
   /               P`.debug$S        �   9  5         @B.text$mn           q               P`.debug$S        @  x  �	         @B.text$mn           
               P`.debug$S        �   
           @B.chks64         P   ?               
     /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   p     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\RISBufferSegmentAllocator.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std �   :   ' t  rtxdi::RISBufferSegmentAllocator  u   uint32_t   �   h      ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  R    檒Gq$�#嗲RR�錨账��K諻刮g�   �    �m倐磟� `悇髬�!_囉vUqx;�)螗护  �    泽閇�R鯄呙+困胢p=�R刐鉍籫�8[     )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  R   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�      I嘛襨签.濟;剕��7啧�)煇9触�.  �   Z   D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\Source\RISBufferSegmentAllocator.cpp D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h   �       L9     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb �    H嬃�   �   �   a G            
       	   5        �rtxdi::RISBufferSegmentAllocator::RISBufferSegmentAllocator 
 >m   this  AJ        
                         @     m  Othis  O �   0           
   P      $         �      �     �,       0      
 �       �      
 �       �      
 �袎�   �   �   W G                      6        �rtxdi::RISBufferSegmentAllocator::allocateSegment 
 >m   this  AJ          >u    sizeInElements  A                                  @     m  Othis     u   OsizeInElements  O �   8              P      ,         �      �     �     �,       0      
 |       �      
 �       �      
            
 ��   �   �   ^ G                      7        �rtxdi::RISBufferSegmentAllocator::getTotalSizeInElements 
 >q   this  AJ                                 @     q  Othis  O�   0              P      $         �      �      �,       0      
 �       �      
 �       �      
 #f'�r蘱舆榓蚳鈊&连0&�&�� j廋岘葴[骈鳶Wv�*Yq��q刯�� MI矱慏�8瘡律        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       /                 .debug$S       �                .debug$T       h                 .text$mn       
       -餞�     .debug$S       �              .text$mn              �>闊     .debug$S       @             .text$mn              *V�     .debug$S    	   �                                 0               k           .chks64     
   P                 �   ??0RISBufferSegmentAllocator@rtxdi@@QEAA@XZ ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?getTotalSizeInElements@RISBufferSegmentAllocator@rtxdi@@QEBAIXZ 