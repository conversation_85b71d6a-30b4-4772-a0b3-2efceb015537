d哠 緢O�  �       .drectve        <  
               
 .debug$S        y  H              @ B.debug$T        h   T�              @ B.text$mn        u  紘  1�          P`.debug$S        �  蹔  彑      �   @B.text$x            ￥            P`.text$x            工  嗓          P`.text$x            婴  悚          P`.text$x            恧            P`.text$x            �  $�          P`.text$x            .�  K�          P`.text$x            U�  r�          P`.text$x            |�  櫏          P`.text$mn           ％  顶          P`.debug$S        �  昆  ��         @B.text$mn        
   姬  骚          P`.debug$S        t  缨  G�         @B.text$mn           儵  柀          P`.debug$S        �  牘  8�         @B.text$mn           t�  嚝          P`.debug$S        �  懌  )�         @B.text$mn        ^   e�  铆          P`.debug$S        X  氕  C�         @B.text$mn        �   话  １          P`.debug$S        �  吮  范      ,   @B.text$mn           o�               P`.debug$S          t�  ��         @B.text$mn           脊               P`.debug$S        �   凉  胶         @B.text$mn                          P`.debug$S            �         @B.text$mn           B�  V�          P`.debug$S          `�  p�         @B.text$mn                          P`.debug$S          敖  季         @B.text$mn                          P`.debug$S                      @B.text$mn           9�               P`.debug$S           >�  >�         @B.text$mn           z�               P`.debug$S           �  �         @B.text$mn           宦               P`.debug$S           缆  烂         @B.text$mn                          P`.debug$S           �  �         @B.text$mn           =�               P`.debug$S           B�  B�         @B.text$mn        �   ~�  �          P`.debug$S        �  4�  鹑         @B.text$mn        :   h�  ⑸          P`.debug$S          郎  允         @B.text$mn           �               P`.debug$S        D  (�  l�         @B.xdata             继  蕴         @0@.pdata             杼  籼         @0@.xdata          	   �  �         @@.xdata          *   /�  Y�         @@.xdata             ┩              @@.xdata             酵              @0@.pdata             磐  淹         @0@.xdata             锿              @0@.pdata             魍  �         @0@.xdata             !�              @0@.pdata             )�  5�         @0@.xdata             S�              @0@.pdata             [�  g�         @0@.xdata             呂              @0@.pdata             嵨  櫸         @0@.xdata             肺              @0@.pdata             课  宋         @0@.xdata             槲           @0@.pdata             �  �         @0@.xdata             1�  A�         @0@.pdata             K�  W�         @0@.xdata             u�              @0@.pdata             }�  壪         @0@.xdata               幌         @0@.pdata             傧  逑         @0@.xdata             �  �         @0@.pdata             1�  =�         @0@.xdata             [�  o�         @0@.pdata             嵭  櫺         @0@.chks64         �  沸               
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   t  p     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ImportanceSamplingContext.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $?A0x639b76ef �   UI  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable ? �   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ �    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E �   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask - L   rtxdi::c_NumReSTIRDIReservoirBuffers - L   rtxdi::c_NumReSTIRGIReservoirBuffers . �    std::integral_constant<bool,0>::value R �   std::allocator<ReGIR_OnionLayerGroup>::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed L �   std::allocator<ReGIR_OnionRing>::_Minimum_asan_allocation_alignment - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 4 �  @ _Mtx_internal_imp_t::_Critical_section_size 5 �   _Mtx_internal_imp_t::_Critical_section_align 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 + �    std::_Aligned_storage<64,8>::_Fits 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits % \    _Atomic_memory_order_relaxed % \   _Atomic_memory_order_consume % \   _Atomic_memory_order_acquire % \   _Atomic_memory_order_release % \   _Atomic_memory_order_acq_rel % \   _Atomic_memory_order_seq_cst $ �    std::strong_ordering::equal : �    std::integral_constant<unsigned __int64,0>::value ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy : �   std::integral_constant<unsigned __int64,2>::value / �   std::atomic<long>::is_always_lock_free : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ; �   std::atomic<unsigned __int64>::is_always_lock_free  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  .  ReSTIRDI_BufferIndices &   $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t + 
  ReSTIRGI_SpatialResamplingParameters  (  _TypeDescriptor 	 )  tm % k  _s__RTTICompleteObjectLocator2 " V  RTXDI_LightBufferParameters A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType  !  rtxdi::uint3  �  rtxdi::ReGIRContext $ �  rtxdi::ReGIRDynamicParameters    rtxdi::float3 1 �  rtxdi::LocalLightReGIRFallbackSamplingMode 8 b  rtxdi::ImportanceSamplingContext_StaticParameters # �  rtxdi::ReGIRStaticParameters ' t  rtxdi::RISBufferSegmentAllocator '   rtxdi::ReGIRGridStaticParameters & �  rtxdi::ReSTIRDIStaticParameters  =  rtxdi::CheckerboardMode (   rtxdi::ReGIROnionStaticParameters & �  rtxdi::ReSTIRGIStaticParameters  {  rtxdi::ReGIRMode ( �  rtxdi::RISBufferSegmentParameters  8  rtxdi::ReSTIRGIContext , u  rtxdi::ReGIROnionCalculatedParameters +   rtxdi::ReGIRGridCalculatedParameters , �  rtxdi::LocalLightReGIRPresamplingMode ' �  rtxdi::ImportanceSamplingContext  �  rtxdi::ReSTIRDIContext & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> &   ReSTIRGI_FinalShadingParameters E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16 M   std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > D   std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >  �  std::_Lockit U .  std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > 2   std::default_delete<rtxdi::ReSTIRGIContext>  "   std::_Atomic_counter_t    std::_Num_base # �  std::numeric_limits<char8_t> & s  std::allocator<ReGIR_OnionRing>  �  std::hash<float> d   std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRGIContext>,rtxdi::ReSTIRGIContext *,1>    std::_Num_int_base , �  std::allocator<ReGIR_OnionLayerGroup>    std::float_denorm_style  q  std::bad_cast     std::_Compare_t " A  std::numeric_limits<double>  �  std::__non_rtti_object ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  \  std::pointer_safety  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short>   |  std::pmr::memory_resource  �  std::false_type    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t> % �  std::integral_constant<bool,1>     std::_Leave_proxy_unbound � �  std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1>  �  std::_Iterator_base12 C �  std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > $ �  std::_Atomic_integral<long,4>  �  std::hash<long double> = �  std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> % �  std::integral_constant<bool,0>  
  std::bad_exception & �  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator ! ?  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> G �  std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> >  T  std::_Ref_count_base  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>  �  std::true_type   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits x A  std::_Compressed_pair<std::default_delete<rtxdi::RISBufferSegmentAllocator>,rtxdi::RISBufferSegmentAllocator *,1> P �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > f �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy ! ,  std::numeric_limits<short> ! c  std::_Shared_ptr_spin_lock     std::bad_alloc # 2  std::numeric_limits<__int64> /   std::default_delete<rtxdi::ReGIRContext>  C  std::memory_order [ O  std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > # �  std::_Atomic_storage<long,4>  l  std::atomic_flag > �  std::allocator_traits<std::allocator<ReGIR_OnionRing> > o �  std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> >   6  std::bad_array_new_length v ~  std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1>  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int>  �  std::atomic<long>   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::bad_typeid < :  std::default_delete<rtxdi::RISBufferSegmentAllocator>  �  std::_Compare_eq    std::nullptr_t  <  std::bad_weak_ptr ) 8  std::numeric_limits<unsigned long>   K  std::_Atomic_padded<long> ' "  std::numeric_limits<signed char>  �  std::_Literal_zero 2 '  std::default_delete<rtxdi::ReSTIRDIContext>      std::numeric_limits<char>  �  std::_Unused_parameter * �  std::ranges::_Uninitialized_fill_fn 7 &  std::ranges::_Uninitialized_value_construct_n_fn # G  std::ranges::_Find_if_not_fn , �  std::ranges::_Uninitialized_move_n_fn !   std::ranges::_Destroy_n_fn $ �  std::ranges::_Construct_at_fn "   std::ranges::_Destroy_at_fn  ;  std::ranges::_Find_fn ! �  std::ranges::subrange_kind    std::ranges::_Next_fn % M  std::ranges::_Adjacent_find_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn 7   std::ranges::_Uninitialized_default_construct_fn * �  std::ranges::_Uninitialized_move_fn , �  std::ranges::_Uninitialized_copy_n_fn   5  std::ranges::_Mismatch_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag  k  std::ranges::_Min_fn  )  std::ranges::_Copy_fn * �  std::ranges::_Uninitialized_copy_fn    std::ranges::_Destroy_fn , �  std::ranges::_Uninitialized_fill_n_fn  #  std::ranges::dangling  S  std::ranges::_Search_fn    std::ranges::_Prev_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn    std::ranges::_Advance_fn 5    std::ranges::_Uninitialized_value_construct_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn 9   std::ranges::_Uninitialized_default_construct_n_fn ^   std::_Compressed_pair<std::default_delete<rtxdi::ReGIRContext>,rtxdi::ReGIRContext *,1> [   std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > d .  std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRDIContext>,rtxdi::ReSTIRDIContext *,1> " �  std::_Asan_aligned_pointers  �  std::partial_ordering  .  std::numeric_limits<int>  �  std::bad_variant_access D j  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > Z 8  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat  8  timespec 
 !   _ino_t , �  ReSTIRDI_TemporalResamplingParameters M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ' Z  RTXDI_RISBufferSegmentParameters + �  ReSTIRDI_SpatialResamplingParameters  �  RTXDI_LightBufferRegion  9  terminate_handler  �  _s__RTTIBaseClassArray  �  ReGIR_OnionLayerGroup 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24 ) �  ReSTIRDI_InitialSamplingParameters  �  _CatchableTypeArray     ptrdiff_t  "  _stat64i32  �  _PMD  >  type_info ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t  U  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo - �  RTXDI_EnvironmentLightBufferParameters  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 ! �  ReSTIRDI_ShadingParameters % |  __RTTIClassHierarchyDescriptor & G  RTXDI_ReservoirBufferParameters 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE  �  ReGIR_OnionRing ,   ReSTIRGI_TemporalResamplingParameters 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray    ReSTIRGI_BufferIndices & �  ReSTIRDI_LocalLightSamplingMode 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  r  RTXDI_RuntimeParameters  �  _ldiv_t  9  _timespec64  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers    �   x      +4[(広
倬禼�溞K^洞齹誇*f�5  `    �"睱建Bi圀対隤v��cB�'窘�n  �    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<     �	R\�5甕:7峡铻崑p!騎P与�3�%�;  6   V8追i顚�^�k细�;>牧惺扴	�\s  t   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�     z�0叐i�%`戉3猂|Ei韍訋�#Q@�  H   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�     �?B�)��攨~�倮wM晫#��%  U    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟      d蜯�:＠T邱�"猊`�?d�B�#G騋  Z   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  J   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  ,   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  y   �0�*е彗9釗獳+U叅[4椪 P"��  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   D���0�郋鬔G5啚髡J竆)俻w��  P   悯R痱v 瓩愿碀"禰J5�>xF痧  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   矨�陘�2{WV�y紥*f�u龘��  "   _O縋[HU-銌�鼪根�鲋薺篮�j��  k   齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  (	   檒Gq$�#嗲RR�錨账��K諻刮g�   [	   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �	   -�
�捂�
y�*犯丁�02?栕9/�Q  �	   	{Z�范�F�m猉	痹缠!囃ZtK�T�  
   副謐�斦=犻媨铩0
龉�3曃譹5D   a
   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �
   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �
   椛`榿B�:瀚�&�%玲�$;舘傼�,擇��      寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  `   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   *u\{┞稦�3壅阱\繺ěk�6U�  &   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  c   繃S,;fi@`騂廩k叉c.2狇x佚�  �   泽閇�R鯄呙+困胢p=�R刐鉍籫�8[  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  D
   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �
   5�\營	6}朖晧�-w氌rJ籠騳榈  �
   �疀�4�A圏,oHB瓳HJ��2�0(v/  
   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  K   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  
   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  J   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   �*o驑瓂a�(施眗9歐湬

�  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  1    I嘛襨签.濟;剕��7啧�)煇9触�.  q   +椬恡�
	#G許�/G候Mc�蜀煟-  �   c�#�'�縌殹龇D兺f�$x�;]糺z�     d潣7熈[$袎o�懠I殑Iy厵唫嬎�  G   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�     偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  H   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  Z   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  /   G�膢刉^O郀�/耦��萁n!鮋W VS  n   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  \   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  )   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  s   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   匐衏�$=�"�3�a旬SY�
乢�骣�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  G   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�     绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  f   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   �
bH<j峪w�/&d[荨?躹耯=�  �   交�,�;+愱`�3p炛秓ee td�	^,  0   _臒~I��歌�0蘏嘺QU5<蝪祰S  u   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�     ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  P   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   �      }  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  `  �  �  `  �  �  `  �  �  `  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �
  �  �  �  �  �  �
  �  �  �  �  �  �
  �  �  �  �  �  �
  �  �  �  �  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �     �  �    �  �    �  �  
  �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �     �  �  !  �  �  "  �  �  #  �  �  $  �  �  (  �  �  )  �  �  *  �  �  +  �  �  �   �   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\RTXPT\External\Rtxdi\Source\ImportanceSamplingContext.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDIParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert D:\RTXPT\External\Rtxdi\include\Rtxdi\ImportanceSamplingContext.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDI.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h �       L�     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb H塡$H塋$VWAVH冹@L嬺H嬞3�H�9H墆H墆H墆W�A A0A@峅�    H塂$hH吚t
H嬋�    �H嬊H�H�H吷t
�   �    A媀A�H��    塁PA婩塁XA�塁TA媀A疺H��    塁`A婩塁hA婩塁dA婩塂$<A婩塂$0A婩塂$4A婩塂$8桂   �    H塂$hH吚tH峊$0H嬋�    �H嬊H婯H塁H吷t
吼   �    H�3箞   �    H塂$hH吚tI峍 L嬈H嬋�    �H嬊H媠H塁H咑劽   H婲`H吷tAH媀pH+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐囼   I嬋�    H墌`H墌hH墌pL婩HM吚tcH婲XI+菻斧*H鏖H龙H嬄H凌?H蠬�RH菱H侜   rH兟'I婬鳯+罥岪鳫凐噳   L嬃I嬋�    H墌HH墌PH墌X簣   H嬑�    A婩塂$(A婩塂$ A婩塂$$工   �    H塂$hH吚tH峊$ H嬋�    H孁H婯H墈H吷t氦   �    怘嬅H媆$pH兡@A^_^描    �<   m    N   �    h   n    x   �    �   �    �   m    �   p      n      m    -  t    �  n    �  n      n    (  m    ?  s    Y  n    p  o       �   �
  a G            u     u  �        �rtxdi::ImportanceSamplingContext::ImportanceSamplingContext 
 >�   this  AI       ]N	  AJ          D`    >�   isParams  AK          AV       `W  >�    restirGIStaticParams  D     >�    restirDIStaticParams  D0    M        �  侳 M        �  侳(
 M          侳 >C    _Old_val  AJ  J      AJ ^      N M        �  
係
 Z   {   N N N M        �  $�" Z   �     N M        �  �莵6�9 M        �  �6(��	�( M          �6 >    _Old_val  AL  :    ;4  N M        �  �秮G�( M        �  l亼��% M        �  亼iW$	v M        �  5伡��  M        �  伳)��
 Z   {  
 >   _Ptr  AP �      >#    _Bytes  AK  �    -  AK o     & M        }  佂d#��
 Z   3   >�    _Ptr_container  AJ  �      AJ �    �  �  >�    _Back_shift  AP  �    T  AP �    �   ! e  N N N N N M        �  J丟  M        �  丟i5$ M        �  .乄 M        �  乕)
 Z   {  
 >   _Ptr  AJ �      >#    _Bytes  AK  T    1    AK o       M        }  乨d# >�    _Ptr_container  AP  l      AP �    �  �  >�    _Back_shift  AJ  K    5  AJ �    �    _ � j  N N N N N N N N M        �  � Z   �  �   >�   <_Args_1>  AL      ,  N M        �  �� M        �  ��(
 M        !  �� >�    _Old_val  AJ  �       AJ       N M        �  
�
 Z   {   N N N M        �  &�� Z   �  �   N M        �  W M           W&
 M        #  W >v    _Old_val  AJ  Z       AJ l       N M        �  
b
 Z   {   N N N M        �  8 Z   �  5   N M        �  % M          % N N M        �  ! M          ! N N M        �   M        
   N N M        �   M           N N Z   6  6   @                    @ .hJ   �  }  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         
                       !  "  #  $  (  )  *  +         $LN215  `   �  Othis  h   �  OisParams !     �  OrestirGIStaticParams ! 0   �  OrestirDIStaticParams  ^;      l   ^�      �   ^     �   ^'        O �   �           u  �     �       ,  �,   +  �8   /  �l   0  �   1  ��   2  ��   3  ��   4  ��   5  ��   8  ��   9  ��   :  ��   ;  ��   <  �  >  �
  A  �  B  �  C  �"  D  �^  E  �o  >  ��     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$0 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$1 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$2 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$3 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$8 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O   �     q F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$10 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O  �     q F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$12 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O  �     q F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$16 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O  ,   v    0   v   
 �   v    �   v   
 �   v    �   v   
 �   v    �   v   
 �   v    �   v   
 �  v    �  v   
 �  v    �  v   
 {  v      v   
 F  v    J  v   
 g  v    k  v   
 w  v    {  v   
 �  v    �  v   
 �  v    �  v   
   v      v   
   v    "  v   
 �  v    �  v   
 �  v    �  v   
   v      v   
 O  v    S  v   
 _  v    c  v   
 �  v    �  v   
 �  v    �  v   
   v      v   
 �  v    �  v   
 �  v    �  v   
 ]  v    a  v   
 m  v    q  v   
 �	  �    �	  �   
 k
  v    o
  v   
 {
  v    
  v   
 �
  v    �
  v   
 �
  v    �
  v   
 �
  v    �
  v   
 �  �    �  �   
 	  �    
  �   
 <  �    @  �   
 o  �    s  �   
 �  �    �  �   
 1
  �    5
  �   
 d
  �    h
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 <  �    @  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 d  �    h  �   
 �  �    �  �   
   �    	  �   
 8  �    <  �   
 �  �    �  �   
 �  �    �  �   
 -  �    1  �   
 `  �    d  �   
 �  �    �  �   
 "  �    &  �   
 U  �    Y  �   
 �  �    �  �   
 H媻`   �       �    H媻`   H兞�       �    H媻`   H兞�       �    H媻`   H兞�       �    @UH冹 H嬯�   H婱h�    H兡 ]�   n    @UH冹 H嬯吼   H婱h�    H兡 ]�   n    @UH冹 H嬯簣   H婱h�    H兡 ]�   n    @UH冹 H嬯氦   H婱h�    H兡 ]�   n    H�	H吷t
�   �    �   n       �   s  � G                      �        �std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> >::~unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> > 
 >�   this  AJ          M        �  
 N                        H�  h   �  �      �  Othis  O �   8              �     ,       � �    � �   � �   � �,   �    0   �   
   �      �   
 �  �    �  �   
 H�H呉�    �   �       �   .  � G            
          �        �std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> >::~unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > 
 >   this  AJ        
 
 Z   �                          H� 
 h   �        Othis  O  �   0           
   �     $       � �    � �   � �,   �    0   �   
 �   �    �   �   
 D  �    H  �   
 H�	H吷t
吼   �    �   n       �   K  � G                      �        �std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> >::~unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > 
 >�   this  AJ          M        �  
 N                        H�  h   �  �      �  Othis  O �   8              �     ,       � �    � �   � �   � �,   �    0   �   
 �   �    �   �   
 `  �    d  �   
 H�	H吷t
氦   �    �   n       �   K  � G                      �        �std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> >::~unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > 
 >6   this  AJ          M        �  
 N                        H�  h   �  �      6  Othis  O �   8              �     ,       � �    � �   � �   � �,   �    0   �   
 �   �    �   �   
 `  �    d  �   
 @SH冹 H嬞H婭H吷t
氦   �    H婼H岾H呉t�    H婯H吷t
吼   �    H�H吷t�   H兡 [�    H兡 [�   n    *   �    =   n    T   n       �     b G            ^      X   �        �rtxdi::ImportanceSamplingContext::~ImportanceSamplingContext 
 >�   this  AI  	     T J   AJ        	  M        �  A
 M        �  I
 N N M        �  .
	 M        �  
7
 Z   {   N N M        �  
 Z   �  
 >   this  AJ  $     
  AJ .       N M        �  )
	 M        �  

 Z   {   N N                       @� 2 h   �  �  �  �  �  �  �  �  �  �  �   0   �  Othis  O   �   (           ^   �            H  �	   J  �,   w    0   w   
 �   w    �   w   
 �   w    �   w   
 `  w    d  w   
 p  w    t  w   
 0  w    4  w   
 H呉勜   SH冹 H婮`H嬟H墊$03�H吷tAH婻pH+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐嚁   I嬋�    H墈`H墈hH墈pL婥HM吚t_H婯XH斧*I+菻鏖H龙H嬄H凌?H蠬�RH菱H侜   rI婬鳫兟'L+罥岪鳫凐w/L嬃I嬋�    H墈HH墈PH墈X簣   H嬎�    H媩$0H兡 [描    蘎   n    �   n    �   n    �   o       �   �  Z G            �      �   �        �std::default_delete<rtxdi::ReGIRContext>::operator() 
 >   this  AJ          AJ �       D0   
 >   _Ptr  AI       � �   AK          AK �       M        �  hb��$ M        �  biS$	 M        �  1��U M        �  ��),
 Z   {  
 >   _Ptr  AP �       >#    _Bytes  AK  �     )  AK �      # M        }  
��#
/
 Z   3   >�    _Ptr_container  AJ  �       AJ �     1  )  >�    _Back_shift  AP  f     P  AP �     1   !   N N N N N M        �  H" M        �  g5$ M        �  .( M        �  ,)
 Z   {  
 >   _Ptr  AJ Q       >#    _Bytes  AK  %     1    AK �       M        }  
5# >�    _Ptr_container  AP  9       AP Q     �  �  >�    _Back_shift  AJ       ?  AJ Q     �    [ �   N N N N N                       H� J h   �  }  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN78  0     Othis  8     O_Ptr  O  �   H           �   �     <       ` �    b �   ` �   b ��   c ��   b �,   �    0   �   
    �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 	  �    
  �   
   �      �   
 C  �    G  �   
 S  �    W  �   
   �    
  �   
 '  �    +  �   
 ;  �    ?  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 c  �    g  �   
 �  �    �  �   
 H岮`�   �   �   q G                      �        �rtxdi::ImportanceSamplingContext::GetEnvironmentLightRISBufferSegmentParams 
 >�   this  AJ                                 @     �  Othis  O �   0              �     $       z  �    {  �   |  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H岮 �   �   �   ` G                      �        �rtxdi::ImportanceSamplingContext::GetLightBufferParameters 
 >�   this  AJ                                 @     �  Othis  O  �   0              �     $       p  �    q  �   r  �,       0      
 �       �      
 �       �      
 H岮P�   �   �   k G                      �        �rtxdi::ImportanceSamplingContext::GetLocalLightRISBufferSegmentParams 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       u  �    v  �   w  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H冹(H婭�    � H兡(�	   r       �   �   ^ G                     �        �rtxdi::ImportanceSamplingContext::GetNeighborOffsetCount 
 >�   this  AJ         
 Z   �   (                      @ 
 h   �   0   �  Othis  O�   0              �     $         �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H��   �   �   d G                      �        �rtxdi::ImportanceSamplingContext::GetRISBufferSegmentAllocator 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O  �   0              �     $       k  �    l  �   m  �,   ~    0   ~   
 �   ~    �   ~   
 �   ~    �   ~   
 H婣�   �   �   W G                      �        �rtxdi::ImportanceSamplingContext::GetReGIRContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O   �   0              �     $       W  �    X  �   Y  �,   z    0   z   
 |   z    �   z   
 �   z    �   z   
 H婣�   �   �   W G                      �        �rtxdi::ImportanceSamplingContext::GetReGIRContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O   �   0              �     $       \  �    ]  �   ^  �,   {    0   {   
 |   {    �   {   
 �   {    �   {   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRDIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       M  �    N  �   O  �,   x    0   x   
    x    �   x   
 �   x    �   x   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRDIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       R  �    S  �   T  �,   y    0   y   
    y    �   y   
 �   y    �   y   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRGIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       a  �    b  �   c  �,   |    0   |   
    |    �   |   
 �   |    �   |   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRGIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       f  �    g  �   h  �,   }    0   }   
    }    �   }   
 �   }    �   }   
 @SH侅�   H�    H3腍墑$�   H嬞H峊$ H婭�    婦$<凐t4凐u+H婯H峊$@�    儀tH婯H峊$`�    儀斃�2离�H媽$�   H3惕    H伳�   [�   �    (   q    D   u    X   u    w   �       �   M  c G            �      k   �        �rtxdi::ImportanceSamplingContext::IsLocalLightPowerRISEnabled 
 >�   this  AI       e  AJ          >�   iss  C      0     7    C     i       D     Z   �  �  �   �                     A  h   �  �  
 :�   O  �   �  Othis      �  Oiss  O   �   X           �   �     L       �  �   �  �,   �  �5   �  �:   �  �e   �  �i   �  �k   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 d  �    h  �   
 H冹XH�    H3腍塂$@H婭H峊$ �    儀斃H婰$@H3惕    H兡X�   �       q    1   �       �   �   V G            :      (   �        �rtxdi::ImportanceSamplingContext::IsReGIREnabled 
 >�   this  AJ         
 Z   �   X                      A 
 h   �  
 :@   O  `   �  Othis  O�   0           :   �     $       �  �   �  �(   �  �,   �    0   �   
 {   �       �   
 �   �    �   �   
 A JI0B A@�   �   �   \ G                      �        �rtxdi::ImportanceSamplingContext::SetLightBufferParams 
 >�   this  AJ          >�   lightBufferParams  AK                                 @     �  Othis     �  OlightBufferParams  O  �   0              �     $       �  �    �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
  4 r�p`           �       �        u          �       �       �    (           �       �        �6    .    .    .    V    ~    �       �       �    
   �       �       �       �    !   �    &   �    v$
T�.R2�* 2P               �       �       �     2P               �       �       �     2P               �       �       �     2P               �       �       �     20    ^           �       �       �     B                 �       �       �     	 0      �      �        �           �       �       �     �      @      �        :           �       �       �     2
0               �       �       �    ! t               �       �       �       �           �       �       �    !                 �       �       �    �   �           �       �       �    !   t               �       �       �    �   �           �       �       �    E褼5`苁(e擆鬚9蚳鈊&连0涩鍩�?r罛2$U掠釬顪$phxl67獄呏M烀�<�68曤m'+�ZsPp侧3G�6�>-B7琏�Q陑V#斮Q頶�#	�M>*#�*�
6�2该赂;12嚞T!�|DtLs激瘼粃减�&(	M
6繯	啃尰�3zEQ@掏-β�:衮鈍旹�"gi酭憰怈�証≒�5eE�ō晷嬏枌蘂磷�p8[Z�?B鼺塲+遠� !FiJ)rY淟捠qBh{誕梷SK�&獘箎-	k傕Tr鐵箎-	kl.H碫�麱陫婉f訯{痬j遜麱陫婉f詪#艽厛k圶�9幄,Q07&=稹k圶�9幄�w嘹� �4e2=9蜘Q�lI�/顅AZ鷈`�榕�;曾韜�I�閷蜫佸挲贿荶峴寮�>与�	0X掷dd�a�:跊1��粽Ozj�&樢閣yQ E<礼\樢閣yQ E<礼\樢閣yQ E<礼\樢閣yQ E<礼\雵J-WV8o��腫62V-坓�(鬄鯅�$劥#?痫�i忞婓�4{	1>&
�?�/B/ｎ	蜍R帙Vr屵祁懚獲r貂筦绬靻3;f2���櫇�"`Z_训0玂昻瓓髱[w鵫�7颸s hw        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       y                .debug$T       h                 .text$mn       u     4     .debug$S       �  �           .text$x              �/�    .text$x              
@獕    .text$x              /f    .text$x     	         朆|�    .text$x     
         鼺裠    .text$x              y2fo    .text$x              蕩	    .text$x     
         硅D�    .text$mn             伭-     .debug$S       �             .text$mn       
      瀲l�     .debug$S       t             .text$mn             洵f4     .debug$S       �             .text$mn             謰檮     .debug$S       �             .text$mn       ^      x�"�     .debug$S       X             .text$mn       �      擙5�     .debug$S       �  ,           .text$mn              4bxa     .debug$S                    .text$mn              1-�     .debug$S       �              .text$mn              荰>�     .debug$S                    .text$mn              �$
�     .debug$S    !                 .text$mn    "          �&定     .debug$S    #            "    .text$mn    $          D,k     .debug$S    %             $    .text$mn    &          D,k     .debug$S    '             &    .text$mn    (          G�7�     .debug$S    )             (    .text$mn    *          G�7�     .debug$S    +             *    .text$mn    ,          熙�     .debug$S    -             ,    .text$mn    .          熙�     .debug$S    /             .    .text$mn    0   �      �#�     .debug$S    1   �         0    .text$mn    2   :      "鎊     .debug$S    3            2    .text$mn    4          �jt�     .debug$S    5   D         4                                        #                F                �                �                <               }               �               *              �              �      (              *        W      $        �      &        �      ,        A      .        �      "        �              \              �              H               �      0        �      2        
      4        j              �              J              �              �              _	               �	               �	              2
              �
                    
        y              �              Q      	        �      
        )
               <
               M
           $LN215  u      $LN218          $LN27           $LN4             $LN12       0    $LN4        2    $LN78   �       $LN82           .xdata      6         �%$z        e
      6    .pdata      7         輲/s        �
      7    .xdata      8   	      � )9        .      8    .xdata      9   *      曣�        �      9    .xdata      :          #Kc&              :    .xdata      ;          k�        i      ;    .pdata      <         �$剧        �      <    .xdata      =          k�        P      =    .pdata      >         �$剧        �      >    .xdata      ?          k�        9      ?    .pdata      @         �$剧        �      @    .xdata      A          k�        "      A    .pdata      B         �$剧        �      B    .xdata      C          （亵              C    .pdata      D         翎珸        ?      D    .xdata      E          �9�         r      E    .pdata      F         �?聒         �      F    .xdata      G         趗�0              G    .pdata      H          媞�0        R      H    .xdata      I         薉O�2        �      I    .pdata      J         礝
2        �      J    .xdata      K          #D[�        #      K    .pdata      L         O?[4        z      L    .xdata      M         T�%~        �      M    .pdata      N         苛�        (      N    .xdata      O         Ｕ�        �      O    .pdata      P         沵�        �      P    .xdata      Q         �:        0      Q    .pdata      R         ＃涺        �      R        �           .chks64     S   �                �  ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn ??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z ?GetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z ??1ImportanceSamplingContext@rtxdi@@QEAA@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRDIContext@2@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRDIContext@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReGIRContext@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReGIRContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRGIContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRGIContext@2@XZ ?GetRISBufferSegmentAllocator@ImportanceSamplingContext@rtxdi@@QEBAAEBVRISBufferSegmentAllocator@2@XZ ?GetLightBufferParameters@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_LightBufferParameters@@XZ ?GetLocalLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetEnvironmentLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ ?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?SetLightBufferParams@ImportanceSamplingContext@rtxdi@@QEAAXAEBURTXDI_LightBufferParameters@@@Z ??1?$unique_ptr@VRISBufferSegmentAllocator@rtxdi@@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRDIContext@rtxdi@@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@@std@@QEAA@XZ ??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z ??1?$unique_ptr@VReGIRContext@rtxdi@@U?$default_delete@VReGIRContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRGIContext@rtxdi@@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@@std@@QEAA@XZ ??0RISBufferSegmentAllocator@rtxdi@@QEAA@XZ ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?dtor$0@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$10@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$12@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$16@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$1@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$2@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$3@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$8@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA __CxxFrameHandler4 __GSHandlerCheck __security_check_cookie $unwind$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $pdata$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $cppxdata$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $stateUnwindMap$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $ip2state$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $unwind$?dtor$8@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$8@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$?dtor$10@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$10@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$?dtor$12@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$12@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$?dtor$16@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$16@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$??1ImportanceSamplingContext@rtxdi@@QEAA@XZ $pdata$??1ImportanceSamplingContext@rtxdi@@QEAA@XZ $unwind$?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ $pdata$?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ $unwind$?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $pdata$?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $unwind$?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $pdata$?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $unwind$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $chain$0$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$0$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $chain$1$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$1$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $chain$2$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$2$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z __security_cookie 