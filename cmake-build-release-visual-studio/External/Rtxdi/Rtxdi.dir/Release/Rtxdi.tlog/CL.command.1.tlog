^D:\RTXPT\EXTERNAL\RTXDI\SOURCE\IMPORTANCESAMPLINGCONTEXT.CPP
/c /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"RTXDI.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\RTXDI\RELEASE\RTXDI.PDB" /external:W1 /Gd /TP D:\RTXPT\EXTERNAL\RTXDI\SOURCE\IMPORTANCESAMPLINGCONTEXT.CPP
^D:\RTXPT\EXTERNAL\RTXDI\SOURCE\REGIR.CPP
/c /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"RTXDI.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\RTXDI\RELEASE\RTXDI.PDB" /external:W1 /Gd /TP D:\RTXPT\EXTERNAL\RTXDI\SOURCE\REGIR.CPP
^D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RESTIRDI.CPP
/c /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"RTXDI.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\RTXDI\RELEASE\RTXDI.PDB" /external:W1 /Gd /TP D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RESTIRDI.CPP
^D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RESTIRGI.CPP
/c /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"RTXDI.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\RTXDI\RELEASE\RTXDI.PDB" /external:W1 /Gd /TP D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RESTIRGI.CPP
^D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RISBUFFERSEGMENTALLOCATOR.CPP
/c /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"RTXDI.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\RTXDI\RELEASE\RTXDI.PDB" /external:W1 /Gd /TP D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RISBUFFERSEGMENTALLOCATOR.CPP
^D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RTXDIUTILS.CPP
/c /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"RTXDI.DIR\RELEASE\\" /Fd"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\RTXDI\RELEASE\RTXDI.PDB" /external:W1 /Gd /TP D:\RTXPT\EXTERNAL\RTXDI\SOURCE\RTXDIUTILS.CPP
