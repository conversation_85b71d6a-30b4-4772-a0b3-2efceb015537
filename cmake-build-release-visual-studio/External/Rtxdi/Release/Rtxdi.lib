!<arch>
/               1755678910              0       10785     `
   �  T�  T�  T�  T�  T�  T�  T�  T�  T�  T�  T�  T�  迂  迂  迂  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$  �$ $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� $� 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕 躕                     ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?ComputePdfTextureSize@rtxdi@@YAXIAEAI00@Z ?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z ?JenkinsHash@rtxdi@@YAII@Z __real@3e800000 __real@3f000000 __real@3f11e10d __real@3f413fa9 __real@3f800000 __real@3ff0000000000000 __real@437a0000 __real@bf800000 ??0RISBufferSegmentAllocator@rtxdi@@QEAA@XZ ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?getTotalSizeInElements@RISBufferSegmentAllocator@rtxdi@@QEBAIXZ ??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z ?GetBufferIndices@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_BufferIndices@@XZ ?GetDefaultReSTIRGIBufferIndices@rtxdi@@YA?AUReSTIRGI_BufferIndices@@XZ ?GetDefaultReSTIRGIFinalShadingParams@rtxdi@@YA?AUReSTIRGI_FinalShadingParameters@@XZ ?GetDefaultReSTIRGISpatialResamplingParams@rtxdi@@YA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRGITemporalResamplingParams@rtxdi@@YA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?GetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_FinalShadingParameters@@XZ ?GetFrameIndex@ReSTIRGIContext@rtxdi@@QEBAIXZ ?GetResamplingMode@ReSTIRGIContext@rtxdi@@QEBA?AW4ReSTIRGI_ResamplingMode@2@XZ ?GetReservoirBufferParameters@ReSTIRGIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetStaticParams@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGIStaticParameters@2@XZ ?GetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?SetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_FinalShadingParameters@@@Z ?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z ?SetResamplingMode@ReSTIRGIContext@rtxdi@@QEAAXW4ReSTIRGI_ResamplingMode@2@@Z ?SetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_SpatialResamplingParameters@@@Z ?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z ?UpdateBufferIndices@ReSTIRGIContext@rtxdi@@AEAAXXZ ??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z ?GetBufferIndices@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_BufferIndices@@XZ ?GetDefaultReSTIRDIBufferIndices@rtxdi@@YA?AUReSTIRDI_BufferIndices@@XZ ?GetDefaultReSTIRDIInitialSamplingParams@rtxdi@@YA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetDefaultReSTIRDIShadingParams@rtxdi@@YA?AUReSTIRDI_ShadingParameters@@XZ ?GetDefaultReSTIRDISpatialResamplingParams@rtxdi@@YA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRDITemporalResamplingParams@rtxdi@@YA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetFrameIndex@ReSTIRDIContext@rtxdi@@QEBAIXZ ?GetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetResamplingMode@ReSTIRDIContext@rtxdi@@QEBA?AW4ReSTIRDI_ResamplingMode@2@XZ ?GetReservoirBufferParameters@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetRuntimeParams@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_RuntimeParameters@@XZ ?GetShadingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_ShadingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ?GetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?NumReservoirBuffers@ReSTIRDIContext@rtxdi@@2IB ?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z ?SetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_InitialSamplingParameters@@@Z ?SetResamplingMode@ReSTIRDIContext@rtxdi@@QEAAXW4ReSTIRDI_ResamplingMode@2@@Z ?SetShadingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_ShadingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z ?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z ?UpdateBufferIndices@ReSTIRDIContext@rtxdi@@AEAAXXZ ?UpdateCheckerboardField@ReSTIRDIContext@rtxdi@@AEAAXXZ ?debugCheckParameters@rtxdi@@YAXAEBUReSTIRDIStaticParameters@1@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Copy_backward_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z ??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z ??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z ??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z ??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z ??$_Guess_median_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM00U?$less@X@0@@Z ??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z ??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??0bad_array_new_length@std@@QEAA@XZ ??0exception@std@@QEAA@AEBV01@@Z ??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ ??1?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@QEAA@XZ ??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??1exception@std@@UEAA@XZ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_7exception@std@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Gexception@std@@UEAAPEAXI@Z ??_R0?AVbad_alloc@std@@@8 ??_R0?AVbad_array_new_length@std@@@8 ??_R0?AVexception@std@@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R2bad_alloc@std@@8 ??_R2bad_array_new_length@std@@8 ??_R2exception@std@@8 ??_R3bad_alloc@std@@8 ??_R3bad_array_new_length@std@@8 ??_R3exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R4bad_array_new_length@std@@6B@ ??_R4exception@std@@6B@ ?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z ?ComputeGridLightSlotCount@ReGIRContext@rtxdi@@AEAAXXZ ?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ ?GetReGIRCellOffset@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ?GetReGIRGridCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRGridCalculatedParameters@2@XZ ?GetReGIRLightSlotCount@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ ?GetReGIRStaticParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRStaticParameters@2@XZ ?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z ?IsLocalLightPowerRISEnable@ReGIRContext@rtxdi@@QEBA_NXZ ?SetDynamicParameters@ReGIRContext@rtxdi@@QEAAXAEBUReGIRDynamicParameters@2@@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ ?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ ?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ ?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ ?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z ?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z ?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z ?what@exception@std@@UEBAPEBDXZ _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 _CTA3?AVbad_array_new_length@std@@ _TI3?AVbad_array_new_length@std@@ __real@40490fdb __real@40c90fdb __real@beaaaaab ??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z ??1?$unique_ptr@VRISBufferSegmentAllocator@rtxdi@@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReGIRContext@rtxdi@@U?$default_delete@VReGIRContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRDIContext@rtxdi@@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRGIContext@rtxdi@@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@@std@@QEAA@XZ ??1ImportanceSamplingContext@rtxdi@@QEAA@XZ ??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z ?GetEnvironmentLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetLightBufferParameters@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_LightBufferParameters@@XZ ?GetLocalLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ ?GetRISBufferSegmentAllocator@ImportanceSamplingContext@rtxdi@@QEBAAEBVRISBufferSegmentAllocator@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReGIRContext@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReGIRContext@2@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRDIContext@2@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRDIContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRGIContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRGIContext@2@XZ ?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?SetLightBufferParams@ImportanceSamplingContext@rtxdi@@QEAAXAEBURTXDI_LightBufferParameters@@@Z 
/               1755678910              0       10501     `
   繲  赜  $�  �$ X� 鳃 �                                                                                                                                                               ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Copy_backward_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z ??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z ??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z ??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z ??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z ??$_Guess_median_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM00U?$less@X@0@@Z ??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z ??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z ??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z ??0RISBufferSegmentAllocator@rtxdi@@QEAA@XZ ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z ??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z ??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??0bad_array_new_length@std@@QEAA@XZ ??0exception@std@@QEAA@AEBV01@@Z ??1?$unique_ptr@VRISBufferSegmentAllocator@rtxdi@@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReGIRContext@rtxdi@@U?$default_delete@VReGIRContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRDIContext@rtxdi@@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRGIContext@rtxdi@@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ ??1?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@QEAA@XZ ??1ImportanceSamplingContext@rtxdi@@QEAA@XZ ??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??1exception@std@@UEAA@XZ ??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_7exception@std@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Gexception@std@@UEAAPEAXI@Z ??_R0?AVbad_alloc@std@@@8 ??_R0?AVbad_array_new_length@std@@@8 ??_R0?AVexception@std@@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R2bad_alloc@std@@8 ??_R2bad_array_new_length@std@@8 ??_R2exception@std@@8 ??_R3bad_alloc@std@@8 ??_R3bad_array_new_length@std@@8 ??_R3exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R4bad_array_new_length@std@@6B@ ??_R4exception@std@@6B@ ?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?ComputeGridLightSlotCount@ReGIRContext@rtxdi@@AEAAXXZ ?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ ?ComputePdfTextureSize@rtxdi@@YAXIAEAI00@Z ?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z ?GetBufferIndices@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_BufferIndices@@XZ ?GetBufferIndices@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_BufferIndices@@XZ ?GetDefaultReSTIRDIBufferIndices@rtxdi@@YA?AUReSTIRDI_BufferIndices@@XZ ?GetDefaultReSTIRDIInitialSamplingParams@rtxdi@@YA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetDefaultReSTIRDIShadingParams@rtxdi@@YA?AUReSTIRDI_ShadingParameters@@XZ ?GetDefaultReSTIRDISpatialResamplingParams@rtxdi@@YA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRDITemporalResamplingParams@rtxdi@@YA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetDefaultReSTIRGIBufferIndices@rtxdi@@YA?AUReSTIRGI_BufferIndices@@XZ ?GetDefaultReSTIRGIFinalShadingParams@rtxdi@@YA?AUReSTIRGI_FinalShadingParameters@@XZ ?GetDefaultReSTIRGISpatialResamplingParams@rtxdi@@YA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRGITemporalResamplingParams@rtxdi@@YA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?GetEnvironmentLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_FinalShadingParameters@@XZ ?GetFrameIndex@ReSTIRDIContext@rtxdi@@QEBAIXZ ?GetFrameIndex@ReSTIRGIContext@rtxdi@@QEBAIXZ ?GetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetLightBufferParameters@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_LightBufferParameters@@XZ ?GetLocalLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ ?GetRISBufferSegmentAllocator@ImportanceSamplingContext@rtxdi@@QEBAAEBVRISBufferSegmentAllocator@2@XZ ?GetReGIRCellOffset@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReGIRContext@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReGIRContext@2@XZ ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ?GetReGIRGridCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRGridCalculatedParameters@2@XZ ?GetReGIRLightSlotCount@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ ?GetReGIRStaticParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRStaticParameters@2@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRDIContext@2@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRDIContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRGIContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRGIContext@2@XZ ?GetResamplingMode@ReSTIRDIContext@rtxdi@@QEBA?AW4ReSTIRDI_ResamplingMode@2@XZ ?GetResamplingMode@ReSTIRGIContext@rtxdi@@QEBA?AW4ReSTIRGI_ResamplingMode@2@XZ ?GetReservoirBufferParameters@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetReservoirBufferParameters@ReSTIRGIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetRuntimeParams@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_RuntimeParameters@@XZ ?GetShadingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_ShadingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ?GetStaticParams@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGIStaticParameters@2@XZ ?GetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z ?IsLocalLightPowerRISEnable@ReGIRContext@rtxdi@@QEBA_NXZ ?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?JenkinsHash@rtxdi@@YAII@Z ?NumReservoirBuffers@ReSTIRDIContext@rtxdi@@2IB ?SetDynamicParameters@ReGIRContext@rtxdi@@QEAAXAEBUReGIRDynamicParameters@2@@Z ?SetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_FinalShadingParameters@@@Z ?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z ?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z ?SetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_InitialSamplingParameters@@@Z ?SetLightBufferParams@ImportanceSamplingContext@rtxdi@@QEAAXAEBURTXDI_LightBufferParameters@@@Z ?SetResamplingMode@ReSTIRDIContext@rtxdi@@QEAAXW4ReSTIRDI_ResamplingMode@2@@Z ?SetResamplingMode@ReSTIRGIContext@rtxdi@@QEAAXW4ReSTIRGI_ResamplingMode@2@@Z ?SetShadingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_ShadingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_SpatialResamplingParameters@@@Z ?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z ?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z ?UpdateBufferIndices@ReSTIRDIContext@rtxdi@@AEAAXXZ ?UpdateBufferIndices@ReSTIRGIContext@rtxdi@@AEAAXXZ ?UpdateCheckerboardField@ReSTIRDIContext@rtxdi@@AEAAXXZ ?_Throw_bad_array_new_length@std@@YAXXZ ?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ ?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ ?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ ?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z ?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z ?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z ?debugCheckParameters@rtxdi@@YAXAEBUReSTIRDIStaticParameters@1@@Z ?getTotalSizeInElements@RISBufferSegmentAllocator@rtxdi@@QEBAIXZ ?what@exception@std@@UEBAPEBDXZ _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 _CTA3?AVbad_array_new_length@std@@ _TI3?AVbad_array_new_length@std@@ __real@3e800000 __real@3f000000 __real@3f11e10d __real@3f413fa9 __real@3f800000 __real@3ff0000000000000 __real@40490fdb __real@40c90fdb __real@437a0000 __real@beaaaaab __real@bf800000 
//              1755678910              0       219       `
Rtxdi.dir\Release\RtxdiUtils.obj Rtxdi.dir\Release\RISBufferSegmentAllocator.obj Rtxdi.dir\Release\ReSTIRGI.obj Rtxdi.dir\Release\ReSTIRDI.obj Rtxdi.dir\Release\ReGIR.obj Rtxdi.dir\Release\ImportanceSamplingContext.obj 
/0              1755678910              100666  32476     `
d� 緢莡  V       .drectve        �   t               
 .debug$S        @_  h              @ B.debug$T        h   ╠              @ B.text$mn        %   e               P`.debug$S           5e  Ug         @B.text$mn        �   醙  竓          P`.debug$S        �  &i  甼         @B.text$mn          瞝  莔          P`.debug$S        |  
n  塸         @B.text$mn        B   祋               P`.debug$S        $  鱭  s      
   @B.xdata             s              @0@.pdata             泂           @0@.xdata             舠              @0@.pdata             裺  輘         @0@.xdata          (   鹲  #t         @0@.pdata             At  Mt         @0@.xdata             kt  {t         @0@.pdata             檛           @0@.rdata             胻              @0@.rdata             莟              @0@.rdata             藅              @0@.rdata             蟭              @0@.rdata             觮              @0@.rdata             譼              @@@.rdata             遲              @0@.rdata             鉻              @0@.chks64         �   鐃               
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   ^  a     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\RtxdiUtils.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $_Binary_hypot   �   #;  . �   std::integral_constant<bool,1>::value 8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable : �    std::integral_constant<unsigned __int64,0>::value ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask $ �    std::strong_ordering::equal ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong : �   std::integral_constant<unsigned __int64,2>::value . �    std::integral_constant<bool,0>::value      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  #   rsize_t  (  _TypeDescriptor % k  _s__RTTICompleteObjectLocator2 A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType  =  rtxdi::CheckerboardMode  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16  �  std::_Lockit    std::_Num_base # �  std::numeric_limits<char8_t>  �  std::hash<float>    std::_Num_int_base    std::float_denorm_style     std::_Compare_t " A  std::numeric_limits<double> ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short>    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t>     std::_Leave_proxy_unbound  �  std::_Iterator_base12  �  std::hash<long double>   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double>  
  std::bad_exception  �  std::_Fake_allocator ! ?  std::numeric_limits<float>  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits ! ,  std::numeric_limits<short>     std::bad_alloc # 2  std::numeric_limits<__int64>  C  std::memory_order   6  std::bad_array_new_length  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int>   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::_Compare_eq    std::nullptr_t ) 8  std::numeric_limits<unsigned long> ' "  std::numeric_limits<signed char>  �  std::_Literal_zero      std::numeric_limits<char>  �  std::_Unused_parameter ! �  std::ranges::_Set_union_fn # A  std::ranges::_Unique_copy_fn '   std::ranges::_Replace_copy_if_fn & �  std::ranges::_Is_partitioned_fn ( q  std::ranges::_Stable_partition_fn !   std::ranges::_Is_sorted_fn # G  std::ranges::_Find_if_not_fn    std::ranges::_Clamp_fn % �  std::ranges::_Is_heap_until_fn ' �  std::ranges::_Partition_point_fn ( 
  std::ranges::_Prev_permutation_fn  �  std::ranges::_All_of_fn "   std::ranges::_Generate_n_fn / %  std::ranges::_Lexicographical_compare_fn  e  std::ranges::_Shuffle_fn ! �  std::ranges::_Make_heap_fn '   std::ranges::_Is_sorted_until_fn   �  std::ranges::_Count_if_fn  G  std::ranges::_Reverse_fn    std::ranges::_Minmax_fn & �  std::ranges::_Minmax_element_fn  �  std::ranges::_Sort_fn # Y  std::ranges::_Rotate_copy_fn # /  std::ranges::_Remove_copy_fn # �  std::ranges::_Nth_element_fn   �  std::ranges::_Search_n_fn   �  std::ranges::_Find_end_fn  #  std::ranges::_Remove_fn  ;  std::ranges::_Find_fn & 5  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  �  std::ranges::_Equal_fn ! �  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! )  std::ranges::_Remove_if_fn   u  std::ranges::_For_each_fn   }  std::ranges::_Pop_heap_fn & �  std::ranges::_Set_difference_fn ) �  std::ranges::_Partial_sort_copy_fn  �  std::ranges::_Is_heap_fn ! w  std::ranges::_Push_heap_fn ! k  std::ranges::_Partition_fn % M  std::ranges::_Adjacent_find_fn $ �  std::ranges::_Partial_sort_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn % �  std::ranges::_Binary_search_fn " {  std::ranges::_For_each_n_fn & �  std::ranges::_Partition_copy_fn  �  std::ranges::_Copy_n_fn $ M  std::ranges::_Reverse_copy_fn # �  std::ranges::_Equal_range_fn  �  std::ranges::_Move_fn $   std::ranges::_Replace_copy_fn     std::ranges::_Generate_fn   5  std::ranges::_Mismatch_fn   �  std::ranges::_Includes_fn  �  std::ranges::_Count_fn  _  std::ranges::_Sample_fn  �  std::ranges::_Merge_fn # �  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �  std::ranges::_Move_backward_fn  k  std::ranges::_Min_fn  �  std::ranges::_Copy_if_fn " �  std::ranges::_Replace_if_fn & �  std::ranges::_Is_permutation_fn  )  std::ranges::_Copy_fn  �  std::ranges::_Replace_fn    std::ranges::_Fill_fn ( �  std::ranges::_Set_intersection_fn % �  std::ranges::_Inplace_merge_fn 0 �  std::ranges::_Set_symmetric_difference_fn  #  std::ranges::dangling % �  std::ranges::_Copy_backward_fn  S  std::ranges::_Search_fn    std::ranges::_Prev_fn # �  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn (   std::ranges::_Next_permutation_fn # �  std::ranges::_Lower_bound_fn  ;  std::ranges::_Unique_fn  �  std::ranges::_None_of_fn    std::ranges::_Advance_fn  �  std::ranges::_Any_of_fn % �  std::ranges::_Find_first_of_fn ! �  std::ranges::_Transform_fn # �  std::ranges::_Stable_sort_fn  S  std::ranges::_Rotate_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn " �  std::_Asan_aligned_pointers  �  std::partial_ordering  .  std::numeric_limits<int>  �  std::bad_variant_access   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat 
 !   _ino_t M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *>  9  terminate_handler  �  _s__RTTIBaseClassArray 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray  "  _stat64i32  �  _PMD      uint8_t ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 % |  __RTTIClassHierarchyDescriptor & G  RTXDI_ReservoirBufferParameters 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  �  _ldiv_t  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers  �   0      J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  M    逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �    +YE擋%1r+套捑@鸋MT61' p廝 飨�  �    吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱     檒Gq$�#嗲RR�錨账��K諻刮g�   F   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �   交�,�;+愱`�3p炛秓ee td�	^,  �   _臒~I��歌�0蘏嘺QU5<蝪祰S     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  p   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  2   _O縋[HU-銌�鼪根�鲋薺篮�j��  {   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  "   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  k   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  &   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  d   繃S,;fi@`騂廩k叉c.2狇x佚�  �   2鶳eL{*2f;�+�<愰R�讝
��  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     �(M↙溋�
q�2,緀!蝺屦碄F觡  g   G�膢刉^O郀�/耦��萁n!鮋W VS  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  ?   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  )   �"睱建Bi圀対隤v��cB�'窘�n  {   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  	   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  k	   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �	   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �	   �0�*е彗9釗獳+U叅[4椪 P"��  2
   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  r
   �=蔑藏鄌�
艼�(YWg懀猊	*)  �
   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �
   匐衏�$=�"�3�a旬SY�
乢�骣�  <   齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  o   悯R痱v 瓩愿碀"禰J5�>xF痧  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   矨�陘�2{WV�y紥*f�u龘��  C   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�  
    I嘛襨签.濟;剕��7啧�)煇9触�.  K
   �
bH<j峪w�/&d[荨?躹耯=�  �
   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �
   a�傌�抣?�g]}拃洘銌刬H-髛&╟     双:Pj �>[�.ぷ�<齠cUt5'蠙砥  O   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  K   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�     蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  c   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  A   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�     v�%啧4壽/�.A腔$矜!洎\,Jr敎  O   5�\營	6}朖晧�-w氌rJ籠騳榈  �   D���0�郋鬔G5啚髡J竆)俻w��  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  (   	{Z�范�F�m猉	痹缠!囃ZtK�T�  g   蜅�萷l�/费�	廵崹
T,W�&連芿  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  "   c�#�'�縌殹龇D兺f�$x�;]糺z�  u   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  
   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   X   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Rtxdi\Source\RtxdiUtils.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h �       L�     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb E吷t�卵陜�A岪凌陵菱�堿H嬃�   �   �  O G            %       $   $        �rtxdi::CalculateReservoirBufferParameters  >u    renderWidth  A             >u    renderHeight  Ah        %  >=   checkerboardMode  Ai        %  >u     renderWidthBlocks  A          >u     renderHeightBlocks  A        	                         @     u   OrenderWidth     u   OrenderHeight      =  OcheckerboardMode         Oparams  O   �   X           %   H     L         �      �	     �     �     �     �!     �$     �,       0      
 {             
 �       �      
 �       �      
 �            
 *      .     
 �      �     
 H塡$H塼$WH冹P)t$@W缷罥嬞)|$0I孁D)D$ H嬺EW莉L*纅A.纖fAQ离	A(黎    �    �=    �_氰    �    �    駾^�(餉(黎    �_氰    �    �    騂,茐騂,莉_茐�    H媡$h�X�(t$@D(D$ �_�(|$0騂,缐H媆$`H兡P_肐        N       V   L    _       d       i       z       �       �       �       �          �     B G            �   *   �   �        �rtxdi::ComputePdfTextureSize  >u    maxItems  A         M  A  M       >�   outWidth  AK        -  AL  -     �  >�   outHeight  AM  $     �  AP        $  >�   outMipLevels  AI       �  AQ          >A     textureHeight  A�   �       >A     textureWidth  A�   m       A�   u     A  Z   �  �   P                     @ 
 h   �   `   u   OmaxItems  h   �  OoutWidth  p   �  OoutHeight  x   �  OoutMipLevels  O �   `           �   H  	   T       !  �   #  �^   $  �m   %  ��   &  ��   '  ��   )  ��   *  ��   ,  �,       0      
 k       o      
 {             
 �       �      
 �       �      
 �       �      
 �       �      
 	      
     
            
 A      E     
 h      l     
 x      |     
 (      ,     
 ��  H冹xD)D$@L嬌驞    E3�)t$`A(伢5    A(�)|$P�=    D)L$0驞
    D)T$ 驞    D)\$驞    D)$$驞%    f愺AX袤AX�/農�X�/謗�X�(�(泱A\梵A\�(�(腆Y朋Y腆X華/蘷 驛Y泱AY塍,藽�A�荔,虲�A�繢;聄桪($$D(\$D(T$ D(L$0(|$P(t$`D(D$@H兡x�   @    .   I    ?   R    N   F    ]   C    l   O    z   =       �   �  E G                   �        �rtxdi::FillNeighborOffsetBuffer  >    buffer  AJ          AQ       �  AJ       AQ       >u    neighborOffsetCount  A           >u     num  Ah  !     �  Ah      
 >@     u  A�   *     �  A�       
 >@     v  A�   6     �  A�        >@     rSq  A�   �     V  A�  �     � 2 b  x                      @  �      Obuffer   �   u   OneighborOffsetCount  O �   �             H     �       /  �    8  �   /  �   6  �!   8  ��   9  ��   :  ��   ;  ��   <  ��   >  ��   ?  ��   B  ��   C  ��   8  �
  E  �,       0      
 l       p      
 |       �      
 �       �      
 �       �      
 �       �      
 �       �      
 �       �      
            
 $      (     
 @      D     
 P      T     
 n      r     
 ~      �     
 �      �     
 i�  ]諂嬋灵3葋�<耡莐�!伮眊V崅ld⒂菱	3聧排Fp�葖亮�3�5	OZ得   �   �   8 G            B       A   #        �rtxdi::JenkinsHash 
 >u    a  A        <   - 	  A         <      A                                 @     u   Oa  O�   X           B   H     L       H  �    J  �   K  �   L  �!   M  �,   N  �5   O  �A   Q  �,       0      
 Z       ^      
 r       v      
 �       �      
 �       �      
 * *� !x h d
 4 �p    �           !       !       %     � �      !           "       "       +    !T T�  F� 7� (� x h     !          "        "    $   +    !             "       "       1    !       !          "       "       +                "       "       7      �>   ?
�?�?A?  �?      �?  zC  �苦霞-摪C完S鍷�0蚳鈊&连0稐h袮a�i�
y猰AGI�6j郀@額�-訲'u綴轙
奭G
�v繎�梡颺軜�Ga中5 靫V饧rb�3�&惢货c8曀黩6O谞5馩錿8髭�喟x睟樢瞿1J侱剈釹�	y~贋XCRC冼bA鵕�"��!堗^笵A傮l<9n值胢y
�,4Ik�0牊        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       �                 .debug$S       @_                .debug$T       h                 .text$mn       %       H飻�     .debug$S                     .text$mn       �      621�     .debug$S       �             .text$mn            矗\�     .debug$S    	   |             .text$mn    
   B       ◣貵     .debug$S       $  
       
                       o               �               �       
    exp2             ceil             log2             sqrt             $LN16           $LN19           .xdata                鳱5�        �           .pdata      
         �              
    .xdata                裴Nx        F          .pdata               萣�5        z          .xdata         (      bNz�        �          .pdata               颍�        �          .xdata               $垕�                  .pdata               �=糪        L          .rdata                鄥恸         �          .rdata                =-f�         �          .rdata                gl蹩         �          .rdata                [@         �          .rdata                v靛�         �          .rdata                �腾�         �          .rdata                V�:         �          .rdata                V6]`         �          _fltused         .chks64        �                 	  ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?ComputePdfTextureSize@rtxdi@@YAXIAEAI00@Z ?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z ?JenkinsHash@rtxdi@@YAII@Z $unwind$?ComputePdfTextureSize@rtxdi@@YAXIAEAI00@Z $pdata$?ComputePdfTextureSize@rtxdi@@YAXIAEAI00@Z $unwind$?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z $pdata$?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z $chain$5$?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z $pdata$5$?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z $chain$6$?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z $pdata$6$?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z __real@3e800000 __real@3f000000 __real@3f11e10d __real@3f413fa9 __real@3f800000 __real@3ff0000000000000 __real@437a0000 __real@bf800000 /33             1755678910              100666  3599      `
d�
 緢�         .drectve        /   �               
 .debug$S        �  �              @ B.debug$T        h   �              @ B.text$mn        
   /               P`.debug$S        �   9  5         @B.text$mn           q               P`.debug$S        @  x  �	         @B.text$mn           
               P`.debug$S        �   
           @B.chks64         P   ?               
     /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   p     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\RISBufferSegmentAllocator.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std �   :   ' t  rtxdi::RISBufferSegmentAllocator  u   uint32_t   �   h      ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  R    檒Gq$�#嗲RR�錨账��K諻刮g�   �    �m倐磟� `悇髬�!_囉vUqx;�)螗护  �    泽閇�R鯄呙+困胢p=�R刐鉍籫�8[     )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  R   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   �*o驑瓂a�(施眗9歐湬

�      I嘛襨签.濟;剕��7啧�)煇9触�.  �   Z   D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\Source\RISBufferSegmentAllocator.cpp D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h   �       L9     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb �    H嬃�   �   �   a G            
       	   5        �rtxdi::RISBufferSegmentAllocator::RISBufferSegmentAllocator 
 >m   this  AJ        
                         @     m  Othis  O �   0           
   P      $         �      �     �,       0      
 �       �      
 �       �      
 �袎�   �   �   W G                      6        �rtxdi::RISBufferSegmentAllocator::allocateSegment 
 >m   this  AJ          >u    sizeInElements  A                                  @     m  Othis     u   OsizeInElements  O �   8              P      ,         �      �     �     �,       0      
 |       �      
 �       �      
            
 ��   �   �   ^ G                      7        �rtxdi::RISBufferSegmentAllocator::getTotalSizeInElements 
 >q   this  AJ                                 @     q  Othis  O�   0              P      $         �      �      �,       0      
 �       �      
 �       �      
 #f'�r蘱舆榓蚳鈊&连0&�&�� j廋岘葴[骈鳶Wv�*Yq��q刯�� MI矱慏�8瘡律        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       /                 .debug$S       �                .debug$T       h                 .text$mn       
       -餞�     .debug$S       �              .text$mn              �>闊     .debug$S       @             .text$mn              *V�     .debug$S    	   �                                 0               k           .chks64     
   P                 �   ??0RISBufferSegmentAllocator@rtxdi@@QEAA@XZ ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?getTotalSizeInElements@RISBufferSegmentAllocator@rtxdi@@QEBAIXZ 
/81             1755678910              100666  17034     `
d�0 緢�0  �       .drectve        /   �               
 .debug$S          �              @ B.debug$T        h   �              @ B.text$mn        .  G  u          P`.debug$S            �      
   @B.text$mn           �               P`.debug$S        �   �  �         @B.text$mn                          P`.debug$S        �   5  %         @B.text$mn           M               P`.debug$S        �   f  >         @B.text$mn        /   f               P`.debug$S        �   �  q         @B.text$mn        G   �               P`.debug$S        �   �  �         @B.text$mn           �               P`.debug$S        �   �  �         @B.text$mn           *               P`.debug$S        �   .           @B.text$mn           R               P`.debug$S        �   V  B         @B.text$mn           ~               P`.debug$S        �   �  }         @B.text$mn           �               P`.debug$S        �   �  �          @B.text$mn           !               P`.debug$S        �   !  �!         @B.text$mn           9"               P`.debug$S        �   T"  L#         @B.text$mn           �#               P`.debug$S        @  �#  �$         @B.text$mn        #   #%  F%          P`.debug$S        \  Z%  �&      
   @B.text$mn           '  "'          P`.debug$S        <  ,'  h(         @B.text$mn           �(               P`.debug$S        P  �(  *         @B.text$mn        1   k*  �*          P`.debug$S        x  �*  ,      
   @B.text$mn        v   �,               P`.debug$S        l  �,  d.         @B.xdata             �.              @0@.pdata             �.  �.         @0@.xdata             �.              @0@.pdata             �.  /         @0@.xdata              /              @0@.pdata             (/  4/         @0@.chks64         �  R/               
     /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   �   _     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReSTIRGI.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  �   �  - L   rtxdi::c_NumReSTIRGIReservoirBuffers + 
  ReSTIRGI_SpatialResamplingParameters & A  rtxdi::ReSTIRGIStaticParameters  8  rtxdi::ReSTIRGIContext %    rtxdi::ReSTIRGI_ResamplingMode &   ReSTIRGI_FinalShadingParameters ) 	  ResTIRGI_SpatialBiasCorrectionMode *   ResTIRGI_TemporalBiasCorrectionMode & G  RTXDI_ReservoirBufferParameters ,   ReSTIRGI_TemporalResamplingParameters    ReSTIRGI_BufferIndices 
 #   size_t  u   uint32_t   �         齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  4    逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  l    檒Gq$�#嗲RR�錨账��K諻刮g�   �    ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �    蜅駠x馘Qf^��=夸餕V�G窄憫尢25  .   躅生.�=�;V宖A�kZ@lxV_�  Z   �	R\�5甕:7峡铻崑p!騎P与�3�%�;  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1     蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  N   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   V8追i顚�^�k细�;>牧惺扴	�\s  �   4         �        �        �   +     �   6   �      D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\RTXPT\External\Rtxdi\Source\ReSTIRGI.cpp D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h �       L2     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb H塡$H墊$UH嬱H冹P�3��H嬞婤堿墆H峂蠨婮D婤��     H墋豀嬅H墋蠬墋�C墋�E袎}貕}�M嗲E�   C$荅型烫=荅詺�?E星E柰蘈>K4荅�   CD荅�   荅�   M嗲E�   荅型烫=KT荅詺�?荅�   BM蠬墋魤}麳墋鋲}�KtH墋貕{ H媩$h荅�   E鹎E�   荅�   Cd荅�   E�儎   E�償   H媆$`H兡P]�7   U       �   �  M G            .     #          �rtxdi::ReSTIRGIContext::ReSTIRGIContext 
 >   this  AI       	 AJ          >   staticParams  AK        6  M          �� N) M          ��+" N< M          X
	+/ N" M          >

 N
 Z   $   P                     @  h            `     Othis  h     OstaticParams  O  �             .  �             F  �   >  �   ?  �(   @  �>   B  �B   G  �E   B  �M   @  �Q   B  �X   C  �[   B  �b   C  �i   B  �m   C  ��   B  ��   C  ��   D  ��   C  ��   D  ��   C  ��   D  ��   E  ��   A  ��   G  ��   C  ��   D  ��   E  �  C  �  E  �
  D  �  E  �#  G  �,   [    0   [   
 r   [    v   [   
 �   [    �   [   
 �   [    �   [   
 �  [    �  [   
 A$H嬄I4J�   �   �   N G                              �rtxdi::ReSTIRGIContext::GetBufferIndices 
 >   this  AJ                                 @       Othis  O�   0              �      $       ^  �    _  �   `  �,   `    0   `   
 s   `    w   `   
 �   `    �   `   
 3繦堿堿H�H堿堿H嬃�   �   �   L G                              �rtxdi::GetDefaultReSTIRGIBufferIndices                         H         ObufferIndices  O�   P              �      D         �      �	     �     �     �     �     �,   W    0   W   
 �   W    �   W   
 H茿    H嬃茿   �   �   �   �   Q G                              �rtxdi::GetDefaultReSTIRGIFinalShadingParams                         H         Oparams  O  �   8              �      ,       6  �    7  �   :  �   ;  �,   Z    0   Z   
 �   Z    �   Z   
 3繦堿堿H嬃茿   茿   �吞�=茿殭?茿   B�   �   �   V G            /       .           �rtxdi::GetDefaultReSTIRGISpatialResamplingParams                         H         Oparams  O �   8           /   �      ,       +  �    ,  �	   2  �.   3  �,   Y    0   Y   
 �   Y    �   Y   
 3繦堿$堿,堿H嬃茿吞L>�吞�=茿   茿   茿   茿   茿殭?茿    �   �   �   W G            G       F           �rtxdi::GetDefaultReSTIRGITemporalResamplingParams                         H         Oparams  O�   @           G   �      4         �      �	   "  �   '  �F   (  �,   X    0   X   
 �   X    �   X   
 仈   H嬄�   �   �   W G                   
           �rtxdi::ReSTIRGIContext::GetFinalShadingParameters 
 >   this  AJ                                 @       Othis  O   �   0              �      $       m  �    n  �
   o  �,   c    0   c   
 |   c    �   c   
 �   c    �   c   
 婣�   �   �   K G                              �rtxdi::ReSTIRGIContext::GetFrameIndex 
 >   this  AJ                                 @       Othis  O   �   0              �      $       O  �    P  �   Q  �,   ]    0   ]   
 p   ]    t   ]   
 �   ]    �   ]   
 婣 �   �   �   O G                              �rtxdi::ReSTIRGIContext::GetResamplingMode 
 >   this  AJ                                 @       Othis  O   �   0              �      $       Y  �    Z  �   [  �,   _    0   _   
 t   _    x   _   
 �   _    �   _   
 AH嬄�   �   �   Z G                   
           �rtxdi::ReSTIRGIContext::GetReservoirBufferParameters 
 >   this  AJ                                 @       Othis  O�   0              �      $       T  �    U  �
   V  �,   ^    0   ^   
    ^    �   ^   
 �   ^    �   ^   
 AtH嬄墑   J�   �   �   \ G                              �rtxdi::ReSTIRGIContext::GetSpatialResamplingParameters 
 >   this  AJ                                 @       Othis  O  �   0              �      $       h  �    i  �   j  �,   b    0   b   
 �   b    �   b   
 �   b    �   b   
 婣��塀H嬄�   �   �   M G                              �rtxdi::ReSTIRGIContext::GetStaticParams 
 >   this  AJ                                 @       Othis  O �   0              �      $       J  �    K  �   L  �,   \    0   \   
 r   \    v   \   
 �   \    �   \   
 ADH嬄ITAdJB �   �   �   ] G                              �rtxdi::ReSTIRGIContext::GetTemporalResamplingParameters 
 >   this  AJ                                 @       Othis  O �   0              �      $       c  �    d  �   e  �,   a    0   a   
 �   a    �   a   
 �   a    �   a   
 仈   �   �   �   W G                   
   !        �rtxdi::ReSTIRGIContext::SetFinalShadingParameters 
 >   this  AJ          >3   finalShadingParams  AK                                 @       Othis     3  OfinalShadingParams  O �   0              �      $       �  �    �  �
   �  �,   h    0   h   
 |   h    �   h   
 �   h    �   h   
   h      h   
 @SH冹 H嬞塓嬍�    H嬎塁hH兡 [�       V       i       �   �   K G            #                 �rtxdi::ReSTIRGIContext::SetFrameIndex 
 >   this  AI  	       AJ        	  >u    frameIndex  A           Z   #  "                         @  0     Othis  8   u   OframeIndex  O �   H           #   �      <       r  �	   s  �   t  �   u  �   v  �   u  �,   d    0   d   
 p   d    t   d   
 �   d    �   d   
 �   d    �   d   
   d      d   
 塓 �       i       �   �   O G                              �rtxdi::ReSTIRGIContext::SetResamplingMode 
 >   this  AJ          >    resamplingMode  A          
 Z   "                          @       Othis        OresamplingMode  O �   0              �      $       y  �    z  �   {  �,   e    0   e   
 t   e    x   e   
 �   e    �   e   
   e      e   
 AtJ墑   �   �   
  \ G                               �rtxdi::ReSTIRGIContext::SetSpatialResamplingParameters 
 >   this  AJ           >/   spatialResamplingParams  AK                                 @       Othis $    /  OspatialResamplingParams  O  �   0              �      $       �  �    �  �   �  �,   g    0   g   
 �   g    �   g   
 �   g    �   g   
    g    $  g   
 @SH冹 H嬞ADJITB Ad婭�    塁hH兡 [�$   V       �   )  ] G            1      +           �rtxdi::ReSTIRGIContext::SetTemporalResamplingParameters 
 >   this  AI       $  AJ         ! >+   temporalResamplingParams  AK        ( 
 Z   #                         @  0     Othis % 8   +  OtemporalResamplingParams  O   �   8           1   �      ,         �   �  �    �  �+   �  �,   f    0   f   
 �   f    �   f   
 �   f    �   f   
 �   f    �   f   
 @  f    D  f   
 H嬔婭 吷tc冮tF冮t*冮t凒uW婮冡嬃塉$凁塉4塀(塉8肏荁(   3狼B4   塀0塀$荁8   脣J冡嬃塉$凁塉,塀(塉8�3缐B8塀$�   �   �   Q G            v       u   "        �rtxdi::ReSTIRGIContext::UpdateBufferIndices 
 >   this  AJ          AK       s                         @       Othis  O �   �           v   �      �       �  �   �  �   �  �$   �  �,   �  �2   �  �5   �  �6   �  �>   �  �U   �  �[   �  �c   �  �i   �  �l   �  �m   �  �o   �  �r   �  �,   i    0   i   
 v   i    z   i   
 �   i    �   i   
 �   i    �   i   
  t
 4 �P    .          j       j       o     20    #           k       k       u     20    1           l       l       {    #f'�欮嬓$吠h鈊&连0�蛢锿l畬u)W*o諱佮@焵ゼ>七�jR�
S�韌斏]a蒔0\�\\8�=/i秃亄>嬃竆癖�.e擁�K拚痻页Oｑ/繺�2廗构芤3e
:[C>�,.3溰]�7靈磝j�6�:�:P甭q噜 G6�%嗸i+#Z$,铸�"l n0I压袢跪屰,婟��譏(穽睶S��丳勠)扃 �U+軃儆炜�郾觢_.つ&xnq��
8}�0Cj�!A怾!rAb軤驲>媐秋8镆 肛xn�褒�}傐% 嵱掑$髍kba�Mi[蓓E匶昹�'[縧�,	雵J-WV8oti觧vmGc雵J-WV8o佗勫r|        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       /                 .debug$S                       .debug$T       h                 .text$mn       .     魶诬     .debug$S         
           .text$mn              ,軻     .debug$S       �              .text$mn              >嵽/     .debug$S    	   �              .text$mn    
          W�)�     .debug$S       �          
    .text$mn       /       �'�=     .debug$S    
   �              .text$mn       G       !邸     .debug$S       �              .text$mn              R�     .debug$S       �              .text$mn              �
劐     .debug$S       �              .text$mn              ea檐     .debug$S       �              .text$mn              f炰     .debug$S       �              .text$mn              駥瑌     .debug$S       �              .text$mn              OX�     .debug$S       �              .text$mn              ?綾     .debug$S       �              .text$mn              �
.q     .debug$S       @             .text$mn        #      �+暟     .debug$S    !   \  
            .text$mn    "         j芫�     .debug$S    #   <         "    .text$mn    $          %矎�     .debug$S    %   P         $    .text$mn    &   1      駛T     .debug$S    '   x  
       &    .text$mn    (   v       *>t�     .debug$S    )   l         (                        o                �               �               4              �      
        �              +              x              �                            U              �              	              p              �               �      "        J      &        �      $                      }      (    $LN12           $LN4             $LN4        &    .xdata      *          �! x        �      *    .pdata      +         衶婮        �      +    .xdata      ,          （亵         B      ,    .pdata      -         礶鵺         y      -    .xdata      .          （亵&        �      .    .pdata      /         鉙gI&        "      /    _fltused         .chks64     0   �                �  ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?JenkinsHash@rtxdi@@YAII@Z ?GetDefaultReSTIRGIBufferIndices@rtxdi@@YA?AUReSTIRGI_BufferIndices@@XZ ?GetDefaultReSTIRGITemporalResamplingParams@rtxdi@@YA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?GetDefaultReSTIRGISpatialResamplingParams@rtxdi@@YA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRGIFinalShadingParams@rtxdi@@YA?AUReSTIRGI_FinalShadingParameters@@XZ ??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z ?GetStaticParams@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGIStaticParameters@2@XZ ?GetFrameIndex@ReSTIRGIContext@rtxdi@@QEBAIXZ ?GetReservoirBufferParameters@ReSTIRGIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetResamplingMode@ReSTIRGIContext@rtxdi@@QEBA?AW4ReSTIRGI_ResamplingMode@2@XZ ?GetBufferIndices@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_BufferIndices@@XZ ?GetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_TemporalResamplingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_SpatialResamplingParameters@@XZ ?GetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEBA?AUReSTIRGI_FinalShadingParameters@@XZ ?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z ?SetResamplingMode@ReSTIRGIContext@rtxdi@@QEAAXW4ReSTIRGI_ResamplingMode@2@@Z ?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_SpatialResamplingParameters@@@Z ?SetFinalShadingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_FinalShadingParameters@@@Z ?UpdateBufferIndices@ReSTIRGIContext@rtxdi@@AEAAXXZ $unwind$??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z $pdata$??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z $unwind$?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z $pdata$?SetFrameIndex@ReSTIRGIContext@rtxdi@@QEAAXI@Z $unwind$?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z $pdata$?SetTemporalResamplingParameters@ReSTIRGIContext@rtxdi@@QEAAXAEBUReSTIRGI_TemporalResamplingParameters@@@Z /112            1755678910              100666  46897     `
d�? 緢菬  �       .drectve        <  �	               
 .debug$S        爂  (  萺         @ B.debug$T        h   躵              @ B.rdata             Ds              @ 0@.text$mn        9  Hs  乽          P`.debug$S        �  媢  #y         @B.text$mn           脃               P`.debug$S        �   謞  緕         @B.text$mn           鷝               P`.debug$S        �   {  �{         @B.text$mn        5   '|               P`.debug$S        �   \|  8}         @B.text$mn        )   `}               P`.debug$S        �   墋  ]~         @B.text$mn        5   厏               P`.debug$S        �   簙  �         @B.text$mn        E   �               P`.debug$S        �   �  還         @B.text$mn           �               P`.debug$S        �   �  髞         @B.text$mn           /�               P`.debug$S        �   B�  6�         @B.text$mn           r�               P`.debug$S        �   v�  b�         @B.text$mn           瀯               P`.debug$S        �   ﹦  潊         @B.text$mn           賲               P`.debug$S        �   鋮  虇         @B.text$mn           �               P`.debug$S        �   !�  
�         @B.text$mn           I�               P`.debug$S        �   b�  Z�         @B.text$mn           枆               P`.debug$S        �   泬  噴         @B.text$mn        $   脢               P`.debug$S        �   鐘  邒         @B.text$mn        g   �  倢          P`.debug$S        �  枌  :�      
   @B.text$mn           瀻               P`.debug$S        H  畮  鰪         @B.text$mn           F�  N�          P`.debug$S        <  X�  攽         @B.text$mn           鋺               P`.debug$S        0  鷳  *�         @B.text$mn        2   z�               P`.debug$S        �  瑩  H�      
   @B.text$mn        =   瑫  闀          P`.debug$S        x  髸  k�      
   @B.text$mn        �   蠗               P`.debug$S        |  v�  驒         @B.text$mn        2   j�               P`.debug$S          湚           @B.text$mn           鄾               P`.debug$S        �   銢  脺         @B.xdata             ��              @0@.pdata             �  �         @0@.xdata             9�              @0@.pdata             A�  M�         @0@.xdata             k�              @0@.pdata             s�  �         @0@.xdata             潩              @0@.pdata               睗         @0@.chks64         �  蠞               
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   R  _     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReSTIRDI.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data   �   <  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable ? �   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ �    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E �   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask - L   rtxdi::c_NumReSTIRDIReservoirBuffers 8 
L        rtxdi::ReSTIRDIContext::NumReservoirBuffers . �    std::integral_constant<bool,0>::value . �   std::integral_constant<bool,1>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 4 �  @ _Mtx_internal_imp_t::_Critical_section_size 5 �   _Mtx_internal_imp_t::_Critical_section_align 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 + �    std::_Aligned_storage<64,8>::_Fits 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits % \    _Atomic_memory_order_relaxed % \   _Atomic_memory_order_consume % \   _Atomic_memory_order_acquire % \   _Atomic_memory_order_release % \   _Atomic_memory_order_acq_rel % \   _Atomic_memory_order_seq_cst $ �    std::strong_ordering::equal : �    std::integral_constant<unsigned __int64,0>::value ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy : �   std::integral_constant<unsigned __int64,2>::value / �   std::atomic<long>::is_always_lock_free : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ; �   std::atomic<unsigned __int64>::is_always_lock_free  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> * �  ReSTIRDI_TemporalBiasCorrectionMode  .  ReSTIRDI_BufferIndices &   $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t  (  _TypeDescriptor 	 )  tm % k  _s__RTTICompleteObjectLocator2 A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType & L  rtxdi::ReSTIRDIStaticParameters  =  rtxdi::CheckerboardMode % �  rtxdi::ReSTIRDI_ResamplingMode  �  rtxdi::ReSTIRDIContext & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16  �  std::_Lockit  "   std::_Atomic_counter_t    std::_Num_base # �  std::numeric_limits<char8_t>  �  std::hash<float>    std::_Num_int_base    std::float_denorm_style  q  std::bad_cast     std::_Compare_t " A  std::numeric_limits<double>  �  std::__non_rtti_object ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  \  std::pointer_safety  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short>   |  std::pmr::memory_resource    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t>     std::_Leave_proxy_unbound  �  std::_Iterator_base12 $ �  std::_Atomic_integral<long,4>  �  std::hash<long double>   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double>  
  std::bad_exception  �  std::_Fake_allocator ! ?  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long>  T  std::_Ref_count_base  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits ! ,  std::numeric_limits<short> ! c  std::_Shared_ptr_spin_lock     std::bad_alloc # 2  std::numeric_limits<__int64>  C  std::memory_order # �  std::_Atomic_storage<long,4>  l  std::atomic_flag   6  std::bad_array_new_length  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int>  �  std::atomic<long>   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::bad_typeid  �  std::_Compare_eq    std::nullptr_t  <  std::bad_weak_ptr ) 8  std::numeric_limits<unsigned long>   K  std::_Atomic_padded<long> ' "  std::numeric_limits<signed char>  �  std::_Literal_zero      std::numeric_limits<char>  �  std::_Unused_parameter * �  std::ranges::_Uninitialized_fill_fn 7 &  std::ranges::_Uninitialized_value_construct_n_fn # G  std::ranges::_Find_if_not_fn , �  std::ranges::_Uninitialized_move_n_fn !   std::ranges::_Destroy_n_fn $ �  std::ranges::_Construct_at_fn "   std::ranges::_Destroy_at_fn  ;  std::ranges::_Find_fn ! �  std::ranges::subrange_kind    std::ranges::_Next_fn % M  std::ranges::_Adjacent_find_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn 7   std::ranges::_Uninitialized_default_construct_fn * �  std::ranges::_Uninitialized_move_fn , �  std::ranges::_Uninitialized_copy_n_fn   5  std::ranges::_Mismatch_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag  k  std::ranges::_Min_fn  )  std::ranges::_Copy_fn * �  std::ranges::_Uninitialized_copy_fn    std::ranges::_Destroy_fn , �  std::ranges::_Uninitialized_fill_n_fn  #  std::ranges::dangling  S  std::ranges::_Search_fn    std::ranges::_Prev_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn    std::ranges::_Advance_fn 5    std::ranges::_Uninitialized_value_construct_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn 9   std::ranges::_Uninitialized_default_construct_n_fn " �  std::_Asan_aligned_pointers  �  std::partial_ordering  .  std::numeric_limits<int>  �  std::bad_variant_access ) �  ReSTIRDI_SpatialBiasCorrectionMode   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat  8  timespec 
 !   _ino_t , �  ReSTIRDI_TemporalResamplingParameters M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> + �  ReSTIRDI_SpatialResamplingParameters  9  terminate_handler  �  _s__RTTIBaseClassArray 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24 ) �  ReSTIRDI_InitialSamplingParameters  �  _CatchableTypeArray  "  _stat64i32  �  _PMD  >  type_info ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t  U  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 ! �  ReSTIRDI_ShadingParameters % |  __RTTIClassHierarchyDescriptor & G  RTXDI_ReservoirBufferParameters 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray & �  ReSTIRDI_LocalLightSamplingMode 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  r  RTXDI_RuntimeParameters  �  _ldiv_t  9  _timespec64  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers  �   �      ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  Q    �"睱建Bi圀対隤v��cB�'窘�n  �    f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �    桅棙�萑�3�<)-~浰-�?>撎�6=Y}  ,   *u\{┞稦�3壅阱\繺ěk�6U�  j   �%逽|犟�1剝%sh鵺K媡簂蹶#楎`{w  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  0   �=蔑藏鄌�
艼�(YWg懀猊	*)  q   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  D   匐衏�$=�"�3�a旬SY�
乢�骣�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u     攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  W   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   鹴y�	宯N卮洗袾uG6E灊搠d�  ;   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     �*M�现.凿萰閱寴诃缶鲍6�#�+�4  U   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  �   K.+6螶�娷滕L昋�=Gn瑺�   �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg     傊P棼r铞
w爉筫y;H+(皈LL��7縮  R   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥     溶�$椉�
悇� 騐`菚y�0O腖悘T  j   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  	   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  R	   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �	   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �	   �0�*е彗9釗獳+U叅[4椪 P"��  &
   齝D屜u�偫[篔聤>橷�6酀嘧0稈  d
   齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  �
   _O縋[HU-銌�鼪根�鲋薺篮�j��  �
   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(     l籴靈LN~噾2u�< 嵓9z0iv&jザ  j   檒Gq$�#嗲RR�錨账��K諻刮g�   �   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  -   副謐�斦=犻媨铩0
龉�3曃譹5D   o   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   �疀�4�A圏,oHB瓳HJ��2�0(v/  �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  !
   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  _
   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �
   繃S,;fi@`騂廩k叉c.2狇x佚�  �
   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  %   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  c   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  ?   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  }   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  G   �*o驑瓂a�(施眗9歐湬

�  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎     D���0�郋鬔G5啚髡J竆)俻w��  k   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  R   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  a   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  6   G�膢刉^O郀�/耦��萁n!鮋W VS  u   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  c   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   +椬恡�
	#G許�/G候Mc�蜀煟-  ?   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  )   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  l   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  7   �
bH<j峪w�/&d[荨?躹耯=�  v   交�,�;+愱`�3p炛秓ee td�	^,  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  =   `k�"�1�^�`�d�.	*貎e挖芺
脑�     +4[(広
倬禼�溞K^洞齹誇*f�5  �   L       �  �     �  �  5   �  �  D   �  �  P     �  �     �  �   �   �   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\numeric D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\RTXPT\External\Rtxdi\Source\ReSTIRDI.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDIParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDI.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp   �       L  v      z     
    b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb    H塡$H墊$UH嬱H冹P3�H嬞H�9墆茿   AD婮H峂蠨婤婻�     H墋鐷墋�M郒墋豀墋�C E�C@KP荂`   荂d   荂h   荂l   荂p费8荂t   H荂x   婯荅�   H荅焱蘈>荅�   荅�   荅鋐ff?荅�   荅型烫=荅�   ?H墋魤}�M星E�   荅�   荅型烫=荅�   ?E郒墋枨E�   荅�   B媭   M�儛   E郒墋鋲}�嫚   M蠬荅�  �A荅�   荅�   荅�   嫲   M�兝   E�冃   嬥   吷t冮t
凒u媨�媨髯冪�菈{4婥D婯�葔C0A岮�vb�E2覊CDD岪斧狝鬣殃�RD+繟岺D塁@斧狣塁H麽D塁L殃�R+菶勔塊PAD菵嬃D塁TH嬅H媩$hD塁H媆$`H兡P]脣斧獕KDA�D岮A鬣殃�RD+繟凒u楧塁@虢;   p       �   k  M G            9     9  �        �rtxdi::ReSTIRDIContext::ReSTIRDIContext 
 >�   this  AI       "�  AJ          >�   params  AK        : 5 M          仢
	(%
'" >�    useSpatialResampling  AX  �      AZ  �    �  c  AZ �    F  N" M          ����#	 N M        �  �' N' M        �  ��''$
 N M        �  ��+ N M        �  B
 N
 Z   $   P                     @ & h   �  �  �  �  �  �       `   �  Othis  h   �  Oparams  O �             9  �            l  �   a  �   c  �   d  �    e  �+   f  �B   g  �V   f  �Z   g  �f   h  ��   n  ��   i  ��   j  ��   i  ��   j  �  i  �#  j  �'  k  �.  i  �5  j  �9  k  �V  j  �]  k  �z  n  ��  o  ��  p  ��  o  ��  p  ��  q  �  p  �  q  �  p  �,   w    0   w   
 r   w    v   w   
 �   w    �   w   
 �   w    �   w   
 
  w      w   
   w    !  w   
 1  w    5  w   
 �  w    �  w   
 A@H嬄IPJ�   �   �   N G                      �        �rtxdi::ReSTIRDIContext::GetBufferIndices 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       �  �    �  �   �  �,   {    0   {   
 s   {    w   {   
 �   {    �   {   
 3繦堿H�H堿H堿H嬃�   �   �   L G                      �        �rtxdi::GetDefaultReSTIRDIBufferIndices                         H         ObufferIndices  O�   P              �     D         �      �     �	     �
   !  �   #  �   $  �,   r    0   r   
 �   r    �   r   
 茿费8H嬃茿   H茿   茿   茿   茿   �   �   �   �   T G            5       4   �        �rtxdi::GetDefaultReSTIRDIInitialSamplingParams                         H         Oparams  O   �   8           5   �     ,       '  �    )  �   1  �4   2  �,   s    0   s   
 �   s    �   s   
 3繦堿堿H嬃H茿  �A�   茿   茿   �   �   �   L G            )       (   �        �rtxdi::GetDefaultReSTIRDIShadingParams                         H         Oparams  O   �   8           )   �     ,       P  �    Q  �	   W  �(   X  �,   v    0   v   
 �   v    �   v   
 H茿    H嬃茿   茿   茿   �吞�=茿   ?茿   B�   �   �   V G            5       4   �        �rtxdi::GetDefaultReSTIRDISpatialResamplingParams                         H         Oparams  O �   8           5   �     ,       D  �    E  �   L  �4   M  �,   u    0   u   
 �   u    �   u   
 3繦堿$堿,H嬃H茿吞L>茿   茿   茿   茿fff?茿   �吞�=茿   ?�   �   �   W G            E       D   �        �rtxdi::GetDefaultReSTIRDITemporalResamplingParams                         H         Oparams  O�   8           E   �     ,       5  �    6  �	   @  �D   A  �,   t    0   t   
 �   t    �   t   
 婣�   �   �   K G                      �        �rtxdi::ReSTIRDIContext::GetFrameIndex 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       �  �    �  �   �  �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 A`H嬄IpJ�   �   �   Z G                      �        �rtxdi::ReSTIRDIContext::GetInitialSamplingParameters 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       �  �    �  �   �  �,   |    0   |   
    |    �   |   
 �   |    �   |   
 婣�   �   �   O G                      �        �rtxdi::ReSTIRDIContext::GetResamplingMode 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       t  �    u  �   v  �,   y    0   y   
 t   y    x   y   
 �   y    �   y   
 A H嬄�   �   �   Z G                   
   �        �rtxdi::ReSTIRDIContext::GetReservoirBufferParameters 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ~  �      �
   �  �,   x    0   x   
    x    �   x   
 �   x    �   x   
 A0H嬄�   �   �   N G                   
   �        �rtxdi::ReSTIRDIContext::GetRuntimeParams 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       y  �    z  �
   {  �,   z    0   z   
 s   z    w   z   
 �   z    �   z   
 佇   H嬄夃   J�   �   �   R G                      �        �rtxdi::ReSTIRDIContext::GetShadingParameters 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       �  �    �  �   �  �,       0      
 w       {      
 �       �      
 伆   H嬄壚   J�   �   �   \ G                      �        �rtxdi::ReSTIRDIContext::GetSpatialResamplingParameters 
 >�   this  AJ                                 @     �  Othis  O  �   0              �     $       �  �    �  �   �  �,   ~    0   ~   
 �   ~    �   ~   
 �   ~    �   ~   
 H岮�   �   �   Q G                      �        �rtxdi::ReSTIRDIContext::GetStaticParameters 
 >�   this  AJ                                 @     �  Othis  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 亐   H嬄墣   仩   JB �   �   �   ] G            $       #   �        �rtxdi::ReSTIRDIContext::GetTemporalResamplingParameters 
 >�   this  AJ        $                         @     �  Othis  O �   0           $   �     $       �  �    �  �#   �  �,   }    0   }   
 �   }    �   }   
 �   }    �   }   
 @SH冹 H嬞塓嬍�    墐�   H嬎婥��    婯吷t-冮t凒u#婥#�缐C4H兡 [脣C餍冟�缐C4H兡 [们C4    H兡 [�   q    "   �       �   5  K G            g      a   �        �rtxdi::ReSTIRDIContext::SetFrameIndex 
 >�   this  AI  	     ] =  P   AJ        	  >u    frameIndex  A           M          &
 N Z   #                           @ 
 h      0   �  Othis  8   u   OframeIndex  O   �   X           g   �     L       �  �	   �  �   �  �   �  �&   �  �>   �  �G   �  �Q   �  �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 �   �    �   �   
 L  �    P  �   
 A`JIp�   �     Z G                              �rtxdi::ReSTIRDIContext::SetInitialSamplingParameters 
 >�   this  AJ          >�   initialSamplingParams  AK                                 @     �  Othis "    �  OinitialSamplingParams  O�   0              �     $       �  �    �  �   �  �,   �    0   �   
    �    �   �   
 �   �    �   �   
   �      �   
 塓�       �       �   �   O G                               �rtxdi::ReSTIRDIContext::SetResamplingMode 
 >�   this  AJ          >�   resamplingMode  A          
 Z                             @     �  Othis     �  OresamplingMode  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 t   �    x   �   
 �   �    �   �   
   �      �   
 佇   J夃   �   �   �   R G                              �rtxdi::ReSTIRDIContext::SetShadingParameters 
 >�   this  AJ          >�   shadingParams  AK                                 @     �  Othis     �  OshadingParams  O�   0              �     $       �  �    �  �   �  �,   �    0   �   
 w   �    {   �   
 �   �    �   �   
    �      �   
 H冹(B媮�   
D$塂$D$壈   伬   H兡(�   �   F  \ G            2      -           �rtxdi::ReSTIRDIContext::SetSpatialResamplingParameters 
 >�   this  AJ        2   >�   spatialResamplingParams  AK        2  >�   srp  C�            !  D     (                      @  0   �  Othis $ 8   �  OspatialResamplingParams      �  Osrp  O  �   @           2   �     4       �  �   �  �   �  �   �  �-   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 \  �    `  �   
 @SH冹 H嬞亐   J墣   B 仩   婭�    墐�   H兡 [�-   q       �   )  ] G            =      7           �rtxdi::ReSTIRDIContext::SetTemporalResamplingParameters 
 >�   this  AI       0  AJ         ! >�   temporalResamplingParams  AK        1 
 Z   #                         @  0   �  Othis % 8   �  OtemporalResamplingParams  O   �   8           =   �     ,       �  �   �  �)   �  �7   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 @  �    D  �   
 D婹L嬌A岯�斧獀AD�E峆A麾斧陯RD+袳塓@E堿DE塓HA岼E塓L麽殃�R+華塈PE塓TE塓脣	D岮A鬣殃�RD+繣堿@A塈DA凓u	E堿TE堿肊峆E堿H斧狤堿LA麾殃�RD+蠩塓PE塓TE塓�   �   �   Q G            �       �           �rtxdi::ReSTIRDIContext::UpdateBufferIndices 
 >�   this  AJ          AQ       �  >�    useSpatialResampling  A   <     
  AZ                                H     �  Othis  O  �   p           �   �     d       �  �    �  �   �  �M   �  �U   �  �V   �  �u   �  �}   �  �~   �  ��   �  ��   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 婹呉t#冴t凓u婣#�缐A4脣A餍冟�缐A4们A4    �   �   �   U G            2       1           �rtxdi::ReSTIRDIContext::UpdateCheckerboardField 
 >�   this  AJ        2                         H     �  Othis  O �   H           2   �     <       �  �    �  �   �  �   �  �   �  �&   �  �,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �     �   �   A G                       �        �rtxdi::debugCheckParameters  >�   params  AJ          D                           H     �  Oparams  O �   (              �            [  �    ^  �,   �    0   �   
 h   �    l   �   
 �   �    �   �   
  t
 4 �P    9          �       �       �     20    g           �       �       �     20    =           �       �       �     B      2           �       �       �    E褼5`苁<b　�撏h鈊&连0潫)'b鸽,et蜴眆u懛葵c筋>qTj吏�EM]z溧y宑FW拔i呛幄�'醚X榀�)'�裨LE唖�X礩T�鏴盈Gm�W泾n��&�(�CL.昬槗賐�:7� 蓱j�
｝v�AmCreF雥S�F憣^xG;:V�?U饁f鏛氜铼�=fpo^id�訑+��N1v 禍z姡!pd�君�F�)FJ�uW賁芌欌氰�1K鄥E閙%蠴�y@穚rg,q雛Lx婕d=犺±&J衄�璗X喼篱 狨� 
=�T涮緘T菱駺G呰A剀5磼�%ォP崣`N謪亇�茊瑚�I棳 `@筯� h�$�/琢k�-钘�牴&@禄L(夊敜�>廃F趃蚁8遂祚皛t�	á;J蓓E匶昹K凈鵒秝k雵J-WV8o冗�=.g�雵J-WV8o�1�8]Z�-坓�(鬄�3,�4q胭        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       爂               .debug$T       h                 .rdata                畀�                     .text$mn       9     聏     .debug$S       �             .text$mn              勌╇     .debug$S       �              .text$mn    	          佹襾     .debug$S    
   �          	    .text$mn       5       触憂     .debug$S       �              .text$mn    
   )       �79d     .debug$S       �          
    .text$mn       5       z�>     .debug$S       �              .text$mn       E       觠h�     .debug$S       �              .text$mn              舷磥     .debug$S       �              .text$mn              Z5g~     .debug$S       �              .text$mn              ��     .debug$S       �              .text$mn              LSa     .debug$S       �              .text$mn              0}�     .debug$S       �              .text$mn              q<!     .debug$S       �              .text$mn              峫躿     .debug$S        �              .text$mn    !          烣0�     .debug$S    "   �          !    .text$mn    #   $       M�w     .debug$S    $   �          #    .text$mn    %   g      惜     .debug$S    &   �  
       %    .text$mn    '          铵I%     .debug$S    (   H         '    .text$mn    )         ��     .debug$S    *   <         )    .text$mn    +          �?鍎     .debug$S    ,   0         +    .text$mn    -   2       俢�-     .debug$S    .   �  
       -    .text$mn    /   =      wU     .debug$S    0   x  
       /    .text$mn    1   �       �1�     .debug$S    2   |         1    .text$mn    3   2       衞j!     .debug$S    4            3    .text$mn    5          .B+�     .debug$S    6   �          5        4                �                �       	                      ^              �                     
        l              �              
              \              �              �              V      #        �              &              y              �      !        �      %        (      )        v      '        �      /        F      -        �      +              1        8      3        p      5    $LN41           $LN15       %    $LN4        /    $LN4        -    .xdata      7          �! x        �      7    .pdata      8         Y
        �      8    .xdata      9          （亵%        C	      9    .pdata      :         ⅸ.�%        z	      :    .xdata      ;          （亵/        �	      ;    .pdata      <         現�/        #
      <    .xdata      =          �9�-        �
      =    .pdata      >          T枨-              >    _fltused         .chks64     ?   �                v  ?NumReservoirBuffers@ReSTIRDIContext@rtxdi@@2IB ?CalculateReservoirBufferParameters@rtxdi@@YA?AURTXDI_ReservoirBufferParameters@@IIW4CheckerboardMode@1@@Z ?JenkinsHash@rtxdi@@YAII@Z ?GetDefaultReSTIRDIBufferIndices@rtxdi@@YA?AUReSTIRDI_BufferIndices@@XZ ?GetDefaultReSTIRDIInitialSamplingParams@rtxdi@@YA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetDefaultReSTIRDITemporalResamplingParams@rtxdi@@YA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetDefaultReSTIRDISpatialResamplingParams@rtxdi@@YA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetDefaultReSTIRDIShadingParams@rtxdi@@YA?AUReSTIRDI_ShadingParameters@@XZ ??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z ?GetReservoirBufferParameters@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetResamplingMode@ReSTIRDIContext@rtxdi@@QEBA?AW4ReSTIRDI_ResamplingMode@2@XZ ?GetRuntimeParams@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_RuntimeParameters@@XZ ?GetBufferIndices@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_BufferIndices@@XZ ?GetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_TemporalResamplingParameters@@XZ ?GetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_SpatialResamplingParameters@@XZ ?GetShadingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_ShadingParameters@@XZ ?GetFrameIndex@ReSTIRDIContext@rtxdi@@QEBAIXZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z ?SetResamplingMode@ReSTIRDIContext@rtxdi@@QEAAXW4ReSTIRDI_ResamplingMode@2@@Z ?SetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_InitialSamplingParameters@@@Z ?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z ?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z ?SetShadingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_ShadingParameters@@@Z ?UpdateBufferIndices@ReSTIRDIContext@rtxdi@@AEAAXXZ ?UpdateCheckerboardField@ReSTIRDIContext@rtxdi@@AEAAXXZ ?debugCheckParameters@rtxdi@@YAXAEBUReSTIRDIStaticParameters@1@@Z $unwind$??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z $pdata$??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z $unwind$?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z $pdata$?SetFrameIndex@ReSTIRDIContext@rtxdi@@QEAAXI@Z $unwind$?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z $pdata$?SetTemporalResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_TemporalResamplingParameters@@@Z $unwind$?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z $pdata$?SetSpatialResamplingParameters@ReSTIRDIContext@rtxdi@@QEAAXAEBUReSTIRDI_SpatialResamplingParameters@@@Z 
/143            1755678910              100666  138804    `
d嗶 緢� �      .drectve        <  4&               
 .debug$S        軀  p'              @ B.debug$T        h   L�              @ B.text$mn        :   搐  瞍          P`.debug$S          �  �         @B.text$mn           ぇ  户          P`.debug$S        �  脓  ]�         @B.text$mn        0   椹  �          P`.debug$S        �  #�  揩         @B.text$mn        0   K�  {�          P`.debug$S        �  叕  A�         @B.text$mn        0   彤            P`.debug$S        �  �  钒         @B.text$mn        �  C�  岵          P`.debug$S        �  1�  �      f   @B.text$x         (   
�  5�          P`.text$mn        #  I�  l�          P`.debug$S        $  寂  嘌      `   @B.text$x         (   犝  日          P`.text$mn        �  苷  }�          P`.debug$S        X  妥  %�      f   @B.text$x         (   !�  I�          P`.text$mn        �  ]�               P`.debug$S          溟  祓          @B.text$mn        �  ,�  轸          P`.debug$S        �  篝  稞      >   @B.text$mn          [  c         P`.debug$S        �	  � g
     l   @B.text$mn        �   � �         P`.debug$S        �  � T        @B.text$x                      P`.text$mn          " 2         P`.debug$S        �
  � V$     J   @B.text$x            :' J'         P`.text$mn        <   T' �'         P`.debug$S        0  �' �(     
   @B.text$mn        <   B) ~)         P`.debug$S        L  �) �*     
   @B.text$mn        !   L+ m+         P`.debug$S        <  �+ �,        @B.text$mn        2   �, +-         P`.debug$S        <  ?- {.        @B.text$mn        [   �. N/         P`.debug$S        �  b/ 62        @B.text$mn           3 3         P`.debug$S        T  !3 u4        @B.text$mn        e   �4 5         P`.debug$S        �  45 �7        @B.text$mn           �8 �8         P`.debug$S        �   �8 �9        @B.text$mn           �9 
:         P`.debug$S        �   : �:        @B.text$mn        B   :; |;         P`.debug$S           �; �<        @B.text$mn        B   �< =         P`.debug$S          6= F>        @B.text$mn        B   �> �>         P`.debug$S        �   �> �?        @B.text$mn        V   @ p@         P`.debug$S        �  凘 (B        @B.text$mn           燘              P`.debug$S        �   禕         @B.text$mn        z  釩 \I         P`.debug$S        P  LJ 淺     �   @B.text$x            萣 詁         P`.text$x            辀 阞         P`.text$mn           鬮              P`.debug$S        �   鱞 遚        @B.text$mn           d              P`.debug$S        �   .d e        @B.text$mn           Ze              P`.debug$S        �   he `f        @B.text$mn           渇              P`.debug$S          籪 蟝        @B.text$mn           h )h         P`.debug$S          3h 7i        @B.text$mn           si              P`.debug$S        �   巌 ~j        @B.text$mn        x  簀 2n     
    P`.debug$S        D  杗 趘     >   @B.text$mn           Fy              P`.debug$S           Xy Xz        @B.text$mn           攝              P`.debug$S        @   鋥        @B.text$mn            4| T|         P`.debug$S        �   r| 6}        @B.text$mn        z   r} 靰         P`.debug$S        �   ~ 纮        @B.text$mn           渷 瓉         P`.debug$S        �   羴 檪        @B.text$mn           諅 鎮         P`.debug$S        �   鷤 騼        @B.text$mn           .� ?�         P`.debug$S        �   S� ?�        @B.text$mn        B   {� 絽         P`.debug$S        �  褏 m�        @B.text$mn        B   q� 硥         P`.debug$S        �  菈 s�        @B.text$mn        A   w� 笉         P`.debug$S        �  虓 x�        @B.text$mn           |� 彂         P`.debug$S        �   檻 m�        @B.xdata                          @0@.pdata             睊 綊        @0@.xdata             蹝             @0@.pdata             鐠 髵        @0@.xdata             �             @0@.pdata             � %�        @0@.xdata             C�             @0@.pdata             O� [�        @0@.xdata             y�             @0@.pdata             亾 崜        @0@.xdata             珦             @0@.pdata             窊 脫        @0@.xdata             釗             @0@.pdata             閾 鯎        @0@.xdata             �             @0@.pdata             � '�        @0@.xdata             E�             @0@.pdata             M� Y�        @0@.xdata             w�             @0@.pdata             � 嫈        @0@.xdata                          @0@.pdata             睌 綌        @0@.xdata             蹟             @0@.pdata             銛 飻        @0@.xdata             
�             @0@.pdata             � !�        @0@.xdata              ?� _�        @0@.pdata             s� �        @0@.xdata          	   潟         @@.xdata             簳 罆        @@.xdata             蕰             @@.xdata             諘 頃        @0@.pdata             � 
�        @0@.xdata          	   +� 4�        @@.xdata             H� N�        @@.xdata             X�             @@.xdata             ^�             @0@.pdata             f� r�        @0@.xdata             悥             @0@.pdata              礀        @0@.xdata          8   覗 
�        @0@.pdata             (� 4�        @0@.xdata             R� b�        @0@.pdata             �� 寳        @0@.xdata          H   獥 驐        @0@.pdata             � �        @0@.xdata          	   0� 9�        @@.xdata          
   M� Z�        @@.xdata             n�             @@.xdata             u�             @0@.pdata             }� 墭        @0@.xdata                          @0@.pdata             瘶 粯        @0@.xdata             贅             @0@.pdata             針 順        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             =� Y�        @0@.pdata             m� y�        @0@.xdata          
   棛         @@.xdata             聶             @@.xdata             艡 蜋        @@.xdata             讬 迿        @@.xdata          	   铏             @@.xdata             駲             @0@.pdata             鶛 �        @0@.voltbl            #�               .xdata             $� @�        @0@.pdata             T� `�        @0@.xdata          
   ~� 嫐        @@.xdata                          @@.xdata             瑲 礆        @@.xdata             練 艢        @@.xdata          	   蠚             @@.xdata             貧             @0@.pdata             鄽 鞖        @0@.voltbl            
�               .xdata             � '�        @0@.pdata             ;� G�        @0@.xdata          
   e� r�        @@.xdata             悰             @@.xdata             摏 洓        @@.xdata              瑳        @@.xdata          	   稕             @@.xdata             繘             @0@.pdata             菦 記        @0@.voltbl            駴               .xdata             驔             @0@.pdata             
� �        @0@.xdata             4� H�        @0@.pdata             f� r�        @0@.xdata             悳 牅        @0@.pdata             緶 蕼        @0@.xdata             铚 鼫        @0@.pdata             � &�        @0@.xdata             D� T�        @0@.pdata             r� ~�        @0@.xdata             湞             @0@.pdata              礉        @0@.xdata             覞             @0@.pdata             逎 隄        @0@.xdata             �             @0@.pdata             � (�        @0@.xdata             F�             @0@.pdata             R� ^�        @0@.xdata             |�             @0@.pdata             劄 悶        @0@.rdata             疄 茷        @@@.rdata             錇             @@@.rdata             鰹 �        @@@.rdata             ,� D�        @@@.rdata             b�             @@@.xdata$x           w� 摕        @@@.xdata$x            脽        @@@.data$r         /   釤 �        @@�.xdata$x        $   � >�        @@@.data$r         $   R� v�        @@�.xdata$x        $   ��         @@@.data$r         $   笭 軤        @@�.xdata$x        $   鏍 
�        @@@.rdata             �             @@@.rdata$r        $   .� R�        @@@.rdata$r           p� 劇        @@@.rdata$r           帯 殹        @@@.rdata$r        $   ぁ 取        @@@.rdata$r        $   堋  �        @@@.rdata$r           � 2�        @@@.rdata$r           <� P�        @@@.rdata$r        $   d� 垻        @@@.rdata$r        $   湤 愧        @@@.rdata$r           蔻 颌        @@@.rdata$r            �        @@@.rdata$r        $   6� Z�        @@@.rdata             n�             @0@.rdata             r�             @0@.rdata             v�             @0@.rdata             z�             @0@.rdata             ~�             @0@.debug$S        4   偅 叮        @B.debug$S        4   剩         @B.debug$S        @   � R�        @B.chks64         �  f�              
     /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   r  \     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ReGIR.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $?A0x23112da8  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $_Binary_hypot   �   �
  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable S �   std::_Trivial_cat<float,float,float &&,float &>::_Same_size_and_compatible P �   std::_Trivial_cat<float,float,float &&,float &>::_Bitcopy_constructible M �   std::_Trivial_cat<float,float,float &&,float &>::_Bitcopy_assignable { �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Same_size_and_compatible x �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Bitcopy_constructible u �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &&,ReGIR_OnionRing &>::_Bitcopy_assignable � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Same_size_and_compatible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Bitcopy_constructible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &&,ReGIR_OnionLayerGroup &>::_Bitcopy_assignable . �    std::integral_constant<bool,0>::value : �    std::integral_constant<unsigned __int64,0>::value ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask  �   鸄  $ �    std::strong_ordering::equal ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong ( �  ��I@`anonymous namespace'::c_pi B �   std::allocator<float>::_Minimum_asan_allocation_alignment : �   std::integral_constant<unsigned __int64,2>::value R �   std::allocator<ReGIR_OnionLayerGroup>::_Minimum_asan_allocation_alignment L �   std::allocator<ReGIR_OnionRing>::_Minimum_asan_allocation_alignment      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 z �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Same_size_and_compatible w �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Bitcopy_constructible t �   std::_Trivial_cat<ReGIR_OnionRing,ReGIR_OnionRing,ReGIR_OnionRing &,ReGIR_OnionRing &>::_Bitcopy_assignable - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Same_size_and_compatible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Bitcopy_constructible � �   std::_Trivial_cat<ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup,ReGIR_OnionLayerGroup &,ReGIR_OnionLayerGroup &>::_Bitcopy_assignable . �   std::integral_constant<bool,1>::value  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  #   rsize_t  (  _TypeDescriptor % k  _s__RTTICompleteObjectLocator2 A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType  �  rtxdi::ReGIRContext $ R  rtxdi::ReGIRDynamicParameters    rtxdi::float3 1 �  rtxdi::LocalLightReGIRFallbackSamplingMode # �  rtxdi::ReGIRStaticParameters ' t  rtxdi::RISBufferSegmentAllocator '   rtxdi::ReGIRGridStaticParameters (   rtxdi::ReGIROnionStaticParameters  {  rtxdi::ReGIRMode  !  rtxdi::uint3 , Y  rtxdi::ReGIROnionCalculatedParameters + d  rtxdi::ReGIRGridCalculatedParameters , �  rtxdi::LocalLightReGIRPresamplingMode  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16 D   std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >  �  std::_Lockit    std::_Num_base # �  std::numeric_limits<char8_t> & s  std::allocator<ReGIR_OnionRing>  �  std::hash<float>  �  std::less<void>    std::_Num_int_base , �  std::allocator<ReGIR_OnionLayerGroup>    std::float_denorm_style = �  std::_Default_allocator_traits<std::allocator<float> >     std::_Compare_t " A  std::numeric_limits<double> ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short> % u  std::_One_then_variadic_args_t   |  std::pmr::memory_resource  �  std::false_type    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t> % �  std::integral_constant<bool,1>     std::_Leave_proxy_unbound b J  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> � �  std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1>  �  std::_Iterator_base12 C �  std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >  �  std::plus<void>  �  std::hash<long double> = �  std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> & �  std::bidirectional_iterator_tag % �  std::integral_constant<bool,0>  
  std::bad_exception & �  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator 3 V  std::_Vector_val<std::_Simple_types<float> > ! ?  std::numeric_limits<float> G �  std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> >  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy = m  std::_Uninitialized_backout_al<std::allocator<float> > $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>  �  std::true_type   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits P ]  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > f �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy ! ,  std::numeric_limits<short>     std::bad_alloc # 2  std::numeric_limits<__int64>  C  std::memory_order ! S  std::pair<float *,float *> > �  std::allocator_traits<std::allocator<ReGIR_OnionRing> >   �  std::forward_iterator_tag   6  std::bad_array_new_length v ~  std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1>  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int> 0 �  std::vector<float,std::allocator<float> > F �  std::vector<float,std::allocator<float> >::_Reallocation_policy   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::_Compare_eq M   std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >    std::nullptr_t & �  std::random_access_iterator_tag ) 8  std::numeric_limits<unsigned long> K "  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > ' "  std::numeric_limits<signed char>  �  std::_Container_base G   std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> >  �  std::_Literal_zero M 3  std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> >      std::numeric_limits<char>  B  std::allocator<float>  �  std::_Unused_parameter ! �  std::ranges::_Set_union_fn # A  std::ranges::_Unique_copy_fn '   std::ranges::_Replace_copy_if_fn & �  std::ranges::_Is_partitioned_fn ( q  std::ranges::_Stable_partition_fn !   std::ranges::_Is_sorted_fn # G  std::ranges::_Find_if_not_fn    std::ranges::_Clamp_fn % �  std::ranges::_Is_heap_until_fn ' �  std::ranges::_Partition_point_fn ( 
  std::ranges::_Prev_permutation_fn  �  std::ranges::_All_of_fn "   std::ranges::_Generate_n_fn / %  std::ranges::_Lexicographical_compare_fn  e  std::ranges::_Shuffle_fn ! �  std::ranges::_Make_heap_fn '   std::ranges::_Is_sorted_until_fn   �  std::ranges::_Count_if_fn  G  std::ranges::_Reverse_fn    std::ranges::_Minmax_fn & �  std::ranges::_Minmax_element_fn  �  std::ranges::_Sort_fn # Y  std::ranges::_Rotate_copy_fn # /  std::ranges::_Remove_copy_fn # �  std::ranges::_Nth_element_fn   �  std::ranges::_Search_n_fn   �  std::ranges::_Find_end_fn  #  std::ranges::_Remove_fn  ;  std::ranges::_Find_fn & 5  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  �  std::ranges::_Equal_fn ! �  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! )  std::ranges::_Remove_if_fn   u  std::ranges::_For_each_fn   }  std::ranges::_Pop_heap_fn & �  std::ranges::_Set_difference_fn ) �  std::ranges::_Partial_sort_copy_fn  �  std::ranges::_Is_heap_fn ! w  std::ranges::_Push_heap_fn ! k  std::ranges::_Partition_fn % M  std::ranges::_Adjacent_find_fn $ �  std::ranges::_Partial_sort_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn % �  std::ranges::_Binary_search_fn " {  std::ranges::_For_each_n_fn & �  std::ranges::_Partition_copy_fn  �  std::ranges::_Copy_n_fn $ M  std::ranges::_Reverse_copy_fn # �  std::ranges::_Equal_range_fn  �  std::ranges::_Move_fn $   std::ranges::_Replace_copy_fn     std::ranges::_Generate_fn   5  std::ranges::_Mismatch_fn   �  std::ranges::_Includes_fn  �  std::ranges::_Count_fn  _  std::ranges::_Sample_fn  �  std::ranges::_Merge_fn # �  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �  std::ranges::_Move_backward_fn  k  std::ranges::_Min_fn  �  std::ranges::_Copy_if_fn " �  std::ranges::_Replace_if_fn & �  std::ranges::_Is_permutation_fn  )  std::ranges::_Copy_fn  �  std::ranges::_Replace_fn    std::ranges::_Fill_fn ( �  std::ranges::_Set_intersection_fn % �  std::ranges::_Inplace_merge_fn 0 �  std::ranges::_Set_symmetric_difference_fn  #  std::ranges::dangling % �  std::ranges::_Copy_backward_fn  S  std::ranges::_Search_fn    std::ranges::_Prev_fn # �  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn (   std::ranges::_Next_permutation_fn # �  std::ranges::_Lower_bound_fn  ;  std::ranges::_Unique_fn  �  std::ranges::_None_of_fn    std::ranges::_Advance_fn  �  std::ranges::_Any_of_fn % �  std::ranges::_Find_first_of_fn ! �  std::ranges::_Transform_fn # �  std::ranges::_Stable_sort_fn  S  std::ranges::_Rotate_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn 4 �  std::allocator_traits<std::allocator<float> >  �  std::input_iterator_tag c �  std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > W �  std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > # �  std::contiguous_iterator_tag " �  std::_Asan_aligned_pointers  �  std::partial_ordering Q   std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > >  .  std::numeric_limits<int>  �  std::bad_variant_access D a  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > Z 8  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat 
 !   _ino_t M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *>  9  terminate_handler  �  _s__RTTIBaseClassArray  �  ReGIR_OnionLayerGroup 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray     ptrdiff_t  "  _stat64i32  �  _PMD ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 % |  __RTTIClassHierarchyDescriptor 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE  �  ReGIR_OnionRing 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  �  _ldiv_t  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers  �   p
      v�%啧4壽/�.A腔$矜!洎\,Jr敎  K    娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �    D���0�郋鬔G5啚髡J竆)俻w��  �    c�#�'�縌殹龇D兺f�$x�;]糺z�  :   +4[(広
倬禼�溞K^洞齹誇*f�5  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎     ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  U   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   鹴y�	宯N卮洗袾uG6E灊搠d�      豊+�丟uJo6粑'@棚荶v�g毩笨C  c   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  J   �
bH<j峪w�/&d[荨?躹耯=�  �   �0�*е彗9釗獳+U叅[4椪 P"��  �   +椬恡�
	#G許�/G候Mc�蜀煟-     {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  D   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷     匐衏�$=�"�3�a旬SY�
乢�骣�  P   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  0   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  }   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   _O縋[HU-銌�鼪根�鲋薺篮�j��     っ8OI枣鮌赊jr�燳;鞾孌j  B   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   �%逽|犟�1剝%sh鵺K媡簂蹶#楎`{w  (	   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  q	   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �	   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �	   繃S,;fi@`騂廩k叉c.2狇x佚�  6
   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  t
   檒Gq$�#嗲RR�錨账��K諻刮g�   �
   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �
   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  @   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   o藾錚\F鄦泭|嚎醖b&惰�_槮  
   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  W   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  G
    d蜯�:＠T邱�"猊`�?d�B�#G騋  �
   溶�$椉�
悇� 騐`菚y�0O腖悘T  �
   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     `k�"�1�^�`�d�.	*貎e挖芺
脑�  [   �"睱建Bi圀対隤v��cB�'窘�n  �   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  6   蜅�萷l�/费�	廵崹
T,W�&連芿  s   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   泽閇�R鯄呙+困胢p=�R刐鉍籫�8[     -�
�捂�
y�*犯丁�02?栕9/�Q  K   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1      ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  j   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   �*o驑瓂a�(施眗9歐湬

�  0    I嘛襨签.濟;剕��7啧�)煇9触�.  p   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   交�,�;+愱`�3p炛秓ee td�	^,  3   _臒~I��歌�0蘏嘺QU5<蝪祰S  x   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  ?   a�傌�抣?�g]}拃洘銌刬H-髛&╟  }   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��     п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  p   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  ;   �	玮媔=zY沚�c簐P`尚足,\�>:O  |   G�膢刉^O郀�/耦��萁n!鮋W VS  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  S   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   �      �  X  B   �  X  H   �  X  Y   �  X  �   }  �   �   �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �   �  �   U   �  �  ,   �  �  4   �  �  �   �  �  �   �  �   S  �  �     �  �   f  �  �   �  �  �   `  �  �   S  �  �   f  �  �   �  �  �   `  �  �   S  �  �   f  �  �   �  �  �   `  �  �   *   �  �   �  �  �   �  �  �  K   �  �  K   �  �  �  �     ,   �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �     �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �  �  �        �  �     �  �     �  �        �       �       �   �  
  �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   ]  "  �   ]  #  �   ]  '  �  �   3  X  �   4  �   �  5  �   �  7  �   �  8  X  �   :  �   �  <  �   �  ?  X  �  A  X  �   C  �   �  E  �   �  H  X  �  J  �   @   K  �   �   L  �   @   P  �   �  V  �  �  W  �  �  X  �  �  [  X  �   \  X  �  `  X  �   d  �   �  f  X  �   j  �   �  k  �   �   l  �   @   m  x
  �	  n  X  3  o  �  ,  p  �  T  t  �  �   w  �   �  y  �   �  z  X  �   |  �   �  }  X  �   ~  X  �    �  �  �  �  I  �  �    �  X  �   �  X  �   �  X  �   �   �   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\Source\ReGIR.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\numeric C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h    �       L�     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   k        �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >�   _Bytes  AJ        9  $  >�    _Block_size  AH       1 
   >�    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        �  
 Z   �   N Z   3  �   (                      H 
 h   �         $LN14  0   �  O_Bytes  O   �   h           :   �   
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  J   w  J  
 �     �    
 H+袸嬂H+翷嬄H嬔H嬋�             �   Q  R G                      ~        �std::_Copy_backward_memmove<float *,float *>  >@   _First  AJ          >@   _Last  AK          >@   _Dest  AH         AP          >�    _Count  AK                                H 
 h   ]      @  O_First     @  O_Last     @  O_Dest  O   �   0              X     $       � �    � �   � �,   	   0   	  
 y   	   }   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 �   	   �   	  
 h  	   l  	  
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   H  I G            0   
   %   \        �std::_Copy_memmove<float *,float *>  >@   _First  AJ          >@   _Last  AK          >@   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   ]   0   @  O_First  8   @  O_Last  @   @  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 p      t     
 �      �     
 �      �     
 �      �     
 �      �     
 \     `    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   h  i G            0   
   %   H        �std::_Copy_memmove<ReGIR_OnionLayerGroup *,ReGIR_OnionLayerGroup *>  >|   _First  AJ          >|   _Last  AK          >|   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h      0   |  O_First  8   |  O_Last  @   |  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 |     �    
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�         �   \  ] G            0   
   %   ?        �std::_Copy_memmove<ReGIR_OnionRing *,ReGIR_OnionRing *>  >�   _First  AJ          >�   _Last  AK          >�   _Dest  AM         AP          >�    _Count  AI  
                             H 
 h   �   0   �  O_First  8   �  O_Last  @   �  O_Dest  O�   @           0   X     4       � �   � �   � �!   � �%   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 p     t    
 L塂$H塋$SVWATAUAVAWH冹 L嬧H孂L�	L孃M+鵌�H婣I+罤柳I�������?I;�凥  L峢H婭I+蒆六H嬔H殃I嬂H+翲;��  H�
M嬽I;臠C餗;��  J�4�    L塼$hH侢   r)H峃'H;�嗕   �    H吚勨   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL塼$h�3跮塼$hH塡$xN�4籋婦$p� A�L婫H�H嬎M;鄒L+码M嬆L+妈    I峃L婫M+腎嬙�    怘�H吷t1H媁H+袶冣麳侜   rH兟'L婣鳬+菻岮鳫凐w:I嬋�    H�J�獺塐H�H塐I嬈H兡 A_A^A]A\_^[描    惕    惕    泰   �    �   �         (     b  �    �  �    �  �    �  �       �   	  s G            �     �  �        �std::vector<float,std::allocator<float> >::_Emplace_reallocate<float const &> 
 >{   this  AJ          AM       j  D`    >�   _Whereptr  AK          AT       �l  >�   <_Val_0>  AH  �       AP        A  Dp    >#     _Newcapacity  AV  u     {  AV �        Bh   �        >�    _Newsize  AU  N     P8 D  >�    _Whereoff  AW  %     y  ]
  >�    _Oldsize  AH  0     g  2 0 >�    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M        4  ur� M        P  ur�& M        K  ��)
)%��( M        k  ��$	%)
��
 Z   �   >�    _Block_size  AJ  �       AJ �      >�    _Ptr_container  AH  �       AH �     �  � 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        l  
r

 N N N M          Nk >�    _Oldcapacity  AJ  R     �   M % z   AJ �     �  �  >�    _Geometric  AH  r     u ;  _   AH �     �  �  M          N N N M        5  
�� M        [  
�� N N M        7  �	 M        \  �	 >�    _Count  AP  �       AP '      N N M        7  �! >�   _Last  AP  !      >@   _Dest  AJ      
  AJ '      M        \  �! >�    _Count  AP  $      AP '      N N M        7  � M        \  � >�    _First_ch  AK        AK '      >�    _Count  AP        N N% M          �-h1#' M        �  *�<\ M        �  丂)7
 Z   {  
 >   _Ptr  AJ a      >#    _Bytes  AK  9    -    AK �     % M        }  両d#
:
 Z   3   >�    _Ptr_container  AP  Q      AP a    <  2  >�    _Back_shift  AJ  0    1  AJ a    <  +  N N N N
 Z                  8         0@ � h   �  �  }  ~  �  �  �  �  �          /  4  5  6  7  K  M  N  O  P  [  \  ]  ^  k  l  w  �         $LN145  `   {  Othis  h   �  O_Whereptr  p   �  O<_Val_0>  O   �   �           �  �      �       * �   3 �,   4 �7   6 �J   : �N   ; �r   = ��   A ��   B ��   E �	  G �  K �  L �  N �-  V �y  W �|  X ��  = ��  7 ��  V ��   �  � F            (   
   (             �`std::vector<float,std::allocator<float> >::_Emplace_reallocate<float const &>'::`1'::catch$0 
 >{   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �                        � f        __catch$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z$0        $LN145  `   {  Nthis  h   �  N_Whereptr  p   �  N<_Val_0>  O   �   0           (   �      $       P �
   R �   S �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
   �    #  �   
 N  �    R  �   
 ^  �    b  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 .  �    2  �   
 B  �    F  �   
    �      �   
   �      �   
 9  �    =  �   
 I  �    M  �   
 l  �    p  �   
 |  �    �  �   
 ;  �    ?  �   
 W  �    [  �   
 �  �    �  �   
 �  �    �  �   
 :  �    >  �   
 J  �    N  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 _  �    c  �   
 o  �    s  �   
 �  �    �  �   
 $  �    (  �   
 E  �    I  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 �  <   �  <  
 ,	  �    0	  �   
 
  
   
  
  
 �
  
   �
  
  
 �
  
   �
  
  
 �
  =   �
  =  
 Q  <   U  <  
 �  
   �  
  
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      L塂$H塋$SVWATAUAVAWH冹 L嬧L嬹L�L嬍M+蔍猾*I嬅I鏖L嬯I笼I嬇H凌?L鐷婭I+蔍嬅H鏖H龙H嬄H凌?H蠭窾UUUUUUI;�劆  L峼I婲I+蔍嬅H鏖H龙H嬄H凌?H蠬嬍H验I嬂H+罤;�噁  H�I嬿I;荋C餓;�嘜  H�<vH羚H塼$hH�   r)H峅'H;��,  �    H吚�*  H峏'H冦郒塁#H�tH嬒�    H嬝H塂$xH塼$h�3跦塼$hH塡$xJ�4m    I鮄伶H驢婦$p HN@ F M婩I�H嬎M;鄒L+码M嬆L+妈    H峃0M婩M+腎嬙�    怣�M吚tSI婲I+菻斧*H鏖H龙H嬄H凌?H蠬�RH菱H侜   rH兟'I婬鳯+罥岪鳫凐wDL嬃I嬋�    I�K�H玲H薎塏H�I塏H嬈H兡 A_A^A]A\_^[描    惕    惕    替   �    
  �    q     �     �  �      �      �      �       �   �  � G            #     #          �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Emplace_reallocate<ReGIR_OnionLayerGroup const &> 
 >�   this  AJ          AV       �  D`    >�   _Whereptr  AK          AT       �  >�   <_Val_0>  AH  @    5  AP        n  Dp    >#     _Newcapacity  AL  �       AL         Bh   �     TH  >�    _Newsize  AW  {     ��
 �  >�    _Whereoff  AU  ;       >�    _Newvec  AI          AI $    � � 
  Bx       
  �   M        
  u��乥 M          u��乥& M        K  ��)
)%��( M        k  ��$	%)
�
 Z   �   >�    _Block_size  AJ  �       AJ       >�    _Ptr_container  AH  �       AH $    �  � 
 >�    _Ptr  AI  �       AI $    � � 
  M        �  ��
 Z   �   N N M        �  �
 Z   �   N N M        L  
��

 N N N M          { >�    _Geometric  AH  �     u ;  _   AH $    �  �  M        #  { N N M        C  �; M        f  �; N N M        E  乪 M        H  乪 >�    _Count  AP  Z      AP �      N N M        E  亇 >�   _Last  AP  }      >|   _Dest  AJ  y    
  AJ �      M        H  亇 >�    _Count  AP  �      AP �      N N M        E  乯 >|    _UFirst  AK  ]      AK �      M        H  乯 >�    _Count  AP  m      N N% M          亯hS#' M        �  1伋j M        �  伝)A
 Z   {  
 >   _Ptr  AP �      >#    _Bytes  AK  �    )  AK      % M        }  伳d#
D
 Z   3   >�    _Ptr_container  AJ  �      AJ �    F  >  >�    _Back_shift  AP  �    P  AP �    F 5   N N N N
 Z   �               8         0@ � h   �  �  }  ~  �  �  �  �  �  �    
      
        #  B  C  D  E  H  K  L  f  h  k  |  �         $LN145  `   �  Othis  h   �  O_Whereptr  p   �  O<_Val_0>  O  �   �           #  �      �       * �   3 �I   4 �d   6 �w   : �{   ; ��   = �$  A �)  B �V  E �e  G �h  K �j  L �u  N ��  V ��  W �  X �  = �  7 �  V ��   I  � F            (   
   (             �`std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Emplace_reallocate<ReGIR_OnionLayerGroup const &>'::`1'::catch$0 
 >�   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �                        � �        __catch$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z$0        $LN145  `   �  Nthis  h   �  N_Whereptr  p   �  N<_Val_0>  O   �   0           (   �      $       P �
   R �   S �,      0     
 �      �     
 �      �     
          
          
 ?     C    
 O     S    
 ~     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 +     /    
 ?     C    
 �         
          
 8     <    
 H     L    
 k     o    
 {         
 ;     ?    
 S     W    
 �     �    
      	    
 C     G    
 c     g    
 s     w    
 �     �    
 �     �    
 �         
          
 I     M    
 �     �    
 �         
          
 j     n    
 z     ~    
 �     �    
 �     �    
 �  B   �  B  
 �     �    
 �	     �	    
 p
     t
    
 �
     �
    
 �
  C   �
  C  
 �  B   �  B  
 �     �    
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      L塂$H塋$SVWATAUAVAWH冹 L嬯H孂H�L嬧L+郘媞L+餓窿I�������M;�凮  I�艸婭H+菻六H嬔H殃I嬂H+翲;��&  H�
M孇I;芁C鳰;��  I嬿H伶L墊$hH侢   r)H峃'H;�嗧   �    H吚勲   H峏'H冦郒塁#H咑tH嬑�    H嬝H塂$xL墊$h�3跮墊$hH塡$xI冧餗�<H婦$p AL婫H�H嬎M;鑥L+码M嬇L+妈    I峅L婫M+臝嬚�    怘�H吷t1H媁H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐w=I嬋�    H�I伶L驦墂H�H塐I嬊H兡 A_A^A]A\_^[描    惕    惕    蹋   �    �   �         (     b  �    �  �    �  �    �  �       �   )	  � G            �     �  �        �std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Emplace_reallocate<ReGIR_OnionRing const &> 
 >   this  AJ          AM       �m  D`    >Y   _Whereptr  AK          AU       �m  >�   <_Val_0>  AH  �     &  AP        =  Dp    >#     _Newcapacity  AW  p     ~  AW �        Bh   �     	  >�    _Newsize  AV  I     X$" L  >�    _Whereoff  AT  %       >�    _Oldsize  AV  ,     o   L >Y    _Newvec  AI  �         AI �     � � 
  Bx   �     �   �   M          tm�" M          tm�"& M        K  ��)
)%��( M        k  ��$	%)
��
 Z   �   >�    _Block_size  AJ  �       AJ �      >�    _Ptr_container  AH  �       AH �     �  � 
 >�    _Ptr  AI  �       AI �     � � 
  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        J  
m
 N N N M          Ik >�    _Oldcapacity  AJ  M     �   L % y   AJ �     � # �  >�    _Geometric  AH  m     t :  ^   AH �     �  �  M        "  I N N M        :  �� M        `  �� N N M        <  �	 M        ?  �	 >�    _Count  AP  �       AP '      N N M        <  �! >Y   _Last  AP  !      >�   _Dest  AJ      
  AJ '      M        ?  �! >�    _Count  AP  $      AP '      N N M        <  � >�    _UFirst  AK        AK '      M        ?  � >�    _Count  AP        N N% M          �-h1#' M        �  *�<_ M        �  丂):
 Z   {  
 >   _Ptr  AJ a      >#    _Bytes  AK  9    -    AK �     % M        }  両d#
=
 Z   3   >�    _Ptr_container  AP  Q      AP a    ?  5  >�    _Back_shift  AJ  0    1  AJ a    ?  #  N N N N
 Z   �               8         0@ � h   �  �  }  ~  �  �  �  �  �  �  �                "  9  :  ;  <  ?  J  K  `  b  k  y  �         $LN145  `     Othis  h   Y  O_Whereptr  p   �  O<_Val_0>  O   �   �           �  �      �       * �   3 �(   4 �3   6 �F   : �I   ; �m   = ��   A ��   B ��   E �	  G �  K �  L �  N �-  V �|  W �  X ��  = ��  7 ��  V ��     � F            (   
   (             �`std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Emplace_reallocate<ReGIR_OnionRing const &>'::`1'::catch$0 
 >   this  EN  `         (  >�   <_Val_0>  EN  p         ( 
 Z   �                        � �        __catch$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z$0        $LN145  `     Nthis  h   Y  N_Whereptr  p   �  N<_Val_0>  O �   0           (   �      $       P �
   R �   S �,       0      
 �       �      
 �       �      
 �       �      
       
     
 -      1     
 =      A     
 l      p     
 |      �     
 �      �     
 �      �     
 �      �     
       
     
 0      4     
 D      H     
 X      \     
            
 &      *     
 O      S     
 _      c     
 �      �     
 �      �     
 Q      U     
 m      q     
 �      �     
 �      �     
 P      T     
 `      d     
 �      �     
 �      �     
 �      �     
 	      
     
            
 Y      ]     
 i      m     
 �      �     
 8      <     
 Y      ]     
 m      q     
 �      �     
 �      �     
            
            
 �  ?   �  ?  
 @	      D	     
  
     $
    
 �
     �
    
 �
     �
    
   @     @  
 �  ?   �  ?  
 (     ,    
 H塗$UH冹 H嬯L婨hH婾xH婱`�    3�3设    �   �    #      �	I嬂H+罬嬋H柳H凐(�2  H�繦柳L��    H��    �/葀	��驛�/葀驛��/衯	���L嬄L+荔A/葀	�
驛 ��
/葀��驛/衯	�驛 M嬃M嬔L+繫+芋A 驛
/葀
驛驛驛驛/葀驛	驛 驛/衯
驛驛��/葀	�
�驛 �
/葀V驛��/衯C��皿/葀�
�驛 �
/葀驛��/衯���   �   �  \ G            �      �  r        �std::_Guess_median_unchecked<float *,std::less<void> >  >@   _First  AJ        �
 >@   _Mid  AK        � >@   _Last  AP        
  AQ  
     z >�   _Pred  AY        
  D     >�    _Count  AH          / AH �      >�    _Step  AH  "        M        �  �.i.I	
 >@   _Last  AP  �       M        m  � N M        '  � N M        m  �" N M        '  �0 N M        m  �9 N M        '  丆 N N  M        �  ��/j/J

 >@   _First  AR  �     �  AR �     
 >@   _Mid  AP  �     �  AP �      M        m  
�� N M        '  �� N M        m  
�� N M        '  �� N M        m  �� N M        '  � N N" M        �  r
*i.I	
 >@   _First  AP  y     C  M        m  r
 N M        '  �� N M        m  �� N M        '  �� N M        m  �� N M        '  �� N N! M        �  2*iK		 M        m  2 N M        '  < N M        m  E N M        '  U N M        m  ` N M        '  i N N  M        �  丮)h.I	 M        m  丮 N M        '  乂 N M        m  乛 N M        '  乴 N M        m  乽 N M        '  亊 N N                        @  h   '  m  �      @  O_First     @  O_Mid     @  O_Last      �  O_Pred     _Diff  O   �   h           �  �  
   \        �     �    �"    �r    ��     �  ! �L  % �M  # ��  % �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 3     7    
 S     W    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 H塡$H塼$H墊$AVH冹 I嬂H嬺H+翴孁H柳L嬹I兝麳嬑H�侶嬘�    H峉H;髎*��     �C麳岰�/葁/羨H嬝(菻;餽釮;譻��/葁/羨	H兟H;譺镠嬍L嬎H;��'  H嬊H+罤兝H冟麳凐傏   L岮�    ��	/葁/�囶   H;裻��
�H兟驛H麵岪/葁/��9  H;衪��
� H兟�驛/葁/��  I;衪
��
驛 H兟驛HI岪�/葁/�囒   H;衪��
� H兟H兞H岹鬒兝H;��1���H;蟬0��	/葁/羨H;裻��
�H兟H兞H;蟫蠰;蝪>I岮� ��/羨/葁"H冸H;豻��� I冮H冭I;駌蘈;蝩GH;�剫   H;裻�����H兟��H兠�H兞門��H嬋閨���I嬋閠���I兞麳;蟯-H冸L;藅驛�A��婤麳冴�������A��H兞驛轺��H媡$8I嬈H媩$@I�H媆$0I塚H兡 A^�7         �   �  i G            �     �  Y        �std::_Partition_by_median_guess_unchecked<float *,std::less<void> >  >@   _First  AK          AL       � >@   _Last  AM  !     � AP        !  >�   _Pred  AY        ;  >@    _Plast  AK  ?     ~
 >@    _Mid  AI  3       AI P     b  � �
 
)  >@    _Pfirst  A�   i       AI  f       }�< � * A�  P     m  '  x � L( �3 � 
)  AI �     R �
 �)  >@    _Glast  AQ  �    	  AQ �     )g	 �I  >@    _Gfirst  AJ  �     , M        m  ^ N M        m  P N M        m  �� N M        m  s N  M        m  ��'1-O N  M        m  ��1,2E NF M        '  ��$)$$$*$9$
 >@     _Tmp  A�   �     �  % =   j % � 5 : A�  �     � ' 9 # ` ) � # � # / [ g) �	 � �  N M        m  佇 N M        m  佪 N M        '  侂$
 >@     _Tmp  A�   �     & A�  �    �   K  W ) � 	 �  �   N M        t  偑 N M        '  �$
 >@     _Tmp  A�         A�  #      N M        '  �#
 >@     _Tmp  A�   '     . A�  �     )0 � , D/ � �) �	 � �  N M        '  俠%
 >@     _Tmp  A�   g    	  A�  p      N M        '  俻7$
 >@     _Tmp  A�   {     . A�  �     )0 � , D/ � �) �	 � �  N M        '  倖	
 >@     _Tmp  A�   �     . A�  �     )0 � , D/ � �) �	 � �  N
 Z   r                         @  h   '  m  q  s  t   8   @  O_First  @   @  O_Last  H   �  O_Pred  O �              �  �  A         ( �   * �(   + �;   - �?   0 �c   1 �n   4 ��   5 ��   8 ��   9 ��   < ��   = ��   ? ��   A ��   B ��   = ��   ? �  A �  B �  = �*  ? �3  A �8  B �E  = �\  ? �e  A �j  B �v  E ��  < ��  = ��  ? ��  A ��  B ��  < ��  I ��  K ��  M ��  O ��  P ��  I �  T �  Y �  Z �#  ^ �'  ] �+  ^ �/  _ �3  ^ �7  ` �@  I �H  ? �P  a �Y  b �b  c �p  f ��  g ��  h ��  i ��  h ��  k ��  l ��  U ��  l ��  U ��  l �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 0     4    
 @     D    
 r     v    
 �     �    
 �     �    
 �     �    
 �     �    
          
 1     5    
 I     M    
 i     m    
          
          
 �     �    
 �     �    
 �     �    
 �     �    
 L     P    
 \     `    
 �     �    
 �     �    
          
       $    
 �     �    
 H塡$H塴$H塼$WAVAWH冹@H嬄A顿H+罥嬸H冟麳嬯H孂H=�   帊   D  H咑幰   D端H峀$ L嬇H嬜�    L媩$ H嬑L媡$(I嬊H六H+荋瑶H冟麳馜端H嬐L嬈I+蜨冡麳;羮I嬜H嬒�    I孇�H嬚I嬑�    I嬶H嬇H+荋冟麳=�   弜���H;��  L峸I嬣L;��  )t$0�   D  �3H嬎�/�啩  L嬈H嬜I嬑�    �7榫  L嬚L+譏龙M嬄I养M吚帹   M岼�I样D  驜L圏I�菼嬋I嬓M;羮3fff�     H�3殷D�/D�柭H�翲袐棄廐嬍I;褆豂;蓇A雎uB婦楛�廔岼�L;羮*@ �     H岮�H养��/葀
�廐嬋L;纜怏廙吚廳���I凓�	  �   L峌麳+黧A
M嬄�L+荌柳3�3褹�M岺�I样M吷~2ff�     H�3殷D�/D�柭H�翲袐棄廐嬍I;褆豂;蓇A隼uB婦圏�廔岺�H吷~*@ �     H岮�H养��/葀
�廐嬋H吚釯冴�廕�H冟麳凐岻���隓H岶麳求 /苬D  �H嬋�@麳冭/苭塍1H兠H兤H;����(t$0H媆$`H媗$hH媡$pH兡@A_A^_肶      �      �              �   .	  T G                 �  0        �std::_Sort_unchecked<float *,std::less<void> >  >@   _First  AJ        /  AM  /     � >@   _Last  AK        ,  AN  ,     � >    _Ideal  AL  %     �R  � 0  AP        %  AL �      >�   _Pred  A        �� C  AY          A  �     
 >S   _Mid  CW      b     f  CV     j     ^  CW     @     �" f  CV    @     �* ^ � F kD  D    < M        V  ��	G-.仌7
 >@    _Mid  AI  �     C � AI �      >@    _Hole  AJ  �     � � AJ �      �
 >@     _Val  A�   �     �' � A�  �       >@    _Prev  AH  �    &    AH �     " �� �  M        m  �� N M        n  � M        ~  � N N M        m  偛	 N N) M        X  
佨$D.P$! M        p  侐D)P"
 >@     _Val  A�   �    �  A�  �     � % M        �  侘DP"8 M        o  �/h+(" >    _Hole  AJ      k  AJ �     k �  � D  >    _Bottom  AP  �    �    AP �     �  >�    _Max_sequence_non_leaf  AQ      �  AQ �     � 
 >    _Idx  AK        AK �      4 " � D  C       &     & M          俖'J >    _Hole  AJ  �      AJ �     k �  � D 
 >     _Idx  AH  w      AH p    +    M        m  倃 N N N N N N+ M        W  �

DP		# >     _Bottom  AR      �    AR �      >     _Hole  AP  (    �  AP �     � 2 M        o  丮
h+(# >    _Hole  AJ  M    c  AJ @    �
 c �  � �  >�    _Max_sequence_non_leaf  AQ  8    �  AQ �    ,2 � 
 >    _Idx  AK  P      AK @    �  & " � �  C       f     # M          仧'J% >    _Hole  AJ  �      AJ @    �
 c �  � � 
 >     _Idx  AH  �      AH @    �$ $ X  t  � �  M        m  伔 N N N N Z   Y  0  0   @                     @ 6 h   �  V  W  X  ]  m  n  o  p  ~    �   `   @  O_First  h   @  O_Last  p      O_Ideal  x   �  O_Pred      S  O_Mid  O  �   �             �     �       o �   r �@   w �I   ~ �]   � ��   � ��   � ��   � ��   � ��   � ��   r ��   s �  x ��  y ��  s ��  � �,      0     
 {           
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 $     (    
 8     <    
 H     L    
 k     o    
      �    
 �     �    
 �     �    
           
 0     4    
 P     T    
 d     h    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 O     S    
 _     c    
 �     �    
 �     �    
 �     �    
 �     �    
 	     
    
          
 9     =    
 �     �    
 �     �    
 �     �    
 �     �    
 M     Q    
 a     e    
 �     �    
 �     �    
 �     �    
 �     �    
 6     :    
 F     J    
 i     m    
 y     }    
 �     �    
 �     �    
 �     �    
          
 )     -    
 D	     H	    
 H塡$H塼$H塋$WH冹 I孁H嬞3鰤1A�J�I婤堿H茿   �?H塹(茿0   茿4   茿8  �?H茿<   塹DH塹HH塹PH塹XH塹`H塹hH塹pH塹xH壉�   婣疉疉疉墎�   �    H嬎�    婯吷t+冮t凒u!婼@H嬒�    ��嫇�   H嬒�    ���3H嬅H媆$8H媡$@H兡 _脷   �    �   �    �   �    �   �       �   �  G G            �      �   �        �rtxdi::ReGIRContext::ReGIRContext 
 >�   this  AI       �  AJ          D0    >u   params  AK        � " >�   risBufferSegmentAllocator  AM       �  AP         " M        �  ��	 Z   6  6   N M        �  �� N M        �  m M        �  m M        �  m N N N M        �  a M        �  a M        �  a N N N Z   �  �                        @ > h   �  �  �  �  �  �  �  �  �  �  �  �  �  �   0   �  Othis  8   u  Oparams & @   �  OrisBufferSegmentAllocator  O �   `           �   �  	   T       $  �   "  �   #  �a   $  ��   %  ��   &  ��   '  ��   (  ��   )  ��   �   V F                                �`rtxdi::ReGIRContext::ReGIRContext'::`1'::dtor$0 
 >�   this  EN  0                                  �  O   ,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
    
   $  
  
 s  
   w  
  
 H媻0   H兞@�       �    H塡$H塴$H塼$ H塋$WAVAWH冹 L嬺H嬹��婤堿3鞨塱H塱H塱H婮H+JI揩*I嬊H鏖H龙H嬄H凌?H�劘   H窾UUUUUUH;�噚  H�RH零H侞   r,H岾'H;�哷  �    H嬋H吚刄  H兝'H冟郒塇H呟t
H嬎�    �H嬇H塅H塅H�H塏H媬I媈I媀H+贚嬅H嬒�    I嬊H麟H龙H嬄H凌?H蠬�RH拎H荋塅H塶 H塶(H塶0I媈(I+^ H聋H呟剫   H�������H;�嚭   H零H侞   r%H岾'H;�啨   �    H吚t~H峢'H冨郒塃H呟tH嬎�    H嬭H塶 H塶(H�+H塅0H媬 I媈(I媀 H+贚嬅H嬒�    H冦餒�;H塅(A婩8塅8A婩<塅<H嬈H媆$HH媗$PH媡$XH兡 A_A^_描    愯    惕    惕    愯    惕    虣   �    �   �    �      e  �    �  �    �     �  �    �  �    �  �    �  �      �      �       �   �	  k G                   �        �rtxdi::ReGIROnionCalculatedParameters::ReGIROnionCalculatedParameters 
 >b   this  AJ        #  AL  #     ��
  D@    >i   __that  AK           AV        �� # M        �  �L���� >�    _Count  AI  &    �   ( �  AI �     % M        �  �.)&eP& M        �  �7R��
 Z   �  & M        �  丣B$X >Y   _Newvec  AN �    Q  C       /     �  C      �     Y� 
 &  M          B丣�� M          B丣��& M        K  丯)
%
k, M        k  乄$	%%v	 Z   3  �   >�    _Block_size  AJ  [    �  �  >�    _Ptr_container  AH  i    �  p  AH �     
 >�    _Ptr  AN  v      AN �    Q  M        �  乨
 Z   �   N N M        �  亖
 Z   �   N N M        J  丣 N N N N N M        �  仱$ >�   _First  AK  �      >�   _Last  AI  �      >�   _Dest  AH  �      AM  �      AH �      M        ?  仺 >�    _Count  AI  �      N N N M        �  � M        �  � N N N" M        �  -N��&亼% M        �  a&=r�% M        �  g_亁
 Z   �  & M        �  zO$�' >�    _Newvec  AH  �       AH �     *  M        
  Oz亊 M          Oz亊) M        K  ��)
,%
�2- M        k  ��$	()
丟 Z   �  3   >�    _Block_size  AJ  �     n [ >�    _Ptr_container  AJ  �     c G AJ �      
 >�    _Ptr  AH  �       AH �     *  M        �  ��
 Z   �   N N M        �  ��
 Z   �   N N M        L  z N N N N N M          ��$ >|   _First  AK  �       >|   _Last  AI  �       >|   _Dest  AH      /  AM  �     5  AH �      M        H  �� >�    _Count  AI  �     >  N N N M        �  - M        �  - N N N                      0@ � h;   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �            
      
            9  ?  @  B  H  I  J  K  L  d  j  k  z  }         $LN154  @   b  Othis  H   i  O__that  O �   �   z F                                �`rtxdi::ReGIROnionCalculatedParameters::ReGIROnionCalculatedParameters'::`1'::dtor$0 
 >b   this  EN  @                                  �  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 '  �    +  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
   �      �   
   �      �   
 :  �    >  �   
 J  �    N  �   
   �      �   
 '  �    +  �   
 G  �    K  �   
 W  �    [  �   
 g  �    k  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 I  �    M  �   
 �	  .   �	  .  
 
     
    
 
     �
    
 H媻@   H兞�       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   #  ? G            <      6           �std::bad_alloc::bad_alloc 
 >   this  AI  	     2  AJ        	  >   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0     Othis  8     O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   =  U G            <      6           �std::bad_array_new_length::bad_array_new_length 
 >%   this  AI  	     2  AJ        	  >*   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �     0   %  Othis  8   *  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >%   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      %  Othis  O   �   8           !   X     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �       �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   X     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 @SH冹 H嬞H�	H吷t>H婼H+袶冣麳侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �   �  v G            [      [   �        �std::vector<float,std::allocator<float> >::~vector<float,std::allocator<float> > 
 >{   this  AI  	     R K   AJ        	 $ M        �  	h1%	
 M        �  *= M        �  )
 Z   {  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        }  
%#

 Z   3   >�    _Ptr_container  AP  )     1    AP =       >�    _Back_shift  AJ       N 1   AJ =       
  N N N N                       H� & h   �  }  ~  �  �  �  �  �         $LN30  0   {  Othis  O  �   8           [   �      ,       � �	   � �O    �U   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 8  �    <  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 n  8   r  8  
 �  �    �  �   
 �       �       �     � G                       �        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::~vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O�   (              �             � �    � �,   �    0   �   
 �   �    �   �   
 ,  �    0  �   
 @SH冹 H嬞H婭 H吷t?H婼0H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁 H塁(H塁0H岾H兡 [�    �    �?   �    [   �    `   �       �   �  l G            e      e   �        �rtxdi::ReGIROnionCalculatedParameters::~ReGIROnionCalculatedParameters 
 >b   this  AI  	     \ Q   AJ        	  M        �  Z N M        �  H	V$ M        �  	i1&	 M        �  *F M        �  )!
 Z   {  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        }  
&#
$
 Z   3   >�    _Ptr_container  AP  *     :  !  AP >       >�    _Back_shift  AJ  
     W 1 !  AJ >         N N N N N                       @� . h
   �  }  ~  �  �  �  �  �  �  �         $LN35  0   b  Othis  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 /  �    3  �   
 �  ,   �  ,  
 H�    H�H兞�       �      �       �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >%   this  AJ          M        �   	
 N                        H�  h   �  �      %  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       �      �       �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   �                          H�     �  Othis  O  �   (              X            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >   this  AJ          AM       -  M        �  

	
 Z   �   N                       @�  h   �  �   0     Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >%   this  AJ          AM       -  M        �  

	
 Z   �   N                       @�  h   �  �  �   0   %  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   �   N                       @� 
 h   �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 @SH冹 D婣L嬍H嬞E吚t5A冭tA凐u)婹@I嬌�    �H兡 [脣憖   I嬌�    �H兡 [们    H兡 [�(   �    >   �       �   E  S G            V      P   �        �rtxdi::ReGIRContext::AllocateRISBufferSegment 
 >�   this  AI       E #  9   AJ         " >�   risBufferSegmentAllocator  AK        
  AQ  
     I   5   Z   6  6                         H  0   �  Othis & 8   �  OrisBufferSegmentAllocator  O   �   H           V   �     <       4  �   5  �!   ?  �,   A  �4   <  �B   A  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 \  �    `  �   
 婣疉疉疉墎�   �   �   �   T G                      �        �rtxdi::ReGIRContext::ComputeGridLightSlotCount 
 >�   this  AJ                                 H     �  Othis  O  �   0              �     $       ,  �    -  �   1  �,   �    0   �   
 y   �    }   �   
 �   �    �   �   
 H嬆H塇USVWATAUAVAWH峢圚侅8  )p�)x楧)@圖)坸���D)恏���D)榅���D)燞���D)�8���D)�(���D)����L嬦W荔D$H3�H墊$8H墊$X�D$`E3鞮塴$0L塴$p3缐厛   H婭PH塎�M媩$HL墊$@W�L媡$PH媡$hL;��%  驞
    f�     E3鞥9o庣   驟$fAn�[葾(氰    驛Y驞Y鳨(黧DX痼EY袂D$     E媑3跠(譋呬幁  驟G 驞厴   Ic(鱄媴�   H婡`H塂$xE(梵EY�(氰    D(囿EY骟Dd$((氰    驛Y企厫   H羚L媩$x恌n�[�驛Y鴧踰E(蓦
D(唧E\軩(煮E?(氰    D(润EY藺(黎    (痼AY鰽(描    �Y痼D\�(氰    D(囿EY鍭(描    驛Y求D\�(氰    (Y綈   A(黎    (痼AY鰽(描    �Y痼\EY潴EY审EX狍Y�驞X�W繟.膚
W荔AQ碾	A(蔫    �D$$H岲$$H峀$ A/翲F馏D驞T$ �肏兦A(駻;荏D厴   驞d$(屜��W�驞
    L媩$@H媩$8L嫢�   I婰$PI+L$HH斧*H鏖H龙H嬄H凌?H��9厛   }X�
    A(畦    驛Y麦厫   L;鱰驛I兤L塼$P雖L崊�   I嬛H峀$H�    H媩$XH墊$8L媡$P際驟^煮D晲   H;t$0t驞H兤H塼$h�#L崊�   H嬛H峀$`�    H婦$pH塂$0H媡$hA�臙;o�&��媴�   H婱��缐厛   I兦0L墊$@L;�咈��L媗$0H媩$HI;%I嬣H+逪聋E3蒐嬅I嬛H嬒�    H央�熾(求AD$xH婰$`H;蝨H嬃�    �X8H兝H;苪篌
    菂�     �?H+馠窿W繦咑x驢*齐H嬈H谚冩H企H*荔X荔厛   H崊�   H崟�   /菻F麦^8驛|$|H吷t:L+镮笼J��    H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚈   �    怘�t5H婽$8H+譎冣麳嬊H侜   rH兟'H�鳫+荋兝鳫凐w[H嬒�    L崪$8  A(s鐰({谽(C菶(K窫(S‥([楨(c圗(玿���E(砲���E(籜���I嬨A_A^A]A\_^[]描    愯    惷   �   �      ]     u     �     �     �     �               )     :     v     �  �     �        P  �    �  �         A  �   �  �      �    o  �    u  �       �     R G            z  c   z  �        �rtxdi::ReGIRContext::ComputeOnionJitterCurve 
 >�   this  AH  G      AJ          AT  f     � �  B�       H >t     layerGroupIndex  A   �     �P �x
  A  �     � F�  B�  �     � >�   cubicRootFactors  CV     �     �  i�%  CV    �     �i � �	  DH    >�    linearFactors  D`    >|    <begin>$L0  AW  �     �� C�  B@   �     � >|    <end>$L0  AJ  �     �F �i  AJ �       B�   �     � >t     layerIndex  Am  �      Am �       >�    innerRadius  A�   �     `  A�  �     E" �IL � 8
  >�    middleRadius  A      � A �     �5 ��  >@     maxCellRadius  D     >�    outerRadius  A       � A  �     � ��  >t     ringIndex  A       � A  �     �M � " �  >�    middleElevation  A�   �    �  A  Y    7  A �      >�    cellRadius  D$    >�    vertexAzimuth  A  �    �  >�    vertexElevation  A  �        A �    �  A �     ��  q-  >@     cubicRootFactor  B�  �     �� Q�:  >@     linearFactor  A  j    	  B�  �     �� Q[:  M        �  { M        �  { M        �  { N N N M        �  i
5 M        �  i
5 M        �  i N N N M        �  
個 N! M        �  佽$6	
 >   d  C      �    c  C         :  C�      F      C     K    
  N M        �  伿4& N  M        �  乊H4$ N M        �  ,傋 N M        �  )�+ M        �  
�+%
 Z   �   M        �  �0	 M        3  �0 N N N N M        �  :僺 M        �  
僺'#
 Z   �   M        �  儂	 M        3  儂 N N N N M        �  冺 M        �  冺
 Z   0   N N M        �  :勧�� M        �  勧5�� M        �  -勵�� M        �  匁)[
 Z   {  
 >   _Ptr  AH  �      AM  �    �
  AH       AM     O  >#   	 _Bytes  B8   v      AK  �    �   0 S  AM  �     m � C       q     _  C      �     m ��  M        }  匎d
e
 Z   3   >�    _Ptr_container  AH  	      AM        N N N N N M        �  >劉�� M        �  劉9�� M        �  2劗�� >�  	 _Count  B0   �     � AH  �    
  AU  �    ��  �  AH �     � � Cm      �     L  Cm     �     � �7 �   M        �  劰)��
 Z   {  
 >   _Ptr  AH  �      AJ  !    �  AH �      AJ �    � 5   >#    _Bytes  AK  �    � * �  M        }  劼d��
 Z   3   >�    _Ptr_container  AH  �      AJ  �      N N N N N M        �  剛 N M        �  凮 N M        �  
�
 M        �  

�	
 >@    _Val  A�   4    	  A�  0    k  	  >@    _UFirst  AH  )      AH =    L +   N N M        �  � M        �  � N N 8          @         @ � h.   �  �  }  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �    .  2  3  Z  [         $LN193  �  �  Othis  H   �  OcubicRootFactors  `   �  OlinearFactors      @   OmaxCellRadius  $   �  OcellRadius  �  @   OcubicRootFactor  �  @   OlinearFactor  O   �   �          z  �  2   �      �  �i   �  �{   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �  �  �  �  �  �  �  �  �!  �  �*  �  �Y  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �$  �  �B  �  ��  �  ��  �  ��  �  ��  �  ��  �  �
  �  �+  �  �T  �  �e  �  �s  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �  �  �  �  �  �  �=  �  ��  �  ��   5  a F                                �`rtxdi::ReGIRContext::ComputeOnionJitterCurve'::`1'::dtor$0  >�    cubicRootFactors  EN  H           >�    linearFactors  EN  `           >@     maxCellRadius  EN              >�    cellRadius  EN  $                                  �  O   �   5  a F                                �`rtxdi::ReGIRContext::ComputeOnionJitterCurve'::`1'::dtor$1  >�    cubicRootFactors  EN  H           >�    linearFactors  EN  `           >@     maxCellRadius  EN              >�    cellRadius  EN  $                                  �  O   ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 	  �    
  �   
 8  �    <  �   
 T  �    X  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
   �      �   
 5  �    9  �   
 E  �    I  �   
 k  �    o  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
   �      �   
    �    $  �   
 L  �    P  �   
 \  �    `  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 !  �    %  �   
 5  �    9  �   
 E  �    I  �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 (  �    ,  �   
 8  �    <  �   
 L  �    P  �   
 \  �    `  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 -	  �    1	  �   
 =	  �    A	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �     
  �   
 
  �    
  �   
 (
  �    ,
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 /  �    3  �   
 ?  �    C  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 '  �    +  �   
 O
  3   S
  3  
 (  �    ,  �   
 �     �    
 b     f    
 �     �    
 �     �    
 �     �    
 8     <    
 �     �    
 �     �    
 �     �    
 #     '    
 H崐H   �       �    H崐`   �       �    ��   �   �   M G                      �        �rtxdi::ReGIRContext::GetReGIRCellOffset 
 >�   this  AJ                                 @     �  Othis  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 r   �    v   �   
 �   �    �   �   
 A H嬄I0J�   �   �   T G                      �        �rtxdi::ReGIRContext::GetReGIRDynamicParameters 
 >�   this  AJ                                 @     �  Othis  O  �   0              �     $       �  �    �  �   �  �,   �    0   �   
 y   �    }   �   
 �   �    �   �   
 H媮�   H�H嬄�   �   �   [ G                   
   �        �rtxdi::ReGIRContext::GetReGIRGridCalculatedParameters 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       �  �    �  �
   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 婹呉t冴t	凓u婣@脣亐   �3烂   �   �   Q G                      �        �rtxdi::ReGIRContext::GetReGIRLightSlotCount 
 >�   this  AJ                                 @     �  Othis  O �   X              �     L       �  �    �  �   �  �   �  �   �  �   �  �   �  �   �  �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 @SH冹0H嬟H峇@H嬎�    H嬅H兡0[�   �       �   �   \ G                     �        �rtxdi::ReGIRContext::GetReGIROnionCalculatedParameters 
 >�   this  AJ         
 Z   �   0                     @  @   �  Othis  O  �   0              �     $       �  �	   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 婣A�I�J塀H嬄�   �   �   S G                      �        �rtxdi::ReGIRContext::GetReGIRStaticParameters 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       �  �    �  �   �  �,   �    0   �   
 x   �    |   �   
 �   �    �   �   
 L嬡I塖UVAUAVI峩菻侅  �   A){�9rL嬮�   E)揾���驞    D嬈DLBA�   D;翂uXA(鶧塽@AO�3缐L$ 塃P吷庉  I塠�蒊墈蠱塩萂墈繟)s‥)C圗)媥���E)沊���驞    E)���D)l$p驞-    塋$$�;羮A�   �D媧A�莊Dn艵[纅An螦(餉(荔AX躞A\�[审^�(畦    I媇hD(囿DY鏓(薍嬅荄$d    (企|$8I+E`H柳驞d$<塂$P驟^辱    嬈D塼$`檳t$4冣D�$3繟咙A�腍塂$(D塪$T塂$0A(鼠DL$XA(殷t$\�^華(荔A^皿L$@A(鼠^润A^洋L$(�T$H�D$,I;]pt��C塁塻I僂h�L岲$(H嬘I峂`�    A�   嬣E;�帓   fAn�[荔AY凌    驛Y黎    I婾h�   �,缐\$0A(�;�O饓t$4fn�[荔A^皿^润D$,�L$(I;Upt�
�B塟塺I僂h�L岲$(I峂`�    A�茘sE;�宷���媢XI婾P塡$LD墊$DI;UXt!D$8L$HD$XJB I僂P0�L岲$8I峂H�    D媢@兤婨PA(麳婾H�缷L$$A塃P塽XD驞塽@;D$ 屍��D(l$pD(�$�   D(�$�   D(�$�   D(�$�   (�$�   L嫾$�   L嫟$   H嫾$  H嫓$  (�$�   D(�$�   E塽DE痷E塽@H伳  A^A]^]�:   �   �   �   �   �   �      @     �                o      �        �   `  J G            x  5   L  �        �rtxdi::ReGIRContext::InitializeOnion 
 >�   this  AJ        (  AU  (     M >u   params  AK        x� DH   >@     innerRadius  A�   V     � >t     totalCells  An  L     ��  B@  Z      >t     numLayerGroups  A   -     H  A  L    ,  B    d      >t     layerGroupIndex  A   `     � � BP  g      >�    equatorialAngle  A  ?    � A �     S 
 >�   ring  C�      �    � R W  C�      4      C          [  C�     �    �  { �   C     �    � % [  D(    >�    layerCount  Ao  �     j   Ao �      
   >t     cellsPerLayer  A   �    �  >�    radiusRatio  A�   �     2 A�  �     %  >�    outerRadius  A      � A �     J  >�   layerGroup  Cl     `    
  D8    >t     ringIndex  An  �    � % M        �  
 N M        �  -伒 M        �  
伒&
 Z   �   M        �  伝 M        8  伝 N N N N M        �  � N M        �  倕5
 M        �  
倕
&!
 Z      M           倶 M        A  倶
 >   _Obj  AK  �    >  AK �      N N N N M        �  �*: M        �  
�:&
 Z   �   M        �  侽 M        8  侽 N N N N M        �  � N                      @ R h   �  �  �  �  �  �  �  �  �  �         8  A  _  `  e  f   @  �  Othis  H  u  Oparams  (   �  Oring  8   �  OlayerGroup  O�   �          x  �  7   �      D  �   E  �   D  �"   E  �%   D  �(   E  �-   D  �5   G  �>   E  �F   J  �L   E  �R   G  �V   J  �Z   E  �g   L  ��   O  ��   Q  ��   R  �  V  �  R  �  S  �  V  �  U  �  Y  �'  V  �/  X  �?  Y  �D  \  �F  ^  �L  a  �j  b  ��  c  ��  d  ��  e  ��  g  ��  h  ��  j  �  n  �  j  �  k  �   l  �$  j  �1  k  �4  l  �C  m  �I  n  �s  h  �v  p  ��  u  ��  s  ��  t  ��  u  ��  y  ��  L  �L    �,   �    0   �   
 o   �    s   �   
    �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 4  �    8  �   
 D  �    H  �   
 T  �    X  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 /  �    3  �   
 K  �    O  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 Q  �    U  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 t  �    x  �   
 儁4t	儁0t2烂��   �   �   U G                      �        �rtxdi::ReGIRContext::IsLocalLightPowerRISEnable 
 >�   this  AJ                                 @     �  Othis  O �   @              �     4        �     �    �    �    �,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 A JI0�   �   �   O G                      �        �rtxdi::ReGIRContext::SetDynamicParameters 
 >�   this  AJ          >�   regirDynamicParameters  AK                                 @     �  Othis #    �  OregirDynamicParameters  O �   0              �     $       �  �    �  �   �  �,   �    0   �   
 t   �    x   �   
 �   �    �   �   
   �      �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       �            �   �   F G                       �        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �       �      
 �   �    �   �   
 @SH冹 H嬞H�	H吷t]H婼H斧*H+袶麝H龙H嬄H凌?H蠬�RH菱H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    蘛   �    u   �       �   T  v G            z      z   �        �std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Tidy 
 >�   this  AI  	     q j   AJ        	  M        �  .3A M        �  ;)
 Z   {  
 >   _Ptr  AJ \       >#    _Bytes  AK  ;     > &  " M        }  
D#

 Z   3   >�    _Ptr_container  AP  H     1    AP \       >�    _Back_shift  AJ       m P   AJ \       
  N N N                       @� " h   �  }  ~  �  �  �  �         $LN27  0   �  Othis  O�   X           z   �      L       � �	    �    �a   	 �f   
 �j    �n   
 �t    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 3  �    7  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 <  $   @  $  
 h  �    l  �   
 H冹(H�
    �    �   �      �       �   �   Y G                             坰td::vector<float,std::allocator<float> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   :   �   :  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   y G                     �        坰td::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   &   �   &  
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   �   m G                     �        坰td::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Xlength 
 Z   �   (                      @        $LN3  O   �   (              �             a �   b �,   �    0   �   
 �   *   �   *  
 �   �    �   �   
 H冹(H嬄J��    H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   O  G G            B      B   �        �std::allocator<float>::deallocate 
 ><   this  AJ          AJ 0       D0   
 >�   _Ptr  AK          >�   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        }  
#

 Z   3   >�    _Ptr_container  AJ       %    AJ 0       >�    _Back_shift  AH          AH 0       N N (                      H  h   �  }  �         $LN20  0   <  Othis  8   �  O_Ptr  @   �  O_Count  O �   8           B   �      ,       � �   � �3   � �7   � �,   �    0   �   
 l   �    p   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    	  �   
 &  �    *  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   6     6  
 d  �    h  �   
 H冹(H嬄K�@H菱H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �8   �    =   �       �   _  W G            B      B   �        �std::allocator<ReGIR_OnionLayerGroup>::deallocate 
 >�   this  AJ          AJ 0       D0   
 >�   _Ptr  AK          >�   _Count  AP        A   M        �  )
 >   _Ptr  AH 0       >#    _Bytes  AK       2 " M        }  
#

 Z   3   >�    _Ptr_container  AJ       %    AJ 0       >�    _Back_shift  AH          AH 0       N N (                      H  h   �  }  �         $LN20  0   �  Othis  8   �  O_Ptr  @   �  O_Count  O �   8           B   �      ,       � �   � �3   � �7   � �,   �    0   �   
 |   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 6  �    :  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   "   #  "  
 t  �    x  �   
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7   �    <   �       �   ]  Q G            A      A   �        �std::allocator<ReGIR_OnionRing>::deallocate 
 >m   this  AJ          AJ ,       D0   
 >Y   _Ptr  AK        @ /   >�   _Count  AP           M        �  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        }  
#

 Z   3   >�    _Ptr_container  AJ       (    AJ ,       >�    _Back_shift  AH         AH ,       N N (                      H  h   �  }  �         $LN20  0   m  Othis  8   Y  O_Ptr  @   �  O_Count  O   �   8           A   �      ,       � �   � �2   � �6   � �,   �    0   �   
 v   �    z   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 4  �    8  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   (   !  (  
 t  �    x  �   
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              X     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  20    2                       N   
 
4 
2p    B                       T    20    <                       Z   
 
4 
2p    B                       `    20    <                       f   
 
4 
2p    B                       l    �                  !      !      r    B      B           #      #      x    20    z           %      %      ~    B                 '      '      �    B      A           )      )      �    B                 +      +      �    20    e           -      -      �   
 d T
 4	 2��p                 �                 /      /      �   (           �      �             a �� h d 4 2p                 �       �           0      0      �   (           �      �          
   e�  R0               1      1      �   5
 5�
 "x
 # 
��	`P    o           2      2      �   !C C� =� ,�	 $� � h � �  
t! 4"     o       ,   2   0   2   4   �   o   L          2      2      �   !       o          2      2      �   L  x          2      2      �   c c�	 [�
 S� K� C�
 ;� 3� +� &x "h ' ���
�p
`	0P        @      D   �       z          4      4      �   (           �      �   
    �2    �   �       �    =
M 20    V           5      5      �    B      B           7      7      �    20    [           9      9      �    B                 ;      ;      �    2����
p`0                 �       �          >      >      �   8                        	                  �       
   � �� 
 
2P    (           
      
           2����
p`0                        �          A      A         8                      #   	   )            &   �          � �� 
 
2P    (                       ,     2����
p`0                 ;       #          D      D      5   8               >      A   	   G            D   �          � �� 
 
2P    (                       J    
 d T
 4 r��p    �           E      E      S   ! h     �          E      E      S   �             E      E      Y   !       �          E      E      S     �          E      E      _   !   h     �          E      E      S   �  �          E      E      e   !       �          E      E      S   �            E      E      k   
 
4 
2p    0           F      F      q   
 
4 
2p    0           G      G      w    t d 4 2�    �          H      H      }   
 
4 
2p    0           I      I      �    B      :           K      K      �                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                    .?AVbad_alloc@std@@     �              ����                      �      �                    .?AVexception@std@@     �               ����                      �      �    vector too long                                       �      �      �                         �                   �               ����    @                   �      �                                         �      �      �                         �                           �      �              ����    @                   �      �                                         �      �      �                         �                                   �      �      �              ����    @                   �      �      ?  �?�I@�葽   �   (   & 
Z        std::exception::`vftable'    �      �  
    �   (   & 
Z        std::bad_alloc::`vftable'    �      �  
    �   3   1 
Z        std::bad_array_new_length::`vftable'     �      �  
 坂晗i�靁��#p\溚h鈊&连0h�K蜌�(�}S�艳"嘇�%kz臲摢QA"R�禶+椿+凑腁"R�尔g樮�7暠A"R�缎k趶溙G�F绣穭?z*譗8$裛s鲚溏q兗�&�6督╙�?肳m愌`s鲚溏qv諲d碜茢+Rh徰`s鲚溏qg?F鷾1秄xD�7膀<B痶泳=亙漽�"�.J
>孔]Mbk→!悪�?狄\uN/蠲BC�1鹥�圢紟�8~�	�(蛁�<C轁棕裔砇妱隂S嚤踖p禭Qb希G袊臂ep禭兣o�=疁6萪O�耝8蘹W誎霵婬(�0-H皘趁7陘�
羟�.桦�'洋m|Χ�q褎�;}溉翜歃埛�2鵴嶀預棊膬:eX6K#犷A棊膬副heiCT侣徇�5>aL剾[挪惵徇�5> �%�=炭箩邆5>R琋t鮚�稽�貵蓽H
 U蕋殿蒘歖S┌,uD"0氆忻ah椡�8N茭瘡j'=$;4�B緤`QI蚶� MI矱�魋2I嫔藲僱頟P�
5'�x氪袣撕V渲�)决M
殬z?垽�勯�,浠兵覽砡廸厄#�V蜈訵網T驌n>YJoH昸鍻瓾uH羍td邌wt+柶,瀉t葠X掇寗萳�W埣�_嘊%I栶賑?T
zW嵫�Ｗ鲴o帵SH翆釽f]{謑pS乨靛=闦f]{謑pゆ蟰�d醘]{謑p豝~衜k容X墺鉪d朿鉻&糗鱒酕嵛奕P{M鏞鈎鮧�槉�8兙﹣nN鵘J庾賖脎Kv入嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄酲;[純o穗嘕-WV8o嗹啴r殅W-坓�(鬄�汬'这�-坓�(鬄鮳�>i,夿-坓�(鬄�汬'这栯嘕-WV8o.*~襠[
B���>H3*臄褼搢dd�a�:_棢杻#Q�賴矖蚳憎X觨椗
a�>20dd�a�:_棢杻#Q8�,惝�烗餯繅鬮�摮Dk.,;U��%�L扟�9竮e{E佋~y�)^肶8vIⅰ婗-~�(	"鳫h遙=�C�H�$�dd�a�:j�=�蓈痔r�=觱釄氹嘕-WV8oW 9�~&-坓�(鬄酲;[純o穗嘕-WV8om�M%>mb-坓�(鬄�汬'这�)�8蛾爨lVGd匱韣y*�杜`颀l+�鞯.r擣�0G#盱谑﹝j�り"(��苳乮5絚_}4n4�硓�)�8蛾爨昉}s畏巠*�杜`颀l+�鞯.r擣�0G#盱谑j啣�q鰱(��苳乮5絚_}4n4�硓�)�8蛾爨鏷蹘洦昖y*�杜`颀l+�鞯.r擣�0G#盱谑坦�褮u(��苳乮5絚_}4n4�硓榌<�<騙螀Q5洪m!悞⌒��*嚮oG�`?烹|W\-0i�! T坿弰囚�<毘罿�*埗玚?烹|W\�]�)�7~9E\$L釉��E光9E\$L釉��E光5�蘫� 輽� �9E\$L釉��E光-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� 贋XCRC冼�^笵A傮:塪逮0[豍&��1�揰煺匵注徔MB叕xsW論7":��        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       軀                .debug$T       h                 .text$mn       :      眡�     .debug$S                    .text$mn             補胑     .debug$S       �             .text$mn       0      燥"V     .debug$S    	   �             .text$mn    
   0      燥"V     .debug$S       �         
    .text$mn       0      燥"V     .debug$S    
   �             .text$mn       �     桒�     .debug$S       �  f           .text$x        (      镽=    .text$mn       #     碡(G     .debug$S       $  `           .text$x        (      镽=    .text$mn       �     z-�     .debug$S       X  f           .text$x        (      镽=    .text$mn       �      W�     .debug$S                     .text$mn       �     G橞�     .debug$S       �  >           .text$mn            兠J�     .debug$S       �	  l           .text$mn       �      搷壖     .debug$S       �             .text$x              }�+c    .text$mn             绩�     .debug$S    !   �
  J            .text$x     "         ��:     .text$mn    #   <      .ズ     .debug$S    $   0  
       #    .text$mn    %   <      .ズ     .debug$S    &   L  
       %    .text$mn    '   !      :著�     .debug$S    (   <         '    .text$mn    )   2      X于     .debug$S    *   <         )    .text$mn    +   [      穥�     .debug$S    ,   �         +    .text$mn    -         �%     .debug$S    .   T         -    .text$mn    /   e      凚Kr     .debug$S    0   �         /    .text$mn    1         ��#     .debug$S    2   �          1    .text$mn    3         ��#     .debug$S    4   �          3    .text$mn    5   B      贘S     .debug$S    6             5    .text$mn    7   B      贘S     .debug$S    8            7    .text$mn    9   B      贘S     .debug$S    :   �          9    .text$mn    ;   V      湴�-     .debug$S    <   �         ;    .text$mn    =          罂橌     .debug$S    >   �          =    .text$mn    ?   z     <b喻     .debug$S    @   P  �       ?    .text$x     A         �$�;?    .text$x     B         T�?    .text$mn    C          *V�     .debug$S    D   �          C    .text$mn    E          Ю嫀     .debug$S    F   �          E    .text$mn    G          x�:�     .debug$S    H   �          G    .text$mn    I          彘|T     .debug$S    J            I    .text$mn    K         c枎]     .debug$S    L            K    .text$mn    M          fi:/     .debug$S    N   �          M    .text$mn    O   x  
   嶬踨     .debug$S    P   D  >       O    .text$mn    Q          SPG     .debug$S    R             Q    .text$mn    S          J飱K     .debug$S    T   @         S    .text$mn    U          aJ鄔     .debug$S    V   �          U    .text$mn    W   z      �
     .debug$S    X   �         W    .text$mn    Y         �ッ     .debug$S    Z   �          Y    .text$mn    [         �ッ     .debug$S    \   �          [    .text$mn    ]         �ッ     .debug$S    ^   �          ]    .text$mn    _   B      鸮     .debug$S    `   �         _    .text$mn    a   B      臣6     .debug$S    b   �         a    .text$mn    c   A      俙Z%     .debug$S    d   �         c    .text$mn    e         崪覩     .debug$S    f   �          e                                        #                F                [                s       )        �       3        �       e        �       9        �           i�                          #        -      5        L          i�                    k      '        �      1        �      %        �      7                  i�                    5      U        ]               |      a        �      -        4      W        �      [        �      c        F      ]        �      /        �                             `      Q        �      C        �      I        �      G        ]      K        �      E              M        a      S        �      O        �      ?        .      =        e      ;        �               �      _        #	      +        R	      Y        �	              �	              t
              '              l              �      
                       �              �              
              [
              �
              �
              �              c              �      "              A        X      B        �               �           cosf             floorf           logf             memmove          powf             sinf             sqrtf            $LN5        )    $LN10       9    $LN7        #    $LN13       5    $LN10       %    $LN16       7    $LN3        U    $LN4        U    $LN20   B   a    $LN23       a    $LN27   z   W    $LN30       W    $LN3       [    $LN4        [    $LN20   A   c    $LN23       c    $LN3       ]    $LN4        ]    $LN35   e   /    $LN38       /    $LN154         $LN161           $LN36           $LN7        K    $LN75       O    $LN193  z  ?    $LN198      ?    $LN13       ;    $LN20   B   _    $LN23       _    $LN30   [   +    $LN33       +    $LN3       Y    $LN4        Y    $LN145  �          �  
       $LN150          $LN145  �            
       $LN150          $LN145  #          �  
       $LN150          $LN146          $LN4            $LN4        
    $LN152          $LN4            $LN14   :       $LN17           .xdata      g          （亵)        �      g    .pdata      h          T枨)        �      h    .xdata      i          %蚘%9        �      i    .pdata      j         惻竗9        �      j    .xdata      k          （亵#              k    .pdata      l         2Fb�#        H      l    .xdata      m          %蚘%5        p      m    .pdata      n         惻竗5        �      n    .xdata      o          （亵%        �      o    .pdata      p         2Fb�%        �      p    .xdata      q          %蚘%7        $      q    .pdata      r         惻竗7        V      r    .xdata      s          懐j濽        �      s    .pdata      t         Vbv鵘        �      t    .xdata      u          �9�a        �      u    .pdata      v         惻竗a        I      v    .xdata      w          （亵W        �      w    .pdata      x         X崘=W              x    .xdata      y          �9�[        |      y    .pdata      z         �1癧        �      z    .xdata      {          �9�c        O      {    .pdata      |         s�7錭        �      |    .xdata      }          �9�]        �      }    .pdata      ~         �1癩        Z      ~    .xdata                （亵/        �          .pdata      �         弋�/        �      �    .xdata      �          (臵         (      �    .pdata      �          $�         h      �    .xdata      �   	      � )9         �      �    .xdata      �         j         �      �    .xdata      �          嵑釄         1      �    .xdata      �         鉪        s      �    .pdata      �         �0�        �      �    .xdata      �   	      � )9        8      �    .xdata      �         j        �      �    .xdata      �          \{j              �    .xdata      �          僣糑        m      �    .pdata      �         #1iK        �      �    .xdata      �          >�5擮        @      �    .pdata      �         菜	O        �      �    .xdata      �   8      G架^O        �      �    .pdata      �         F>O        3      �    .xdata      �         鸛湴O        �      �    .pdata      �         �肘O        �      �    .xdata      �   H      �?        +      �    .pdata      �         �ⅷo?        h      �    .xdata      �   	      � )9?        �      �    .xdata      �   
      輴�?        �      �    .xdata      �          |鑒�?        (       �    .xdata      �          （亵;        g       �    .pdata      �         A鶬�;        �       �    .xdata      �          �9�_        "!      �    .pdata      �         惻竗_        W!      �    .xdata      �          （亵+        �!      �    .pdata      �         愶L+        �!      �    .xdata      �          �9�Y        �!      �    .pdata      �         �1癥        4"      �    .xdata      �         萦[�        o"      �    .pdata      �         w瓆�        �"      �    .xdata      �   
      B>z]        $#      �    .xdata      �          �2g�        �#      �    .xdata      �         T�8        �#      �    .xdata      �         r%�        ?$      �    .xdata      �   	       \�<{        �$      �    .xdata      �          3賟P        �$      �    .pdata      �         銀�*        f%      �    .voltbl     �                  _volmd      �    .xdata      �         萦[�        �%      �    .pdata      �         惱        s&      �    .xdata      �   
      B>z]        '      �    .xdata      �          �2g�        �'      �    .xdata      �         T�8        e(      �    .xdata      �         r%�        )      �    .xdata      �   	       �8雗        �)      �    .xdata      �          3賟P        T*      �    .pdata      �         銀�*        +      �    .voltbl     �                  _volmd      �    .xdata      �         萦[�        �+      �    .pdata      �         渢f�        t,      �    .xdata      �   
      B>z]        .-      �    .xdata      �          �2g�        �-      �    .xdata      �         T�8        �.      �    .xdata      �         r%�        i/      �    .xdata      �   	       螏汖        (0      �    .xdata      �          3賟P        �0      �    .pdata      �         銀�*        �1      �    .voltbl     �                  _volmd      �    .xdata      �          Qsv        z2      �    .pdata      �         �%zO        �2      �    .xdata      �         "�0�        3      �    .pdata      �         Ni朤        a3      �    .xdata      �         �-�        �3      �    .pdata      �         羭架        �3      �    .xdata      �         帀隳        K4      �    .pdata      �         3廹�        �4      �    .xdata      �         �-�        �4      �    .pdata      �         p$`�        55      �    .xdata      �          %蚘%        �5      �    .pdata      �         }S蛥        �5      �    .xdata      �          %蚘%
        :6      �    .pdata      �         }S蛥
        �6      �    .xdata      �          �F�        	7      �    .pdata      �         _襩�        }7      �    .xdata      �          %蚘%        �7      �    .pdata      �         }S蛥        &8      �    .xdata      �          �9�        [8      �    .pdata      �         礝
        �8      �    .rdata      �                      9     �    .rdata      �          �;�         +9      �    .rdata      �                      R9     �    .rdata      �                      i9     �    .rdata      �          �)         �9      �    .xdata$x    �                      �9      �    .xdata$x    �         虼�)         �9      �    .data$r     �   /      嶼�         �9      �    .xdata$x    �   $      4��         !:      �    .data$r     �   $      鎊=         v:      �    .xdata$x    �   $      銸E�         �:      �    .data$r     �   $      騏糡         �:      �    .xdata$x    �   $      4��         �:      �        (;           .rdata      �          IM         ;;      �    .rdata$r    �   $      'e%�         a;      �    .rdata$r    �         �          y;      �    .rdata$r    �                      �;      �    .rdata$r    �   $      Gv�:         �;      �    .rdata$r    �   $      'e%�         �;      �    .rdata$r    �         }%B         �;      �    .rdata$r    �                      �;      �    .rdata$r    �   $      `         <      �    .rdata$r    �   $      'e%�         '<      �    .rdata$r    �         �弾         J<      �    .rdata$r    �                      k<      �    .rdata$r    �   $      H衡�         �<      �    .rdata      �          =-f�         �<      �    .rdata      �          v靛�         �<      �    .rdata      �          y蘮�         �<      �    .rdata      �          2T頄         �<      �    .rdata      �          Z尨6         �<      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �   �                =  ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlength_error@std@@YAXPEBD@Z ?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z ??1?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@QEAA@XZ ?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ ?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ ?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z ?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ ??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ ??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ?IsLocalLightPowerRISEnable@ReGIRContext@rtxdi@@QEBA_NXZ ?GetReGIRCellOffset@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRLightSlotCount@ReGIRContext@rtxdi@@QEBAIXZ ?GetReGIRGridCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRGridCalculatedParameters@2@XZ ?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ?GetReGIRStaticParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRStaticParameters@2@XZ ?SetDynamicParameters@ReGIRContext@rtxdi@@QEAAXAEBUReGIRDynamicParameters@2@@Z ?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z ?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ ?ComputeGridLightSlotCount@ReGIRContext@rtxdi@@AEAAXXZ ?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z ??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ ?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ ??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z ??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z ??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z ??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z ??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z ??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z ??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z ??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Guess_median_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM00U?$less@X@0@@Z ??$_Copy_backward_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z ?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA ?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA ?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA ?dtor$0@?0???0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z@4HA ?dtor$0@?0???0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z@4HA ?dtor$0@?0??ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ@4HA ?dtor$1@?0??ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ@4HA _CxxThrowException __CxxFrameHandler4 __catch$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z$0 __catch$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z$0 __catch$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z$0 $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z $pdata$?deallocate@?$allocator@UReGIR_OnionLayerGroup@@@std@@QEAAXQEAUReGIR_OnionLayerGroup@@_K@Z $unwind$?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ $pdata$?_Tidy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAXXZ $unwind$?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@CAXXZ $unwind$?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z $pdata$?deallocate@?$allocator@UReGIR_OnionRing@@@std@@QEAAXQEAUReGIR_OnionRing@@_K@Z $unwind$?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@CAXXZ $unwind$??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ $pdata$??1ReGIROnionCalculatedParameters@rtxdi@@QEAA@XZ $unwind$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $pdata$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $cppxdata$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $stateUnwindMap$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $ip2state$??0ReGIROnionCalculatedParameters@rtxdi@@QEAA@AEBU01@@Z $unwind$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $pdata$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $cppxdata$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $stateUnwindMap$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $ip2state$??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z $unwind$?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ $pdata$?GetReGIROnionCalculatedParameters@ReGIRContext@rtxdi@@QEBA?AUReGIROnionCalculatedParameters@2@XZ $unwind$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $chain$9$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$9$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $chain$10$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $pdata$10$?InitializeOnion@ReGIRContext@rtxdi@@AEAAXAEBUReGIRStaticParameters@2@@Z $unwind$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $pdata$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $cppxdata$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $stateUnwindMap$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $ip2state$?ComputeOnionJitterCurve@ReGIRContext@rtxdi@@AEAAXXZ $unwind$?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z $pdata$?AllocateRISBufferSegment@ReGIRContext@rtxdi@@AEAAXAEAVRISBufferSegmentAllocator@2@@Z $unwind$?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z $pdata$?deallocate@?$allocator@M@std@@QEAAXQEAM_K@Z $unwind$??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ $pdata$??1?$vector@MV?$allocator@M@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ $unwind$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $pdata$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $cppxdata$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $stateUnwindMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $tryMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $handlerMap$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $ip2state$??$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBM@?$vector@MV?$allocator@M@std@@@std@@AEAAPEAMQEAMAEBM@Z@4HA $unwind$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionRing@@@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@AEAAPEAUReGIR_OnionRing@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $pdata$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $cppxdata$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $stateUnwindMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $tryMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $handlerMap$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $ip2state$??$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z $unwind$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA $pdata$?catch$0@?0???$_Emplace_reallocate@AEBUReGIR_OnionLayerGroup@@@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@AEAAPEAUReGIR_OnionLayerGroup@@QEAU2@AEBU2@@Z@4HA $unwind$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$0$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$0$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$1$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$1$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$2$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$2$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $chain$3$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $pdata$3$??$_Sort_unchecked@PEAMU?$less@X@std@@@std@@YAXPEAM0_JU?$less@X@0@@Z $unwind$??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUReGIR_OnionRing@@PEAU1@@std@@YAPEAUReGIR_OnionRing@@PEAU1@00@Z $unwind$??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z $pdata$??$_Copy_memmove@PEAUReGIR_OnionLayerGroup@@PEAU1@@std@@YAPEAUReGIR_OnionLayerGroup@@PEAU1@00@Z $unwind$??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z $pdata$??$_Partition_by_median_guess_unchecked@PEAMU?$less@X@std@@@std@@YA?AU?$pair@PEAMPEAM@0@PEAM0U?$less@X@0@@Z $unwind$??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z $pdata$??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __real@3f000000 __real@3f800000 __real@40490fdb __real@40c90fdb __real@beaaaaab /171            1755678910              100666  64725     `
d哠 緢O�  �       .drectve        <  
               
 .debug$S        y  H              @ B.debug$T        h   T�              @ B.text$mn        u  紘  1�          P`.debug$S        �  蹔  彑      �   @B.text$x            ￥            P`.text$x            工  嗓          P`.text$x            婴  悚          P`.text$x            恧            P`.text$x            �  $�          P`.text$x            .�  K�          P`.text$x            U�  r�          P`.text$x            |�  櫏          P`.text$mn           ％  顶          P`.debug$S        �  昆  ��         @B.text$mn        
   姬  骚          P`.debug$S        t  缨  G�         @B.text$mn           儵  柀          P`.debug$S        �  牘  8�         @B.text$mn           t�  嚝          P`.debug$S        �  懌  )�         @B.text$mn        ^   e�  铆          P`.debug$S        X  氕  C�         @B.text$mn        �   话  １          P`.debug$S        �  吮  范      ,   @B.text$mn           o�               P`.debug$S          t�  ��         @B.text$mn           脊               P`.debug$S        �   凉  胶         @B.text$mn                          P`.debug$S            �         @B.text$mn           B�  V�          P`.debug$S          `�  p�         @B.text$mn                          P`.debug$S          敖  季         @B.text$mn                          P`.debug$S                      @B.text$mn           9�               P`.debug$S           >�  >�         @B.text$mn           z�               P`.debug$S           �  �         @B.text$mn           宦               P`.debug$S           缆  烂         @B.text$mn                          P`.debug$S           �  �         @B.text$mn           =�               P`.debug$S           B�  B�         @B.text$mn        �   ~�  �          P`.debug$S        �  4�  鹑         @B.text$mn        :   h�  ⑸          P`.debug$S          郎  允         @B.text$mn           �               P`.debug$S        D  (�  l�         @B.xdata             继  蕴         @0@.pdata             杼  籼         @0@.xdata          	   �  �         @@.xdata          *   /�  Y�         @@.xdata             ┩              @@.xdata             酵              @0@.pdata             磐  淹         @0@.xdata             锿              @0@.pdata             魍  �         @0@.xdata             !�              @0@.pdata             )�  5�         @0@.xdata             S�              @0@.pdata             [�  g�         @0@.xdata             呂              @0@.pdata             嵨  櫸         @0@.xdata             肺              @0@.pdata             课  宋         @0@.xdata             槲           @0@.pdata             �  �         @0@.xdata             1�  A�         @0@.pdata             K�  W�         @0@.xdata             u�              @0@.pdata             }�  壪         @0@.xdata               幌         @0@.pdata             傧  逑         @0@.xdata             �  �         @0@.pdata             1�  =�         @0@.xdata             [�  o�         @0@.pdata             嵭  櫺         @0@.chks64         �  沸               
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES"    �   t  p     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Rtxdi.dir\Release\ImportanceSamplingContext.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $?A0x639b76ef �   UI  8 �    std::_False_trivial_cat::_Bitcopy_constructible 5 �    std::_False_trivial_cat::_Bitcopy_assignable ? �   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ �    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J �   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E �   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask - L   rtxdi::c_NumReSTIRDIReservoirBuffers - L   rtxdi::c_NumReSTIRGIReservoirBuffers . �    std::integral_constant<bool,0>::value R �   std::allocator<ReGIR_OnionLayerGroup>::_Minimum_asan_allocation_alignment . �   std::integral_constant<bool,1>::value 6 �   std::_Iterator_base0::_Unwrap_when_unverified 7 �   std::_Iterator_base12::_Unwrap_when_unverified      std::denorm_absent     std::denorm_present      std::round_toward_zero     std::round_to_nearest #     std::_Num_base::has_denorm ( �    std::_Num_base::has_denorm_loss % �    std::_Num_base::has_infinity & �    std::_Num_base::has_quiet_NaN * �    std::_Num_base::has_signaling_NaN # �    std::_Num_base::is_bounded ! �    std::_Num_base::is_exact " �    std::_Num_base::is_iec559 # �    std::_Num_base::is_integer " �    std::_Num_base::is_modulo " �    std::_Num_base::is_signed ' �    std::_Num_base::is_specialized ( �    std::_Num_base::tinyness_before  �    std::_Num_base::traps $     std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' �   std::_Num_int_base::is_bounded % �   std::_Num_int_base::is_exact ' �   std::_Num_int_base::is_integer + �   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix )    std::_Num_float_base::has_denorm + �   std::_Num_float_base::has_infinity , �   std::_Num_float_base::has_quiet_NaN 0 �   std::_Num_float_base::has_signaling_NaN ) �   std::_Num_float_base::is_bounded ( �   std::_Num_float_base::is_iec559 ( �   std::_Num_float_base::is_signed - �   std::_Num_float_base::is_specialized *    std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - �   std::numeric_limits<char>::is_signed L �   std::allocator<ReGIR_OnionRing>::_Minimum_asan_allocation_alignment - �    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 �   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 �   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 �   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 �   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 �   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 �   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . �   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , �   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - �   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 �   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 �   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 �   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 �   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 �   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 4 �  @ _Mtx_internal_imp_t::_Critical_section_size 5 �   _Mtx_internal_imp_t::_Critical_section_align 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 + �    std::_Aligned_storage<64,8>::_Fits 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 * �    std::_Aligned<64,8,char,0>::_Fits + �    std::_Aligned<64,8,short,0>::_Fits ) �   std::_Aligned<64,8,int,0>::_Fits % \    _Atomic_memory_order_relaxed % \   _Atomic_memory_order_consume % \   _Atomic_memory_order_acquire % \   _Atomic_memory_order_release % \   _Atomic_memory_order_acq_rel % \   _Atomic_memory_order_seq_cst $ �    std::strong_ordering::equal : �    std::integral_constant<unsigned __int64,0>::value ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy : �   std::integral_constant<unsigned __int64,2>::value / �   std::atomic<long>::is_always_lock_free : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : L  � std::_Floating_type_traits<float>::_Exponent_mask E L  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G L  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J L  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B L  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F L  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; �  �std::_Floating_type_traits<double>::_Exponent_mask J �  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L �  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O �  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G �  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K �  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask ; �   std::atomic<unsigned __int64>::is_always_lock_free  t   int32_t  �  _CatchableType " f  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6 6  __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  .  ReSTIRDI_BufferIndices &   $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t + 
  ReSTIRGI_SpatialResamplingParameters  (  _TypeDescriptor 	 )  tm % k  _s__RTTICompleteObjectLocator2 " V  RTXDI_LightBufferParameters A %  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType  !  rtxdi::uint3  �  rtxdi::ReGIRContext $ �  rtxdi::ReGIRDynamicParameters    rtxdi::float3 1 �  rtxdi::LocalLightReGIRFallbackSamplingMode 8 b  rtxdi::ImportanceSamplingContext_StaticParameters # �  rtxdi::ReGIRStaticParameters ' t  rtxdi::RISBufferSegmentAllocator '   rtxdi::ReGIRGridStaticParameters & �  rtxdi::ReSTIRDIStaticParameters  =  rtxdi::CheckerboardMode (   rtxdi::ReGIROnionStaticParameters & �  rtxdi::ReSTIRGIStaticParameters  {  rtxdi::ReGIRMode ( �  rtxdi::RISBufferSegmentParameters  8  rtxdi::ReSTIRGIContext , u  rtxdi::ReGIROnionCalculatedParameters +   rtxdi::ReGIRGridCalculatedParameters , �  rtxdi::LocalLightReGIRPresamplingMode ' �  rtxdi::ImportanceSamplingContext  �  rtxdi::ReSTIRDIContext & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> &   ReSTIRGI_FinalShadingParameters E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & v  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16 M   std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > D   std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> >  �  std::_Lockit U .  std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > 2   std::default_delete<rtxdi::ReSTIRGIContext>  "   std::_Atomic_counter_t    std::_Num_base # �  std::numeric_limits<char8_t> & s  std::allocator<ReGIR_OnionRing>  �  std::hash<float> d   std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRGIContext>,rtxdi::ReSTIRGIContext *,1>    std::_Num_int_base , �  std::allocator<ReGIR_OnionLayerGroup>    std::float_denorm_style  q  std::bad_cast     std::_Compare_t " A  std::numeric_limits<double>  �  std::__non_rtti_object ( !  std::_Basic_container_proxy_ptr12  =  std::_Num_float_base  \  std::pointer_safety  �  std::_Compare_ncmp     std::numeric_limits<bool>     std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * 4  std::numeric_limits<unsigned short>   |  std::pmr::memory_resource  �  std::false_type    std::float_round_style  �  std::weak_ordering , :  std::numeric_limits<unsigned __int64> $ &  std::numeric_limits<char16_t> % �  std::integral_constant<bool,1>     std::_Leave_proxy_unbound � �  std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1>  �  std::_Iterator_base12 C �  std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > $ �  std::_Atomic_integral<long,4>  �  std::hash<long double> = �  std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >   �  std::_Comparison_category # *  std::numeric_limits<wchar_t>  �  std::_Container_base0  �  std::hash<double> % �  std::integral_constant<bool,0>  
  std::bad_exception & �  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator ! ?  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> G �  std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> >  T  std::_Ref_count_base  R  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy $ (  std::numeric_limits<char32_t>  �  std::exception  �  std::_Iterator_base0  z  std::tuple<>  �  std::_Container_base12 ) $  std::numeric_limits<unsigned char>  �  std::true_type   0  std::numeric_limits<long>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits x A  std::_Compressed_pair<std::default_delete<rtxdi::RISBufferSegmentAllocator>,rtxdi::RISBufferSegmentAllocator *,1> P �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > f �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy ! ,  std::numeric_limits<short> ! c  std::_Shared_ptr_spin_lock     std::bad_alloc # 2  std::numeric_limits<__int64> /   std::default_delete<rtxdi::ReGIRContext>  C  std::memory_order [ O  std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > # �  std::_Atomic_storage<long,4>  l  std::atomic_flag > �  std::allocator_traits<std::allocator<ReGIR_OnionRing> > o �  std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> >   6  std::bad_array_new_length v ~  std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1>  �  std::_Container_proxy  �  std::nested_exception  �  std::_Distance_unknown ( 6  std::numeric_limits<unsigned int>  �  std::atomic<long>   �  std::hash<std::nullptr_t> ' C  std::numeric_limits<long double>  �  std::bad_typeid < :  std::default_delete<rtxdi::RISBufferSegmentAllocator>  �  std::_Compare_eq    std::nullptr_t  <  std::bad_weak_ptr ) 8  std::numeric_limits<unsigned long>   K  std::_Atomic_padded<long> ' "  std::numeric_limits<signed char>  �  std::_Literal_zero 2 '  std::default_delete<rtxdi::ReSTIRDIContext>      std::numeric_limits<char>  �  std::_Unused_parameter * �  std::ranges::_Uninitialized_fill_fn 7 &  std::ranges::_Uninitialized_value_construct_n_fn # G  std::ranges::_Find_if_not_fn , �  std::ranges::_Uninitialized_move_n_fn !   std::ranges::_Destroy_n_fn $ �  std::ranges::_Construct_at_fn "   std::ranges::_Destroy_at_fn  ;  std::ranges::_Find_fn ! �  std::ranges::subrange_kind    std::ranges::_Next_fn % M  std::ranges::_Adjacent_find_fn # Y  std::ranges::_Max_element_fn  A  std::ranges::_Find_if_fn 7   std::ranges::_Uninitialized_default_construct_fn * �  std::ranges::_Uninitialized_move_fn , �  std::ranges::_Uninitialized_copy_n_fn   5  std::ranges::_Mismatch_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag  k  std::ranges::_Min_fn  )  std::ranges::_Copy_fn * �  std::ranges::_Uninitialized_copy_fn    std::ranges::_Destroy_fn , �  std::ranges::_Uninitialized_fill_n_fn  #  std::ranges::dangling  S  std::ranges::_Search_fn    std::ranges::_Prev_fn     std::ranges::_Distance_fn # e  std::ranges::_Min_element_fn    std::ranges::_Advance_fn 5    std::ranges::_Uninitialized_value_construct_fn  /  std::ranges::_Fill_n_fn  _  std::ranges::_Max_fn 9   std::ranges::_Uninitialized_default_construct_n_fn ^   std::_Compressed_pair<std::default_delete<rtxdi::ReGIRContext>,rtxdi::ReGIRContext *,1> [   std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > d .  std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRDIContext>,rtxdi::ReSTIRDIContext *,1> " �  std::_Asan_aligned_pointers  �  std::partial_ordering  .  std::numeric_limits<int>  �  std::bad_variant_access D j  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > Z 8  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy   f  __RTTIBaseClassDescriptor 
    _off_t  !  stat  8  timespec 
 !   _ino_t , �  ReSTIRDI_TemporalResamplingParameters M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  )  _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ' Z  RTXDI_RISBufferSegmentParameters + �  ReSTIRDI_SpatialResamplingParameters  �  RTXDI_LightBufferRegion  9  terminate_handler  �  _s__RTTIBaseClassArray  �  ReGIR_OnionLayerGroup 
 �  ldiv_t - p  $_s__RTTIBaseClassArray$_extraBytes_24 ) �  ReSTIRDI_InitialSamplingParameters  �  _CatchableTypeArray     ptrdiff_t  "  _stat64i32  �  _PMD  >  type_info ' |  _s__RTTIClassHierarchyDescriptor  t   errno_t  �  _lldiv_t  U  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  n  _s__ThrowInfo - �  RTXDI_EnvironmentLightBufferParameters  �  __RTTIBaseClassArray - �  $_s__CatchableTypeArray$_extraBytes_24 ! �  ReSTIRDI_ShadingParameters % |  __RTTIClassHierarchyDescriptor & G  RTXDI_ReservoirBufferParameters 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  �  FILE  �  ReGIR_OnionRing ,   ReSTIRGI_TemporalResamplingParameters 3 �  __vcrt_va_list_is_reference<wchar_t const *>  )  mbstate_t  f  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray    ReSTIRGI_BufferIndices & �  ReSTIRDI_LocalLightSamplingMode 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  �  lldiv_t  r  RTXDI_RuntimeParameters  �  _ldiv_t  9  _timespec64  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers    �   x      +4[(広
倬禼�溞K^洞齹誇*f�5  `    �"睱建Bi圀対隤v��cB�'窘�n  �    ま錗~缃+mBC�$�6C\[懦G)N憓Tf<     �	R\�5甕:7峡铻崑p!騎P与�3�%�;  6   V8追i顚�^�k细�;>牧惺扴	�\s  t   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�     z�0叐i�%`戉3猂|Ei韍訋�#Q@�  H   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�     �?B�)��攨~�倮wM晫#��%  U    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟      d蜯�:＠T邱�"猊`�?d�B�#G騋  Z   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  J   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  ,   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  y   �0�*е彗9釗獳+U叅[4椪 P"��  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   D���0�郋鬔G5啚髡J竆)俻w��  P   悯R痱v 瓩愿碀"禰J5�>xF痧  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   矨�陘�2{WV�y紥*f�u龘��  "   _O縋[HU-銌�鼪根�鲋薺篮�j��  k   齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  �   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  (	   檒Gq$�#嗲RR�錨账��K諻刮g�   [	   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �	   -�
�捂�
y�*犯丁�02?栕9/�Q  �	   	{Z�范�F�m猉	痹缠!囃ZtK�T�  
   副謐�斦=犻媨铩0
龉�3曃譹5D   a
   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �
   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �
   椛`榿B�:瀚�&�%玲�$;舘傼�,擇��      寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  `   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   *u\{┞稦�3壅阱\繺ěk�6U�  &   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  c   繃S,;fi@`騂廩k叉c.2狇x佚�  �   泽閇�R鯄呙+困胢p=�R刐鉍籫�8[  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  D
   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �
   5�\營	6}朖晧�-w氌rJ籠騳榈  �
   �疀�4�A圏,oHB瓳HJ��2�0(v/  
   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  K   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  
   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  J   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   �*o驑瓂a�(施眗9歐湬

�  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  1    I嘛襨签.濟;剕��7啧�)煇9触�.  q   +椬恡�
	#G許�/G候Mc�蜀煟-  �   c�#�'�縌殹龇D兺f�$x�;]糺z�     d潣7熈[$袎o�懠I殑Iy厵唫嬎�  G   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�     偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  H   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  Z   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  /   G�膢刉^O郀�/耦��萁n!鮋W VS  n   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  \   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  )   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  s   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   匐衏�$=�"�3�a旬SY�
乢�骣�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  G   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�     绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  f   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   �
bH<j峪w�/&d[荨?躹耯=�  �   交�,�;+愱`�3p炛秓ee td�	^,  0   _臒~I��歌�0蘏嘺QU5<蝪祰S  u   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�     ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  P   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   �      }  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  `  �  �  `  �  �  `  �  �  `  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �
  �  �  �  �  �  �
  �  �  �  �  �  �
  �  �  �  �  �  �
  �  �  �  �  �  �   �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �     �  �    �  �    �  �  
  �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �    �  �     �  �  !  �  �  "  �  �  #  �  �  $  �  �  (  �  �  )  �  �  *  �  �  +  �  �  �   �   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\RTXPT\External\Rtxdi\Source\ImportanceSamplingContext.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDIParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert D:\RTXPT\External\Rtxdi\include\Rtxdi\ImportanceSamplingContext.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDI.h D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h �       L�     b 骓騝�谽樣�1�Pj   D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb H塡$H塋$VWAVH冹@L嬺H嬞3�H�9H墆H墆H墆W�A A0A@峅�    H塂$hH吚t
H嬋�    �H嬊H�H�H吷t
�   �    A媀A�H��    塁PA婩塁XA�塁TA媀A疺H��    塁`A婩塁hA婩塁dA婩塂$<A婩塂$0A婩塂$4A婩塂$8桂   �    H塂$hH吚tH峊$0H嬋�    �H嬊H婯H塁H吷t
吼   �    H�3箞   �    H塂$hH吚tI峍 L嬈H嬋�    �H嬊H媠H塁H咑劽   H婲`H吷tAH媀pH+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐囼   I嬋�    H墌`H墌hH墌pL婩HM吚tcH婲XI+菻斧*H鏖H龙H嬄H凌?H蠬�RH菱H侜   rH兟'I婬鳯+罥岪鳫凐噳   L嬃I嬋�    H墌HH墌PH墌X簣   H嬑�    A婩塂$(A婩塂$ A婩塂$$工   �    H塂$hH吚tH峊$ H嬋�    H孁H婯H墈H吷t氦   �    怘嬅H媆$pH兡@A^_^描    �<   m    N   �    h   n    x   �    �   �    �   m    �   p      n      m    -  t    �  n    �  n      n    (  m    ?  s    Y  n    p  o       �   �
  a G            u     u  �        �rtxdi::ImportanceSamplingContext::ImportanceSamplingContext 
 >�   this  AI       ]N	  AJ          D`    >�   isParams  AK          AV       `W  >�    restirGIStaticParams  D     >�    restirDIStaticParams  D0    M        �  侳 M        �  侳(
 M          侳 >C    _Old_val  AJ  J      AJ ^      N M        �  
係
 Z   {   N N N M        �  $�" Z   �     N M        �  �莵6�9 M        �  �6(��	�( M          �6 >    _Old_val  AL  :    ;4  N M        �  �秮G�( M        �  l亼��% M        �  亼iW$	v M        �  5伡��  M        �  伳)��
 Z   {  
 >   _Ptr  AP �      >#    _Bytes  AK  �    -  AK o     & M        }  佂d#��
 Z   3   >�    _Ptr_container  AJ  �      AJ �    �  �  >�    _Back_shift  AP  �    T  AP �    �   ! e  N N N N N M        �  J丟  M        �  丟i5$ M        �  .乄 M        �  乕)
 Z   {  
 >   _Ptr  AJ �      >#    _Bytes  AK  T    1    AK o       M        }  乨d# >�    _Ptr_container  AP  l      AP �    �  �  >�    _Back_shift  AJ  K    5  AJ �    �    _ � j  N N N N N N N N M        �  � Z   �  �   >�   <_Args_1>  AL      ,  N M        �  �� M        �  ��(
 M        !  �� >�    _Old_val  AJ  �       AJ       N M        �  
�
 Z   {   N N N M        �  &�� Z   �  �   N M        �  W M           W&
 M        #  W >v    _Old_val  AJ  Z       AJ l       N M        �  
b
 Z   {   N N N M        �  8 Z   �  5   N M        �  % M          % N N M        �  ! M          ! N N M        �   M        
   N N M        �   M           N N Z   6  6   @                    @ .hJ   �  }  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �         
                       !  "  #  $  (  )  *  +         $LN215  `   �  Othis  h   �  OisParams !     �  OrestirGIStaticParams ! 0   �  OrestirDIStaticParams  ^;      l   ^�      �   ^     �   ^'        O �   �           u  �     �       ,  �,   +  �8   /  �l   0  �   1  ��   2  ��   3  ��   4  ��   5  ��   8  ��   9  ��   :  ��   ;  ��   <  �  >  �
  A  �  B  �  C  �"  D  �^  E  �o  >  ��     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$0 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$1 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$2 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                                �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$3 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                  �  O   �     p F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$8 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O   �     q F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$10 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O  �     q F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$12 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O  �     q F                               �`rtxdi::ImportanceSamplingContext::ImportanceSamplingContext'::`1'::dtor$16 
 >�   this  EN  `           >�    restirGIStaticParams  EN              >�    restirDIStaticParams  EN  0                                 �  O  ,   v    0   v   
 �   v    �   v   
 �   v    �   v   
 �   v    �   v   
 �   v    �   v   
 �  v    �  v   
 �  v    �  v   
 {  v      v   
 F  v    J  v   
 g  v    k  v   
 w  v    {  v   
 �  v    �  v   
 �  v    �  v   
   v      v   
   v    "  v   
 �  v    �  v   
 �  v    �  v   
   v      v   
 O  v    S  v   
 _  v    c  v   
 �  v    �  v   
 �  v    �  v   
   v      v   
 �  v    �  v   
 �  v    �  v   
 ]  v    a  v   
 m  v    q  v   
 �	  �    �	  �   
 k
  v    o
  v   
 {
  v    
  v   
 �
  v    �
  v   
 �
  v    �
  v   
 �
  v    �
  v   
 �  �    �  �   
 	  �    
  �   
 <  �    @  �   
 o  �    s  �   
 �  �    �  �   
 1
  �    5
  �   
 d
  �    h
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 Y  �    ]  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 <  �    @  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 d  �    h  �   
 �  �    �  �   
   �    	  �   
 8  �    <  �   
 �  �    �  �   
 �  �    �  �   
 -  �    1  �   
 `  �    d  �   
 �  �    �  �   
 "  �    &  �   
 U  �    Y  �   
 �  �    �  �   
 H媻`   �       �    H媻`   H兞�       �    H媻`   H兞�       �    H媻`   H兞�       �    @UH冹 H嬯�   H婱h�    H兡 ]�   n    @UH冹 H嬯吼   H婱h�    H兡 ]�   n    @UH冹 H嬯簣   H婱h�    H兡 ]�   n    @UH冹 H嬯氦   H婱h�    H兡 ]�   n    H�	H吷t
�   �    �   n       �   s  � G                      �        �std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> >::~unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> > 
 >�   this  AJ          M        �  
 N                        H�  h   �  �      �  Othis  O �   8              �     ,       � �    � �   � �   � �,   �    0   �   
   �      �   
 �  �    �  �   
 H�H呉�    �   �       �   .  � G            
          �        �std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> >::~unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > 
 >   this  AJ        
 
 Z   �                          H� 
 h   �        Othis  O  �   0           
   �     $       � �    � �   � �,   �    0   �   
 �   �    �   �   
 D  �    H  �   
 H�	H吷t
吼   �    �   n       �   K  � G                      �        �std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> >::~unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > 
 >�   this  AJ          M        �  
 N                        H�  h   �  �      �  Othis  O �   8              �     ,       � �    � �   � �   � �,   �    0   �   
 �   �    �   �   
 `  �    d  �   
 H�	H吷t
氦   �    �   n       �   K  � G                      �        �std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> >::~unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > 
 >6   this  AJ          M        �  
 N                        H�  h   �  �      6  Othis  O �   8              �     ,       � �    � �   � �   � �,   �    0   �   
 �   �    �   �   
 `  �    d  �   
 @SH冹 H嬞H婭H吷t
氦   �    H婼H岾H呉t�    H婯H吷t
吼   �    H�H吷t�   H兡 [�    H兡 [�   n    *   �    =   n    T   n       �     b G            ^      X   �        �rtxdi::ImportanceSamplingContext::~ImportanceSamplingContext 
 >�   this  AI  	     T J   AJ        	  M        �  A
 M        �  I
 N N M        �  .
	 M        �  
7
 Z   {   N N M        �  
 Z   �  
 >   this  AJ  $     
  AJ .       N M        �  )
	 M        �  

 Z   {   N N                       @� 2 h   �  �  �  �  �  �  �  �  �  �  �   0   �  Othis  O   �   (           ^   �            H  �	   J  �,   w    0   w   
 �   w    �   w   
 �   w    �   w   
 `  w    d  w   
 p  w    t  w   
 0  w    4  w   
 H呉勜   SH冹 H婮`H嬟H墊$03�H吷tAH婻pH+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐嚁   I嬋�    H墈`H墈hH墈pL婥HM吚t_H婯XH斧*I+菻鏖H龙H嬄H凌?H蠬�RH菱H侜   rI婬鳫兟'L+罥岪鳫凐w/L嬃I嬋�    H墈HH墈PH墈X簣   H嬎�    H媩$0H兡 [描    蘎   n    �   n    �   n    �   o       �   �  Z G            �      �   �        �std::default_delete<rtxdi::ReGIRContext>::operator() 
 >   this  AJ          AJ �       D0   
 >   _Ptr  AI       � �   AK          AK �       M        �  hb��$ M        �  biS$	 M        �  1��U M        �  ��),
 Z   {  
 >   _Ptr  AP �       >#    _Bytes  AK  �     )  AK �      # M        }  
��#
/
 Z   3   >�    _Ptr_container  AJ  �       AJ �     1  )  >�    _Back_shift  AP  f     P  AP �     1   !   N N N N N M        �  H" M        �  g5$ M        �  .( M        �  ,)
 Z   {  
 >   _Ptr  AJ Q       >#    _Bytes  AK  %     1    AK �       M        }  
5# >�    _Ptr_container  AP  9       AP Q     �  �  >�    _Back_shift  AJ       ?  AJ Q     �    [ �   N N N N N                       H� J h   �  }  ~  �  �  �  �  �  �  �  �  �  �  �  �  �  �         $LN78  0     Othis  8     O_Ptr  O  �   H           �   �     <       ` �    b �   ` �   b ��   c ��   b �,   �    0   �   
    �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 	  �    
  �   
   �      �   
 C  �    G  �   
 S  �    W  �   
   �    
  �   
 '  �    +  �   
 ;  �    ?  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 c  �    g  �   
 �  �    �  �   
 H岮`�   �   �   q G                      �        �rtxdi::ImportanceSamplingContext::GetEnvironmentLightRISBufferSegmentParams 
 >�   this  AJ                                 @     �  Othis  O �   0              �     $       z  �    {  �   |  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H岮 �   �   �   ` G                      �        �rtxdi::ImportanceSamplingContext::GetLightBufferParameters 
 >�   this  AJ                                 @     �  Othis  O  �   0              �     $       p  �    q  �   r  �,       0      
 �       �      
 �       �      
 H岮P�   �   �   k G                      �        �rtxdi::ImportanceSamplingContext::GetLocalLightRISBufferSegmentParams 
 >�   this  AJ                                 @     �  Othis  O   �   0              �     $       u  �    v  �   w  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H冹(H婭�    � H兡(�	   r       �   �   ^ G                     �        �rtxdi::ImportanceSamplingContext::GetNeighborOffsetCount 
 >�   this  AJ         
 Z   �   (                      @ 
 h   �   0   �  Othis  O�   0              �     $         �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H��   �   �   d G                      �        �rtxdi::ImportanceSamplingContext::GetRISBufferSegmentAllocator 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O  �   0              �     $       k  �    l  �   m  �,   ~    0   ~   
 �   ~    �   ~   
 �   ~    �   ~   
 H婣�   �   �   W G                      �        �rtxdi::ImportanceSamplingContext::GetReGIRContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O   �   0              �     $       W  �    X  �   Y  �,   z    0   z   
 |   z    �   z   
 �   z    �   z   
 H婣�   �   �   W G                      �        �rtxdi::ImportanceSamplingContext::GetReGIRContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O   �   0              �     $       \  �    ]  �   ^  �,   {    0   {   
 |   {    �   {   
 �   {    �   {   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRDIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       M  �    N  �   O  �,   x    0   x   
    x    �   x   
 �   x    �   x   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRDIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       R  �    S  �   T  �,   y    0   y   
    y    �   y   
 �   y    �   y   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRGIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       a  �    b  �   c  �,   |    0   |   
    |    �   |   
 �   |    �   |   
 H婣�   �   �   Z G                      �        �rtxdi::ImportanceSamplingContext::GetReSTIRGIContext 
 >�   this  AJ                                 @ 
 h   �      �  Othis  O�   0              �     $       f  �    g  �   h  �,   }    0   }   
    }    �   }   
 �   }    �   }   
 @SH侅�   H�    H3腍墑$�   H嬞H峊$ H婭�    婦$<凐t4凐u+H婯H峊$@�    儀tH婯H峊$`�    儀斃�2离�H媽$�   H3惕    H伳�   [�   �    (   q    D   u    X   u    w   �       �   M  c G            �      k   �        �rtxdi::ImportanceSamplingContext::IsLocalLightPowerRISEnabled 
 >�   this  AI       e  AJ          >�   iss  C      0     7    C     i       D     Z   �  �  �   �                     A  h   �  �  
 :�   O  �   �  Othis      �  Oiss  O   �   X           �   �     L       �  �   �  �,   �  �5   �  �:   �  �e   �  �i   �  �k   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 d  �    h  �   
 H冹XH�    H3腍塂$@H婭H峊$ �    儀斃H婰$@H3惕    H兡X�   �       q    1   �       �   �   V G            :      (   �        �rtxdi::ImportanceSamplingContext::IsReGIREnabled 
 >�   this  AJ         
 Z   �   X                      A 
 h   �  
 :@   O  `   �  Othis  O�   0           :   �     $       �  �   �  �(   �  �,   �    0   �   
 {   �       �   
 �   �    �   �   
 A JI0B A@�   �   �   \ G                      �        �rtxdi::ImportanceSamplingContext::SetLightBufferParams 
 >�   this  AJ          >�   lightBufferParams  AK                                 @     �  Othis     �  OlightBufferParams  O  �   0              �     $       �  �    �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
  4 r�p`           �       �        u          �       �       �    (           �       �        �6    .    .    .    V    ~    �       �       �    
   �       �       �       �    !   �    &   �    v$
T�.R2�* 2P               �       �       �     2P               �       �       �     2P               �       �       �     2P               �       �       �     20    ^           �       �       �     B                 �       �       �     	 0      �      �        �           �       �       �     �      @      �        :           �       �       �     2
0               �       �       �    ! t               �       �       �       �           �       �       �    !                 �       �       �    �   �           �       �       �    !   t               �       �       �    �   �           �       �       �    E褼5`苁(e擆鬚9蚳鈊&连0涩鍩�?r罛2$U掠釬顪$phxl67獄呏M烀�<�68曤m'+�ZsPp侧3G�6�>-B7琏�Q陑V#斮Q頶�#	�M>*#�*�
6�2该赂;12嚞T!�|DtLs激瘼粃减�&(	M
6繯	啃尰�3zEQ@掏-β�:衮鈍旹�"gi酭憰怈�証≒�5eE�ō晷嬏枌蘂磷�p8[Z�?B鼺塲+遠� !FiJ)rY淟捠qBh{誕梷SK�&獘箎-	k傕Tr鐵箎-	kl.H碫�麱陫婉f訯{痬j遜麱陫婉f詪#艽厛k圶�9幄,Q07&=稹k圶�9幄�w嘹� �4e2=9蜘Q�lI�/顅AZ鷈`�榕�;曾韜�I�閷蜫佸挲贿荶峴寮�>与�	0X掷dd�a�:跊1��粽Ozj�&樢閣yQ E<礼\樢閣yQ E<礼\樢閣yQ E<礼\樢閣yQ E<礼\雵J-WV8o��腫62V-坓�(鬄鯅�$劥#?痫�i忞婓�4{	1>&
�?�/B/ｎ	蜍R帙Vr屵祁懚獲r貂筦绬靻3;f2���櫇�"`Z_训0玂昻瓓髱[w鵫�7颸s hw        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       <                .debug$S       y                .debug$T       h                 .text$mn       u     4     .debug$S       �  �           .text$x              �/�    .text$x              
@獕    .text$x              /f    .text$x     	         朆|�    .text$x     
         鼺裠    .text$x              y2fo    .text$x              蕩	    .text$x     
         硅D�    .text$mn             伭-     .debug$S       �             .text$mn       
      瀲l�     .debug$S       t             .text$mn             洵f4     .debug$S       �             .text$mn             謰檮     .debug$S       �             .text$mn       ^      x�"�     .debug$S       X             .text$mn       �      擙5�     .debug$S       �  ,           .text$mn              4bxa     .debug$S                    .text$mn              1-�     .debug$S       �              .text$mn              荰>�     .debug$S                    .text$mn              �$
�     .debug$S    !                 .text$mn    "          �&定     .debug$S    #            "    .text$mn    $          D,k     .debug$S    %             $    .text$mn    &          D,k     .debug$S    '             &    .text$mn    (          G�7�     .debug$S    )             (    .text$mn    *          G�7�     .debug$S    +             *    .text$mn    ,          熙�     .debug$S    -             ,    .text$mn    .          熙�     .debug$S    /             .    .text$mn    0   �      �#�     .debug$S    1   �         0    .text$mn    2   :      "鎊     .debug$S    3            2    .text$mn    4          �jt�     .debug$S    5   D         4                                        #                F                �                �                <               }               �               *              �              �      (              *        W      $        �      &        �      ,        A      .        �      "        �              \              �              H               �      0        �      2        
      4        j              �              J              �              �              _	               �	               �	              2
              �
                    
        y              �              Q      	        �      
        )
               <
               M
           $LN215  u      $LN218          $LN27           $LN4             $LN12       0    $LN4        2    $LN78   �       $LN82           .xdata      6         �%$z        e
      6    .pdata      7         輲/s        �
      7    .xdata      8   	      � )9        .      8    .xdata      9   *      曣�        �      9    .xdata      :          #Kc&              :    .xdata      ;          k�        i      ;    .pdata      <         �$剧        �      <    .xdata      =          k�        P      =    .pdata      >         �$剧        �      >    .xdata      ?          k�        9      ?    .pdata      @         �$剧        �      @    .xdata      A          k�        "      A    .pdata      B         �$剧        �      B    .xdata      C          （亵              C    .pdata      D         翎珸        ?      D    .xdata      E          �9�         r      E    .pdata      F         �?聒         �      F    .xdata      G         趗�0              G    .pdata      H          媞�0        R      H    .xdata      I         薉O�2        �      I    .pdata      J         礝
2        �      J    .xdata      K          #D[�        #      K    .pdata      L         O?[4        z      L    .xdata      M         T�%~        �      M    .pdata      N         苛�        (      N    .xdata      O         Ｕ�        �      O    .pdata      P         沵�        �      P    .xdata      Q         �:        0      Q    .pdata      R         ＃涺        �      R        �           .chks64     S   �                �  ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z _invalid_parameter_noinfo_noreturn ??0ReSTIRDIContext@rtxdi@@QEAA@AEBUReSTIRDIStaticParameters@1@@Z ?GetInitialSamplingParameters@ReSTIRDIContext@rtxdi@@QEBA?AUReSTIRDI_InitialSamplingParameters@@XZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ??0ReSTIRGIContext@rtxdi@@QEAA@AEBUReSTIRGIStaticParameters@1@@Z ??0ReGIRContext@rtxdi@@QEAA@AEBUReGIRStaticParameters@1@AEAVRISBufferSegmentAllocator@1@@Z ?GetReGIRDynamicParameters@ReGIRContext@rtxdi@@QEBA?AUReGIRDynamicParameters@2@XZ ??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z ??1ImportanceSamplingContext@rtxdi@@QEAA@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRDIContext@2@XZ ?GetReSTIRDIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRDIContext@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReGIRContext@2@XZ ?GetReGIRContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReGIRContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEAAAEAVReSTIRGIContext@2@XZ ?GetReSTIRGIContext@ImportanceSamplingContext@rtxdi@@QEBAAEBVReSTIRGIContext@2@XZ ?GetRISBufferSegmentAllocator@ImportanceSamplingContext@rtxdi@@QEBAAEBVRISBufferSegmentAllocator@2@XZ ?GetLightBufferParameters@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_LightBufferParameters@@XZ ?GetLocalLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetEnvironmentLightRISBufferSegmentParams@ImportanceSamplingContext@rtxdi@@QEBAAEBURTXDI_RISBufferSegmentParameters@@XZ ?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ ?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ ?SetLightBufferParams@ImportanceSamplingContext@rtxdi@@QEAAXAEBURTXDI_LightBufferParameters@@@Z ??1?$unique_ptr@VRISBufferSegmentAllocator@rtxdi@@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRDIContext@rtxdi@@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@@std@@QEAA@XZ ??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z ??1?$unique_ptr@VReGIRContext@rtxdi@@U?$default_delete@VReGIRContext@rtxdi@@@std@@@std@@QEAA@XZ ??1?$unique_ptr@VReSTIRGIContext@rtxdi@@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@@std@@QEAA@XZ ??0RISBufferSegmentAllocator@rtxdi@@QEAA@XZ ?allocateSegment@RISBufferSegmentAllocator@rtxdi@@QEAAII@Z ?dtor$0@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$10@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$12@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$16@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$1@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$2@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$3@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA ?dtor$8@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA __CxxFrameHandler4 __GSHandlerCheck __security_check_cookie $unwind$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $pdata$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $cppxdata$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $stateUnwindMap$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $ip2state$??0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z $unwind$?dtor$8@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$8@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$?dtor$10@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$10@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$?dtor$12@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$12@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$?dtor$16@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $pdata$?dtor$16@?0???0ImportanceSamplingContext@rtxdi@@QEAA@AEBUImportanceSamplingContext_StaticParameters@1@@Z@4HA $unwind$??1ImportanceSamplingContext@rtxdi@@QEAA@XZ $pdata$??1ImportanceSamplingContext@rtxdi@@QEAA@XZ $unwind$?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ $pdata$?GetNeighborOffsetCount@ImportanceSamplingContext@rtxdi@@QEBAIXZ $unwind$?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $pdata$?IsLocalLightPowerRISEnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $unwind$?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $pdata$?IsReGIREnabled@ImportanceSamplingContext@rtxdi@@QEBA_NXZ $unwind$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $chain$0$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$0$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $chain$1$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$1$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $chain$2$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z $pdata$2$??R?$default_delete@VReGIRContext@rtxdi@@@std@@QEBAXPEAVReGIRContext@rtxdi@@@Z __security_cookie 
