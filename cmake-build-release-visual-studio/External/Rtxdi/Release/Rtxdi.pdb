Microsoft C/C++ MSF 7.00
DS         M   $      =                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������鑰     0 ������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������`k 5� 贱 �2 ^� 畡 螬 �8 f� 9� � @6 辺 L� 魺 8 �  Q O  觀 0� `v  刀 [h  旼 夦  !�  ZF ┉ 蜣 Uj X 3 :�  S�  w� ]L 夐 1 N�  o&  ,�  � #N  王 撃 珘 廅 \�  �	  带 萝  ^� 蔛 歶 � 穡 f� 鄨  K� 臋  Y" �  _` $� #� ZA ]� yJ e& 旌 <K 趡 瑳 欥 ER }� 儚 �1 遯  e�  �+ �( � � 0�  卣 w| C� }� 2� b�  )~ � 娇 _O .� >m 孛 Qj  嗧 /� � 2a T 冑 � 復  笽 涻 %
  W� aE  6 u] � n� 诓 鱗  盃  瀋 o 碕   鳯  �  	7  4 |z �2 �3 kQ  )�  �)  礰   � i� h  1� �< � H� � ~@ _  鈘 %� 鞦  �2  �K  北  ^� 肛 瀘 岃  + 蚅 秡 鏽 雱  �� 慢 D� + S� ?� 膒 ╜ 釙 灟 �$ 
B  5� 4; 循 蹨 嬖 觅 E  ~� 仢 A� 嵊 目  o� o.  � み he  J) �  �  康 xa �C _� � 0� 0D 3� o9 镅 谼  沅 � G�  N< .�  詆 �
 � ea 柪 m�  f� 划   w�  *� r� 橾 ?� 糬  .d  0�  �  sF $ � 嚉 L�  �  惨 >� 聸  1y $� + CA ]�  豣  衡 糛 @� K� @: 0| g� p( 暷 堙 �'  �"  
 楰 葲 b� 僎 魠 T` � ,� < ^ e)  /� �r �8 �  r [� ] 肟  倪 妎  8�  萮 』 89  z0 "� # n 歞  "� � 纣 祣  3� V� H�  魨 U: Bl 矝 =<  �  �  讇  浸 蕸 廲 T ) 锱 鍛 �  鞾 � 9h  {n 峇 ?<  aG 続 旳  i� 药  � � 恑 pr �� 塥 禆 媻  �9 I5 偛 -� 獊 �Z  � 袖 Ys  ℅ �- ┺ 籂 I � 谒  	�  Ⅷ  ,� ︳ by  憴 j� 矑  攒 � 臍 `� ↗ 西 蛴  |�  b� d� �;  u� 	 kN � 饌 甏  �" 欦 P� 棈 黆  瀗 河  �% 訅 7� 繒 � 摚 ?� yV  � �$ 惀  醮 $7 L� � �  � � � Wl ◇ 荔 D�  )�  鮕    i$  撱 7 丼  [� 孤 @�  帓 D� > 擽 $1  鯷 �  �4 l 哕  � 补 _G e� Q� 8� 屏 z�  � 怌 �$ v 
� �6 蓜 Y2  *� \� 廓 g� 歮  $R P8 �g  0� f
  �5 褡  d& Y�  佚 瓻    d  喡 9�  +�  � y\ c �!  '2 水 P; !r V7  � � �+ J  氡 Q :�  / j  揙 噯  X  �) � r� 旆 <2 舦 <�  老 +� Zj 2c 蹏  {� � 瀌 �  ┇ I� 伛 蝰 蟛 �( {� n� �   弅   � SX 伒  -C � 擲 � � �2 儸 "� q� 坪 gS 岳 逳 嶚 恰  鲐 婧  �  lD } 佛 醏 �8  "q  ]� 磵  聃 勵 殊 A�  ζ � *4  � M Eg 藵 垇 ◆ 差 誴  j� ヨ 蜾 辒 熃 X�  5 k� ` b�   � 菧 峫  j� k � �:  Z  p� 駶 -a i2 �# `� #� Wc Ⅰ w� � (� �  泉 �  姗 佐 擄 峒  
� 勐 Z 騮 e! 鷻 � 浡 跿  %j  � � d> 齟 �  齄  驜 � F% &=  �
 | 瘧 9  �  晬 vG �5  �  $ �� ︺ 蔟  婪 P� 榖 �4 8S @x �+   Cg � E�  vE 啋 柹  T UA �4 諵  �  m� , 3U  翥 鞚 u� l\ b� N�  �3  5 i< 瓜 ]� ほ *  秊 G� �6  )$ Ｆ 籨   I� 硚 鑫 膊 咵 浜 )� f� 6� ;'  � 確 K
 愷  桴 m r@  Yd  �  冺 ?� '�  徐 9� _�  �7 w� 矝 t [� 5�  U� 垆 k� /�  ~�  鳙 54 #� 5w o[ 郌 :   q� 頟 �   [2         �  H   �  @  �  @`  �  �  �  �  h  �  
  $�  �  D  �  �  @  @ �  ` L  � �  \� 2  � �  � �    Y  �  �  DE �  $` �  �    
      @      �   <  �  u  Z  �  �  j  <  �  U  �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       �      ~exception � �     what 篁�
 �   _Data �  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁�:  &�      �   std::exception .?AVexception@std@@ 篁�
 x   
 �    
 �    
 �    
 �    & 
 �    _What 
 0    _DoFree 蝰F   �           __std_exception_data .?AU__std_exception_data@@ 蝰 	   �  �    �      
 �    
 �   蝰
 �        �  �         �  
 �     	   �  �    �       	   �  �    �      
 �    
 �    
    �         �  
 �     �  #     �      #         �  B   �              std::bad_exception .?AVbad_exception@std@@ 篁�
 �   
 �  �  
    �   	   �  �   
 �      
 �   蝰
 �  ,  
    �   	   �  �   
 �       	   �  �   
           �             	   �  �           
 �  ,   	  �  �    �       	  �  �    �                	  �  �    &      �   �    蝰   bad_exception 蝰  ~bad_exception �   operator= 蝰  __local_vftable_ctor_closure 篁�      __vecDelDtor 篁馚 	 &	      �   std::bad_exception .?AVbad_exception@std@@ 篁� �  #     �
 �    :   �              std::bad_alloc .?AVbad_alloc@std@@ 篁�
 
   
 
  �  
       	   
     
       
 
   蝰
   ,  
       	   
     
        	   
     
 �       	   
     
        "                    	   
             
 
  ,   	  
             	  
                      	  
      &      �   �    蝰   bad_alloc 蝰  ~bad_alloc �   operator= 蝰  __local_vftable_ctor_closure 篁�      __vecDelDtor 篁�: 
 &      �   std::bad_alloc .?AVbad_alloc@std@@ 篁� �  #     � 	   
      �      
 
    N   �              std::bad_array_new_length .?AVbad_array_new_length@std@@ �
 $   
 $  �  
    &   	   $  %   
 '      
 $   蝰
 )  ,  
    *   	   $  %   
 +       	   $  %   
           (    ,     -   	   $  %           
 $  ,   	0  $  %    '       	0  $  %    +         1    2   	  $  %    &      �   
    蝰 .  bad_array_new_length 篁�/  ~bad_array_new_length 蝰 3  operator= 蝰/  __local_vftable_ctor_closure 篁�4      __vecDelDtor 篁馧 	 &5      �   std::bad_array_new_length .?AVbad_array_new_length@std@@ � �  #     �
 $    
 x     9        B   �              std::exception_ptr .?AVexception_ptr@std@@ 篁�
 ;   
 ;   蝰
 =  ,  
    >   	   ;  <   
 ?       	   ;  <   
 �       	   ;  <   
            @     A     B   	   ;  <           
 ;  ,   	E  ;  <    �       	E  ;  <    ?          F     G  
 =    	0   ;  I            	;  ;       	        
     蝰
 L          M   	;  ;        N       	  ;  <    &      �  C  exception_ptr 蝰 D  ~exception_ptr � H  operator= 蝰 J  operator bool 蝰 K  _Current_exception � O  _Copy_exception 
     _Data1 篁�
    _Data2 篁�P  __vecDelDtor 篁馚  fQ           std::exception_ptr .?AVexception_ptr@std@@ 篁�
           �  
 ;     	   ;  <    �       	   ;  <    ?            N  
 =    
    M   0     Z        M  M        \                ^      M  M   0     `         Z  6   �              _s__ThrowInfo .?AU_s__ThrowInfo@@ 
 c   蝰
 d    
 �    
         t      g  
 h    J   �              _s__CatchableTypeArray .?AU_s__CatchableTypeArray@@ 蝰
 j   蝰
 k    n 
 u     attributes 篁�
 f   pmfnUnwind 篁�
 i   pForwardCompat 篁�
 l   pCatchableTypeArray 蝰6   m          �18      �  <�  ��   ��     @;  @;  �   �;      J   �              ReSTIRGI_BufferIndices .?AUReSTIRGI_BufferIndices@@ 蝰:
 u     secondarySurfaceReSTIRDIOutputBufferIndex 
 u    temporalResamplingInputBufferIndex 篁�
 u    temporalResamplingOutputBufferIndex 蝰
 u    spatialResamplingInputBufferIndex 
 u    spatialResamplingOutputBufferIndex 篁�
 u    finalShadingInputBufferIndex �
 u    pad1 �
 u    pad2 馢               ReSTIRGI_BufferIndices .?AUReSTIRGI_BufferIndices@@ 蝰*    Off 蝰  Basic   Raytraced ^   u     ResTIRGI_TemporalBiasCorrectionMode .?AW4ResTIRGI_TemporalBiasCorrectionMode@@ �
     f   �              ReSTIRGI_TemporalResamplingParameters .?AUReSTIRGI_TemporalResamplingParameters@@ b
 @     depthThreshold 篁�
 @    normalThreshold 蝰
 u    enablePermutationSampling 
 u    maxHistoryLength �
 u    maxReservoirAge 蝰
 u    enableBoilingFilter 蝰
 @    boilingFilterStrength 
 u    enableFallbackSampling 篁�
     temporalBiasCorrectionMode 篁�
 u   $ uniformRandomNumber 蝰
 u   ( pad2 �
 u   , pad3 駀             0 ReSTIRGI_TemporalResamplingParameters .?AUReSTIRGI_TemporalResamplingParameters@@ ^   u     ResTIRGI_SpatialBiasCorrectionMode .?AW4ResTIRGI_SpatialBiasCorrectionMode@@ 篁�
 	    f   �              ReSTIRGI_SpatialResamplingParameters .?AUReSTIRGI_SpatialResamplingParameters@@ 蝰� 
 @     spatialDepthThreshold 
 @    spatialNormalThreshold 篁�
 u    numSpatialSamples 
 @    spatialSamplingRadius 
 	   spatialBiasCorrectionMode 
 u    pad1 �
 u    pad2 �
 u    pad3 駀               ReSTIRGI_SpatialResamplingParameters .?AUReSTIRGI_SpatialResamplingParameters@@ 蝰Z   �              ReSTIRGI_FinalShadingParameters .?AUReSTIRGI_FinalShadingParameters@@ ^ 
 u     enableFinalVisibility 
 u    enableFinalMIS 篁�
 u    pad1 �
 u    pad2 馴              ReSTIRGI_FinalShadingParameters .?AUReSTIRGI_FinalShadingParameters@@ J   �              rtxdi::ReSTIRGIContext .?AVReSTIRGIContext@rtxdi@@ 篁�
    Z   �              rtxdi::ReSTIRGIStaticParameters .?AUReSTIRGIStaticParameters@rtxdi@@ �
    蝰
   ,  
       	                    
    蝰
     	                	u                 Z   �              RTXDI_ReservoirBufferParameters .?AURTXDI_ReservoirBufferParameters@@  	               f    None �  Temporal �  Spatial 蝰  TemporalAndSpatial 篁�  FusedSpatiotemporal 蝰R   u     rtxdi::ReSTIRGI_ResamplingMode .?AW4ReSTIRGI_ResamplingMode@rtxdi@@  	                  	                 	                	                	               
    u    	          &      
        	          (      
    蝰
 *  ,  
    +   	          ,      
    蝰
 .  ,  
    /   	          0      
    蝰
 2  ,  
    3   	          4       	                 �   ReSTIRGIContext    GetStaticParams    GetFrameIndex 蝰   GetReservoirBufferParameters 篁� !  GetResamplingMode 蝰 "  GetBufferIndices 篁� #  GetTemporalResamplingParameters  $  GetSpatialResamplingParameters � %  GetFinalShadingParameters 蝰 '  SetFrameIndex 蝰 )  SetResamplingMode 蝰 -  SetTemporalResamplingParameters  1  SetSpatialResamplingParameters � 5  SetFinalShadingParameters 蝰 u   numReservoirBuffers 
     m_staticParams 篁�
 u    m_frameIndex �
    m_reservoirBufferParams 蝰
      m_resamplingMode �
    $ m_bufferIndices 蝰
   D m_temporalResamplingParams 篁�
   t m_spatialResamplingParams 
   � m_finalShadingParams � 6  UpdateBufferIndices J  7          � rtxdi::ReSTIRGIContext .?AVReSTIRGIContext@rtxdi@@ 篁� 	                
     
 :    &    Off 蝰  Black   White F   u   <  rtxdi::Checkerbo� �>  	% w  頉 映 薕 f� 鈾 G1 �  d�  �  s ` �% 亵  
� 	  慺 o� k@ CU f   �  5�  iw  蕋 l� �-  �"  惦 粧 翥 q:  嚊 � 瘞 軝 颋 "�  i
  �0  �1 � 讐    �= `�  [ G� 睽 k6 Nk �
  竻 D  (� 鴑 UI }1  �( e 桽 �  5C  q  �% _ 瘗 
� �* z� M� 殣  @b )� 紤 動 u� 摻 ?b   泱 碆  忆 {  酺 臉  T� C\  SF  稢 徒 � 竧  嘠 沪 �  彧 �  釥  � +H '�  ,� 茕    揑 笂 x�  .�  榳  髯 斯  勮  顗  XJ B� 谶 � e� �/ A� 湡 NT  �  唾 [�  t� 	� d� 檃 蜀 e� 嬝 !� q= = b *  9 � 烼 {� 	�   �>  z� � 鄊  7 緺 撞 粕 乗 啷 @? 闀 z� m1 紦 3�  鎝  汛  ;n  碤 �*   睒  嬛 �  X L� 偧  讧 檎 *� �  TZ  � ! �  f� C 侬 Vn xc �5 黸 愝 �3 �"  �"  �"  �"  �"  �"  u� � 竲 @� 姾  /I  N� 狺 鼳 � (�  曂 4� 祳 �  4  f[ 缥 昀  ,�  袁  =�  .8 騼 C%  Y1 崺 坝  薛 溱 4< ╲ 狦  �' 'b  @}  �  >8 -9 艫 礍 搗        �  0   �   @  �  `    �  �  �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �18      �  袪   ��   ��     �  �  0         F     D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h 蝰      _         !         7    	     )    
     J         W   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h           8    +   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h � =  
      A       >     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h  G  
  \        rtxdi 蝰2   Q  GetDefaultReSTIRGIBufferIndices nx	埘�5�>   T  GetDefaultReSTIRGITemporalResamplingParams 蕦t\逡	�>   W  GetDefaultReSTIRGISpatialResamplingParams 黄$a� 厄�:   Z  GetDefaultReSTIRGIFinalShadingParams 9碦$d{	$篁�     {ctor} BA誜�"     GetStaticParams 骐k劐齭"     GetFrameIndex 嬐覦M*B傭�2     GetReservoirBufferParameters if-�?�=篁�&   !  GetResamplingMode OG僋o穆蝰&   "  GetBufferIndices 薲渠 痖s篁�2   #  GetTemporalResamplingParameters �麭8龞,w2   $  GetSpatialResamplingParameters 9Y℡暩栺.   %  GetFinalShadingParameters n籺�拭蝰"   '  SetFrameIndex 爕`幞琪蝰&   )  SetResamplingMode 龘躠
�"7蝰2   -  SetTemporalResamplingParameters 鐪{�<2   1  SetSpatialResamplingParameters 襕.曘�<�.   5  SetFinalShadingParameters x0�+*�(蝰&   6  UpdateBufferIndices *=|b亦-   k  JenkinsHash 9j�nV6   I  CalculateReservoirBufferParameters :帒wj徿勸B     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi 蝰N     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\bin\HostX64\x64\CL.exe 蝰�     -c -ID:\RTXPT\External\Rtxdi\include -Zi -nologo -W1 -WX -diagnostics:column -MP -O2 -Ob2 -D_MBCS -DWIN32 -D_WINDOWS -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DNDEBUG -DRTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\") -DCMAKE_INTDIR=\"Release\" 聱      -EHs -EHc -MT -GS -fp:precise -Zc:wchar_t -Zc:forScope -Zc:inline -GR -std:c++20 -external:W1 -Gd -TP -errorreport:queue -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include 聆      -ID:\1softwares\VS2022\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" �      -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -I"C:\Program 篁颃      Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\include 蝰�      -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" -external:I"C:\Program 聱      Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -external:I"C:\Program 蝰"    '  (  )  *  +  ,  -  > .   Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -X 蝰2     D:\RTXPT\External\Rtxdi\Source\ReSTIRGI.cpp R     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb   %  &  0  1  /  蝰V     D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h  t  3      l  n  {ctor} =]U-斛媋�" l  o  allocateSegment 麺J�9�* l  r  getTotalSizeInElements hD87x馞     D:\RTXPT\External\Rtxdi\Source\RISBufferSegmentAllocator.cpp 篁�  %  &  8  1  /  蝰R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h 蝰 �  :     �  :    F     D:\1softardMode .?AW4CheckerboardMode@rtxdi@@ 蝰
     	     >           z 
 u     RenderWidth 蝰
 u    RenderHeight �
 =   CheckerboardSamplingMode �?  ReSTIRGIStaticParameters 篁馴  @           rtxdi::ReSTIRGIStaticParameters .?AUReSTIRGIStaticParameters@rtxdi@@ �
     
     
     
 D    f 
 u     reservoirBlockRowPitch 篁�
 u    reservoirArrayPitch 蝰
 u    pad1 �
 u    pad2 馴   F           RTXDI_ReservoirBufferParameters .?AURTXDI_ReservoirBufferParameters@@     u   u   =        H  
 =   蝰
 J    
 u    蝰
 L    
      
      
 O              
     
 R             
     
 U             
     
 X             
     
 B    
    蝰
 ]    
 ^    
     蝰
 `    
     蝰
 b    
 c    
 *    
 e    
 .    
 g    
 2    
 i     u      &  ^   �              rtxdi::RISBufferSegmentAllocator .?AVRISBufferSegmentAllocator@rtxdi@@ 篁�
 l    	   l  m            	u   l  m     &      
 l   蝰
 p    	u   l  q            ~  n  RISBufferSegmentAllocator 蝰 o  allocateSegment  r  getTotalSizeInElements �
 u     m_totalSizeInElements ^  s           rtxdi::RISBufferSegmentAllocator .?AVRISBufferSegmentAllocator@rtxdi@@ 篁� 	   l  m            
 l    
 p              
    @    t      y  
    A    t      {      @   @    t      }      A   A    t            A   t   A      �      A   t    A      �   A      {   A            A   A   A      �  
     蝰
     蝰*   �              _ldiv_t .?AU_ldiv_t@@ " 
      quot �
     rem 蝰*   �           _ldiv_t .?AU_ldiv_t@@ .   �              _lldiv_t .?AU_lldiv_t@@ 蝰" 
      quot �
     rem 蝰.   �           _lldiv_t .?AU_lldiv_t@@ 蝰 @      y   *        �  std::byte .?AW4byte@std@@ 蝰
 �   蝰
 �  ,      �  �   �    �  
 #    蝰
      蝰
 �   :   �              std::hash<float> .?AU?$hash@M@std@@ 蝰
 �   蝰
 �   
 @    蝰
    �   	#   �  �    �      R   @   _Unnameable_argument 篁�  #   _Unnameable_result � �  operator() �:  �           std::hash<float> .?AU?$hash@M@std@@ 蝰
 �  ,  
    �   #     �  
 �    :   �              std::hash<double> .?AU?$hash@N@std@@ �
 �   蝰
 �   
 A    蝰
    �   	#   �  �    �      R   A   _Unnameable_argument 篁�  #   _Unnameable_result � �  operator() �:  �           std::hash<double> .?AU?$hash@N@std@@ �
 �  ,  
    �   #     �  
 �    >   �              std::hash<long double> .?AU?$hash@O@std@@ 
 �   蝰
 �    	#   �  �    �      R   A   _Unnameable_argument 篁�  #   _Unnameable_result � �  operator() �>  �           std::hash<long double> .?AU?$hash@O@std@@ F   �              std::hash<std::nullptr_t> .?AU?$hash@$$T@std@@ 篁�
 �   蝰
 �   
       	#   �  �    �      R     _Unnameable_argument 篁�  #   _Unnameable_result � �  operator() 馞  �           std::hash<std::nullptr_t> .?AU?$hash@$$T@std@@ 篁�
     
 �  ,  
    �   #     �  
             �  :   �              std::exception .?AVexception@std@@ 篁�
 �    
  U�
 �    
 �   蝰
 �  ,  
    �   	   �  �   
 �      
 p    蝰
 �       �  t    	   �  �   
 �      
    �   	   �  �   
 �       	   �  �   
        "    �     �     �     �  
 �  ,   	�  �  �    �       	   �  �           
 �    
 �    	�  �  �            F   �              __std_exception_data .?AU__std_exception_data@@ 蝰 	  �  �    &      � 	  �   �  exception 蝰 �  operator= 蝰 _s__ThrowInfo .?AU_s__ThrowInfo@@  	   
            
      	   $  %    +      
 )    F   �              std::nested_exception .?AVnested_exception@std@@ �
 s    
  P�
 u    
 s   蝰
 w  ,  
    x   	   s  t   
 y       	   s  t   
            z     {  
 s  ,   	}  s  t    y       	   s  t           
 w    	   s  �             	;  s  �   	         	  s  t    &      � 	  v   |  nested_exception 篁� ~  operator= 蝰       ~nested_exception 蝰 �  rethrow_nested � �  nested_ptr �
 ;   _Exc �  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁馞 
 &�      u   std::nested_exception .?AVnested_exception@std@@ � ;   	    
 U    
 s    
 w    
 Y    
    ;         �           J   �              std::bad_variant_access .?AVbad_variant_access@std@@ �
 �   蝰
 �   
 �  �  
    �  
 �    	   �  �   
 �      
 �  ,  
    �   	   �  �   
 �       	   �  �   
           �    �     �   	�  �  �            	   �  �           
 �  ,   	�  �  �    �       	�  �  �    �         �    �   	  �  �    &      �   �    蝰 �  bad_variant_access � �  what 篁��  ~bad_variant_access  �  operator= 蝰�  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁馢 
 &�      �   std::bad_variant_access .?AVbad_variant_access@std@@ � �  #     �
 �     	   �  �    �      
 �    *   �              _iobuf .?AU_iobuf@@ 蝰
 �    
 q    蝰
 �    F   �              __crt_locale_pointers .?AU__crt_locale_pointers@@ 
 �        #   �  �  �  p   t      �  
 �   >   �              __crt_locale_data .?AU__crt_locale_data@@ 
 �    F   �              __crt_multibyte_data .?AU__crt_multibyte_data@@ 蝰
 �    * 
 �    locinfo 蝰
 �   mbcinfo 蝰F   �           __crt_locale_pointers .?AU__crt_locale_pointers@@ 
 �   
 �    
     _Placeholder �*   �           _iobuf .?AU_iobuf@@ 蝰 #            �  �  �  p   t      �   �     &  �   �              __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEAU__crt_locale_pointers@@@@ 篁癃    �           __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEAU__crt_locale_pointers@@@@ 篁�
 �    
 p    �   �              __vcrt_assert_va_start_is_not_reference<wchar_t const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEB_W@@ 駣    �           __vcrt_assert_va_start_is_not_reference<wchar_t const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEB_W@@ �
 �        #   q  #   �  �  p   t      �  
 q    
 t    蝰"    #   q  #   #   �  �  p   t      �      �  �  �  �  �  p   t      �      �  �  �  �  p   t      �      �  �  p   t      �      �  �  �  p   t      �  �   �              __vcrt_assert_va_start_is_not_reference<wchar_t const *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEB_W@@ 篁駟    �           __vcrt_assert_va_start_is_not_reference<wchar_t const *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEB_W@@ 篁�
 �        �  �  �  p   t      �      #   �  #   �  �  p   t      �      �  �  �  p   t      �  �   �              __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEAU__crt_locale_pointers@@@@ 瘼    �           __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> .?AU?$__vcrt_assert_va_start_is_not_reference@PEAU__crt_locale_pointers@@@@ �
 �        �  �  �  �  p   t      �      #   �  �  �  p   t      �      �  �  �  p   t      �  �   �              __vcrt_assert_va_start_is_not_reference<char const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEBD@@ 駟    �           __vcrt_assert_va_start_is_not_reference<char const * const> .?AU?$__vcrt_assert_va_start_is_not_reference@QEBD@@ �
 �        #   p  #   �  �  p   t      �  
 p        �  �  �  �  p   t      �  "    #   p  #   #   �  �  p   t      �      �  �  �  �  �  p   t      �      �  �  p   t      �      �  �  �  p   t      �      �  �  �  p   t      �      �  p   t      �      #   �  #   �  �  p   t      �      �  �  �  p   t      �      �  �  p   t      �   t        
 L       �  #    #        
 q        q  �     q           �  q    �           �  �   �           �  #    #      
      �  t    �           �  �   �           #   �  �  p   t            �  t   t   t   t  t    t        
 �    
       q       
 �       q  #      t        2   �              _stat64i32 .?AU_stat64i32@@ 蝰
         t      t        &   �              stat .?AUstat@@ 蝰
    � 
 u     st_dev 篁�
 !    st_ino 篁�
 !    st_mode 蝰
     st_nlink �
    
 st_uid 篁�
     st_gid 篁�
 u    st_rdev 蝰
     st_size 蝰
     st_atime �
      st_mtime �
    ( st_ctime �&              0 stat .?AUstat@@ 蝰2              0 _stat64i32 .?AU_stat64i32@@ 蝰    �     t      #  .   �              _Mbstatet .?AU_Mbstatet@@ 
 %   蝰
 &    : 
 "     _Wchar 篁�
 !    _Byte 
 !    _State 篁�.   (           _Mbstatet .?AU_Mbstatet@@ 
 "    蝰
 *        �  q   #    �     ,  .   �              type_info .?AVtype_info@@ 
 .   蝰
 /   
 /  ,  
    1  
 .    	   .  3    2      
 .  ,   	5  .  3     2       	#   .  0            	0   .  0    2       	�  .  0            	   .  3           F   �              __std_type_info_data .?AU__std_type_info_data@@ 蝰 	  .  3    &      � 	  v   4  type_info 蝰 6  operator= 蝰 7  hash_code 蝰 8  operator== � 8  before � 9  name 篁� 9  raw_name 篁� :      ~type_info �
 ;   _Data <      __vecDelDtor 篁�.  &=      u   type_info .?AVtype_info@@ 
 ;   蝰
 ?    
    @   #      A  
 /    
 ;    
 D     �  #     �
 ;  �  
    G  
 ;    	   ;  I    H      
 ?  ,  
    K   	   ;  I    L       	   ;  I               J     M     N  
 ;  ,   	P  ;  I     H       	P  ;  I     L          Q     R  n 
 �    _UndecoratedName �
 F   _DecoratedName 篁� O  __std_type_info_data 篁� S  operator= 蝰F  &T           __std_type_info_data .?AU__std_type_info_data@@ 蝰    @  @   t      V  >   �              __type_info_node .?AU__type_info_node@@ 蝰
 X        D  Y   �     Z  
 F    6   �              std::bad_cast .?AVbad_cast@std@@ �
 ]   
 ]  �  
    _   	   ]  ^   
 `      
 ]   蝰
 b  ,  
    c   	   ]  ^   
 d       	   ]  ^   
 �       	   ]  ^   
        "   a    e     f     g   	]  ]       	 �       	   ]  ^           
 ]  ,   	k  ]  ^    `       	k  ]  ^    d         l    m   	  ]  ^    &      �   �    蝰 h  bad_cast 篁� i  __construct_from_string_literal j  ~bad_cast 蝰 n  operator= 蝰j  __local_vftable_ctor_closure 篁�o      __vecDelDtor 篁�6  &p      �   std::bad_cast .?AVbad_cast@std@@ � �  #   	  �
 ]     	   ]  ^    �      :   �              std::bad_typeid .?AVbad_typeid@std@@ �
 u   
 u  �  
    w   	   u  v   
 x      
 u   蝰
 z  ,  
    {   	   u  v   
 |       	   u  v   
 �       	   u  v   
        "   y    }     ~        	u  u       	 �       	   u  v           
 u  ,   	�  u  v    x       	�  u  v    |         �    �   	  u  v    &      �   �    蝰 �  bad_typeid � �  __construct_from_string_literal �  ~bad_typeid  �  operator= 蝰�  __local_vftable_ctor_closure 篁��      __vecDelDtor 篁�:  &�      �   std::bad_typeid .?AVbad_typeid@std@@ � �  #     �
 u     	   u  v    �      J   �              std::__non_rtti_object .?AV__non_rtti_object@std@@ 篁�
 �     	�  �       	 �      
 �  �  
    �  
 �    	   �  �   
 �      
 �   蝰
 �  ,  
    �   	   �  �   
 �       	   �  �   
 �         �    �     �   	   �  �           
 �  ,   	�  �  �    �       	�  �  �    �         �    �   	  �  �    &      �   u    蝰 �  __construct_from_string_literal  �  __non_rtti_object 蝰�  ~__non_rtti_object � �  operator= 蝰�      __vecDelDtor 篁馢 	 &�      �   std::__non_rtti_object .?AV__non_rtti_object@std@@ 篁� 	   �  �    �       	   ]  ^    d      
 b    N   �              std::numeric_limits<bool> .?AV?$numeric_limits@_N@std@@ 蝰 	0   �               N   �              std::numeric_limits<char> .?AV?$numeric_limits@D@std@@ 篁� 	p   �               R   �              std::numeric_limits<signed char> .?AV?$numeric_limits@C@std@@  	   �               V   �              std::numeric_limits<unsigned char> .?AV?$numeric_limits@E@std@@ 蝰 	    �               R   �              std::numeric_limits<char16_t> .?AV?$numeric_limits@_S@std@@ 蝰 	z   �               R   �              std::numeric_limits<char32_t> .?AV?$numeric_limits@_U@std@@ 蝰 	{   �               R   �              std::numeric_limits<wchar_t> .?AV?$numeric_limits@_W@std@@ 篁� 	q   �               N   �              std::numeric_limits<short> .?AV?$numeric_limits@F@std@@ 蝰 	   �               J   �              std::numeric_limits<int> .?AV?$numeric_limits@H@std@@  	t   �               N   �              std::numeric_limits<long> .?AV?$numeric_limits@J@std@@ 篁� 	   �               R   �              std::numeric_limits<__int64> .?AV?$numeric_limits@_J@std@@ 篁� 	   �               V   �              std::numeric_limits<unsigned short> .?AV?$numeric_limits@G@std@@ � 	!   �               V   �              std::numeric_limits<unsigned int> .?AV?$numeric_limits@I@std@@ 篁� 	u   �               V   �              std::numeric_limits<unsigned long> .?AV?$numeric_limits@K@std@@ 蝰 	"   �               Z   �              std::numeric_limits<unsigned __int64> .?AV?$numeric_limits@_K@std@@ 蝰 	#   �               N   �              std::numeric_limits<float> .?AV?$numeric_limits@M@std@@ 蝰 	@   �               N   �              std::numeric_limits<double> .?AV?$numeric_limits@N@std@@ � 	A   �               R   �              std::numeric_limits<long double> .?AV?$numeric_limits@O@std@@  	A   �               
     蝰
 �    B   �              std::_Literal_zero .?AU_Literal_zero@std@@ 篁馚    �           std::_Literal_zero .?AU_Literal_zero@std@@ 篁馞   �              std::partial_ordering .?AUpartial_ordering@std@@ �
 �   蝰^  �  less 篁� �  equivalent � �  greater  �  unordered 蝰
      _Value 篁馞   �           std::partial_ordering .?AUpartial_ordering@std@@ �    �  �   0     �  
 �    
 �    B   �              std::weak_ordering .?AUweak_ordering@std@@ 篁�
 �   蝰
 �    	�  �  �   	        z  �  less 篁� �  equivalent � �  greater  �  operator struct std::partial_ordering 蝰
      _Value 篁馚  D�           std::weak_ordering .?AUweak_ordering@std@@ 篁�
 �        �  �   0     �  
 �    F   �              std::strong_ordering .?AUstrong_ordering@std@@ 篁�
 �   蝰
 �    	�  �  �   	         	�  �  �   	        �  �  less 篁� �  equal 蝰 �  equivalent � �  greater  �  operator struct std::partial_ordering 蝰 �  operator struct std::weak_ordering �
      _Value 篁馞  D�           std::strong_ordering .?AUstrong_ordering@std@@ 篁�
 �        �  �   0     �  
 �    J   �              std::_Unused_parameter .?AU_Unused_parameter@std@@ 篁�
 �   J    �           std::_Unused_parameter .?AU_Unused_parameter@std@@ 篁� 	   �  �           J   �              std::_Distance_unknown .?AU_Distance_unknown@std@@ 篁�
 �   蝰
 �    	�  �  �   	          �  operator- 蝰J  �           std::_Distance_unknown .?AU_Distance_unknown@std@@ 篁�
 �    V   �              std::ranges::_Not_quite_object .?AV_Not_quite_object@ranges@std@@ 
 �   v   �              std::ranges::_Not_quite_object::_Construct_tag .?AU_Construct_tag@_Not_quite_object@ranges@std@@ �
 �   蝰
 �  ,  
    �   	   �  �    �      
    �   	   �  �   
 �       	   �  �               �     �     �  
 �  ,   	   �  �     �      
 �    	   �              ^   �  _Construct_tag � �  _Not_quite_object 蝰   operator= 蝰   operator& 蝰V  6           std::ranges::_Not_quite_object .?AV_Not_quite_object@ranges@std@@  	   �  �    �      v   �           std::ranges::_Not_quite_object::_Construct_tag .?AU_Construct_tag@_Not_quite_object@ranges@std@@ 馢   �              std::ranges::_Advance_fn .?AV_Advance_fn@ranges@std@@ 
     	     	   
 �      "   �    蝰
  _Advance_fn J             std::ranges::_Advance_fn .?AV_Advance_fn@ranges@std@@  	     	    �      
 �    N   �              std::ranges::_Distance_fn .?AV_Distance_fn@ranges@std@@ 蝰
     	        
 �      &   �    蝰  _Distance_fn 篁馧             std::ranges::_Distance_fn .?AV_Distance_fn@ranges@std@@ 蝰 	         �      F   �              std::ranges::_Next_fn .?AV_Next_fn@ranges@std@@ 蝰
     	        
 �      "   �    蝰  _Next_fn 篁馞             std::ranges::_Next_fn .?AV_Next_fn@ranges@std@@ 蝰 	         �      F   �              std::ranges::_Prev_fn .?AV_Prev_fn@ranges@std@@ 蝰
     	        
 �      "   �    蝰  _Prev_fn 篁馞             std::ranges::_Prev_fn .?AV_Prev_fn@ranges@std@@ 蝰 	         �      F   �              std::ranges::dangling .?AUdangling@ranges@std@@ 蝰
 !   F    �           std::ranges::dangling .?AUdangling@ranges@std@@ 蝰 	   !  "           F   �              std::ranges::_Copy_fn .?AV_Copy_fn@ranges@std@@ 蝰
 %    	   %  &   
 �      "   �    蝰'  _Copy_fn 篁馞  (           std::ranges::_Copy_fn .?AV_Copy_fn@ranges@std@@ 蝰 	   %  &    �      J   �              std::ranges::_Fill_n_fn .?AV_Fill_n_fn@ranges@std@@ 蝰
 +    	   +  ,   
 �      "   �    蝰-  _Fill_n_fn 馢  .           std::ranges::_Fill_n_fn .?AV_Fill_n_fn@ranges@std@@ 蝰 	   +  ,    �      N   �              std::ranges::_Mismatch_fn .?AV_Mismatch_fn@ranges@std@@ 蝰
 1    	   1  2   
 �      &   �    蝰3  _Mismatch_fn 篁馧  4           std::ranges::_Mismatch_fn .?AV_Mismatch_fn@ranges@std@@ 蝰 	   1  2    �      F   �              std::ranges::_Find_fn .?AV_Find_fn@ranges@std@@ 蝰
 7    	   7  8   
 �      "   �    蝰9  _Find_fn 篁馞  :           std::ranges::_Find_fn .?AV_Find_fn@ranges@std@@ 蝰 	   7  8    �      J   �              std::ranges::_Find_if_fn .?AV_Find_if_fn@ranges@std@@ 
 =    	   =  >   
 �      "   �    蝰?  _Find_if_fn J  @           std::ranges::_Find_if_fn .?AV_Find_if_fn@ranges@std@@  	   =  >    �      R   �              std::ranges::_Find_if_not_fn .?AV_Find_if_not_fn@ranges@std@@ 
 C    	   C  D   
 �      &   �    蝰E  _Find_if_not_fn R  F           std::ranges::_Find_if_not_fn .?AV_Find_if_not_fn@ranges@std@@  	   C  D    �      V   �              std::ranges::_Adjacent_find_fn .?AV_Adjacent_find_fn@ranges@std@@ 
 I    	   I  J   
 �      *   �    蝰K  _Adjacent_find_fn 蝰V  L           std::ranges::_Adjacent_find_fn .?AV_Adjacent_find_fn@ranges@std@@  	   I  J    �      J   �              std::ranges::_Search_fn .?AV_Search_fn@ranges@std@@ 蝰
 O    	   O  P   
 �      "   �    蝰Q  _Search_fn 馢  R           std::ranges::_Search_fn .?AV_Search_fn@ranges@std@@ 蝰 	   O  P    �      R   �              std::ranges::_Max_element_fn .?AV_Max_element_fn@ranges@std@@ 
 U    	   U  V   
 �      &   �    蝰W  _Max_element_fn R  X           std::ranges::_Max_element_fn .?AV_Max_element_fn@ranges@std@@  	   U  V    �      B   �              std::ranges::_Max_fn .?AV_Max_fn@ranges@std@@ 
 [    	   [  \   
 �         �    蝰]  _Max_fn B  ^           std::ranges::_Max_fn .?AV_Max_fn@ranges@std@@  	   [  \    �      R   �              std::ranges::_Min_element_fn .?AV_Min_element_fn@ranges@std@@ 
 a    	   a  b   
 �      &   �    蝰c  _Min_element_fn R  d           std::ranges::_Min_element_fn .?AV_Min_element_fn@ranges@std@@  	   a  b    �      B   �              std::ranges::_Min_fn .?AV_Min_fn@ranges@std@@ 
 g    	   g  h   
 �         �    蝰i  _Min_fn B  j           std::ranges::_Min_fn .?AV_Min_fn@ranges@std@@  	   g  h    �      :   �              std::tuple<> .?AV?$tuple@$$V@std@@ 篁�
 m   
 m   蝰
 o  ,  
    p   	   m  n   
 q      
 m  ,  
    s   	   m  n    t      
 o    	0   m  v    q       	�  m  v   	 q      N  r  tuple<>  u  swap 篁� w  _Equals  x  _Three_way_compare �:  y           std::tuple<> .?AV?$tuple@$$V@std@@ 篁� 	   m  n    q      
    #         |  6    #   �  std::align_val_t .?AW4align_val_t@std@@     #   ~          
 #   ,  
   ,  
 �   R   �              std::_Asan_aligned_pointers .?AU_Asan_aligned_pointers@std@@ �
 �   蝰
 �    	M  �  �    Z      > 
 M    _First 篁�
 M   _End � �  _Clamp_to_end 蝰R   �           std::_Asan_aligned_pointers .?AU_Asan_aligned_pointers@std@@ �
 �    
      
 �    
 M    F   �              std::_Container_base0 .?AU_Container_base0@std@@ �
 �    	   �  �           
 �  ,  
    �   	   �  �    �      F   �              std::_Fake_allocator .?AU_Fake_allocator@std@@ 篁�
 �   蝰
 �  ,  
    �   	   �  �    �          �  �   	   �  �    �      j  �  _Orphan_all  �  _Swap_proxy_and_iterators 蝰 �  _Alloc_proxy 篁� �  _Reload_proxy 蝰F   �           std::_Container_base0 .?AU_Container_base0@std@@ 馞    �           std::_Fake_allocator .?AU_Fake_allocator@std@@ 篁馞   �              std::_Iterator_base0 .?AU_Iterator_base0@std@@ 篁�
 �    	   �  �    Z      
 �   蝰
 �    
 �   蝰
 �    	�  �  �           
 0    蝰F  �  _Adopt � �  _Getcont 篁� �  _Unwrap_when_unverified F   �           std::_Iterator_base0 .?AU_Iterator_base0@std@@ 篁馞   �              std::_Container_proxy .?AU_Container_proxy@std@@ �
 �   J   �              std::_Container_base12 .?AU_Container_base12@std@@ 篁�
 �    
    �   	   �  �   
 �       	   �  �   
            �     �  
 �   蝰
 �    F   �              std::_Iterator_base12 .?AU_Iterator_base12@std@@ �
 �    J  �  _Container_proxy 篁�
 �    _Mycont 蝰
 �   _Myfirstiter 馞  �           std::_Container_proxy .?AU_Container_proxy@std@@ � 	   �  �    �      
 �    
 �    
 �     	   �  �           
 �   
 �  ,  
    �   	   �  �    �       	   �  �   
            �     �  
 �  ,   	�  �  �     �       	   �  �           
    �   	   �  �    �       �  _Container_base12 蝰 �  operator= 蝰 �  _Orphan_all  �  _Swap_proxy_and_iterators 蝰
 �    _Myproxy � �  _Orphan_all_unlocked_v3  �  _Swap_proxy_and_iterators_unlocked � �  _Orphan_all_locked_v3 蝰 �  _Swap_proxy_and_iterators_locked 篁馢 
 &�           std::_Container_base12 .?AU_Container_base12@std@@ 篁�6   �              std::_Lockit .?AV_Lockit@std@@ 篁�
 �   
 �   蝰
 �  ,  
    �   	   �  �    �      
    t    	   �  �   
 �       	   �  �   
            �     �     �   	   �  �           
 �        �  t    	   �        �      
    �   	   �        �       	   �        �       	   �  	   �     �   	   �     �  
 �  ,   	�  �  �     �       	  �  �    &      �  �  _Lockit  �  ~_Lockit 篁� �  _Lockit_ctor 篁� �  _Lockit_dtor 篁� �  operator= 蝰
 t     _Locktype �  __vecDelDtor 篁�6  &�           std::_Lockit .?AV_Lockit@std@@ 篁� 	   �  �    �      
 �    
 �   
 �   蝰
 �  ,  
    �   	   �  �   
 �       	   �  �   
            �     �  
 �  ,   	�  �  �    �      
    �   	   �  �    �      
 �    	�  �  �           �  �  _Iterator_base12 篁� �  operator= 蝰 �  _Adopt � �  _Getcont 篁� �  _Unwrap_when_unverified 
 �    _Myproxy �
 �   _Mynextiter 蝰F  &�           std::_Iterator_base12 .?AU_Iterator_base12@std@@ � 	   �  �    �      
 �    
 �     	   �  �           
 �  ,  
   �      �  �   �    �  
     
 �    N   �              std::_Fake_proxy_ptr_impl .?AU_Fake_proxy_ptr_impl@std@@ �
 �   
 �  ,      �  �   	   �  �   
 �      N   �              std::_Leave_proxy_unbound .?AU_Leave_proxy_unbound@std@@ �    �     	   �  �   
       
 �   蝰
   ,  
       	   �  �                           
 �  ,   		  �  �           
 �        �     	   �  �           	   �  �           Z    _Fake_proxy_ptr_impl 篁� 
  operator= 蝰 
  _Bind 蝰   _Release 篁馧  &           std::_Fake_proxy_ptr_impl .?AU_Fake_proxy_ptr_impl@std@@ � 	   �  �          N    �           std::_Leave_proxy_unbound .?AU_Leave_proxy_unbound@std@@ � 	   �  �    �      ^   �              std::_Basic_container_proxy_ptr12 .?AU_Basic_container_proxy_ptr12@std@@ �
     	                
   �  
       	               
    蝰
   ,  
       	                	                                N 
 �    _Ptr �   _Release 篁�   _Basic_container_proxy_ptr12 篁馸              std::_Basic_container_proxy_ptr12 .?AU_Basic_container_proxy_ptr12@std@@ �
      p                  A      $  "   �              tm .?AUtm@@ 蝰
 &    � 
 t     tm_sec 篁�
 t    tm_min 篁�
 t    tm_hour 蝰
 t    tm_mday 蝰
 t    tm_mon 篁�
 t    tm_year 蝰
 t    tm_wday 蝰
 t    tm_yday 蝰
 t     tm_isdst �" 	  (          $ tm .?AUtm@�.1N馟h   骓騝�谽樣�1�Pj   /names                            躋3ternal\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h 蝰      _         !         7    	     )    
     J         W   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h           8    +   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h � =  
      A       >     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h  G  
  \        rtxdi 蝰2   Q  GetDefaultReSTIRGIBufferIndices nx	埘�5�>   T  GetDefaultReSTIRGITemporalResamplingParams 蕦t\逡	�>   W  GetDefaultReSTIRGISpatialResamplingParams 黄$a� 厄�:   Z  GetDefaultReSTIRGIFinalShadingParams 9碦$d{	$篁�     {ctor} BA誜�"     GetStaticParams 骐k劐齭"     GetFrameIndex 嬐覦M*B傭�2     GetReservoirBufferParameters if-�?�=篁�&   !  GetResamplingMode OG僋o穆蝰&   "  GetBufferIndices 薲渠 痖s篁�2   #  GetTemporalResamplingParameters �麭8龞,w2   $  GetSpatialResamplingParameters 9Y℡暩栺.   %  GetFinalShadingParameters n籺�拭蝰"   '  SetFrameIndex 爕`幞琪蝰&   )  SetResamplingMode 龘躠
�"7蝰2   -  SetTemporalResamplingParameters 鐪{�<2   1  SetSpatialResamplingParameters 襕.曘�<�.   5  SetFinalShadingParameters x0�+*�(蝰&   6  UpdateBufferIndices *=|b亦-   k  JenkinsHash 9j�nV6   I  CalculateReservoirBufferParameters :帒wj徿勸B     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi 蝰N     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\bin\HostX64\x64\CL.exe 蝰�     -c -ID:\RTXPT\External\Rtxdi\include -Zi -nologo -W1 -WX -diagnostics:column -MP -O2 -Ob2 -D_MBCS -DWIN32 -D_WINDOWS -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DNDEBUG -DRTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\") -DCMAKE_INTDIR=\"Release\" 聱      -EHs -EHc -MT -GS -fp:precise -Zc:wchar_t -Zc:forScope -Zc:inline -GR -std:c++20 -external:W1 -Gd -TP -errorreport:queue -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include 聆      -ID:\1softwares\VS2022\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" �      -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -I"C:\Program 篁颃      Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\include 蝰�      -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" -external:I"C:\Program 聱      Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -external:I"C:\Program 蝰"    '  (  )  *  +  ,  -  > .   Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -X 蝰2     D:\RTXPT\External\Rtxdi\Source\ReSTIRGI.cpp R     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb   %  &  0  1  /  蝰V     D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h  t  3      l  n  {ctor} =]U-斛媋�" l  o  allocateSegment 麺J�9�* l  r  getTotalSizeInElements hD87x馞     D:\RTXPT\External\Rtxdi\Source\RISBufferSegmentAllocator.cpp 篁�  %  &  8  1  /  蝰R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h 蝰 �  :     �  :    F     D:\1soft`k 5� 贱 �2 ^� 畡 螬 �8 f� 9� � @6 辺 L� 魺 8 �  Q O  觀 0� `v  刀 [h  旼 夦  !�  ZF ┉ 蜣 Uj X 3 :�  S�  w� ]L 夐 1 N�  o&  ,�  � #N  王 撃 珘 廅 \�  �	  带 萝  ^� 蔛 歶 � 穡 f� 鄨  K� 臋  Y" �  _` $� #� ZA ]� yJ e& 旌 <K 趡 瑳 欥 ER }� 儚 �1 遯  e�  �+ �( � � 0�  卣 w| C� }� 2� b�  )~ � 娇 _O .� >m 孛 Qj  嗧 /� � 2a T 冑 � 復  笽 涻 %
  W� aE  6 u] � n� 诓 鱗  盃  瀋 o 碕   鳯  �  	7  4 |z �2 �3 kQ  )�  �)  礰   � i� h  1� �< � H� � ~@ _  鈘 %� 鞦  �2  �K  北  ^� 肛 瀘 岃  + 蚅 秡 鏽 雱  �� 慢 D� + S� ?� 膒 ╜ 釙 灟 �$ 
B  5� 4; 循 蹨 嬖 觅 E  ~� 仢 A� 嵊 目  o� o.  � み he  J) �  �  康 xa �C _� � 0� 0D 3� o9 镅 谼  沅 � G�  N< .�  詆 �
 � ea 柪 m�  f� 划   w�  *� r� 橾 ?� 糬  .d  0�  �  sF $ � 嚉 L�  �  惨 >� 聸  1y $� + CA ]�  豣  衡 糛 @� K� @: 0| g� p( 暷 堙 �'  �"  
 楰 葲 b� 僎 魠 T` � ,� < ^ e)  /� �r �8 �  r [� ] 肟  倪 妎  8�  萮 』 89  z0 "� # n 歞  "� � 纣 祣  3� V� H�  魨 U: Bl 矝 =<  �  �  讇  浸 蕸 廲 T ) 锱 鍛 �  鞾 � 9h  {n 峇 ?<  aG 続 旳  i� 药  � � 恑 pr �� 塥 禆 媻  �9 I5 偛 -� 獊 �Z  � 袖 Ys  ℅ �- ┺ 籂 I � 谒  	�  Ⅷ  ,� ︳ by  憴 j� 矑  攒 � 臍 `� ↗ 西 蛴  |�  b� d� �;  u� 	 kN � 饌 甏  �" 欦 P� 棈 黆  瀗 河  �% 訅 7� 繒 � 摚 ?� yV  � �$ 惀  醮 $7 L� � �  � � � Wl ◇ 荔 D�  )�  鮕    i$  撱 7 丼  [� 孤 @�  帓 D� > 擽 $1  鯷 �  �4 l 哕  � 补 _G e� Q� 8� 屏 z�  � 怌 �$ v 
� �6 蓜 Y2  *� \� 廓 g� 歮  $R P8 �g  0� f
  �5 褡  d& Y�  佚 瓻    d  喡 9�  +�  � y\ c �!  '2 水 P; !r V7  � � �+ J  氡 Q :�  / j  揙 噯  X  �) � r� 旆 <2 舦 <�  老 +� Zj 2c 蹏  {� � 瀌 �  ┇ I� 伛 蝰 蟛 �( {� n� �   弅   � SX 伒  -C � 擲 � � �2 儸 "� q� 坪 gS 岳 逳 嶚 恰  鲐 婧  �  lD } 佛 醏 �8  "q  ]� 磵  聃 勵 殊 A�  ζ � *4  � M Eg 藵 垇 ◆ 差 誴  j� ヨ 蜾 辒 熃 X�  5 k� ` b�   � 菧 峫  j� k � �:  Z  p� 駶 -a i2 �# `� #� Wc Ⅰ w� � (� �  泉 �  姗 佐 擄 峒  
� 勐 Z 騮 e! 鷻 � 浡 跿  %j  � � d> 齟 �  齄  驜 � F% &=  �
 | 瘧 9  �  晬 vG �5  �  $ �� ︺ 蔟  婪 P� 榖 �4 8S @x �+   Cg � E�  vE 啋 柹  T UA �4 諵  �  m� , 3U  翥 鞚 u� l\ b� N�  �3  5 i< 瓜 ]� ほ *  秊 G� �6  )$ Ｆ 籨   I� 硚 鑫 膊 咵 浜 )� f� 6� ;'  � 確 K
 愷  桴 m r@  Yd  �  冺 ?� '�  徐 9� _�  �7 w� 矝 t [� 5�  U� 垆 k� /�  ~�  鳙 54 #� 5w o[ 郌 :   q� 頟 �   [2         �  H   �  @  �  @`  �  �  �  �  h  �  
  $�  �  D  �  �  @  @ �  ` L  � �  \� 2  � �  � �    Y  �  �  DE �  $` �  �    
      �           �  u  Z  �  �  j  <  �  U  �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      �18      �  <�  ��   ��     @;  @;  �   �;  D   J   �              ReSTIRGI_BufferIndices .?AUReSTIRGI_BufferIndices@@ 蝰:
 u     secondarySurfaceReSTIRDIOutputBufferIndex 
 u    temporalResamplingInputBufferIndex 篁�
 u    temporalResamplingOutputBufferIndex 蝰
 u    spatialResamplingInputBufferIndex 
 u    spatialResamplingOutputBufferIndex 篁�
 u    finalShadingInputBufferIndex �
 u    pad1 �
 u    pad2 馢               ReSTIRGI_BufferIndices .?AUReSTIRGI_BufferIndices@@ 蝰*    Off 蝰  Basic   Raytraced ^   u     ResTIRGI_TemporalBiasCorrectionMode .?AW4ResTIRGI_TemporalBiasCorrectionMode@@ �
     f   �              ReSTIRGI_TemporalResamplingParameters .?AUReSTIRGI_TemporalResamplingParameters@@ b
 @     depthThreshold 篁�
 @    normalThreshold 蝰
 u    enablePermutationSampling 
 u    maxHistoryLength �
 u    maxReservoirAge 蝰
 u    enableBoilingFilter 蝰
 @    boilingFilterStrength 
 u    enableFallbackSampling 篁�
     temporalBiasCorrectionMode 篁�
 u   $ uniformRandomNumber 蝰
 u   ( pad2 �
 u   , pad3 駀             0 ReSTIRGI_TemporalResamplingParameters .?AUReSTIRGI_TemporalResamplingParameters@@ ^   u     ResTIRGI_SpatialBiasCorrectionMode .?AW4ResTIRGI_SpatialBiasCorrectionMode@@ 篁�
 	    f   �              ReSTIRGI_SpatialResamplingParameters .?AUReSTIRGI_SpatialResamplingParameters@@ 蝰� 
 @     spatialDepthThreshold 
 @    spatialNormalThreshold 篁�
 u    numSpatialSamples 
 @    spatialSamplingRadius 
 	   spatialBiasCorrectionMode 
 u    pad1 �
 u    pad2 �
 u    pad3 駀               ReSTIRGI_SpatialResamplingParameters .?AUReSTIRGI_SpatialResamplingParameters@@ 蝰Z   �              ReSTIRGI_FinalShadingParameters .?AUReSTIRGI_FinalShadingParameters@@ ^ 
 u     enableFinalVisibility 
 u    enableFinalMIS 篁�
 u    pad1 �
 u    pad2 馴              ReSTIRGI_FinalShadingParameters .?AUReSTIRGI_FinalShadingParameters@@ J   �              rtxdi::ReSTIRGIContext .?AVReSTIRGIContext@rtxdi@@ 篁�
    Z   �              rtxdi::ReSTIRGIStaticParameters .?AUReSTIRGIStaticParameters@rtxdi@@ �
    蝰
   ,  
       	                    
    蝰
     	                	u                 Z   �              RTXDI_ReservoirBufferParameters .?AURTXDI_ReservoirBufferParameters@@  	               f    None �  Temporal �  Spatial 蝰  TemporalAndSpatial 篁�  FusedSpatiotemporal 蝰R   u     rtxdi::ReSTIRGI_ResamplingMode .?AW4ReSTIRGI_ResamplingMode@rtxdi@@  	                  	                 	                	                	               
    u    	          &      
        	          (      
    蝰
 *  ,  
    +   	          ,      
    蝰
 .  ,  
    /   	          0      
    蝰
 2  ,  
    3   	          4       	                 �   ReSTIRGIContext    GetStaticParams    GetFrameIndex 蝰   GetReservoirBufferParameters 篁� !  GetResamplingMode 蝰 "  GetBufferIndices 篁� #  GetTemporalResamplingParameters  $  GetSpatialResamplingParameters � %  GetFinalShadingParameters 蝰 '  SetFrameIndex 蝰 )  SetResamplingMode 蝰 -  SetTemporalResamplingParameters  1  SetSpatialResamplingParameters � 5  SetFinalShadingParameters 蝰 u   numReservoirBuffers 
     m_staticParams 篁�
 u    m_frameIndex �
    m_reservoirBufferParams 蝰
      m_resamplingMode �
    $ m_bufferIndices 蝰
   D m_temporalResamplingParams 篁�
   t m_spatialResamplingParams 
   � m_finalShadingParams � 6  UpdateBufferIndices J  7          � rtxdi::ReSTIRGIContext .?AVReSTIRGIContext@rtxdi@@ 篁� 	                
     
 :    &    Off 蝰  Black   White F   u   <  rtxdi::Checkerbodi::ReSTIRDIContext *,1> std::default_delete<rtxdi::RISBufferSegmentAllocator> std::_Compressed_pair<std::default_delete<rtxdi::RISBufferSegmentAllocator>,rtxdi::RISBufferSegmentAllocator *,1> std::_Atomic_padded<long> rtxdi::ReGIROnionCalculatedParameters std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > std::allocator<ReGIR_OnionRing> std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1> std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > ReGIR_OnionRing std::allocator<ReGIR_OnionLayerGroup> std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1> std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > ReGIR_OnionLayerGroup std::_Zero_then_variadic_args_t std::float_denorm_style std::float_round_style std::_Num_base std::_Num_int_base std::numeric_limits<bool> std::numeric_limits<char> std::numeric_limits<signed char> std::numeric_limits<unsigned char> std::numeric_limits<char16_t> std::numeric_limits<char32_t> std::numeric_limits<wchar_t> std::numeric_limits<short> std::numeric_limits<int> std::numeric_limits<long> std::numeric_limits<__int64> std::numeric_limits<unsigned short> std::numeric_limits<unsigned int> std::numeric_limits<unsigned long> std::numeric_limits<unsigned __int64> std::_Num_float_base std::numeric_limits<float> std::numeric_limits<double> std::numeric_limits<long double> <unnamed-enum-_Atomic_memory_order_relaxed> _s__RTTIBaseClassDescriptor _s__RTTICompleteObjectLocator2 $_s__RTTIBaseClassArray$_extraBytes_24 RTXDI_RuntimeParameters $_TypeDescriptor$_extraBytes_20 _s__RTTIClassHierarchyDescriptor $_s__RTTIBaseClassArray$_extraBytes_16 _s__CatchableTypeArray $_s__CatchableTypeArray$_extraBytes_24 __vcrt_va_list_is_reference<wchar_t const *>::<unnamed-enum-__the_value> __vcrt_va_list_is_reference<wchar_t const *> $_TypeDescriptor$_extraBytes_24 __vcrt_va_list_is_reference<char const *>::<unnamed-enum-__the_value> __vcrt_va_list_is_reference<char const *> ReSTIRDI_ShadingParameters $_TypeDescriptor$_extraBytes_27 _s__RTTIBaseClassArray RTXDI_EnvironmentLightBufferParameters $_TypeDescriptor$_extraBytes_21 $_s__CatchableTypeArray$_extraBytes_16 $_s__RTTIBaseClassArray$_extraBytes_8 _PMD $_TypeDescriptor$_extraBytes_19 RTXDI_LightBufferRegion ReSTIRDI_SpatialBiasCorrectionMode ReSTIRDI_SpatialResamplingParameters __vcrt_va_list_is_reference<__crt_locale_pointers *>::<unnamed-enum-__the_value> __vcrt_va_list_is_reference<__crt_locale_pointers *> ReSTIRDI_TemporalBiasCorrectionMode ReSTIRDI_TemporalResamplingParameters _s__CatchableType std::_Comparison_category std::ranges::subrange_kind std::_Invoker_strategy std::_Compare_eq std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> > std::allocator_traits<std::allocator<ReGIR_OnionRing> > std::_Default_allocate_traits std::integral_constant<bool,1> std::_Itraits_pointer_strategy std::integral_constant<bool,0> std::_Compare_ord std::_Compare_ncmp std::numeric_limits<char8_t> std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > $_TypeDescriptor$_extraBytes_28 $_TypeDescriptor$_extraBytes_26 __vcrt_va_list_is_reference<wchar_t const * const>::<unnamed-enum-__the_value> __vcrt_va_list_is_reference<wchar_t const * const> rtxdi::ReGIRGridCalculatedParameters rtxdi::ReGIROnionStaticParameters rtxdi::ReGIRGridStaticParameters rtxdi::float3 rtxdi::uint3 __vcrt_va_list_is_reference<__crt_locale_pointers * const>::<unnamed-enum-__the_value> __vcrt_va_list_is_reference<__crt_locale_pointers * const> _TypeDescriptor $_TypeDescriptor$_extraBytes_29 ReSTIRDI_BufferIndices $_TypeDescriptor$_extraBytes_23 __vcrt_va_list_is_reference<char const * const>::<unnamed-enum-__the_value> __vcrt_va_lis@ 蝰 '       
    '         +  
 &   
             .  
     2   �              _timespec64 .?AU_timespec64@@ 
 1        2  t    t      3  .   �              timespec .?AUtimespec@@ 蝰
 5   * 
      tv_sec 篁�
     tv_nsec 蝰.   7           timespec .?AUtimespec@@ 蝰2   7           _timespec64 .?AU_timespec64@@     p  #      t      :      '     t      <  
     蝰
 >    
    L        @  
   relaxed 蝰  consume 蝰  acquire 蝰  release 蝰  acq_rel 蝰  seq_cst 蝰   memory_order_relaxed �  memory_order_consume �  memory_order_acquire �  memory_order_release �  memory_order_acq_rel �  memory_order_seq_cst �:   t   B  std::memory_order .?AW4memory_order@std@@ 蝰
 C   蝰
    D        E   D  #     � G  #   �  �
 t    蝰
 I    
    ,  
    S         L                       N      #     �>   �              std::atomic_flag .?AUatomic_flag@std@@ 篁�
 Q   蝰
 R   
 Q   蝰
 T    	0   Q  U    E       	0   Q  S    E          V     W  
 Q   蝰
 Y    	0   Q  Z    E      
 Q    	0   Q  \    E          [     ]   	   Q  Z    E       	   Q  \    E          _     `   	   Q  \   
            �  D   	   Q  U    c       	   Q  S    c          d     e   	   Q  Z            	   Q  \               g     h  >   �              std::atomic<long> .?AU?$atomic@J@std@@ 篁駳  X  test 篁� ^  test_and_set 篁� a  clear 蝰 b  atomic_flag  f  wait 篁� i  notify_one � i  notify_all �
 j    _Storage �>  k           std::atomic_flag .?AUatomic_flag@std@@ 篁馬   �              std::_Atomic_storage<long,4> .?AU?$_Atomic_storage@J$03@std@@ 
 m   蝰
 n       �  D  
 m    	   m  q    p      
    �   	   m  q    s          r     t   	   m  o    E       	   m  o               v     w   	   m  q    p          K  �  D   	0   m  q    z       	   m  o    p       	   m  q           N   �              std::_Atomic_padded<long> .?AU?$_Atomic_padded@J@std@@ 篁癫      _TVal 蝰 u  store 蝰 x  load 篁� y  exchange 篁� {  compare_exchange_strong  |  wait 篁� }  notify_one � }  notify_all �
 ~    _Storage 馬             std::_Atomic_storage<long,4> .?AU?$_Atomic_storage@J$03@std@@ V   �              std::_Atomic_integral<long,4> .?AU?$_Atomic_integral@J$03@std@@ 蝰
 �    	   �  �    p       	   �  �            	   �  �    �          �     �  �   m    蝰  m  _Base 蝰 �  fetch_add 蝰 �  fetch_and 蝰 �  fetch_or 篁� �  fetch_xor 蝰 �  operator++ � �  operator-- 馰 
 �           std::_Atomic_integral<long,4> .?AU?$_Atomic_integral@J$03@std@@ 蝰^   �              std::_Atomic_integral_facade<long> .?AU?$_Atomic_integral_facade@J@std@@ �
 �   蝰
 �    	   �  �    p       	   �  �    s          �     �   	   �        s      
 �    	   �  �    p       	   �  �    s      "    �     �     �     �   	   �  �            	   �  �    �          �     �      �     �  6  �    蝰  �  _Base 蝰     difference_type  �  fetch_add 蝰 �  _Negate  �  fetch_sub 蝰 �  fetch_and 蝰 �  fetch_or 篁� �  fetch_xor 蝰 �  operator++ � �  operator-- � �  operator+= � �  operator-= � �  operator&= � �  operator|= � �  operator^= 馸  �           std::_Atomic_integral_facade<long> .?AU?$_Atomic_integral_facade@J@std@@ �
 j    	   j  �   
 s      
 j   蝰
 �  ,  
    �   	   j  �    �       	   j  �              �     �     �   	   j  �    s      
 j   蝰
 �    	   j  �    s      
 j  ,   	�  j  �     �          �     �     �  
 �    	0   j  �           
 j   蝰
 �    	0   j  �               �     �   	   j  �    p       	   j  �    s          �     �   	   j  �    E       	   j  �               �     �   	   j  �    p          �     �      K  �  D  D   	0   j  �    �       	0   j  �    �       	0   j  �    z          K  �   	0   j  �    �      "    �     �     �     �   	0   j  �    z       	0   j  �    �      2    �     �     �     �     �     �   	   j  �    p       	   j  �            	   j  �               �     �  V  �    蝰  �  _Base 蝰     value_type � �  atomic<long> 篁� �  operator= 蝰 �  is_always_lock_free  �  is_lock_free 篁� �  store 蝰 �  load 篁� �  exchange 篁� �  compare_exchange_strong  �  compare_exchange_weak 蝰 �  wait 篁� �  notify_one � �  notify_all � �  operator long 蝰> ! v�           std::atomic<long> .?AU?$atomic@J@std@@ 篁�
 R    
 �    
 �    
 T    
 �    
 �    
 Q    
 j    
 �    
 Y    
 �    
 �     	   j  �           b   �              std::ranges::_Uninitialized_copy_fn .?AV_Uninitialized_copy_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      .   �    蝰�  _Uninitialized_copy_fn 馼  �           std::ranges::_Uninitialized_copy_fn .?AV_Uninitialized_copy_fn@ranges@std@@ 蝰 	   �  �    �      f   �              std::ranges::_Uninitialized_copy_n_fn .?AV_Uninitialized_copy_n_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      2   �    蝰�  _Uninitialized_copy_n_fn 篁駀  �           std::ranges::_Uninitialized_copy_n_fn .?AV_Uninitialized_copy_n_fn@ranges@std@@ 蝰 	   �  �    �      b   �              std::ranges::_Uninitialized_move_fn .?AV_Uninitialized_move_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      .   �    蝰�  _Uninitialized_move_fn 馼  �           std::ranges::_Uninitialized_move_fn .?AV_Uninitialized_move_fn@ranges@std@@ 蝰 	   �  �    �      f   �              std::ranges::_Uninitialized_move_n_fn .?AV_Uninitialized_move_n_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      2   �    蝰�  _Uninitialized_move_n_fn 篁駀  �           std::ranges::_Uninitialized_move_n_fn .?AV_Uninitialized_move_n_fn@ranges@std@@ 蝰 	   �  �    �      b   �              std::ranges::_Uninitialized_fill_fn .?AV_Uninitialized_fill_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      .   �    蝰�  _Uninitialized_fill_fn 馼  �           std::ranges::_Uninitialized_fill_fn .?AV_Uninitialized_fill_fn@ranges@std@@ 蝰 	   �  �    �      f   �              std::ranges::_Uninitialized_fill_n_fn .?AV_Uninitialized_fill_n_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      2   �    蝰�  _Uninitialized_fill_n_fn 篁駀  �           std::ranges::_Uninitialized_fill_n_fn .?AV_Uninitialized_fill_n_fn@ranges@std@@ 蝰 	   �  �    �      V   �              std::ranges::_Construct_at_fn .?AV_Construct_at_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Construct_at_fn 篁馰  �           std::ranges::_Construct_at_fn .?AV_Construct_at_fn@ranges@std@@ 蝰 	   �  �    �      R   �              std::ranges::_Destroy_at_fn .?AV_Destroy_at_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      &   �    蝰   _Destroy_at_fn 馬             std::ranges::_Destroy_at_fn .?AV_Destroy_at_fn@ranges@std@@ 蝰 	   �  �    �      J   �              std::ranges::_Destroy_fn .?AV_Destroy_fn@ranges@std@@ 
     	        
 �      "   �    蝰  _Destroy_fn J             std::ranges::_Destroy_fn .?AV_Destroy_fn@ranges@std@@  	         �      N   �              std::ranges::_Destroy_n_fn .?AV_Destroy_n_fn@ranges@std@@ 
 
    	   
     
 �      &   �    蝰  _Destroy_n_fn 蝰N  
           std::ranges::_Destroy_n_fn .?AV_Destroy_n_fn@ranges@std@@  	   
      �      z   �              std::ranges::_Uninitialized_default_construct_fn .?AV_Uninitialized_default_construct_fn@ranges@std@@ 
     	        
 �      :   �    蝰  _Uninitialized_default_construct_fn z             std::ranges::_Uninitialized_default_construct_fn .?AV_Uninitialized_default_construct_fn@ranges@std@@  	         �      ~   �              std::ranges::_Uninitialized_default_construct_n_fn .?AV_Uninitialized_default_construct_n_fn@ranges@std@@ 
     	        
 �      >   �    蝰  _Uninitialized_default_construct_n_fn 蝰~             std::ranges::_Uninitialized_default_construct_n_fn .?AV_Uninitialized_default_construct_n_fn@ranges@std@@  	         �      v   �              std::ranges::_Uninitialized_value_construct_fn .?AV_Uninitialized_value_construct_fn@ranges@std@@ 
     	        
 �      :   �    蝰  _Uninitialized_value_construct_fn 蝰v             std::ranges::_Uninitialized_value_construct_fn .?AV_Uninitialized_value_construct_fn@ranges@std@@  	         �      z   �              std::ranges::_Uninitialized_value_construct_n_fn .?AV_Uninitialized_value_construct_n_fn@ranges@std@@ 
 "    	   "  #   
 �      :   �    蝰$  _Uninitialized_value_construct_n_fn z  %           std::ranges::_Uninitialized_value_construct_n_fn .?AV_Uninitialized_value_construct_n_fn@ranges@std@@  	   "  #    �      >   �              std::bad_weak_ptr .?AVbad_weak_ptr@std@@ �
 (   
 (  �  
    *   	   (  )   
 +      
 (   蝰
 -  ,  
    .   	   (  )   
 /       	   (  )   
           ,    0     1  
 -    	�  (  3            	   (  )           
 (  ,   	6  (  )    +       	6  (  )    /         7    8   	  (  )    &      �   �    蝰 2  bad_weak_ptr 篁� 4  what 篁�5  ~bad_weak_ptr 蝰 9  operator= 蝰5  __local_vftable_ctor_closure 篁�:      __vecDelDtor 篁�> 
 &;      �   std::bad_weak_ptr .?AVbad_weak_ptr@std@@ � �  #   
  �
 (     	   (  )    /      
 -    F   �              std::_Ref_count_base .?AV_Ref_count_base@std@@ 篁�
 A    
  UU
 C     	   A  B           
 A   蝰
 F  ,  
    G   	   A  B    H       	   A  B   
            I     J  
 A  ,   	L  A  B     H       	0   A  B           
 F    	   A  O            	  A  O    2       	  A  B    &      ~	  D   E      _Destroy 篁� E     _Delete_this 篁�
 "    _Uses 
 "    _Weaks 篁� K  _Ref_count_base  M  operator= 蝰 E     ~_Ref_count_base 篁� N  _Incref_nz � E  _Incref  E  _Incwref 篁� E  _Decref  E  _Decwref 篁� P  _Use_count � Q     _Get_deleter 篁�E  __local_vftable_ctor_closure 篁�R     __vecDelDtor 篁馞  &S      C   std::_Ref_count_base .?AV_Ref_count_base@std@@ 篁�
 A    
 "    
 >  ,  
 t    蝰
 X    
 F    2    relaxed 蝰  preferred   strict 篁�>   t   [  std::pointer_safety .?AW4pointer_safety@std@@ 蝰R   �              std::_Shared_ptr_spin_lock .?AU_Shared_ptr_spin_lock@std@@ 篁�
 ]    	   ]  ^            	   ]  ^            	  ]  ^    &      Z  _  _Shared_ptr_spin_lock 蝰 `  ~_Shared_ptr_spin_lock �a  __vecDelDtor 篁馬  b           std::_Shared_ptr_spin_lock .?AU_Shared_ptr_spin_lock@std@@ 篁� 	   ]  ^            N   �              std::pmr::memory_resource .?AVmemory_resource@pmr@std@@ 蝰
 e    	   e  f               �  �   	  e  f     h          �  �  �   	   e  f     j      
 e   蝰
 l  ,  
    m  
 l    	0   e  o    n          #   #    	  e  f     q            #   #    	   e  f     s       	   e  f   
 n       	   e  f   
           u    v  
 e  ,   	x  e  f    n       	  e  f    &      "	  D   g      ~memory_resource 篁� i  allocate 篁� k  deallocate � p  is_equal 篁� r     do_allocate  t     do_deallocate 蝰 p     do_is_equal  w  memory_resource y  operator= 蝰g  __local_vftable_ctor_closure 篁�z      __vecDelDtor 篁馧 
 &{      C   std::pmr::memory_resource .?AVmemory_resource@pmr@std@@ 蝰
 e        #          ~  
 l     }       ^   �              rtxdi::RISBufferSegmentParameters .?AURISBufferSegmentParameters@rtxdi@@ �
 �   蝰
 �  ,  * 
 u     tileSize �
 u    tileCount ^   �           rtxdi::RISBufferSegmentParameters .?AURISBufferSegmentParameters@rtxdi@@ 馴   �              rtxdi::ReSTIRDIStaticParameters .?AUReSTIRDIStaticParameters@rtxdi@@ �
 �    	   �  �   
        � 
 u     NeighborOffsetCount 蝰
 u    RenderWidth 蝰
 u    RenderHeight �
 =   CheckerboardSamplingMode ��  ReSTIRDIStaticParameters 篁馴  �           rtxdi::ReSTIRDIStaticParameters .?AUReSTIRDIStaticParameters@rtxdi@@ � 	   �  �           
 =     	     >   
        z 
 u     RenderWidth 蝰
 u    RenderHeight �
 =   CheckerboardSamplingMode ��  ReSTIRGIStaticParameters 篁馴  �           rtxdi::ReSTIRGIStaticParameters .?AUReSTIRGIStaticParameters@rtxdi@@ � 	     >           ^   �              rtxdi::ImportanceSamplingContext .?AVImportanceSamplingContext@rtxdi@@ 篁�
 �   
 �   蝰
 �  ,  
    �   	   �  �    �      ~   �              rtxdi::ImportanceSamplingContext_StaticParameters .?AUImportanceSamplingContext_StaticParameters@rtxdi@@ �
 �   蝰
 �  ,  
    �   	   �  �    �          �     �   	�  �  �    �       	   �  �           J   �              rtxdi::ReSTIRDIContext .?AVReSTIRDIContext@rtxdi@@ 篁�
 �   蝰
 �  ,  
 �    	�  �  �            
 �  ,   	�  �  �                �     �  B   �              rtxdi::ReGIRContext .?AVReGIRContext@rtxdi@@ �
 �   蝰
 �  ,   	�  �  �            
 �  ,   	�  �  �                �     �  
   ,   	�  �  �            
   ,   	�  �  �                �     �  
 p  ,   	�  �  �            R   �              RTXDI_LightBufferParameters .?AURTXDI_LightBufferParameters@@ 
 �   蝰
 �  ,   	�  �  �            ^   �              RTXDI_RISBufferSegmentParameters .?AURTXDI_RISBufferSegmentParameters@@ 蝰
 �   蝰
 �  ,   	�  �  �             	u   �  �             	0   �  �            
    �   	   �  �     �      �   �              std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> > .?AV?$unique_ptr@VRISBufferSegmentAllocator@rtxdi@@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@@std@@ 袷   �              std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > .?AV?$unique_ptr@VReSTIRDIContext@rtxdi@@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@@std@@ 窬   �              std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > .?AV?$unique_ptr@VReGIRContext@rtxdi@@U?$default_delete@VReGIRContext@rtxdi@@@std@@@std@@ 袷   �              std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > .?AV?$unique_ptr@VReSTIRGIContext@rtxdi@@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@@std@@ � 	  �  �    &      � �  ImportanceSamplingContext 蝰 �  operator= 蝰 �  ~ImportanceSamplingContext � �  GetReSTIRDIContext � �  GetReGIRContext  �  GetReSTIRGIContext � �  GetRISBufferSegmentAllocator 篁� �  GetLightBufferParameters 篁� �  GetLocalLightRISBufferSegmentParams  �  GetEnvironmentLightRISBufferSegmentParams 蝰 �  GetNeighborOffsetCount � �  IsLocalLightPowerRISEnabled  �  IsReGIREnabled � �  SetLightBufferParams 篁�
 �    m_risBufferSegmentAllocator 蝰
 �   m_restirDIContext 
 �   m_regirContext 篁�
 �   m_restirGIContext 
 �    m_lightBufferParams 蝰
 �  P m_localLightRISBufferSegmentParams 篁�
 �  ` m_environmentLightRISBufferSegmentParams ��  __vecDelDtor 篁馸  &�          p rtxdi::ImportanceSamplingContext .?AVImportanceSamplingContext@rtxdi@@ 篁� 	   �  �     �      
 �    
 �    �   �              std::default_delete<rtxdi::RISBufferSegmentAllocator> .?AU?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@ 蝰
 �   蝰
 �  ,  
    �  
 �    	   �  �    �      
 �  ,   	�  �  �     �       	�  �  �    �          �     �  
    �   	   �  �    �       	   �  �           
 �   蝰
 �  ,  
 �    	�  �  �           
 �  ,   	�  �  �               �     �  
 l  ,   	�  �  �            	v  �  �            	0   �  �            	v  �  �           
    v   	   �  �    �        �              std::_Compressed_pair<std::default_delete<rtxdi::RISBufferSegmentAllocator>,rtxdi::RISBufferSegmentAllocator *,1> .?AV?$_Compressed_pair@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@PEAVRISBufferSegmentAllocator@rtxdi@@$00@std@@  	  �  �    &      �  v  pointer   l  element_type 篁�  �  deleter_type 篁� �  unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> >  �  operator= 蝰 �  swap 篁� �  ~unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> > 篁� �  get_deleter  �  operator* 蝰 �  operator-> � �  get  �  operator bool 蝰 �  release  �  reset 蝰
 �    _Mypair 蝰�  __vecDelDtor 篁耱  v�           std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> > .?AV?$unique_ptr@VRISBufferSegmentAllocator@rtxdi@@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@@std@@ � 	   �  �   
        
 �    
 �    
 �    v   �              std::default_delete<rtxdi::ReSTIRDIContext> .?AU?$default_delete@VReSTIRDIContext@rtxdi@@@std@@ 蝰
 �   蝰
 �  ,  
    �  
 �    	   �  �    �      
 �  ,   	�  �  �     �       	�  �  �    �          �     �  
    �   	   �  �    �       	   �  �           
 �   蝰
 �  ,  
 �    	�  �  �           
 �  ,   	   �  �               �        	�  �  �             	�  �  �            	0   �  �            	�  �  �           
    �   	   �  �          �   �              std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRDIContext>,rtxdi::ReSTIRDIContext *,1> .?AV?$_Compressed_pair@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@PEAVReSTIRDIContext@rtxdi@@$00@std@@  	  �  �    &      �  �  pointer   �  element_type 篁�  �  deleter_type 篁� �  unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> >  �  operator= 蝰 �  swap 篁� �  ~unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > 篁�   get_deleter    operator* 蝰   operator-> �   get    operator bool 蝰   release    reset 蝰
 	    _Mypair 蝰
  __vecDelDtor 篁袷  v           std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > .?AV?$unique_ptr@VReSTIRDIContext@rtxdi@@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@@std@@ � 	   �  �   
        
 �    
     
 �    n   �              std::default_delete<rtxdi::ReGIRContext> .?AU?$default_delete@VReGIRContext@rtxdi@@@std@@ 
 �   蝰
   ,  
      
 �    	   �            
 �  ,   	  �              	  �      �                 
       	   �             	   �             
    蝰
   ,  
     	  �              
   ,   	"  �                 !     #   	�  �                	  �               	0   �               	  �             
       	   �      )      �   �              std::_Compressed_pair<std::default_delete<rtxdi::ReGIRContext>,rtxdi::ReGIRContext *,1> .?AV?$_Compressed_pair@U?$default_delete@VReGIRContext@rtxdi@@@std@@PEAVReGIRContext@rtxdi@@$00@std@@  	  �      &      �    pointer   �  element_type 篁�    deleter_type 篁�   unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > 蝰   operator= 蝰   swap 篁�   ~unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > � $  get_deleter  %  operator* 蝰 &  operator-> � &  get  '  operator bool 蝰 (  release  *  reset 蝰
 +    _Mypair 蝰,  __vecDelDtor 篁窬  v-           std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > .?AV?$unique_ptr@VReGIRContext@rtxdi@@U?$default_delete@VReGIRContext@rtxdi@@@std@@@std@@ � 	   �     
        
 �    
 0    v   �              std::default_delete<rtxdi::ReSTIRGIContext> .?AU?$default_delete@VReSTIRGIContext@rtxdi@@@std@@ 蝰
 �   蝰
 3  ,  
    4  
 �    	   �  6    5      
 �  ,   	8  �  6     5       	8  �  6    �          9     :  
    8   	   �  6    <       	   �  6           
 2   蝰
 ?  ,  
 3    	@  �  A           
 2  ,   	C  �  6               B     D   	�  �  A             	C  �  A            	0   �  A            	C  �  6           
    C   	   �  6    J      �   �              std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRGIContext>,rtxdi::ReSTIRGIContext *,1> .?AV?$_Compressed_pair@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@PEAVReSTIRGIContext@rtxdi@@$00@std@@  	  �  6    &      �  C  pointer     element_type 篁�  2  deleter_type 篁� 7  unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> >  ;  operator= 蝰 =  swap 篁� >  ~unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > 篁� E  get_deleter  F  operator* 蝰 G  operator-> � G  get  H  operator bool 蝰 I  release  K  reset 蝰
 L    _Mypair 蝰M  __vecDelDtor 篁袷  vN           std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > .?AV?$unique_ptr@VReSTIRGIContext@rtxdi@@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@@std@@ � 	   �  6   
        
 �    
 Q    J   �              RTXDI_LightBufferRegion .?AURTXDI_LightBufferRegion@@ j   �              RTXDI_EnvironmentLightBufferParameters .?AURTXDI_EnvironmentLightBufferParameters@@ 蝰n 
 S    localLightBufferRegion 篁�
 S   infiniteLightBufferRegion 
 T    environmentLightParams 篁馬   U          0 RTXDI_LightBufferParameters .?AURTXDI_LightBufferParameters@@ 
 �    
 W    R 
 u     bufferOffset �
 u    tileSize �
 u    tileCount 
 u    pad1 馸   Y           RTXDI_RISBufferSegmentParameters .?AURTXDI_RISBufferSegmentParameters@@ 蝰    �  �         [  
 �    V   �              rtxdi::ReGIRStaticParameters .?AUReGIRStaticParameters@rtxdi@@ 篁�
 �    	   �  _           
 �    localLightRISBufferParams 
 �   environmentLightRISBufferParams 蝰
 u    NeighborOffsetCount 蝰
 u    renderWidth 蝰
 u    renderHeight �
 =   CheckerboardSamplingMode �
 ^    regirStaticParams `  ImportanceSamplingContext_StaticParameters 駘  a          < rtxdi::ImportanceSamplingContext_StaticParameters .?AUImportanceSamplingContext_StaticParameters@rtxdi@@ �
 �    
 c    
 �    
 �  �  
    f   	�  �  �    g       �       
 �    
 �  �  
    k   	�  �  �    l      
 �  ,  
    n   �    o  
 �  �  
    q   	  �      r      
 ^   蝰
 t  ,      u  �   �    v  
 t    
 x    *    Disabled �  Grid �  Onion 6   u   z  rtxdi::ReGIRMode .?AW4ReGIRMode@rtxdi@@ ^   �              rtxdi::ReGIRGridStaticParameters .?AUReGIRGridStaticParameters@rtxdi@@ 篁馸   �              rtxdi::ReGIROnionStaticParameters .?AUReGIROnionStaticParameters@rtxdi@@ �
 ^    	   ^  ~           � 
 {    Mode �
 u    LightsPerCell 
 |   gridParameters 篁�
 }   onionParameters 蝰  ReGIRStaticParameters 蝰V  �           rtxdi::ReGIRStaticParameters .?AUReGIRStaticParameters@rtxdi@@ 篁�
 �  �  
    �   	8  �  6    �      
   ,  
    �   �    �  
 �   蝰
 �  ,  
    �  
 �    	   �  �    �      
 �    	  �  �           R   u     rtxdi::ReSTIRDI_ResamplingMode .?AW4ReSTIRDI_ResamplingMode@rtxdi@@  	�  �  �            J   �              RTXDI_RuntimeParameters .?AURTXDI_RuntimeParameters@@  	�  �  �           J   �              ReSTIRDI_BufferIndices .?AUReSTIRDI_BufferIndices@@ 蝰 	�  �  �           b   �              ReSTIRDI_InitialSamplingParameters .?AUReSTIRDI_InitialSamplingParameters@@ 蝰 	�  �  �           f   �              ReSTIRDI_TemporalResamplingParameters .?AUReSTIRDI_TemporalResamplingParameters@@  	�  �  �           f   �              ReSTIRDI_SpatialResamplingParameters .?AUReSTIRDI_SpatialResamplingParameters@@ 蝰 	�  �  �           R   �              ReSTIRDI_ShadingParameters .?AUReSTIRDI_ShadingParameters@@ 蝰 	�  �  �            	u   �  �             	�  �  �             	   �  �     &      
    �   	   �  �     �      
 �   蝰
 �  ,  
    �   	   �  �     �      
 �   蝰
 �  ,  
    �   	   �  �     �      
 �   蝰
 �  ,  
    �   	   �  �     �      
 �   蝰
 �  ,  
    �   	   �  �     �       	   �  �             �  ReSTIRDIContext  �  GetReservoirBufferParameters 篁� �  GetResamplingMode 蝰 �  GetRuntimeParams 篁� �  GetBufferIndices 篁� �  GetInitialSamplingParameters 篁� �  GetTemporalResamplingParameters  �  GetSpatialResamplingParameters � �  GetShadingParameters 篁� �  GetFrameIndex 蝰 �  GetStaticParameters  �  SetFrameIndex 蝰 �  SetResamplingMode 蝰 �  SetInitialSamplingParameters 篁� �  SetTemporalResamplingParameters  �  SetSpatialResamplingParameters � �  SetShadingParameters 篁� L  NumReservoirBuffers 
 u     m_lastFrameOutputReservoir 篁�
 u    m_currentFrameOutputReservoir 
 u    m_frameIndex �
 �   m_staticParams 篁�
 �   m_resamplingMode �
     m_reservoirBufferParams 蝰
 �  0 m_runtimeParams 蝰
 �  @ m_bufferIndices 蝰
 �  ` m_initialSamplingParams 蝰
 �  � m_temporalResamplingParams 篁�
 �  � m_spatialResamplingParams 
 �  � m_shadingParams 蝰 �  UpdateBufferIndices  �  UpdateCheckerboardField J   �          � rtxdi::ReSTIRDIContext .?AVReSTIRDIContext@rtxdi@@ 篁� 	�  �  �           
 �    
 �    
 �    
 �    
 �  �  
    �  
 �    	   �  �   
 �      
    �   	   �  �    �       	   �  �    v         �    �     �  
 �    	0   �  �             	u   �  �            f   �              rtxdi::ReGIRGridCalculatedParameters .?AUReGIRGridCalculatedParameters@rtxdi@@ 篁� 	�  �  �           f   �              rtxdi::ReGIROnionCalculatedParameters .?AUReGIROnionCalculatedParameters@rtxdi@@ � 	�  �  �           V   �              rtxdi::ReGIRDynamicParameters .?AUReGIRDynamicParameters@rtxdi@@ � 	�  �  �            	^  �  �           
 �   蝰
 �  ,  
    �   	   �  �     �      
    u   	   �  �     �       	   �  �            
    �   	   �  �     �       	   �  �            	�  �  �     �       	�  �  �     �         �    �   	  �  �    &      � �  ReGIRContext 篁� �  IsLocalLightPowerRISEnable � �  GetReGIRCellOffset � �  GetReGIRLightSlotCount � �  GetReGIRGridCalculatedParameters 篁� �  GetReGIROnionCalculatedParameters 蝰 �  GetReGIRDynamicParameters 蝰 �  GetReGIRStaticParameters 篁� �  SetDynamicParameters 篁� �  InitializeOnion  �  ComputeOnionJitterCurve  �  ComputeGridLightSlotCount 蝰 �  AllocateRISBufferSegment 篁�
 u     m_regirCellOffset 
 ^   m_regirStaticParameters 蝰
 �    m_regirDynamicParameters �
 �  @ m_regirOnionCalculatedParameters �
 �  � m_regirGridCalculatedParameters 蝰�  ~ReGIRContext 蝰 �  operator= 蝰�  __vecDelDtor 篁馚  &�          � rtxdi::ReGIRContext .?AVReGIRContext@rtxdi@@ � 	�  �              
     
 �    
 �     	�  �  A           
 3    
 �    
 �    
 �    
 �    
 �    
 �    
 �    
 �    2    Uniform 蝰  Power_RIS   ReGIR_RIS V   u   �  ReSTIRDI_LocalLightSamplingMode .?AW4ReSTIRDI_LocalLightSamplingMode@@ �*
 u     numPrimaryLocalLightSamples 蝰
 u    numPrimaryInfiniteLightSamples 篁�
 u    numPrimaryEnvironmentSamples �
 u    numPrimaryBrdfSamples 
 @    brdfCutoff 篁�
 u    enableInitialVisibility 蝰
 u    environmentMapImportanceSampling �
 �   localLightSamplingMode 篁馼   �            ReSTIRDI_InitialSamplingParameters .?AUReSTIRDI_InitialSamplingParameters@@ 蝰
 �    
 �    6   �              rtxdi::float3 .?AUfloat3@rtxdi@@ �"    Uniform 蝰  Power_RIS j   u   �  rtxdi::LocalLightReGIRFallbackSamplingMode .?AW4LocalLightReGIRFallbackSamplingMode@rtxdi@@ b   u   �  rtxdi::LocalLightReGIRPresamplingMode .?AW4LocalLightReGIRPresamplingMode@rtxdi@@ 蝰
 �    	   �  �           � 
 @     regirCellSize 
 �   center 篁�
 �   fallbackSamplingMode �
 �   presamplingMode 蝰
 @    regirSamplingJitter 蝰
 u    regirNumBuildSamples ��  ReGIRDynamicParameters 馰  �            rtxdi::ReGIRDynamicParameters .?AUReGIRDynamicParameters@rtxdi@@ �
 �    
 �    
 �    
 L   蝰
 �    
 �    
 ?    	   2  �    J        �  operator() 駐              std::default_delete<rtxdi::ReSTIRGIContext> .?AU?$default_delete@VReSTIRGIContext@rtxdi@@@std@@ 蝰
 �    	@  L             
 L    	C  L                        F   2    蝰
 C    _Myval2 蝰  2  _Mybase    _Get_first 褶             std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRGIContext>,rtxdi::ReSTIRGIContext *,1> .?AV?$_Compressed_pair@U?$default_delete@VReSTIRGIContext@rtxdi@@@std@@PEAVReSTIRGIContext@rtxdi@@$00@std@@ 
     
 L    
 
    
 C    
 2    
 +   蝰
     
     
     	         )          operator() 駈             std::default_delete<rtxdi::ReGIRContext> .?AU?$default_delete@VReGIRContext@rtxdi@@@std@@ 
     	  +             
 +    	"  +                        F       蝰
     _Myval2 蝰    _Mybase    _Get_first 褚             std::_Compressed_pair<std::default_delete<rtxdi::ReGIRContext>,rtxdi::ReGIRContext *,1> .?AV?$_Compressed_pair@U?$default_delete@VReGIRContext@rtxdi@@@std@@PEAVReGIRContext@rtxdi@@$00@std@@ 
 �    
 +    
     
     
     
 	   蝰
 !    
 "    
 �    	   �  $            %  operator() 駐  &           std::default_delete<rtxdi::ReSTIRDIContext> .?AU?$default_delete@VReSTIRDIContext@rtxdi@@@std@@ 蝰
 !    	�  	  (           
 	    	   	  *               )     +  F   �    蝰
 �    _Myval2 蝰  �  _Mybase  ,  _Get_first 褶  -           std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRDIContext>,rtxdi::ReSTIRDIContext *,1> .?AV?$_Compressed_pair@U?$default_delete@VReSTIRDIContext@rtxdi@@@std@@PEAVReSTIRDIContext@rtxdi@@$00@std@@ 
 �    
 	    
 0    
 �    
 �    
 �   蝰
 4    
 5    
 �    	   �  7    �        8  operator() 駣  9           std::default_delete<rtxdi::RISBufferSegmentAllocator> .?AU?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@ 蝰
 4    	�  �  ;           
 �    	�  �  =               <     >  F   �    蝰
 v    _Myval2 蝰  �  _Mybase  ?  _Get_first � @           std::_Compressed_pair<std::default_delete<rtxdi::RISBufferSegmentAllocator>,rtxdi::RISBufferSegmentAllocator *,1> .?AV?$_Compressed_pair@U?$default_delete@VRISBufferSegmentAllocator@rtxdi@@@std@@PEAVRISBufferSegmentAllocator@rtxdi@@$00@std@@ 
 m    
 �    
 C    
 v    
 �    
 �          Z  
 ~     
      _Value 篁馧   J           std::_Atomic_padded<long> .?AU?$_Atomic_padded@J@std@@ 篁�
 ~  ,  
    L   I    M  
 m    
 I        o     D        Q  
 �  ,  
    S        T  
 n     ?    M  
 ~   蝰
 X  ,  
    Y   J    Z  
 X    
 \    
 I    Y    M  
 X    t     T  
 �   �   �              std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > .?AV?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 駷   �              std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > .?AV?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �
 �  �  
    e   	   �  b   
 f      
 �   蝰
 h  ,  
    i   	   �  b    j       	   �  b              g    k    l   	   �  b           
 �  ,   	o  �  b     f       	o  �  b     j         p    q   	  �  b    &      6
 u     lightSlotCount 篁�
 u    regirOnionCells 蝰
 c   regirOnionLayers �
 d    regirOnionRings 蝰
 @   8 regirOnionCubicRootFactor 
 @   < regirOnionLinearFactor 篁� m  ReGIROnionCalculatedParameters �n  ~ReGIROnionCalculatedParameters  r  operator= 蝰s  __vecDelDtor 篁駀 
 &t          @ rtxdi::ReGIROnionCalculatedParameters .?AUReGIROnionCalculatedParameters@rtxdi@@ �
 �    
 c    
 w    j   �              std::allocator<ReGIR_OnionLayerGroup> .?AV?$allocator@UReGIR_OnionLayerGroup@@@std@@ 駷   �              std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 篁馞   �              ReGIR_OnionLayerGroup .?AUReGIR_OnionLayerGroup@@ 
 {    
 {   蝰
 }    
 {  ,  
 }  ,  �   �              std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@ 裎   �              std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ 篁褛   �              std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ 篁颃   �              std::reverse_iterator<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > > .?AV?$reverse_iterator@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@@std@@ �
  �              std::reverse_iterator<std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > > > .?AV?$reverse_iterator@V?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@@std@@ �
 c  �  
 y   蝰
 �  ,      �  �  
 c    	   c  �    �      
    �   	   c  �   
 �      
 c   蝰
 �  ,      �  �   	   c  �    �      
    �   	   c  �    �      z   �              std::initializer_list<ReGIR_OnionLayerGroup> .?AV?$initializer_list@UReGIR_OnionLayerGroup@@@std@@ 篁�    �  �   	   c  �    �          �  �  �   	   c  �    �          �  �   	   c  �    �      
    �   	   c  �   
 �       	   c  �           J    �     �     �     �     �     �     �     �     �  
 c  ,  
    �   	�  c  �     �       	�  c  �     �       	�  c  �     �          �     �     �   	   c  �           
 {  �  
    �   	   c  �     �      
    �   	   c  �     �          �     �      �  �   	�  c  �    �          �  �  �   	�  c  �    �          �  �   	�  c  �    �          �  �   	�  c  �    �      "    �     �     �     �  
 �   蝰
    �   	   c  �     �          �  �   	   c  �     �          �     �  
    �   	   c  �     �          �     �  "    _At_least   _Exactly 褶  t   �  std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> >::_Reallocation_policy .?AW4_Reallocation_policy@?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 篁� 	   c  �     |       	   c  �                �  �   	�  c  �    �      
    �   	�  c  �    �          �     �  
    �   	   c  �    �      
 �    	~  c  �            	|  c  �               �     �   	�  c  �   	         	�  c  �   	            �     �   	�  c  �   	         	�  c  �   	            �     �   	0   c  �            	#   c  �            	�  c  �    �       	  c  �    �          �     �   	�  c  �     �       	  c  �     �          �     �   	�  c  �            	  c  �               �     �   	y  c  �   	         	#   c  �     �      
 {       �  �  �   	   c  �     �       	   c  �     �       	   c                    |  |   	   c  �     �       	�  c  �           
 y  ,   	�  c  �               �     �  
    �   	�  c  �   	 �       	�  c  �   	 �        �              std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionLayerGroup@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@2@$00@std@@ � 	  c  �    &      �  y  _Alty 蝰  z  _Alty_traits 篁�  {  value_type �  y  allocator_type �  |  pointer   ~  const_pointer 蝰    reference 蝰  �  const_reference   #   size_type 蝰     difference_type   �  _Scary_val �  �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�  �  const_reverse_iterator �	 �  vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 篁� �  operator= 蝰 �  ~vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 蝰 �  push_back 蝰 �  insert � �  assign � �  resize �  �  _Reallocation_policy 篁� �  _Clear_and_reserve_geometric 篁� �  reserve  �  shrink_to_fit 蝰 �  pop_back 篁� �  erase 蝰 �  clear 蝰 �  swap 篁� �  data 篁� �  begin 蝰 �  end  �  rbegin � �  rend 篁� �  cbegin � �  cend 篁� �  crbegin  �  crend 蝰 �  _Unchecked_begin 篁� �  _Unchecked_end � �  empty 蝰 �  size 篁� �  max_size 篁� �  capacity 篁� �  operator[] � �  at � �  front 蝰 �  back 篁� �  get_allocator 蝰 �  _Calculate_growth 蝰 �  _Buy_raw 篁� �  _Buy_nonzero 篁� �  _Change_array 蝰 �  _Tidy 蝰 �  _Move_assign_unequal_alloc �	 �  _Xlength 篁�	 �  _Xrange  �  _Orphan_range 蝰 �  _Getal � �  _Make_iterator � �  _Make_iterator_offset 蝰
 �    _Mypair 蝰�  __vecDelDtor 篁穸 ] 6�           std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > .?AV?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �
 d    
 �    ^   �              std::allocator<ReGIR_OnionRing> .?AV?$allocator@UReGIR_OnionRing@@@std@@ 駫   �              std::allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 篁�:   �              ReGIR_OnionRing .?AUReGIR_OnionRing@@ 
 �    
 �   蝰
 �    
 �  ,  
 �  ,  �   �              std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@ 衤   �              std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@ 篁裎   �              std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@ 篁耱   �              std::reverse_iterator<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > > .?AV?$reverse_iterator@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@@std@@ 颃   �              std::reverse_iterator<std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > > > .?AV?$reverse_iterator@V?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@@std@@@std@@ �
 d  �  
 �   蝰
   ,          
 d    	   d            
       	   d     
       
 d   蝰
 	  ,      
     	   d            
    
   	   d      
      n   �              std::initializer_list<ReGIR_OnionRing> .?AV?$initializer_list@UReGIR_OnionRing@@@std@@ 篁�         	   d                �  �     	   d                �     	   d            
       	   d     
        	   d             J                                              
 d  ,  
       	  d              	  d       
       	  d                              	   d             
 �  �  
    !   	   d       "      
    �   	   d       $          #     %      �     	�  d      '          �  �  �   	�  d      )          �  !   	�  d      +          �  �   	�  d      -      "    (     *     ,     .  
    蝰
    0   	   d       1          �  �   	   d       3          2     4   	   d       �          4     6  �  t   �  std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> >::_Reallocation_policy .?AW4_Reallocation_policy@?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 篁� 	   d       |       	   d                  �  �   	�  d      ;      
    �   	�  d      =          <     >  
       	   d      @      
 	    	�  d  B            	�  d                 C     D   	�  d  B   	         	�  d     	            F     G   	   d  B   	         	�  d     	            I     J   	0   d  B            	#   d  B            	�  d  B    �       	�  d      �          N     O   	�  d  B     �       	�  d       �          Q     R   	�  d  B            	�  d                 T     U   	�  d  B   	         	#   d  B     �      
 �       Y  �  �   	   d       Z       	   d       @       	   d                    �  �   	   d  B     ^       	  d  B           
 �  ,   	a  d                 `     b  
    Y   	�  d     	 d       	�  d     	 �        �              std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionRing@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@2@$00@std@@ � 	  d      &      �  �  _Alty 蝰  �  _Alty_traits 篁�  �  value_type �  �  allocator_type �  �  pointer   �  const_pointer 蝰  �  reference 蝰  �  const_reference   #   size_type 蝰     difference_type   �  _Scary_val �  �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�     const_reverse_iterator �	   vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > 篁�   operator= 蝰    ~vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > 蝰 &  push_back 蝰 /  insert � 5  assign � 7  resize �  8  _Reallocation_policy 篁� 6  _Clear_and_reserve_geometric 篁� 9  reserve  :  shrink_to_fit 蝰    pop_back 篁� ?  erase 蝰    clear 蝰 A  swap 篁� E  data 篁� H  begin 蝰 H  end  K  rbegin � K  rend 篁� F  cbegin � F  cend 篁� I  crbegin  I  crend 蝰 E  _Unchecked_begin 篁� E  _Unchecked_end � L  empty 蝰 M  size 篁� M  max_size 篁� M  capacity 篁� P  operator[] � S  at � V  front 蝰 V  back 篁� W  get_allocator 蝰 X  _Calculate_growth 蝰 9  _Buy_raw 篁� 6  _Buy_nonzero 篁� [  _Change_array 蝰    _Tidy 蝰 \  _Move_assign_unequal_alloc �	 ]  _Xlength 篁�	 ]  _Xrange  _  _Orphan_range 蝰 c  _Getal � e  _Make_iterator � f  _Make_iterator_offset 蝰
 g    _Mypair 蝰h  __vecDelDtor 篁駷 ] 6i           std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > .?AV?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �
 v    Z   �              std::integral_constant<bool,1> .?AU?$integral_constant@_N$00@std@@ 篁�
 �    	   �  m   
            Y  �   	   �  m     o       	�  �  m     �        �  _From_primary 蝰  �  value_type �  #   size_type 蝰     difference_type   l  propagate_on_container_move_assignment �  l  is_always_equal  n  allocator<ReGIR_OnionRing> � p  deallocate � q  allocate 篁� �  _Minimum_asan_allocation_alignment 馸 
 r           std::allocator<ReGIR_OnionRing> .?AV?$allocator@UReGIR_OnionRing@@@std@@ �
 �    
 g    
 u    
 g   蝰
 w    	  g  x           
 g    	a  g  z               y     {  F   �    蝰
 �    _Myval2 蝰  �  _Mybase  |  _Get_first � }           std::_Compressed_pair<std::allocator<ReGIR_OnionRing>,std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionRing@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@2@$00@std@@ �
 �    
         �  �  �  
 �    	   �  �   
 �       	   �  �   
            �     �  
 �  ,  
    �   	   �  �    �      F  �    蝰  �  value_type �  #   size_type 蝰     difference_type   �  pointer   �  const_pointer 蝰  �  reference 蝰  �  const_reference  �  _Vector_val<std::_Simple_types<ReGIR_OnionRing> > 蝰 �  _Swap_val 蝰 �  _Take_contents �
 �    _Myfirst �
 �   _Mylast 蝰
 �   _Myend 篁駧  �           std::_Vector_val<std::_Simple_types<ReGIR_OnionRing> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionRing@@@std@@@std@@ �
 �    
 �    Z 
 @     cellAngle 
 @    invCellAngle �
 t    cellOffset 篁�
 t    cellCount :   �           ReGIR_OnionRing .?AUReGIR_OnionRing@@ 
 �  ,      �  Y  a        �  
 y    	   y  �   
            �  �   	   y  �     �       	|  y  �     �      "  y  _From_primary 蝰  {  value_type �  #   size_type 蝰     difference_type   l  propagate_on_container_move_assignment �  l  is_always_equal  �  allocator<ReGIR_OnionLayerGroup> 篁� �  deallocate � �  allocate 篁� �  _Minimum_asan_allocation_alignment 駄 
 �           std::allocator<ReGIR_OnionLayerGroup> .?AV?$allocator@UReGIR_OnionLayerGroup@@@std@@ �
 y    
 �    
 �    
 �   蝰
 �    	�  �  �           
 �    	�  �  �               �     �  F   y    蝰
 �    _Myval2 蝰  y  _Mybase  �  _Get_first � �           std::_Compressed_pair<std::allocator<ReGIR_OnionLayerGroup>,std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >,1> .?AV?$_Compressed_pair@V?$allocator@UReGIR_OnionLayerGroup@@@std@@V?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@2@$00@std@@ �
 �    
 �        |  |  |  
 �    	   �  �   
 �       	   �  �   
            �     �  
 �  ,  
    �   	   �  �    �      J  �    蝰  {  value_type �  #   size_type 蝰     difference_type   |  pointer   ~  const_pointer 蝰    reference 蝰  �  const_reference  �  _Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> >  �  _Swap_val 蝰 �  _Take_contents �
 |    _Myfirst �
 |   _Mylast 蝰
 |   _Myend 篁駳  �           std::_Vector_val<std::_Simple_types<ReGIR_OnionLayerGroup> > .?AV?$_Vector_val@U?$_Simple_types@UReGIR_OnionLayerGroup@@@std@@@std@@ �
 |    
 �    2
 @     innerRadius 蝰
 @    outerRadius 蝰
 @    invLogLayerScale �
 t    layerCount 篁�
 @    invEquatorialCellAngle 篁�
 t    cellsPerLayer 
 t    ringOffset 篁�
 t    ringCount 
 @     equatorialCellAngle 蝰
 @   $ layerScale 篁�
 t   ( layerCellOffset 蝰
 t   , pad1 馞   �          0 ReGIR_OnionLayerGroup .?AUReGIR_OnionLayerGroup@@ 
 |  ,      |  �  �        �  
 �    
 �  ,      �  �   #     �      �  �   #     �      �  �   #     �  
 �     0        
    �   �    �  
 �  ,  
    �   �    �  
 �  �      �  �   �    �  
 �     t     @  Z   �              std::_Zero_then_variadic_args_t .?AU_Zero_then_variadic_args_t@std@@ �
    �   	   �  =    �       	   �  =   
 �      Z    �           std::_Zero_then_variadic_args_t .?AU_Zero_then_variadic_args_t@std@@ �
 �     	   	  *    �       	   	  *   
 �       	   +      �       	   +     
 �       	   L      �       	   L     
 �       	   �  �   
 �       	   �  �   
        	   �  �     �       	   �     
 )       	   �  �     v       	   �  6   
 J       \    Z  
 X   
 �        M    #   "    t     �      �  �         �  
 C  ,      �  �   C    �  
         �  �   C    �  
   ,      �  �       �  
         �  �       �  
 �  ,      �  �   �    �  
 2        �  �   �    �  
 v  ,      �  �   v    �  
 E        �  �   v    �      #   �  �   #     �  
 �    
 �  ,   t     &      �  �   	   �  =           	   �  =   
       
 �  �      �  �   	   	  *           	   	  *   
       
 �  �      �  �   	   +      	       	   +     
 	      
   �      �  �   	   L      
       	   L     
 
      
 2  �  B   �              std::_Num_int_base .?AU_Num_int_base@std@@ 篁�:   �              std::_Num_base .?AU_Num_base@std@@ 篁馢   ��denorm_indeterminate    denorm_absent   denorm_present 篁馞   t     std::float_denorm_style .?AW4float_denorm_style@std@@ 蝰
    蝰�   ��round_indeterminate �   round_toward_zero   round_to_nearest �  round_toward_infinity   round_toward_neg_infinity B   t     std::float_round_style .?AW4float_round_style@std@@ 
    蝰�   has_denorm � �  has_denorm_loss  �  has_infinity 篁� �  has_quiet_NaN 蝰 �  has_signaling_NaN 蝰 �  is_bounded � �  is_exact 篁� �  is_iec559 蝰 �  is_integer � �  is_modulo 蝰 �  is_signed 蝰 �  is_specialized � �  tinyness_before  �  traps 蝰   round_style  �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 � �  radix 蝰:              std::_Num_base .?AU_Num_base@std@@ 篁駌       蝰 �  is_bounded � �  is_exact 篁� �  is_integer � �  is_specialized � �  radix 蝰B              std::_Num_int_base .?AU_Num_int_base@std@@ 篁窬       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits 馧              std::numeric_limits<bool> .?AV?$numeric_limits@_N@std@@ 蝰�       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_signed 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馧              std::numeric_limits<char> .?AV?$numeric_limits@D@std@@ 篁矜       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馬 
  !           std::numeric_limits<signed char> .?AV?$numeric_limits@C@std@@ �       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  #           std::numeric_limits<unsigned char> .?AV?$numeric_limits@E@std@@ 蝰�       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  %           std::numeric_limits<char16_t> .?AV?$numeric_limits@_S@std@@ 蝰�       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  '           std::numeric_limits<char32_t> .?AV?$numeric_limits@_U@std@@ 蝰�       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  )           std::numeric_limits<wchar_t> .?AV?$numeric_limits@_W@std@@ 篁矜       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馧 
  +           std::numeric_limits<short> .?AV?$numeric_limits@F@std@@ 蝰�       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馢 
  -           std::numeric_limits<int> .?AV?$numeric_limits@H@std@@ �       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馧 
  /           std::numeric_limits<long> .?AV?$numeric_limits@J@std@@ 篁矜       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_signed 蝰 �  digits � �  digits10 篁馬 
  1           std::numeric_limits<__int64> .?AV?$numeric_limits@_J@std@@ 篁矜       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  3           std::numeric_limits<unsigned short> .?AV?$numeric_limits@G@std@@ 矜       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  5           std::numeric_limits<unsigned int> .?AV?$numeric_limits@I@std@@ 篁矜       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馰 
  7           std::numeric_limits<unsigned long> .?AV?$numeric_limits@K@std@@ 蝰�       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馴 
  9           std::numeric_limits<unsigned __int64> .?AV?$numeric_limits@_K@std@@ 蝰F   �              std::_Num_float_base .?AU_Num_float_base@std@@ 篁矜       蝰   has_denorm � �  has_infinity 篁� �  has_quiet_NaN 蝰 �  has_signaling_NaN 蝰 �  is_bounded � �  is_iec559 蝰 �  is_signed 蝰 �  is_specialized �   round_style  �  radix 蝰F   <           std::_Num_float_base .?AU_Num_float_base@std@@ 篁馢  ;    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 馧   >           std::numeric_limits<float> .?AV?$numeric_limits@M@std@@ 蝰J  ;    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 馧   @           std::numeric_limits<double> .?AV?$numeric_limits@N@std@@ 馢  ;    蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  digits � �  digits10 篁� �  max_digits10 篁� �  max_exponent 篁� �  max_exponent10 � �  min_exponent 篁� �  min_exponent10 馬   B           std::numeric_limits<long double> .?AV?$numeric_limits@O@std@@ 
    �   �    D  
    �       F   n    o  
            I   u    �   �    �  
    "       M   �    �  
    C       P  
    �   �    R  
    �   �    T  
    �   �    V  
    �   �    X   �  #     褛    _Atomic_memory_order_relaxed �  _Atomic_memory_order_consume �  _Atomic_memory_order_acquire �  _Atomic_memory_order_release �  _Atomic_memory_order_acq_rel �  _Atomic_memory_order_seq_cst 駈   t   [  <unnamed-enum-_Atomic_memory_order_relaxed> .?AW4<unnamed-enum-_Atomic_memory_order_relaxed>@@ 馬   �              _s__RTTIBaseClassDescriptor .?AU_s__RTTIBaseClassDescriptor@@ 
 ]   蝰:   �              _TypeDescriptor .?AU_TypeDescriptor@@ 
 _    &   �              _PMD .?AU_PMD@@ 蝰^   �              _s__RTTIClassHierarchyDescriptor .?AU_s__RTTIClassHierarchyDescriptor@@ 蝰
 b   蝰
 c    ~ 
 `    pTypeDescriptor 蝰
 "    numContainedBases 
 a   where 
 "    attributes 篁�
 d   pClassDescriptor 馬   e          $ _s__RTTIBaseClassDescriptor .?AU_s__RTTIBaseClassDescriptor@@ Z   �              _s__RTTICompleteObjectLocator2 .?AU_s__RTTICompleteObjectLocator2@@ 蝰
 g   蝰
 h    � 
 "     signature 
 "    offset 篁�
 "    cdOffset �
 `   pTypeDescriptor 蝰
 d   pClassDescriptor �
 i   pSelf Z   j          $ _s__RTTICompleteObjectLocator2 .?AU_s__RTTICompleteObjectLocator2@@ 蝰j   �              $_s__RTTIBaseClassArray$_extraBytes_24 .?AU$_s__RTTIBaseClassArray$_extraBytes_24@@ 蝰
 ^     m  #     �* 
 n    arrayOfBaseClassDescriptors 蝰j   o           $_s__RTTIBaseClassArray$_extraBytes_24 .?AU$_s__RTTIBaseClassArray$_extraBytes_24@@ 蝰f 
 u     neighborOffsetMask 篁�
 u    activeCheckerboardField 蝰
 u    pad1 �
 u    pad2 馢   q           RTXDI_RuntimeParameters .?AURTXDI_RuntimeParameters@@ Z   �              $_TypeDescriptor$_extraBytes_20 .?AU$_TypeDescriptor$_extraBytes_20@@  p   #     �6 
 M    pVFTable �
    spare 
 t   name 馴   u          $ $_TypeDescriptor$_extraBytes_20 .?AU$_TypeDescriptor$_extraBytes_20@@  �  #     馢   �              _s__RTTIBaseClassArray .?AU_s__RTTIBaseClassArray@@ 蝰
 x   蝰
 y    f 
 "     signature 
 "    attributes 篁�
 "    numBaseClasses 篁�
 z   pBaseClassArray 蝰^   {           _s__RTTIClassHierarchyDescriptor .?AU_s__RTTIClassHierarchyDescriptor@@ 蝰j   �              $_s__RTTIBaseClassArray$_extraBytes_16 .?AU$_s__RTTIBaseClassArray$_extraBytes_16@@ 蝰 m  #     �* 
 ~    arrayOfBaseClassDescriptors 蝰j              $_s__RTTIBaseClassArray$_extraBytes_16 .?AU$_s__RTTIBaseClassArray$_extraBytes_16@@ 蝰>   �              _s__CatchableType .?AU_s__CatchableType@@ 
 �   蝰
 �     �  #      �> 
 t     nCatchableTypes 蝰
 �   arrayOfCatchableTypes J   �           _s__CatchableTypeArray .?AU_s__CatchableTypeArray@@ 蝰j   �              $_s__CatchableTypeArray$_extraBytes_24 .?AU$_s__CatchableTypeArray$_extraBytes_24@@ 蝰 �  #     �> 
 t     nCatchableTypes 蝰
 �   arrayOfCatchableTypes j   �           $_s__CatchableTypeArray$_extraBytes_24 .?AU$_s__CatchableTypeArray$_extraBytes_24@@ 蝰n   �              __vcrt_va_list_is_reference<wchar_t const *> .?AU?$__vcrt_va_list_is_reference@PEB_W@@ 篁�    __the_value 蝰�  0   �  __vcrt_va_list_is_reference<wchar_t const *>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@PEB_W@@ �&   �  <unnamed-enum-__the_value> 駈  �           __vcrt_va_list_is_reference<wchar_t const *> .?AU?$__vcrt_va_list_is_reference@PEB_W@@ 篁馴   �              $_TypeDescriptor$_extraBytes_24 .?AU$_TypeDescriptor$_extraBytes_24@@  p   #     �6 
 M    pVFTable �
    spare 
 �   name 馴   �          ( $_TypeDescriptor$_extraBytes_24 .?AU$_TypeDescriptor$_extraBytes_24@@ j   �              __vcrt_va_list_is_reference<char const *> .?AU?$__vcrt_va_list_is_reference@PEBD@@ 篁駳  0   �  __vcrt_va_list_is_reference<char const *>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@PEBD@@ �&   �  <unnamed-enum-__the_value> 駄  �           __vcrt_va_list_is_reference<char const *> .?AU?$__vcrt_va_list_is_reference@PEBD@@ 篁疋 
 u     enableFinalVisibility 
 u    reuseFinalVisibility �
 u    finalVisibilityMaxAge 
 @    finalVisibilityMaxDistance 篁�
 u    enableDenoiserInputPacking 篁�
 u    pad1 �
 u    pad2 �
 u    pad3 馬   �            ReSTIRDI_ShadingParameters .?AUReSTIRDI_ShadingParameters@@ 蝰Z   �              $_TypeDescriptor$_extraBytes_27 .?AU$_TypeDescriptor$_extraBytes_27@@  p   #     �6 
 M    pVFTable �
    spare 
 �   name 馴   �          + $_TypeDescriptor$_extraBytes_27 .?AU$_TypeDescriptor$_extraBytes_27@@  m  #      �* 
 �    arrayOfBaseClassDescriptors 蝰J   �           _s__RTTIBaseClassArray .?AU_s__RTTIBaseClassArray@@ 蝰R 
 u     lightPresent �
 u    lightIndex 篁�
 u    pad1 �
 u    pad2 駄   �           RTXDI_EnvironmentLightBufferParameters .?AURTXDI_EnvironmentLightBufferParameters@@ 蝰Z   �              $_TypeDescriptor$_extraBytes_21 .?AU$_TypeDescriptor$_extraBytes_21@@  p   #     �6 
 M    pVFTable �
    spare 
 �   name 馴   �          % $_TypeDescriptor$_extraBytes_21 .?AU$_TypeDescriptor$_extraBytes_21@@ j   �              $_s__CatchableTypeArray$_extraBytes_16 .?AU$_s__CatchableTypeArray$_extraBytes_16@@ 蝰 �  #     �> 
 t     nCatchableTypes 蝰
 �   arrayOfCatchableTypes j   �           $_s__CatchableTypeArray$_extraBytes_16 .?AU$_s__CatchableTypeArray$_extraBytes_16@@ 蝰f   �              $_s__RTTIBaseClassArray$_extraBytes_8 .?AU$_s__RTTIBaseClassArray$_extraBytes_8@@  m  #     �* 
 �    arrayOfBaseClassDescriptors 蝰f   �           $_s__RTTIBaseClassArray$_extraBytes_8 .?AU$_s__RTTIBaseClassArray$_extraBytes_8@@ 2 
 t     mdisp 
 t    pdisp 
 t    vdisp &   �           _PMD .?AU_PMD@@ 蝰 �  #   (  馴   �              $_TypeDescriptor$_extraBytes_19 .?AU$_TypeDescriptor$_extraBytes_19@@  p   #     �6 
 M    pVFTable �
    spare 
 �   name 馴   �          # $_TypeDescriptor$_extraBytes_19 .?AU$_TypeDescriptor$_extraBytes_19@@  t         R 
 u     firstLightIndex 蝰
 u    numLights 
 u    pad1 �
 u    pad2 馢   �           RTXDI_LightBufferRegion .?AURTXDI_LightBufferRegion@@ :    Off 蝰  Basic   Pairwise �  Raytraced ^   u   �  ReSTIRDI_SpatialBiasCorrectionMode .?AW4ReSTIRDI_SpatialBiasCorrectionMode@@ 篁�

 @     spatialDepthThreshold 
 @    spatialNormalThreshold 篁�
 �   spatialBiasCorrection 
 u    numSpatialSamples 
 u    numDisocclusionBoostSamples 蝰
 @    spatialSamplingRadius 
 u    neighborOffsetMask 篁�
 u    discountNaiveSamples 駀   �            ReSTIRDI_SpatialResamplingParameters .?AUReSTIRDI_SpatialResamplingParameters@@ 蝰�   �              __vcrt_va_list_is_reference<__crt_locale_pointers *> .?AU?$__vcrt_va_list_is_reference@PEAU__crt_locale_pointers@@@@ 窬  0   �  __vcrt_va_list_is_reference<__crt_locale_pointers *>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@PEAU__crt_locale_pointers@@@@ 篁�&   �  <unnamed-enum-__the_value> 駣  �           __vcrt_va_list_is_reference<__crt_locale_pointers *> .?AU?$__vcrt_va_list_is_reference@PEAU__crt_locale_pointers@@@@ 馸   u   �  ReSTIRDI_TemporalBiasCorrectionMode .?AW4ReSTIRDI_TemporalBiasCorrectionMode@@ 駔
 @     temporalDepthThreshold 篁�
 @    temporalNormalThreshold 蝰
 u    maxHistoryLength �
 �   temporalBiasCorrection 篁�
 u    enablePermutationSampling 
 @    permutationSamplingThreshold �
 u    enableBoilingFilter 蝰
 @    boilingFilterStrength 
 u     discardInvisibleSamples 蝰
 u   $ uniformRandomNumber 蝰
 u   ( pad2 �
 u   , pad3 駀   �          0 ReSTIRDI_TemporalResamplingParameters .?AUReSTIRDI_TemporalResamplingParameters@@ v 
 u     properties 篁�
 `   pType 
 a   thisDisplacement �
 t    sizeOrOffset �
 f   copyFunction �>   �          $ _s__CatchableType .?AU_s__CatchableType@@ �   _Comparison_category_none   _Comparison_category_partial �  _Comparison_category_weak    _Comparison_category_strong 蝰J       �  std::_Comparison_category .?AW4_Comparison_category@std@@ 蝰    unsized 蝰  sized J   0   �  std::ranges::subrange_kind .?AW4subrange_kind@ranges@std@@ 駣    _Functor �  _Pmf_object 蝰  _Pmf_refwrap �  _Pmf_pointer �  _Pmd_object 蝰  _Pmd_refwrap �  _Pmd_pointer 馚   t   �  std::_Invoker_strategy .?AW4_Invoker_strategy@std@@ 
 �   蝰"    equal    equivalent 篁�6      �  std::_Compare_eq .?AW4_Compare_eq@std@@ �   �              std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 馴   �              std::integral_constant<bool,0> .?AU?$integral_constant@_N$0A@@std@@ 蝰    a  �  M   	�  �         �          a  �   	�  �         �          �     �      a  Y  �   	   �         �       	#   �               	�  �              �  �  allocator_type �  �  value_type �  �  pointer   �  const_pointer 蝰    void_pointer 篁�  M  const_void_pointer �  #   size_type 蝰     difference_type   �  propagate_on_container_copy_assignment �  l  propagate_on_container_move_assignment �  �  propagate_on_container_swap   l  is_always_equal  �  allocate 篁� �  deallocate � �  max_size 篁� �  select_on_container_copy_construction 蝰�  �           std::_Default_allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �   �    蝰�   �           std::allocator_traits<std::allocator<ReGIR_OnionRing> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ 篁馰   �              std::_Default_allocate_traits .?AU_Default_allocate_traits@std@@ � 	  �         �       	  �         h      2  �  _Allocate 蝰 �  _Allocate_aligned 蝰V   �           std::_Default_allocate_traits .?AU_Default_allocate_traits@std@@ �
 l   蝰
 �    	0   l  �           b  �  value 蝰  0   value_type �  l  type 篁� �  operator bool 蝰 �  operator() 馴  T�           std::integral_constant<bool,1> .?AU?$integral_constant@_N$00@std@@ 篁�:    _Use_void   _Use_member 蝰  _Use_decltype R   t   �  std::_Itraits_pointer_strategy .?AW4_Itraits_pointer_strategy@std@@ 
 �   蝰
 �    	0   �  �           b  �  value 蝰  0   value_type �  �  type 篁� �  operator bool 蝰 �  operator() 馴  T�           std::integral_constant<bool,0> .?AU?$integral_constant@_N$0A@@std@@ 蝰   ��less   greater 蝰:      �  std::_Compare_ord .?AW4_Compare_ord@std@@ 蝰   ��unordered 篁�:      �  std::_Compare_ncmp .?AW4_Compare_ncmp@std@@ R   �              std::numeric_limits<char8_t> .?AV?$numeric_limits@_Q@std@@ 篁� 	|   �               �       蝰 �  min  �  max  �  lowest � �  epsilon  �  round_error  �  denorm_min � �  infinity 篁� �  quiet_NaN 蝰 �  signaling_NaN 蝰 �  is_modulo 蝰 �  digits � �  digits10 篁馬 
  �           std::numeric_limits<char8_t> .?AV?$numeric_limits@_Q@std@@ 篁癞   �              std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �    �  �  M   	|  �         �          �  �   	|  �         �          �     �      �  �  �   	   �         �       	#   �        �       	y  �        �      �  y  allocator_type �  {  value_type �  |  pointer   ~  const_pointer 蝰    void_pointer 篁�  M  const_void_pointer �  #   size_type 蝰     difference_type   �  propagate_on_container_copy_assignment �  l  propagate_on_container_move_assignment �  �  propagate_on_container_swap   l  is_always_equal  �  allocate 篁� �  deallocate � �  max_size 篁�    select_on_container_copy_construction 蝰�             std::_Default_allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$_Default_allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �   �    蝰�              std::allocator_traits<std::allocator<ReGIR_OnionLayerGroup> > .?AU?$allocator_traits@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ 篁馴   �              $_TypeDescriptor$_extraBytes_28 .?AU$_TypeDescriptor$_extraBytes_28@@  p   #     �6 
 M    pVFTable �
    spare 
    name 馴             , $_TypeDescriptor$_extraBytes_28 .?AU$_TypeDescriptor$_extraBytes_28@@ Z   �              $_TypeDescriptor$_extraBytes_26 .?AU$_TypeDescriptor$_extraBytes_26@@  p   #     �6 
 M    pVFTable �
    spare 
 
   name 馴             * $_TypeDescriptor$_extraBytes_26 .?AU$_TypeDescriptor$_extraBytes_26@@ r   �              __vcrt_va_list_is_reference<wchar_t const * const> .?AU?$__vcrt_va_list_is_reference@QEB_W@@ 瘭  0   �  __vcrt_va_list_is_reference<wchar_t const * const>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@QEB_W@@ 篁�&     <unnamed-enum-__the_value> 駌             __vcrt_va_list_is_reference<wchar_t const * const> .?AU?$__vcrt_va_list_is_reference@QEB_W@@ �
 �    	   �             V 
 u     lightSlotCount 篁�
 u    pad 蝰  ReGIRGridCalculatedParameters 蝰f             rtxdi::ReGIRGridCalculatedParameters .?AUReGIRGridCalculatedParameters@rtxdi@@ 篁�
 }    	   }             b 
 u     OnionDetailLayers 
 u    OnionCoverageLayers 蝰  ReGIROnionStaticParameters 馸             rtxdi::ReGIROnionStaticParameters .?AUReGIROnionStaticParameters@rtxdi@@ �6   �              rtxdi::uint3 .?AUuint3@rtxdi@@ 篁�
 |    	   |             : 
     GridSize �  ReGIRGridStaticParameters 蝰^             rtxdi::ReGIRGridStaticParameters .?AUReGIRGridStaticParameters@rtxdi@@ 篁�& 
 @     x 
 @    y 
 @    z 6   
           rtxdi::float3 .?AUfloat3@rtxdi@@ �& 
 u     x 
 u    y 
 u    z 6               rtxdi::uint3 .?AUuint3@rtxdi@@ 篁駫   �              __vcrt_va_list_is_reference<__crt_locale_pointers * const> .?AU?$__vcrt_va_list_is_reference@QEAU__crt_locale_pointers@@@@ 篁衤  0   �  __vcrt_va_list_is_reference<__crt_locale_pointers * const>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@QEAU__crt_locale_pointers@@@@ �&   #  <unnamed-enum-__the_value> 駫  $           __vcrt_va_list_is_reference<__crt_locale_pointers * const> .?AU?$__vcrt_va_list_is_reference@QEAU__crt_locale_pointers@@@@ 篁� p   #      �6 
 M    pVFTable �
    spare 
 &   name �:   '           _TypeDescriptor .?AU_TypeDescriptor@@ Z   �              $_TypeDescriptor$_extraBytes_29 .?AU$_TypeDescriptor$_extraBytes_29@@  p   #     �6 
 M    pVFTable �
    spare 
 *   name 馴   +          - $_TypeDescriptor$_extraBytes_29 .?AU$_TypeDescriptor$_extraBytes_29@@ .
 u     initialSamplingOutputBufferIndex �
 u    temporalResamplingInputBufferIndex 篁�
 u    temporalResamplingOutputBufferIndex 蝰
 u    spatialResamplingInputBufferIndex 
 u    spatialResamplingOutputBufferIndex 篁�
 u    shadingInputBufferIndex 蝰
 u    pad1 �
 u    pad2 馢   -            ReSTIRDI_BufferIndices .?AUReSTIRDI_BufferIndices@@ 蝰Z   �              $_TypeDescriptor$_extraBytes_23 .?AU$_TypeDescriptor$_extraBytes_23@@  p   #     �6 
 M    pVFTable �
    spare 
 0   name 馴   1          ' $_TypeDescriptor$_extraBytes_23 .?AU$_TypeDescriptor$_extraBytes_23@@ n   �              __vcrt_va_list_is_reference<char const * const> .?AU?$__vcrt_va_list_is_reference@QEBD@@ 瘼  0   �  __vcrt_va_list_is_reference<char const * const>::<unnamed-enum-__the_value> .?AW4<unnamed-enum-__the_value>@?$__vcrt_va_list_is_reference@QEBD@@ 篁�&   4  <unnamed-enum-__the_value> 駈  5           __vcrt_va_list_is_reference<char const * const> .?AU?$__vcrt_va_list_is_reference@QEBD@@ 馴   �              $_TypeDescriptor$_extraBytes_31 .?AU$_TypeDescriptor$_extraBytes_31@@  p   #     �6 
 M    pVFTable �
    spare 
 8   name 馴   9          / $_TypeDescriptor$_extraBytes_31 .?AU$_TypeDescriptor$_extraBytes_31@@ B >          .   ??_7type_info@@6B@ ??_Etype_info@@UEAAPEAXI@Z 蝰j �          V   ??_7exception@std@@6B@ ??_Eexception@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰r 
  <      ^   ??_7bad_exception@std@@6B@ ??_Ebad_exception@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰j    <      V   ??_7bad_alloc@std@@6B@ ??_Ebad_alloc@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰~ 6  >      l   ??_7bad_array_new_length@std@@6B@ ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ V �          D   ??_7nested_exception@std@@6B@ ??_Enested_exception@std@@UEAAPEAXI@Z � �  <      q   ??_7bad_variant_access@std@@6B@ ??_Ebad_variant_access@std@@UEAAPEAXI@Z ?what@bad_variant_access@std@@UEBAPEBDXZ 篁駀 q  <      T   ??_7bad_cast@std@@6B@ ??_Ebad_cast@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ j �  <      X   ??_7bad_typeid@std@@6B@ ??_Ebad_typeid@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ z �  C      f   ??_7__non_rtti_object@std@@6B@ ??_E__non_rtti_object@std@@UEAAPEAXI@Z ?what@exception@std@@UEBAPEBDXZ 蝰r <  <      _   ??_7bad_weak_ptr@std@@6B@ ??_Ebad_weak_ptr@std@@UEAAPEAXI@Z ?what@bad_weak_ptr@std@@UEBAPEBDXZ 矜 T          �   ??_7_Ref_count_base@std@@6B@ ?_Destroy@_Ref_count_base@std@@EEAAXXZ ?_Delete_this@_Ref_count_base@std@@EEAAXXZ ??_E_Ref_count_base@std@@UEAAPEAXI@Z ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z 篁颃 |          �   ??_7memory_resource@pmr@std@@6B@ ??_Ememory_resource@pmr@std@@UEAAPEAXI@Z ?do_allocate@memory_resource@pmr@std@@EEAAPEAX_K0@Z ?do_deallocate@memory_resource@pmr@std@@EEAAXPEAX_K1@Z ?do_is_equal@memory_resource@pmr@std@@EEBA_NAEBV123@@Z 
 �    
 �     	   �  �           � 
 u     NeighborOffsetCount 蝰
 u    RenderWidth 蝰
 u    RenderHeight �
 =   CheckerboardSamplingMode �J  ReSTIRDIStaticParameters 篁馴  K           rtxdi::ReSTIRDIStaticParameters .?AUReSTIRDIStaticParameters@rtxdi@@ �
 j    
 �    
 �    
 O    
 �    
 Q     �        
 �     �        
 �    
 V     �        
 �    
 Y     �        
 �    
 \     �               �  
 �   蝰
 `    
 �   蝰
 b    
 c    
 �   蝰
 e    
 f    
 �    
 h    
 �    
 j    
 �    
 l    
 �    
 n    
 �    N   �              std::ranges::_For_each_fn .?AV_For_each_fn@ranges@std@@ 蝰
 q    	   q  r   
 �      &   �    蝰s  _For_each_fn 篁馧  t           std::ranges::_For_each_fn .?AV_For_each_fn@ranges@std@@ 蝰 	   q  r    �      R   �              std::ranges::_For_each_n_fn .?AV_For_each_n_fn@ranges@std@@ 蝰
 w    	   w  x   
 �      &   �    蝰y  _For_each_n_fn 馬  z           std::ranges::_For_each_n_fn .?AV_For_each_n_fn@ranges@std@@ 蝰 	   w  x    �      F   �              std::ranges::_Count_fn .?AV_Count_fn@ranges@std@@ 
 }    	   }  ~   
 �      "   �    蝰  _Count_fn 蝰F  �           std::ranges::_Count_fn .?AV_Count_fn@ranges@std@@  	   }  ~    �      N   �        挓 d  v�  �� 銴 v� 蒢 孋 OM 効 垗 }�  +q  � 盽 m� ZF 德 �!   h � 
�    �$ "U 侊  C�  %  Zx 櫏  '3 }	 � > Z�  	� Yt z�  ~� W# 2� 箟  _� 榡  鰑 )� 髋 �  昐 龑 枟 }J k 鞏  �= 糧  �0  � 6@  箅 D� � 脀 款  i� 儃  /�  Kq  壧  孷 u� 荿 议  L~ 懒 %. 瀰 �  磛 紞 宕 S *  �.  � 澕 鰺  揋 舸 @ � 潡 *2 _� 伞 	  _� - K 9? 庆 榟 � 貸   刹   狐 � ▓  ?; 繒  b  C 0 )W *� 唟 烉 y�  �
   � 詘  �3 i 寪   g� #  I- D r  � � �  鞇 	� 敐 q 0| � F�    �  攦 b 郅  蹎 � ぼ �
 與 Z  呥  8� -< ~K  駵 瑶 k3  哚 俍 `� +?  �+ 嵭 2�  k� 瘆 藈  藽  纽  
  �!  擕 v 挂 v�  乘 4 鯞 8� 尉  粟 齺 4� 旈 槢 綫 冈 �+  炅 � ll +? ,� 羆 M:  鑺 P�  菎  h� n� 侲 h6 ^ N� |� 愥 nC 亓 RW +�  ~ # 嶯 電 ?\ 1 ki a| $�  6� �  XK 髳 E| )y `Y  淓 箧  B[ 砵 q� #= 鎟 語 g� #� 	D 炴 � *�    墷 R� O8 
� 覢 估 � � � � | � E� ?� =	 寏 镊  今 议 �3 儕 � x � 玜 :! �/ l �  � � 膴 �  L  薟 骺  蛲 W� �  r� 9� � T  s,  堐 k� /2 桧 K  � 枺  �6 �8 �  �=  1( �) �  X: ㄤ  � 諯 K�  躼 悭  伭 'O  <� �+ 川 澋 鳝 �  \�  6� G� � +y 挈 ∵  疯  鄽 1� o� �3 1 � �. 擥 a�  で 璛 嵃 � 1� d 鴼 !� 贸 /�  %� {� y�   � ^2 � 蔧 z
 �
 E� 荐  � 隋 Q 稇  ym  j( �0 m�  ,� �	  蓧 � +! v  鉝 卭 b  郓  yp n� E� S� &�   貖  t�  �2 �  票 :| 椘 Y)   as  `a 尰 � 緔 y� 雋 猚 � ?
 B� � 1i J y* 氻 9�  ( Yt 僝 �  铛 w 忞  v� @� b�  翅 D� �4 帄  �8  s�   搜 ∶   <� pP 郈 q� D _{ V,  +� 糚 :l y-  � �   Ju ^ Ｆ Q� 曣   {� 屜  s )� 囯 J� � 杈 eh d� |� 跌 �(  a� N� 冰 z *r #:  a� P� r!  D� �2 � C� 呱 至 K    �O f3 z� =� �  2� O  锝  �  D� r  5H h a� � 咂 )� a� W� 顺 拢   C Z�  � 'G �) 棹 橴 麍 , &\ U�  	� >+  s~ М  癲  }   Om yC P�  壂 � �,  B 虂  x[ 虇 �7 � a? 
X 尟 �   � 化 M� l] 犲 (: L� F5 嘕 訧 �  彅 �)  �  p& 絇 綬  梢 q� 	  )� R 寡  鱃 97 �	  w j� Ｔ 6  峘  f} 塟 :h � u� ?:  bD :� �  ?�  . L�  � 0� KG �  nd �� l� � , �  篼 黵  1  蝸  J6  � � 鬯   9  !B  躖  懦 �  � Е �   枌 , �- �! ^� T �9  N W$ 鳃  膑  � 侁 � 頪 h�  ∟ � �. 屸 ?0 `z  蝮 任  � 蛥  B� 椅 �  <s  喂 � D  彺 r� i� |K � 9� 7� ん  濆  =� 罟 扢 姢  �+ ＆ S� � � �- bb fh W  0 魎 � �  N� ~e 披  v� #Q 鴌 8  � |� R�  u�  剃 「 燢 喚 萸 q� � � 2 ２ � >� ~� M� 5� � 5C AY 誢  , 蛭 q� 3� Hb k� ;� h� @ � HF �8 �
 嵈  t g 挐 C� 驫  釹 �+ 崉  "# 1�   届 �1  '  倅  � '- 8�  ZC �
    � �
 L� 閠 p> &�  /- GV l%  窜 :�  (� 2� @  嚋 鰑 �  Ul  ō �  2� 訛 >� 铗 b� � 藷 觃 瞛 �	  耻 � l �, $� qO  Lu 虫 � 滭 旗 馭 瀤 � k�  湤 嬰 愺 @� 软 ?  Q8 告  �� 棸 遬 n �
 W� ~ 帉  j� 拚  &� �$ 鲏 买 只 澜 � �) O1 栞  Yv 鳎  E� �! 灢 � 蛐  榩 6� >R  鮓 =�  ?�  cb M 	E  眊  叴  �  �  @
 ^�  孌 MY 賰 f� � 忞 xW %! �0  �? S� �  s -6 廜 [�  得 �&  F? 済  ~� d� $� 鶪  !� �- 
F K5 � 獞 � r 憪  �  粦 皻 箽 � @  簋 �? 鴔 � .�  d 鏴 軗 �  i� 歾 VS  甈 脖 狡 a( d� � 臤  p 鄌  [� 釖 � �	 痟 蜾 #� ^m  A 柋 � Q� /  CO   痳 � 阌 -c  粉 V� 瘆 镲 �1 -&  晑 O� �=  >�  V� 視  �1 軡 想 @S 虗 犚  � �8 � xd 毿 數 Og  � 枞 狤 ”  '�  ＋  � PK  }� 奘 '� +� $
 vO 錔 軔 盔 鼧 >S �*  � 腉 弭 欼  鐕 L� m� 崗 Jq 俣 � �< �
 !� 棉 H  y 7  !� O� �  �4  4 鮕 鄤  -� 儓 慚  A�  紆 K� O� A 骓 6. ?r  SA I  忶  �< ��  笔 � �3  .� 釿 	� 4i 啜  d A3 觾 =n Hs �  � � k 鯶  � � �2  庭 za V �,  ^�  h � 捊 铃  醞 穀 �  昸  Xa �! 様  m� +3 嚯 �  岕 ▎ �  踘 � � �    鐲 儝 �< 莂 丏 鮙  )W J�  fQ  � 厫 C� 椊 Gq  ��   � 窻 7O ^ ! 晁 � 椼  �(  躣  旱 � �4  � f� }  � {� �>  浭  Ka 蹟  	� 蚵  攽 
� 纷 蒳 I� �! f� d�  5  [| �. �5 
�  胚 �< @ 1� � � 揨  熹  4 Aa M� ub  �9  � 郞 �: #� 蕍 B 4� k  戢 后 if q� m� 5 惧 '� j �+  h 凒 剌  偵 Π � 鑻 量  ε � Bh 裆  �& (   〕  镹  溕  vz 矎  e�  3�  �;  � l� ェ  昨  � 騈  �8 濛 bK J�  �. 艍  L5 � Y 俱  '.  營 M6  +\ - 錣 x 涂 & 蓜  [� M �;  計 � � bH $ �   v  泤 娬 � -^ � 驰 3�  奉  堽 Q�  憦  , �)  � � ^� � v�  	� S, R � 1� 藹 E� W! hT 裰 
�  臠 艢 瘧  鬂  \# �/  1 $u q  v� g H� �  �4 U� w  �� Y�  .R A� m�  �' 緡 擲 q� � E�  V) � g� 
� 祇 � )3  簶  �1  c�  3 � 翽 �  钕 悗  汘 �    /Q Aq  � u A� 隑 cd 麙 銽 a  +� 鬠 h6 q 硡 0� ｊ 锖  <c 蒳 A� J�  .T  /J  �1 f�  ㈱ �  �! � >   #q f� � � +�  <� 1� 顦 ]� y� 駁 'f  炟  鶷 5| 唖 宊 :� V� �%  �  t  胅 鳣  X�  誷  銶  B �' 蠥 �1 梌 蚓 仯 il >�  �>  Z� � i� ;C  jC r� ?W �6 %� 齇 ; �) � �  � ?	 堌 涂 Vv r  崠  禗 彠 ~6  鄴   隿 �  霣 坧 扎 �  ZR �  n$ ] N^ ネ  畠  駾 �  W! @� Y'  ,� u% `M  韂  Z� y� s� j Ee   $ 騌  � � %� �% ,8  �	 Y� 奌 � i� 疁 霩 p�  ks i�   w� 巔 � |^ � � 5 � 巘  K� '� �  縫 \  榀  熕 PH   E+  � 亘 鉆 d +� j  o� &T 踣 8 �  ]  焸  Pu R� !  枇 GM  宝 K: 衣 S& G �q  挨 ( ^X �  蕏 cD 笞  � .�  猃 ! P C� Q  佑  ]C � 9
 镒  �  �   51 � � D@ � '� 鐋 ?� !  輏 )� �+  #�  .� T� L� �  獒 滛  
  邐 贐 i;  ǔ 拭  tr `# � F w� 磰 c� 撂 � 媚 G�  5* 飗 � 紧 >2  禸  	  &5 H} e� �2 - � o�  蝱 蛇 �+  鬎 谩  6  !E $O 湟 c + #� 嘁 �  "� � '^  � F� U�  п '� (� 7 课 餫 #� P s� `k 騌 9D 7� ab Q� ~�   �4  鞳 � `2  
d J\ ,� $d 嫪 � g� 鰱 �.  �   Q  !q 膫 �) � 魔 柃 ぢ 虩 n" 跗 鉠 �? 0( P� � �3 Z_  h   聽  � 丧  嬔  �  uY � O� 櫐 嚭 X� x_ X�  (� 犿 尻  鰓 '� �� 譍 � a
 � H� 幈 M� 简 箨 ɡ 飗 n� 箾 邑 � 2�   /} D� 顢 喽 Cu 姥 Ⅰ   s b$ Cn �(  昨 A� 队 Ⅶ hM %z 聱 N� lk  键 紋 *   H+ 禝 � A� } 稐 7 殣 A  銡  h' � n} �*  痟 鬥  疴 �   � c|  W�  狺  U  U* &�  5� + 塕 '8 m� . �, Z�  �   �	 -
  YQ #  3p S4 糫  � �!  菉 � b� �9  M� �)  濜  爭 DX J� 鵱 N9 瓥  g 騻 �6 �# q� �  嫿 =9  -9 て (6 h5 "j 薼 a 撾 婳 f�  錵  羾  �  鹈 9�   嶆 Mv #a d�  � ^ 崗 紽 蓞 	� 岘 9� 崢  栬 � [
  飲  �/  ] 鯑 B � � [� ㄇ 捁 � �+ F  抔 :x 壔 ,�  f� 肚 蘲 C4 隌 * 芀  �= �
  ε � �  op ~� 鵕 T� �= tG 5n  較 沲 �& �  1� � 36  惫  臷 r� u) 鳑 蠲  桹 p�  i� 	�  邘  鯱 �5 ] ^{ 捄 隒 丶  8� pK �� k� �  愃  瓾 � 姖 &� A� �7 3+ 驉 洸  p  ~� �  $h  3� 鴤 蟊  況 镬 �  脴  � 
  � ]L 屴  o 1W 倗 6� � �+   Tw D� j� } x� P  � } *7 霼 !k D� l� 鬷 �  �0 WM |� 蘎 2� s� 皊  A� �"  艶 U� dm 拧 l �  旐  鄜 殎 7 n� R�  C� v, @- e� ;� s q�  蹼  6� 
� � � 0p A  p <� �  H =
  � 览  茡 ,� 佰 �5 O�  弮 偏 G� |4 戨  引 /� 摗 Y�  m  l� 餀 亦 Rj  R* 1  jr  8B   d� 籫  'z ^� �(  筪 � %v r� � 噻 T�  (� Xl 窥 杓  3 � �< �  H/  0� 梯 �= 麕 贑 sE � �  歺 氷 � 彧  憄  �
 �  駦 #� I� 酖 � 鮊 ~
  �!  L� 輨 U[ IZ  � /�  �0 �! �9 A�   B yc 緺  蜳 穕 � 飫 e �4 D� f� 譕 �/  獨 7� �& 驕 ?   蒨  �= v )~ �2  aI  睪 � �  n $r  9�  �   员  k� |p �        std::ranges::_Count_if_fn .?AV_Count_if_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      &   �    蝰�  _Count_if_fn 篁馧  �           std::ranges::_Count_if_fn .?AV_Count_if_fn@ranges@std@@ 蝰 	   �  �    �      F   �              std::ranges::_Equal_fn .?AV_Equal_fn@ranges@std@@ 
 �    	   �  �   
 �      "   �    蝰�  _Equal_fn 蝰F  �           std::ranges::_Equal_fn .?AV_Equal_fn@ranges@std@@  	   �  �    �      Z   �              std::ranges::_Is_permutation_fn .?AV_Is_permutation_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Is_permutation_fn 馴  �           std::ranges::_Is_permutation_fn .?AV_Is_permutation_fn@ranges@std@@ 蝰 	   �  �    �      J   �              std::ranges::_All_of_fn .?AV_All_of_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      "   �    蝰�  _All_of_fn 馢  �           std::ranges::_All_of_fn .?AV_All_of_fn@ranges@std@@ 蝰 	   �  �    �      J   �              std::ranges::_Any_of_fn .?AV_Any_of_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      "   �    蝰�  _Any_of_fn 馢  �           std::ranges::_Any_of_fn .?AV_Any_of_fn@ranges@std@@ 蝰 	   �  �    �      J   �              std::ranges::_None_of_fn .?AV_None_of_fn@ranges@std@@ 
 �    	   �  �   
 �      "   �    蝰�  _None_of_fn J  �           std::ranges::_None_of_fn .?AV_None_of_fn@ranges@std@@  	   �  �    �      J   �              std::ranges::_Copy_n_fn .?AV_Copy_n_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      "   �    蝰�  _Copy_n_fn 馢  �           std::ranges::_Copy_n_fn .?AV_Copy_n_fn@ranges@std@@ 蝰 	   �  �    �      V   �              std::ranges::_Copy_backward_fn .?AV_Copy_backward_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Copy_backward_fn 蝰V  �           std::ranges::_Copy_backward_fn .?AV_Copy_backward_fn@ranges@std@@  	   �  �    �      J   �              std::ranges::_Copy_if_fn .?AV_Copy_if_fn@ranges@std@@ 
 �    	   �  �   
 �      "   �    蝰�  _Copy_if_fn J  �           std::ranges::_Copy_if_fn .?AV_Copy_if_fn@ranges@std@@  	   �  �    �      F   �              std::ranges::_Move_fn .?AV_Move_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      "   �    蝰�  _Move_fn 篁馞  �           std::ranges::_Move_fn .?AV_Move_fn@ranges@std@@ 蝰 	   �  �    �      V   �              std::ranges::_Move_backward_fn .?AV_Move_backward_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Move_backward_fn 蝰V  �           std::ranges::_Move_backward_fn .?AV_Move_backward_fn@ranges@std@@  	   �  �    �      Z   �              std::ranges::_Partition_copy_fn .?AV_Partition_copy_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Partition_copy_fn 馴  �           std::ranges::_Partition_copy_fn .?AV_Partition_copy_fn@ranges@std@@ 蝰 	   �  �    �      Z   �              std::ranges::_Is_partitioned_fn .?AV_Is_partitioned_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Is_partitioned_fn 馴  �           std::ranges::_Is_partitioned_fn .?AV_Is_partitioned_fn@ranges@std@@ 蝰 	   �  �    �      Z   �              std::ranges::_Partition_point_fn .?AV_Partition_point_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Partition_point_fn Z  �           std::ranges::_Partition_point_fn .?AV_Partition_point_fn@ranges@std@@  	   �  �    �      N   �              std::ranges::_Search_n_fn .?AV_Search_n_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      &   �    蝰�  _Search_n_fn 篁馧  �           std::ranges::_Search_n_fn .?AV_Search_n_fn@ranges@std@@ 蝰 	   �  �    �      N   �              std::ranges::_Find_end_fn .?AV_Find_end_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      &   �    蝰�  _Find_end_fn 篁馧  �           std::ranges::_Find_end_fn .?AV_Find_end_fn@ranges@std@@ 蝰 	   �  �    �      V   �              std::ranges::_Find_first_of_fn .?AV_Find_first_of_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Find_first_of_fn 蝰V  �           std::ranges::_Find_first_of_fn .?AV_Find_first_of_fn@ranges@std@@  	   �  �    �      R   �              std::ranges::_Swap_ranges_fn .?AV_Swap_ranges_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Swap_ranges_fn R  �           std::ranges::_Swap_ranges_fn .?AV_Swap_ranges_fn@ranges@std@@  	   �  �    �      N   �              std::ranges::_Transform_fn .?AV_Transform_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Transform_fn 蝰N  �           std::ranges::_Transform_fn .?AV_Transform_fn@ranges@std@@  	   �  �    �      J   �              std::ranges::_Replace_fn .?AV_Replace_fn@ranges@std@@ 
 �    	   �  �   
 �      "   �    蝰�  _Replace_fn J  �           std::ranges::_Replace_fn .?AV_Replace_fn@ranges@std@@  	   �  �    �      R   �              std::ranges::_Replace_if_fn .?AV_Replace_if_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      &   �    蝰�  _Replace_if_fn 馬  �           std::ranges::_Replace_if_fn .?AV_Replace_if_fn@ranges@std@@ 蝰 	   �  �    �      V   �              std::ranges::_Replace_copy_fn .?AV_Replace_copy_fn@ranges@std@@ 蝰
     	        
 �      *   �    蝰  _Replace_copy_fn 篁馰             std::ranges::_Replace_copy_fn .?AV_Replace_copy_fn@ranges@std@@ 蝰 	         �      Z   �              std::ranges::_Replace_copy_if_fn .?AV_Replace_copy_if_fn@ranges@std@@ 
     	        
 �      *   �    蝰	  _Replace_copy_if_fn Z  
           std::ranges::_Replace_copy_if_fn .?AV_Replace_copy_if_fn@ranges@std@@  	         �      F   �              std::ranges::_Fill_fn .?AV_Fill_fn@ranges@std@@ 蝰
 
    	   
     
 �      "   �    蝰  _Fill_fn 篁馞             std::ranges::_Fill_fn .?AV_Fill_fn@ranges@std@@ 蝰 	   
      �      N   �              std::ranges::_Generate_fn .?AV_Generate_fn@ranges@std@@ 蝰
     	        
 �      &   �    蝰  _Generate_fn 篁馧             std::ranges::_Generate_fn .?AV_Generate_fn@ranges@std@@ 蝰 	         �      R   �              std::ranges::_Generate_n_fn .?AV_Generate_n_fn@ranges@std@@ 蝰
     	        
 �      &   �    蝰  _Generate_n_fn 馬             std::ranges::_Generate_n_fn .?AV_Generate_n_fn@ranges@std@@ 蝰 	         �      J   �              std::ranges::_Remove_fn .?AV_Remove_fn@ranges@std@@ 蝰
     	         
 �      "   �    蝰!  _Remove_fn 馢  "           std::ranges::_Remove_fn .?AV_Remove_fn@ranges@std@@ 蝰 	          �      N   �              std::ranges::_Remove_if_fn .?AV_Remove_if_fn@ranges@std@@ 
 %    	   %  &   
 �      &   �    蝰'  _Remove_if_fn 蝰N  (           std::ranges::_Remove_if_fn .?AV_Remove_if_fn@ranges@std@@  	   %  &    �      R   �              std::ranges::_Remove_copy_fn .?AV_Remove_copy_fn@ranges@std@@ 
 +    	   +  ,   
 �      &   �    蝰-  _Remove_copy_fn R  .           std::ranges::_Remove_copy_fn .?AV_Remove_copy_fn@ranges@std@@  	   +  ,    �      Z   �              std::ranges::_Remove_copy_if_fn .?AV_Remove_copy_if_fn@ranges@std@@ 蝰
 1    	   1  2   
 �      *   �    蝰3  _Remove_copy_if_fn 馴  4           std::ranges::_Remove_copy_if_fn .?AV_Remove_copy_if_fn@ranges@std@@ 蝰 	   1  2    �      J   �              std::rangewares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef � �  =  "   J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits � �  ?  W	   �  ?  `	   �  ?  i	   �  ?  r	  V     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h 篁� �  D  0    �  D      
  D  g       D  r    6  D  �   J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception 篁� R  J  �   j     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\predefined C++ types (compiler internal) � n  L  �    �  J  N   �  J  �  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h � �  P  i  Z     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h 蝰 �  R      �  R  �   �  R  �   �  R  �   �  R  �  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h 篁� �  X  H  R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h  !  Z  W    "  Z  6    )  P  q  R     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h  >  ^  I    U  ^  )    q  ^  �    �  ^  �    �  ^  �   F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare � �  d      �  d  /    �  d  q    �  d  �   F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility  �  i  C   �  i  �     i  �     i  �     i  �     i  �     i  C
     i  b
   #  i  y   )  i  �   /  i  �   5  i  $   ;  i  ,   A  i  �   G  i  �   M  i     S  i  z   Y  i  K   _  i  {   e  i     k  i  9  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple 篁� z    �   N     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h � ~  �     F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory � �  �      �  �  �   �  �  �   �  �  �   �  �  �   �  �  �  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h � �  �  [   �  �  �     �  �     �  �   !  �  �  Z     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h 篁� )  �     N     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h  8  �  -    9  �  &   J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h 篁� C  �  F   F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic 蝰 l  �  
   �  �  i   �  �  �   �  �  g   �  �  �
  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory 蝰 �  �  7    �  �  �    �  �  �    �  �  S   �  �  �   �  �  �   �  �       �  )     �  V     �  �     �  �     �        �  H   &  �  �   <  �  V   T  �  d   \  �  4   c  �  �  V     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h  |  �  �   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDI.h  �  �  !   J     D:\RTXPT\External\Rtxdi\include\Rtxdi\ImportanceSamplingContext.h 蝰 �  �      �  �      �  �  +    �  �  G      �  H    .  �  I    O  �  J    V  
  U   Z     D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentParameters.h 篁� Z  �      b  �     :     D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIR.h  {  �  '    �  �      �  �      �  �  9    �  �  �   F     D:\RTXPT\External\Rtxdi\include\Rtxdi\DI\ReSTIRDIParameters.h 蝰 �  �      �  �  ;    �  �  n    �  �  h    �  �  v      �       �  �     �       �  �   '  �     .  �  �   :  �     A  �  �   K  �  �   u  �  ^   F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector 蝰 �  �  ?   �  �  b    8  �  ?   j  �  c    s  �  �   ~  �  �   �  �  W  F     D:\RTXPT\External\Rtxdi\include\Rtxdi\ReGIR\ReGIRParameters.h 蝰 �  �  -    �  �  �   �  �  �   �  �  W   �  �      �  �  �  "       operator new 莜O颠鍈篁�     �  fabsf 懕H$醆i蝰     �  fabsl ~�;馗	~#蝰
     std * �  �  is_constant_evaluated  oTN蝰 �  �  operator| �~1\彾`蝰 �  �  operator& pl0�釓蝰 �  �  operator^ 矅旉痍趸蝰& �  �  _Fnv1a_append_bytes z愳7镅鵧 �  �  {ctor} eJ0楍 �  �  {ctor} 比犍,w� �  �  {ctor} /� �J疡 �  �  {dtor} Й@珪g蓠 �  �  what i岷kw�!l篁� �  �  __delDtor m咄�'忩� �    __delDtor c鲮�?龥打� �    {dtor} 鷙攈y绸 
    {ctor} 关�/棉喟� 
    __delDtor y"吙魔膀� 
    {dtor} 箦9R廟撷� $  -  {ctor} \瞾埌#涶 $  4  __delDtor sV耮向蝰 $  /  {dtor} 	�#肨絟~� ;  B  {ctor} 斩韯�巴� ;  D  {dtor} �w.阐曬 ;  @  {ctor} S5P�
�
篑" ;  J  operator bool $C0,�$S蝰& ;  K  _Current_exception 鄲n凋柽�& �  �  current_exception 慞If	Z�蝰& �  �  rethrow_exception L:$�-旙打� $  ,  {ctor} ��;嘽� 
    {ctor} 盕~M垊[捡 s    {dtor} Is�8Q偞 s  �  __delDtor �嵾揫骝� �  �  what �>\ k�
烍蝰 �  �  {ctor} J�l4O橊 �  �  {dtor} 悽�"鍋� �  �  {ctor} 倏鄤|羼 �  �  __delDtor 謉Y徜�蝰2     �  __local_stdio_printf_options 帪羻篁�.     �  __local_stdio_scanf_options #鸅�6棗�"     �  _vfwprintf_l q
'爀�&痼蝰"     �  _vfwprintf_s_l A 耩�H�"     �  _vfwprintf_p_l .臒 瘙I�     �  _vfwscanf_l �姶��"     �  _vfwscanf_s_l 睖脈�姠蝰"     �  _vsnwprintf_l 妈縶嚒7�"     �  _vsnwprintf_s_l 憈sw嶾"     �  _vswprintf_c_l �
Y>,I劀�"     �  _vswprintf_l Yo艾H泽蝰"     �  __vswprintf_l �g鱮^講蝰     �  vswprintf �7F蝰"     �  _vswprintf_s_l #苷�芅岏"     �  _vswprintf_p_l 鼿�3_l�%�"     �  _vscwprintf_l $瓉Y6!C'蝰"     �  _vscwprintf_p_l 9疧kl鉘d     �  _vswscanf_l v鶂:w樼�"     �  _vswscanf_s_l JZ吨託蝰"     �  _vsnwscanf_l Q�9ft蝰"     �  _vsnwscanf_s_l 6轇=5Ｌ婑     �  _vfprintf_l 馁,'塕L�"     �  _vfprintf_s_l �)B枤軉蝰"     �  _vfprintf_p_l _*a�沣蝰     �  _vfscanf_l �5�+聵濜�"     �  _vfscanf_s_l Deri(創篁�"     �  _vsnprintf_l 钡4G椝U
篁�     �  _vsnprintf 0>韝�:�     �  vsnprintf 嫜�5�/鞁蝰     �  _vsprintf_l 跨圳堛〃"     �  _vsprintf_s_l _N葬q�$乞�"     �  _vsprintf_p_l 諕�!5硑蝰"     �  _vsnprintf_s_l 湖�-�"     �  _vscprintf_l 訽Zq1]兡篁�"     �  _vscprintf_p_l 籄K幙谋�"     �  _vscprintf_p 慇Y	4w�'篁�"     �  _vsnprintf_c_l g�ET=>曬     �  _vsscanf_l 菀#�"     �  _vsscanf_s_l �=蒘簌@篁�     �  vsscanf_s �扼E蝰"     �  _vcwprintf_l �膴狜狊蝰"     �  _vcwprintf_s_l 禂蕀蛰��"     �  _vcwprintf_p_l 証躅容駩�     �  _vcwscanf_l %B僮哰�"     �  _vcwscanf_s_l 甃�薈蝰     -  wmemchr 6b
J儔m ]  g  {ctor} ]肮S泉胶� ]  f  {ctor} 'D伋k�[� ]  o  __delDtor 懴�4\�>蝰 ]  j  {dtor} g� z6锏[� u  ~  {ctor} zV+眓秐j� u  �  __delDtor @筫91@蝰 u  �  {dtor} 虻玮fv.&� �  �  {ctor} LF��弣� �  �  {dtor} 8亢1�5�� �  �  __delDtor 晀XB鬘蝰 ]  e  {ctor} �'�%iF     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits 蝰   E        E         E  (      E  v      E  �    �  �  min �1�蓩�    E  �    �  �  min 塖姊�8 "  E  �    �  �  min 埉
1 $  E     �  �  min 
UUkI篨x &  E  l   �  �  min 4仹书"勥 (  E  �   �  �  min 疟[�: *  E  �   �  �  min �^�蠍 ,  E  �   �  �  min -�>埲军 .  E     �  �  min 頷@�	 0  E  H   �  �  min '菵韬�� 2  E  u   �  �  min �,摐�'� 4  E  �   �  �  min 蔑�x垎� 6  E  �   �  �  min 觮+竰�
 8  E  �   �  �  min X蹰N��
 :  E  (   �  �  min q,蹊gK� =  E  ~    ?  E  T   �  �  max 鰬鮌^2�8 A  E  �   �  �  max �j'+懙 C  E  �   �  �  max 捙V艾� �  �  operator== C7=�臕嬹 �  �  operator< 艰@�砈� �  �  operator> FW餻傕膀� �  �  operator<= {軵荌儚 �  �  operator>= A奯貊�$� �  �  operator< X齪�5�6蝰 �  �  operator> b%喹��蝰 �  �  operator<= W�.b髀埳� �  �  operator>= 裈E烒趖� �  �  operator< vtc�$蝰 �  �  operator> 7厂!]Z弪� �  �  operator<= 傔Q畘錸 �  �  operator>= Gp棆.但躐 �  �  {ctor} 燶n�
	2 �  �  _Adjust_manually_vector_aligned %�劻,旰 �  �  _Orphan_all 繅fS遉� �  �  {ctor} 諺搹c~� �  �  {ctor} C鷟@盂,� �  �  operator= ~8w碯耱�* �  �  _Orphan_all_unlocked_v3 �?罰�6 �  �  _Swap_proxy_and_iterators_unlocked M躯}o&     A  _Check_memory_order #非S'U*     A  _Atomic_thread_fence gI�鈑掴篁�. �  F  _Check_load_memory_order �W圣芋蝰 Q  W  test �/醒眢蝰 Q  V  test q祫纇xC報蝰" Q  ]  test_and_set j達�9吔大蝰" Q  [  test_and_set 摲rf锡�-篁� Q  `  clear 媻fA问蝰 Q  _  clear 﨔;麏k┮蝰 Q  e  wait 亅欗虥C篁� Q  d  wait 5Y$鱵倔d篁� Q  h  notify_one 豗晥腦蔄� Q  g  notify_one 蓦�g漫M� Q  h  notify_all �=旧楠i� Q  g  notify_all #庼l�9*X� (  1  {ctor} "~"��#)>� (  4  what 	�鳸羓篁� (  :  __delDtor Ι)(T9潋� (  5  {dtor} N�,�.� (  0  {ctor} �,橼� 檿� A  E  {dtor} W淖傴�m[� A  E  _Decwref  ��$*O篁�" A  Q  _Get_deleter  芬��蝰 A  R  __delDtor @糏徰�+蝰 e  p  is_equal B顟棄I篁�     ?A0x639b76ef 篁�* �  \  debugCheckParameters 
N稫鄠,	篁� �  �  {ctor} 唟Z葵瞲� �  �  {ctor} h伸\	�   �  {ctor} /l模腓崻� �  �  {dtor} 湻G沓�& �  �  GetReSTIRDIContext h`(N]琪鲴& �  �  GetReSTIRDIContext 跈蛛I
�" �  �  GetReGIRContext �|邟廗�" �  �  GetReGIRContext m入N滾	�& �  �  GetReSTIRGIContext S�!!a	］�& �  �  GetReSTIRGIContext 珯��2 �  �  GetRISBufferSegmentAllocator Qq汒Sm?q篁�. �  �  GetLightBufferParameters Ft憪猢t夡蝰6 �  �  GetLocalLightRISBufferSegmentParams 应浒7�> �  �  GetEnvironmentLightRISBufferSegmentParams 潜'�'(惀蝰* �  �  GetNeighborOffsetCount +欕m栗s腭. �  �  IsLocalLightPowerRISEnabled 繜鉠��:" �  �  IsReGIREnabled p12#隈* �  �  SetLightBufferParams �:垳
;�0篁� �  �  operator* 矗@Y	@蝰 �  >  {dtor} 珒�XF疡 �  &  operator-> 僋芴4軫鸟 �  �  operator* Q�9 p:�+蝰 �    {dtor} �7	A�/$岏 �    operator-> }沛!v紟羼 �  �  operator* Q.酔蝰 �  �  {dtor} V瀎s� �  �  operator-> 崢l_w+Q �  �  operator* � g摨m栻� �  �  {dtor} 娯uF
遵 j  �  notify_all 鄢訟囊抙� j  �  notify_one N惽��侎 j  �  wait ��%}/篁� j  �  exchange 浠嵉猂Y襟蝰 j  �  load y袄ツY�9篁� j  �  store gr=怜z简� j  �  {ctor} %莤蹫<�%� m  }  notify_all 訹鏂阊O� m  }  notify_one c	卲�橡� m  |  wait K層亜壦篁� m  y  exchange w�#d{唧蝰 m  v  load 誢┝蝰 m  r  store ^
C騕� L    _Get_first 焑�紿K荞 2  �  operator() 姠恭u疡 +    _Get_first B&釸　滖     operator() #捔滫穫欛 	  +  _Get_first 62fQ9簟 �  %  operator() 	�7 H 榺� �  >  _Get_first ò駠骒� �  8  operator() /L鳫?|r"� m  t  store aJ窐	n蝰 �  �  __delDtor 	槯NANbV蝰 �  �  {dtor} 洽·�(鋰� �  n  {dtor} ]��Ⅰ d     {dtor} 荰蘨魁漻� c  �  {dtor} w媩ss<4滖 d     _Tidy 栿3[y鹖蝰 c  �  _Tidy B竩葅uD蝰 d  b  _Getal 辬N剙槶锐 �  p  deallocate 颚J&�9栥� c  �  _Getal }糇c#TR隈 y  �  deallocate 戍�9|荞 g  {  _Get_first 魧k龠�&像 �  �  _Get_first Z苭刄l{!�* �  �  _Hash_representation �.�n呎祗蝰* �  �  _Hash_representation Ea熪m�1蝰* �  �  _Hash_representation >
疮�*⒁篁�* �  �  _Hash_representation 敷噹�;@惑蝰 �  �  exchange h9eL=篁� �  �  {ctor} 糨煃姭菞� �  
  {ctor} *�gZ恍'� �  /  {ctor} Wl胰悩瘩 �  P  {ctor} 齼�t歑骜 �  i  make_unique |
涵Z�) �  h  operator= ��:n+厄� �  p  make_unique 繀
�=sy �  m  operator= 萺臹墙泈蝰 �  w  make_unique 葼H
H2 �  s  operator= :Us簒"7蝰 �  �  make_unique {9]牓� �  �  operator= j藂棈9蝰 �  N  addressof X^~�/	蝰* �  U  _Atomic_reinterpret_as �鋏3
�#婑& �  R  _Atomic_wait_direct E%蚩%�& �  W  _Atomic_address_as �md}C�& �  [  _Atomic_address_as 蟲傒Q$�& �  _  _Atomic_address_as _6O歲\��* �  a  _Atomic_reinterpret_as F╲畒溤�" �  �  _Destroy_range \!3JS瘪" �  �  _Destroy_range 騨etq�=&� �  �  _Deallocate �テ櫈褒 �  K  reset 
�+隊C?6蝰 �  I  release 枷�(R� �  *  reset ,!4塛g~6蝰 �  (  release YV栂繝� �    reset b沪(壐m蝰 �    release �嚡�\� �  �  reset `桧K糕蝰 �  �  release 蘕\涷T�& �  �  _Fnv1a_append_value �O9`*∥& �  �  _Fnv1a_append_value �"裊嘂�& �  �  _Fnv1a_append_value  �2zT�& �  �  _Fnv1a_append_value D"ⅶ �  �  addressof gF|�钸-蓑�" �  �  construct_at 聒O �
 \篁� �  E  forward 邙�F栂�" �  �  construct_at �2�佫�篁�2 �  �  _Checked_x86_x64_countr_zero 勧顣-�,_篁�* �  �  _Countr_zero_fallback △r噍�8� �  �  {ctor} 籉畔@� 	  �  {ctor} |沤G=3� +  �  {ctor} �
餈�凂 L  �  {ctor} 郭歴廩获 �  �  {ctor} 志搨鋞*� �  G  forward 棻頙.嚮 �  H  forward *曜;M溌~ �  �  {ctor} 偮-U醬y� �  J  forward 纕}�幎 �  K  forward Brw� �  L  forward el|�1`� �  �  {ctor} 芾�/`l%� �  N  forward �脔4暌� �  O  forward �� $y� �  �  {ctor} 鯤u�:� �  Q  forward {V赃�� �  �  addressof ]濵�'沏蝰 �  �  exchange �$僤S鼙篁� �  �  exchange �螑嘚篁� �  �  exchange 鶨� 諎篁� �  �  exchange 琝馏譗篁� �  �  exchange y輜僂qL篁� �  �  exchange ,2I恥�g篁� �  �  exchange 7矑乀漖篁� �  �  exchange 懲�:鰛篁�& �  �  _Countr_zero_tzcnt 廘^T]K颥�& �  �  _Countr_zero_bsf Z隦蘃篁�* �     _Countl_zero_fallback /�澢嗲4蝰 �    {ctor} �_﹄Ed礼 	    {ctor} 捻O鈖Ｙ� +    {ctor} ]��(\S� L    {ctor} 悳�y3)� �  S  forward G鞞]毉ㄟ �  U  forward 奪p另L� �  W  forward 蓐悁~[ �  Y  forward �
|騢+� \  �  V    f  L  �    k  L  �   6     x  _invalid_parameter_noinfo_noreturn 7毺欛詮�       _fstat64i32 5dＩji� p  L  �    r  �      v  L  �        
  strrchr 2拋1^睖�       wcsrchr [凵�+�.     �  __stdio_common_vfprintf_s �/�箛蝰*     �  __stdio_common_vsscanf ""健谌:庱.       __conio_common_vcwprintf x�嗃G+*篁�     #  _ctime64 vJ#垕�
篁�     ;  _ctime64_s �哫� 5&K�     �  _ldpcomp �<X櫂篁� |  L  �          strnlen s�cp6.       __conio_common_vcwprintf_p ��J唏     
  strchr �:'�嘫j� �  L  �          strstr エ^.A:�,�"     x  __std_terminate uSw檽\�+ �  L  �        �  frexp �7il�孹滘�&       _wsopen_dispatch �=铩v�.
篁� �  L  �        �  free 瓡怔e╩篁馞     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h  �  L  �    �  L  �   "     *  _localtime64 �煞1,Qs篁�     |  _ldsign [r遘鹣N     /  _time64 腢濭
m@�*     [  __std_type_info_name �>椎鐰蒹蝰2     H  __std_atomic_notify_one_direct H嫟ヒ%Q蜀 �  L  �   *     �  __stdio_common_vfscanf 5'LcB趄f� �  L  �    �  L  �   *     B  __std_type_info_hash 刷@助龃殷蝰     �  ldexp n氕薆钇铗� �  �  g    �  L  �   .       __conio_common_vcwprintf_s 嘛g咕d� �  L  �    �  
  E    �  L  �   .     �  __stdio_common_vswprintf_p 赺庌戸鼐�*     �  __stdio_common_vsprintf 韃CjT詡     	  wcsstr 
挊[v锐 �  L  �          wcschr 鬡6渟振     z  _fdsign 赤)滑擣     �  _errno 唒庘E3�.     �  __stdio_common_vswprintf 嬏朹7S篁�     �  _copysign 崪㈢4侣简�     �  _chgsign (,z�%p%篁� �  L  �    �  L  {   "     :  _get_terminate 	dJ猔�     $  _stat64i32 薢5蹀4u�       wcstok ;,�7W綊榴*     b  __ExceptionPtrRethrow 株d}[-�*蝰2     T  __ExceptionPtrCurrentException �*蹝潵t柜     	  wcspbrk 軡猈_ �  L  �        �  terminate R鲌-翉K蝰*     a  __ExceptionPtrCompare +耐M蝰.     �  __stdio_common_vsnwprintf_s 椳"捏濟.     �  __stdio_common_vsnprintf_s `4$1��*     M  _Smtx_lock_exclusive  U觜冤篁�     �  _purecall G眇鹮蝰*     T  __ExceptionPtrDestroy 
�/妦捔欜�*     �  __stdio_common_vfprintf xG踮銕dJ"     �  operator delete ln%�="     T  operator delete 儫圂Z孔h �  
  =    �  �  !    �  �  Z   .     x  _invalid_parameter_noinfo a團阵縜蝰 �  L  �    �  L  �   *     W  __std_type_info_compare r	埘1鎜*     [  __ExceptionPtrToBool b噠稵對燇蝰.     �  __stdio_common_vsprintf_s 瓷4?C袮蝰     *  _gmtime64 瑓斉"rl沈� �  �      �  �  H   *     �  __std_exception_copy ＼?)恉�	篁� �  L  �   .     �  __stdio_common_vsprintf_p 怳u]|S!]蝰6     O  __std_atomic_compare_exchange_128 �*偾�uG蝰     ,  _mktime64 >$芗芳Z蝰.     �  __stdio_common_vfwprintf 犱tw王摡篁�*     M  _Smtx_unlock_exclusive 睠~_�&     _  __ExceptionPtrSwap Ua鉦丫燽�*     �  __stdio_common_vswscanf 划�9L钌~*       __conio_common_vcwscanf 炞2赕幟�" �  �  __vecDelDtor 血賚W﹗象蝰 �  d    R     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp  �  �  �  " (  :  __vecDelDtor 4栂lH瞍8篁� �  ?  U   �  d  +   " u  �  __vecDelDtor �)N~篁�" s  �  __vecDelDtor .>N耰,掦蝰2 �  x  _Unlock_shared_ptr_spin_lock |�~輗+篁�" $  4  __vecDelDtor d苂{8�(篁� �  �  �   �  �    " 
    __vecDelDtor �#�榹+篁� �  �  P   J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common 蝰 �  �  *   " �  �  __vecDelDtor 9u�^�3蝰 �  �  �    A  E  _Destroy 1�纄谠篁�" A  E  _Delete_this �懕.W慒篁�" A  R  __vecDelDtor 釼a胜:mt篁�" �    __vecDelDtor 嫓��1!/篁� �  �  *    e  r  do_allocate 籟婲棌�(" e  t  do_deallocate d
0�6�2球� e  p  do_is_equal o>⑸�0诒     std::pmr 篁�2 �  �  _Aligned_get_default_resource ?!|挅j蝰 �  d  ,    �  d  -   " �  �  __vecDelDtor �0蹚飝篁�" ]  o  __vecDelDtor 匫u� �圀蝰 �  E  ?   �  �  {ctor} 鯸肖H�)M� �  �  {dtor} 
蜀盬慮G�   �  �     �    . �  x  _Lock_shared_ptr_spin_lock ^9蔍bｑ   L  �   &     X  __ExceptionPtrCopy �&�5铖   L  �      L  �      L  �   .     �  __std_atomic_wait_direct 妥潈;�篁�"     �  operator new 岧p邀FL朋蝰"     }  operator new 煮g貙I汅蝰"     =  _localtime64_s �'迓�# �  �  {ctor} <弈J碕屡�2 �  �  GetInitialSamplingParameters 1�6窱�殷蝰& �  �  GetStaticParameters $礬.韕�   �  U      �  4      �  .      �       �  �  {ctor} 係郫銾Q赳. �  �  GetReGIRDynamicParameters L握珻`婒� !  �            _wctime64_s 栖b瓷矏     |  _dsign �o,試6I�"     �  __acrt_iob_func ��-曖*     �  __stdio_common_vfwscanf vVツし�.     ]  __ExceptionPtrCopyException +赹�9�3 #  L  �    %  L  �        =  _gmtime64_s 杳悓嘄|       strpbrk '$[W
堈2     H  __std_atomic_notify_all_direct 饝j厳皆婑"     4  _timespec64_get 2pヾ��.     �  __stdio_common_vfprintf_p 哜[8蝰     ~  _fdpcomp z�7!Ky篁�     ,  _mkgmtime64 皙O痜z�     �  _dpcomp ��!4{S (  L  �   .     �  __stdio_common_vswprintf_s \纣迖N竹 ,  L  �        %  _difftime64 �(頹睄7*     T  __ExceptionPtrCreate 淜蚨t $篁�.     �  __stdio_common_vfwprintf_s =謔S燱耞� .  �  .   .     �  __stdio_common_vfwprintf_p 眏F
�8 狁     �  modf E晔茄r镢篁� 2  L  �    4  L  �    6  L  �    :  L  �   *     X  __ExceptionPtrAssign K44�?篁�*     �  __std_exception_destroy �-�-穞�2       _wctime64 坓殕!uw忩�       wcsnlen 	霩b调^qF     D:\RTXPT\External\Rtxdi\Source\ImportanceSamplingContext.cpp 篁�  %  &  �  1  /  蝰 L  �  )    r  
  M   2   S  GetDefaultReSTIRDIBufferIndices 頗嗱嵨�:   U  GetDefaultReSTIRDIInitialSamplingParams �8戽m@>   X  GetDefaultReSTIRDITemporalResamplingParams �&gb>H�>   [  GetDefaultReSTIRDISpatialResamplingParams T衐綺.{怛�2   ^  GetDefaultReSTIRDIShadingParams ;烸|劳J�*   _  debugCheckParameters �8J�4篁�& �  �  GetResamplingMode pS7b'q膛蝰& �  �  GetRuntimeParams �2Dn|k�篁�2 �  �  GetReservoirBufferParameters E慄�&螀篁�& �  �  GetBufferIndices ㄇn]"Um黧蝰2 �  �  GetTemporalResamplingParameters 剑虮潦湇2 �  �  GetSpatialResamplingParameters =駻譯� 烋* �  �  GetShadingParameters 2
e}游隗蝰" �  �  SetFrameIndex m娙��2蝰" �  �  GetFrameIndex !妑�"蝰& �  �  SetResamplingMode 馚�!,�+囹�2 �  �  SetInitialSamplingParameters nhXBん蝰2 �  �  SetTemporalResamplingParameters r遜U�?眉2 �  �  SetSpatialResamplingParameters 貭.�籭锺* �  �  SetShadingParameters d毐j═蛀篁�& �  �  UpdateBufferIndices ^珥�蟩�* �  �  UpdateCheckerboardField ^聻�t�
2     D:\RTXPT\External\Rtxdi\Source\ReSTIRDI.cpp   %  &    1  /  蝰J     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm 篁� u  	  '   {  	  Y   �  	  �   �  	     �  	  �   �  	  L   �  	  q   �  	  �   �  	  �   �  	  V   �  	  u   �  	  �   �  	  �   �  	  *   �  	  p   �  	  �   �  	     �  	  :   �  	  �   �  	  �   �  	  
   �  	  �
   �  	  '   �  	  q     	  �     	  )     	  `     	  �     	  �   #  	  A   )  	  v   /  	  �   5  	  �   ;  	  W   A  	  �   G  	  ~   M  	  �   S  	  t   Y  	  �   _  	     e  	     k  	  �   q  	     w  	  �   }  	  �   �  	  �   �  	  q   �  	  �   �  	  �   �  	     �  	  <   �  	  �   �  	  �   �  	  W   �  	  �   �  	  5    �  	  ?!   �  	  k"   �  	  �"   �  	  {#   �  	  �#   �  	  �$   �  	  %   �  	  �%   �  	  &   �  	  �&     	  U'     	  (   
  	  �(     	  )     	  )     	  \)   %  	  x)   R  �  v    Y  �  ^    ]  �  b    a  �  c    d  �  U    �  �  G    �  �  ?  2     D:\RTXPT\External\Rtxdi\Source\ReGIR.cpp 篁� �  Z  �      �  \   "  �  [   B  �  �   J  �  �   V  �  W   u  �  �   �  ?  �	   �  i  �   �  �  �   �  �  �     �  =   3  �  =  F     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility � S  h  �    m  �  �       )  frexpf Q�6睅��     '  hypotf 慱Oc=1�     +  ldexpf Gw�镚n?�     �  acosl X.樱t隍�     �  asinl ﹀烟; 堯�     �  atan2l 垌L�d这�     �  atanl �*烿鳤�8蝰     �  coshl Q�郺�
蒡�     �  cosl 铩攱.�篁�     �  expl �?絅缼)篁�     �  fmodl 釬�1v1膨�     �  frexpl 	�-A�bxY�     �  hypotl 蚻P98�     �  ldexpl 陁A圥皇     �  logl ⒒d�(J骟蝰     �  log10l 姃(�(OS=�     �  modfl �=<紣蝰     �  powl {S陣�/篁�     �  sinhl �蜠>F嘱蝰     �  sinl 忂鴬�<�:篁�     �  sqrtl 綨<�)囹�     �  tanhl Ｍv�
蝰     �  tanl 擝|PC篁� �  �  max �5�=E�+. �  x  _Throw_bad_array_new_length 擠諚聖 �  �  _Allocate yC羡E抖�" �  �  _Alloc_proxy 敤dy.躰篁� �  �  _Adopt Jd�:��!� �     {ctor} 轊允A熍駍::_Unique_fn .?AV_Unique_fn@ranges@std@@ 蝰
 7    	   7  8   
 �      "   �    蝰9  _Unique_fn 馢  :           std::ranges::_Unique_fn .?AV_Unique_fn@ranges@std@@ 蝰 	   7  8    �      R   �              std::ranges::_Unique_copy_fn .?AV_Unique_copy_fn@ranges@std@@ 
 =    	   =  >   
 �      &   �    蝰?  _Unique_copy_fn R  @           std::ranges::_Unique_copy_fn .?AV_Unique_copy_fn@ranges@std@@  	   =  >    �      J   �              std::ranges::_Reverse_fn .?AV_Reverse_fn@ranges@std@@ 
 C    	   C  D   
 �      "   �    蝰E  _Reverse_fn J  F           std::ranges::_Reverse_fn .?AV_Reverse_fn@ranges@std@@  	   C  D    �      V   �              std::ranges::_Reverse_copy_fn .?AV_Reverse_copy_fn@ranges@std@@ 蝰
 I    	   I  J   
 �      *   �    蝰K  _Reverse_copy_fn 篁馰  L           std::ranges::_Reverse_copy_fn .?AV_Reverse_copy_fn@ranges@std@@ 蝰 	   I  J    �      J   �              std::ranges::_Rotate_fn .?AV_Rotate_fn@ranges@std@@ 蝰
 O    	   O  P   
 �      "   �    蝰Q  _Rotate_fn 馢  R           std::ranges::_Rotate_fn .?AV_Rotate_fn@ranges@std@@ 蝰 	   O  P    �      R   �              std::ranges::_Rotate_copy_fn .?AV_Rotate_copy_fn@ranges@std@@ 
 U    	   U  V   
 �      &   �    蝰W  _Rotate_copy_fn R  X           std::ranges::_Rotate_copy_fn .?AV_Rotate_copy_fn@ranges@std@@  	   U  V    �      J   �              std::ranges::_Sample_fn .?AV_Sample_fn@ranges@std@@ 蝰
 [    	   [  \   
 �      "   �    蝰]  _Sample_fn 馢  ^           std::ranges::_Sample_fn .?AV_Sample_fn@ranges@std@@ 蝰 	   [  \    �      J   �              std::ranges::_Shuffle_fn .?AV_Shuffle_fn@ranges@std@@ 
 a    	   a  b   
 �      "   �    蝰c  _Shuffle_fn J  d           std::ranges::_Shuffle_fn .?AV_Shuffle_fn@ranges@std@@  	   a  b    �      N   �              std::ranges::_Partition_fn .?AV_Partition_fn@ranges@std@@ 
 g    	   g  h   
 �      &   �    蝰i  _Partition_fn 蝰N  j           std::ranges::_Partition_fn .?AV_Partition_fn@ranges@std@@  	   g  h    �      ^   �              std::ranges::_Stable_partition_fn .?AV_Stable_partition_fn@ranges@std@@ 蝰
 m    	   m  n   
 �      .   �    蝰o  _Stable_partition_fn 篁馸  p           std::ranges::_Stable_partition_fn .?AV_Stable_partition_fn@ranges@std@@ 蝰 	   m  n    �      N   �              std::ranges::_Push_heap_fn .?AV_Push_heap_fn@ranges@std@@ 
 s    	   s  t   
 �      &   �    蝰u  _Push_heap_fn 蝰N  v           std::ranges::_Push_heap_fn .?AV_Push_heap_fn@ranges@std@@  	   s  t    �      N   �              std::ranges::_Pop_heap_fn .?AV_Pop_heap_fn@ranges@std@@ 蝰
 y    	   y  z   
 �      &   �    蝰{  _Pop_heap_fn 篁馧  |           std::ranges::_Pop_heap_fn .?AV_Pop_heap_fn@ranges@std@@ 蝰 	   y  z    �      N   �              std::ranges::_Make_heap_fn .?AV_Make_heap_fn@ranges@std@@ 
     	     �   
 �      &   �    蝰�  _Make_heap_fn 蝰N  �           std::ranges::_Make_heap_fn .?AV_Make_heap_fn@ranges@std@@  	     �    �      J   �              std::ranges::_Is_heap_fn .?AV_Is_heap_fn@ranges@std@@ 
 �    	   �  �   
 �      "   �    蝰�  _Is_heap_fn J  �           std::ranges::_Is_heap_fn .?AV_Is_heap_fn@ranges@std@@  	   �  �    �      V   �              std::ranges::_Is_heap_until_fn .?AV_Is_heap_until_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Is_heap_until_fn 蝰V  �           std::ranges::_Is_heap_until_fn .?AV_Is_heap_until_fn@ranges@std@@  	   �  �    �      N   �              std::ranges::_Sort_heap_fn .?AV_Sort_heap_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Sort_heap_fn 蝰N  �           std::ranges::_Sort_heap_fn .?AV_Sort_heap_fn@ranges@std@@  	   �  �    �      R   �              std::ranges::_Lower_bound_fn .?AV_Lower_bound_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Lower_bound_fn R  �           std::ranges::_Lower_bound_fn .?AV_Lower_bound_fn@ranges@std@@  	   �  �    �      R   �              std::ranges::_Upper_bound_fn .?AV_Upper_bound_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Upper_bound_fn R  �           std::ranges::_Upper_bound_fn .?AV_Upper_bound_fn@ranges@std@@  	   �  �    �      R   �              std::ranges::_Equal_range_fn .?AV_Equal_range_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Equal_range_fn R  �           std::ranges::_Equal_range_fn .?AV_Equal_range_fn@ranges@std@@  	   �  �    �      V   �              std::ranges::_Binary_search_fn .?AV_Binary_search_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Binary_search_fn 蝰V  �           std::ranges::_Binary_search_fn .?AV_Binary_search_fn@ranges@std@@  	   �  �    �      F   �              std::ranges::_Merge_fn .?AV_Merge_fn@ranges@std@@ 
 �    	   �  �   
 �      "   �    蝰�  _Merge_fn 蝰F  �           std::ranges::_Merge_fn .?AV_Merge_fn@ranges@std@@  	   �  �    �      V   �              std::ranges::_Inplace_merge_fn .?AV_Inplace_merge_fn@ranges@std@@ 
 �    	   �  �   
 �      *   �    蝰�  _Inplace_merge_fn 蝰V  �           std::ranges::_Inplace_merge_fn .?AV_Inplace_merge_fn@ranges@std@@  	   �  �    �      F   �              std::ranges::_Sort_fn .?AV_Sort_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      "   �    蝰�  _Sort_fn 篁馞  �           std::ranges::_Sort_fn .?AV_Sort_fn@ranges@std@@ 蝰 	   �  �    �      R   �              std::ranges::_Stable_sort_fn .?AV_Stable_sort_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Stable_sort_fn R  �           std::ranges::_Stable_sort_fn .?AV_Stable_sort_fn@ranges@std@@  	   �  �    �      V   �              std::ranges::_Partial_sort_fn .?AV_Partial_sort_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Partial_sort_fn 篁馰  �           std::ranges::_Partial_sort_fn .?AV_Partial_sort_fn@ranges@std@@ 蝰 	   �  �    �      ^   �              std::ranges::_Partial_sort_copy_fn .?AV_Partial_sort_copy_fn@ranges@std@@ 
 �    	   �  �   
 �      .   �    蝰�  _Partial_sort_copy_fn 蝰^  �           std::ranges::_Partial_sort_copy_fn .?AV_Partial_sort_copy_fn@ranges@std@@  	   �  �    �      R   �              std::ranges::_Nth_element_fn .?AV_Nth_element_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Nth_element_fn R  �           std::ranges::_Nth_element_fn .?AV_Nth_element_fn@ranges@std@@  	   �  �    �      N   �              std::ranges::_Includes_fn .?AV_Includes_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      &   �    蝰�  _Includes_fn 篁馧  �           std::ranges::_Includes_fn .?AV_Includes_fn@ranges@std@@ 蝰 	   �  �    �      N   �              std::ranges::_Set_union_fn .?AV_Set_union_fn@ranges@std@@ 
 �    	   �  �   
 �      &   �    蝰�  _Set_union_fn 蝰N  �           std::ranges::_Set_union_fn .?AV_Set_union_fn@ranges@std@@  	   �  �    �      ^   �              std::ranges::_Set_intersection_fn .?AV_Set_intersection_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      .   �    蝰�  _Set_intersection_fn 篁馸  �           std::ranges::_Set_intersection_fn .?AV_Set_intersection_fn@ranges@std@@ 蝰 	   �  �    �      Z   �              std::ranges::_Set_difference_fn .?AV_Set_difference_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Set_difference_fn 馴  �           std::ranges::_Set_difference_fn .?AV_Set_difference_fn@ranges@std@@ 蝰 	   �  �    �      n   �              std::ranges::_Set_symmetric_difference_fn .?AV_Set_symmetric_difference_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      6   �    蝰�  _Set_symmetric_difference_fn 篁駈  �           std::ranges::_Set_symmetric_difference_fn .?AV_Set_symmetric_difference_fn@ranges@std@@ 蝰 	   �  �    �      Z   �              std::ranges::_Minmax_element_fn .?AV_Minmax_element_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      *   �    蝰�  _Minmax_element_fn 馴  �           std::ranges::_Minmax_element_fn .?AV_Minmax_element_fn@ranges@std@@ 蝰 	   �  �    �      J   �              std::ranges::_Minmax_fn .?AV_Minmax_fn@ranges@std@@ 蝰
 �    	   �  �   
 �      "   �    蝰�  _Minmax_fn 馢              std::ranges::_Minmax_fn .?AV_Minmax_fn@ranges@std@@ 蝰 	   �  �    �      ^   �              std::ranges::_Next_permutation_fn .?AV_Next_permutation_fn@ranges@std@@ 蝰
     	        
 �      .   �    蝰  _Next_permutation_fn 篁馸             std::ranges::_Next_permutation_fn .?AV_Next_permutation_fn@ranges@std@@ 蝰 	         �      ^   �              std::ranges::_Prev_permutation_fn .?AV_Prev_permutation_fn@ranges@std@@ 蝰
 	    	   	  
   
 �      .   �    蝰  _Prev_permutation_fn 篁馸             std::ranges::_Prev_permutation_fn .?AV_Prev_permutation_fn@ranges@std@@ 蝰 	   	  
    �      N   �              std::ranges::_Is_sorted_fn .?AV_Is_sorted_fn@ranges@std@@ 
     	        
 �      &   �    蝰  _Is_sorted_fn 蝰N             std::ranges::_Is_sorted_fn .?AV_Is_sorted_fn@ranges@std@@  	         �      Z   �              std::ranges::_Is_sorted_until_fn .?AV_Is_sorted_until_fn@ranges@std@@ 
     	        
 �      *   �    蝰  _Is_sorted_until_fn Z             std::ranges::_Is_sorted_until_fn .?AV_Is_sorted_until_fn@ranges@std@@  	         �      F   �              std::ranges::_Clamp_fn .?AV_Clamp_fn@ranges@std@@ 
     	        
 �      "   �    蝰  _Clamp_fn 蝰F             std::ranges::_Clamp_fn .?AV_Clamp_fn@ranges@std@@  	         �      j   �              std::ranges::_Lexicographical_compare_fn .?AV_Lexicographical_compare_fn@ranges@std@@ 
 !    	   !  "   
 �      2   �    蝰#  _Lexicographical_compare_fn j  $           std::ranges::_Lexicographical_compare_fn .?AV_Lexicographical_compare_fn@ranges@std@@  	   !  "    �       @      }      @   t   @      (      @   t    @      *         y         y      @   @   @      .      @   A    @      0      @   @   t   @      2      @       @      4         {         {      A   A   t   A      8      A       A      :      u   u   A    A     <      u   u   @    @     >   A        @     }   A     {   @     y      A   A   A    A     D      @   @   @    @     F      u   A    A     H      u   @    @     J      �  �  �   @     L      �  �  �   A     N   	   �  �   
        � 
 @     regirCellSize 
 �   center 篁�
 �   fallbackSamplingMode �
 �   presamplingMode 蝰
 @    regirSamplingJitter 蝰
 u    regirNumBuildSamples �P  ReGIRDynamicParameters 馰  Q            rtxdi::ReGIRDynamicParameters .?AUReGIRDynamicParameters@rtxdi@@ � 	   �  �           
 �    
 T     	   �  b   
           g    k    V  6
 u     lightSlotCount 篁�
 u    regirOnionCells 蝰
 c   regirOnionLayers �
 d    regirOnionRings 蝰
 @   8 regirOnionCubicRootFactor 
 @   < regirOnionLinearFactor 篁� W  ReGIROnionCalculatedParameters �n  ~ReGIROnionCalculatedParameters  r  operator= 蝰s  __vecDelDtor 篁駀 
 &X          @ rtxdi::ReGIROnionCalculatedParameters .?AUReGIROnionCalculatedParameters@rtxdi@@ � 	   c  �   
        J    �     �     �     �     �     �     �     �     Z  �  y  _Alty 蝰  z  _Alty_traits 篁�  {  value_type �  y  allocator_type �  |  pointer   ~  const_pointer 蝰    reference 蝰  �  const_reference   #   size_type 蝰     difference_type   �  _Scary_val �  �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�  �  const_reverse_iterator �	 [  vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 篁� �  operator= 蝰 �  ~vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > 蝰 �  push_back 蝰 �  insert � �  assign � �  resize �  �  _Reallocation_policy 篁� �  _Clear_and_reserve_geometric 篁� �  reserve  �  shrink_to_fit 蝰 �  pop_back 篁� �  erase 蝰 �  clear 蝰 �  swap 篁� �  data 篁� �  begin 蝰 �  end  �  rbegin � �  rend 篁� �  cbegin � �  cend 篁� �  crbegin  �  crend 蝰 �  _Unchecked_begin 篁� �  _Unchecked_end � �  empty 蝰 �  size 篁� �  max_size 篁� �  capacity 篁� �  operator[] � �  at � �  front 蝰 �  back 篁� �  get_allocator 蝰 �  _Calculate_growth 蝰 �  _Buy_raw 篁� �  _Buy_nonzero 篁� �  _Change_array 蝰 �  _Tidy 蝰 �  _Move_assign_unequal_alloc �	 �  _Xlength 篁�	 �  _Xrange  �  _Orphan_range 蝰 �  _Getal � �  _Make_iterator � �  _Make_iterator_offset 蝰
 �    _Mypair 蝰�  __vecDelDtor 篁穸 ] 6\           std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > .?AV?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ � 	   d     
        J                                            ^  �  �  _Alty 蝰  �  _Alty_traits 篁�  �  value_type �  �  allocator_type �  �  pointer   �  const_pointer 蝰  �  reference 蝰  �  const_reference   #   size_type 蝰     difference_type   �  _Scary_val �  �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�     const_reverse_iterator �	 _  vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > 篁�   operator= 蝰    ~vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > 蝰 &  push_back 蝰 /  insert � 5  assign � 7  resize �  8  _Reallocation_policy 篁� 6  _Clear_and_reserve_geometric 篁� 9  reserve  :  shrink_to_fit 蝰    pop_back 篁� ?  erase 蝰    clear 蝰 A  swap 篁� E  data 篁� H  begin 蝰 H  end  K  rbegin � K  rend 篁� F  cbegin � F  cend 篁� I  crbegin  I  crend 蝰 E  _Unchecked_begin 篁� E  _Unchecked_end � L  empty 蝰 M  size 篁� M  max_size 篁� M  capacity 篁� P  operator[] � S  at � V  front 蝰 V  back 篁� W  get_allocator 蝰 X  _Calculate_growth 蝰 9  _Buy_raw 篁� 6  _Buy_nonzero 篁� [  _Change_array 蝰    _Tidy 蝰 \  _Move_assign_unequal_alloc �	 ]  _Xlength 篁�	 ]  _Xrange  _  _Orphan_range 蝰 c  _Getal � e  _Make_iterator � f  _Make_iterator_offset 蝰
 g    _Mypair 蝰h  __vecDelDtor 篁駷 ] 6`           std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > .?AV?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ � 	   �     
        V 
 u     lightSlotCount 篁�
 u    pad 蝰b  ReGIRGridCalculatedParameters 蝰f  c           rtxdi::ReGIRGridCalculatedParameters .?AUReGIRGridCalculatedParameters@rtxdi@@ 篁� 	   �             
 ^    
 f    
 �    
 �    
 i    
 |    
 k    
     
 m    
 {    
 �  ,      p  p   p    q  
 �    
 }   蝰
 t    
 u    
 �   蝰
 w    
 w  ,  j   �              std::vector<float,std::allocator<float> > .?AV?$vector@MV?$allocator@M@std@@@std@@ 篁�
 z   B   �              std::allocator<float> .?AV?$allocator@M@std@@ v   �              std::allocator_traits<std::allocator<float> > .?AU?$allocator_traits@V?$allocator@M@std@@@std@@ 蝰
 @   ,  r   �              std::_Vector_val<std::_Simple_types<float> > .?AV?$_Vector_val@U?$_Simple_types@M@std@@@std@@ �   �              std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰�   �              std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰�   �              std::reverse_iterator<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > > .?AV?$reverse_iterator@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@@std@@ �   �              std::reverse_iterator<std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > > .?AV?$reverse_iterator@V?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@@std@@ 
 z  �  
 |   蝰
 �  ,      �  �   	   z  {    �      
    �   	   z  {   
 �      
 z   蝰
 �  ,      �  �   	   z  {    �      
    �   	   z  {    �      R   �              std::initializer_list<float> .?AV?$initializer_list@M@std@@ 蝰    �  �   	   z  {    �          �  �  �   	   z  {    �          �  �   	   z  {    �      
    �   	   z  {   
 �       	   z  {   
        J    �     �     �     �     �     �     �     �     �  
 z  ,  
    �   	�  z  {     �       	�  z  {     �       	�  z  {     �          �     �     �   	   z  {           
 @   �  
    �   	   z  {     �       	   z  {     �          �     �      �  �   	�  z  {    �          �  �  �   	�  z  {    �          �  �   	�  z  {    �          �  �   	�  z  {    �      "    �     �     �     �  
 �   蝰
    �   	   z  {     �       	   z  {     �          �     �   	   z  {     �          �     �  �  t   �  std::vector<float,std::allocator<float> >::_Reallocation_policy .?AW4_Reallocation_policy@?$vector@MV?$allocator@M@std@@@std@@ � 	   z  {     |       	   z  {                �  �   	�  z  {    �      
    �   	�  z  {    �          �     �  
    �   	   z  {    �      
 �    	�  z  �            	@  z  {               �     �   	�  z  �   	         	�  z  {   	            �     �   	�  z  �   	         	�  z  {   	            �     �   	0   z  �            	#   z  �            	�  z  �    �       	~  z  {    �          �     �   	�  z  �     �       	~  z  {     �          �     �   	�  z  �            	~  z  {               �     �   	|  z  �   	         	#   z  �     �      
 @        �  �  �   	   z  {     �       	   z  {     �       	   z                    @  @   	   z  �     �       	�  z  �           
 |  ,   	�  z  {               �     �  
    �   	�  z  {   	 �       	�  z  {   	 �      �   �              std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> .?AV?$_Compressed_pair@V?$allocator@M@std@@V?$_Vector_val@U?$_Simple_types@M@std@@@2@$00@std@@ 篁� 	  z  {    &      b  |  _Alty 蝰  }  _Alty_traits 篁�  @   value_type �  |  allocator_type �  @  pointer   �  const_pointer 蝰  ~  reference 蝰  �  const_reference   #   size_type 蝰     difference_type     _Scary_val �  �  iterator 篁�  �  const_iterator �  �  reverse_iterator 篁�  �  const_reverse_iterator �	 �  vector<float,std::allocator<float> > 篁� �  operator= 蝰 �  ~vector<float,std::allocator<float> > 蝰 �  push_back 蝰 �  insert � �  assign � �  resize �  �  _Reallocation_policy 篁� �  _Clear_and_reserve_geometric 篁� �  reserve  �  shrink_to_fit 蝰 �  pop_back 篁� �  erase 蝰 �  clear 蝰 �  swap 篁� �  data 篁� �  begin 蝰 �  end  �  rbegin � �  rend 篁� �  cbegin � �  cend 篁� �  crbegin  �  crend 蝰 �  _Unchecked_begin 篁� �  _Unchecked_end � �  empty 蝰 �  size 篁� �  max_size 篁� �  capacity 篁� �  operator[] � �  at � �  front 蝰 �  back 篁� �  get_allocator 蝰 �  _Calculate_growth 蝰 �  _Buy_raw 篁� �  _Buy_nonzero 篁� �  _Change_array 蝰 �  _Tidy 蝰 �  _Move_assign_unequal_alloc �	 �  _Xlength 篁�	 �  _Xrange  �  _Orphan_range 蝰 �  _Getal � �  _Make_iterator � �  _Make_iterator_offset 蝰
 �    _Mypair 蝰�  __vecDelDtor 篁駄 ] 6�           std::vector<float,std::allocator<float> > .?AV?$vector@MV?$allocator@M@std@@@std@@ 篁�
 z     �     L      y  y   @      �      �  �   �    �  
 �   蝰    �  �         �  V   �              std::contiguous_iterator_tag .?AUcontiguous_iterator_tag@std@@ 篁馴   �              std::random_access_iterator_tag .?AUrandom_access_iterator_tag@std@@ �    @  �  
 �    	   �  �   
 �       	   �  �   
            �     �  
 �   蝰
 �    	�  �  �            	�  �  �            	�  �  �   	 �      
 �  ,   	   �  �               �       
    �   	   �  �           	   �  �           	�  �  �   	       
 �  ,  
       	   �  �              	        	�  �  �           	0   �  �           	�  �  �   	        	   �  �          
    �   	   �  �          V  �    蝰  �  iterator_concept 篁�  �  iterator_category 蝰  @   value_type �     difference_type   �  pointer   �  reference 蝰  @  _Tptr 蝰 �  _Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > 蝰 �  operator* 蝰 �  operator-> �   operator++ �   operator-- �   _Verify_offset �   operator+= �   operator+ 蝰   operator-= � 
  operator- 蝰   operator[] �   operator== � 
  operator<=>    _Compat   �  _Prevent_inheriting_unwrap � �  _Unwrapped �   _Seek_to 篁�
 @    _Ptr 癫             std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰
 �    	~  �              	@  �             
 �    	�  �     	 �      
 �  ,   	  �                         	  �             	�  �     	        	~  �             	   �     
 �       	   �     
                 �  �    蝰  �  _Mybase   �  iterator_concept 篁�  �  iterator_category 蝰  @   value_type �     difference_type   @  pointer   ~  reference 蝰   operator* 蝰   operator-> �   operator++ �   operator-- �   operator+= �   operator+ 蝰   operator-= �   operator- 蝰   operator[] �  �  _Prevent_inheriting_unwrap �   _Unwrapped �    _Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > �  !           std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > .?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@M@std@@@std@@@std@@ 蝰
 �        �  �  @    @      $  
 �   蝰
 &    
 '     	   �  b     j      
 h    
 *     	   c  �     �      
 �    
 -     	   d       
      
 	    
 0    
 {   蝰
 2    
 �    
 4    
 �   蝰
 6    
 �   蝰
 8    
 �    
 :    
 |    	   |  <   
            �  �   	   |  <     >       	@  |  <     �        |  _From_primary 蝰  @   value_type �  #   size_type 蝰     difference_type   l  propagate_on_container_move_assignment �  l  is_always_equal  =  allocator<float> 篁� ?  deallocate � @  allocate 篁� �  _Minimum_asan_allocation_alignment 馚 
 A           std::allocator<float> .?AV?$allocator@M@std@@ 
 �   蝰
 C    	�  �  D           
 �    	�  �  F               E     G  F   |    蝰
     _Myval2 蝰  |  _Mybase  H  _Get_first 褚  I           std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> .?AV?$_Compressed_pair@V?$allocator@M@std@@V?$_Vector_val@U?$_Simple_types@M@std@@@2@$00@std@@ 篁�
     
 K        @  @  @  
     	     N   
 M       	     N   
            O     P  
   ,  
    R   	     N    S      :  �    蝰  @   value_type �  #   size_type 蝰     difference_type   @  pointer   �  const_pointer 蝰  ~  reference 蝰  �  const_reference  Q  _Vector_val<std::_Simple_types<float> >  T  _Swap_val 蝰 T  _Take_contents �
 @    _Myfirst �
 @   _Mylast 蝰
 @   _Myend 篁駌  U           std::_Vector_val<std::_Simple_types<float> > .?AV?$_Vector_val@U?$_Simple_types@M@std@@@std@@ 
 @    
 �    
 C    
 Y    
    蝰
 [    
 \    
 [  ,  
 �     	   �      �      
 �     	   �  �    �       K    S   	~  z  {     �       	   �  F    �       	   �  F   
 �      
 w    
 g    
 �   蝰
 i    
 j    
 i  ,  
 Y     	�  d       $      Z   �              std::_One_then_variadic_args_t .?AU_One_then_variadic_args_t@std@@ 篁�
 �  �      o  p   	   g  z    q       	   g  z   
 q      
     Z    �           std::_One_then_variadic_args_t .?AU_One_then_variadic_args_t@std@@ 篁�
 o    
 Y  ,      �  w  w   	   d       x      
 m     	   g  z    �       	   g  z   
 �      
 �    
 }    
 �   蝰
     
 �    
   ,  
 �     	  c  �     �      
 y  �      o  �   	   �  �    �       	   �  �   
 �      
 �    
 �  ,      �  �  �   	   c  �     �      
 �     	   �  �    �       	   �  �   
 �      
 �    
 |    
 W    
 @  ,      @  �  �        �   	   �  m           	   y  �    �       0     �   0     �  :   �              std::less<void> .?AU?$less@X@std@@ 篁�    �  �  �         �     t   is_transparent �:  �           std::less<void> .?AU?$less@X@std@@ 篁�
 �    :   �              std::plus<void> .?AU?$plus@X@std@@ 篁�    �  �  @   �   @      �  :  �           std::plus<void> .?AU?$plus@X@std@@ 篁�
 �        �  �   	@  z  {     �       	   |  <            	     N               Y  �   	�  d       �       	   �  �           
 �    
 �    �   �              std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@@std@@ �
 �    	   �  �            	  �  �    &      � 
 �    _Target 蝰 �  ~_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > 篁��  __vecDelDtor 篁衿  �           std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionRing@@V?$allocator@UReGIR_OnionRing@@@std@@@std@@@std@@ �    �  �  �  a   �     �   	   �  m               �  �   	|  c  �     �       	   �  �           �   �              std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ �
 �    	   �  �            	  �  �    &      � 
 w    _Target 蝰 �  ~_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > 篁��  __vecDelDtor 篁褶  �           std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > .?AU?$_Tidy_guard@V?$vector@UReGIR_OnionLayerGroup@@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@@std@@ �    |  |  |  �   |     �   	   y  �           
 �    
 �    
    �         �   �  #     �    a  �   �     �      �  �   �    �      �  �   |     �   u     �   @     �      ~  ~        �   A     �  
 A   ,      �  �        �  
 �    
 �  ,  
    @   @    �  
 �  ,      �  �         �  
    �   @    �      @  @     �         �  
    �   �    �      �  ~  
 �   蝰
 �    	@   �  �    �          ~  �        �      �  �   @     �  �   �              std::_Default_allocator_traits<std::allocator<float> > .?AU?$_Default_allocator_traits@V?$allocator@M@std@@@std@@     �  �  �   	   �         �          �  �  @  �   @     �      �  �        �      a  Y  �   	   �         �      
    �   �    �      Y  Y  �  a   �     �  
 �  �   �    d  
    �   �    �   �     �  
    w   �    �  �   �              std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �    �  a  
 �    	   �              
 �    
 �   蝰
   ,  
       	   �             	   �                        
 �  ,   	
  �              	   �              	�  �               	  �      &        �  pointer  	  _Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> >    operator= 蝰   ~_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > 篁� 
  _Release 篁�
 �    _First 篁�
 �   _Last 
 a   _Al 蝰  __vecDelDtor 篁瘼 
 6           std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionRing@@@std@@@std@@ �
    �   	   �                   �              �  �  �   	   �               
    |   |          �  �  |  �   |       
 |  �   |    �  
       |       |     �  
    �   |       �   �              std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �    |  �  
 "    	   "  $     #      
 "    
 "   蝰
 '  ,  
    (   	   "  $    )       	   "  $    #          *     +  
 "  ,   	-  "  $     )       	   "  $            	|  "  $             	  "  $    &        |  pointer  ,  _Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > 蝰 .  operator= 蝰 /  ~_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > � 0  _Release 篁�
 |    _First 篁�
 |   _Last 
 �   _Al 蝰1  __vecDelDtor 篁癞 
 62           std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > .?AV?$_Uninitialized_backout_al@V?$allocator@UReGIR_OnionLayerGroup@@@std@@@std@@ �
       	   "  $     4      
 �    
 a     	#   �        �      
 �          �   #      �  
 L  ,  
    <   @     =  
    �   A     ?      �  �  �   @     A      @  @  �         C  J   �              std::pair<float *,float *> .?AU?$pair@PEAMPEAM@std@@ �
 E    
 E  �  
    G  
 E    	   E  I   
 H      
 E  ,  
 E   蝰
 L  ,  
    M   	K  E  I     N      
    K   	   E  I     P      �   @  first_type �  @  second_type  J  pair<float *,float *> 蝰 O  operator= 蝰 Q  swap 篁�
 @    first 
 @   second 篁馢  6R           std::pair<float *,float *> .?AU?$pair@PEAMPEAM@std@@ � E    C   @    �  
    ~   @    V  
 �  ,  
    X   @    Y   @     M  �   �              std::_Uninitialized_backout_al<std::allocator<float> > .?AV?$_Uninitialized_backout_al@V?$allocator@M@std@@@std@@     @  �  
 \    	   \  ^     ]      
 \    
 \   蝰
 a  ,  
    b   	   \  ^    c       	   \  ^    ]          d     e  
 \  ,   	g  \  ^     c       	   \  ^            	@  \  ^             	  \  ^    &      �   @  pointer  f  _Uninitialized_backout_al<std::allocator<float> > 蝰 h  operator= 蝰 i  ~_Uninitialized_backout_al<std::allocator<float> > � j  _Release 篁�
 @    _First 篁�
 @   _Last 
 �   _Al 蝰k  __vecDelDtor 篁駟 
 6l           std::_Uninitialized_backout_al<std::allocator<float> > .?AV?$_Uninitialized_backout_al@V?$allocator@M@std@@@std@@  	   \  ^     �       �    �   �       	   �       "      
 p    蝰
 r        a  Y  �   	   �         t       |    �   |    4   	   "  $     �          �  �     	   �         y      
 �    
 A   �  
 �   蝰
 }    	0   �  ~    �          @        �  �         �      @  @  @  �         �   @     �      �  �   	   E  I    �       	   E  I   
 �       @    �      �  �  �   	   �         �          a  Y  !   	   �         �          Y  �   �    �      �  �  �   	   �         �          �     |    �      @  @  @  �  �         �      �  �   @    �      Y  !   �    �      �  �   |    �   �    �   �    $  
    a   p    �   w    �   �    �  
    �   �    �   �           �  �  M   	@  �         �          �  �   	@  �         �          �     �      �  �  �   	   �         �       	|  �        �      �  |  allocator_type �  @   value_type �  @  pointer   �  const_pointer 蝰    void_pointer 篁�  M  const_void_pointer �  #   size_type 蝰     difference_type   �  propagate_on_container_copy_assignment �  l  propagate_on_container_move_assignment �  �  propagate_on_container_swap   l  is_always_equal  �  allocate 篁� �  deallocate � 8  max_size 篁� �  select_on_container_copy_construction 蝰�  �           std::_Default_allocator_traits<std::allocator<float> > .?AU?$_Default_allocator_traits@V?$allocator@M@std@@@std@@  �    V  
    �   �    �  
    �       �  
    �   |    �   !       �       �    4       4  
    �   �    �  Z   �              std::bidirectional_iterator_tag .?AUbidirectional_iterator_tag@std@@ 馧   �              std::forward_iterator_tag .?AUforward_iterator_tag@std@@ 馢   �              std::input_iterator_tag .?AUinput_iterator_tag@std@@ 馢    �           std::input_iterator_tag .?AUinput_iterator_tag@std@@ �   �    蝰N   �           std::forward_iterator_tag .?AUforward_iterator_tag@std@@ �   �    蝰Z   �           std::bidirectional_iterator_tag .?AUbidirectional_iterator_tag@std@@ �   �    蝰Z   �           std::random_access_iterator_tag .?AUrandom_access_iterator_tag@std@@ �   �    蝰V   �           std::contiguous_iterator_tag .?AUcontiguous_iterator_tag@std@@ 篁�   �    蝰v   �           std::allocator_traits<std::allocator<float> > .?AU?$allocator_traits@V?$allocator@M@std@@@std@@ 蝰    �  �   �    �  
 u   ,      u   �  �  �         �         u          �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              67 丵 Fh 8� ? � 硯 �    最 � �  嘢 R	 果  
  np L f3 4� 鎺  
� M] �+   磇 � 谑 ;� 冺 � 鸦 ]  � 怹 >�  L� =� � )c {� k6 辩  < 櫺 z�  %D  �
  �0 牏  �� 睇 d
 忧 (� (� d 3 r� 匤  { �
 门  �    =�  刽  � 蟎 悶 E� s 蠘  華 鷹 窹 羝  � b7 O C    7  � 灣  撾  K2 �   hj 	� &� c� �9 绮 C� �  � 瑮 �5 \+  艉 M GT .� � 輝 �   �
  拌 T? 祗 誎  I~ 臃  茚 薰 @O Se 9M 屴 � i	  m^ d� uW  l  竼 +b 対  ' t @ � 嚄 M]  芤 )� "  � -^  ュ  踲  =� 瞌 漱  + "R � Y� Bk  3� S� �=  雦 匝 璈  |h 賢  衡 俎 	� 悂  讌 [.  �  贇 鬅 嵌 处 � �
 ]� � 瞑  楹 摲 芨 �   駌 -� � X	  舢 j� 娺 Z2 堨 u+ 昗 �5 i  -  OL '� �! 十   �   n  'b �= 〞 �p <  ]� ' � n0 M � 庛 嘥 � 蹍 uf �"  � O\ v�  爐  箖 v  鹲  醸 粎 B� 圂  U< 钖 瀦 kt i� `� �  "� � 鄖 煓 :�  | ∈ 衔 隆 鋀  �  4I w� 6 髍  r�  v;  驹  �   � 獳 +�  X 偩  蔜  徐 
g  酧  魑  炷  啺 '� � �> H�  Ta 1% 蚝 份 f� ." 慰  3>  .� 铠 i� 槩 沱 . A4  _;  讠 ]?  H| )( Vs  }  `� [B d�  Qa  R� =� Lz 犃  吱 [� � 锰 (6 :� � �% 唍  � 瘜 	 寂 訮 #� �> > W 罇 t� 銴 深 �t U� 縢  L  亏 鱦 � � 'E (. 沈 � ⊥ 弜 ﹁ \� �  氃  k	 � 嗚 
� 柛 q� � 逅 q? �> 堤 栉 泿   ⒌  � 龈 	j � 託  �%  ,� ∕ h t� �0 温 匦 椈 �  Y� 讯  錙 �
 � 逻 � rI 閭 �  D 檃 梾 濈  秚 ]� E0 )}  �  ]� 沨 p� 嵓 � k  RT |�  蜡 鈑 � s� ! �  �) �.  x� 燡 褗 u� Wn 悃 d, 炲  磻 _ " 凕  蝓 炔 OT  �# �� m� +  � �  ？ #� �  j� 殌  "� 珈 EL  淜  餖 Y: Q	  W�   �,  
 K� R 巓 玺 吵 孂 8� 嬅   番 诙 F� C� r 鐀 y F r� �1  该 比 訨 q� ｈ 臎 V�  � � &� � ;s  :: wK `�  虺 塽 � !�  p  �  �  叞 
' / [k 謿  �
 k�  Xq a� 槫 8�  m�   +� }�  DG 頽  v[ � �% =  l� |� bl 訃 r� 踺 nf �: C8  夷 q�  Qj  ${ x�  � 凒 � p�  諾 �/  � 赴 � M 亃  �6 � 瀳 Ⅺ T� M ,  堍 � m� FP 讜 袔 椃  9�  w�  �: �  �  �  槔  @
 u� 藍 芡 z� �$ �  `I @� �0 顕 H 慨 ne �" � �  濣  �  i� 嶖 L� 
'  田  e �  Y� 輴  殪  _�  q  =� � 欽 R   3� 沔 g�  憳  綘 
� F %� ∠ ;� 道 Ｑ  鞅  闂 � 潒  羺 �	 �; Y`  � 嬔 3� y  $ Nz d> 釕  {  XT w� 教 	� Q3 Mo �  }T  翥 � '�  梈 �'  -b  蝨 �/  浭 �  ']  � Cr a�  铗  烸  (x 斞  6�  � kf a 狄 ?� ━  雮 鄬 }� z=  堏  F
 �  W� 晭  ]
  詯  /� 冄  嘑  荭  捯  镚 荴  :�  ,, r x�  �  �8 徯 8Y �# �$ � 陒 议 2� es 艙 �: /� �  � �4 匁  �  m rO � ?
 掼  � e,  � 伴  ｂ  褵 h~  s
 � �  鐴 太 Km  � a �&  Y  V�  @�  x � 垼 �4 �( P 7� ?� a 苊 �* 夥 苳 +�  ' 2� �  f� b/ ^� C �3 熘 �,  终 n� 脊  粔 � %  �) 從 o � 罰 5b p� 浕 J� 餆 � 抔 D� 蠡 �"   U [� 鰖 >� [�  R� 膦 �
 鯖 v�  頺  栤   鱐 \�  [W 纳 鸢 冰 �1 �( Z� 鈆 (p " 皹 �  鍟 ~t �, ? = }�  jN  $� 畗 � � ^
  穣  s�  1� 嶁  W�  � 捔  盼 羷  �  ]� 5� ]� ~� € I 囈 m_ M p�  k?  �  稡 ee 
  檮  )�  AC V�  T� � 沽 賠 靆 %q �� 潄 %� �'  右 �  餙 殞 i�  >Z � 阦 E� � j� k  e� �/ 簛 B 芖 b� 箐 �  `8 醡  r�  滆  嘚  蕭 r	  `  >� �  6 � 蚔 4� }:  �9  ! �5 k 莮 穃 - �" *� } 嗥 皮 `D w� m� 悺  睍 9�  j? 札 彗 �  宑 縱 g YF 價  �< 摐 d J� Q� �$  >? �  "� 羕 � Kf � X� 8a 鱀 �;  搽  蜎 *L  A�   颼 鎻 �& 2j 頊 	� "� 瘗 �
 f� �  y
  *� 鲿  C�  � 褛  4>  6    ��  s� 夒  _ 慌 R� )_  i�  �$ 壇 � o�  遧 詚 � � �.  uP � 漮 � 嵸 � (. �
 �: D� (� ~v  3C 靌 苪 t� 瘧  � X 傩  1� � |� 漑  � '| � ┑ �  П �4 忧 儧 dm    $  K   t�     �  4  <    I               	   
         
                                                 !   "   #   $   %   &   )   *   1   2   3   4   5   6   7   8   9   :      +   ,   -   .   /   0   >   ?   @   A   D      E   F   '   (   ;      B      趡 瑳 欥 ER }� 儚 �1 遯  e�  �+ �( � � 0�  卣 w| C� }� 2� b�  )~ � 娇 _O .� >m 孛 Qj  嗧 /� � 2a T 冑 � 復  笽 涻 %
  W� aE  6 u] � n� 诓 鱗  盃  瀋 o 碕   鳯  �  	7  4 |z �2 �3 kQ  )�  �)  礰   � i� h  1� �< � H� � ~@ _  鈘 %� 鞦  �2  �K  北  ^� 肛 瀘 岃  + 蚅 秡 鏽 雱  �� 慢 D� + S� ?� 膒 ╜ 釙 灟 �$ 
B  5� 4; 循 蹨 嬖 觅 E  ~� 仢 A� 嵊 目  o� o.  � み he  J) �  �  康 xa �C _� � 0� 0D 3� o9 镅 谼  沅 � G�  N< .�  詆 �
 � ea 柪 m�  f� 划   w�  *� r� 橾 ?� 糬  .d  0�  �  sF $ � 嚉 L�  �  惨 >� 聸  1y $� + CA ]�  豣  衡 糛 @� K� @: 0| g� p( 暷 堙 �'  �"  
 楰 葲 b� 僎 魠 T` � ,� < ^ e)  /� �r �8 �  r [� ] 肟  倪 妎  8�  萮 』 89  z0 "� # n 歞  "� � 纣 祣  3� V� H�  魨 U: Bl 矝 =<  �  �  讇  浸 蕸 廲 T ) 锱 鍛 �  鞾 � 9h  {n 峇 ?<  aG 続 旳  i� 药  � � 恑 pr �� 塥 禆 媻  �9 I5 偛 -� 獊 �Z  � 袖 Ys  ℅ �- ┺ 籂 I � 谒  	�  Ⅷ  ,� ︳ by  憴 j� 矑  攒 � 臍 `� ↗ 西 蛴  |�  b� d� �;  u� 	 kN � 饌 甏  �" 欦 P� 棈 黆  瀗 河  �% 訅 7� 繒 � 摚 ?� yV  � �$ 惀  醮 $7 L� � �  � � � Wl ◇ 荔 D�  )�  鮕    i$  撱 7 丼  [� 孤 @�  帓 D� > 擽 $1  鯷 �  �4 l 哕  � 补 _G e� Q� 8� 屏 z�  � 怌 �$ v 
� �6 蓜 Y2  *� \� 廓 g� 歮  $R P8 �g  0� f
  �5 褡  d& Y�  佚 瓻    d  喡 9�  +�  � y\ c �!  '2 水 P; !r V7  � � �+ J  氡 Q :�  / j  揙 噯  X  �) � r� 旆 <2 舦 <�  老 +� Zj 2c 蹏  {� � 瀌 �  ┇ I� 伛 蝰 蟛 �( {� n� �   弅   � SX 伒  -C � 擲 � � �2 儸 "� q� 坪 gS 岳 逳 嶚 恰  鲐 婧  �  lD } 佛 醏 �8  "q  ]� 磵  聃 勵 殊 A�  ζ � *4  � M Eg 藵 垇 ◆ 差 誴  j� ヨ 蜾 辒 熃 X�  5 k� ` b�   � 菧 峫  j� k � �:  Z  p� 駶 -a i2 �# `� #� Wc Ⅰ w� � (� �  泉 �  姗 佐 擄 峒  
� 勐 Z 騮 e! 鷻 � 浡 跿  %j  � � d> 齟 �  齄  驜 � F% &=  �
 | 瘧 9  �  晬 vG �5  �  $ �� ︺ 蔟  婪 P� 榖 �4 8S @x �+   Cg � E�  vE 啋 柹  T UA �4 諵  �  m� , 3U  翥 鞚 u� l\ b� N�  �3  5 i< 瓜 ]� ほ *  秊 G� �6  )$ Ｆ 籨   I� 硚 鑫 膊 咵 浜 )� f� 6� ;'  � 確 K
 愷  桴 m r@  Yd  �  冺 ?� '�  徐 9� _�  �7 w� 矝 t [� 5�  U� 垆 k� /�  ~�  鳙 54 #� 5w o[ 郌        �  H   �  @  �  @`  �  �  �  �  h  �  
  $�  �  D  �  �  @  @ �  ` L  � �  \� 2  � �  � �    Y  �  �  DE �  $` �  �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     <     K   t�     �     �;       <            	   
         
                                                 !   "   #   $   %   &   )   *   1   2   3   4   5   6   7   8   9   :      +   ,   -   .   /   0   >   ?   @   A      '   (   ;      B      x 蝰
 u    spatialResamplingInputBufferIndex 
 u    spatialResamplingOutputBufferIndex 篁�
 u    finalShadingInputBufferIndex �
 u    pad1 �
 u    pad2 馢               ReSTIRGI_BufferIndices .?AUReSTIRGI_BufferIndices@@ 蝰*    Off 蝰  Basic   Raytraced ^   u     ResTIRGI_TemporalBiasCorrectionMode .?AW4ResTIRGI_TemporalBiasCorrectionMode@@ �
     f   �              ReSTIRGI_TemporalResamplingParameters .?AUReSTIRGI_TemporalResamplingParameters@@ b
 @     depthThreshold 篁�
 @    normalThreshold 蝰
 u    enablePermutationSampling 
 u    maxHistoryLength �
 u    maxReservoirAge 蝰
 u    enableBoilingFilter 蝰
 @    boilingFilterStrength 
 u    enableFallbackSampling 篁�
     temporalBiasCorrectionMode 篁�
 u   $ uniformRandomNumber 蝰
 u   ( pad2 �
 u   , pad3 駀             0 ReSTIRGI_TemporalResamplingParameters .?AUReSTIRGI_TemporalResamplingParameters@@ ^   u     ResTIRGI_SpatialBiasCorrectionMode .?AW4ResTIRGI_SpatialBiasCorrectionMode@@ 篁�
 	    f   �              ReSTIRGI_SpatialResamplingParameters .?AUReSTIRGI_SpatialResamplingParameters@@ 蝰� 
 @     spatialDepthThreshold 
 @    spatialNormalThreshold 篁�
 u    numSpatialSamples 
 @    spatialSamplingRadius 
 	   spatialBiasCorrectionMode 
 u    pad1 �
 u    pad2 �
 u    pad3 駀               ReSTIRGI_SpatialResamplingParameters .?AUReSTIRGI_SpatialResamplingParameters@@ 蝰Z   �              ReSTIRGI_FinalShadingParameters .?AUReSTIRGI_FinalShadingParameters@@ ^ 
 u     enableFinalVisibility 
 u    enableFinalMIS 篁�
 u    pad1 �
 u    pad2 馴              ReSTIRGI_FinalShadingParameters .?AUReSTIRGI_FinalShadingParameters@@ J   �              rtxdi::ReSTIRGIContext .?AVReSTIRGIContext@rtxdi@@ 篁�
    Z   �              rtxdi::ReSTIRGIStaticParameters .?AUReSTIRGIStaticParameters@rtxdi@@ �
    蝰
   ,  
       	                    
    蝰
     	                	u                 Z   �              RTXDI_ReservoirBufferParameters .?AURTXDI_ReservoirBufferParameters@@  	               f    None �  Temporal �  Spatial 蝰  TemporalAndSpatial 篁�  FusedSpatiotemporal 蝰R   u     rtxdi::ReSTIRGI_ResamplingMode .?AW4ReSTIRGI_ResamplingMode@rtxdi@@  	                  	                 	                	                	               
    u    	          &      
        	          (      
    蝰
 *  ,  
    +   	          ,      
    蝰
 .  ,  
    /   	          0      
    蝰
 2  ,  
    3   	          4       	                 �   ReSTIRGIContext    GetStaticParams    GetFrameIndex 蝰   GetReservoirBufferParameters 篁� !  GetResamplingMode 蝰 "  GetBufferIndices 篁� #  GetTemporalResamplingParameters  $  GetSpatialResamplingParameters � %  GetFinalShadingParameters 蝰 '  SetFrameIndex 蝰 )  SetResamplingMode 蝰 -  SetTemporalResamplingParameters  1  SetSpatialResamplingParameters � 5  SetFinalShadingParameters 蝰 u   numReservoirBuffers 
     m_staticParams 篁�
 u    m_frameIndex �
    m_reservoirBufferParams 蝰
      m_resamplingMode �
    $ m_bufferIndices 蝰
   D m_temporalResamplingParams 篁�
   t m_spatialResamplingParams 
   � m_finalShadingParams � 6  UpdateBufferIndices J  7          � rtxdi::ReSTIRGIContext .?AVReSTIRGIContext@rtxdi@@ 篁� 	                
     
 :    &    Off 蝰  Black   White F   u   <  rtxdi::Checkerbo �    _Release ;F撕;�"篁�     G  fma 侹�Q幟'     E  fma Yw舉玺愎 �  P  {ctor} 怆S攱N甓� �  V  {ctor} 愐裼*壯 �  b  {ctor} W朅めe覰�. �  �  ComputeGridLightSlotCount ~撜cf粕
蝰. �  �  AllocateRISBufferSegment 畘[粀s?篁�" �  �  InitializeOnion :L5猧賞O*   �  SphericalToCartesian 揺b!齴鶤篁�   �  Distance o<茰&�#篁�* �  �  ComputeOnionJitterCurve ��9⑼榣6 �  �  GetReGIRGridCalculatedParameters �4╘�.后蝰6 �  �  GetReGIROnionCalculatedParameters �:"厪�蝰 �  k  {ctor} 揭N:�3�& �  �  GetReGIRCellOffset 鮦 傫* �  �  GetReGIRLightSlotCount 凳魲幗蕚�. �  �  GetReGIRStaticParameters 69J辿p篁�* �  �  SetDynamicParameters r桋[賣t阁蝰. �  �  IsLocalLightPowerRISEnable 苍爋h{� z  �  operator[] 菜zV訙� z  �  size 4В飅嫵矿蝰 z  �  empty ��+�+┓蝰 z  �  end �I'溠Z �    {ctor} 峊獽�/�N� z  �  begin 褥溅奔暊蝰 z  �  push_back 袢O8�1蝰 z  �  {dtor} ME7c濇n� z  �  {ctor} 秦O6�7P� d  O  operator[] n�迸鏥桉 d  M  size 萸豑�儵篁� d  %  push_back ,鍬i眙_衮� d    {ctor} yX祆尠� d  ^  {ctor} 发�	C�J� c  �  size 嗻`rCソ殷蝰" c  �  _Unchecked_end 瑰銸�1紧�& c  �  _Unchecked_begin 歬y0�#f趔蝰 c  �  push_back u枓万� c  �  {ctor} |p仝㏎D像 c  Z  {ctor} 絽A蔼弶� �  �  {ctor} ��>�薪� z  �  _Tidy o��矰� d  `  _Getal 椏A痨w��: �  �  select_on_container_copy_construction `R:/]`� c  �  _Getal 怛T`�燆�: �     select_on_container_copy_construction �桯肛X)蝰 z  �  _Getal EA>辌D�� |  ?  deallocate �,\R�
荫 g  y  _Get_first 坤zh�
� �  �  _Get_first �4G/j铳隈 �  G  _Get_first 捤`!p^�" �  M  _Common_lerp 峜縙箤1篁�" �  O  _Common_lerp e躏$%称篁�" �  O  _Common_lerp O礓Y韭捏蝰 �  r  min �?R薧a侱 �  r  max 4犊緲� �  �  max >膖}H]撎 �  �  sort w傾娊蝰 �  %  accumulate RxW:�鸟 �  c  addressof W冑�[醹蝰* z  d  _Emplace_one_at_back  �X;N篁� �  f  {ctor} 
醮�<c兩�* d  n  _Emplace_one_at_back �b銆股G祗蝰 g  s  {ctor} 隱埥b�" d  y  _Construct_n n6&Q鷣�篁� g  |  {ctor} ru炂箋�)�* c  �  _Emplace_one_at_back 加燍�皃篁� �  �  {ctor} E懎 鶦像" c  �  _Construct_n T�0型b碿篁� �  �  {ctor} 埙�窊'嚇�" �  �  _Destroy_range }
�5�詁� �  �  {dtor} 	槦r苑� �  �  {dtor} � ��   P  {ctor} 
Nw?滛 |  =  {ctor} 雂鳐oo嬠� �  �  {ctor} ┣儽Bf铷�" d  6  _Buy_nonzero �(8w蟂`凅蝰 �  n  {ctor} 蠨堮�' �  �  {ctor} �碽EU�" c  �  _Buy_nonzero �<闂赝3蝰 y  �  {ctor} gM轛哺恶 d  ]  _Xlength XZ崘禅4篁� d  9  _Buy_raw *銟u0>篁� d  M  max_size Z蘛p鸷G篁� c  �  _Xlength 惜$0{隗蝰 c  �  _Buy_raw 邸踨,A堙篁� c  �  max_size MC殥FB|篁� �  �  max_size 冔蜣戽^弩蝰 �  �  max_size 
23棹?�S篁� �  �  _Is_finite 猓业�9"�" �  �  _Float_abs_bits �x硯侳�& �  M  _Linear_for_lerp "�
摒篁� �  �  _Is_nan 6W鶬�"  �  �  _Is_finite 猲*殒h狇" �  �  _Float_abs_bits W冹桪q9=& �  O  _Linear_for_lerp nV&#7廩篁� �  �  _Is_nan a媄寋�+� �  �  _Is_finite 炦s�煈a�" �  �  _Float_abs_bits 鵣}(^p軒& �  O  _Linear_for_lerp m8!翇ù凅蝰 �  �  _Is_nan ◣>H�%5� �  �  sort 4 �兲豑篁�" �  �  _Get_unwrapped bVV戛咇 �    _Unwrapped C-藧rL� �  �  _Pass_fn 搆z穚刍篁� �  �  accumulate 磓D#sVe� �  �  forward  sO仔鮓6 z  d  _Emplace_back_with_unused_capacity 墭鼹U=�& z  �  _Emplace_reallocate 9P胤遉 � �  �  forward �	媅�(+(6 d  n  _Emplace_back_with_unused_capacity wv�m	k�& d  �  _Emplace_reallocate �+鑓� �  �  forward 椟�:S洀 �  �  forward 桯煦櫳     std::ranges " �  �  _Unwrap_iter .<)�
钎c篁�" �  �  _Unwrap_sent �睛D戵蝰& �  �  _Uninitialized_copy 賃燪5溴 �  �  _To_address :Mz黽
S� �  �  forward �|

i�6 c  �  _Emplace_back_with_unused_capacity D腺簷�& c  �  _Emplace_reallocate 胸h绞� �  �  forward }5O,闈 �  �  forward gn募
棝" �    _Unwrap_iter Y蚂Gl]篁�" �    _Unwrap_sent 彩桀�漬篁�& �  �  _Uninitialized_copy 捰椴5� �  !  _To_address 頖A+踢竤. �  �  _Allocate_at_least_helper �鎐昍蝰 �  �  min Zv珵�	�. �  �  _Allocate_at_least_helper 蒧燊偁]蝰 "  0  _Release a笀/$矊|篁� "  /  {dtor} 閃+y崣瘃 "  +  {ctor} �i*聫 �  
  _Release �犱N篁� �    {dtor} ┗攧嚶@� �    {ctor} 乗d#皈T唏" z  �  _Orphan_range H	�;ヲ� z  �  _Xlength Z鍠椶s5朋蝰" z  �  _Change_array o馢楇Y蝰& z  �  _Calculate_growth 漪]鞢蝰 z  �  max_size `癶�%Y婠篁�" d  _  _Orphan_range t檫�畃蝰" d  [  _Change_array 4�B*唑�& d  X  _Calculate_growth ENZ� �
蝰 �  q  allocate l]3讚庴:篁�" c  �  _Orphan_range ke� 蝰" c  �  _Change_array H氝薬妠R蝰& c  �  _Calculate_growth kA�v:t蝰 y  �  allocate �5b礃hu篁� z  �  _Getal 瘴�:拖>!� z  �  capacity A菫D溶蘇篁� �  �  �   �  8  max_size !*/T�篁� d  M  capacity 野;H咼篁� c  �  capacity 崞`�2An篁� �  E  _Get_first t娢戋�<� �  �  _Bit_cast 逰"&b碡咈� �  �  _Float_abs B籯fq� �  �  swap ゃ�*N�篁� �  �  _Bit_cast �楡羆m蝰 �  �  _Float_abs T,g妒I婑 �  �  swap ▊ぅ鸆Q篁� �  �  _Bit_cast 婐鲳1鲬婒� �  �  _Float_abs %_��  � �  �  swap 瘂H�(GM篁�& �  �  _Adl_verify_range (v{X'蝰 �  �  _Unfancy 糄倯埩�9篁�" �  �  _Sort_unchecked ��︸鰧 �  �  move 崈郼蔆{戵蝰 �  �  operator() 3:�m甃w�& �  �  _Construct_in_place N�
�Ъ�. �  �  _Allocate_at_least_helper iWgI菿轵� �  �  construct 綂fr敋蝰" �  Z  _Get_unwrapped '\7�S�& �  �  _Uninitialized_move �2@磹:�& �  �  _Construct_in_place 補)q�/?� �  �  _Unfancy 叀澨/S~阵蝰 �  �  construct �6r>Yo旁蝰" �  �  _Get_unwrapped q朧�怄�& �  �  _Uninitialized_move 褡%E嘣� �  �  move 9�k�%瞒篁� �  �  to_address v^P嫇" �  �  _Copy_memmove �+E亨v{G蝰" �    _Emplace_back SC壨蝰& �    _Construct_in_place /�捫 �    _Unfancy UH)PY结篁� �    construct *堁�;�0蝰" �  !  _Get_unwrapped |�瀲肳& �    _Uninitialized_move �/��=憕 �  �  move �;P�Us篁� �    to_address 柈at槆e_�" �    _Copy_memmove ?睼&硩盺蝰" "  5  _Emplace_back 祕O鲀�3蝰" �  ;  _Get_size_of_n [ㄐ+7︸ �  :  _Allocate ㎜苾�$舔�" �  ;  _Get_size_of_n 前箩蹒� \  j  _Release 肇�#Y�&篁� \  i  {dtor} @硞_N� \  e  {ctor} 袍篼<Njm� |  @  allocate gj灦Z壌蝰 �  >  _Bit_cast 翥削wcx|蝰 �  @  _Bit_cast 肳2��-軯蝰 �  �  move 梡4nTT篁� �  @  _Bit_cast i��钋潋� �  �  move '屋YK輮"篁�. �  B  _Insertion_sort_unchecked 涣)搋伮候�* �  D  _Make_heap_unchecked 錴�?�
篁�* �  D  _Sort_heap_unchecked 跇
|嗶B 篁�: �  T  _Partition_by_median_guess_unchecked W釋斚j栿蝰 �  W  addressof ap鶛q�4蝰" �  U  construct_at 靸縤�".篁�" �  [  _Copy_memmove �4К諐狐蝰 �  Z  _To_address 眐a撰" \  n  _Emplace_back ;誶锭0`蝰 �  p  addressof z咽4�%买�" �  o  construct_at 4澁2昘蝰 �  �  move 粯�&兏胟篁�" �  q  _Emplace_back 列J>A-s2蝰 �  �  forward e溞侫+$� �  u  construct �(榫8蝰 �  w  addressof O$樳oU苠蝰" �  v  construct_at ǜ�	C-篁� �  �  move �- ck~Jb篁�" "  x  _Emplace_back 濷 龈仳� �  �  forward H(=t銊s �  z  construct Q誱溷g
瞅�6 �  :  _Allocate_manually_vector_aligned .m?N瓿9.蝰" �  ;  _Get_size_of_n 腹,_嘠,� �    operator() cT煐�. �  [  _Move_backward_unchecked F�咎偙篁�* �  �  _Pop_heap_hole_by_index �殖佃傿& �  D  _Pop_heap_unchecked k]块� �  �  _Prev_iter 鞜M代�* �  �  _Guess_median_unchecked �鲛e�  �  �  _Next_iter 筐K謯� E  �  {ctor} 得#&褆琋� �  �  to_address 鹃o圥�>� �  �  forward ︸ ap螫 �  �  construct 廧〩釺_膨� �  �  forward 蕽魍欛顝 �  �  construct l	飗�#蝰" �  �  construct_at y�,6塖�<篁� �  �  forward z.夆鐔勮 �  �  construct I�蝰" �  �  construct_at �#Rs填篁�* �  [  _Copy_backward_memmove �4MS�.��& �  �  _Push_heap_by_index 剑=�/�. �  �  _Pop_heap_hole_unchecked ]!N瑑｝篁�" �  �  _Med3_unchecked �2V�$<� �  �  forward =箚^F?" �  �  construct_at 鶕磩,J=篁�" �  �  construct_at ='�+3呦篁�" �  �  construct_at 樎\!抍峰篁�&     @  __std_smf_ellint_2 �%
�6漾骜     �  fminl 麆MHEET蝰     +  scalbnf �`QR�8�     �  exp2l k纎苛L�蝰&     E  __std_smf_ellint_3 餱�6Z厹恶     �  fdiml &KVＱ�篁�     �  logbf '��0z_旘�&     G  __std_smf_ellint_3f KF$j籡�     �  asinhl 奵�
蓲�     '  nextafterf [兺許�     �  erfcf *.鍹筭?趄�     '  fminf 趥#O漐囼�     '  fmaxf �VH肬蝰     �  exp2f 餙郐H愹�*     B  __std_smf_riemann_zeta 瓦$��&     I  __std_smf_legendre 鸠h潷7橊*     @  __std_smf_comp_ellint_3 ǜ�枽&     @  __std_smf_ellint_1 S鱢q�8,民     |  ilogbl '揹鹲G�     �  log2l 鋯�呎L买�&     A  __std_smf_ellint_2f r�t毇Dr     �  tgammaf #抽�.     C  __std_smf_comp_ellint_2f 垘<�-�?篁�&     K  __std_smf_laguerref o�艐&     C  __std_smf_expintf 敚Nk�昛蝰&     K  __std_smf_hermitef 夲朚猶f�     �  rintf  �5\Ihx蝰     �  scalbnl 朊昃�3H�*     A  __std_smf_cyl_bessel_if T 炫Q'�     �  rintl 6�搎畩简�*     I  __std_smf_sph_neumann 漼&d壪谩蝰*     K  __std_smf_sph_besself �混+�>潋�*     B  __std_smf_comp_ellint_2 ?崩�(x�     �  acoshl |d鞳Q伛     ,  llrintf 瞝賸R,bV*     @  __std_smf_cyl_bessel_j N祤�7�     1  nexttowardf 6�桷a.     ?  __std_smf_assoc_laguerref P�>A;歍�*     I  __std_smf_sph_bessel 蛐&
N�X篁�     �  expm1l 勾Y嵐�=�.     C  __std_smf_comp_ellint_1f 伤巬S矿蝰.     ?  __std_smf_assoc_legendref U"w#`雌蝰*     A  __std_smf_cyl_bessel_kf 艭q遝�&     K  __std_smf_legendref 贙��5$�"     @  __std_smf_beta 湂0麯狴8�*     =  __std_smf_sph_legendre :TxfD}O�*     A  __std_smf_cyl_bessel_jf 旘SL��     �  lgammaf >{��8Z*     B  __std_smf_comp_ellint_1 =h>�.     =  __std_smf_assoc_legendre �/T揙鼠蝰.     A  __std_smf_comp_ellint_3f )j葼m5"袤蝰&     I  __std_smf_laguerre )�8回=\�     �  nexttowardl /�<�6�     6  llrintl Pd�汤�&     G  __std_smf_hypot3f Dqj鲐剬蝰     �  erfl J郫<�篁�     �  fmaxl 谲q�饏蝰     �  acoshf �C癣血�     �  cbrtf 	娼膗偖y蝰     �  erfcl 昸�&寄梞蝰*     K  __std_smf_sph_neumannf W     �  atanhf 0i(��&�     �  tgammal 2=峅%萣�     �  log1pf 囶蔉G腇�*     C  __std_smf_riemann_zetaf (u�
�&     I  __std_smf_hermite 椗_#訲蝰 �  �  (    �  �  ,    �  �  .    �  �  0    �  �  3    �  �    " �  �  _Xlength_error 蠫Vn泑�     �  logbl 欌S-莓x蝰     9  remquol Jd紥gsq�     '  fdimf 
窵旯]蝰*     A  __std_smf_cyl_neumannf 釜@蕦#骺�     �  nearbyintl -K燵牿使�     �  atanhl )鞉o談=�*     @  __std_smf_cyl_bessel_k Jq��	韉�     '  remainderf v 樻a慍躐     z  ilogbf Ts藝|^薚�     6  llroundl 瓍P\*!蒹蝰     �  nextafterl `q/扈�.     =  __std_smf_assoc_laguerre �*鯺�'H篁�     �  log1pl 懞�0K� �     3  remquof \L便殔A�     -  lroundf 朤襳QA�;     /  modff #蠄圴蝰     �  remainderl 礛乲gk�     ;  scalblnl N[崬鄛篁�*     @  __std_smf_cyl_neumann z鮪[i
I|蝰     �  cbrtl k霃�z4w蝰&     E  __std_smf_hypot3 ~W嶩ko篁�"     A  __std_smf_betaf 葻�詆�     �  lgammal 蛠豦#�     ,  llroundf 鉿�1�繤篁�     7  lroundl F榸鈦*     @  __std_smf_cyl_bessel_i F]纈m�+�     �  expm1f %�<犵�     5  scalblnf B�$[
篁�     �  asinhf 瘌辽r礊神     �  nearbyintf  
霽N磢赳&     A  __std_smf_ellint_1f �/.发髒�     �  erff *@帋鈭p欝蝰&     B  __std_smf_expint 
蕔嗃+,蝰*     ?  __std_smf_sph_legendref 祄謡Q�  %  &  Z  1  /  蝰*   �  ComputePdfTextureSize �2J ǎ蝰.   �  FillNeighborOffsetBuffer 	\>2�o篁� �  �  max �懛#�&n     �  exp2 �;�糇_篁�6     D:\RTXPT\External\Rtxdi\Source\RtxdiUtils.cpp 蝰  %  &  �  1  /  蝰                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        u+ 	            爡   #  朶 (  L  � N  �=  樐 � 胊 |�  穨  � �-  8� � 1�  跟 �  裗 �  �  氈 	� 1�  疚 玒 N ��  td 藛 W� $	  L�  鯱 �   ^� Ga = L�  獔 _  拔  [) ( l� f 檺 �  �  � �  晉 �  �  �  �  AP �  �        籮 Y  pp U  �  �  宨 �  ;m �  �  �  �  �  �! �  嫚   	    G %  `  \  �  �  蔂 �  �  �  �  侩 �  �          $  &  
         "  L  N  X  Z  d  f  P  R  击 a  靔  e  �.  �  �  �  �  �  �  诛 �  �        来   
  #  $  `� J  叢  W  �  �  �  �  #8 �  �  �  �  �  �  �  	        !    
  '  _  g  J  詪 g  K �  � �  �  �  �      V  ]  �# a  I  �"  b  �  �  �  �  宭 �  �  �  �  �        "      !  L  R  `  z�  �  �  $$  R$  Y$  f$  �$  OJ  �$  �$  �$  �$  �$  �$  鞫   擫 尫 ｓ Y � s	 \ j�  k�  � z�  愽 J� 晫 7s 听  漌 9= �3 灼 �4 Vs 険 � 麇 &  _  3�  再   耫 蕦 得  炯 �% LZ d0 9- 3 i� �; 4 l� k+ � :O  _d ]G \  � �  p�  紓 鲬   =H 搄 � 愧 y� �- � 膚 抻 �4 鮹 氍  -6 � �3 雓 s� 鐎 璈 陓 y�  � 嶲  '`  � �  麹 垼 _ S� c   =  z	 袝 锞  顐 熭 b2  %  %  "%  (%  &%  � %   � 
%  坄 %    %  H"  %  荼 %  c�  %  6  %  栄 %  �? %  ぴ   %  j�  %  |� $%  ]� "%  �  '%  %%  镰 K%    I%  � 題 q  � H5   6 XU ,; � 6 ]J  D  	g  5| 嚔  柟  �# 糨 廑 8�  臺 5q  I� v  `� � � \t Q*  � %_ E�  h  忺 襱 J�   � �   *p 馮 鈎 ZZ  w�  ia    萯 g� /K  慀 銺 VH 鏈 鹜 櫣 r.    鷥 x v� � 蔞 籹  壌  T� R� |W h� K�  B�  銺 �  � � 案  � Eb 餚 旜 彜 �" [� P8 R�  %� �9  椾 � 匔   h�  絮 漋 +? 撑  gG F  襐 U  t� e  � 餡 氾 Tk > � �5 *� � e�  G  nM 洫 禼 p]  鵞  r� � 茶 眍  浝 攫 Z� 朹 $U {s 琙  M� 螭     Z� J  讯 4�  CC 摂 o 8}  �4  }, �#  Cw  � 擨 趣 f� w:  i� � 匭 韘 櫡 ;� V 籛 齧 P. (�  f  嵴 s�  漁 頨 赿 鹠 牖  	 禚  S  稇 �  囒 6]  H< s 仇 �; � 趭 袒  歹  Rg  櫂 #| G� 谅 m� I h%  N%  Q%  @ �� \%  Z%  ^%  觡 �)     x  cr ;�  � h%  � HP :z  �%  0 腇  �%  +� J$ �%  m� '�  �%  �%  茦 搔 憕 q� �? �%  ぶ �%  �%  %�  �%  �%  虙 �%  �%  �%  � 旼 f �%  � 圭 ^`  8} 傕 eb  �%  �%  r� ~J 娥 f� B4 �	 �%  訞 翂 商 J Z&  �  ;! L 蚐 巫 �%  �%  �%  蕊 �%  �%  %� g�  ; �:  �%  �%  淏 �%  =� 縰 6� � � � a, 佴  !� �%  釣 �%  釰 �%  �%  N �,  � �%  �%  b �%  鄠  �%  �/ �%  �2 @� �) qW  �%  翫 Y] �  傋  P8 �%  �%   K �%  騸   
&  &  E� &  姴  &  &  &  趬 翔 l. �  � 鶏   &  $&  '&  %&  Z� f�  &  !F 耿 DZ 9�  C 	&  &  鳰 炗 >�  j� 5� � ★ 鍗  &  稩 &  ) 躲 
� &  =N  -� &   &  &  "&  漿 { z� 0?  �?  *� X&  Z%  +� 
V z� 7�   fC @v  �? 峡 酑  �< 6  瑣  参 E  `� ⑻  逼 傂  2T  �2 c�  豋 r Y� _&  a&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  �&  '  '  '  '  ''  	'  '  '  '  !'  K'  M'  W'  Y'  c'  e'  O'  Q'  ['  ]'  g'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'  �'     
         #   %      Z   c   g   K   P   �  �   Em  �   !  
!  J!  R!  ^!  _!  �!  �!  �!  �!  "  "  橚  Y"  W"  X�  '� DI �
 褑 PY 冱 Ie Ｗ  � $f 猜  �' m�  l�  =t �  矎 妵 隍 妝 頎  (� 9� 樂   
*  z� �- 苞 q, 扤 C� 燴  #� 嬄  r# #U D�  I�  嬴 择 � �) 騳 h� U� .� 鍶  鷐 燻 #Q  /$ El �� ↗ 鵙 \ 湿  e� & � X  � <H �?  }� :� 錶 I` 39 笅 vY 纙 3 c 1� 銀 凔 ;! 虀 l`  �- 裠    O�  咱  ]� 鞛  H� �	 !e 綩 \�  wK  涑  � 7| 弟  �  m ~�  罓 G $� yR 3�  钖  �5  p� 糶 s� 襠 惱 ^  �. 痢  峇 翟  鮅 蕇  KI O� 窴  
k � � t� 軱 誼  � j� D�  僴 � 龃 � U� �   霁 踠 hr 冚 榉  � �>  	% w  頉 映 薕 f� 鈾 G1 �  d�  �  s ` �% 亵  
� 	  慺 o� k@ CU f   �  5�  iw  蕋 l� �-  �"  惦 粧 翥 q:  嚊 � 瘞 軝 颋 "�  i
  �0  �1 � 讐    �= `�  [ G� 睽 k6 Nk �
  竻 D  (� 鴑 UI }1  �( e 桽 �  5C  q  �% _ 瘗 
� �* z� M� 殣  @b )� 紤 動 u� 摻 ?b   泱 碆  忆 {  酺 臉  T� C\  SF  稢 徒 � 竧  嘠 沪 �  彧 �  釥  � +H '�  ,� 茕    揑 笂 x�  .�  榳  髯 斯  勮  顗  XJ B� 谶 � e� �/ A� 湡 NT  �  唾 [�  t� 	� d� 檃 蜀 e� 嬝 !� q= = b *  9 � 烼 {� 	�   �>  z� � 鄊  7 緺 撞 粕 乗 啷 @? 闀 z� m1 紦 3�  鎝  汛  ;n  碤 �*   睒  嬛 �  X L� 偧  讧 檎 *� �  TZ  � ! �  f� C 侬 Vn xc �5 黸 愝 �3 �"  �"  �"  �"  �"  �"  u� � 竲 @� 姾  /I  N� 狺 鼳 � (�  曂 4� 祳 �  4  f[ 缥 昀  ,�  袁  =�  .8 騼 C%  Y1 崺 坝  薛 溱 4< ╲ 狦  �' 'b  @}  �  >8 -9 艫 礍 搗        �  0   �   @  �  `    �  �  �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     �,   ReSTIRGI_BufferIndices ResTIRGI_TemporalBiasCorrectionMode ReSTIRGI_TemporalResamplingParameters ResTIRGI_SpatialBiasCorrectionMode ReSTIRGI_SpatialResamplingParameters ReSTIRGI_FinalShadingParameters rtxdi::ReSTIRGI_ResamplingMode rtxdi::ReSTIRGIContext rtxdi::CheckerboardMode rtxdi::ReSTIRGIStaticParameters RTXDI_ReservoirBufferParameters rtxdi::RISBufferSegmentAllocator _ldiv_t _lldiv_t std::byte std::hash<float> std::hash<double> std::hash<long double> std::hash<std::nullptr_t> std::exception __std_exception_data std::bad_exception std::bad_alloc std::bad_array_new_length std::exception_ptr _s__ThrowInfo std::nested_exception std::bad_variant_access __crt_locale_pointers _iobuf __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const> __vcrt_assert_va_start_is_not_reference<wchar_t const * const> __vcrt_assert_va_start_is_not_reference<wchar_t const *> __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *> __vcrt_assert_va_start_is_not_reference<char const * const> stat _stat64i32 _Mbstatet type_info __std_type_info_data std::bad_cast std::bad_typeid std::__non_rtti_object std::_Literal_zero std::partial_ordering std::weak_ordering std::strong_ordering std::_Unused_parameter std::_Distance_unknown std::ranges::_Not_quite_object std::ranges::_Not_quite_object::_Construct_tag std::ranges::_Advance_fn std::ranges::_Distance_fn std::ranges::_Next_fn std::ranges::_Prev_fn std::ranges::dangling std::ranges::_Copy_fn std::ranges::_Fill_n_fn std::ranges::_Mismatch_fn std::ranges::_Find_fn std::ranges::_Find_if_fn std::ranges::_Find_if_not_fn std::ranges::_Adjacent_find_fn std::ranges::_Search_fn std::ranges::_Max_element_fn std::ranges::_Max_fn std::ranges::_Min_element_fn std::ranges::_Min_fn std::tuple<> std::align_val_t std::_Asan_aligned_pointers std::_Container_base0 std::_Fake_allocator std::_Iterator_base0 std::_Container_proxy std::_Container_base12 std::_Lockit std::_Iterator_base12 std::_Fake_proxy_ptr_impl std::_Leave_proxy_unbound std::_Basic_container_proxy_ptr12 tm timespec _timespec64 std::memory_order std::atomic_flag std::_Atomic_storage<long,4> std::_Atomic_integral<long,4> std::_Atomic_integral_facade<long> std::atomic<long> std::ranges::_Uninitialized_copy_fn std::ranges::_Uninitialized_copy_n_fn std::ranges::_Uninitialized_move_fn std::ranges::_Uninitialized_move_n_fn std::ranges::_Uninitialized_fill_fn std::ranges::_Uninitialized_fill_n_fn std::ranges::_Construct_at_fn std::ranges::_Destroy_at_fn std::ranges::_Destroy_fn std::ranges::_Destroy_n_fn std::ranges::_Uninitialized_default_construct_fn std::ranges::_Uninitialized_default_construct_n_fn std::ranges::_Uninitialized_value_construct_fn std::ranges::_Uninitialized_value_construct_n_fn std::bad_weak_ptr std::_Ref_count_base std::pointer_safety std::_Shared_ptr_spin_lock std::pmr::memory_resource rtxdi::RISBufferSegmentParameters rtxdi::ReSTIRDIStaticParameters rtxdi::ImportanceSamplingContext std::unique_ptr<rtxdi::RISBufferSegmentAllocator,std::default_delete<rtxdi::RISBufferSegmentAllocator> > std::unique_ptr<rtxdi::ReSTIRDIContext,std::default_delete<rtxdi::ReSTIRDIContext> > std::unique_ptr<rtxdi::ReGIRContext,std::default_delete<rtxdi::ReGIRContext> > std::unique_ptr<rtxdi::ReSTIRGIContext,std::default_delete<rtxdi::ReSTIRGIContext> > RTXDI_LightBufferParameters RTXDI_RISBufferSegmentParameters rtxdi::ImportanceSamplingContext_StaticParameters rtxdi::ReGIRMode rtxdi::ReGIRStaticParameters rtxdi::ReSTIRDI_ResamplingMode rtxdi::ReSTIRDIContext rtxdi::ReGIRContext ReSTIRDI_LocalLightSamplingMode ReSTIRDI_InitialSamplingParameters rtxdi::LocalLightReGIRFallbackSamplingMode rtxdi::LocalLightReGIRPresamplingMode rtxdi::ReGIRDynamicParameters std::default_delete<rtxdi::ReSTIRGIContext> std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRGIContext>,rtxdi::ReSTIRGIContext *,1> std::default_delete<rtxdi::ReGIRContext> std::_Compressed_pair<std::default_delete<rtxdi::ReGIRContext>,rtxdi::ReGIRContext *,1> std::default_delete<rtxdi::ReSTIRDIContext> std::_Compressed_pair<std::default_delete<rtxdi::ReSTIRDIContext>,rtxt_is_reference<char const * const> $_TypeDescriptor$_extraBytes_31 std::ranges::_For_each_fn std::ranges::_For_each_n_fn std::ranges::_Count_fn std::ranges::_Count_if_fn std::ranges::_Equal_fn std::ranges::_Is_permutation_fn std::ranges::_All_of_fn std::ranges::_Any_of_fn std::ranges::_None_of_fn std::ranges::_Copy_n_fn std::ranges::_Copy_backward_fn std::ranges::_Copy_if_fn std::ranges::_Move_fn std::ranges::_Move_backward_fn std::ranges::_Partition_copy_fn std::ranges::_Is_partitioned_fn std::ranges::_Partition_point_fn std::ranges::_Search_n_fn std::ranges::_Find_end_fn std::ranges::_Find_first_of_fn std::ranges::_Swap_ranges_fn std::ranges::_Transform_fn std::ranges::_Replace_fn std::ranges::_Replace_if_fn std::ranges::_Replace_copy_fn std::ranges::_Replace_copy_if_fn std::ranges::_Fill_fn std::ranges::_Generate_fn std::ranges::_Generate_n_fn std::ranges::_Remove_fn std::ranges::_Remove_if_fn std::ranges::_Remove_copy_fn std::ranges::_Remove_copy_if_fn std::ranges::_Unique_fn std::ranges::_Unique_copy_fn std::ranges::_Reverse_fn std::ranges::_Reverse_copy_fn std::ranges::_Rotate_fn std::ranges::_Rotate_copy_fn std::ranges::_Sample_fn std::ranges::_Shuffle_fn std::ranges::_Partition_fn std::ranges::_Stable_partition_fn std::ranges::_Push_heap_fn std::ranges::_Pop_heap_fn std::ranges::_Make_heap_fn std::ranges::_Is_heap_fn std::ranges::_Is_heap_until_fn std::ranges::_Sort_heap_fn std::ranges::_Lower_bound_fn std::ranges::_Upper_bound_fn std::ranges::_Equal_range_fn std::ranges::_Binary_search_fn std::ranges::_Merge_fn std::ranges::_Inplace_merge_fn std::ranges::_Sort_fn std::ranges::_Stable_sort_fn std::ranges::_Partial_sort_fn std::ranges::_Partial_sort_copy_fn std::ranges::_Nth_element_fn std::ranges::_Includes_fn std::ranges::_Set_union_fn std::ranges::_Set_intersection_fn std::ranges::_Set_difference_fn std::ranges::_Set_symmetric_difference_fn std::ranges::_Minmax_element_fn std::ranges::_Minmax_fn std::ranges::_Next_permutation_fn std::ranges::_Prev_permutation_fn std::ranges::_Is_sorted_fn std::ranges::_Is_sorted_until_fn std::ranges::_Clamp_fn std::ranges::_Lexicographical_compare_fn std::vector<float,std::allocator<float> >::_Reallocation_policy std::vector<float,std::allocator<float> > std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<float> > > std::_Vector_iterator<std::_Vector_val<std::_Simple_types<float> > > std::allocator<float> std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1> std::_Vector_val<std::_Simple_types<float> > std::_One_then_variadic_args_t std::less<void> std::plus<void> std::_Tidy_guard<std::vector<ReGIR_OnionRing,std::allocator<ReGIR_OnionRing> > > std::_Tidy_guard<std::vector<ReGIR_OnionLayerGroup,std::allocator<ReGIR_OnionLayerGroup> > > std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionRing> > std::_Uninitialized_backout_al<std::allocator<ReGIR_OnionLayerGroup> > std::pair<float *,float *> std::_Uninitialized_backout_al<std::allocator<float> > std::_Default_allocator_traits<std::allocator<float> > std::input_iterator_tag std::forward_iterator_tag std::bidirectional_iterator_tag std::random_access_iterator_tag std::contiguous_iterator_tag std::allocator_traits<std::allocator<float> > �  W"  E  �   t"                  �     O#  
$            �   �
  �+  :    g#  �  �      �%      �)  x      U+  D  m   �%              �    �  �  �   X    "  0,  �  (      8"          �+      |
  �	  
  y
  +  �      3  
  �#  �              �           �  !  �(    �  G          .  �$  i  �&  �            �  e  �  �      �  7  �  �    �#      �	  �  q  �      �      z  �      �"      �	  �  �     �  �  H  v  �     �    <  �	  \  �    *          X      �  1  �  6!          7%  &  8  ^  �$                  �  N  �  s  �  <   �  �
  �  Z  �  �  �  q%  9  �  �  _
  w  �  b   4  ?  Q   �#  3&  �'      f  �(  +$  S      �  l  �     �  �  }  _  �                                       ,      P,  �      �
      �!  #  x	          "      �          o'  �"      U                  �      ^  	  C$  �*      �!      s  `$  R	  �&      �                  �"      J
  D  +  �
  �  �  7   t  �  4  �!          c     (  )          �  �  �  �'              �  �          n!  *      �              �  �  �      �      
  �  �  3#  �  x$  �  *     	  �  >  �$                          +  �  A)  �
  �      �
  �      .  �  �  �  �  4      �  p*      G(              �          �                  �  �      
    �  �!  �  G  %'      �  �           �   5  [  �  .	  �  �$        4  �
      �  P&      i  �  �  9  4  �  m,            �&  �  �  �"  �)      Z  �    �  �%      V%  �  %  �'  �&  �+              �      �                   �      I  _  k  N
  %    �  �  �(      n&                  ;          #  ]  �  D  '  h  �  }    L  q  Q  {  �#  W)  O'  p+      !  "  �)  [  �%  y          �  �
  U!  ;     
  n  �#  3  J    C  
  d  �%  L  �'  �  	  P                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   �18      �  袪   ��   ��     �  �  0         F     D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h 蝰      _         !         7    	     )    
     J         W   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h           8    +   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h � =  
      A       >     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h  G  
  \        rtxdi 蝰2   Q  GetDefaultReSTIRGIBufferIndices nx	埘�5�>   T  GetDefaultReSTIRGITemporalResamplingParams 蕦t\逡	�>   W  GetDefaultReSTIRGISpatialResamplingParams 黄$a� 厄�:   Z  GetDefaultReSTIRGIFinalShadingParams 9碦$d{	$篁�     {ctor} BA誜�"     GetStaticParams 骐k劐齭"     GetFrameIndex 嬐覦M*B傭�2     GetReservoirBufferParameters if-�?�=篁�&   !  GetResamplingMode OG僋o穆蝰&   "  GetBufferIndices 薲渠 痖s篁�2   #  GetTemporalResamplingParameters �麭8龞,w2   $  GetSpatialResamplingParameters 9Y℡暩栺.   %  GetFinalShadingParameters n籺�拭蝰"   '  SetFrameIndex 爕`幞琪蝰&   )  SetResamplingMode 龘躠
�"7蝰2   -  SetTemporalResamplingParameters 鐪{�<2   1  SetSpatialResamplingParameters 襕.曘�<�.   5  SetFinalShadingParameters x0�+*�(蝰&   6  UpdateBufferIndices *=|b亦-   k  JenkinsHash 9j�nV6   I  CalculateReservoirBufferParameters :帒wj徿勸B     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi 蝰N     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\bin\HostX64\x64\CL.exe 蝰�     -c -ID:\RTXPT\External\Rtxdi\include -Zi -nologo -W1 -WX -diagnostics:column -MP -O2 -Ob2 -D_MBCS -DWIN32 -D_WINDOWS -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DNDEBUG -DRTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\") -DCMAKE_INTDIR=\"Release\" 聱      -EHs -EHc -MT -GS -fp:precise -Zc:wchar_t -Zc:forScope -Zc:inline -GR -std:c++20 -external:W1 -Gd -TP -errorreport:queue -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include 聆      -ID:\1softwares\VS2022\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" �      -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -I"C:\Program 篁颃      Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\include 蝰�      -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" -external:I"C:\Program 聱      Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -external:I"C:\Program 蝰"    '  (  )  *  +  ,  -  > .   Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -X 蝰2     D:\RTXPT\External\Rtxdi\Source\ReSTIRGI.cpp R     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb   %  &  0  1  /  蝰V     D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h  t  3      l  n  {ctor} =]U-斛媋�" l  o  allocateSegment 麺J�9�* l  r  getTotalSizeInElements hD87x馞     D:\RTXPT\External\Rtxdi\Source\RISBufferSegmentAllocator.cpp 篁�  %  &  8  1  /  蝰R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h 蝰 �  :     �  :    F     D:\1soft�.1N馟h   骓騝�谽樣�1�Pj   /names                            躋3ternal\Rtxdi\include\Rtxdi\GI\ReSTIRGIParameters.h 蝰      _         !         7    	     )    
     J         W   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\GI\ReSTIRGI.h           8    +   :     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiUtils.h � =  
      A       >     D:\RTXPT\External\Rtxdi\include\Rtxdi\RtxdiParameters.h  G  
  \        rtxdi 蝰2   Q  GetDefaultReSTIRGIBufferIndices nx	埘�5�>   T  GetDefaultReSTIRGITemporalResamplingParams 蕦t\逡	�>   W  GetDefaultReSTIRGISpatialResamplingParams 黄$a� 厄�:   Z  GetDefaultReSTIRGIFinalShadingParams 9碦$d{	$篁�     {ctor} BA誜�"     GetStaticParams 骐k劐齭"     GetFrameIndex 嬐覦M*B傭�2     GetReservoirBufferParameters if-�?�=篁�&   !  GetResamplingMode OG僋o穆蝰&   "  GetBufferIndices 薲渠 痖s篁�2   #  GetTemporalResamplingParameters �麭8龞,w2   $  GetSpatialResamplingParameters 9Y℡暩栺.   %  GetFinalShadingParameters n籺�拭蝰"   '  SetFrameIndex 爕`幞琪蝰&   )  SetResamplingMode 龘躠
�"7蝰2   -  SetTemporalResamplingParameters 鐪{�<2   1  SetSpatialResamplingParameters 襕.曘�<�.   5  SetFinalShadingParameters x0�+*�(蝰&   6  UpdateBufferIndices *=|b亦-   k  JenkinsHash 9j�nV6   I  CalculateReservoirBufferParameters :帒wj徿勸B     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi 蝰N     D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\bin\HostX64\x64\CL.exe 蝰�     -c -ID:\RTXPT\External\Rtxdi\include -Zi -nologo -W1 -WX -diagnostics:column -MP -O2 -Ob2 -D_MBCS -DWIN32 -D_WINDOWS -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DNDEBUG -DRTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\") -DCMAKE_INTDIR=\"Release\" 聱      -EHs -EHc -MT -GS -fp:precise -Zc:wchar_t -Zc:forScope -Zc:inline -GR -std:c++20 -external:W1 -Gd -TP -errorreport:queue -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include 聆      -ID:\1softwares\VS2022\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" �      -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -I"C:\Program 篁颃      Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include -external:ID:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\atlmfc\include -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\include 蝰�      -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt" -external:ID:\1softwares\VS2022\VC\Auxiliary\VS\UnitTest\include -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" -external:I"C:\Program 聱      Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt" -external:I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\cppwinrt" -external:I"C:\Program 蝰"    '  (  )  *  +  ,  -  > .   Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" -X 蝰2     D:\RTXPT\External\Rtxdi\Source\ReSTIRGI.cpp R     D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\Release\Rtxdi.pdb   %  &  0  1  /  蝰V     D:\RTXPT\External\Rtxdi\include\Rtxdi\LightSampling\RISBufferSegmentAllocator.h  t  3      l  n  {ctor} =]U-斛媋�" l  o  allocateSegment 麺J�9�* l  r  getTotalSizeInElements hD87x馞     D:\RTXPT\External\Rtxdi\Source\RISBufferSegmentAllocator.cpp 篁�  %  &  8  1  /  蝰R     C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h 蝰 �  :     �  :    F     D:\1soft   $  K   t�     �  4  ,<    <   H            	   
         
                                                 !   "   #   $   %   &   )   *   1   2   3   4   5   6   7   8   9   :   G   +   ,   -   .   /   0   >   ?   @   A   D      E   F   '   (   ;      B   C                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               I     K   t�     �  4  �;    =   I            	   
         
                                                 !   "   #   $   %   &   )   *   1   2   3   4   5   6   7   8   9   :   H   +   ,   -   .   /   0   >   ?   @   A   D      E   F   '   (   ;      B   G                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               J                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               