{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/nvrhi/thirdparty/DirectX-Headers/Release/DirectX-Guids.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions"], "files": ["External/Donut/nvrhi/thirdparty/DirectX-Headers/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 45, "parent": 0}, {"command": 1, "file": 0, "line": 46, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 55, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP  /MP /W4 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++14 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 2, "isSystem": true, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "DirectX-Headers::@36095ebc80295b0f7532"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "DirectX-Guids::@36095ebc80295b0f7532", "name": "DirectX-Guids", "nameOnDisk": "DirectX-Guids.lib", "paths": {"build": "External/Donut/nvrhi/thirdparty/DirectX-Headers", "source": "External/Donut/nvrhi/thirdparty/DirectX-Headers"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/thirdparty/DirectX-Headers/src/dxguids.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}