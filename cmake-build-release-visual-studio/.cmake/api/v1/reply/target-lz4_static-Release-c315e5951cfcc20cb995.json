{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Omm/external/lz4/build/cmake/Release/lz4.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "target_compile_definitions", "include_directories"], "files": ["External/Omm/external/lz4/build/cmake/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 119, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 55, "parent": 2}, {"command": 2, "file": 0, "line": 143, "parent": 0}, {"command": 3, "file": 0, "line": 76, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG /Zi /Zi -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 3, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 4, "define": "XXH_NAMESPACE=LZ4_"}], "includes": [{"backtrace": 5, "path": "D:/RTXPT/External/Omm/external/lz4/build/cmake/../../lib"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "lz4_static::@056654a294b82f1b5e8a", "name": "lz4_static", "nameOnDisk": "lz4.lib", "paths": {"build": "External/Omm/external/lz4/build/cmake", "source": "External/Omm/external/lz4/build/cmake"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/lz4/lib/lz4.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/lz4/lib/lz4file.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/lz4/lib/lz4frame.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/lz4/lib/lz4hc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/lz4/lib/xxhash.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}