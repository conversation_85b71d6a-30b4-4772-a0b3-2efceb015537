{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/nvrhi/Release/nvrhi_d3d12.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["External/Donut/nvrhi/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 267, "parent": 0}, {"command": 1, "file": 0, "line": 350, "parent": 0}, {"command": 2, "file": 0, "line": 280, "parent": 0}, {"command": 3, "file": 0, "line": 285, "parent": 0}, {"command": 3, "file": 0, "line": 290, "parent": 0}, {"file": 1}, {"command": 4, "file": 1, "line": 55, "parent": 6}, {"command": 5, "file": 0, "line": 272, "parent": 0}, {"command": 2, "file": 0, "line": 283, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP  /MP /W4 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "NVRHI_D3D12_WITH_NVAPI=1"}, {"backtrace": 5, "define": "NVRHI_WITH_AFTERMATH=0"}, {"backtrace": 7, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 8, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 3, "isSystem": true, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include"}, {"backtrace": 9, "isSystem": true, "path": "D:/RTXPT/External/nvapi"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [2, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}], "dependencies": [{"backtrace": 3, "id": "DirectX-Headers::@36095ebc80295b0f7532"}, {"backtrace": 3, "id": "DirectX-Guids::@36095ebc80295b0f7532"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "D:/RTXPT/cmake-build-release-visual-studio/install"}}, "name": "nvrhi_d3d12", "nameOnDisk": "nvrhi_d3d12.lib", "paths": {"build": "External/Donut/nvrhi", "source": "External/Donut/nvrhi"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 3, 8]}, {"name": "Source Files", "sourceIndexes": [2, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}], "sources": [{"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/d3d12.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/src/common/dxgi-format.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/dxgi-format.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Donut/nvrhi/src/common/versioning.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-buffer.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-commandlist.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-compute.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-constants.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Donut/nvrhi/src/d3d12/d3d12-backend.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-descriptor-heap.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-device.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-graphics.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-meshlets.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-queries.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-raytracing.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-resource-bindings.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-shader.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-state-tracking.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-texture.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/d3d12/d3d12-upload.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}