{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Rtxdi/Release/Rtxdi.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "target_include_directories"], "files": ["External/Rtxdi/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 51, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 55, "parent": 2}, {"command": 2, "file": 0, "line": 52, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++20 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 3, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 4, "path": "D:/RTXPT/External/Rtxdi/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [34, 35, 36, 37, 38, 39]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "RTXDI SDK"}, "id": "Rtxdi::@62fca8016d331f72548b", "name": "Rtxdi", "nameOnDisk": "Rtxdi.lib", "paths": {"build": "External/Rtxdi", "source": "External/Rtxdi"}, "sourceGroups": [{"name": "Include\\Rtxdi", "sourceIndexes": [0, 1, 2, 3]}, {"name": "Include\\Rtxdi\\DI", "sourceIndexes": [4, 5, 6, 7, 8, 9, 10, 11, 12]}, {"name": "Include\\Rtxdi\\GI", "sourceIndexes": [13, 14, 15, 16, 17, 18, 19, 20]}, {"name": "Include\\Rtxdi\\LightSampling", "sourceIndexes": [21, 22, 23, 24, 25, 26]}, {"name": "Include\\Rtxdi\\ReGIR", "sourceIndexes": [27, 28, 29]}, {"name": "Include\\Rtxdi\\Utils", "sourceIndexes": [30, 31, 32, 33]}, {"name": "Source", "sourceIndexes": [34, 35, 36, 37, 38, 39]}], "sources": [{"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/ImportanceSamplingContext.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/RtxdiParameters.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/RtxdiTypes.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/RtxdiUtils.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/BoilingFilter.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/InitialSampling.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/PairwiseStreaming.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/Reservoir.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/ReSTIRDI.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/ReSTIRDIParameters.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/SpatialResampling.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/SpatioTemporalResampling.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/DI/TemporalResampling.hlsli", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/BoilingFilter.hlsli", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/JacobianMath.hlsli", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/Reservoir.hlsli", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/ReSTIRGI.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/ReSTIRGIParameters.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/SpatialResampling.hlsli", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/SpatioTemporalResampling.hlsli", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/GI/TemporalResampling.hlsli", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/LightSampling/LocalLightSelection.hlsli", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/LightSampling/PresamplingFunctions.hlsli", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/LightSampling/RISBuffer.hlsli", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/LightSampling/RISBufferSegmentAllocator.h", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/LightSampling/RISBufferSegmentParameters.h", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/LightSampling/UniformSampling.hlsli", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/ReGIR/ReGIR.h", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/ReGIR/ReGIRParameters.h", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/ReGIR/ReGIRSampling.hlsli", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/Utils/BoilingFilter.hlsli", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/Utils/Checkerboard.hlsli", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/Utils/Math.hlsli", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Rtxdi/Include/Rtxdi/Utils/ReservoirAddressing.hlsli", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Rtxdi/Source/ImportanceSamplingContext.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Rtxdi/Source/ReGIR.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Rtxdi/Source/ReSTIRDI.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Rtxdi/Source/ReSTIRGI.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Rtxdi/Source/RISBufferSegmentAllocator.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Rtxdi/Source/RtxdiUtils.cpp", "sourceGroupIndex": 6}], "type": "STATIC_LIBRARY"}