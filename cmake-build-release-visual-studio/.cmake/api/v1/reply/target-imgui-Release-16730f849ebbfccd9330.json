{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/thirdparty/Release/imgui.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "add_definitions", "target_include_directories"], "files": ["External/Donut/thirdparty/imgui.cmake", "External/Donut/thirdparty/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 29, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 36, "parent": 2}, {"file": 2}, {"command": 2, "file": 2, "line": 55, "parent": 4}, {"command": 3, "file": 0, "line": 37, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 5, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 6, "path": "D:/RTXPT/External/Donut/thirdparty/imgui"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [1, 2, 3, 5, 10]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Third-Party Libraries"}, "id": "imgui::@c8be3c763eb3df35a7f3", "name": "imgui", "nameOnDisk": "imgui.lib", "paths": {"build": "External/Donut/thirdparty", "source": "External/Donut/thirdparty"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 4, 6, 7, 8, 9]}, {"name": "Source Files", "sourceIndexes": [1, 2, 3, 5, 10]}], "sources": [{"backtrace": 3, "path": "External/Donut/thirdparty/imgui/imconfig.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/imgui/imgui_draw.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/imgui/imgui_tables.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/imgui/imgui_widgets.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "External/Donut/thirdparty/imgui/imgui_internal.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/imgui/imgui.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "External/Donut/thirdparty/imgui/imgui.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/thirdparty/imgui/imstb_rectpack.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/thirdparty/imgui/imstb_textedit.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/thirdparty/imgui/imstb_truetype.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/imgui/imgui_demo.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}