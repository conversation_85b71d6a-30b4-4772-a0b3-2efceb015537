{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/thirdparty/jsoncpp/src/lib_json/Release/jsoncpp.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_definitions", "include_directories", "target_include_directories", "target_compile_features"], "files": ["External/Donut/thirdparty/jsoncpp/src/lib_json/CMakeLists.txt", "CMakeLists.txt", "External/Donut/thirdparty/jsoncpp/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 142, "parent": 0}, {"command": 1, "file": 0, "line": 199, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 55, "parent": 3}, {"file": 2}, {"command": 3, "file": 2, "line": 118, "parent": 5}, {"command": 4, "file": 0, "line": 165, "parent": 0}, {"command": 5, "file": 0, "line": 163, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 6, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/include"}, {"backtrace": 7, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}], "language": "CXX", "languageStandard": {"backtraces": [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], "standard": "11"}, "sourceIndexes": [9, 11, 12]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Third-Party Libraries"}, "id": "jsoncpp_static::@dc903dbc33f565e211e1", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "D:/RTXPT/cmake-build-release-visual-studio/install"}}, "name": "jsoncpp_static", "nameOnDisk": "jsoncpp.lib", "paths": {"build": "External/Donut/thirdparty/jsoncpp/src/lib_json", "source": "External/Donut/thirdparty/jsoncpp/src/lib_json"}, "sourceGroups": [{"name": "Public API", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [8, 10]}, {"name": "Source Files", "sourceIndexes": [9, 11, 12]}], "sources": [{"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/config.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/forwards.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/json_features.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/value.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/reader.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/version.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/writer.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/include/json/assertions.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/src/lib_json/json_tool.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/jsoncpp/src/lib_json/json_reader.cpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Donut/thirdparty/jsoncpp/src/lib_json/json_valueiterator.inl", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/jsoncpp/src/lib_json/json_value.cpp", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/jsoncpp/src/lib_json/json_writer.cpp", "sourceGroupIndex": 2}], "type": "STATIC_LIBRARY"}