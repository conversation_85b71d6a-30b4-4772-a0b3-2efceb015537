{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Omm/external/xxHash/cmake_unofficial/Release/xxhash.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "target_include_directories"], "files": ["External/Omm/external/xxHash/cmake_unofficial/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 92, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 55, "parent": 2}, {"command": 2, "file": 0, "line": 96, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG /Zi /Zi -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 3, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 4, "path": "D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial/.."}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "xxhash::@6461965e4d567125b4d4", "name": "xxhash", "nameOnDisk": "xxhash.lib", "paths": {"build": "External/Omm/external/xxHash/cmake_unofficial", "source": "External/Omm/external/xxHash/cmake_unofficial"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/xxHash/xxhash.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}