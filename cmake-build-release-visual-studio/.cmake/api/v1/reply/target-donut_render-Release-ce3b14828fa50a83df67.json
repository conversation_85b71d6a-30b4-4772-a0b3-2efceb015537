{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/Release/donut_render.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_dependencies", "add_definitions", "target_include_directories"], "files": ["External/Donut/donut-render.cmake", "External/Donut/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 96, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 29, "parent": 2}, {"command": 2, "file": 0, "line": 31, "parent": 2}, {"command": 3, "file": 0, "line": 33, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 55, "parent": 6}, {"command": 5, "file": 0, "line": 30, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "DONUT_WITH_AFTERMATH=0"}, {"backtrace": 4, "define": "DONUT_WITH_DX11=0"}, {"backtrace": 4, "define": "DONUT_WITH_DX12=1"}, {"backtrace": 4, "define": "DONUT_WITH_MINIZ"}, {"backtrace": 4, "define": "DONUT_WITH_STATIC_SHADERS=0"}, {"backtrace": 4, "define": "DONUT_WITH_TASKFLOW"}, {"backtrace": 4, "define": "DONUT_WITH_TINYEXR"}, {"backtrace": 4, "define": "DONUT_WITH_VULKAN=0"}, {"backtrace": 4, "define": "MINIZ_STATIC_DEFINE"}, {"backtrace": 4, "define": "NOMINMAX"}, {"backtrace": 7, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 8, "path": "D:/RTXPT/External/Donut/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/stb"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/cgltf"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/taskflow"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/tinyexr"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], "standard": "17"}, "sourceIndexes": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37]}], "dependencies": [{"backtrace": 4, "id": "donut_core::@3f75b14119991a9702cc"}, {"backtrace": 4, "id": "jsoncpp_static::@dc903dbc33f565e211e1"}, {"backtrace": 4, "id": "donut_engine::@3f75b14119991a9702cc"}, {"backtrace": 4, "id": "ShaderMakeBlob::@b014256a752891a2614a"}, {"backtrace": 4, "id": "miniz::@8f637d5d2c9d0ba648a2"}, {"backtrace": 4, "id": "nvrhi::@1e4fb8cca40b12049cc4"}, {"backtrace": 5, "id": "donut_shaders::@111d5be078c7280583c9"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Donut"}, "id": "donut_render::@3f75b14119991a9702cc", "name": "donut_render", "nameOnDisk": "donut_render.lib", "paths": {"build": "External/Donut", "source": "External/Donut"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"name": "Source Files", "sourceIndexes": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37]}], "sources": [{"backtrace": 3, "path": "External/Donut/include/donut/render/BloomPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/CascadedShadowMap.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/DeferredLightingPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/DepthPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/DrawStrategy.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/EnvironmentMapPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/ForwardShadingPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/GBuffer.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/GBufferFillPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/GeometryPasses.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/JointsRenderPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/LightProbeProcessingPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/MipMapGenPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/PixelReadbackPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/PlanarShadowMap.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/SkyPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/SsaoPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/TemporalAntiAliasingPass.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/render/ToneMappingPasses.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/BloomPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/CascadedShadowMap.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/DeferredLightingPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/DepthPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/DrawStrategy.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/EnvironmentMapPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/ForwardShadingPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/GBuffer.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/GBufferFillPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/GeometryPasses.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/JointsRenderPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/LightProbeProcessingPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/MipMapGenPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/PixelReadbackPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/PlanarShadowMap.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/SkyPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/SsaoPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/TemporalAntiAliasingPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/render/ToneMappingPasses.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}