{"artifacts": [{"path": "D:/RTXPT/bin/Rtxpt.exe"}, {"path": "D:/RTXPT/bin/Rtxpt.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include", "add_dependencies", "add_definitions", "target_include_directories"], "files": ["Rtxpt/CMakeLists.txt", "External/Donut/donut-engine.cmake", "External/Donut/CMakeLists.txt", "External/Donut/donut-render.cmake", "External/Donut/donut-core.cmake", "External/Donut/donut-app.cmake", "External/Omm/libraries/omm-lib/CMakeLists.txt", "External/Omm/external/lz4/build/cmake/CMakeLists.txt", "External/Donut/nvrhi/CMakeLists.txt", "External/Omm/libraries/omm-gpu-nvrhi/CMakeLists.txt", "External/Streamline/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 115, "parent": 0}, {"command": 1, "file": 0, "line": 119, "parent": 0}, {"file": 2}, {"command": 2, "file": 2, "line": 95, "parent": 3}, {"file": 1, "parent": 4}, {"command": 1, "file": 1, "line": 37, "parent": 5}, {"command": 2, "file": 2, "line": 96, "parent": 3}, {"file": 3, "parent": 7}, {"command": 1, "file": 3, "line": 31, "parent": 8}, {"command": 2, "file": 2, "line": 93, "parent": 3}, {"file": 4, "parent": 10}, {"command": 1, "file": 4, "line": 60, "parent": 11}, {"command": 2, "file": 2, "line": 97, "parent": 3}, {"file": 5, "parent": 13}, {"command": 1, "file": 5, "line": 41, "parent": 14}, {"command": 1, "file": 5, "line": 79, "parent": 14}, {"command": 1, "file": 5, "line": 94, "parent": 14}, {"file": 6}, {"command": 1, "file": 6, "line": 95, "parent": 18}, {"file": 7}, {"command": 1, "file": 7, "line": 134, "parent": 20}, {"command": 1, "file": 5, "line": 92, "parent": 14}, {"file": 8}, {"command": 1, "file": 8, "line": 280, "parent": 23}, {"command": 1, "file": 8, "line": 283, "parent": 23}, {"command": 1, "file": 5, "line": 116, "parent": 14}, {"file": 9}, {"command": 1, "file": 9, "line": 15, "parent": 27}, {"command": 3, "file": 0, "line": 117, "parent": 0}, {"file": 10}, {"command": 3, "file": 10, "line": 126, "parent": 30}, {"file": 11}, {"command": 4, "file": 11, "line": 55, "parent": 32}, {"command": 5, "file": 0, "line": 125, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /Zi /Ob0 /Od /RTC1 /D_ITERATOR_DEBUG_LEVEL=1 -std:c++20 -MTd"}, {"backtrace": 2, "fragment": "-openmp"}, {"fragment": "-WX"}], "defines": [{"backtrace": 2, "define": "DONUT_WITH_AFTERMATH=0"}, {"backtrace": 2, "define": "DONUT_WITH_DX11=0"}, {"backtrace": 2, "define": "DONUT_WITH_DX12=1"}, {"backtrace": 2, "define": "DONUT_WITH_MINIZ"}, {"backtrace": 2, "define": "DONUT_WITH_STATIC_SHADERS=0"}, {"backtrace": 2, "define": "DONUT_WITH_STREAMLINE=1"}, {"backtrace": 2, "define": "DONUT_WITH_TASKFLOW"}, {"backtrace": 2, "define": "DONUT_WITH_TINYEXR"}, {"backtrace": 2, "define": "DONUT_WITH_VULKAN=0"}, {"backtrace": 2, "define": "MINIZ_STATIC_DEFINE"}, {"backtrace": 2, "define": "NOMINMAX"}, {"backtrace": 33, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 2, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 34, "path": "D:/RTXPT/Rtxpt/../External/Nrd/Include"}, {"backtrace": 34, "path": "D:/RTXPT/External/Rtxdi/Include"}, {"backtrace": 2, "path": "D:/RTXPT/External/cxxopts/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 2, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/stb"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/cgltf"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/taskflow"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/tinyexr"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/glfw/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/thirdparty/imgui"}, {"backtrace": 2, "path": "D:/RTXPT/External/Nrd/Include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Rtxdi/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/glm"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/stb"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial/.."}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/lz4/build/cmake/../../lib"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/libraries/omm-gpu-nvrhi"}, {"backtrace": 2, "isSystem": true, "path": "D:/RTXPT/External/Streamline/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/RTXPT/External/nvapi"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], "standard": "20"}, "sourceIndexes": [1, 3, 5, 8, 10, 12, 14, 16, 18, 20, 22, 24, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 52, 54, 56, 58, 60, 62, 64, 75, 78]}], "dependencies": [{"backtrace": 2, "id": "donut_core::@3f75b14119991a9702cc"}, {"backtrace": 2, "id": "imgui::@c8be3c763eb3df35a7f3"}, {"backtrace": 2, "id": "jsoncpp_static::@dc903dbc33f565e211e1"}, {"backtrace": 29, "id": "nrd_shaders::@6dcf5859e8d89a406b62"}, {"backtrace": 2, "id": "donut_app::@3f75b14119991a9702cc"}, {"backtrace": 29, "id": "omm_shaders::@6dcf5859e8d89a406b62"}, {"backtrace": 2, "id": "donut_engine::@3f75b14119991a9702cc"}, {"backtrace": 2, "id": "donut_render::@3f75b14119991a9702cc"}, {"backtrace": 2, "id": "ShaderMakeBlob::@b014256a752891a2614a"}, {"backtrace": 2, "id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4"}, {"backtrace": 2, "id": "miniz::@8f637d5d2c9d0ba648a2"}, {"backtrace": 2, "id": "glfw::@cde7473d92b8787751e5"}, {"backtrace": 2, "id": "nvrhi::@1e4fb8cca40b12049cc4"}, {"backtrace": 2, "id": "DirectX-Headers::@36095ebc80295b0f7532"}, {"backtrace": 2, "id": "DirectX-Guids::@36095ebc80295b0f7532"}, {"backtrace": 31, "id": "CopyStreamlineDLLs::@2c60e108f346c3843468"}, {"backtrace": 2, "id": "NRD::@239232d11973c9d5b0a8"}, {"backtrace": 2, "id": "lz4_static::@056654a294b82f1b5e8a"}, {"backtrace": 2, "id": "omm-gpu-nvrhi::@beed3f881e98d782abb3"}, {"backtrace": 2, "id": "xxhash::@6461965e4d567125b4d4"}, {"backtrace": 2, "id": "glm::@3d8cf37488c348ec8d16"}, {"backtrace": 2, "id": "omm-lib::@415ec9098909d7ea5fcd"}, {"backtrace": 2, "id": "Rtxdi::@62fca8016d331f72548b"}, {"backtrace": 29, "id": "ShaderDynamicAssets::@b012b6e63bd5043e3e8b"}, {"backtrace": 29, "id": "rtxpt_shaders::@b012b6e63bd5043e3e8b"}, {"backtrace": 29, "id": "ShaderDynamicAssets_CopyAlways::@b012b6e63bd5043e3e8b"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "!RTX Path Tracing"}, "id": "Rtxpt::@b012b6e63bd5043e3e8b", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /Zi /Ob0 /Od /RTC1 /D_ITERATOR_DEBUG_LEVEL=1 -MTd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"fragment": "/subsystem:windows", "role": "flags"}, {"backtrace": 2, "fragment": "..\\External\\Donut\\Debug\\donut_render.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "..\\External\\Donut\\Debug\\donut_app.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "..\\External\\Donut\\Debug\\donut_engine.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\RTXPT\\bin\\Debug\\NRD.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "..\\External\\Rtxdi\\Debug\\Rtxdi.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "..\\External\\Omm\\libraries\\omm-gpu-nvrhi\\Debug\\omm-gpu-nvrhi.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "..\\External\\Donut\\ShaderMake\\Debug\\ShaderMakeBlob.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "..\\External\\Donut\\Debug\\donut_core.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "..\\External\\Donut\\thirdparty\\jsoncpp\\src\\lib_json\\Debug\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "..\\External\\Donut\\thirdparty\\miniz\\Debug\\miniz.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "D:\\RTXPT\\External\\Streamline\\lib\\x64\\sl.interposer.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "..\\External\\Donut\\thirdparty\\glfw\\src\\Debug\\glfw3.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "..\\External\\Donut\\thirdparty\\Debug\\imgui.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "..\\External\\Omm\\libraries\\omm-lib\\Debug\\omm-lib.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "..\\External\\Omm\\external\\glm\\glm\\Debug\\glm.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "..\\External\\Omm\\external\\xxHash\\cmake_unofficial\\Debug\\xxhash.lib", "role": "libraries"}, {"backtrace": 21, "fragment": "..\\External\\Omm\\external\\lz4\\build\\cmake\\Debug\\lz4.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "..\\External\\Donut\\nvrhi\\Debug\\nvrhi_d3d12.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "..\\External\\Donut\\nvrhi\\thirdparty\\DirectX-Headers\\Debug\\DirectX-Guids.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "..\\External\\Donut\\nvrhi\\thirdparty\\DirectX-Headers\\Debug\\DirectX-Headers.lib", "role": "libraries"}, {"backtrace": 25, "fragment": "D:\\RTXPT\\External\\nvapi\\amd64\\nvapi64.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "..\\External\\Donut\\nvrhi\\Debug\\nvrhi.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "dxgi.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Rtxpt", "nameOnDisk": "Rtxpt.exe", "paths": {"build": "Rtxpt", "source": "Rtxpt"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4, 6, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 51, 53, 55, 57, 59, 61, 63, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 79]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5, 8, 10, 12, 14, 16, 18, 20, 22, 24, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 52, 54, 56, 58, 60, 62, 64, 75, 78]}], "sources": [{"backtrace": 1, "path": "Rtxpt/AccelerationStructureUtil.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/AccumulationPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/AccumulationPass.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/ComputePass.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/ComputePass.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/ExtendedScene.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/ExtendedScene.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/GPUSort/FFX_ParallelSort.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/GPUSort/GPUSort.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/GPUSort/GPUSort.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Lighting/Distant/EnvMapBaker.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Lighting/Distant/EnvMapBaker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Lighting/Distant/EnvMapImportanceSamplingBaker.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Lighting/Distant/EnvMapImportanceSamplingBaker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Lighting/Distant/SampleProceduralSky.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Lighting/Distant/SampleProceduralSky.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Lighting/LightsBaker.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Lighting/LightsBaker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/LocalConfig.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/LocalConfig.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Materials/MaterialsBaker.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Materials/MaterialsBaker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Misc/CommandLine.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Misc/CommandLine.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Misc/Korgi.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Misc/Korgi.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Misc/picosha2.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/NRD/NrdConfig.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/NRD/NrdConfig.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/NRD/NrdIntegration.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/NRD/NrdIntegration.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/OpacityMicroMap/OmmBaker.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/OpacityMicroMap/OmmBaker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/OpacityMicroMap/OmmBuildQueue.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/OpacityMicroMap/OmmBuildQueue.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/PTPipelineBaker.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/PTPipelineBaker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/PostProcess.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/PostProcess.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RTXDI/GeneratePdfMipsPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RTXDI/GeneratePdfMipsPass.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RTXDI/PrepareLightsPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RTXDI/PrepareLightsPass.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RTXDI/RayTracingPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RTXDI/RayTracingPass.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RTXDI/RtxdiApplicationSettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RTXDI/RtxdiApplicationSettings.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RTXDI/RtxdiPass.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RTXDI/RtxdiPass.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RTXDI/RtxdiResources.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RTXDI/RtxdiResources.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/RTXDI/ShaderParameters.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/RenderTargets.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/RenderTargets.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/Sample.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/Sample.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/SampleCommon.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/SampleCommon.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/SampleGame/SampleGame.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/SampleGame/SampleGame.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/SampleGame/SampleGameProp.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/SampleGame/SampleGameProp.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/SampleUI.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/SampleUI.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/ShaderDebug.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/ShaderDebug.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/PathTracer/Config.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/PathTracer/Lighting/LightingConfig.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/PathTracer/Lighting/LightingTypes.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/PathTracer/Lighting/PolymorphicLight.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/PathTracer/Materials/MaterialPT.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/PathTracer/PathTracerShared.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/SampleConstantBuffer.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/Shaders/SubInstanceData.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/ToneMapper/ColorUtils.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/ToneMapper/ToneMappingPasses.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/ToneMapper/ToneMappingPasses.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Rtxpt/ToneMapper/ToneMapping_cb.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Rtxpt/ZoomTool.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Rtxpt/ZoomTool.h", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}