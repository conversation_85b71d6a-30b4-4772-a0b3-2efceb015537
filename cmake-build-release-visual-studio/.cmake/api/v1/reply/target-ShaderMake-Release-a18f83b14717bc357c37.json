{"artifacts": [{"path": "D:/RTXPT/bin/ShaderMake.exe"}, {"path": "D:/RTXPT/bin/ShaderMake.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_options", "target_link_libraries", "target_compile_options", "target_compile_definitions", "add_definitions"], "files": ["External/Donut/ShaderMake/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 76, "parent": 0}, {"command": 1, "file": 0, "line": 101, "parent": 0}, {"command": 2, "file": 0, "line": 97, "parent": 0}, {"command": 2, "file": 0, "line": 102, "parent": 0}, {"command": 3, "file": 0, "line": 81, "parent": 0}, {"command": 4, "file": 0, "line": 100, "parent": 0}, {"file": 1}, {"command": 5, "file": 1, "line": 55, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG /Zi /Zi -MT"}, {"backtrace": 5, "fragment": "/W4"}, {"backtrace": 5, "fragment": "/WX"}, {"backtrace": 5, "fragment": "/wd4324"}, {"fragment": "-WX"}], "defines": [{"backtrace": 6, "define": "NOMINMAX"}, {"backtrace": 8, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 6, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 6, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 3, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"backtrace": 5, "fragment": "/W4"}, {"backtrace": 5, "fragment": "/WX"}, {"backtrace": 5, "fragment": "/wd4324"}, {"fragment": "-WX"}], "defines": [{"backtrace": 6, "define": "NOMINMAX"}, {"backtrace": 8, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 6, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 6, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 3, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [2]}], "dependencies": [{"backtrace": 3, "id": "ShaderMakeBlob::@b014256a752891a2614a"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "ShaderMake::@b014256a752891a2614a", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -MT", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /DEBUG /DEBUG", "role": "flags"}, {"fragment": "/subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "/DELAYLOAD:dxcompiler.dll", "role": "flags"}, {"backtrace": 3, "fragment": "Release\\ShaderMakeBlob.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "d3dcompiler.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "dxcompiler.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "delayimp.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "ShaderMake", "nameOnDisk": "ShaderMake.exe", "paths": {"build": "External/Donut/ShaderMake", "source": "External/Donut/ShaderMake"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/ShaderMake/src/argparse.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/ShaderMake/src/argparse.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "External/Donut/ShaderMake/src/ShaderMake.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}