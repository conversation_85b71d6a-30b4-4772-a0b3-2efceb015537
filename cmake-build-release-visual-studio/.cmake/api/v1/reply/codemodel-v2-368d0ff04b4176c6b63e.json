{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 30], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.18"}, "projectIndex": 0, "source": ".", "targetIndexes": [2, 30]}, {"build": "External", "childIndexes": [2, 16, 18, 27, 28], "hasInstallRule": true, "jsonFile": "directory-External-Debug-7eb880457d7b45551c8c.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 0, "projectIndex": 0, "source": "External", "targetIndexes": [42, 47]}, {"build": "External/Donut", "childIndexes": [3, 11, 12, 14, 15], "hasInstallRule": true, "jsonFile": "directory-External.Donut-Debug-26514524e1da3094e909.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 1, "projectIndex": 1, "source": "External/Donut", "targetIndexes": [9, 31, 32, 33, 34]}, {"build": "External/Donut/thirdparty", "childIndexes": [4, 8, 10], "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty-Debug-ec68bace4b187fb4ca62.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 1, "source": "External/Donut/thirdparty", "targetIndexes": [38]}, {"build": "External/Donut/thirdparty/jsoncpp", "childIndexes": [5, 7], "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp-Debug-ac44a227cb911b405532.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 3, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp", "targetIndexes": [4]}, {"build": "External/Donut/thirdparty/jsoncpp/src", "childIndexes": [6], "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp.src-Debug-c8e1642b9717b2ca05a9.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 4, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp/src"}, {"build": "External/Donut/thirdparty/jsoncpp/src/lib_json", "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp.src.lib_json-Debug-3e1d0a0f9c5eebf9c32c.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 5, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp/src/lib_json", "targetIndexes": [39]}, {"build": "External/Donut/thirdparty/jsoncpp/include", "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp.include-Debug-3330a41c8087791e1e3d.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 4, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp/include"}, {"build": "External/Donut/thirdparty/glfw", "childIndexes": [9], "jsonFile": "directory-External.Donut.thirdparty.glfw-Debug-5408a007b3d161c78178.json", "minimumCMakeVersion": {"string": "3.4"}, "parentIndex": 3, "projectIndex": 3, "source": "External/Donut/thirdparty/glfw", "targetIndexes": [12]}, {"build": "External/Donut/thirdparty/glfw/src", "jsonFile": "directory-External.Donut.thirdparty.glfw.src-Debug-40b61309c9d84d798cde.json", "minimumCMakeVersion": {"string": "3.4"}, "parentIndex": 8, "projectIndex": 3, "source": "External/Donut/thirdparty/glfw/src", "targetIndexes": [36, 49]}, {"build": "External/Donut/thirdparty/miniz", "jsonFile": "directory-External.Donut.thirdparty.miniz-Debug-a155ba67da73f16c489c.json", "minimumCMakeVersion": {"string": "3.5"}, "parentIndex": 3, "projectIndex": 4, "source": "External/Donut/thirdparty/miniz", "targetIndexes": [5, 41]}, {"build": "External/Donut/ShaderMake", "jsonFile": "directory-External.Donut.ShaderMake-Debug-bbace32002f9dc732add.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 2, "projectIndex": 5, "source": "External/Donut/ShaderMake", "targetIndexes": [8, 28, 29]}, {"build": "External/Donut/nvrhi", "childIndexes": [13], "hasInstallRule": true, "jsonFile": "directory-External.Donut.nvrhi-Debug-a75ce0fa68c6c0281ce2.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 6, "source": "External/Donut/nvrhi", "targetIndexes": [1, 43, 44]}, {"build": "External/Donut/nvrhi/thirdparty/DirectX-Headers", "jsonFile": "directory-External.Donut.nvrhi.thirdparty.DirectX-Headers-Debug-18b044421fb5f6490c56.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 12, "projectIndex": 7, "source": "External/Donut/nvrhi/thirdparty/DirectX-Headers", "targetIndexes": [0, 14, 16, 17, 18, 22, 23]}, {"build": "External/Donut/shaders", "jsonFile": "directory-External.Donut.shaders-Debug-46c83919407678f312b1.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 1, "source": "External/Donut/shaders", "targetIndexes": [35]}, {"build": "streamline", "hasInstallRule": true, "jsonFile": "directory-streamline-Debug-b09999c80ab7389b3e70.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 1, "source": "External/Streamline", "targetIndexes": [15]}, {"build": "External/Nrd", "childIndexes": [17], "jsonFile": "directory-External.Nrd-Debug-970bdc57992747043173.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 8, "source": "External/Nrd", "targetIndexes": [3, 20, 21]}, {"build": "_deps/mathlib-build", "jsonFile": "directory-_deps.mathlib-build-Debug-d5d5f2c7601b16c95145.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 16, "projectIndex": 8, "source": "cmake-build-release-visual-studio/_deps/mathlib-src", "targetIndexes": [19]}, {"build": "External/Omm", "childIndexes": [19, 24, 25, 26], "hasInstallRule": true, "jsonFile": "directory-External.Omm-Debug-a104ea8098e5ca8ba025.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 1, "projectIndex": 9, "source": "External/Omm", "targetIndexes": [13]}, {"build": "External/Omm/external", "childIndexes": [20, 22, 23], "jsonFile": "directory-External.Omm.external-Debug-c8787142108aab488d09.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/external"}, {"build": "External/Omm/external/glm", "childIndexes": [21], "jsonFile": "directory-External.Omm.external.glm-Debug-c70c0db305201d49b45a.json", "minimumCMakeVersion": {"string": "3.6"}, "parentIndex": 19, "projectIndex": 10, "source": "External/Omm/external/glm", "targetIndexes": [11]}, {"build": "External/Omm/external/glm/glm", "jsonFile": "directory-External.Omm.external.glm.glm-Debug-1c278af8c77f63a892c2.json", "minimumCMakeVersion": {"string": "3.6"}, "parentIndex": 20, "projectIndex": 10, "source": "External/Omm/external/glm/glm", "targetIndexes": [37]}, {"build": "External/Omm/external/lz4/build/cmake", "jsonFile": "directory-External.Omm.external.lz4.build.cmake-Debug-7a7b35b501d126660bba.json", "minimumCMakeVersion": {"string": "3.5"}, "parentIndex": 19, "projectIndex": 11, "source": "External/Omm/external/lz4/build/cmake", "targetIndexes": [10, 40]}, {"build": "External/Omm/external/xxHash/cmake_unofficial", "jsonFile": "directory-External.Omm.external.xxHash.cmake_unofficial-Debug-4640140c2d15dfaf86f1.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 19, "projectIndex": 12, "source": "External/Omm/external/xxHash/cmake_unofficial", "targetIndexes": [7, 50]}, {"build": "External/Omm/libraries/omm-lib", "hasInstallRule": true, "jsonFile": "directory-External.Omm.libraries.omm-lib-Debug-8dd46d80b08f75e55629.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/libraries/omm-lib", "targetIndexes": [46]}, {"build": "External/Omm/libraries/omm-gpu-nvrhi", "jsonFile": "directory-External.Omm.libraries.omm-gpu-nvrhi-Debug-0c67e9fb60a2412ad298.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/libraries/omm-gpu-nvrhi", "targetIndexes": [45]}, {"build": "External/Omm/support/scripts", "hasInstallRule": true, "jsonFile": "directory-External.Omm.support.scripts-Debug-9fc12ec62da6b51dacfa.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/support/scripts"}, {"build": "External/Rtxdi", "jsonFile": "directory-External.Rtxdi-Debug-a2946db0808a1175c1fe.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 1, "projectIndex": 0, "source": "External/Rtxdi", "targetIndexes": [24]}, {"build": "External/cxxopts", "childIndexes": [29], "jsonFile": "directory-External.cxxopts-Debug-0b4af7ed010fdbf63b8a.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 1, "projectIndex": 13, "source": "External/cxxopts", "targetIndexes": [6]}, {"build": "External/cxxopts/include", "jsonFile": "directory-External.cxxopts.include-Debug-94e04bf9aebef4f506ef.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 28, "projectIndex": 13, "source": "External/cxxopts/include"}, {"build": "Rtxpt", "jsonFile": "directory-Rtxpt-Debug-1e3087731abcd0edb041.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 0, "projectIndex": 0, "source": "Rtxpt", "targetIndexes": [25, 26, 27, 48]}], "name": "Debug", "projects": [{"childIndexes": [1, 8, 9, 13], "directoryIndexes": [0, 1, 27, 30], "name": "RTXPathTracing", "targetIndexes": [2, 24, 25, 26, 27, 30, 42, 47, 48]}, {"childIndexes": [2, 3, 4, 5, 6], "directoryIndexes": [2, 3, 14, 15], "name": "donut", "parentIndex": 0, "targetIndexes": [9, 15, 31, 32, 33, 34, 35, 38]}, {"directoryIndexes": [4, 5, 6, 7], "name": "jsoncpp", "parentIndex": 1, "targetIndexes": [4, 39]}, {"directoryIndexes": [8, 9], "name": "GLFW", "parentIndex": 1, "targetIndexes": [12, 36, 49]}, {"directoryIndexes": [10], "name": "miniz", "parentIndex": 1, "targetIndexes": [5, 41]}, {"directoryIndexes": [11], "name": "ShaderMake", "parentIndex": 1, "targetIndexes": [8, 28, 29]}, {"childIndexes": [7], "directoryIndexes": [12], "name": "nvrhi", "parentIndex": 1, "targetIndexes": [1, 43, 44]}, {"directoryIndexes": [13], "name": "DirectX-Headers", "parentIndex": 6, "targetIndexes": [0, 14, 16, 17, 18, 22, 23]}, {"directoryIndexes": [16, 17], "name": "NRD", "parentIndex": 0, "targetIndexes": [3, 19, 20, 21]}, {"childIndexes": [10, 11, 12], "directoryIndexes": [18, 19, 24, 25, 26], "name": "Opacity Micro-Map SDK", "parentIndex": 0, "targetIndexes": [13, 45, 46]}, {"directoryIndexes": [20, 21], "name": "glm", "parentIndex": 9, "targetIndexes": [11, 37]}, {"directoryIndexes": [22], "name": "LZ4", "parentIndex": 9, "targetIndexes": [10, 40]}, {"directoryIndexes": [23], "name": "xxHash", "parentIndex": 9, "targetIndexes": [7, 50]}, {"directoryIndexes": [28, 29], "name": "cxxopts", "parentIndex": 0, "targetIndexes": [6]}], "targets": [{"directoryIndex": 13, "id": "ALL_BUILD::@36095ebc80295b0f7532", "jsonFile": "target-ALL_BUILD-Debug-80211b7c0825a519b9e4.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 12, "id": "ALL_BUILD::@1e4fb8cca40b12049cc4", "jsonFile": "target-ALL_BUILD-Debug-43cc024797cb0c40adb3.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-4a07a22af554f19f8248.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 16, "id": "ALL_BUILD::@239232d11973c9d5b0a8", "jsonFile": "target-ALL_BUILD-Debug-2d080ff4ab75b280fa31.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 4, "id": "ALL_BUILD::@10175f99a46424ae9e27", "jsonFile": "target-ALL_BUILD-Debug-c402e7484d2307ebdf2d.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 10, "id": "ALL_BUILD::@8f637d5d2c9d0ba648a2", "jsonFile": "target-ALL_BUILD-Debug-cf78b8f5e180b52ceb47.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 28, "id": "ALL_BUILD::@eff4f5fe60f26418d6fe", "jsonFile": "target-ALL_BUILD-Debug-09a0418300ed8b245264.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 23, "id": "ALL_BUILD::@6461965e4d567125b4d4", "jsonFile": "target-ALL_BUILD-Debug-0a3d00d4da14670d404f.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 11, "id": "ALL_BUILD::@b014256a752891a2614a", "jsonFile": "target-ALL_BUILD-Debug-821c2a1452f78c0a3f7d.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 2, "id": "ALL_BUILD::@3f75b14119991a9702cc", "jsonFile": "target-ALL_BUILD-Debug-d5dc1a876ec512fea8d5.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 22, "id": "ALL_BUILD::@056654a294b82f1b5e8a", "jsonFile": "target-ALL_BUILD-Debug-ddee3f1f81f798f78499.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 20, "id": "ALL_BUILD::@62d76fcc7bb8624efaf3", "jsonFile": "target-ALL_BUILD-Debug-8a61172334690d0a236e.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 8, "id": "ALL_BUILD::@a4c578d98de775ea62cc", "jsonFile": "target-ALL_BUILD-Debug-0ffa6d5ac2a8477ad49f.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 18, "id": "ALL_BUILD::@9a8fb8386807ee1c7036", "jsonFile": "target-ALL_BUILD-Debug-b7e455b17a7667420db1.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 13, "id": "Continuous::@36095ebc80295b0f7532", "jsonFile": "target-Continuous-Debug-b205acae3a56576de4d6.json", "name": "Continuous", "projectIndex": 7}, {"directoryIndex": 15, "id": "CopyStreamlineDLLs::@2c60e108f346c3843468", "jsonFile": "target-CopyStreamlineDLLs-Debug-83c92bc13de002537440.json", "name": "CopyStreamlineDLLs", "projectIndex": 1}, {"directoryIndex": 13, "id": "DirectX-Guids::@36095ebc80295b0f7532", "jsonFile": "target-DirectX-Guids-Debug-5b5a01e0788e5a311709.json", "name": "DirectX-Guids", "projectIndex": 7}, {"directoryIndex": 13, "id": "DirectX-Headers::@36095ebc80295b0f7532", "jsonFile": "target-DirectX-Headers-Debug-48765c164285170caf64.json", "name": "DirectX-Headers", "projectIndex": 7}, {"directoryIndex": 13, "id": "Experimental::@36095ebc80295b0f7532", "jsonFile": "target-Experimental-Debug-f07206e16a37c51ca6e1.json", "name": "Experimental", "projectIndex": 7}, {"directoryIndex": 17, "id": "MathLib::@6b8530d6352516f31722", "jsonFile": "target-MathLib-Debug-4b63d5733fadfcc8cc81.json", "name": "MathLib", "projectIndex": 8}, {"directoryIndex": 16, "id": "NRD::@239232d11973c9d5b0a8", "jsonFile": "target-NRD-Debug-ee7d4a77e8703560a0ca.json", "name": "NRD", "projectIndex": 8}, {"directoryIndex": 16, "id": "NRDIntegration::@239232d11973c9d5b0a8", "jsonFile": "target-NRDIntegration-Debug-70fcb2f845121850bc0e.json", "name": "NRDIntegration", "projectIndex": 8}, {"directoryIndex": 13, "id": "Nightly::@36095ebc80295b0f7532", "jsonFile": "target-Nightly-Debug-ce3f198bdd110e601ce1.json", "name": "Nightly", "projectIndex": 7}, {"directoryIndex": 13, "id": "NightlyMemoryCheck::@36095ebc80295b0f7532", "jsonFile": "target-NightlyMemoryCheck-Debug-15ddf0daaec95cf9ffe4.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 7}, {"directoryIndex": 27, "id": "Rtxdi::@62fca8016d331f72548b", "jsonFile": "target-Rtxdi-Debug-7fb93398c501f49b1a66.json", "name": "Rtxdi", "projectIndex": 0}, {"directoryIndex": 30, "id": "Rtxpt::@b012b6e63bd5043e3e8b", "jsonFile": "target-Rtxpt-Debug-04285123e5556fde1c90.json", "name": "Rtxpt", "projectIndex": 0}, {"directoryIndex": 30, "id": "ShaderDynamicAssets::@b012b6e63bd5043e3e8b", "jsonFile": "target-ShaderDynamicAssets-Debug-807249cd2db5216eadbd.json", "name": "ShaderDynamicAssets", "projectIndex": 0}, {"directoryIndex": 30, "id": "ShaderDynamicAssets_CopyAlways::@b012b6e63bd5043e3e8b", "jsonFile": "target-ShaderDynamicAssets_CopyAlways-Debug-6656de469627b62d2ea6.json", "name": "ShaderDynamicAssets_CopyAlways", "projectIndex": 0}, {"directoryIndex": 11, "id": "ShaderMake::@b014256a752891a2614a", "jsonFile": "target-ShaderMake-Debug-1f35c8940d037e3f24fa.json", "name": "ShaderMake", "projectIndex": 5}, {"directoryIndex": 11, "id": "ShaderMakeBlob::@b014256a752891a2614a", "jsonFile": "target-ShaderMakeBlob-Debug-3ff11597edc1babd54b6.json", "name": "ShaderMakeBlob", "projectIndex": 5}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-0028a7e95cc1498b23c4.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 2, "id": "donut_app::@3f75b14119991a9702cc", "jsonFile": "target-donut_app-Debug-4969828457c69d6e701e.json", "name": "donut_app", "projectIndex": 1}, {"directoryIndex": 2, "id": "donut_core::@3f75b14119991a9702cc", "jsonFile": "target-donut_core-Debug-22fc1acb6b0bdeb0e017.json", "name": "donut_core", "projectIndex": 1}, {"directoryIndex": 2, "id": "donut_engine::@3f75b14119991a9702cc", "jsonFile": "target-donut_engine-Debug-b8b38a0bb7353dc13abd.json", "name": "donut_engine", "projectIndex": 1}, {"directoryIndex": 2, "id": "donut_render::@3f75b14119991a9702cc", "jsonFile": "target-donut_render-Debug-99635d83948796b82a6f.json", "name": "donut_render", "projectIndex": 1}, {"directoryIndex": 14, "id": "donut_shaders::@111d5be078c7280583c9", "jsonFile": "target-donut_shaders-Debug-9adf001a034f6d5fd092.json", "name": "donut_shaders", "projectIndex": 1}, {"directoryIndex": 9, "id": "glfw::@cde7473d92b8787751e5", "jsonFile": "target-glfw-Debug-ad4bab0a1f1dedaa150f.json", "name": "glfw", "projectIndex": 3}, {"directoryIndex": 21, "id": "glm::@3d8cf37488c348ec8d16", "jsonFile": "target-glm-Debug-84da394faaaee2d9d69d.json", "name": "glm", "projectIndex": 10}, {"directoryIndex": 3, "id": "imgui::@c8be3c763eb3df35a7f3", "jsonFile": "target-imgui-Debug-76dfafa232af6b003fa2.json", "name": "imgui", "projectIndex": 1}, {"directoryIndex": 6, "id": "jsoncpp_static::@dc903dbc33f565e211e1", "jsonFile": "target-jsoncpp_static-Debug-536f89ad5455c4806f82.json", "name": "jsoncpp_static", "projectIndex": 2}, {"directoryIndex": 22, "id": "lz4_static::@056654a294b82f1b5e8a", "jsonFile": "target-lz4_static-Debug-d1cf91d3d23835e25b90.json", "name": "lz4_static", "projectIndex": 11}, {"directoryIndex": 10, "id": "miniz::@8f637d5d2c9d0ba648a2", "jsonFile": "target-miniz-Debug-6e16e0151b8b5d1646a8.json", "name": "miniz", "projectIndex": 4}, {"directoryIndex": 1, "id": "nrd_shaders::@6dcf5859e8d89a406b62", "jsonFile": "target-nrd_shaders-Debug-db54aae25abf7738e6e9.json", "name": "nrd_shaders", "projectIndex": 0}, {"directoryIndex": 12, "id": "nvrhi::@1e4fb8cca40b12049cc4", "jsonFile": "target-nvrhi-Debug-403d0a87853351a3a5a8.json", "name": "nvrhi", "projectIndex": 6}, {"directoryIndex": 12, "id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4", "jsonFile": "target-nvrhi_d3d12-Debug-f89c743f656d55eabca3.json", "name": "nvrhi_d3d12", "projectIndex": 6}, {"directoryIndex": 25, "id": "omm-gpu-nvrhi::@beed3f881e98d782abb3", "jsonFile": "target-omm-gpu-nvrhi-Debug-cbe1ad32b0787890baf0.json", "name": "omm-gpu-nvrhi", "projectIndex": 9}, {"directoryIndex": 24, "id": "omm-lib::@415ec9098909d7ea5fcd", "jsonFile": "target-omm-lib-Debug-4e4e579d32b7ecbf647f.json", "name": "omm-lib", "projectIndex": 9}, {"directoryIndex": 1, "id": "omm_shaders::@6dcf5859e8d89a406b62", "jsonFile": "target-omm_shaders-Debug-b7276ef8a63e8496176d.json", "name": "omm_shaders", "projectIndex": 0}, {"directoryIndex": 30, "id": "rtxpt_shaders::@b012b6e63bd5043e3e8b", "jsonFile": "target-rtxpt_shaders-Debug-eb90fdce0b37412c894a.json", "name": "rtxpt_shaders", "projectIndex": 0}, {"directoryIndex": 9, "id": "update_mappings::@cde7473d92b8787751e5", "jsonFile": "target-update_mappings-Debug-70446d4fb60ffc15c12b.json", "name": "update_mappings", "projectIndex": 3}, {"directoryIndex": 23, "id": "xxhash::@6461965e4d567125b4d4", "jsonFile": "target-xxhash-Debug-4982678889266cc492c9.json", "name": "xxhash", "projectIndex": 12}]}, {"directories": [{"build": ".", "childIndexes": [1, 30], "hasInstallRule": true, "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.18"}, "projectIndex": 0, "source": ".", "targetIndexes": [2, 30]}, {"build": "External", "childIndexes": [2, 16, 18, 27, 28], "hasInstallRule": true, "jsonFile": "directory-External-Release-7eb880457d7b45551c8c.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 0, "projectIndex": 0, "source": "External", "targetIndexes": [42, 47]}, {"build": "External/Donut", "childIndexes": [3, 11, 12, 14, 15], "hasInstallRule": true, "jsonFile": "directory-External.Donut-Release-26514524e1da3094e909.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 1, "projectIndex": 1, "source": "External/Donut", "targetIndexes": [9, 31, 32, 33, 34]}, {"build": "External/Donut/thirdparty", "childIndexes": [4, 8, 10], "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty-Release-ec68bace4b187fb4ca62.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 1, "source": "External/Donut/thirdparty", "targetIndexes": [38]}, {"build": "External/Donut/thirdparty/jsoncpp", "childIndexes": [5, 7], "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp-Release-ac44a227cb911b405532.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 3, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp", "targetIndexes": [4]}, {"build": "External/Donut/thirdparty/jsoncpp/src", "childIndexes": [6], "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp.src-Release-c8e1642b9717b2ca05a9.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 4, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp/src"}, {"build": "External/Donut/thirdparty/jsoncpp/src/lib_json", "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp.src.lib_json-Release-d686819891bfccdb0893.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 5, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp/src/lib_json", "targetIndexes": [39]}, {"build": "External/Donut/thirdparty/jsoncpp/include", "hasInstallRule": true, "jsonFile": "directory-External.Donut.thirdparty.jsoncpp.include-Release-3330a41c8087791e1e3d.json", "minimumCMakeVersion": {"string": "3.8.0"}, "parentIndex": 4, "projectIndex": 2, "source": "External/Donut/thirdparty/jsoncpp/include"}, {"build": "External/Donut/thirdparty/glfw", "childIndexes": [9], "jsonFile": "directory-External.Donut.thirdparty.glfw-Release-5408a007b3d161c78178.json", "minimumCMakeVersion": {"string": "3.4"}, "parentIndex": 3, "projectIndex": 3, "source": "External/Donut/thirdparty/glfw", "targetIndexes": [12]}, {"build": "External/Donut/thirdparty/glfw/src", "jsonFile": "directory-External.Donut.thirdparty.glfw.src-Release-40b61309c9d84d798cde.json", "minimumCMakeVersion": {"string": "3.4"}, "parentIndex": 8, "projectIndex": 3, "source": "External/Donut/thirdparty/glfw/src", "targetIndexes": [36, 49]}, {"build": "External/Donut/thirdparty/miniz", "jsonFile": "directory-External.Donut.thirdparty.miniz-Release-a155ba67da73f16c489c.json", "minimumCMakeVersion": {"string": "3.5"}, "parentIndex": 3, "projectIndex": 4, "source": "External/Donut/thirdparty/miniz", "targetIndexes": [5, 41]}, {"build": "External/Donut/ShaderMake", "jsonFile": "directory-External.Donut.ShaderMake-Release-bbace32002f9dc732add.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 2, "projectIndex": 5, "source": "External/Donut/ShaderMake", "targetIndexes": [8, 28, 29]}, {"build": "External/Donut/nvrhi", "childIndexes": [13], "hasInstallRule": true, "jsonFile": "directory-External.Donut.nvrhi-Release-42fd0aa762270caeb70c.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 6, "source": "External/Donut/nvrhi", "targetIndexes": [1, 43, 44]}, {"build": "External/Donut/nvrhi/thirdparty/DirectX-Headers", "jsonFile": "directory-External.Donut.nvrhi.thirdparty.DirectX-Headers-Release-18b044421fb5f6490c56.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 12, "projectIndex": 7, "source": "External/Donut/nvrhi/thirdparty/DirectX-Headers", "targetIndexes": [0, 14, 16, 17, 18, 22, 23]}, {"build": "External/Donut/shaders", "jsonFile": "directory-External.Donut.shaders-Release-46c83919407678f312b1.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 1, "source": "External/Donut/shaders", "targetIndexes": [35]}, {"build": "streamline", "hasInstallRule": true, "jsonFile": "directory-streamline-Release-b09999c80ab7389b3e70.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 1, "source": "External/Streamline", "targetIndexes": [15]}, {"build": "External/Nrd", "childIndexes": [17], "jsonFile": "directory-External.Nrd-Release-970bdc57992747043173.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 8, "source": "External/Nrd", "targetIndexes": [3, 20, 21]}, {"build": "_deps/mathlib-build", "jsonFile": "directory-_deps.mathlib-build-Release-d5d5f2c7601b16c95145.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 16, "projectIndex": 8, "source": "cmake-build-release-visual-studio/_deps/mathlib-src", "targetIndexes": [19]}, {"build": "External/Omm", "childIndexes": [19, 24, 25, 26], "hasInstallRule": true, "jsonFile": "directory-External.Omm-Release-a104ea8098e5ca8ba025.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 1, "projectIndex": 9, "source": "External/Omm", "targetIndexes": [13]}, {"build": "External/Omm/external", "childIndexes": [20, 22, 23], "jsonFile": "directory-External.Omm.external-Release-c8787142108aab488d09.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/external"}, {"build": "External/Omm/external/glm", "childIndexes": [21], "jsonFile": "directory-External.Omm.external.glm-Release-c70c0db305201d49b45a.json", "minimumCMakeVersion": {"string": "3.6"}, "parentIndex": 19, "projectIndex": 10, "source": "External/Omm/external/glm", "targetIndexes": [11]}, {"build": "External/Omm/external/glm/glm", "jsonFile": "directory-External.Omm.external.glm.glm-Release-1c278af8c77f63a892c2.json", "minimumCMakeVersion": {"string": "3.6"}, "parentIndex": 20, "projectIndex": 10, "source": "External/Omm/external/glm/glm", "targetIndexes": [37]}, {"build": "External/Omm/external/lz4/build/cmake", "jsonFile": "directory-External.Omm.external.lz4.build.cmake-Release-7a7b35b501d126660bba.json", "minimumCMakeVersion": {"string": "3.5"}, "parentIndex": 19, "projectIndex": 11, "source": "External/Omm/external/lz4/build/cmake", "targetIndexes": [10, 40]}, {"build": "External/Omm/external/xxHash/cmake_unofficial", "jsonFile": "directory-External.Omm.external.xxHash.cmake_unofficial-Release-4640140c2d15dfaf86f1.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 19, "projectIndex": 12, "source": "External/Omm/external/xxHash/cmake_unofficial", "targetIndexes": [7, 50]}, {"build": "External/Omm/libraries/omm-lib", "hasInstallRule": true, "jsonFile": "directory-External.Omm.libraries.omm-lib-Release-45ae68edc5e4e5baf49d.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/libraries/omm-lib", "targetIndexes": [46]}, {"build": "External/Omm/libraries/omm-gpu-nvrhi", "jsonFile": "directory-External.Omm.libraries.omm-gpu-nvrhi-Release-0c67e9fb60a2412ad298.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/libraries/omm-gpu-nvrhi", "targetIndexes": [45]}, {"build": "External/Omm/support/scripts", "hasInstallRule": true, "jsonFile": "directory-External.Omm.support.scripts-Release-9fc12ec62da6b51dacfa.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 18, "projectIndex": 9, "source": "External/Omm/support/scripts"}, {"build": "External/Rtxdi", "jsonFile": "directory-External.Rtxdi-Release-a2946db0808a1175c1fe.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 1, "projectIndex": 0, "source": "External/Rtxdi", "targetIndexes": [24]}, {"build": "External/cxxopts", "childIndexes": [29], "jsonFile": "directory-External.cxxopts-Release-0b4af7ed010fdbf63b8a.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 1, "projectIndex": 13, "source": "External/cxxopts", "targetIndexes": [6]}, {"build": "External/cxxopts/include", "jsonFile": "directory-External.cxxopts.include-Release-94e04bf9aebef4f506ef.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 28, "projectIndex": 13, "source": "External/cxxopts/include"}, {"build": "Rtxpt", "jsonFile": "directory-Rtxpt-Release-1e3087731abcd0edb041.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 0, "projectIndex": 0, "source": "Rtxpt", "targetIndexes": [25, 26, 27, 48]}], "name": "Release", "projects": [{"childIndexes": [1, 8, 9, 13], "directoryIndexes": [0, 1, 27, 30], "name": "RTXPathTracing", "targetIndexes": [2, 24, 25, 26, 27, 30, 42, 47, 48]}, {"childIndexes": [2, 3, 4, 5, 6], "directoryIndexes": [2, 3, 14, 15], "name": "donut", "parentIndex": 0, "targetIndexes": [9, 15, 31, 32, 33, 34, 35, 38]}, {"directoryIndexes": [4, 5, 6, 7], "name": "jsoncpp", "parentIndex": 1, "targetIndexes": [4, 39]}, {"directoryIndexes": [8, 9], "name": "GLFW", "parentIndex": 1, "targetIndexes": [12, 36, 49]}, {"directoryIndexes": [10], "name": "miniz", "parentIndex": 1, "targetIndexes": [5, 41]}, {"directoryIndexes": [11], "name": "ShaderMake", "parentIndex": 1, "targetIndexes": [8, 28, 29]}, {"childIndexes": [7], "directoryIndexes": [12], "name": "nvrhi", "parentIndex": 1, "targetIndexes": [1, 43, 44]}, {"directoryIndexes": [13], "name": "DirectX-Headers", "parentIndex": 6, "targetIndexes": [0, 14, 16, 17, 18, 22, 23]}, {"directoryIndexes": [16, 17], "name": "NRD", "parentIndex": 0, "targetIndexes": [3, 19, 20, 21]}, {"childIndexes": [10, 11, 12], "directoryIndexes": [18, 19, 24, 25, 26], "name": "Opacity Micro-Map SDK", "parentIndex": 0, "targetIndexes": [13, 45, 46]}, {"directoryIndexes": [20, 21], "name": "glm", "parentIndex": 9, "targetIndexes": [11, 37]}, {"directoryIndexes": [22], "name": "LZ4", "parentIndex": 9, "targetIndexes": [10, 40]}, {"directoryIndexes": [23], "name": "xxHash", "parentIndex": 9, "targetIndexes": [7, 50]}, {"directoryIndexes": [28, 29], "name": "cxxopts", "parentIndex": 0, "targetIndexes": [6]}], "targets": [{"directoryIndex": 13, "id": "ALL_BUILD::@36095ebc80295b0f7532", "jsonFile": "target-ALL_BUILD-Release-80211b7c0825a519b9e4.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 12, "id": "ALL_BUILD::@1e4fb8cca40b12049cc4", "jsonFile": "target-ALL_BUILD-Release-43cc024797cb0c40adb3.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-4a07a22af554f19f8248.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 16, "id": "ALL_BUILD::@239232d11973c9d5b0a8", "jsonFile": "target-ALL_BUILD-Release-2d080ff4ab75b280fa31.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 4, "id": "ALL_BUILD::@10175f99a46424ae9e27", "jsonFile": "target-ALL_BUILD-Release-c402e7484d2307ebdf2d.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 10, "id": "ALL_BUILD::@8f637d5d2c9d0ba648a2", "jsonFile": "target-ALL_BUILD-Release-cf78b8f5e180b52ceb47.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 28, "id": "ALL_BUILD::@eff4f5fe60f26418d6fe", "jsonFile": "target-ALL_BUILD-Release-09a0418300ed8b245264.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 23, "id": "ALL_BUILD::@6461965e4d567125b4d4", "jsonFile": "target-ALL_BUILD-Release-0a3d00d4da14670d404f.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 11, "id": "ALL_BUILD::@b014256a752891a2614a", "jsonFile": "target-ALL_BUILD-Release-821c2a1452f78c0a3f7d.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 2, "id": "ALL_BUILD::@3f75b14119991a9702cc", "jsonFile": "target-ALL_BUILD-Release-d5dc1a876ec512fea8d5.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 22, "id": "ALL_BUILD::@056654a294b82f1b5e8a", "jsonFile": "target-ALL_BUILD-Release-ddee3f1f81f798f78499.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 20, "id": "ALL_BUILD::@62d76fcc7bb8624efaf3", "jsonFile": "target-ALL_BUILD-Release-8a61172334690d0a236e.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 8, "id": "ALL_BUILD::@a4c578d98de775ea62cc", "jsonFile": "target-ALL_BUILD-Release-0ffa6d5ac2a8477ad49f.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 18, "id": "ALL_BUILD::@9a8fb8386807ee1c7036", "jsonFile": "target-ALL_BUILD-Release-b7e455b17a7667420db1.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 13, "id": "Continuous::@36095ebc80295b0f7532", "jsonFile": "target-Continuous-Release-b205acae3a56576de4d6.json", "name": "Continuous", "projectIndex": 7}, {"directoryIndex": 15, "id": "CopyStreamlineDLLs::@2c60e108f346c3843468", "jsonFile": "target-CopyStreamlineDLLs-Release-83c92bc13de002537440.json", "name": "CopyStreamlineDLLs", "projectIndex": 1}, {"directoryIndex": 13, "id": "DirectX-Guids::@36095ebc80295b0f7532", "jsonFile": "target-DirectX-Guids-Release-cb8a92a97cdb0daec6f3.json", "name": "DirectX-Guids", "projectIndex": 7}, {"directoryIndex": 13, "id": "DirectX-Headers::@36095ebc80295b0f7532", "jsonFile": "target-DirectX-Headers-Release-2586a2bd5c47575457c2.json", "name": "DirectX-Headers", "projectIndex": 7}, {"directoryIndex": 13, "id": "Experimental::@36095ebc80295b0f7532", "jsonFile": "target-Experimental-Release-f07206e16a37c51ca6e1.json", "name": "Experimental", "projectIndex": 7}, {"directoryIndex": 17, "id": "MathLib::@6b8530d6352516f31722", "jsonFile": "target-MathLib-Release-4b63d5733fadfcc8cc81.json", "name": "MathLib", "projectIndex": 8}, {"directoryIndex": 16, "id": "NRD::@239232d11973c9d5b0a8", "jsonFile": "target-NRD-Release-dbdfaeddb6a44f146705.json", "name": "NRD", "projectIndex": 8}, {"directoryIndex": 16, "id": "NRDIntegration::@239232d11973c9d5b0a8", "jsonFile": "target-NRDIntegration-Release-70fcb2f845121850bc0e.json", "name": "NRDIntegration", "projectIndex": 8}, {"directoryIndex": 13, "id": "Nightly::@36095ebc80295b0f7532", "jsonFile": "target-Nightly-Release-ce3f198bdd110e601ce1.json", "name": "Nightly", "projectIndex": 7}, {"directoryIndex": 13, "id": "NightlyMemoryCheck::@36095ebc80295b0f7532", "jsonFile": "target-NightlyMemoryCheck-Release-15ddf0daaec95cf9ffe4.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 7}, {"directoryIndex": 27, "id": "Rtxdi::@62fca8016d331f72548b", "jsonFile": "target-Rtxdi-Release-fb7230d72797a0e4be1a.json", "name": "Rtxdi", "projectIndex": 0}, {"directoryIndex": 30, "id": "Rtxpt::@b012b6e63bd5043e3e8b", "jsonFile": "target-Rtxpt-Release-410e00b3f8cfb467ee50.json", "name": "Rtxpt", "projectIndex": 0}, {"directoryIndex": 30, "id": "ShaderDynamicAssets::@b012b6e63bd5043e3e8b", "jsonFile": "target-ShaderDynamicAssets-Release-807249cd2db5216eadbd.json", "name": "ShaderDynamicAssets", "projectIndex": 0}, {"directoryIndex": 30, "id": "ShaderDynamicAssets_CopyAlways::@b012b6e63bd5043e3e8b", "jsonFile": "target-ShaderDynamicAssets_CopyAlways-Release-6656de469627b62d2ea6.json", "name": "ShaderDynamicAssets_CopyAlways", "projectIndex": 0}, {"directoryIndex": 11, "id": "ShaderMake::@b014256a752891a2614a", "jsonFile": "target-ShaderMake-Release-a18f83b14717bc357c37.json", "name": "ShaderMake", "projectIndex": 5}, {"directoryIndex": 11, "id": "ShaderMakeBlob::@b014256a752891a2614a", "jsonFile": "target-ShaderMakeBlob-Release-1cc2973791e9f22d8dc6.json", "name": "ShaderMakeBlob", "projectIndex": 5}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-0028a7e95cc1498b23c4.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 2, "id": "donut_app::@3f75b14119991a9702cc", "jsonFile": "target-donut_app-Release-538238fd87e4bdc69b1b.json", "name": "donut_app", "projectIndex": 1}, {"directoryIndex": 2, "id": "donut_core::@3f75b14119991a9702cc", "jsonFile": "target-donut_core-Release-c848959dd69bd5726f60.json", "name": "donut_core", "projectIndex": 1}, {"directoryIndex": 2, "id": "donut_engine::@3f75b14119991a9702cc", "jsonFile": "target-donut_engine-Release-6c7b1286d2e736dad1c8.json", "name": "donut_engine", "projectIndex": 1}, {"directoryIndex": 2, "id": "donut_render::@3f75b14119991a9702cc", "jsonFile": "target-donut_render-Release-ce3b14828fa50a83df67.json", "name": "donut_render", "projectIndex": 1}, {"directoryIndex": 14, "id": "donut_shaders::@111d5be078c7280583c9", "jsonFile": "target-donut_shaders-Release-9adf001a034f6d5fd092.json", "name": "donut_shaders", "projectIndex": 1}, {"directoryIndex": 9, "id": "glfw::@cde7473d92b8787751e5", "jsonFile": "target-glfw-Release-f16bb5a898c5a8a02920.json", "name": "glfw", "projectIndex": 3}, {"directoryIndex": 21, "id": "glm::@3d8cf37488c348ec8d16", "jsonFile": "target-glm-Release-d65ae18702febd0a9457.json", "name": "glm", "projectIndex": 10}, {"directoryIndex": 3, "id": "imgui::@c8be3c763eb3df35a7f3", "jsonFile": "target-imgui-Release-16730f849ebbfccd9330.json", "name": "imgui", "projectIndex": 1}, {"directoryIndex": 6, "id": "jsoncpp_static::@dc903dbc33f565e211e1", "jsonFile": "target-jsoncpp_static-Release-624d8976f45a014072dc.json", "name": "jsoncpp_static", "projectIndex": 2}, {"directoryIndex": 22, "id": "lz4_static::@056654a294b82f1b5e8a", "jsonFile": "target-lz4_static-Release-c315e5951cfcc20cb995.json", "name": "lz4_static", "projectIndex": 11}, {"directoryIndex": 10, "id": "miniz::@8f637d5d2c9d0ba648a2", "jsonFile": "target-miniz-Release-0f2fdc55ee3dca9c38ef.json", "name": "miniz", "projectIndex": 4}, {"directoryIndex": 1, "id": "nrd_shaders::@6dcf5859e8d89a406b62", "jsonFile": "target-nrd_shaders-Release-db54aae25abf7738e6e9.json", "name": "nrd_shaders", "projectIndex": 0}, {"directoryIndex": 12, "id": "nvrhi::@1e4fb8cca40b12049cc4", "jsonFile": "target-nvrhi-Release-0921c4d1f7e138e69edf.json", "name": "nvrhi", "projectIndex": 6}, {"directoryIndex": 12, "id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4", "jsonFile": "target-nvrhi_d3d12-Release-6b84f26932cc9f32daa6.json", "name": "nvrhi_d3d12", "projectIndex": 6}, {"directoryIndex": 25, "id": "omm-gpu-nvrhi::@beed3f881e98d782abb3", "jsonFile": "target-omm-gpu-nvrhi-Release-2927c80b00c412722880.json", "name": "omm-gpu-nvrhi", "projectIndex": 9}, {"directoryIndex": 24, "id": "omm-lib::@415ec9098909d7ea5fcd", "jsonFile": "target-omm-lib-Release-2ceb90eaf1d54ce1cb0e.json", "name": "omm-lib", "projectIndex": 9}, {"directoryIndex": 1, "id": "omm_shaders::@6dcf5859e8d89a406b62", "jsonFile": "target-omm_shaders-Release-b7276ef8a63e8496176d.json", "name": "omm_shaders", "projectIndex": 0}, {"directoryIndex": 30, "id": "rtxpt_shaders::@b012b6e63bd5043e3e8b", "jsonFile": "target-rtxpt_shaders-Release-eb90fdce0b37412c894a.json", "name": "rtxpt_shaders", "projectIndex": 0}, {"directoryIndex": 9, "id": "update_mappings::@cde7473d92b8787751e5", "jsonFile": "target-update_mappings-Release-70446d4fb60ffc15c12b.json", "name": "update_mappings", "projectIndex": 3}, {"directoryIndex": 23, "id": "xxhash::@6461965e4d567125b4d4", "jsonFile": "target-xxhash-Release-b7573d909045837850db.json", "name": "xxhash", "projectIndex": 12}]}], "kind": "codemodel", "paths": {"build": "D:/RTXPT/cmake-build-release-visual-studio", "source": "D:/RTXPT"}, "version": {"major": 2, "minor": 8}}