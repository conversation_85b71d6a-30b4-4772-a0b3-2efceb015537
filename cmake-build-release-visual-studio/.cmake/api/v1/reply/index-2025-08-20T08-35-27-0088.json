{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cmake.exe", "cpack": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cpack.exe", "ctest": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/ctest.exe", "root": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 2, "string": "4.0.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-368d0ff04b4176c6b63e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-cf885686fb871dbcc966.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6424b009a8c50f4860b0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-d2f27347bcbe15ef00b1.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-cf885686fb871dbcc966.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6424b009a8c50f4860b0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-368d0ff04b4176c6b63e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, "toolchains-v1": {"jsonFile": "toolchains-v1-d2f27347bcbe15ef00b1.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}