{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Omm/libraries/omm-gpu-nvrhi/Release/omm-gpu-nvrhi.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "target_include_directories"], "files": ["External/Omm/libraries/omm-gpu-nvrhi/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 15, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 55, "parent": 3}, {"command": 3, "file": 0, "line": 14, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++20 -MT"}, {"backtrace": 2, "fragment": "-openmp"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 5, "path": "D:/RTXPT/External/Omm/libraries/omm-gpu-nvrhi"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/include"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/glm"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/stb"}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial/.."}, {"backtrace": 2, "path": "D:/RTXPT/External/Omm/external/lz4/build/cmake/../../lib"}, {"backtrace": 2, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/RTXPT/External/nvapi"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 2, "id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4"}, {"backtrace": 2, "id": "nvrhi::@1e4fb8cca40b12049cc4"}, {"backtrace": 2, "id": "DirectX-Headers::@36095ebc80295b0f7532"}, {"backtrace": 2, "id": "DirectX-Guids::@36095ebc80295b0f7532"}, {"backtrace": 2, "id": "lz4_static::@056654a294b82f1b5e8a"}, {"backtrace": 2, "id": "xxhash::@6461965e4d567125b4d4"}, {"backtrace": 2, "id": "glm::@3d8cf37488c348ec8d16"}, {"backtrace": 2, "id": "omm-lib::@415ec9098909d7ea5fcd"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Omm"}, "id": "omm-gpu-nvrhi::@beed3f881e98d782abb3", "name": "omm-gpu-nvrhi", "nameOnDisk": "omm-gpu-nvrhi.lib", "paths": {"build": "External/Omm/libraries/omm-gpu-nvrhi", "source": "External/Omm/libraries/omm-gpu-nvrhi"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "path": "External/Omm/libraries/omm-gpu-nvrhi/omm-gpu-nvrhi.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-gpu-nvrhi/omm-gpu-nvrhi.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}