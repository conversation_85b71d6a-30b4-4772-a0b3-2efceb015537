{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/nvrhi/Debug/nvrhi.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_compile_definitions", "add_definitions", "target_include_directories", "target_sources"], "files": ["External/Donut/nvrhi/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 338, "parent": 0}, {"command": 2, "file": 0, "line": 222, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 55, "parent": 4}, {"command": 4, "file": 0, "line": 216, "parent": 0}, {"command": 5, "file": 0, "line": 211, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP  /MP /W4 /MP /Zi /Ob0 /Od /RTC1 /D_ITERATOR_DEBUG_LEVEL=1 -D_DEBUG -std:c++17 -MTd"}, {"fragment": "-WX"}], "defines": [{"backtrace": 3, "define": "NVRHI_WITH_AFTERMATH=0"}, {"backtrace": 5, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 6, "path": "D:/RTXPT/External/Donut/nvrhi/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [7, 8, 9, 11, 12, 15, 16, 19]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "nvrhi::@1e4fb8cca40b12049cc4", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "D:/RTXPT/cmake-build-release-visual-studio/install"}}, "name": "nvrhi", "nameOnDisk": "nvrhi.lib", "paths": {"build": "External/Donut/nvrhi", "source": "External/Donut/nvrhi"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 10, 14, 17, 18]}, {"name": "Source Files", "sourceIndexes": [7, 8, 9, 11, 12, 15, 16, 19]}, {"name": "", "sourceIndexes": [13]}], "sources": [{"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/nvrhi.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/nvrhiHLSL.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/utils.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/common/containers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/common/misc.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/common/resource.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/nvrhi/include/nvrhi/common/aftermath.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/format-info.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/misc.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/state-tracking.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Donut/nvrhi/src/common/state-tracking.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/utils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/aftermath.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Donut/nvrhi/tools/nvrhi.natvis", "sourceGroupIndex": 2}, {"backtrace": 7, "path": "External/Donut/nvrhi/include/nvrhi/validation.h", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/validation/validation-commandlist.cpp", "sourceGroupIndex": 1}, {"backtrace": 7, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/validation/validation-device.cpp", "sourceGroupIndex": 1}, {"backtrace": 7, "path": "External/Donut/nvrhi/src/validation/validation-backend.h", "sourceGroupIndex": 0}, {"backtrace": 7, "path": "External/Donut/nvrhi/src/common/sparse-bitset.h", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/src/common/sparse-bitset.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}