{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/Release/donut_core.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_definitions", "add_definitions", "target_include_directories", "target_sources"], "files": ["External/Donut/donut-core.cmake", "External/Donut/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 93, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 38, "parent": 2}, {"command": 2, "file": 0, "line": 40, "parent": 2}, {"command": 2, "file": 0, "line": 60, "parent": 2}, {"command": 3, "file": 0, "line": 65, "parent": 2}, {"command": 3, "file": 0, "line": 51, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 55, "parent": 8}, {"command": 5, "file": 0, "line": 39, "parent": 2}, {"command": 6, "file": 0, "line": 47, "parent": 2}, {"command": 6, "file": 0, "line": 61, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 6, "define": "DONUT_WITH_MINIZ"}, {"backtrace": 5, "define": "MINIZ_STATIC_DEFINE"}, {"backtrace": 7, "define": "NOMINMAX"}, {"backtrace": 9, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 7, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 10, "path": "D:/RTXPT/External/Donut/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}, {"backtrace": 5, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 5, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], "standard": "17"}, "sourceIndexes": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35]}], "dependencies": [{"backtrace": 4, "id": "jsoncpp_static::@dc903dbc33f565e211e1"}, {"backtrace": 5, "id": "miniz::@8f637d5d2c9d0ba648a2"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Donut"}, "id": "donut_core::@3f75b14119991a9702cc", "name": "donut_core", "nameOnDisk": "donut_core.lib", "paths": {"build": "External/Donut", "source": "External/Donut"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 32, 34]}, {"name": "Source Files", "sourceIndexes": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35]}], "sources": [{"backtrace": 3, "path": "External/Donut/include/donut/core/chunk/chunk.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/chunk/chunkFile.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/circular_buffer.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/json.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/log.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/affine.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/basics.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/box.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/color.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/frustum.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/math.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/matrix.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/quat.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/sphere.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/math/vector.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/string_utils.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/vfs/Compression.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/vfs/TarFile.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/core/vfs/VFS.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/chunk/chunkFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/chunk/chunkReader.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/chunk/chunkWriter.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/json.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/log.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/math/color.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/math/frustum.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/math/matrix.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/math/vector.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/string_utils.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/vfs/Compression.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/vfs/TarFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/core/vfs/VFS.cpp", "sourceGroupIndex": 1}, {"backtrace": 11, "path": "External/Donut/include/donut/core/vfs/WinResFS.h", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "External/Donut/src/core/vfs/WinResFS.cpp", "sourceGroupIndex": 1}, {"backtrace": 12, "path": "External/Donut/include/donut/core/vfs/ZipFile.h", "sourceGroupIndex": 0}, {"backtrace": 12, "compileGroupIndex": 0, "path": "External/Donut/src/core/vfs/ZipFile.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}