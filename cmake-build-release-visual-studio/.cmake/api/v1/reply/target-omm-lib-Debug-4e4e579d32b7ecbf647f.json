{"artifacts": [{"path": "D:/RTXPT/bin/omm-lib.dll"}, {"path": "External/Omm/libraries/omm-lib/Debug/omm-lib.lib"}, {"path": "D:/RTXPT/bin/omm-lib.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_compile_options", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["External/Omm/libraries/omm-lib/CMakeLists.txt", "External/Omm/external/lz4/build/cmake/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 84, "parent": 0}, {"command": 1, "file": 0, "line": 172, "parent": 0}, {"command": 2, "file": 0, "line": 95, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 134, "parent": 4}, {"command": 3, "file": 0, "line": 153, "parent": 0}, {"command": 2, "file": 0, "line": 89, "parent": 0}, {"command": 4, "file": 0, "line": 121, "parent": 0}, {"command": 4, "file": 0, "line": 131, "parent": 0}, {"command": 5, "file": 0, "line": 61, "parent": 0}, {"file": 2}, {"command": 5, "file": 2, "line": 55, "parent": 11}, {"command": 6, "file": 0, "line": 98, "parent": 0}, {"command": 6, "file": 0, "line": 99, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /Zi /Ob0 /Od /RTC1 /D_ITERATOR_DEBUG_LEVEL=1 -std:c++20 -MTd"}, {"backtrace": 6, "fragment": "/fp:fast"}, {"backtrace": 7, "fragment": "-openmp"}, {"fragment": "-WX"}], "defines": [{"backtrace": 8, "define": "NOMINMAX"}, {"backtrace": 9, "define": "OMM_API=extern \"C\" __declspec(dllexport)"}, {"backtrace": 10, "define": "OMM_VK_B_SHIFT=300"}, {"backtrace": 10, "define": "OMM_VK_S_SHIFT=100"}, {"backtrace": 10, "define": "OMM_VK_T_SHIFT=200"}, {"backtrace": 10, "define": "OMM_VK_U_SHIFT=400"}, {"backtrace": 12, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 8, "define": "UNICODE"}, {"backtrace": 8, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 8, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 8, "define": "_UNICODE"}, {"define": "omm_lib_EXPORTS"}], "includes": [{"backtrace": 13, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/include"}, {"backtrace": 14, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/shaders"}, {"backtrace": 14, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/src"}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/glm"}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/stb"}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial/.."}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/lz4/build/cmake/../../lib"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1, 3, 6, 12, 14, 18, 21]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 8, "define": "NOMINMAX"}, {"backtrace": 9, "define": "OMM_API=extern \"C\" __declspec(dllexport)"}, {"backtrace": 10, "define": "OMM_VK_B_SHIFT=300"}, {"backtrace": 10, "define": "OMM_VK_S_SHIFT=100"}, {"backtrace": 10, "define": "OMM_VK_T_SHIFT=200"}, {"backtrace": 10, "define": "OMM_VK_U_SHIFT=400"}, {"backtrace": 12, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 8, "define": "UNICODE"}, {"backtrace": 8, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 8, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 8, "define": "_UNICODE"}, {"define": "omm_lib_EXPORTS"}], "includes": [{"backtrace": 13, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/include"}, {"backtrace": 14, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/shaders"}, {"backtrace": 14, "path": "D:/RTXPT/External/Omm/libraries/omm-lib/src"}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/glm"}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/stb"}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial/.."}, {"backtrace": 3, "path": "D:/RTXPT/External/Omm/external/lz4/build/cmake/../../lib"}], "language": "RC", "sourceIndexes": [24]}], "dependencies": [{"backtrace": 3, "id": "lz4_static::@056654a294b82f1b5e8a"}, {"backtrace": 3, "id": "xxhash::@6461965e4d567125b4d4"}, {"backtrace": 3, "id": "glm::@3d8cf37488c348ec8d16"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Omm"}, "id": "omm-lib::@415ec9098909d7ea5fcd", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "bin"}], "prefix": {"path": "D:/RTXPT/cmake-build-release-visual-studio/install"}}, "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 3, "fragment": "..\\..\\external\\glm\\glm\\Debug\\glm.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "..\\..\\external\\xxHash\\cmake_unofficial\\Debug\\xxhash.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\external\\lz4\\build\\cmake\\Debug\\lz4.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "omm-lib", "nameOnDisk": "omm-lib.dll", "paths": {"build": "External/Omm/libraries/omm-lib", "source": "External/Omm/libraries/omm-lib"}, "sourceGroups": [{"name": "Sources", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}, {"name": "Include", "sourceIndexes": [25, 26]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/bake.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/bake_cpu_impl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/bake_cpu_impl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/bake_gpu_impl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/bake_gpu_impl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/bake_kernels_cpu.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/debug_impl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/debug_impl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/defines.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/log.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/omm_handle.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/resource.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/serialize_impl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/serialize_impl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/shader_bindings.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/shader_bindings.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/shader_bindings_expand.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/shader_registry.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/stb_lib.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/std_allocator.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/std_containers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/libraries/omm-lib/src/texture_impl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/texture_impl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/src/version.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "External/Omm/libraries/omm-lib/src/bake.rc", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/include/omm.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Omm/libraries/omm-lib/include/omm.hpp", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}