{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/nvrhi/thirdparty/DirectX-Headers/Release/DirectX-Headers.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "target_include_directories"], "files": ["External/Donut/nvrhi/thirdparty/DirectX-Headers/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 55, "parent": 2}, {"command": 2, "file": 0, "line": 41, "parent": 0}, {"command": 2, "file": 0, "line": 37, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP  /MP /W4 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++14 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 3, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 4, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include/directx"}, {"backtrace": 5, "isSystem": true, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "DirectX-Headers::@36095ebc80295b0f7532", "name": "DirectX-Headers", "nameOnDisk": "DirectX-Headers.lib", "paths": {"build": "External/Donut/nvrhi/thirdparty/DirectX-Headers", "source": "External/Donut/nvrhi/thirdparty/DirectX-Headers"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/nvrhi/thirdparty/DirectX-Headers/src/d3dx12_property_format_table.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}