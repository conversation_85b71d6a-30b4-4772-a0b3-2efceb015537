{"artifacts": [{"path": "D:/RTXPT/bin/NRD.dll"}, {"path": "D:/RTXPT/bin/Release/NRD.lib"}, {"path": "D:/RTXPT/bin/NRD.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_options", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["External/Nrd/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 154, "parent": 0}, {"command": 1, "file": 0, "line": 176, "parent": 0}, {"command": 2, "file": 0, "line": 179, "parent": 0}, {"command": 3, "file": 0, "line": 178, "parent": 0}, {"command": 3, "file": 0, "line": 162, "parent": 0}, {"file": 1}, {"command": 4, "file": 1, "line": 55, "parent": 6}, {"command": 5, "file": 0, "line": 177, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /O2 /Ob2 /DNDEBUG /Zi /Zi /Zi -std:c++17 -MT"}, {"backtrace": 3, "fragment": "/W4"}, {"backtrace": 3, "fragment": "/WX"}, {"backtrace": 3, "fragment": "/wd4324"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "NOMINMAX"}, {"backtrace": 5, "define": "NRD_API=extern \"C\" __declspec(dllexport)"}, {"define": "NRD_EXPORTS"}, {"backtrace": 4, "define": "NRD_NORMAL_ENCODING=2"}, {"backtrace": 4, "define": "NRD_ROUGHNESS_ENCODING=1"}, {"backtrace": 7, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 4, "define": "UNICODE"}, {"backtrace": 4, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 4, "define": "_ENFORCE_MATCHING_ALLOCATORS=0"}, {"backtrace": 4, "define": "_UNICODE"}], "includes": [{"backtrace": 8, "path": "D:/RTXPT/External/Nrd/Include"}, {"backtrace": 2, "path": "D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-src/."}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 2, 3, 4, 5, 7, 9]}, {"compileCommandFragments": [{"fragment": "-DWIN32"}], "defines": [{"backtrace": 4, "define": "NOMINMAX"}, {"backtrace": 5, "define": "NRD_API=extern \"C\" __declspec(dllexport)"}, {"define": "NRD_EXPORTS"}, {"backtrace": 4, "define": "NRD_NORMAL_ENCODING=2"}, {"backtrace": 4, "define": "NRD_ROUGHNESS_ENCODING=1"}, {"backtrace": 7, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 4, "define": "UNICODE"}, {"backtrace": 4, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 4, "define": "_ENFORCE_MATCHING_ALLOCATORS=0"}, {"backtrace": 4, "define": "_UNICODE"}], "includes": [{"backtrace": 8, "path": "D:/RTXPT/External/Nrd/Include"}, {"backtrace": 2, "path": "D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-src/."}], "language": "RC", "sourceIndexes": [29]}], "dependencies": [{"backtrace": 2, "id": "MathLib::@6b8530d6352516f31722"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "NRD"}, "id": "NRD::@239232d11973c9d5b0a8", "link": {"commandFragments": [{"fragment": "/machine:x64 /INCREMENTAL:NO /DEBUG /DEBUG /DEBUG /OPT:REF /OPT:ICF", "role": "flags"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "NRD", "nameOnDisk": "NRD.dll", "paths": {"build": "External/Nrd", "source": "External/Nrd"}, "sourceGroups": [{"name": "Source", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}, {"name": "Denoisers", "sourceIndexes": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]}, {"name": "Resources", "sourceIndexes": [29, 30]}, {"name": "Include", "sourceIndexes": [31, 32, 33]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/InstanceImpl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Nrd/Source/InstanceImpl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/Reblur.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/Reference.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/Relax.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/Sigma.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Nrd/Source/StdAllocator.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/Timer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Nrd/Source/Timer.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Nrd/Source/Wrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_Diffuse.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_DiffuseDirectionalOcclusion.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_DiffuseOcclusion.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_DiffuseSh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_DiffuseSpecular.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_DiffuseSpecularOcclusion.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_DiffuseSpecularSh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_Specular.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_SpecularOcclusion.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reblur_SpecularSh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Reference.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Relax_Diffuse.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Relax_DiffuseSh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Relax_DiffuseSpecular.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Relax_DiffuseSpecularSh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Relax_Specular.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Relax_SpecularSh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Sigma_Shadow.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Nrd/Source/Denoisers/Sigma_ShadowTranslucency.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "External/Nrd/Resources/NRD.rc", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Nrd/Resources/Version.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Nrd/Include/NRD.h", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Nrd/Include/NRDDescs.h", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Nrd/Include/NRDSettings.h", "sourceGroupIndex": 3}], "type": "SHARED_LIBRARY"}