{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/ShaderMake/Release/ShaderMakeBlob.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "add_definitions", "target_include_directories"], "files": ["External/Donut/ShaderMake/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 64, "parent": 0}, {"command": 1, "file": 0, "line": 69, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 55, "parent": 3}, {"command": 3, "file": 0, "line": 68, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"backtrace": 2, "fragment": "/W4"}, {"backtrace": 2, "fragment": "/WX"}, {"backtrace": 2, "fragment": "/wd4324"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 5, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "ShaderMakeBlob::@b014256a752891a2614a", "name": "ShaderMakeBlob", "nameOnDisk": "ShaderMakeBlob.lib", "paths": {"build": "External/Donut/ShaderMake", "source": "External/Donut/ShaderMake"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "path": "External/Donut/ShaderMake/include/ShaderMake/ShaderBlob.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/ShaderMake/src/ShaderBlob.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}