{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-release-visual-studio/CMakeFiles/4.0.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-release-visual-studio/CMakeFiles/4.0.2/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-release-visual-studio/CMakeFiles/4.0.2/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "cmake-build-release-visual-studio/CMakeFiles/4.0.2/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"path": "External/CMakeLists.txt"}, {"path": "External/Donut/compileshaders.cmake"}, {"path": "External/Donut/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"path": "External/Donut/donut-core.cmake"}, {"path": "External/Donut/donut-engine.cmake"}, {"path": "External/Donut/donut-render.cmake"}, {"path": "External/Donut/donut-app.cmake"}, {"path": "External/Donut/cmake/FindSTREAMLINE.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "External/Donut/thirdparty/CMakeLists.txt"}, {"path": "External/Donut/thirdparty/imgui.cmake"}, {"path": "External/Donut/thirdparty/jsoncpp.cmake"}, {"path": "External/Donut/thirdparty/jsoncpp/CMakeLists.txt"}, {"path": "External/Donut/thirdparty/jsoncpp/include/PreventInSourceBuilds.cmake"}, {"path": "External/Donut/thirdparty/jsoncpp/include/PreventInBuildInstalls.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"path": "External/Donut/thirdparty/jsoncpp/version.in"}, {"path": "External/Donut/thirdparty/jsoncpp/src/CMakeLists.txt"}, {"path": "External/Donut/thirdparty/jsoncpp/src/lib_json/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckTypeSize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckStructHasMember.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCXXSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckSymbolExists.cmake"}, {"path": "External/Donut/thirdparty/jsoncpp/include/CMakeLists.txt"}, {"path": "External/Donut/thirdparty/glfw/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake"}, {"path": "External/Donut/thirdparty/glfw/CMake/glfw3Config.cmake.in"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"}, {"path": "External/Donut/thirdparty/glfw/src/CMakeLists.txt"}, {"path": "External/Donut/thirdparty/glfw/CMake/glfw3.pc.in"}, {"path": "External/Donut/thirdparty/miniz/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GenerateExportHeader.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckFlagCommonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/exportheader.cmake.in"}, {"path": "External/Donut/thirdparty/miniz/miniz.pc.in"}, {"path": "External/Donut/ShaderMake/CMakeLists.txt"}, {"path": "External/Donut/nvrhi/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"path": "External/Donut/nvrhi/cmake/FindNVAPI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "External/Donut/nvrhi/thirdparty/DirectX-Headers/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CTestUseLaunchers.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CTestTargets.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/DartConfiguration.tcl.in"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"path": "External/Donut/shaders/CMakeLists.txt"}, {"path": "External/Donut/compileshaders.cmake"}, {"path": "External/Streamline/CMakeLists.txt"}, {"path": "External/Nrd/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/ExternalProject/shared_internal_commands.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindGit.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent/CMakeLists.cmake.in"}, {"isGenerated": true, "path": "cmake-build-release-visual-studio/_deps/mathlib-src/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/ExternalProject/shared_internal_commands.cmake"}, {"path": "External/Omm/CMakeLists.txt"}, {"path": "External/Omm/external/CMakeLists.txt"}, {"path": "External/Omm/external/glm/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"path": "External/Omm/external/glm/glm/CMakeLists.txt"}, {"path": "External/Omm/external/lz4/build/cmake/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"path": "External/Omm/external/lz4/lib/liblz4.pc.in"}, {"path": "External/Omm/external/xxHash/cmake_unofficial/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CheckCCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CPack.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CPackComponent.cmake"}, {"isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Templates/CPackConfig.cmake.in"}, {"isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Templates/CPackConfig.cmake.in"}, {"path": "External/Omm/libraries/omm-lib/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindOpenMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "External/Omm/libraries/omm-gpu-nvrhi/CMakeLists.txt"}, {"path": "External/Omm/support/scripts/CMakeLists.txt"}, {"path": "External/Rtxdi/CMakeLists.txt"}, {"path": "External/cxxopts/CMakeLists.txt"}, {"path": "External/cxxopts/cmake/cxxopts.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"path": "External/cxxopts/include/CMakeLists.txt"}, {"path": "Rtxpt/CMakeLists.txt"}, {"path": "External/Donut/compileshaders.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/RTXPT/cmake-build-release-visual-studio", "source": "D:/RTXPT"}, "version": {"major": 1, "minor": 1}}