{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/Release/donut_app.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_dependencies", "target_compile_definitions", "add_definitions", "target_include_directories", "target_sources"], "files": ["External/Donut/donut-app.cmake", "External/Donut/CMakeLists.txt", "External/Streamline/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 97, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 34, "parent": 2}, {"command": 2, "file": 0, "line": 79, "parent": 2}, {"command": 2, "file": 0, "line": 92, "parent": 2}, {"file": 2}, {"command": 3, "file": 2, "line": 126, "parent": 6}, {"command": 3, "file": 0, "line": 118, "parent": 2}, {"command": 4, "file": 0, "line": 113, "parent": 2}, {"command": 4, "file": 0, "line": 109, "parent": 2}, {"command": 4, "file": 0, "line": 110, "parent": 2}, {"file": 3}, {"command": 5, "file": 3, "line": 55, "parent": 12}, {"command": 4, "file": 0, "line": 59, "parent": 2}, {"command": 4, "file": 0, "line": 62, "parent": 2}, {"command": 4, "file": 0, "line": 56, "parent": 2}, {"command": 4, "file": 0, "line": 65, "parent": 2}, {"command": 4, "file": 0, "line": 74, "parent": 2}, {"command": 6, "file": 0, "line": 35, "parent": 2}, {"command": 6, "file": 0, "line": 40, "parent": 2}, {"command": 2, "file": 0, "line": 41, "parent": 2}, {"command": 7, "file": 0, "line": 39, "parent": 2}, {"command": 7, "file": 0, "line": 90, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /O2 /Ob2 /DNDEBUG /Zi /Zi -std:c++17 -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 9, "define": "DONUT_FORCE_DISCRETE_GPU=0"}, {"backtrace": 10, "define": "DONUT_WITH_AFTERMATH=0"}, {"backtrace": 4, "define": "DONUT_WITH_DX11=0"}, {"backtrace": 4, "define": "DONUT_WITH_DX12=1"}, {"backtrace": 4, "define": "DONUT_WITH_MINIZ"}, {"backtrace": 4, "define": "DONUT_WITH_STATIC_SHADERS=0"}, {"backtrace": 11, "define": "DONUT_WITH_STREAMLINE=1"}, {"backtrace": 4, "define": "DONUT_WITH_TASKFLOW"}, {"backtrace": 4, "define": "DONUT_WITH_TINYEXR"}, {"backtrace": 4, "define": "DONUT_WITH_VULKAN=0"}, {"backtrace": 4, "define": "MINIZ_STATIC_DEFINE"}, {"backtrace": 4, "define": "NOMINMAX"}, {"backtrace": 13, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 14, "define": "STREAMLINE_FEATURE_DLSS_FG"}, {"backtrace": 15, "define": "STREAMLINE_FEATURE_DLSS_RR"}, {"backtrace": 16, "define": "STREAMLINE_FEATURE_DLSS_SR"}, {"backtrace": 17, "define": "STREAMLINE_FEATURE_IMGUI"}, {"backtrace": 18, "define": "STREAMLINE_FEATURE_REFLEX"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/RTXPT/External/Donut/include"}, {"backtrace": 20, "path": "D:/RTXPT/External/Donut/src/app/streamline"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/stb"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/cgltf"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/taskflow"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/tinyexr"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/glfw/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/imgui"}, {"backtrace": 21, "isSystem": true, "path": "D:/RTXPT/External/Streamline/include"}, {"backtrace": 5, "isSystem": true, "path": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include"}, {"backtrace": 5, "isSystem": true, "path": "D:/RTXPT/External/nvapi"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], "standard": "17"}, "sourceIndexes": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}], "dependencies": [{"backtrace": 4, "id": "donut_core::@3f75b14119991a9702cc"}, {"backtrace": 4, "id": "imgui::@c8be3c763eb3df35a7f3"}, {"backtrace": 4, "id": "jsoncpp_static::@dc903dbc33f565e211e1"}, {"backtrace": 4, "id": "donut_engine::@3f75b14119991a9702cc"}, {"backtrace": 4, "id": "ShaderMakeBlob::@b014256a752891a2614a"}, {"backtrace": 5, "id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4"}, {"backtrace": 4, "id": "miniz::@8f637d5d2c9d0ba648a2"}, {"backtrace": 4, "id": "glfw::@cde7473d92b8787751e5"}, {"backtrace": 4, "id": "nvrhi::@1e4fb8cca40b12049cc4"}, {"backtrace": 5, "id": "DirectX-Headers::@36095ebc80295b0f7532"}, {"backtrace": 5, "id": "DirectX-Guids::@36095ebc80295b0f7532"}, {"backtrace": 7, "id": "CopyStreamlineDLLs::@2c60e108f346c3843468"}, {"backtrace": 8, "id": "donut_shaders::@111d5be078c7280583c9"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Donut"}, "id": "donut_app::@3f75b14119991a9702cc", "name": "donut_app", "nameOnDisk": "donut_app.lib", "paths": {"build": "External/Donut", "source": "External/Donut"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}, {"name": "Source Files", "sourceIndexes": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}], "sources": [{"backtrace": 3, "path": "External/Donut/include/donut/app/AftermathCrashDump.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/ApplicationBase.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/Camera.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/DeviceManager.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/DeviceManager_DX11.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/DeviceManager_DX12.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/DeviceManager_VK.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/MediaFileSystem.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/StreamlineInterface.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/Timer.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/UserInterfaceUtils.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/imgui_console.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/imgui_nvrhi.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/app/imgui_renderer.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/ApplicationBase.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/Camera.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/DeviceManager.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/MediaFileSystem.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/UserInterfaceUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/imgui_console.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/imgui_nvrhi.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/app/imgui_renderer.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "External/Donut/src/app/streamline/StreamlineIntegration.cpp", "sourceGroupIndex": 1}, {"backtrace": 23, "compileGroupIndex": 0, "path": "External/Donut/src/app/dx12/DeviceManager_DX12.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}