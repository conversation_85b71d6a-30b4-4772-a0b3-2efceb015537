{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/thirdparty/miniz/Release/miniz.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "target_include_directories"], "files": ["External/Donut/thirdparty/miniz/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 143, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 55, "parent": 2}, {"command": 2, "file": 0, "line": 157, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG /Zi /Zi -MT"}, {"fragment": "-WX"}], "defines": [{"backtrace": 3, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}], "language": "C", "sourceIndexes": [0, 1, 2, 3]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Third-Party Libraries"}, "id": "miniz::@8f637d5d2c9d0ba648a2", "name": "miniz", "nameOnDisk": "miniz.lib", "paths": {"build": "External/Donut/thirdparty/miniz", "source": "External/Donut/thirdparty/miniz"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/miniz/miniz.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/miniz/miniz_zip.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/miniz/miniz_tinfl.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/miniz/miniz_tdef.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}