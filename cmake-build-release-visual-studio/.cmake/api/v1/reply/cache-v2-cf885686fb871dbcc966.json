{"entries": [{"name": "AMALGAMATE_SOURCES", "properties": [{"name": "HELPSTRING", "value": "Amalgamate sources into miniz.h/c"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_EXAMPLES", "properties": [{"name": "HELPSTRING", "value": "Build examples"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_FUZZERS", "properties": [{"name": "HELPSTRING", "value": "Build fuzz targets"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_HEADER_ONLY", "properties": [{"name": "HELPSTRING", "value": "Build a header-only version"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_NO_STDIO\" Build a without stdio version\"", "properties": [{"name": "HELPSTRING", "value": "OFF"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_SHARED_LIBS", "properties": [{"name": "HELPSTRING", "value": "Build shared libraries"}], "type": "INTERNAL", "value": "OFF"}, {"name": "BUILD_TESTING", "properties": [{"name": "HELPSTRING", "value": "Build the testing tree."}], "type": "BOOL", "value": "ON"}, {"name": "BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build tests"}], "type": "BOOL", "value": "OFF"}, {"name": "BZRCOMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/1softwares/VS2022/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/lib.exe"}, {"name": "CMAKE_ARCHIVE_OUTPUT_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "d:/RTXPT/cmake-build-release-visual-studio"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "2"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "Enable colored diagnostics throughout."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cmake.exe"}, {"name": "CMAKE_CONFIGURATION_TYPES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "Debug;Release"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/bin/ctest.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /GR /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG /Zi /Zi"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS"}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG /Zi /Zi"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/INCREMENTAL:NO /DEBUG /DEBUG"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Visual Studio 17 2022"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": "D:/1softwares/VS2022"}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREADS_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthreads"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREAD_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthread"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "D:/RTXPT"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "default install path"}], "type": "PATH", "value": "D:/RTXPT/cmake-build-release-visual-studio/install"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LIBRARY_OUTPUT_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/1softwares/VS2022/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/link.exe"}, {"name": "CMAKE_LIST_FILE_NAME", "properties": [{"name": "HELPSTRING", "value": "Name of CMakeLists files to read"}], "type": "INTERNAL", "value": "CMakeLists.txt"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_MT-NOTFOUND"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "31"}, {"name": "CMAKE_PDB_OUTPUT_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "RTXPathTracing"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1.9.6"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "9"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "6"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "rc"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.2/bin/cmake/win/x64/share/cmake-4.0"}, {"name": "CMAKE_RUNTIME_OUTPUT_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "D:/RTXPT/bin"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/INCREMENTAL:NO /DEBUG /DEBUG"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "CMAKE_WARN_DEPRECATED", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "OFF"}, {"name": "COMPILER_HAS_DEPRECATED", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_HAS_DEPRECATED"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_HAS_DEPRECATED_ATTR", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_HAS_DEPRECATED_ATTR"}], "type": "INTERNAL", "value": ""}, {"name": "COVERAGE_COMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to the coverage program that CTest uses for performing coverage inspection"}], "type": "FILEPATH", "value": "COVERAGE_COMMAND-NOTFOUND"}, {"name": "COVERAGE_EXTRA_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Extra command line flags to pass to the coverage tool"}], "type": "STRING", "value": "-l"}, {"name": "CPACK_BINARY_7Z", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build 7-Zip packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_BINARY_IFW", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build IFW packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_BINARY_INNOSETUP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build Inno Setup packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_BINARY_NSIS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build NSIS packages"}], "type": "BOOL", "value": "ON"}, {"name": "CPACK_BINARY_NUGET", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build NuGet packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_BINARY_WIX", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build WiX packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_BINARY_ZIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build ZIP packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_SOURCE_7Z", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build 7-Zip source packages"}], "type": "BOOL", "value": "ON"}, {"name": "CPACK_SOURCE_ZIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build ZIP source packages"}], "type": "BOOL", "value": "ON"}, {"name": "CTEST_SUBMIT_RETRY_COUNT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "How many times to retry timed-out CTest submissions."}], "type": "STRING", "value": "3"}, {"name": "CTEST_SUBMIT_RETRY_DELAY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "How long to wait between timed-out CTest submissions."}], "type": "STRING", "value": "5"}, {"name": "CVSCOMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "CVS_UPDATE_OPTIONS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "CXXOPTS_BUILD_EXAMPLES", "properties": [{"name": "HELPSTRING", "value": "Set to ON to build examples"}], "type": "BOOL", "value": "OFF"}, {"name": "CXXOPTS_BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Set to ON to build tests"}], "type": "BOOL", "value": "OFF"}, {"name": "CXXOPTS_ENABLE_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Generate the install target"}], "type": "BOOL", "value": "OFF"}, {"name": "CXXOPTS_ENABLE_WARNINGS", "properties": [{"name": "HELPSTRING", "value": "Add warnings to CMAKE_CXX_FLAGS"}], "type": "BOOL", "value": "OFF"}, {"name": "CXXOPTS_USE_UNICODE_HELP", "properties": [{"name": "HELPSTRING", "value": "Use ICU Unicode library"}], "type": "BOOL", "value": "OFF"}, {"name": "CXXOPTS__VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "3"}, {"name": "CXXOPTS__VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2"}, {"name": "CXXOPTS__VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "DART_TESTING_TIMEOUT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Maximum time allowed before CTest will kill the test."}], "type": "STRING", "value": "1500"}, {"name": "DONUT_EMBED_SHADER_PDBS", "properties": [{"name": "HELPSTRING", "value": "Embed shader PDBs with shader binary files"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_FORCE_DISCRETE_GPU", "properties": [{"name": "HELPSTRING", "value": "Declare symbols to make the OS run the app on discrete GPU on laptops"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_SHADER_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "PATH", "value": "D:/RTXPT/External/Donut/include"}, {"name": "DONUT_STREAMLINE_FETCH_DIR", "properties": [{"name": "HELPSTRING", "value": "Directory to fetch streamline to, empty uses build directory default"}], "type": "STRING", "value": "D:/RTXPT/External/Donut/thirdparty/streamline"}, {"name": "DONUT_STREAMLINE_FETCH_TAG", "properties": [{"name": "HELPSTRING", "value": "Tag of streamline git repo"}], "type": "STRING", "value": ""}, {"name": "DONUT_STREAMLINE_FETCH_URL", "properties": [{"name": "HELPSTRING", "value": "Url to streamline git repo to fetch"}], "type": "STRING", "value": ""}, {"name": "DONUT_STREAMLINE_SEARCH_PATHS", "properties": [{"name": "HELPSTRING", "value": "Search paths for streamline package"}], "type": "STRING", "value": "D:/RTXPT/External/Streamline"}, {"name": "DONUT_WITH_AFTERMATH", "properties": [{"name": "HELPSTRING", "value": "Enable Aftermath crash dump generation with Donut"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_WITH_AUDIO", "properties": [{"name": "HELPSTRING", "value": "Include Audio features (XAudio2)"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_WITH_DX11", "properties": [{"name": "HELPSTRING", "value": "Enable the DX11 version of Donut"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_WITH_DX12", "properties": [{"name": "HELPSTRING", "value": "Enable the DX12 version of Donut"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_WITH_LZ4", "properties": [{"name": "HELPSTRING", "value": "Include LZ4"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_WITH_MINIZ", "properties": [{"name": "HELPSTRING", "value": "Include miniz (support for zip archives)"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_WITH_NVRHI", "properties": [{"name": "HELPSTRING", "value": "Enable NVRHI and related projects"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_WITH_STATIC_SHADERS", "properties": [{"name": "HELPSTRING", "value": "Build Donut with statically linked shaders"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_WITH_STREAMLINE", "properties": [{"name": "HELPSTRING", "value": "Enable streamline, separate package required"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_WITH_TASKFLOW", "properties": [{"name": "HELPSTRING", "value": "Include TaskFlow"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_WITH_TINYEXR", "properties": [{"name": "HELPSTRING", "value": "Include TinyEXR"}], "type": "BOOL", "value": "ON"}, {"name": "DONUT_WITH_UNIT_TESTS", "properties": [{"name": "HELPSTRING", "value": "Donut unit-tests (see CMake/CTest documentation)"}], "type": "BOOL", "value": "OFF"}, {"name": "DONUT_WITH_VULKAN", "properties": [{"name": "HELPSTRING", "value": "Enable the Vulkan version of Donut"}], "type": "BOOL", "value": "OFF"}, {"name": "DXC_CUSTOM_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to embedded dxc"}], "type": "STRING", "value": "D:/RTXPT/External/dxc/bin/x64/"}, {"name": "DXC_DXIL_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to embedded dxc file for DX12"}], "type": "STRING", "value": "D:/RTXPT/External/dxc/bin/x64//dxc.exe"}, {"name": "DXC_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to embedded dxc file for DX12"}], "type": "STRING", "value": "D:/RTXPT/External/dxc/bin/x64//dxc.exe"}, {"name": "DXC_SPIRV_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/VulkanSDK/1.4.309.0/Bin/dxc.exe"}, {"name": "DXHEADERS_BUILD_GOOGLE_TEST", "properties": [{"name": "HELPSTRING", "value": "Build the google test suite"}], "type": "BOOL", "value": "OFF"}, {"name": "DXHEADERS_BUILD_TEST", "properties": [{"name": "HELPSTRING", "value": "Build the test"}], "type": "BOOL", "value": "OFF"}, {"name": "DXHEADERS_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Installation logic"}], "type": "BOOL", "value": "OFF"}, {"name": "DirectX-Guids_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;DirectX-Headers;"}, {"name": "DirectX-Headers_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/nvrhi/thirdparty/DirectX-Headers"}, {"name": "DirectX-Headers_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "DirectX-Headers_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers"}, {"name": "FETCHCONTENT_BASE_DIR", "properties": [{"name": "HELPSTRING", "value": "Directory under which to collect all populated content"}], "type": "PATH", "value": "D:/RTXPT/cmake-build-release-visual-studio/_deps"}, {"name": "FETCHCONTENT_FULLY_DISCONNECTED", "properties": [{"name": "HELPSTRING", "value": "Disables all attempts to download or update content and assumes source dirs already exist"}], "type": "BOOL", "value": "OFF"}, {"name": "FETCHCONTENT_QUIET", "properties": [{"name": "HELPSTRING", "value": "Enables QUIET option for all content population"}], "type": "BOOL", "value": "ON"}, {"name": "FETCHCONTENT_SOURCE_DIR_MATHLIB", "properties": [{"name": "HELPSTRING", "value": "When not empty, overrides where to find pre-populated content for mathlib"}], "type": "PATH", "value": ""}, {"name": "FETCHCONTENT_UPDATES_DISCONNECTED", "properties": [{"name": "HELPSTRING", "value": "Enables UPDATE_DISCONNECTED behavior for all content population"}], "type": "BOOL", "value": "OFF"}, {"name": "FETCHCONTENT_UPDATES_DISCONNECTED_MATHLIB", "properties": [{"name": "HELPSTRING", "value": "Enables UPDATE_DISCONNECTED behavior just for population of mathlib"}], "type": "BOOL", "value": "OFF"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_NVAPI", "properties": [{"name": "HELPSTRING", "value": "Details about finding NVAPI"}], "type": "INTERNAL", "value": "[D:/RTXPT/External/nvapi/][D:/RTXPT/External/nvapi/amd64/nvapi64.lib][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenMP", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenMP"}], "type": "INTERNAL", "value": "[TRUE][TRUE][ ][v2.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenMP_C"}], "type": "INTERNAL", "value": "[-openmp][v2.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenMP_CXX"}], "type": "INTERNAL", "value": "[-openmp][v2.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_STREAMLINE", "properties": [{"name": "HELPSTRING", "value": "Details about finding STREAMLINE"}], "type": "INTERNAL", "value": "[D:/RTXPT/External/Streamline][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "GITCOMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Users/<USER>/AppData/Local/UGit/app-5.37.0/resources/app/git/cmd/git.exe"}, {"name": "GIT_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Git command line client"}], "type": "FILEPATH", "value": "C:/Users/<USER>/AppData/Local/UGit/app-5.37.0/resources/app/git/cmd/git.exe"}, {"name": "GLFW_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/glfw"}, {"name": "GLFW_BUILD_DOCS", "properties": [{"name": "HELPSTRING", "value": "Build the GLFW documentation"}], "type": "BOOL", "value": "OFF"}, {"name": "GLFW_BUILD_EXAMPLES", "properties": [{"name": "HELPSTRING", "value": "Build the GLFW example programs"}], "type": "BOOL", "value": "OFF"}, {"name": "GLFW_BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build the GLFW test programs"}], "type": "BOOL", "value": "OFF"}, {"name": "GLFW_BUILD_WIN32", "properties": [{"name": "HELPSTRING", "value": "Build support for Win32"}], "type": "BOOL", "value": "ON"}, {"name": "GLFW_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Generate installation target"}], "type": "BOOL", "value": "OFF"}, {"name": "GLFW_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GLFW_LIBRARY_TYPE", "properties": [{"name": "HELPSTRING", "value": "Library type override for GLFW (SHARED, STATIC, OBJECT, or empty to follow BUILD_SHARED_LIBS)"}], "type": "STRING", "value": ""}, {"name": "GLFW_PKG_CONFIG_LIBS_PRIVATE", "properties": [{"name": "HELPSTRING", "value": "GLFW pkg-config Libs.private"}], "type": "INTERNAL", "value": " -lgdi32"}, {"name": "GLFW_PKG_CONFIG_REQUIRES_PRIVATE", "properties": [{"name": "HELPSTRING", "value": "GLFW pkg-config Requires.private"}], "type": "INTERNAL", "value": ""}, {"name": "GLFW_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut/thirdparty/glfw"}, {"name": "GLFW_USE_HYBRID_HPG", "properties": [{"name": "HELPSTRING", "value": "Force use of high-performance GPU on hybrid systems"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_BUILD_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Generate the install target"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_BUILD_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Build dynamic/static library"}], "type": "BOOL", "value": "ON"}, {"name": "GLM_BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build the test programs"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_DISABLE_AUTO_DETECTION", "properties": [{"name": "HELPSTRING", "value": "Disable platform, compiler, arch and C++ language detection"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_CXX_11", "properties": [{"name": "HELPSTRING", "value": "Enable C++ 11"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_CXX_14", "properties": [{"name": "HELPSTRING", "value": "Enable C++ 14"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_CXX_17", "properties": [{"name": "HELPSTRING", "value": "Enable C++ 17"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_CXX_20", "properties": [{"name": "HELPSTRING", "value": "Enable C++ 20"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_CXX_98", "properties": [{"name": "HELPSTRING", "value": "Enable C++ 98"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_FAST_MATH", "properties": [{"name": "HELPSTRING", "value": "Enable fast math optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_LANG_EXTENSIONS", "properties": [{"name": "HELPSTRING", "value": "Enable language extensions"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_AVX", "properties": [{"name": "HELPSTRING", "value": "Enable AVX optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_AVX2", "properties": [{"name": "HELPSTRING", "value": "Enable AVX2 optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_NEON", "properties": [{"name": "HELPSTRING", "value": "Enable ARM NEON optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_SSE2", "properties": [{"name": "HELPSTRING", "value": "Enable SSE2 optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_SSE3", "properties": [{"name": "HELPSTRING", "value": "Enable SSE3 optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_SSE4_1", "properties": [{"name": "HELPSTRING", "value": "Enable SSE 4.1 optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_SSE4_2", "properties": [{"name": "HELPSTRING", "value": "Enable SSE 4.2 optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_ENABLE_SIMD_SSSE3", "properties": [{"name": "HELPSTRING", "value": "Enable SSSE3 optimizations"}], "type": "BOOL", "value": "OFF"}, {"name": "GLM_FORCE_PURE", "properties": [{"name": "HELPSTRING", "value": "Force 'pure' instructions"}], "type": "BOOL", "value": "OFF"}, {"name": "GLOBAL_BIN_OUTPUT_PATH", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "D:/RTXPT/cmake-build-release-visual-studio"}, {"name": "HAVE_CLOCALE", "properties": [{"name": "HELPSTRING", "value": "Have include clocale"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DECIMAL_POINT", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_DECIMAL_POINT"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_LCONV_SIZE", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_LOCALECONV", "properties": [{"name": "HELPSTRING", "value": "Have symbol localeconv"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDDEF_H", "properties": [{"name": "HELPSTRING", "value": "Have include stddef.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDINT_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdint.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_TYPES_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/types.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HGCOMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "INSTALL_PROJECT", "properties": [{"name": "HELPSTRING", "value": "Install project"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_STATIC_WINDOWS_RUNTIME", "properties": [{"name": "HELPSTRING", "value": "Use static (MT/MTd) Windows runtime"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_WITH_CMAKE_PACKAGE", "properties": [{"name": "HELPSTRING", "value": "Generate and install cmake package files"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_WITH_EXAMPLE", "properties": [{"name": "HELPSTRING", "value": "Compile JsonCpp example"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_WITH_PKGCONFIG_SUPPORT", "properties": [{"name": "HELPSTRING", "value": "Generate and install .pc files"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_WITH_POST_BUILD_UNITTEST", "properties": [{"name": "HELPSTRING", "value": "Automatically run unit-tests as a post build step"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_WITH_STRICT_ISO", "properties": [{"name": "HELPSTRING", "value": "Issue all the warnings demanded by strict ISO C and ISO C++"}], "type": "BOOL", "value": "ON"}, {"name": "JSONCPP_WITH_TESTS", "properties": [{"name": "HELPSTRING", "value": "Compile and (for jsoncpp_check) run JsonCpp test executables"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_WITH_WARNING_AS_ERROR", "properties": [{"name": "HELPSTRING", "value": "Force compilation to fail if a warning occurs"}], "type": "BOOL", "value": "OFF"}, {"name": "LCONV_SIZE", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(lconv)"}], "type": "INTERNAL", "value": "152"}, {"name": "LZ4_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/lz4/build/cmake"}, {"name": "LZ4_BUILD_CLI", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "OFF"}, {"name": "LZ4_BUNDLED_MODE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "LZ4_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "LZ4_POSITION_INDEPENDENT_LIB", "properties": [{"name": "HELPSTRING", "value": "Use position independent code for static library (if applicable)"}], "type": "BOOL", "value": "ON"}, {"name": "LZ4_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Omm/external/lz4/build/cmake"}, {"name": "LZ4_VERSION_MAJOR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "LZ4_VERSION_MINOR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "LZ4_VERSION_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "LZ4_VERSION_STRING", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "MAKECOMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Command to build the project"}], "type": "STRING", "value": "\"C:\\Program Files\\JetBrains\\CLion 2025.2\\bin\\cmake\\win\\x64\\bin\\cmake.exe\" --build . --config \"${CTEST_CONFIGURATION_TYPE}\""}, {"name": "MEMORYCHECK_COMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to the memory checking command, used for memory error detection."}], "type": "FILEPATH", "value": "MEMORYCHECK_COMMAND-NOTFOUND"}, {"name": "MEMORYCHECK_SUPPRESSIONS_FILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "File that contains suppressions for the memory checker"}], "type": "FILEPATH", "value": ""}, {"name": "NRD_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Nrd"}, {"name": "NRD_DISABLE_SHADER_COMPILATION", "properties": [{"name": "HELPSTRING", "value": "Disable shader compilation"}], "type": "BOOL", "value": "ON"}, {"name": "NRD_DXC_CUSTOM_PATH", "properties": [{"name": "HELPSTRING", "value": "Custom DXC to use if Vulkan SDK is not installed"}], "type": "STRING", "value": "custom/path/to/dxc"}, {"name": "NRD_DXC_PATH", "properties": [{"name": "HELPSTRING", "value": "DXC shader compiler path for NRD"}], "type": "STRING", "value": "D:/RTXPT/External/dxc/bin/x64//dxc.exe"}, {"name": "NRD_EMBEDS_DXBC_SHADERS", "properties": [{"name": "HELPSTRING", "value": "NRD embeds DXBC shaders"}], "type": "BOOL", "value": "OFF"}, {"name": "NRD_EMBEDS_DXIL_SHADERS", "properties": [{"name": "HELPSTRING", "value": "NRD embeds DXIL shaders"}], "type": "BOOL", "value": "OFF"}, {"name": "NRD_EMBEDS_SPIRV_SHADERS", "properties": [{"name": "HELPSTRING", "value": "NRD embeds SPIRV shaders"}], "type": "BOOL", "value": "OFF"}, {"name": "NRD_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "NRD_NORMAL_ENCODING", "properties": [{"name": "HELPSTRING", "value": "Normal encoding variant (0-4, matches nrd::NormalEncoding)"}], "type": "STRING", "value": "2"}, {"name": "NRD_ROUGHNESS_ENCODING", "properties": [{"name": "HELPSTRING", "value": "Roughness encoding variant (0-2, matches nrd::RoughnessEncoding)"}], "type": "STRING", "value": "1"}, {"name": "NRD_SHADERS_PATH", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "D:/RTXPT/cmake-build-release-visual-studio/NRDShaders"}, {"name": "NRD_SHADER_OUTPUT_PATH", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "NRD_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Nrd"}, {"name": "NRD_STATIC_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Build static library"}], "type": "BOOL", "value": "OFF"}, {"name": "NRD_USE_PRECOMPILED_SHADERS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "OFF"}, {"name": "NVAPI_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to NVAPI include headers/shaders"}], "type": "STRING", "value": "D:/RTXPT/External/nvapi/"}, {"name": "NVAPI_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to NVAPI .lib file"}], "type": "STRING", "value": "D:/RTXPT/External/nvapi/amd64/nvapi64.lib"}, {"name": "NVRHI_BUILD_SHARED", "properties": [{"name": "HELPSTRING", "value": "Build NVRHI as a shared library (DLL or .so)"}], "type": "BOOL", "value": "OFF"}, {"name": "NVRHI_INSTALL", "properties": [{"name": "HELPSTRING", "value": "OFF"}], "type": "BOOL", "value": "ON"}, {"name": "NVRHI_INSTALL_EXPORTS", "properties": [{"name": "HELPSTRING", "value": "Install CMake exports"}], "type": "BOOL", "value": "OFF"}, {"name": "NVRHI_WITH_AFTERMATH", "properties": [{"name": "HELPSTRING", "value": "Include Aftermath support (requires NSight Aftermath SDK)"}], "type": "BOOL", "value": "OFF"}, {"name": "NVRHI_WITH_DX11", "properties": [{"name": "HELPSTRING", "value": "Build the NVRHI D3D11 backend"}], "type": "BOOL", "value": "OFF"}, {"name": "NVRHI_WITH_DX12", "properties": [{"name": "HELPSTRING", "value": "Build the NVRHI D3D12 backend"}], "type": "BOOL", "value": "ON"}, {"name": "NVRHI_WITH_NVAPI", "properties": [{"name": "HELPSTRING", "value": "Include NVAPI support (requires NVAPI SDK)"}], "type": "BOOL", "value": "ON"}, {"name": "NVRHI_WITH_RTXMU", "properties": [{"name": "HELPSTRING", "value": "Use RTXMU for acceleration structure management"}], "type": "BOOL", "value": "OFF"}, {"name": "NVRHI_WITH_VALIDATION", "properties": [{"name": "HELPSTRING", "value": "Build NVRHI the validation layer"}], "type": "BOOL", "value": "ON"}, {"name": "NVRHI_WITH_VULKAN", "properties": [{"name": "HELPSTRING", "value": "Build the NVRHI Vulkan backend"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_BUILD_VIEWER", "properties": [{"name": "HELPSTRING", "value": "Build omm viewer tool"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_CROSSCOMPILE_AARCH64", "properties": [{"name": "HELPSTRING", "value": "cross compilation for aarch64"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_CROSSCOMPILE_X86_64", "properties": [{"name": "HELPSTRING", "value": "cross compilation for x86_64"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_DISABLE_INTERPROCEDURAL_OPTIMIZATION", "properties": [{"name": "HELPSTRING", "value": "disable interprocedural optimization"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_ENABLE_BENCHMARK", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_ENABLE_FAST_MATH", "properties": [{"name": "HELPSTRING", "value": "Enable fast math optimizations()"}], "type": "BOOL", "value": "ON"}, {"name": "OMM_ENABLE_OPENMP", "properties": [{"name": "HELPSTRING", "value": "enable openmp"}], "type": "BOOL", "value": "ON"}, {"name": "OMM_ENABLE_PRECOMPILED_SHADERS_DXIL", "properties": [{"name": "HELPSTRING", "value": "Embedded precompiled DXIL shaders. Require path to dxc.exe (normally located in Window SDK)."}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_ENABLE_PRECOMPILED_SHADERS_SPIRV", "properties": [{"name": "HELPSTRING", "value": "Embedded precompiled SPIRV shaders. Require path to Vulkan SDK."}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_ENABLE_TESTS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_INTEGRATION_LAYER_NVRHI", "properties": [{"name": "HELPSTRING", "value": "Build nvrhi integration layer"}], "type": "BOOL", "value": "ON"}, {"name": "OMM_LIB_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Generate install rules for OMM"}], "type": "BOOL", "value": "ON"}, {"name": "OMM_SHADER_DEBUG_INFO", "properties": [{"name": "HELPSTRING", "value": "enable embedded shader debug info"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_STATIC_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "build static lib"}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_USE_LEGACY_OMM_LIB_NAME", "properties": [{"name": "HELPSTRING", "value": "Use the legacy target name of omm-lib: \"omm-sdk\""}], "type": "BOOL", "value": "OFF"}, {"name": "OMM_VK_B_SHIFT", "properties": [{"name": "HELPSTRING", "value": "OMM_VK_B_SHIFT"}], "type": "STRING", "value": "300"}, {"name": "OMM_VK_S_SHIFT", "properties": [{"name": "HELPSTRING", "value": "OMM_VK_S_SHIFT"}], "type": "STRING", "value": "100"}, {"name": "OMM_VK_T_SHIFT", "properties": [{"name": "HELPSTRING", "value": "OMM_VK_T_SHIFT"}], "type": "STRING", "value": "200"}, {"name": "OMM_VK_U_SHIFT", "properties": [{"name": "HELPSTRING", "value": "OMM_VK_U_SHIFT"}], "type": "STRING", "value": "400"}, {"name": "Opacity Micro-Map SDK_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Omm"}, {"name": "Opacity Micro-Map SDK_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Opacity Micro-Map SDK_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Omm"}, {"name": "OpenMP_COMPILE_RESULT_CXX_openmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "OpenMP_COMPILE_RESULT_C_openmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "OpenMP_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "-openmp"}, {"name": "OpenMP_CXX_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": ""}, {"name": "OpenMP_CXX_SPEC_DATE", "properties": [{"name": "HELPSTRING", "value": "CXX compiler's OpenMP specification date"}], "type": "INTERNAL", "value": "200203"}, {"name": "OpenMP_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "-openmp"}, {"name": "OpenMP_C_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": ""}, {"name": "OpenMP_C_SPEC_DATE", "properties": [{"name": "HELPSTRING", "value": "C compiler's OpenMP specification date"}], "type": "INTERNAL", "value": "200203"}, {"name": "OpenMP_SPECTEST_CXX_", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "OpenMP_SPECTEST_C_", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "P4COMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "RTXDI_SKIP_SHADER_VALIDATION", "properties": [{"name": "HELPSTRING", "value": "Skip RTXDI shader validation"}], "type": "STRING", "value": "ON"}, {"name": "RTXPT_D3D_AGILITY_SDK_PATH", "properties": [{"name": "HELPSTRING", "value": "AgilitySDKPath"}], "type": "STRING", "value": ""}, {"name": "RTXPT_D3D_AGILITY_SDK_VERSION", "properties": [{"name": "HELPSTRING", "value": "AgilitySDKVersion"}], "type": "STRING", "value": ""}, {"name": "RTXPT_DOWNLOAD_AND_ENABLE_AGILITY_SDK", "properties": [{"name": "HELPSTRING", "value": "Attempt to automatically download DirectX AgilitySDK and enable experimental features"}], "type": "BOOL", "value": ""}, {"name": "RTXPT_LOCAL_CONFIG_ID", "properties": [{"name": "HELPSTRING", "value": "Local user initials for user-specific settings"}], "type": "STRING", "value": "NONAME"}, {"name": "RTXPathTracing_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio"}, {"name": "RTXPathTracing_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "RTXPathTracing_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT"}, {"name": "SHADERMAKE_BIN_OUTPUT_PATH", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "SHADERMAKE_FIND_DXC", "properties": [{"name": "HELPSTRING", "value": "Toggles whether to search for DXC for DXIL"}], "type": "BOOL", "value": "ON"}, {"name": "SHADERMAKE_FIND_DXC_SPIRV", "properties": [{"name": "HELPSTRING", "value": "Toggles whether to search for DXC for SPIR-V"}], "type": "BOOL", "value": "ON"}, {"name": "SHADERMAKE_FIND_FXC", "properties": [{"name": "HELPSTRING", "value": "Toggles whether to search for FXC"}], "type": "BOOL", "value": "OFF"}, {"name": "SHADERMAKE_SEARCH_FOR_COMPILERS", "properties": [{"name": "HELPSTRING", "value": "Toggles whether to search for dxc.exe and fxc.exe"}], "type": "BOOL", "value": "ON"}, {"name": "SITE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Name of the computer/site where compile is being run"}], "type": "STRING", "value": "SCOLU-PC0"}, {"name": "STREAMLINE_CMAKE_FILE", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "FILEPATH", "value": "D:/RTXPT/External/Streamline/CMakeLists.txt"}, {"name": "STREAMLINE_FEATURE_DEEPDVC", "properties": [{"name": "HELPSTRING", "value": "Include DEEPDVC dll"}], "type": "BOOL", "value": "OFF"}, {"name": "STREAMLINE_FEATURE_DIRECTSR", "properties": [{"name": "HELPSTRING", "value": "Include DirectSR dll"}], "type": "BOOL", "value": "OFF"}, {"name": "STREAMLINE_FEATURE_DLSS_FG", "properties": [{"name": "HELPSTRING", "value": "Include DLSS-FG dll"}], "type": "BOOL", "value": "ON"}, {"name": "STREAMLINE_FEATURE_DLSS_RR", "properties": [{"name": "HELPSTRING", "value": "Include DLSS-RR dll"}], "type": "BOOL", "value": "ON"}, {"name": "STREAMLINE_FEATURE_DLSS_SR", "properties": [{"name": "HELPSTRING", "value": "Include DLSS-SR dll"}], "type": "BOOL", "value": "ON"}, {"name": "STREAMLINE_FEATURE_IMGUI", "properties": [{"name": "HELPSTRING", "value": "Include Imgui dll"}], "type": "BOOL", "value": "ON"}, {"name": "STREAMLINE_FEATURE_NIS", "properties": [{"name": "HELPSTRING", "value": "Include NIS dll"}], "type": "BOOL", "value": "OFF"}, {"name": "STREAMLINE_FEATURE_NRD", "properties": [{"name": "HELPSTRING", "value": "Include NRD dll"}], "type": "BOOL", "value": "OFF"}, {"name": "STREAMLINE_FEATURE_NVPERF", "properties": [{"name": "HELPSTRING", "value": "Include NSight Perf SDK dll"}], "type": "BOOL", "value": "OFF"}, {"name": "STREAMLINE_FEATURE_REFLEX", "properties": [{"name": "HELPSTRING", "value": "Include Reflex dll"}], "type": "BOOL", "value": "ON"}, {"name": "STREAMLINE_IMPORT_AS_INTERFACE", "properties": [{"name": "HELPSTRING", "value": "Import Streamline as an Interface without lib"}], "type": "BOOL", "value": "OFF"}, {"name": "STREAMLINE_INSTALL_DIR", "properties": [{"name": "HELPSTRING", "value": "Streamline Install Dir"}], "type": "STRING", "value": "C:/Program Files (x86)/RTXPathTracing"}, {"name": "STREAMLINE_SDK_ROOT", "properties": [{"name": "HELPSTRING", "value": "SL SDK Root Directory"}], "type": "STRING", "value": "D:/RTXPT/External/Streamline"}, {"name": "SVNCOMMAND", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "ShaderMake_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/ShaderMake"}, {"name": "ShaderMake_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "ShaderMake_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut/ShaderMake"}, {"name": "USE_MSVC_RUNTIME_LIBRARY_DLL", "properties": [{"name": "HELPSTRING", "value": "Use MSVC runtime library DLL"}], "type": "BOOL", "value": "ON"}, {"name": "XXHASH_BUNDLED_MODE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "ON"}, {"name": "XXHASH_LIB_SOVERSION", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "XXHASH_LIB_VERSION", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "XXHASH_VERSION_MAJOR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "XXHASH_VERSION_MINOR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "XXHASH_VERSION_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "XXHASH_VERSION_STRING", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "D:/RTXPT/cmake-build-release-visual-studio/install"}, {"name": "cxxopts_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/cxxopts"}, {"name": "cxxopts_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "cxxopts_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/cxxopts"}, {"name": "donut_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut"}, {"name": "donut_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "donut_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut"}, {"name": "donut_app_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;streamline;general;donut_core;general;donut_engine;general;glfw;general;imgui;general;nvrhi_d3d12;general;d3d12;general;dxgi;general;dxguid;general;nvrhi;"}, {"name": "donut_core_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;jsoncpp_static;general;miniz;"}, {"name": "donut_engine_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;donut_core;general;nvrhi;general;jsoncpp_static;general;ShaderMakeBlob;"}, {"name": "donut_render_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;donut_core;general;donut_engine;"}, {"name": "glm_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/glm"}, {"name": "glm_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "glm_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Omm/external/glm"}, {"name": "jsoncpp_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/jsoncpp"}, {"name": "jsoncpp_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "jsoncpp_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut/thirdparty/jsoncpp"}, {"name": "miniz_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}, {"name": "miniz_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "miniz_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"name": "nvrhi_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/nvrhi"}, {"name": "nvrhi_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "nvrhi_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Donut/nvrhi"}, {"name": "nvrhi_d3d12_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;Microsoft::DirectX-Headers;general;Microsoft::DirectX-Guids;general;d3d12;general;nvapi;"}, {"name": "xxHash_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/cmake-build-release-visual-studio/External/Omm/external/xxHash/cmake_unofficial"}, {"name": "xxHash_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "xxHash_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/RTXPT/External/Omm/external/xxHash/cmake_unofficial"}], "kind": "cache", "version": {"major": 2, "minor": 0}}