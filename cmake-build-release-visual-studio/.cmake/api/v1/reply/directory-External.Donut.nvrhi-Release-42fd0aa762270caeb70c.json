{"backtraceGraph": {"commands": ["install"], "files": ["External/Donut/nvrhi/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 335, "parent": 0}, {"command": 0, "file": 0, "line": 338, "parent": 0}, {"command": 0, "file": 0, "line": 350, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "D:/RTXPT/cmake-build-release-visual-studio/install/include", "paths": ["External/Donut/nvrhi/include/nvrhi"], "type": "directory"}, {"backtrace": 2, "component": "Unspecified", "destination": "lib", "paths": ["External/Donut/nvrhi/Release/nvrhi.lib"], "targetId": "nvrhi::@1e4fb8cca40b12049cc4", "targetIndex": 43, "type": "target"}, {"backtrace": 3, "component": "Unspecified", "destination": "lib", "paths": ["External/Donut/nvrhi/Release/nvrhi_d3d12.lib"], "targetId": "nvrhi_d3d12::@1e4fb8cca40b12049cc4", "targetIndex": 44, "type": "target"}, {"backtrace": 3, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "nvrhi_d3d12::@1e4fb8cca40b12049cc4", "index": 44}, "destination": "lib", "type": "cxxModuleBmi"}], "paths": {"build": "External/Donut/nvrhi", "source": "External/Donut/nvrhi"}}