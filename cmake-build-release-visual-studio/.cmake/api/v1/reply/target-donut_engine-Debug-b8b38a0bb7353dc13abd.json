{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/Debug/donut_engine.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_dependencies", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["External/Donut/donut-engine.cmake", "External/Donut/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 95, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 34, "parent": 2}, {"command": 2, "file": 0, "line": 37, "parent": 2}, {"command": 3, "file": 0, "line": 39, "parent": 2}, {"command": 4, "file": 0, "line": 66, "parent": 2}, {"command": 4, "file": 0, "line": 62, "parent": 2}, {"command": 4, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 65, "parent": 2}, {"command": 4, "file": 0, "line": 43, "parent": 2}, {"command": 4, "file": 0, "line": 57, "parent": 2}, {"command": 4, "file": 0, "line": 64, "parent": 2}, {"command": 4, "file": 0, "line": 47, "parent": 2}, {"file": 2}, {"command": 5, "file": 2, "line": 55, "parent": 14}, {"command": 6, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 0, "line": 42, "parent": 2}, {"command": 2, "file": 0, "line": 56, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /W3 /MP /Zi /Ob0 /Od /RTC1 /D_ITERATOR_DEBUG_LEVEL=1 -D_DEBUG -std:c++17 -MTd"}, {"fragment": "-WX"}], "defines": [{"backtrace": 6, "define": "DONUT_WITH_AFTERMATH=0"}, {"backtrace": 7, "define": "DONUT_WITH_DX11=0"}, {"backtrace": 8, "define": "DONUT_WITH_DX12=1"}, {"backtrace": 4, "define": "DONUT_WITH_MINIZ"}, {"backtrace": 9, "define": "DONUT_WITH_STATIC_SHADERS=0"}, {"backtrace": 10, "define": "DONUT_WITH_TASKFLOW"}, {"backtrace": 11, "define": "DONUT_WITH_TINYEXR"}, {"backtrace": 12, "define": "DONUT_WITH_VULKAN=0"}, {"backtrace": 4, "define": "MINIZ_STATIC_DEFINE"}, {"backtrace": 13, "define": "NOMINMAX"}, {"backtrace": 15, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 16, "path": "D:/RTXPT/External/Donut/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/stb"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/cgltf"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}, {"backtrace": 17, "path": "D:/RTXPT/External/Donut/thirdparty/taskflow"}, {"backtrace": 18, "path": "D:/RTXPT/External/Donut/thirdparty/tinyexr"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], "standard": "17"}, "sourceIndexes": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MTd"}, {"fragment": "-WX"}], "defines": [{"backtrace": 6, "define": "DONUT_WITH_AFTERMATH=0"}, {"backtrace": 7, "define": "DONUT_WITH_DX11=0"}, {"backtrace": 8, "define": "DONUT_WITH_DX12=1"}, {"backtrace": 4, "define": "DONUT_WITH_MINIZ"}, {"backtrace": 9, "define": "DONUT_WITH_STATIC_SHADERS=0"}, {"backtrace": 10, "define": "DONUT_WITH_TASKFLOW"}, {"backtrace": 11, "define": "DONUT_WITH_TINYEXR"}, {"backtrace": 12, "define": "DONUT_WITH_VULKAN=0"}, {"backtrace": 4, "define": "MINIZ_STATIC_DEFINE"}, {"backtrace": 13, "define": "NOMINMAX"}, {"backtrace": 15, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 16, "path": "D:/RTXPT/External/Donut/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/jsoncpp/src/lib_json/../../include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/cmake-build-release-visual-studio/External/Donut/thirdparty/miniz"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/nvrhi/include"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/stb"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/thirdparty/cgltf"}, {"backtrace": 4, "path": "D:/RTXPT/External/Donut/ShaderMake/include"}, {"backtrace": 17, "path": "D:/RTXPT/External/Donut/thirdparty/taskflow"}, {"backtrace": 18, "path": "D:/RTXPT/External/Donut/thirdparty/tinyexr"}], "language": "C", "sourceIndexes": [41]}], "dependencies": [{"backtrace": 4, "id": "donut_core::@3f75b14119991a9702cc"}, {"backtrace": 4, "id": "jsoncpp_static::@dc903dbc33f565e211e1"}, {"backtrace": 4, "id": "ShaderMakeBlob::@b014256a752891a2614a"}, {"backtrace": 4, "id": "miniz::@8f637d5d2c9d0ba648a2"}, {"backtrace": 4, "id": "nvrhi::@1e4fb8cca40b12049cc4"}, {"backtrace": 5, "id": "donut_shaders::@111d5be078c7280583c9"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "Donut"}, "id": "donut_engine::@3f75b14119991a9702cc", "name": "donut_engine", "nameOnDisk": "donut_engine.lib", "paths": {"build": "External/Donut", "source": "External/Donut"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 40]}, {"name": "Source Files", "sourceIndexes": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41]}, {"name": "", "sourceIndexes": [42]}], "sources": [{"backtrace": 3, "path": "External/Donut/include/donut/engine/AudioCache.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/AudioEngine.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/BindingCache.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/CommonRenderPasses.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/ConsoleInterpreter.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/ConsoleObjects.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/DDSFile.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/DescriptorTableManager.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/FramebufferFactory.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/GltfImporter.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/IesProfile.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/KeyframeAnimation.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/MaterialBindingCache.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/Scene.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/SceneGraph.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/SceneTypes.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/ShaderFactory.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/ShadowMap.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/TextureCache.h", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "External/Donut/include/donut/engine/View.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/AudioCache.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/AudioEngine.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/BindingCache.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/CommonRenderPasses.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/ConsoleInterpreter.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/ConsoleObjects.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/DDSFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/DescriptorTableManager.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/FramebufferFactory.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/GltfImporter.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/IesProfile.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/KeyframeAnimation.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/Material.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/MaterialBindingCache.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/Scene.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/SceneGraph.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/SceneTypes.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/ShaderFactory.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/TextureCache.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "External/Donut/src/engine/View.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "External/Donut/src/engine/dds.h", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 1, "path": "External/Donut/src/engine/stb_impl.c", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "External/Donut/donut.natvis", "sourceGroupIndex": 2}], "type": "STATIC_LIBRARY"}