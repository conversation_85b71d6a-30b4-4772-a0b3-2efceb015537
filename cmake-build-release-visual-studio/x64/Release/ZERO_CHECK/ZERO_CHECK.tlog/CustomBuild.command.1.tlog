^D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\CMAKEFILES\E3A505B6F528AC900A17054ADB207B3E\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/RTXPT/cmake-build-release-visual-studio/RTXPathTracing.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
