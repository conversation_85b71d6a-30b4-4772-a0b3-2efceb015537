d:\rtxpt\cmake-build-release-visual-studio\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\jsoncpp\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\jsoncpp\src\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\jsoncpp\src\lib_json\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\jsoncpp\include\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\glfw\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\glfw\src\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\thirdparty\miniz\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\shadermake\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\nvrhi\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\nvrhi\thirdparty\directx-headers\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\donut\shaders\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\streamline\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\nrd\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\_deps\mathlib-build\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\external\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\external\glm\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\external\glm\glm\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\external\lz4\build\cmake\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\external\xxhash\cmake_unofficial\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\libraries\omm-lib\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\libraries\omm-gpu-nvrhi\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\omm\support\scripts\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\rtxdi\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\cxxopts\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\external\cxxopts\include\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\rtxpt\cmakefiles\generate.stamp
d:\rtxpt\cmake-build-release-visual-studio\x64\release\zero_check\zero_check.tlog\custombuild.command.1.tlog
d:\rtxpt\cmake-build-release-visual-studio\x64\release\zero_check\zero_check.tlog\custombuild.read.1.tlog
d:\rtxpt\cmake-build-release-visual-studio\x64\release\zero_check\zero_check.tlog\custombuild.write.1.tlog
