^D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\CMAKEFILES\D75F5B43056B03E008AB17445C5A9698\COPYDX.MARKER.RULE
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic/Tools/d3d12/x64
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E copy_directory_if_different D:/RTXPT/External/dxc/bin/x64/ D:/RTXPT/bin/ShaderDynamic/Tools/d3d12/x64
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/bin/ShaderDynamic/Tools/copyDX.marker
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\CMAKEFILES\D75F5B43056B03E008AB17445C5A9698\COPYVK.MARKER.RULE
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic/Tools/vk/x64
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/VulkanSDK/1.4.309.0/Bin/SDL2.dll C:/VulkanSDK/1.4.309.0/Bin/SDL2d.dll C:/VulkanSDK/1.4.309.0/Bin/SPIRV-Tools-shared.dll C:/VulkanSDK/1.4.309.0/Bin/SPIRV-Tools-sharedd.dll C:/VulkanSDK/1.4.309.0/Bin/SPIRV.dll C:/VulkanSDK/1.4.309.0/Bin/SPIRVd.dll C:/VulkanSDK/1.4.309.0/Bin/SPVRemapper.dll C:/VulkanSDK/1.4.309.0/Bin/SPVRemapperd.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_api_dump.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_api_dump.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_crash_diagnostic.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_crash_diagnostic.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_gfxreconstruct.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_gfxreconstruct.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_profiles.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_profiles.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_shader_object.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_shader_object.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_synchronization2.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_synchronization2.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_validation.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_khronos_validation.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_monitor.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_monitor.json C:/VulkanSDK/1.4.309.0/Bin/VkLayer_screenshot.dll C:/VulkanSDK/1.4.309.0/Bin/VkLayer_screenshot.json C:/VulkanSDK/1.4.309.0/Bin/dxc.exe C:/VulkanSDK/1.4.309.0/Bin/dxcompiler.dll C:/VulkanSDK/1.4.309.0/Bin/dxcompilerd.dll C:/VulkanSDK/1.4.309.0/Bin/gfx.dll C:/VulkanSDK/1.4.309.0/Bin/gfxd.dll C:/VulkanSDK/1.4.309.0/Bin/gfxrecon-compress.exe C:/VulkanSDK/1.4.309.0/Bin/gfxrecon-convert.exe C:/VulkanSDK/1.4.309.0/Bin/gfxrecon-extract.exe C:/VulkanSDK/1.4.309.0/Bin/gfxrecon-info.exe C:/VulkanSDK/1.4.309.0/Bin/gfxrecon-optimize.exe C:/VulkanSDK/1.4.309.0/Bin/gfxrecon-replay.exe C:/VulkanSDK/1.4.309.0/Bin/glslang-default-resource-limits.dll C:/VulkanSDK/1.4.309.0/Bin/glslang-default-resource-limitsd.dll C:/VulkanSDK/1.4.309.0/Bin/glslang.dll C:/VulkanSDK/1.4.309.0/Bin/glslang.exe C:/VulkanSDK/1.4.309.0/Bin/glslangValidator.exe C:/VulkanSDK/1.4.309.0/Bin/glslangd.dll C:/VulkanSDK/1.4.309.0/Bin/glslc.exe C:/VulkanSDK/1.4.309.0/Bin/shaderc_shared.dll C:/VulkanSDK/1.4.309.0/Bin/shaderc_sharedd.dll C:/VulkanSDK/1.4.309.0/Bin/slang-glsl-module.dll C:/VulkanSDK/1.4.309.0/Bin/slang-glsl-moduled.dll C:/VulkanSDK/1.4.309.0/Bin/slang-glslang.dll C:/VulkanSDK/1.4.309.0/Bin/slang-glslangd.dll C:/VulkanSDK/1.4.309.0/Bin/slang-rt.dll C:/VulkanSDK/1.4.309.0/Bin/slang-rtd.dll C:/VulkanSDK/1.4.309.0/Bin/slang.dll C:/VulkanSDK/1.4.309.0/Bin/slangc.exe C:/VulkanSDK/1.4.309.0/Bin/slangd.dll C:/VulkanSDK/1.4.309.0/Bin/slangd.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-as.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-cfg.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-cross-c-shared.dll C:/VulkanSDK/1.4.309.0/Bin/spirv-cross-c-sharedd.dll C:/VulkanSDK/1.4.309.0/Bin/spirv-cross.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-dis.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-link.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-lint.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-objdump.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-opt.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-reduce.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-reflect-pp.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-reflect.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-remap.exe C:/VulkanSDK/1.4.309.0/Bin/spirv-val.exe C:/VulkanSDK/1.4.309.0/Bin/vkconfig-gui.exe C:/VulkanSDK/1.4.309.0/Bin/vkconfig.exe C:/VulkanSDK/1.4.309.0/Bin/vkcube.exe C:/VulkanSDK/1.4.309.0/Bin/vkcubepp.exe C:/VulkanSDK/1.4.309.0/Bin/vkvia.exe C:/VulkanSDK/1.4.309.0/Bin/vulkanCapsViewer.exe C:/VulkanSDK/1.4.309.0/Bin/vulkaninfoSDK.exe D:/RTXPT/bin/ShaderDynamic/Tools/vk/x64
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/bin/ShaderDynamic/Tools/copyVK.marker
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\CMAKEFILES\1E3E7F8FD4C6E835428AFBABE12EA0FB\SHADERDYNAMICASSETS.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\RTXPT\RTXPT\CMAKELISTS.TXT
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/Rtxpt/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
