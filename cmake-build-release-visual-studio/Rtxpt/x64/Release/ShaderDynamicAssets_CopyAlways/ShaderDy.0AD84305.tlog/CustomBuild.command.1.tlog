^D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\CMAKEFILES\1E3E7F8FD4C6E835428AFBABE12EA0FB\SHADERDYNAMICASSETS_COPYALWAYS.RULE
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic/Source/Rtxpt
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E copy_directory_if_different D:/RTXPT/Rtxpt/Shaders D:/RTXPT/bin/ShaderDynamic/Source/Rtxpt
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic/Source/External/donut/shaders
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E copy_directory_if_different D:/RTXPT/External/Donut/include/donut/shaders D:/RTXPT/bin/ShaderDynamic/Source/External/donut/shaders
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/bin/ShaderDynamic/Source/External/NVAPI
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different D:/RTXPT/External/NVAPI/nvHLSLExtns.h D:/RTXPT/External/NVAPI/nvHLSLExtnsInternal.h D:/RTXPT/External/NVAPI/nvShaderExtnEnums.h D:/RTXPT/bin/ShaderDynamic/Source/External/NVAPI
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\RTXPT\RTXPT\CMAKELISTS.TXT
setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/Rtxpt/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
