d唌驁紣 `      .drectve        ~  9               
 .debug$S        搓 �:  N%        @ B.debug$T        l   �%             @ B.rdata          @   
&             @ @@.text$mn        �   J& '         P`.debug$S        L  ' e+        @B.text$mn        :   �, �,         P`.debug$S          �, �.        @B.text$mn           �/ �/         P`.debug$S        �   �/ b0        @B.text$mn            �0 �0         P`.debug$S        �   �0 �1        @B.text$mn            �1              P`.debug$S        4  �1  5        @B.text$mn           �5              P`.debug$S          �5 �6        @B.text$mn        7  7 N8         P`.debug$S        D
  v8 築     D   @B.text$x            bE nE         P`.text$x            xE 圗         P`.text$x            扙          P`.text$mn           珽              P`.debug$S        �   癊 團        @B.text$mn        �  腇 扝         P`.debug$S          淗      |   @B.text$x            |` 坄         P`.text$x            抈 瀈         P`.text$x            ╜ 碻         P`.text$x            綻 蔪         P`.text$x            訿 鋊         P`.text$x            頯          P`.text$x            a a         P`.text$x            "a 2a         P`.text$mn        <   <a xa         P`.debug$S        0  朼 芺     
   @B.text$mn        <   *c fc         P`.debug$S        L  刢 衐     
   @B.text$mn        !   4e Ue         P`.debug$S        <  ie         @B.text$mn        2   醘 g         P`.debug$S        <  'g ch        @B.text$mn           踙              P`.debug$S        �  鬶 鄇        @B.text$mn           0k              P`.debug$S        (  Ik qm        @B.text$mn        "   羗              P`.debug$S        �  鉳 {o        @B.text$mn        "   p              P`.debug$S        �  =p 裶        @B.text$mn        "   qr              P`.debug$S        �  搑 t        @B.text$mn        "   縯              P`.debug$S        �  醫 mv        @B.text$mn        e   
w rw         P`.debug$S        �  恮 8|        @B.text$mn        [    } [}         P`.debug$S          o} w�        @B.text$mn           S�              P`.debug$S        �  V� 顑        @B.text$mn        }   *�          P`.debug$S        �  粍 噳        @B.text$mn        K   c�              P`.debug$S        �  畩 帉        @B.text$mn        K   �              P`.debug$S        �  e� E�        @B.text$mn        K   褟              P`.debug$S        �  � 饝        @B.text$mn        K   |�              P`.debug$S        �  菕 洈        @B.text$mn        .   '� U�         P`.debug$S        �  i� �        @B.text$mn        `   =� 潡         P`.debug$S        �  睏 u�        @B.text$mn        ?   )� h�         P`.debug$S        \  |� 販        @B.text$mn          P� 蠠         P`.debug$S          贌 愍     n   @B.text$mn        x  9� 卑         P`.debug$S        x  习 G�     T   @B.text$mn        `   徏 锛         P`.debug$S        �  � 丝        @B.text$mn           �              P`.debug$S        �   偫 b�        @B.text$mn           灹 绷         P`.debug$S        �   帕 ┞        @B.text$mn           崖 渎         P`.debug$S        �    孛        @B.text$mn        !   � 5�         P`.debug$S        0  ?� o�        @B.text$mn        B    砼         P`.debug$S           � �        @B.text$mn        B   G� 壡         P`.debug$S           啡        @B.text$mn        B   笕 5�         P`.debug$S        �   S� O�        @B.text$mn        H   嬍              P`.debug$S        �  邮 椞        @B.text$mn        �   =�         P`.debug$S        8  U� 嶋     �   @B.text$x            Y� e�         P`.text$x            o� {�         P`.text$x            咈 戲         P`.text$x            涷 蛤         P`.text$x             买 怛         P`.text$mn        T  祢 @�         P`.debug$S          犍      �   @B.text$x            �! �!         P`.text$x            �! �!         P`.text$mn           �!              P`.debug$S        0  �! #        @B.text$mn        ;   e#              P`.debug$S          �# �&        @B.text$mn          �' �(         P`.debug$S        p  ) �-     (   @B.text$x            / #/         P`.text$mn        �  -/ 2         P`.debug$S        
  \2 h?     j   @B.text$x            孋 楥         P`.text$x             瓹         P`.text$x            窩 腃         P`.text$x            蜟 贑         P`.text$mn           銫 D         P`.debug$S        H  D ]E        @B.text$mn           璄              P`.debug$S          矱 稦        @B.text$mn        d  驠 WI         P`.debug$S        (  滻 臥     8   @B.text$x            鮎 S         P`.text$mn        �   S 碨         P`.debug$S          萐 躘     <   @B.text$x            4^ @^         P`.text$x            J^ V^         P`.text$x            `^ l^         P`.text$mn           v^              P`.debug$S        L  坁 訽        @B.text$mn        /   $` S`         P`.debug$S        h  g` 蟖        @B.text$mn        .   b 9b         P`.debug$S        �  Mb 	d     
   @B.text$mn            md 峝         P`.debug$S        �   玠 oe        @B.text$mn           玡 緀         P`.debug$S        �   萫 渇        @B.xdata             豧             @0@.pdata             靎 鴉        @0@.xdata             g             @0@.pdata             g *g        @0@.xdata             Hg             @0@.pdata             Tg `g        @0@.xdata             ~g             @0@.pdata             唃 抔        @0@.xdata             癵             @0@.pdata             糶 萭        @0@.xdata             鎔             @0@.pdata             頶 鷊        @0@.xdata             h             @0@.pdata             $h 0h        @0@.xdata             Nh             @0@.pdata             Vh bh        @0@.xdata             �h             @0@.pdata             坔 攈        @0@.xdata             瞙             @0@.pdata             篽 苃        @0@.xdata             鋒 鬶        @0@.pdata             i i        @0@.xdata          	   2i ;i        @@.xdata             Oi Ui        @@.xdata             _i             @@.xdata             bi ri        @0@.pdata             唅 抜        @0@.xdata          	   癷 筰        @@.xdata             蚷 觟        @@.xdata             輎             @@.xdata             鈏 騣        @0@.pdata             j j        @0@.xdata          	   0j 9j        @@.xdata             Mj Sj        @@.xdata             ]j             @@.xdata             `j |j        @0@.pdata             恓 渏        @0@.xdata          	   簀 胘        @@.xdata             譲 輏        @@.xdata             鏹             @@.xdata             阩             @0@.pdata             騤         @0@.xdata             k             @0@.pdata             $k 0k        @0@.xdata             Nk jk        @0@.pdata             ~k 妅        @0@.xdata          	   ╧ 眐        @@.xdata             舓 踜        @@.xdata             l             @@.xdata             
l &l        @0@.pdata             :l Fl        @0@.xdata          	   dl ml        @@.xdata          2   乴 砽     	   @@.xdata             
m             @@.voltbl            m                .xdata             %m Am        @0@.pdata             Um am        @0@.xdata          	   m 坢        @@.xdata             渕         @@.xdata             琺             @@.voltbl            竚                .xdata          $   衜 鬽        @0@.pdata             n n        @0@.xdata          	   2n ;n        @@.xdata             On mn        @@.xdata          0   焠             @@.voltbl            蟦                .xdata          $   觧 鱪        @0@.pdata             o o        @0@.xdata          	   5o >o        @@.xdata             Ro co        @@.xdata             乷             @@.xdata             峯         @0@.pdata             祇 羙        @0@.xdata          	   遫 鑟        @@.xdata          (   黲 $p        @@.xdata             jp             @@.xdata             yp             @0@.pdata             乸 峱        @0@.xdata             玴 籶        @0@.pdata             蟨 踦        @0@.xdata          	   鵳 q        @@.xdata             q q        @@.xdata             &q             @@.xdata          (   +q Sq        @0@.pdata             gq sq        @0@.xdata          	   憅 歲        @@.xdata          .   畄 躴        @@.xdata          !   ,r             @@.xdata             Mr             @0@.pdata             Ur ar        @0@.xdata             r             @0@.pdata             噐 搑        @0@.voltbl            眗                .xdata          (   祌 輗        @0@.pdata             駌 齬        @0@.xdata          	   s $s        @@.xdata          .   8s fs        @@.xdata             瑂             @@.xdata             膕 鄐        @0@.pdata             魋  t        @0@.xdata          	   t 't        @@.xdata             ;t Gt        @@.xdata             [t             @@.xdata             ct             @0@.pdata             kt wt        @0@.xdata             晅 ﹖        @0@.pdata             莟 觮        @0@.xdata             駎 u        @0@.pdata             u +u        @0@.voltbl            Iu               .xdata             Ku             @0@.pdata             Su _u        @0@.xdata             }u 憉        @0@.pdata             痷 籾        @0@.xdata             賣 閡        @0@.pdata             v v        @0@.voltbl            1v               .xdata             3v             @0@.pdata             ;v Gv        @0@.xdata             ev yv        @0@.pdata             梫         @0@.xdata             羦 裿        @0@.pdata             飗 鹶        @0@.voltbl            w               .xdata             w             @0@.pdata             #w /w        @0@.xdata             Mw aw        @0@.pdata             w 媤        @0@.xdata             ﹚ 箇        @0@.pdata             譿 鉾        @0@.voltbl            x               .xdata             x x        @0@.pdata             'x 3x        @0@.xdata          	   Qx Zx        @@.xdata             nx tx        @@.xdata             ~x             @@.xdata             亁 憍        @0@.pdata              眡        @0@.xdata          	   蟲 豿        @@.xdata             靫 騲        @@.xdata             黿             @@.xdata             �x y        @0@.pdata             /y ;y        @0@.xdata          	   Yy by        @@.xdata             vy |y        @@.xdata          
   唝             @@.voltbl            恲               .xdata             抷             @0@.pdata             歽         @0@.xdata             膟             @0@.pdata             貀 鋣        @0@.voltbl            z               .xdata             z z        @0@.pdata             (z 4z        @0@.xdata             Rz Wz        @@.xdata             az             @@.xdata             dz tz        @0@.pdata             坺 攝        @0@.xdata             瞶 穤        @@.xdata             羫             @@.xdata             膠             @0@.pdata             蘻 貁        @0@.xdata             鰖             @0@.pdata              
{        @0@.xdata             ({             @0@.pdata             0{ <{        @0@.xdata             Z{             @0@.pdata             b{ n{        @0@.rdata             寋         @@@.rdata             聓             @@@.rdata             詛 靮        @@@.rdata             
| "|        @@@.rdata             @|             @@@.xdata$x           U| q|        @@@.xdata$x           厊         @@@.data$r         /   縷 顋        @@�.xdata$x        $   鴟 }        @@@.data$r         $   0} T}        @@�.xdata$x        $   ^} 倉        @@@.data$r         $   杴 簘        @@�.xdata$x        $   膤 鑮        @@@.rdata             鼄             @@@.data               ~             @ @�.rdata          '   ,~             @@@.rdata             S~             @@@.rdata          8   r~             @@@.rdata             獈             @@@.rdata             粇             @@@.rdata             蕕             @0@.rdata             衺             @0@.rdata          (   讆 �~        @@@.data$r         I   1 z        @P�.rdata$r        $   � �        @@@.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $   2� V�        @@@.rdata$r           t� 垁        @@@.rdata$r           拃         @@@.rdata$r        $   簚 迉        @@@.rdata$r        $   騹 �        @@@.rdata$r           4� H�        @@@.rdata$r           R� n�        @@@.rdata$r        $   寔 皝        @@@.data$rs        *   膩 顏        @@�.rdata$r           鴣 �        @@@.rdata$r           � "�        @@@.rdata$r        $   ,� P�        @@@.rdata$r        $   d� 垈        @@@.data$rs        �    ,�        @P�.rdata$r           6� J�        @@@.rdata$r           T� h�        @@@.rdata$r        $   |� 爟        @@@.rdata             磧             @0@.rdata             竷             @@@.rdata             纼             @@@.debug$S        �   葍 \�        @B.debug$S        4   p�         @B.debug$S        4   竸 靹        @B.debug$S        @    � @�        @B.chks64         h  T�              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   �  k     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\EnvMapImportanceSamplingBaker.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $tf  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $app  $vfs  $math 	 $colors  $log 	 $render  $Json 	 $stdext  $ImGui  �   �  P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >::_Minimum_asan_allocation_alignment O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy < U  �X呩std::integral_constant<__int64,31556952>::value # U  2 std::ratio<50,438291>::num ' U  �� std::ratio<50,438291>::den ,:    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>::_Multi /:   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>::_Standard N U  ��Q std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Nx2 ( U  �X呩std::ratio<31556952,1>::num J U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Dx2 $ U   std::ratio<31556952,1>::den L U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx2 P U  ��Q std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gx K U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gy � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 2 U   std::integral_constant<__int64,24>::value E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gx D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gy � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >::_Minimum_asan_allocation_alignment  U   std::ratio<1,24>::num  U   std::ratio<1,24>::den  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 ( 淝   ImDrawFlags_RoundCornersTopLeft ) 淝    ImDrawFlags_RoundCornersTopRight + 淝  @ ImDrawFlags_RoundCornersBottomLeft N U  �r ( std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Nx2 , 淝  � ImDrawFlags_RoundCornersBottomRight % 淝   ImDrawFlags_RoundCornersNone J U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Dx2 ! <q    COINITBASE_MULTITHREADED $ 淝  � ImDrawFlags_RoundCornersAll Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1 L U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx1  U   std::ratio<1,12>::num L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx2  U   std::ratio<1,12>::den P U  �r ( std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gx K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gy M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy � :   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified 2 U  
 std::integral_constant<__int64,10>::value ; U  �r ( std::integral_constant<__int64,2629746>::value # U  
 std::ratio<10,438291>::num ' U  �� std::ratio<10,438291>::den ; :   std::atomic<unsigned __int64>::is_always_lock_free E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment �   覻 ) <   nvrhi::ObjectTypes::SharedHandle - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView O U  �X呩std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Nx2 - :    std::chrono::system_clock::is_steady 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView K U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Dx2 : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView M U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx1 < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx1 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx2 / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool $ U   std::ratio<1,10000000>::num Q U  �X呩std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx2 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet ( U  ��枠 std::ratio<1,10000000>::den L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gx 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gy , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo 6 :   std::_Iterator_base0::_Unwrap_when_unverified 1 U   std::integral_constant<__int64,5>::value C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity # U   std::ratio<5,2629746>::num ' U  �r ( std::ratio<5,2629746>::den 7 :   std::_Iterator_base12::_Unwrap_when_unverified d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size L U  ��Q std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Nx2 H U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Dx2 j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size J U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx1 A _   std::allocator<bool>::_Minimum_asan_allocation_alignment J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx2 N U  ��Q std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx2 I U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gx I U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gy 4 U  �std::integral_constant<__int64,1440>::value � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment   U   std::ratio<1,1440>::num   U  �std::ratio<1,1440>::den ?_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Bucket_size ?_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Min_buckets 9:    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Multi  U   std::ratio<1,1>::num  U   std::ratio<1,1>::den I _   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment :    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos :   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard C :   std::atomic<std::chrono::tzdb_list *>::is_always_lock_free c _   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment / 羟    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV + 羟   D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER ' 羟   D3D12_DESCRIPTOR_HEAP_TYPE_RTV ' 羟   D3D12_DESCRIPTOR_HEAP_TYPE_DSV 8 :   std::atomic<unsigned long>::is_always_lock_free � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( U  ��枠 std::ratio<10000000,1>::num $ U   std::ratio<10000000,1>::den P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy ( �    D3D12_DESCRIPTOR_RANGE_TYPE_SRV ( �   D3D12_DESCRIPTOR_RANGE_TYPE_UAV ( �   D3D12_DESCRIPTOR_RANGE_TYPE_CBV < U  ��枠 std::integral_constant<__int64,10000000>::value / <  � nvrhi::rt::cluster::kClasByteAlignment . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > >::_Minimum_asan_allocation_alignment 3 ]�    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE 2 ]�   D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS & ]�   D3D12_ROOT_PARAMETER_TYPE_CBV & ]�   D3D12_ROOT_PARAMETER_TYPE_SRV + :    std::_Aligned_storage<72,8>::_Fits * :    std::_Aligned<72,8,char,0>::_Fits + :    std::_Aligned<72,8,short,0>::_Fits - :   std::chrono::steady_clock::is_steady . :    std::integral_constant<bool,0>::value ' >q  �   CLSCTX_ACTIVATE_X86_SERVER ) :   std::_Aligned<72,8,int,0>::_Fits & U   std::ratio<1,1000000000>::num * U  � 蕷;std::ratio<1,1000000000>::den ( �    std::chrono::local_info::unique - �   std::chrono::local_info::nonexistent + �   std::chrono::local_info::ambiguous 4 �    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK / �   D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK N :   std::_Trivial_cat<char,char,char &,char &>::_Same_size_and_compatible K :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_constructible H :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_assignable + g   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 g   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - g   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 g   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS % _   std::ctype<char>::table_size � :   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 * g   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 g   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 g   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A g   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP M U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gy . :   std::integral_constant<bool,1>::value O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 * U  � 蕷;std::ratio<1000000000,1>::num & U   std::ratio<1000000000,1>::den U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value , Dq   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment 1 U  
� 牳0F  std::ratio<3600000000000,1>::num -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets ) U   std::ratio<3600000000000,1>::den ':    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi ; ;  ���donut::app::StreamlineInterface::kInvalidFloat : <  �����donut::app::StreamlineInterface::kInvalidUint  �    LightType_None  �   LightType_Directional  �   LightType_Spot  �   LightType_Point i _   std::allocator<std::shared_ptr<donut::engine::TextureData> >::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable ( <   donut::math::vector<int,2>::DIM F U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Nx2 F U  �std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Dx2 C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE * :    std::chrono::utc_clock::is_steady E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx1   U  �std::ratio<1000,1>::num   U   std::ratio<1000,1>::den H U  �std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gy d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val 4 U  �std::integral_constant<__int64,1000>::value m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size j _   std::allocator<std::shared_ptr<donut::engine::TextureData> *>::_Minimum_asan_allocation_alignment * :    std::chrono::tai_clock::is_steady 6 v,  �奮�std::chrono::tai_clock::_Tai_epoch_adjust _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment * 銮    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW 2 銮   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED . 銮   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH 8 銮   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW 7 銮   D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW . 銮   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT : 銮   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW : 銮   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW ; 銮   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW 3 銮  	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS  &q    NODE_INVALID  &q   NODE_ELEMENT  &q   NODE_ATTRIBUTE � �   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Minimum_map_size  &q   NODE_TEXT  &q   NODE_CDATA_SECTION  &q   NODE_ENTITY_REFERENCE  &q   NODE_ENTITY $ &q   NODE_PROCESSING_INSTRUCTION  &q   NODE_COMMENT  &q  	 NODE_DOCUMENT  &q  
 NODE_DOCUMENT_TYPE  &q   NODE_DOCUMENT_FRAGMENT  4q    XMLELEMTYPE_ELEMENT  4q   XMLELEMTYPE_TEXT  4q   XMLELEMTYPE_COMMENT  4q   XMLELEMTYPE_DOCUMENT  4q   XMLELEMTYPE_DTD  4q   XMLELEMTYPE_PI � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable * :    std::chrono::gps_clock::is_steady 6 v,  �w�*韘td::chrono::gps_clock::_Gps_epoch_adjust  @q   VT_I2  @q   VT_I4  @q   VT_BSTR  @q  	 VT_DISPATCH  @q  
 VT_ERROR  @q   VT_VARIANT  @q  
 VT_UNKNOWN 3 竡   DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos  @q   VT_I1  @q   VT_I8 2 <  �����std::shared_timed_mutex::_Max_readers  @q  $ VT_RECORD L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2  @q  � �VT_RESERVED N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy   �   std::_Iosb<int>::skipws ! �   std::_Iosb<int>::unitbuf # �   std::_Iosb<int>::uppercase " �   std::_Iosb<int>::showbase # �   std::_Iosb<int>::showpoint ! �    std::_Iosb<int>::showpos  �  @ std::_Iosb<int>::left 5 :    std::filesystem::_File_time_clock::is_steady  �  � std::_Iosb<int>::right " �   std::_Iosb<int>::internal  �   std::_Iosb<int>::dec > U  � 蕷;std::integral_constant<__int64,1000000000>::value  �   std::_Iosb<int>::oct  �   std::_Iosb<int>::hex $ �   std::_Iosb<int>::scientific  緌    TYSPEC_CLSID  緌   TYSPEC_FILEEXT  �    std::_Iosb<int>::fixed  緌   TYSPEC_MIMETYPE  緌   TYSPEC_FILENAME  緌   TYSPEC_PROGID " �   0std::_Iosb<int>::hexfloat  緌   TYSPEC_PACKAGENAME # �   @std::_Iosb<int>::boolalpha " �  � �std::_Iosb<int>::_Stdio % �  �std::_Iosb<int>::adjustfield # �   std::_Iosb<int>::basefield $ �   0std::_Iosb<int>::floatfield ! �    std::_Iosb<int>::goodbit   �   std::_Iosb<int>::eofbit ! �   std::_Iosb<int>::failbit   �   std::_Iosb<int>::badbit  �   std::_Iosb<int>::in  �   std::_Iosb<int>::out  �   std::_Iosb<int>::ate  �   std::_Iosb<int>::app  �   std::_Iosb<int>::trunc # �  @ std::_Iosb<int>::_Nocreate $ �  � std::_Iosb<int>::_Noreplace   �    std::_Iosb<int>::binary  �    std::_Iosb<int>::beg  �   std::_Iosb<int>::cur  �   std::_Iosb<int>::end , �  @ std::_Iosb<int>::_Default_open_prot M U  �r ( std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Nx2 I U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx2 O U  �r ( std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx2 J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gx J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gy i _   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >::_Bytes n �   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >::_Block_size � �   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Block_size � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable N U  �X呩std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Dx2 T U  �r ( std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx2 P U  �X呩std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gy _ _   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx1 V U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx1 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx2 Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gy F U  
� 泌�  std::integral_constant<__int64,26297460000000>::value L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 K U  ��Q std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Dx2 * U   std::ratio<1,26297460000000>::num N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 2 U  
� 泌�  std::ratio<1,26297460000000>::den N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx1 O _   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx1 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy M U  ��Q std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gx H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gy U U  �X呩std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Nx2 Q U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Dx2 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx1 W U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx1 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx2 W U  �X呩std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx2 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gy G U  
� <$A std::integral_constant<__int64,315569520000000>::value � _   std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment + U   std::ratio<1,315569520000000>::num 3 U  
� <$A std::ratio<1,315569520000000>::den R U  ��Q std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Nx2 � _   std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment N U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Dx2 :    std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi :   std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx1 T U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gy D U  
� 纈*�   std::integral_constant<__int64,864000000000>::value  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den  :    std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi #:   std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard ( U   std::ratio<1,864000000000>::num 0 U  
� 纈*�   std::ratio<1,864000000000>::den � _   std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment � _   std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment Z _   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment � _   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment   U   std::ratio<1,1000>::num   U  �std::ratio<1,1000>::den D _   ��std::basic_string_view<char,std::char_traits<char> >::npos Z:    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]:   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard � _   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_assignable  觪   PowerUserMaximum # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den  �    DVEXTENT_CONTENT J U  std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Dx2 : _    std::integral_constant<unsigned __int64,0>::value P U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx1   U   std::ratio<1,3600>::num   U  std::ratio<1,3600>::den Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx2 L U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gx K U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gy /_   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size /_   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets ):    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi 9 �   std::chrono::_Time_parse_fields::_Era_begin_wday * <   donut::math::vector<float,3>::DIM S _   std::allocator<std::chrono::time_zone>::_Minimum_asan_allocation_alignment d _   std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> >::_Minimum_asan_allocation_alignment 3_   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size 3_   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets -:    std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 H U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gx Z _   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gy 4 U  std::integral_constant<__int64,3600>::value / U   std::ratio<1,1000000000000000000>::num 7 U  
�  dС多
std::ratio<1,1000000000000000000>::den # $q   BINDSTATUS_FINDINGRESOURCE  $q   BINDSTATUS_CONNECTING  $q   BINDSTATUS_REDIRECTING % $q   BINDSTATUS_BEGINDOWNLOADDATA # $q   BINDSTATUS_DOWNLOADINGDATA # $q   BINDSTATUS_ENDDOWNLOADDATA + $q   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( $q   BINDSTATUS_INSTALLINGCOMPONENTS ) $q  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # $q  
 BINDSTATUS_USINGCACHEDCOPY " $q   BINDSTATUS_SENDINGREQUEST $ $q   BINDSTATUS_CLASSIDAVAILABLE % $q  
 BINDSTATUS_MIMETYPEAVAILABLE * $q   BINDSTATUS_CACHEFILENAMEAVAILABLE & $q   BINDSTATUS_BEGINSYNCOPERATION $ $q   BINDSTATUS_ENDSYNCOPERATION # $q   BINDSTATUS_BEGINUPLOADDATA ! $q   BINDSTATUS_UPLOADINGDATA ! $q   BINDSTATUS_ENDUPLOADDATA # $q   BINDSTATUS_PROTOCOLCLASSID  $q   BINDSTATUS_ENCODING - $q   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( $q   BINDSTATUS_CLASSINSTALLLOCATION  $q   BINDSTATUS_DECODING & $q   BINDSTATUS_LOADINGMIMEHANDLER , $q   BINDSTATUS_CONTENTDISPOSITIONATTACH ( $q   BINDSTATUS_FILTERREPORTMIMETYPE ' $q   BINDSTATUS_CLSIDCANINSTANTIATE % $q   BINDSTATUS_IUNKNOWNAVAILABLE  $q   BINDSTATUS_DIRECTBIND  $q   BINDSTATUS_RAWMIMETYPE " $q    BINDSTATUS_PROXYDETECTING   $q  ! BINDSTATUS_ACCEPTRANGES  $q  " BINDSTATUS_COOKIE_SENT + $q  # BINDSTATUS_COMPACT_POLICY_RECEIVED % $q  $ BINDSTATUS_COOKIE_SUPPRESSED ( $q  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' $q  & BINDSTATUS_COOKIE_STATE_ACCEPT ' $q  ' BINDSTATUS_COOKIE_STATE_REJECT ' $q  ( BINDSTATUS_COOKIE_STATE_PROMPT & $q  ) BINDSTATUS_COOKIE_STATE_LEASH * $q  * BINDSTATUS_COOKIE_STATE_DOWNGRADE  $q  + BINDSTATUS_POLICY_HREF  $q  , BINDSTATUS_P3P_HEADER + $q  - BINDSTATUS_SESSION_COOKIE_RECEIVED . $q  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + $q  / BINDSTATUS_SESSION_COOKIES_ALLOWED   $q  0 BINDSTATUS_CACHECONTROL . $q  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) $q  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & $q  3 BINDSTATUS_PUBLISHERAVAILABLE ( $q  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ $q  5 BINDSTATUS_SSLUX_NAVBLOCKED , $q  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , $q  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " $q  8 BINDSTATUS_64BIT_PROGRESS  $q  8 BINDSTATUS_LAST  $q  9 BINDSTATUS_RESERVED_0  $q  : BINDSTATUS_RESERVED_1  $q  ; BINDSTATUS_RESERVED_2  $q  < BINDSTATUS_RESERVED_3  $q  = BINDSTATUS_RESERVED_4  $q  > BINDSTATUS_RESERVED_5  $q  ? BINDSTATUS_RESERVED_6  $q  @ BINDSTATUS_RESERVED_7  $q  A BINDSTATUS_RESERVED_8  $q  B BINDSTATUS_RESERVED_9  $q  C BINDSTATUS_RESERVED_A  $q  D BINDSTATUS_RESERVED_B  $q  E BINDSTATUS_RESERVED_C  $q  F BINDSTATUS_RESERVED_D  $q  G BINDSTATUS_RESERVED_E  $q  H BINDSTATUS_RESERVED_F  $q  I BINDSTATUS_RESERVED_10  $q  J BINDSTATUS_RESERVED_11  $q  K BINDSTATUS_RESERVED_12  $q  L BINDSTATUS_RESERVED_13  $q  M BINDSTATUS_RESERVED_14 J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos $ 昵    D3D12_LIFETIME_STATE_IN_USE � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment V _   std::allocator<donut::app::IRenderPass *>::_Minimum_asan_allocation_alignment U :   std::_String_view_iterator<std::char_traits<char> >::_Unwrap_when_unverified m_   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m_   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g:    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi * <   donut::math::vector<float,4>::DIM \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment * 錏        donut::math::lumaCoefficients ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy o _   std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >::_Minimum_asan_allocation_alignment - �   std::_Invoker_pmf_refwrap::_Strategy J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos X _   std::allocator<std::chrono::time_zone_link>::_Minimum_asan_allocation_alignment - �   std::_Invoker_pmf_pointer::_Strategy * <   donut::math::vector<float,2>::DIM  Bq    CIP_DISK_FULL  Bq   CIP_ACCESS_DENIED ! Bq   CIP_NEWER_VERSION_EXISTS ! Bq   CIP_OLDER_VERSION_EXISTS  Bq   CIP_NAME_CONFLICT 1 Bq   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + Bq   CIP_EXE_SELF_REGISTERATION_TIMEOUT  Bq   CIP_UNSAFE_TO_ABORT  Bq   CIP_NEED_REBOOT , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy " 唓    Uri_PROPERTY_ABSOLUTE_URI - �   std::_Invoker_pmd_pointer::_Strategy  唓   Uri_PROPERTY_USER_NAME  唓   Uri_PROPERTY_HOST_TYPE  唓   Uri_PROPERTY_ZONE ? �   D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG1  萹    Uri_HOST_UNKNOWN  萹   Uri_HOST_DNS c _   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment  萹   Uri_HOST_IPV4  萹   Uri_HOST_IPV6 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity  ;  �  �donut::math::NaN G _   std::allocator<ImDrawVert>::_Minimum_asan_allocation_alignment U _   std::allocator<std::chrono::leap_second>::_Minimum_asan_allocation_alignment L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos e _   std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >::_Minimum_asan_allocation_alignment Z _   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment 1 嵘    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES  q   BINDSTRING_HEADERS   q   BINDSTRING_ACCEPT_MIMES  q   BINDSTRING_EXTRA_URL  q   BINDSTRING_LANGUAGE  q   BINDSTRING_USERNAME  q   BINDSTRING_PASSWORD  q   BINDSTRING_UA_PIXELS  q   BINDSTRING_UA_COLOR  q  	 BINDSTRING_OS  q  
 BINDSTRING_USER_AGENT $ q   BINDSTRING_ACCEPT_ENCODINGS  q   BINDSTRING_POST_COOKIE " q  
 BINDSTRING_POST_DATA_MIME  q   BINDSTRING_URL  q   BINDSTRING_IID ' q   BINDSTRING_FLAG_BIND_TO_OBJECT $ q   BINDSTRING_PTR_BIND_CONTEXT  q   BINDSTRING_XDR_ORIGIN   q   BINDSTRING_DOWNLOADPATH  q   BINDSTRING_ROOTDOC_URL $ q   BINDSTRING_INITIAL_FILENAME " q   BINDSTRING_PROXY_USERNAME " q   BINDSTRING_PROXY_PASSWORD ) <   donut::math::frustum::numCorners ! q   BINDSTRING_ENTERPRISE_ID  q   BINDSTRING_DOC_URL a _   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment K _   std::allocator<unsigned short>::_Minimum_asan_allocation_alignment  *q   PARSE_CANONICALIZE  *q   PARSE_FRIENDLY  *q   PARSE_SECURITY_URL  *q   PARSE_ROOTDOCUMENT  *q   PARSE_DOCUMENT  *q   PARSE_ANCHOR ! *q   PARSE_ENCODE_IS_UNESCAPE  *q   PARSE_DECODE_IS_ESCAPE  *q  	 PARSE_PATH_FROM_URL  *q  
 PARSE_URL_FROM_PATH  *q   PARSE_MIME  *q   PARSE_SERVER  *q  
 PARSE_SCHEMA  *q   PARSE_SITE  *q   PARSE_DOMAIN  *q   PARSE_LOCATION  *q   PARSE_SECURITY_DOMAIN  *q   PARSE_ESCAPE  wr   PSU_DEFAULT  :q   QUERY_EXPIRATION_DATE " :q   QUERY_TIME_OF_LAST_CHANGE  :q   QUERY_CONTENT_ENCODING  :q   QUERY_CONTENT_TYPE  :q   QUERY_REFRESH  :q   QUERY_RECOMBINE  :q   QUERY_CAN_NAVIGATE  :q   QUERY_USES_NETWORK  :q  	 QUERY_IS_CACHED   :q  
 QUERY_IS_INSTALLEDENTRY " :q   QUERY_IS_CACHED_OR_MAPPED  :q   QUERY_USES_CACHE  :q  
 QUERY_IS_SECURE  :q   QUERY_IS_SAFE  膓    ServerApplication ! :q   QUERY_USES_HISTORYFOLDER  Fs    IdleShutdown B _   std::allocator<float>::_Minimum_asan_allocation_alignment ' U  �r ( std::ratio<2629746,1>::num # U   std::ratio<2629746,1>::den : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask = <   donut::engine::c_MaxRenderPassConstantBufferVersions R :    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified  8q    FEATURE_OBJECT_CACHING  8q   FEATURE_ZONE_ELEVATION ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits  8q   FEATURE_MIME_HANDLING  8q   FEATURE_MIME_SNIFFING ; �   std::_Floating_type_traits<double>::_Exponent_bits $ 8q   FEATURE_WINDOW_RESTRICTIONS E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent . �   donut::math::box<float,2>::numCorners & 8q   FEATURE_WEBOC_POPUPMANAGEMENT  8q   FEATURE_BEHAVIORS G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent $ 8q   FEATURE_DISABLE_MK_PROTOCOL & 8q   FEATURE_LOCALMACHINE_LOCKDOWN ; �  �std::_Floating_type_traits<double>::_Exponent_bias  8q  	 FEATURE_SECURITYBAND ( 8q  
 FEATURE_RESTRICT_ACTIVEXINSTALL 8 �  ? std::_Floating_type_traits<double>::_Sign_shift & 8q   FEATURE_VALIDATE_NAVIGATE_URL & 8q   FEATURE_RESTRICT_FILEDOWNLOAD < �  4 std::_Floating_type_traits<double>::_Exponent_shift ! 8q  
 FEATURE_ADDON_MANAGEMENT " 8q   FEATURE_PROTOCOL_LOCKDOWN / 8q   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE " 8q   FEATURE_SAFE_BINDTOOBJECT ; _  �std::_Floating_type_traits<double>::_Exponent_mask # 8q   FEATURE_UNC_SAVEDFILECHECK / 8q   FEATURE_GET_URL_DOM_FILEPATH_UNENCODED J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask   8q   FEATURE_TABBED_BROWSING  8q   FEATURE_SSLUX L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask * 8q   FEATURE_DISABLE_NAVIGATION_SOUNDS + 8q   FEATURE_DISABLE_LEGACY_COMPRESSION O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask & 8q   FEATURE_FORCE_ADDR_AND_STATUS  8q   FEATURE_XMLHTTP G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask ( 8q   FEATURE_DISABLE_TELNET_PROTOCOL  8q   FEATURE_FEEDS K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask $ 8q   FEATURE_BLOCK_INPUT_PROMPTS   Q�    D3D_DRIVER_TYPE_UNKNOWN ! Q�   D3D_DRIVER_TYPE_HARDWARE " Q�   D3D_DRIVER_TYPE_REFERENCE  Q�   D3D_DRIVER_TYPE_NULL ! Q�   D3D_DRIVER_TYPE_SOFTWARE W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified ) 柷    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED ) 柷   D3D_PRIMITIVE_TOPOLOGY_POINTLIST ( 柷   D3D_PRIMITIVE_TOPOLOGY_LINELIST ) 柷   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP , 柷   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST - 柷   D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP , 柷  
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ - 柷   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 0 柷   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 1 柷  
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 9 柷  ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST 9 柷  " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 9 柷  # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 9 柷  $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 9 柷  % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 9 柷  & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST b <    std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::fractional_width 9 柷  ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 9 柷  ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 9 柷  ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST : 柷  * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST : 柷  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST : 柷  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST : 柷  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST : 柷  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST : 柷  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST : 柷  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST : 柷  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST : 柷  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST : 柷  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST : 柷  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST : 柷  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST : 柷  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST : 柷  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST : 柷  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST : 柷  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST : 柷  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST : 柷  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST : 柷  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST : 柷  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST : 柷  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST : 柷  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST : 柷  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_assignable R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified   E�    D3D_PRIMITIVE_UNDEFINED  E�   D3D_PRIMITIVE_POINT  E�   D3D_PRIMITIVE_LINE  E�   D3D_PRIMITIVE_TRIANGLE  E�   D3D_PRIMITIVE_LINE_ADJ # E�   D3D_PRIMITIVE_TRIANGLE_ADJ , E�   D3D_PRIMITIVE_1_CONTROL_POINT_PATCH , E�  	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH , E�  
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH , E�   D3D_PRIMITIVE_4_CONTROL_POINT_PATCH , E�   D3D_PRIMITIVE_5_CONTROL_POINT_PATCH , E�  
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH , E�   D3D_PRIMITIVE_7_CONTROL_POINT_PATCH , E�   D3D_PRIMITIVE_8_CONTROL_POINT_PATCH , E�   D3D_PRIMITIVE_9_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_10_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_11_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_12_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_13_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_14_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_15_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_16_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_17_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_18_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_19_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_20_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_21_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_22_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_23_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_24_CONTROL_POINT_PATCH - E�    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH - E�  ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH - E�  " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH - E�  # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH - E�  $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH - E�  % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH - E�  & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH - E�  ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH ' �  ��std::chrono::year::_Year_min % �  �std::chrono::year::_Year_max "     D3D_SRV_DIMENSION_UNKNOWN !    D3D_SRV_DIMENSION_BUFFER $    D3D_SRV_DIMENSION_TEXTURE1D )    D3D_SRV_DIMENSION_TEXTURE1DARRAY $    D3D_SRV_DIMENSION_TEXTURE2D )    D3D_SRV_DIMENSION_TEXTURE2DARRAY &    D3D_SRV_DIMENSION_TEXTURE2DMS +    D3D_SRV_DIMENSION_TEXTURE2DMSARRAY $    D3D_SRV_DIMENSION_TEXTURE3D &   	 D3D_SRV_DIMENSION_TEXTURECUBE +   
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY #    D3D_SRV_DIMENSION_BUFFEREX ) <   donut::math::vector<bool,2>::DIM 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices � :   std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Same_size_and_compatible  裶    URLZONE_LOCAL_MACHINE  裶   URLZONE_INTRANET  裶   URLZONE_TRUSTED  裶   URLZONE_INTERNET � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_constructible ) <   donut::math::vector<bool,3>::DIM  炃    D3D_INCLUDE_LOCAL  炃   D3D_INCLUDE_SYSTEM � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_assignable � _   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment  樓    D3D_SVC_SCALAR  樓   D3D_SVC_VECTOR  樓   D3D_SVC_MATRIX_ROWS  樓   D3D_SVC_MATRIX_COLUMNS  樓   D3D_SVC_OBJECT  樓   D3D_SVC_STRUCT   樓   D3D_SVC_INTERFACE_CLASS " 樓   D3D_SVC_INTERFACE_POINTER # �        nvrhi::AllSubresources  芮   D3D_SVF_USERPACKED  芮   D3D_SVF_USED " 芮   D3D_SVF_INTERFACE_POINTER  ur    URLZONEREG_DEFAULT $ 芮   D3D_SVF_INTERFACE_PARAMETER  ur   URLZONEREG_HKLM ) <   donut::math::vector<bool,4>::DIM  M�    D3D_SVT_VOID  M�   D3D_SVT_BOOL  M�   D3D_SVT_INT  M�   D3D_SVT_FLOAT  M�   D3D_SVT_STRING  M�   D3D_SVT_TEXTURE  M�   D3D_SVT_TEXTURE1D  M�   D3D_SVT_TEXTURE2D  M�   D3D_SVT_TEXTURE3D  M�  	 D3D_SVT_TEXTURECUBE  M�  
 D3D_SVT_SAMPLER  M�   D3D_SVT_SAMPLER1D  M�   D3D_SVT_SAMPLER2D  M�  
 D3D_SVT_SAMPLER3D $ �   ��std::strong_ordering::less  M�   D3D_SVT_SAMPLERCUBE  M�   D3D_SVT_PIXELSHADER $ �    std::strong_ordering::equal  M�   D3D_SVT_VERTEXSHADER  M�   D3D_SVT_PIXELFRAGMENT  M�   D3D_SVT_VERTEXFRAGMENT  M�   D3D_SVT_UINT & �   std::strong_ordering::greater  M�   D3D_SVT_UINT8  M�   D3D_SVT_GEOMETRYSHADER  M�   D3D_SVT_RASTERIZER  M�   D3D_SVT_DEPTHSTENCIL  M�   D3D_SVT_BLEND  M�   D3D_SVT_BUFFER  M�   D3D_SVT_CBUFFER  M�   D3D_SVT_TBUFFER  M�   D3D_SVT_TEXTURE1DARRAY  M�   D3D_SVT_TEXTURE2DARRAY ! M�   D3D_SVT_RENDERTARGETVIEW ! M�   D3D_SVT_DEPTHSTENCILVIEW  M�    D3D_SVT_TEXTURE2DMS ! M�  ! D3D_SVT_TEXTURE2DMSARRAY ! M�  " D3D_SVT_TEXTURECUBEARRAY �:    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi  M�  # D3D_SVT_HULLSHADER  M�  $ D3D_SVT_DOMAINSHADER �:   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard " M�  % D3D_SVT_INTERFACE_POINTER  M�  & D3D_SVT_COMPUTESHADER  M�  ' D3D_SVT_DOUBLE  M�  ( D3D_SVT_RWTEXTURE1D ! M�  ) D3D_SVT_RWTEXTURE1DARRAY  M�  * D3D_SVT_RWTEXTURE2D ! M�  + D3D_SVT_RWTEXTURE2DARRAY  M�  , D3D_SVT_RWTEXTURE3D  M�  - D3D_SVT_RWBUFFER # M�  . D3D_SVT_BYTEADDRESS_BUFFER % M�  / D3D_SVT_RWBYTEADDRESS_BUFFER " M�  0 D3D_SVT_STRUCTURED_BUFFER $ M�  1 D3D_SVT_RWSTRUCTURED_BUFFER ) M�  2 D3D_SVT_APPEND_STRUCTURED_BUFFER * M�  3 D3D_SVT_CONSUME_STRUCTURED_BUFFER  S�   D3D_SIF_USERPACKED # S�   D3D_SIF_COMPARISON_SAMPLER $ S�   D3D_SIF_TEXTURE_COMPONENT_0 $ S�   D3D_SIF_TEXTURE_COMPONENT_1 # S�   D3D_SIF_TEXTURE_COMPONENTS � _   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment  =�    D3D_SIT_CBUFFER  =�   D3D_SIT_TBUFFER  =�   D3D_SIT_TEXTURE  =�   D3D_SIT_SAMPLER  =�   D3D_SIT_UAV_RWTYPED  =�   D3D_SIT_STRUCTURED ! =�   D3D_SIT_UAV_RWSTRUCTURED  =�   D3D_SIT_BYTEADDRESS " =�   D3D_SIT_UAV_RWBYTEADDRESS & =�  	 D3D_SIT_UAV_APPEND_STRUCTURED ' =�  
 D3D_SIT_UAV_CONSUME_STRUCTURED . =�   D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER ( =�   D3D_SIT_RTACCELERATIONSTRUCTURE . �   donut::math::box<float,3>::numCorners � :   std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_assignable  �   D3D_CBF_USERPACKED  枨    D3D_CT_CBUFFER  枨   D3D_CT_TBUFFER " 枨   D3D_CT_INTERFACE_POINTERS " 枨   D3D_CT_RESOURCE_BIND_INFO i _   std::allocator<std::shared_ptr<donut::app::RegisteredFont> >::_Minimum_asan_allocation_alignment  [�    D3D_NAME_UNDEFINED  [�   D3D_NAME_POSITION  [�   D3D_NAME_CLIP_DISTANCE  [�   D3D_NAME_CULL_DISTANCE + [�   D3D_NAME_RENDER_TARGET_ARRAY_INDEX & [�   D3D_NAME_VIEWPORT_ARRAY_INDEX 1 鹎    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED  [�   D3D_NAME_VERTEX_ID  [�   D3D_NAME_PRIMITIVE_ID F 鹎   D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS A 鹎   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK  [�   D3D_NAME_INSTANCE_ID  [�  	 D3D_NAME_IS_FRONT_FACE  [�  
 D3D_NAME_SAMPLE_INDEX , [�   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR + 溓    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 1 溓   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS . [�   D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR ' �   std::_Comparison_category_none ? 溓   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY * �   std::_Comparison_category_partial + [�  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR - [�   D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong . [�   D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR / [�   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR  [�   D3D_NAME_BARYCENTRICS  [�   D3D_NAME_SHADINGRATE  [�   D3D_NAME_CULLPRIMITIVE  [�  @ D3D_NAME_TARGET  [�  A D3D_NAME_DEPTH  [�  B D3D_NAME_COVERAGE % [�  C D3D_NAME_DEPTH_GREATER_EQUAL " [�  D D3D_NAME_DEPTH_LESS_EQUAL  [�  E D3D_NAME_STENCIL_REF   [�  F D3D_NAME_INNER_COVERAGE  C�   D3D_RETURN_TYPE_UNORM  C�   D3D_RETURN_TYPE_SNORM  C�   D3D_RETURN_TYPE_SINT  C�   D3D_RETURN_TYPE_UINT  C�   D3D_RETURN_TYPE_FLOAT  C�   D3D_RETURN_TYPE_MIXED  C�   D3D_RETURN_TYPE_DOUBLE " C�   D3D_RETURN_TYPE_CONTINUED ' Y�    D3D_REGISTER_COMPONENT_UNKNOWN & Y�   D3D_REGISTER_COMPONENT_UINT32 & Y�   D3D_REGISTER_COMPONENT_SINT32 ' Y�   D3D_REGISTER_COMPONENT_FLOAT32 ) _�    D3D_TESSELLATOR_DOMAIN_UNDEFINED ' _�   D3D_TESSELLATOR_DOMAIN_ISOLINE # _�   D3D_TESSELLATOR_DOMAIN_TRI $ _�   D3D_TESSELLATOR_DOMAIN_QUAD / 烨    D3D_TESSELLATOR_PARTITIONING_UNDEFINED - 烨   D3D_TESSELLATOR_PARTITIONING_INTEGER * 烨   D3D_TESSELLATOR_PARTITIONING_POW2 4 烨   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 5 烨   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN )     D3D_TESSELLATOR_OUTPUT_UNDEFINED %    D3D_TESSELLATOR_OUTPUT_POINT $    D3D_TESSELLATOR_OUTPUT_LINE +    D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW ,    D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW ' 厍    D3D12_COMMAND_LIST_TYPE_DIRECT ' 厍   D3D12_COMMAND_LIST_TYPE_BUNDLE ( 厍   D3D12_COMMAND_LIST_TYPE_COMPUTE % 厍   D3D12_COMMAND_LIST_TYPE_COPY - 厍   D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE . 厍   D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS - 厍   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE ':   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable # 蘱   BINDHANDLETYPES_DEPENDENCY 8 �    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD 9 �   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE 6 �   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR 1 <   donut::math::vector<unsigned int,2>::DIM           nvrhi::EntireBuffer 5 G�    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD 6 G�   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE 5 G�   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE # <   kPolymorphicLightTypeShift " <   kPolymorphicLightTypeMask . <  �   kPolymorphicLightShapingEnableBit 1 <  �    kPolymorphicLightIesProfileEnableBit - ;  �   羕PolymorphicLightMinLog2Radiance - ;  �   BkPolymorphicLightMaxLog2Radiance T U  �r ( std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Dx2 A _   std::allocator<char>::_Minimum_asan_allocation_alignment V U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx1 # U   std::ratio<1,2629746>::num ' U  �r ( std::ratio<1,2629746>::den R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gy 2 U   std::integral_constant<__int64,12>::value  誵    TKIND_ENUM  誵   TKIND_RECORD  誵   TKIND_MODULE  誵   TKIND_INTERFACE  誵   TKIND_DISPATCH  誵   TKIND_COCLASS  誵   TKIND_ALIAS  誵   TKIND_UNION % 蚯   D3D12_COLOR_WRITE_ENABLE_RED ' 蚯   D3D12_COLOR_WRITE_ENABLE_GREEN & 蚯   D3D12_COLOR_WRITE_ENABLE_BLUE ' 蚯   D3D12_COLOR_WRITE_ENABLE_ALPHA  ?�    D3D12_LOGIC_OP_CLEAR  ?�   D3D12_LOGIC_OP_SET  "q    PIDMSI_STATUS_NORMAL  ?�   D3D12_LOGIC_OP_COPY % ?�   D3D12_LOGIC_OP_COPY_INVERTED  "q   PIDMSI_STATUS_NEW  "q   PIDMSI_STATUS_PRELIM  ?�   D3D12_LOGIC_OP_NOOP  ?�   D3D12_LOGIC_OP_INVERT  "q   PIDMSI_STATUS_DRAFT ! "q   PIDMSI_STATUS_INPROGRESS  ?�   D3D12_LOGIC_OP_AND  ?�   D3D12_LOGIC_OP_NAND  "q   PIDMSI_STATUS_EDIT  "q   PIDMSI_STATUS_REVIEW  ?�   D3D12_LOGIC_OP_OR  ?�  	 D3D12_LOGIC_OP_NOR  "q   PIDMSI_STATUS_PROOF  ?�  
 D3D12_LOGIC_OP_XOR  ?�   D3D12_LOGIC_OP_EQUIV # ?�   D3D12_LOGIC_OP_AND_REVERSE $ ?�  
 D3D12_LOGIC_OP_AND_INVERTED " ?�   D3D12_LOGIC_OP_OR_REVERSE ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask �_   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size �_   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets �:    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi R U  ��Q std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Dx2 ' 
�    D3D12_SHADER_CACHE_MODE_MEMORY ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE T U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx1 A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity ! U   std::ratio<1,86400>::num % U  ��Q std::ratio<1,86400>::den : _   std::integral_constant<unsigned __int64,3>::value j _   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gy X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask : U  ��: std::integral_constant<__int64,146097>::value e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size % A�    D3D12_BARRIER_LAYOUT_PRESENT * A�   D3D12_BARRIER_LAYOUT_GENERIC_READ + A�   D3D12_BARRIER_LAYOUT_RENDER_TARGET . A�   D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS 1 A�   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE 0 A�   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ - A�   D3D12_BARRIER_LAYOUT_SHADER_RESOURCE ) A�   D3D12_BARRIER_LAYOUT_COPY_SOURCE ' A�   D3D12_BARRIER_LAYOUT_COPY_DEST , A�  	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE  Fq   CC_CDECL * A�  
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST  Fq   CC_MSCPASCAL 1 A�   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE / A�   D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ  Fq   CC_PASCAL 0 A�  
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE  Fq   CC_MACPASCAL  Fq   CC_STDCALL 0 A�   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ  Fq   CC_FPFASTCALL 1 A�   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE 3 U  �std::integral_constant<__int64,400>::value / A�   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ  Fq   CC_SYSCALL 0 A�   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE  Fq   CC_MPWCDECL 1 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON  Fq   CC_MPWPASCAL 7 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ ; A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS : A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE 6 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE  6q    FUNC_VIRTUAL 4 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST  6q   FUNC_PUREVIRTUAL 2 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON  6q   FUNC_NONVIRTUAL  6q   FUNC_STATIC 8 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ < A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS ; A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 7 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 5 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST  0q    VAR_PERINSTANCE  0q   VAR_STATIC  0q   VAR_CONST � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable : _   std::integral_constant<unsigned __int64,2>::value T U  �X呩std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Dx2 V U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx1 T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx1 $ U   std::ratio<1,31556952>::num � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment ( U  �X呩std::ratio<1,31556952>::den ; O�    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx2 3 �  \ std::filesystem::path::preferred_separator : O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT V U  �X呩std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx2 2 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND 8 O�  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 7 O�  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER U U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gx : O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL Q U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gy 9 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT ? O�  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE ? O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY B O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS A O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 8 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 6 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK 7 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 2 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS ; O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS "  �    D3D12_BARRIER_TYPE_GLOBAL #  �   D3D12_BARRIER_TYPE_TEXTURE  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity & :    std::_Num_base::has_quiet_NaN * :    std::_Num_base::has_signaling_NaN # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized 4 _  @ _Mtx_internal_imp_t::_Critical_section_size * K   std::_Num_float_base::round_style 5 _   _Mtx_internal_imp_t::_Critical_section_align $ �   std::_Num_float_base::radix + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits * �   std::numeric_limits<bool>::digits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 - :   std::numeric_limits<char>::is_signed - :    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 :   std::numeric_limits<unsigned char>::is_modulo  (q    DESCKIND_NONE 3 �   std::numeric_limits<unsigned char>::digits  (q   DESCKIND_FUNCDESC  (q   DESCKIND_VARDESC 5 �   std::numeric_limits<unsigned char>::digits10  (q   DESCKIND_TYPECOMP   (q   DESCKIND_IMPLICITAPPOBJ 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10  抏   _Mtx_try  抏   _Mtx_recursive 1 :   std::numeric_limits<char16_t>::is_modulo  yr   COR_VERSION_MAJOR_V2 . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10  蔱   std::_INVALID_ARGUMENT  蔱   std::_NO_SUCH_PROCESS & 蔱   std::_OPERATION_NOT_PERMITTED , 蔱   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蔱   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits % �    _Atomic_memory_order_relaxed 0 �  	 std::numeric_limits<char32_t>::digits10 % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst $ 嗲   ImGuiWindowFlags_NoTitleBar " 嗲   ImGuiWindowFlags_NoResize % 嗲   ImGuiWindowFlags_NoScrollbar $ 嗲    ImGuiWindowFlags_NoCollapse ' 嗲   ImGuiWindowFlags_NoMouseInputs ) 嗲  �   ImGuiWindowFlags_NoNavInputs ( 嗲  �   ImGuiWindowFlags_NoNavFocus 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 # 奕  � ImGuiChildFlags_FrameStyle . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 " K�   ImGuiTreeNodeFlags_Framed ( K�   ImGuiTreeNodeFlags_AllowOverlap , K�   ImGuiTreeNodeFlags_NoTreePushOnOpen + K�   ImGuiTreeNodeFlags_NoAutoOpenOnLog , :   std::numeric_limits<int>::is_signed # 斍   ImGuiPopupFlags_AnyPopupId ) �   std::numeric_limits<int>::digits & 斍   ImGuiPopupFlags_AnyPopupLevel + �  	 std::numeric_limits<int>::digits10 * �   ImGuiSelectableFlags_AllowOverlap  �  g D3D_SHADER_MODEL_6_7 $ 智   ImGuiComboFlags_HeightSmall & 智   ImGuiComboFlags_HeightRegular $ 智   ImGuiComboFlags_HeightLarge & 智   ImGuiComboFlags_HeightLargest 1 奚  @ ImGuiTabBarFlags_FittingPolicyResizeDown - 奚  � ImGuiTabBarFlags_FittingPolicyScroll - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 '    ImGuiFocusedFlags_ChildWindows %    ImGuiFocusedFlags_RootWindow ' 媲   ImGuiHoveredFlags_ChildWindows % 媲   ImGuiHoveredFlags_RootWindow 2 媲    ImGuiHoveredFlags_AllowWhenBlockedByPopup 7 媲  � ImGuiHoveredFlags_AllowWhenBlockedByActiveItem 4 媲   ImGuiHoveredFlags_AllowWhenOverlappedByItem 6 媲   ImGuiHoveredFlags_AllowWhenOverlappedByWindow . 媲   ImGuiHoveredFlags_AllowWhenOverlapped 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 0 �   ImGuiDragDropFlags_AcceptBeforeDelivery 3 �   ImGuiDragDropFlags_AcceptNoDrawDefaultRect D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10  宷    SYS_WIN16  宷   SYS_WIN32  宷   SYS_MAC 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity Q U  �r ( std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Nx2  艋  �ImGuiKey_COUNT 3 耷    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS M U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Dx2 C 耷   D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS  艋   ImGuiMod_Ctrl  艋    ImGuiMod_Shift  艋   @ImGuiMod_Alt  艋  � �ImGuiMod_Super   艋   ImGuiKey_NamedKey_BEGIN  艋  �ImGuiKey_NamedKey_END  艋  �ImGuiKey_KeysData_SIZE R _   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment 3     D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 3    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1 S U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx2 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 S U  �r ( std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx2 2 �   std::numeric_limits<double>::max_digits10 N U  6 std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gx N U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gy 2 �   std::numeric_limits<double>::max_exponent  钋   ImGuiNavInput_COUNT 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset 4 U  @std::integral_constant<__int64,1600>::value h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 : 馇    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE 7 �   std::numeric_limits<long double>::max_exponent I 馇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY J 馇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION 9 �  4std::numeric_limits<long double>::max_exponent10 H 馇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10  2q    CHANGEKIND_ADDMEMBER   2q   CHANGEKIND_DELETEMEMBER  2q   CHANGEKIND_SETNAMES $ 2q   CHANGEKIND_SETDOCUMENTATION  2q   CHANGEKIND_GENERAL  2q   CHANGEKIND_INVALIDATE   2q   CHANGEKIND_CHANGEFAILED 7 U  �;緎td::integral_constant<__int64,48699>::value  �  5 ImGuiCol_COUNT )  �   ImGuiButtonFlags_MouseButtonLeft *  �   ImGuiButtonFlags_MouseButtonRight +  �   ImGuiButtonFlags_MouseButtonMiddle $ U  @std::ratio<1600,48699>::num H :    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified & U  �;緎td::ratio<1600,48699>::den + 郧  �   ImGuiColorEditFlags_DisplayRGB + 郧  �    ImGuiColorEditFlags_DisplayHSV + 郧  �  @ ImGuiColorEditFlags_DisplayHex & 郧  �  � ImGuiColorEditFlags_Uint8 & 郧  �   ImGuiColorEditFlags_Float - 郧  �   ImGuiColorEditFlags_PickerHueBar / 郧  �   ImGuiColorEditFlags_PickerHueWheel ) 郧  �   ImGuiColorEditFlags_InputRGB ) 郧  �   ImGuiColorEditFlags_InputHSV R U  �X呩std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Dx2 T U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx2 T U  �X呩std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gx O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gy ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos & 毲  � ImGuiTableFlags_BordersInnerH & 毲   ImGuiTableFlags_BordersOuterH & 毲   ImGuiTableFlags_BordersInnerV & 毲   ImGuiTableFlags_BordersOuterV  �   9  % 毲  �ImGuiTableFlags_BordersInner % 毲   ImGuiTableFlags_BordersOuter ' 毲    ImGuiTableFlags_SizingFixedFit ( 毲   @ImGuiTableFlags_SizingFixedSame * 毲   `ImGuiTableFlags_SizingStretchProp , 毲  � �ImGuiTableFlags_SizingStretchSame + 谇   ImGuiTableColumnFlags_WidthStretch ) 谇   ImGuiTableColumnFlags_WidthFixed / 谇  �   ImGuiTableColumnFlags_IndentEnable 0 谇  �   ImGuiTableColumnFlags_IndentDisable , 谇  �   ImGuiTableColumnFlags_IsEnabled , 谇  �   ImGuiTableColumnFlags_IsVisible + 谇  �   ImGuiTableColumnFlags_IsSorted , 谇  �   ImGuiTableColumnFlags_IsHovered $ U  �std::ratio<400,146097>::num @ �   std::_General_precision_tables_2<float>::_Max_special_P ( U  ��: std::ratio<400,146097>::den 8 �  ' std::_General_precision_tables_2<float>::_Max_P    �   ~   A �   std::_General_precision_tables_2<double>::_Max_special_P 9 �  5std::_General_precision_tables_2<double>::_Max_P   �   S� $ �   std::_Locbase<int>::collate 3 �    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED - �   D3D12_MESSAGE_CATEGORY_MISCELLANEOUS " �   std::_Locbase<int>::ctype . �   D3D12_MESSAGE_CATEGORY_INITIALIZATION % �   std::_Locbase<int>::monetary ' �   D3D12_MESSAGE_CATEGORY_CLEANUP + �   D3D12_MESSAGE_CATEGORY_COMPILATION $ �   std::_Locbase<int>::numeric a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment . �   D3D12_MESSAGE_CATEGORY_STATE_CREATION - �   D3D12_MESSAGE_CATEGORY_STATE_SETTING ! �   std::_Locbase<int>::time - �   D3D12_MESSAGE_CATEGORY_STATE_GETTING 5 �   D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION ) �  	 D3D12_MESSAGE_CATEGORY_EXECUTION % �    std::_Locbase<int>::messages   �  ? std::_Locbase<int>::all * I�    D3D12_MESSAGE_SEVERITY_CORRUPTION ! �    std::_Locbase<int>::none % I�   D3D12_MESSAGE_SEVERITY_ERROR ' I�   D3D12_MESSAGE_SEVERITY_WARNING $ I�   D3D12_MESSAGE_SEVERITY_INFO i _   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment - <  `std::_Big_integer_flt::_Maximum_bits :    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi - <    std::_Big_integer_flt::_Element_bits ":   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard . <  s std::_Big_integer_flt::_Element_count � _   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment x _   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment   U  std::ratio<3600,1>::num   U   std::ratio<3600,1>::den + �!        nvrhi::rt::c_IdentityTransform � _   std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > >::_Minimum_asan_allocation_alignment  �1   std::_Consume_header  �1   std::_Generate_header - �    std::integral_constant<int,0>::value / �    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION - �   D3D12_RESOURCE_BARRIER_TYPE_ALIASING B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size  U   std::ratio<24,1>::num  U   std::ratio<24,1>::den 7 :   std::atomic<unsigned int>::is_always_lock_free I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy 9 U  ��Q std::integral_constant<__int64,86400>::value 1 U   std::integral_constant<__int64,1>::value C _   std::_Locked_pointer<std::_Stop_callback_base>::_Lock_mask D _    std::_Locked_pointer<std::_Stop_callback_base>::_Not_locked R _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_not_needed N _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_needed I _   �黶td::_Locked_pointer<std::_Stop_callback_base>::_Ptr_value_mask P U  �r ( std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Dx2 % U  ��Q std::ratio<86400,1>::num ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos ! U   std::ratio<86400,1>::den N U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx2 R U  �r ( std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gx M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gy 3 U  � std::integral_constant<__int64,200>::value :   ED3D12_MESSAGE_ID_ALPHA_BLEND_FACTOR_NOT_SUPPORTED : _   std::integral_constant<unsigned __int64,1>::value 2_   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2_   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,:    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi $ U  � std::ratio<200,146097>::num ( U  ��: std::ratio<200,146097>::den  U   std::ratio<7,1>::num  U   std::ratio<7,1>::den I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1 M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy K :   std::atomic<std::_Stop_callback_base const *>::is_always_lock_free Q U  �X呩std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Dx2 / :   std::atomic<long>::is_always_lock_free : U  ��:	 std::integral_constant<__int64,604800>::value O U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx2 S U  �X呩std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx2 N U  H std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gx N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gy 2 U  2 std::integral_constant<__int64,50>::value $ 6g   TP_CALLBACK_PRIORITY_NORMAL % 6g   TP_CALLBACK_PRIORITY_INVALID ( U  ��: std::ratio<146097,400>::num $ U  �std::ratio<146097,400>::den : U  �� std::integral_constant<__int64,438291>::value T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2  晄  LPPARAMDESCEX  6q  FUNCKIND  檚  tagPARAMDESCEX  梥  PARAMDESC  梥  tagPARAMDESC  搒  tagARRAYDESC  Fq  CALLCONV  (q  DESCKIND  Rs  ELEMDESC  憇  BINDPTR  峴  tagFUNCDESC  Nr  INVOKEKIND  Hs  TLIBATTR  憇  tagBINDPTR  rs  tagSTATSTG  Ys  tagTYPEDESC  峴  FUNCDESC  "   HREFTYPE  宷  SYSKIND  !   ImWchar16  硆  tagVARDESC  誵  TYPEKIND  坰  IEnumSTATSTG  rs  STATSTG  ps  ITypeComp  Ys  TYPEDESC  Os  IDLDESC  Rs  tagELEMDESC  Os  tagIDLDESC  鋑  VARIANTARG  Ms  EXCEPINFO  Ms  tagEXCEPINFO 
    DISPID     MEMBERID  �  _CatchableType  u   UINT ' 鹎  D3D12_BACKGROUND_PROCESSING_MODE  罨  ImNewWrapper  A�  D3D12_BARRIER_LAYOUT  袄  ImVector<ImFont *>  &r  tagCAUL  Hs  tagTLIBATTR  6g  _TP_CALLBACK_PRIORITY " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  '0  __std_tzdb_error  H�  ImFontConfig & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const>  艋  ImGuiKey - W�  EnvMapImportanceSamplingBakerConstants  柷  D3D_PRIMITIVE_TOPOLOGY  Fs  tagShutdownType  q   OLECHAR G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �0  _Ctypevec  奕  ImGuiChildFlags_  簈  tagCABSTR   �  D3D12_BARRIER_TYPE  Fq  tagCALLCONV  誵  tagTYPEKIND   �  D3D12_STATIC_BORDER_COLOR  酕  PolymorphicLightInfo & �  $_TypeDescriptor$_extraBytes_28  鋑  VARIANT     ImS16  #   uintmax_t  s  ISequentialStream     int64_t  塺  BSTRBLOB    _Smtx_t    ImGuiTextBuffer  齚  _Thrd_result  #   rsize_t  炃  _D3D_INCLUDE_TYPE  #   DWORD_PTR  }r  TYPEATTR  婢  ImVector<ImDrawVert>     VARIANT_BOOL  �>  __std_fs_find_data  ~�  ImVector<ImVec2> &   $_TypeDescriptor$_extraBytes_23 
 鑗  PUWSTR - cZ  $_s__CatchableTypeArray$_extraBytes_32 # )D  __std_fs_reparse_data_buffer Z _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^  _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> `   __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �>  __std_fs_dir_handle  �  ImGuiOnceUponAFrame ( g  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  rg  AR_STATE  5s  tagCADBL  乬  _DEVICE_DATA_SET_RANGE  �/  __std_access_rights  0q  VARKIND 3 馇  D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34 ! 羟  D3D12_DESCRIPTOR_HEAP_TYPE  wr  _tagPSUACTION  B�  ImFontAtlasCustomRect 
 7s  tagDEC  9s  CALPSTR     LONG_PTR  q  tagBINDSTRING  渇  _Stl_critical_section 	 I  tm   M�  _D3D_SHADER_VARIABLE_TYPE ! 樓  _D3D_SHADER_VARIABLE_CLASS  祌  tagCACLIPDATA  #   ULONG_PTR " �  D3D12_RESOURCE_BARRIER_TYPE % �  _s__RTTICompleteObjectLocator2 " �  D3D12_DESCRIPTOR_RANGE_TYPE  裶  tagURLZONE  峄  ImGuiTableSortSpecs  頶  PUWSTR_C  *g  PTP_CLEANUP_GROUP  Bq  __MIDL_ICodeInstall_0001  p  PCHAR  $q  tagBINDSTATUS  蚮  _GUID  ur  _URLZONEREG  梣  _LARGE_INTEGER ' <s  _LARGE_INTEGER::<unnamed-type-u>    ImGuiFocusedFlags_ & kZ  $_TypeDescriptor$_extraBytes_30  蚯  D3D12_COLOR_WRITE_ENABLE  纐  CLIPDATA  憅  CAFILETIME  9s  tagCALPSTR  r  CALPWSTR 
  q  CAL  �r  tagCABSTRBLOB      ImU8  遯  tagSAFEARRAYBOUND  茨  ImDrawChannel  偨  ImDrawCallback  4s  tagCAFLT A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 焃  $_TypeDescriptor$_extraBytes_46 
 �  ImFont ' �  $_TypeDescriptor$_extraBytes_118 
 攓  tagCAH  7s  DECIMAL  聁  tagCAUI  !   WORD  �  _s__CatchableType  牻  ImDrawListSplitter  3�  ImVector<unsigned int>  噐  CAUH  [�  D3D_NAME  .q  tagCADATE  �  ImGuiDragDropFlags_  �  D3D_SHADER_MODEL  5s  CADBL  �  LPCOLESTR  K�  ImGuiTreeNodeFlags_  頶  PCUWSTR  巕  CAPROPVARIANT  媲  ImGuiHoveredFlags_  4s  CAFLT & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t  t   ImGuiMouseCursor ' g  JOB_OBJECT_IO_RATE_CONTROL_FLAGS 9   __vcrt_va_list_is_reference<wchar_t const * const>  觪  _USER_ACTIVITY_PRESENCE  uy  ProgressBar  銩  __std_fs_filetime E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const>  h�  ImColor    PLONG & �  $_TypeDescriptor$_extraBytes_20  醧  DISPPARAMS  貴  LightingControlData  坬  _FILETIME  p  va_list  F�  ImDrawList  攇  FS_BPIO_INFLAGS - �  $_s__CatchableTypeArray$_extraBytes_16   SD  __std_fs_copy_file_result  �5  __std_code_page  僩  PDEVICE_DSM_DEFINITION      BYTE . �  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE % 嵘  D3D12_RAYTRACING_GEOMETRY_TYPE 
 �  PCWSTR  2s  IStream � 葝  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > d 讔  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > G 啀  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > � 瓖  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � �  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > � 薧  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > a 顛  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> d  �  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> > > � 赹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > ] 鍗  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ f�  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > >  A   std::max_align_t z 韃  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> [ 賺  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 蕧  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > f 呦  std::_Compressed_pair<std::default_delete<donut::app::ImGui_NVRHI>,donut::app::ImGui_NVRHI *,1> 3 紫  std::default_delete<donut::app::ImGui_NVRHI> � <�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � 粛  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 閶  std::_Default_allocator_traits<std::allocator<float> > � 瘝  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > C 獚  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > i鋇  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � 爫  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> Q   std::_Default_allocator_traits<std::allocator<donut::app::IRenderPass *> > �   std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > � 笇  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 槏  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 悕  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 亶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > M J�  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 躛  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 淾  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > L 垗  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 儘  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > T r�  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > @ g�  std::_Arg_types<donut::app::DeviceManager &,unsigned int> U h�  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � Z�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::TextureData> >,std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >,1> 3 覊  std::_Ptr_base<donut::engine::LoadedTexture> j 葡  std::_Default_allocator_traits<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > > _ R�  std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > > : 榝  std::_Vector_val<std::_Simple_types<unsigned int> > D L�  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � >�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � �  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > 6 緡  std::_Ptr_base<donut::engine::DescriptorHandle> � .�  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> F 栁  std::_Func_class<void,donut::app::DeviceManager &,unsigned int> P 酉  std::_Func_class<void,donut::app::DeviceManager &,unsigned int>::_Storage U �  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > e "�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> *> > \ $�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> *> > � �  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �鰠  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > � O]  std::_Non_trivial_copy<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > "p�  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > � 蚟  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > [ �  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> > > �魧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> 4 曂  std::_Simple_types<donut::app::IRenderPass *> c 閷  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> U 鐘  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > d 嬐  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > 4 鄬  std::allocator<donut::math::vector<float,2> > � 邢  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::app::RegisteredFont> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::app::RegisteredFont> > >,1> = �  std::allocator<donut::math::vector<unsigned short,4> > K 賹  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p 巉  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U ﹪  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 聤  std::_Ptr_base<donut::engine::BufferGroup> � 蠈  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > M k^  std::_Normal_allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > � �  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > F脤  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � 皩  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> e Y�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > s �  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � Y�  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > { ▽  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > a 认  std::allocator_traits<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > > � 倖  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > � 废  std::_Compressed_pair<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >,std::_List_val<std::_List_simple_types<donut::app::IRenderPass *> >,1> , <�  std::allocator<nvrhi::BindingSetItem> K 5�  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � 萟  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 綶  std::_Ptr_base<donut::vfs::IFileSystem> �糬  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � +�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > J g�  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � �  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � 匽  std::_Non_trivial_move_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > H   std::allocator_traits<std::allocator<donut::app::IRenderPass *> > � 盺  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 猑  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> ��  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> L �  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  m�  std::allocator<float> � 	�  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> 鷭  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 髬  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1> k ,]  std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> 4 雼  std::allocator_traits<std::allocator<float> > < �  std::_Conditionally_enabled_hash<nvrhi::ITexture *,1> [ 輯  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > Q謰  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � a]  std::_Non_trivial_move<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 瀆  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > 廭  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> 螊  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > g\  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > � )�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > [ b�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > C X�  std::allocator<std::shared_ptr<donut::engine::TextureData> > � Q�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > ] 栂  std::unique_ptr<donut::app::ImGui_NVRHI,std::default_delete<donut::app::ImGui_NVRHI> > [ 坁  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' {I  std::default_delete<wchar_t [0]> . /  std::_Conditionally_enabled_hash<int,1> A C  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> > �;�  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >,1>  �  std::_Lockit  坃  std::_Stop_callback_base  b  std::timed_mutex D �;  std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > � 3�  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty - �?  std::reverse_iterator<wchar_t const *> 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int> � Z|  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > B l�  std::shared_ptr<donut::render::MipMapGenPass::NullTextures>  0  std::_Fs_file  �=  std::optional<int> � 俕  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � ,�  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 5 $0  std::_Tzdb_deleter<__std_tzdb_time_zones_info> � z^  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128    std::condition_variable � �  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >  �=  std::optional<__int64> � 	�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > E m^  std::allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > G ^^  std::allocator_traits<std::allocator<std::chrono::leap_second> > ) 怷  std::_Narrow_char_traits<char,int> i 魥  std::initializer_list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > L 閵  std::allocator_traits<std::allocator<donut::math::vector<float,2> > >  j  std::hash<float> E D~  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> ! �4  std::__floating_decimal_64 S 蘕  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone_link> > \^  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > R 歿  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > \ 絹  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > " 唂  std::_Align_type<double,64>  �5  std::less<void>  /  std::hash<int> � =|  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *>  P  std::_Num_int_base  i3  std::ctype<wchar_t> " �/  std::_System_error_category / 揚  std::_Conditionally_enabled_hash<bool,1> 1 剂  std::_Ptr_base<donut::app::RegisteredFont> 2 蹔  std::shared_ptr<donut::engine::BufferGroup> 8 疑  std::default_delete<donut::render::MipMapGenPass> + 琠  std::_Atomic_storage<unsigned int,4> � 瘖  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::_Format_arg_index � K�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > >  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > / y2  std::codecvt<char32_t,char8_t,_Mbstatet> 4 鑹  std::shared_ptr<donut::engine::LoadedTexture> k =]  std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > f �;  std::pair<std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >,bool> 碶  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 蓅  std::_Ptr_base<std::mutex> �}  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> u 訵  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > . �  std::_Ptr_base<donut::engine::MeshInfo> 6 餧  std::allocator_traits<std::allocator<wchar_t> >  t  std::shared_timed_mutex & ;~  std::equal_to<unsigned __int64> � s]  std::_Non_trivial_copy_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  `  std::bad_cast  JR  std::equal_to<void> 4 D�  std::allocator<donut::math::vector<float,4> > 3 僄  std::_Ptr_base<donut::engine::ShaderFactory> � 粿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > q 綁  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 媺  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o 踈  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> 6 ~H  std::initializer_list<nvrhi::BindingLayoutItem>     std::_Compare_t 4 �  std::shared_ptr<donut::render::MipMapGenPass> " u  std::numeric_limits<double> $ d  std::atomic<unsigned __int64>  �  std::__non_rtti_object < 鰒  std::_Ptr_base<donut::engine::DescriptorTableManager> , K2  std::_Codecvt_guard<char8_t,char16_t> ( @  std::_Basic_container_proxy_ptr12 � 巤  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> � D�  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 2 #L  std::allocator<std::chrono::time_zone_link> 4 F�  std::allocator<donut::math::vector<float,3> > = 6`  std::_Atomic_pointer<std::_Stop_callback_base const *> > 刦  std::vector<unsigned int,std::allocator<unsigned int> > T Tf  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy   �5  std::_Decode_result<char> 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base  抈  std::stop_token  �-  std::logic_error � ?�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety / 頚  std::allocator<std::chrono::leap_second> P �  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 鈭  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! 頬  std::char_traits<char32_t> � ﹫  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  Q1  std::locale  �1  std::locale::_Locimp  b1  std::locale::facet   k1  std::locale::_Facet_guard  1  std::locale::id - VL  std::allocator<std::chrono::time_zone>  �  std::_Compare_ncmp s 竂  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   R  std::numeric_limits<bool>  S6  std::_Fmt_codec<char,0> # *Y  std::_WChar_traits<char16_t> _ 殘  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u i�  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P絴  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P 獻  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> � 納  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z W  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> >  綻  std::stop_source R �9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > h {9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >::_Reallocation_policy M i?  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � +�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > r I�  std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> > � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  F.  std::overflow_error " x5  std::_Basic_format_arg_type , b2  std::_Codecvt_guard<char16_t,char8_t> % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * jb  std::_Vb_val<std::allocator<bool> > E 鞢  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j 靅  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   闿  std::char_traits<wchar_t> \ :  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > r �9  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> >::_Reallocation_policy  pa  std::recursive_mutex   �  std::pmr::memory_resource  	>  std::pair<int,int> ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> � 鑍  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 鶉  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � 鵷  std::unordered_map<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � 駠  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> � 鐕  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy "邍  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>  砞  std::false_type S 賴  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > 泧  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > > #讄  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � �:  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! a  std::hash<std::thread::id> $ 
6  std::_Fmt_fixed_buffer_traits  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > c 蠶  std::_Alloc_construct_ptr<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  扟  std::fpos<_Mbstatet> b gQ  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > > , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  俛  std::adopt_lock_t o 奓  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_current_zone_info>,__std_tzdb_current_zone_info *,1>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 讎  std::shared_ptr<donut::engine::DescriptorHandle> , n  std::numeric_limits<unsigned __int64> F L  std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >  �0  std::_Locinfo 6 B  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L 珖  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > # �  std::hash<nvrhi::ITexture *> 9 枤  std::shared_ptr<donut::engine::FramebufferFactory> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 8 k�  std::_Ptr_base<donut::engine::FramebufferFactory> 4 婯  std::_Atomic_padded<std::chrono::tzdb_list *> s 奤  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > N 諼  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone> > � 潎  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > > � 萞  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > 9 DJ  std::basic_streambuf<char,std::char_traits<char> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16> � 獅  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void> f ;�  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > U 翁  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IBindingSet> > > = 媷  std::shared_ptr<donut::engine::DescriptorTableManager>  �  std::string_view  �  std::wstring_view � 憒  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound  Qa  std::_Mutex_base �by  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > b u�  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  Z5  std::money_base  縘  std::money_base::pattern  y0  std::_Timevec  胉  std::nostopstate_t D f�  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  f  std::defer_lock_t ? ^�  std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >   �.  std::_Init_once_completer v  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > �   std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >  絔  std::endian j sC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � BC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �a  std::scoped_lock<> + �2  std::codecvt<wchar_t,char,_Mbstatet> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> d xK  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::leap_second> > > Q 籡  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16> � _�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >    std::_Iterator_base12 � 颸  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  mN  std::_Pocma_values . B0  std::_Tzdb_deleter<__std_tzdb_sys_info> 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> * 皒  std::shared_lock<std::shared_mutex> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > N 	@  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > ( 筣  std::ratio<1,1000000000000000000> @ nY  std::_Default_allocator_traits<std::allocator<char32_t> > ( d  std::_Atomic_padded<unsigned int> D R�  std::allocator<std::shared_ptr<donut::engine::TextureData> *>  �?  std::allocator<char32_t> ? !D  std::unique_ptr<char [0],std::default_delete<char [0]> > � }  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > $ �  std::_Atomic_integral<long,4> � 1�  std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  L<  std::_Flist_unchecked_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,std::_Iterator_base0> R =K  std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >  桺  std::hash<bool> � K�  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >     std::streamsize 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty � �;  std::_Compressed_pair<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >,std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,1> ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> 2 鼿  std::initializer_list<nvrhi::IBindingSet *> � �:  std::tuple<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > " b  std::lock_guard<std::mutex> N 蔝  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone> > K I�  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > ] �:  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � ?�  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > S 礥  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone_link> > | 嫔  std::_Uhash_choose_transparency<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *>,void> k =�  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �  std::hash<long double> � 侤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � Q@  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy 2 (~  std::equal_to<nvrhi::TextureSubresourceSet> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy [ �<  std::_Flist_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > � 鑯  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >   �  std::_Comparison_category / 8�  std::shared_ptr<donut::engine::MeshInfo> X}  std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>  f  std::try_to_lock_t  $�  std::array<bool,3> � }  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ �5  std::_Decode_result<char32_t> # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0 F 镂  std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1> J 菸  std::_List_val<std::_List_simple_types<donut::app::IRenderPass *> >  w  std::hash<double> L 奈  std::function<void __cdecl(donut::app::DeviceManager &,unsigned int)> O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> n �:  std::_Tuple_val<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > D �  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> " 猘  std::_Align_type<double,72> G �;  std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > x �  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �5  std::_Lazy_locale � ]x  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >,std::_Iterator_base0> / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error < 朿  std::_Atomic_padded<std::_Stop_callback_base const *> ( �)  std::hash<nvrhi::FramebufferInfo>  螸  std::_Fmt_buffer<char> 9 桰  std::allocator<std::filesystem::_Find_file_handle> b 衶  std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �.  std::error_condition % 砞  std::integral_constant<bool,0> | �  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > > � 鈫  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Is_bidi � 鄦  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Pop_direction  �  std::bad_exception & 肐  std::_Zero_then_variadic_args_t � 眧  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > / 甝  std::_General_precision_tables_2<double> ? $`  std::_Atomic_storage<std::_Stop_callback_base const *,8> � ]L  std::_Compressed_pair<std::allocator<std::chrono::time_zone>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >,1>  �  std::u32string � 绿  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,1> N {J  std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> [   std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > . ]  std::_General_precision_tables_2<float> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 剢  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 1 臺  std::_Atomic_integral_facade<unsigned int>  瀉  std::cv_status 9 椡  std::_List_simple_types<donut::app::IRenderPass *> S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 s�  std::_Vector_val<std::_Simple_types<float> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > b L:  std::unique_ptr<__std_tzdb_time_zones_info,std::_Tzdb_deleter<__std_tzdb_time_zones_info> > + rD  std::pair<enum __std_win_error,bool> A i�  std::allocator_traits<std::allocator<nvrhi::BufferRange> > S  ?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char8_t> > > � t�  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void>  �5  std::_Fmt_codec_base<0> J ]  std::allocator_traits<std::allocator<std::chrono::time_zone_link> >  鑐  std::thread  a  std::thread::id S Y?  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �-  std::length_error \ [�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >  3a  std::jthread F TU  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 烮  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! s  std::numeric_limits<float>  �4  std::time_base   }4  std::time_base::dateorder ) �  std::_Atomic_integral_facade<long> i 鐊  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >  絘  std::mutex Q �  std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64> f 9  std::unique_ptr<__std_tzdb_current_zone_info,std::_Tzdb_deleter<__std_tzdb_current_zone_info> > 1 癒  std::_Flist_node<std::chrono::tzdb,void *>  ]  std::ratio<60,1> k 軰  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_time_zones_info>,__std_tzdb_time_zones_info *,1> : +6  std::_String_view_iterator<std::char_traits<char> >  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> % �  std::_Itraits_pointer_strategy [ 嵧  std::allocator_traits<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > � M�  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy ' �4  std::pair<char *,enum std::errc> * �=  std::_Optional_destruct_base<int,1> ! �4  std::__floating_decimal_32 J :w  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �\  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �3  std::_Iosb<int>   �3  std::_Iosb<int>::_Seekdir ! �3  std::_Iosb<int>::_Openmode   �3  std::_Iosb<int>::_Iostate ! �3  std::_Iosb<int>::_Fmtflags # �3  std::_Iosb<int>::_Dummy_enum 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano ( }�  std::_Ptr_base<donut::vfs::IBlob> I 7L  std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> � 蛝  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > | =�  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> C �  std::allocator<std::shared_ptr<donut::app::RegisteredFont> > M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > j5�  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � 馶  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 Y  std::_Char_traits<char16_t,unsigned short> a _<  std::_Flist_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > $ �)  std::hash<nvrhi::BufferRange> V ^;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> > l -;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >::_Reallocation_policy T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  "1  std::_Locbase<int> " 鈙  std::shared_ptr<std::mutex> i 恾  std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> 9 $z  std::shared_ptr<donut::engine::CommonRenderPasses> S bX  std::forward_list<std::chrono::tzdb,std::_Crt_allocator<std::chrono::tzdb> > ! 郳  std::char_traits<char16_t> � 瞾  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > � 鍇  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<> 5 �<  std::_Atomic_pointer<std::chrono::tzdb_list *> P 燯  std::_Uninitialized_backout_al<std::allocator<std::chrono::leap_second> >  �  std::_Container_base12 W 轡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  t  std::shared_mutex  	/  std::io_errc  4  std::ios_base  )4  std::ios_base::_Fnarray  #4  std::ios_base::_Iosarray  �3  std::ios_base::Init  �3  std::ios_base::failure  �3  std::ios_base::event ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type  p�  std::array<bool,349>   d  std::numeric_limits<long> " 衆  std::initializer_list<char> � p�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >  �  std::_Invoker_strategy  鯟  std::nothrow_t � [�  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � L�  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1>  檁  std::_Stop_state 荺  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �  std::_Default_allocate_traits   璍  std::_Fmt_buffer<wchar_t> M 0?  std::_String_iterator<std::_String_val<std::_Simple_types<char8_t> > > N �?  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short>  u   std::_Vbase . S  std::allocator<nvrhi::rt::GeometryDesc> # 醓  std::unique_lock<std::mutex>  痋  std::ratio<2629746,1> ( 苭  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > 1 [�  std::_Ptr_base<donut::engine::TextureData> ' K�  std::equal_to<nvrhi::ITexture *> �璡  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! �3  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � qx  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > | �  std::_Compressed_pair<std::hash<nvrhi::ITexture *>,std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1>,1> + �5  std::_Grapheme_Break_property_values < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 0  �  std::allocator<donut::app::IRenderPass *> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 2 化  std::_Ptr_base<donut::engine::TextureCache> O 镻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock � 鯧  std::_Compressed_pair<std::allocator<std::chrono::leap_second>,std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >,1>  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> ) 礚  std::_Fmt_counting_buffer<wchar_t> c 蔏  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_leap_info [0]>,__std_tzdb_leap_info *,1>  =�  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � �  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy J 侷  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D oI  std::_Compressed_pair<std::default_delete<char [0]>,char *,1> � A}  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0> Z 尢  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::app::RegisteredFont> > >   騙  std::atomic<unsigned int>  L5  std::messages_base - 遊  std::_Crt_allocator<std::chrono::tzdb> g 癥  std::_Normal_allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  .  std::out_of_range # f  std::numeric_limits<__int64> _ 苿  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 晞  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i 鮅  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > " 骵  std::initializer_list<bool>  +3  std::ctype<char> @ W�  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > d �:  std::_Tuple_val<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > >  h  std::memory_order `   std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � M�  std::queue<std::shared_ptr<donut::engine::TextureData>,std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > > > ! (b  std::recursive_timed_mutex  �4  std::chars_format " 磗  std::condition_variable_any 6 鮯  std::condition_variable_any::_Cv_any_notify_all }   std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > � z�  std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::_Reallocation_policy  T4  std::nullopt_t  V4  std::nullopt_t::_Tag  賉  std::ratio<3600,1> � &�  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > # �  std::_Atomic_storage<long,4> , �<  std::atomic<std::chrono::tzdb_list *> # *  std::hash<nvrhi::BlendState> / 譡  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  U  std::in_place_t z f}  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > D jL  std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > g N�  std::unique_ptr<donut::render::MipMapGenPass,std::default_delete<donut::render::MipMapGenPass> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > g 楺  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> > >    std::ratio<1,1> I 匪  std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > ^ �;  std::unique_ptr<__std_tzdb_leap_info [0],std::_Tzdb_deleter<__std_tzdb_leap_info [0]> > 2 F6  std::_Measure_string_prefix_iterator_legacy ) �=  std::_Optional_construct_base<int> 3 闔  std::initializer_list<nvrhi::BindingSetItem>   D[  std::forward_iterator_tag  ..  std::runtime_error �倈  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   
  std::bad_array_new_length ; 殈  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > � �  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > / 綾  std::_Atomic_storage<unsigned __int64,8> p 谏  std::_Compressed_pair<std::default_delete<donut::render::MipMapGenPass>,donut::render::MipMapGenPass *,1> v �  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �0  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z WY  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > /'y  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >  鑕  std::allocator<bool>  �  std::u16string _ 	�  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 貎  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy ]蝩  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >  ^  std::nested_exception  �  std::_Distance_unknown H jX  std::pointer_traits<std::_Flist_node<std::chrono::tzdb,void *> *> ) 殐  std::allocator<nvrhi::BufferRange> , 妝  std::lock_guard<std::recursive_mutex> 7 K  std::basic_ostream<char,std::char_traits<char> > ( j  std::numeric_limits<unsigned int> < 7U  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> +檨  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > ) 搩  std::shared_ptr<donut::vfs::IBlob> � ,}  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > E   std::allocator_traits<std::allocator<std::chrono::time_zone> > , 2  std::codecvt<char32_t,char,_Mbstatet> | �}  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff 0 h�  std::vector<float,std::allocator<float> > F 6�  std::vector<float,std::allocator<float> >::_Reallocation_policy  �  std::atomic<long> � 藎  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> , �2  std::_Codecvt_guard<char8_t,char32_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> � f�  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,1> ' w  std::numeric_limits<long double>  /  std::errc V W�  std::list<donut::app::IRenderPass *,std::allocator<donut::app::IRenderPass *> > " hX  std::pointer_traits<char *> V 仁  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > ; 唟  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > , 凷  std::default_delete<std::_Facet_base>  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �4  std::to_chars_result  颽  std::_UInt_is_zero  �  std::_Compare_eq y 調  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void>  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> $ hI  std::default_delete<char [0]> . 糱  std::vector<bool,std::allocator<bool> > J 鷤  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 蓚  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000> � 媯  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  z[  std::ratio<1,10000000> V �  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> > > ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  $1  std::_Crt_new_delete % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag 4 c`  std::atomic<std::_Stop_callback_base const *>  �8  std::allocator<char> 秚  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 3 跞  std::_Ptr_base<donut::render::MipMapGenPass>    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> ' x[  std::_Floating_to_chars_overload & H[  std::random_access_iterator_tag 4 汫  std::shared_ptr<donut::engine::ShaderFactory> ; 梷  std::_Conditionally_enabled_hash<unsigned __int64,1> R 酙  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> 2 q�  std::shared_ptr<donut::engine::TextureData> � 倄  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > 3 P0  std::_Tzdb_deleter<__std_tzdb_leap_info [0]> @ 闎  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> s �  std::vector<nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> > > � 芩  std::vector<nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Reallocation_policy # 鄀  std::allocator<unsigned int>  �0  std::_Yarn<wchar_t> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  b  std::wstring 5 譪  std::_Atomic_integral_facade<unsigned __int64> z F�  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ' V  std::numeric_limits<signed char> � 獲  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �-  std::domain_error  �  std::u32string_view  �  std::_Container_base � M|  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  5  std::_Big_integer_flt 2 樟  std::shared_ptr<donut::app::RegisteredFont>  �  std::_Literal_zero ; w  std::weak_ptr<donut::engine::DescriptorTableManager> $ cP  std::hash<nvrhi::IResource *> 3 爷  std::shared_ptr<donut::engine::TextureCache>  �4  std::from_chars_result A =�  std::_Ptr_base<donut::render::MipMapGenPass::NullTextures> � *L  std::_Compressed_pair<std::allocator<std::chrono::time_zone_link>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >,1> " w4  std::_Nontrivial_dummy_type 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> D 譭  std::_Default_allocator_traits<std::allocator<unsigned int> > _ �  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> > > 8 �6  std::chrono::duration<int,std::ratio<2629746,1> > i u;  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<3600,1> > > & 28  std::chrono::year_month_weekday  u6  std::chrono::day 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> > j %7  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<86400,1> > > n :=  std::chrono::time_point<std::chrono::gps_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  7  std::chrono::weekday  �,  std::chrono::nanoseconds  �6  std::chrono::year ' 8  std::chrono::year_month_day_last  -  std::chrono::minutes  �6  std::chrono::days y N=  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > # L7  std::chrono::weekday_indexed 9 �6  std::chrono::duration<int,std::ratio<31556952,1> >  �6  std::chrono::years , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds " �8  std::chrono::time_zone_link 6 �6  std::chrono::duration<int,std::ratio<86400,1> > 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > + X8  std::chrono::year_month_weekday_last  <  std::chrono::tzdb_list + l<  std::chrono::tzdb_list::_Unique_lock + *<  std::chrono::tzdb_list::_Shared_lock ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > " �7  std::chrono::month_day_last  %7  std::chrono::sys_days n =  std::chrono::time_point<std::chrono::utc_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  �8  std::chrono::time_zone q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >   `7  std::chrono::weekday_last ! �7  std::chrono::month_weekday 5 �,  std::chrono::duration<int,std::ratio<3600,1> >  i7  std::chrono::last_spec & �7  std::chrono::month_weekday_last  :7  std::chrono::local_days j �8  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,1> > > N SK  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > > Z .>  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::<lambda_1>  �8  std::chrono::sys_seconds  �7  std::chrono::year_month 8 Z-  std::chrono::duration<double,std::ratio<1,1000> >  s7  std::chrono::month_day " �7  std::chrono::year_month_day  �,  std::chrono::hours < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > @ m[  std::chrono::zoned_traits<std::chrono::time_zone const *> 5 �,  std::chrono::duration<double,std::ratio<1,1> >  �6  std::chrono::months  89  std::chrono::tzdb e :7  std::chrono::time_point<std::chrono::local_t,std::chrono::duration<int,std::ratio<86400,1> > >  @Y  std::chrono::gps_clock  =Y  std::chrono::tai_clock  �6  std::chrono::month n "=  std::chrono::time_point<std::chrono::tai_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  dX  std::chrono::utc_clock  h[  std::chrono::local_t & �=  std::chrono::_Time_parse_fields 8 y=  std::chrono::_Time_parse_fields::_Leap_second_rep = w=  std::chrono::_Time_parse_fields::_Parse_tp_or_duration 3 Y=  std::chrono::_Time_parse_fields::_FieldFlags H g[  std::chrono::duration<__int64,std::ratio<1,1000000000000000000> >  �8  std::chrono::leap_second 8 �,  std::chrono::duration<double,std::ratio<3600,1> >  �2  std::ctype_base  EA  std::filesystem::perms ' 繟  std::filesystem::directory_entry $ JA  std::filesystem::copy_options ( 5A  std::filesystem::filesystem_error 7 rR  std::filesystem::_Path_iterator<wchar_t const *> ) �>  std::filesystem::_Find_file_handle & y>  std::filesystem::_Is_slash_oper . 螧  std::filesystem::_Should_recurse_result $   std::filesystem::perm_options 4 滳  std::filesystem::recursive_directory_iterator . �A  std::filesystem::_File_status_and_error & 9B  std::filesystem::_Dir_enum_impl 0 KB  std::filesystem::_Dir_enum_impl::_Creator @ QB  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! TA  std::filesystem::file_type . nB  std::filesystem::_Directory_entry_proxy " 揇  std::filesystem::space_info * 桞  std::filesystem::directory_iterator & N=  std::filesystem::file_time_type 0 虰  std::filesystem::_Recursive_dir_enum_impl ) 鞟  std::filesystem::directory_options # gA  std::filesystem::file_status u 霡  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( U[  std::filesystem::_File_time_clock  ?  std::filesystem::path $ �>  std::filesystem::path::format * ?R  std::filesystem::_Normal_conversion < gU  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �1  std::codecvt<char16_t,char,_Mbstatet> , �2  std::_Codecvt_guard<char32_t,char8_t> � u  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  R[  std::char_traits<char> � 镮  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �.  std::error_category ) �.  std::error_category::_Addr_storage  \~  std::_Wrap<std::mutex> i o:  std::tuple<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! �/  std::_System_error_message  �  std::_Unused_parameter " 瀪  std::hash<nvrhi::IShader *> h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  l4  std::bad_optional_access A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > W   std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > , 確  std::_Atomic_integral<unsigned int,4> u 嵤  std::vector<nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � >�  std::vector<nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Reallocation_policy 7 !B  std::shared_ptr<std::filesystem::_Dir_enum_impl> �   std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> " DT  std::_Floating_point_string 8 z  std::_Ptr_base<donut::engine::CommonRenderPasses> = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q 1~  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1> 2 �5  std::_Extended_Pictographic_property_values  �1  std::_Codecvt_mode @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > z 爙  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1> ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn $ q?  std::ranges::_Iter_move::_Cpo ) o?  std::ranges::_Iter_move::_Cpo::_St % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn    std::_Exact_args_t  P[  std::ratio<86400,1>  V6  std::default_sentinel_t � A  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > >  N[  std::ratio<31556952,1> 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8> {b|  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >,1>  =K  std::ostringstream � w|  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >  6  std::_Fmt_buffer_traits \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  g0  std::_Facet_base ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> 2 �2  std::codecvt<unsigned short,char,_Mbstatet> . �=  std::_Optional_destruct_base<__int64,1> - �=  std::_Optional_construct_base<__int64> # �/  std::_Generic_error_category  扟  std::streampos  B[  std::input_iterator_tag 2 JU  std::_Wrap<std::filesystem::_Dir_enum_impl> X 孲  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> 0 芻  std::_Atomic_integral<unsigned __int64,8> ^ �  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > , 塪  std::_Atomic_padded<unsigned __int64> ' 綪  std::hash<enum nvrhi::ColorMask> / ,2  std::codecvt<char16_t,char8_t,_Mbstatet> 5 
`  std::_Locked_pointer<std::_Stop_callback_base> 3 豃  std::basic_ios<char,std::char_traits<char> >  �1  std::codecvt_base & 譒  std::_Fmt_counting_buffer<char> �   std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *>  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag ' P~  std::_Ref_count_obj2<std::mutex> v 鮸  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> � 5�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > ; 賓  std::allocator_traits<std::allocator<unsigned int> > ' 蜠  std::hash<std::filesystem::path> 	r}  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � L}  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > _ ?[  std::allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > > 7 �<  std::_Atomic_storage<std::chrono::tzdb_list *,8>  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > l ��  std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> > 7 40  std::_Tzdb_deleter<__std_tzdb_current_zone_info> " �  std::_Asan_aligned_pointers �   std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m 剉  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � Sv  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy > 诽  std::allocator<nvrhi::RefCountPtr<nvrhi::IBindingSet> > F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  q5  std::format_error  �  std::partial_ordering # |5  std::_Decode_result<wchar_t> � ,�  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > . <#  std::array<nvrhi::BindingLayoutItem,16>  9[  std::_Default_sentinel P 耎  std::_Default_allocator_traits<std::allocator<std::chrono::leap_second> > $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int>   �5  std::_GB11_LeftHand_regex * �5  std::_GB11_LeftHand_regex::_State_t 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access 
 !   wint_t  t   ImDrawFlags  �  ImGuiStorage % 婕  ImGuiStorage::ImGuiStoragePair  !   ImWchar  祌  CACLIPDATA # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling " �  nvrhi::SamplerReductionType $ $�  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets  s  nvrhi::ResourceStates . d�  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  &  nvrhi::GraphicsState * DH  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16> ! v  nvrhi::SharedResourceFlags  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! �(  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc 2 馟  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray . 鮞  nvrhi::RefCountPtr<nvrhi::ICommandList>  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture ! 蒪  nvrhi::utils::ScopedMarker $ =b  nvrhi::utils::BitSetAllocator . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  DH  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # {w  nvrhi::DescriptorTableHandle     nvrhi::ShaderType  �(  nvrhi::TimerQueryHandle 2 {w  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # 馟  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - H  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  d�  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  賧  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 $�  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 賧  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  H  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # 齺  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  �  nvrhi::CpuAccessMode  鮞  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState 2 齺  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery  硆  VARDESC & 枥  ImVector<ImFontAtlasCustomRect>     LONG  皉  ITypeLib  $r  tagCACY  塺  tagBSTRBLOB  噐  tagCAUH  8g  _TP_CALLBACK_ENVIRON_V3 0 Bg  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> B Og  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s>  "r  _ULARGE_INTEGER ( 剅  _ULARGE_INTEGER::<unnamed-type-u>  �/  __std_win_error  S0  __std_tzdb_leap_info  辡  LPVARIANT  泀  SAFEARRAY  �0  lconv    D3D_SRV_DIMENSION % O�  EnvMapImportanceSamplingParams  乺  tagCABOOL   �  __RTTIBaseClassDescriptor  
�  D3D12_SHADER_CACHE_MODE  @�  ImVector<float>  r  tagBLOB 
 乺  CABOOL   ]�  D3D12_ROOT_PARAMETER_TYPE 
    _off_t  #   ULONG64 
 �  SNB  8q  _tagINTERNETFEATURELIST  �r  CABSTRBLOB 
 #   SIZE_T  }r  tagTYPEATTR � 嬌  GenericScope<`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`3'::<lambda_1>,`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`3'::<lambda_2> >    stat  t   ImFontAtlasFlags  智  ImGuiComboFlags_  t   int32_t  Z  timespec & JZ  $_TypeDescriptor$_extraBytes_37  cD  __std_fs_file_id 
 !   _ino_t 
 沘  _Cnd_t  奚  ImGuiTabBarFlags_  A   DATE # yr  ReplacesCorHdrNumericDefines  榞  FS_BPIO_OUTFLAGS  郧  ImGuiColorEditFlags_  "   DWORD # 70  __std_tzdb_current_zone_info  0g  PTP_CALLBACK_INSTANCE ' ID  __std_fs_create_directory_result 
   PSHORT    D3D12_MESSAGE_ID  "   TP_VERSION  q  BSTR  Q�  D3D_DRIVER_TYPE  !   uint16_t  t   ImGuiViewportFlags  �/  __std_fs_stats  旨  ImVector<char>  譹  CAUB  sr  ITypeInfo $ 僃  donut::engine::ICompositeView    donut::engine::IView ( qz  donut::engine::CommonRenderPasses 5 眝  donut::engine::CommonRenderPasses::PsoCacheKey ; 箆  donut::engine::CommonRenderPasses::PsoCacheKey::Hash " $x  donut::engine::TextureCache , =x  donut::engine::TextureCache::Iterator $ 謥  donut::engine::BlitParameters   蜦  donut::engine::PlanarView ( Cu  donut::engine::FramebufferFactory ! 焪  donut::engine::BufferGroup ! 﨑  donut::engine::ShaderMacro # 紿  donut::engine::ShaderFactory  鈝  donut::engine::MeshInfo  褀  donut::engine::MeshType " +t  donut::engine::BindingCache & 輛  donut::engine::DescriptorHandle , Ow  donut::engine::DescriptorTableManager B w  donut::engine::DescriptorTableManager::BindingSetItemsEqual B w  donut::engine::DescriptorTableManager::BindingSetItemHasher % 墂  donut::engine::VertexAttribute % t   donut::engine::DescriptorIndex " 沍  donut::engine::StaticShader &   donut::app::StreamlineInterface 6 苋  donut::app::StreamlineInterface::DLSSRRSettings 5 厝  donut::app::StreamlineInterface::DLSSRROptions A 內  donut::app::StreamlineInterface::DLSSRRNormalRoughnessMode 4 伻  donut::app::StreamlineInterface::DLSSRRPreset 2 匀  donut::app::StreamlineInterface::DLSSGState 3 w�  donut::app::StreamlineInterface::DLSSGStatus 4 腥  donut::app::StreamlineInterface::DLSSGOptions A t�  donut::app::StreamlineInterface::DLSSGQueueParallelismMode 2 r�  donut::app::StreamlineInterface::DLSSGFlags 1 p�  donut::app::StreamlineInterface::DLSSGMode 3 倘  donut::app::StreamlineInterface::ReflexState 4 侨  donut::app::StreamlineInterface::ReflexReport 5 萌  donut::app::StreamlineInterface::ReflexOptions 2 c�  donut::app::StreamlineInterface::ReflexMode 6 咳  donut::app::StreamlineInterface::DeepDVCOptions 3 Z�  donut::app::StreamlineInterface::DeepDVCMode 2 蝗  donut::app::StreamlineInterface::NISOptions . S�  donut::app::StreamlineInterface::NISHDR / Q�  donut::app::StreamlineInterface::NISMode 4 啡  donut::app::StreamlineInterface::DLSSSettings 3 橙  donut::app::StreamlineInterface::DLSSOptions 2 A�  donut::app::StreamlineInterface::DLSSPreset 0 ?�  donut::app::StreamlineInterface::DLSSMode 1   donut::app::StreamlineInterface::Constants .   donut::app::StreamlineInterface::Extent  祷  donut::app::IRenderPass   吇  donut::app::DeviceManager 3 3�  donut::app::DeviceManager::PipelineCallbacks + 敾  donut::app::DeviceCreationParameters % 幓  donut::app::InstanceParameters ! ┝  donut::app::ImGui_Renderer ! z�  donut::app::RegisteredFont  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2 # 錏  donut::math::vector<float,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes % 慫  donut::math::matrix<float,3,4>  (G  donut::math::uint2  F  donut::math::float4 % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3>   z{  donut::math::box<float,2>   4I  donut::math::box<float,3> " *E  donut::math::vector<bool,2>  4I  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> * (G  donut::math::vector<unsigned int,2> # #�  donut::render::MipMapGenPass ) �  donut::render::MipMapGenPass::Mode  踘  tagPROPVARIANT  &r  CAUL M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  $r  CACY    _Mbstatet  "r  ULARGE_INTEGER   �  ImGuiButtonFlags_  6g  TP_CALLBACK_PRIORITY  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ! )0  __std_tzdb_time_zones_info  @q  VARENUM     intmax_t  蛁  tagCASCODE # 烨  D3D_TESSELLATOR_PARTITIONING  Q�  ImGuiViewport    terminate_handler  �  _s__RTTIBaseClassArray  ,q  tagCACLSID  Ug  MACHINE_ATTRIBUTES & VZ  $_TypeDescriptor$_extraBytes_52  C�  D3D_RESOURCE_RETURN_TYPE  x�  ImFontAtlas 
 Y  ldiv_t 0 u�  ImVector<ImGuiTextFilter::ImGuiTextRange>  r  tagCALPWSTR  �/  __std_fs_file_flags  �0  _Cvtvec  !   ImDrawIdx  r  BLOB  #   DWORD64  u   _Thrd_id_t  t   ImDrawListFlags  !   PROPVAR_PAD1 - �  $_s__RTTIBaseClassArray$_extraBytes_24  3g  PTP_SIMPLE_CALLBACK  �  D3D12_MESSAGE_CATEGORY � 纳  GenericScope<`EnvMapImportanceSamplingBaker::Update'::`2'::<lambda_1>,`EnvMapImportanceSamplingBaker::Update'::`2'::<lambda_2> > 
 t   INT  �  _CatchableTypeArray  r  IStorage  [�  ImGuiPlatformImeData  鋑  tagVARIANT 
 蟩  tagCAI 
 A   DOUBLE      UCHAR  �  ImGuiPayload   �  _D3D_SHADER_CBUFFER_FLAGS  "   LCID      BOOLEAN  &g  PTP_CALLBACK_ENVIRON  �/  __std_fs_copy_options     ptrdiff_t  緌  tagTYSPEC  籫  LPVERSIONEDSTREAM  
  _stat64i32  ?�  D3D12_LOGIC_OP  醧  tagDISPPARAMS  E0  __std_tzdb_sys_info  嚱  ImDrawCmd 
 !   USHORT  �  _PMD  �  ImVector<ImVec4>      uint8_t  鑗  LPUWSTR    ImVector<unsigned short>  0q  tagVARKIND & 衼  $_TypeDescriptor$_extraBytes_41  ,  type_info  �  ImFontGlyph    PVOID  遯  SAFEARRAYBOUND ' �  _s__RTTIClassHierarchyDescriptor  Yq  IUnknown  t   errno_t  q   WCHAR     PBYTE  _�  D3D_TESSELLATOR_DOMAIN  t   ImGuiWindowFlags  �/  __std_fs_reparse_tag  単  _DEVICE_DSM_DEFINITION 
 苢  tagCAC  譹  tagCAUB  \  _lldiv_t 
 蚮  IID 
 栈  ImVec4 ! 芮  _D3D_SHADER_VARIABLE_FLAGS  �  ImGuiCol_  :q  _tagQUERYOPTION  嗲  ImGuiWindowFlags_  q  LPOLESTR  E�  D3D_PRIMITIVE  �  tagExtentMode  萹  __MIDL_IUri_0002     HRESULT  =�  _D3D_SHADER_INPUT_TYPE  C  __std_type_info_data 
 蟩  CAI  zg  PDEVICE_DSM_INPUT & �  $_TypeDescriptor$_extraBytes_27  淝  ImDrawFlags_  蛁  CASCODE  G  _s__ThrowInfo  c6  __std_fs_convert_result /   D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER ! 蘱  __MIDL_IGetBindHandle_0001  �/  __std_fs_stats_flags  妐  tagCY 
    LONG64  |�  ImVector<ImDrawCmd>  <q  tagCOINITBASE  頶  LPCUWSTR  "   ULONG  �  __RTTIBaseClassArray ! �  D3D12_STATE_SUBOBJECT_TYPE  !   VARTYPE  t   BOOL 
 苢  CAC / F�  ImVector<ImGuiStorage::ImGuiStoragePair>  �  __crt_locale_data_public  膓  tagApplicationType  �  ImFontGlyphRangesBuilder 0 廹  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES_OUTPUT  摽  ImDrawVert  �  LPCWSTR & 竡  DISPLAYCONFIG_SCANLINE_ORDERING - �  $_s__CatchableTypeArray$_extraBytes_24  @   FLOAT  !   PROPVAR_PAD3  &q  tagDOMNodeType  聁  CAUI  纐  tagCLIPDATA  �  ImGuiSelectableFlags_  Ya  _Mtx_internal_imp_t  泀  tagSAFEARRAY & 4Z  $_TypeDescriptor$_extraBytes_25  "   DEVICE_DSM_ACTION % �  __RTTIClassHierarchyDescriptor  �0  _Collvec   艭  __std_fs_volume_name_kind  紂  tagVersionedStream 0 �  __vcrt_va_list_is_reference<char const *> 
 簈  CABSTR     __time64_t  2q  tagCHANGEKIND 
    fpos_t 
 u   UINT32  鏔  PolymorphicLightInfoEx  �  FILE  宷  tagSYSKIND  憧  ImVector<ImDrawList *>  u   ImGuiID 
 Ma  _Mtx_t 3 �  __vcrt_va_list_is_reference<wchar_t const *>  秖  IDispatch  蚮  CLSID    mbstate_t  ?  _PMFN  #   uintptr_t 
 q  LPWSTR  踘  PROPVARIANT  絞  LPSAFEARRAY  #   UINT_PTR  谇  ImGuiTableColumnFlags_  (g  PTP_POOL  �  _s__CatchableTypeArray   榛  ImGuiTableColumnSortSpecs  DD  __std_fs_remove_result  蚮  GUID * #g  _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG '   D3D_TESSELLATOR_OUTPUT_PRIMITIVE # 銮  D3D12_INDIRECT_ARGUMENT_TYPE  厍  D3D12_COMMAND_LIST_TYPE  8g  TP_CALLBACK_ENVIRON_V3  6q  tagFUNCKIND  u   ImU32  钋  ImGuiNavInput  効  ImDrawCmdHeader  梣  LARGE_INTEGER 
 攓  CAH  t   ImGuiChildFlags  騀  PolymorphicLightInfoFull  t   INT32  憅  tagCAFILETIME 
   HANDLE  昵  D3D12_LIFETIME_STATE  "q  PIDMSI_STATUS_VALUE $ I�  EnvMapImportanceSamplingBaker  枨  _D3D_CBUFFER_TYPE  #   ULONGLONG    ImTextureID  巕  tagCAPROPVARIANT ( ,g  PTP_CLEANUP_GROUP_CANCEL_CALLBACK  t   ImGuiSortDirection 	 妐  CY  靈  _Thrd_t  坬  FILETIME  g  PDEVICE_DSM_RANGE ( 耷  D3D12_DEBUG_DEVICE_PARAMETER_TYPE - �  $_s__RTTIBaseClassArray$_extraBytes_16  唓  __MIDL_IUri_0001    ImDrawData 
 済  REGCLS  仪  ImVector<ImFontGlyph> - /Z  $_s__RTTIBaseClassArray$_extraBytes_32  u   DXGI_USAGE  剄  IRecordInfo  t   ImGuiInputTextFlags 
 #   size_t  峠  PDEVICE_DSM_OUTPUT 
    time_t  �/  __std_fs_file_attr     LONGLONG   溓  D3D12_MEASUREMENTS_ACTION  �  __std_exception_data * O�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE 
 u   _dev_t  紺  __std_ulong_and_error ) |g  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES  毲  ImGuiTableFlags_  Dq  tagGLOBALOPT_EH_VALUES 
 然  ImVec2 * !g  PCUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG  :�  ImGuiTextFilter & 伡  ImGuiTextFilter::ImGuiTextRange  A   __std_tzdb_epoch_milli  \  lldiv_t     SHORT  S�  ImGuiListClipper    PLONG64  Y  _ldiv_t  爂  COWAIT_FLAGS     SCODE  >q  tagCLSCTX  斍  ImGuiPopupFlags_  俳  ImVector<ImDrawChannel>  [  _timespec64     intptr_t     INT_PTR  S�  _D3D_SHADER_INPUT_FLAGS  捛  ImVector<ImFontConfig>  u   uint32_t  4q  tagXMLEMEM_TYPE " Y�  D3D_REGISTER_COMPONENT_TYPE 
 �  _iobuf 
 .q  CADATE !   ImGuiInputTextCallbackData  p   CHAR  ,q  CACLSID  !   PROPVAR_PAD2  *q  _tagPARSEACTION  I�  D3D12_MESSAGE_SEVERITY + G�  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE  p  LPSTR  K�  ImVector<void *>  (q  tagDESCKIND  �  __crt_locale_pointers 
  q  tagCAL  #   DWORDLONG  �   x5      衠琪槡铟钭}_XO>�蛭X�7Mp处d  Q    �(M↙溋�
q�2,緀!蝺屦碄F觡  �    �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  �    �~鴧傳.P怬WsP-"焫#N�:�&場璁  2   �fwv鋽砻毆�經�⒂k秼芴襚扉w  {   �=A�%K鹹圛19振╯鵽C殾錦`蔣  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   伙a蕑堐v!@曓�+梭�'驂蒀飫鴐  C   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  �   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   *u\{┞稦�3壅阱\繺ěk�6U�     o�椨�4梠"愜��
}z�$ )鰭荅珽X  O   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   D,y邥鞃黎v)�8%遾1�*8赩�婯�     駒og暼硚~鮼_5屏�8BL.9妖"�r/�-R  ]   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  ;   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  {   5睔`&N_鏃|�<�$�獖�!銸]}"  �   �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  	   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  \   ┫緞A$窄�0� NG�%+�*�
!7�=b  �   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  I   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  �   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  0   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  t    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   頒牛/�	� G犨韈圂J�.山o楾鐴  �   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  J	   喲8s7痺撆�&!林qA>ブ�Y~a璳`N3�  �	   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  �	   溶�$椉�
悇� 騐`菚y�0O腖悘T  @
   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �
   ��(`.巑QEo"焷�"娧汝l毮89fб�  �
   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  '   �8��/X昋旒�.胱#h=J"髈篒go#  s   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  �   a: 覩徿舻壼紾甉� l(候鼱\Q?m�  �   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  /   �儔14褥緅�3]饃鹷�hK3g搋bA竑  {   綔)\�谑U⒊磒'�!W磼B0锶!;  �   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  
   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  ;
   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  y
   觑v�#je<d鼋^r
u��闑鯙珢�  �
   ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �
   E縄�7�g虩狱呂�/y蛨惏l斋�笵  >   �	玮媔=zY沚�c簐P`尚足,\�>:O     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5     !m�#~6蠗4璟飜陷]�絨案翈T3骮�  _    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   泭盨p榩,^藎�髈V尦�懰?v��`  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟     �暊M茀嚆{�嬦0亊2�;i[C�/a\  O   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  �   �呾��+h7晃O枖��*谵|羓嗡捬  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  .   蕰уt厅隍2GeE升QU"柬�)忲嗅{≌洮  f    栀��綔&@�.�)�C�磍萘k  �   d2軇L沼vK凔J!女計j儨杹3膦���  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  F   t	*=Pr,�8qQ镯椅鯘�=咽Bz  w    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  �   <AE#(�/y鍹籲贚痶蹒-	蒸|邨VWTR     鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  W   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   B�睃娏�,棒繪�1舸麂筓1�0m蘉##  �   2W瓓�<X	綧]�龐IE?'笼t唰��  1   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  
   揾配饬`vM|�%
犕�哝煹懿鏈椸  J   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  �   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  /   �F9�6K�v�/亅S诵]t婻F廤2惶I  }   襋钡�/翤㏄^觜�藔膛vЮ7��37嵇  �   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A     馒{蓱蕩L蘯e�LW�疗譄幗IR:;a�  f   ﹪冓�嗈�1蓌�)eF�;珉5� �橌5偧  �   錵s铿勃砓b棬偡遯鮓尛�9泂惻  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  5   $G\|R_熖泤煡4勄颧绖�?(�~�:  �   �"睱建Bi圀対隤v��cB�'窘�n  �   }竓4慡6騘P砽o镧j漵pA瓊鼔�I     �7穲碶⒖鍉鸻�:怉婤莞b=竱�  `   �2梉�7伆鼂:E囂!2Wｔ孵>羑�輡  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  :   擐�0阅累~-�X澐媆P 舋gD�     U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   D���0�郋鬔G5啚髡J竆)俻w��     鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  O   嫎V濺蕠&kZ峠l甩u8蝬QW蹚z`k
+  �   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  �   9芽�!綤襽}癬�&-�嗊靗�  "   Pフ+
V
*戋zydD躩=壠�jA�N盪{u5  i   蓏翰bsF讍E熑j]�0萛b*xs*q翻惲�  �   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  �   仫減f╋萩}_遫R衵9弧0Up*q蹩鎍,  9   f扥�,攇(�
}2�祛浧&Y�6橵�  w   �芮�>5�+鮆"�>fw瘛h�=^���  �   F�#D瓄钐�2�,韼\pQ(�垜LA's纡-     [届T藎秏1潴�藠?鄧j穊亘^a  M   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  �   �9俅�(� .]嬝暧烵啤�秾*~w�=[@�  �   蜅�萷l�/费�	廵崹
T,W�&連芿     c�#�'�縌殹龇D兺f�$x�;]糺z�  r   bRè1�5捘:.z錨{娯啹}坬麺P  �   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  
    6��7@L�.�梗�4�檕�!Q戸�$�  W    ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  �    鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �    (鄁盯J錭澥A��/�!c� ;b卹  >!   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  �!   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �!   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  "   覽s鴧罪}�'v,�*!�
9E汲褑g;  `"   饵嶝{郀�穮炗
AD2峵濝k鴖N  �"   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �"   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  #   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�  c#   G髼*悭�2睆�侻皣軁舃裄樘珱)  �#   Fp{�悗鉟壍Au4DV�`t9���&*I  �#   k�8.s��鉁�-[粽I*1O鲠-8H� U  .$   +YE擋%1r+套捑@鸋MT61' p廝 飨�  o$   交�,�;+愱`�3p炛秓ee td�	^,  �$   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  %   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  L%   妇舠幸佦郒]泙茸餈u)	�位剎  �%   _臒~I��歌�0蘏嘺QU5<蝪祰S  �%   靋!揕�H|}��婡欏B箜围紑^@�銵  &   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  J&   禿辎31�;添谞擎�.H闄(岃黜��  �&   戹�j-�99檽=�8熈讠鳖铮�  �&   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  ''   �颠喲津,嗆y�%\峤'找_廔�Z+�  p'   ��嵉氒髅嘁棭够*ヅ�
�'徺p4  �'   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  (   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  D(   t�j噾捴忊��
敟秊�
渷lH�#  �(   ,�<鈬獿鍢憁�g$��8`�"�  �(   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  
)   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  R)   傠3UX�<頹%O磳me鸘 涒雋xW5叒z迦  �)   �-考X韵歎K�fUi2�'跎Zq@锍�2aJ�  �)   8蟴B或绢溵9"C dD揭鞧Vm5TB�  /*   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  n*   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �*   鹰杩@坓!)IE搒�;puY�'i憷n!  �*   Eム聂�
C�?潗'{胿D'x劵;釱�  G+   猯�諽!~�:gn菾�]騈购����'  �+   悯R痱v 瓩愿碀"禰J5�>xF痧  �+   聤�苮g8鄞<aZ�%4)闪�|袉uh�  ,   0T砞獃钎藰�0逪喌I窐G(崹�  d,   矨�陘�2{WV�y紥*f�u龘��  �,   o藾錚\F鄦泭|嚎醖b&惰�_槮  �,   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  4-   蠯3
掽K謈 � l�6襕鞜��H#�  q-   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �-   齛|)3h�2%籨糜/N_燿C虺r_�9仌  .   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  c.   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �.   嵮楖"qa�$棛獧矇oPc续忴2#
  �.   謷蘋愦	豢]麸Md�觙 G澿f1e
T奞~  A/   )�Q�Ａ9氫�&煝]R埬�楜� 磛庋p�  �/   �)D舼PS橼鈝{#2{r�#獷欲3x(  �/   zY{���睃R焤�0聃
扨-瘜}  0   A縏 �;面褡8歸�-構�壋馵�2�-R癕  Q0   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �0   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �0   吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  -1   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  w1   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �1   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  
2   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  S2   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�  �2   P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�  �2   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  *3   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  i3   潝(綊r�*9�6}颞7V竅\剫�8値�#  �3   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �3   �l{`G2
`0m襭皗犿鷕g貋⒌joゥ  A4   ^憖�眜蘓�y冊日/缁ta铁6殔  �4   魯f�u覬n\��zx騖笹笾骊q*砎�,�  �4   ,沒k~w(I�$儃儒齩r?)i歗隰4  5    萾箒�$.潆�j閖i转pf-�稃陞��  g5   彲 ㄋ牐媝e哟�<N7%�Yn�52�峟�  �5   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  �5   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  $6   チ畴�
�&u?�#寷K�資 +限^塌>�j  X6   跻]�	隗[鼀hF鰘廰)鋥Le6�攬R7  �6   iu�	��&? 蔬釥�#簤Ηu-塘b@遍迭  �6   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  47   � 罟)M�:J榊?纸i�6R�CS�7膧俇  �7   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �7   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  8   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  O8   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  �8   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  �8   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  "9   V� c鯐鄥杕me綻呥EG磷扂浝W)  n9   穫農�.伆l'h��37x,��
fO��  �9   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �9   5�\營	6}朖晧�-w氌rJ籠騳榈  .:   v-�+鑟臻U裦@驍�0屽锯
砝簠@  i:   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �:   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �:   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  +;   郖�Χ葦'S詍7,U若眤�M进`  |;   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �;   哳訵a醴泉撬4wO�)鹁疜�)誊)�=跹�  <   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  F<   �*o驑瓂a�(施眗9歐湬

�  �<   dhl12� 蒑�3L� q酺試\垉R^{i�  �<   �0�*е彗9釗獳+U叅[4椪 P"��  =    I嘛襨签.濟;剕��7啧�)煇9触�.  H=   �3掍S\絧飦戀轝
5鍄0d
妺XE��
  �=   閯�価=�<酛皾u漑O�髦jx`-�4睲�  �=   �=蔑藏鄌�
艼�(YWg懀猊	*)  >   �="V�A�D熈fó 喦坭7b曉叼o1  d>   =J�(o�'k螓4o奇缃�
黓睆=呄k_  �>   L�9[皫zS�6;厝�楿绷]!��t  �>   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  (?   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  i?   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �?   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �?   檅鋲�1o婈$�;�芯厁%rP�衃K設  3@   )鎋]5岽B鑯 �誽|寋獸辪牚  q@   _O縋[HU-銌�鼪根�鲋薺篮�j��  篅   +FK茂c�G1灈�7ほ��F�鳺彷餃�  隌   煋�	y鋵@$5х葑愔*濋>�( 懪銳  %A   龀缬@唶�鱮�瓗禽鐑�^]>Xz豹tN  iA   l籴靈LN~噾2u�< 嵓9z0iv&jザ  籄   r�L剟FsS鏴醼+E千I呯贄0鬬/�  B   v峞M� {�:稚�闙蛂龣 �]<��  LB   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏  滲   ii(祍P%舌+;�'縿E╪嫏脈3L籾
�  酈   W簅r�hc鷟;9攫�.�6Y柦阁��}.J╗  +C   �'稌� 变邯D)\欅)	@'1:A:熾/�  tC   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  盋   �-�雧n�5L屯�:I硾�鮎访~(梱  鯟   	{Z�范�F�m猉	痹缠!囃ZtK�T�  5D   ��8/�
0躚/﨤h盙裉餠G怤爛��]�  D   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  紻   澏泼寊H"鱄LUHFy]厽I媢h�J赫Cp   E   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  OE   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  滶   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  逧   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  F   _%1糠7硘籺蚻q5饶昈v纪嗈�  gF   �:2K] �
j�苊赁e�
湿�3k椨�  盕   樸7 忁�珨��3]"Fキ�:�,郩�  鵉    狾闘�	C縟�&9N�┲蘻c蟝2  7G   繃S,;fi@`騂廩k叉c.2狇x佚�  �G   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  罣   犢x琿儳莻k鬿锘HC閷�!(/捱&eM殙�  	H    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  VH   掴'圭,@H4sS裬�!泉:莠й�"fE)     o忍x:筞e飴刌ed'�g%X鶩赴5�n�  鞨   `k�"�1�^�`�d�.	*貎e挖芺
脑�  /I   <瑓�傻a鱼张隥T$ vJ黠I鯝�0  zI   �茬� 霚FmH暃蜽.駢宫誖�6?靵oP  翴   U恂{榸冾�fⅢ��Hb釃"�6e`a  J   
捃閺嚞?� 龀�*�煾/踈0�R璷�  \J   窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  琂   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  麶   僘u鹋� !敒99DK汜簯�叮瀒蛂  7K   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  qK   譫鰿3鳪v鐇�6瘻x侃�h�3&�  疜   �
bH<j峪w�/&d[荨?躹耯=�  頚   W躊��:(蚏濠迤鵢僛L生N!g`璣{  (L   �fE液}髢V壥~�?"浬�^PEΡ4L�  nL   嶹栢ABZC凂U久Gk�!貟~龡单癉Q  窵   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  鱈   j轲P[塵5m榤g摏癭 鋍1O骺�*�  @M   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  扢   +椬恡�
	#G許�/G候Mc�蜀煟-  襇   K�:荳)a懃J�拌� ,寨吙u⑺�  N   詄�9LTU�梀黂[&瓬0櫽鲑C墅P  eN   葦鮆培XDW駐鰄�W{�+ノ2$<F$帰B  琋   t$�>喰k柸窣鄾埉笺%撙>2 pg�,  鯪   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  BO   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  丱   8�'预P�憖�0R�(3銖� pN*�  蚈   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  P   sL&%�znOdz垗�M,�:吶1B滖  cP   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  癙   鹴y�	宯N卮洗袾uG6E灊搠d�  鳳   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  AQ   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  慟   渐袿.@=4L笴速婒m瑜;_琲M %q�  鉗   副謐�斦=犻媨铩0
龉�3曃譹5D   %R   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠  uR   弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  薘   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  S   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  SS   RX鰷稐蒋駏U	�>�5妆癫�
8A/     x)憂^恛閶{籮>鐼末BA<颭~儽&'%;-g�  釹   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  T   �>2
^�﨟2W酟傲X{b?荼猲�;  ^T   険L韱#�簀O闚样�4莿Y丳堟3捜狰  汿   .QVBL籺'Z鬖b濁蟂�8w,/kr彨�$擠�  轙   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  &U   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  pU   豊+�丟uJo6粑'@棚荶v�g毩笨C  砋   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  V   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  CV   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  擵   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  訴   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�   W   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  TW   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  揥      鞼   匐衏�$=�"�3�a旬SY�
乢�骣�  7X   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  俋   b骺_�(4参♁� N�z陾Ia癓t�&醇  蚗   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  Y   .�-髳�o2o~翵4D�8鷗a殔氰3籃G  gY   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  礩   丩{F*}皦N誫l雘啫椊�梮,圶`�  齓   �n儹`
舔�	Y氀�:b
#p:  PZ   "�挨	b�'+舒�5<O�呱_歲+/�P�?  橺   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  醃   6觏v畿S倂9紵"�%��;_%z︹  ([   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  r[   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   蔥   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  \   鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  G\   �X�& 嗗�鹄-53腱mN�<杴媽1魫  抃   曀"�H枩U传嫘�"繹q�>窃�8  裓   鏀q�N�&}
;霂�#�0ncP抝  
]   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  ]]   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃     R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  轢   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  )^   $^IXV嫓進OI蔁
�;T6T@佮m琦�  `^   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   �
      �  �  B   �  �  H   �  �  Y   �  �  �   b  �  �  r  �&  U   s  �&  �   �  x  \   �  x  �  �  x  n  �  x  o  �  x  x  �  x    �  x  %  �  x  [  �  �  �  �  �  �  �  �  x  �  �   K   
  0  Q     0  Q   �  �  �  �  �  Z  �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `  �   �  h  q   �  h  @   �  h  5   �  h  x   �  h  5   �  `    �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `  �   �  h  q   �  h  @   �  h  5   �  h  q   �  h  @   �  h  5   J     B  R     �	  m  �  5  �  �  t  �  `    �  `  �   �  `    �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `  �   �  `    �  `  �   �  `  �   �  `  �   �  `    �  `  �   �  `  �   %     �  &     �  )     +
  0     �  3  �&  �  >     �  ?     0   k  `  �   �     D
  �     �  �     O   �  `  �   �  `  �   �  `  �   �  �&  �  
  �  D  A  �&  �  c  �&  �   e  �   �   �  �  n  �  `  �   �  `  �   �     �  �     �  �  �&  �       s       �  �     )
  (  �&  �   9   �&  �   J   �&  @   �   �&  @   �'  �%     �'  �  5  �'  `  �   �'  `    �'  `  �   �'  `  �   (  �  �  (     j   (  �  >  (     �  (  `    (  `  �   (  `  �   %(  �  Z  &(  �  t  '(  �  4  ((  �  u  )(  `  �  /(     @
  G(  �    H(  �    I(  �&  �  L(  `  X  M(  `  "  N(     1   Q(  `  %   Z(  �  
  [(  �&  �  ](  `  '  `(  �&  �  }(  �  D  (  �    �(  `  C  �(  `  3  �(  �&  �  �(  �&  �  �(  1  �  �(  �&  �  �(  �&  F  �(  �  n  �(  `  a  �(  �&  �  �(  �&  �  �(  �&  �  �(  `  <  �(  �&  R  �(  @  �  )  �&  �  !)  �&  9  *  x  [   
*  `    *  `  �   �*  �  �  �*  �  t  �*  0  P   +  �  5  +  �     1  x  �  1  x  �  1  x  w  1  x  L  �1  `  �   �1  �  �  �1  �  Z  �1  �  5  �1  `  �   �1  �  t  F2  �  D  c2  `  �   k2  �  n  �3  x  �  �3  x  �	  �3  x  �	  �3  �  Z   �3  �     �3  �  5  �3  �
  �   �3  �
  �   �3  �
  �   �3  �
  �   �3  �
  �   �3  �
  �   �3  h  !   �3  h      �3  h  !   �3  h       4  �  �  4  `  �   4  �  �  4  �  �  	4  `  �   
4  `  �   4  �  `   4  �  �  !4  �  d  "4  �  t  %4     9
  )4  �  �  *4  @  �  +4  �  z  ,4  �  t  -4  @  �  /4  �&  �  44  X  �  54  �  �
  74  X  �  94  0  �   B4  �  :  E4     �  G4  �   �   H4  �&  >  M4  �  �  ]4  �  �  b4  �&  �  i4  �  �  j4  �  �  s4  �  9  v4  �&  �  w4  �&  9  z4  �&  �  {4  �   �  �   橿   C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\queue D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\bcrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmeapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winscard.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsystem.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsyscom.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h D:\RTXPT\Rtxpt\SampleCommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\include\donut\app\StreamlineInterface.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\RTXPT\External\Donut\include\donut\engine\View.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\RTXPT\External\Donut\include\donut\app\imgui_nvrhi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\RTXPT\External\Donut\thirdparty\imgui\imgui.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_print.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\External\Donut\thirdparty\imgui\imconfig.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgitype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dpapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\Rtxpt\Lighting\Distant\EnvMapImportanceSamplingBaker.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleauto.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\cderr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dde.h D:\RTXPT\External\Donut\include\donut\core\math\box.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\commdlg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h D:\RTXPT\External\Donut\include\donut\render\MipMapGenPass.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_tzdb.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ddeml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\format D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_format_ucd_tables.hpp D:\RTXPT\Rtxpt\Lighting\Distant\EnvMapImportanceSamplingBaker.hlsl D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stop_token D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\inaddr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bit D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\LightingTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\RTXPT\External\Donut\include\donut\core\math\color.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winsmcrd.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu_tables.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\shellapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h D:\RTXPT\Rtxpt\Lighting\Distant\EnvMapImportanceSamplingBaker.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h D:\RTXPT\External\Donut\include\donut\engine\BindingCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsock.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\nb30.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\joystickapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winperf.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dlgs.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\External\Donut\include\donut\engine\TextureCache.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\PolymorphicLight.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\forward_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\shared_mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\condition_variable D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winioctl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ncrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mciapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\lzexpand.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3d12sdklayers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h D:\RTXPT\External\Donut\include\donut\app\imgui_renderer.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\RTXPT\External\Donut\include\donut\app\DeviceManager.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ole2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winspool.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\DXGI.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\prsht.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\charconv D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\predefined C++ types (compiler internal) C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3dcommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\playsoundapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\External\Donut\include\donut\core\math\affine.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h    �       Li}  嘶      匣     
 栗  �   孽  �  
 ~ �   � �  
 6�     :�    
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?    H塡$H塼$H墊$ AVH冹 H�23跮嬺H孂嬅H咑t?岾�    H塂$0H吚t$W繦�
     茾   茾   H�H塸�H嬅I�H嬣H�H媉H塆H呟t,����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH媆$8H嬊H媩$HH媡$@H兡 A^�+   &   ?         �   �  � G            �      �   64        �std::shared_ptr<donut::render::MipMapGenPass>::operator=<donut::render::MipMapGenPass,std::default_delete<donut::render::MipMapGenPass>,0> 
 >   this  AJ           AM        �  >,�   _Right  AK          AV       �  M        4  .t M        "4  t, M        �  v M        b  ��	
 N N N N M         4  f M        +4  f M        e  i N M        G4  f N N N# M        M4  
B$
 Z      >�    _Fancy  AH  "     
  AL       
  AH f     D  %  >?�    _Rx  AH  `       AH f     D  %  B0   4     �  M        j4  ` M        {4  ` N N M        i4  < M        z4  W N M        �  	F N N N                       @ N h   b  �  �  e  �3  4   4  "4  +4  G4  M4  i4  j4  k4  l4  s4  z4  {4   0     Othis  8   ,�  O_Right  ^*      =�   9�       �   9�       �   O  �   @           �   �     4       � �   � �   � �    � ��   � �,   c   0   c  
 �   c   �   c  
 �   c   �   c  
   c   
  c  
   c     c  
 I  c   M  c  
 Y  c   ]  c  
 i  c   m  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
   c     c  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   &   /   )   5   =      �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :   �&  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   h   0   h  
 �   h   �   h  
 �   h   �   h  
 �   h   �   h  
   h     h  
 s  �   w  �  
 �  h   �  h  
 嬃W莉H*篱       �      �   �   : G                   
   44        �log2<unsigned int,0>  >u    _Left  A                                  H�     u   O_Left  O  �                  X            � �,   a   0   a  
 `   a   d   a  
 �   a   �   a  
 嬃W�W莉H*萬.羨fQ撩(灵       �      �   �   : G                       74        �sqrt<unsigned int,0>  >u    _Left  A                                   H�     u   O_Left  O  �                   X            � �,   d   0   d  
 `   d   d   d  
 �   d   �   d  
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  mG                       �(        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >  >
}   _First  AJ          AJ       
   >
}   _Last  AK          
 >"}   _Val  AP           >   _Backout  CJ            CJ          
   M        �(    N M        �(   N                        H " h   �(  �(  �(  �(  �(  �(  )      
}  O_First     
}  O_Last     "}  O_Val  O�   H               �&     <       � �    � �   � �   � �   � �   � �,   g   0   g  
 �  g   �  g  
 �  g   �  g  
 �  g   �  g  
 �  g   �  g  
   g     g  
 "  g   &  g  
 �  g   �  g  
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AJ                                 H�     b%  Othis  O   �   0              `     $       �  �    �  �   �  �,   A   0   A  
 �   A   �   A  
 �   A   �   A  
 H塡$H塴$ H塋$VWAVH冹 H嬹H�H呉t
H�H嬍�P怘峖H塡$HE3鯠�3L塻L塻A峃 �    H� H堾H塁L塻L塻 L塻(H荂0   H荂8   �  �?H媖A嬑A嬈H柳H凐su箑   �    H孁H婯H婥(H+罤柳H吚t.H��    H侜   rH兟'L婣鳬+菻岮鳫凐w]I嬋�    H墈H崌�   H塁 H塁(H;鴗#H�/H兦H;鴘綦H兞H灵H吷t3�H嬇驢獿塿HH嬈H媆$PH媗$XH兡 A^_^描    怘   &   �   &   �   '   2  )      �   �  O G            7     7  �'        �donut::engine::BindingCache::BindingCache 
 >#t   this  AJ          AL         D@    >))   device  AK        +  AK ,        M        �'  B� N M        (  ��5��
 >絫   this  AI  0       BH   5       M        ((  5.H����6 M        G(  }j&M/E.$'$$/ >_   _Oldsize  AH  �     �  k  AH       C       �       >
}    _Newend  AH  �       AH       >_    _Oldcapacity  AH  �     ,    AH �       >
}    _Newvec  AM  �     � Z =  AM �     ;    M        Z(  
} N M        H(  �� N M        [(  
�� M        (  
�� M        r  
��
 Z      N N N M        �(  ��#" >   _Backout  CM     �       CM    �     ;    M        �(  �� N M        �(  �� N N M        I(  .���� M        c  ��)Z
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     � & U % M        s  ��d#
]
 Z   �   >_    _Ptr_container  AP  �     o  Z  AP �       >_    _Back_shift  AJ  �     � 9 Z  AJ �     Z +   N N N M        �(  .�
 N N M        L(  y M        Q(  y N N M        (  W M        �(  W M        �(  W N N N M        M(  ; M        ](  C)# >4t    _Newhead  AH  L     7  M        `(  	C M        (  	C M        r  	C
 Z      N N N N M        �(  ; M        �(  ; N N N M        N(  5 N N N M        �   M        �  	 N N                      0@ � h6   �  r  s  v  w  x  y  �  c  �  (  9   J   �   �'  (  ((  *(  +(  F(  G(  H(  I(  J(  K(  L(  M(  N(  P(  Q(  Z(  [(  ](  `(  ~(  (  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  )         $LN163  @   #t  Othis  H   ))  Odevice  9(       E   O �   0           7        $       2  �   1  �,   2  ��   �   ^ F                                �`donut::engine::BindingCache::BindingCache'::`1'::dtor$0 
 >#t   this  EN  @                                  �  O   �   �   ^ F                                �`donut::engine::BindingCache::BindingCache'::`1'::dtor$4 
 >#t   this  EN  @                                  �  O   �   �   ^ F                                �`donut::engine::BindingCache::BindingCache'::`1'::dtor$5 
 >#t   this  EN  @                                  �  O   ,   J   0   J  
 t   J   x   J  
 �   J   �   J  
 �   J   �   J  
 �   J   �   J  
   J     J  
 "  J   &  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
    J   $  J  
 4  J   8  J  
 V  J   Z  J  
 j  J   n  J  
 @  J   D  J  
 T  J   X  J  
 �  J   �  J  
   J     J  
 y  J   }  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 �  �   �  �  
 �  J   �  J  
   J     J  
 \  p   `  p  
 �  p   �  p  
 	  �   	  �  
 g	  �   k	  �  
 �	  �   �	  �  
 
  �   
  �  
 H媻@   �       F   H媻H   H兞�       G   H媻H   H兞�       H   H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >d#   this  AJ                                 H     d#  Othis  O   �                  x             �,   @   0   @  
 p   @   t   @  
 �   @   �   @  
 H塡$L塋$ L塂$H塋$UVWAVAWH冹 M嬹I嬸H孃H嬞H�H呉t
H�H嬍�P�3鞨塳H塳H婩H吚t�@H�H塁H婩H塁H塳H塳 L媩$pI婫H吚t�@I�H塁I婫H塁 H塳(H塳0H塳8H塳@I婩H吚t�@I�H塁8I婩H塁@H岾HH嬜�    H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H壂�   H媬����H�t*嬈�罣凐uH�H嬒�嬈�罣凐u
H�H嬒�P怚媬H�t*嬈�罣凐uH�H嬒�嬈�罣凐u
H�H嬒�P怚�H�t'嬈�罣凐uH�H嬒��羨凗u	H�H嬒�PH嬅H媆$XH兡 A_A^_^]美   J      �   s  b G            �     �  3        �EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker 
 >2�   this  AI  +     � AJ        +  DP    >))   device  AK        (  AM  (     �  >   textureCache  AL  %      AP        %  D`    >mH   shaderFactory  AQ        "  AV  "     � Dh    >鱵   commonPasses  AW  p     X EO  (           Dp    M        (  0亰 M        &(  亰'	 M        �  亾,
 >�   this  AM  �    =  M        b  仹	
 N N N N M        �  2乄 M        �  乄)	 M        �  乣,
 >�   this  AM  [    3  M        b  乼	 N N N N M        �1  7� M        �1  �) M        �  �-,
 >�   this  AM  #    8  M        b  丄	 N N N N M        �3  � M        �3  �� N N M        �3  � M        �3  �� N N M        �  �� N M        �  �� N M        �  �� N M        �  �� N M        �  �� N M        �  �� N M        �'  �� N M        �  �� N M        �  �� N M        �  �� M        
  ��M M        �  ��	 M        �  �� N N N M        m  ��� N N M        +  �� M        +  ��� N N M        %(  c! M        }(  k M        �(  k M        �  y N N N M        �'  �c N N M        �1  =
 M        F2  GM M        k2  G	 M        �  P N N N M        �1  �= N N M        �  + M        �  .	 N N
 Z   �'               (         @ � h#   b  �  �  �  �  �  �  �  �  �  �  m  �  �  
  �  �  �'  �'  (  %(  &(  }(  �(  +  +  �1  �1  �1  �1  F2  k2  �3  �3  �3   P   2�  Othis  X   ))  Odevice  `     OtextureCache  h   mH  OshaderFactory  p   鱵  OcommonPasses  99       E   9?      �   9S      �   9r      �   9�      �   9�      �   9�      �   O �   `           �  �
  	   T       #  �+     �=     �c      ��   #  ��   "  ��   !  ��   #  �  $  ��   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$0 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$1 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$2 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$3 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$4 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$5 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$6 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  �   :  q F                                �`EnvMapImportanceSamplingBaker::EnvMapImportanceSamplingBaker'::`1'::dtor$7 
 >2�   this  EN  P           >   textureCache  EN  `           >mH   shaderFactory  EN  h           >鱵   commonPasses  EN  p                                  �  O  ,   K   0   K  
 �   K   �   K  
 �   K   �   K  
 �   K   �   K  
 �   K   �   K  
 �   K   �   K  
   K     K  
 7  K   ;  K  
 G  K   K  K  
 v  K   z  K  
 �  K   �  K  
 �  K     K  
 �  K   �  K  
 '  K   +  K  
   K     K  
   K   #  K  
 /  K   3  K  
 ?  K   C  K  
 O  K   S  K  
 _  K   c  K  
 o  K   s  K  
 �  K   �  K  
 	  q   	  q  
 ~	  q   �	  q  
 �	  q   �	  q  
 �	  q   �	  q  
  
  q   
  q  
 T
  y   X
  y  
 �
  y   �
  y  
 �
  y   �
  y  
   y     y  
 D  y   H  y  
 �  |   �  |  
   |   
  |  
 1  |   5  |  
 ]  |   a  |  
 �  |   �  |  
 �     �    
 J
     N
    
 u
     y
    
 �
     �
    
 �
     �
    
    �   $  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 d  �   h  �  
 �  �   �  �  
 �  �     �  
 )  �   -  �  
 T  �   X  �  
 �  �   �  �  
   �     �  
 A  �   E  �  
 m  �   q  �  
 �  �   �  �  
 �  �   �  �  
 Z  �   ^  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H媻p   �       X   H媻h   �       Z   H媻`   �       W   H媻P   �       F   H媻P   H兞�       W   H媻P   H兞�       X   H媻P   H兞(�       Y   H媻P   H兞8�       Z   @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   +   ,   �      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   3   0   3  
 d   3   h   3  
 t   3   x   3  
 �   3   �   3  
 �   3   �   3  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   +   ,   �      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,   9   0   9  
 z   9   ~   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   7   0   7  
 z   7   ~   7  
   7     7  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   +      �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   -   0   -  
 d   -   h   -  
 t   -   x   -  
 �   -   �   -  
 �   -   �   -  
   -     -  
 H冹(H�H�H��怷  怘兡(�   �   �  )F                     �3        �GenericScope<`EnvMapImportanceSamplingBaker::Update'::`2'::<lambda_1>,`EnvMapImportanceSamplingBaker::Update'::`2'::<lambda_2> >::~GenericScope<`EnvMapImportanceSamplingBaker::Update'::`2'::<lambda_1>,`EnvMapImportanceSamplingBaker::Update'::`2'::<lambda_2> > 
 >旧   this  AJ        
  M        �3   N (                     0H�  h   �3  4   0   旧  Othis  9
       *)   O  �                  h            !  �,   f   0   f  
 N  f   R  f  
 �  f   �  f  
 �  f   �  f  
 H冹(H�H�H��怷  怘兡(�   �   �  eF                     �3        �GenericScope<`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`3'::<lambda_1>,`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`3'::<lambda_2> >::~GenericScope<`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`3'::<lambda_1>,`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`3'::<lambda_2> > 
 >吷   this  AJ        
  M        �3   N (                     0H�  h   �3  4   0   吷  Othis  9
       *)   O  �                  h            !  �,   e   0   e  
 �  e   �  e  
 �  e   �  e  
   e     e  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AH         AJ          AH        M        �  GCE
 >t$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   b%  Othis  9       E   O�   0           "   `     $       �  �   �  �   �  �,   B   0   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
   B     B  
 P  B   T  B  
 h  B   l  B  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   P  p G            "         �1        �nvrhi::RefCountPtr<nvrhi::ICommandList>::~RefCountPtr<nvrhi::ICommandList> 
 >襜   this  AH         AJ          AH        M        �1  GCE
 > )    temp  AJ  
       AJ        N (                     0H� 
 h   �1   0   襜  Othis  9       E   O�   0           "   `     $       �  �   �  �   �  �,   ]   0   ]  
 �   ]   �   ]  
 �   ]   �   ]  
 �   ]   �   ]  
 �   ]   �   ]  
 �   ]     ]  
 L  ]   P  ]  
 d  ]   h  ]  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >   this  AH         AJ          AH        M        �  GCE
 >))    temp  AJ  
       AJ        N (                     0H� 
 h   �   0     Othis  9       E   O  �   0           "   `     $       �  �   �  �   �  �,   F   0   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 �   F   �   F  
 B  F   F  F  
 \  F   `  F  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >JH   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   JH  Othis  9       E   O�   0           "   `     $       �  �   �  �   �  �,   [   0   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 D  [   H  [  
 \  [   `  [  
 @SH冹 H嬞H婭H吷t?H婼(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁H塁 H塁(H岾H兡 [�    �    �?   '   [   G   `   )      �   �  TG            e      e   �'        �std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::~_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > 
 >Gt   this  AI  	     \ Q   AJ        	  M        (  H	V" M        '(  )I1& M        I(  *F M        c  )!
 Z   �  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        s  
&#
$
 Z   �   >_    _Ptr_container  AP  *     :  !  AP >       >_    _Back_shift  AJ  
     W 1 !  AJ >         N N N M        H(   N N N                       @� * h	   �  s  c  (  '(  F(  H(  I(  ~(         $LN35  0   Gt  Othis  O ,   I   0   I  
 y  I   }  I  
 �  I   �  I  
 ,  I   0  I  
 M  I   Q  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I      I  
 �  �   �  �  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   '   V   )      �   �  �G            [      [   (        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 
 >
u   this  AI  	     R K   AJ        	 " M        '(  )H1%
 M        I(  *= M        c  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        s  
%#

 Z   �   >_    _Ptr_container  AP  )     1    AP =       >_    _Back_shift  AJ       N 1   AJ =       
  N N N M        H(   N N                       H� & h   �  s  c  '(  F(  H(  I(  ~(         $LN32  0   
u  Othis  O   �   8           [   �     ,       > �	   ? �O   D �U   ? �,   H   0   H  
 �  H   �  H  
 �  H   �  H  
 R  H   V  H  
 s  H   w  H  
 �  H   �  H  
 �  H   �  H  
   H     H  
 "  H   &  H  
 �  �   �  �  
 �  H   �  H  
 �     �   d  � G                       n4        �std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> >::~_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> > 
 >?�   this  AJ          D                           H� 
 h   a      ?�  Othis  O�                  �            � �,   i   0   i  
   i   #  i  
 x  i   |  i  
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯H吷tH塳H��P惡    H嬎�    H嬤H�u院    H�H媆$0H媗$8H媡$@H兡 _�    P   '   y   '      �     �G            }      d   (        �std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >::~list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 
 > u   this  AJ          AL       \   M        )(   M        �(  
\ M        �(  \ M        c  \ N N N' M        �(  I*
 >4t   _Head  AI         >4t    _Pnode  AI  &     C  >4t    _Pnext  AM  3     )  AM 0     H  )  M        �(  3
 M        �(  

G M        �(  
G M        c  
G
 Z   �   N N N M        )  3 M        !)  3 M        �  3 M        �  3DE
 >�%    temp  AJ  7       AJ G       N N N N N N N                      0@� R h   �  s  t  �  �  c  )(  K(  �(  �(  �(  �(  �(  �(  )  )  !)  ")  *)   0    u  Othis  9C       E   O �   8           }   `     ,        �    �d    �x    �,   G   0   G  
 �  G   �  G  
 �  G   �  G  
 |  G   �  G  
 �  G   �  G  
 �  G   �  G  
 �  G   �  G  
 �  G   �  G  
 �  G   �  G  
 {  G     G  
 �  G   �  G  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   (        �std::shared_ptr<donut::engine::CommonRenderPasses>::~shared_ptr<donut::engine::CommonRenderPasses> 
 >z   this  AJ        +  AJ @       M        &(  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  &(   0   z  Othis  9+       �   9=       �   O  �   0           K   �     $       � �   � �E   � �,   X   0   X  
 �   X   �   X  
 �   X   �   X  
   X     X  
 �  X   �  X  
 �  X   �  X  
 �  X   �  X  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   �*        �std::shared_ptr<donut::engine::FramebufferFactory>::~shared_ptr<donut::engine::FramebufferFactory> 
 >s�   this  AJ        +  AJ @       M        �*  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �*   0   s�  Othis  9+       �   9=       �   O  �   0           K   �     $       � �   � �E   � �,   Y   0   Y  
 �   Y   �   Y  
 �   Y   �   Y  
   Y     Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   �        �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >nG   this  AJ        +  AJ @       M        �  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �   0   nG  Othis  9+       �   9=       �   O�   0           K   �     $       � �   � �E   � �,   Z   0   Z  
 �   Z   �   Z  
 �   Z   �   Z  
   Z   
  Z  
 |  Z   �  Z  
 �  Z   �  Z  
 �  Z   �  Z  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  | G            K      E   �1        �std::shared_ptr<donut::engine::TextureCache>::~shared_ptr<donut::engine::TextureCache> 
 >刊   this  AJ        +  AJ @       M        �1  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �1   0   刊  Othis  9+       �   9=       �   O  �   0           K   �     $       � �   � �E   � �,   W   0   W  
 �   W   �   W  
 �   W   �   W  
   W     W  
 z  W   ~  W  
 �  W   �  W  
 �  W   �  W  
 @SH冹 H�H呟tH嬎�    酣   H嬎H兡 [�    H兡 [�   `   $   '      �   R  � G            .      (    4        �std::unique_ptr<donut::render::MipMapGenPass,std::default_delete<donut::render::MipMapGenPass> >::~unique_ptr<donut::render::MipMapGenPass,std::default_delete<donut::render::MipMapGenPass> > 
 >4�   this  AJ        .                          H�  h   4  4  '4   0   4�  Othis  O  �   0           .   �     $       � �   � �   � �,   b   0   b  
 	  b   
  b  
 h  b   l  b  
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   '   [   )      �   �  D G            `      `   �'        �nvrhi::BufferDesc::~BufferDesc 
 >�   this  AI  
     S L   AJ        
  M        J  GM) M        %  -(

 M        �   N M        �  -G M        3  &@ M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        s  
##
"
 Z   �   >_    _Ptr_container  AP  '     8    AP ;       >_    _Back_shift  AJ  .     1 
   N N N N N N                       H� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN39  0   �  Othis  O ,   ?   0   ?  
 i   ?   m   ?  
 }   ?   �   ?  
 ]  ?   a  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
   ?     ?  
 �  �   �  �  
 @SH冹 H嬞H兞L�
    �   D岯    怘�H吷tH�    H��P怘兡 [�   B      %      �   M  V G            ?      9   �        �nvrhi::ComputePipelineDesc::~ComputePipelineDesc 
 >�%   this  AI  	     5  AJ        	  M        �  # M        �  #CE
 >k$    temp  AJ  &       AJ 9       N N                      0H�  h        �  �   0   �%  Othis  95       E   O   ,   C   0   C  
 {   C      C  
 �   C   �   C  
 �   C   �   C  
 �   C   �   C  
 I  C   M  C  
 H塡$H塴$H塼$WH冹 H嬞H嫻�   ����H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH嫽�   H�t*嬈�罣凐uH�H嬒�嬈�罣凐u
H�H嬒�P怘媼�   3鞨吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘媼�   H吷tH壂�   H��P怘岾P�    怘婯HH吷tH塳HH��P怘媨@H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH媨0H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH媨 H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH媨H�t(嬈�罣凐uH�H嬒��羨凗u
H�H嬒�P怘�H吷t
H�+H��P怘媆$0H媗$8H媡$@H兡 _脁  I      �   �
  c G                 j  3        �EnvMapImportanceSamplingBaker::~EnvMapImportanceSamplingBaker 
 >2�   this  AI       X AJ          M        �  俋 M        �  俋CE
 >))    temp  AJ  [      AJ j      N N M        �1  0�' M        �1  �''	 M        �  �0,
 >�   this  AM  +    S  M        b  侱	
 N N N N M        (  2侕 M        &(  侕)	 M        �  侢,
 >�   this  AM  �    2  M        b  �	 N N N N M        �*  2伱 M        �*  伱)	 M        �  佁,
 >�   this  AM  �    2  M        b  佮	 N N N N M        �  2亼 M        �  亼)	 M        �  仛,
 >�   this  AM  �    2  M        b  伄	 N N N N M        �  亇 M        �  亇DE
 >))    temp  AJ  �      AJ �    �   M    �   N N M        �  乊 M        �  乊GE
 >n#    temp  AJ  `      AJ s      N N M        �  �? M        �  �?GE
 >n#    temp  AJ  F      AJ Y      N N M        �'  �% M        (  �%GE
 >�     temp  AJ  ,      AJ ?      N N M        �  � M        �  �GE
 >�    temp  AJ        AJ %      N N M        �  �� M        �  ��GE
 >�    temp  AJ  �       AJ       N N M        �  �� M        �  ��GE
 >k$    temp  AJ  �       AJ �       N N M        �  �� M        �  ��GE
 >t$    temp  AJ  �       AJ �       N N M        �  �� M        �  ��GE
 >�&    temp  AJ  �       AJ �       N N M        �  �� M        �  ��GG
 >�%    temp  AJ  �       AJ �       N N M        4  5Q M        "4  Q) M        �  ],
 >�   this  AM  X     = M        b  q	 N N N N M        4  : M        "4  ) M        �  (,
 >�   this  AM       :  M        b  <	 N N N N                      @� ~ h   b  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �'  �'  �'  (  (  &(  �*  �*  �1  �1  4  "4   0   2�  Othis  9:       �   9N       �   9o       �   9�       �   9�       E   9�       E   9�       E   9�       E   9      E   9!      E   9;      E   9U      E   9o      E   9�      E   9�      �   9�      �   9�      �   9�      �   9      �   9$      �   9B      �   9T      �   9f      E   O�   (             �
            '  �   (  �,   L   0   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 �   L   �   L  
 n  L   r  L  
   L     L  
 �  L   �  L  
 -  L   1  L  
 �  L   �  L  
 �  L   �  L  
 %  L   )  L  
 5  L   9  L  
 �  L   �  L  
 �  L   �  L  
 �  L   �  L  
   L     L  
 f  L   j  L  
 v  L   z  L  
 �  L   �  L  
 �  L   �  L  
 <  L   @  L  
 L  L   P  L  
 �  L   �  L  
 �  L   �  L  
   L     L  
 "  L   &  L  
 }  L   �  L  
 �  L   �  L  
 �  L     L  
 �  L   �  L  
 t	  L   x	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 �	  L   �	  L  
 
  L   
  L  
 
  L   
  L  
 $
  L   (
  L  
 4
  L   8
  L  
 D
  L   H
  L  
 T
  L   X
  L  
 d
  L   h
  L  
 t
  L   x
  L  
 �
  L   �
  L  
 �
  L   �
  L  
 �
  L   �
  L  
 �
  L   �
  L  
 �
  L   �
  L  
 �
  L   �
  L  
 �
  L   �
  L  
 H塡$H塴$H塼$WH冹 H嬞H兞`�    怘婯X3鞨吷tH塳XH��P怘媨PH�t-����嬈�罣凐uH�H嬒��羨凗u
H�H嬒�P怘婯@H吷tH塳@H��P怘媨(H�tqH媠0H;H�H吷t
H�/H��P怘兦H;錒婯(H婥8H+罤柳H��    H侜   rH兟'L婣鳬+菻岮鳫凐噵   I嬋�    H塳(H塳0H塳8H婯 H吷tH塳 H��P怘婯H吷tH塳H��P怘婯H吷tH塳H��P怘婯H吷tH塳H��P怘�H吷t
H�+H��P怘媆$0H媗$8H媡$@H兡 _描    �   I   �   '   s  )      �   l  R G            x     x  (4        �donut::render::MipMapGenPass::~MipMapGenPass 
 >�   this  AI       aK  AJ          M        �  並 M        �  並CE
 >))    temp  AJ  N      AJ ]      N N M        �  �7 M        �  �7DE
 >k$    temp  AJ  ;      AJ K      N N M        �  �# M        �  �#DE
 >�    temp  AJ  '      AJ 7      N N M        �'  � M        (  �DE
 >�     temp  AJ        AJ #      N N M        �  �� M        �  ��DE
 >t$    temp  AJ  �       AJ       N N M        *4  #��V$��, M        -4  ��	IA$	{ M        /4  2���� >_   _Count  AH  �     '    AH �        M        c  ��)��
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     � * � & M        s  ��d#��
 Z   �   >_    _Ptr_container  AP  �     �  �  AP �       >_    _Back_shift  AJ  �     � 8 �  AJ �       N N N M        H4  ��	 >騁   _First  AM  �     � �   >鱃   _Last  AL  �     � m w  AL �     q  M        b4  �� M        w4  �� M        �  �� M        �  ��CE
 >�%    temp  AJ  �       AJ �         N N N N N N N M        �  m M        �  mDE
 >�&    temp  AJ  q       AJ �     ~   1 I  N N M        )4  57 M        ,4  7,	 M        �  @
 >�   this  AM  ;     J  M        b  Y	
 N N N N M        �  ! M        �  !DG
 >))    temp  AJ  %       AJ 7     :     N N                      0@� � h!   �  b  s  t  �  �  �  �  �  �  �  �  �  �  �  �  �  c  �'  �'  �'  (  )4  *4  ,4  -4  .4  /4  H4  `4  b4  w4  |4         $LN112  0   �  Othis  93       E   9W       �   9i       �   9}       E   9�       E   9      E   9      E   93      E   9G      E   9Y      E   O,   `   0   `  
 w   `   {   `  
 �   `   �   `  
 �   `   �   `  
 �   `   �   `  
 I  `   M  `  
 Y  `   ]  `  
 �  `   �  `  
 �  `   �  `  
   `   #  `  
 /  `   3  `  
 �  `   �  `  
 �  `   �  `  
 (  `   ,  `  
 <  `   @  `  
 �  `   �  `  
 �  `   �  `  
   `     `  
   `   #  `  
 E  `   I  `  
 Y  `   ]  `  
 �  `   �  `  
 �  `   �  `  
 �  `   �  `  
 \  `   `  `  
 l  `   p  `  
 �  `   �  `  
 �  `   �  `  
 g  `   k  `  
 �  `   �  `  
 �  `   �  `  
 �  �   �  �  
 �  `   �  `  
 �  `   �  `  
 �  `   �  `  
   `     `  
   `     `  
 (  `   ,  `  
 8  `   <  `  
 H  `   L  `  
 X  `   \  `  
 h  `   l  `  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂0    H荂8   艭  H兡 [描    �<   '   [   )      �   �  F G            `      `   *        �nvrhi::TextureDesc::~TextureDesc 
 >y   this  AI  
     S L   AJ        
  M        J  GM) M        %  -(

 M        �   N M        �  -G M        3  &@ M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        s  
##
"
 Z   �   >_    _Ptr_container  AP  '     8    AP ;       >_    _Back_shift  AJ  .     1 
   N N N N N N                       H� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN39  0   y  Othis  O   ,   >   0   >  
 k   >   o   >  
    >   �   >  
 _  >   c  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
   >     >  
 �  �   �  �  
 �     �   �   L G                       a        �std::_Ref_count_base::~_Ref_count_base 
 >�   this  AJ          D                           H�     �  Othis  O  �                  �            ~ �,   E   0   E  
 q   E   u   E  
 �   E   �   E  
 H�    H�H兞�       �      ,      �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   8   0   8  
 {   8      8  
 H�    H�H兞�       �      ,      �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   .   0   .  
 e   .   i   .  
 �   .   �   .  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   '      �   !  � G            !         m4        �std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> >::`scalar deleting destructor' 
 >?�   this  AI  	       AJ        	                        @� 
 h   n4   0   ?�  Othis  O   ,   m   0   m  
 �   m   �   m  
 �   m   �   m  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      ,   0   '      �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   4   0   4  
 w   4   {   4  
 �   4   �   4  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      ,   0   '      �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,   :   0   :  
 �   :   �   :  
 �   :   �   :  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      ,   0   '      �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,   0   0   0  
 w   0   {   0  
 �   0   �   0  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   #   0   #  
 g   #   k   #  
 w   #   {   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
 �   #   �   #  
   #     #  
 !  #   %  #  
 1  #   5  #  
 A  #   E  #  
 �  #   �  #  
 H塡$H塼$H墊$ UAVAWH峫$餒侅  H�    H3腍塃 H孂E3鲵    �    騂,�狼E�   荅�   H荅�   f荅�!W�E癓塽繦荅�   D坲癴荅�fD塽覦塽豧D塽�)E郉坲餌塽鬌坲E�   荅�   塃犉E�A峃 �    H荅�   H荅�        D坧H塃扒E鬇   艵�H�H�L岴怘峊$H�P(A嬛H峀$xH;萾H�L�0H嫃�   H墬�   H吷tH��P怘婰$HH吷tL塼$HH��P惼E�$L媫菼�r3H峕癐�HG]癏荅�   A�   H�    H嬎�    艭 轵   I嬒H验H�������H嬈H+罫;鴙-H�       �H兝'H嬋�    H吚勎  H峏'H冦郒塁>I��   H;艸G餒峃H侚   rH岮'H;�啇  氲H吷t
�    H嬝�I嬣H荅�   H塽闰    ��   塁�   f塁艭 I�v2I峎H婱癏嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    H塢癏�H�L岴怘峊$0�P(I嬛H峂�H;萾H�L�0H嫃�   H墬�   H吷tH��P怘婰$0H吷tL塼$0H��P惞�   �    H嬝H塂$@H吚t\H嫃�   H塋$PH吷tH��P�W荔D$hH婫@H吚t�@H婫8H塂$hH婫@H塂$p艱$  L峀$PL岲$hH�H嬎�    �I嬈H塂$8H崗�   H峊$8�    怘媆$8H呟tH嬎�    酣   H嬎�    龚   �    H嬝H塃圚吚t]H嫃�   H塋$@H吷tH��P�W荔D$XH婫@H吚t�@H婫8H塂$XH婫@H塂$`艱$  L峀$@L岲$XH�H嬎�    L嬸L塼$0H崗�   H峊$0�    怘媆$0H呟tH嬎�    酣   H嬎�    怘婾菻凓v-H�翲婱癏嬃H侜   rH兟'H婭鳫+罤兝鳫凐w.�    H婱 H3惕    L崪$  I媅(I媠0I媨8I嬨A_A^]描    愯    愯    �#   T   8   S   =   �   �   &   �   �   k  �   s  �   �  &   �  &     �      �   *  �   j  '   �  &   3  ^   N  c   a  `   n  '   x  &   �  ^   �  c   
  `     '   O  '   [  �   }  )   �  =   �  )      �   �  X G            �  .   �  �3        �EnvMapImportanceSamplingBaker::CreateImportanceMap 
 >2�   this  AJ        1  AM  1     ]B	  >�   texDesc  CK  8        	  CK 8   S    )  D�    M        J  7�` M        %  �-
V M        �  � N M        �  -�&V M        3  *�)S M        c  �0).
 Z   �  
 >   _Ptr  AH  0      AJ  -      AH N      >#    _Bytes  AK  )    X * )  M        s  �9d
8
 Z   �   >_    _Ptr_container  AH  D      AJ  A      N N N N N N M         4  
凕 N M        54  -價G1 Z     �4   M        ]4  冨
 >钊  	 _Ptr  AV  �    � �   Cn      4     �  Cn         k  M        v4  冨 N N M        �  儯 M        
  儵M		 M        �  儵	 M        �  儾 N N N M        m  �儯 N N M        �'  儓 M        �  償 N N N M         4  
僑 N M        54  .偲I2 Z     �4   M        ]4  �<
 >钊   _Ptr  AH  7        M        v4  �< N N M        �  傴 M        
  傼M		 M        �  傼	 M        �  � N N N M        m  �傴 N N M        �'  傒 M        �  傞 N N N M        �  偘 M        �  偘HB
 >�    temp  AJ  �      AJ �      B0   �    + B�  �    � N N M        
*  +倓 M        �  偆 M        �  偆
 >�    temp  AJ  �      AJ �      B�      w.   N N M        �  倴 >�    tmp  AK  �    (  AK �         N M        *  倓C	 M        �  倫 N N N  M        (  �瘉Cz��値  M        /(  �瘉Cz��値. M        )  丆J-(Drzt値@ M        E4  亐&#($$2侳 >#    _New_capacity  AH  �      AJ  �    �"  * | AL  �    L  # A   AH �      AJ �    �  Z � *  C       �      >_    _Old_capacity  AJ  �    V #   AW  G    ?  AJ �      AW r      M        %4  �  M        ?    � N N$ M        �  仼	/値 >p    _Fancy_ptr  AI        AI     �� � M        �  仼3値 M        �  仼3値. M        (  仼3
	
%
�+ M        9   仼()#	倷
 Z   q   >_    _Block_size  AH  �    � � AH �      >_    _Ptr_container  AH  �      AH     � d
 >�    _Ptr  AI  �      AI     �� � M        r  仼
 Z      N N M        r  侚
 Z      N N N N N M        �  #亞B M          亞- N N M        �  2�<侺 M        3  .侤侶  M        c  侴)�
 Z   �  
 >   _Ptr  AH  G      AJ  D      AH i      >#    _Bytes  AK  @    .  AK �      M        s  侾d�-
 Z   �   >_    _Ptr_container  AH  [      AJ  X      N N N N N M        �  L乥 N M        0  丮	
 >嘚   this  AI  Q    	  >p    _Result  AI  Z    &  AI r    a  N N N N M        �  �) M        �  �)HB
 >�    temp  AJ  .      AJ ?    D 3   BH   8    V B  �     � N N M        
*  ,�� M        �  � M        �  �
 >�    temp  AJ        AJ )      B�      w.   N N M        �  � >�   tmp  AK       & AK )    ^  F  �  �  . X C       �     
 . C          x  /  `  �  �  1. r N M        *  ��C
 M        �  �	 N N N M        �3  �� N M        (  +�� M        /(  +�� M        )  +��  M        E4  ��	((
 M        %4  ��
 M        ?   
�� N N M        �  	�� >p    _Fancy_ptr  AH  �     3  M        �  	�� M        �  	�� M        (  	�� M        r  	��
 Z      N N N N N N N N N M        *  �� N M        R  g M        &  k$ N M        A  g M        �  g M          g N N N N M        �3  4
 >u     mips  A   H     u  M        44  
4 N N Z   64  64                      A .hJ   �  �  r  s  t  v  �  �  �  �  �  J  K  R  S  m  �  �  $  %  &  )  0  3  >  ?  �  �  �  �  �  �  �  
  <  A  ^  c  �  �  �  �  �  �      �  �  '  (  /   9   �'  (  /(  *  *  *  
*  *  �1  �3  �3  �3   4  4  4  %4  '4  44  54  E4  ]4  v4  
 :   O        $LN527  0  2�  Othis  �   �  OtexDesc  9�       �(   9%      E   9;      E   9�      �(   9�      E   9�      E   ^�     馊   9�      E   ^w     馊   9�      E   O  �   �           �  �
     �       �  �4   �  �^   �  �g   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  �?  �  �C  �  �n  �  ��  �  �r  �  �  �  ��  �  ��   �   g F                                �`EnvMapImportanceSamplingBaker::CreateImportanceMap'::`1'::dtor$0  >�    texDesc  EN  �                                  �  O   �   �   h F                               �`EnvMapImportanceSamplingBaker::CreateImportanceMap'::`1'::dtor$14  >�    texDesc  EN  �                                 �  O  �   �   g F                                �`EnvMapImportanceSamplingBaker::CreateImportanceMap'::`1'::dtor$3  >�    texDesc  EN  �                                  �  O   �   �   h F                                �`EnvMapImportanceSamplingBaker::CreateImportanceMap'::`1'::dtor$19  >�    texDesc  EN  �                                 �  O  �   �   g F                                �`EnvMapImportanceSamplingBaker::CreateImportanceMap'::`1'::dtor$4  >�    texDesc  EN  �                                  �  O   ,   T   0   T  
 }   T   �   T  
 �   T   �   T  
 �   T   �   T  
 �   T   �   T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 :  T   >  T  
 J  T   N  T  
 �  T   �  T  
 �  T   �  T  
   T     T  
 i  T   m  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 H  T   L  T  
 X  T   \  T  
 h  T   l  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
   T     T  
 $  T   (  T  
 4  T   8  T  
 P  T   T  T  
 x  T   |  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 3	  T   7	  T  
 C	  T   G	  T  
 
  T   
  T  
 *
  T   .
  T  
 S
  T   W
  T  
 c
  T   g
  T  
 �
  T   �
  T  
 �
  T   �
  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T      T  
   T     T  
 a  T   e  T  
 q  T   u  T  
 �  T   �  T  
 �  T   �  T  
 

  T   
  T  
 m
  T   q
  T  
 }
  T   �
  T  
 �
  T   �
  T  
 �
  T   �
  T  
   T     T  
   T   "  T  
 .  T   2  T  
 r  T   v  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
   T     T  
 f  T   j  T  
 �  �   �  �  
 2  T   6  T  
 B  T   F  T  
 R  T   V  T  
 b  T   f  T  
 r  T   v  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  r   �  r  
   r     r  
 p  w   t  w  
 �  w   �  w  
 ,  �   0  �  
 �  �   �  �  
 �  x   �  x  
 P  x   T  x  
 �  �   �  �  
   �     �  
 H崐�   �       >   H崐8   �       b   H崐0   �       b   @UH冹 H嬯酣   H婱@�    H兡 ]�   '   @UH冹 H嬯酣   H媿�   �    H兡 ]�   '   H塡$H塼$UWAVH崿$@��H侅�	  H�    H3腍墔�  H嬞H荄$H  �?f荄$U W�D$8荄$P艱$TH�	H�L岲$8H峊$`�惾   3鰦諬峂�H;萾H�H�0H媼�   H墦�   H吷tH��P怘婰$`H吷tH塼$`H��P恌荄$Q  艱$P f荄$T  艱$S H�H�L岲$8H峊$h�惾   H嬛H峂圚;萾H�H�0H媼�   H墦�   H吷tH��P怘婰$hH吷tH塼$hH��P怘塽�W�E繦塽蠬荅�   艵� H荅�    荅�    艵� 荅�   f荅�  塽鳫荅�0   �0   �    H荅�&   H荅�/        
   H�
    塇 �
$   f塇$艪& H塃榔E�艵�荅�   H�H�L岴癏峊$p�悁   H嬛H峂怘;萾H�H�0H媼�   H墦�   H吷tH��P怘婰$pH吷tH塼$pH��P怘婾豀凓v1H�翲婱繦嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    �    f墊$(H塼$ L�
    L�    H峊$xH婯8�    H嬛H峂楬;萾H�H�0H媼�   H墦�   H吷tH��P怘婰$xH吷tH塼$xH��P悏ut艵x 3褹�   H崓�   �    壍�  菂�  �   菂�     菂�  �  f墋pH塼$0艱$4
H婦$0H塃8H塼$0艱$4H婦$0H塃@H塼$0艱$4H婦$0H塃HH塼$0荄$0   艱$4H婦$0H塃PH塼$0艱$4H婦$0H塃XH塼$0荄$0   艱$4H婦$0H塃`H嬑H墠�  H峌8H�H墑蜖  H媿�  H�罤墠�  H兟H岴hH;衭譎崓�   H崊�  �     HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�H�H�L岴pH峊$0�怭  H嬛H峂燞;萾H�H�0H媼�   H墦�   H吷tH��P怘婰$0H吷tH塼$0H��P怘塽 W�3�EEH塃(L�5    L塼$ L�
    峆D岪H峂�    怘塽0H嫽�   H婱 H;蟭"H�t
H�H嬒�PH婱 H墋 H吷tH��P怘嫽�   H�t
H�H嬒�P怘婨0H婰�H墊�H吷tH��P怘�E0H�H�L岴 H峊$X��8  H嬛H峂℉;萾H�H�0H媼�   H墦�   H吷tH��P怘婰$XH吷tH塼$XH��P怘媼�   H吷tH壋�   H��P怣嬑�   D岯鼿峂�    怘婱 H吷tH塽 H��P�W��   H媼�   H吷tH壋�   H��P怘壋�   H嫽�   H壋�   H�t,����嬅�罣凐uH�H嬒��羅凔u	H�H嬒�PH媿�  H3惕    L崪$�	  I媅(I媠0I嬨A^_]描    �    T   k  &   �  �   �  �   �  �   �  �   M  '   c  �   j  �   x  \   �  �   �  B   �  A   �  $   �  %   2  �   O  )      �   f  W G            T  .   T  3        �EnvMapImportanceSamplingBaker::CreateRenderPasses 
 >2�   this  AI  1     #�N  AJ        1  AI '      >�    samplerDesc  D8    >   constBufferDesc  CK  (       	  CK (   Q    "  D�    >�"    layoutDesc  Dp   >�%    pipelineDesc  D    M        �  �	 M        �  �	HB
 >n#    temp  AJ        AJ     K  B�
  �     w Dh    N N M        �  +�� M        �  �� M        �  ��
 >n#    temp  AJ  �       AJ 	      B`
      G  N N M        �  �� >n#    tmp  AK  �     (  AK 	    f    N M        �  ��C	 M        �  �� N N N M        1  �� N M        �  �� N M        �  �� M        �  ��HB
 >n#    temp  AJ  �       AJ �       B(
  l     � D`    N N M        �  ,l M        �  �� M        �  ��
 >n#    temp  AJ  �       AJ �       B
      G  N N M        �   >n#   tmp  AK  |       AK �     >    C       p       C           X   /   N M        �  lD	 M        �  y N N N M        1  : N M        �  I N M        1  D N M        J  ;��8 M        %  �1
�. M        �  1� �. M        3  .�#�+  M        c  �*)�
 Z   �  
 >   _Ptr  AH  *      AJ  '      AH L      >#    _Bytes  AK  #    0. � M        s  �3d�
 Z   �   >_    _Ptr_container  AH  >      AJ  ;      N N N N N N M        �'  �  M        (  � HB
 >�     temp  AJ        AJ     a  *  B�  �    � Dp    N N M        �'  +佋 M        �'  侓 M        (  侓
 >�     temp  AJ  �      AJ        Bx      G  N N M        (  佹 >�     tmp  AK  �    (  AK          N M        (  佋C	 M        (  佮 N N N M        (  K乪 M        /(  K乪 M        )  K乪  M        E4  乪
((
- M        %4  �) M        ?   )� N N M        �  
乪 >p    _Fancy_ptr  AH  o    V  M        �  
乪 M        �  
乪 M        (  
乪 M        r  
乪
 Z      N N N N N N N N N M        R  �& M        &  �*$ N M        A  �& M        �  �& M          �& N N N N M        4  F呩 M        4  .咘 M        "4  咘, M        �  咞 M        b  �	
 N N N N M         4  呩 M        +4  呩 M        e  呰
 N M        G4  呩 N N N N M        
4  吳 M        �  吳GE
 >�    temp  AJ  �      AJ �    M 1   N N M        �  叐 M        �  叐GB
 >k$    temp  AJ  �      AJ �      N N M        	4  厃 M        �  厃GE
 >�%    temp  AJ  �      AJ �      N N M        �  卌 M        �  卌HB
 >�&    temp  AJ  h      AJ y      BX   r    �  B   7     N N M        �  +�7 M        �  匴 M        �  匴
 >�&    temp  AJ  P      AJ c      B       G  N N M        �  匢 >�&    tmp  AK  :    (  AK c    8   /   N M        �  �7C	 M        �  匔 N N N M        �3  匁 >t$   layout  AM  �    �  M        �  � M        �  � M        �  � M        �  �
 >t$    temp  AJ  
      AJ       B�      G  N N M        �  � N N N M        �  匁 M        �  匁#	 N N N M        �3  *劽 >k$   value  AM  �    2  M        �  劽!	 M        �  勨 M        �  勨
 >k$    temp  AJ  �    &    AJ �        BH      G  N N M        �  勣 N M        �  勌 M        k  勌#
 N N N N M        �  剢2 N M        �  � N M        �  刬 M        �  刬HB
 >t$    temp  AJ  n      AJ     3  B0   x    � B  =     N N M        �  +�= M        �  刔 M        �  刔
 >t$    temp  AJ  V      AJ i      B�
      G  N N M        �  凮 >t$    tmp  AK  @    (  AK i    A    N M        �  �=C	 M        �  処 N N N M        �  儍J$
 >�"    <begin>$L0  AK  �    <  M        �  儜 N N M        �  僪 >�"    result  B0   m    �  N M        �  
僓 >�"    result  B0   Z      N M        �  �: >�"    result  B0   ?      N M        �  
�' >�"    result  B0   ,      N M        �  
� >�"    result  B0         N M        1  
� >�"    result  B0         N M        �  偱$ N M        �  偍 M        �  偍HB
 >k$    temp  AJ  �      AJ �      BP
  |    � Dx    N N M        �  +倈 M        �  倻 M        �  倻
 >k$    temp  AJ  �      AJ �      B0
      G  N N M        �  値 >k$    tmp  AK      (  AK �        N M        �  倈C	 M        �  倛 N N N
 Z   �!   �	                   A 
h�   �  �  b  r  s  t  v  �  �  �  �  �  �  �  �  �  �  �  �         �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  J  K  R  S  n  ~    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  $  %  &  )  0  3  >  ?  k  �  �  �  �  �  �  �  �  �  �  <  A  ^  c  e  �  �  �  �      �  �  '  (  /   9   �#  �'  �'  �'  �'  (  (  (  (  /(  1  
1  1  1  �3  �3  �3  4  4  4  	4  
4   4  !4  "4  %4  +4  B4  E4  G4  
 :�	  O        $LN576  �	  2�  Othis  8   �  OsamplerDesc  �     OconstBufferDesc  p  �"  OlayoutDesc     �%  OpipelineDesc  9f       �(   9�       E   9�       E   9�       �(   9      E   9      E   9�      �(   9�      E   9      E   9�      E   9�      E   97      �(   9e      E   9{      E   9�      E   9�      E   9       E   9      E   91      �(   9_      E   9u      E   9�      E   9�      E   9�      E   9      �   9$      �   O  �              T  �
  !         +  �:   2  �D   /  �I   0  �Q   2  �V   3  ��   5  ��   6  ��   7  �&  ;  �]  <  �e  =  ��  >  ��  ?  ��  @  ��  A  �  B  �Q  F  ��  I  ��  J  �  K  �(  S  �  U  ��  V  ��  W  �"  X  �y  Z  ��  [  ��  �  ��  �  ��  �  �'  �  �N  B  ��   6  f F                                �`EnvMapImportanceSamplingBaker::CreateRenderPasses'::`1'::dtor$2  >�    samplerDesc  EN  8           >    constBufferDesc  EN  �           >�"    layoutDesc  EN  p          >�%    pipelineDesc  EN                                    �  O  �   6  f F                                �`EnvMapImportanceSamplingBaker::CreateRenderPasses'::`1'::dtor$6  >�    samplerDesc  EN  8           >    constBufferDesc  EN  �           >�"    layoutDesc  EN  p          >�%    pipelineDesc  EN                                    �  O  ,   M   0   M  
 |   M   �   M  
 �   M   �   M  
 �   M   �   M  
 �   M   �   M  
    M     M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 ,  M   0  M  
 <  M   @  M  
 L  M   P  M  
 �  M   �  M  
 �  M   �  M  
 i  M   m  M  
 y  M   }  M  
 �  M   �  M  
 �  M     M  
 
  M     M  
   M   !  M  
 \  M   `  M  
 l  M   p  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M   	  M  
 &  M   *  M  
   M   �  M  
 �  M   �  M  
 �  M   �  M  
 
  M     M  
   M   "  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M     M  
 $	  M   (	  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M     M  
 _  M   c  M  
 o  M   s  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 g
  M   k
  M  
 w
  M   {
  M  
 �
  M   �
  M  
 �
  M   �
  M  
 �
  M   �
  M  
 `  M   d  M  
 �  M   �  M  
 �  M   �  M  
    M     M  
 �  M   �  M  
   M     M  
   M     M  
 /  M   3  M  
   M   !  M  
 -  M   1  M  
 =  M   A  M  
 M  M   Q  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M     M  
 *  M   .  M  
 �  M   �  M  
   M     M  
 K  M   O  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 /  M   3  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 9  M   =  M  
 I  M   M  M  
 Y  M   ]  M  
 �  M   �  M  
 �  M   �  M  
 A  �   E  �  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M     M  
   M     M  
 "  M   &  M  
 2  M   6  M  
 B  M   F  M  
 R  M   V  M  
 b  M   f  M  
 r  M   v  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
 �  M   �  M  
   M     M  
   M     M  
 "  M   &  M  
 2  M   6  M  
 B  M   F  M  
 R  M   V  M  
 b  M   f  M  
 |  M   �  M  
 �  }   �  }  
 .  }   2  }  
 \  }   `  }  
 �  }   �  }  
 �  }   �  }  
   �     �  
 n  �   r  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H崐�   �       ?   H崐   �       C   2烂   �   �   M G                      �3        �EnvMapImportanceSamplingBaker::DebugGUI 
 >2�   this  AJ          D    >@    indent  A�           D                           @     2�  Othis     @   Oindent  O   �   0              �
     $       , �    - �   . �,   S   0   S  
 r   S   v   S  
 �   S   �   S  
    S     S  
 @SH冹 I嬝H�
H吷tH�    H��P怘�H吷tH�    H��P怘兡 [�   �   �  W G            ;      5   �3        �EnvMapImportanceSamplingBaker::ExecutePresampling 
 >2�   this  AJ          D0    >纀   commandList  AK          AK          D8    >EH   sourceCubemap  AI  	     1  AP        	  D@    >t    sampleIndex  Ai          Ai          DH    M        �   M        �  CE
 >�    temp  AJ  "       AJ 5       N N M        �1  	 M        �1  ICE
 > )    temp  AJ         AJ        N N                      @  h   �  �  �1  �1   0   2�  Othis  8   纀  OcommandList  @   EH  OsourceCubemap  H   t   OsampleIndex  9       E   91       E   O   �   (           ;   �
             �	   ( �,   R   0   R  
 |   R   �   R  
 �   R   �   R  
 �   R   �   R  
 �   R   �   R  
 �   R     R  
 ,  R   0  R  
 <  R   @  R  
 �  R   �  R  
 �  R   �  R  
    R     R  
   R     R  
 �  R   �  R  
 �  R   �  R  
 �  R   �  R  
 H塡$L塂$UVWAVAWH冹 A嬮I嬸L嬺L孂W莉
    f.羨fQ岭(凌    騂,揽   ;�G�3腋   鼢嬝H�H��R �A�H�H��R 婬A塏荄$P   荄$T   H婦$PI塅嬊拎
塂$P嬅拎
塂$TH婦$PI塅墊$P塡$TH婦$PI塅 嬅W荔H*荔
    �^润AN(I嫃�   H��P 婬�葾塏,A塶H�H吷tH�    H��P怘媆$XH兡 A_A^_^]�(   P   <   �   �   M      �   (  T G                   �3        �EnvMapImportanceSamplingBaker::FillBakerConsts 
 >2�   this  AJ        !  AW  !     �  >C�   constants  AK          AV       �  >EH   sourceCubemap  AL         AP          D`    >t    sampleIndex  A         Ai          >u     samplesX  A   J     �  >u     samplesY  A   Z     f  M        �  �� M        �  ��CE
 >�    temp  AJ  �       AJ       N N M        
  �� N M        
  ��	 N M        �*  x N M        74  $ N             (         H " h   �  
  �  �  �  �*  74   P   2�  Othis  X   C�  Oconstants  `   EH  OsourceCubemap  h   t   OsampleIndex  9`       �   9n       �   9�       �   9      E   O�   x             �
     l       �  �$   �  �O   �  �Z   �  �h   �  �x   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �  ��   �   c F                                �`EnvMapImportanceSamplingBaker::FillBakerConsts'::`1'::dtor$0  >EH   sourceCubemap  EN  `                                  �  O ,   V   0   V  
 y   V   }   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
 #  V   '  V  
 3  V   7  V  
 V  V   Z  V  
 y  V   }  V  
 �  V   �  V  
 �  V   �  V  
 �  V   �  V  
   V     V  
   V     V  
 $  V   (  V  
 <  V   @  V  
 �  s   �  s  
 E  s   I  s  
 H媻`   �       [   H嬆L堾H塒USVWATAUAVAWH峢窰侅  )p�)x楳嬸L嬧L孂E3鞮塵楬媮�   H塂$`H媮�   H塃癏荅�   E�D$hM�L$xE�E圛�H塢PH呟t
H�H嬎�PH媇PH岴PH塃hW莉
    f.羨fQ岭(凌    騂,谰   ;�G�3腋   黯孁H�H嬎�R �塋$0H婱PH��R 婬塋$4荄$@   荄$D   嬑玲
嬊拎
塋$H塂$L塼$P墊$T嬊W审H*润=    (求^馏D$XI嫃�   H��P 婬�蓧L$\荄$8����H婱PH吷tL塵PH��P怢塭PI�$H�H�    �怭  怚�$H�L塴$ A�0   L岲$0I嫍�   �PxI�$H�H峊$`�愗   I�$H�篅   D岼罝嬄�愢   怚�$H��怷  怉����I�$I嫃�   �    A����I�$I嫃�   �    I�$H�    )E燗笯   L岴營嫍�   �悙  I�$H��惛  婦$\A墖  I嫃�   H��P 婬W鲶H*馡嫃�   H��P (象^螊 W荔H*荔^A�   驛�  E壇  I�$H吷tM�,$H��P怚�H吷t
M�.H��P�(�$�   (�$�   H伳  A_A^A]A\_^[]脹   P   �   �   (  M   y     �  _     _     �      �   m  Z G            �  *   �  �3        �EnvMapImportanceSamplingBaker::GenerateImportanceMap 
 >2�   this  AJ        3  AW  3     � >纀   commandList  AK        0  AT  0     � DX   >EH   sourceCubemap  AP        -  AV  -     � D`   >W�    constants  D0    >�&    state  D`   ! >嬌    _generic_raii_scopevar_0  BP  o    z; M        �3  ��+K/ >u     samplesX  A   �     ) >u     samplesY  A   �     M  M        �  乄 M        �  乄GB
 >�    temp  AJ  [      AJ k      N N M        74  �� N N M        �'  s M        �  z
 N N M        �  M M        �  M N N M        �  3 N M        �3  
佈 M        �3  
佈 N N M        �3  乲 M        �3  乻 N N M        �  偝 M        �  偝CE
 >�    temp  AJ  �      AJ �    $  N N M        �1  偀 M        �1  偀DE
 > )    temp  AJ  �      AJ �      N N M        94  倐 N M          俼 N Z   �4  �4             @         @ � h$   �      
    �  �  �  �  �  �  �  x  y  �  �  �  �'  �'  �*  �*  �1  �1  �3  �3  �3  �3  �3  �3  �3  �3  4  4  74  84  94   P  2�  Othis  X  纀  OcommandList  `  EH  OsourceCubemap  0   W�  Oconstants  `   �&  Ostate % P  嬌  O_generic_raii_scopevar_0 M }�  EnvMapImportanceSamplingBaker::GenerateImportanceMap::__l3::<lambda_2> M g�  EnvMapImportanceSamplingBaker::GenerateImportanceMap::__l3::<lambda_1>  9�       E   9�       �   9�       �   9C      �   9g      E   9}      t)   9�      =)   9�      U)   9�      V)   9�      *)   9.      })   9;      *)   9V      �   9n      �   9�      E   9�      E   O   �   �           �  �
     �       �  �3   �  �:   �  �F   �  �s   �  �k  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �  �  �4  �  �A  �  �L  �  ��  �  ��  �  ��   /  i F                                �`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`1'::dtor$0  >纀   commandList  EN  X          >EH   sourceCubemap  EN  `          >W�    constants  EN  0           >�&    state  EN  `                                  �  O �   /  i F                                �`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`1'::dtor$1  >纀   commandList  EN  X          >EH   sourceCubemap  EN  `          >W�    constants  EN  0           >�&    state  EN  `                                  �  O �   /  i F                                �`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`1'::dtor$5  >纀   commandList  EN  X          >EH   sourceCubemap  EN  `          >W�    constants  EN  0           >�&    state  EN  `                                  �  O �   /  i F                                �`EnvMapImportanceSamplingBaker::GenerateImportanceMap'::`1'::dtor$3  >纀   commandList  EN  X          >EH   sourceCubemap  EN  `          >W�    constants  EN  0           >�&    state  EN  `                                  �  O ,   U   0   U  
    U   �   U  
 �   U   �   U  
 �   U   �   U  
 �   U   �   U  
 �   U   �   U  
   U   	  U  
 t  U   x  U  
 �  U   �  U  
 �  U   �  U  
 J  U   N  U  
 Z  U   ^  U  
 �  U   �  U  
 �  U   �  U  
 %  U   )  U  
 5  U   9  U  
 y  U   }  U  
 �  U   �  U  
 �  U   �  U  
 �  U   �  U  
 �  U   �  U  
 �  U   �  U  
 �  U   �  U  
 �  U   �  U  
 �  U   �  U  
 	  U   
  U  
   U     U  
 )  U   -  U  
 9  U   =  U  
 I  U   M  U  
 Y  U   ]  U  
 i  U   m  U  
 �  U   �  U  
 T  t   X  t  
 �  t   �  t  
 �  t   �  t  
 	  t   	  t  
 9	  t   =	  t  
 �	  z   �	  z  
 �	  z   �	  z  
 %
  z   )
  z  
 M
  z   Q
  z  
 q
  z   u
  z  
 �
  �   �
  �  
 1  �   5  �  
 ]  �   a  �  
 �  �   �  �  
 �  �   �  �  
 �  �      �  
 i  �   m  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H媻`  �       [   H媻X  �       ]   H崐P  �       e   H媻h  �       [   H冹(�    �    騂,�繦兡(�   S   
   �      �     ^ G                     �3        �EnvMapImportanceSamplingBaker::GetImportanceMapMIPLevels 
 >2�   this  AJ          D0   
 >u     mips  A          M        44  
 N (                      H  h   �3  44   0   2�  Othis  O  �   0              �
     $       �  �   �  �   �  �,   Q   0   Q  
 �   Q   �   Q  
 �   Q   �   Q  
   Q     Q  
 �   �   �   �   _ G                      �3        �EnvMapImportanceSamplingBaker::GetImportanceMapResolution 
 >2�   this  AJ          D                           H     2�  Othis  O   �   0              �
     $       �  �    �  �   �  �,   P   0   P  
 �   P   �   P  
 �   P   �   P  
 H塡$H塼$ UWAVH崿$羞���0!  �    H+郒�    H3腍墔    H孃H嬞H塗$0H児�    u�    E3鯨9池   呁  茀   H嫵�       )D$ H咑tH�H嬑�P D8p;�
u�	D塼$H圖$LH塼$@fD塼$M(D$ )D$PD坱$OH�D塼$h荄$l   H塂$`    )D$pH媰�   D塽圚荅�   H塃�H荅�   荅����H媰�   荅�   H荅�   H塃燞荅�   荅����H媰�   D塽惹E�   H塃繪塽蠰塽豀媰�   荅�   荅�   H塃郘塽餖塽鳬嬈H墔   H峀$@@ fff�     H拎D ILH媴   H�繦墔   H兞 H峌 H;蕌虯�  H峌 H崓  �    H�H�L媼�   L崊  H峊$ �恅  I嬛H峀$8H;萾H�L�0H媼�   H墦�   H吷tH��P怘婰$ H吷tL塼$ H��P怘�H吷t
L�7H��P怘媿    H3惕    L崪$0!  I媅0I媠8I嬨A^_]�   �   &   T   J   T   o   �   �   �   �  �   H  �      �   �  N G            d  4   =  3        �EnvMapImportanceSamplingBaker::PreUpdate 
 >2�   this  AI  :      AJ        :  >EH   sourceCubemap  D0    AK        7  AM  7     + DX!   >�#    bindingSetDesc  D   M        �  � M        �  �HB
 >�%    temp  AJ        AJ +      B    $    @  B�#  �    |  N N M        �  ,佽 M        �  �	 M        �  �	
 >�%    temp  AJ        AJ       N N M        �  侞 >�%    tmp  AK  �    )  AK     O   '   N M        �  佽C
 M        �  侕 N N N M        �  
乥
& >�#    <begin>$L0  AJ  q    S  M        �  亐 N N! M        �  丠'' >n#   sampler  AH  H      N M        �  �*$' >n#   sampler  AH  *      N M        �  �'( N M        �  ��$( N M        �  ��%( N# M        1  x%$E&
 N M        �  �+ M        �  �+CE
 >�    temp  AJ  .      AJ =      N N
 Z   �3   0!                   A v h   �  �  �  �  �    	  �  �  �  �  �  �  �  �  �  �  o  z  {  �  �  �  �  �  �'  �*  1  
 : !  O  P!  2�  Othis  X!  EH  OsourceCubemap    �#  ObindingSetDesc  9�       I!   9�      �(   9      E   9'      E   99      E   O   �   X           d  �
     L       �  �:   �  �?   �  �I   �  �N   �  �e   �  ��  �  �+  �  ��   �   ] F                                �`EnvMapImportanceSamplingBaker::PreUpdate'::`1'::dtor$0  >EH   sourceCubemap  EN  0           EN  X!          >�#    bindingSetDesc  EN                                   �  O  ,   N   0   N  
 s   N   w   N  
 �   N   �   N  
 �   N   �   N  
 �   N   �   N  
 ?  N   C  N  
 O  N   S  N  
 _  N   c  N  
 o  N   s  N  
 �  N   �  N  
 �  N   �  N  
 ,  N   0  N  
 <  N   @  N  
 �  N   �  N  
 5  N   9  N  
 y  N   }  N  
 _  N   c  N  
 o  N   s  N  
 }  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 X  u   \  u  
 �  u   �  u  
 �  u   �  u  
 �  u      u  
 H媻0   �       [   L塂$H塗$SVWH冹0I孁H嬟H嬹H塗$(H�
H�H�    �怭  怘�H塋$hH吷tH��P怘�H塋$ H吷tH��P怢岲$hH峊$ H嬑�    怘�H��怷  怘�3鯤吷t
H�3H��P怘�H吷t
H�7H��P怘兡0_^[�(      i   U      �   �  K G            �      �   3        �EnvMapImportanceSamplingBaker::Update 
 >2�   this  AJ          AL       f  >纀   commandList  AI       �  AK          DX    >EH   sourceCubemap  AM       �  AP          D`   ! >纳    _generic_raii_scopevar_1  D(    M        �  �� M        �  ��CE
 >�    temp  AJ  �       AJ �       N N M        �1  { M        �1  {CG
 > )    temp  AJ  ~       AJ �       N N M        �3  n M        �3  n N N M        4  G M        c2  O N N M        �'  3 M        �  ; N N M        �3   M        �3   N N
 Z   �3   0                    @ B h   �  �  �  �'  �1  �1  c2  �3  �3  �3  �3  �3  �3  4  4   P   2�  Othis  X   纀  OcommandList  `   EH  OsourceCubemap % (   纳  O_generic_raii_scopevar_1 > 飞  EnvMapImportanceSamplingBaker::Update::__l2::<lambda_2> > ⑸  EnvMapImportanceSamplingBaker::Update::__l2::<lambda_1>  9,       t)   9C       E   9W       E   9t       *)   9�       E   9�       E   O �   8           �   �
     ,       �  �   �  �3    �n    ��     Z F                                �`EnvMapImportanceSamplingBaker::Update'::`1'::dtor$0  >纀   commandList  EN  X           >EH   sourceCubemap  EN  `          ! >纳    _generic_raii_scopevar_1  EN  (                                  �  O �     Z F                                �`EnvMapImportanceSamplingBaker::Update'::`1'::dtor$1  >纀   commandList  EN  X           >EH   sourceCubemap  EN  `          ! >纳    _generic_raii_scopevar_1  EN  (                                  �  O �     Z F                                �`EnvMapImportanceSamplingBaker::Update'::`1'::dtor$2  >纀   commandList  EN  X           >EH   sourceCubemap  EN  `          ! >纳    _generic_raii_scopevar_1  EN  (                                  �  O ,   O   0   O  
 p   O   t   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 �   O   �   O  
 |  O   �  O  
 �  O   �  O  
 �  O   �  O  
 �  O   �  O  
 7  O   ;  O  
 G  O   K  O  
 W  O   [  O  
 g  O   k  O  
 w  O   {  O  
 �  O   �  O  
 �  O   �  O  
    v     v  
 ^  v   b  v  
 �  v   �  v  
 �  v   �  v  
   {     {  
 r  {   v  {  
 �  {   �  {  
 �  {   �  {  
 (  ~   ,  ~  
 �  ~   �  ~  
 �  ~   �  ~  
 �  ~   �  ~  
 H媻`   �       [   H媻X   �       ]   H崐(   �       f   H吷tH��   H�`�   �     � G                      f4        �std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> >::_Delete_this 
 >?�   this  AJ                                 @�     ?�  Othis  9
       F�   O   �   0              �     $       � �    � �   � �,   l   0   l  
 �   l   �   l  
   l     l  
   l      l  
 @SH冹 H媃H呟tH嬎�    酣   H嬎H兡 [�    H兡 [�   `   %   '      �   "  � G            /      )   g4        �std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> >::_Destroy 
 >?�   this  AJ        /    M        4  	 N                       @�  h   4  4  '4   0   ?�  Othis  O  �   0           /   �     $       � �   � �   � �,   k   0   k  
 �   k   �   k  
 8  k   <  k  
 @SH冹 H嬞H岼H�   �    3襀岾吚HD袶嬄H兡 [�         D      �   v  � G            .      (   h4        �std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> >::_Get_deleter 
 >C�   this  AI  	     $  AJ        	  >   _Typeid  AK          M        �3  	
 Z   �   N                       @�  h   �3  o4  x4   0   C�  Othis  8     O_Typeid  O  �   0           .   �     $       � �	   � �(   � �,   j   0   j  
 �   j   �   j  
 �   j   �   j  
 �   j   �   j  
 �  j   �  j  
 H冹HH峀$ �    H�    H峀$ �    �
   7      �      �      �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (                #            J �   K �,   =   0   =  
 �   �   �   �  
 �   =   �   =  
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   /   0   /  
 _   /   c   /  
 �   /   �   /  
  d T 4 2p    H           �      �      �    20    2           �      �      �   
 
4 
2p    B           �      �      �    20    <           �      �      �   
 
4 
2p    B           �      �      �    20    <           �      �      �   
 
4 
2p    B           �      �      �    �                  �      �      �    20    `           �      �      �    20    `           �      �      �    B             �             "           �      �         h                           (   2 20           �             ?           �      �         h                            (   :8  B             �      )       "           �      �      #   h           ,      /          (   2 d T 4 2p           �      8       }           �      �      2   h           ;      >          (   � 20    [           �      �      A    20    e           �      �      G    T 4
 2�p`           �      S       7          �      �      M   (           V      Y       4    �6    .       (      F   
   �      �   P>� 4 2��p`P           �      b       �          �      �      \   (           e      h       �4    �4    �6    \    �6    .    .    .       X      Z      W      (      F      �   $   �   )   �   .   �   rP y � /Cbv�� d T 4 2p           �      t                 �      �      n   h           w      z          (   }Q 4u * > _ s ���� 2D.	 d>4=8�pP          �	     �      �       T          �      �      �   (           �      �   
    2    �>    �    >       (      ?      (      C      (   ,� ^4 �D �)D D }D fJ
.$
<r
xT @� 4	 #d-#4,#&�pP          "!     �      �       d          �      �      �   (           �      �       `6    f       [      (   
   (   
6 \D$  R
p`0           �      �       �           �      �      �   (           �      �       �4    �2    P6    �    �    -       [      ]      f      (      (      (   $   (   X.0&
($  B                 �      �      �    20           �      �       ;           �      �      �   (           �      �          (   64 .  t)  d(  4'  " ��P               �       �       �          �      �      �   (           �      �   
    A>    f    .    �    p�    .    e    `   >   	   (      w      (      b      x   #   (   )   b   ��D�D$2�6
TJ�2� P 2P               w      w      �    2P                x      x      �   �* *x &h "! ����p`
0P            �   $   �       �          �      �      �   (           �      �       �<    a>    d    ��    A    �       [   	   ]      (      [      e   $   (   *   (   �(2
�($  4 2��
p`P           �      �                 �      �      �   (           �      �       �>       [      (   �Y,  20               �      �         ! t               �      �            E           �      �         !                 �      �         E   K           �      �      
   - 20               �      �         ! t               �      �            E           �      �         !                 �      �         E   K           �      �      "   - 20               �      �      +   ! t               �      �      +      E           �      �      1   !                 �      �      +   E   K           �      �      7   - 20               �      �      @   ! t               �      �      @      E           �      �      F   !                 �      �      @   E   K           �      �      L   - B             �      [       "           �      �      U   h           ^      a          (   2 B             �      j       "           �      �      d   h           m      p          (   2 d T 4 2p           �      y       x          �      �      s   h           |                (   f� @� GY 20    .           �      �      �    t	 d 4 2�    �           �      �      �   }� B             �      �                  e      e      �   `       �     B             �      �                  f      f      �   `       �     B      :           �      �      �    20    .           �      �      �    20    /           �      �      �    20    !           �      �      �                                     1      /   Unknown exception                                   5      /                               &      ;      /   bad array new length                                8      �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      9                   .?AVbad_alloc@std@@     �              ����                      �      3                   .?AVexception@std@@     �               ����                      �      -   string too long     ����    ����        ��������EnvMapImportanceSamplingBakerConstants BuildMIPDescentImportanceMapCS app/Lighting/Distant/EnvMapImportanceSamplingBaker.hlsl EnvImportanceMap EnvRadianceMap GenIM ISBake                                             >      k      l      n       j                   .?AU?$default_delete@VMipMapGenPass@render@donut@@@std@@     �                                         �                                                                       ����    @                   �                                               �                                                                 #                    ����    @                   �                                               �      )      &                         ,                                   /      #                    ����    @                   �      )                   .?AV_Ref_count_base@std@@     �                         8                   ;               ����    @                   2      5                                         A      D      >                   .?AV?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@     �                         G                           J      ;              ����    @                   A      D     �?      0@      怈   �   �   � 
�        std::_Ref_count_resource<donut::render::MipMapGenPass *,std::default_delete<donut::render::MipMapGenPass> >::`vftable'               
    �   (   & 
�        std::exception::`vftable'    �      �  
    �   (   & 
�        std::bad_alloc::`vftable'    �      �  
    �   3   1 
�        std::bad_array_new_length::`vftable'     �      �  
 斻C�bCOdJ�拎i玌刦k網叜月o"0Y�a�5E}e譇擼h�K蜌�(薄砰顢痴[s濦鍷梯C齜蓁鮻Je裴gF7饄熀�Ur�(！
Z曅蓘胤M�3>飖9屓N逦B憂=鐙鯊貗僤脇坕4#惮&開m.o9蛁7F蠍I�諜L乘翇^=f瓵#蠿骑嘍鷎6 d)凐4�/壅Hc/b�8諜kl�9癓楊�$phxl�;镩垓浄A鸇畟豼鈎
t
�6囸谷竛B0{l獕褳L嚤踖p禭�?As贛嚤踖p禭飓D堦縵�6萪O�采譮�m3K霵婬(m铬彺��=咚E偟嘧\笯�=咚E縢|�<单�'項jUF$
 o旂'項jaO喡�	歧'項j筻&��5�'項j�.澻d蠲朵�+^{3癕�VF{v蹪\夂嫑�T�袲ǚq端祆癜~t傳Ⅰ梳9臉
孷砄鴮�k�$愜w獛啯d<狸�#tZ$愜w獛啯b「+�|�$愜w獛啯o3�_蒆$愜w獛啯�7弳�J鱀)m凾�'鶴hY猕up泸睨袙"F�?鲢�6�<#�(棙 嫃枉瀂Po⒙ �
(1*摩t
)�7Ax浣:Q蚴0k浱U鞶琺� 端祆癜~t+隙緻徫犷A棊膬/S;圾j硘嶀預棊膬訡q�.�熟l
�7賹眖箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7� �蹰k�%禾)課仛泸1瓞阳q襃
坺$P閝+肱+ 埚巌;��昸鳐3鑩`焥X窧乓%1浩��2<R佦 嬯矊臗e琈�佗�&V*�鷑薌衳磩j┱fh廳&�%1鑦�窗mN跴侁.�槍寄踆顪$phxl'n眼;?粢娴棷�O`qSJ锊新嫶N�砶@笂4檜�3湓S|{鎜=橠�讵� J]?糃o飌嵦8)c嵻揽恞��衹K!睉K嫜臷'y\F董貁矕勹9�$phxl8s|積�/進♁恗昷躔7颦硣K桿-�丈1�)岡澀�;#鍜U]s箎詽:U*门tY�%I栶賑?T娔x侮Pd�nN鵘J怄-�唊�F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪雵J-WV8o;き8乿る嘕-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D罵��$m翲咞taR�,F_棢杻#Q惕屖碋箎了5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊嘕-WV8om�M%>mb雵J-WV8o.*~襠[
BG嶊4K鋆 鈨?鷾�dd�a�:湌h恩悟鯺�)閇P鑎鬡x�02D#\觺w dd�a�:h鋇甚L\I麠憃耣哥!晕sW.�?覡RWQ
0▋咞taR�,F_棢杻#Q'轍�v昪�/�+禙撹嚺0W^R廱zEdd�a�:�j|�f[Q�妨抁/M煞�D�-�-&k�堢��dd�a�:yy蟵D觴�滊^J)伬#�*�.w⒇衞dd�a�:g矪A病u屋�
蹺�2-坓�(鬄� E<礼\L3Iq磻9
啋帹鉹 dd�a�:_棢杻#QZ绯SC�>s8ka尮c3躾�(dd�a�:灳6(�鸾晏E�7樢閣yQ E<礼\樢閣yQ5R犵�喪�8099X>� .�=栒帱x*@Udd�a�:印a}A崬�闕1�<鵷S�0琩噬諩源皖dd�a�:|啊三C3�秬芥雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡?�9)咞taR�,F_棢杻#Q狻b钣R}樓殧�-嵉2雵J-WV8o�
,騤\W=�I籄`@衔�#炽�
^チ�5YJq寅`兟+d+盚挎驻趀铑摕|獇吜�5YJq寅`兟+d+盚挎驻趀铑摕|獇�-坓�(鬄�/ｎ	蜍R雵J-WV8o�
,騤雵J-WV8o礻n�尔z岆嘕-WV8oc8曀黩6潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H虧�-�@“m夶~z�>�!鐁�9劷獩4~ 寀U/奅�</彊k恤阀z-�,4��;儗� 隧P%)��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2�i>嬕S%ZZ�$为赞G刹~赣 "^惋砤揉^笵A傮黈�K$疞叏R▄m"℃轀R�;� タY�脌祦�
監�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       ~                .debug$S       搓              .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       �      �Y�     .debug$S       L             .text$mn       :      眡�     .debug$S                    .text$mn    	         9拙�     .debug$S    
   �          	    .text$mn              u\�'     .debug$S       �              .text$mn    
           _葓�     .debug$S       4         
    .text$mn              �邆     .debug$S                    .text$mn       7     Z頞u     .debug$S       D
  D           .text$x              碙辢    .text$x              曍譧    .text$x              �c    .text$mn              恶Lc     .debug$S       �              .text$mn       �     H鈬     .debug$S         |           .text$x              f岋F    .text$x              l鱍    .text$x              �/�    .text$x              ��    .text$x              "�    .text$x              汞�    .text$x               ��    .text$x     !         彧    .text$mn    "   <      .ズ     .debug$S    #   0  
       "    .text$mn    $   <      .ズ     .debug$S    %   L  
       $    .text$mn    &   !      :著�     .debug$S    '   <         &    .text$mn    (   2      X于     .debug$S    )   <         (    .text$mn    *          tS>4     .debug$S    +   �         *    .text$mn    ,          tS>4     .debug$S    -   (         ,    .text$mn    .   "       坼	     .debug$S    /   �         .    .text$mn    0   "       坼	     .debug$S    1   �         0    .text$mn    2   "       坼	     .debug$S    3   �         2    .text$mn    4   "       坼	     .debug$S    5   �         4    .text$mn    6   e      D远     .debug$S    7   �         6    .text$mn    8   [       荘�     .debug$S    9            8    .text$mn    :          .B+�     .debug$S    ;   �         :    .text$mn    <   }      1�-�     .debug$S    =   �         <    .text$mn    >   K       }'     .debug$S    ?   �         >    .text$mn    @   K       }'     .debug$S    A   �         @    .text$mn    B   K       }'     .debug$S    C   �         B    .text$mn    D   K       }'     .debug$S    E   �         D    .text$mn    F   .      嗰�     .debug$S    G   �         F    .text$mn    H   `      板@�     .debug$S    I   �         H    .text$mn    J   ?      劸惂     .debug$S    K   \         J    .text$mn    L        +鴨�     .debug$S    M     n       L    .text$mn    N   x     l俅�     .debug$S    O   x  T       N    .text$mn    P   `      ,     .debug$S    Q   �         P    .text$mn    R          .B+�     .debug$S    S   �          R    .text$mn    T         ��#     .debug$S    U   �          T    .text$mn    V         ��#     .debug$S    W   �          V    .text$mn    X   !      冘�     .debug$S    Y   0         X    .text$mn    Z   B      贘S     .debug$S    [             Z    .text$mn    \   B      贘S     .debug$S    ]            \    .text$mn    ^   B      贘S     .debug$S    _   �          ^    .text$mn    `   H       襶.      .debug$S    a   �         `    .text$mn    b   �     Vv讄     .debug$S    c   8  �       b    .text$x     d         吻l攂    .text$x     e         :�薭    .text$x     f         "E萷b    .text$x     g         豒�&b    .text$x     h          �l韇    .text$mn    i   T     *咜+     .debug$S    j     �       i    .text$x     k         mE�i    .text$x     l         n匰抜    .text$mn    m          簎x�     .debug$S    n   0         m    .text$mn    o   ;       �7鼃     .debug$S    p            o    .text$mn    q        LO0�     .debug$S    r   p  (       q    .text$x     s         �/阸    .text$mn    t   �     頊     .debug$S    u   
  j       t    .text$x     v         壩�&t    .text$x     w         C琹硉    .text$x     x         �鄑    .text$x     y         憀]漷    .text$mn    z         �崇     .debug$S    {   H         z    .text$mn    |          E晼{     .debug$S    }            |    .text$mn    ~   d     �颅     .debug$S       (  8       ~    .text$x     �         a弣榽    .text$mn    �   �      腊宍     .debug$S    �     <       �    .text$x     �         �/陙    .text$x     �         莠��    .text$x     �         Kバg�    .text$mn    �          c淖�     .debug$S    �   L         �    .text$mn    �   /      �u纉     .debug$S    �   h         �    .text$mn    �   .      :槻@     .debug$S    �   �  
       �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         崪覩     .debug$S    �   �          �        \       `        x                �                �                �                �                �                �                               1               I      (        j      V        �      �        �      ^        �          i0                   �      "              Z        "          i4                   A      &        f      T        �      $        �      \        �          i:                         �        3      P        Q      H        n              �              �      .        �      J        %               =      R        ]      2        �      <        4      8        �      6        �                            �      L              i        J      ~        �      �        "	      |        e	      z        �	      o        2
      m        e
      b        �
      t        .      q        �      D        �      >        5      @        u      B        �      4        �               �
      0        �
               {               �      N        �      	        �      F        o              q              �      ,        \      *        "      
        s              �      :        D      �        �      �        `      �        �      X        h          im                   �              1                    d        Y      s        �      v        �      �        �      �        �      g        �      h                      �      w        �      �        %                    k        L      �        �              �      e               x        �               �               �!      f        
"              U"              2#      y        �#               �$      l        �$      !        �%               �%               �%           __chkstk             
&           log2             memcpy           memmove          memset           sqrt             $LN13       `    $LN5        (    $LN10       ^    $LN7        "    $LN13       Z    $LN10       $    $LN16       \    $LN3        �    $LN4        �    $LN39   `   P    $LN42       P    $LN39   `   H    $LN42       H    $LN10       .    $LN18       J    $LN10       2    $LN125      <    $LN32   [   8    $LN35       8    $LN35   e   6    $LN38       6    $LN163  7      $LN166          $LN130          $LN144      L    $LN576  T  i    $LN580      i    $LN90       ~    $LN48       �    $LN7        z    $LN20       o    $LN527  �  b    $LN532      b    $LN85       t    $LN25       q    $LN18       D    $LN18       >    $LN18       @    $LN18       B    $LN10       4    $LN10       0    $LN112  x  N    $LN115      N    $LN14       F    $LN53           $LN14   :       $LN17           $LN8        �    $LN13       �    $LN8        X    .xdata      �          F┑@`        %&      �    .pdata      �         X賦鷃        I&      �    .xdata      �          （亵(        l&      �    .pdata      �          T枨(        �&      �    .xdata      �          %蚘%^        �&      �    .pdata      �         惻竗^        �&      �    .xdata      �          （亵"        
'      �    .pdata      �         2Fb�"        3'      �    .xdata      �          %蚘%Z        ['      �    .pdata      �         惻竗Z        �'      �    .xdata      �          （亵$        �'      �    .pdata      �         2Fb�$        �'      �    .xdata      �          %蚘%\        (      �    .pdata      �         惻竗\        A(      �    .xdata      �          懐j瀸        r(      �    .pdata      �         Vbv鶎        �(      �    .xdata      �          （亵P        �(      �    .pdata      �         粻胄P        �(      �    .xdata      �          （亵H        )      �    .pdata      �         粻胄H        A)      �    .xdata      �         /
�.        e)      �    .pdata      �         +eS�.        �)      �    .xdata      �   	      �#荤.        �)      �    .xdata      �         j.        &*      �    .xdata      �          3狷 .        n*      �    .xdata      �         蚲7MJ        �*      �    .pdata      �         袮韁J        �*      �    .xdata      �   	      �#荤J        +      �    .xdata      �         jJ        ;+      �    .xdata      �          愔
~J        q+      �    .xdata      �         /
�2        �+      �    .pdata      �         +eS�2        �+      �    .xdata      �   	      �#荤2        ,      �    .xdata      �         j2        M,      �    .xdata      �          3狷 2        �,      �    .xdata      �         vQ9	<        �,      �    .pdata      �         A刄7<        w-      �    .xdata      �   	      �#荤<        $.      �    .xdata      �         j<        �.      �    .xdata      �          強S�<        �/      �    .xdata      �          （亵8        :0      �    .pdata      �         愶L8        1      �    .xdata      �          （亵6        �1      �    .pdata      �         弋�6        �2      �    .xdata      �         鸝�        �3      �    .pdata      �         蠶)        �3      �    .xdata      �   	      � )9        -4      �    .xdata      �         QuX#        p4      �    .xdata      �          M欤+        �4      �    .xdata      �         頒@        �4      �    .pdata      �         盺[�        �5      �    .xdata      �   	      � )9        �6      �    .xdata      �   2   	   铿;T        7      �    .xdata      �          cRO        ]8      �    .voltbl     �          FQ錦    _volmd      �    .xdata      �         vQ9	L        59      �    .pdata      �         掞釲        g9      �    .xdata      �   	      �#荤L        �9      �    .xdata      �         jL        �9      �    .xdata      �          罻b6L        :      �    .voltbl     �          !椢L    _volmd      �    .xdata      �   $      �⑩鏸        ::      �    .pdata      �         燃瞚        }:      �    .xdata      �   	      � )9i        �:      �    .xdata      �         _f鵬        ;      �    .xdata      �   0       �i        O;      �    .voltbl     �          Y艻ii    _volmd      �    .xdata      �   $      軬�~        �;      �    .pdata      �         h�>脋        �;      �    .xdata      �   	      � )9~        W<      �    .xdata      �         j俔#~        �<      �    .xdata      �          �JS~        %=      �    .xdata      �         仃�        �=      �    .pdata      �         祦        >      �    .xdata      �   	      � )9�        �>      �    .xdata      �   (      �(E.�        ?      �    .xdata      �          G��        �?      �    .xdata      �          �9�z        2@      �    .pdata      �         �$剧z        |@      �    .xdata      �         傴$>o        臔      �    .pdata      �         +Oжo        XA      �    .xdata      �   	      � )9o        闍      �    .xdata      �         jo        B      �    .xdata      �          $&n榦        C      �    .xdata      �   (      �Mpb        疌      �    .pdata      �         b        驝      �    .xdata      �   	      � )9b        6D      �    .xdata      �   .      膿'4b        |D      �    .xdata      �   !       �=譩        菵      �    .xdata      �          k筨        E      �    .pdata      �         �$剧b        bE      �    .xdata      �          k筨        礒      �    .pdata      �         Vbv鵥        	F      �    .voltbl     �          `k衎    _volmd      �    .xdata      �   (      �$~猼        \F      �    .pdata      �         c/ t        馞      �    .xdata      �   	      � )9t        匞      �    .xdata      �   .      	痶        H      �    .xdata      �          鸏�t        笻      �    .xdata      �         P�蟩        PI      �    .pdata      �         N/葾q        錓      �    .xdata      �   	      � )9q        yJ      �    .xdata      �         氡�q        K      �    .xdata      �          D憟鎞        璌      �    .xdata      �          （亵D        DL      �    .pdata      �         � 貲        哃      �    .xdata      �         范^揇        荓      �    .pdata      �         鳶�D        
M      �    .xdata      �         @鴚`D        MM      �    .pdata      �         [7蹹        怣      �    .voltbl     �          飾殪D    _volmd      �    .xdata      �          （亵>        覯      �    .pdata               � �>        N          .xdata              范^�>        bN         .pdata              鳶�>        玁         .xdata              @鴚`>        鬘         .pdata              [7�>        =O         .voltbl              飾殪>    _volmd         .xdata               （亵@        哋         .pdata              � 貮        蜲         .xdata              范^揁        P         .pdata      	        鳶�@        ^P      	   .xdata      
        @鴚`@              
   .pdata              [7蹳        餚         .voltbl              飾殪@    _volmd         .xdata      
         （亵B        9Q      
   .pdata              � 貰        |Q         .xdata              范^揃        綫         .pdata              鳶�B        R         .xdata              @鴚`B        FR         .pdata              [7蹷        奟         .voltbl              飾殪B    _volmd         .xdata              /
�4        蜶         .pdata              +eS�4        S         .xdata        	      �#荤4        AS         .xdata              j4        }S         .xdata               3狷 4        縎         .xdata              /
�0        鸖         .pdata              +eS�0        9T         .xdata        	      �#荤0        vT         .xdata              j0        禩         .xdata               3狷 0        黅         .xdata              vQ9	N        <U         .pdata              憗沦N        kU         .xdata         	      �#荤N        橴          .xdata      !        jN        蔝      !   .xdata      "  
       HH)"N        V      "   .voltbl     #         �0彮N    _volmd      #   .xdata      $         （亵F        2V      $   .pdata      %        dpF        猇      %   .xdata      &         襢@        !W      &   .pdata      '        袷湅        +X      '   .voltbl     (         
櫀�    _volmd      (   .xdata      )        /
�,        4Y      )   .pdata      *         *鬰,        Z      *   .xdata      +        Mw2�,        鞿      +   .xdata      ,         �.┏,        蘙      ,   .xdata      -        /
�*        玕      -   .pdata      .         *鬰*        y]      .   .xdata      /        Mw2�*        F^      /   .xdata      0         �.┏*        _      0   .xdata      1         �9�        鎋      1   .pdata      2        礝
        C`      2   .xdata      3         （亵�        焋      3   .pdata      4        dp�        @a      4   .xdata      5         （亵�        郺      5   .pdata      6        鷓V �        kb      6   .xdata      7         （亵X        鮞      7   .pdata      8        萣�5X        ~c      8   .rdata      9                     d     9   .rdata      :         �;�         d      :   .rdata      ;                     Dd     ;   .rdata      <                     [d     <   .rdata      =         �)         }d      =   .xdata$x    >                     ヾ      >   .xdata$x    ?        虼�)         薲      ?   .data$r     @  /      嶼�         頳      @   .xdata$x    A  $      4��         e      A   .data$r     B  $      鎊=         he      B   .xdata$x    C  $      銸E�         俥      C   .data$r     D  $      騏糡         羍      D   .xdata$x    E  $      4��         踖      E       f           .rdata      F         燺渾         -f      F   .data       G          烀�          Sf      G       噁     G   .rdata      H  '       �を         甪      H   .rdata      I         苞         鈌      I   .rdata      J  8       MJ勩         g      J   .rdata      K         � �         Lg      K   .rdata      L         丸于         qg      L   .rdata      M         夼辎         揼      M   .rdata      N         焜(�         玤      N   .rdata      O  (                   膅     O   .data$r     P  I      !進�         =h      P   .rdata$r    Q  $      'e%�         |h      Q   .rdata$r    R        �          攈      R   .rdata$r    S                     猦      S   .rdata$r    T  $      Gv�:         纇      T   .rdata$r    U  $      'e%�         遠      U   .rdata$r    V        }%B         鱤      V   .rdata$r    W                     
i      W   .rdata$r    X  $      `         #i      X   .rdata$r    Y  $      'e%�         Bi      Y   .rdata$r    Z        �弾         ei      Z   .rdata$r    [                     唅      [   .rdata$r    \  $      H衡�               \   .data$rs    ]  *      8V綊         裪      ]   .rdata$r    ^        �          駃      ^   .rdata$r    _                     
j      _   .rdata$r    `  $      Gv�:         )j      `   .rdata$r    a  $      'e%�         Nj      a   .data$rs    b  �      �F"         萰      b   .rdata$r    c        }%B         Dk      c   .rdata$r    d                     糼      d   .rdata$r    e  $      `         4l      e   .rdata      f         v靛�         祃      f   .rdata      g         cw毄         舕      g   .rdata      h         娝�         輑      h       鮨           _fltused         .debug$S    i  �          O   .debug$S    j  4          9   .debug$S    k  4          ;   .debug$S    l  @          <   .chks64     m  h                m  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ??1TextureDesc@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1ComputePipelineDesc@nvrhi@@QEAA@XZ __std_type_info_compare ??1_Ref_count_base@std@@UEAA@XZ ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAA@XZ ??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z ??0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z ??1EnvMapImportanceSamplingBaker@@QEAA@XZ ?CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ ?PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z ?Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z ?GetImportanceMapResolution@EnvMapImportanceSamplingBaker@@QEAAHXZ ?GetImportanceMapMIPLevels@EnvMapImportanceSamplingBaker@@QEAAHXZ ?ExecutePresampling@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@H@Z ?DebugGUI@EnvMapImportanceSamplingBaker@@QEAA_NM@Z ?CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ ?GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z ?FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z ??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ ??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ ??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ?CreateShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ ??0MipMapGenPass@render@donut@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@V?$RefCountPtr@VITexture@nvrhi@@@4@W4Mode@012@@Z ?Dispatch@MipMapGenPass@render@donut@@QEAAXPEAVICommandList@nvrhi@@H@Z ??1MipMapGenPass@render@donut@@QEAA@XZ ??$log2@I$0A@@@YANI@Z ??1?$unique_ptr@VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@QEAA@XZ ??$?4VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@$0A@@?$shared_ptr@VMipMapGenPass@render@donut@@@std@@QEAAAEAV01@$$QEAV?$unique_ptr@VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@1@@Z ??$sqrt@I$0A@@@YANI@Z ??1?$GenericScope@V<lambda_1>@?2??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?2??23@AEAAX01@Z@@@QEAA@XZ ??1?$GenericScope@V<lambda_1>@?1??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?1??23@QEAAX01@Z@@@QEAA@XZ ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@0@0AEBV10@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??1?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEAA@XZ ?_Get_deleter@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEBAPEAXAEBVtype_info@@@Z ?_Destroy@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@EEAAXXZ ??_G?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEAAPEAXI@Z ?dtor$0@?0???0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$0@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$0@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA ?dtor$0@?0??FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z@4HA ?dtor$0@?0??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$0@?0??PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z@4HA ?dtor$0@?0??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$14@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA ?dtor$19@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA ?dtor$1@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$1@?0??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$1@?0??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$2@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$2@?0??CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ@4HA ?dtor$2@?0??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$3@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$3@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA ?dtor$3@?0??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$4@?0???0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$4@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$4@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA ?dtor$5@?0???0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$5@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$5@?0??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z@4HA ?dtor$6@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA ?dtor$6@?0??CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ@4HA ?dtor$7@?0???0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$??1TextureDesc@nvrhi@@QEAA@XZ $pdata$??1TextureDesc@nvrhi@@QEAA@XZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $pdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@2@@std@@QEAA@XZ $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@V?$_Uhash_compare@_KU?$hash@_K@std@@U?$equal_to@_K@2@@std@@V?$allocator@U?$pair@$$CB_KV?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@@std@@@4@$0A@@std@@@std@@QEAA@XZ $unwind$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $pdata$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $cppxdata$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $stateUnwindMap$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $ip2state$??0BindingCache@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $unwind$??0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z $pdata$??0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z $cppxdata$??0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z $stateUnwindMap$??0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z $ip2state$??0EnvMapImportanceSamplingBaker@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VShaderFactory@engine@donut@@@4@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@@Z $unwind$??1EnvMapImportanceSamplingBaker@@QEAA@XZ $pdata$??1EnvMapImportanceSamplingBaker@@QEAA@XZ $cppxdata$??1EnvMapImportanceSamplingBaker@@QEAA@XZ $stateUnwindMap$??1EnvMapImportanceSamplingBaker@@QEAA@XZ $ip2state$??1EnvMapImportanceSamplingBaker@@QEAA@XZ $unwind$?CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ $pdata$?CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ $cppxdata$?CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ $stateUnwindMap$?CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ $ip2state$?CreateRenderPasses@EnvMapImportanceSamplingBaker@@QEAAXXZ $unwind$?PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z $pdata$?PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z $cppxdata$?PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z $stateUnwindMap$?PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z $ip2state$?PreUpdate@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@Z $unwind$?Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $pdata$?Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $cppxdata$?Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $stateUnwindMap$?Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $ip2state$?Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $unwind$?GetImportanceMapMIPLevels@EnvMapImportanceSamplingBaker@@QEAAHXZ $pdata$?GetImportanceMapMIPLevels@EnvMapImportanceSamplingBaker@@QEAAHXZ $unwind$?ExecutePresampling@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@H@Z $pdata$?ExecutePresampling@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@H@Z $cppxdata$?ExecutePresampling@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@H@Z $stateUnwindMap$?ExecutePresampling@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@H@Z $ip2state$?ExecutePresampling@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@H@Z $unwind$?CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ $pdata$?CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ $cppxdata$?CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ $stateUnwindMap$?CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ $ip2state$?CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ $unwind$?dtor$14@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA $pdata$?dtor$14@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA $unwind$?dtor$19@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA $pdata$?dtor$19@?0??CreateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXXZ@4HA $unwind$?GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $pdata$?GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $cppxdata$?GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $stateUnwindMap$?GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $ip2state$?GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@3@@Z $unwind$?FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z $pdata$?FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z $cppxdata$?FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z $stateUnwindMap$?FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z $ip2state$?FillBakerConsts@EnvMapImportanceSamplingBaker@@AEAAXAEAUEnvMapImportanceSamplingBakerConstants@@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@H@Z $unwind$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1MipMapGenPass@render@donut@@QEAA@XZ $pdata$??1MipMapGenPass@render@donut@@QEAA@XZ $cppxdata$??1MipMapGenPass@render@donut@@QEAA@XZ $stateUnwindMap$??1MipMapGenPass@render@donut@@QEAA@XZ $ip2state$??1MipMapGenPass@render@donut@@QEAA@XZ $unwind$??1?$unique_ptr@VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$unique_ptr@VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@QEAA@XZ $unwind$??$?4VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@$0A@@?$shared_ptr@VMipMapGenPass@render@donut@@@std@@QEAAAEAV01@$$QEAV?$unique_ptr@VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@1@@Z $pdata$??$?4VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@$0A@@?$shared_ptr@VMipMapGenPass@render@donut@@@std@@QEAAAEAV01@$$QEAV?$unique_ptr@VMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@1@@Z $unwind$??1?$GenericScope@V<lambda_1>@?2??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?2??23@AEAAX01@Z@@@QEAA@XZ $pdata$??1?$GenericScope@V<lambda_1>@?2??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?2??23@AEAAX01@Z@@@QEAA@XZ $cppxdata$??1?$GenericScope@V<lambda_1>@?2??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?2??23@AEAAX01@Z@@@QEAA@XZ $ip2state$??1?$GenericScope@V<lambda_1>@?2??GenerateImportanceMap@EnvMapImportanceSamplingBaker@@AEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?2??23@AEAAX01@Z@@@QEAA@XZ $unwind$??1?$GenericScope@V<lambda_1>@?1??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?1??23@QEAAX01@Z@@@QEAA@XZ $pdata$??1?$GenericScope@V<lambda_1>@?1??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?1??23@QEAAX01@Z@@@QEAA@XZ $cppxdata$??1?$GenericScope@V<lambda_1>@?1??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?1??23@QEAAX01@Z@@@QEAA@XZ $ip2state$??1?$GenericScope@V<lambda_1>@?1??Update@EnvMapImportanceSamplingBaker@@QEAAXV?$RefCountPtr@VICommandList@nvrhi@@@nvrhi@@V?$RefCountPtr@VITexture@nvrhi@@@5@@Z@V<lambda_2>@?1??23@QEAAX01@Z@@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$?_Get_deleter@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEBAPEAXAEBVtype_info@@@Z $pdata$?_Get_deleter@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEBAPEAXAEBVtype_info@@@Z $unwind$?_Destroy@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@EEAAXXZ $pdata$?_Destroy@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@EEAAXXZ $unwind$??_G?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@UEAAPEAXI@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0CH@INKJBLBM@EnvMapImportanceSamplingBakerCo@ ??_C@_0BP@GMFOKJPP@BuildMIPDescentImportanceMapCS@ ??_C@_0DI@MPLDBAPL@app?1Lighting?1Distant?1EnvMapImpo@ ??_C@_0BB@KLBGCOPK@EnvImportanceMap@ ??_C@_0P@PCPPBENF@EnvRadianceMap@ ??_C@_05OMNHJLIC@GenIM@ ??_C@_06MELLEKBO@ISBake@ ??_7?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@6B@ ??_R0?AU?$default_delete@VMipMapGenPass@render@donut@@@std@@@8 ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@6B@ ??_R0?AV?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@@8 ??_R3?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@8 ??_R2?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_resource@PEAVMipMapGenPass@render@donut@@U?$default_delete@VMipMapGenPass@render@donut@@@std@@@std@@8 __real@3f800000 __real@4030000000000000 __real@4090000000000000 __security_cookie 